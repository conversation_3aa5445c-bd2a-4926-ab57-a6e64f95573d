/**
 * @file color.service.ts
 * @description Color Service Orchestrator - Dependency Injection Pattern
 * 
 * This service acts as a pure orchestrator that coordinates between specialized services:
 * - ColorRepository: All database operations
 * - ColorValidator: Input validation and standardization
 * - ColorSpaceCalculator: Color space calculations
 * - GradientProcessor: Gradient data processing
 * - ColorSyncService: Synchronization operations
 * - ColorAnalyticsService: Usage analytics and statistics
 * - ColorMappingService: Data transformation and mapping
 * 
 * The service contains no direct database access or business logic,
 * instead delegating all operations to the appropriate specialized services.
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { ColorEntry, NewColorEntry, UpdateColorEntry, ColorWithUsageResponse } from '../../../shared/types/color.types';
import { requireValidOrganizationId } from '../../utils/organization-validation';
// ColorRepository imported but using OptimizedColorRepository instead
// import { ColorRepository } from '../repositories/color.repository';
import { OptimizedColorRepository } from '../repositories/optimized-color.repository';
import { IColorRepository } from '../repositories/interfaces/color.repository.interface';
import { ColorSpaceCalculator } from '../../services/color/color-space-calculator.service';
import { ColorValidator } from '../../services/color/color-validator.service';
import { ColorSyncService } from '../../services/color/color-sync.service';
import { ColorAnalyticsService } from '../../services/color/color-analytics.service';
import { ColorMappingService } from '../../services/color/color-mapping.service';

export class ColorService {
  private colorRepository: IColorRepository;
  private colorSpaceCalculator: ColorSpaceCalculator;
  private colorValidator: ColorValidator;
  private colorSyncService: ColorSyncService;
  private colorAnalyticsService: ColorAnalyticsService;
  private colorMappingService: ColorMappingService;
  
  constructor(
    private db: Database.Database, 
    colorRepository?: IColorRepository,
    colorSpaceCalculator?: ColorSpaceCalculator,
    colorValidator?: ColorValidator,
    colorSyncService?: ColorSyncService,
    colorAnalyticsService?: ColorAnalyticsService,
    colorMappingService?: ColorMappingService
  ) {
    if (!db) {
      throw new Error('ColorService requires a valid database instance');
    }
    
    // Initialize all dependencies with sensible defaults for dependency injection
    // Use OptimizedColorRepository for better local-first performance
    this.colorRepository = colorRepository || (new OptimizedColorRepository(db, true) as any);
    this.colorSpaceCalculator = colorSpaceCalculator || new ColorSpaceCalculator();
    this.colorValidator = colorValidator || new ColorValidator();
    this.colorSyncService = colorSyncService || new ColorSyncService(
      db, 
      this.colorRepository, 
      this.colorValidator
    );
    this.colorAnalyticsService = colorAnalyticsService || new ColorAnalyticsService(
      this.colorRepository
    );
    this.colorMappingService = colorMappingService || new ColorMappingService(
      this.colorSpaceCalculator
    );
  }
  
  


  /**
   * Create color_spaces JSON object for storage
   * Only stores CMYK data - RGB/LAB/HSL are calculated on-demand
   */
  private createColorSpacesJson(_hex: string, cmykValues: { c: number; m: number; y: number; k: number }, gradient?: any): string {
    const colorSpaces: any = {};
    
    // Only store CMYK values (75% storage reduction)
    colorSpaces.cmyk = {
      c: cmykValues.c,
      m: cmykValues.m,
      y: cmykValues.y,
      k: cmykValues.k
    };
    
    // Add gradient data if present (NEW FORMAT)
    if (gradient && gradient.colors && Array.isArray(gradient.colors)) {
      colorSpaces.gradient = {
        colors: gradient.colors,
        colorCodes: gradient.colorCodes,
        type: 'linear',
        angle: gradient.angle || 45
      };
    }
    
    return JSON.stringify(colorSpaces);
  }

  /**
   * Create gradient_colors CSV string from GradientInfo format
   * Supports both hex colors and optional color codes
   */
  private createGradientColorsFromGradientInfo(gradient: any): string | null {
    if (!gradient) return null;
    
    // Handle GradientInfo format with colors array
    if (gradient.colors && Array.isArray(gradient.colors)) {
      // If color codes are provided, combine them with hex colors
      if (gradient.colorCodes && Array.isArray(gradient.colorCodes)) {
        return gradient.colors.map((color: string, index: number) => {
          const colorCode = gradient.colorCodes[index];
          return colorCode ? `${color}|${colorCode}` : color;
        }).join(',');
      }
      
      // Just hex colors
      return gradient.colors.join(',');
    }
    
    return null;
  }

  /**
   * Get all colors - includes product relationships for ColorEntry.product field
   * This version avoids deduplication to ensure all color-product relationships are returned,
   * which is critical for swatch dropdowns that show all products for a given color.
   * 
   * Uses OptimizedColorRepository for 3-5x faster performance with local-first architecture.
   */
  getAll(organizationId: string): ColorEntry[] {
    try {
      // TEMPORARY FIX: Always use base repository until color_usage_counts aggregation table is created
      // The optimized repository expects a color_usage_counts table that doesn't exist in Supabase
      console.log('[ColorService] Using base repository for reliable product associations');
      const rows = this.colorRepository.findAll(organizationId);
      
      // TODO: Re-enable optimized repository once aggregation table is created:
      // const optimizedRepo = this.colorRepository as OptimizedColorRepository;
      // if (optimizedRepo.findAllOptimized) {
      //   rows = optimizedRepo.findAllOptimized(organizationId);
      // }
      
      // TODO: Temporarily disable automatic orphan cleanup to allow proper color-product association
      // The application should handle orphan cleanup manually, not automatically during data retrieval
      // this.invalidateOrphanColors(rows, organizationId);
      
      // Return all colors - let the application handle proper product association
      const validRows = rows; // rows.filter(row => row.product_name || row.is_library);
      
      console.log(`[ColorService] Retrieved ${validRows.length} colors for organization ${organizationId}`);
      
      // Convert each row to a ColorEntry object using optimized mapping (skip expensive calculations for basic listing)
      const converted = validRows.map((row: any) => this.colorMappingService.convertToColorEntry(row, organizationId, {
        skipColorSpaceCalculation: true, // Skip RGB/HSL/LAB calculations for better performance
        skipGradientProcessing: false    // ENABLE gradient processing to fix gradient display in all views
      }));
      
      // Return converted color entries
      
      return converted;
    } catch (error) {
      console.error('[ColorService] Error getting all colors:', error);
      return [];
    }
  }

  /**
   * Invalidate (soft-delete) orphan colors that are not library colors and have no product associations.
   * TODO: Implement when needed
   */
  // private _invalidateOrphanColors(rows: any[], organizationId: string) {
  //   const orphanColors = rows.filter(row => !row.is_library && !row.product_name);
  //   
  //   if (orphanColors.length > 0) {
  //     console.warn(`[ColorService] Found ${orphanColors.length} orphan colors to invalidate for organization ${organizationId}`);
  //     
  //     // Delegate orphan cleanup to repository
  //     this.colorRepository.invalidateOrphans(organizationId);
  //   }
  // }

  /**
   * Get a color by ID
   */
  getById(colorId: string, organizationId: string): ColorEntry | null {
    try {
      // Delegate to repository for data access
      const row = this.colorRepository.findById(colorId, organizationId);
      
      if (!row) {
        console.warn(`[ColorService] Color not found: ${colorId} for organization ${organizationId}`);
        return null;
      }
      
      return this.colorMappingService.convertToColorEntry(row, organizationId);
    } catch (error) {
      console.error('[ColorService] Error getting color by ID:', error);
      return null;
    }
  }

  /**
   * Add a new color - orchestrates business logic and delegates data access
   * REQUIRES product association - no orphaned colors allowed
   */
  async add(color: NewColorEntry, userId?: string, organizationId?: string, fromSync: boolean = false): Promise<string> {
    try {
      // CRITICAL: Enforce organization requirement
      if (!organizationId) {
        throw new Error('Organization ID is required when creating colors');
      }

      // CRITICAL: Enforce product association requirement
      if (!color.product || typeof color.product !== 'string' || color.product.trim().length === 0) {
        throw new Error('Product name is required - no colors can exist without a product association');
      }

      const id = uuidv4();
      
      // Validate and standardize color values using ColorValidator
      const hexValidation = this.colorValidator.validateHex(color.hex);
      if (!hexValidation.isValid) {
        throw new Error(`Invalid HEX color: ${hexValidation.errors.join(', ')}`);
      }
      const standardizedHex = this.colorValidator.standardizeHex(color.hex);
      
      // Get source_id via repository to avoid direct database access
      const sourceCode = color.source || 'user';
      const sourceId = this.colorRepository.getSourceIdByCode(sourceCode);
      
      // Standardize code using ColorValidator
      const finalCode = this.colorValidator.standardizeColorCode(color.code || color.name);
      
      // Handle CMYK values
      let cmykValues: { c: number; m: number; y: number; k: number };
      
      if (color.gradient || !color.cmyk) {
        // For gradients or missing CMYK, derive from hex using ColorSpaceCalculator
        const derivedCMYK = this.colorSpaceCalculator.hexToCmyk(standardizedHex);
        if (!derivedCMYK) {
          throw new Error(`Could not derive CMYK from hex: ${standardizedHex}`);
        }
        cmykValues = derivedCMYK;
      } else {
        // Validate and parse provided CMYK using ColorValidator
        const cmykValidation = this.colorValidator.validateCMYK(color.cmyk);
        if (!cmykValidation.isValid) {
          throw new Error(`Invalid CMYK color: ${cmykValidation.errors.join(', ')}`);
        }
        const standardizedCMYK = this.colorValidator.standardizeCMYK(color.cmyk);
        const cmykMatch = standardizedCMYK.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/);
        if (!cmykMatch) {
          throw new Error(`Invalid CMYK format: ${standardizedCMYK}`);
        }
        cmykValues = {
          c: parseInt(cmykMatch[1] || '0', 10),
          m: parseInt(cmykMatch[2] || '0', 10),
          y: parseInt(cmykMatch[3] || '0', 10),
          k: parseInt(cmykMatch[4] || '0', 10)
        };
      }
      
      // Create JSON for denormalized storage
      const colorSpacesJson = this.createColorSpacesJson(standardizedHex, cmykValues, color.gradient);
      
      // Create gradient_colors string if this is a gradient  
      const gradientColors = color.gradient 
        ? this.createGradientColorsFromGradientInfo(color.gradient)
        : null;
      
      // Prepare data for repository insertion
      const colorData: any = {
        id: id,
        source_id: sourceId,
        code: finalCode,
        display_name: this.colorValidator.normalizeColorName(color.name),
        hex: standardizedHex,
        color_spaces: colorSpacesJson,
        is_gradient: color.gradient ? true : false,
        is_metallic: false,
        is_effect: false,
        is_library: color.isLibrary || false,
        gradient_colors: gradientColors,
        notes: color.notes || null,
        tags: null,
        properties: JSON.stringify({
          product: color.product,
          gradient: color.gradient
        }),
        is_synced: fromSync || false
      };
      
      // Delegate to repository for data insertion
      const insertedId = this.colorRepository.insert(colorData, organizationId);
      
      console.log(`[ColorService] Color ${insertedId} added locally`);

      // CRITICAL: Create product-color association immediately after color creation
      await this.createProductColorAssociation(insertedId, color.product, organizationId, userId);

      if (!fromSync) {
        const { syncOutboxService } = await import('../../services/sync/sync-outbox.service');
        await syncOutboxService.addToOutbox('colors', 'create', { id: insertedId, organizationId });
      }
      
      return insertedId;
      
    } catch (error) {
      console.error('[ColorService] Error adding color:', error);
      throw error;
    }
  }

  /**
   * Create product-color association by finding or creating product and linking to color
   */
  private async createProductColorAssociation(
    colorId: string, 
    productName: string, 
    organizationId: string, 
    userId?: string
  ): Promise<void> {
    try {
      console.log(`[ColorService] Creating product-color association: color=${colorId}, product="${productName}", org=${organizationId}`);

      // Import services dynamically to avoid circular dependencies
      const { ProductService } = await import('./product.service');
      const { ProductColorRelationshipService } = await import('../../services/product/product-color-relationship.service');
      const { ProductRepository } = await import('../repositories/product.repository');

      // Initialize services
      const productRepository = new ProductRepository(this.db);
      const productService = new ProductService(this.db, this);
      const relationshipService = new ProductColorRelationshipService(productRepository, this.colorRepository as any);

      // 1. Find existing product by name
      let productId: string | null = null;
      const existingProducts = productService.getAll(organizationId);
      const matchingProduct = existingProducts.find((p: any) => 
        p.name.toLowerCase().trim() === productName.toLowerCase().trim()
      );

      if (matchingProduct) {
        productId = matchingProduct.id;
        console.log(`[ColorService] Found existing product: ${productId} (${productName})`);
      } else {
        // 2. Create new product if it doesn't exist
        console.log(`[ColorService] Creating new product: "${productName}"`);
        const newProduct = await productService.add({
          name: productName.trim(),
          description: `Product created automatically for color association`,
          organizationId: organizationId,
          userId: userId || '',
          metadata: { autoCreated: true, createdForColor: colorId }
        }, userId || '', organizationId);
        productId = newProduct.id;
        console.log(`[ColorService] Created new product: ${productId}`);
      }

      // 3. Create the product-color association
      if (!productId) {
        throw new Error('Product ID is null - failed to find or create product');
      }
      const associationResult = relationshipService.addColorToProduct(productId, colorId, organizationId);
      
      if (!associationResult.success) {
        throw new Error(`Failed to create product-color association: ${associationResult.error}`);
      }

      console.log(`[ColorService] ✅ Successfully created product-color association: product=${productId}, color=${colorId}`);

    } catch (error) {
      console.error('[ColorService] ❌ Failed to create product-color association:', error);
      throw new Error(`Failed to create product-color association: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update a color - orchestrates business logic and delegates to repository
   */
  async update(id: string, updates: UpdateColorEntry, organizationId: string, fromSync: boolean = false): Promise<boolean> {
    try {
      
      const processedUpdates: any = {};
      
      // Handle business logic transformations using ColorValidator
      if (updates.name !== undefined) {
        const normalizedName = this.colorValidator.normalizeColorName(updates.name);
        processedUpdates.display_name = normalizedName;
      }
      
      if (updates.hex !== undefined) {
        const hexValidation = this.colorValidator.validateHex(updates.hex);
        if (!hexValidation.isValid) {
          throw new Error(`Invalid HEX color: ${hexValidation.errors.join(', ')}`);
        }
        const standardizedHex = this.colorValidator.standardizeHex(updates.hex);
        processedUpdates.hex = standardizedHex;
        
        // If hex changes, update CMYK in color_spaces using ColorSpaceCalculator
        if (updates.cmyk || !updates.gradient) {
          let cmykValues;
          if (updates.cmyk) {
            const cmykValidation = this.colorValidator.validateCMYK(updates.cmyk);
            if (cmykValidation.isValid) {
              const standardizedCMYK = this.colorValidator.standardizeCMYK(updates.cmyk);
              const match = standardizedCMYK.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/);
              cmykValues = match ? {
                c: parseInt(match[1] || '0', 10),
                m: parseInt(match[2] || '0', 10),
                y: parseInt(match[3] || '0', 10),
                k: parseInt(match[4] || '0', 10)
              } : null;
            } else {
              cmykValues = null;
            }
          } else {
            cmykValues = this.colorSpaceCalculator.hexToCmyk(standardizedHex);
          }
            
          if (cmykValues) {
            processedUpdates.color_spaces = JSON.stringify({
              cmyk: cmykValues
            });
          }
        }
      }
      
      // CRITICAL FIX: Handle gradient updates properly by fetching existing data first
      if (updates.gradient !== undefined) {
        console.log('[ColorService] Updating simplified gradient data:', JSON.stringify(updates.gradient, null, 2));
        
        // STEP 1: Fetch current color data from database to get existing color_spaces
        const existingColor = this.colorRepository.findById(id, organizationId);
        let existingColorSpaces = {};
        
        if (existingColor && existingColor.color_spaces) {
          try {
            existingColorSpaces = JSON.parse(existingColor.color_spaces);
            console.log('[ColorService] Loaded existing color_spaces:', JSON.stringify(existingColorSpaces, null, 2));
          } catch (e) {
            console.warn('[ColorService] Failed to parse existing color_spaces from database, using empty object:', e);
            existingColorSpaces = {};
          }
        } else {
          console.log('[ColorService] No existing color_spaces found, starting with empty object');
        }
        
        // STEP 2: Merge any CMYK data that was processed earlier in this method
        if (processedUpdates.color_spaces) {
          try {
            const newColorSpacesData = JSON.parse(processedUpdates.color_spaces);
            existingColorSpaces = { ...existingColorSpaces, ...newColorSpacesData };
            console.log('[ColorService] Merged new CMYK data with existing color_spaces');
          } catch (e) {
            console.warn('[ColorService] Failed to parse new color_spaces data:', e);
          }
        }
        
        // STEP 3: Handle gradient data update/removal
        if (updates.gradient && updates.gradient.colors && Array.isArray(updates.gradient.colors)) {
          processedUpdates.is_gradient = true;
          // Store colors as comma-separated list for backward compatibility
          processedUpdates.gradient_colors = updates.gradient.colors.join(',');
          
          // Store simplified gradient structure with color codes, preserving ALL existing data
          processedUpdates.color_spaces = JSON.stringify({ 
            ...existingColorSpaces, // Preserve ALL existing data (CMYK, etc.)
            gradient: {
              colors: updates.gradient.colors,
              colorCodes: updates.gradient.colorCodes,
              type: 'linear',
              angle: 45
            }
          });
          processedUpdates.properties = JSON.stringify({ gradient: updates.gradient });
          
          console.log('[ColorService] Successfully merged gradient data with existing color_spaces');
        } else {
          // Remove gradient data but preserve other color_spaces data
          processedUpdates.is_gradient = false;
          processedUpdates.gradient_colors = null;
          
          // Remove gradient from color_spaces but keep everything else
          const cleanedColorSpaces = { ...existingColorSpaces };
          if ('gradient' in cleanedColorSpaces) {
            delete cleanedColorSpaces.gradient;
          }
          processedUpdates.color_spaces = JSON.stringify(cleanedColorSpaces);
          
          console.log('[ColorService] Removed gradient data while preserving other color_spaces');
        }
      }
      
      if (updates.notes !== undefined) {
        processedUpdates.notes = updates.notes || null;
      }
      
      if (updates.tags !== undefined) {
        processedUpdates.tags = updates.tags || null;
      }
      
      // Set sync status
      processedUpdates.is_synced = fromSync || false;
      
      console.log('[ColorService] Final processed updates:', JSON.stringify(processedUpdates, null, 2));
      
      // Delegate to repository for data update
      const success = this.colorRepository.update(id, processedUpdates, organizationId);
      
      if (success) {
        console.log(`[ColorService] Successfully updated color ${id} locally`);
        
        // Verify the update by fetching the color back
        const verificationColor = this.colorRepository.findById(id, organizationId);
        if (verificationColor) {
          console.log('[ColorService] Post-update verification:');
          console.log(`  - Gradient Colors: ${verificationColor.gradient_colors}`);
          console.log(`  - Is Gradient: ${verificationColor.is_gradient}`);
          console.log(`  - Color Spaces: ${verificationColor.color_spaces?.substring(0, 200)}...`);
        }
        
        if (!fromSync) {
          const { syncOutboxService } = await import('../../services/sync/sync-outbox.service');
          await syncOutboxService.addToOutbox('colors', 'update', { id: id, organizationId });
        }
      } else {
        console.error(`[ColorService] Failed to update color ${id} in database`);
      }
      
      return success;
      
    } catch (error) {
      console.error('[ColorService] Error updating color:', error);
      return false;
    }
  }



  /**
   * Delete a color by ID - orchestrates business logic and delegates to repository
   */
  async delete(colorId: string, organizationId: string, fromSync: boolean = false): Promise<boolean> {
    try {
      console.log(`[ColorService] Delete called with colorId: ${colorId}, organizationId: ${organizationId}`);
      
      // Delegate soft delete to repository
      const success = this.colorRepository.softDelete(colorId, organizationId);
      
      if (success && !fromSync) {
        console.log(`[ColorService] Soft deleted color ${colorId} locally`);
        const { syncOutboxService } = await import('../../services/sync/sync-outbox.service');
        await syncOutboxService.addToOutbox('colors', 'delete', { id: colorId, organizationId });
      }
      
      console.log(`[ColorService] Delete result: ${success ? 'success' : 'failed'}`);
      return success;
    } catch (error) {
      console.error('[ColorService] Error deleting color:', error);
      return false;
    }
  }

  


  /**
   * Clear all colors for an organization - delegates to repository
   */
  clearAll(organizationId: string, hardDelete = false): boolean {
    try {
      // Delegate to repository for data clearing
      const success = this.colorRepository.clearAll(organizationId, hardDelete);
      
      if (success) {
        console.log(`[ColorService] ${hardDelete ? 'Hard' : 'Soft'} deleted colors with cascade handling`);
      }
      
      return success;
    } catch (error) {
      console.error('[ColorService] Error clearing colors:', error);
      return false;
    }
  }

  /**
   * Get soft deleted colors for recovery - delegates to repository
   */
  getSoftDeleted(organizationId: string, limit: number = 100, offset: number = 0): ColorEntry[] {
    try {
      // Delegate to repository for data access
      const rows = this.colorRepository.findSoftDeleted(organizationId, limit, offset);
      
      return rows.map(row => this.colorMappingService.convertToColorEntry(row, organizationId, {
        skipColorSpaceCalculation: true, // Skip expensive calculations for soft deleted listing
        skipGradientProcessing: true
      }));
    } catch (error) {
      console.error('[ColorService] Error getting soft deleted colors:', error);
      return [];
    }
  }

  /**
   * Restore a soft deleted color - delegates to repository
   */
  restore(colorId: string, organizationId: string, _userId?: string): boolean {
    try {
      // Delegate to repository for record restoration
      return this.colorRepository.restoreRecord(colorId, organizationId);
    } catch (error) {
      console.error('[ColorService] Error restoring color:', error);
      return false;
    }
  }

  /**
   * Bulk restore multiple colors - delegates to repository
   */
  bulkRestore(colorIds: string[], organizationId: string, _userId?: string): { success: boolean; restored: number } {
    try {
      // Delegate to repository for bulk restoration
      const result = this.colorRepository.bulkRestoreRecords(colorIds, organizationId);
      
      console.log(`[ColorService] Bulk restored ${result.restored} colors`);
      
      return result;
    } catch (error) {
      console.error('[ColorService] Error bulk restoring colors:', error);
      return { success: false, restored: 0 };
    }
  }

  /**
   * Cleanup old soft deleted colors - delegates to repository
   */
  cleanupOldSoftDeleted(organizationId: string, daysOld: number = 30): { success: boolean; cleaned: number } {
    try {
      // Delegate to repository for cleanup
      return this.colorRepository.cleanupOldSoftDeleted(organizationId, daysOld);
    } catch (error) {
      console.error('[ColorService] Error cleaning up old soft deleted colors:', error);
      return { success: false, cleaned: 0 };
    }
  }

  /**
   * Get color usage counts - delegates to ColorAnalyticsService
   */
  getColorUsageCounts(organizationId: string): Map<string, { count: number; products: string[] }> {
    try {
      // Delegate to ColorAnalyticsService for analytics operations
      return this.colorAnalyticsService.getColorUsageCounts(organizationId);
    } catch (error) {
      console.error('[ColorService] Error getting color usage counts:', error);
      return new Map();
    }
  }


  /**
   * Get unsynced colors - delegates to ColorSyncService
   */
  getUnsynced(): ColorEntry[] {
    try {
      // Delegate to ColorSyncService for sync-related operations
      return this.colorSyncService.getUnsyncedColors();
    } catch (error) {
      console.error('[ColorService] Error getting unsynced colors:', error);
      return [];
    }
  }

  /**
   * Push a local color to Supabase - delegates to ColorSyncService
   */
  async pushColorToSupabase(colorId: string, organizationId?: string, userId?: string): Promise<void> {
    return this.colorSyncService.pushColorToSupabase(colorId, organizationId || '', userId);
  }

  /**
   * Sync colors from Supabase - delegates to ColorSyncService
   */
  async syncColorsFromSupabase(userId: string, organizationId: string): Promise<ColorEntry[]> {
    return this.colorSyncService.syncColorsFromSupabase(userId, organizationId);
  }

  /**
   * Get all colors with usage counts combined
   * Returns ColorWithUsageResponse format expected by the frontend
   */
  async getAllWithUsage(organizationId: string): Promise<ColorWithUsageResponse> {
    try {
      // Get all colors
      const colors = await this.getAll(organizationId);
      
      // Get usage counts from analytics service
      const usageMap = this.colorAnalyticsService.getColorUsageCounts(organizationId);
      
      // Convert Map to Record for the API response
      const usageCounts: Record<string, { count: number; products: string[] }> = {};
      for (const [key, value] of usageMap.entries()) {
        usageCounts[key] = value;
      }
      
      // Count colors with usage
      const colorsWithUsage = Array.from(usageMap.values()).filter(usage => usage.count > 0).length;
      
      console.log(`[ColorService] getAllWithUsage returning ${colors.length} colors with ${colorsWithUsage} having usage data`);
      
      return {
        colors,
        usageCounts,
        organizationId,
        totalColors: colors.length,
        colorsWithUsage
      };
    } catch (error) {
      console.error('[ColorService] Error in getAllWithUsage:', error);
      return {
        colors: [],
        usageCounts: {},
        organizationId,
        totalColors: 0,
        colorsWithUsage: 0
      };
    }
  }

  /**
   * Get all products for each color name - delegates to ColorAnalyticsService
   * Returns a Map where key = color name, value = array of all products using that color
   */
  buildColorNameProductMap(organizationId: string): Map<string, string[]> {
    try {
      // Delegate to ColorAnalyticsService for analytics operations
      return this.colorAnalyticsService.buildColorNameProductMap(organizationId);
    } catch (error) {
      console.error('[ColorService] Error building color name product map:', error);
      return new Map();
    }
  }

  /**
   * Get products grouped by color name for the useColorProductMap hook
   */
  getProductsByColorName(organizationId: string): Record<string, any[]> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'ColorService.getProductsByColorName');
      
      // Delegate to ColorAnalyticsService for analytics operations
      return this.colorAnalyticsService.getProductsByColorName(validatedOrgId);
    } catch (error) {
      console.error('[ColorService] Error getting products by color name:', error);
      return {};
    }
  }

  /**
   * Get usage counts for colors - delegates to ColorAnalyticsService
   * This method is called by IPC handlers
   */
  getUsageCounts(organizationId: string): Record<string, { count: number; products: string[] }> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'ColorService.getUsageCounts');
      
      // Delegate to ColorAnalyticsService for analytics operations
      const usageMap = this.colorAnalyticsService.getColorUsageCounts(validatedOrgId);
      
      // Convert Map to Record for the API response
      const usageCounts: Record<string, { count: number; products: string[] }> = {};
      for (const [key, value] of usageMap.entries()) {
        usageCounts[key] = value;
      }
      
      return usageCounts;
    } catch (error) {
      console.error('[ColorService] Error getting usage counts:', error);
      return {};
    }
  }

  /**
   * Normalize Pantone codes - stub implementation
   * This method is called by IPC handlers
   */
  async normalizePantoneCodes(organizationId: string): Promise<{ success: boolean; message: string; updatedCount?: number }> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'ColorService.normalizePantoneCodes');
      
      console.log(`[ColorService] Normalizing Pantone codes for organization: ${validatedOrgId}`);
      
      // TODO: Implement actual Pantone code normalization logic
      // For now, return a success response indicating no codes were normalized
      return {
        success: true,
        message: 'Pantone code normalization completed (no codes required normalization)',
        updatedCount: 0
      };
    } catch (error) {
      console.error('[ColorService] Error normalizing Pantone codes:', error);
      return {
        success: false,
        message: `Failed to normalize Pantone codes: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  // ============================================================================
  // ROUTER-COMPATIBLE METHODS
  // These methods provide compatibility with the IPC router expectations
  // ============================================================================

  /**
   * Analyze color data (placeholder implementation)
   */
  async analyzeColor(colorData: any, organizationId: string): Promise<any> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'ColorService.analyzeColor');
      
      console.log(`[ColorService] Analyzing color for organization: ${validatedOrgId}`, colorData);
      
      // TODO: Implement actual color analysis logic
      return {
        success: true,
        analysis: {
          hex: colorData.hex || '#000000',
          colorSpace: 'sRGB',
          harmony: 'complementary',
          accessibility: 'AA'
        }
      };
    } catch (error) {
      console.error('[ColorService] Error analyzing color:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Import colors (placeholder implementation)
   */
  async importColors(importData: any, organizationId: string): Promise<any> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'ColorService.importColors');
      
      console.log(`[ColorService] Importing colors for organization: ${validatedOrgId}`, importData);
      
      // TODO: Implement actual color import logic
      return {
        success: true,
        imported: 0,
        errors: []
      };
    } catch (error) {
      console.error('[ColorService] Error importing colors:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Export colors (placeholder implementation)
   */
  async exportColors(exportOptions: any, organizationId: string): Promise<any> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'ColorService.exportColors');
      
      console.log(`[ColorService] Exporting colors for organization: ${validatedOrgId}`, exportOptions);
      
      // TODO: Implement actual color export logic
      return {
        success: true,
        exported: 0,
        format: exportOptions.format || 'json'
      };
    } catch (error) {
      console.error('[ColorService] Error exporting colors:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}