/**
 * @file oauth-service.test.ts
 * @description Unit tests for OAuthService
 */

import { describe, it, expect, beforeEach, afterEach, vi, MockedFunction } from 'vitest';
import Database from 'better-sqlite3';
import { OAuthService } from '../oauth-service';

// Mock external dependencies
vi.mock('electron', () => ({
  shell: {
    openExternal: vi.fn()
  },
  safeStorage: {
    encryptString: vi.fn((str: string) => Buffer.from(str, 'utf8')),
    decryptString: vi.fn((buffer: Buffer) => buffer.toString('utf8')),
    isEncryptionAvailable: vi.fn(() => true)
  }
}));

vi.mock('node-machine-id', () => ({
  machineId: vi.fn(() => Promise.resolve('test-machine-id'))
}));

vi.mock('../../../utils/logger.service', () => ({
  LoggerFactory: {
    create: vi.fn(() => ({
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn()
    }))
  }
}));

vi.mock('../../supabase-client', () => ({
  createSupabaseClient: vi.fn(() => ({
    auth: {
      signInWithOAuth: vi.fn(),
      signOut: vi.fn(),
      getUser: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: { subscription: { unsubscribe: vi.fn() } }
      }))
    }
  }))
}));

// Mock fetch for OAuth token exchange
global.fetch = vi.fn();

describe('OAuthService', () => {
  let service: OAuthService;
  let db: Database.Database;
  let mockFetch: MockedFunction<typeof fetch>;

  beforeEach(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Create minimal schema
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        email TEXT NOT NULL,
        name TEXT,
        google_id TEXT,
        avatar_url TEXT,
        metadata TEXT DEFAULT '{}',
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE TABLE IF NOT EXISTS organizations (
        id TEXT PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE TABLE IF NOT EXISTS organization_users (
        organization_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        role TEXT NOT NULL DEFAULT 'member',
        joined_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (organization_id, user_id),
        FOREIGN KEY (organization_id) REFERENCES organizations(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
      );
    `);

    service = new OAuthService(db);
    mockFetch = fetch as MockedFunction<typeof fetch>;
  });

  afterEach(() => {
    db.close();
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully', () => {
      expect(service).toBeDefined();
      expect(service.isAuthenticated()).toBe(false);
    });

    it('should generate PKCE code verifier and challenge', () => {
      const { codeVerifier, codeChallenge } = service.generatePKCE();
      
      expect(codeVerifier).toBeDefined();
      expect(codeChallenge).toBeDefined();
      expect(codeVerifier.length).toBeGreaterThan(40);
      expect(codeChallenge.length).toBeGreaterThan(40);
    });
  });

  describe('authentication flow', () => {
    it('should generate correct authorization URL', () => {
      const { authUrl, codeVerifier } = service.getAuthorizationUrl();
      
      expect(authUrl).toContain('https://accounts.google.com/o/oauth2/v2/auth');
      expect(authUrl).toContain('client_id=');
      expect(authUrl).toContain('response_type=code');
      expect(authUrl).toContain('scope=');
      expect(authUrl).toContain('code_challenge=');
      expect(authUrl).toContain('code_challenge_method=S256');
      expect(codeVerifier).toBeDefined();
    });

    it('should handle successful token exchange', async () => {
      const mockTokenResponse = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expires_in: 3600,
        token_type: 'Bearer'
      };

      const mockUserResponse = {
        id: 'google-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        picture: 'https://example.com/avatar.jpg'
      };

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockTokenResponse)
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockUserResponse)
        } as Response);

      const result = await service.exchangeCodeForTokens('test-auth-code', 'test-code-verifier');

      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.user!.email).toBe('<EMAIL>');
      expect(result.user!.name).toBe('Test User');
    });

    it('should handle token exchange errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({ error: 'invalid_grant' })
      } as Response);

      const result = await service.exchangeCodeForTokens('invalid-code', 'test-code-verifier');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Token exchange failed');
    });

    it('should handle network errors during token exchange', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await service.exchangeCodeForTokens('test-code', 'test-code-verifier');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error');
    });
  });

  describe('user management', () => {
    it('should create or update user in database', async () => {
      const googleUser = {
        id: 'google-123',
        email: '<EMAIL>',
        name: 'New User',
        picture: 'https://example.com/avatar.jpg'
      };

      const user = await service.createOrUpdateUser(googleUser);

      expect(user).toBeDefined();
      expect(user.email).toBe('<EMAIL>');
      expect(user.name).toBe('New User');
      expect(user.external_id).toBeDefined();

      // Verify user was inserted into database
      const dbUser = db.prepare('SELECT * FROM users WHERE email = ?').get('<EMAIL>');
      expect(dbUser).toBeDefined();
    });

    it('should update existing user', async () => {
      // Create initial user
      const initialUser = {
        id: 'google-123',
        email: '<EMAIL>',
        name: 'Initial Name',
        picture: 'https://example.com/old-avatar.jpg'
      };

      await service.createOrUpdateUser(initialUser);

      // Update user
      const updatedUser = {
        id: 'google-123',
        email: '<EMAIL>',
        name: 'Updated Name',
        picture: 'https://example.com/new-avatar.jpg'
      };

      const result = await service.createOrUpdateUser(updatedUser);

      expect(result.name).toBe('Updated Name');
      expect(result.avatar_url).toBe('https://example.com/new-avatar.jpg');

      // Verify only one user exists
      const userCount = db.prepare('SELECT COUNT(*) as count FROM users WHERE email = ?').get('<EMAIL>');
      expect(userCount.count).toBe(1);
    });
  });

  describe('token management', () => {
    it('should store and retrieve tokens securely', async () => {
      const tokens = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expires_in: 3600
      };

      await service.storeTokens(tokens);

      const storedTokens = await service.getStoredTokens();
      expect(storedTokens.accessToken).toBe('test-access-token');
      expect(storedTokens.refreshToken).toBe('test-refresh-token');
      expect(storedTokens.expiresAt).toBeDefined();
    });

    it('should detect expired tokens', async () => {
      const expiredTokens = {
        access_token: 'expired-token',
        refresh_token: 'refresh-token',
        expires_in: -3600 // Expired 1 hour ago
      };

      await service.storeTokens(expiredTokens);

      const isValid = await service.isTokenValid();
      expect(isValid).toBe(false);
    });

    it('should refresh expired tokens', async () => {
      // Store expired tokens
      const expiredTokens = {
        access_token: 'expired-token',
        refresh_token: 'valid-refresh-token',
        expires_in: -3600
      };

      await service.storeTokens(expiredTokens);

      // Mock successful refresh
      const mockRefreshResponse = {
        access_token: 'new-access-token',
        expires_in: 3600
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockRefreshResponse)
      } as Response);

      const result = await service.refreshTokens();

      expect(result.success).toBe(true);
      
      // Verify new tokens are stored
      const newTokens = await service.getStoredTokens();
      expect(newTokens.accessToken).toBe('new-access-token');
    });
  });

  describe('logout', () => {
    it('should clear all authentication data on logout', async () => {
      // Set up authenticated state
      const tokens = {
        access_token: 'test-token',
        refresh_token: 'test-refresh',
        expires_in: 3600
      };

      await service.storeTokens(tokens);

      // Verify initially authenticated
      expect(service.isAuthenticated()).toBe(true);

      // Logout
      await service.logout();

      // Verify authentication state is cleared
      expect(service.isAuthenticated()).toBe(false);
      
      const storedTokens = await service.getStoredTokens();
      expect(storedTokens.accessToken).toBeNull();
      expect(storedTokens.refreshToken).toBeNull();
    });
  });

  describe('GDPR compliance', () => {
    it('should check GDPR consent', async () => {
      const hasConsent = await service.checkGDPRConsent();
      
      // Currently always returns true (as per implementation)
      expect(hasConsent).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      // Close the database to cause errors
      db.close();

      const googleUser = {
        id: 'google-123',
        email: '<EMAIL>',
        name: 'Test User'
      };

      // Should not throw, but handle error gracefully
      await expect(service.createOrUpdateUser(googleUser)).rejects.toThrow();
    });

    it('should handle malformed token responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ invalid: 'response' })
      } as Response);

      const result = await service.exchangeCodeForTokens('test-code', 'test-verifier');

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });
});