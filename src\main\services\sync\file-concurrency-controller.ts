/**
 * @file file-concurrency-controller.ts
 * @description File-based concurrency control system for cross-process synchronization
 * 
 * This implements a file-based locking mechanism to prevent race conditions
 * between multiple ChromaSync instances or processes accessing sync operations.
 */

import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import { EventEmitter } from 'events';

export interface Lock {
  id: string;
  resource: string;
  processId: number;
  acquiredAt: number;
  expiresAt: number;
  lockFilePath: string;
}

export interface LockMetadata {
  id: string;
  resource: string;
  processId: number;
  acquiredAt: number;
  expiresAt: number;
  hostname: string;
  appVersion: string;
}

export interface ConcurrencyController {
  acquireLock(resource: string, timeout?: number): Promise<Lock>;
  releaseLock(lock: Lock): Promise<void>;
  isLocked(resource: string): boolean;
  waitForLock(resource: string, timeout?: number): Promise<void>;
  cleanup(): Promise<void>;
}

/**
 * File-based concurrency controller for cross-process synchronization
 */
export class FileConcurrencyController extends EventEmitter implements ConcurrencyController {
  private static instance: FileConcurrencyController | null = null;
  
  private readonly lockDir: string;
  private readonly activeLocks: Map<string, Lock> = new Map();
  private readonly lockWaiters: Map<string, Array<{ resolve: () => void; reject: (error: Error) => void; timeout: NodeJS.Timeout }>> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;
  
  // Configuration
  private readonly DEFAULT_LOCK_TIMEOUT = 30000; // 30 seconds
  private readonly CLEANUP_INTERVAL = 10000; // 10 seconds
  private readonly STALE_LOCK_THRESHOLD = 60000; // 1 minute
  private readonly MAX_WAIT_TIME = 120000; // 2 minutes
  private readonly DEADLOCK_DETECTION_INTERVAL = 5000; // 5 seconds
  
  // Deadlock detection
  private readonly lockDependencies: Map<string, Set<string>> = new Map();
  private deadlockDetectionTimer: NodeJS.Timeout | null = null;
  
  private constructor() {
    super();
    
    // Create locks directory in user data
    this.lockDir = path.join(app.getPath('userData'), 'sync-locks');
    this.ensureLockDirectory();
    
    // Start cleanup and deadlock detection
    this.startPeriodicCleanup();
    this.startDeadlockDetection();
    
    console.log('[FileConcurrency] 🔒 File-based concurrency controller initialized');
    console.log('[FileConcurrency] 📁 Lock directory:', this.lockDir);
  }
  
  /**
   * Get singleton instance
   */
  static getInstance(): FileConcurrencyController {
    if (!FileConcurrencyController.instance) {
      FileConcurrencyController.instance = new FileConcurrencyController();
    }
    return FileConcurrencyController.instance;
  }
  
  /**
   * Ensure lock directory exists
   */
  private ensureLockDirectory(): void {
    try {
      if (!fs.existsSync(this.lockDir)) {
        fs.mkdirSync(this.lockDir, { recursive: true });
        console.log('[FileConcurrency] 📁 Created lock directory:', this.lockDir);
      }
    } catch (error) {
      console.error('[FileConcurrency] ❌ Failed to create lock directory:', error);
      throw new Error(`Failed to create lock directory: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Acquire a lock for a resource
   */
  async acquireLock(resource: string, timeout: number = this.DEFAULT_LOCK_TIMEOUT): Promise<Lock> {
    const lockId = this.generateLockId();
    const lockFilePath = path.join(this.lockDir, `${resource}.lock`);
    const processId = process.pid;
    const acquiredAt = Date.now();
    const expiresAt = acquiredAt + timeout;
    
    console.log(`[FileConcurrency] 🔐 Attempting to acquire lock for resource: ${resource}`);
    console.log(`[FileConcurrency] 🆔 Lock ID: ${lockId}, Process: ${processId}, Timeout: ${timeout}ms`);
    
    // Check if we already have this lock
    if (this.activeLocks.has(resource)) {
      const existingLock = this.activeLocks.get(resource)!;
      if (existingLock.processId === processId) {
        console.log(`[FileConcurrency] ♻️ Reusing existing lock for resource: ${resource}`);
        return existingLock;
      }
    }
    
    // Check for potential deadlock before acquiring
    await this.checkForPotentialDeadlock(resource);
    
    // Try to acquire the lock with retry logic
    const maxRetries = 3;
    let retryCount = 0;
    
    while (retryCount < maxRetries) {
      try {
        // Check if lock file exists and is valid
        if (await this.isResourceLocked(resource)) {
          const existingLock = await this.readLockFile(lockFilePath);
          
          // Check if lock is stale
          if (existingLock && this.isLockStale(existingLock)) {
            console.log(`[FileConcurrency] 🧹 Removing stale lock for resource: ${resource}`);
            await this.forceReleaseLock(resource);
          } else {
            // Lock is active, add dependency tracking for deadlock detection
            if (existingLock) {
              this.addLockDependency(resource, existingLock.processId);
            }
            
            // Lock is active, wait or throw error
            if (retryCount === maxRetries - 1) {
              throw new Error(`Resource ${resource} is locked by process ${existingLock?.processId || 'unknown'}`);
            }
            
            console.log(`[FileConcurrency] ⏳ Resource ${resource} is locked, retrying in 1s... (${retryCount + 1}/${maxRetries})`);
            await this.sleep(1000);
            retryCount++;
            continue;
          }
        }
        
        // Create lock metadata
        const lockMetadata: LockMetadata = {
          id: lockId,
          resource,
          processId,
          acquiredAt,
          expiresAt,
          hostname: require('os').hostname(),
          appVersion: app.getVersion()
        };
        
        // Write lock file atomically
        await this.writeLockFile(lockFilePath, lockMetadata);
        
        // Create lock object
        const lock: Lock = {
          id: lockId,
          resource,
          processId,
          acquiredAt,
          expiresAt,
          lockFilePath
        };
        
        // Store in active locks
        this.activeLocks.set(resource, lock);
        
        console.log(`[FileConcurrency] ✅ Successfully acquired lock for resource: ${resource}`);
        this.emit('lock_acquired', { resource, lockId, processId });
        
        return lock;
        
      } catch (error) {
        console.error(`[FileConcurrency] ❌ Failed to acquire lock (attempt ${retryCount + 1}):`, error);
        
        if (retryCount === maxRetries - 1) {
          throw new Error(`Failed to acquire lock for resource ${resource}: ${error instanceof Error ? error.message : String(error)}`);
        }
        
        retryCount++;
        await this.sleep(1000 * retryCount); // Exponential backoff
      }
    }
    
    throw new Error(`Failed to acquire lock for resource ${resource} after ${maxRetries} attempts`);
  }
  
  /**
   * Release a lock
   */
  async releaseLock(lock: Lock): Promise<void> {
    console.log(`[FileConcurrency] 🔓 Releasing lock for resource: ${lock.resource}`);
    
    try {
      // Remove from active locks
      this.activeLocks.delete(lock.resource);
      
      // Remove lock file
      if (fs.existsSync(lock.lockFilePath)) {
        fs.unlinkSync(lock.lockFilePath);
        console.log(`[FileConcurrency] 🗑️ Removed lock file: ${lock.lockFilePath}`);
      }
      
      // Remove from lock dependencies
      this.lockDependencies.delete(lock.resource);
      
      // Notify waiters
      this.notifyWaiters(lock.resource);
      
      console.log(`[FileConcurrency] ✅ Successfully released lock for resource: ${lock.resource}`);
      this.emit('lock_released', { resource: lock.resource, lockId: lock.id, processId: lock.processId });
      
    } catch (error) {
      console.error(`[FileConcurrency] ❌ Failed to release lock for resource ${lock.resource}:`, error);
      throw new Error(`Failed to release lock: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * Check if a resource is locked
   */
  isLocked(resource: string): boolean {
    // Check active locks first
    if (this.activeLocks.has(resource)) {
      const lock = this.activeLocks.get(resource)!;
      if (!this.isLockExpired(lock)) {
        return true;
      } else {
        // Lock expired, remove it
        this.activeLocks.delete(resource);
      }
    }
    
    // Check file system
    const lockFilePath = path.join(this.lockDir, `${resource}.lock`);
    return fs.existsSync(lockFilePath);
  }
  
  /**
   * Wait for a lock to be released
   */
  async waitForLock(resource: string, timeout: number = this.MAX_WAIT_TIME): Promise<void> {
    console.log(`[FileConcurrency] ⏳ Waiting for lock on resource: ${resource} (timeout: ${timeout}ms)`);
    
    return new Promise((resolve, reject) => {
      // Check if already unlocked
      if (!this.isLocked(resource)) {
        resolve();
        return;
      }
      
      // Set up timeout
      const timeoutHandle = setTimeout(() => {
        this.removeWaiter(resource, resolve);
        reject(new Error(`Timeout waiting for lock on resource: ${resource}`));
      }, timeout);
      
      // Add to waiters
      if (!this.lockWaiters.has(resource)) {
        this.lockWaiters.set(resource, []);
      }
      
      this.lockWaiters.get(resource)!.push({
        resolve: () => {
          clearTimeout(timeoutHandle);
          resolve();
        },
        reject: (error: Error) => {
          clearTimeout(timeoutHandle);
          reject(error);
        },
        timeout: timeoutHandle
      });
    });
  }
  
  /**
   * Force release a lock (for cleanup)
   */
  private async forceReleaseLock(resource: string): Promise<void> {
    const lockFilePath = path.join(this.lockDir, `${resource}.lock`);
    
    try {
      if (fs.existsSync(lockFilePath)) {
        fs.unlinkSync(lockFilePath);
      }
      
      this.activeLocks.delete(resource);
      this.lockDependencies.delete(resource);
      this.notifyWaiters(resource);
      
      console.log(`[FileConcurrency] 🧹 Force released lock for resource: ${resource}`);
      
    } catch (error) {
      console.error(`[FileConcurrency] ❌ Failed to force release lock for resource ${resource}:`, error);
    }
  }
  
  /**
   * Check if resource is locked by reading file
   */
  private async isResourceLocked(resource: string): Promise<boolean> {
    const lockFilePath = path.join(this.lockDir, `${resource}.lock`);
    return fs.existsSync(lockFilePath);
  }
  
  /**
   * Read lock file metadata
   */
  private async readLockFile(lockFilePath: string): Promise<LockMetadata | null> {
    try {
      if (!fs.existsSync(lockFilePath)) {
        return null;
      }
      
      const content = fs.readFileSync(lockFilePath, 'utf8');
      return JSON.parse(content) as LockMetadata;
      
    } catch (error) {
      console.error('[FileConcurrency] ❌ Failed to read lock file:', error);
      return null;
    }
  }
  
  /**
   * Write lock file atomically
   */
  private async writeLockFile(lockFilePath: string, metadata: LockMetadata): Promise<void> {
    const tempFilePath = `${lockFilePath}.tmp`;
    
    try {
      // Write to temporary file first
      fs.writeFileSync(tempFilePath, JSON.stringify(metadata, null, 2));
      
      // Atomic rename
      fs.renameSync(tempFilePath, lockFilePath);
      
    } catch (error) {
      // Cleanup temp file if it exists
      if (fs.existsSync(tempFilePath)) {
        try {
          fs.unlinkSync(tempFilePath);
        } catch (cleanupError) {
          console.error('[FileConcurrency] ❌ Failed to cleanup temp file:', cleanupError);
        }
      }
      
      throw error;
    }
  }
  
  /**
   * Check if lock is stale
   */
  private isLockStale(lockMetadata: LockMetadata): boolean {
    const now = Date.now();
    const lockAge = now - lockMetadata.acquiredAt;
    const isExpired = now > lockMetadata.expiresAt;
    const isStale = lockAge > this.STALE_LOCK_THRESHOLD;
    
    return isExpired || isStale;
  }
  
  /**
   * Check if lock is expired
   */
  private isLockExpired(lock: Lock): boolean {
    return Date.now() > lock.expiresAt;
  }
  
  /**
   * Generate unique lock ID
   */
  private generateLockId(): string {
    return `lock_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
  
  /**
   * Notify waiters that a resource is available
   */
  private notifyWaiters(resource: string): void {
    const waiters = this.lockWaiters.get(resource);
    if (waiters && waiters.length > 0) {
      console.log(`[FileConcurrency] 📢 Notifying ${waiters.length} waiters for resource: ${resource}`);
      
      // Notify all waiters
      waiters.forEach(waiter => {
        clearTimeout(waiter.timeout);
        waiter.resolve();
      });
      
      // Clear waiters
      this.lockWaiters.delete(resource);
    }
  }
  
  /**
   * Remove a specific waiter
   */
  private removeWaiter(resource: string, resolveFunc: () => void): void {
    const waiters = this.lockWaiters.get(resource);
    if (waiters) {
      const index = waiters.findIndex(w => w.resolve === resolveFunc);
      if (index !== -1) {
        clearTimeout(waiters[index].timeout);
        waiters.splice(index, 1);
        
        if (waiters.length === 0) {
          this.lockWaiters.delete(resource);
        }
      }
    }
  }
  
  /**
   * Start periodic cleanup of stale locks
   */
  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(async () => {
      await this.performCleanup();
    }, this.CLEANUP_INTERVAL);
    
    console.log(`[FileConcurrency] 🧹 Started periodic cleanup (interval: ${this.CLEANUP_INTERVAL}ms)`);
  }
  
  /**
   * Perform cleanup of stale locks
   */
  private async performCleanup(): Promise<void> {
    try {
      const lockFiles = fs.readdirSync(this.lockDir).filter(file => file.endsWith('.lock'));
      
      for (const lockFile of lockFiles) {
        const lockFilePath = path.join(this.lockDir, lockFile);
        const lockMetadata = await this.readLockFile(lockFilePath);
        
        if (lockMetadata && this.isLockStale(lockMetadata)) {
          const resource = path.basename(lockFile, '.lock');
          console.log(`[FileConcurrency] 🧹 Cleaning up stale lock for resource: ${resource}`);
          await this.forceReleaseLock(resource);
        }
      }
      
    } catch (error) {
      console.error('[FileConcurrency] ❌ Error during cleanup:', error);
    }
  }
  
  /**
   * Start deadlock detection
   */
  private startDeadlockDetection(): void {
    this.deadlockDetectionTimer = setInterval(() => {
      this.detectDeadlocks();
    }, this.DEADLOCK_DETECTION_INTERVAL);
    
    console.log(`[FileConcurrency] 🔍 Started deadlock detection (interval: ${this.DEADLOCK_DETECTION_INTERVAL}ms)`);
  }
  
  /**
   * Detect and resolve deadlocks
   */
  private detectDeadlocks(): void {
    try {
      // Simple deadlock detection: check for circular dependencies
      const visited = new Set<string>();
      const recursionStack = new Set<string>();
      
      for (const [resource, dependencies] of this.lockDependencies.entries()) {
        if (!visited.has(resource)) {
          if (this.hasCircularDependency(resource, visited, recursionStack)) {
            console.warn(`[FileConcurrency] ⚠️ Deadlock detected involving resource: ${resource}`);
            this.resolveDeadlock(resource);
          }
        }
      }
      
    } catch (error) {
      console.error('[FileConcurrency] ❌ Error during deadlock detection:', error);
    }
  }
  
  /**
   * Check for circular dependencies (deadlock)
   */
  private hasCircularDependency(resource: string, visited: Set<string>, recursionStack: Set<string>): boolean {
    visited.add(resource);
    recursionStack.add(resource);
    
    const dependencies = this.lockDependencies.get(resource);
    if (dependencies) {
      for (const dependency of dependencies) {
        if (!visited.has(dependency)) {
          if (this.hasCircularDependency(dependency, visited, recursionStack)) {
            return true;
          }
        } else if (recursionStack.has(dependency)) {
          return true; // Circular dependency found
        }
      }
    }
    
    recursionStack.delete(resource);
    return false;
  }
  
  /**
   * Resolve deadlock by releasing oldest lock
   */
  private resolveDeadlock(resource: string): void {
    console.log(`[FileConcurrency] 🔧 Resolving deadlock for resource: ${resource}`);
    
    // Find oldest lock in the dependency chain
    let oldestLock: Lock | null = null;
    let oldestTime = Date.now();
    
    for (const [, lock] of this.activeLocks.entries()) {
      if (lock.acquiredAt < oldestTime) {
        oldestTime = lock.acquiredAt;
        oldestLock = lock;
      }
    }
    
    if (oldestLock) {
      console.log(`[FileConcurrency] 🔓 Releasing oldest lock to resolve deadlock: ${oldestLock.resource}`);
      this.forceReleaseLock(oldestLock.resource);
      this.emit('deadlock_resolved', { resource: oldestLock.resource, lockId: oldestLock.id });
    }
  }
  
  /**
   * Check for potential deadlock before acquiring a lock
   */
  private async checkForPotentialDeadlock(resource: string): Promise<void> {
    // Check if acquiring this lock would create a circular dependency
    const currentProcessLocks = Array.from(this.activeLocks.keys());
    
    if (currentProcessLocks.length === 0) {
      return; // No existing locks, no deadlock possible
    }
    
    // Simple heuristic: if we already hold locks and are trying to acquire another,
    // check if any other process is waiting for our locks while holding the target resource
    const lockFilePath = path.join(this.lockDir, `${resource}.lock`);
    if (fs.existsSync(lockFilePath)) {
      const existingLock = await this.readLockFile(lockFilePath);
      if (existingLock && existingLock.processId !== process.pid) {
        // Another process holds this lock, check for potential circular wait
        console.log(`[FileConcurrency] 🔍 Checking for potential deadlock: we want ${resource}, process ${existingLock.processId} has it`);
        
        // If we detect a potential deadlock pattern, wait a random amount to break symmetry
        if (currentProcessLocks.length > 0) {
          const randomDelay = Math.random() * 100; // 0-100ms random delay
          console.log(`[FileConcurrency] ⏸️ Adding random delay (${randomDelay.toFixed(1)}ms) to prevent deadlock`);
          await this.sleep(randomDelay);
        }
      }
    }
  }

  /**
   * Add lock dependency for deadlock detection
   */
  private addLockDependency(resource: string, processId: number): void {
    const dependencyKey = `process_${processId}`;
    
    if (!this.lockDependencies.has(resource)) {
      this.lockDependencies.set(resource, new Set());
    }
    
    this.lockDependencies.get(resource)!.add(dependencyKey);
    
    console.log(`[FileConcurrency] 📊 Added dependency: ${resource} -> process ${processId}`);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    console.log('[FileConcurrency] 🧹 Starting cleanup...');
    
    // Stop timers
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    if (this.deadlockDetectionTimer) {
      clearInterval(this.deadlockDetectionTimer);
      this.deadlockDetectionTimer = null;
    }
    
    // Release all active locks
    const lockPromises = Array.from(this.activeLocks.values()).map(lock => 
      this.releaseLock(lock).catch(error => 
        console.error(`[FileConcurrency] ❌ Failed to release lock ${lock.resource}:`, error)
      )
    );
    
    await Promise.all(lockPromises);
    
    // Clear all waiters
    for (const [resource, waiters] of this.lockWaiters.entries()) {
      waiters.forEach(waiter => {
        clearTimeout(waiter.timeout);
        waiter.reject(new Error(`Cleanup: Lock wait cancelled for resource ${resource}`));
      });
    }
    this.lockWaiters.clear();
    
    // Clear dependencies
    this.lockDependencies.clear();
    
    console.log('[FileConcurrency] ✅ Cleanup completed');
  }
  
  /**
   * Get current lock status for debugging
   */
  getLockStatus(): { activeLocks: number; waiters: number; dependencies: number } {
    return {
      activeLocks: this.activeLocks.size,
      waiters: Array.from(this.lockWaiters.values()).reduce((sum, waiters) => sum + waiters.length, 0),
      dependencies: this.lockDependencies.size
    };
  }
}

// Export singleton instance
export const fileConcurrencyController = FileConcurrencyController.getInstance();