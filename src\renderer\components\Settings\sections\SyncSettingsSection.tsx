/**
 * @file SyncSettingsSection.tsx
 * @description Cloud sync settings section component
 */

import React from 'react';
import { Cloud, CloudOff, Info } from 'lucide-react';
import { useSettingsStore } from '../../../store/settings.store';
import { useSyncStore } from '../../../store/sync.store';

/**
 * Sync settings section component
 */
export const SyncSettingsSection: React.FC = () => {
  const settings = useSettingsStore();
  const { authState } = useSyncStore();

  return (
    <section>
      <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
        Cloud Sync Settings
      </h3>
      
      <div className="space-y-4">
        {/* Auto Sync Toggle */}
        <div>
          <label className="flex items-center justify-between cursor-pointer bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
            <div className="flex items-center">
              <Cloud size={20} className="text-brand-primary dark:text-blue-400 mr-3" />
              <div>
                <span className="text-ui-foreground-primary dark:text-gray-300 block font-medium">
                  Enable Auto Sync
                </span>
                <span className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                  Automatically sync changes to the cloud
                </span>
              </div>
            </div>
            <input
              type="checkbox"
              className="form-checkbox h-5 w-5 text-brand-primary rounded"
              checked={settings.autoSyncInterval > 0}
              onChange={(e) => settings.setAutoSyncInterval(e.target.checked ? 30 : 0)}
              disabled={!authState.isAuthenticated}
            />
          </label>
        </div>

        {/* Sync Frequency */}
        <div>
          <label className="block text-ui-foreground-primary dark:text-white mb-2">
            Sync Frequency
          </label>
          <select
            className="w-full px-3 py-2 bg-ui-background-secondary dark:bg-zinc-800 border border-ui-border-light dark:border-zinc-600 rounded-[var(--radius-md)] text-ui-foreground-primary dark:text-white"
            value={settings.autoSyncInterval}
            onChange={(e) => settings.setAutoSyncInterval(parseInt(e.target.value))}
            disabled={!authState.isAuthenticated || settings.autoSyncInterval === 0}
          >
            <option value={0}>Manual only</option>
            <option value={5}>Every 5 minutes</option>
            <option value={15}>Every 15 minutes</option>
            <option value={30}>Every 30 minutes</option>
            <option value={60}>Every hour</option>
          </select>
        </div>

        {/* Real-time Sync */}
        <div>
          <label className="flex items-center justify-between cursor-pointer bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
            <div className="flex items-center">
              <Cloud size={20} className="text-ui-foreground-secondary dark:text-gray-400 mr-3" />
              <div>
                <span className="text-ui-foreground-primary dark:text-gray-300 block font-medium">
                  Real-time Sync
                </span>
                <span className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                  Instantly sync changes as they happen
                </span>
              </div>
            </div>
            <input
              type="checkbox"
              className="form-checkbox h-5 w-5 text-brand-primary rounded"
              checked={settings.enableRealtimeSync}
              onChange={(e) => settings.setEnableRealtimeSync(e.target.checked)}
              disabled={!authState.isAuthenticated}
            />
          </label>
        </div>

        {/* Conflict Resolution */}
        <div>
          <label className="block text-ui-foreground-primary dark:text-white mb-2">
            Conflict Resolution
          </label>
          <select
            className="w-full px-3 py-2 bg-ui-background-secondary dark:bg-zinc-800 border border-ui-border-light dark:border-zinc-600 rounded-[var(--radius-md)] text-ui-foreground-primary dark:text-white"
            value={settings.syncConflictStrategy}
            onChange={(e) => settings.setSyncConflictStrategy(e.target.value as 'local' | 'remote' | 'newest')}
          >
            <option value="local">Prefer local changes</option>
            <option value="remote">Prefer cloud changes</option>
            <option value="newest">Use newest changes</option>
          </select>
          <div className="text-sm text-ui-foreground-secondary dark:text-gray-400 mt-2 flex items-start">
            <Info size={14} className="mr-1 mt-0.5 flex-shrink-0" />
            <span>
              Choose how to handle conflicts when the same data is modified both locally and in the cloud.
            </span>
          </div>
        </div>

        {/* Startup Sync */}
        <div>
          <label className="flex items-center justify-between cursor-pointer bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
            <div className="flex items-center">
              <CloudOff size={20} className="text-ui-foreground-secondary dark:text-gray-400 mr-3" />
              <div>
                <span className="text-ui-foreground-primary dark:text-gray-300 block font-medium">
                  Sync on Startup
                </span>
                <span className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                  Automatically sync when the app starts
                </span>
              </div>
            </div>
            <input
              type="checkbox"
              className="form-checkbox h-5 w-5 text-brand-primary rounded"
              checked={settings.autoSyncOnStartup}
              onChange={(e) => settings.setAutoSyncOnStartup(e.target.checked)}
              disabled={!authState.isAuthenticated}
            />
          </label>
        </div>

        {/* Authentication Notice */}
        {!authState.isAuthenticated && (
          <div className="bg-brand-primary/10 dark:bg-blue-900/20 border border-brand-primary dark:border-blue-600 rounded-[var(--radius-lg)] p-4">
            <div className="flex items-start">
              <Info size={16} className="text-brand-primary dark:text-blue-400 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <span className="text-ui-foreground-primary dark:text-white font-medium block">
                  Sign in Required
                </span>
                <p className="text-sm text-ui-foreground-secondary dark:text-gray-400 mt-1">
                  Sign in to your account to enable cloud sync features.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default SyncSettingsSection;