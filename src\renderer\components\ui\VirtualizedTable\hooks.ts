/**
 * @file VirtualizedTable/hooks.ts
 * @description Custom hooks for virtualized table functionality
 */

import React, {
  useState,
  useMemo,
  useCallback,
  useRef,
  useEffect,
} from 'react';
import { VirtualizedTableColumn } from './index';

export interface UseSortingOptions {
  defaultSortColumn?: string;
  defaultSortDirection?: 'asc' | 'desc';
}

export interface UseSortingReturn {
  sortColumn: string | undefined;
  sortDirection: 'asc' | 'desc' | null;
  handleSortChange: (
    columnKey: string,
    direction: 'asc' | 'desc' | null
  ) => void;
  clearSort: () => void;
}

/**
 * Hook for managing table sorting state
 */
export const useSorting = (
  options: UseSortingOptions = {}
): UseSortingReturn => {
  const [sortColumn, setSortColumn] = useState<string | undefined>(
    options.defaultSortColumn
  );
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc' | null>(
    options.defaultSortDirection || null
  );

  const handleSortChange = useCallback(
    (columnKey: string, direction: 'asc' | 'desc' | null) => {
      setSortColumn(direction ? columnKey : undefined);
      setSortDirection(direction);
    },
    []
  );

  const clearSort = useCallback(() => {
    setSortColumn(undefined);
    setSortDirection(null);
  }, []);

  return {
    sortColumn,
    sortDirection,
    handleSortChange,
    clearSort,
  };
};

export interface UseRowSelectionOptions {
  multiSelect?: boolean;
  defaultSelectedIndices?: number[];
}

export interface UseRowSelectionReturn {
  selectedIndices: Set<number>;
  selectedRowIndex: number | undefined;
  selectRow: (index: number) => void;
  selectMultiple: (indices: number[]) => void;
  clearSelection: () => void;
  toggleRow: (index: number) => void;
  selectAll: (totalRows: number) => void;
  isSelected: (index: number) => boolean;
}

/**
 * Hook for managing row selection state
 */
export const useRowSelection = (
  options: UseRowSelectionOptions = {}
): UseRowSelectionReturn => {
  const [selectedIndices, setSelectedIndices] = useState<Set<number>>(
    new Set(options.defaultSelectedIndices || [])
  );

  const selectedRowIndex = useMemo(() => {
    if (selectedIndices.size === 1) {
      return Array.from(selectedIndices)[0];
    }
    return undefined;
  }, [selectedIndices]);

  const selectRow = useCallback(
    (index: number) => {
      if (options.multiSelect) {
        setSelectedIndices(prev => new Set([...prev, index]));
      } else {
        setSelectedIndices(new Set([index]));
      }
    },
    [options.multiSelect]
  );

  const selectMultiple = useCallback(
    (indices: number[]) => {
      if (options.multiSelect) {
        setSelectedIndices(prev => new Set([...prev, ...indices]));
      } else {
        setSelectedIndices(new Set(indices.slice(-1))); // Only last one if not multi-select
      }
    },
    [options.multiSelect]
  );

  const clearSelection = useCallback(() => {
    setSelectedIndices(new Set());
  }, []);

  const toggleRow = useCallback(
    (index: number) => {
      setSelectedIndices(prev => {
        const newSet = new Set(prev);
        if (newSet.has(index)) {
          newSet.delete(index);
        } else {
          if (!options.multiSelect) {
            newSet.clear();
          }
          newSet.add(index);
        }
        return newSet;
      });
    },
    [options.multiSelect]
  );

  const selectAll = useCallback(
    (totalRows: number) => {
      if (options.multiSelect) {
        setSelectedIndices(
          new Set(Array.from({ length: totalRows }, (_, i) => i))
        );
      }
    },
    [options.multiSelect]
  );

  const isSelected = useCallback(
    (index: number) => {
      return selectedIndices.has(index);
    },
    [selectedIndices]
  );

  return {
    selectedIndices,
    selectedRowIndex,
    selectRow,
    selectMultiple,
    clearSelection,
    toggleRow,
    selectAll,
    isSelected,
  };
};

export interface UseVirtualTableKeyboardOptions<T> {
  data: T[];
  onRowClick?: (item: T, index: number) => void;
  onRowDoubleClick?: (item: T, index: number) => void;
  selectedRowIndex?: number;
  onSelectedRowChange?: (index: number) => void;
}

export interface UseVirtualTableKeyboardReturn {
  onKeyDown: (event: React.KeyboardEvent) => void;
}

/**
 * Hook for keyboard navigation in virtualized table
 */
export const useVirtualTableKeyboard = <T>({
  data,
  onRowClick,
  onRowDoubleClick,
  selectedRowIndex,
  onSelectedRowChange,
}: UseVirtualTableKeyboardOptions<T>): UseVirtualTableKeyboardReturn => {
  const onKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (data.length === 0) {
        return;
      }

      const currentIndex = selectedRowIndex ?? -1;

      switch (event.key) {
        case 'ArrowDown': {
          event.preventDefault();
          const nextIndex = Math.min(currentIndex + 1, data.length - 1);
          onSelectedRowChange?.(nextIndex);
          break;
        }

        case 'ArrowUp': {
          event.preventDefault();
          const prevIndex = Math.max(currentIndex - 1, 0);
          onSelectedRowChange?.(prevIndex);
          break;
        }

        case 'Home':
          event.preventDefault();
          onSelectedRowChange?.(0);
          break;

        case 'End':
          event.preventDefault();
          onSelectedRowChange?.(data.length - 1);
          break;

        case 'Enter':
          event.preventDefault();
          if (currentIndex >= 0 && currentIndex < data.length) {
            if (event.shiftKey && onRowDoubleClick) {
              onRowDoubleClick(data[currentIndex], currentIndex);
            } else if (onRowClick) {
              onRowClick(data[currentIndex], currentIndex);
            }
          }
          break;

        case 'PageDown': {
          event.preventDefault();
          const pageDownIndex = Math.min(currentIndex + 10, data.length - 1);
          onSelectedRowChange?.(pageDownIndex);
          break;
        }

        case 'PageUp': {
          event.preventDefault();
          const pageUpIndex = Math.max(currentIndex - 10, 0);
          onSelectedRowChange?.(pageUpIndex);
          break;
        }

        default:
          break;
      }
    },
    [data, selectedRowIndex, onRowClick, onRowDoubleClick, onSelectedRowChange]
  );

  return { onKeyDown };
};

export interface UseTableFiltersOptions<T> {
  data: T[];
  columns: VirtualizedTableColumn<T>[];
}

export interface UseTableFiltersReturn<T> {
  filteredData: T[];
  filters: Record<string, string>;
  setFilter: (columnKey: string, value: string) => void;
  clearFilter: (columnKey: string) => void;
  clearAllFilters: () => void;
  hasActiveFilters: boolean;
}

/**
 * Hook for table filtering functionality
 */
export const useTableFilters = <T>({
  data,
  columns,
}: UseTableFiltersOptions<T>): UseTableFiltersReturn<T> => {
  const [filters, setFilters] = useState<Record<string, string>>({});

  const filteredData = useMemo(() => {
    if (Object.keys(filters).length === 0) {
      return data;
    }

    return data.filter(item => {
      return Object.entries(filters).every(([columnKey, filterValue]) => {
        if (!filterValue.trim()) {
          return true;
        }

        const column = columns.find(col => col.key === columnKey);
        if (!column || !column.filterable) {
          return true;
        }

        // For now, do simple string matching
        // This could be enhanced with more sophisticated filtering
        const cellValue = String(column.render(item, 0)).toLowerCase();
        return cellValue.includes(filterValue.toLowerCase());
      });
    });
  }, [data, filters, columns]);

  const setFilter = useCallback((columnKey: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [columnKey]: value,
    }));
  }, []);

  const clearFilter = useCallback((columnKey: string) => {
    setFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[columnKey];
      return newFilters;
    });
  }, []);

  const clearAllFilters = useCallback(() => {
    setFilters({});
  }, []);

  const hasActiveFilters = Object.values(filters).some(
    value => value.trim() !== ''
  );

  return {
    filteredData,
    filters,
    setFilter,
    clearFilter,
    clearAllFilters,
    hasActiveFilters,
  };
};

export interface UseInfiniteScrollOptions {
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  threshold?: number;
}

export interface UseInfiniteScrollReturn {
  onScrollToBottom: () => void;
}

/**
 * Hook for infinite scrolling functionality
 */
export const useInfiniteScroll = ({
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  threshold = 0.8,
}: UseInfiniteScrollOptions): UseInfiniteScrollReturn => {
  const loadingRef = useRef(false);
  const timeoutRef = useRef<number | null>(null);

  const onScrollToBottom = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !loadingRef.current) {
      loadingRef.current = true;
      fetchNextPage();

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Reset loading flag after a brief delay
      timeoutRef.current = setTimeout(() => {
        loadingRef.current = false;
        timeoutRef.current = null;
      }, 100) as unknown as number;
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return { onScrollToBottom };
};

export interface UseColumnResizingOptions<T> {
  columns: VirtualizedTableColumn<T>[];
  minColumnWidth?: number;
  maxColumnWidth?: number;
}

export interface UseColumnResizingReturn<T> {
  columns: VirtualizedTableColumn<T>[];
  resizeColumn: (columnKey: string, newWidth: number) => void;
  resetColumnWidths: () => void;
}

/**
 * Hook for column resizing functionality
 */
export const useColumnResizing = <T>({
  columns: initialColumns,
  minColumnWidth = 50,
  maxColumnWidth = 500,
}: UseColumnResizingOptions<T>): UseColumnResizingReturn<T> => {
  const [columns, setColumns] = useState(initialColumns);

  const resizeColumn = useCallback(
    (columnKey: string, newWidth: number) => {
      const clampedWidth = Math.max(
        minColumnWidth,
        Math.min(maxColumnWidth, newWidth)
      );

      setColumns(prev =>
        prev.map(col =>
          col.key === columnKey ? { ...col, width: clampedWidth } : col
        )
      );
    },
    [minColumnWidth, maxColumnWidth]
  );

  const resetColumnWidths = useCallback(() => {
    setColumns(initialColumns);
  }, [initialColumns]);

  // Update columns when initial columns change
  useEffect(() => {
    setColumns(initialColumns);
  }, [initialColumns]);

  return {
    columns,
    resizeColumn,
    resetColumnWidths,
  };
};
