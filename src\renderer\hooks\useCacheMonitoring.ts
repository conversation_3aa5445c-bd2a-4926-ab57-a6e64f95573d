/**
 * @file useCacheMonitoring.ts
 * @description Hook for monitoring cache performance with real-time metrics
 */

import { useState, useEffect, useCallback } from 'react';
import { colorCacheService } from '../services/color-cache.service';

interface CacheMonitoringData {
  hitRate: number;
  totalRequests: number;
  hits: number;
  misses: number;
  deduplicatedRequests: number;
  cacheSize: number;
  inFlightCount: number;
  averageResponseTime: number;
  memoryUsage: string;
  apiCallReduction: string;
  lastUpdate: number;
}

/**
 * Hook to monitor cache performance with automatic updates
 * Returns real-time cache metrics and performance data
 */
export function useCacheMonitoring(refreshInterval: number = 5000) {
  const [metrics, setMetrics] = useState<CacheMonitoringData | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);

  // Get fresh metrics
  const refreshMetrics = useCallback(() => {
    try {
      const rawMetrics = colorCacheService.getMetrics();
      
      const apiCallReduction = rawMetrics.deduplicatedRequests > 0 
        ? `${Math.round((rawMetrics.deduplicatedRequests / rawMetrics.totalRequests) * 100)}%`
        : '0%';

      setMetrics({
        hitRate: rawMetrics.hitRate,
        totalRequests: rawMetrics.totalRequests,
        hits: rawMetrics.hits,
        misses: rawMetrics.misses,
        deduplicatedRequests: rawMetrics.deduplicatedRequests,
        cacheSize: rawMetrics.cacheSize,
        inFlightCount: rawMetrics.inFlightCount,
        averageResponseTime: Math.round(rawMetrics.averageResponseTime * 100) / 100,
        memoryUsage: rawMetrics.memoryUsage,
        apiCallReduction,
        lastUpdate: Date.now()
      });
    } catch (error) {
      console.error('[CacheMonitoring] Error refreshing metrics:', error);
    }
  }, []);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    refreshMetrics();
  }, [refreshMetrics]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
  }, []);

  // Log performance summary
  const logPerformanceSummary = useCallback(() => {
    colorCacheService.logPerformanceSummary();
  }, []);

  // Reset metrics
  const resetMetrics = useCallback(() => {
    colorCacheService.resetMetrics();
    refreshMetrics();
  }, [refreshMetrics]);

  // Setup automatic refresh when monitoring is active
  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(refreshMetrics, refreshInterval);
    return () => clearInterval(interval);
  }, [isMonitoring, refreshInterval, refreshMetrics]);

  // Start monitoring on mount
  useEffect(() => {
    startMonitoring();
    return () => stopMonitoring();
  }, [startMonitoring, stopMonitoring]);

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    refreshMetrics,
    logPerformanceSummary,
    resetMetrics
  };
}

/**
 * Simple hook to get cache metrics once without monitoring
 */
export function useCacheMetrics() {
  const [metrics, setMetrics] = useState<CacheMonitoringData | null>(null);

  const refreshMetrics = useCallback(() => {
    try {
      const rawMetrics = colorCacheService.getMetrics();
      
      const apiCallReduction = rawMetrics.deduplicatedRequests > 0 
        ? `${Math.round((rawMetrics.deduplicatedRequests / rawMetrics.totalRequests) * 100)}%`
        : '0%';

      setMetrics({
        hitRate: rawMetrics.hitRate,
        totalRequests: rawMetrics.totalRequests,
        hits: rawMetrics.hits,
        misses: rawMetrics.misses,
        deduplicatedRequests: rawMetrics.deduplicatedRequests,
        cacheSize: rawMetrics.cacheSize,
        inFlightCount: rawMetrics.inFlightCount,
        averageResponseTime: Math.round(rawMetrics.averageResponseTime * 100) / 100,
        memoryUsage: rawMetrics.memoryUsage,
        apiCallReduction,
        lastUpdate: Date.now()
      });
    } catch (error) {
      console.error('[CacheMetrics] Error getting metrics:', error);
    }
  }, []);

  useEffect(() => {
    refreshMetrics();
  }, [refreshMetrics]);

  return {
    metrics,
    refreshMetrics
  };
}