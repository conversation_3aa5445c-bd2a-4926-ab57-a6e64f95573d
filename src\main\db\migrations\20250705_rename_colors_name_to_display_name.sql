-- Migration: Remove colors.name and keep only display_name for consistency with Supabase
-- Date: 2025-07-05
-- Purpose: Standardize column naming between local and remote databases
-- Note: display_name column already exists in schema, we just need to remove name column

BEGIN TRANSACTION;

-- First, ensure display_name has data from name column if it's currently NULL
UPDATE colors SET display_name = name WHERE display_name IS NULL AND name IS NOT NULL;

-- Create a temporary table with the new structure (without name column)
CREATE TABLE colors_new (
    id TEXT PRIMARY KEY,
    display_name TEXT,
    code TEXT,
    hex TEXT NOT NULL,
    source_id INTEGER NOT NULL DEFAULT 1,
    color_spaces JSON DEFAULT '{}',
    is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
    is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
    is_effect BOOLEAN NOT NULL DEFAULT FALSE,
    gradient_colors TEXT,
    notes TEXT,
    tags TEXT,
    is_library BOOLEAN NOT NULL DEFAULT FALSE,
    properties JSON DEFAULT '{}',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    organization_id TEXT,
    created_by TEXT,
    user_id TEXT,
    deleted_at TEXT,
    device_id TEXT,
    conflict_resolved_at TEXT,
    is_synced INTEGER DEFAULT 0,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);

-- Copy all data to the new table (excluding name column)
INSERT INTO colors_new (
    id, display_name, code, hex, source_id, color_spaces,
    is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
    is_library, properties, created_at, updated_at, version,
    organization_id, created_by, user_id, deleted_at, device_id,
    conflict_resolved_at, is_synced
)
SELECT 
    id, display_name, code, hex, source_id, color_spaces,
    is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
    is_library, properties, created_at, updated_at, version,
    organization_id, created_by, user_id, deleted_at, device_id,
    conflict_resolved_at, is_synced
FROM colors;

-- Drop the old table
DROP TABLE colors;

-- Rename the new table
ALTER TABLE colors_new RENAME TO colors;

-- Recreate indexes (only for columns that exist)
CREATE INDEX idx_colors_hex ON colors(hex);
CREATE INDEX idx_colors_organization_id ON colors(organization_id);
CREATE INDEX idx_colors_created_at ON colors(created_at);
CREATE INDEX idx_colors_display_name ON colors(display_name);
CREATE INDEX idx_colors_code ON colors(code) WHERE code IS NOT NULL;

-- Update any triggers or constraints that might reference the old column
-- (Add specific trigger recreations here if needed)

COMMIT;

-- Verification query
SELECT 'Migration completed. Colors with display_name: ' || COUNT(*) FROM colors WHERE display_name IS NOT NULL;