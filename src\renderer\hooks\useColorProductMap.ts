/**
 * @file useColorProductMap.ts
 * @description Custom hook to fetch complete product relationships by color name
 * Used to fix swatch dropdown incomplete product lists issue
 * FIXED: Added proper memoization and organization context tracking to prevent excessive API calls
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useOrganizationStore } from '../store/organization.store';

export interface UseColorProductMapResult {
  productMap: Record<string, string[]>;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Global cache to prevent duplicate requests across component instances
const productMapCache = new Map<
  string,
  { data: Record<string, string[]>; timestamp: number }
>();
const CACHE_DURATION = 30000; // 30 seconds cache
const pendingRequests = new Map<string, Promise<Record<string, string[]>>>();

/**
 * Hook to fetch and manage color name → products mapping
 * This provides the complete product relationships needed for swatch dropdowns
 * FIXED: Now uses organization-based caching and prevents duplicate requests
 */
export function useColorProductMap(): UseColorProductMapResult {
  const [productMap, setProductMap] = useState<Record<string, string[]>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Track organization context to invalidate cache on org changes
  const { currentOrganization } = useOrganizationStore();
  const organizationId = currentOrganization?.external_id;

  const fetchProductMap = useCallback(
    async (forceRefresh = false) => {
      // Don't fetch if no organization is selected
      if (!organizationId) {
        setProductMap({});
        setIsLoading(false);
        setError(null);
        return;
      }

      const cacheKey = `productMap_${organizationId}`;

      // Check cache first unless forcing refresh
      if (!forceRefresh) {
        const cached = productMapCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
          setProductMap(cached.data);
          setIsLoading(false);
          setError(null);
          return;
        }
      }

      // Check if there's already a pending request for this org
      if (pendingRequests.has(cacheKey)) {
        try {
          const result = await pendingRequests.get(cacheKey)!;
          setProductMap(result);
          setIsLoading(false);
          setError(null);
          return;
        } catch (err) {
          // Continue with new request if pending one failed
        }
      }

      // Cancel any existing request for this component
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();
      setIsLoading(true);
      setError(null);

      // Create the request promise and cache it
      const requestPromise = (async () => {
        try {
          const response = await window.colorAPI.getProductsByColorName();

          if (response.success && response.data) {
            // Cache the successful result
            productMapCache.set(cacheKey, {
              data: response.data,
              timestamp: Date.now(),
            });
            return response.data;
          } else {
            const errorMsg =
              response.error || 'Failed to load product relationships';
            throw new Error(errorMsg);
          }
        } catch (err) {
          if (err instanceof Error && err.name === 'AbortError') {
            throw err; // Let abort errors bubble up
          }
          const errorMsg =
            err instanceof Error
              ? err.message
              : 'Unknown error loading product map';
          throw new Error(errorMsg);
        }
      })();

      // Cache the pending request
      pendingRequests.set(cacheKey, requestPromise);

      try {
        const result = await requestPromise;

        // Only update state if request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          setProductMap(result);
          setError(null);
        }
      } catch (err) {
        if (err instanceof Error && err.name === 'AbortError') {
          return; // Don't update state for aborted requests
        }
        if (!abortControllerRef.current?.signal.aborted) {
          const errorMsg =
            err instanceof Error
              ? err.message
              : 'Unknown error loading product map';
          setError(errorMsg);
          setProductMap({});
        }
      } finally {
        // Clean up pending request
        pendingRequests.delete(cacheKey);

        if (!abortControllerRef.current?.signal.aborted) {
          setIsLoading(false);
        }
      }
    },
    [organizationId]
  );

  // Debounced effect to prevent rapid successive calls when organization changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchProductMap();
    }, 100); // 100ms debounce

    // Cleanup on unmount or when dependencies change
    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchProductMap]);

  // Listen for force refresh events
  useEffect(() => {
    const handleForceRefresh = (event: CustomEvent) => {
      const { organizationId: eventOrgId } = event.detail;
      if (eventOrgId === organizationId) {
        console.log(
          '[useColorProductMap] Force refresh triggered for organization:',
          eventOrgId
        );
        fetchProductMap(true); // Force refresh
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener(
        'product-map:force-refresh',
        handleForceRefresh as EventListener
      );

      return () => {
        window.removeEventListener(
          'product-map:force-refresh',
          handleForceRefresh as EventListener
        );
      };
    }

    return undefined;
  }, [organizationId, fetchProductMap]);

  const refetch = useCallback(() => fetchProductMap(true), [fetchProductMap]);

  return {
    productMap,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Helper function to get products for a specific color name from the map
 */
export function getProductsForColorName(
  productMap: Record<string, string[]>,
  colorName: string | undefined
): string[] {
  if (!colorName || !productMap) {
    return [];
  }

  return productMap[colorName] || [];
}

/**
 * Invalidate product map cache for a specific organization
 */
export function invalidateProductMapCache(organizationId: string): void {
  const cacheKey = `productMap_${organizationId}`;
  productMapCache.delete(cacheKey);
  pendingRequests.delete(cacheKey);
  console.log(
    '[useColorProductMap] Cache invalidated for organization:',
    organizationId
  );
}

/**
 * Force refresh product map for all hook instances
 */
export function refreshProductMapForOrganization(organizationId: string): void {
  invalidateProductMapCache(organizationId);
  // Trigger a custom event that hook instances can listen to
  if (typeof window !== 'undefined') {
    window.dispatchEvent(
      new CustomEvent('product-map:force-refresh', {
        detail: { organizationId },
      })
    );
  }
}
