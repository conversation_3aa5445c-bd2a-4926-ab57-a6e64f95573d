# ChromaSync UUID Migration Project

## Problem Statement

ChromaSync is experiencing critical sync failures due to UUID mapping complexity. The system currently uses mixed UUID patterns with external_id mappings, causing:

- Sync outbox failures: "Invalid product data in outbox: external_id"
- Database constraint conflicts: "UNIQUE constraint failed: organizations.slug"  
- IdMappingManager complexity with external_id → id mapping logic
- Inconsistent field naming (external_id vs id)
- Performance overhead from mapping lookups

## Goals

**Primary Goal**: Eliminate ALL UUID mapping complexity and use UUIDs directly as primary identifiers across both local SQLite and Supabase databases.

**Success Metrics**:
- Zero sync failures related to ID mapping
- Removal of all external_id references 
- Direct UUID usage in all database operations
- Simplified codebase with reduced cognitive load
- Improved sync performance (eliminate mapping lookups)

## Solution Overview

Implement a 6-phase migration strategy to transition from complex UUID mapping to direct UUID usage:

1. **Database Schema Cleanup** - Remove mapping columns and validate schema consistency
2. **Code Cleanup** - Remove IdMappingManager and simplify type definitions  
3. **Supabase Schema Alignment** - Ensure remote schema matches local
4. **Sync Logic Simplification** - Direct UUID sync patterns
5. **Testing & Validation** - Comprehensive migration testing
6. **Deployment Strategy** - Safe rollout with rollback plan

## Technical Requirements

### Phase 1: Database Schema Cleanup
- Remove supabase_id column from colors table
- Remove any remaining external_id references
- Verify all tables use UUID primary keys (TEXT PRIMARY KEY)
- Validate UUID formatting across all records
- Create