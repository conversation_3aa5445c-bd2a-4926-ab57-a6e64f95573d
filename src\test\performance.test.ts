import { describe, test, beforeAll, afterAll, expect } from 'vitest';
import { chromium, Page, Browser } from 'playwright';
import PerformanceMetrics from './utils/performance-metrics';

describe('Performance tests for large datasets', () => {
  let browser: Browser;
  let page: Page;
  let metrics: PerformanceMetrics;

  beforeAll(async () => {
    browser = await chromium.launch();
    const context = await browser.newContext();
    page = await context.newPage();
    metrics = new PerformanceMetrics();

    // Navigate to app
    await page.goto('http://localhost:5173');

    // Verify the app is loaded
    await page.waitForSelector('[data-testid="app-container"]');
  });

  afterAll(async () => {
    await browser.close();

    // Generate and save performance report
    const report = metrics.generateReport();
    // In a real implementation, you'd save this to a file
    console.log(report);
  });

  test('Initial page load performance', async () => {
    const loadMetrics = await metrics.measurePageLoad(page);
    metrics.storeResult(loadMetrics, 'initial-load');

    // Check if any metrics exceed thresholds
    const slowMetrics = loadMetrics.filter(
      metric => metric.threshold && metric.value > metric.threshold
    );

    if (slowMetrics.length > 0) {
      console.warn('Slow metrics detected:', slowMetrics);
    }

    // Verify domComplete is under 2000ms
    const domComplete = loadMetrics.find(m => m.name === 'domComplete');
    if (domComplete) {
      expect(domComplete.value).toBeLessThan(2000);
    }
  });

  test('Memory usage with idle application', async () => {
    const memoryMetrics = await metrics.measureMemoryUsage(page);
    metrics.storeResult(memoryMetrics, 'idle-app');

    // Verify heap usage is reasonable (under 100MB for idle app)
    const heapUsage = memoryMetrics.find(m => m.name === 'Used JS Heap');
    if (heapUsage) {
      expect(heapUsage.value).toBeLessThan(100);
    }
  });

  test('Loading 100 color entries performance', async () => {
    // Function to generate test data and load it into the app
    const loadTestData = async () => {
      // Execute load test data function in browser context
      await page.evaluate(() => {
        // This function would be implemented in the app
        // to load a large dataset for testing
        // @ts-ignore: Assuming this function exists in the app
        window.testHelpers?.loadTestData(100);
      });

      // Wait for data to be displayed
      await page.waitForSelector('[data-testid="color-row"]:nth-child(100)');
    };

    const dataLoadMetrics = await metrics.measureLargeDatasetPerformance(
      page,
      loadTestData,
      100
    );
    metrics.storeResult(dataLoadMetrics, 'load-100-items');

    // Verify performance is acceptable
    const loadTime = dataLoadMetrics[0];
    expect(loadTime.value).toBeLessThan(1000);
  });

  test('Scrolling performance with large dataset', async () => {
    // Measure scrolling performance
    const scrollAction = async () => {
      // Scroll down to the bottom
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });

      // Wait a moment for rendering to complete
      await page.waitForTimeout(500);

      // Scroll back to top
      await page.evaluate(() => {
        window.scrollTo(0, 0);
      });
    };

    const scrollMetrics = await metrics.measureInteraction(page, scrollAction);
    metrics.storeResult(scrollMetrics, 'scroll-large-dataset');

    // Verify scrolling is smooth (under 500ms)
    expect(scrollMetrics[0].value).toBeLessThan(500);
  });

  test('Search performance with large dataset', async () => {
    // Measure search performance
    const searchAction = async () => {
      await page.fill('[data-testid="search-input"]', 'Test');
      await page.press('[data-testid="search-input"]', 'Enter');

      // Wait for search results to be displayed
      await page.waitForSelector('[data-testid="search-results-container"]');
    };

    const searchMetrics = await metrics.measureInteraction(page, searchAction);
    metrics.storeResult(searchMetrics, 'search-large-dataset');

    // Verify search is fast (under 300ms)
    expect(searchMetrics[0].value).toBeLessThan(300);
  });

  test('Switching between table and swatch views', async () => {
    // Measure view switching performance
    const switchViewAction = async () => {
      // Switch to swatches view
      await page.click('[data-testid="swatch-view-tab"]');
      await page.waitForSelector('[data-testid="color-swatches"]');

      // Switch back to table view
      await page.click('[data-testid="table-view-tab"]');
      await page.waitForSelector('[data-testid="color-table"]');
    };

    const viewSwitchMetrics = await metrics.measureInteraction(
      page,
      switchViewAction
    );
    metrics.storeResult(viewSwitchMetrics, 'view-switching');

    // Verify view switching is fast (under 200ms)
    expect(viewSwitchMetrics[0].value).toBeLessThan(200);
  });
});
