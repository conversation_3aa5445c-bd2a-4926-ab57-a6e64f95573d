/**
 * @file color-sync.service.ts
 * @description Service for color synchronization with Supabase
 *
 * Extracted from ColorService to follow single responsibility principle.
 * Handles all Supabase synchronization operations for colors including:
 * - Pushing local colors to Supabase
 * - Syncing colors from Supabase to local database
 * - Managing sync status and error recovery
 * - Handling authentication and RLS policies
 */

import Database from 'better-sqlite3';
import { ColorEntry } from '../../../shared/types/color.types';
import { ColorValidator } from './color-validator.service';
import { IColorRepository } from '../../db/repositories/interfaces/color.repository.interface';
// IdMappingManager removed - using direct UUID mapping
// import { randomUUID } from 'crypto';

// Types for sync operations
export interface ColorSyncResult {
  success: boolean;
  syncedCount: number;
  errors: string[];
  warnings: string[];
}

export interface ColorSyncOptions {
  batchSize?: number;
  retryAttempts?: number;
  skipValidation?: boolean;
  forceUpdate?: boolean;
}

export interface SupabaseColorData {
  id: string; // Direct UUID - same in local SQLite and Supabase
  organization_id: string;
  user_id: string;
  display_name: string;
  code: string;
  hex: string;
  color_spaces: {
    cmyk?: { c: number; m: number; y: number; k: number };
    rgb?: { r: number; g: number; b: number };
    hsl?: { h: number; s: number; l: number };
    lab?: { l: number; a: number; b: number };
  };
  is_gradient: boolean;
  gradient_colors?: string | null;
  notes?: string | null;
  tags?: string | null;
  is_library: boolean;
  updated_at: string;
  created_at?: string;
}

/**
 * ColorSyncService handles all color synchronization operations with Supabase
 *
 * This service is responsible for:
 * - Maintaining sync state between local SQLite and Supabase
 * - Handling authentication requirements for RLS policies
 * - Managing error recovery and retry logic
 * - Validating data consistency during sync operations
 */
export class ColorSyncService {
  private colorValidator: ColorValidator;
  // IdMappingManager removed - using direct UUID mapping

  constructor(
    private db: Database.Database,
    private colorRepository: IColorRepository,
    colorValidator?: ColorValidator
  ) {
    this.colorValidator = colorValidator || new ColorValidator();
    // IdMappingManager initialization removed - using direct UUID mapping
  }

  // =============================================================================
  // PUSH OPERATIONS (Local to Supabase)
  // =============================================================================

  /**
   * Push a single color to Supabase
   * @param colorId - UUID of color to push
   * @param organizationId - Organization ID for RLS
   * @param userId - User ID for authentication
   * @returns Promise resolving when push completes
   */
  async pushColorToSupabase(
    colorId: string,
    organizationId: string,
    userId?: string
  ): Promise<void> {
    try {
      // Ensure authenticated session for RLS policies
      const { getSupabaseClient, ensureAuthenticatedSession } = await import(
        '../../services/supabase-client'
      );
      const { session, error: sessionError } =
        await ensureAuthenticatedSession();

      if (!session || sessionError) {
        console.error(
          '[ColorSyncService] Cannot sync to Supabase - no authenticated session:',
          sessionError
        );
        throw new Error('No authenticated session');
      }

      // Get color data from local repository
      const color = this.getColorForSync(colorId, organizationId);
      if (!color) {
        console.error(
          `[ColorSyncService] Could not find color ${colorId} to push to Supabase.`
        );
        throw new Error(`Color ${colorId} not found`);
      }

      // Validate color data for sync
      const validationResult = this.colorValidator.validateForSync({
        hex: color.hex,
        cmyk: color.cmyk,
        name: color.name,
        code: color.code,
        externalId: color.id,
      });

      if (!validationResult.isValid) {
        throw new Error(
          `Color validation failed: ${validationResult.errors.join(', ')}`
        );
      }

      // Prepare data for Supabase
      const supabaseData = this.prepareColorForSupabase(
        color,
        organizationId,
        userId || session.user.id
      );

      // Push to Supabase - upsert on id to avoid duplicate UUIDs
      const supabase = getSupabaseClient();
      const { error } = await supabase
        .from('colors')
        .upsert(supabaseData, { onConflict: 'id' });

      if (error) {
        console.error(
          `[ColorSyncService] Error pushing color ${colorId} to Supabase:`,
          error
        );
        throw error;
      }

      // Mark as synced in local database
      this.colorRepository.markAsSynced(colorId);
      console.log(
        `[ColorSyncService] Successfully pushed color ${colorId} to Supabase.`
      );
    } catch (error) {
      console.error(`[ColorSyncService] Error in pushColorToSupabase:`, error);
      throw error;
    }
  }

  /**
   * Push multiple colors to Supabase in batches
   * @param colorIds - Array of color IDs to push
   * @param organizationId - Organization ID for RLS
   * @param options - Sync options
   * @returns Sync result with success count and errors
   */
  async pushColorsToSupabase(
    colorIds: string[],
    organizationId: string,
    options: ColorSyncOptions = {}
  ): Promise<ColorSyncResult> {
    const { batchSize = 50, retryAttempts = 3 } = options;
    const errors: string[] = [];
    let syncedCount = 0;

    // Process in batches to avoid overwhelming Supabase
    for (let i = 0; i < colorIds.length; i += batchSize) {
      const batch = colorIds.slice(i, i + batchSize);

      for (const colorId of batch) {
        let attempts = 0;
        let success = false;

        while (attempts < retryAttempts && !success) {
          try {
            await this.pushColorToSupabase(colorId, organizationId);
            syncedCount++;
            success = true;
          } catch (error) {
            attempts++;
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';

            if (attempts === retryAttempts) {
              errors.push(
                `Failed to push color ${colorId} after ${retryAttempts} attempts: ${errorMessage}`
              );
            } else {
              // Wait before retry (exponential backoff)
              await this.delay(Math.pow(2, attempts) * 1000);
            }
          }
        }
      }
    }

    return {
      success: errors.length === 0,
      syncedCount,
      errors,
      warnings: [],
    };
  }

  // =============================================================================
  // PULL OPERATIONS (Supabase to Local)
  // =============================================================================

  /**
   * Sync colors from Supabase to local database
   * @param userId - User ID for authentication
   * @param organizationId - Organization ID to sync
   * @param options - Sync options
   * @returns Array of synced ColorEntry objects
   */
  async syncColorsFromSupabase(
    userId: string,
    organizationId: string,
    options: ColorSyncOptions = {}
  ): Promise<ColorEntry[]> {
    try {
      const { getSupabaseClient, ensureAuthenticatedSession } = await import(
        '../../services/supabase-client'
      );

      console.log(
        `[ColorSyncService] 🔍 Starting color sync for organization: ${organizationId}`
      );

      // CRITICAL: Ensure we have a valid authenticated session before making RLS queries
      const { session, error: sessionError } =
        await ensureAuthenticatedSession();
      if (!session || sessionError) {
        console.error(
          `[ColorSyncService] ❌ No authenticated session available: ${sessionError}`
        );
        console.error(
          `[ColorSyncService] Cannot proceed with sync - RLS policies will block queries`
        );
        return [];
      }

      console.log(
        `[ColorSyncService] ✅ Authenticated session confirmed for user: ${session.user?.email}`
      );

      const supabase = getSupabaseClient();

      // Test basic connectivity with authenticated session
      await this.verifySupabaseConnectivity(supabase);

      // Query colors for the specific organization
      console.log(
        `[ColorSyncService] 🔍 Querying colors for organization: ${organizationId}`
      );
      const { data: colors, error } = await supabase
        .from('colors')
        .select('*')
        .eq('organization_id', organizationId);

      if (error) {
        console.error(
          '[ColorSyncService] Failed to fetch colors from Supabase:',
          error
        );
        return [];
      }

      if (!colors || colors.length === 0) {
        console.log(
          '[ColorSyncService] No colors found in Supabase for organization:',
          organizationId
        );
        return [];
      }

      console.log(
        `[ColorSyncService] Found ${colors.length} colors in Supabase, syncing to local database...`
      );

      // Ensure organization exists locally before syncing colors
      await this.ensureOrganizationExists(organizationId, userId);

      // Sync each color to local database
      const syncedColors: ColorEntry[] = [];
      for (const supabaseColor of colors) {
        try {
          const colorEntry = await this.syncColorToLocal(
            supabaseColor,
            organizationId,
            options
          );
          if (colorEntry) {
            syncedColors.push(colorEntry);
          }
        } catch (error) {
          console.error(
            `[ColorSyncService] Error syncing color ${supabaseColor.code}:`,
            error
          );
        }
      }

      console.log(
        `[ColorSyncService] Successfully synced ${syncedColors.length} colors from Supabase`
      );
      return syncedColors;
    } catch (error) {
      console.error(
        '[ColorSyncService] Failed to sync colors from Supabase:',
        error
      );
      return [];
    }
  }

  // =============================================================================
  // SYNC STATUS MANAGEMENT
  // =============================================================================

  /**
   * Get colors that need to be synced to Supabase
   * @returns Array of unsynced ColorEntry objects
   */
  getUnsyncedColors(): ColorEntry[] {
    try {
      const rows = this.colorRepository.findUnsynced();
      return rows.map(row => this.convertRowToColorEntry(row));
    } catch (error) {
      console.error('[ColorSyncService] Error getting unsynced colors:', error);
      return [];
    }
  }

  /**
   * Mark color as synced in local database
   * @param colorId - UUID of color to mark as synced
   */
  markColorAsSynced(colorId: string): void {
    this.colorRepository.markAsSynced(colorId);
  }

  /**
   * Reset sync status for colors (mark as unsynced)
   * @param colorIds - Array of color IDs to reset
   */
  resetSyncStatus(colorIds: string[]): void {
    for (const colorId of colorIds) {
      // Implementation would depend on repository interface
      // For now, this is a placeholder
      console.log(`[ColorSyncService] Reset sync status for color: ${colorId}`);
    }
  }

  // =============================================================================
  // HELPER METHODS
  // =============================================================================

  /**
   * Get color data formatted for sync operations
   * @param colorId - UUID of color
   * @param organizationId - Organization ID
   * @returns Color data or null if not found
   */
  private getColorForSync(
    colorId: string,
    organizationId: string
  ): ColorEntry | null {
    const row = this.colorRepository.findById(colorId, organizationId);
    if (!row) {return null;}

    return this.convertRowToColorEntry(row);
  }

  /**
   * Convert database row to ColorEntry object
   * @param row - Database row
   * @returns ColorEntry object
   */
  private convertRowToColorEntry(row: any): ColorEntry {
    // Parse color spaces from JSON column
    let colorSpaces: any = {};
    if (row.color_spaces) {
      try {
        colorSpaces =
          typeof row.color_spaces === 'string'
            ? JSON.parse(row.color_spaces)
            : row.color_spaces;
      } catch (error) {
        console.warn(
          '[ColorSyncService] Failed to parse color_spaces JSON:',
          error
        );
        colorSpaces = {};
      }
    }

    // Extract CMYK from JSON
    let cmyk = 'C:0 M:0 Y:0 K:0';
    if (colorSpaces.cmyk) {
      const cmykObj = colorSpaces.cmyk;
      cmyk = `C:${cmykObj.c || 0} M:${cmykObj.m || 0} Y:${cmykObj.y || 0} K:${cmykObj.k || 0}`;
    }

    // Parse gradient data from gradient_colors CSV field OR color_spaces.gradient
    let gradientInfo = null;
    if (row.is_gradient) {
      if (row.gradient_colors) {
        // Use gradient_colors CSV field (preferred)
        gradientInfo = this.parseGradientColorsCSV(row.gradient_colors);
      } else if (colorSpaces.gradient) {
        // Fall back to color_spaces.gradient (legacy)
        gradientInfo = colorSpaces.gradient;
      }
    }

    return {
      id: row.id,
      source: row.source || 'user',
      code: row.code,
      name: row.display_name || row.code,
      hex: row.hex,
      cmyk,
      rgb: colorSpaces.rgb,
      hsl: colorSpaces.hsl,
      lab: colorSpaces.lab,
      product: row.product_name || '',
      gradient: gradientInfo,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      organizationId: row.organization_id,
      isLibrary: row.is_library === 1,
      notes: row.notes,
      tags: row.tags,
    };
  }

  /**
   * Prepare color data for Supabase insertion/update
   * @param color - ColorEntry to prepare
   * @param organizationId - Organization ID
   * @param userId - User ID
   * @returns Formatted data for Supabase
   */
  private prepareColorForSupabase(
    color: ColorEntry,
    organizationId: string,
    userId: string
  ): SupabaseColorData {
    return {
      id: color.id, // Direct UUID - same in local and Supabase
      organization_id: organizationId,
      user_id: userId,
      display_name: color.name,
      code: color.code,
      hex: color.hex,
      color_spaces: {
        cmyk: this.parseCMYKForSupabase(color.cmyk) ?? undefined,
        rgb: color.rgb,
        hsl: color.hsl,
        lab: color.lab,
      },
      is_gradient: !!color.gradient,
      gradient_colors: color.gradient
        ? this.createGradientColorsFromGradientInfo(color.gradient)
        : null,
      notes: color.notes || null,
      tags: color.tags || null,
      is_library: color.isLibrary || false,
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Parse gradient colors CSV back to GradientInfo structure
   * @param gradientColorsCSV - CSV string of gradient colors
   * @returns GradientInfo object or null
   */
  private parseGradientColorsCSV(gradientColorsCSV: string): any {
    if (!gradientColorsCSV || gradientColorsCSV.trim() === '') {
      return null;
    }

    const colorEntries = gradientColorsCSV
      .split(',')
      .map(entry => entry.trim());
    const colors: string[] = [];
    const colorCodes: string[] = [];

    for (const entry of colorEntries) {
      if (entry.includes('|')) {
        // Format: "color|colorCode"
        const [color, colorCode] = entry.split('|');
        if (color) {colors.push(color.trim());}
        if (colorCode) {colorCodes.push(colorCode.trim());}
      } else {
        // Just a color without code
        colors.push(entry);
        colorCodes.push(''); // Empty string to maintain array alignment
      }
    }

    return {
      colors,
      colorCodes: colorCodes.some(code => code !== '') ? colorCodes : undefined,
      type: 'linear',
      angle: 45,
    };
  }

  /**
   * Create gradient colors CSV from GradientInfo structure
   * @param gradientInfo - GradientInfo to convert
   * @returns CSV string of gradient colors with optional color codes
   */
  private createGradientColorsFromGradientInfo(gradientInfo: any): string {
    if (
      !gradientInfo ||
      !gradientInfo.colors ||
      gradientInfo.colors.length === 0
    ) {
      console.warn(
        '[ColorSyncService] Empty or invalid gradient info provided'
      );
      return '';
    }

    const colors = gradientInfo.colors;
    const colorCodes = gradientInfo.colorCodes || [];

    return colors
      .map((color: string, index: number) => {
        const colorCode = colorCodes[index];
        if (colorCode) {
          return `${color}|${colorCode}`;
        }
        return color;
      })
      .join(',');
  }

  /**
   * Parse CMYK string for Supabase storage
   * @param cmyk - CMYK string to parse
   * @returns Parsed CMYK object or null
   */
  private parseCMYKForSupabase(
    cmyk: string
  ): { c: number; m: number; y: number; k: number } | null {
    if (!cmyk) {return null;}

    const cmykValidation = this.colorValidator.validateCMYK(cmyk, {
      allowEmpty: true,
    });
    if (!cmykValidation.isValid) {
      return null;
    }

    const standardized = this.colorValidator.standardizeCMYK(cmyk);
    const match = standardized.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/);
    if (!match) {
      return null;
    }

    return {
      c: parseInt(match[1] ?? '0', 10),
      m: parseInt(match[2] ?? '0', 10),
      y: parseInt(match[3] ?? '0', 10),
      k: parseInt(match[4] ?? '0', 10),
    };
  }

  /**
   * Sync individual color from Supabase to local database
   * @param supabaseColor - Color data from Supabase
   * @param organizationId - Organization ID
   * @param options - Sync options
   * @returns Synced ColorEntry or null
   */
  private async syncColorToLocal(
    supabaseColor: any,
    organizationId: string,
    options: ColorSyncOptions
  ): Promise<ColorEntry | null> {
    try {
      // Use UUID directly from Supabase (no more integer->UUID mapping needed)
      const localUUID = supabaseColor.id;

      // Check if this UUID exists in the database
      const existingStmt = this.db.prepare(`
        SELECT id FROM colors WHERE id = ? AND organization_id = ?
      `);
      const existing = existingStmt.get(localUUID, organizationId);

      if (existing && !options.forceUpdate) {
        // Update existing color using the correct local UUID
        const success = this.colorRepository.update(
          localUUID,
          this.prepareColorForLocal(supabaseColor),
          organizationId
        );

        if (success) {
          console.log(
            `[ColorSyncService] ✅ Updated existing local color: ${supabaseColor.code} (UUID: ${localUUID})`
          );
        } else {
          console.error(
            `[ColorSyncService] ❌ Failed to update local color: ${supabaseColor.code}`
          );
        }
      } else {
        // Insert new color (only if it truly doesn't exist)
        const colorData = this.prepareColorForLocal(supabaseColor);
        this.colorRepository.insert(colorData, organizationId);
        console.log(
          `[ColorSyncService] ✅ Inserted new color: ${supabaseColor.code} (UUID: ${localUUID})`
        );
      }

      // Convert to ColorEntry format for return
      return this.convertSupabaseToColorEntry(supabaseColor, organizationId);
    } catch (error) {
      console.error(
        `[ColorSyncService] Error syncing color ${supabaseColor.code}:`,
        error
      );
      return null;
    }
  }

  /**
   * Prepare Supabase color data for local database insertion
   * @param supabaseColor - Color data from Supabase
   * @returns Formatted data for local database
   */
  private prepareColorForLocal(supabaseColor: any): any {
    // Use UUID directly from Supabase
    const localUUID = supabaseColor.id;

    console.log(
      `[ColorSyncService] 🔄 Using Supabase UUID ${localUUID} for color ${supabaseColor.code}`
    );

    return {
      id: localUUID, // Use UUID directly from Supabase
      code: supabaseColor.code,
      display_name: supabaseColor.display_name || supabaseColor.name,
      hex: supabaseColor.hex,
      color_spaces: JSON.stringify(supabaseColor.color_spaces || {}),
      is_gradient: supabaseColor.is_gradient || false,
      gradient_colors: supabaseColor.gradient_colors || null,
      notes: supabaseColor.notes,
      tags: supabaseColor.tags,
      is_library: supabaseColor.is_library || false,
      is_synced: true,
      created_at: supabaseColor.created_at,
      updated_at: supabaseColor.updated_at,
    };
  }

  /**
   * Convert Supabase color data to ColorEntry
   * @param supabaseColor - Color data from Supabase
   * @param organizationId - Organization ID
   * @returns ColorEntry object
   */
  private convertSupabaseToColorEntry(
    supabaseColor: any,
    organizationId: string
  ): ColorEntry {
    // Use UUID directly from Supabase
    const localUUID = supabaseColor.id;

    // Parse gradient data from gradient_colors CSV field
    let gradientInfo = null;
    if (supabaseColor.is_gradient && supabaseColor.gradient_colors) {
      gradientInfo = this.parseGradientColorsCSV(supabaseColor.gradient_colors);
    }

    return {
      id: localUUID, // Use UUID directly from Supabase
      source: 'supabase',
      code: supabaseColor.code,
      name: supabaseColor.display_name || supabaseColor.name,
      hex: supabaseColor.hex,
      cmyk: this.formatCMYKFromSupabase(supabaseColor.color_spaces?.cmyk),
      rgb: supabaseColor.color_spaces?.rgb,
      hsl: supabaseColor.color_spaces?.hsl,
      lab: supabaseColor.color_spaces?.lab,
      product: '',
      gradient: gradientInfo,
      createdAt: supabaseColor.created_at,
      updatedAt: supabaseColor.updated_at,
      organizationId,
      isLibrary: supabaseColor.is_library || false,
      notes: supabaseColor.notes,
      tags: supabaseColor.tags,
    };
  }

  /**
   * Format CMYK object from Supabase to string
   * @param cmyk - CMYK object from Supabase
   * @returns Formatted CMYK string
   */
  private formatCMYKFromSupabase(cmyk: any): string {
    if (!cmyk || typeof cmyk !== 'object') {
      return 'C:0 M:0 Y:0 K:0';
    }

    const c = cmyk.c || 0;
    const m = cmyk.m || 0;
    const y = cmyk.y || 0;
    const k = cmyk.k || 0;

    return `C:${c} M:${m} Y:${y} K:${k}`;
  }

  /**
   * Verify Supabase connectivity with authenticated session
   * @param supabase - Supabase client
   */
  private async verifySupabaseConnectivity(supabase: any): Promise<void> {
    console.log(
      `[ColorSyncService] 🔍 Testing basic Supabase connection with authenticated session...`
    );

    const { count: totalColors, error: countError } = await supabase
      .from('colors')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error(`[ColorSyncService] ❌ Basic query failed:`, countError);
      throw countError;
    }

    console.log(
      `[ColorSyncService] ✅ Total colors accessible: ${totalColors}`
    );
  }

  /**
   * Ensure organization exists locally before syncing colors
   * @param organizationId - Organization ID
   * @param userId - User ID
   */
  private async ensureOrganizationExists(
    organizationId: string,
    userId: string
  ): Promise<void> {
    // With UUID primary keys, check organization by ID directly
    const orgStmt = this.db.prepare(`
      SELECT id FROM organizations WHERE id = ?
    `);
    let localOrg = orgStmt.get(organizationId) as { id: string } | undefined;

    // SYNC GUARD: If organization doesn't exist locally, sync it first
    if (!localOrg) {
      console.log(
        `[ColorSyncService] Organization ${organizationId} not found locally - syncing organizations first`
      );
      try {
        const { OrganizationService } = await import(
          '../../db/services/organization.service'
        );
        const orgService = new OrganizationService(this.db);
        await orgService.syncOrganizationsFromSupabase(userId);

        // Try to get the organization again after sync
        localOrg = orgStmt.get(organizationId) as { id: string } | undefined;

        if (!localOrg) {
          throw new Error(
            `Organization ${organizationId} still not found after sync`
          );
        }

        console.log(
          `[ColorSyncService] ✅ Organization synced successfully, proceeding with color sync`
        );
      } catch (syncError) {
        console.error(
          '[ColorSyncService] Failed to sync organization:',
          syncError
        );
        throw syncError;
      }
    }
  }

  /**
   * Get local UUID for Supabase color ID (direct passthrough since UUIDs are now the same)
   * @param supabaseColorId - Supabase UUID color ID
   * @returns Same UUID (since local and Supabase now use identical UUIDs)
   */
  getLocalUUIDForSupabaseColorId(supabaseColorId: string): string | null {
    return supabaseColorId; // Direct passthrough - no mapping needed
  }

  // Direct UUID operations - no mapping initialization needed

  /**
   * Utility method for delays in retry logic
   * @param ms - Milliseconds to delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // =============================================================================
  // PUBLIC UTILITY METHODS
  // =============================================================================

  /**
   * Get sync service information and capabilities
   * @returns Service metadata
   */
  getServiceInfo(): { name: string; version: string; capabilities: string[] } {
    return {
      name: 'ColorSyncService',
      version: '1.0.0',
      capabilities: [
        'Direct UUID sync operations',
        'Push colors to Supabase',
        'Sync colors from Supabase',
        'Batch sync operations',
        'Retry logic with exponential backoff',
        'Authentication handling',
        'RLS policy compliance',
        'Sync status management',
        'Data validation during sync',
        'Organization dependency management',
        'Error recovery and logging',
      ],
    };
  }
}
