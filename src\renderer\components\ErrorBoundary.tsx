import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home } from 'lucide-react';
import { errorLogger } from '../utils/errorLogger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return { 
      hasError: true, 
      error,
      errorInfo: null 
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      errorInfo
    });

    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to error reporting service
    this.reportError(error, errorInfo);
  }

  private reportError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      // Log to local error logger for debugging and metrics
      errorLogger.logError({
        message: error.message,
        stack: error.stack,
        category: 'ui',
        context: {
          componentStack: errorInfo.componentStack,
          location: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }
      });

      // Send to monitoring service via IPC (production and development)
      if (window.monitoring?.trackError) {
        await window.monitoring.trackError({
          message: error.message,
          stack: error.stack || undefined,
          componentStack: errorInfo.componentStack || undefined,
          location: window.location.href
        });
      }
    } catch (reportingError) {
      // Don't let error reporting failures crash the error boundary
      console.warn('Failed to report error:', reportingError);
    }
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  override render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-ui-background-primary dark:bg-ui-background-primary p-4">
          <div className="max-w-md w-full bg-ui-background-secondary dark:bg-ui-background-secondary rounded-lg shadow-lg p-6 text-center">
            <AlertTriangle 
              className="w-16 h-16 text-feedback-warning mx-auto mb-4" 
              aria-hidden="true"
            />
            
            <h1 className="text-2xl font-semibold text-ui-foreground-primary dark:text-ui-foreground-primary mb-2">
              Oops! Something went wrong
            </h1>
            
            <p className="text-ui-foreground-secondary dark:text-ui-foreground-secondary mb-6">
              We're sorry for the inconvenience. The application encountered an unexpected error.
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mb-6 text-left">
                <summary className="cursor-pointer text-sm text-ui-foreground-tertiary hover:text-ui-foreground-secondary">
                  Error details
                </summary>
                <pre className="mt-2 p-3 bg-ui-background-tertiary dark:bg-ui-background-tertiary rounded text-xs overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo && this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}

            <div className="flex gap-3 justify-center">
              <button
                onClick={this.handleReset}
                className="inline-flex items-center px-4 py-2 bg-brand-primary text-white rounded-md hover:bg-brand-primary-dark transition-colors"
                aria-label="Try again"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </button>
              
              <button
                onClick={this.handleReload}
                className="inline-flex items-center px-4 py-2 bg-ui-background-tertiary dark:bg-ui-background-tertiary text-ui-foreground-primary dark:text-ui-foreground-primary rounded-md hover:bg-ui-background-secondary dark:hover:bg-ui-background-secondary transition-colors"
                aria-label="Reload application"
              >
                <Home className="w-4 h-4 mr-2" />
                Reload App
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Functional component wrapper for easier use
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) {
  return (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  );
}