/**
 * Performance Test Data Generator
 * Generates large datasets for performance testing
 */

import { v4 as uuidv4 } from 'uuid';
import type { ColorEntry } from '../../shared/types';

export interface GeneratorOptions {
  count: number;
  includeGradients?: boolean;
  includeAllColorSpaces?: boolean;
  realWorldDistribution?: boolean;
}

/**
 * Generate a large dataset of test colors
 */
export function generateTestColors(options: GeneratorOptions): ColorEntry[] {
  const {
    count,
    includeGradients = true,
    includeAllColorSpaces = true,
    realWorldDistribution = true,
  } = options;

  const colors: ColorEntry[] = [];
  const startTime = performance.now();

  console.log(`Generating ${count.toLocaleString()} test colors...`);

  // Color distribution based on real-world usage
  const distribution = realWorldDistribution
    ? {
        pantone: 0.4, // 40% Pantone colors
        ral: 0.2, // 20% RAL colors
        ncs: 0.1, // 10% NCS colors
        custom: 0.25, // 25% Custom colors
        gradient: 0.05, // 5% Gradients
      }
    : {
        pantone: 0.25,
        ral: 0.25,
        ncs: 0.25,
        custom: 0.25,
        gradient: 0,
      };

  for (let i = 0; i < count; i++) {
    const rand = Math.random();
    let source: string;
    let isGradient = false;

    // Determine color source based on distribution
    if (rand < distribution.pantone) {
      source = 'pantone';
    } else if (rand < distribution.pantone + distribution.ral) {
      source = 'ral';
    } else if (
      rand <
      distribution.pantone + distribution.ral + distribution.ncs
    ) {
      source = 'ncs';
    } else if (includeGradients && rand > 1 - distribution.gradient) {
      source = 'user';
      isGradient = true;
    } else {
      source = 'user';
    }

    const color = generateSingleColor(
      i,
      source,
      isGradient,
      includeAllColorSpaces
    );
    colors.push(color);

    // Progress reporting
    if (i > 0 && i % 10000 === 0) {
      const elapsed = performance.now() - startTime;
      const rate = i / (elapsed / 1000);
      const remaining = (count - i) / rate;
      console.log(
        `Progress: ${i.toLocaleString()}/${count.toLocaleString()} ` +
          `(${Math.round((i / count) * 100)}%) - ` +
          `${Math.round(rate).toLocaleString()} colors/sec - ` +
          `ETA: ${Math.round(remaining)}s`
      );
    }
  }

  const totalTime = performance.now() - startTime;
  console.log(
    `Generated ${count.toLocaleString()} colors in ${(totalTime / 1000).toFixed(2)}s ` +
      `(${Math.round(count / (totalTime / 1000)).toLocaleString()} colors/sec)`
  );

  return colors;
}

/**
 * Generate a single test color
 */
function generateSingleColor(
  index: number,
  source: string,
  isGradient: boolean,
  includeAllColorSpaces: boolean
): ColorEntry {
  const hex = generateRandomHex();
  const rgb = hexToRgb(hex);

  const baseColor: ColorEntry = {
    id: uuidv4(),
    source,
    code: generateColorCode(source, index),
    name: generateColorName(source, index),
    hex,
    rgb,
    isGradient,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // Add color space data
  if (includeAllColorSpaces && rgb) {
    // Add CMYK
    baseColor.cmyk = rgbToCmyk(rgb);

    // Add HSL
    baseColor.hsl = rgbToHsl(rgb);

    // Add LAB (simplified)
    baseColor.lab = {
      l: Math.random() * 100,
      a: Math.random() * 256 - 128,
      b: Math.random() * 256 - 128,
    };
  }

  // Add gradient stops if needed
  if (isGradient) {
    baseColor.gradientStops = generateGradientStops();
  }

  // Add metadata for testing
  baseColor.metadata = {
    testIndex: index,
    generated: true,
    performanceTest: true,
  };

  return baseColor;
}

/**
 * Generate a random hex color
 */
function generateRandomHex(): string {
  const hex = Math.floor(Math.random() * 16777215)
    .toString(16)
    .padStart(6, '0');
  return `#${hex.toUpperCase()}`;
}

/**
 * Generate color code based on source
 */
function generateColorCode(source: string, index: number): string {
  switch (source) {
    case 'pantone':
      return `PANTONE ${1000 + (index % 9000)} C`;
    case 'ral':
      return `RAL ${1000 + (index % 9000)}`;
    case 'ncs':
      return `NCS S ${1000 + (index % 9000)}-Y90R`;
    default:
      return `CUSTOM-${index.toString().padStart(6, '0')}`;
  }
}

/**
 * Generate color name
 */
function generateColorName(source: string, index: number): string {
  const adjectives = [
    'Bright',
    'Deep',
    'Soft',
    'Bold',
    'Subtle',
    'Vibrant',
    'Muted',
    'Rich',
  ];
  const nouns = [
    'Sky',
    'Ocean',
    'Forest',
    'Sunset',
    'Dawn',
    'Mountain',
    'Desert',
    'Meadow',
  ];

  const adj = adjectives[index % adjectives.length];
  const noun = nouns[Math.floor(index / adjectives.length) % nouns.length];

  return `${adj} ${noun} ${index % 100}`;
}

/**
 * Generate gradient stops
 */
function generateGradientStops() {
  const stopCount = 2 + Math.floor(Math.random() * 3); // 2-4 stops
  const stops = [];

  for (let i = 0; i < stopCount; i++) {
    stops.push({
      position: i / (stopCount - 1),
      color: generateRandomHex(),
    });
  }

  return stops;
}

/**
 * Convert hex to RGB
 */
function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}

/**
 * Convert RGB to CMYK
 */
function rgbToCmyk(rgb: { r: number; g: number; b: number }) {
  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;

  const k = 1 - Math.max(r, g, b);
  const c = (1 - r - k) / (1 - k) || 0;
  const m = (1 - g - k) / (1 - k) || 0;
  const y = (1 - b - k) / (1 - k) || 0;

  return {
    c: Math.round(c * 100),
    m: Math.round(m * 100),
    y: Math.round(y * 100),
    k: Math.round(k * 100),
  };
}

/**
 * Convert RGB to HSL
 */
function rgbToHsl(rgb: { r: number; g: number; b: number }) {
  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = ((g - b) / d + (g < b ? 6 : 0)) / 6;
        break;
      case g:
        h = ((b - r) / d + 2) / 6;
        break;
      case b:
        h = ((r - g) / d + 4) / 6;
        break;
    }
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100),
  };
}
