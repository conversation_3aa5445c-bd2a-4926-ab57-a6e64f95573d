/**
 * @file service-container.ts
 * @description Service container with dependency injection for managing application services
 */

import {
    AsyncResult,
    failure,
    Result,
    success,
} from '../../shared/types/result.types';
import { toError } from '../../shared/types/type-guards';
import { getDatabase } from '../db/database';
import { ColorImportService } from '../db/services/color-import.service';
import { ColorLibraryImportService } from '../db/services/color-library-import.service';
import { ColorLibraryQueryService } from '../db/services/color-library-query.service';
import { ColorService } from '../db/services/color.service';
import { DatasheetService } from '../db/services/datasheet.service';
import { OrganizationService } from '../db/services/organization.service';
import { ProductService } from '../db/services/product.service';
import { ILogger, LoggerFactory } from '../utils/logger.service';
import { AuthErrorRecoveryService } from './auth/auth-error-recovery.service';
import { AuthNotificationService } from './auth/auth-notification.service';
import { AuthenticationManager } from './auth/authentication-manager';
import { CircuitBreakerAuthManager } from './auth/circuit-breaker-auth-manager';
import { OAuthService } from './auth/oauth-service';
import { SessionManager } from './auth/session-manager';
import { EmailSender } from './email/email-sender';
import { ZohoEmailService } from './email/zoho-email.service';
import { ZohoTokenManager } from './email/zoho-token-manager';
// import { unifiedSyncManager } from './sync';

export type ServiceName =
  | 'logger'
  | 'database'
  | 'config'
  | 'authentication'
  | 'session'
  | 'authErrorRecovery'
  // Database Services
  | 'colorService'
  | 'productService'
  | 'datasheetService'
  | 'organizationService'
  | 'colorImportService'
  | 'colorLibraryImportService'
  | 'colorLibraryQueryService'
  // Auth Services
  | 'authenticationManager'
  | 'sessionManager'
  | 'circuitBreakerAuthManager'
  | 'authNotification'
  | 'oauthService'
  // Email Services
  | 'zohoTokenManager'
  | 'emailSender'
  | 'zohoEmailService'
  // Sync Services
  | 'unifiedSyncManager';

export interface ServiceConfiguration {
  // Core services
  database?: any;
  config?: {
    zoho?: {
      clientId?: string;
      clientSecret?: string;
      refreshToken?: string;
      accountId?: string;
      region?: string;
      supportAlias?: string;
    };
    supabase?: {
      url?: string;
      anonKey?: string;
    };
  };
  // Authentication services
  authentication?: {
    authTimeoutMs?: number;
    useLocalRedirect?: boolean;
    enableCircuitBreaker?: boolean;
    enableRetryLogic?: boolean;
  };
  session?: {
    sessionTimeoutHours?: number;
    sessionWarningMinutes?: number;
    autoLogoutEnabled?: boolean;
    activityCheckIntervalMs?: number;
  };
  authErrorRecovery?: {
    failureThreshold?: number;
    cooldownMinutes?: number;
    recoveryAttempts?: number;
    windowMinutes?: number;
    maxRetries?: number;
    baseDelayMs?: number;
    maxDelayMs?: number;
    backoffMultiplier?: number;
  };
  // Email services
  zohoTokenManager?: {
    refreshCooldownMs?: number;
    maxRetryAttempts?: number;
    baseRetryDelayMs?: number;
    maxRetryDelayMs?: number;
    circuitBreakerFailures?: number;
    circuitBreakerTimeoutMs?: number;
  };
  emailSender?: {
    maxRetryAttempts?: number;
    baseRetryDelayMs?: number;
    maxRetryDelayMs?: number;
    requestTimeoutMs?: number;
    enableRetryQueue?: boolean;
  };
}

export interface ServiceHealth {
  serviceName: string;
  healthy: boolean;
  status: any;
  dependencies: string[];
  lastChecked: number;
}

export interface ContainerHealth {
  healthy: boolean;
  services: ServiceHealth[];
  issues: string[];
}

/**
 * Service container result types using Result pattern
 */
export type ServiceResult<T> = Result<T, ServiceError>;

/**
 * Service container specific error type
 */
export interface ServiceError {
  readonly type:
    | 'INIT_FAILED'
    | 'SERVICE_NOT_FOUND'
    | 'DEPENDENCY_ERROR'
    | 'CONFIG_ERROR'
    | 'HEALTH_CHECK_FAILED';
  readonly message: string;
  readonly serviceName?: string;
  readonly dependencies?: string[];
}

/**
 * Service Container - Manages service lifecycle and dependencies
 */
export class ServiceContainer {
  private static instance: ServiceContainer | null = null;
  private readonly logger: ILogger;
  private readonly services = new Map<ServiceName, any>();
  private readonly serviceConfig = new Map<ServiceName, any>();
  private readonly dependencyGraph = new Map<ServiceName, ServiceName[]>();
  private initialized: boolean = false;

  private constructor() {
    this.logger = LoggerFactory.getInstance().createLogger('ServiceContainer');
    this.setupDependencyGraph();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Initialize the service container
   */
  async initialize(configuration?: ServiceConfiguration): Promise<void> {
    if (this.initialized) {
      this.logger.debug('Service container already initialized');
      return;
    }

    this.logger.info('Initializing service container', {
      operation: 'initialize',
    });

    try {
      // Apply configuration
      if (configuration) {
        this.configure(configuration);
      }

      // Initialize core services in dependency order
      await this.initializeCoreServices();

      // Initialize authentication services
      await this.initializeAuthServices();

      // Initialize email services
      await this.initializeEmailServices();

      // Initialize sync services
      await this.initializeSyncServices();

      this.initialized = true;

      this.logger.info('Service container initialized successfully', {
        servicesCount: this.services.size,
        operation: 'initialize',
      });
    } catch (error) {
      const serviceError = toError(
        error instanceof Error ? error.message : String(error)
      );
      this.logger.error(
        'Failed to initialize service container',
        serviceError,
        {
          operation: 'initialize',
        }
      );
      throw serviceError;
    }
  }

  /**
   * Get service by name with type safety
   */
  get<T>(serviceName: ServiceName): T {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(
        `Service '${serviceName}' not found. Make sure the container is initialized.`
      );
    }
    return service as T;
  }

  /**
   * Check if service exists
   */
  has(serviceName: ServiceName): boolean {
    return this.services.has(serviceName);
  }

  /**
   * Register a service instance
   */
  register<T>(serviceName: ServiceName, service: T): void {
    this.services.set(serviceName, service);
    this.logger.debug('Service registered', { serviceName });
  }

  /**
   * Configure services
   */
  configure(configuration: ServiceConfiguration): void {
    this.logger.info('Configuring services', {
      configuration,
      operation: 'configure',
    });

    // Store configuration for each service, handling special cases
    Object.entries(configuration).forEach(([key, config]) => {
      if (key === 'database') {
        // Store database instance directly
        this.serviceConfig.set('database', config);
      } else if (key === 'config') {
        // Store general config separately
        this.serviceConfig.set('config' as ServiceName, config);
      } else {
        // Store service-specific configuration
        this.serviceConfig.set(key as ServiceName, config);
      }
    });

    // Apply configuration to already initialized services
    if (this.initialized) {
      this.applyConfigurationToServices(configuration);
    }
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<ContainerHealth> {
    const services: ServiceHealth[] = [];
    const issues: string[] = [];

    for (const [serviceName, service] of this.services.entries()) {
      try {
        const dependencies = this.dependencyGraph.get(serviceName) || [];
        let healthy = true;
        let status: any = { available: true };

        // Check service-specific health if method exists
        if (typeof service.getHealthStatus === 'function') {
          status = service.getHealthStatus();
          healthy = status.healthy !== false;
        } else if (typeof service.getStatus === 'function') {
          status = service.getStatus();
          healthy = status.healthy !== false;
        }

        services.push({
          serviceName,
          healthy,
          status,
          dependencies,
          lastChecked: Date.now(),
        });

        if (!healthy) {
          issues.push(`Service '${serviceName}' is unhealthy`);
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        services.push({
          serviceName,
          healthy: false,
          status: { error: errorMessage },
          dependencies: this.dependencyGraph.get(serviceName) || [],
          lastChecked: Date.now(),
        });
        issues.push(
          `Service '${serviceName}' health check failed: ${errorMessage}`
        );
      }
    }

    const healthy = issues.length === 0;

    return {
      healthy,
      services,
      issues,
    };
  }

  /**
   * Cleanup all services
   */
  async cleanup(): Promise<void> {
    this.logger.info('Cleaning up service container');

    // Cleanup services in reverse dependency order
    const cleanupOrder = this.getCleanupOrder();

    for (const serviceName of cleanupOrder) {
      try {
        const service = this.services.get(serviceName);
        if (service && typeof service.cleanup === 'function') {
          await service.cleanup();
          this.logger.debug('Service cleaned up', { serviceName });
        }
      } catch (error) {
        const serviceError = toError(
          error instanceof Error ? error.message : String(error)
        );
        this.logger.error('Service cleanup failed', serviceError, {
          serviceName,
        });
      }
    }

    this.services.clear();
    this.serviceConfig.clear();
    this.initialized = false;

    this.logger.info('Service container cleanup completed');
  }

  /**
   * Initialize service container using Result pattern
   */
  async initializeResult(
    configuration?: ServiceConfiguration
  ): AsyncResult<void, ServiceError> {
    try {
      await this.initialize(configuration);
      return success(undefined);
    } catch (error) {
      return failure({
        type: 'INIT_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Get service by name using Result pattern
   */
  getResult<T>(serviceName: ServiceName): ServiceResult<T> {
    try {
      const service = this.services.get(serviceName);
      if (!service) {
        return failure({
          type: 'SERVICE_NOT_FOUND',
          message: `Service '${serviceName}' not found. Make sure the container is initialized.`,
          serviceName,
        });
      }
      return success(service as T);
    } catch (error) {
      return failure({
        type: 'SERVICE_NOT_FOUND',
        message: error instanceof Error ? error.message : String(error),
        serviceName,
      });
    }
  }

  /**
   * Get service health status using Result pattern
   */
  async getHealthStatusResult(): AsyncResult<ContainerHealth, ServiceError> {
    try {
      const result = await this.getHealthStatus();
      return success(result);
    } catch (error) {
      return failure({
        type: 'HEALTH_CHECK_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Cleanup all services using Result pattern
   */
  async cleanupResult(): AsyncResult<void, ServiceError> {
    try {
      await this.cleanup();
      return success(undefined);
    } catch (error) {
      return failure({
        type: 'INIT_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Get all registered service names
   */
  getServiceNames(): ServiceName[] {
    return Array.from(this.services.keys());
  }

  /**
   * Get service dependency graph
   */
  getDependencyGraph(): Map<ServiceName, ServiceName[]> {
    return new Map(this.dependencyGraph);
  }

  // Private methods

  /**
   * Setup service dependency graph
   */
  private setupDependencyGraph(): void {
    // Core services (no dependencies)
    this.dependencyGraph.set('logger', []);
    this.dependencyGraph.set('database', ['logger']);

    // Database services
    this.dependencyGraph.set('colorService', ['database', 'logger']);
    this.dependencyGraph.set('productService', ['database', 'logger']);
    this.dependencyGraph.set('datasheetService', ['database', 'logger']);
    this.dependencyGraph.set('organizationService', ['database', 'logger']);
    this.dependencyGraph.set('colorImportService', ['database', 'colorService', 'logger']);
    this.dependencyGraph.set('colorLibraryImportService', ['logger']);
    this.dependencyGraph.set('colorLibraryQueryService', ['logger']);

    // Authentication services
    this.dependencyGraph.set('authenticationManager', ['logger']);
    this.dependencyGraph.set('sessionManager', ['logger']);
    this.dependencyGraph.set('authErrorRecovery', ['logger']);
    this.dependencyGraph.set('circuitBreakerAuthManager', ['authenticationManager', 'sessionManager', 'authErrorRecovery', 'logger']);
    this.dependencyGraph.set('authNotification', ['logger']);
    this.dependencyGraph.set('oauthService', [
      'authenticationManager',
      'sessionManager',
      'authErrorRecovery',
      'authNotification',
      'database',
      'logger',
    ]);

    // Email services
    this.dependencyGraph.set('zohoTokenManager', ['logger']);
    this.dependencyGraph.set('emailSender', ['zohoTokenManager', 'logger']);
    this.dependencyGraph.set('zohoEmailService', [
      'zohoTokenManager',
      'emailSender',
      'logger',
    ]);

    // Sync services
    this.dependencyGraph.set('unifiedSyncManager', [
      'database',
      'oauthService',
      'logger',
    ]);
  }

  /**
   * Initialize core services
   */
  private async initializeCoreServices(): Promise<void> {
    this.logger.debug('Initializing core services');

    // Logger (already initialized as singleton)
    this.register('logger', this.logger);

    // Database - use from configuration if provided, otherwise fallback to getDatabase()
    const configDatabase = this.serviceConfig.get('database');
    const database = configDatabase || getDatabase();
    this.register('database', database);

    // Database services
    if (database) {
      console.log('[ServiceContainer] Initializing database services with valid database connection');
      try {
        // Core data services
        const colorService = new ColorService(database); // Dependencies injected automatically in constructor
        const productService = new ProductService(database);
        const datasheetService = new DatasheetService(database);
        const organizationService = new OrganizationService(database);

        // Import/library services
        const colorImportService = new ColorImportService(database, colorService);
        const colorLibraryImportService = new ColorLibraryImportService();
        const colorLibraryQueryService = new ColorLibraryQueryService();

        // Register all database services
        this.register('colorService', colorService);
        this.register('productService', productService);
        this.register('datasheetService', datasheetService);
        this.register('organizationService', organizationService);
        this.register('colorImportService', colorImportService);
        this.register('colorLibraryImportService', colorLibraryImportService);
        this.register('colorLibraryQueryService', colorLibraryQueryService);

        this.logger.debug('Database services initialized', {
          services: ['colorService', 'productService', 'datasheetService', 'organizationService', 'colorImportService', 'colorLibraryImportService', 'colorLibraryQueryService']
        });
      } catch (error) {
        console.error('[ServiceContainer] Failed to initialize database services:', error);
        this.logger.error('Database service initialization failed', error instanceof Error ? error : new Error(String(error)));
        throw new Error(`Database service initialization failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    } else {
      console.error('[ServiceContainer] Database is null/undefined - cannot initialize database services');
      this.logger.error('Database not available, cannot initialize database services');
      throw new Error('Database not available - cannot initialize database services. Make sure database initialization completed successfully.');
    }
  }

  /**
   * Initialize authentication services
   */
  private async initializeAuthServices(): Promise<void> {
    this.logger.debug('Initializing authentication services');

    // Authentication Manager
    const authManager = new AuthenticationManager(this.logger);
    this.register('authenticationManager', authManager);

    // Session Manager
    const sessionManager = new SessionManager(this.logger);
    const sessionConfig = this.serviceConfig.get('session');
    if (sessionConfig) {
      sessionManager.configure(sessionConfig);
    }
    this.register('sessionManager', sessionManager);

    // Auth Error Recovery Service
    const authErrorRecovery = new AuthErrorRecoveryService(this.logger);
    const errorRecoveryConfig = this.serviceConfig.get('authErrorRecovery');
    if (errorRecoveryConfig) {
      authErrorRecovery.configureCircuitBreaker(errorRecoveryConfig);
      authErrorRecovery.configureRetry(errorRecoveryConfig);
    }
    this.register('authErrorRecovery', authErrorRecovery);

    // Circuit Breaker Auth Manager (enhanced auth manager)
    const circuitBreakerAuthManager = new CircuitBreakerAuthManager(this.logger);
    const authConfig = this.serviceConfig.get('authentication');
    if (authConfig || sessionConfig || errorRecoveryConfig) {
      circuitBreakerAuthManager.configure({
        circuitBreaker: errorRecoveryConfig,
        retry: errorRecoveryConfig,
        session: sessionConfig,
        networkInterruption: authConfig?.networkInterruption,
        sessionValidation: authConfig?.sessionValidation
      });
    }
    this.register('circuitBreakerAuthManager', circuitBreakerAuthManager);

    // Auth Notification Service
    const authNotification = new AuthNotificationService(this.logger);
    this.register('authNotification', authNotification);

    // OAuth Service (orchestrates other auth services)
    const oauthService = new OAuthService(this.logger);
    if (authConfig) {
      oauthService.configure({
        ...authConfig,
        sessionConfig,
      });
    }
    this.register('oauthService', oauthService);
  }

  /**
   * Initialize email services
   */
  private async initializeEmailServices(): Promise<void> {
    this.logger.debug('Initializing email services');

    try {
      // Zoho Token Manager
      const tokenManager = new ZohoTokenManager(this.logger);
      const tokenConfig = this.serviceConfig.get('zohoTokenManager');
      if (tokenConfig) {
        tokenManager.configure(tokenConfig);
      }
      await tokenManager.initialize();
      this.register('zohoTokenManager', tokenManager);

      // Email Sender
      const emailSender = new EmailSender(tokenManager, this.logger);
      const emailConfig = this.serviceConfig.get('emailSender');
      if (emailConfig) {
        emailSender.configure(emailConfig);
      }
      this.register('emailSender', emailSender);

      // Zoho Email Service (orchestrates email services)
      const zohoEmailService = new ZohoEmailService(this.logger);
      await zohoEmailService.initialize();

      const serviceConfig = {
        tokenManager: tokenConfig,
        emailSender: emailConfig,
      };
      if (tokenConfig || emailConfig) {
        zohoEmailService.configure(serviceConfig);
      }

      this.register('zohoEmailService', zohoEmailService);
    } catch (error) {
      const _errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.warn(
        'Email services initialization failed, continuing without email',
        {
          error: error as Error,
        }
      );
      // Email services are optional, continue without them
    }
  }

  /**
   * Initialize sync services
   */
  private async initializeSyncServices(): Promise<void> {
    this.logger.debug('Initializing sync services');

    try {
      // Register unified sync manager (lazy initialization - will be properly initialized when user authenticates)
      const { unifiedSyncManager } = await import(
        './sync/unified-sync-manager'
      );
      this.register('unifiedSyncManager', unifiedSyncManager);

      this.logger.debug(
        'Unified sync manager registered (lazy initialization)'
      );
    } catch (error) {
      const _errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.warn(
        'Sync services initialization failed, continuing without sync',
        {
          error: error as Error,
        }
      );
      // Sync services are optional, continue without them
    }
  }

  /**
   * Apply configuration to already initialized services
   */
  private applyConfigurationToServices(
    configuration: ServiceConfiguration
  ): void {
    // Configure OAuth service
    if (configuration.authentication || configuration.session) {
      const oauthService = this.services.get('oauthService');
      if (oauthService) {
        oauthService.configure({
          ...configuration.authentication,
          sessionConfig: configuration.session,
        });
      }
    }

    // Configure session manager
    if (configuration.session) {
      const sessionManager = this.services.get('sessionManager');
      if (sessionManager) {
        sessionManager.configure(configuration.session);
      }
    }

    // Configure auth error recovery
    if (configuration.authErrorRecovery) {
      const authErrorRecovery = this.services.get('authErrorRecovery');
      if (authErrorRecovery) {
        authErrorRecovery.configureCircuitBreaker(
          configuration.authErrorRecovery
        );
        authErrorRecovery.configureRetry(configuration.authErrorRecovery);
      }
    }

    // Configure email services
    if (configuration.zohoTokenManager) {
      const tokenManager = this.services.get('zohoTokenManager');
      if (tokenManager) {
        tokenManager.configure(configuration.zohoTokenManager);
      }
    }

    if (configuration.emailSender) {
      const emailSender = this.services.get('emailSender');
      if (emailSender) {
        emailSender.configure(configuration.emailSender);
      }
    }

    if (configuration.zohoTokenManager || configuration.emailSender) {
      const zohoEmailService = this.services.get('zohoEmailService');
      if (zohoEmailService) {
        zohoEmailService.configure({
          tokenManager: configuration.zohoTokenManager,
          emailSender: configuration.emailSender,
        });
      }
    }
  }

  /**
   * Get cleanup order (reverse of dependency order)
   */
  private getCleanupOrder(): ServiceName[] {
    const order: ServiceName[] = [];
    const visited = new Set<ServiceName>();

    const visit = (serviceName: ServiceName) => {
      if (visited.has(serviceName)) {
        return;
      }
      visited.add(serviceName);

      const dependencies = this.dependencyGraph.get(serviceName) || [];
      dependencies.forEach(dep => visit(dep));

      order.push(serviceName);
    };

    // Visit all services
    this.services.forEach((_, serviceName) => visit(serviceName));

    // Reverse for cleanup order
    return order.reverse();
  }
}

// Export singleton instance
export const serviceContainer = ServiceContainer.getInstance();
