/**
 * @file ImportExportSection.tsx
 * @description Import/Export preferences section component
 */

import React from 'react';
import { useSettingsStore } from '../../../store/settings.store';

/**
 * Import/Export preferences section component
 */
export const ImportExportSection: React.FC = () => {
  const settings = useSettingsStore();

  return (
    <section>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        Import/Export Preferences
      </h3>
      <div className='space-y-4'>
        {/* Default Import Format */}
        <div>
          <label className='block text-ui-foreground-primary dark:text-white mb-2'>
            Default Import Format
          </label>
          <div className='flex items-center space-x-4'>
            <label className='flex items-center cursor-pointer'>
              <input
                type='radio'
                className='form-radio h-4 w-4 text-brand-primary'
                checked={settings.defaultImportFormat === 'json'}
                onChange={() => settings.setDefaultImportFormat('json')}
              />
              <span className='ml-2 text-ui-foreground-primary dark:text-gray-300'>
                JSON
              </span>
            </label>
            <label className='flex items-center cursor-pointer'>
              <input
                type='radio'
                className='form-radio h-4 w-4 text-brand-primary'
                checked={settings.defaultImportFormat === 'csv'}
                onChange={() => settings.setDefaultImportFormat('csv')}
              />
              <span className='ml-2 text-ui-foreground-primary dark:text-gray-300'>
                CSV
              </span>
            </label>
          </div>
        </div>

        {/* Default Export Format */}
        <div>
          <label className='block text-ui-foreground-primary dark:text-white mb-2'>
            Default Export Format
          </label>
          <div className='flex items-center space-x-4'>
            <label className='flex items-center cursor-pointer'>
              <input
                type='radio'
                className='form-radio h-4 w-4 text-brand-primary'
                checked={settings.defaultExportFormat === 'json'}
                onChange={() => settings.setDefaultExportFormat('json')}
              />
              <span className='ml-2 text-ui-foreground-primary dark:text-gray-300'>
                JSON
              </span>
            </label>
            <label className='flex items-center cursor-pointer'>
              <input
                type='radio'
                className='form-radio h-4 w-4 text-brand-primary'
                checked={settings.defaultExportFormat === 'csv'}
                onChange={() => settings.setDefaultExportFormat('csv')}
              />
              <span className='ml-2 text-ui-foreground-primary dark:text-gray-300'>
                CSV
              </span>
            </label>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ImportExportSection;
