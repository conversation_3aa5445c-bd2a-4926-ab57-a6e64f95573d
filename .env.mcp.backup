# Supabase credentials
SUPABASE_URL=https://tzqnxhsnitogmtihhsrq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM4MzIxNiwiZXhwIjoyMDYzOTU5MjE2fQ.b6LPM_pAKVJs-QH_VmMQii9-mMJIzRe14pX-3Fjxvb4
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM4MzIxNiwiZXhwIjoyMDYzOTU5MjE2fQ.b6LPM_pAKVJs-QH_VmMQii9-mMJIzRe14pX-3Fjxvb4

# MCP server configuration (optional)
MCP_SERVER_PORT=3000
MCP_SERVER_HOST=localhost
MCP_API_KEY=chromasync-mcp-secret-key