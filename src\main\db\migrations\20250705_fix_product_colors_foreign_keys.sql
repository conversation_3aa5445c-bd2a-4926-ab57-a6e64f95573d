-- Migration: Fix product_colors foreign key types to match UUID primary keys
-- Date: 2025-07-05
-- Purpose: Update product_colors table to use TEXT foreign keys instead of INTEGER

BEGIN TRANSACTION;

-- Create a new product_colors table with correct foreign key types
CREATE TABLE product_colors_new (
    product_id TEXT NOT NULL,
    color_id TEXT NOT NULL,
    display_order INTEGER NOT NULL DEFAULT 0,
    organization_id TEXT NOT NULL,
    added_at TEXT DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (product_id, color_id),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (color_id) REFERENCES colors(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);

-- Copy any existing data that has valid UUID references
-- Note: Since the old table used INTEGER foreign keys, existing data may not be valid
INSERT INTO product_colors_new (product_id, color_id, display_order, organization_id, added_at)
SELECT 
    CAST(product_id AS TEXT),
    CAST(color_id AS TEXT), 
    display_order,
    organization_id,
    added_at
FROM product_colors
WHERE 
    -- Only copy rows where the foreign keys exist as UUIDs in the target tables
    CAST(product_id AS TEXT) IN (SELECT id FROM products) AND
    CAST(color_id AS TEXT) IN (SELECT id FROM colors);

-- Drop the old table
DROP TABLE product_colors;

-- Rename the new table
ALTER TABLE product_colors_new RENAME TO product_colors;

-- Create indexes for performance
CREATE INDEX idx_product_colors_product ON product_colors(product_id);
CREATE INDEX idx_product_colors_color ON product_colors(color_id);
CREATE INDEX idx_product_colors_org ON product_colors(organization_id);
CREATE INDEX idx_product_colors_org_product ON product_colors(organization_id, product_id);
CREATE INDEX idx_product_colors_org_color ON product_colors(organization_id, color_id);

COMMIT;

-- Verification query
SELECT 'Migration completed. Product-color relationships: ' || COUNT(*) FROM product_colors;