/**
 * Tests for color analysis utilities using colord
 */

import { describe, it, expect } from 'vitest';
import {
  calculateRelativeLuminance,
  calculateContrastRatio,
  calculateDeltaE,
  calculateDeltaERgb,
  checkAccessibility,
  getWcagCompliance,
  interpretDeltaE,
} from '../analysis';
import { RGB, LAB } from '../types';

describe('Color Analysis', () => {
  describe('Luminance Calculations', () => {
    it('should calculate correct luminance for black', () => {
      const black: RGB = { r: 0, g: 0, b: 0 };
      expect(calculateRelativeLuminance(black)).toBeCloseTo(0, 5);
    });

    it('should calculate correct luminance for white', () => {
      const white: RGB = { r: 255, g: 255, b: 255 };
      expect(calculateRelativeLuminance(white)).toBeCloseTo(1, 5);
    });

    it('should calculate correct luminance for mid gray', () => {
      const gray: RGB = { r: 128, g: 128, b: 128 };
      const luminance = calculateRelativeLuminance(gray);
      expect(luminance).toBeGreaterThan(0.2);
      expect(luminance).toBeLessThan(0.25);
    });
  });

  describe('Contrast Ratio Calculations', () => {
    it('should calculate maximum contrast for black on white', () => {
      const black: RGB = { r: 0, g: 0, b: 0 };
      const white: RGB = { r: 255, g: 255, b: 255 };
      expect(calculateContrastRatio(black, white)).toBeCloseTo(21, 1);
    });

    it('should calculate minimum contrast for same colors', () => {
      const color: RGB = { r: 128, g: 128, b: 128 };
      expect(calculateContrastRatio(color, color)).toBeCloseTo(1, 1);
    });

    it('should calculate correct contrast for WCAG AA example', () => {
      // #595959 on white should have 7:1 contrast ratio
      const darkGray: RGB = { r: 89, g: 89, b: 89 };
      const white: RGB = { r: 255, g: 255, b: 255 };
      const ratio = calculateContrastRatio(darkGray, white);
      expect(ratio).toBeCloseTo(7, 1);
    });
  });

  describe('Delta E Calculations', () => {
    it('should calculate zero delta E for identical colors', () => {
      const lab: LAB = { l: 50, a: 0, b: 0 };
      expect(calculateDeltaE(lab, lab)).toBeCloseTo(0, 2);
    });

    it('should calculate small delta E for very similar colors', () => {
      const lab1: LAB = { l: 50, a: 0, b: 0 };
      const lab2: LAB = { l: 51, a: 0, b: 0 };
      const deltaE = calculateDeltaE(lab1, lab2);
      expect(deltaE).toBeGreaterThan(0);
      expect(deltaE).toBeLessThan(2);
    });

    it('should calculate large delta E for very different colors', () => {
      const lab1: LAB = { l: 0, a: 0, b: 0 }; // Black
      const lab2: LAB = { l: 100, a: 0, b: 0 }; // White
      const deltaE = calculateDeltaE(lab1, lab2);
      expect(deltaE).toBeGreaterThan(90);
    });

    it('should calculate delta E from RGB colors', () => {
      const red: RGB = { r: 255, g: 0, b: 0 };
      const green: RGB = { r: 0, g: 255, b: 0 };
      const deltaE = calculateDeltaERgb(red, green);
      expect(deltaE).toBeGreaterThan(50); // Red and green are very different
    });

    it('should interpret delta E values correctly', () => {
      expect(interpretDeltaE(0.5).interpretation).toBe('Imperceptible');
      expect(interpretDeltaE(1.5).interpretation).toBe(
        'Perceptible through close observation'
      );
      expect(interpretDeltaE(3).interpretation).toBe('Perceptible at a glance');
      expect(interpretDeltaE(4.5).interpretation).toBe('Noticeable difference');
      expect(interpretDeltaE(10).interpretation).toBe('Different colors');
    });
  });

  describe('WCAG Compliance', () => {
    it('should identify AA compliance correctly', () => {
      const compliance = getWcagCompliance(4.5);
      expect(compliance.aa.normal).toBe(true);
      expect(compliance.aa.large).toBe(true);
      expect(compliance.aaa.normal).toBe(false);
      expect(compliance.aaa.large).toBe(true);
    });

    it('should identify AAA compliance correctly', () => {
      const compliance = getWcagCompliance(7);
      expect(compliance.aa.normal).toBe(true);
      expect(compliance.aa.large).toBe(true);
      expect(compliance.aaa.normal).toBe(true);
      expect(compliance.aaa.large).toBe(true);
    });

    it('should check accessibility correctly', () => {
      const black: RGB = { r: 0, g: 0, b: 0 };
      const white: RGB = { r: 255, g: 255, b: 255 };
      const gray: RGB = { r: 128, g: 128, b: 128 };

      // Black on white should pass all levels
      expect(checkAccessibility(black, white, 'AA', 'normal')).toBe(true);
      expect(checkAccessibility(black, white, 'AAA', 'normal')).toBe(true);

      // Gray on white might not pass AAA
      expect(checkAccessibility(gray, white, 'AA', 'large')).toBe(true);
      expect(checkAccessibility(gray, white, 'AAA', 'normal')).toBe(false);
    });
  });
});
