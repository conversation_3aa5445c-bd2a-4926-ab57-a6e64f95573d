/**
 * @file Modal.tsx
 * @description A reusable modal component for the application
 */

import React, { useEffect } from 'react';
import { X } from 'lucide-react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  maxWidth?: string;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  maxWidth = 'max-w-xl',
}) => {
  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    // Prevent body scroll when modal is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  if (!isOpen) {
    return null;
  }

  return (
    <div
      className='fixed inset-0 flex items-center justify-center p-4'
      style={{
        backgroundColor: 'var(--backdrop-primary)',
        zIndex: 'var(--z-modal)',
      }}
      role='dialog'
      aria-modal='true'
      aria-labelledby={title ? 'modal-title' : undefined}
    >
      <div
        className={`${maxWidth} w-full flex flex-col overflow-hidden`}
        style={{
          backgroundColor: 'var(--color-ui-background-primary)',
          borderRadius: 'var(--radius-lg)',
          boxShadow: 'var(--shadow-xl)',
        }}
        onClick={e => e.stopPropagation()}
      >
        {title && (
          <div
            className='px-6 py-4 flex items-center justify-between'
            style={{
              borderBottom: `1px solid var(--color-ui-border-light)`,
            }}
          >
            <h3
              id='modal-title'
              className='text-lg font-medium'
              style={{
                fontSize: 'var(--font-size-lg)',
                fontWeight: 'var(--font-weight-medium)',
                color: 'var(--color-ui-foreground-primary)',
              }}
            >
              {title}
            </h3>
            <button
              onClick={onClose}
              className='transition-standard'
              style={{
                color: 'var(--color-ui-foreground-tertiary)',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer',
                borderRadius: 'var(--radius-DEFAULT)',
                padding: '0.25rem',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.color =
                  'var(--color-ui-foreground-primary)';
              }}
              onMouseLeave={e => {
                e.currentTarget.style.color =
                  'var(--color-ui-foreground-tertiary)';
              }}
              onFocus={e => {
                e.target.style.outline = 'none';
                e.target.style.boxShadow = `0 0 0 2px var(--form-border-focus)`;
              }}
              onBlur={e => {
                e.target.style.boxShadow = 'none';
              }}
              aria-label='Close modal'
              data-testid='modal-close'
            >
              <X size={20} />
            </button>
          </div>
        )}

        {children}
      </div>
    </div>
  );
};

export default Modal;
