/**
 * @file ipc-registry.ts
 * @description Central IPC handler registry with dependency injection pattern
 * This demonstrates how to register all IPC handlers with proper service injection
 */

import { ipcMain } from 'electron';
import type { Database } from 'better-sqlite3';
import { ServiceLocator } from '../services/service-locator';

// Service imports
import { ColorService } from '../db/services/color.service';
import { ProductService } from '../db/services/product.service';
import { DatasheetService } from '../db/services/datasheet.service';
import { OrganizationService } from '../db/services/organization.service';
import { ColorImportService } from '../db/services/color-import.service';

// IPC handler registration functions (existing)
import { registerColorHandlers } from './color.ipc';
import { registerProductHandlers } from './product.ipc';
import { registerDatasheetHandlers } from './datasheet.ipc';
import { registerOrganizationHandlers } from './organization.ipc';

// New DI-based handlers
import { registerSoftDeleteHandlers } from './soft-delete.ipc';

/**
 * Service bundle interface for dependency injection
 */
export interface ServiceBundle {
  database: Database;
  colorService: ColorService;
  productService: ProductService;
  datasheetService: DatasheetService;
  organizationService: OrganizationService;
  colorImportService: ColorImportService;
}

/**
 * Register all IPC handlers with dependency injection
 * This is the central point for IPC handler registration
 * 
 * @param services - Optional service bundle for dependency injection
 */
export async function registerAllIpcHandlers(services?: Partial<ServiceBundle>): Promise<void> {
  console.log('[IPCRegistry] Starting IPC handler registration with dependency injection');

  try {
    // Ensure ServiceLocator is initialized
    if (!ServiceLocator.hasService('database')) {
      await ServiceLocator.initialize();
    }

    // Get services from injection or ServiceLocator
    const serviceBundle = {
      database: services?.database || ServiceLocator.getDatabase(),
      colorService: services?.colorService || ServiceLocator.getColorService(),
      productService: services?.productService || ServiceLocator.getProductService(),
      datasheetService: services?.datasheetService || ServiceLocator.getDatasheetService(),
      organizationService: services?.organizationService || ServiceLocator.getOrganizationService(),
      colorImportService: services?.colorImportService || ServiceLocator.getColorImportService()
    };

    console.log('[IPCRegistry] Service bundle ready:', {
      database: !!serviceBundle.database,
      colorService: !!serviceBundle.colorService,
      productService: !!serviceBundle.productService,
      datasheetService: !!serviceBundle.datasheetService,
      organizationService: !!serviceBundle.organizationService,
      colorImportService: !!serviceBundle.colorImportService
    });

    // Register handlers with dependency injection
    await registerCoreDataHandlers(serviceBundle);
    await registerUtilityHandlers(serviceBundle);
    await registerLegacyHandlers(serviceBundle);

    console.log('[IPCRegistry] All IPC handlers registered successfully');

  } catch (error) {
    console.error('[IPCRegistry] Failed to register IPC handlers:', error);
    throw error;
  }
}

/**
 * Register core data operation handlers
 */
async function registerCoreDataHandlers(services: ServiceBundle): Promise<void> {
  console.log('[IPCRegistry] Registering core data handlers');

  // Color handlers - already uses DI pattern
  registerColorHandlers({
    colorService: services.colorService,
    colorImportService: services.colorImportService
  });

  // Product handlers - already uses DI pattern  
  registerProductHandlers(services.productService, services.colorService);

  // Datasheet handlers - already uses DI pattern
  registerDatasheetHandlers({
    datasheetService: services.datasheetService
  });

  // Organization handlers - needs DI refactoring
  registerOrganizationHandlers(services.organizationService, services.database);
}

/**
 * Register utility and maintenance handlers
 */
async function registerUtilityHandlers(services: ServiceBundle): Promise<void> {
  console.log('[IPCRegistry] Registering utility handlers');

  // Soft delete handlers - NEW DI + Universal wrapper pattern
  registerSoftDeleteHandlers({
    colorService: services.colorService,
    productService: services.productService,
    datasheetService: services.datasheetService
  });

  // TODO: Add other utility handlers here:
  // - registerIntegrityHandlers(services)
  // - registerAuditHandlers(services)
  // - registerSettingsHandlers(services)
}

/**
 * Register legacy handlers that haven't been refactored yet
 * These should gradually be migrated to the DI pattern
 */
async function registerLegacyHandlers(_services: ServiceBundle): Promise<void> {
  console.log('[IPCRegistry] Registering legacy handlers (to be migrated)');

  // Import and register legacy handlers
  const { registerAuditIpcHandlers } = await import('./audit.ipc');
  // const { registerSoftDeleteIpcHandlers } = await import('./soft-delete.ipc');
  const { registerIntegrityIpcHandlers } = await import('./integrity.ipc');
  const { registerSettingsIpcHandlers } = await import('./settings.ipc');
  const { registerLicenseHandlers } = await import('./license-handlers');
  const { registerSyncHandlers } = await import('./sync-handlers');
  const { registerTestDataHandlers } = await import('./test-data.ipc');
  const { registerSampleDataHandlers } = await import('./sample-data.ipc');
  const { registerColorLibraryIPC } = await import('./color-library.ipc');

  // Register legacy handlers (these create their own service instances)
  registerAuditIpcHandlers();
  // registerSoftDeleteIpcHandlers();
  registerIntegrityIpcHandlers();
  registerSettingsIpcHandlers();
  registerLicenseHandlers(null); // License handlers don't need main window in this context
  registerSyncHandlers();
  registerTestDataHandlers();
  registerSampleDataHandlers();
  registerColorLibraryIPC();

  console.log('[IPCRegistry] Legacy handlers registered (consider migrating to DI pattern)');
}

/**
 * Clean up all IPC handlers
 */
export function unregisterAllIpcHandlers(): void {
  console.log('[IPCRegistry] Cleaning up all IPC handlers');
  
  // Get all registered channels and remove them
  const registeredChannels = ipcMain.eventNames();
  registeredChannels.forEach(channel => {
    if (typeof channel === 'string') {
      ipcMain.removeHandler(channel);
    }
  });

  console.log('[IPCRegistry] IPC handlers cleanup completed');
}

/**
 * Get handler registration status for debugging
 */
export function getRegistrationStatus(): {
  totalHandlers: number;
  channels: string[];
  serviceLocatorReady: boolean;
} {
  const channels = ipcMain.eventNames().filter(name => typeof name === 'string') as string[];
  
  return {
    totalHandlers: channels.length,
    channels: channels.sort(),
    serviceLocatorReady: ServiceLocator.hasService('database')
  };
}