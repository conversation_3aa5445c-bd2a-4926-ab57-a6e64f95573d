import React from 'react';
import { useTokens } from '../hooks/useTokens';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
}

/**
 * Button component that uses the token system
 * Features:
 * - Theme-aware styling
 * - Multiple variants and sizes
 * - Accessibility support
 */
export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  onClick,
  children,
  type = 'button',
}) => {
  const tokens = useTokens();

  // Get size classes based on size prop
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-[var(--font-size-sm)]';
      case 'lg':
        return 'px-6 py-3 text-[var(--font-size-lg)]';
      case 'md':
      default:
        return 'px-4 py-2 text-[var(--font-size-base)]';
    }
  };

  // Token system style classNames
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-brand-primary text-ui-foreground-inverse hover:bg-opacity-90 focus-outline';
      case 'secondary':
        return 'bg-brand-secondary text-ui-foreground-inverse hover:bg-opacity-90 focus-outline';
      case 'outline':
        return 'bg-transparent border border-brand-primary text-brand-primary hover:bg-brand-primary hover:bg-opacity-10 focus-outline';
      case 'ghost':
        return 'bg-transparent text-brand-primary hover:bg-brand-primary hover:bg-opacity-10 focus-outline';
      default:
        return 'bg-brand-primary text-ui-foreground-inverse hover:bg-opacity-90 focus-outline';
    }
  };

  // Common classes
  const commonClasses = `
    font-[var(--font-weight-medium)]
    rounded
    transition-all
    inline-flex
    items-center
    justify-center
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    ${getSizeClasses()}
  `;

  // Get variant classes
  const variantClasses = getVariantClasses();

  // Combine all classes
  const buttonClasses =
    `${commonClasses} ${variantClasses} ${className}`.trim();

  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
      style={{
        // Direct token usage with the hook
        transitionDuration: tokens.transitions.duration[200],
        transitionTimingFunction: tokens.transitions.easing.apple,
      }}
    >
      {children}
    </button>
  );
};
