/**
 * @file enhanced-color.types.ts
 * @description Enhanced color types with sophisticated generic constraints and advanced patterns
 * Implements cutting-edge TypeScript features for bulletproof type safety
 */

import type {
  Brand,
} from './advanced-utilities.types';

import type {
  ApplicationError,
  Result,
  AsyncResult,
} from './error-handling.types';

import type { NonNullString } from './null-safety.types';

// ===== BRANDED COLOR DOMAIN TYPES =====

export type ColorId = Brand<string, 'ColorId'>;
export type HexColor = Brand<string, 'HexColor'>;
export type PantoneCode = Brand<string, 'PantoneCode'>;
export type RALCode = Brand<string, 'RALCode'>;
export type ColorName = Brand<string, 'ColorName'>;
export type GradientId = Brand<string, 'GradientId'>;
export type ColorSpaceValue = Brand<number, 'ColorSpaceValue'>;
export type ColorSource = Brand<string, 'ColorSource'>;

// ===== ADVANCED COLOR SPACE CONSTRAINTS =====

/**
 * Constrained CMYK values with proper ranges
 */
export interface CMYK {
  readonly c: ColorSpaceValue & Brand<number, 'CMYK_C'>; // 0-100
  readonly m: ColorSpaceValue & Brand<number, 'CMYK_M'>; // 0-100
  readonly y: ColorSpaceValue & Brand<number, 'CMYK_Y'>; // 0-100
  readonly k: ColorSpaceValue & Brand<number, 'CMYK_K'>; // 0-100
}

/**
 * Constrained RGB values with proper ranges
 */
export interface RGB {
  readonly r: ColorSpaceValue & Brand<number, 'RGB_R'>; // 0-255
  readonly g: ColorSpaceValue & Brand<number, 'RGB_G'>; // 0-255
  readonly b: ColorSpaceValue & Brand<number, 'RGB_B'>; // 0-255
}

/**
 * Constrained HSL values with proper ranges
 */
export interface HSL {
  readonly h: ColorSpaceValue & Brand<number, 'HSL_H'>; // 0-360
  readonly s: ColorSpaceValue & Brand<number, 'HSL_S'>; // 0-100
  readonly l: ColorSpaceValue & Brand<number, 'HSL_L'>; // 0-100
}

/**
 * Constrained LAB values with proper ranges
 */
export interface LAB {
  readonly l: ColorSpaceValue & Brand<number, 'LAB_L'>; // 0-100
  readonly a: ColorSpaceValue & Brand<number, 'LAB_A'>; // -128 to 127
  readonly b: ColorSpaceValue & Brand<number, 'LAB_B'>; // -128 to 127
  readonly illuminant?: 'D50' | 'D65' | 'A' | 'C';
  readonly observer?: '2°' | '10°';
}

// ===== GENERIC COLOR SPACE OPERATIONS =====

/**
 * Generic color space type with constraints
 */
export type ColorSpace<T extends 'CMYK' | 'RGB' | 'HSL' | 'LAB'> =
  T extends 'CMYK'
    ? CMYK
    : T extends 'RGB'
      ? RGB
      : T extends 'HSL'
        ? HSL
        : T extends 'LAB'
          ? LAB
          : never;

/**
 * Color space value constraints
 */
export type ColorSpaceConstraints<T extends keyof ColorSpace<any>> =
  T extends 'CMYK'
    ? { min: 0; max: 100 }
    : T extends 'RGB'
      ? { min: 0; max: 255 }
      : T extends 'HSL'
        ? T extends 'h'
          ? { min: 0; max: 360 }
          : { min: 0; max: 100 }
        : T extends 'LAB'
          ? T extends 'l'
            ? { min: 0; max: 100 }
            : { min: -128; max: 127 }
          : never;

/**
 * Validate color space values with generic constraints
 */
export type ValidatedColorSpace<T, TSpace extends keyof ColorSpace<any>> =
  T extends ColorSpace<TSpace> ? T : never;

// ===== ADVANCED GRADIENT TYPES =====

/**
 * Gradient stop with enhanced type safety
 */
export interface GradientStop<TColor extends HexColor = HexColor> {
  readonly id: Brand<string, 'GradientStopId'>;
  readonly position: Brand<number, 'GradientPosition'>; // 0.0 to 1.0
  readonly color: TColor;
  readonly opacity?: Brand<number, 'Opacity'>; // 0.0 to 1.0
  readonly metadata?: Record<string, unknown>;
}

/**
 * Gradient information with generic constraints
 */
export interface GradientInfo<
  TStops extends readonly GradientStop[] = readonly GradientStop[],
  TType extends 'linear' | 'radial' | 'conic' = 'linear',
> {
  readonly id: GradientId;
  readonly type: TType;
  readonly stops: TStops;
  readonly angle?: TType extends 'linear'
    ? Brand<number, 'GradientAngle'>
    : never; // 0-360
  readonly center?: TType extends 'radial' | 'conic'
    ? { x: number; y: number }
    : never;
  readonly radius?: TType extends 'radial' ? number : never;
  readonly metadata?: Record<string, unknown>;
}

/**
 * Validate gradient stops minimum requirement
 */
export type ValidGradientStops<T extends readonly GradientStop[]> =
  T extends readonly [GradientStop, GradientStop, ...GradientStop[]]
    ? T
    : never;

/**
 * Linear gradient with at least 2 stops
 */
export type LinearGradient<
  TStops extends readonly GradientStop[] = readonly GradientStop[],
> = GradientInfo<ValidGradientStops<TStops>, 'linear'>;

/**
 * Radial gradient with at least 2 stops
 */
export type RadialGradient<
  TStops extends readonly GradientStop[] = readonly GradientStop[],
> = GradientInfo<ValidGradientStops<TStops>, 'radial'>;

// ===== ENHANCED COLOR ENTRY WITH GENERIC CONSTRAINTS =====

/**
 * Base color properties that all color entries must have
 */
export interface BaseColorProperties {
  readonly id: ColorId;
  readonly name: ColorName;
  readonly hex: HexColor;
  readonly createdAt: string;
  readonly updatedAt: string;
}

/**
 * Color entry with sophisticated generic constraints
 */
export interface EnhancedColorEntry<
  TColorSpaces extends Partial<
    Record<keyof ColorSpace<any>, ColorSpace<any>>
  > = {},
  TGradient extends GradientInfo | null = null,
  TMetadata extends Record<string, unknown> = Record<string, unknown>,
> extends BaseColorProperties {
  // Source and classification
  readonly source?: ColorSource;
  readonly code?: PantoneCode | RALCode;
  readonly product: NonNullString;
  readonly organizationId: Brand<string, 'OrganizationId'>;
  readonly userId?: Brand<string, 'UserId'>;

  // Color spaces with generic constraints
  readonly colorSpaces: TColorSpaces;

  // Runtime-computed color spaces (optional)
  readonly rgb?: TColorSpaces extends { rgb: RGB } ? TColorSpaces['rgb'] : RGB;
  readonly cmyk?: TColorSpaces extends { cmyk: CMYK }
    ? TColorSpaces['cmyk']
    : CMYK;
  readonly hsl?: TColorSpaces extends { hsl: HSL } ? TColorSpaces['hsl'] : HSL;
  readonly lab?: TColorSpaces extends { lab: LAB } ? TColorSpaces['lab'] : LAB;

  // Gradient information
  readonly gradient: TGradient;
  readonly isGradient: TGradient extends null ? false : true;

  // Flags and properties
  readonly isLibrary: boolean;
  readonly isMetallic: boolean;
  readonly isEffect: boolean;
  readonly isActive: boolean;

  // User content
  readonly notes?: string;
  readonly tags?: readonly string[];

  // Extended metadata with type safety
  readonly metadata: TMetadata;
}

// ===== SPECIALIZED COLOR ENTRY TYPES =====

/**
 * Solid color entry (no gradient)
 */
export type SolidColorEntry<
  TColorSpaces extends Partial<
    Record<keyof ColorSpace<any>, ColorSpace<any>>
  > = {},
  TMetadata extends Record<string, unknown> = Record<string, unknown>,
> = EnhancedColorEntry<TColorSpaces, null, TMetadata>;

/**
 * Gradient color entry with required gradient
 */
export type GradientColorEntry<
  TGradient extends GradientInfo = GradientInfo,
  TColorSpaces extends Partial<
    Record<keyof ColorSpace<any>, ColorSpace<any>>
  > = {},
  TMetadata extends Record<string, unknown> = Record<string, unknown>,
> = EnhancedColorEntry<TColorSpaces, TGradient, TMetadata>;

/**
 * Complete color entry with all color spaces
 */
export type CompleteColorEntry<
  TGradient extends GradientInfo | null = null,
  TMetadata extends Record<string, unknown> = Record<string, unknown>,
> = EnhancedColorEntry<
  {
    readonly rgb: RGB;
    readonly cmyk: CMYK;
    readonly hsl: HSL;
    readonly lab: LAB;
  },
  TGradient,
  TMetadata
>;

/**
 * Library color entry with enhanced metadata
 */
export type LibraryColorEntry<
  TLibraryMeta extends Record<string, unknown> = Record<string, unknown>,
> = CompleteColorEntry<
  null,
  {
    readonly library: {
      readonly version: string;
      readonly standard: string;
      readonly category: string;
    } & TLibraryMeta;
  }
>;

// ===== COLOR OPERATIONS WITH GENERIC CONSTRAINTS =====

/**
 * Color transformation function type
 */
export type ColorTransform<
  TFrom extends EnhancedColorEntry,
  TTo extends EnhancedColorEntry = TFrom,
> = (color: TFrom) => Result<TTo>;

/**
 * Async color transformation function type
 */
export type AsyncColorTransform<
  TFrom extends EnhancedColorEntry,
  TTo extends EnhancedColorEntry = TFrom,
> = (color: TFrom) => AsyncResult<TTo>;

/**
 * Color validation function type
 */
export type ColorValidator<T extends EnhancedColorEntry> = (
  color: T
) => Result<T, ApplicationError>;

/**
 * Color space converter with generic constraints
 */
export interface ColorSpaceConverter<
  TFrom extends keyof ColorSpace<any>,
  TTo extends keyof ColorSpace<any>,
> {
  readonly from: TFrom;
  readonly to: TTo;
  readonly convert: (value: ColorSpace<TFrom>) => Result<ColorSpace<TTo>>;
  readonly accuracy?: 'low' | 'medium' | 'high';
}

// ===== ADVANCED COLOR COLLECTION TYPES =====

/**
 * Color collection with enhanced type safety
 */
export interface ColorCollection<
  TColor extends EnhancedColorEntry = EnhancedColorEntry,
  TMetadata extends Record<string, unknown> = Record<string, unknown>,
> {
  readonly id: Brand<string, 'ColorCollectionId'>;
  readonly name: string;
  readonly colors: readonly TColor[];
  readonly organizationId: Brand<string, 'OrganizationId'>;
  readonly createdBy: Brand<string, 'UserId'>;
  readonly metadata: TMetadata;
  readonly createdAt: string;
  readonly updatedAt: string;
}

/**
 * Color palette with design constraints
 */
export type ColorPalette<
  TColors extends readonly EnhancedColorEntry[] = readonly EnhancedColorEntry[],
> = ColorCollection<
  TColors[number],
  {
    readonly purpose: 'branding' | 'product' | 'marketing' | 'ui' | 'print';
    readonly colorHarmony:
      | 'monochromatic'
      | 'analogous'
      | 'complementary'
      | 'triadic'
      | 'tetradic'
      | 'custom';
    readonly accessibility: {
      readonly contrastChecked: boolean;
      readonly wcagLevel: 'AA' | 'AAA' | 'none';
    };
  }
>;

/**
 * Extract color type from collection
 */
export type ExtractColorType<T> =
  T extends ColorCollection<infer TColor, any> ? TColor : never;

/**
 * Extract metadata type from collection
 */
export type ExtractMetadataType<T> =
  T extends ColorCollection<any, infer TMeta> ? TMeta : never;

// ===== CONDITIONAL TYPE UTILITIES =====

/**
 * Check if color entry has gradient
 */
export type HasGradient<T extends EnhancedColorEntry> =
  T extends EnhancedColorEntry<any, infer TGradient, any>
    ? TGradient extends null
      ? false
      : true
    : false;

/**
 * Extract gradient type from color entry
 */
export type ExtractGradient<T extends EnhancedColorEntry> =
  T extends EnhancedColorEntry<any, infer TGradient, any> ? TGradient : never;

/**
 * Extract color spaces from color entry
 */
export type ExtractColorSpaces<T extends EnhancedColorEntry> =
  T extends EnhancedColorEntry<infer TSpaces, any, any> ? TSpaces : never;

/**
 * Check if color entry has specific color space
 */
export type HasColorSpace<
  T extends EnhancedColorEntry,
  TSpace extends keyof ColorSpace<any>,
> = TSpace extends keyof ExtractColorSpaces<T> ? true : false;

// ===== FACTORY TYPES AND BUILDERS =====

/**
 * Color entry builder with type-safe step validation
 */
export interface ColorEntryBuilder<
  TState extends {
    hasId?: boolean;
    hasName?: boolean;
    hasHex?: boolean;
    hasProduct?: boolean;
    hasOrganization?: boolean;
  } = {},
> {
  setId<T extends ColorId>(id: T): ColorEntryBuilder<TState & { hasId: true }>;
  setName<T extends ColorName>(
    name: T
  ): ColorEntryBuilder<TState & { hasName: true }>;
  setHex<T extends HexColor>(
    hex: T
  ): ColorEntryBuilder<TState & { hasHex: true }>;
  setProduct<T extends NonNullString>(
    product: T
  ): ColorEntryBuilder<TState & { hasProduct: true }>;
  setOrganization<T extends Brand<string, 'OrganizationId'>>(
    orgId: T
  ): ColorEntryBuilder<TState & { hasOrganization: true }>;

  addColorSpace<TSpace extends keyof ColorSpace<any>>(
    space: TSpace,
    values: ColorSpace<TSpace>
  ): ColorEntryBuilder<TState>;

  setGradient<T extends GradientInfo>(gradient: T): ColorEntryBuilder<TState>;
  setMetadata<T extends Record<string, unknown>>(
    metadata: T
  ): ColorEntryBuilder<TState>;

  build<T extends TState = TState>(): T extends {
    hasId: true;
    hasName: true;
    hasHex: true;
    hasProduct: true;
    hasOrganization: true;
  }
    ? Result<EnhancedColorEntry>
    : 'Missing required properties';
}

/**
 * Gradient builder with step validation
 */
export interface GradientBuilder<
  TState extends {
    hasType?: boolean;
    hasStops?: boolean;
    minStopsValid?: boolean;
  } = {},
> {
  setType<T extends 'linear' | 'radial' | 'conic'>(
    type: T
  ): GradientBuilder<TState & { hasType: true }>;

  addStop(stop: GradientStop): GradientBuilder<TState & { hasStops: true }>;

  setStops<T extends readonly GradientStop[]>(
    stops: T
  ): GradientBuilder<
    TState & {
      hasStops: true;
      minStopsValid: T extends ValidGradientStops<T> ? true : false;
    }
  >;

  build<T extends TState = TState>(): T extends {
    hasType: true;
    hasStops: true;
    minStopsValid: true;
  }
    ? Result<GradientInfo>
    : 'Invalid gradient configuration';
}

// ===== TYPE GUARDS AND UTILITIES =====

/**
 * Type guard for solid colors
 */
export function isSolidColor<T extends EnhancedColorEntry>(
  color: T
): color is T & SolidColorEntry {
  return color.gradient === null && color.isGradient === false;
}

/**
 * Type guard for gradient colors
 */
export function isGradientColor<T extends EnhancedColorEntry>(
  color: T
): color is T & GradientColorEntry {
  return color.gradient !== null && color.isGradient;
}

/**
 * Type guard for complete color entries
 */
export function isCompleteColorEntry<T extends EnhancedColorEntry>(
  color: T
): color is T & CompleteColorEntry {
  return (
    color.colorSpaces &&
    'rgb' in color.colorSpaces &&
    'cmyk' in color.colorSpaces &&
    'hsl' in color.colorSpaces &&
    'lab' in color.colorSpaces
  );
}

/**
 * Type guard for library colors
 */
export function isLibraryColor<T extends EnhancedColorEntry>(
  color: T
): color is T & LibraryColorEntry {
  return (
    color.isLibrary === true && color.metadata && 'library' in color.metadata
  );
}

// ===== CONVERSION AND TRANSFORMATION UTILITIES =====

/**
 * Convert legacy ColorEntry to EnhancedColorEntry
 */
export type LegacyToEnhanced<T> = T extends {
  id: string;
  name: string;
  hex: string;
  product: string;
}
  ? EnhancedColorEntry<{}, T extends { gradient: any } ? GradientInfo : null>
  : never;

/**
 * Convert EnhancedColorEntry to legacy format
 */
export type EnhancedToLegacy<T extends EnhancedColorEntry> = {
  id: string;
  name: string;
  hex: string;
  product: string;
  gradient: T extends EnhancedColorEntry<any, infer TGradient, any>
    ? TGradient extends null
      ? undefined
      : any
    : undefined;
};

// Export all types for module compatibility
export {};
