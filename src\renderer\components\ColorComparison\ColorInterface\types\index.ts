/**
 * Type definitions for the Color Interface components
 * Following TypeScript discrimination patterns for type safety
 */

import { HarmonyType } from '../../../../../shared/types/colorComparison.types';

export interface SimpleColorEntry {
  id: string;
  hex: string;
  pantone: string;
  cmyk: string;
}

export interface ColorMetrics {
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  cmyk: { c: number; m: number; y: number; k: number };
  lab?: { l: number; a: number; b: number };
}

export interface ContrastResult {
  ratio: number;
  level: 'AAA' | 'AA' | 'AA Large' | 'Fail';
  passes: boolean;
}

export interface DeltaEResult {
  value: number;
  interpretation: string;
  description: string;
}

export interface ColorRecommendation {
  title: string;
  description: string;
  actionable: string;
  severity?: 'info' | 'warning' | 'error';
}

export interface PrintSimulation {
  substrate: 'coated' | 'uncoated' | 'newsprint' | 'recycled';
  adjustment: number;
  filter: string;
}

export interface AccessibilitySimulation {
  type: 'protanopia' | 'deuteranopia' | 'tritanopia' | 'achromatopsia';
  description: string;
  filter?: string;
}

// Tab type discrimination
export type TabType = 'comparison' | 'metrics' | 'print' | 'accessibility' | 'harmony' | 'output' | 'batch';

export interface TabProps {
  selectedColor: SimpleColorEntry | null;
  secondaryColor?: SimpleColorEntry | null;
  onColorChange?: (color: SimpleColorEntry) => void;
}

// Discriminated union for tab-specific props
export type TabComponentProps = 
  | { type: 'comparison'; props: ComparisonTabProps }
  | { type: 'metrics'; props: MetricsTabProps }
  | { type: 'print'; props: PrintTabProps }
  | { type: 'accessibility'; props: AccessibilityTabProps }
  | { type: 'harmony'; props: HarmonyTabProps }
  | { type: 'output'; props: OutputTabProps };

export interface ComparisonTabProps extends TabProps {
  recommendations: ColorRecommendation[];
  similarColors: SimpleColorEntry[];
}

export interface MetricsTabProps extends TabProps {
  metrics: ColorMetrics | null;
}

export interface PrintTabProps extends TabProps {
  inkCoverage: number;
  dominantInk: string;
}

export interface AccessibilityTabProps extends TabProps {
  contrastResults: {
    white: ContrastResult;
    black: ContrastResult;
    gray: ContrastResult;
  };
}

export interface HarmonyTabProps extends TabProps {
  harmonyType: HarmonyType;
  harmonyColors: SimpleColorEntry[];
  onHarmonyTypeChange: (type: HarmonyType) => void;
}

export interface OutputTabProps extends TabProps {
  outputFormat: string;
  onFormatChange: (format: string) => void;
}