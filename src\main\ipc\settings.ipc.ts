/**
 * @file settings.ipc.ts
 * @description IPC handlers for application settings and developer tools using secure handler pattern
 */

import { ipcMain, dialog, app, BrowserWindow } from 'electron';
import { getDatabaseStats, runMaintenance } from '../db/database';
import { createRelationshipIntegrityManager } from '../utils/relationship-integrity';
import { getDatabase } from '../db/database';
import { getDatabaseInitializer } from '../db/core/initialization';
import { validateRequired } from '../utils/input-validation';
import {
  registerSystemHandler,
  registerSecureHandler,
} from '../utils/ipc-wrapper';
import * as fs from 'fs';
import * as path from 'path';

async function readLastNLines(filePath: string, n: number): Promise<string[]> {
  try {
    const content = await fs.promises.readFile(filePath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim() !== '');
    return lines.slice(Math.max(lines.length - n, 0));
  } catch (error) {
    console.error(`Error reading log file ${filePath}:`, error);
    return [];
  }
}

/**
 * Register settings-related IPC handlers
 */
export function registerSettingsIpcHandlers(): void {
  // Developer Tools handlers
  registerSystemHandler(
    'settings:open-dev-tools',
    async () => {
      // This is handled by the existing window:toggleDevTools handler
      // Just return success for API compatibility
      return { success: true };
    },
    ipcMain,
    {
      logChannel: 'SettingsSystem',
      customErrorMessage: 'Failed to open developer tools.',
    }
  );

  registerSystemHandler(
    'settings:run-performance-test',
    async () => {
      console.log('[IPC] Running performance test...');

      // Simple performance test - measure database operations
      const startTime = Date.now();
      const db = getDatabase();

      // Run a few database operations to test performance
      let colorCount, productCount;
      try {
        colorCount = db
          .prepare('SELECT COUNT(*) as count FROM colors WHERE is_active = 1')
          .get();
      } catch {
        colorCount = db
          .prepare(
            'SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL'
          )
          .get();
      }

      try {
        productCount = db
          .prepare('SELECT COUNT(*) as count FROM products WHERE is_active = 1')
          .get();
      } catch {
        productCount = db
          .prepare(
            'SELECT COUNT(*) as count FROM products WHERE deleted_at IS NULL'
          )
          .get();
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      const results = {
        duration,
        colorCount: colorCount?.count || 0,
        productCount: productCount?.count || 0,
        timestamp: new Date().toISOString(),
      };

      console.log('[IPC] Performance test completed:', results);
      return { success: true, results };
    },
    ipcMain,
    {
      logChannel: 'SettingsPerf',
      customErrorMessage: 'Failed to run performance test.',
    }
  );

  registerSecureHandler(
    'settings:check-database-integrity',
    async (organizationId: string, organizationIdParam?: string) => {
      console.log('[IPC] Checking database integrity...');

      const db = getDatabase();
      const integrityManager = createRelationshipIntegrityManager(db);

      // Use the organizationId from context or the passed parameter
      const targetOrgId = organizationIdParam || organizationId;

      // Run integrity checks
      let integrityResults;
      if (targetOrgId) {
        const validatedOrgId = validateRequired(
          'organizationId',
          targetOrgId,
          'uuid'
        ) as string;
        integrityResults = integrityManager.runIntegrityChecks(validatedOrgId);
      } else {
        // Run basic integrity check without organization scoping
        integrityResults = {
          timestamp: new Date().toISOString(),
          checks: [],
          totalIssues: 0,
          totalCleaned: 0,
        };
      }

      // Run SQLite integrity check
      const sqliteIntegrity = db.prepare('PRAGMA integrity_check').all();

      const results = {
        relationshipIntegrity: integrityResults,
        sqliteIntegrity,
        timestamp: new Date().toISOString(),
      };

      console.log('[IPC] Database integrity check completed');
      return { success: true, results };
    },
    ipcMain,
    {
      logChannel: 'SettingsIntegrity',
      customErrorMessage: 'Failed to check database integrity.',
    }
  );

  // Database maintenance handlers
  registerSystemHandler(
    'settings:get-database-stats',
    async () => {
      const stats = getDatabaseStats();
      return { success: true, stats };
    },
    ipcMain,
    {
      logChannel: 'SettingsStats',
      customErrorMessage: 'Failed to retrieve database statistics.',
    }
  );

  registerSystemHandler(
    'settings:optimize-database',
    async () => {
      console.log('[IPC] Optimizing database...');
      runMaintenance();
      console.log('[IPC] Database optimization completed');
      return { success: true };
    },
    ipcMain,
    {
      logChannel: 'SettingsOptimize',
      customErrorMessage: 'Failed to optimize database.',
    }
  );

  registerSystemHandler(
    'settings:vacuum-database',
    async () => {
      console.log('[IPC] Running database vacuum...');
      const db = getDatabase();
      db.exec('VACUUM');
      console.log('[IPC] Database vacuum completed');
      return { success: true };
    },
    ipcMain,
    {
      logChannel: 'SettingsVacuum',
      customErrorMessage: 'Failed to vacuum database.',
    }
  );

  // Enhanced integrity check for troubleshooting
  registerSystemHandler(
    'settings:run-enhanced-integrity-check',
    async () => {
      console.log('[IPC] Running enhanced database integrity check...');

      const initializer = getDatabaseInitializer();
      const integrityResult = await initializer.runIntegrityCheck();

      console.log('[IPC] Enhanced integrity check completed:', integrityResult);
      return {
        success: true,
        result: integrityResult,
      };
    },
    ipcMain,
    {
      logChannel: 'SettingsIntegrityEnhanced',
      customErrorMessage: 'Failed to run enhanced integrity check.',
    }
  );

  // Error logs handlers
  registerSystemHandler(
    'settings:get-application-logs',
    async () => {
      const logDir = app.getPath('logs');
      const logFiles = fs.readdirSync(logDir).filter(f => f.endsWith('.log'));
      let allLogs: any[] = [];

      for (const logFile of logFiles) {
        const filePath = path.join(logDir, logFile);
        const lines = await readLastNLines(filePath, 200);
        const logs = lines.map((line, index) => {
          try {
            const parsed = JSON.parse(line);
            return {
              id: `${logFile}-${index}`,
              ...parsed,
            };
          } catch {
            return {
              id: `${logFile}-${index}`,
              timestamp: new Date().toISOString(),
              level: 'info',
              message: line,
              category: 'raw',
            };
          }
        });
        allLogs = allLogs.concat(logs);
      }

      allLogs.sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      return { success: true, logs: allLogs.slice(0, 500) };
    },
    ipcMain,
    {
      logChannel: 'SettingsLogs',
      customErrorMessage: 'Failed to retrieve application logs.',
    }
  );

  registerSystemHandler(
    'settings:clear-application-logs',
    async () => {
      const logDir = app.getPath('logs');
      const logFiles = fs.readdirSync(logDir).filter(f => f.endsWith('.log'));
      for (const logFile of logFiles) {
        fs.unlinkSync(path.join(logDir, logFile));
      }
      console.log('[IPC] Application logs cleared');
      return { success: true };
    },
    ipcMain,
    {
      logChannel: 'SettingsLogsClear',
      customErrorMessage: 'Failed to clear application logs.',
    }
  );

  registerSystemHandler(
    'settings:export-application-logs',
    async () => {
      const logDir = app.getPath('logs');
      const logFiles = fs.readdirSync(logDir).filter(f => f.endsWith('.log'));
      let logContent = `ChromaSync Application Logs\nExported: ${new Date().toISOString()}\n\n`;

      for (const logFile of logFiles) {
        logContent += `--- ${logFile} ---\n`;
        logContent += fs.readFileSync(path.join(logDir, logFile), 'utf-8');
        logContent += '\n\n';
      }

      const result = await dialog.showSaveDialog(
        BrowserWindow.getFocusedWindow()!,
        {
          title: 'Export Application Logs',
          defaultPath: `chromasync-logs-${Date.now()}.txt`,
          filters: [{ name: 'Text Files', extensions: ['txt'] }],
        }
      );

      if (result.canceled || !result.filePath) {
        return { success: false, canceled: true };
      }

      fs.writeFileSync(result.filePath, logContent);

      return { success: true, path: result.filePath };
    },
    ipcMain,
    {
      logChannel: 'SettingsLogsExport',
      customErrorMessage: 'Failed to export application logs.',
    }
  );

  // File picker handler for logo selection
  registerSystemHandler(
    'settings:select-logo-file',
    async () => {
      console.log('[IPC] Opening logo file selection dialog...');

      const focusedWindow = BrowserWindow.getFocusedWindow();
      const allWindows = BrowserWindow.getAllWindows();
      const parentWindow = focusedWindow || allWindows[0];

      if (!parentWindow) {
        throw new Error('No browser window available for file dialog');
      }

      const result = await dialog.showOpenDialog(parentWindow, {
        title: 'Select Logo File',
        properties: ['openFile'],
        filters: [
          {
            name: 'Image Files',
            extensions: ['png', 'jpg', 'jpeg', 'gif', 'svg', 'bmp', 'webp'],
          },
          { name: 'All Files', extensions: ['*'] },
        ],
      });

      if (result.canceled || result.filePaths.length === 0) {
        console.log('[IPC] Logo file selection canceled');
        return { success: false, canceled: true };
      }

      const selectedPath = result.filePaths[0];
      if (!selectedPath) {
        return { success: false, error: 'No file path selected' };
      }

      console.log(`[IPC] Logo file selected: ${selectedPath}`);

      // Verify file exists and is accessible
      if (!fs.existsSync(selectedPath)) {
        return { success: false, error: 'Selected file does not exist' };
      }

      // Get file stats to check if it's a reasonable size (under 10MB)
      const stats = fs.statSync(selectedPath);
      const maxSize = 10 * 1024 * 1024; // 10MB

      if (stats.size > maxSize) {
        return {
          success: false,
          error: 'Logo file is too large (maximum 10MB)',
        };
      }

      return { success: true, path: selectedPath };
    },
    ipcMain,
    {
      logChannel: 'SettingsFilePicker',
      customErrorMessage: 'Failed to select logo file.',
    }
  );

  // Application reset handler
  registerSystemHandler(
    'settings:reset-application-data',
    async () => {
      console.log('[IPC] Resetting application data...');

      const db = getDatabase();

      // 1. Clear all tables (but keep schema)
      const tables = [
        'colors',
        'products',
        'product_colors',
        'datasheets',
        'organizations',
        'organization_members',
      ];

      for (const table of tables) {
        try {
          db.prepare(`DELETE FROM ${table}`).run();
          console.log(`[IPC] Cleared table: ${table}`);
        } catch (error) {
          console.warn(`[IPC] Could not clear table ${table}:`, error);
        }
      }

      // 2. Reset auto-increment counters
      try {
        db.prepare('DELETE FROM sqlite_sequence').run();
      } catch (error) {
        console.warn('[IPC] Could not reset auto-increment counters:', error);
      }

      // 3. Vacuum database to reclaim space
      try {
        db.exec('VACUUM');
        console.log('[IPC] Database vacuumed');
      } catch (error) {
        console.warn('[IPC] Could not vacuum database:', error);
      }

      // 4. Clear any cached data or settings
      try {
        // In a real implementation, this would also:
        // - Clear electron-store data
        // - Clear authentication tokens
        // - Clear cache directories
        // - Reset user preferences
        console.log('[IPC] Additional cleanup would be performed here');
      } catch (error) {
        console.warn('[IPC] Additional cleanup failed:', error);
      }

      console.log('[IPC] Application data reset completed');
      return {
        success: true,
        message: 'Application data has been reset successfully',
      };
    },
    ipcMain,
    {
      logChannel: 'SettingsReset',
      customErrorMessage: 'Failed to reset application data.',
    }
  );

  // App info handler
  registerSystemHandler(
    'settings:get-app-info',
    async () => {
      const appInfo = {
        version: app.getVersion(),
        name: app.getName(),
        build:
          new Date().toISOString().split('T')[0]?.replace(/-/g, '') ||
          'unknown', // YYYYMMDD format
        platform: `Electron ${process.versions.electron}`,
        electronVersion: process.versions.electron,
        nodeVersion: process.versions.node,
        chromeVersion: process.versions.chrome,
        arch: process.arch,
        os: process.platform,
      };

      return { success: true, appInfo };
    },
    ipcMain,
    {
      logChannel: 'SettingsAppInfo',
      customErrorMessage: 'Failed to retrieve application information.',
    }
  );

  console.log('[IPC] Settings handlers registered');
}
