/**
 * @file AboutSection.tsx
 * @description About section component for displaying app information
 */

import React, { useState, useEffect } from 'react';
import { ExternalLink, Info } from 'lucide-react';

interface AppInfo {
  version: string;
  name: string;
  build: string;
  platform: string;
  electronVersion: string;
  nodeVersion: string;
  chromeVersion: string;
  arch: string;
  os: string;
}

/**
 * About section component
 */
export const AboutSection: React.FC = () => {
  const [appInfo, setAppInfo] = useState<AppInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchAppInfo = async () => {
      try {
        const response = await (window as any).api.getAppInfo();
        if (response.success && response.appInfo) {
          setAppInfo(response.appInfo);
        } else {
          console.error('Failed to fetch app info:', response.error);
          // Use fallback data
          setAppInfo({
            version: '2.0.0-dev',
            name: 'ChromaSync',
            build: '20241201',
            platform: 'Electron',
            electronVersion: 'Unknown',
            nodeVersion: 'Unknown',
            chromeVersion: 'Unknown',
            arch: 'Unknown',
            os: 'Unknown',
          });
        }
      } catch (error) {
        console.error('Error fetching app info:', error);
        // Use fallback data
        setAppInfo({
          version: '2.0.0-dev',
          name: 'ChromaSync',
          build: '20241201',
          platform: 'Electron',
          electronVersion: 'Unknown',
          nodeVersion: 'Unknown',
          chromeVersion: 'Unknown',
          arch: 'Unknown',
          os: 'Unknown',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppInfo();
  }, []);

  const handleLinkClick = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <section>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        About ChromaSync
      </h3>
      <div className='space-y-4'>
        {/* App Info */}
        <div className='bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4'>
          {isLoading ? (
            <div className='text-center text-ui-foreground-secondary dark:text-gray-400 py-4'>
              Loading app information...
            </div>
          ) : appInfo ? (
            <div className='space-y-3'>
              <div className='flex items-center justify-between'>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  Version
                </span>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  {appInfo.version}
                </span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  Build
                </span>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  {appInfo.build}
                </span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  Platform
                </span>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  {appInfo.platform}
                </span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  Architecture
                </span>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  {appInfo.arch}
                </span>
              </div>
              <div className='flex items-center justify-between'>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  OS
                </span>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  {appInfo.os}
                </span>
              </div>
            </div>
          ) : (
            <div className='text-center text-ui-foreground-secondary dark:text-gray-400 py-4'>
              Failed to load app information
            </div>
          )}
        </div>

        {/* Links */}
        <div className='space-y-2'>
          <button
            onClick={() => handleLinkClick('https://chromasync.app')}
            className='w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors'
          >
            <span className='text-ui-foreground-primary dark:text-gray-300'>
              Visit Website
            </span>
            <ExternalLink
              size={16}
              className='text-ui-foreground-secondary dark:text-gray-400'
            />
          </button>

          <button
            onClick={() => handleLinkClick('https://chromasync.app/docs.html')}
            className='w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors'
          >
            <span className='text-ui-foreground-primary dark:text-gray-300'>
              Documentation
            </span>
            <ExternalLink
              size={16}
              className='text-ui-foreground-secondary dark:text-gray-400'
            />
          </button>

          <button
            onClick={() => handleLinkClick('https://chromasync.app/about.html')}
            className='w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors'
          >
            <span className='text-ui-foreground-primary dark:text-gray-300'>
              Support
            </span>
            <ExternalLink
              size={16}
              className='text-ui-foreground-secondary dark:text-gray-400'
            />
          </button>
        </div>

        {/* Copyright */}
        <div className='text-center pt-4'>
          <div className='flex items-center justify-center text-sm text-ui-foreground-secondary dark:text-gray-400'>
            <Info size={14} className='mr-1' />© 2024 ChromaSync. All rights
            reserved.
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
