/**
 * @file organization-member.service.test.ts
 * @description Comprehensive unit tests for OrganizationMemberService
 */

import {
  describe,
  it,
  expect,
  beforeEach,
  jest,
  afterEach,
} from '@jest/globals';
import {
  OrganizationMemberService,
  MemberSearchOptions,
  BulkMemberOperation,
} from '../organization-member.service';
import { IOrganizationRepository } from '../../../db/repositories/interfaces/organization.repository.interface';
import { OrganizationMember } from '../../../../shared/types/organization.types';

// Mock the external dependencies
jest.mock('../../supabase-client');
jest.mock('../../service-locator');

describe('OrganizationMemberService', () => {
  let service: OrganizationMemberService;
  let mockRepository: jest.Mocked<IOrganizationRepository>;

  // Test data
  const mockOrganizationId = 'org-123';
  const mockUserId = 'user-123';
  const mockOwnerId = 'owner-123';
  const mockAdminId = 'admin-123';
  const mockMemberId = 'member-123';
  const mockInviterId = 'inviter-123';

  const mockOrganization = {
    id: 1,
    external_id: mockOrganizationId,
    name: 'Test Organization',
    slug: 'test-org',
    plan: 'free',
    settings: '{}',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockOwnerMember = {
    organization_id: 1,
    user_id: mockOwnerId,
    role: 'owner',
    joined_at: '2024-01-01T00:00:00Z',
    invited_by: null,
    user_email: '<EMAIL>',
    user_name: 'Owner User',
    user_display_name: 'Owner User',
  };

  const mockAdminMember = {
    organization_id: 1,
    user_id: mockAdminId,
    role: 'admin',
    joined_at: '2024-01-01T00:00:00Z',
    invited_by: mockOwnerId,
    user_email: '<EMAIL>',
    user_name: 'Admin User',
    user_display_name: 'Admin User',
  };

  const mockRegularMember = {
    organization_id: 1,
    user_id: mockMemberId,
    role: 'member',
    joined_at: '2024-01-01T00:00:00Z',
    invited_by: mockAdminId,
    user_email: '<EMAIL>',
    user_name: 'Member User',
    user_display_name: 'Member User',
  };

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      // Organization CRUD
      findAll: jest.fn(),
      findById: jest.fn(),
      findByExternalId: jest.fn(),
      insert: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findBySlug: jest.fn(),

      // Organization Query
      findForUser: jest.fn(),
      checkUserMembership: jest.fn(),
      getUserRole: jest.fn(),
      getInternalId: jest.fn(),
      generateUniqueSlug: jest.fn(),

      // Member Management
      findMembers: jest.fn(),
      findMember: jest.fn(),
      insertMember: jest.fn(),
      updateMemberRole: jest.fn(),
      removeMember: jest.fn(),

      // Invitation Management
      findInvitation: jest.fn(),
      findPendingInvitations: jest.fn(),
      insertInvitation: jest.fn(),
      acceptInvitation: jest.fn(),
      revokeInvitation: jest.fn(),
      checkExistingInvitation: jest.fn(),
      deleteInvitation: jest.fn(),

      // User Profile
      findUser: jest.fn(),
      insertUser: jest.fn(),
      updateUser: jest.fn(),
      syncUserProfile: jest.fn(),
      cleanupPlaceholderEmails: jest.fn(),

      // Bulk Operations
      deleteOrganizationCascade: jest.fn(),
      getOrganizationDataCounts: jest.fn(),

      // Utility
      getPreparedStatement: jest.fn(),
    };

    service = new OrganizationMemberService(mockRepository);

    // Setup default mocks
    mockRepository.findByExternalId.mockReturnValue(mockOrganization);
    mockRepository.getUserRole.mockImplementation((orgId, userId) => {
      if (userId === mockOwnerId) return 'owner';
      if (userId === mockAdminId) return 'admin';
      if (userId === mockMemberId) return 'member';
      return null;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getMembers', () => {
    it('should return all members for an organization', async () => {
      const mockMembers = [mockOwnerMember, mockAdminMember, mockRegularMember];
      mockRepository.findMembers.mockReturnValue(mockMembers);

      const result = await service.getMembers(mockOrganizationId);

      expect(result).toHaveLength(3);
      expect(result[0]).toMatchObject({
        user_id: mockOwnerId,
        role: 'owner',
        user: expect.objectContaining({
          id: mockOwnerId,
          email: '<EMAIL>',
          name: 'Owner User',
        }),
      });
      expect(mockRepository.findMembers).toHaveBeenCalledWith(
        mockOrganizationId
      );
    });

    it('should filter members by role', async () => {
      const mockMembers = [mockOwnerMember, mockAdminMember, mockRegularMember];
      mockRepository.findMembers.mockReturnValue(mockMembers);

      const options: MemberSearchOptions = { role: 'admin' };
      const result = await service.getMembers(mockOrganizationId, options);

      expect(result).toHaveLength(1);
      expect(result[0].role).toBe('admin');
    });

    it('should filter members by search term', async () => {
      const mockMembers = [mockOwnerMember, mockAdminMember, mockRegularMember];
      mockRepository.findMembers.mockReturnValue(mockMembers);

      const options: MemberSearchOptions = { searchTerm: 'admin' };
      const result = await service.getMembers(mockOrganizationId, options);

      expect(result).toHaveLength(1);
      expect(result[0].user?.email).toBe('<EMAIL>');
    });

    it('should sort members by join date', async () => {
      const member1 = {
        ...mockRegularMember,
        joined_at: '2024-01-02T00:00:00Z',
      };
      const member2 = { ...mockAdminMember, joined_at: '2024-01-01T00:00:00Z' };
      mockRepository.findMembers.mockReturnValue([member1, member2]);

      const options: MemberSearchOptions = {
        sortBy: 'joined_at',
        sortOrder: 'asc',
      };
      const result = await service.getMembers(mockOrganizationId, options);

      expect(result[0].joined_at).toBe('2024-01-01T00:00:00Z');
      expect(result[1].joined_at).toBe('2024-01-02T00:00:00Z');
    });

    it('should apply pagination', async () => {
      const mockMembers = [mockOwnerMember, mockAdminMember, mockRegularMember];
      mockRepository.findMembers.mockReturnValue(mockMembers);

      const options: MemberSearchOptions = { limit: 2, offset: 1 };
      const result = await service.getMembers(mockOrganizationId, options);

      expect(result).toHaveLength(2);
      expect(result[0].user_id).toBe(mockAdminId);
    });

    it('should handle empty member list', async () => {
      mockRepository.findMembers.mockReturnValue([]);

      const result = await service.getMembers(mockOrganizationId);

      expect(result).toHaveLength(0);
    });

    it('should handle repository errors', async () => {
      mockRepository.findMembers.mockImplementation(() => {
        throw new Error('Database error');
      });

      await expect(service.getMembers(mockOrganizationId)).rejects.toThrow(
        'Failed to get organization members: Database error'
      );
    });
  });

  describe('getMember', () => {
    it('should return a specific member', async () => {
      mockRepository.findMember.mockReturnValue(mockAdminMember);

      const result = await service.getMember(mockOrganizationId, mockAdminId);

      expect(result).toMatchObject({
        user_id: mockAdminId,
        role: 'admin',
        user: expect.objectContaining({
          email: '<EMAIL>',
        }),
      });
      expect(mockRepository.findMember).toHaveBeenCalledWith(
        mockOrganizationId,
        mockAdminId
      );
    });

    it('should return null when member not found', async () => {
      mockRepository.findMember.mockReturnValue(null);

      const result = await service.getMember(
        mockOrganizationId,
        'non-existent'
      );

      expect(result).toBeNull();
    });

    it('should handle repository errors gracefully', async () => {
      mockRepository.findMember.mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = await service.getMember(mockOrganizationId, mockUserId);

      expect(result).toBeNull();
    });
  });

  describe('addMember', () => {
    beforeEach(() => {
      mockRepository.findMember.mockReturnValue(null); // Member doesn't exist
      mockRepository.insertMember.mockReturnValue(true);
    });

    it('should add a member with admin permission', async () => {
      const newMember = { ...mockRegularMember, user_id: mockUserId };
      mockRepository.findMember
        .mockReturnValueOnce(null) // First call for existence check
        .mockReturnValueOnce(newMember); // Second call after insertion

      const result = await service.addMember(
        mockOrganizationId,
        mockUserId,
        'member',
        mockAdminId
      );

      expect(result.success).toBe(true);
      expect(result.member?.user_id).toBe(mockUserId);
      expect(mockRepository.insertMember).toHaveBeenCalledWith(
        mockOrganizationId,
        mockUserId,
        'member',
        mockAdminId
      );
    });

    it('should add a member with owner permission', async () => {
      const newMember = { ...mockRegularMember, user_id: mockUserId };
      mockRepository.findMember
        .mockReturnValueOnce(null)
        .mockReturnValueOnce(newMember);

      const result = await service.addMember(
        mockOrganizationId,
        mockUserId,
        'admin',
        mockOwnerId
      );

      expect(result.success).toBe(true);
      expect(result.member?.role).toBe('admin');
    });

    it('should reject addition by regular member', async () => {
      const result = await service.addMember(
        mockOrganizationId,
        mockUserId,
        'member',
        mockMemberId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Members cannot manage other members');
      expect(mockRepository.insertMember).not.toHaveBeenCalled();
    });

    it('should reject adding existing member', async () => {
      mockRepository.findMember.mockReturnValue(mockRegularMember);

      const result = await service.addMember(
        mockOrganizationId,
        mockMemberId,
        'member',
        mockAdminId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('already a member');
      expect(mockRepository.insertMember).not.toHaveBeenCalled();
    });

    it('should handle repository insertion failure', async () => {
      mockRepository.insertMember.mockReturnValue(false);

      const result = await service.addMember(
        mockOrganizationId,
        mockUserId,
        'member',
        mockAdminId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to add member');
    });
  });

  describe('updateMemberRole', () => {
    beforeEach(() => {
      mockRepository.findMember.mockImplementation((orgId, userId) => {
        if (userId === mockMemberId) return mockRegularMember;
        if (userId === mockAdminId) return mockAdminMember;
        return null;
      });
      mockRepository.updateMemberRole.mockReturnValue(true);
    });

    it('should update member role with owner permission', async () => {
      const updatedMember = { ...mockRegularMember, role: 'admin' };
      mockRepository.findMember
        .mockReturnValueOnce(mockRegularMember) // Initial check
        .mockReturnValueOnce(updatedMember); // After update

      const result = await service.updateMemberRole(
        mockOrganizationId,
        mockMemberId,
        'admin',
        mockOwnerId
      );

      expect(result.success).toBe(true);
      expect(result.member?.role).toBe('admin');
      expect(mockRepository.updateMemberRole).toHaveBeenCalledWith(
        mockOrganizationId,
        mockMemberId,
        'admin'
      );
    });

    it('should update member role with admin permission', async () => {
      const updatedMember = { ...mockRegularMember, role: 'admin' };
      mockRepository.findMember
        .mockReturnValueOnce(mockRegularMember)
        .mockReturnValueOnce(updatedMember);

      const result = await service.updateMemberRole(
        mockOrganizationId,
        mockMemberId,
        'admin',
        mockAdminId
      );

      expect(result.success).toBe(true);
    });

    it('should reject role change by regular member', async () => {
      const result = await service.updateMemberRole(
        mockOrganizationId,
        mockAdminId,
        'member',
        mockMemberId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Members cannot change user roles');
      expect(mockRepository.updateMemberRole).not.toHaveBeenCalled();
    });

    it('should reject changing owner role', async () => {
      mockRepository.findMember.mockReturnValue(mockOwnerMember);

      const result = await service.updateMemberRole(
        mockOrganizationId,
        mockOwnerId,
        'admin',
        mockAdminId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Cannot change owner role');
    });

    it('should reject admin changing another admin role', async () => {
      const anotherAdmin = { ...mockAdminMember, user_id: 'admin-2' };
      mockRepository.findMember.mockReturnValue(anotherAdmin);

      const result = await service.updateMemberRole(
        mockOrganizationId,
        'admin-2',
        'member',
        mockAdminId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Admins cannot change other admin');
    });

    it('should handle non-existent member', async () => {
      mockRepository.findMember.mockReturnValue(null);

      const result = await service.updateMemberRole(
        mockOrganizationId,
        'non-existent',
        'admin',
        mockOwnerId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Member not found');
    });
  });

  describe('removeMember', () => {
    beforeEach(() => {
      mockRepository.findMember.mockImplementation((orgId, userId) => {
        if (userId === mockMemberId) return mockRegularMember;
        if (userId === mockAdminId) return mockAdminMember;
        return null;
      });
      mockRepository.removeMember.mockReturnValue(true);
    });

    it('should remove member with owner permission', async () => {
      const result = await service.removeMember(
        mockOrganizationId,
        mockMemberId,
        mockOwnerId
      );

      expect(result.success).toBe(true);
      expect(result.member?.user_id).toBe(mockMemberId);
      expect(mockRepository.removeMember).toHaveBeenCalledWith(
        mockOrganizationId,
        mockMemberId
      );
    });

    it('should remove member with admin permission (member only)', async () => {
      const result = await service.removeMember(
        mockOrganizationId,
        mockMemberId,
        mockAdminId
      );

      expect(result.success).toBe(true);
    });

    it('should allow self-removal', async () => {
      const result = await service.removeMember(
        mockOrganizationId,
        mockMemberId,
        mockMemberId
      );

      expect(result.success).toBe(true);
    });

    it('should reject removing owner', async () => {
      mockRepository.findMember.mockReturnValue(mockOwnerMember);

      const result = await service.removeMember(
        mockOrganizationId,
        mockOwnerId,
        mockAdminId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Cannot remove organization owner');
    });

    it('should reject admin removing another admin', async () => {
      const result = await service.removeMember(
        mockOrganizationId,
        mockAdminId,
        'another-admin'
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Insufficient permissions');
    });

    it('should reject removal by regular member', async () => {
      const result = await service.removeMember(
        mockOrganizationId,
        mockAdminId,
        mockMemberId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Insufficient permissions');
    });
  });

  describe('inviteMember', () => {
    beforeEach(() => {
      mockRepository.checkExistingInvitation.mockReturnValue(null);
      mockRepository.insertInvitation.mockReturnValue('invitation-123');
      mockRepository.findUser.mockReturnValue({
        id: mockInviterId,
        email: '<EMAIL>',
        name: 'Inviter User',
        display_name: 'Inviter User',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });
    });

    it('should create invitation with admin permission', async () => {
      const result = await service.inviteMember(
        mockOrganizationId,
        '<EMAIL>',
        'member',
        mockAdminId
      );

      expect(result.success).toBe(true);
      expect(result.invitation?.email).toBe('<EMAIL>');
      expect(result.invitation?.role).toBe('member');
      expect(mockRepository.insertInvitation).toHaveBeenCalled();
    });

    it('should create invitation with owner permission', async () => {
      const result = await service.inviteMember(
        mockOrganizationId,
        '<EMAIL>',
        'admin',
        mockOwnerId
      );

      expect(result.success).toBe(true);
      expect(result.invitation?.role).toBe('admin');
    });

    it('should reject invitation by regular member', async () => {
      const result = await service.inviteMember(
        mockOrganizationId,
        '<EMAIL>',
        'member',
        mockMemberId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Members cannot manage other members');
    });

    it('should handle existing accepted invitation', async () => {
      mockRepository.checkExistingInvitation.mockReturnValue({
        id: 1,
        external_id: 'inv-123',
        organization_id: 1,
        email: '<EMAIL>',
        role: 'member',
        token: 'token',
        invited_by: mockAdminId,
        expires_at: '2024-12-31T23:59:59Z',
        accepted_at: '2024-01-15T00:00:00Z',
        created_at: '2024-01-01T00:00:00Z',
      });

      const result = await service.inviteMember(
        mockOrganizationId,
        '<EMAIL>',
        'member',
        mockAdminId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('already accepted an invitation');
    });

    it('should replace existing pending invitation', async () => {
      mockRepository.checkExistingInvitation.mockReturnValue({
        id: 1,
        external_id: 'inv-123',
        organization_id: 1,
        email: '<EMAIL>',
        role: 'member',
        token: 'token',
        invited_by: mockAdminId,
        expires_at: '2024-12-31T23:59:59Z',
        accepted_at: null,
        created_at: '2024-01-01T00:00:00Z',
      });
      mockRepository.deleteInvitation.mockReturnValue(true);

      const result = await service.inviteMember(
        mockOrganizationId,
        '<EMAIL>',
        'member',
        mockAdminId
      );

      expect(result.success).toBe(true);
      expect(mockRepository.deleteInvitation).toHaveBeenCalledWith(
        mockOrganizationId,
        '<EMAIL>'
      );
    });

    it('should handle organization not found', async () => {
      mockRepository.findByExternalId.mockReturnValue(null);

      const result = await service.inviteMember(
        mockOrganizationId,
        '<EMAIL>',
        'member',
        mockAdminId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Organization not found');
    });
  });

  describe('acceptInvitation', () => {
    const mockInvitation = {
      id: 1,
      external_id: 'inv-123',
      organization_id: 1,
      email: '<EMAIL>',
      role: 'member',
      token: 'valid-token',
      invited_by: mockAdminId,
      expires_at: '2024-12-31T23:59:59Z',
      accepted_at: null,
      created_at: '2024-01-01T00:00:00Z',
    };

    beforeEach(() => {
      mockRepository.findInvitation.mockReturnValue(mockInvitation);
      mockRepository.findUser.mockReturnValue({
        id: mockUserId,
        email: '<EMAIL>',
        name: 'Invited User',
        display_name: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });
      mockRepository.checkUserMembership.mockReturnValue(false);
      mockRepository.insertMember.mockReturnValue(true);
      mockRepository.acceptInvitation.mockReturnValue(true);
      mockRepository.findById.mockReturnValue(mockOrganization);
    });

    it('should accept valid invitation', async () => {
      const result = await service.acceptInvitation('valid-token', mockUserId);

      expect(result.success).toBe(true);
      expect(result.organization?.name).toBe('Test Organization');
      expect(mockRepository.insertMember).toHaveBeenCalledWith(
        mockOrganizationId,
        mockUserId,
        'member',
        mockAdminId
      );
      expect(mockRepository.acceptInvitation).toHaveBeenCalledWith('1');
    });

    it('should reject invalid token', async () => {
      mockRepository.findInvitation.mockReturnValue(null);

      const result = await service.acceptInvitation(
        'invalid-token',
        mockUserId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid or expired invitation');
    });

    it('should reject expired invitation', async () => {
      const expiredInvitation = {
        ...mockInvitation,
        expires_at: '2020-01-01T00:00:00Z',
      };
      mockRepository.findInvitation.mockReturnValue(expiredInvitation);

      const result = await service.acceptInvitation(
        'expired-token',
        mockUserId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('invitation has expired');
    });

    it('should reject mismatched email', async () => {
      mockRepository.findUser.mockReturnValue({
        id: mockUserId,
        email: '<EMAIL>',
        name: 'User',
        display_name: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });

      const result = await service.acceptInvitation('valid-token', mockUserId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('different email address');
    });

    it('should reject if user is already a member', async () => {
      mockRepository.checkUserMembership.mockReturnValue(true);

      const result = await service.acceptInvitation('valid-token', mockUserId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('already a member');
    });
  });

  describe('bulkMemberOperation', () => {
    const mockUserIds = ['user1', 'user2', 'user3'];

    beforeEach(() => {
      // Mock members exist
      mockRepository.findMember.mockImplementation((orgId, userId) => {
        return mockUserIds.includes(userId)
          ? { ...mockRegularMember, user_id: userId }
          : null;
      });
    });

    it('should perform bulk role updates with owner permission', async () => {
      mockRepository.updateMemberRole.mockReturnValue(true);

      const operation: BulkMemberOperation = {
        userIds: mockUserIds,
        operation: 'update_role',
        newRole: 'admin',
      };

      const result = await service.bulkMemberOperation(
        mockOrganizationId,
        operation,
        mockOwnerId
      );

      expect(result.success).toBe(true);
      expect(result.results).toHaveLength(3);
      expect(result.results.every(r => r.success)).toBe(true);
      expect(mockRepository.updateMemberRole).toHaveBeenCalledTimes(3);
    });

    it('should perform bulk member removal with owner permission', async () => {
      mockRepository.removeMember.mockReturnValue(true);

      const operation: BulkMemberOperation = {
        userIds: mockUserIds,
        operation: 'remove',
      };

      const result = await service.bulkMemberOperation(
        mockOrganizationId,
        operation,
        mockOwnerId
      );

      expect(result.success).toBe(true);
      expect(result.results).toHaveLength(3);
      expect(mockRepository.removeMember).toHaveBeenCalledTimes(3);
    });

    it('should reject bulk operations by regular member', async () => {
      const operation: BulkMemberOperation = {
        userIds: mockUserIds,
        operation: 'remove',
      };

      const result = await service.bulkMemberOperation(
        mockOrganizationId,
        operation,
        mockMemberId
      );

      expect(result.success).toBe(false);
      expect(result.errors[0]).toContain(
        'Members cannot perform bulk operations'
      );
    });

    it('should handle partial failures in bulk operations', async () => {
      mockRepository.updateMemberRole
        .mockReturnValueOnce(true)
        .mockReturnValueOnce(false)
        .mockReturnValueOnce(true);

      const operation: BulkMemberOperation = {
        userIds: mockUserIds,
        operation: 'update_role',
        newRole: 'admin',
      };

      const result = await service.bulkMemberOperation(
        mockOrganizationId,
        operation,
        mockOwnerId
      );

      expect(result.success).toBe(true); // At least one success
      expect(result.results.filter(r => r.success)).toHaveLength(2);
      expect(result.results.filter(r => !r.success)).toHaveLength(1);
    });

    it('should handle missing new role for update operation', async () => {
      const operation: BulkMemberOperation = {
        userIds: ['user1'],
        operation: 'update_role',
        // Missing newRole
      };

      const result = await service.bulkMemberOperation(
        mockOrganizationId,
        operation,
        mockOwnerId
      );

      expect(result.success).toBe(false);
      expect(result.errors[0]).toContain('No new role specified');
    });
  });

  describe('Permission Validation', () => {
    describe('validateMemberManagementPermission', () => {
      it('should authorize owner', async () => {
        const result = await service.validateMemberManagementPermission(
          mockOrganizationId,
          mockOwnerId
        );

        expect(result.authorized).toBe(true);
        expect(result.currentRole).toBe('owner');
      });

      it('should authorize admin', async () => {
        const result = await service.validateMemberManagementPermission(
          mockOrganizationId,
          mockAdminId
        );

        expect(result.authorized).toBe(true);
        expect(result.currentRole).toBe('admin');
      });

      it('should reject member', async () => {
        const result = await service.validateMemberManagementPermission(
          mockOrganizationId,
          mockMemberId
        );

        expect(result.authorized).toBe(false);
        expect(result.reason).toContain('Members cannot manage other members');
        expect(result.currentRole).toBe('member');
        expect(result.requiredRole).toBe('admin');
      });

      it('should reject non-member', async () => {
        mockRepository.getUserRole.mockReturnValue(null);

        const result = await service.validateMemberManagementPermission(
          mockOrganizationId,
          'non-member'
        );

        expect(result.authorized).toBe(false);
        expect(result.reason).toContain('not a member');
        expect(result.currentRole).toBe('none');
      });
    });

    describe('validateRoleChangePermission', () => {
      it('should allow owner to change any non-owner role', async () => {
        const result = await service.validateRoleChangePermission(
          mockOrganizationId,
          mockAdminId,
          'member',
          mockOwnerId
        );

        expect(result.authorized).toBe(true);
      });

      it('should allow admin to change member role', async () => {
        const result = await service.validateRoleChangePermission(
          mockOrganizationId,
          mockMemberId,
          'admin',
          mockAdminId
        );

        expect(result.authorized).toBe(true);
      });

      it('should reject admin changing admin role', async () => {
        const result = await service.validateRoleChangePermission(
          mockOrganizationId,
          'another-admin',
          'member',
          mockAdminId
        );

        // Mock another admin
        mockRepository.getUserRole.mockImplementation((orgId, userId) => {
          if (userId === 'another-admin') return 'admin';
          return mockRepository.getUserRole.mock.results[0]?.value || null;
        });

        const result2 = await service.validateRoleChangePermission(
          mockOrganizationId,
          'another-admin',
          'member',
          mockAdminId
        );
        expect(result2.authorized).toBe(false);
        expect(result2.reason).toContain('Admins cannot change other admin');
      });

      it('should reject changing owner role', async () => {
        const result = await service.validateRoleChangePermission(
          mockOrganizationId,
          mockOwnerId,
          'admin',
          mockAdminId
        );

        expect(result.authorized).toBe(false);
      });
    });

    describe('validateMemberRemovalPermission', () => {
      it('should allow owner to remove any non-owner', async () => {
        const result = await service.validateMemberRemovalPermission(
          mockOrganizationId,
          mockAdminId,
          mockOwnerId
        );

        expect(result.authorized).toBe(true);
      });

      it('should allow admin to remove member', async () => {
        const result = await service.validateMemberRemovalPermission(
          mockOrganizationId,
          mockMemberId,
          mockAdminId
        );

        expect(result.authorized).toBe(true);
      });

      it('should allow self-removal for non-owners', async () => {
        const result = await service.validateMemberRemovalPermission(
          mockOrganizationId,
          mockMemberId,
          mockMemberId
        );

        expect(result.authorized).toBe(true);
      });

      it('should reject removing owner', async () => {
        const result = await service.validateMemberRemovalPermission(
          mockOrganizationId,
          mockOwnerId,
          mockAdminId
        );

        expect(result.authorized).toBe(false);
        expect(result.reason).toContain('Cannot remove organization owner');
      });

      it('should reject admin removing admin', async () => {
        const result = await service.validateMemberRemovalPermission(
          mockOrganizationId,
          'another-admin',
          mockAdminId
        );

        // Mock another admin for this test
        mockRepository.getUserRole.mockImplementation((orgId, userId) => {
          if (userId === 'another-admin') return 'admin';
          if (userId === mockAdminId) return 'admin';
          return null;
        });

        const result2 = await service.validateMemberRemovalPermission(
          mockOrganizationId,
          'another-admin',
          mockAdminId
        );
        expect(result2.authorized).toBe(false);
        expect(result2.reason).toContain('Insufficient permissions');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle repository errors in getMembers', async () => {
      mockRepository.findMembers.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      await expect(service.getMembers(mockOrganizationId)).rejects.toThrow(
        'Failed to get organization members: Database connection failed'
      );
    });

    it('should handle repository errors in permission validation', async () => {
      mockRepository.getUserRole.mockImplementation(() => {
        throw new Error('Permission check failed');
      });

      const result = await service.validateMemberManagementPermission(
        mockOrganizationId,
        mockUserId
      );

      expect(result.authorized).toBe(false);
      expect(result.reason).toContain('Error checking permissions');
    });

    it('should handle missing organization in operations', async () => {
      mockRepository.findByExternalId.mockReturnValue(null);

      const result = await service.addMember(
        mockOrganizationId,
        mockUserId,
        'member',
        mockAdminId
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Organization not found');
    });
  });
});
