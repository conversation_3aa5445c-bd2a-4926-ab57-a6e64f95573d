/**
 * @file react-null-safety.ts
 * @description Performance-optimized null safety utilities for React components
 */

import { useCallback, useMemo } from 'react';
import { isNotNullish, safeArrayAccess, getOrDefault } from '../../shared/types/type-guards';

/**
 * Performance-optimized null-safe array access hook
 * Memoizes array access to prevent unnecessary re-renders
 */
export function useSafeArray<T>(array: T[] | null | undefined, index: number): T | undefined {
  return useMemo(() => {
    return safeArrayAccess(array, index);
  }, [array, index]);
}

/**
 * Performance-optimized null-safe object property access hook
 * Memoizes property access to prevent unnecessary re-renders
 */
export function useSafeProperty<T, K extends keyof T>(
  obj: T | null | undefined, 
  property: K,
  defaultValue: T[K]
): T[K] {
  return useMemo(() => {
    return obj?.[property] ?? defaultValue;
  }, [obj, property, defaultValue]);
}

/**
 * Memoized safe array filter with null checks
 * Optimized for large arrays with potential null/undefined elements
 */
export function useSafeFilter<T>(
  array: (T | null | undefined)[] | null | undefined,
  predicate?: (item: T) => boolean
): T[] {
  return useMemo(() => {
    if (!array || !Array.isArray(array)) return [];
    
    const filtered = array.filter(isNotNullish);
    return predicate ? filtered.filter(predicate) : filtered;
  }, [array, predicate]);
}

/**
 * Memoized safe array mapping with null checks
 * Optimized for transforming arrays while handling null/undefined elements
 */
export function useSafeMap<T, U>(
  array: (T | null | undefined)[] | null | undefined,
  mapper: (item: T, index: number) => U
): U[] {
  return useMemo(() => {
    if (!array || !Array.isArray(array)) return [];
    
    return array
      .filter(isNotNullish)
      .map(mapper);
  }, [array, mapper]);
}

/**
 * Performance-optimized null-safe event handler creator
 * Prevents recreation of handlers on every render
 */
export function useSafeCallback<T extends any[]>(
  callback: ((...args: T) => void) | null | undefined,
  dependencies: any[]
): (...args: T) => void {
  return useCallback((...args: T) => {
    callback?.(...args);
  }, dependencies);
}

/**
 * Safe default value provider with memoization
 * Optimized for expensive default value calculations
 */
export function useSafeDefault<T>(
  value: T | null | undefined,
  defaultFactory: () => T
): T {
  return useMemo(() => {
    return value ?? defaultFactory();
  }, [value, defaultFactory]);
}

/**
 * Type-safe component prop validator
 * Validates props at runtime and provides safe defaults
 */
export function validateComponentProps<T extends Record<string, any>>(
  props: Partial<T>,
  required: (keyof T)[],
  defaults: Partial<T> = {}
): T {
  const validated = { ...defaults } as T;
  
  // Check required props
  for (const key of required) {
    if (!isNotNullish(props[key])) {
      throw new Error(`Missing required prop: ${String(key)}`);
    }
    validated[key] = props[key]!;
  }
  
  // Add optional props
  for (const [key, value] of Object.entries(props)) {
    if (isNotNullish(value) && !required.includes(key)) {
      (validated as any)[key] = value;
    }
  }
  
  return validated;
}

/**
 * Safe array slice with memoization
 * Optimized for pagination and virtual scrolling
 */
export function useSafeSlice<T>(
  array: T[] | null | undefined,
  start: number,
  end?: number
): T[] {
  return useMemo(() => {
    if (!array || !Array.isArray(array)) return [];
    return array.slice(start, end);
  }, [array, start, end]);
}

/**
 * Optimized null-safe string operations
 */
export const SafeString = {
  /**
   * Safe string splitting with null checks
   */
  split: (str: string | null | undefined, separator: string): string[] => {
    if (!str || typeof str !== 'string') return [];
    return str.split(separator).filter(part => part.length > 0);
  },

  /**
   * Safe string trimming with fallback
   */
  trim: (str: string | null | undefined, fallback = ''): string => {
    if (!str || typeof str !== 'string') return fallback;
    return str.trim() || fallback;
  },

  /**
   * Safe string case conversion
   */
  toLowerCase: (str: string | null | undefined): string => {
    if (!str || typeof str !== 'string') return '';
    return str.toLowerCase();
  },

  /**
   * Safe string truncation
   */
  truncate: (str: string | null | undefined, maxLength: number, suffix = '...'): string => {
    if (!str || typeof str !== 'string') return '';
    if (str.length <= maxLength) return str;
    return str.slice(0, maxLength - suffix.length) + suffix;
  }
};

/**
 * Optimized null-safe number operations
 */
export const SafeNumber = {
  /**
   * Safe number parsing with validation
   */
  parse: (value: unknown, defaultValue = 0): number => {
    if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
      return value;
    }
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      if (!isNaN(parsed) && isFinite(parsed)) {
        return parsed;
      }
    }
    return defaultValue;
  },

  /**
   * Safe number clamping
   */
  clamp: (value: number | null | undefined, min: number, max: number): number => {
    const safeValue = SafeNumber.parse(value);
    return Math.max(min, Math.min(max, safeValue));
  },

  /**
   * Safe percentage calculation
   */
  toPercentage: (value: number | null | undefined, total: number): number => {
    const safeValue = SafeNumber.parse(value);
    const safeTotal = SafeNumber.parse(total, 1); // Avoid division by zero
    return safeTotal === 0 ? 0 : (safeValue / safeTotal) * 100;
  }
};

/**
 * React key generator for safe list rendering
 * Ensures unique keys even with null/undefined data
 */
export function generateSafeKey(
  item: any, 
  index: number, 
  prefix = 'item'
): string {
  if (item && typeof item === 'object' && 'id' in item && item.id) {
    return `${prefix}-${item.id}`;
  }
  if (item && typeof item === 'object' && 'key' in item && item.key) {
    return `${prefix}-${item.key}`;
  }
  return `${prefix}-${index}`;
}

/**
 * Safe component children validator
 * Ensures children prop is properly typed and handled
 */
export function validateChildren(children: React.ReactNode): React.ReactElement[] {
  if (!children) return [];
  
  const childArray = Array.isArray(children) ? children : [children];
  return childArray
    .filter(child => child !== null && child !== undefined)
    .filter((child): child is React.ReactElement => 
      typeof child === 'object' && 'type' in child
    );
}

/**
 * Performance monitoring for null safety operations
 * Tracks performance impact of null checking in development
 */
export const NullSafetyPerformance = {
  measureOperation: <T>(operation: () => T, label: string): T => {
    if (process.env.NODE_ENV === 'development') {
      const start = performance.now();
      const result = operation();
      const end = performance.now();
      
      if (end - start > 1) { // Only log if operation takes > 1ms
        console.debug(`🔍 Null Safety Operation: ${label} took ${(end - start).toFixed(2)}ms`);
      }
      
      return result;
    }
    return operation();
  }
};

/**
 * React Context safe value provider
 * Ensures context values are never null/undefined
 */
export function createSafeContext<T>(defaultValue: T) {
  return {
    provide: (value: T | null | undefined): T => value ?? defaultValue,
    consume: (value: T | null | undefined): T => value ?? defaultValue
  };
}

/**
 * Type-safe props spreading utility
 * Safely spreads props while filtering out null/undefined values
 */
export function spreadSafeProps<T extends Record<string, any>>(
  props: Partial<T>,
  exclude: (keyof T)[] = []
): Partial<T> {
  const safe: Partial<T> = {};
  
  for (const [key, value] of Object.entries(props)) {
    if (!exclude.includes(key as keyof T) && isNotNullish(value)) {
      (safe as any)[key] = value;
    }
  }
  
  return safe;
}