/**
 * @file zoho-email.service.ts
 * @description Refactored Zoho Email Service using modular architecture with focused services
 */

import { LoggerFactory, ILogger } from '../../utils/logger.service';
import { ZohoTokenManager } from './zoho-token-manager';
import { EmailSender, EmailOptions, InvitationData } from './email-sender';

export interface ZohoEmailServiceConfig {
  tokenManager?: {
    refreshCooldownMs?: number;
    maxRetryAttempts?: number;
    baseRetryDelayMs?: number;
    maxRetryDelayMs?: number;
    circuitBreakerFailures?: number;
    circuitBreakerTimeoutMs?: number;
  };
  emailSender?: {
    maxRetryAttempts?: number;
    baseRetryDelayMs?: number;
    maxRetryDelayMs?: number;
    requestTimeoutMs?: number;
    enableRetryQueue?: boolean;
  };
}

/**
 * Refactored Zoho Email Service with modular architecture
 */
export class ZohoEmailService {
  private readonly logger: ILogger;
  private readonly tokenManager: ZohoTokenManager;
  private readonly emailSender: EmailSender;
  private initialized: boolean = false;

  constructor(logger?: ILogger) {
    this.logger =
      logger || LoggerFactory.getInstance().createLogger('ZohoEmailService');

    // Initialize focused services
    this.tokenManager = new ZohoTokenManager(this.logger);
    this.emailSender = new EmailSender(this.tokenManager, this.logger);

    this.logger.info(
      'Zoho Email Service initialized with modular architecture'
    );
  }

  /**
   * Initialize the email service
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      this.logger.debug('Service already initialized');
      return;
    }

    this.logger.info('Initializing Zoho Email Service', {
      operation: 'initialize',
    });

    try {
      // Initialize token manager
      await this.tokenManager.initialize();

      this.initialized = true;

      this.logger.info('Zoho Email Service initialized successfully', {
        tokenStatus: this.tokenManager.getStatus(),
        operation: 'initialize',
      });
    } catch (error) {
      this.logger.error(
        'Failed to initialize Zoho Email Service',
        error as Error,
        {
          operation: 'initialize',
        }
      );
      throw error;
    }
  }

  /**
   * Send email using the email sender service
   */
  async sendEmail(
    options: EmailOptions,
    enableRetryQueue: boolean = true
  ): Promise<boolean> {
    this.ensureInitialized();

    // Configure retry queue setting
    this.emailSender.configure({ enableRetryQueue });

    const result = await this.emailSender.sendEmail(options);
    return result.success;
  }

  /**
   * Send invitation email with high priority
   */
  async sendInvitationEmail(
    to: string,
    invitation: InvitationData
  ): Promise<boolean> {
    this.ensureInitialized();

    const result = await this.emailSender.sendInvitationEmail(to, invitation);
    return result.success;
  }

  /**
   * Get retry queue statistics
   */
  getRetryQueueStats(): {
    totalQueued: number;
    readyToProcess: number;
    processed: number;
    failed: number;
    oldestEmail?: number;
  } {
    return this.emailSender.getRetryQueueStats();
  }

  /**
   * Get service health status
   */
  getHealthStatus(): {
    initialized: boolean;
    tokenManager: any;
    emailSender: any;
    overallHealth: boolean;
  } {
    const tokenStatus = this.tokenManager.getStatus();
    const emailConfig = this.emailSender.getConfiguration();

    const overallHealth =
      this.initialized &&
      tokenStatus.hasValidToken &&
      tokenStatus.circuitBreakerState !== 'OPEN';

    return {
      initialized: this.initialized,
      tokenManager: tokenStatus,
      emailSender: emailConfig,
      overallHealth,
    };
  }

  /**
   * Configure the email service
   */
  configure(config: ZohoEmailServiceConfig): void {
    this.logger.info('Configuring Zoho Email Service', {
      config,
      operation: 'configure',
    });

    if (config.tokenManager) {
      this.tokenManager.configure(config.tokenManager);
    }

    if (config.emailSender) {
      this.emailSender.configure(config.emailSender);
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): {
    tokenManager: any;
    emailSender: any;
  } {
    return {
      tokenManager: this.tokenManager.getConfiguration(),
      emailSender: this.emailSender.getConfiguration(),
    };
  }

  /**
   * Force token refresh (for testing/debugging)
   */
  async refreshToken(): Promise<void> {
    this.ensureInitialized();

    // Force token refresh by requesting a valid token
    await this.tokenManager.getValidToken();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.logger.info('Cleaning up Zoho Email Service');

    this.tokenManager.cleanup();
    this.initialized = false;
  }

  // Private methods

  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error(
        'Zoho Email Service not initialized. Call initialize() first.'
      );
    }
  }
}

// Export singleton instance
export const zohoEmailService = new ZohoEmailService();
