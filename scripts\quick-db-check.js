#!/usr/bin/env node

/**
 * Quick database check to see what's happening
 */

const Database = require('better-sqlite3');
const path = require('path');

// Test database connection directly
const dbPath = path.join(require('os').homedir(), 'Library/Application Support/chroma-sync/chromasync.db');

console.log('🔍 Quick Database Check...');
console.log('Database path:', dbPath);

try {
  console.log('\n1️⃣ Testing direct database connection...');
  const db = new Database(dbPath);
  
  console.log('✅ Database opened successfully');
  
  console.log('\n2️⃣ Testing basic query...');
  const result = db.prepare('SELECT 1 as test').get();
  console.log('✅ Basic query works:', result);
  
  console.log('\n3️⃣ Checking tables...');
  const tables = db.prepare(`
    SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
  `).all();
  console.log('Tables found:', tables.length);
  console.log('Table names:', tables.map(t => t.name));
  
  console.log('\n4️⃣ Testing organization query...');
  const orgs = db.prepare('SELECT COUNT(*) as count FROM organizations').get();
  console.log('Organization count:', orgs);
  
  console.log('\n5️⃣ Testing color query...');
  const colors = db.prepare('SELECT COUNT(*) as count FROM colors').get();
  console.log('Color count:', colors);
  
  db.close();
  console.log('\n🎉 All tests passed! Database is working fine.');
  
} catch (error) {
  console.error('\n❌ Database test failed:', error);
  console.error('Error details:', {
    message: error.message,
    code: error.code,
    stack: error.stack
  });
}