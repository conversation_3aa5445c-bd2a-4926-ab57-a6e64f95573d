/**
 * Performance Optimizer for ChromaSync Database
 * Handles index creation and prepared statement warming
 */

import fs from 'fs';
import path from 'path';
import { advancedPool } from './advanced-pool';

interface PreparedStatementCache {
  [key: string]: {
    query: string;
    lastUsed: number;
    useCount: number;
    averageExecutionTime: number;
  };
}

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private preparedStatements: PreparedStatementCache = {};
  private indexesApplied = false;
  private warmupCompleted = false;

  private constructor() {}

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  /**
   * Apply performance indexes to the database
   */
  async applyPerformanceIndexes(): Promise<void> {
    if (this.indexesApplied) {
      console.log(
        '[PerformanceOptimizer] Indexes already applied, skipping...'
      );
      return;
    }

    try {
      console.log('[PerformanceOptimizer] Applying performance indexes...');

      // Read the SQL file
      const sqlFilePath = path.join(__dirname, 'performance-indexes.sql');
      const sqlContent = fs.readFileSync(sqlFilePath, 'utf-8');

      // Split by semicolons and filter out comments and empty lines
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(
          stmt =>
            stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('/*')
        );

      let appliedCount = 0;
      let skippedCount = 0;

      for (const statement of statements) {
        try {
          if (statement.toLowerCase().includes('create index')) {
            await advancedPool.executeUpdate(statement);
            appliedCount++;
          }
        } catch (error: any) {
          if (error.message?.includes('already exists')) {
            skippedCount++;
          } else {
            console.error(
              '[PerformanceOptimizer] Error applying index:',
              error
            );
            throw error;
          }
        }
      }

      // Update database statistics for query planner
      await advancedPool.executeUpdate('ANALYZE');

      this.indexesApplied = true;
      console.log(
        `[PerformanceOptimizer] Performance optimization complete: ${appliedCount} indexes applied, ${skippedCount} already existed`
      );
    } catch (error) {
      console.error(
        '[PerformanceOptimizer] Failed to apply performance indexes:',
        error
      );
      throw error;
    }
  }

  /**
   * Warm up prepared statements with common queries
   */
  async warmupPreparedStatements(): Promise<void> {
    if (this.warmupCompleted) {
      console.log(
        '[PerformanceOptimizer] Prepared statements already warmed up, skipping...'
      );
      return;
    }

    try {
      console.log('[PerformanceOptimizer] Warming up prepared statements...');

      const commonQueries = [
        // Color queries
        {
          name: 'colors_get_all_active',
          query:
            'SELECT * FROM colors WHERE deleted_at IS NULL ORDER BY updated_at DESC',
        },
        {
          name: 'colors_get_by_external_id',
          query:
            'SELECT * FROM colors WHERE external_id = ? AND deleted_at IS NULL',
        },
        {
          name: 'colors_get_by_hex',
          query: 'SELECT * FROM colors WHERE hex = ? AND deleted_at IS NULL',
        },
        {
          name: 'colors_get_by_product',
          query: `SELECT c.* FROM colors c 
                  JOIN product_colors pc ON (
                    c.id = pc.color_id AND 
                    pc.organization_id = ?
                  )
                  JOIN products p ON (
                    p.id = pc.product_id AND 
                    p.organization_id = ?
                  )
                  WHERE p.external_id = ? AND c.organization_id = ? AND c.deleted_at IS NULL`,
        },
        {
          name: 'colors_search_by_name',
          query:
            'SELECT * FROM colors WHERE LOWER(name) LIKE LOWER(?) AND deleted_at IS NULL LIMIT 50',
        },
        {
          name: 'colors_search_by_code',
          query:
            'SELECT * FROM colors WHERE LOWER(code) LIKE LOWER(?) AND deleted_at IS NULL LIMIT 50',
        },

        // Product queries
        {
          name: 'products_get_all_active',
          query:
            'SELECT * FROM products WHERE deleted_at IS NULL ORDER BY name',
        },
        {
          name: 'products_get_by_external_id',
          query:
            'SELECT * FROM products WHERE external_id = ? AND deleted_at IS NULL',
        },
        {
          name: 'products_with_colors',
          query: `SELECT p.*, COUNT(pc.color_id) as color_count 
                  FROM products p 
                  LEFT JOIN product_colors pc ON p.id = pc.product_id 
                  WHERE p.deleted_at IS NULL 
                  GROUP BY p.id 
                  ORDER BY p.name`,
        },

        // CMYK queries
        {
          name: 'cmyk_get_by_color_id',
          query: 'SELECT * FROM color_cmyk WHERE color_id = ?',
        },
        {
          name: 'cmyk_search_similar',
          query: `SELECT cc.*, c.* FROM color_cmyk cc 
                  JOIN colors c ON c.id = cc.color_id 
                  WHERE ABS(cc.c - ?) <= 5 AND ABS(cc.m - ?) <= 5 
                  AND ABS(cc.y - ?) <= 5 AND ABS(cc.k - ?) <= 5 
                  AND c.deleted_at IS NULL`,
        },

        // Datasheet queries
        {
          name: 'datasheets_get_by_product',
          query: `SELECT d.* FROM datasheets d 
                  JOIN products p ON p.id = d.product_id 
                  WHERE p.external_id = ? AND d.deleted_at IS NULL`,
        },

        // Sync queries
        {
          name: 'colors_updated_since',
          query:
            'SELECT * FROM colors WHERE updated_at > ? ORDER BY updated_at',
        },
        {
          name: 'products_updated_since',
          query:
            'SELECT * FROM products WHERE updated_at > ? ORDER BY updated_at',
        },

        // Statistics queries
        {
          name: 'colors_count_by_source',
          query: `SELECT cs.name, COUNT(c.id) as count 
                  FROM color_sources cs 
                  LEFT JOIN colors c ON c.source_id = cs.id AND c.deleted_at IS NULL 
                  GROUP BY cs.id, cs.name`,
        },
        {
          name: 'colors_count_total',
          query:
            'SELECT COUNT(*) as total FROM colors WHERE deleted_at IS NULL',
        },
      ];

      for (const queryDef of commonQueries) {
        try {
          const startTime = Date.now();

          // Execute a test query to warm up the prepared statement
          await advancedPool.executeQuery(queryDef.query, []);

          const executionTime = Date.now() - startTime;

          // Cache the prepared statement info
          this.preparedStatements[queryDef.name] = {
            query: queryDef.query,
            lastUsed: Date.now(),
            useCount: 1,
            averageExecutionTime: executionTime,
          };

          console.log(
            `[PerformanceOptimizer] Warmed up: ${queryDef.name} (${executionTime}ms)`
          );
        } catch (error) {
          console.warn(
            `[PerformanceOptimizer] Failed to warm up ${queryDef.name}:`,
            error
          );
        }
      }

      this.warmupCompleted = true;
      console.log('[PerformanceOptimizer] Prepared statement warmup completed');
    } catch (error) {
      console.error(
        '[PerformanceOptimizer] Failed to warm up prepared statements:',
        error
      );
      throw error;
    }
  }

  /**
   * Get a warmed prepared statement by name
   */
  getPreparedStatement(name: string): string | null {
    const stmt = this.preparedStatements[name];
    if (stmt) {
      stmt.lastUsed = Date.now();
      stmt.useCount++;
      return stmt.query;
    }
    return null;
  }

  /**
   * Update execution time for a prepared statement
   */
  updateExecutionTime(name: string, executionTime: number): void {
    const stmt = this.preparedStatements[name];
    if (stmt) {
      // Calculate running average
      stmt.averageExecutionTime =
        (stmt.averageExecutionTime * (stmt.useCount - 1) + executionTime) /
        stmt.useCount;
    }
  }

  /**
   * Analyze database performance
   */
  async analyzePerformance(): Promise<{
    tableStats: any[];
    indexStats: any[];
    queryPlanStats: any[];
    recommendations: string[];
  }> {
    try {
      // Get table statistics
      const tableStats = await advancedPool.executeQuery(`
        SELECT 
          name,
          (SELECT COUNT(*) FROM pragma_table_info(name)) as column_count
        FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `);

      // Get index statistics
      const indexStats = await advancedPool.executeQuery(`
        SELECT 
          name,
          tbl_name,
          sql
        FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
        ORDER BY tbl_name, name
      `);

      // Get query plan for common queries
      const queryPlanStats = [];
      for (const [name, stmt] of Object.entries(this.preparedStatements)) {
        try {
          const plan = await advancedPool.executeQuery(
            `EXPLAIN QUERY PLAN ${stmt.query}`,
            []
          );
          queryPlanStats.push({ name, plan });
        } catch (error) {
          console.warn(`Failed to get query plan for ${name}:`, error);
        }
      }

      // Generate recommendations
      const recommendations = this.generatePerformanceRecommendations();

      return {
        tableStats,
        indexStats,
        queryPlanStats,
        recommendations,
      };
    } catch (error) {
      console.error(
        '[PerformanceOptimizer] Failed to analyze performance:',
        error
      );
      throw error;
    }
  }

  /**
   * Generate performance recommendations
   */
  private generatePerformanceRecommendations(): string[] {
    const recommendations: string[] = [];

    // Check prepared statement usage
    const lowUsageStatements = Object.entries(this.preparedStatements)
      .filter(([_, stmt]) => stmt.useCount < 10)
      .map(([name]) => name);

    if (lowUsageStatements.length > 0) {
      recommendations.push(
        `Consider removing unused prepared statements: ${lowUsageStatements.join(', ')}`
      );
    }

    // Check slow queries
    const slowStatements = Object.entries(this.preparedStatements)
      .filter(([_, stmt]) => stmt.averageExecutionTime > 100)
      .map(
        ([name, stmt]) => `${name} (${stmt.averageExecutionTime.toFixed(2)}ms)`
      );

    if (slowStatements.length > 0) {
      recommendations.push(
        `Optimize slow queries: ${slowStatements.join(', ')}`
      );
    }

    // Generic recommendations
    if (!this.indexesApplied) {
      recommendations.push('Apply performance indexes to improve query speed');
    }

    if (!this.warmupCompleted) {
      recommendations.push(
        'Complete prepared statement warmup for better performance'
      );
    }

    return recommendations;
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats() {
    const poolStats = advancedPool.getPerformanceMetrics();

    return {
      pool: poolStats,
      preparedStatements: {
        total: Object.keys(this.preparedStatements).length,
        warmedUp: this.warmupCompleted,
        averageExecutionTime: this.calculateAverageExecutionTime(),
        mostUsed: this.getMostUsedStatements(5),
      },
      optimization: {
        indexesApplied: this.indexesApplied,
        warmupCompleted: this.warmupCompleted,
      },
    };
  }

  private calculateAverageExecutionTime(): number {
    const statements = Object.values(this.preparedStatements);
    if (statements.length === 0) {
      return 0;
    }

    const total = statements.reduce(
      (sum, stmt) => sum + stmt.averageExecutionTime,
      0
    );
    return total / statements.length;
  }

  private getMostUsedStatements(limit: number) {
    return Object.entries(this.preparedStatements)
      .sort(([, a], [, b]) => b.useCount - a.useCount)
      .slice(0, limit)
      .map(([name, stmt]) => ({
        name,
        useCount: stmt.useCount,
        averageExecutionTime: stmt.averageExecutionTime,
      }));
  }

  /**
   * Initialize the performance optimizer
   */
  async initialize(): Promise<void> {
    try {
      console.log(
        '[PerformanceOptimizer] Initializing performance optimizer...'
      );

      // Apply indexes first
      await this.applyPerformanceIndexes();

      // Then warm up prepared statements
      await this.warmupPreparedStatements();

      console.log(
        '[PerformanceOptimizer] Performance optimizer initialized successfully'
      );
    } catch (error) {
      console.error('[PerformanceOptimizer] Failed to initialize:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const performanceOptimizer = PerformanceOptimizer.getInstance();
