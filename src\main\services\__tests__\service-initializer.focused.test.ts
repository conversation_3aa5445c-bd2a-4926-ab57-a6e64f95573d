/**
 * @file service-initializer.focused.test.ts
 * @description Focused tests for service initialization with minimal mocking
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
    createServiceConfig,
    ServiceConfig,
} from '../service-initializer';

// Mock electron for tests that don't need it
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/test/userData'),
  },
}));

describe('Service Initializer - Focused Tests', () => {
  beforeEach(() => {
    // Clean environment for each test
    vi.clearAllMocks();
  });

  describe('createServiceConfig', () => {
    beforeEach(() => {
      // Reset environment variables
      delete process.env.ZOHO_CLIENT_ID;
      delete process.env.ZOHO_CLIENT_SECRET;
      delete process.env.ZOHO_REFRESH_TOKEN;
      delete process.env.ZOHO_ACCOUNT_ID;
      delete process.env.ZOHO_REGION;
      delete process.env.ZOHO_SUPPORT_ALIAS;
      delete process.env.SUPABASE_URL;
      delete process.env.SUPABASE_ANON_KEY;
    });

    it('should create configuration with all environment variables set', () => {
      // Arrange
      process.env.ZOHO_CLIENT_ID = 'test-client-id';
      process.env.ZOHO_CLIENT_SECRET = 'test-client-secret';
      process.env.ZOHO_REFRESH_TOKEN = 'test-refresh-token';
      process.env.ZOHO_ACCOUNT_ID = 'test-account-id';
      process.env.ZOHO_REGION = 'EU';
      process.env.ZOHO_SUPPORT_ALIAS = '<EMAIL>';
      process.env.SUPABASE_URL = 'https://test.supabase.co';
      process.env.SUPABASE_ANON_KEY = 'test-anon-key';

      // Act
      const config = createServiceConfig();

      // Assert
      expect(config).toEqual({
        zoho: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          refreshToken: 'test-refresh-token',
          accountId: 'test-account-id',
          region: 'EU',
          supportAlias: '<EMAIL>',
        },
        supabase: {
          url: 'https://test.supabase.co',
          anonKey: 'test-anon-key',
        },
      });
    });

    it('should use default values when environment variables are missing', () => {
      // Act
      const config = createServiceConfig();

      // Assert
      expect(config.zoho.region).toBe('US');
      expect(config.zoho.supportAlias).toBe('<EMAIL>');
      expect(config.zoho.clientId).toBeUndefined();
      expect(config.supabase.url).toBeUndefined();
    });

    it('should handle empty string environment variables', () => {
      // Arrange
      process.env.ZOHO_CLIENT_ID = '';
      process.env.SUPABASE_URL = '';

      // Act
      const config = createServiceConfig();

      // Assert
      expect(config.zoho.clientId).toBe('');
      expect(config.supabase.url).toBe('');
    });

    it('should maintain consistent structure regardless of environment', () => {
      // Act
      const config = createServiceConfig();

      // Assert - Verify all required properties exist
      expect(config).toHaveProperty('zoho');
      expect(config).toHaveProperty('supabase');
      
      expect(config.zoho).toHaveProperty('clientId');
      expect(config.zoho).toHaveProperty('clientSecret');
      expect(config.zoho).toHaveProperty('refreshToken');
      expect(config.zoho).toHaveProperty('accountId');
      expect(config.zoho).toHaveProperty('region');
      expect(config.zoho).toHaveProperty('supportAlias');
      
      expect(config.supabase).toHaveProperty('url');
      expect(config.supabase).toHaveProperty('anonKey');
    });

    it('should be type-safe with ServiceConfig interface', () => {
      // Act
      const config = createServiceConfig();

      // Assert - TypeScript compilation will fail if this doesn't match
      const typedConfig: ServiceConfig = config;
      expect(typedConfig).toBeDefined();
    });

    it('should reflect environment changes between calls', () => {
      // Arrange & Act
      const config1 = createServiceConfig();
      
      process.env.ZOHO_CLIENT_ID = 'changed-client-id';
      
      const config2 = createServiceConfig();

      // Assert
      expect(config1.zoho.clientId).not.toBe(config2.zoho.clientId);
      expect(config2.zoho.clientId).toBe('changed-client-id');
    });

    it('should handle special characters in environment variables', () => {
      // Arrange
      process.env.ZOHO_CLIENT_SECRET = 'secret!@#$%^&*()';
      process.env.SUPABASE_ANON_KEY = 'key-with-dashes_and_underscores.dots';

      // Act
      const config = createServiceConfig();

      // Assert
      expect(config.zoho.clientSecret).toBe('secret!@#$%^&*()');
      expect(config.supabase.anonKey).toBe('key-with-dashes_and_underscores.dots');
    });

    it('should not throw with malformed environment values', () => {
      // Arrange - Set potentially problematic values
      process.env.ZOHO_REGION = null as any;
      process.env.SUPABASE_URL = undefined as any;

      // Act & Assert
      expect(() => createServiceConfig()).not.toThrow();
    });

    it('should handle very long environment variable values', () => {
      // Arrange
      const longValue = 'a'.repeat(10000);
      process.env.ZOHO_CLIENT_SECRET = longValue;

      // Act
      const config = createServiceConfig();

      // Assert
      expect(config.zoho.clientSecret).toBe(longValue);
      expect(config.zoho.clientSecret?.length).toBe(10000);
    });
  });

  describe('ServiceConfig validation', () => {
    it('should create consistent configuration objects', () => {
      // Arrange
      process.env.ZOHO_CLIENT_ID = 'consistent-id';
      
      // Act
      const config1 = createServiceConfig();
      const config2 = createServiceConfig();

      // Assert
      expect(config1).toEqual(config2);
    });

    it('should handle undefined vs empty string correctly', () => {
      // Arrange
      delete process.env.ZOHO_CLIENT_ID;
      process.env.ZOHO_CLIENT_SECRET = '';

      // Act
      const config = createServiceConfig();

      // Assert
      expect(config.zoho.clientId).toBeUndefined();
      expect(config.zoho.clientSecret).toBe('');
    });
  });
});