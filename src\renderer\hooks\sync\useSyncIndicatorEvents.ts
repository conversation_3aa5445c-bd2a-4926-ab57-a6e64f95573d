/// <reference types="../../../types/preload" />
import { useEffect, useCallback, SetStateAction } from 'react';
import { SyncStatus as SyncStatusEnum } from '../../../shared/constants/sync-status';
import { SyncProgress, QueueStats, SyncMetrics } from './useSyncIndicatorState';
import { useSyncStatusManager } from './useSyncStatusManager';
import { storeEventBus } from '../../services/store-event-bus.service';

export interface StatusUpdate {
  status: SyncStatusEnum;
  timestamp?: number;
  error?: string;
  message?: string;
  phase?: string;
}

export function useSyncIndicatorEvents(
  isManualSyncing: boolean,
  setIsManualSyncing: (isManualSyncing: boolean) => void,
  setHasUnsyncedChanges: (hasUnsyncedChanges: boolean) => void,
  setLastSync: (lastSync: number | null) => void,
  setSyncProgress: React.Dispatch<React.SetStateAction<SyncProgress | null>>,
  setQueueStats: React.Dispatch<React.SetStateAction<QueueStats | null>>,
  setMetrics: (metrics: SyncMetrics | null) => void,
  setCurrentPhase: (currentPhase: string) => void,
  setDetailedMessage: (detailedMessage: string) => void,
  setStoreRefreshStatus: (
    status: SetStateAction<{ colorStore: boolean; productStore: boolean }>
  ) => void
) {
  // Use centralized sync status manager for intelligent polling
  const { performStatusCheck, updateUserActivity } = useSyncStatusManager({
    onUnsyncedChanges: (hasChanges: boolean) => {
      setHasUnsyncedChanges(hasChanges);

      // If no changes, also clear any stale outbox entries
      if (!hasChanges && window.syncAPI?.clearNotFoundDeletes) {
        window.syncAPI.clearNotFoundDeletes().catch(error => {
          console.warn(
            '[SyncIndicator] Failed to clear stale outbox entries:',
            error
          );
        });
      }
    },
    onProgress: (progress: SyncProgress | null) => {
      if (progress) {
        setSyncProgress(progress);
      }
    },
    onQueueStats: (stats: QueueStats | null) => {
      if (stats) {
        setQueueStats(stats);
      }
    },
    onMetrics: (metrics: SyncMetrics | null) => {
      if (metrics) {
        setMetrics(metrics);
      }
    },
  });

  // Simplified legacy methods for compatibility
  const checkUnsyncedChanges = useCallback(async () => {
    console.log(
      '[SyncIndicator] checkUnsyncedChanges called - using smart status manager'
    );
    await performStatusCheck();
  }, [performStatusCheck]);

  const loadProgressData = useCallback(async () => {
    console.log(
      '[SyncIndicator] loadProgressData called - using smart status manager'
    );
    await performStatusCheck();
  }, [performStatusCheck]);

  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    if (window.syncAPI?.onStatusUpdate) {
      const unsubscribe = window.syncAPI.onStatusUpdate((update: unknown) => {
        const statusUpdate = update as StatusUpdate;
        setCurrentPhase(statusUpdate.phase || '');
        setDetailedMessage(statusUpdate.message || '');

        if (statusUpdate.timestamp) {
          setLastSync(statusUpdate.timestamp);
        }

        if (statusUpdate.status === SyncStatusEnum.SUCCESS) {
          setHasUnsyncedChanges(false);
          setSyncProgress(null);

          // Emit data refresh event to trigger store updates
          storeEventBus.emit({
            type: 'DATA_REFRESH_REQUESTED',
            source: 'sync-completion',
          });

          if (isManualSyncing) {
            setIsManualSyncing(false);

            (async () => {
              try {
                await checkUnsyncedChanges();
                await loadProgressData();
              } catch (error) {
                console.error(
                  '[UnifiedSyncIndicator] ❌ Error updating indicators:',
                  error
                );
              }
            })();
          }
        } else if (statusUpdate.status === SyncStatusEnum.ERROR) {
          if (isManualSyncing) {
            setIsManualSyncing(false);
          }
        }
      });
      if (typeof unsubscribe === 'function') {
        unsubscribers.push(unsubscribe);
      }
    }

    if (window.syncAPI?.onProgressUpdate) {
      const unsubscribe = window.syncAPI.onProgressUpdate(
        (progress: unknown) => {
          const syncProgress = progress as SyncProgress;
          setSyncProgress(syncProgress);
          setCurrentPhase(syncProgress.phase);
          setDetailedMessage(syncProgress.currentOperation);
        }
      );
      if (typeof unsubscribe === 'function') {
        unsubscribers.push(unsubscribe);
      }
    }

    if (window.syncAPI?.onQueueUpdate) {
      const unsubscribe = window.syncAPI.onQueueUpdate((stats: unknown) => {
        const queueStats = stats as QueueStats;
        setQueueStats(queueStats);
      });
      if (typeof unsubscribe === 'function') {
        unsubscribers.push(unsubscribe);
      }
    }

    if (window.syncAPI?.onMetricsUpdate) {
      const unsubscribe = window.syncAPI.onMetricsUpdate((metrics: unknown) => {
        const syncMetrics = metrics as SyncMetrics;
        setMetrics(syncMetrics);
      });
      if (typeof unsubscribe === 'function') {
        unsubscribers.push(unsubscribe);
      }
    }

    if (window.syncAPI?.onSlowOperation) {
      const unsubscribe = window.syncAPI.onSlowOperation((data: any) => {
        console.warn('[UnifiedSyncIndicator] Slow operation detected:', data);
      });
      if (typeof unsubscribe === 'function') {
        unsubscribers.push(unsubscribe);
      }
    }

    // Initial status check is handled by useSyncStatusManager
    console.log(
      '[SyncIndicator] Initial setup complete - smart polling is active'
    );

    if (window.autoSyncAPI?.onSyncCompleted) {
      const unsubscribe = window.autoSyncAPI.onSyncCompleted((_data: any) => {
        if (isManualSyncing) {
          setIsManualSyncing(false);
        }
        // Smart status manager will handle the refresh
        performStatusCheck().catch(error => {
          console.error(
            '[UnifiedSyncIndicator] Error updating indicators after auto-sync:',
            error
          );
        });
      });
      if (typeof unsubscribe === 'function') {
        unsubscribers.push(unsubscribe);
      }
    }

    if (window.autoSyncAPI?.onSyncFailed) {
      const unsubscribe = window.autoSyncAPI.onSyncFailed((_error: any) => {
        if (isManualSyncing) {
          setIsManualSyncing(false);
        }
        // Update user activity on sync failure
        updateUserActivity();
      });
      if (typeof unsubscribe === 'function') {
        unsubscribers.push(unsubscribe);
      }
    }

    const handleColorStoreRefresh = (_event: any) => {
      if (isManualSyncing) {
        setStoreRefreshStatus(prev => ({ ...prev, colorStore: true }));
      }
      // Track user activity on store refresh
      updateUserActivity();
    };

    const handleProductStoreRefresh = (_event: any) => {
      if (isManualSyncing) {
        setStoreRefreshStatus(prev => ({ ...prev, productStore: true }));
      }
      // Track user activity on store refresh
      updateUserActivity();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener(
        'color-store:refresh-complete',
        handleColorStoreRefresh
      );
      window.addEventListener(
        'product-store:refresh-complete',
        handleProductStoreRefresh
      );
    }

    const cleanupStoreEvents = () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener(
          'color-store:refresh-complete',
          handleColorStoreRefresh
        );
        window.removeEventListener(
          'product-store:refresh-complete',
          handleProductStoreRefresh
        );
      }
    };

    // NOTE: Removed the 5-minute interval - smart polling now handles this through useSyncStatusManager

    return () => {
      unsubscribers.forEach(unsub => {
        if (typeof unsub === 'function') {
          try {
            unsub();
          } catch (error) {
            console.warn(
              '[UnifiedSyncIndicator] Error during unsubscribe:',
              error
            );
          }
        }
      });
      cleanupStoreEvents();
      // NOTE: No interval to clear - smart polling cleanup is handled by useSyncStatusManager
    };
  }, [
    performStatusCheck,
    updateUserActivity,
    isManualSyncing,
    setIsManualSyncing,
    setHasUnsyncedChanges,
    setLastSync,
    setSyncProgress,
    setQueueStats,
    setMetrics,
    setCurrentPhase,
    setDetailedMessage,
    setStoreRefreshStatus,
  ]);
}
