# ChromaSync Application Windsurf Rules
# Version 1.1
#
# --- Architecture & Process Communication ---

# Rule 1: Strict Process Separation
# Description: Enforce Electron's security model. Renderer cannot directly access Node.js APIs.
ipc.renderer-must-use-preload = true

# Rule 2: IPC via Preload Only
# Description: All main-renderer communication must go through the functions exposed via contextBridge in preload.
ipc.enforce-context-bridge = true

# --- Database ---

# Rule 3: Main Process Database Access Only
database.main-process-only = true

# Rule 4: Parameterized Queries Mandatory
database.require-parameterized-queries = true

# Rule 4a: Snake Case Required for SQL
database.require-snake-case-sql = true

# --- Typing & Code Standards ---

# Rule 5: No 'any' Type Allowed
typescript.disallow-any = true

# Rule 6: Shared Type Usage
typescript.require-shared-types-for-ipc = true

# Rule 7: File Naming Convention (kebab-case)
naming.file-kebab-case = true

# Rule 8: Component Naming Convention (PascalCase)
naming.component-pascal-case = true

# Rule 9: Variable/Function Naming Convention (camelCase)
naming.variable-camel-case = true
naming.constant-upper-snake-case = true

# --- State Management ---

# Rule 10: Global State via Zustand Only
state.zustand-for-global = true

# --- Styling ---

# Rule 11: Avoid Hardcoded Styles
styling.prefer-theme-semantics = true

# --- Error Handling ---

# Rule 12: Mandatory Try/Catch for Async/IPC
errors.require-try-catch-async = true

# --- Configuration ---

# Rule 13: Disallow Hardcoded Paths/Secrets
config.no-hardcoding = true

# --- Tech Stack & Feature Alignment ---

techstack.enforce-main-stack = true

# Rule 15: Feature-Stack Alignment Required
techstack.feature-stack-alignment = true

# Rule 16: UI/UX Consistency
# Description: All UI components must follow established design patterns, theming, and accessibility guidelines. Use shared UI libraries/components where possible.
uiux.consistency = true

# Rule 17: Modularization & Separation of Concerns
# Description: New modules and features must be implemented as isolated, reusable units with clear interfaces. Avoid monolithic or tightly coupled code.
architecture.modular-separation = true

# Rule 18: Documentation for New Features
# Description: All new features and major changes must be accompanied by clear documentation describing their purpose, usage, and integration points.
docs.require-feature-docs = true

# Rule 19: Automated Testing for Core Features
# Description: All core business logic and sync features must include automated tests (unit/integration) to ensure reliability and prevent regressions.
testing.core-features-required = true

# Rule 20: Code Review Required for Merges
# Description: All pull requests must be reviewed by at least one other developer before merging into main branches.
process.code-review-required = true
