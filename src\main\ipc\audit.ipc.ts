/**
 * IPC handlers for database audit log access
 */

import { ipcMain } from 'electron';
import { databaseAuditLogger } from '../utils/database-audit-logger';
import { requireValidOrganizationId } from '../utils/organization-validation';
import { registerHandlerSafely } from '../utils/ipcRegistry';

/**
 * Register audit log IPC handlers
 */
export function registerAuditIpcHandlers(): void {
  /**
   * Get audit logs with filtering
   */
  registerHandlerSafely(
    ipcMain,
    'audit:get-logs',
    async (
      _event,
      filter?: {
        organizationId?: string;
        userId?: string;
        table?: string;
        operation?: string;
        startDate?: string;
        endDate?: string;
        onlyErrors?: boolean;
      }
    ) => {
      try {
        // Validate organization ID if provided
        if (filter?.organizationId) {
          requireValidOrganizationId(filter.organizationId, 'audit:get-logs');
        }

        // Convert date strings to Date objects
        const dateFilter = {
          organizationId: filter?.organizationId,
          userId: filter?.userId,
          table: filter?.table,
          operation: filter?.operation as
            | 'DELETE'
            | 'SELECT'
            | 'INSERT'
            | 'UPDATE'
            | 'TRANSACTION'
            | undefined,
          startDate: filter?.startDate ? new Date(filter.startDate) : undefined,
          endDate: filter?.endDate ? new Date(filter.endDate) : undefined,
          onlyErrors: filter?.onlyErrors,
        };

        const logs = databaseAuditLogger.getAuditLogs(dateFilter);

        return {
          success: true,
          logs,
          total: logs.length,
        };
      } catch (error) {
        console.error('[Audit IPC] Error getting audit logs:', error);
        return {
          success: false,
          error:
            error instanceof Error ? error.message : 'Failed to get audit logs',
        };
      }
    }
  );

  /**
   * Get security analytics
   */
  registerHandlerSafely(
    ipcMain,
    'audit:get-analytics',
    async (_event, organizationId?: string) => {
      try {
        // Validate organization ID if provided
        if (organizationId) {
          requireValidOrganizationId(organizationId, 'audit:get-analytics');
        }

        const analytics = databaseAuditLogger.getSecurityAnalytics();

        // Filter analytics by organization if provided
        if (organizationId) {
          const orgLogs = databaseAuditLogger.getAuditLogs({ organizationId });

          // Recalculate analytics for this organization only
          const orgFailedLogs = orgLogs.filter(log => !log.success);

          const orgOperationsByType = orgLogs.reduce(
            (acc, log) => {
              acc[log.operation] = (acc[log.operation] || 0) + 1;
              return acc;
            },
            {} as Record<string, number>
          );

          return {
            success: true,
            analytics: {
              ...analytics,
              totalOperations: orgLogs.length,
              failedOperations: orgFailedLogs.length,
              failureRate:
                orgLogs.length > 0
                  ? (orgFailedLogs.length / orgLogs.length) * 100
                  : 0,
              operationsByType: orgOperationsByType,
              organizationId,
            },
          };
        }

        return {
          success: true,
          analytics,
        };
      } catch (error) {
        console.error('[Audit IPC] Error getting security analytics:', error);
        return {
          success: false,
          error:
            error instanceof Error ? error.message : 'Failed to get analytics',
        };
      }
    }
  );

  /**
   * Get suspicious activity
   */
  registerHandlerSafely(
    ipcMain,
    'audit:get-suspicious-activity',
    async (_event, organizationId?: string) => {
      try {
        // Validate organization ID if provided
        if (organizationId) {
          requireValidOrganizationId(
            organizationId,
            'audit:get-suspicious-activity'
          );
        }

        const analytics = databaseAuditLogger.getSecurityAnalytics();
        let suspiciousActivity = analytics.suspiciousActivity;

        // Filter by organization if provided
        if (organizationId) {
          suspiciousActivity = suspiciousActivity.filter(
            log => log.organizationId === organizationId
          );
        }

        return {
          success: true,
          suspiciousActivity,
          count: suspiciousActivity.length,
        };
      } catch (error) {
        console.error('[Audit IPC] Error getting suspicious activity:', error);
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to get suspicious activity',
        };
      }
    }
  );

  /**
   * Export audit logs (for compliance/backup)
   */
  registerHandlerSafely(
    ipcMain,
    'audit:export-logs',
    async (
      _event,
      options?: {
        organizationId?: string;
        startDate?: string;
        endDate?: string;
        format?: 'json' | 'csv';
      }
    ) => {
      try {
        // Validate organization ID if provided
        if (options?.organizationId) {
          requireValidOrganizationId(
            options.organizationId,
            'audit:export-logs'
          );
        }

        const filter = {
          organizationId: options?.organizationId,
          startDate: options?.startDate
            ? new Date(options.startDate)
            : undefined,
          endDate: options?.endDate ? new Date(options.endDate) : undefined,
        };

        const logs = databaseAuditLogger.getAuditLogs(filter);

        if (options?.format === 'csv') {
          const csvData = convertLogsToCSV(logs);
          return {
            success: true,
            data: csvData,
            format: 'csv',
            filename: `audit-logs-${new Date().toISOString().split('T')[0]}.csv`,
          };
        }

        return {
          success: true,
          data: JSON.stringify(logs, null, 2),
          format: 'json',
          filename: `audit-logs-${new Date().toISOString().split('T')[0]}.json`,
        };
      } catch (error) {
        console.error('[Audit IPC] Error exporting audit logs:', error);
        return {
          success: false,
          error:
            error instanceof Error ? error.message : 'Failed to export logs',
        };
      }
    }
  );

  /**
   * Clear old audit logs
   */
  registerHandlerSafely(ipcMain, 'audit:cleanup-logs', async _event => {
    try {
      databaseAuditLogger.cleanupOldLogs();

      return {
        success: true,
        message: 'Old audit logs cleaned up successfully',
      };
    } catch (error) {
      console.error('[Audit IPC] Error cleaning up logs:', error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Failed to cleanup logs',
      };
    }
  });

  console.log('[Audit IPC] Audit log handlers registered');
}

/**
 * Convert audit logs to CSV format
 */
function convertLogsToCSV(logs: any[]): string {
  if (logs.length === 0) {
    return 'No data available';
  }

  const headers = [
    'id',
    'timestamp',
    'operation',
    'table',
    'organizationId',
    'userId',
    'recordId',
    'rowsAffected',
    'executionTime',
    'success',
    'error',
    'context',
  ];

  const csvRows = [
    headers.join(','),
    ...logs.map(log =>
      headers
        .map(header => {
          const value = log[header];
          // Escape commas and quotes in CSV
          if (
            typeof value === 'string' &&
            (value.includes(',') || value.includes('"'))
          ) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || '';
        })
        .join(',')
    ),
  ];

  return csvRows.join('\n');
}
