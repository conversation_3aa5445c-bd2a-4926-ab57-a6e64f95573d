/**
 * Optimized database implementation using modular architecture
 * This file now coordinates between modular components for better maintainability
 */

import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';

// Import modular database components
import {
  getPooledConnection,
  releasePooledConnection,
  executeWithPool,
  getDatabasePath
} from './core/connection';
import { initializeDatabase as simpleInitDb } from './simple-init';

// Global database instance for backward compatibility
let db: any = null;

// ID mapping caches removed - no longer used after color function cleanup

// Export connection utilities for backward compatibility
export { getPooledConnection, releasePooledConnection, executeWithPool, getDatabasePath };

/**
 * Initialize database with simplified single-schema approach
 * Fast startup: ~500ms vs 5-15 seconds with migrations
 */
export async function initDatabase(): Promise<any | null> {
  if (db) {
    console.log('[DB] Database already initialized, returning existing connection');
    return db;
  }

  try {
    console.log('[DB] Starting simplified database initialization...');
    
    console.log('[DB] Calling initializeDatabase function...');
    db = await simpleInitDb();
    console.log('[DB] initializeDatabase function returned:', db ? 'SUCCESS' : 'NULL');
    
    if (db) {
      console.log('[DB] ✅ Database initialized successfully with pure UUID schema');
      return db;
    } else {
      console.error('[DB] ❌ Simplified initialization returned null');
      db = null;
      return null;
    }
  } catch (error) {
    console.error('[DB] ❌ Simplified initialization failed:', error);
    console.error('[DB] Error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    db = null;
    return null;
  }
}

// Schema creation functions are now handled by the modular SchemaManager
// These functions are kept for backward compatibility but delegate to the new system

// Organization table creation is now handled by SchemaManager

// Schema verification and gradient column management are now handled by modular components

// Deprecated schema creation function removed - now handled by modular SchemaManager


// getOrCreateIntegerId function removed - no longer used after color function cleanup

/**
 * Close the database connection
 */
export function closeDatabase(): void {
  if (db) {
    try {
      // Checkpoint WAL to ensure all data is written to main database file
      console.log('[DB] Checkpointing WAL before closing...');
      db.pragma('wal_checkpoint(FULL)');
      
      // Run optimize before closing
      console.log('[DB] Running optimization...');
      db.exec('PRAGMA optimize');
      
      console.log('[DB] Closing database connection...');
      db.close();
      db = null;
      console.log('[DB] Database closed successfully');
    } catch (error) {
      console.error('[DB] Error during database close:', error);
      // Force close even if checkpoint fails
      try {
        db.close();
      } catch (closeError) {
        console.error('[DB] Error force closing database:', closeError);
      }
      db = null;
    }
  }
}

// Export compatibility functions that match the old API
export function getDatabase(): any | null {
  return db;
}

// Continue in next part...
// ============================================
// Compatibility Layer - Product Operations
// ============================================

/**
 * Get all products (maintains old API)
 */
export function getAllProducts(): any[] {
  if (!db) {throw new Error('Database not initialized');}

  let products;
  try {
    products = db.prepare(`
      SELECT 
        p.id,
        p.name,
        p.metadata->>'description' as description,
        p.created_at as createdAt,
        p.updated_at as updatedAt,
        p.metadata->>'createdBy' as createdBy,
        p.metadata->>'updatedBy' as updatedBy
      FROM products p
      WHERE p.is_active = TRUE
      ORDER BY p.name
    `).all();
  } catch {
    products = db.prepare(`
      SELECT 
        p.id,
        p.name,
        p.metadata->>'description' as description,
        p.created_at as createdAt,
        p.updated_at as updatedAt,
        p.metadata->>'createdBy' as createdBy,
        p.metadata->>'updatedBy' as updatedBy
      FROM products p
      WHERE p.deleted_at IS NULL
      ORDER BY p.name
    `).all();
  }

  return products;
}

/**
 * Get product by ID (maintains old API)
 */
export function getProductById(id: string): any | null {
  if (!db) {throw new Error('Database not initialized');}

  let product;
  try {
    product = db.prepare(`
      SELECT 
        p.id,
        p.name,
        p.metadata->>'description' as description,
        p.created_at as createdAt,
        p.updated_at as updatedAt,
        p.metadata->>'createdBy' as createdBy,
        p.metadata->>'updatedBy' as updatedBy
      FROM products p
      WHERE p.id = ? AND p.is_active = TRUE
    `).get(id) as any;
  } catch {
    product = db.prepare(`
      SELECT 
        p.id,
        p.name,
        p.metadata->>'description' as description,
        p.created_at as createdAt,
        p.updated_at as updatedAt,
        p.metadata->>'createdBy' as createdBy,
        p.metadata->>'updatedBy' as updatedBy
      FROM products p
      WHERE p.id = ? AND p.deleted_at IS NULL
    `).get(id) as any;
  }

  return product || null;
}

/**
 * Create product (maintains old API)
 */
export function createProduct(data: { name: string; description?: string; createdBy?: string }): any {
  if (!db) {throw new Error('Database not initialized');}

  const id = uuidv4();
  const metadata = {
    description: data.description,
    createdBy: data.createdBy
  };

  db.prepare(`
    INSERT INTO products (id, name, metadata)
    VALUES (?, ?, json(?))
  `).run(id, data.name, JSON.stringify(metadata));

  return {
    id,
    name: data.name,
    description: data.description,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: data.createdBy
  };
}

/**
 * Update product (maintains old API)
 */
export function updateProduct(id: string, data: { name?: string; description?: string; updatedBy?: string }): any | null {
  if (!db) {throw new Error('Database not initialized');}

  const existing = getProductById(id);
  if (!existing) {return null;}

  const metadata = {
    description: data.description || existing.description,
    createdBy: existing.createdBy,
    updatedBy: data.updatedBy
  };

  db.prepare(`
    UPDATE products 
    SET name = COALESCE(?, name),
        metadata = json(?),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `).run(data.name, JSON.stringify(metadata), id);

  return getProductById(id);
}

/**
 * Delete product (maintains old API)
 */
export function deleteProduct(id: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  let result;
  try {
    result = db.prepare(`
      UPDATE products 
      SET is_active = FALSE,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(id);
  } catch {
    result = db.prepare(`
      UPDATE products 
      SET deleted_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(id);
  }

  return result.changes > 0;
}

// ============================================
// Compatibility Layer - Color Operations
// ============================================

// Color functions removed - use ColorService instead
// Legacy functions that referenced normalized tables (color_cmyk, color_rgb) have been removed
// because migration 020 removes these tables. Use src/main/db/services/color.service.ts instead.

// ============================================
// Color Search Operations (Legacy - Use ColorService instead)
// ============================================

/**
 * Run database maintenance
 */
export function runMaintenance(): void {
  if (!db) {throw new Error('Database not initialized');}

  console.log('[DB] Running maintenance...');
  
  // Checkpoint WAL
  db.pragma('wal_checkpoint(TRUNCATE)');
  
  // Analyze tables for query optimizer
  db.exec('ANALYZE');
  
  // Run optimize
  db.pragma('optimize');
  
  console.log('[DB] Maintenance complete');
}

/**
 * Get database statistics
 */
export function getDatabaseStats(): any {
  if (!db) {throw new Error('Database not initialized');}

  const dbPath = getDatabasePath();
  const totalSize = fs.existsSync(dbPath) ? fs.statSync(dbPath).size : 0;
  
  const productCount = db.prepare('SELECT COUNT(*) as count FROM products WHERE deleted_at IS NULL').get()?.count || 0;
  const colorCount = db.prepare('SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL').get()?.count || 0;
  
  // Get last vacuum time (this is a bit of a hack, relies on file modification time)
  const lastVacuum = fs.existsSync(dbPath) ? fs.statSync(dbPath).mtime.toISOString() : null;

  // Calculate fragmentation
  const pageCount = db.pragma('page_count')[0].page_count;
  const freeListCount = db.pragma('freelist_count')[0].freelist_count;
  const fragmentationLevel = pageCount > 0 ? (freeListCount / pageCount) * 100 : 0;

  const stats = {
    totalSize: `${(totalSize / 1024 / 1024).toFixed(2)} MB`,
    productCount,
    colorCount,
    lastVacuum,
    fragmentationLevel: Math.round(fragmentationLevel),
    libraryColors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE source_id != 1 AND deleted_at IS NULL').get()?.count || 0,
    userColors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE source_id = 1 AND deleted_at IS NULL').get()?.count || 0,
    gradients: db.prepare('SELECT COUNT(*) as count FROM colors WHERE is_gradient = TRUE AND deleted_at IS NULL').get()?.count || 0,
    associations: db.prepare('SELECT COUNT(*) as count FROM product_colors').get()?.count || 0,
    walSize: 0
  };

  // Check WAL file size
  try {
    const walPath = getDatabasePath() + '-wal';
    if (fs.existsSync(walPath)) {
      stats.walSize = fs.statSync(walPath).size;
    }
  } catch (_error) {
    // Ignore
  }

  return stats;
}

// Search functions removed - use ColorService instead
// Legacy search functions that referenced normalized tables have been removed.
// Use src/main/db/services/color.service.ts for color search operations.

// ============================================
// Performance Monitoring & Maintenance
// ============================================

// Export all functions
export default {
  initDatabase,
  closeDatabase,
  getDatabase,
  
  // Products
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  
  // Legacy color functions removed - use ColorService instead
  
  // Maintenance
  runMaintenance,
  getDatabaseStats
};