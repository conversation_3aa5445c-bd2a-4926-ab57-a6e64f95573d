/**
 * Color Library IPC Handlers
 * Handles communication between frontend and color library services
 */

import { ipcMain } from 'electron';
import { getColorLibraryQueryService } from '../db/services/color-library-query.service';
import { getColorLibraryImportService } from '../db/services/color-library-import.service';
import type { ColorLibrarySearchOptions } from '../db/services/color-library-query.service';
import {
  createSuccessResponse,
  createErrorResponse,
} from '../utils/ipc-wrapper';

/**
 * Register color library IPC handlers
 */
export function registerColorLibraryIPC(): void {
  const queryService = getColorLibraryQueryService();
  const importService = getColorLibraryImportService();

  /**
   * Search colors across libraries
   */
  ipcMain.handle(
    'color-library:search-colors',
    async (_event, options: ColorLibrarySearchOptions) => {
      try {
        const results = await queryService.searchColors(options);
        return createSuccessResponse(
          results,
          `Found ${results?.colors?.length || 0} colors matching search criteria`
        );
      } catch (error) {
        console.error('[ColorLibraryIPC] Failed to search colors:', error);
        return createErrorResponse(
          'Failed to search color libraries. Please try again.'
        );
      }
    }
  );

  /**
   * Get Pantone colors
   */
  ipcMain.handle(
    'color-library:get-pantone-colors',
    async (_event, options: Omit<ColorLibrarySearchOptions, 'library'>) => {
      try {
        return await queryService.getPantoneColors(options);
      } catch (error) {
        console.error('[ColorLibraryIPC] Failed to get Pantone colors:', error);
        throw error;
      }
    }
  );

  /**
   * Get RAL colors
   */
  ipcMain.handle(
    'color-library:get-ral-colors',
    async (_event, options: Omit<ColorLibrarySearchOptions, 'library'>) => {
      try {
        return await queryService.getRalColors(options);
      } catch (error) {
        console.error('[ColorLibraryIPC] Failed to get RAL colors:', error);
        throw error;
      }
    }
  );

  /**
   * Full-text search
   */
  ipcMain.handle(
    'color-library:full-text-search',
    async (_event, query: string, options: ColorLibrarySearchOptions) => {
      try {
        return await queryService.fullTextSearch(query, options);
      } catch (error) {
        console.error(
          '[ColorLibraryIPC] Failed to perform full-text search:',
          error
        );
        throw error;
      }
    }
  );

  /**
   * Get popular colors
   */
  ipcMain.handle(
    'color-library:get-popular-colors',
    async (_event, libraryCode?: string, limit: number = 20) => {
      try {
        return await queryService.getPopularColors(libraryCode, limit);
      } catch (error) {
        console.error('[ColorLibraryIPC] Failed to get popular colors:', error);
        throw error;
      }
    }
  );

  /**
   * Get color by external ID
   */
  ipcMain.handle(
    'color-library:get-color-by-external-id',
    async (_event, externalId: string) => {
      try {
        return await queryService.getColorByExternalId(externalId);
      } catch (error) {
        console.error(
          '[ColorLibraryIPC] Failed to get color by external ID:',
          error
        );
        throw error;
      }
    }
  );

  /**
   * Get color by library and code
   */
  ipcMain.handle(
    'color-library:get-color-by-code',
    async (_event, libraryCode: string, colorCode: string) => {
      try {
        return await queryService.getColorByCode(libraryCode, colorCode);
      } catch (error) {
        console.error('[ColorLibraryIPC] Failed to get color by code:', error);
        throw error;
      }
    }
  );

  // ===== ENHANCED HANDLERS FOR OPTIMIZED LOADING =====

  /**
   * Enhanced search with better performance
   */
  ipcMain.handle(
    'color-library:search-colors-enhanced',
    async (_event, options: ColorLibrarySearchOptions) => {
      try {
        const results = await queryService.searchColorsEnhanced(options);
        return createSuccessResponse(
          results,
          `Enhanced search found ${results?.colors?.length || 0} colors`
        );
      } catch (error) {
        console.error('[ColorLibraryIPC] Enhanced search failed:', error);
        return createErrorResponse(
          'Enhanced color search failed. Please try the standard search.'
        );
      }
    }
  );

  /**
   * Get library metadata
   */
  ipcMain.handle(
    'color-library:get-library-metadata',
    async (_event, libraryCode: string) => {
      try {
        return await queryService.getLibraryMetadata(libraryCode);
      } catch (error) {
        console.error(
          '[ColorLibraryIPC] Failed to get library metadata:',
          error
        );
        throw error;
      }
    }
  );

  /**
   * Get all available libraries
   */
  ipcMain.handle('color-library:get-available-libraries', async () => {
    try {
      return await queryService.getAvailableLibraries();
    } catch (error) {
      console.error(
        '[ColorLibraryIPC] Failed to get available libraries:',
        error
      );
      throw error;
    }
  });

  /**
   * Load library chunk for virtual scrolling
   */
  ipcMain.handle(
    'color-library:load-library-chunk',
    async (
      _event,
      libraryCode: string,
      startIndex: number,
      chunkSize?: number
    ) => {
      try {
        return await queryService.loadLibraryChunk(
          libraryCode,
          startIndex,
          chunkSize
        );
      } catch (error) {
        console.error('[ColorLibraryIPC] Failed to load library chunk:', error);
        throw error;
      }
    }
  );

  /**
   * Get cache statistics
   */
  ipcMain.handle('color-library:get-cache-stats', async () => {
    try {
      return queryService.getCacheStats();
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to get cache stats:', error);
      throw error;
    }
  });

  /**
   * Clear cache
   */
  ipcMain.handle(
    'color-library:clear-cache',
    async (_event, libraryCode?: string) => {
      try {
        queryService.clearCache(libraryCode);
        return { success: true };
      } catch (error) {
        console.error('[ColorLibraryIPC] Failed to clear cache:', error);
        throw error;
      }
    }
  );

  /**
   * Increment usage count
   */
  ipcMain.handle(
    'color-library:increment-usage',
    async (_event, externalId: string) => {
      try {
        await queryService.incrementUsage(externalId);
      } catch (error) {
        console.error('[ColorLibraryIPC] Failed to increment usage:', error);
        throw error;
      }
    }
  );

  /**
   * Get library statistics
   */
  ipcMain.handle('color-library:get-stats', async _event => {
    try {
      return await importService.getLibraryStats();
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to get library stats:', error);
      throw error;
    }
  });

  /**
   * Force re-import of color libraries (admin function)
   */
  ipcMain.handle('color-library:force-reimport', async _event => {
    try {
      console.log(
        '[ColorLibraryIPC] Starting forced re-import of color libraries...'
      );

      // Clear existing data
      await importService.clearLibraryData();

      // Re-import all libraries
      const results = await importService.importAllLibraries();

      console.log('[ColorLibraryIPC] Forced re-import completed:', results);
      return createSuccessResponse(
        results,
        'Color libraries successfully re-imported'
      );
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to force re-import:', error);
      return createErrorResponse(
        'Failed to re-import color libraries. Please check your connection and try again.'
      );
    }
  });

  /**
   * Check if libraries need importing
   */
  ipcMain.handle('color-library:needs-import', async _event => {
    try {
      return await importService.needsImport();
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to check import status:', error);
      throw error;
    }
  });

  console.log('[ColorLibraryIPC] Color library IPC handlers registered');
}

/**
 * Unregister color library IPC handlers
 */
export function unregisterColorLibraryIPC(): void {
  const handlers = [
    'color-library:search-colors',
    'color-library:get-pantone-colors',
    'color-library:get-ral-colors',
    'color-library:full-text-search',
    'color-library:get-popular-colors',
    'color-library:get-color-by-external-id',
    'color-library:get-color-by-code',
    'color-library:search-colors-enhanced',
    'color-library:get-library-metadata',
    'color-library:get-available-libraries',
    'color-library:load-library-chunk',
    'color-library:get-cache-stats',
    'color-library:clear-cache',
    'color-library:increment-usage',
    'color-library:get-stats',
    'color-library:force-reimport',
    'color-library:needs-import',
  ];

  handlers.forEach(handler => {
    ipcMain.removeAllListeners(handler);
  });

  console.log('[ColorLibraryIPC] Color library IPC handlers unregistered');
}
