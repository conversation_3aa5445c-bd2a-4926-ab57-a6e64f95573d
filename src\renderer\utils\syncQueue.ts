/**
 * Sync Queue Manager for ChromaSync
 * Handles offline changes and automatic synchronization
 */

import { offlineStorage } from './offlineStorage';
import { errorLogger } from './errorLogger';
import { ColorEntry } from '../../shared/types/color.types';
import { Product as ProductEntry } from '../../shared/types/product.types';
import { DatasheetEntry } from '../../shared/types/datasheet.types';

interface SyncOptions {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  maxBackoffDelay: number;
  timeout: number;
}

interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  errors: string[];
}

interface SyncProgress {
  total: number;
  completed: number;
  failed: number;
  current?: string;
}

type SyncChangeData =
  | ColorEntry
  | ProductEntry
  | DatasheetEntry
  | { colorId: string; productId: string };

interface SyncChange {
  id: string;
  type: 'color' | 'product' | 'datasheet' | 'product_color';
  action: 'create' | 'update' | 'delete';
  data: SyncChangeData;
  timestamp: number;
  synced: boolean;
  attempts: number;
  lastAttempt?: number;
  error?: string;
}

type SyncProgressCallback = (progress: SyncProgress) => void;

export class SyncQueue {
  private static instance: SyncQueue;
  private isProcessing = false;
  private progressCallbacks: SyncProgressCallback[] = [];
  private autoSyncInterval: number | null = null;
  private readonly defaultOptions: SyncOptions = {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 2,
    maxBackoffDelay: 30000,
    timeout: 30000,
  };

  private constructor() {
    this.initializeAutoSync();
    this.setupConnectionMonitoring();
  }

  static getInstance(): SyncQueue {
    if (!SyncQueue.instance) {
      SyncQueue.instance = new SyncQueue();
    }
    return SyncQueue.instance;
  }

  /**
   * Initialize auto-sync when connection is restored
   */
  private initializeAutoSync(): void {
    // Auto-sync every 5 minutes when online
    this.autoSyncInterval = setInterval(
      () => {
        if (offlineStorage.isOnline() && !this.isProcessing) {
          this.processPendingChanges();
        }
      },
      5 * 60 * 1000
    ) as unknown as number; // 5 minutes
  }

  /**
   * Monitor connection changes
   */
  private setupConnectionMonitoring(): void {
    offlineStorage.onConnectionChange(isOnline => {
      if (isOnline && !this.isProcessing) {
        console.log(
          '[SyncQueue] Connection restored, processing pending changes...'
        );
        this.processPendingChanges();
      }
    });
  }

  /**
   * Add a change to the sync queue
   */
  async addChange(
    type: 'color' | 'product' | 'datasheet' | 'product_color',
    action: 'create' | 'update' | 'delete',
    data: SyncChangeData
  ): Promise<string> {
    try {
      const recordId = await offlineStorage.addToOfflineQueue({
        type,
        action,
        data,
      });

      errorLogger.logInfo('Change added to sync queue', {
        recordId,
        type,
        action,
        dataSize: JSON.stringify(data).length,
      });

      // If online, try to sync immediately
      if (offlineStorage.isOnline() && !this.isProcessing) {
        setTimeout(() => this.processPendingChanges(), 100);
      }

      return recordId;
    } catch (error) {
      errorLogger.logError({
        message: 'Failed to add change to sync queue',
        context: {
          type,
          action,
          error: String(error),
        },
      });
      throw error;
    }
  }

  /**
   * Process all pending changes
   */
  async processPendingChanges(
    options?: Partial<SyncOptions>
  ): Promise<SyncResult> {
    if (this.isProcessing) {
      console.log('[SyncQueue] Sync already in progress, skipping...');
      return {
        success: false,
        syncedCount: 0,
        failedCount: 0,
        errors: ['Sync already in progress'],
      };
    }

    if (!offlineStorage.isOnline()) {
      console.log('[SyncQueue] Offline, cannot sync');
      return {
        success: false,
        syncedCount: 0,
        failedCount: 0,
        errors: ['Device is offline'],
      };
    }

    this.isProcessing = true;
    const syncOptions = { ...this.defaultOptions, ...options };
    let syncedCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    try {
      console.log('[SyncQueue] Starting sync process...');

      const pendingChanges = await offlineStorage.getPendingSync();
      console.log(`[SyncQueue] Found ${pendingChanges.length} pending changes`);

      if (pendingChanges.length === 0) {
        return { success: true, syncedCount: 0, failedCount: 0, errors: [] };
      }

      // Notify progress
      this.notifyProgress({
        total: pendingChanges.length,
        completed: 0,
        failed: 0,
      });

      // Process changes in order
      for (const change of pendingChanges) {
        try {
          this.notifyProgress({
            total: pendingChanges.length,
            completed: syncedCount,
            failed: failedCount,
            current: `${change.action} ${change.type}`,
          });

          const success = await this.syncSingleChange(change, syncOptions);

          if (success) {
            await offlineStorage.markAsSynced(change.id);
            syncedCount++;

            errorLogger.logInfo('Change synced successfully', {
              recordId: change.id,
              type: change.type,
              action: change.action,
            });
          } else {
            await offlineStorage.updateSyncAttempt(change.id, 'Sync failed');
            failedCount++;
            errors.push(`Failed to sync ${change.type} ${change.action}`);
          }
        } catch (error) {
          await offlineStorage.updateSyncAttempt(change.id, String(error));
          failedCount++;
          errors.push(String(error));

          errorLogger.logError({
            message: 'Error syncing change',
            context: {
              recordId: change.id,
              type: change.type,
              action: change.action,
              error: String(error),
            },
          });
        }
      }

      // Final progress notification
      this.notifyProgress({
        total: pendingChanges.length,
        completed: syncedCount,
        failed: failedCount,
      });

      // Update last sync time
      await offlineStorage.setLastSyncTime(Date.now());

      const result: SyncResult = {
        success: failedCount === 0,
        syncedCount,
        failedCount,
        errors,
      };

      console.log(
        `[SyncQueue] Sync completed: ${syncedCount} synced, ${failedCount} failed`
      );

      errorLogger.logInfo('Sync process completed', {
        syncedCount,
        failedCount,
        totalChanges: pendingChanges.length,
        success: result.success,
      });

      return result;
    } catch (error) {
      console.error('[SyncQueue] Sync process failed:', error);
      errorLogger.logError({
        message: 'Sync process failed',
        context: { error: String(error) },
      });

      return {
        success: false,
        syncedCount,
        failedCount: failedCount + 1,
        errors: [...errors, String(error)],
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Sync a single change with retry logic
   */
  private async syncSingleChange(
    change: SyncChange,
    options: SyncOptions
  ): Promise<boolean> {
    let attempt = 0;
    let delay = options.retryDelay;

    while (attempt < options.maxRetries) {
      try {
        // Attempt to sync the change
        const success = await this.executeSyncChange(change, options.timeout);

        if (success) {
          return true;
        }

        attempt++;

        if (attempt < options.maxRetries) {
          console.log(
            `[SyncQueue] Retry ${attempt}/${options.maxRetries} for ${change.id} in ${delay}ms`
          );
          await this.delay(delay);

          // Exponential backoff
          delay = Math.min(
            delay * options.backoffMultiplier,
            options.maxBackoffDelay
          );
        }
      } catch (error) {
        console.error(
          `[SyncQueue] Attempt ${attempt + 1} failed for ${change.id}:`,
          error
        );
        attempt++;

        if (attempt < options.maxRetries) {
          await this.delay(delay);
          delay = Math.min(
            delay * options.backoffMultiplier,
            options.maxBackoffDelay
          );
        }
      }
    }

    console.error(`[SyncQueue] All retry attempts failed for ${change.id}`);
    return false;
  }

  /**
   * Execute the actual sync operation
   */
  private async executeSyncChange(
    change: SyncChange,
    timeout: number
  ): Promise<boolean> {
    return new Promise(resolve => {
      const timeoutId = setTimeout(() => {
        console.error(`[SyncQueue] Sync timeout for ${change.id}`);
        resolve(false);
      }, timeout);

      try {
        // In a real implementation, this would make IPC calls to the main process
        // For now, we'll simulate the sync operation

        switch (change.type) {
          case 'color':
            this.syncColor(change)
              .then(success => {
                clearTimeout(timeoutId);
                resolve(success);
              })
              .catch(() => {
                clearTimeout(timeoutId);
                resolve(false);
              });
            break;

          case 'product':
            this.syncProduct(change)
              .then(success => {
                clearTimeout(timeoutId);
                resolve(success);
              })
              .catch(() => {
                clearTimeout(timeoutId);
                resolve(false);
              });
            break;

          case 'datasheet':
            this.syncDatasheet(change)
              .then(success => {
                clearTimeout(timeoutId);
                resolve(success);
              })
              .catch(() => {
                clearTimeout(timeoutId);
                resolve(false);
              });
            break;

          default:
            clearTimeout(timeoutId);
            resolve(false);
        }
      } catch (error) {
        clearTimeout(timeoutId);
        console.error(
          `[SyncQueue] Sync execution error for ${change.id}:`,
          error
        );
        resolve(false);
      }
    });
  }

  /**
   * Sync color changes
   */
  private async syncColor(change: SyncChange): Promise<boolean> {
    try {
      // Simulate API call
      console.log(
        `[SyncQueue] Syncing color ${change.action}:`,
        (change.data as ColorEntry).id
      );

      switch (change.action) {
        case 'create':
          // await window.colorAPI.add(change.data as ColorEntry)
          break;
        case 'update':
          // await window.colorAPI.update((change.data as ColorEntry).id, change.data as any)
          break;
        case 'delete':
          // await window.colorAPI.delete((change.data as ColorEntry).id)
          break;
      }

      // Simulate network delay
      await this.delay(100 + Math.random() * 200);

      // Simulate 95% success rate
      return Math.random() > 0.05;
    } catch (error) {
      console.error('[SyncQueue] Color sync error:', error);
      return false;
    }
  }

  /**
   * Sync product changes
   */
  private async syncProduct(change: SyncChange): Promise<boolean> {
    try {
      console.log(
        `[SyncQueue] Syncing product ${change.action}:`,
        (change.data as ProductEntry).id
      );

      // Simulate network delay
      await this.delay(100 + Math.random() * 200);

      // Simulate 95% success rate
      return Math.random() > 0.05;
    } catch (error) {
      console.error('[SyncQueue] Product sync error:', error);
      return false;
    }
  }

  /**
   * Sync datasheet changes
   */
  private async syncDatasheet(change: SyncChange): Promise<boolean> {
    try {
      console.log(
        `[SyncQueue] Syncing datasheet ${change.action}:`,
        (change.data as DatasheetEntry).id
      );

      // Simulate network delay
      await this.delay(100 + Math.random() * 200);

      // Simulate 95% success rate
      return Math.random() > 0.05;
    } catch (error) {
      console.error('[SyncQueue] Datasheet sync error:', error);
      return false;
    }
  }

  /**
   * Add progress callback
   */
  addProgressCallback(callback: SyncProgressCallback): () => void {
    this.progressCallbacks.push(callback);

    return () => {
      const index = this.progressCallbacks.indexOf(callback);
      if (index > -1) {
        this.progressCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Notify progress to all callbacks
   */
  private notifyProgress(progress: SyncProgress): void {
    this.progressCallbacks.forEach(callback => {
      try {
        callback(progress);
      } catch (error) {
        console.error('[SyncQueue] Progress callback error:', error);
      }
    });
  }

  /**
   * Get sync status
   */
  async getSyncStatus(): Promise<{
    isProcessing: boolean;
    pendingCount: number;
    lastSync: Date | null;
    isOnline: boolean;
  }> {
    const stats = await offlineStorage.getStorageStats();

    return {
      isProcessing: this.isProcessing,
      pendingCount: stats.pendingSync,
      lastSync: stats.lastSync,
      isOnline: stats.isOnline,
    };
  }

  /**
   * Force sync now
   */
  async forceSyncNow(): Promise<SyncResult> {
    if (!offlineStorage.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    return this.processPendingChanges();
  }

  /**
   * Clear failed sync items
   */
  async clearFailedSync(): Promise<void> {
    try {
      const pendingChanges = await offlineStorage.getPendingSync();
      const failedChanges = pendingChanges.filter(
        change => change.attempts > 0
      );

      for (const change of failedChanges) {
        await offlineStorage.markAsSynced(change.id);
      }

      console.log(
        `[SyncQueue] Cleared ${failedChanges.length} failed sync items`
      );
    } catch (error) {
      console.error('[SyncQueue] Failed to clear failed sync items:', error);
      throw error;
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval);
      this.autoSyncInterval = null;
    }
    this.progressCallbacks = [];
  }
}

// Export singleton instance
export const syncQueue = SyncQueue.getInstance();
