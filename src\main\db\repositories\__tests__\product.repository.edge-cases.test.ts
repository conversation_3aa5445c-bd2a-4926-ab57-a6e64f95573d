/**
 * @file product.repository.edge-cases.test.ts
 * @description Comprehensive edge case and constraint testing for ProductRepository
 *
 * This test suite focuses on database edge cases, constraints, security, and error handling
 * that go beyond the basic CRUD operations. It covers:
 *
 * - Invalid organization IDs and SQL injection prevention
 * - Database constraint violations and foreign key integrity
 * - Concurrent access scenarios and race conditions
 * - Large dataset operations and performance limits
 * - Transaction rollback scenarios and data consistency
 * - Soft delete edge cases and cascade operations
 * - Memory leak detection and resource management
 * - Data validation and sanitization
 * - Error boundary testing
 */

import {
  describe,
  test,
  expect,
  beforeEach,
  afterEach,
  vi,
  beforeAll,
} from 'vitest';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { ProductRepository } from '../product.repository';
import { NewProduct, UpdateProduct } from '../../../shared/types/product.types';
import { COMPLETE_SCHEMA } from '../../schemas/complete-schema';
import { OrganizationValidationError } from '../../../utils/organization-validation';

describe.sequential('ProductRepository - Edge Cases & Constraints', () => {
  let db: Database.Database;
  let repository: ProductRepository;
  let validOrgId: string;
  let validOrgId2: string;
  let validUserId: string;

  beforeEach(() => {
    // Create in-memory SQLite database for testing
    db = new Database(':memory:');

    // Apply complete schema
    db.exec(COMPLETE_SCHEMA);

    // Create repository instance
    repository = new ProductRepository(db);

    // Set up valid test IDs
    validOrgId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479'; // Valid UUID
    validOrgId2 = 'f47ac10b-58cc-4372-a567-0e02b2c3d480'; // Different valid UUID
    validUserId = 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf'; // Valid user UUID

    // Seed minimal test data
    seedBasicTestData(db, validOrgId, validOrgId2, validUserId);
  });

  afterEach(() => {
    if (db && db.open) {
      try {
        db.close();
      } catch (error) {
        console.warn('Database close error:', error);
      }
    }
  });

  describe('Organization ID Validation & SQL Injection Prevention', () => {
    test('should reject null organization ID', () => {
      expect(() => {
        repository.findAll(null as any);
      }).toThrow(OrganizationValidationError);
    });

    test('should reject undefined organization ID', () => {
      expect(() => {
        repository.findAll(undefined as any);
      }).toThrow(OrganizationValidationError);
    });

    test('should reject empty string organization ID', () => {
      expect(() => {
        repository.findAll('');
      }).toThrow(OrganizationValidationError);
    });

    test('should reject whitespace-only organization ID', () => {
      expect(() => {
        repository.findAll('   \t\n   ');
      }).toThrow(OrganizationValidationError);
    });

    test('should reject non-string organization ID', () => {
      expect(() => {
        repository.findAll(123 as any);
      }).toThrow(OrganizationValidationError);

      expect(() => {
        repository.findAll({} as any);
      }).toThrow(OrganizationValidationError);

      expect(() => {
        repository.findAll([] as any);
      }).toThrow(OrganizationValidationError);
    });

    test('should reject malformed UUID organization ID', () => {
      const malformedUUIDs = [
        'not-a-uuid',
        'f47ac10b-58cc-4372-a567', // Too short
        'f47ac10b-58cc-4372-a567-0e02b2c3d479-extra', // Too long
        'g47ac10b-58cc-4372-a567-0e02b2c3d479', // Invalid hex character
        'f47ac10b58cc4372a5670e02b2c3d479', // Missing hyphens
        'f47ac10b-58cc-5372-a567-0e02b2c3d479', // Wrong version (should be 4)
      ];

      malformedUUIDs.forEach(malformedId => {
        expect(() => {
          repository.findAll(malformedId);
        }).toThrow(OrganizationValidationError);
      });
    });

    test('should prevent SQL injection through organization ID', () => {
      const sqlInjectionAttempts = [
        "'; DROP TABLE products; --",
        "' OR 1=1 --",
        "' UNION SELECT * FROM users --",
        "'; UPDATE products SET deleted_at = NOW(); --",
        "'; INSERT INTO products (name) VALUES ('hacked'); --",
        "' OR organization_id IS NOT NULL --",
        "\\'; EXEC xp_cmdshell('dir'); --",
      ];

      sqlInjectionAttempts.forEach(injectionAttempt => {
        expect(() => {
          repository.findAll(injectionAttempt);
        }).toThrow(OrganizationValidationError);
      });
    });

    test('should handle SQL injection attempts in product names safely', () => {
      const maliciousProduct: NewProduct = {
        name: "'; DROP TABLE products; --",
        description: 'Malicious product',
        organizationId: validOrgId,
      };

      // Should complete without throwing but sanitize the input
      const productId = repository.insert(
        maliciousProduct,
        validOrgId,
        validUserId
      );
      expect(productId).toBeTruthy();

      // Verify the malicious SQL wasn't executed
      const allProducts = repository.findAll(validOrgId);
      expect(allProducts.length).toBeGreaterThan(0); // Table should still exist

      const insertedProduct = repository.findById(productId, validOrgId);
      expect(insertedProduct?.name).toBe("'; DROP TABLE products; --"); // Stored as literal string
    });

    test('should handle SQL injection attempts in search queries safely', () => {
      const maliciousSearches = [
        "'; DROP TABLE products; --",
        "' OR 1=1 --",
        "'; UPDATE products SET deleted_at = NOW(); --",
      ];

      maliciousSearches.forEach(maliciousQuery => {
        // Should not throw and should not execute malicious SQL
        const results = repository.search(maliciousQuery, validOrgId);
        expect(results).toBeInstanceOf(Array);

        // Verify no damage was done
        const allProducts = repository.findAll(validOrgId);
        expect(allProducts.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Database Constraint Violations', () => {
    test('should handle unique constraint violation on external_id', () => {
      const externalId = 'duplicate-test-id';

      // Insert first product directly to bypass repository deduplication logic
      db.prepare(
        `
        INSERT INTO products (external_id, name, organization_id, created_at, updated_at, is_active)
        VALUES (?, ?, ?, ?, ?, 1)
      `
      ).run(
        externalId,
        'First Product',
        validOrgId,
        new Date().toISOString(),
        new Date().toISOString()
      );

      // Attempt to insert with same external_id should fail
      expect(() => {
        db.prepare(
          `
          INSERT INTO products (external_id, name, organization_id, created_at, updated_at, is_active)
          VALUES (?, ?, ?, ?, ?, 1)
        `
        ).run(
          externalId,
          'Second Product',
          validOrgId,
          new Date().toISOString(),
          new Date().toISOString()
        );
      }).toThrow();
    });

    test('should handle foreign key constraint on product_colors', () => {
      const nonExistentProductId = 99999;
      const nonExistentColorId = 99999;

      expect(() => {
        db.prepare(
          `
          INSERT INTO product_colors (product_id, color_id, organization_id, display_order)
          VALUES (?, ?, ?, 1)
        `
        ).run(nonExistentProductId, nonExistentColorId, validOrgId);
      }).toThrow();
    });

    test('should handle NOT NULL constraint violations', () => {
      expect(() => {
        db.prepare(
          `
          INSERT INTO products (external_id, organization_id, created_at, updated_at, is_active)
          VALUES (?, ?, ?, ?, 1)
        `
        ).run(
          uuidv4(),
          validOrgId,
          new Date().toISOString(),
          new Date().toISOString()
        );
      }).toThrow(); // Missing required 'name' field
    });
  });

  describe('Large Dataset Operations', () => {
    test('should handle insertion of many products efficiently', () => {
      const startTime = Date.now();
      const productIds: string[] = [];

      // Insert 500 products (reduced from 1000 for faster testing)
      for (let i = 0; i < 500; i++) {
        const productId = repository.insert(
          {
            name: `Bulk Product ${i}`,
            description: `Description for product ${i}`,
            organizationId: validOrgId,
          },
          validOrgId,
          validUserId
        );
        productIds.push(productId);
      }

      const endTime = Date.now();

      expect(productIds).toHaveLength(500);
      expect(endTime - startTime).toBeLessThan(15000); // Should complete in under 15 seconds

      // Verify all products were inserted
      const allProducts = repository.findAll(validOrgId);
      expect(allProducts.length).toBeGreaterThanOrEqual(500);
    });

    test('should handle querying large datasets efficiently', () => {
      // First insert many products
      for (let i = 0; i < 200; i++) {
        repository.insert(
          {
            name: `Performance Product ${i}`,
            description: `Performance test product ${i}`,
            organizationId: validOrgId,
          },
          validOrgId,
          validUserId
        );
      }

      const startTime = Date.now();
      const allProducts = repository.findAll(validOrgId);
      const endTime = Date.now();

      expect(allProducts.length).toBeGreaterThanOrEqual(200);
      expect(endTime - startTime).toBeLessThan(1000); // Should be fast even with many products
    });

    test('should handle bulk deletion efficiently', () => {
      // Create many products
      const productIds: string[] = [];
      for (let i = 0; i < 50; i++) {
        const productId = repository.insert(
          {
            name: `Bulk Delete Product ${i}`,
            organizationId: validOrgId,
          },
          validOrgId,
          validUserId
        );
        productIds.push(productId);
      }

      const startTime = Date.now();
      const result = repository.deleteMultiple(productIds, validOrgId);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.deletedIds).toHaveLength(50);
      expect(endTime - startTime).toBeLessThan(5000); // Should be efficient
    });

    test('should handle deduplication of many duplicate products', () => {
      // Create many products with duplicate names
      const duplicateNames = ['Duplicate A', 'Duplicate B', 'Duplicate C'];

      duplicateNames.forEach(name => {
        for (let i = 0; i < 10; i++) {
          repository.insert(
            {
              name: name,
              description: `Instance ${i}`,
              organizationId: validOrgId,
            },
            validOrgId,
            validUserId
          );
        }
      });

      const startTime = Date.now();
      const result = repository.deduplicateProducts(validOrgId);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.deduplicatedCount).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(5000); // Should be efficient

      // Verify deduplication worked
      const allProducts = repository.findAll(validOrgId);
      duplicateNames.forEach(name => {
        const productsWithName = allProducts.filter(p => p.name === name);
        expect(productsWithName).toHaveLength(1);
      });
    });
  });

  describe('Soft Delete Edge Cases', () => {
    test('should handle soft delete of already deleted product', () => {
      const productId = repository.insert(
        {
          name: 'Double Delete Test',
          organizationId: validOrgId,
        },
        validOrgId,
        validUserId
      );

      // First delete should succeed
      const firstDelete = repository.softDelete(productId, validOrgId);
      expect(firstDelete).toBe(true);

      // Second delete should return false (already deleted)
      const secondDelete = repository.softDelete(productId, validOrgId);
      expect(secondDelete).toBe(false);

      // Product should still be deleted
      const product = repository.findById(productId, validOrgId);
      expect(product).toBeNull();
    });

    test('should handle restore of already active product', () => {
      const productId = repository.insert(
        {
          name: 'Double Restore Test',
          organizationId: validOrgId,
        },
        validOrgId,
        validUserId
      );

      // Product is active, restore should return false
      const restoreActive = repository.restoreRecord(productId, validOrgId);
      expect(restoreActive).toBe(false);

      // Delete then restore
      repository.softDelete(productId, validOrgId);
      const restoreDeleted = repository.restoreRecord(productId, validOrgId);
      expect(restoreDeleted).toBe(true);

      // Second restore should return false (already active)
      const secondRestore = repository.restoreRecord(productId, validOrgId);
      expect(secondRestore).toBe(false);
    });

    test('should handle orphaned color cleanup during cascade delete', () => {
      const productId = repository.insert(
        {
          name: 'Orphan Cleanup Test',
          organizationId: validOrgId,
        },
        validOrgId,
        validUserId
      );

      // Create a unique color for this product
      const uniqueColorId = 'unique-orphan-test-color';
      db.prepare(
        `
        INSERT INTO colors (external_id, organization_id, code, name, display_name, hex, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `
      ).run(
        uniqueColorId,
        validOrgId,
        'ORPHAN001',
        'Orphan Test Color',
        'Orphan Test Color',
        '#FF0000',
        new Date().toISOString(),
        new Date().toISOString()
      );

      // Associate color with product
      repository.addProductColor(productId, uniqueColorId, validOrgId);

      // Verify color exists and is active
      const colorBefore = db
        .prepare('SELECT deleted_at FROM colors WHERE external_id = ?')
        .get(uniqueColorId) as { deleted_at: string | null };
      expect(colorBefore.deleted_at).toBeNull();

      // Cascade delete product
      repository.softDelete(productId, validOrgId);

      // Orphaned color should be soft deleted
      const colorAfter = db
        .prepare('SELECT deleted_at FROM colors WHERE external_id = ?')
        .get(uniqueColorId) as { deleted_at: string | null };
      expect(colorAfter.deleted_at).toBeTruthy();
    });

    test('should not delete colors shared by other products', () => {
      const productId1 = repository.insert(
        {
          name: 'Shared Color Test 1',
          organizationId: validOrgId,
        },
        validOrgId,
        validUserId
      );

      const productId2 = repository.insert(
        {
          name: 'Shared Color Test 2',
          organizationId: validOrgId,
        },
        validOrgId,
        validUserId
      );

      const sharedColorId = 'test-color-1-org0'; // Exists in seed data for first org

      // Associate shared color with both products
      repository.addProductColor(productId1, sharedColorId, validOrgId);
      repository.addProductColor(productId2, sharedColorId, validOrgId);

      // Delete first product
      repository.softDelete(productId1, validOrgId);

      // Shared color should still be active (not orphaned)
      const color = db
        .prepare('SELECT deleted_at FROM colors WHERE external_id = ?')
        .get(sharedColorId) as { deleted_at: string | null };
      expect(color.deleted_at).toBeNull();

      // Color should still be associated with second product
      const product2Colors = repository.getProductColors(
        productId2,
        validOrgId
      );
      const hasSharedColor = product2Colors.some(
        c => c.color_external_id === sharedColorId
      );
      expect(hasSharedColor).toBe(true);
    });

    test('should handle soft delete limits and pagination', () => {
      // Create and delete many products
      const deletedIds: string[] = [];
      for (let i = 0; i < 25; i++) {
        const productId = repository.insert(
          {
            name: `Pagination Delete Test ${i}`,
            organizationId: validOrgId,
          },
          validOrgId,
          validUserId
        );
        repository.softDelete(productId, validOrgId);
        deletedIds.push(productId);
      }

      // Test pagination of soft deleted records
      const page1 = repository.findSoftDeleted(validOrgId, 10, 0);
      const page2 = repository.findSoftDeleted(validOrgId, 10, 10);
      const page3 = repository.findSoftDeleted(validOrgId, 10, 20);

      expect(page1).toHaveLength(10);
      expect(page2).toHaveLength(10);
      expect(page3.length).toBeGreaterThan(0);

      // No duplicates between pages
      const allIds = [...page1, ...page2, ...page3].map(p => p.external_id);
      const uniqueIds = new Set(allIds);
      expect(uniqueIds.size).toBe(allIds.length);
    });
  });

  describe('Data Validation & Sanitization', () => {
    test('should handle unicode and special characters in product names', () => {
      const unicodeProducts = [
        { name: '测试产品', description: 'Chinese product' },
        { name: 'Тестовый продукт', description: 'Russian product' },
        { name: 'منتج اختبار', description: 'Arabic product' },
        { name: '🎨 Color Product 🌈', description: 'Emoji product' },
        {
          name: 'Product "Quotes" & <Tags>',
          description: 'Special chars product',
        },
      ];

      unicodeProducts.forEach(product => {
        const productId = repository.insert(
          {
            ...product,
            organizationId: validOrgId,
          },
          validOrgId,
          validUserId
        );

        const insertedProduct = repository.findById(productId, validOrgId);
        expect(insertedProduct?.name).toBe(product.name);
      });
    });

    test('should validate external ID format for updates', () => {
      const invalidIds = ['', 'not-a-uuid', null, undefined, 123, {}, []];

      invalidIds.forEach(invalidId => {
        const result = repository.update(
          invalidId as any,
          { name: 'Test' },
          validOrgId,
          validUserId
        );
        expect(result).toBe(false);
      });
    });

    test('should handle malformed metadata JSON gracefully', () => {
      // Insert product with malformed metadata directly
      const productId = uuidv4();
      db.prepare(
        `
        INSERT INTO products (external_id, name, metadata, organization_id, created_at, updated_at, is_active)
        VALUES (?, ?, ?, ?, ?, ?, 1)
      `
      ).run(
        productId,
        'Malformed Metadata Test',
        'invalid-json{', // Malformed JSON
        validOrgId,
        new Date().toISOString(),
        new Date().toISOString()
      );

      // Repository should handle gracefully
      expect(() => {
        const product = repository.findById(productId, validOrgId);
        expect(product).toBeTruthy();
      }).not.toThrow();

      // Update should work even with malformed existing metadata
      expect(() => {
        repository.update(
          productId,
          { description: 'Updated description' },
          validOrgId,
          validUserId
        );
      }).not.toThrow();
    });
  });

  describe('Organization Isolation', () => {
    test('should enforce strict organization isolation', () => {
      const product1Id = repository.insert(
        {
          name: 'Org 1 Product',
          organizationId: validOrgId,
        },
        validOrgId,
        validUserId
      );

      const product2Id = repository.insert(
        {
          name: 'Org 2 Product',
          organizationId: validOrgId2,
        },
        validOrgId2,
        validUserId
      );

      // Products should be isolated by organization
      const org1Products = repository.findAll(validOrgId);
      const org2Products = repository.findAll(validOrgId2);

      const org1ProductIds = org1Products.map(p => p.external_id);
      const org2ProductIds = org2Products.map(p => p.external_id);

      expect(org1ProductIds).toContain(product1Id);
      expect(org1ProductIds).not.toContain(product2Id);
      expect(org2ProductIds).toContain(product2Id);
      expect(org2ProductIds).not.toContain(product1Id);
    });

    test('should prevent cross-organization access', () => {
      const product1Id = repository.insert(
        {
          name: 'Cross Org Test',
          organizationId: validOrgId,
        },
        validOrgId,
        validUserId
      );

      // Attempt to access product from wrong organization
      const wrongOrgAccess = repository.findById(product1Id, validOrgId2);
      expect(wrongOrgAccess).toBeNull();

      // Attempt to update from wrong organization
      const wrongOrgUpdate = repository.update(
        product1Id,
        { name: 'Hacked' },
        validOrgId2,
        validUserId
      );
      expect(wrongOrgUpdate).toBe(false);

      // Attempt to delete from wrong organization
      const wrongOrgDelete = repository.softDelete(product1Id, validOrgId2);
      expect(wrongOrgDelete).toBe(false);

      // Product should still exist in correct organization
      const correctOrgAccess = repository.findById(product1Id, validOrgId);
      expect(correctOrgAccess).toBeTruthy();
      expect(correctOrgAccess!.name).toBe('Cross Org Test');
    });
  });

  describe('Error Boundary Testing', () => {
    test('should provide meaningful error messages', () => {
      try {
        repository.findAll('invalid-org-id');
      } catch (error) {
        expect(error).toBeInstanceOf(OrganizationValidationError);
        expect((error as Error).message).toContain(
          'Invalid organization ID format'
        );
      }
    });

    test('should handle database corruption gracefully', () => {
      // Simulate database corruption by dropping a table
      db.exec('DROP TABLE IF EXISTS products');

      expect(() => {
        repository.findAll(validOrgId);
      }).toThrow();
    });
  });

  describe('Performance Edge Cases', () => {
    test('should handle products with many color associations efficiently', () => {
      const productId = repository.insert(
        {
          name: 'Many Colors Product',
          organizationId: validOrgId,
        },
        validOrgId,
        validUserId
      );

      // Create and associate many colors
      for (let i = 0; i < 50; i++) {
        const colorId = `performance-color-${i}`;
        db.prepare(
          `
          INSERT INTO colors (external_id, organization_id, code, name, display_name, hex, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `
        ).run(
          colorId,
          validOrgId,
          `PERF${i.toString().padStart(3, '0')}`,
          `Performance Color ${i}`,
          `Performance Color ${i}`,
          `#${i.toString(16).padStart(6, '0')}`,
          new Date().toISOString(),
          new Date().toISOString()
        );

        repository.addProductColor(productId, colorId, validOrgId);
      }

      const startTime = Date.now();
      const productWithColors = repository.getProductWithColors(
        productId,
        validOrgId
      );
      const endTime = Date.now();

      expect(productWithColors).toBeTruthy();
      expect(endTime - startTime).toBeLessThan(500); // Should be fast even with many colors
    });

    test('should handle memory efficiently with large result sets', () => {
      // Insert products and measure memory impact
      const initialMemory = process.memoryUsage().heapUsed;

      for (let i = 0; i < 100; i++) {
        repository.insert(
          {
            name: `Memory Test Product ${i}`,
            description: 'A'.repeat(1000), // Large description
            organizationId: validOrgId,
          },
          validOrgId,
          validUserId
        );
      }

      // Query all products multiple times
      for (let i = 0; i < 5; i++) {
        const products = repository.findAll(validOrgId);
        expect(products.length).toBeGreaterThanOrEqual(100);
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 50MB for this test)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('Prepared Statement Management', () => {
    test('should not create unlimited prepared statements', () => {
      const initialStatements =
        (repository as any).constructor.preparedStatements.size || 0;

      // Perform many operations that create prepared statements
      for (let i = 0; i < 50; i++) {
        repository.findAll(validOrgId);
        repository.search(`query ${i}`, validOrgId);
        if (i % 10 === 0) {
          repository.getAllWithColors(validOrgId);
        }
      }

      const finalStatements =
        (repository as any).constructor.preparedStatements.size || 0;

      // Should reuse prepared statements efficiently
      expect(finalStatements - initialStatements).toBeLessThan(20);
    });
  });
});

// Helper function to seed basic test data
function seedBasicTestData(
  db: Database.Database,
  orgId1: string,
  orgId2: string,
  userId: string
) {
  // Insert test organizations
  db.prepare(
    `
    INSERT OR IGNORE INTO organizations (external_id, name, slug)
    VALUES (?, ?, ?), (?, ?, ?)
  `
  ).run(
    orgId1,
    'Test Organization 1',
    'test-org-1',
    orgId2,
    'Test Organization 2',
    'test-org-2'
  );

  // Insert test colors
  const testColors = [
    {
      external_id: 'test-color-1',
      code: 'TEST001',
      display_name: 'Test Red',
      hex: '#FF0000',
      color_spaces: JSON.stringify({ rgb: { r: 255, g: 0, b: 0 } }),
    },
    {
      external_id: 'test-color-2',
      code: 'TEST002',
      display_name: 'Test Blue',
      hex: '#0000FF',
      color_spaces: JSON.stringify({ rgb: { r: 0, g: 0, b: 255 } }),
    },
  ];

  const insertColor = db.prepare(`
    INSERT INTO colors (external_id, organization_id, code, name, display_name, hex, color_spaces, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  [orgId1, orgId2].forEach((orgId, orgIndex) => {
    testColors.forEach(color => {
      insertColor.run(
        `${color.external_id}-org${orgIndex}`, // Make external_id unique per org
        orgId,
        color.code,
        color.display_name, // name
        color.display_name, // display_name
        color.hex,
        color.color_spaces,
        new Date().toISOString(),
        new Date().toISOString()
      );
    });
  });

  // Insert test products
  const testProducts = [
    {
      external_id: 'test-product-seed-1',
      name: 'Seed Product Alpha',
      description: 'First seed product',
    },
    {
      external_id: 'test-product-seed-2',
      name: 'Seed Product Beta',
      description: 'Second seed product',
    },
  ];

  const insertProduct = db.prepare(`
    INSERT INTO products (external_id, name, metadata, organization_id, user_id, is_active, is_synced, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, 1, 1, ?, ?)
  `);

  [orgId1, orgId2].forEach((orgId, orgIndex) => {
    testProducts.forEach(product => {
      insertProduct.run(
        `${product.external_id}-org${orgIndex}`, // Make external_id unique per org
        product.name,
        JSON.stringify({ description: product.description }),
        orgId,
        userId,
        new Date().toISOString(),
        new Date().toISOString()
      );
    });
  });
}
