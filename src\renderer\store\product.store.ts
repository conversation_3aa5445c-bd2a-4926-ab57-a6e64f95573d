/**
 * @file product.store.ts
 * @description Store for managing products using the optimized schema
 */

import { create } from 'zustand';
import { ColorEntry } from '../../shared/types/color.types';
import { storeEventBus } from '../services/store-event-bus.service';

// IPC Response interface to match the new backend format
interface IPCResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  userMessage?: string;
  timestamp: number;
}

// Helper function to handle IPC responses
function handleIPCResponse<T>(response: T | IPCResponse<T>): T {
  // Check if it's the new IPCResponse format
  if (response && typeof response === 'object' && 'success' in response) {
    const ipcResponse = response as IPCResponse<T>;
    if (ipcResponse.success) {
      return ipcResponse.data as T;
    } else {
      // Check if it's an organization context error
      if ('code' in ipcResponse && (ipcResponse as IPCResponse<T> & { code: string }).code === 'NO_ORGANIZATION_SELECTED') {
        throw new Error('No organization selected. Please select an organization from the dropdown menu.');
      }
      throw new Error(ipcResponse.userMessage || ipcResponse.error || 'Operation failed');
    }
  }
  // Return as-is if it's raw data (legacy format)
  return response as T;
}

// Product interface matching the optimized schema
export interface Product {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductWithColors extends Product {
  colors: ColorEntry[];
}

// Product store interface
export interface ProductState {
  // State
  products: ProductWithColors[];
  activeProductId: string | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchProducts: () => Promise<void>;
  fetchProductsWithColors: () => Promise<void>;
  forceRefreshProducts: () => Promise<void>;
  addColorToProduct: (productId: string, colorId: string) => Promise<boolean>;
  addLibraryColorToProduct: (productId: string, libraryColor: Record<string, unknown>, customName?: string) => Promise<boolean>;
  removeColorFromProduct: (productId: string, colorId: string) => Promise<boolean>;
  createProduct: (name: string, metadata?: Record<string, unknown>) => Promise<Product | null>;
  deleteProduct: (productId: string) => Promise<boolean>;
  deduplicateProducts: () => Promise<{ success: boolean; deduplicatedCount: number; errors: string[] }>;
  setActiveProductId: (id: string | null) => void;
  clearState: () => void;
}

// Create the product store
export const useProductStore = create<ProductState>((set, get) => {
  // Set up event bus listeners when store is created
  if (typeof window !== 'undefined') {
    storeEventBus.subscribe('ORGANIZATION_CLEARED', () => {
      console.log('[ProductStore] Received ORGANIZATION_CLEARED event, clearing products');
      set({
        products: [],
        activeProductId: null,
        isLoading: false,
        error: null
      });
    });

    storeEventBus.subscribe('ORGANIZATION_SWITCHED', (event) => {
      if (event.type === 'ORGANIZATION_SWITCHED') {
        console.log('[ProductStore] Received ORGANIZATION_SWITCHED event, loading products for:', event.organizationId);
        // Trigger product loading asynchronously
        setTimeout(() => {
          get().fetchProductsWithColors().catch(console.error);
        }, 200); // Small delay after color store
      }
    });

    storeEventBus.subscribe('DATA_REFRESH_REQUESTED', (event) => {
      if (event.type === 'DATA_REFRESH_REQUESTED' && event.source !== 'product-store') {
        console.log('[ProductStore] Received DATA_REFRESH_REQUESTED event from:', event.source);
        setTimeout(() => {
          get().fetchProductsWithColors().catch(console.error);
        }, 200);
      }
    });
  }

  return ({
  // Initial state
  products: [],
  activeProductId: null,
  isLoading: false,
  error: null,

  // Fetch all products
  fetchProducts: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await window.productAPI.getAll();
      const products = handleIPCResponse<ProductWithColors[]>(response);
      set({ 
        products: products || [],
        isLoading: false 
      });
    } catch (error) {
      console.error('Error fetching products:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch products',
        isLoading: false 
      });
    }
  },

  // Fetch all products with their colors
  fetchProductsWithColors: async () => {
    console.log('[ProductStore] fetchProductsWithColors called');
    set({ isLoading: true, error: null });
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('[ProductStore] window.ipc not available for fetching products');
        set({ isLoading: false });
        return;
      }
      
      console.log('[ProductStore] Calling window.productAPI.getAllWithColors()...');
      const response = await window.productAPI.getAllWithColors();
      // Handle the new IPCResponse format
      const products = handleIPCResponse<ProductWithColors[]>(response);
      console.log('[ProductStore] Processed products count:', products?.length || 0);
      
      // Debug: Check how many products have colors  
      const productsWithColors = products?.filter(p => p.colors && p.colors.length > 0) || [];
      console.log('[ProductStore] Products with colors:', productsWithColors.length);
      console.log('[ProductStore] Products without colors:', (products?.length || 0) - productsWithColors.length);
      
      set({ 
        products: products || [],
        isLoading: false 
      });
      console.log('[ProductStore] State updated with', products?.length || 0, 'products');
      
      // Emit completion event for sync indicator
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('product-store:refresh-complete', {
          detail: {
            success: true,
            timestamp: Date.now(),
            productCount: products?.length || 0
          }
        }));
      }
    } catch (error) {
      console.error('[ProductStore] Error fetching products with colors:', error);
      console.error('[ProductStore] Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch products',
        isLoading: false 
      });
      
      // Emit completion event for error case
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('product-store:refresh-complete', {
          detail: {
            success: false,
            timestamp: Date.now(),
            error: error instanceof Error ? error.message : 'Failed to fetch products'
          }
        }));
      }
    }
  },

  // Force refresh products with enhanced clearing
  forceRefreshProducts: async () => {
    console.log('[ProductStore] 🔄 Force refresh initiated - clearing state first');
    
    // Clear current state
    set({ 
      products: [], 
      isLoading: true, 
      error: null 
    });
    
    // Small delay to ensure state is cleared
    await new Promise(resolve => setTimeout(resolve, 100));
    
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('[ProductStore] window.ipc not available for force refresh');
        set({ isLoading: false });
        return;
      }
      
      console.log('[ProductStore] 🔄 Calling fresh product data from database...');
      const response = await window.productAPI.getAllWithColors();
      console.log('[ProductStore] 🔄 Force refresh raw response:', JSON.stringify(response, null, 2));
      
      // Handle the new IPCResponse format
      const products = handleIPCResponse<ProductWithColors[]>(response);
      console.log('[ProductStore] 🔄 Force refresh processed products:', products?.length || 0);
      
      set({ 
        products: products || [],
        isLoading: false 
      });
      
      console.log('[ProductStore] ✅ Force refresh completed - state updated with', products?.length || 0, 'products');
    } catch (error) {
      console.error('[ProductStore] ❌ Force refresh failed:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to force refresh products',
        isLoading: false 
      });
    }
  },

  // Add color to product
  addColorToProduct: async (productId: string, colorId: string): Promise<boolean> => {
    try {
      console.log('[ProductStore] 🎨 Adding color to product:', { productId, colorId });
      
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.error('[ProductStore] ❌ window.ipc not available for adding color to product');
        set({ error: 'Application not properly initialized' });
        return false;
      }

      if (!window.productAPI) {
        console.error('[ProductStore] ❌ window.productAPI not available');
        set({ error: 'Product API not available' });
        return false;
      }
      
      console.log('[ProductStore] 📡 Calling productAPI.addColor...');
      const response = await window.productAPI.addColor(productId, colorId);
      console.log('[ProductStore] 📡 Response received:', response);
      
      const success = handleIPCResponse<boolean>(response);
      console.log('[ProductStore] 📡 Processed response:', { success });
      
      if (success) {
        console.log('[ProductStore] ✅ Color added successfully, refreshing products...');
        // Add a small delay to allow sync to complete before refreshing
        setTimeout(async () => {
          await get().fetchProductsWithColors();
        }, 500); // 500ms delay to reduce race conditions
      } else {
        console.error('[ProductStore] ❌ Failed to add color to product');
        set({ error: 'Failed to add color to product' });
      }
      return !!success;
    } catch (error) {
      console.error('[ProductStore] ❌ Error adding color to product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to add color' });
      return false;
    }
  },

  // Add library color to product (creates a new color entry)
  addLibraryColorToProduct: async (productId: string, libraryColor: Record<string, unknown>, customName?: string): Promise<boolean> => {
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for adding library color to product');
        return false;
      }
      
      const response = await window.productAPI.addLibraryColor(productId, libraryColor, customName);
      const createdColor = handleIPCResponse<Record<string, unknown>>(response);
      if (createdColor) {
        // Refresh products to get updated data
        await get().fetchProductsWithColors();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error adding library color to product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to add library color' });
      return false;
    }
  },

  // Remove color from product
  removeColorFromProduct: async (productId: string, colorId: string): Promise<boolean> => {
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for removing color from product');
        return false;
      }
      
      const response = await window.productAPI.removeColor(productId, colorId);
      const success = handleIPCResponse<boolean>(response);
      if (success) {
        // Add a small delay to allow sync to complete before refreshing
        setTimeout(async () => {
          await get().fetchProductsWithColors();
        }, 500); // 500ms delay to reduce race conditions
      }
      return !!success;
    } catch (error) {
      console.error('Error removing color from product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to remove color' });
      return false;
    }
  },

  // Create new product
  createProduct: async (name: string, metadata?: Record<string, unknown>): Promise<Product | null> => {
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for creating product');
        return null;
      }
      
      const response = await window.productAPI.add({ name, metadata });
      const product = handleIPCResponse<Product>(response);
      if (product) {
        // Refresh products to include the new one
        await get().fetchProductsWithColors();
      }
      return product;
    } catch (error) {
      console.error('Error creating product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to create product' });
      return null;
    }
  },

  // Delete product
  deleteProduct: async (productId: string): Promise<boolean> => {
    try {
      console.log(`[ProductStore] Starting deletion of product: ${productId}`);
      
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for deleting product');
        return false;
      }
      
      console.log(`[ProductStore] Calling product:delete IPC for product: ${productId}`);
      const response = await window.productAPI.delete(productId);
      console.log(`[ProductStore] Delete response:`, response);
      console.log(`[ProductStore] Delete response type:`, typeof response);
      console.log(`[ProductStore] Delete response JSON:`, JSON.stringify(response, null, 2));
      
      const success = handleIPCResponse<boolean>(response as boolean | IPCResponse<boolean>);
      console.log(`[ProductStore] Delete success:`, success);
      
      if (success) {
        console.log(`[ProductStore] Delete successful, refreshing products...`);
        // Refresh products
        await get().fetchProductsWithColors();
        console.log(`[ProductStore] Products refreshed after deletion`);
      } else {
        console.warn(`[ProductStore] Delete failed for product: ${productId}`);
      }
      return !!success;
    } catch (error) {
      console.error('Error deleting product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to delete product' });
      return false;
    }
  },

  // Deduplicate products
  deduplicateProducts: async (): Promise<{ success: boolean; deduplicatedCount: number; errors: string[] }> => {
    try {
      console.log('[ProductStore] Starting product deduplication...');
      
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('[ProductStore] window.ipc not available for deduplication');
        return { success: false, deduplicatedCount: 0, errors: ['IPC not available'] };
      }
      
      const response = await window.productAPI.deduplicate();
      const result = handleIPCResponse<{ deduplicatedCount: number; errors: string[] }>(response);
      
      console.log(`[ProductStore] Deduplication result:`, result);
      
      if (result) {
        // Refresh products after deduplication
        await get().fetchProductsWithColors();
        return { 
          success: true, 
          deduplicatedCount: result.deduplicatedCount, 
          errors: result.errors 
        };
      }
      
      return { success: false, deduplicatedCount: 0, errors: ['No result from deduplication'] };
    } catch (error) {
      console.error('[ProductStore] Error during deduplication:', error);
      return { 
        success: false, 
        deduplicatedCount: 0, 
        errors: [error instanceof Error ? error.message : 'Failed to deduplicate products'] 
      };
    }
  },

  // Set active product ID
  setActiveProductId: (id: string | null) => {
    console.log('[ProductStore] 🎯 setActiveProductId called:', {
      newId: id,
      currentId: get().activeProductId,
      productsCount: get().products.length
    });
    set({ activeProductId: id });
  },

  // Clear state
  clearState: () => {
    set({
      products: [],
      activeProductId: null,
      isLoading: false,
      error: null
    });
  }
  });
});