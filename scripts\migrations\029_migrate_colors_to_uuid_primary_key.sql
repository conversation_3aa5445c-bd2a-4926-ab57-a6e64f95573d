-- Migration 029: Convert colors table to pure UUID primary key
-- This migration transforms the colors table from dual-ID system to pure UUID architecture
-- WARNING: This is a breaking change that modifies core table structure
-- Must be executed AFTER migration 028 (products table migration)

BEGIN TRANSACTION;

-- Step 1: Create backup table with current data
CREATE TABLE colors_backup_pre_uuid_migration AS 
SELECT * FROM colors;

-- Step 2: Drop existing indexes on colors table
DROP INDEX IF EXISTS idx_colors_external;
DROP INDEX IF EXISTS idx_colors_hex;
DROP INDEX IF EXISTS idx_colors_source;
DROP INDEX IF EXISTS idx_colors_org;
DROP INDEX IF EXISTS idx_colors_gradient_colors;
DROP INDEX IF EXISTS idx_colors_notes;
DROP INDEX IF EXISTS idx_colors_tags;
DROP INDEX IF EXISTS idx_colors_is_library;
DROP INDEX IF EXISTS idx_colors_library_gradient;

-- Step 3: Store foreign key data for product_colors junction table
-- At this point, product_colors should already have UUID product_ids from migration 028
-- We need to update the color_id references to use UUIDs as well
CREATE TEMPORARY TABLE product_colors_color_migration_map AS
SELECT 
    pc.product_id,  -- This is already a UUID from products migration
    pc.color_id as old_color_id,  -- This is still referencing colors.id (integer)
    c.external_id as color_uuid,  -- This will become the new color_id
    pc.display_order,
    pc.organization_id,
    pc.added_at
FROM product_colors pc
JOIN colors c ON pc.color_id = c.id
WHERE c.external_id IS NOT NULL AND length(c.external_id) = 36;

-- Step 4: Clear product_colors table temporarily (we'll repopulate it)
DELETE FROM product_colors;

-- Step 5: Create new colors table with UUID primary key
CREATE TABLE colors_new (
  id TEXT PRIMARY KEY,  -- This was external_id, now becomes primary key
  name TEXT NOT NULL,
  display_name TEXT,
  code TEXT,
  hex TEXT NOT NULL,
  source_id INTEGER NOT NULL DEFAULT 1 REFERENCES color_sources(id),
  -- Color spaces as JSON (Supabase compatibility)
  color_spaces JSON DEFAULT '{}',
  -- Color characteristics  
  is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
  is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
  is_effect BOOLEAN NOT NULL DEFAULT FALSE,
  -- Cloud compatibility columns
  gradient_colors TEXT,  -- CSV of hex values for gradients
  notes TEXT,           -- User notes
  tags TEXT,            -- User tags
  is_library BOOLEAN NOT NULL DEFAULT FALSE,
  -- Legacy properties
  properties JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  version INTEGER NOT NULL DEFAULT 1,
  -- Organization support (UUID)
  organization_id TEXT,
  created_by TEXT,
  user_id TEXT,
  deleted_at TEXT,
  device_id TEXT,
  conflict_resolved_at TEXT,
  is_synced INTEGER DEFAULT 0,
  
  -- Constraints
  CHECK (length(id) = 36),  -- UUID v4 format validation
  CHECK (length(trim(name)) > 0),
  CHECK (length(hex) = 7 AND substr(hex, 1, 1) = '#' AND 
         hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]')
);

-- Step 6: Migrate data from old table to new table
INSERT INTO colors_new (
    id, name, display_name, code, hex, source_id, color_spaces,
    is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
    is_library, properties, created_at, updated_at, version,
    organization_id, created_by, user_id, deleted_at, device_id,
    conflict_resolved_at, is_synced
)
SELECT 
    external_id as id,  -- external_id becomes the primary key
    name, display_name, code, hex, source_id, color_spaces,
    is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
    is_library, properties, created_at, updated_at, version,
    organization_id, created_by, user_id, deleted_at, device_id,
    conflict_resolved_at, is_synced
FROM colors
WHERE external_id IS NOT NULL AND length(external_id) = 36;

-- Step 7: Verify data migration
-- Count records to ensure no data loss
SELECT 
    (SELECT COUNT(*) FROM colors WHERE external_id IS NOT NULL) as original_count,
    (SELECT COUNT(*) FROM colors_new) as migrated_count;

-- Step 8: Drop old colors table and rename new one
DROP TABLE colors;
ALTER TABLE colors_new RENAME TO colors;

-- Step 9: Recreate indexes for UUID primary key
CREATE INDEX IF NOT EXISTS idx_colors_hex ON colors(hex);
CREATE INDEX IF NOT EXISTS idx_colors_source ON colors(source_id);
CREATE INDEX IF NOT EXISTS idx_colors_org ON colors(organization_id);
CREATE INDEX IF NOT EXISTS idx_colors_gradient_colors ON colors(gradient_colors) WHERE gradient_colors IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_colors_notes ON colors(notes) WHERE notes IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_colors_tags ON colors(tags) WHERE tags IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_colors_is_library ON colors(is_library);
CREATE INDEX IF NOT EXISTS idx_colors_library_gradient ON colors(is_library, is_gradient);
CREATE INDEX IF NOT EXISTS idx_colors_name ON colors(name);
CREATE INDEX IF NOT EXISTS idx_colors_code ON colors(code) WHERE code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_colors_created_at ON colors(created_at);
CREATE INDEX IF NOT EXISTS idx_colors_updated_at ON colors(updated_at);
CREATE INDEX IF NOT EXISTS idx_colors_deleted_at ON colors(deleted_at);

-- Step 10: Update product_colors table to use UUID color foreign keys
-- Repopulate product_colors with UUID foreign keys for both products and colors
INSERT INTO product_colors (product_id, color_id, display_order, organization_id, added_at)
SELECT 
    product_id,           -- Already UUID from products migration
    color_uuid as color_id,  -- Now using UUID for colors too
    display_order,
    organization_id,
    added_at
FROM product_colors_color_migration_map
WHERE product_id IS NOT NULL 
  AND color_uuid IS NOT NULL
  AND length(product_id) = 36
  AND length(color_uuid) = 36;

-- Step 11: Update triggers for UUID-based colors table
DROP TRIGGER IF EXISTS update_colors_timestamp;

CREATE TRIGGER IF NOT EXISTS update_colors_timestamp 
AFTER UPDATE ON colors
BEGIN
  UPDATE colors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Step 12: Clean up temporary tables
DROP TABLE product_colors_color_migration_map;

-- Step 13: Add migration record
INSERT OR IGNORE INTO schema_migrations (version, name) 
VALUES (29, 'migrate_colors_to_uuid_primary_key');

-- Verification queries (run after migration)
-- Uncomment these to verify migration success:

-- Check colors table structure
-- PRAGMA table_info(colors);

-- Verify UUID format in colors
-- SELECT COUNT(*) as total_colors,
--        COUNT(CASE WHEN length(id) = 36 THEN 1 END) as valid_uuid_colors
-- FROM colors;

-- Verify product_colors relationships with UUIDs
-- SELECT COUNT(*) as total_relationships,
--        COUNT(CASE WHEN length(product_id) = 36 AND length(color_id) = 36 THEN 1 END) as valid_uuid_relationships
-- FROM product_colors;

-- Check foreign key integrity for both directions
-- SELECT c.id, c.name, COUNT(pc.product_id) as product_count
-- FROM colors c
-- LEFT JOIN product_colors pc ON c.id = pc.color_id
-- GROUP BY c.id, c.name
-- HAVING product_count > 0
-- LIMIT 10;

-- Verify products can still find their colors
-- SELECT p.id, p.name, COUNT(pc.color_id) as color_count
-- FROM products p
-- LEFT JOIN product_colors pc ON p.id = pc.product_id
-- GROUP BY p.id, p.name
-- HAVING color_count > 0
-- LIMIT 10;

COMMIT;

-- Post-migration notes:
-- 1. The colors table now uses UUID as primary key (id column)
-- 2. Product-color relationships now use UUIDs for both foreign keys
-- 3. All color-related foreign key references have been updated
-- 4. Backup table (colors_backup_pre_uuid_migration) contains original data
-- 5. Next migration should handle organizations table
-- 6. Repository layer will need updates to remove external_id→internal_id conversion
-- 7. Both products and colors tables are now fully UUID-based