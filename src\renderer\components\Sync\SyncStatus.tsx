/**
 * @file SyncStatus.tsx
 * @description Component for displaying sync status
 */

import React, { useEffect, useState } from 'react';
import {
  useSyncStatus,
  useSyncAuth,
  useSyncActions,
} from '../../store/sync.store';
import { SyncStatus as SyncStatusEnum } from '../../../shared/types/sync.types';

// Format a timestamp as a readable date/time
const formatTimestamp = (timestamp: number | null): string => {
  if (!timestamp) {
    return 'Never';
  }

  const date = new Date(timestamp);
  return date.toLocaleString();
};

// Status indicator component
const StatusIndicator: React.FC<{ status: SyncStatusEnum }> = ({ status }) => {
  const getStatusColor = () => {
    switch (status) {
      case SyncStatusEnum.SUCCESS:
        return 'bg-green-500 dark:bg-green-400';
      case SyncStatusEnum.ERROR:
        return 'bg-red-500 dark:bg-red-400';
      case SyncStatusEnum.SYNCING:
        return 'bg-brand-primary dark:bg-brand-primary animate-pulse';
      default:
        return 'bg-gray-500 dark:bg-gray-400';
    }
  };

  return <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />;
};

interface SyncStatusProps {
  showControls?: boolean;
}

export const SyncStatus: React.FC<SyncStatusProps> = ({
  showControls = true,
}) => {
  const { status, message, error, lastSyncTime, syncStats } = useSyncStatus();
  const { isAuthenticated } = useSyncAuth();
  const { syncData } = useSyncActions();
  const [isSyncing, setIsSyncing] = useState(false);

  // Reset syncing state when status changes
  useEffect(() => {
    if (status !== SyncStatusEnum.SYNCING) {
      setIsSyncing(false);
    }
  }, [status]);

  // Handle sync button click
  const handleSync = async () => {
    if (isSyncing) {
      return;
    }

    setIsSyncing(true);
    await syncData();
  };

  if (!isAuthenticated) {
    return (
      <div className='flex items-center space-x-2 text-sm text-ui-foreground-secondary dark:text-ui-foreground-secondary'>
        <StatusIndicator status={SyncStatusEnum.IDLE} />
        <span>Not logged in</span>
      </div>
    );
  }

  return (
    <div className='flex flex-col space-y-2'>
      <div className='flex items-center space-x-2'>
        <StatusIndicator status={status} />
        <span className='text-sm text-ui-foreground-primary dark:text-ui-foreground-primary'>
          {status === SyncStatusEnum.SYNCING
            ? 'Syncing...'
            : status === SyncStatusEnum.SUCCESS
              ? 'Synced'
              : status === SyncStatusEnum.ERROR
                ? 'Sync failed'
                : 'Ready to sync'}
        </span>
      </div>

      {message && (
        <div className='text-xs text-ui-foreground-secondary dark:text-ui-foreground-secondary'>
          {message}
        </div>
      )}

      {error && (
        <div className='text-xs text-red-500 dark:text-red-400'>{error}</div>
      )}

      <div className='text-xs text-ui-foreground-secondary dark:text-ui-foreground-secondary'>
        Last synced: {formatTimestamp(lastSyncTime)}
      </div>

      {status === SyncStatusEnum.SUCCESS && syncStats && (
        <div className='text-xs text-ui-foreground-secondary dark:text-ui-foreground-secondary'>
          <div>Synced data:</div>
          <ul className='list-disc list-inside ml-2 mt-1'>
            <li>Colors: {syncStats.colors}</li>
            <li>Products: {syncStats.products}</li>
            <li>Datasheets: {syncStats.datasheets}</li>
          </ul>
        </div>
      )}

      {showControls && (
        <button
          onClick={handleSync}
          disabled={isSyncing || status === SyncStatusEnum.SYNCING}
          className={`mt-2 px-3 py-1 text-sm rounded-[var(--radius-md)] ${
            isSyncing || status === SyncStatusEnum.SYNCING
              ? 'bg-ui-background-tertiary dark:bg-ui-background-tertiary text-ui-foreground-secondary dark:text-ui-foreground-secondary cursor-not-allowed'
              : 'bg-brand-primary hover:bg-brand-primary/90 text-ui-foreground-inverse'
          }`}
        >
          {isSyncing || status === SyncStatusEnum.SYNCING
            ? 'Syncing...'
            : 'Sync Now'}
        </button>
      )}
    </div>
  );
};
