/**
 * @file IPCRouter.test.ts
 * @description Comprehensive test suite for Express.js-style IPC Router system
 *
 * Tests the IPC Router's ability to handle route registration, middleware execution,
 * parameter extraction, and integration with ChromaSync's existing security infrastructure.
 */

import { IPCRouter } from '../IPCRouter';
import { IpcMainEvent } from 'electron';
import { IPCResponse } from '../../../shared/types/ipc.types';
import { ipcValidation } from '../middleware/IPCValidationMiddleware';
import { describe, test, expect, beforeEach, vi } from 'vitest';

// Mock Electron IPC event
const createMockEvent = (
  url: string = 'file://test.html',
  senderId: number = 1
): Partial<IpcMainEvent> => ({
  sender: {
    id: senderId,
    getURL: () => url,
    isDestroyed: () => false,
  } as any,
});

// Mock handlers for testing
const mockHandler = vi.fn().mockResolvedValue({ success: true, data: 'test' });
const mockMiddleware = vi.fn((req, res, next) => next());
const mockErrorHandler = vi.fn().mockRejectedValue(new Error('Test error'));

describe('IPCRouter', () => {
  let router: IPCRouter;

  beforeEach(() => {
    router = new IPCRouter();
    vi.clearAllMocks();
  });

  describe('Route Registration', () => {
    test('should register basic route successfully', () => {
      router.register('get', '/api/colors', mockHandler);

      const routes = router.getRoutes();
      expect(routes).toHaveLength(1);
      expect(routes[0].method).toBe('get');
      expect(routes[0].path).toBe('/api/colors');
    });

    test('should register route with middleware', () => {
      router.register('post', '/api/colors', mockMiddleware, mockHandler);

      const routes = router.getRoutes();
      expect(routes[0].middleware).toHaveLength(1);
    });

    test('should register route with multiple middleware', () => {
      const middleware2 = vi.fn((req, res, next) => next());

      router.register(
        'put',
        '/api/colors/:id',
        mockMiddleware,
        middleware2,
        mockHandler
      );

      const routes = router.getRoutes();
      expect(routes[0].middleware).toHaveLength(2);
    });

    test('should support HTTP method shortcuts', () => {
      router.get('/api/colors', mockHandler);
      router.post('/api/colors', mockHandler);
      router.put('/api/colors/:id', mockHandler);
      router.delete('/api/colors/:id', mockHandler);

      const routes = router.getRoutes();
      expect(routes).toHaveLength(4);
      expect(routes.map(r => r.method)).toEqual([
        'get',
        'post',
        'put',
        'delete',
      ]);
    });

    test('should register global middleware with use()', () => {
      router.use('*', mockMiddleware);

      const globalMiddleware = router.getGlobalMiddleware();
      expect(globalMiddleware).toHaveLength(1);
    });

    test('should register path-specific middleware with use()', () => {
      router.use('/api/*', mockMiddleware);

      const pathMiddleware = router.getPathMiddleware();
      expect(pathMiddleware.has('/api/*')).toBe(true);
    });

    test('should throw error for invalid route pattern', () => {
      expect(() => {
        router.register('get', '', mockHandler);
      }).toThrow('Route path cannot be empty');
    });

    test('should throw error for invalid HTTP method', () => {
      expect(() => {
        router.register('invalid' as any, '/api/test', mockHandler);
      }).toThrow('Invalid HTTP method');
    });

    test('should prevent duplicate route registration', () => {
      router.register('get', '/api/colors', mockHandler);

      expect(() => {
        router.register('get', '/api/colors', mockHandler);
      }).toThrow('Route already registered');
    });
  });

  describe('Route Matching', () => {
    beforeEach(() => {
      router.register('get', '/api/colors', mockHandler);
      router.register('get', '/api/colors/:id', mockHandler);
      router.register('post', '/api/colors', mockHandler);
      router.register(
        'get',
        '/api/products/:productId/colors/:colorId',
        mockHandler
      );
      router.register('get', '/api/admin/*', mockHandler);
    });

    test('should match exact routes', () => {
      const match = router.matchRoute('get:/api/colors');

      expect(match).not.toBeNull();
      expect(match!.route.path).toBe('/api/colors');
      expect(match!.params).toEqual({});
    });

    test('should match parametric routes', () => {
      const match = router.matchRoute('get:/api/colors/123');

      expect(match).not.toBeNull();
      expect(match!.route.path).toBe('/api/colors/:id');
      expect(match!.params).toEqual({ id: '123' });
    });

    test('should match complex parametric routes', () => {
      const match = router.matchRoute('get:/api/products/456/colors/789');

      expect(match).not.toBeNull();
      expect(match!.route.path).toBe(
        '/api/products/:productId/colors/:colorId'
      );
      expect(match!.params).toEqual({ productId: '456', colorId: '789' });
    });

    test('should match wildcard routes', () => {
      const match = router.matchRoute('get:/api/admin/users/settings');

      expect(match).not.toBeNull();
      expect(match!.route.path).toBe('/api/admin/*');
    });

    test('should distinguish HTTP methods', () => {
      const getMatch = router.matchRoute('get:/api/colors');
      const postMatch = router.matchRoute('post:/api/colors');

      expect(getMatch).not.toBeNull();
      expect(postMatch).not.toBeNull();
      expect(getMatch!.route.method).toBe('get');
      expect(postMatch!.route.method).toBe('post');
    });

    test('should return null for unmatched routes', () => {
      const match = router.matchRoute('get:/api/nonexistent');
      expect(match).toBeNull();
    });

    test('should parse channel format correctly', () => {
      // Test legacy channel format
      const legacyMatch = router.parseChannel('color:getAll');
      expect(legacyMatch).toEqual({ method: 'get', path: 'color:getAll' });

      // Test router channel format
      const routerMatch = router.parseChannel('get:/api/colors');
      expect(routerMatch).toEqual({ method: 'get', path: '/api/colors' });
    });

    test('should handle malformed channels gracefully', () => {
      const match = router.parseChannel('malformed');
      expect(match).toEqual({ method: 'get', path: 'malformed' });
    });
  });

  describe('Middleware System', () => {
    let executionOrder: string[];

    beforeEach(() => {
      executionOrder = [];
    });

    const createOrderedMiddleware = (name: string) =>
      jest.fn((req, res, next) => {
        executionOrder.push(name);
        next();
      });

    test('should execute middleware in correct order', async () => {
      const global = createOrderedMiddleware('global');
      const path = createOrderedMiddleware('path');
      const route = createOrderedMiddleware('route');
      const handler = jest.fn(req => {
        executionOrder.push('handler');
        return Promise.resolve({ success: true, data: 'test' });
      });

      router.use('*', global);
      router.use('/api/*', path);
      router.register('get', '/api/colors', route, handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      await router.handle('get:/api/colors', mockEvent);

      expect(executionOrder).toEqual(['global', 'path', 'route', 'handler']);
    });

    test('should stop execution when middleware throws error', async () => {
      const errorMiddleware = jest.fn(() => {
        throw new Error('Middleware error');
      });
      const handler = jest.fn();

      router.register('get', '/api/test', errorMiddleware, handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      const response = await router.handle('get:/api/test', mockEvent);

      expect(response.success).toBe(false);
      expect(response.error).toContain('Middleware error');
      expect(handler).not.toHaveBeenCalled();
    });

    test('should pass request object through middleware chain', async () => {
      const middleware1 = jest.fn((req, res, next) => {
        req.custom1 = 'value1';
        next();
      });
      const middleware2 = jest.fn((req, res, next) => {
        req.custom2 = 'value2';
        next();
      });
      const handler = jest.fn(req => {
        expect(req.custom1).toBe('value1');
        expect(req.custom2).toBe('value2');
        return Promise.resolve({ success: true, data: 'test' });
      });

      router.register('get', '/api/test', middleware1, middleware2, handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      await router.handle('get:/api/test', mockEvent, { test: 'data' });

      expect(middleware1).toHaveBeenCalled();
      expect(middleware2).toHaveBeenCalled();
      expect(handler).toHaveBeenCalled();
    });

    test('should provide response helper methods', async () => {
      const middleware = jest.fn((req, res, next) => {
        expect(res.success).toBeInstanceOf(Function);
        expect(res.error).toBeInstanceOf(Function);
        expect(res.validation).toBeInstanceOf(Function);
        next();
      });

      router.register('get', '/api/test', middleware, mockHandler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      await router.handle('get:/api/test', mockEvent);

      expect(middleware).toHaveBeenCalled();
    });
  });

  describe('Parameter Extraction', () => {
    beforeEach(() => {
      router.register('get', '/api/colors/:id', mockHandler);
      router.register(
        'get',
        '/api/products/:productId/colors/:colorId',
        mockHandler
      );
      router.register(
        'get',
        '/api/users/:userId/settings/:setting',
        mockHandler
      );
    });

    test('should extract single parameter', async () => {
      const handler = jest.fn(req => {
        expect(req.params.id).toBe('123');
        return Promise.resolve({ success: true, data: 'test' });
      });

      router.register('get', '/api/test/:id', handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      await router.handle('get:/api/test/123', mockEvent);

      expect(handler).toHaveBeenCalled();
    });

    test('should extract multiple parameters', async () => {
      const handler = jest.fn(req => {
        expect(req.params.productId).toBe('456');
        expect(req.params.colorId).toBe('789');
        return Promise.resolve({ success: true, data: 'test' });
      });

      router.register('get', '/api/test/:productId/colors/:colorId', handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      await router.handle('get:/api/test/456/colors/789', mockEvent);

      expect(handler).toHaveBeenCalled();
    });

    test('should sanitize parameters to prevent injection', async () => {
      const handler = jest.fn(req => {
        // Parameters should be sanitized
        expect(req.params.id).toHaveLength(50); // Truncated
        return Promise.resolve({ success: true, data: 'test' });
      });

      router.register('get', '/api/test/:id', handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      const longParam = 'a'.repeat(100); // Long parameter
      await router.handle(`get:/api/test/${longParam}`, mockEvent);

      expect(handler).toHaveBeenCalled();
    });

    test('should handle URL-encoded parameters', async () => {
      const handler = jest.fn(req => {
        expect(req.params.name).toBe('test color');
        return Promise.resolve({ success: true, data: 'test' });
      });

      router.register('get', '/api/test/:name', handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      await router.handle('get:/api/test/test%20color', mockEvent);

      expect(handler).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    test('should handle handler errors gracefully', async () => {
      const errorHandler = jest
        .fn()
        .mockRejectedValue(new Error('Handler error'));
      router.register('get', '/api/error', errorHandler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      const response = await router.handle('get:/api/error', mockEvent);

      expect(response.success).toBe(false);
      expect(response.error).toContain('Handler error');
      expect(response.userMessage).toContain('Unable to complete');
    });

    test('should handle middleware errors gracefully', async () => {
      const errorMiddleware = jest.fn(() => {
        throw new Error('Middleware error');
      });

      router.register('get', '/api/error', errorMiddleware, mockHandler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      const response = await router.handle('get:/api/error', mockEvent);

      expect(response.success).toBe(false);
      expect(response.error).toContain('Middleware error');
      expect(mockHandler).not.toHaveBeenCalled();
    });

    test('should handle route not found', async () => {
      const mockEvent = createMockEvent() as IpcMainEvent;
      const response = await router.handle('get:/api/nonexistent', mockEvent);

      expect(response.success).toBe(false);
      expect(response.error).toBe('Route not found');
      expect(response.userMessage).toContain('endpoint not found');
    });

    test('should provide detailed error information in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const errorHandler = jest.fn().mockImplementation(() => {
        throw new Error('Detailed error for development');
      });

      router.register('get', '/api/dev-error', errorHandler);

      // Restore environment
      process.env.NODE_ENV = originalEnv;
    });

    test('should sanitize error messages in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const errorHandler = jest.fn().mockImplementation(() => {
        throw new Error('Sensitive production error');
      });

      router.register('get', '/api/prod-error', errorHandler);

      // Restore environment
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Security Features', () => {
    test('should validate sender origin', async () => {
      const mockEvent = createMockEvent(
        'https://malicious.com'
      ) as IpcMainEvent;
      router.register('get', '/api/test', mockHandler);

      const response = await router.handle('get:/api/test', mockEvent);

      expect(response.success).toBe(false);
      expect(response.error).toContain('Unauthorized sender');
    });

    test('should reject destroyed senders', async () => {
      const mockEvent = {
        sender: {
          id: 1,
          getURL: () => 'file://test.html',
          isDestroyed: () => true,
        },
      } as IpcMainEvent;

      router.register('get', '/api/test', mockHandler);

      const response = await router.handle('get:/api/test', mockEvent);

      expect(response.success).toBe(false);
      expect(response.error).toContain('Unauthorized sender');
    });

    test('should protect against DoS with parameter limits', async () => {
      const handler = jest.fn(req => {
        expect(req.params.id).toHaveLength(50); // Truncated for security
        return Promise.resolve({ success: true, data: 'test' });
      });

      router.register('get', '/api/test/:id', handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      const maliciousParam = 'x'.repeat(1000); // Very long parameter
      await router.handle(`get:/api/test/${maliciousParam}`, mockEvent);

      expect(handler).toHaveBeenCalled();
    });

    test('should handle malformed channels safely', async () => {
      const mockEvent = createMockEvent() as IpcMainEvent;

      const response1 = await router.handle('', mockEvent);
      expect(response1.success).toBe(false);

      const response2 = await router.handle('malformed:::channel', mockEvent);
      expect(response2.success).toBe(false);
    });
  });

  describe('Integration with ChromaSync Infrastructure', () => {
    test('should integrate with organization context validation', async () => {
      // Mock organization middleware
      const orgMiddleware = jest.fn((req, res, next) => {
        req.organizationId = 'test-org-123';
        next();
      });

      const handler = jest.fn(req => {
        expect(req.organizationId).toBe('test-org-123');
        return Promise.resolve({ success: true, data: 'test' });
      });

      router.register('get', '/api/colors', orgMiddleware, handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      await router.handle('get:/api/colors', mockEvent);

      expect(orgMiddleware).toHaveBeenCalled();
      expect(handler).toHaveBeenCalled();
    });

    test('should integrate with input validation middleware', async () => {
      // Mock validation middleware
      const validationMiddleware = jest.fn((req, res, next) => {
        if (!req.body.name) {
          throw new Error('Name is required');
        }
        next();
      });

      const handler = jest.fn(() =>
        Promise.resolve({ success: true, data: 'test' })
      );

      router.register('post', '/api/colors', validationMiddleware, handler);

      const mockEvent = createMockEvent() as IpcMainEvent;

      // Test with invalid data
      const invalidResponse = await router.handle(
        'post:/api/colors',
        mockEvent,
        {}
      );
      expect(invalidResponse.success).toBe(false);
      expect(invalidResponse.error).toContain('Name is required');

      // Test with valid data
      const validResponse = await router.handle('post:/api/colors', mockEvent, {
        name: 'Test Color',
      });
      expect(validResponse.success).toBe(true);
    });

    test('should integrate with rate limiting middleware', async () => {
      let requestCount = 0;

      const rateLimitMiddleware = jest.fn((req, res, next) => {
        requestCount++;
        if (requestCount > 2) {
          throw new Error('Rate limit exceeded');
        }
        next();
      });

      router.register('get', '/api/test', rateLimitMiddleware, mockHandler);

      const mockEvent = createMockEvent() as IpcMainEvent;

      // First two requests should succeed
      const response1 = await router.handle('get:/api/test', mockEvent);
      expect(response1.success).toBe(true);

      const response2 = await router.handle('get:/api/test', mockEvent);
      expect(response2.success).toBe(true);

      // Third request should fail
      const response3 = await router.handle('get:/api/test', mockEvent);
      expect(response3.success).toBe(false);
      expect(response3.error).toContain('Rate limit exceeded');
    });

    test('should use response helper methods', async () => {
      const handler = jest.fn((req, res) => {
        return res.success({ id: 123, name: 'Test' }, 'Created successfully');
      });

      router.register('post', '/api/test', handler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      const response = await router.handle('post:/api/test', mockEvent);

      expect(response.success).toBe(true);
      expect(response.data).toEqual({ id: 123, name: 'Test' });
      expect(response.userMessage).toBe('Created successfully');
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle large number of routes efficiently', () => {
      const startTime = Date.now();

      // Register 1000 routes
      for (let i = 0; i < 1000; i++) {
        router.register('get', `/api/route-${i}`, mockHandler);
      }

      const registrationTime = Date.now() - startTime;
      expect(registrationTime).toBeLessThan(1000); // Should register 1000 routes in under 1 second

      // Test route matching performance
      const matchStartTime = Date.now();
      const match = router.matchRoute('get:/api/route-500');
      const matchTime = Date.now() - matchStartTime;

      expect(match).not.toBeNull();
      expect(matchTime).toBeLessThan(50); // Should match route in under 50ms
    });

    test('should cache compiled route patterns for performance', () => {
      router.register(
        'get',
        '/api/complex/:param1/nested/:param2/deep/:param3',
        mockHandler
      );

      // First match compiles the pattern
      const match1 = router.matchRoute('get:/api/complex/a/nested/b/deep/c');
      expect(match1).not.toBeNull();

      // Subsequent matches should use cached pattern
      const match2 = router.matchRoute('get:/api/complex/x/nested/y/deep/z');
      expect(match2).not.toBeNull();
      expect(match2!.params).toEqual({ param1: 'x', param2: 'y', param3: 'z' });
    });

    test('should handle complex nested middleware chains efficiently', async () => {
      const middleware = Array.from({ length: 10 }, (_, i) =>
        jest.fn((req, res, next) => {
          req[`middleware${i}`] = true;
          next();
        })
      );

      router.register('get', '/api/complex', ...middleware, mockHandler);

      const mockEvent = createMockEvent() as IpcMainEvent;
      const startTime = Date.now();

      await router.handle('get:/api/complex', mockEvent);

      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(100); // Should execute 10 middleware + handler in under 100ms

      // Verify all middleware executed
      middleware.forEach(mw => expect(mw).toHaveBeenCalled());
    });
  });

  describe('Router Utility Methods', () => {
    test('should provide route inspection methods', () => {
      router.register('get', '/api/colors', mockHandler);
      router.register('post', '/api/colors', mockHandler);
      router.use('*', mockMiddleware);

      const routes = router.getRoutes();
      expect(routes).toHaveLength(2);

      const globalMiddleware = router.getGlobalMiddleware();
      expect(globalMiddleware).toHaveLength(1);

      const stats = router.getStats();
      expect(stats.totalRoutes).toBe(2);
      expect(stats.totalMiddleware).toBeGreaterThan(0);
    });

    test('should provide route grouping functionality', () => {
      const colorGroup = router.group('/api/colors');

      colorGroup.get('/', mockHandler);
      colorGroup.post('/', mockHandler);
      colorGroup.get('/:id', mockHandler);

      const routes = router.getRoutes();
      expect(routes).toHaveLength(3);
      expect(routes.map(r => r.path)).toEqual([
        '/api/colors/',
        '/api/colors/',
        '/api/colors/:id',
      ]);
    });
  });
});
