Write-Host "Starting ChromaSync..." -ForegroundColor Green
Write-Host ""

Set-Location "C:\Projects\Applications\chromasync-mac"

Write-Host "Cleaning electron installation..." -ForegroundColor Yellow
Remove-Item -Path "node_modules\electron" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Installing Windows Electron..." -ForegroundColor Yellow
$env:npm_config_target_platform = "win32"
$env:npm_config_target_arch = "x64"
$env:npm_config_disturl = "https://electronjs.org/headers"
$env:npm_config_runtime = "electron"
npm install electron --save-dev

Write-Host "Installing other dependencies..." -ForegroundColor Yellow
npm install

Write-Host ""
Write-Host "Building and starting ChromaSync..." -ForegroundColor Green
npm run start:windows

Read-Host "Press Enter to continue..."