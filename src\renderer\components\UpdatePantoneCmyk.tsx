import React, { useState } from 'react';
import { Button, Typography, Box, CircularProgress } from '@mui/material';
import { updatePantoneCmykToCoated } from '../utils/updatePantoneCmyk';

/**
 * Component to update Pantone CMYK values to coated
 */
const UpdatePantoneCmyk: React.FC = () => {
  const [updating, setUpdating] = useState(false);
  const [result, setResult] = useState<{
    updated: number;
    skipped: number;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleUpdate = async () => {
    setUpdating(true);
    setResult(null);
    setError(null);

    try {
      const updateResult = await updatePantoneCmykToCoated();
      setResult(updateResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setUpdating(false);
    }
  };

  return (
    <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 1, mb: 2 }}>
      <Typography variant='h6' gutterBottom>
        Update Pantone CMYK Values to Coated
      </Typography>
      <Typography variant='body2' color='text.secondary' paragraph>
        This will update all Pantone CMYK values from uncoated to coated values
        based on the PANTONES COATED IVG.pdf document.
      </Typography>

      <Button
        variant='contained'
        color='primary'
        onClick={handleUpdate}
        disabled={updating}
        startIcon={
          updating ? <CircularProgress size={20} color='inherit' /> : null
        }
      >
        {updating ? 'Updating...' : 'Update CMYK Values'}
      </Button>

      {result && (
        <Box sx={{ mt: 2 }}>
          <Typography color='success.main'>
            Update completed successfully!
          </Typography>
          <Typography variant='body2'>
            Updated: {result.updated} colors
          </Typography>
          <Typography variant='body2'>
            Skipped: {result.skipped} colors (already using coated values)
          </Typography>
        </Box>
      )}

      {error && (
        <Box sx={{ mt: 2 }}>
          <Typography color='error'>Error: {error}</Typography>
        </Box>
      )}
    </Box>
  );
};

export default UpdatePantoneCmyk;
