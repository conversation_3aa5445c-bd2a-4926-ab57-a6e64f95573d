/**
 * @file product.repository.ts
 * @description Repository for product data access operations
 * 
 * Handles all database operations for products following the Repository pattern.
 * Separates data access concerns from business logic in ProductService.
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { 
  IProductRepository, 
  ProductRow, 
  ProductWithColorsRow,
  ProductColorRow,
  DuplicationGroupRow,
  ProductDetailRow
} from './interfaces/product.repository.interface';
import { NewProduct, UpdateProduct } from '../../shared/types/product.types';
import { requireValidOrganizationId } from '../../utils/organization-validation';
import { createSoftDeleteManager, SoftDeleteQueries } from '../../utils/soft-delete-patterns';
import { getOrCreateDeviceId } from '../../utils/deviceId';

export class ProductRepository implements IProductRepository {
  private static preparedStatements = new Map<string, Database.Statement>();
  private softDeleteManager: ReturnType<typeof createSoftDeleteManager>;

  constructor(private db: Database.Database) {
    this.softDeleteManager = createSoftDeleteManager(db);
  }

  // Core CRUD Operations
  findAll(organizationId: string): ProductRow[] {
    requireValidOrganizationId(organizationId);

    const { where, params } = SoftDeleteQueries.activeRecordsWhere(organizationId);
    
    const stmt = this.getPreparedStatement(`
      SELECT 
        id,
        id as external_id,
        organization_id,
        user_id,
        name,
        description,
        metadata,
        is_active,
        is_synced,
        created_at,
        updated_at,
        deleted_at,
        created_by
      FROM products
      WHERE organization_id = ? AND deleted_at IS NULL AND is_active = 1
      ORDER BY name ASC
    `);

    return stmt.all(organizationId) as ProductRow[];
  }

  findById(productId: string, organizationId: string): ProductRow | null {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      SELECT 
        id,
        id as external_id,
        organization_id,
        user_id,
        name,
        description,
        metadata,
        is_active,
        is_synced,
        created_at,
        updated_at,
        deleted_at,
        created_by
      FROM products
      WHERE id = ? AND organization_id = ? AND deleted_at IS NULL AND is_active = 1
    `);

    const result = stmt.get(productId, organizationId) as ProductRow | undefined;
    return result || null;
  }

  insert(productData: NewProduct, organizationId: string, userId?: string, fromSync: boolean = false): string {
    requireValidOrganizationId(organizationId);

    const productId = uuidv4();
    const now = new Date().toISOString();
    const deviceId = getOrCreateDeviceId();

    // Check for existing product with same name
    const existing = this.getPreparedStatement(`
      SELECT id FROM products 
      WHERE name = ? AND organization_id = ? AND deleted_at IS NULL AND is_active = 1
    `).get(productData.name, organizationId) as { id: string } | undefined;

    if (existing) {
      return existing.id; // Return existing product ID
    }

    const stmt = this.getPreparedStatement(`
      INSERT INTO products (
        id, name, metadata, organization_id, user_id, 
        created_at, updated_at, is_synced
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const metadata = JSON.stringify({
      description: productData.description,
      createdBy: userId || deviceId,
      updatedBy: userId || deviceId
    });

    stmt.run(
      productId,
      productData.name,
      metadata,
      organizationId,
      userId || null,
      now,
      now,
      fromSync ? 1 : 0
    );

    return productId;
  }

  update(id: string, updates: UpdateProduct, organizationId: string, userId?: string, fromSync: boolean = false): boolean {
    requireValidOrganizationId(organizationId);

    // Get current metadata
    const current = this.getPreparedStatement(`
      SELECT metadata FROM products WHERE id = ? AND organization_id = ?
    `).get(id, organizationId) as { metadata: string } | undefined;

    if (!current) {
      return false;
    }

    const currentMetadata = current.metadata ? JSON.parse(current.metadata) : {};
    const deviceId = getOrCreateDeviceId();
    const now = new Date().toISOString();

    const updatedMetadata = {
      ...currentMetadata,
      description: updates.description !== undefined ? updates.description : currentMetadata.description,
      updatedBy: userId || deviceId
    };

    const updateFields: string[] = [];
    const updateValues: unknown[] = [];

    if (updates.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(updates.name);
    }

    updateFields.push('metadata = ?');
    updateValues.push(JSON.stringify(updatedMetadata));
    updateFields.push('updated_at = ?');
    updateValues.push(now);
    updateFields.push('is_synced = ?');
    updateValues.push(fromSync ? 1 : 0);
    
    updateValues.push(id);
    updateValues.push(organizationId);

    const stmt = this.getPreparedStatement(`
      UPDATE products 
      SET ${updateFields.join(', ')} 
      WHERE id = ? AND organization_id = ?
    `);

    const result = stmt.run(...updateValues);
    return result.changes > 0;
  }

  softDelete(productId: string, organizationId: string): boolean {
    requireValidOrganizationId(organizationId);

    // Get the product ID for cascade operations
    const product = this.getPreparedStatement(`
      SELECT id FROM products WHERE id = ? AND organization_id = ?
    `).get(productId, organizationId) as { id: string } | undefined;

    if (!product) {
      return false;
    }

    const deleteTransaction = this.db.transaction(() => {
      // 1. Soft delete related datasheets
      this.getPreparedStatement(`
        UPDATE datasheets 
        SET is_active = 0, updated_at = datetime('now')
        WHERE product_id = ? AND is_active = 1
      `).run(product.id);

      // 2. Get colors that will become orphaned
      const orphanedColors = this.getPreparedStatement(`
        SELECT DISTINCT c.id, c.id as external_id 
        FROM colors c
        JOIN product_colors pc ON c.id = pc.color_id
        WHERE pc.product_id = ? AND c.organization_id = ?
        AND NOT EXISTS (
          SELECT 1 FROM product_colors pc2 
          WHERE pc2.color_id = c.id AND pc2.product_id != ?
        )
      `).all(product.id, organizationId, product.id);

      // 3. Remove product-color associations
      this.getPreparedStatement(`
        DELETE FROM product_colors WHERE product_id = ?
      `).run(product.id);

      // 4. Delete orphaned colors (colors with no remaining product associations)
      if (orphanedColors.length > 0) {
        const orphanedColorIds = orphanedColors.map((c: any) => c.id);
        const placeholders = orphanedColorIds.map(() => '?').join(',');
        this.getPreparedStatement(`
          UPDATE colors 
          SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
          WHERE id IN (${placeholders}) AND organization_id = ?
        `).run(...orphanedColorIds, organizationId);
      }

      // 5. Soft delete the product
      const result = this.getPreparedStatement(`
        UPDATE products 
        SET deleted_at = CURRENT_TIMESTAMP, is_active = 0, updated_at = CURRENT_TIMESTAMP, is_synced = 0
        WHERE id = ? AND organization_id = ? AND deleted_at IS NULL
      `).run(productId, organizationId);

      return result.changes > 0;
    });

    return deleteTransaction();
  }

  // Query Operations
  search(query: string, organizationId: string): ProductRow[] {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      SELECT 
        id,
        id as external_id,
        organization_id,
        user_id,
        name,
        description,
        metadata,
        is_active,
        is_synced,
        created_at,
        updated_at,
        deleted_at,
        created_by
      FROM products
      WHERE is_active = 1 AND organization_id = ? AND name LIKE ?
      ORDER BY name ASC
    `);

    return stmt.all(organizationId, `%${query}%`) as ProductRow[];
  }

  findUnsynced(): ProductRow[] {
    const stmt = this.getPreparedStatement(`
      SELECT 
        id,
        id as external_id,
        organization_id,
        user_id,
        name,
        description,
        metadata,
        is_active,
        is_synced,
        created_at,
        updated_at,
        deleted_at,
        created_by
      FROM products 
      WHERE is_synced = 0
    `);

    return stmt.all() as ProductRow[];
  }

  getAllWithColors(organizationId: string): ProductWithColorsRow[] {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      SELECT 
        p.id as product_id,
        p.name as product_name,
        p.description as product_description,
        p.created_at as product_created_at,
        p.updated_at as product_updated_at,
        c.id as color_id,
        c.code as color_code,
        c.display_name as color_name,
        c.hex as color_hex,
        c.color_spaces,
        c.is_gradient,
        c.gradient_colors,
        c.notes as color_notes,
        c.tags as color_tags,
        c.is_library,
        c.created_at as color_created_at,
        c.updated_at as color_updated_at,
        pc.display_order
      FROM products p
      LEFT JOIN product_colors pc ON p.id = pc.product_id
      LEFT JOIN colors c ON pc.color_id = c.id AND c.deleted_at IS NULL AND c.organization_id = ?
      WHERE p.organization_id = ? AND p.deleted_at IS NULL AND p.is_active = 1
      ORDER BY p.name, pc.display_order
    `);

    return stmt.all(organizationId, organizationId) as ProductWithColorsRow[];
  }

  getProductWithColors(productId: string, organizationId: string): ProductWithColorsRow | null {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      SELECT 
        p.id as product_id,
        p.name as product_name,
        p.description as product_description,
        p.created_at as product_created_at,
        p.updated_at as product_updated_at,
        c.id as color_id,
        c.code as color_code,
        c.display_name as color_name,
        c.hex as color_hex,
        c.color_spaces,
        c.is_gradient,
        c.gradient_colors,
        c.notes as color_notes,
        c.tags as color_tags,
        c.is_library,
        c.created_at as color_created_at,
        c.updated_at as color_updated_at,
        pc.display_order
      FROM products p
      LEFT JOIN product_colors pc ON p.id = pc.product_id
      LEFT JOIN colors c ON pc.color_id = c.id AND c.deleted_at IS NULL
      WHERE p.id = ? AND p.organization_id = ? AND p.deleted_at IS NULL AND p.is_active = 1
      ORDER BY pc.display_order
    `);

    const results = stmt.all(productId, organizationId) as ProductWithColorsRow[];
    return results.length > 0 ? results[0] : null;
  }

  // Product-Color Relationship Operations
  addProductColor(productId: string, colorId: string, organizationId: string): boolean {
    requireValidOrganizationId(organizationId);

    // Get internal IDs
    const product = this.getPreparedStatement(`
      SELECT id FROM products WHERE id = ? AND organization_id = ? AND deleted_at IS NULL
    `).get(productId, organizationId) as { id: number } | undefined;

    const color = this.getPreparedStatement(`
      SELECT id FROM colors WHERE id = ? AND organization_id = ? AND deleted_at IS NULL
    `).get(colorId, organizationId) as { id: number } | undefined;

    if (!product || !color) {
      return false;
    }

    // Get next display order
    const maxOrder = this.getPreparedStatement(`
      SELECT MAX(display_order) as max_order 
      FROM product_colors 
      WHERE product_id = ?
    `).get(product.id) as { max_order: number | null } | undefined;

    const displayOrder = (maxOrder?.max_order || 0) + 1;

    // Insert product-color association
    const stmt = this.getPreparedStatement(`
      INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order, organization_id)
      VALUES (?, ?, ?, ?)
    `);

    const result = stmt.run(product.id, color.id, displayOrder, organizationId);
    return result.changes > 0;
  }

  removeProductColor(productId: string, colorId: string, organizationId: string): boolean {
    requireValidOrganizationId(organizationId);

    // Get internal IDs
    const product = this.getPreparedStatement(`
      SELECT id FROM products WHERE id = ? AND organization_id = ? AND deleted_at IS NULL
    `).get(productId, organizationId) as { id: number } | undefined;

    const color = this.getPreparedStatement(`
      SELECT id FROM colors WHERE id = ? AND organization_id = ? AND deleted_at IS NULL
    `).get(colorId, organizationId) as { id: number } | undefined;

    if (!product || !color) {
      return false;
    }

    const stmt = this.getPreparedStatement(`
      DELETE FROM product_colors 
      WHERE product_id = ? AND color_id = ? AND organization_id = ?
    `);

    const result = stmt.run(product.id, color.id, organizationId);
    return result.changes > 0;
  }

  getProductColors(productId: string, organizationId: string): ProductColorRow[] {
    requireValidOrganizationId(organizationId);

    // Get internal product ID
    const product = this.getPreparedStatement(`
      SELECT id FROM products WHERE id = ? AND organization_id = ?
    `).get(productId, organizationId) as { id: number } | undefined;

    if (!product) {
      return [];
    }

    const stmt = this.getPreparedStatement(`
      SELECT 
        pc.product_id,
        pc.color_id,
        c.id as color_external_id,
        pc.display_order,
        pc.organization_id
      FROM product_colors pc
      JOIN colors c ON pc.color_id = c.id
      WHERE pc.product_id = ? AND pc.organization_id = ? AND c.deleted_at IS NULL
      ORDER BY pc.display_order
    `);

    return stmt.all(product.id, organizationId) as ProductColorRow[];
  }

  // Soft Delete Operations
  findSoftDeleted(organizationId: string, limit: number = 100, offset: number = 0): ProductRow[] {
    requireValidOrganizationId(organizationId);

    return this.softDeleteManager.getSoftDeletedRecords('products', organizationId, limit, offset)
      .map((row: any) => ({
        id: row.id,
        external_id: row.id,
        organization_id: row.organization_id,
        user_id: row.user_id,
        name: row.name,
        description: row.description,
        metadata: row.metadata,
        is_active: row.is_active,
        is_synced: row.is_synced,
        created_at: row.created_at,
        updated_at: row.updated_at,
        deleted_at: row.deleted_at,
        created_by: row.created_by
      })) as ProductRow[];
  }

  restoreRecord(productId: string, organizationId: string): boolean {
    requireValidOrganizationId(organizationId);

    const result = this.softDeleteManager.restoreRecord('products', 'id', productId, {
      organizationId,
      context: 'product_restore'
    });

    if (result.success) {
      // Also reactivate the product
      this.getPreparedStatement(`
        UPDATE products 
        SET is_active = 1, updated_at = ?
        WHERE id = ? AND organization_id = ?
      `).run(new Date().toISOString(), productId, organizationId);
    }

    return result.success;
  }

  // Bulk Operations
  deleteMultiple(productIds: string[], organizationId: string): { success: boolean; deletedIds: string[] } {
    requireValidOrganizationId(organizationId);

    const deletedIds: string[] = [];

    try {
      const deleteTransaction = this.db.transaction(() => {
        for (const id of productIds) {
          if (this.softDelete(id, organizationId)) {
            deletedIds.push(id);
          }
        }
      });

      deleteTransaction();
      
      return {
        success: deletedIds.length > 0,
        deletedIds
      };
    } catch (error) {
      console.error('[ProductRepository] Error deleting multiple products:', error);
      return {
        success: false,
        deletedIds: []
      };
    }
  }

  upsertFromSupabase(supabaseProduct: any, organizationId: string, userId?: string): ProductRow | null {
    requireValidOrganizationId(organizationId);

    const now = new Date().toISOString();
    const deviceId = getOrCreateDeviceId();

    try {
      // Prepare metadata with SKU from Supabase
      let metadata: any = {};
      
      // Preserve existing metadata if product exists
      const existing = this.getPreparedStatement(`
        SELECT metadata FROM products WHERE id = ? AND organization_id = ?
      `).get(supabaseProduct.id, organizationId) as { metadata: string } | undefined;

      if (existing && existing.metadata) {
        try {
          metadata = JSON.parse(existing.metadata);
        } catch (error) {
          metadata = {};
        }
      }

      // Update metadata with data from Supabase
      metadata = {
        ...metadata,
        description: supabaseProduct.description || metadata.description,
        sku: supabaseProduct.sku,
        syncedAt: now,
        syncedFrom: 'supabase',
        updatedBy: userId || deviceId
      };

      if (!existing) {
        metadata.createdBy = userId || deviceId;
      }

      // Use UPSERT pattern
      const stmt = this.getPreparedStatement(`
        INSERT INTO products (id, name, metadata, organization_id, user_id, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON CONFLICT(id, organization_id) DO UPDATE SET
          name = excluded.name,
          metadata = excluded.metadata,
          user_id = excluded.user_id,
          updated_at = excluded.updated_at
      `);

      stmt.run(
        supabaseProduct.id,
        supabaseProduct.name,
        JSON.stringify(metadata),
        supabaseProduct.organization_id,
        userId || null,
        existing ? undefined : now,
        now
      );

      return this.findById(supabaseProduct.id, organizationId);

    } catch (error) {
      console.error('[ProductRepository] Error upserting product from Supabase:', error);
      return null;
    }
  }

  deduplicateProducts(organizationId: string): { success: boolean; deduplicatedCount: number; errors: string[] } {
    requireValidOrganizationId(organizationId);

    try {
      // Get all products grouped by name
      const duplicateGroups = this.getPreparedStatement(`
        SELECT name, COUNT(*) as count, GROUP_CONCAT(id) as ids
        FROM products 
        WHERE organization_id = ? AND is_active = 1
        GROUP BY name 
        HAVING COUNT(*) > 1
        ORDER BY count DESC
      `).all(organizationId) as DuplicationGroupRow[];

      let deduplicatedCount = 0;
      const errors: string[] = [];

      const deduplicationTransaction = this.db.transaction(() => {
        for (const group of duplicateGroups) {
          try {
            const productIds = group.ids.split(',');

            // Get detailed info for each product in this group
            const products = productIds.map(id => {
              const product = this.getPreparedStatement(`
                SELECT id, id as external_id, name, created_at
                FROM products 
                WHERE id = ? AND organization_id = ?
              `).get(id.trim(), organizationId) as ProductDetailRow | undefined;

              if (!product) return null;

              // Count colors for this product
              const colorCount = this.getPreparedStatement(`
                SELECT COUNT(*) as count
                FROM product_colors pc
                JOIN colors c ON pc.color_id = c.id
                WHERE pc.product_id = ? AND pc.organization_id = ? AND c.is_active = 1
              `).get(product.id, organizationId) as { count: number } | undefined;

              return {
                ...product,
                colorCount: colorCount?.count || 0
              };
            }).filter(p => p !== null) as ProductDetailRow[];

            if (products.length <= 1) continue;

            // Sort products: most colors first, then by creation date (oldest first)
            products.sort((a, b) => {
              if (a.colorCount !== b.colorCount) {
                return b.colorCount - a.colorCount;
              }
              return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
            });

            const keepProduct = products[0];
            const deleteProducts = products.slice(1);

            // Move color associations from duplicates to the kept product
            for (const duplicateProduct of deleteProducts) {
              if (duplicateProduct.colorCount > 0) {
                // Transfer color associations
                this.getPreparedStatement(`
                  UPDATE OR IGNORE product_colors 
                  SET product_id = ?
                  WHERE product_id = ? AND organization_id = ?
                `).run(keepProduct.id, duplicateProduct.id, organizationId);

                // Remove any remaining duplicated associations
                this.getPreparedStatement(`
                  DELETE FROM product_colors 
                  WHERE product_id = ? AND organization_id = ?
                `).run(duplicateProduct.id, organizationId);
              }

              // Soft delete the duplicate product
              this.getPreparedStatement(`
                UPDATE products 
                SET is_active = 0, updated_at = ?
                WHERE id = ? AND organization_id = ?
              `).run(new Date().toISOString(), duplicateProduct.id, organizationId);

              deduplicatedCount++;
            }

          } catch (error) {
            const errorMsg = `Failed to deduplicate ${group.name}: ${error instanceof Error ? error.message : String(error)}`;
            errors.push(errorMsg);
          }
        }
      });

      deduplicationTransaction();

      return {
        success: true,
        deduplicatedCount,
        errors
      };

    } catch (error) {
      return {
        success: false,
        deduplicatedCount: 0,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

  // Utility Operations
  markAsSynced(productId: string): void {
    const stmt = this.getPreparedStatement(`
      UPDATE products 
      SET is_synced = 1
      WHERE id = ?
    `);

    stmt.run(productId);
  }


  getPreparedStatement(sql: string): Database.Statement {
    if (!ProductRepository.preparedStatements.has(sql)) {
      ProductRepository.preparedStatements.set(sql, this.db.prepare(sql));
    }
    return ProductRepository.preparedStatements.get(sql)!;
  }
}