/**
 * @file store-utilities.ts
 * @description Unified export for all shared store utilities
 * Provides a single import point for consistent store behavior across the application
 */

// Import all dependencies
import {
  createStoreDataLoader as _createStoreDataLoader,
  createSimpleStoreDataLoader,
  createColorDataLoader,
  createProductDataLoader,
  createStoreDataLoaderResult,
  type StoreDataLoaderConfig,
  type StoreState,
  type StateSetter,
} from './shared-store-data-loader';

import {
  handleIPCResponse as _handleIPCResponse,
  safeHandleIPCResponse,
  isIPCResponse,
  createSuccessResponse,
  createErrorResponse,
  validateResponseData,
  IPCError,
  type IPCResponse,
} from './ipc-response-handler';

import {
  validateOrganizationContext,
  getValidatedOrganizationId,
  hasOrganizationContext,
  waitForOrganizationContext,
  safeValidateOrganizationContext,
  type OrganizationValidationResult,
  type OrganizationContextConfig,
} from './organization-context-validator';

import {
  emitStoreCompletionEvent as _emitStoreCompletionEvent,
  listenForStoreCompletion,
  waitForStoreCompletion,
  emitRefreshComplete,
  createStoreTimer,
  logStoreOperation as _logStoreOperation,
  StoreOperationTimer,
  type StoreCompletionDetail,
} from './store-event-completion';

// Re-export with correct names
export {
  createSimpleStoreDataLoader,
  createColorDataLoader,
  createProductDataLoader,
  createStoreDataLoaderResult,
  type StoreDataLoaderConfig,
  type StoreState,
  type StateSetter,
  safeHandleIPCResponse,
  isIPCResponse,
  createSuccessResponse,
  createErrorResponse,
  validateResponseData,
  IPCError,
  type IPCResponse,
  validateOrganizationContext,
  getValidatedOrganizationId,
  hasOrganizationContext,
  waitForOrganizationContext,
  safeValidateOrganizationContext,
  type OrganizationValidationResult,
  type OrganizationContextConfig,
  listenForStoreCompletion,
  waitForStoreCompletion,
  emitRefreshComplete,
  createStoreTimer,
  StoreOperationTimer,
  type StoreCompletionDetail,
};

// Make functions available for use within this module
const createStoreDataLoader = _createStoreDataLoader;
const handleIPCResponse = _handleIPCResponse;
const emitStoreCompletionEvent = _emitStoreCompletionEvent;
const logStoreOperation = _logStoreOperation;

/**
 * Quick setup functions for common store patterns
 */

/**
 * Create a complete store data loading setup with standardized timing
 * @param storeName - Name of the store
 * @param apiCall - API function to call
 * @param organizationRequired - Whether organization context is required
 * @returns Object with data loader and helper functions
 */
export async function setupStoreDataLoading<T>(
  storeName: string,
  apiCall: () => Promise<T>,
  organizationRequired = true
) {
  const { createStoreDataLoader } = await import('./shared-store-data-loader');
  const { emitStoreCompletionEvent, logStoreOperation } = await import('./store-event-completion');
  
  const dataLoader = createStoreDataLoader<T>({
    storeName,
    operation: 'refresh',
    apiCall,
    organizationRequired,
    organizationConfig: {
      retryAttempts: 3,
      retryDelay: 200,
      requiresBackendSync: true,
    },
  });

  const refreshComplete = (success: boolean, data?: T, error?: string) => {
    emitStoreCompletionEvent(storeName, 'refresh', success, data, error);
  };

  const logOperation = (
    status: 'start' | 'success' | 'error',
    details?: any
  ) => {
    logStoreOperation(storeName, 'refresh', status, details);
  };

  return {
    dataLoader,
    refreshComplete,
    logOperation,
    storeName,
  };
}

/**
 * Create event bus listeners with standardized timing for organization switches
 * @param storeName - Name of the store
 * @param dataLoader - Data loading function
 * @param delay - Delay in milliseconds (default: 200ms to match ProductStore)
 * @returns Setup function to call in store initialization
 */
export function setupStoreEventListeners<T>(
  storeName: string,
  dataLoader: (setState: any) => Promise<void>,
  delay = 200
) {
<<<<<<< HEAD
  return function setupEventListeners(setState: StateSetter<T>) {
    if (typeof window === 'undefined') {return;}

    // Import store event bus dynamically to avoid circular dependencies
    import('../services/store-event-bus.service')
      .then(({ storeEventBus }) => {
        // Organization cleared event
        storeEventBus.subscribe('ORGANIZATION_CLEARED', () => {
          logStoreOperation(
            storeName,
            'clear',
            'start',
            'Organization cleared event received'
          );
          setState({
            data: undefined,
            isLoading: false,
            error: undefined,
          });
=======
  return function setupEventListeners(setState: any) {
    if (typeof window === 'undefined') return;

    // Import dependencies dynamically to avoid circular dependencies
    Promise.all([
      import('../services/store-event-bus.service'),
      import('./store-event-completion')
    ]).then(([{ storeEventBus }, { logStoreOperation }]) => {
      // Organization cleared event
      storeEventBus.subscribe('ORGANIZATION_CLEARED', () => {
        logStoreOperation(storeName, 'clear', 'start', 'Organization cleared event received');
        setState({
          data: undefined,
          isLoading: false,
          error: undefined
>>>>>>> main
        });

        // Organization switched event
        storeEventBus.subscribe('ORGANIZATION_SWITCHED', event => {
          if (event.type === 'ORGANIZATION_SWITCHED') {
            logStoreOperation(
              storeName,
              'switch',
              'start',
              `Organization switched to: ${event.organizationId}`
            );

            // Use standardized timing delay
            setTimeout(() => {
              dataLoader(setState).catch(error => {
                logStoreOperation(storeName, 'switch', 'error', error.message);
              });
            }, delay);
          }
        });

        // Data refresh requested event
        storeEventBus.subscribe('DATA_REFRESH_REQUESTED', event => {
          if (
            event.type === 'DATA_REFRESH_REQUESTED' &&
            event.source !== storeName
          ) {
            logStoreOperation(
              storeName,
              'refresh',
              'start',
              `Data refresh requested by: ${event.source}`
            );

            setTimeout(() => {
              dataLoader(setState).catch(error => {
                logStoreOperation(storeName, 'refresh', 'error', error.message);
              });
            }, delay);
          }
        });
      })
      .catch(error => {
        console.error(`[${storeName}] Failed to setup event listeners:`, error);
      });
  };
}

/**
 * Common store state interface
 */
export interface CommonStoreState<T> {
  data: T | undefined;
  isLoading: boolean;
  error: string | undefined;
}

/**
 * Common store actions interface
 */
export interface CommonStoreActions<T> {
  loadData: () => Promise<void>;
  clearState: () => void;
  setError: (error: string | undefined) => void;
}

/**
 * Complete store setup with all utilities
 * @param config - Store configuration
 * @returns Complete store utilities
 */
export async function createCompleteStoreSetup<T>(config: {
  storeName: string;
  apiCall: () => Promise<T>;
  organizationRequired?: boolean;
  eventDelay?: number;
  validateData?: (data: T) => boolean;
  transformData?: (data: T) => T;
}) {
  const {
    storeName,
    apiCall,
    organizationRequired = true,
    eventDelay = 200,
    validateData,
    transformData,
  } = config;

  // Import dependencies dynamically
  const [
    { createStoreDataLoader },
    { emitStoreCompletionEvent, logStoreOperation }
  ] = await Promise.all([
    import('./shared-store-data-loader'),
    import('./store-event-completion')
  ]);

  // Create data loader
  const dataLoader = createStoreDataLoader<T>({
    storeName,
    operation: 'refresh',
    apiCall,
    organizationRequired,
    validateData,
    transformData,
    organizationConfig: {
      retryAttempts: 3,
      retryDelay: 200,
      requiresBackendSync: true,
    },
  });

  // Create event listeners setup
  const setupEventListeners = setupStoreEventListeners(
    storeName,
    dataLoader,
    eventDelay
  );

  // Create common actions
  const createActions = (setState: any) => ({
    loadData: () => dataLoader(setState),

    clearState: () => {
      setState({
        data: undefined,
        isLoading: false,
        error: undefined,
      });
    },
<<<<<<< HEAD

=======
    
>>>>>>> main
    setError: (error: string | undefined) => {
      setState({ error, isLoading: false });
    },
  });

  return {
    dataLoader,
    setupEventListeners,
    createActions,
    emitRefreshComplete: (success: boolean, data?: T, error?: string) =>
      emitStoreCompletionEvent(storeName, 'refresh', success, data, error),
    logOperation: (status: 'start' | 'success' | 'error', details?: any) =>
      logStoreOperation(storeName, 'refresh', status, details),
  };
}

/**
 * Legacy compatibility - export the old handleIPCResponse function
 * @deprecated Use handleIPCResponse from ipc-response-handler instead
 */
<<<<<<< HEAD
export const handleIPCResponseLegacy = handleIPCResponse;
=======
export const handleIPCResponseLegacy = async <T>(response: T | any): Promise<T> => {
  const { handleIPCResponse } = await import('./ipc-response-handler');
  return handleIPCResponse(response);
};
>>>>>>> main
