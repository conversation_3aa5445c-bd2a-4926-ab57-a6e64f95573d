/**
 * @file product-color-relationship.service.test.ts
 * @description Comprehensive unit tests for ProductColorRelationshipService
 */

import { describe, it, expect, beforeEach, vi, type Mock } from 'vitest';
import { ProductColorRelationshipService } from '../product-color-relationship.service';
import { ProductRepository } from '../../../db/repositories/product.repository';
import { ColorRepository } from '../../../db/repositories/color.repository';
import type {
  ProductRow,
  ProductColorRow,
} from '../../../db/repositories/interfaces/product.repository.interface';
import type { ColorRow } from '../../../db/repositories/interfaces/color.repository.interface';

// Mock the repositories
vi.mock('../../../db/repositories/product.repository');
vi.mock('../../../db/repositories/color.repository');

// Mock validation utilities
vi.mock('../../../utils/input-validation', () => ({
  validateRequired: vi.fn((field, value) => value),
}));

describe('ProductColorRelationshipService', () => {
  let service: ProductColorRelationshipService;
  let mockProductRepository: vi.Mocked<ProductRepository>;
  let mockColorRepository: vi.Mocked<ColorRepository>;

  const testOrgId = 'test-org-123';
  const testProductId = 'product-123';
  const testColorId = 'color-123';

  const mockProduct: ProductRow = {
    id: 1,
    external_id: testProductId,
    organization_id: testOrgId,
    user_id: 'user-123',
    name: 'Test Product',
    description: 'Test Description',
    metadata: '{}',
    is_active: true,
    is_synced: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    deleted_at: null,
    created_by: 'user-123',
  };

  const mockColor: ColorRow = {
    id: 'color-internal-123',
    external_id: testColorId,
    organization_id: testOrgId,
    source_id: 1,
    code: 'TEST001',
    display_name: 'Test Color',
    hex: '#FF0000',
    color_spaces: '{"cmyk": [0, 100, 100, 0]}',
    is_gradient: false,
    is_metallic: false,
    is_effect: false,
    is_library: false,
    gradient_colors: null,
    notes: null,
    tags: null,
    properties: null,
    is_synced: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    deleted_at: null,
    source: 'PANTONE',
  };

  beforeEach(() => {
    vi.clearAllMocks();

    mockProductRepository = new ProductRepository(
      {} as any
    ) as vi.Mocked<ProductRepository>;
    mockColorRepository = new ColorRepository(
      {} as any
    ) as vi.Mocked<ColorRepository>;

    service = new ProductColorRelationshipService(
      mockProductRepository,
      mockColorRepository
    );
  });

  describe('addColorToProduct', () => {
    it('should successfully add a color to a product', () => {
      // Arrange
      mockProductRepository.findById.mockReturnValue(mockProduct);
      mockColorRepository.findById.mockReturnValue(mockColor);
      mockProductRepository.getProductColors.mockReturnValue([]);
      mockProductRepository.addProductColor.mockReturnValue(true);

      // Act
      const result = service.addColorToProduct(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
      expect(mockProductRepository.findById).toHaveBeenCalledWith(
        testProductId,
        testOrgId
      );
      expect(mockColorRepository.findById).toHaveBeenCalledWith(
        testColorId,
        testOrgId
      );
      expect(mockProductRepository.addProductColor).toHaveBeenCalledWith(
        testProductId,
        testColorId,
        testOrgId
      );
    });

    it('should return true if color is already associated with product', () => {
      // Arrange
      mockProductRepository.findById.mockReturnValue(mockProduct);
      mockColorRepository.findById.mockReturnValue(mockColor);
      mockProductRepository.getProductColors.mockReturnValue([
        {
          product_id: 1,
          color_id: 1,
          color_external_id: testColorId,
          display_order: 1,
          organization_id: testOrgId,
        },
      ]);

      // Act
      const result = service.addColorToProduct(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
      expect(mockProductRepository.addProductColor).not.toHaveBeenCalled();
    });

    it('should fail when product does not exist', () => {
      // Arrange
      mockProductRepository.findById.mockReturnValue(null);

      // Act
      const result = service.addColorToProduct(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Product product-123 not found');
    });

    it('should fail when color does not exist', () => {
      // Arrange
      mockProductRepository.findById.mockReturnValue(mockProduct);
      mockColorRepository.findById.mockReturnValue(null);

      // Act
      const result = service.addColorToProduct(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Color color-123 not found');
    });
  });

  describe('removeColorFromProduct', () => {
    it('should successfully remove a color from a product', () => {
      // Arrange
      mockProductRepository.removeProductColor.mockReturnValue(true);

      // Act
      const result = service.removeColorFromProduct(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.removed).toBe(true);
      expect(result.data?.colorDeleted).toBe(false);
      expect(mockProductRepository.removeProductColor).toHaveBeenCalledWith(
        testProductId,
        testColorId,
        testOrgId
      );
    });

    it('should delete orphaned color when option is enabled', () => {
      // Arrange
      mockProductRepository.removeProductColor.mockReturnValue(true);
      mockColorRepository.getUsageCounts.mockReturnValue(
        new Map([['Test Color', { count: 0, products: [] }]])
      );
      mockColorRepository.findById.mockReturnValue({
        ...mockColor,
        is_library: false,
      });
      mockColorRepository.softDelete.mockReturnValue(true);

      // Act
      const result = service.removeColorFromProduct(
        testProductId,
        testColorId,
        testOrgId,
        { deleteOrphanedColor: true }
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.removed).toBe(true);
      expect(result.data?.colorDeleted).toBe(true);
      expect(mockColorRepository.softDelete).toHaveBeenCalledWith(
        testColorId,
        testOrgId
      );
    });

    it('should not delete library colors even when orphaned', () => {
      // Arrange
      mockProductRepository.removeProductColor.mockReturnValue(true);
      mockColorRepository.getUsageCounts.mockReturnValue(
        new Map([['Test Color', { count: 0, products: [] }]])
      );
      mockColorRepository.findById.mockReturnValue({
        ...mockColor,
        is_library: true,
      });

      // Act
      const result = service.removeColorFromProduct(
        testProductId,
        testColorId,
        testOrgId,
        { deleteOrphanedColor: true }
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.removed).toBe(true);
      expect(result.data?.colorDeleted).toBe(false);
      expect(mockColorRepository.softDelete).not.toHaveBeenCalled();
    });

    it('should fail when removal is unsuccessful', () => {
      // Arrange
      mockProductRepository.removeProductColor.mockReturnValue(false);

      // Act
      const result = service.removeColorFromProduct(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.removed).toBe(false);
    });
  });

  describe('bulkAssignColors', () => {
    it('should successfully assign multiple colors to multiple products', () => {
      // Arrange
      const productIds = ['product-1', 'product-2'];
      const colorIds = ['color-1', 'color-2'];

      mockProductRepository.findById.mockReturnValue(mockProduct);
      mockColorRepository.findById.mockReturnValue(mockColor);
      mockProductRepository.getProductColors.mockReturnValue([]);
      mockProductRepository.addProductColor.mockReturnValue(true);

      // Act
      const result = service.bulkAssignColors(productIds, colorIds, testOrgId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.successfulAssignments).toBe(4); // 2 products × 2 colors
      expect(result.data?.failedAssignments).toBe(0);
      expect(result.data?.errors).toHaveLength(0);
    });

    it('should handle partial failures in bulk assignment', () => {
      // Arrange
      const productIds = ['product-1', 'product-2'];
      const colorIds = ['color-1', 'color-2'];

      // Mock findById to fail on the second product for color-2 only
      mockProductRepository.findById.mockImplementation(productId => {
        if (
          productId === 'product-1' &&
          mockProductRepository.findById.mock.calls.length === 2
        ) {
          return null; // Fail on second call for product-1
        }
        return mockProduct;
      });

      mockColorRepository.findById.mockReturnValue(mockColor);
      mockProductRepository.getProductColors.mockReturnValue([]);
      mockProductRepository.addProductColor.mockReturnValue(true);

      // Act
      const result = service.bulkAssignColors(productIds, colorIds, testOrgId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.successfulAssignments).toBe(3); // 3 successful out of 4 attempts
      expect(result.data?.failedAssignments).toBe(1);
      expect(result.data?.errors.length).toBeGreaterThan(0);
    });
  });

  describe('analyzeColorUsage', () => {
    it('should correctly analyze color usage', () => {
      // Arrange
      mockColorRepository.findById.mockReturnValue(mockColor);
      mockColorRepository.getUsageCounts.mockReturnValue(
        new Map([
          [
            'Test Color',
            { count: 3, products: ['Product A', 'Product B', 'Product C'] },
          ],
        ])
      );

      // Act
      const result = service.analyzeColorUsage(testColorId, testOrgId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        colorId: testColorId,
        colorName: 'Test Color',
        usageCount: 3,
        usedInProducts: ['Product A', 'Product B', 'Product C'],
        isOrphan: false,
        isLibraryColor: false,
      });
    });

    it('should identify orphaned colors', () => {
      // Arrange
      mockColorRepository.findById.mockReturnValue(mockColor);
      mockColorRepository.getUsageCounts.mockReturnValue(new Map()); // No usage data

      // Act
      const result = service.analyzeColorUsage(testColorId, testOrgId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.isOrphan).toBe(true);
      expect(result.data?.usageCount).toBe(0);
    });

    it('should fail when color does not exist', () => {
      // Arrange
      mockColorRepository.findById.mockReturnValue(null);

      // Act
      const result = service.analyzeColorUsage(testColorId, testOrgId);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Color color-123 not found');
    });
  });

  describe('getProductColors', () => {
    it('should return formatted color entries for a product', () => {
      // Arrange
      const productColors: ProductColorRow[] = [
        {
          product_id: 1,
          color_id: 1,
          color_external_id: testColorId,
          display_order: 1,
          organization_id: testOrgId,
        },
      ];

      mockProductRepository.getProductColors.mockReturnValue(productColors);
      mockColorRepository.findById.mockReturnValue(mockColor);

      // Act
      const result = service.getProductColors(testProductId, testOrgId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data?.[0]).toMatchObject({
        id: testColorId,
        code: 'TEST001',
        name: 'Test Color',
        hex: '#FF0000',
        source: 'PANTONE',
        organizationId: testOrgId,
        isLibrary: false,
      });
    });

    it('should return empty array for product with no colors', () => {
      // Arrange
      mockProductRepository.getProductColors.mockReturnValue([]);

      // Act
      const result = service.getProductColors(testProductId, testOrgId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(0);
    });
  });

  describe('validateRelationship', () => {
    it('should validate a valid relationship', () => {
      // Arrange
      mockProductRepository.findById.mockReturnValue(mockProduct);
      mockColorRepository.findById.mockReturnValue(mockColor);
      mockProductRepository.getProductColors.mockReturnValue([]);

      // Act
      const result = service.validateRelationship(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.valid).toBe(true);
      expect(result.data?.issues).toHaveLength(0);
    });

    it('should identify missing product', () => {
      // Arrange
      mockProductRepository.findById.mockReturnValue(null);
      mockColorRepository.findById.mockReturnValue(mockColor);

      // Act
      const result = service.validateRelationship(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.valid).toBe(false);
      expect(result.data?.issues).toContain('Product product-123 not found');
    });

    it('should identify existing relationship', () => {
      // Arrange
      mockProductRepository.findById.mockReturnValue(mockProduct);
      mockColorRepository.findById.mockReturnValue(mockColor);
      mockProductRepository.getProductColors.mockReturnValue([
        {
          product_id: 1,
          color_id: 1,
          color_external_id: testColorId,
          display_order: 1,
          organization_id: testOrgId,
        },
      ]);

      // Act
      const result = service.validateRelationship(
        testProductId,
        testColorId,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.valid).toBe(false);
      expect(result.data?.issues).toContain(
        'Relationship already exists between product and color'
      );
    });
  });

  describe('findSimilarProducts', () => {
    it('should find products with similar color palettes', () => {
      // Arrange
      const targetColors = [
        {
          id: 'color-1',
          code: 'C1',
          name: 'Color 1',
          hex: '#FF0000',
          organizationId: testOrgId,
          isLibrary: false,
          createdAt: '',
          updatedAt: '',
        },
        {
          id: 'color-2',
          code: 'C2',
          name: 'Color 2',
          hex: '#00FF00',
          organizationId: testOrgId,
          isLibrary: false,
          createdAt: '',
          updatedAt: '',
        },
      ];

      const allProductsWithColors = [
        {
          product_id: 'product-similar',
          product_name: 'Similar Product',
          product_description: null,
          product_created_at: '2024-01-01T00:00:00Z',
          product_updated_at: '2024-01-01T00:00:00Z',
          color_id: 'color-1',
          color_code: 'C1',
          color_name: 'Color 1',
          color_hex: '#FF0000',
          color_spaces: null,
          is_gradient: false,
          gradient_colors: null,
          color_notes: null,
          color_tags: null,
          is_library: false,
          color_created_at: '2024-01-01T00:00:00Z',
          color_updated_at: '2024-01-01T00:00:00Z',
          display_order: 1,
        },
      ];

      // Mock the getProductColors method for the target product
      vi.spyOn(service, 'getProductColors').mockReturnValue({
        success: true,
        data: targetColors,
        error: null,
      });

      mockProductRepository.getAllWithColors.mockReturnValue(
        allProductsWithColors
      );

      // Act
      const result = service.findSimilarProducts(testProductId, testOrgId, 0.3);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data?.[0]).toMatchObject({
        productId: 'product-similar',
        productName: 'Similar Product',
        sharedColors: ['color-1'],
        similarity: expect.any(Number),
      });
    });

    it('should return empty array when target product has no colors', () => {
      // Arrange
      vi.spyOn(service, 'getProductColors').mockReturnValue({
        success: true,
        data: [],
        error: null,
      });

      // Act
      const result = service.findSimilarProducts(testProductId, testOrgId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(0);
    });
  });

  describe('getRelationshipStatistics', () => {
    it('should calculate comprehensive relationship statistics', () => {
      // Arrange
      const mockColors2 = {
        ...mockColor,
        external_id: 'color-2',
        display_name: 'Test Color 2',
      };
      const mockProducts = [
        mockProduct,
        { ...mockProduct, external_id: 'product-2' },
      ];
      const mockColors = [mockColor, mockColors2];

      mockProductRepository.findAll.mockReturnValue(mockProducts);
      mockColorRepository.findAll.mockReturnValue(mockColors);
      mockProductRepository.getProductColors
        .mockReturnValueOnce([
          {
            product_id: 1,
            color_id: 1,
            color_external_id: 'color-1',
            display_order: 1,
            organization_id: testOrgId,
          },
        ])
        .mockReturnValueOnce([]);

      mockColorRepository.getUsageCounts.mockReturnValue(
        new Map([
          ['Test Color', { count: 1, products: ['Test Product'] }],
          ['Test Color 2', { count: 0, products: [] }],
        ])
      );

      // Act
      const result = service.getRelationshipStatistics(testOrgId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(
        expect.objectContaining({
          totalProducts: 2,
          totalColors: 2,
          totalRelationships: 1,
          productsWithoutColors: 1,
          orphanedColors: 1,
          averageColorsPerProduct: 0.5,
          mostUsedColors: expect.arrayContaining([
            expect.objectContaining({
              colorName: 'Test Color',
              usageCount: 1,
            }),
          ]),
        })
      );
    });
  });

  describe('reorderProductColors', () => {
    it('should successfully reorder product colors', () => {
      // Arrange
      const colorIds = ['color-1', 'color-2', 'color-3'];
      const currentColors: ProductColorRow[] = [
        {
          product_id: 1,
          color_id: 1,
          color_external_id: 'color-1',
          display_order: 1,
          organization_id: testOrgId,
        },
        {
          product_id: 1,
          color_id: 2,
          color_external_id: 'color-2',
          display_order: 2,
          organization_id: testOrgId,
        },
        {
          product_id: 1,
          color_id: 3,
          color_external_id: 'color-3',
          display_order: 3,
          organization_id: testOrgId,
        },
      ];

      mockProductRepository.getProductColors.mockReturnValue(currentColors);
      mockProductRepository.removeProductColor.mockReturnValue(true);
      mockProductRepository.addProductColor.mockReturnValue(true);

      // Act
      const result = service.reorderProductColors(
        testProductId,
        colorIds,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
      expect(mockProductRepository.removeProductColor).toHaveBeenCalledTimes(3);
      expect(mockProductRepository.addProductColor).toHaveBeenCalledTimes(3);
    });

    it('should fail when trying to reorder with invalid color IDs', () => {
      // Arrange
      const colorIds = ['color-1', 'color-invalid'];
      const currentColors: ProductColorRow[] = [
        {
          product_id: 1,
          color_id: 1,
          color_external_id: 'color-1',
          display_order: 1,
          organization_id: testOrgId,
        },
      ];

      mockProductRepository.getProductColors.mockReturnValue(currentColors);

      // Act
      const result = service.reorderProductColors(
        testProductId,
        colorIds,
        testOrgId
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain(
        'Color color-invalid is not associated with product'
      );
    });
  });

  // COMPLEX SCENARIO TESTS
  describe('Complex Relationship Scenarios', () => {
    describe('Duplicate Relationship Prevention', () => {
      it('should prevent duplicate relationships across concurrent operations', () => {
        // Arrange - Simulate concurrent attempts to add same relationship
        const concurrentAttempts = 5;
        mockProductRepository.findById.mockReturnValue(mockProduct);
        mockColorRepository.findById.mockReturnValue(mockColor);

        // First call returns empty (allowing addition), subsequent calls show relationship exists
        let callCount = 0;
        mockProductRepository.getProductColors.mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            return []; // First call - no existing relationship
          }
          return [
            {
              product_id: 1,
              color_id: 1,
              color_external_id: testColorId,
              display_order: 1,
              organization_id: testOrgId,
            },
          ];
        });

        mockProductRepository.addProductColor.mockReturnValue(true);

        // Act - Simulate concurrent calls
        const results = [];
        for (let i = 0; i < concurrentAttempts; i++) {
          results.push(
            service.addColorToProduct(testProductId, testColorId, testOrgId)
          );
        }

        // Assert - All should succeed but only first should actually add
        results.forEach(result => {
          expect(result.success).toBe(true);
          expect(result.data).toBe(true);
        });

        // Only first call should trigger actual addition
        expect(mockProductRepository.addProductColor).toHaveBeenCalledTimes(1);
      });

      it('should handle race conditions in bulk operations', () => {
        // Arrange - Setup for race condition testing
        const productIds = ['product-1', 'product-2'];
        const colorIds = ['color-1', 'color-2'];

        mockProductRepository.findById.mockReturnValue(mockProduct);
        mockColorRepository.findById.mockReturnValue(mockColor);

        // Simulate racing condition where second call sees relationship that first created
        let addCallCount = 0;
        mockProductRepository.getProductColors.mockImplementation(
          (productId, colorId) => {
            // Return existing relationship for second product on second color
            if (productId === 'product-2' && addCallCount > 1) {
              return [
                {
                  product_id: 2,
                  color_id: 1,
                  color_external_id: 'color-1',
                  display_order: 1,
                  organization_id: testOrgId,
                },
              ];
            }
            return [];
          }
        );

        mockProductRepository.addProductColor.mockImplementation(() => {
          addCallCount++;
          return true;
        });

        // Act
        const result = service.bulkAssignColors(
          productIds,
          colorIds,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.data?.successfulAssignments).toBeGreaterThan(0);
        expect(result.data?.failedAssignments).toBeGreaterThanOrEqual(0);
      });
    });

    describe('Orphaned Relationship Cleanup', () => {
      it('should handle cascade deletion of orphaned relationships', () => {
        // Arrange - Setup product with colors where some will become orphaned
        const productColors: ProductColorRow[] = [
          {
            product_id: 1,
            color_id: 1,
            color_external_id: 'color-1',
            display_order: 1,
            organization_id: testOrgId,
          },
          {
            product_id: 1,
            color_id: 2,
            color_external_id: 'color-2',
            display_order: 2,
            organization_id: testOrgId,
          },
        ];

        mockProductRepository.getProductColors.mockReturnValue(productColors);
        mockProductRepository.removeProductColor.mockReturnValue(true);

        // Mock color-1 as orphaned, color-2 as used elsewhere
        mockColorRepository.findById.mockImplementation(colorId => {
          if (colorId === 'color-1')
            return {
              ...mockColor,
              external_id: 'color-1',
              display_name: 'Color 1',
              is_library: false,
            };
          if (colorId === 'color-2')
            return {
              ...mockColor,
              external_id: 'color-2',
              display_name: 'Color 2',
              is_library: false,
            };
          return null;
        });

        mockColorRepository.getUsageCounts.mockReturnValue(
          new Map([
            ['Color 1', { count: 0, products: [] }], // Orphaned
            ['Color 2', { count: 1, products: ['Other Product'] }], // Still in use
          ])
        );

        mockColorRepository.softDelete.mockReturnValue(true);

        // Act - Remove all colors from product with orphan cleanup
        const results = [];
        for (const pc of productColors) {
          const result = service.removeColorFromProduct(
            testProductId,
            pc.color_external_id,
            testOrgId,
            { deleteOrphanedColor: true }
          );
          results.push(result);
        }

        // Assert
        results.forEach(result => expect(result.success).toBe(true));
        expect(mockColorRepository.softDelete).toHaveBeenCalledWith(
          'color-1',
          testOrgId
        ); // Orphaned color deleted
        expect(mockColorRepository.softDelete).toHaveBeenCalledTimes(1); // Only orphaned color should be deleted
      });

      it('should preserve library colors during orphan cleanup', () => {
        // Arrange
        mockProductRepository.removeProductColor.mockReturnValue(true);
        mockColorRepository.findById.mockReturnValue({
          ...mockColor,
          is_library: true,
        });
        mockColorRepository.getUsageCounts.mockReturnValue(
          new Map([['Test Color', { count: 0, products: [] }]])
        );

        // Act
        const result = service.removeColorFromProduct(
          testProductId,
          testColorId,
          testOrgId,
          { deleteOrphanedColor: true }
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.data?.colorDeleted).toBe(false);
        expect(mockColorRepository.softDelete).not.toHaveBeenCalled();
      });
    });

    describe('Cross-Organization Isolation', () => {
      it('should prevent cross-organization relationship creation', () => {
        // Arrange - Product in org A, Color in org B
        const orgA = 'org-a-123';
        const orgB = 'org-b-456';

        const productInOrgA = { ...mockProduct, organization_id: orgA };
        const colorInOrgB = { ...mockColor, organization_id: orgB };

        mockProductRepository.findById.mockReturnValue(productInOrgA);
        mockColorRepository.findById.mockReturnValue(colorInOrgB);
        mockProductRepository.getProductColors.mockReturnValue([]);

        // Act - Try to create relationship in org A
        const result = service.validateRelationship(
          testProductId,
          testColorId,
          orgA
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.data?.valid).toBe(false);
        expect(result.data?.issues).toContain(
          'Color belongs to different organization: org-b-456'
        );
      });

      it('should isolate relationship statistics by organization', () => {
        // Arrange - Multiple organizations with different data
        const orgA = 'org-a-123';
        const orgB = 'org-b-456';

        const productsOrgA = [
          { ...mockProduct, external_id: 'product-a1', organization_id: orgA },
          { ...mockProduct, external_id: 'product-a2', organization_id: orgA },
        ];

        const colorsOrgA = [
          {
            ...mockColor,
            external_id: 'color-a1',
            organization_id: orgA,
            display_name: 'Test Color',
            code: 'TEST001',
          },
          {
            ...mockColor,
            external_id: 'color-a2',
            organization_id: orgA,
            display_name: 'Test Color 2',
            code: 'TEST002',
          },
        ];

        mockProductRepository.findAll.mockReturnValue(productsOrgA);
        mockColorRepository.findAll.mockReturnValue(colorsOrgA);
        mockProductRepository.getProductColors
          .mockReturnValueOnce([
            {
              product_id: 1,
              color_id: 1,
              color_external_id: 'color-a1',
              display_order: 1,
              organization_id: orgA,
            },
          ])
          .mockReturnValueOnce([]);

        mockColorRepository.getUsageCounts.mockReturnValue(
          new Map([
            ['Test Color', { count: 1, products: ['Product A1'] }],
            // Test Color 2 is not in the usage map, making it orphaned
          ])
        );

        // Act
        const result = service.getRelationshipStatistics(orgA);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data?.totalProducts).toBe(2);
        expect(result.data?.totalColors).toBe(2);
        expect(result.data?.totalRelationships).toBe(1);
        expect(result.data?.orphanedColors).toBe(1);
      });
    });

    describe('Bulk Relationship Operations', () => {
      it('should handle large-scale bulk operations efficiently', () => {
        // Arrange - Large dataset
        const productIds = Array.from(
          { length: 100 },
          (_, i) => `product-${i}`
        );
        const colorIds = Array.from({ length: 50 }, (_, i) => `color-${i}`);

        mockProductRepository.findById.mockReturnValue(mockProduct);
        mockColorRepository.findById.mockReturnValue(mockColor);
        mockProductRepository.getProductColors.mockReturnValue([]);
        mockProductRepository.addProductColor.mockReturnValue(true);

        // Act
        const result = service.bulkAssignColors(
          productIds,
          colorIds,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.data?.successfulAssignments).toBe(5000); // 100 * 50
        expect(result.data?.failedAssignments).toBe(0);
        expect(result.data?.errors).toHaveLength(0);
      });

      it('should handle partial failures in large bulk operations', () => {
        // Arrange
        const productIds = Array.from({ length: 10 }, (_, i) => `product-${i}`);
        const colorIds = Array.from({ length: 10 }, (_, i) => `color-${i}`);

        // Mock failures for specific combinations
        mockProductRepository.findById.mockImplementation(productId => {
          if (productId === 'product-5') return null; // This product doesn't exist
          return mockProduct;
        });

        mockColorRepository.findById.mockImplementation(colorId => {
          if (colorId === 'color-3') return null; // This color doesn't exist
          return mockColor;
        });

        mockProductRepository.getProductColors.mockReturnValue([]);
        mockProductRepository.addProductColor.mockReturnValue(true);

        // Act
        const result = service.bulkAssignColors(
          productIds,
          colorIds,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.data?.successfulAssignments).toBe(81); // 9*9 successful (excluding failed product/color combos)
        expect(result.data?.failedAssignments).toBe(19); // 10 failures for product-5, 9 failures for color-3
        expect(result.data?.errors.length).toBeGreaterThan(0);
      });

      it('should limit error array size in bulk operations', () => {
        // Arrange - Setup to generate many errors
        const productIds = Array.from({ length: 20 }, (_, i) => `product-${i}`);
        const colorIds = Array.from({ length: 20 }, (_, i) => `color-${i}`);

        mockProductRepository.findById.mockReturnValue(null); // All products fail
        mockColorRepository.findById.mockReturnValue(mockColor);

        // Act
        const result = service.bulkAssignColors(
          productIds,
          colorIds,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(true); // Service succeeds but with failures
        expect(result.data?.successfulAssignments).toBe(0);
        expect(result.data?.failedAssignments).toBe(400); // 20 * 20
        expect(result.data?.errors).toHaveLength(10); // Limited to 10 errors
      });
    });

    describe('Display Order Management', () => {
      it('should maintain display order consistency during reordering', () => {
        // Arrange
        const colorIds = ['color-3', 'color-1', 'color-2']; // Reordered sequence
        const currentColors: ProductColorRow[] = [
          {
            product_id: 1,
            color_id: 1,
            color_external_id: 'color-1',
            display_order: 1,
            organization_id: testOrgId,
          },
          {
            product_id: 1,
            color_id: 2,
            color_external_id: 'color-2',
            display_order: 2,
            organization_id: testOrgId,
          },
          {
            product_id: 1,
            color_id: 3,
            color_external_id: 'color-3',
            display_order: 3,
            organization_id: testOrgId,
          },
        ];

        mockProductRepository.getProductColors.mockReturnValue(currentColors);
        mockProductRepository.removeProductColor.mockReturnValue(true);
        mockProductRepository.addProductColor.mockReturnValue(true);

        // Act
        const result = service.reorderProductColors(
          testProductId,
          colorIds,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(true);
        expect(mockProductRepository.removeProductColor).toHaveBeenCalledTimes(
          3
        );
        expect(mockProductRepository.addProductColor).toHaveBeenCalledTimes(3);

        // Verify colors are added in the correct order
        expect(mockProductRepository.addProductColor).toHaveBeenNthCalledWith(
          1,
          testProductId,
          'color-3',
          testOrgId
        );
        expect(mockProductRepository.addProductColor).toHaveBeenNthCalledWith(
          2,
          testProductId,
          'color-1',
          testOrgId
        );
        expect(mockProductRepository.addProductColor).toHaveBeenNthCalledWith(
          3,
          testProductId,
          'color-2',
          testOrgId
        );
      });

      it('should handle partial failures during reordering', () => {
        // Arrange
        const colorIds = ['color-1', 'color-2', 'color-3'];
        const currentColors: ProductColorRow[] = [
          {
            product_id: 1,
            color_id: 1,
            color_external_id: 'color-1',
            display_order: 1,
            organization_id: testOrgId,
          },
          {
            product_id: 1,
            color_id: 2,
            color_external_id: 'color-2',
            display_order: 2,
            organization_id: testOrgId,
          },
          {
            product_id: 1,
            color_id: 3,
            color_external_id: 'color-3',
            display_order: 3,
            organization_id: testOrgId,
          },
        ];

        mockProductRepository.getProductColors.mockReturnValue(currentColors);
        mockProductRepository.removeProductColor.mockReturnValue(true);

        // Mock failure on re-adding the second color
        mockProductRepository.addProductColor.mockImplementation(
          (productId, colorId) => {
            return colorId !== 'color-2'; // Fail on color-2
          }
        );

        // Act
        const result = service.reorderProductColors(
          testProductId,
          colorIds,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.data).toBe(false); // Overall operation failed due to partial failure
      });
    });

    describe('Relationship Cascading Deletes', () => {
      it('should handle cascading deletes when product is deleted', () => {
        // Arrange - Product with multiple colors
        const productColors: ProductColorRow[] = [
          {
            product_id: 1,
            color_id: 1,
            color_external_id: 'color-1',
            display_order: 1,
            organization_id: testOrgId,
          },
          {
            product_id: 1,
            color_id: 2,
            color_external_id: 'color-2',
            display_order: 2,
            organization_id: testOrgId,
          },
          {
            product_id: 1,
            color_id: 3,
            color_external_id: 'color-3',
            display_order: 3,
            organization_id: testOrgId,
          },
        ];

        mockProductRepository.getProductColors.mockReturnValue(productColors);
        mockProductRepository.removeProductColor.mockReturnValue(true);

        // Mock colors with different usage patterns
        mockColorRepository.findById.mockImplementation(colorId => {
          const baseColor = { ...mockColor, is_library: false };
          if (colorId === 'color-1')
            return {
              ...baseColor,
              external_id: 'color-1',
              display_name: 'Color 1',
            };
          if (colorId === 'color-2')
            return {
              ...baseColor,
              external_id: 'color-2',
              display_name: 'Color 2',
            };
          if (colorId === 'color-3')
            return {
              ...baseColor,
              external_id: 'color-3',
              display_name: 'Color 3',
            };
          return null;
        });

        mockColorRepository.getUsageCounts.mockReturnValue(
          new Map([
            ['Color 1', { count: 0, products: [] }], // Will be orphaned
            ['Color 2', { count: 1, products: ['Other Product'] }], // Still in use
            ['Color 3', { count: 0, products: [] }], // Will be orphaned
          ])
        );

        mockColorRepository.softDelete.mockReturnValue(true);

        // Act - Simulate deleting all relationships (as would happen in product deletion)
        const results = [];
        for (const pc of productColors) {
          const result = service.removeColorFromProduct(
            testProductId,
            pc.color_external_id,
            testOrgId,
            { deleteOrphanedColor: true }
          );
          results.push(result);
        }

        // Assert
        results.forEach(result => expect(result.success).toBe(true));
        expect(mockColorRepository.softDelete).toHaveBeenCalledWith(
          'color-1',
          testOrgId
        );
        expect(mockColorRepository.softDelete).toHaveBeenCalledWith(
          'color-3',
          testOrgId
        );
        expect(mockColorRepository.softDelete).toHaveBeenCalledTimes(2); // Two orphaned colors deleted
      });
    });

    describe('Performance with Large Relationship Sets', () => {
      it('should handle products with many colors efficiently', () => {
        // Arrange - Product with 1000 colors
        const manyColors = Array.from({ length: 1000 }, (_, i) => ({
          product_id: 1,
          color_id: i + 1,
          color_external_id: `color-${i + 1}`,
          display_order: i + 1,
          organization_id: testOrgId,
        }));

        mockProductRepository.getProductColors.mockReturnValue(manyColors);
        mockColorRepository.findById.mockReturnValue(mockColor);

        // Act
        const result = service.getProductColors(testProductId, testOrgId);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data).toHaveLength(1000);
        expect(mockColorRepository.findById).toHaveBeenCalledTimes(1000);
      });

      it('should efficiently calculate similarity with large color sets', () => {
        // Arrange - Setup for similarity calculation with many products and colors
        const targetColors = Array.from({ length: 100 }, (_, i) => ({
          id: `color-${i}`,
          code: `C${i}`,
          name: `Color ${i}`,
          hex: '#FF0000',
          organizationId: testOrgId,
          isLibrary: false,
          createdAt: '',
          updatedAt: '',
        }));

        const manyProductsWithColors = Array.from({ length: 500 }, (_, i) => ({
          product_id: `product-${i}`,
          product_name: `Product ${i}`,
          product_description: null,
          product_created_at: '2024-01-01T00:00:00Z',
          product_updated_at: '2024-01-01T00:00:00Z',
          color_id: `color-${i % 100}`, // Each product gets one color from the set
          color_code: `C${i % 100}`,
          color_name: `Color ${i % 100}`,
          color_hex: '#FF0000',
          color_spaces: null,
          is_gradient: false,
          gradient_colors: null,
          color_notes: null,
          color_tags: null,
          is_library: false,
          color_created_at: '2024-01-01T00:00:00Z',
          color_updated_at: '2024-01-01T00:00:00Z',
          display_order: 1,
        }));

        vi.spyOn(service, 'getProductColors').mockReturnValue({
          success: true,
          data: targetColors,
          error: null,
        });

        mockProductRepository.getAllWithColors.mockReturnValue(
          manyProductsWithColors
        );

        // Act
        const result = service.findSimilarProducts(
          testProductId,
          testOrgId,
          0.1
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.data).toHaveLength(0); // No products should be found as target product has no valid color overlap
      });
    });

    describe('Concurrent Relationship Modifications', () => {
      it('should handle concurrent modifications gracefully', () => {
        // Arrange - Simulate concurrent operations on the same relationship
        const concurrentOperations = [
          () =>
            service.addColorToProduct(testProductId, testColorId, testOrgId),
          () =>
            service.removeColorFromProduct(
              testProductId,
              testColorId,
              testOrgId
            ),
          () =>
            service.validateRelationship(testProductId, testColorId, testOrgId),
        ];

        // Setup mocks to simulate changing state
        let relationshipExists = false;
        mockProductRepository.findById.mockReturnValue(mockProduct);
        mockColorRepository.findById.mockReturnValue(mockColor);

        mockProductRepository.getProductColors.mockImplementation(() => {
          return relationshipExists
            ? [
                {
                  product_id: 1,
                  color_id: 1,
                  color_external_id: testColorId,
                  display_order: 1,
                  organization_id: testOrgId,
                },
              ]
            : [];
        });

        mockProductRepository.addProductColor.mockImplementation(() => {
          relationshipExists = true;
          return true;
        });

        mockProductRepository.removeProductColor.mockImplementation(() => {
          relationshipExists = false;
          return true;
        });

        // Act - Execute concurrent operations
        const results = concurrentOperations.map(op => op());

        // Assert - All operations should complete successfully
        results.forEach(result => {
          expect(result.success).toBe(true);
        });
      });

      it('should maintain data consistency during concurrent bulk operations', () => {
        // Arrange - Multiple bulk operations running concurrently
        const productIds1 = ['product-1', 'product-2'];
        const colorIds1 = ['color-1', 'color-2'];
        const productIds2 = ['product-3', 'product-4'];
        const colorIds2 = ['color-3', 'color-4'];

        mockProductRepository.findById.mockReturnValue(mockProduct);
        mockColorRepository.findById.mockReturnValue(mockColor);
        mockProductRepository.getProductColors.mockReturnValue([]);
        mockProductRepository.addProductColor.mockReturnValue(true);

        // Act - Execute concurrent bulk operations
        const [result1, result2] = [
          service.bulkAssignColors(productIds1, colorIds1, testOrgId),
          service.bulkAssignColors(productIds2, colorIds2, testOrgId),
        ];

        // Assert
        expect(result1.success).toBe(true);
        expect(result2.success).toBe(true);
        expect(result1.data?.successfulAssignments).toBe(4);
        expect(result2.data?.successfulAssignments).toBe(4);
      });
    });

    describe('Error Handling and Recovery', () => {
      it('should handle database errors gracefully', () => {
        // Arrange
        mockProductRepository.findById.mockImplementation(() => {
          throw new Error('Database connection failed');
        });

        // Act
        const result = service.addColorToProduct(
          testProductId,
          testColorId,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('Database connection failed');
      });

      it('should handle malformed data gracefully', () => {
        // Arrange
        mockProductRepository.findById.mockReturnValue(mockProduct);
        mockColorRepository.findById.mockReturnValue(mockColor);
        mockProductRepository.getProductColors.mockReturnValue([]);
        mockProductRepository.addProductColor.mockImplementation(() => {
          throw new Error('Invalid data format');
        });

        // Act
        const result = service.addColorToProduct(
          testProductId,
          testColorId,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(false);
        expect(result.error).toContain('Invalid data format');
      });

      it('should recover from partial failures in reordering', () => {
        // Arrange
        const colorIds = ['color-1', 'color-2'];
        const currentColors: ProductColorRow[] = [
          {
            product_id: 1,
            color_id: 1,
            color_external_id: 'color-1',
            display_order: 1,
            organization_id: testOrgId,
          },
          {
            product_id: 1,
            color_id: 2,
            color_external_id: 'color-2',
            display_order: 2,
            organization_id: testOrgId,
          },
        ];

        mockProductRepository.getProductColors.mockReturnValue(currentColors);

        // Mock successful removal but failed re-addition
        mockProductRepository.removeProductColor.mockReturnValue(true);
        mockProductRepository.addProductColor.mockReturnValue(false);

        // Act
        const result = service.reorderProductColors(
          testProductId,
          colorIds,
          testOrgId
        );

        // Assert
        expect(result.success).toBe(true);
        expect(result.data).toBe(false); // Operation failed but didn't throw
      });
    });

    describe('Memory and Resource Management', () => {
      it('should handle large result sets without memory issues', () => {
        // Arrange - Large dataset for statistics
        const manyProducts = Array.from({ length: 10000 }, (_, i) => ({
          ...mockProduct,
          external_id: `product-${i}`,
          name: `Product ${i}`,
        }));

        const manyColors = Array.from({ length: 5000 }, (_, i) => ({
          ...mockColor,
          external_id: `color-${i}`,
          display_name: `Color ${i}`,
        }));

        mockProductRepository.findAll.mockReturnValue(manyProducts);
        mockColorRepository.findAll.mockReturnValue(manyColors);

        // Mock getProductColors to return empty for most, some relationships for others
        mockProductRepository.getProductColors.mockImplementation(productId => {
          const productNum = parseInt(productId.split('-')[1]);
          if (productNum % 100 === 0) {
            // Every 100th product has a color
            return [
              {
                product_id: productNum,
                color_id: 1,
                color_external_id: 'color-1',
                display_order: 1,
                organization_id: testOrgId,
              },
            ];
          }
          return [];
        });

        const usageMap = new Map();
        for (let i = 0; i < 1000; i++) {
          usageMap.set(`Color ${i}`, {
            count: Math.floor(Math.random() * 10),
            products: [],
          });
        }
        mockColorRepository.getUsageCounts.mockReturnValue(usageMap);

        // Act
        const result = service.getRelationshipStatistics(testOrgId);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data?.totalProducts).toBe(10000);
        expect(result.data?.totalColors).toBe(5000);
        expect(result.data?.mostUsedColors).toHaveLength(10); // Should be limited to top 10
      });
    });
  });
});
