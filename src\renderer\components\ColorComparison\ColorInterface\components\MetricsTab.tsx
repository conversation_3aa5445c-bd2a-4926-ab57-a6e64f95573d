/**
 * Metrics Tab Component
 * Displays comprehensive color metrics and values
 */

import { memo } from 'react';
import { BarChart3 } from 'lucide-react';
import type { MetricsTabProps } from '../types';

export const MetricsTab = memo<MetricsTabProps>(({ selectedColor, metrics }) => {
  if (!selectedColor || !metrics) {
    return (
      <div className="p-4 text-center text-ui-text-muted">
        <BarChart3 className="mx-auto mb-2 h-8 w-8 opacity-50" />
        <p>Select a color to view metrics</p>
      </div>
    );
  }

  const { rgb, hsl, cmyk, lab } = metrics;

  return (
    <div className="p-4">
      <div 
        className="rounded-md p-3 mb-3"
        style={{
          backgroundColor: 'var(--color-ui-background-tertiary)',
          borderRadius: 'var(--radius-md)'
        }}
      >
        <h4 
          className="text-sm font-medium mb-3"
          style={{
            fontSize: 'var(--font-size-sm)',
            fontWeight: 'var(--font-weight-medium)',
            color: 'var(--color-ui-foreground-primary)'
          }}
        >
          Color Values
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* RGB Values */}
          <div className="space-y-3">
            <div>
              <div 
                className="text-xs font-medium mb-1"
                style={{
                  fontSize: 'var(--font-size-xs)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-ui-foreground-tertiary)'
                }}
              >
                RGB
              </div>
              <div className="grid grid-cols-3 gap-2">
                <div className="flex flex-col items-center">
                  <div 
                    className="h-4 w-full"
                    style={{ 
                      backgroundColor: `rgb(${rgb.r},0,0)`,
                      borderRadius: 'var(--radius-DEFAULT)'
                    }}
                  />
                  <div 
                    className="text-xs mt-1 font-medium"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      fontWeight: 'var(--font-weight-medium)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    {Math.round(rgb.r)}
                  </div>
                </div>
                <div className="flex flex-col items-center">
                  <div 
                    className="h-4 w-full"
                    style={{ 
                      backgroundColor: `rgb(0,${rgb.g},0)`,
                      borderRadius: 'var(--radius-DEFAULT)'
                    }}
                  />
                  <div 
                    className="text-xs mt-1 font-medium"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      fontWeight: 'var(--font-weight-medium)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    {Math.round(rgb.g)}
                  </div>
                </div>
                <div className="flex flex-col items-center">
                  <div 
                    className="h-4 w-full"
                    style={{ 
                      backgroundColor: `rgb(0,0,${rgb.b})`,
                      borderRadius: 'var(--radius-DEFAULT)'
                    }}
                  />
                  <div 
                    className="text-xs mt-1 font-medium"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      fontWeight: 'var(--font-weight-medium)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    {Math.round(rgb.b)}
                  </div>
                </div>
              </div>
            </div>

            {/* HSL Values */}
            <div>
              <div 
                className="text-xs font-medium mb-1"
                style={{
                  fontSize: 'var(--font-size-xs)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-ui-foreground-tertiary)'
                }}
              >
                HSL
              </div>
              <div className="space-y-1">
                <div 
                  className="flex justify-between text-xs"
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)'
                  }}
                >
                  <span>Hue</span>
                  <span 
                    className="font-mono"
                    style={{ fontFamily: 'var(--font-family-mono)' }}
                  >
                    {hsl.h}°
                  </span>
                </div>
                <div 
                  className="w-full h-2 bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-cyan-500 via-blue-500 via-violet-500 to-red-500"
                  style={{ borderRadius: 'var(--radius-DEFAULT)' }}
                />
                
                <div 
                  className="flex justify-between text-xs mt-2"
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)'
                  }}
                >
                  <span>Saturation</span>
                  <span 
                    className="font-mono"
                    style={{ fontFamily: 'var(--font-family-mono)' }}
                  >
                    {hsl.s}%
                  </span>
                </div>
                <div 
                  className="w-full h-2"
                  style={{ 
                    borderRadius: 'var(--radius-DEFAULT)',
                    background: `linear-gradient(to right, var(--color-ui-foreground-tertiary), ${selectedColor.hex})`
                  }}
                />
                
                <div 
                  className="flex justify-between text-xs mt-2"
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)'
                  }}
                >
                  <span>Lightness</span>
                  <span 
                    className="font-mono"
                    style={{ fontFamily: 'var(--font-family-mono)' }}
                  >
                    {hsl.l}%
                  </span>
                </div>
                <div 
                  className="w-full h-2"
                  style={{ 
                    borderRadius: 'var(--radius-DEFAULT)',
                    background: `linear-gradient(to right, black, ${selectedColor.hex}, white)`
                  }}
                />
              </div>
            </div>
          </div>

          {/* CMYK Values */}
          <div className="space-y-3">
            <div>
              <div 
                className="text-xs font-medium mb-1"
                style={{
                  fontSize: 'var(--font-size-xs)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-ui-foreground-tertiary)'
                }}
              >
                CMYK
              </div>
              <div className="space-y-2">
                <div>
                  <div 
                    className="flex justify-between text-xs mb-1"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    <span>Cyan</span>
                    <span 
                      className="font-mono"
                      style={{ fontFamily: 'var(--font-family-mono)' }}
                    >
                      {cmyk.c}%
                    </span>
                  </div>
                  <div 
                    className="w-full h-1.5"
                    style={{
                      backgroundColor: 'var(--progress-bg)',
                      borderRadius: 'var(--radius-full)'
                    }}
                  >
                    <div 
                      className="h-1.5 transition-standard"
                      style={{ 
                        width: `${cmyk.c}%`,
                        backgroundColor: 'var(--progress-cyan)',
                        borderRadius: 'var(--radius-full)'
                      }}
                    />
                  </div>
                </div>
                
                <div>
                  <div 
                    className="flex justify-between text-xs mb-1"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    <span>Magenta</span>
                    <span 
                      className="font-mono"
                      style={{ fontFamily: 'var(--font-family-mono)' }}
                    >
                      {cmyk.m}%
                    </span>
                  </div>
                  <div 
                    className="w-full h-1.5"
                    style={{
                      backgroundColor: 'var(--progress-bg)',
                      borderRadius: 'var(--radius-full)'
                    }}
                  >
                    <div 
                      className="h-1.5 transition-standard"
                      style={{ 
                        width: `${cmyk.m}%`,
                        backgroundColor: 'var(--progress-magenta)',
                        borderRadius: 'var(--radius-full)'
                      }}
                    />
                  </div>
                </div>
                
                <div>
                  <div 
                    className="flex justify-between text-xs mb-1"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    <span>Yellow</span>
                    <span 
                      className="font-mono"
                      style={{ fontFamily: 'var(--font-family-mono)' }}
                    >
                      {cmyk.y}%
                    </span>
                  </div>
                  <div 
                    className="w-full h-1.5"
                    style={{
                      backgroundColor: 'var(--progress-bg)',
                      borderRadius: 'var(--radius-full)'
                    }}
                  >
                    <div 
                      className="h-1.5 transition-standard"
                      style={{ 
                        width: `${cmyk.y}%`,
                        backgroundColor: 'var(--progress-yellow)',
                        borderRadius: 'var(--radius-full)'
                      }}
                    />
                  </div>
                </div>
                
                <div>
                  <div 
                    className="flex justify-between text-xs mb-1"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    <span>Black</span>
                    <span 
                      className="font-mono"
                      style={{ fontFamily: 'var(--font-family-mono)' }}
                    >
                      {cmyk.k}%
                    </span>
                  </div>
                  <div 
                    className="w-full h-1.5"
                    style={{
                      backgroundColor: 'var(--progress-bg)',
                      borderRadius: 'var(--radius-full)'
                    }}
                  >
                    <div 
                      className="h-1.5 transition-standard"
                      style={{ 
                        width: `${cmyk.k}%`,
                        backgroundColor: 'var(--progress-black)',
                        borderRadius: 'var(--radius-full)'
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* LAB Values */}
          <div className="space-y-3">
            {lab && (
              <div>
                <div 
                  className="text-xs font-medium mb-1"
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    fontWeight: 'var(--font-weight-medium)',
                    color: 'var(--color-ui-foreground-tertiary)'
                  }}
                >
                  LAB
                </div>
                <div className="space-y-2">
                  <div 
                    className="flex justify-between text-xs"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    <span>Lightness (L*)</span>
                    <span 
                      className="font-mono"
                      style={{ fontFamily: 'var(--font-family-mono)' }}
                    >
                      {lab.l.toFixed(1)}
                    </span>
                  </div>
                  <div 
                    className="flex justify-between text-xs"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    <span>Green-Red (a*)</span>
                    <span 
                      className="font-mono"
                      style={{ fontFamily: 'var(--font-family-mono)' }}
                    >
                      {lab.a.toFixed(1)}
                    </span>
                  </div>
                  <div 
                    className="flex justify-between text-xs"
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      color: 'var(--color-ui-foreground-primary)'
                    }}
                  >
                    <span>Blue-Yellow (b*)</span>
                    <span 
                      className="font-mono"
                      style={{ fontFamily: 'var(--font-family-mono)' }}
                    >
                      {lab.b.toFixed(1)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Additional Info */}
            <div 
              className="mt-4 pt-4"
              style={{ borderTop: `1px solid var(--color-ui-border-light)` }}
            >
              <div className="text-xs space-y-1">
                <div 
                  className="flex justify-between"
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)'
                  }}
                >
                  <span style={{ color: 'var(--color-ui-foreground-tertiary)' }}>
                    Hex
                  </span>
                  <span 
                    className="font-mono"
                    style={{ fontFamily: 'var(--font-family-mono)' }}
                  >
                    {selectedColor.hex}
                  </span>
                </div>
                <div 
                  className="flex justify-between"
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)'
                  }}
                >
                  <span style={{ color: 'var(--color-ui-foreground-tertiary)' }}>
                    Pantone
                  </span>
                  <span 
                    className="font-medium"
                    style={{ fontWeight: 'var(--font-weight-medium)' }}
                  >
                    {selectedColor.pantone}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

MetricsTab.displayName = 'MetricsTab';