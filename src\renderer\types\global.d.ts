import { SharedFolderAPI } from '../../shared/types/shared-folder';

declare global {
  interface Window {
    // Add sharedFolder API to window interface
    sharedFolder: SharedFolderAPI;
    
    // Add organization API
    organizationAPI: {
      createOrganization: (data: { name: string; slug?: string }) => Promise<any>;
      getOrganizations: () => Promise<any>;
      getCurrentOrganization: () => Promise<any>;
      setCurrentOrganization: (organizationId: string) => Promise<any>;
      getMembers: (organizationId: string) => Promise<any>;
      inviteMember: (data: { organizationId: string; email: string; role?: string }) => Promise<any>;
      removeMember: (data: { organizationId: string; userId: string }) => Promise<any>;
      updateMemberRole: (data: { organizationId: string; userId: string; role: string }) => Promise<any>;
      updateSettings: (data: { organizationId: string; settings: any }) => Promise<any>;
      deleteOrganization: (organizationId: string, forceCascade?: boolean) => Promise<any>;
      acceptInvitation: (token: string) => Promise<any>;
      getPendingInvitations: (organizationId: string) => Promise<any>;
      revokeInvitation: (data: { organizationId: string; invitationId: string }) => Promise<any>;
      onInvitationReceived: (callback: (token: string) => void) => () => void;
    };
    
    // Keep existing interfaces
    // ... existing interfaces ...
  }
} 