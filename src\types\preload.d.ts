/**
 * @file preload.d.ts
 * @description Global type definitions for all APIs exposed by the preload script
 */

import type { 
  ColorEntry, 
  NewColorEntry, 
  UpdateColorEntry, 
  ColorWithUsageResponse,
  ColorApiResponse 
} from '../shared/types/color.types';
import type { 
  SyncConfig, 
  SyncConflict
} from '../shared/types/sync.types';
import type { Organization } from '../shared/types/organization.types';
import type { SharedFolderFile } from '../shared/types/shared-folder';

// ===== WINDOW API INTERFACES =====

interface ElectronAPI {
  process: {
    versions: {
      node: string;
      chrome: string;
      electron: string;
    };
    platform: string;
    arch: string;
  };
  window: {
    minimize: () => void;
    maximize: () => void;
    unmaximize: () => void;
    close: () => void;
    isMaximized: () => Promise<boolean>;
    toggleDevTools: () => Promise<void>;
  };
  ipcRenderer: {
    invoke: (channel: string, ...args: any[]) => Promise<any>;
  };
}

interface IpcAPI {
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (...args: any[]) => void) => () => void;
}

interface AppAPI {
  zoom: {
    zoomIn: () => void;
    zoomOut: () => void;
    resetZoom: () => void;
    getZoomFactor: () => Promise<number>;
  };
  shortcuts: {
    zoomIn: string;
    zoomOut: string;
    resetZoom: string;
  };
}

interface SetupAPI {
  getInitialConfigStatus: () => Promise<any>;
  selectSharedFolder: () => Promise<any>;
  saveStorageConfig: (config: { 
    mode: 'standalone' | 'collaboration' | 'server-sync'; 
    path?: string; 
  }) => Promise<any>;
  onShowSetupModal: (callback: () => void) => () => void;
  sendSetupComplete: () => void;
}

interface ApiAPI {
  openProductDatasheet: (product: string) => Promise<any>;
  openDevTools: () => Promise<any>;
  runPerformanceTest: () => Promise<any>;
  checkDatabaseIntegrity: (organizationId?: string) => Promise<any>;
  getDatabaseStats: () => Promise<any>;
  optimizeDatabase: () => Promise<any>;
  vacuumDatabase: () => Promise<any>;
  getApplicationLogs: () => Promise<any>;
  clearApplicationLogs: () => Promise<any>;
  exportApplicationLogs: () => Promise<any>;
  resetApplicationData: () => Promise<any>;
  selectLogoFile: () => Promise<any>;
  getAppInfo: () => Promise<any>;
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: Function) => void;
  off: (channel: string, callback: Function) => void;
}

interface LicenseAPI {
  getStatus: () => Promise<any>;
  activateDevice: () => Promise<any>;
  deactivateDevice: () => Promise<any>;
  checkAuthorization: () => Promise<any>;
  showActivationDialog: () => Promise<any>;
  onShowActivationDialog: (callback: (data: any) => void) => () => void;
  onShowDialog: (callback: (data: any) => void) => () => void;
  activateLicense: () => Promise<any>;
  getDeviceId: () => Promise<string>;
  getDeviceLicenseKey: () => Promise<string>;
}

interface SharedFolderAPI {
  getPath: () => Promise<string>;
  ensureExists: () => Promise<any>;
  readFile: (fileName: string) => Promise<string>;
  writeFile: (fileName: string, content: string) => Promise<any>;
  listFiles: () => Promise<SharedFolderFile[]>;
}

interface MonitoringAPI {
  trackError: (error: Error | { 
    message: string; 
    stack?: string; 
    componentStack?: string; 
    location?: string; 
  }) => Promise<any>;
}

interface AutoUpdateAPI {
  onStatusChange: (callback: (status: {
    status: 'checking' | 'available' | 'not-available' | 'downloading' | 'downloaded' | 'error';
    data?: any;
    error?: string;
  }) => void) => () => void;
  checkForUpdates: () => Promise<any>;
  downloadUpdate: () => Promise<any>;
  installUpdate: () => Promise<any>;
}

interface ProductAPI {
  getAll: () => Promise<any>;
  getAllWithColors: () => Promise<any>;
  addColor: (productId: string, colorId: string) => Promise<any>;
  addLibraryColor: (productId: string, libraryColor: any, customName?: string) => Promise<any>;
  removeColor: (productId: string, colorId: string) => Promise<any>;
  add: (data: { name: string; metadata?: any }) => Promise<any>;
  delete: (productId: string) => Promise<any>;
  openDatasheet: (product: string) => Promise<any>;
  deduplicate: () => Promise<any>;
}

interface DatasheetAPI {
  getByProduct: (data: { productId: string }) => Promise<any>;
  addWebLink: (data: { productId: string; url: string; displayName: string }) => Promise<any>;
  remove: (data: { datasheetId: string }) => Promise<any>;
  open: (data: { datasheetId: string }) => Promise<any>;
  openAll: (data: { productId: string }) => Promise<any>;
  migrate: () => Promise<any>;
}

interface ColorAPI {
  getAll: () => Promise<ColorApiResponse<ColorEntry[]>>;
  getUsageCounts: () => Promise<ColorApiResponse<Record<string, { count: number; products: string[] }>>>;
  getById: (id: string) => Promise<ColorApiResponse<ColorEntry>>;
  add: (color: NewColorEntry) => Promise<ColorApiResponse<ColorEntry>>;
  update: (id: string, updates: UpdateColorEntry) => Promise<ColorApiResponse<ColorEntry>>;
  delete: (id: string) => Promise<ColorApiResponse<boolean>>;
  clearAll: () => Promise<ColorApiResponse<boolean>>;
  importColors: (mergeMode?: 'replace' | 'merge', filePath?: string, format?: 'json' | 'csv') => Promise<ColorApiResponse<{ added: number; errors: string[] }>>;
  exportColors: (filePath?: string, format?: 'json' | 'csv') => Promise<ColorApiResponse<boolean>>;
  normalizePantoneCodes: () => Promise<ColorApiResponse<{ success: boolean; message: string }>>;
  getAllWithUsage: () => Promise<ColorApiResponse<ColorWithUsageResponse>>;
  getProductsByColorName: () => Promise<ColorApiResponse<Record<string, string[]>>>;
  clearFrontendState: () => Promise<ColorApiResponse<boolean>>;
  adminClearAll: (options?: { hardDelete?: boolean }) => Promise<ColorApiResponse<boolean>>;
}

interface SyncAPI {
  getConfig: () => Promise<{ success: boolean; data?: SyncConfig; error?: string }>;
  hasUnsyncedLocalChanges: () => Promise<{ success: boolean; count: number; hasChanges: boolean; timestamp: number }>;
  updateConfig: (config: Partial<SyncConfig>) => Promise<{ success: boolean; error?: string }>;
  getState: () => Promise<any>;
  getAuthState: () => Promise<{
    isAuthenticated: boolean;
    user?: any;
    session?: any;
    status?: 'needs_organization_setup' | 'needs_organization_selection' | 'authenticated';
    organizations?: Organization[];
  }>;
  login: () => Promise<{
    success: boolean;
    error?: string;
    requiresConsent?: boolean;
    status?: 'needs_organization_setup' | 'needs_organization_selection' | 'authenticated';
    organizations?: Organization[];
  }>;
  acceptGDPR: (ip?: string) => Promise<{ success: boolean; error?: string }>;
  exportData: () => Promise<{ success: boolean; data?: any; error?: string }>;
  deleteAccount: () => Promise<{ success: boolean; error?: string }>;
  signup: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<{ success: boolean; error?: string }>;
  syncData: () => Promise<{ success: boolean; error?: string }>;
  sync: () => Promise<{ success: boolean; error?: string }>;
  initialize: () => Promise<{ success: boolean; error?: string }>;
  testConnection: () => Promise<void>;
  subscribe: () => Promise<void>;
  unsubscribe: () => Promise<void>;
  resolveConflicts: (resolutions: Array<{
    conflictId: string;
    resolution: 'local' | 'remote' | 'merged';
    mergedData?: unknown;
  }>) => Promise<{ success: boolean; error?: string }>;
  onStatusUpdate: (callback: (status: {
    status: 'idle' | 'syncing' | 'error' | 'success' | 'info' | 'offline';
    message?: string;
    timestamp?: number;
    error?: string;
    phase?: string;
  }) => void) => () => void;
  onDataChanged: (callback: (data: {
    table: string;
    event: string;
    data: any;
  }) => void) => () => void;
  onConflicts: (callback: (conflicts: SyncConflict[]) => void) => () => void;
  configureOAuth: (settings: {
    authTimeout?: number;
    sessionTimeout?: number;
    sessionWarningTime?: number;
    autoLogoutEnabled?: boolean;
  }) => Promise<{ success: boolean; error?: string }>;
  getOAuthConfig: () => Promise<{ success: boolean; data?: any; error?: string }>;
  updateActivity: () => Promise<{ success: boolean; error?: string }>;
  resetAuthLoop: () => Promise<{ success: boolean; error?: string }>;
  recoverAuth: () => Promise<{ success: boolean; error?: string }>;
  checkAuthHealth: () => Promise<{ success: boolean; data?: any; error?: string }>;
  onSessionWarning: (callback: (data: { minutesRemaining: number }) => void) => () => void;
  onSessionExpired: (callback: (data: { reason: string; timeout: number }) => void) => () => void;
  onAuthLoopDetected: (callback: (data: { 
    cooldownMinutes: number; 
    cooldownUntil: number; 
    attempts: number; 
    windowMinutes: number; 
  }) => void) => () => void;
  onAuthLoopReset: (callback: () => void) => () => void;
  
  // Progress and queue management
  getProgress: () => Promise<{ success: boolean; progress?: any; timestamp?: number }>;
  getQueueStats: () => Promise<{ success: boolean; queueStats?: any; timestamp?: number }>;
  getMetrics: () => Promise<{ success: boolean; metrics?: any; timestamp?: number }>;
  clearNotFoundDeletes: () => Promise<{ success: boolean; removedCount?: number; message?: string; error?: string; timestamp?: number }>;
  getStatusReport: () => Promise<{ success: boolean; report?: any; timestamp?: number }>;
  updateUserActivity: () => Promise<{ success: boolean }>;
  
  // Real-time event listeners
  onProgressUpdate: (callback: (progress: any) => void) => () => void;
  onQueueUpdate: (callback: (queueStats: any) => void) => () => void;
  onMetricsUpdate: (callback: (metrics: any) => void) => () => void;
  onSlowOperation: (callback: (data: any) => void) => () => void;
}

interface OrganizationAPI {
  createOrganization: (data: { name: string; slug?: string }) => Promise<{
    success: boolean;
    data?: Organization;
    error?: string;
  }>;
  getOrganizations: () => Promise<{
    success: boolean;
    data: Organization[];
    error?: string;
  }>;
  getCurrentOrganization: () => Promise<{
    success: boolean;
    data?: Organization;
    error?: string;
  }>;
  setCurrentOrganization: (organizationId: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  getMembers: (organizationId: string) => Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }>;
  inviteMember: (data: { organizationId: string; email: string; role?: string }) => Promise<{
    success: boolean;
    error?: string;
  }>;
  removeMember: (data: { organizationId: string; userId: string }) => Promise<{
    success: boolean;
    error?: string;
  }>;
  updateMemberRole: (data: { organizationId: string; userId: string; role: string }) => Promise<{
    success: boolean;
    error?: string;
  }>;
  updateSettings: (data: { organizationId: string; settings: any }) => Promise<{
    success: boolean;
    error?: string;
  }>;
  deleteOrganization: (organizationId: string, forceCascade?: boolean) => Promise<{
    success: boolean;
    error?: string;
  }>;
  acceptInvitation: (token: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  getPendingInvitations: (organizationId: string) => Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }>;
  revokeInvitation: (data: { organizationId: string; invitationId: string }) => Promise<{
    success: boolean;
    error?: string;
  }>;
  onInvitationReceived: (callback: (token: string) => void) => () => void;
}

interface TestDataAPI {
  createTestProduct: () => Promise<any>;
  removeTestData: () => Promise<any>;
}

interface ColorLibraryAPI {
  searchColors: (options: any) => Promise<any>;
  getPantoneColors: (options: any) => Promise<any>;
  getRalColors: (options: any) => Promise<any>;
  fullTextSearch: (query: string, options: any) => Promise<any>;
  getPopularColors: (libraryCode?: string, limit?: number) => Promise<any>;
  getColorByExternalId: (externalId: string) => Promise<any>;
  getColorByCode: (libraryCode: string, colorCode: string) => Promise<any>;
  incrementUsage: (externalId: string) => Promise<any>;
  getStats: () => Promise<any>;
  forceReimport: () => Promise<any>;
  needsImport: () => Promise<any>;
  searchColorsEnhanced: (options: any) => Promise<any>;
  getLibraryMetadata: (libraryCode: string) => Promise<any>;
  getAvailableLibraries: () => Promise<any>;
  loadLibraryChunk: (libraryCode: string, startIndex: number, chunkSize?: number) => Promise<any>;
  getCacheStats: () => Promise<any>;
  clearCache: (libraryCode?: string) => Promise<any>;
}

interface SoftDeleteAPI {
  colors: {
    getSoftDeleted: (organizationId: string, limit?: number, offset?: number) => Promise<any>;
    restore: (colorId: string, organizationId: string, userId?: string) => Promise<any>;
    bulkRestore: (colorIds: string[], organizationId: string, userId?: string) => Promise<any>;
    cleanupOld: (organizationId: string, daysOld?: number) => Promise<any>;
  };
  products: {
    getSoftDeleted: (organizationId: string, limit?: number, offset?: number) => Promise<any>;
    restore: (productId: string, organizationId: string, userId?: string) => Promise<any>;
  };
  datasheets: {
    getSoftDeleted: (organizationId: string, limit?: number, offset?: number) => Promise<any>;
    restore: (datasheetId: string, organizationId: string, userId?: string) => Promise<any>;
    cleanupOld: (organizationId: string, daysOld?: number) => Promise<any>;
  };
}

interface IntegrityAPI {
  runChecks: (organizationId: string) => Promise<any>;
  fixColorSpaces: (organizationId: string) => Promise<any>;
}

interface AuditAPI {
  getLogs: (filter?: {
    organizationId?: string;
    userId?: string;
    table?: string;
    operation?: string;
    startDate?: string;
    endDate?: string;
    onlyErrors?: boolean;
  }) => Promise<any>;
  getAnalytics: (organizationId?: string) => Promise<any>;
  getSuspiciousActivity: (organizationId?: string) => Promise<any>;
  exportLogs: (options?: {
    organizationId?: string;
    startDate?: string;
    endDate?: string;
    format?: 'json' | 'csv';
  }) => Promise<any>;
  cleanupLogs: () => Promise<any>;
}

interface InvitationHandlerAPI {
  onInvitation: (callback: (token: string) => void) => () => void;
}

interface AutoSyncAPI {
  getConfig: () => Promise<{ success: boolean; config?: any; status?: any; error?: string }>;
  updateConfig: (config: any) => Promise<{ success: boolean; config?: any; error?: string }>;
  initialize: (config?: any) => Promise<{ success: boolean; config?: any; status?: any; error?: string }>;
  forceSync: () => Promise<{ success: boolean; result?: any; error?: string }>;
  getStatus: () => Promise<{ success: boolean; status?: any; error?: string }>;
  cleanup: () => Promise<{ success: boolean; error?: string }>;
  onSyncStarted: (callback: (data: any) => void) => () => void;
  onSyncCompleted: (callback: (data: any) => void) => () => void;
  onSyncFailed: (callback: (data: any) => void) => () => void;
  onConfigUpdated: (callback: (data: any) => void) => () => void;
  removeAllListeners: () => void;
}

// ===== GLOBAL WINDOW INTERFACE =====

declare global {
  interface Window {
    electron: ElectronAPI;
    ipc: IpcAPI;
    app: AppAPI;
    setupAPI: SetupAPI;
    api: ApiAPI;
    licenseAPI: LicenseAPI;
    sharedFolder: SharedFolderAPI;
    monitoring: MonitoringAPI;
    autoUpdate: AutoUpdateAPI;
    colorAPI: ColorAPI;
    productAPI: ProductAPI;
    datasheetAPI: DatasheetAPI;
    syncAPI: SyncAPI;
    autoSyncAPI: AutoSyncAPI;
    organizationAPI: OrganizationAPI;
    testDataAPI: TestDataAPI;
    colorLibraryAPI: ColorLibraryAPI;
    softDeleteAPI: SoftDeleteAPI;
    integrityAPI: IntegrityAPI;
    auditAPI: AuditAPI;
    invitationHandler: InvitationHandlerAPI;
  }
}

export {};