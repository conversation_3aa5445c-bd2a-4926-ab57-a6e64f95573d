#!/usr/bin/env node

/**
 * Test script to verify product-color relationship organization filtering
 * Usage: node scripts/test-product-color-relationships.js [organization-id]
 */

const path = require('path');
const Database = require('better-sqlite3');

// Database path
const dbPath = path.join(
  process.env.HOME || process.env.USERPROFILE,
  process.platform === 'darwin' 
    ? 'Library/Application Support/chroma-sync/chromasync.db'
    : process.platform === 'win32'
    ? 'AppData/Roaming/chroma-sync/chromasync.db'
    : '.config/chroma-sync/chromasync.db'
);

console.log('🔍 Testing product-color relationship organization filtering...');
console.log(`Database path: ${dbPath}`);

try {
  // Connect to database
  const db = new Database(dbPath);

  // Test organization ID
  const TEST_ORG_ID = process.argv[2];

  if (!TEST_ORG_ID) {
    console.error('❌ Please provide organization ID as argument');
    console.log('Usage: node scripts/test-product-color-relationships.js <organization-id>');
    
    // Show available organizations
    const orgs = db.prepare('SELECT external_id, name FROM organizations').all();
    console.log('\nAvailable organizations:');
    orgs.forEach(org => console.log(`  - ${org.external_id} (${org.name})`));
    
    process.exit(1);
  }

  console.log(`\n🧪 Testing for organization: ${TEST_ORG_ID}`);

  // 1. Check products in this organization
  const products = db.prepare(`
    SELECT external_id, name, organization_id
    FROM products 
    WHERE organization_id = ? AND deleted_at IS NULL AND is_active = 1
  `).all(TEST_ORG_ID);

  console.log(`\n📦 Found ${products.length} products in organization`);
  products.forEach(p => console.log(`  - ${p.name} (${p.external_id})`));

  // 2. Check product_colors table for organization filtering
  const productColorsCount = db.prepare(`
    SELECT COUNT(*) as count
    FROM product_colors 
    WHERE organization_id = ?
  `).get(TEST_ORG_ID);

  console.log(`\n🎨 Product-color relationships in this organization: ${productColorsCount.count}`);

  // 3. Test the fixed query (simulate what ProductService.getAllProductsWithColors does)
  const productsWithColors = products.map(product => {
    // Get internal product ID
    const internalProduct = db.prepare(`
      SELECT id FROM products 
      WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL
    `).get(product.external_id, TEST_ORG_ID);

    if (!internalProduct) {
      return { ...product, colorCount: 0, issue: 'Internal product not found' };
    }

    // Test the FIXED query with organization filtering
    const colorIds = db.prepare(`
      SELECT c.external_id
      FROM product_colors pc
      JOIN colors c ON pc.color_id = c.id
      WHERE pc.product_id = ? AND pc.organization_id = ? AND c.deleted_at IS NULL
      ORDER BY pc.display_order
    `).all(internalProduct.id, TEST_ORG_ID);

    // Test the OLD query without organization filtering (for comparison)
    const colorIdsOld = db.prepare(`
      SELECT c.external_id
      FROM product_colors pc
      JOIN colors c ON pc.color_id = c.id
      WHERE pc.product_id = ? AND c.deleted_at IS NULL
      ORDER BY pc.display_order
    `).all(internalProduct.id);

    return {
      ...product,
      colorCountFixed: colorIds.length,
      colorCountOld: colorIdsOld.length,
      colors: colorIds.map(c => c.external_id),
      issue: colorIds.length !== colorIdsOld.length ? 'Organization filtering working!' : null
    };
  });

  console.log('\n🔍 Product-color relationship test results:');
  productsWithColors.forEach(product => {
    console.log(`\n  📦 ${product.name}:`);
    console.log(`    - Fixed query (with org filter): ${product.colorCountFixed} colors`);
    console.log(`    - Old query (without org filter): ${product.colorCountOld} colors`);
    if (product.colors.length > 0) {
      console.log(`    - Color IDs: ${product.colors.slice(0, 3).join(', ')}${product.colors.length > 3 ? '...' : ''}`);
    }
    if (product.issue) {
      console.log(`    - ⚠️  ${product.issue}`);
    }
  });

  // 4. Check for cross-organization data leakage
  console.log('\n🔒 Testing for cross-organization data leakage...');
  
  const allOrgs = db.prepare('SELECT external_id, name FROM organizations').all();
  const otherOrgs = allOrgs.filter(org => org.external_id !== TEST_ORG_ID);
  
  if (otherOrgs.length > 0) {
    const otherOrgId = otherOrgs[0].external_id;
    console.log(`    Comparing with organization: ${otherOrgs[0].name} (${otherOrgId})`);
    
    const crossOrgResults = products.map(product => {
      const internalProduct = db.prepare(`
        SELECT id FROM products 
        WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL
      `).get(product.external_id, TEST_ORG_ID);

      if (!internalProduct) return { ...product, crossOrgColors: 0 };

      // Try to get colors using the OTHER organization ID
      const crossOrgColors = db.prepare(`
        SELECT c.external_id
        FROM product_colors pc
        JOIN colors c ON pc.color_id = c.id
        WHERE pc.product_id = ? AND pc.organization_id = ? AND c.deleted_at IS NULL
      `).all(internalProduct.id, otherOrgId);

      return {
        ...product,
        crossOrgColors: crossOrgColors.length
      };
    });

    const leakageFound = crossOrgResults.some(p => p.crossOrgColors > 0);
    if (leakageFound) {
      console.log('    ❌ POTENTIAL DATA LEAKAGE FOUND!');
      crossOrgResults.filter(p => p.crossOrgColors > 0).forEach(p => {
        console.log(`      - ${p.name}: ${p.crossOrgColors} colors found with wrong org ID`);
      });
    } else {
      console.log('    ✅ No cross-organization data leakage detected');
    }
  }

  // 5. Summary
  const totalColorsFound = productsWithColors.reduce((sum, p) => sum + p.colorCountFixed, 0);
  const organizationFilteringWorking = productsWithColors.some(p => p.colorCountFixed !== p.colorCountOld);
  
  console.log('\n📊 Test Summary:');
  console.log(`  - Products tested: ${products.length}`);
  console.log(`  - Total colors found (with org filtering): ${totalColorsFound}`);
  console.log(`  - Organization filtering working: ${organizationFilteringWorking ? '✅ YES' : '❓ UNCLEAR'}`);
  console.log(`  - Cross-organization leakage: ${!leakageFound ? '✅ NONE' : '❌ DETECTED'}`);

  console.log('\n✨ Test complete!');
  console.log('\nIf organization filtering is working correctly:');
  console.log('- Products should show consistent color counts after refresh');
  console.log('- No cross-organization data should be visible');
  console.log('- Both gradients and regular colors should display properly');

  db.close();

} catch (error) {
  console.error('❌ Test failed:', error.message);
  if (error.code === 'SQLITE_CANTOPEN') {
    console.log('\n💡 Make sure ChromaSync is not running and the database path is correct');
  }
  process.exit(1);
}