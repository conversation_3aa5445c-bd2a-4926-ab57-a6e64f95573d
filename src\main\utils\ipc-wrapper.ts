/**
 * @file ipc-wrapper.ts
 * @description Universal IPC wrapper functions for consistent security, error handling, and response formatting
 * Consolidates best practices from existing handlers into reusable utilities
 */

import { IpcMainInvokeEvent } from 'electron';
import {
  validateOrganizationContext,
  getValidatedOrganizationId,
  OrganizationContextError,
} from '../middleware/organization-context.middleware';
import {
  IPCResponse,
  IPCHandlerOptions,
  OrgIPCHandler,
  SystemIPCHandler,
} from '../../shared/types/ipc.types';
import { registerHandlerSafely } from './ipcRegistry';

/**
 * Type for handling unknown errors
 */
type ErrorWithMessage = {
  message: string;
};

/**
 * Helper function to safely extract error message
 * @param error - Any error object
 * @returns Normalized error message
 */
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  if (isErrorWithMessage(error)) {
    return error.message;
  }
  return String(error);
}

/**
 * Type guard for objects with message property
 */
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

/**
 * Create standardized success response
 */
export function createSuccessResponse<T>(
  data?: T,
  userMessage?: string
): IPCResponse<T> {
  return {
    success: true,
    data,
    userMessage,
    timestamp: Date.now(),
  };
}

/**
 * Create standardized error response
 */
export function createErrorResponse(
  error: unknown,
  userMessage?: string
): IPCResponse {
  const errorMessage = getErrorMessage(error);

  return {
    success: false,
    error: errorMessage,
    userMessage:
      userMessage || 'An unexpected error occurred. Please try again.',
    timestamp: Date.now(),
  };
}

/**
 * Handle organization context validation errors
 */
function handleOrganizationError(error: OrganizationContextError): IPCResponse {
  return {
    success: false,
    error: error.error,
    userMessage: error.userMessage,
    timestamp: error.timestamp,
  };
}

/**
 * Universal IPC handler wrapper with organization context validation
 * This version requires organization context and passes organizationId as the first parameter
 *
 * @param handler - Handler function that receives (organizationId, ...args)
 * @param options - Configuration options
 * @returns Wrapped handler with comprehensive error handling and security
 */
export function createSecureOrgHandler<T extends any[], R>(
  handler: OrgIPCHandler<T, R>,
  options: Partial<IPCHandlerOptions> = {}
) {
  const {
    requireOrganization = true,
    logChannel = 'IPC',
    customErrorMessage = 'Unable to complete the operation. Please try again or contact support if the issue persists.',
  } = options;

  return async (
    _event: IpcMainInvokeEvent,
    ...args: T
  ): Promise<IPCResponse<R>> => {
    try {
      let organizationId: string | undefined;

      if (requireOrganization) {
        // Validate organization context
        const validation = await validateOrganizationContext();
        if (!validation.isValid && validation.error) {
          console.warn(
            `[${logChannel}] Organization context validation failed:`,
            validation.error
          );
          return handleOrganizationError(validation.error);
        }
        organizationId = validation.organizationId!;
      } else {
        // Try to get organization ID but don't fail if not available
        organizationId = (await getValidatedOrganizationId()) || undefined;
      }

      // Execute handler with validated context
      const result = await handler(organizationId!, ...args);

      // Handle if result is already a response object
      if (
        typeof result === 'object' &&
        result !== null &&
        'success' in result &&
        'timestamp' in result
      ) {
        return result as unknown as IPCResponse<R>;
      }

      return createSuccessResponse(result);
    } catch (error) {
      console.error(`[${logChannel}] Handler error:`, error);
      return createErrorResponse(error, customErrorMessage);
    }
  };
}

/**
 * Universal IPC handler wrapper WITHOUT organization context requirements
 * For system-level operations that don't need organization scoping
 *
 * @param handler - Handler function that receives (...args)
 * @param options - Configuration options
 * @returns Wrapped handler with comprehensive error handling
 */
export function createSecureHandler<T extends any[], R>(
  handler: SystemIPCHandler<T, R>,
  options: Partial<IPCHandlerOptions> = {}
) {
  const {
    logChannel = 'IPC',
    customErrorMessage = 'Unable to complete the operation. Please try again or contact support if the issue persists.',
    requireAuth: _requireAuth = false,
  } = options;

  return async (
    _event: IpcMainInvokeEvent,
    ...args: T
  ): Promise<IPCResponse<R>> => {
    try {
      // TODO: Add authentication check if requireAuth is true
      // This could check for valid user session, etc.

      // Execute handler
      const result = await handler(...args);

      // Handle if result is already a response object
      if (
        typeof result === 'object' &&
        result !== null &&
        'success' in result &&
        'timestamp' in result
      ) {
        return result as unknown as IPCResponse<R>;
      }

      return createSuccessResponse(result);
    } catch (error) {
      console.error(`[${logChannel}] Handler error:`, error);
      return createErrorResponse(error, customErrorMessage);
    }
  };
}

// Deprecated withOrganizationContext function removed - use createSecureOrgHandler instead

/**
 * Type helper for IPC handler registration with the canRegisterHandler pattern
 */
export type SecureIPCHandler<T extends any[], R> = (
  event: IpcMainInvokeEvent,
  ...args: T
) => Promise<IPCResponse<R>>;

/**
 * Utility to safely register IPC handlers with duplicate prevention
 * Combines the universal wrapper with the canRegisterHandler pattern
 */
export function registerSecureHandler<T extends any[], R>(
  channel: string,
  handler: OrgIPCHandler<T, R>,
  ipcMain: any,
  options: Partial<IPCHandlerOptions> = {}
) {
  const { skipDuplicateCheck = false } = options;

  const wrappedHandler = createSecureOrgHandler(handler, options);

  if (!skipDuplicateCheck) {
    // Use registerHandlerSafely to properly handle duplicates
    return registerHandlerSafely(ipcMain, channel, wrappedHandler);
  } else {
    // Skip duplicate checking and register directly
    ipcMain.handle(channel, wrappedHandler);
    return true;
  }
}

/**
 * Utility to register system-level handlers (no organization context required)
 */
export function registerSystemHandler<T extends any[], R>(
  channel: string,
  handler: SystemIPCHandler<T, R>,
  ipcMain: any,
  options: Partial<IPCHandlerOptions> = {}
) {
  const { skipDuplicateCheck = false } = options;

  const wrappedHandler = createSecureHandler(handler, options);

  if (!skipDuplicateCheck) {
    // Use registerHandlerSafely to properly handle duplicates
    return registerHandlerSafely(ipcMain, channel, wrappedHandler);
  } else {
    // Skip duplicate checking and register directly
    ipcMain.handle(channel, wrappedHandler);
    return true;
  }
}
