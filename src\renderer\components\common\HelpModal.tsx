/**
 * @file HelpModal.tsx
 * @description Help modal component
 */

import React from 'react';
import Modal from '../ui/Modal';
import { ExternalLink, Book, MessageCircle } from 'lucide-react';

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Help modal component
 */
const HelpModal: React.FC<HelpModalProps> = ({ isOpen, onClose }) => {
  const handleLinkClick = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Help & Support">
      <div className="space-y-6">
        {/* Quick Help */}
        <section>
          <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
            Quick Help
          </h3>
          <div className="space-y-3">
            <div className="p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)]">
              <h4 className="font-medium text-ui-foreground-primary dark:text-white mb-1">
                Adding Colors
              </h4>
              <p className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                Click the "+" button or use Ctrl+N to add new colors to your collection.
              </p>
            </div>
            
            <div className="p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)]">
              <h4 className="font-medium text-ui-foreground-primary dark:text-white mb-1">
                Search & Filter
              </h4>
              <p className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                Use the search bar to find colors by name, code, or properties.
              </p>
            </div>
            
            <div className="p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)]">
              <h4 className="font-medium text-ui-foreground-primary dark:text-white mb-1">
                Keyboard Shortcuts
              </h4>
              <p className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                Press ? to view all available keyboard shortcuts.
              </p>
            </div>
          </div>
        </section>

        {/* Resources */}
        <section>
          <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
            Resources
          </h3>
          <div className="space-y-2">
            <button
              onClick={() => handleLinkClick('https://chromasync.app/docs.html')}
              className="w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors"
            >
              <div className="flex items-center">
                <Book size={16} className="text-brand-primary dark:text-blue-400 mr-2" />
                <span className="text-ui-foreground-primary dark:text-gray-300">Documentation</span>
              </div>
              <ExternalLink size={14} className="text-ui-foreground-secondary dark:text-gray-400" />
            </button>
            
            <button
              onClick={() => handleLinkClick('https://chromasync.app/about.html')}
              className="w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors"
            >
              <div className="flex items-center">
                <MessageCircle size={16} className="text-brand-primary dark:text-blue-400 mr-2" />
                <span className="text-ui-foreground-primary dark:text-gray-300">Contact Support</span>
              </div>
              <ExternalLink size={14} className="text-ui-foreground-secondary dark:text-gray-400" />
            </button>
          </div>
        </section>

        {/* Version Info */}
        <section className="pt-4 border-t border-ui-border-light dark:border-zinc-700">
          <div className="text-center text-sm text-ui-foreground-secondary dark:text-gray-400">
            ChromaSync v1.0.0
          </div>
        </section>
      </div>
    </Modal>
  );
};

export default HelpModal;