/**
 * @file index.ts
 * @description Product service exports
 * 
 * Exports all product-related services including sync and deduplication capabilities.
 */

export { ProductSyncService } from './product-sync.service';
export type { 
  ProductSyncResult, 
  ProductSyncOptions, 
  SupabaseProductData,
  ProductColorSyncResult
} from './product-sync.service';

export { ProductColorRelationshipService } from './product-color-relationship.service';
export type {
  ProductColorAssociation,
  ColorUsageAnalysis,
  BulkAssignmentResult,
  SimilarProductSuggestion
} from './product-color-relationship.service';

export { ProductDeduplicationService } from './product-deduplication.service';
export type {
  DuplicateDetectionCriteria,
  MatchingAlgorithm,
  MergeStrategy,
  DuplicateGroup,
  ProductDuplicate,
  DeduplicationResult,
  DeduplicationBackup,
  SimilarityScore,
  DeduplicationOptions
} from './product-deduplication.service';

export {
  DEFAULT_DETECTION_CRITERIA,
  DEFAULT_MATCHING_ALGORITHM,
  DEFAULT_MERGE_STRATEGY,
  DEFAULT_DEDUPLICATION_OPTIONS
} from './product-deduplication.service';