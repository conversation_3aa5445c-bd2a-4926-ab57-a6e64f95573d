import { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import { hexToHsl } from '../../../../../../shared/utils/color/conversion';
// import { useTokens } from '../../../../../hooks/useTokens';
import {
  safeArrayAccess,
  getOrDefault,
} from '../../../../../../shared/types/type-guards';

interface HueDistributionChartProps {
  colors: Array<{
    id: string;
    hex: string;
    name: string;
  }>;
}

const HUE_RANGES = [
  { name: 'Red', start: 0, end: 30, color: '#FF0000' },
  { name: 'Orange', start: 30, end: 60, color: '#FFA500' },
  { name: 'Yellow', start: 60, end: 90, color: '#FFFF00' },
  { name: 'Yellow-Green', start: 90, end: 120, color: '#9ACD32' },
  { name: '<PERSON>', start: 120, end: 150, color: '#00FF00' },
  { name: '<PERSON><PERSON>-<PERSON>', start: 150, end: 180, color: '#00CED1' },
  { name: '<PERSON><PERSON>', start: 180, end: 210, color: '#00FFFF' },
  { name: 'Blue', start: 210, end: 240, color: '#0000FF' },
  { name: 'Blue-Violet', start: 240, end: 270, color: '#8A2BE2' },
  { name: 'Violet', start: 270, end: 300, color: '#EE82EE' },
  { name: 'Magenta', start: 300, end: 330, color: '#FF00FF' },
  { name: 'Red-Magenta', start: 330, end: 360, color: '#DC143C' },
];

export default function HueDistributionChart({
  colors,
}: HueDistributionChartProps) {
  // const _tokens = useTokens();

  const hueData = useMemo(() => {
    // Initialize buckets
    const buckets = HUE_RANGES.map(range => ({
      ...range,
      value: 0,
      percentage: 0,
      colors: [] as string[],
    }));

    // Categorize colors by hue
    colors.forEach(color => {
      const hsl = hexToHsl(color.hex);
      if (!hsl) {
        return;
      }

      const hue = hsl.h;
      const bucketIndex = HUE_RANGES.findIndex(
        range => hue >= range.start && hue < range.end
      );

      if (bucketIndex !== -1) {
        const bucket = safeArrayAccess(buckets, bucketIndex);
        if (bucket) {
          bucket.value++;
          bucket.colors.push(getOrDefault(color.name, color.hex));
        }
      }
    });

    // Calculate percentages
    const total = colors.length;
    buckets.forEach(bucket => {
      bucket.percentage = total > 0 ? (bucket.value / total) * 100 : 0;
    });

    // Filter out empty buckets for cleaner visualization
    return buckets.filter(bucket => bucket.value > 0);
  }, [colors]);

  const saturationData = useMemo(() => {
    const ranges = [
      { name: 'Desaturated', min: 0, max: 33 },
      { name: 'Moderate', min: 33, max: 66 },
      { name: 'Saturated', min: 66, max: 100 },
    ];

    const data = ranges.map(range => ({
      ...range,
      value: 0,
      percentage: 0,
    }));

    colors.forEach(color => {
      const hsl = hexToHsl(color.hex);
      if (!hsl) {
        return;
      }

      const saturation = hsl.s;
      const rangeIndex = ranges.findIndex(
        range => saturation >= range.min && saturation <= range.max
      );

      if (rangeIndex !== -1) {
        const item = safeArrayAccess(data, rangeIndex);
        if (item) {
          item.value++;
        }
      }
    });

    // Calculate percentages
    const total = colors.length;
    data.forEach(item => {
      item.percentage = total > 0 ? (item.value / total) * 100 : 0;
    });

    return data;
  }, [colors]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload[0]) {
      const data = payload[0].payload;
      return (
        <div className='bg-white p-3 rounded-lg shadow-lg border border-ui-border-light'>
          <p className='font-medium'>{data.name}</p>
          <p className='text-sm text-ui-foreground-secondary'>
            {data.value} colors ({data.percentage.toFixed(1)}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className='h-full overflow-y-auto p-4 space-y-6'>
      <div>
        <h3 className='text-lg font-medium mb-4'>Hue Distribution</h3>
        <p className='text-sm text-ui-foreground-secondary mb-6'>
          Distribution of colors across the hue spectrum
        </p>

        <div className='bg-ui-background-secondary rounded-lg p-4'>
          <ResponsiveContainer width='100%' height={400}>
            <PieChart>
              <Pie
                data={hueData}
                dataKey='value'
                nameKey='name'
                cx='50%'
                cy='50%'
                innerRadius={60}
                outerRadius={120}
                paddingAngle={2}
                label={entry => `${entry.percentage.toFixed(1)}%`}
                labelLine={false}
              >
                {hueData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend
                verticalAlign='bottom'
                height={36}
                formatter={(value, entry) =>
                  `${value} (${entry?.payload?.value || 0})`
                }
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div>
        <h3 className='text-lg font-medium mb-4'>Saturation Distribution</h3>
        <div className='space-y-3'>
          {saturationData.map(item => (
            <div key={item.name}>
              <div className='flex justify-between text-sm mb-1'>
                <span>{item.name}</span>
                <span className='text-ui-foreground-secondary'>
                  {item.value} colors ({item.percentage.toFixed(1)}%)
                </span>
              </div>
              <div className='w-full bg-ui-background-tertiary rounded-full h-2'>
                <div
                  className='h-2 rounded-full bg-brand-primary transition-all duration-300'
                  style={{ width: `${item.percentage}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      <div className='grid grid-cols-2 gap-4'>
        <div className='bg-ui-background-tertiary rounded-lg p-4'>
          <h4 className='text-sm font-medium text-ui-foreground-secondary mb-1'>
            Most Common Hue
          </h4>
          <p className='text-lg font-semibold'>
            {hueData.length > 0
              ? (safeArrayAccess(
                  hueData.sort((a, b) => b.value - a.value),
                  0
                )?.name ?? 'N/A')
              : 'N/A'}
          </p>
        </div>
        <div className='bg-ui-background-tertiary rounded-lg p-4'>
          <h4 className='text-sm font-medium text-ui-foreground-secondary mb-1'>
            Hue Diversity
          </h4>
          <p className='text-lg font-semibold'>{hueData.length} / 12 ranges</p>
        </div>
      </div>
    </div>
  );
}
