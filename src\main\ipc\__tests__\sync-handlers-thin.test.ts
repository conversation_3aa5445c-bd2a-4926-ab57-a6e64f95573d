/**
 * @file sync-handlers-thin.test.ts
 * @description Test IPC handler registration and response format for sync handlers
 */

import { ipcMain } from 'electron';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type { SyncOutboxService } from '../../services/sync/sync-outbox.service';
import type { SyncStatusManagerService } from '../../services/sync/sync-status-manager.service';
import type { UnifiedSyncManager } from '../../services/sync/unified-sync-manager';
import { SyncHandlersThin } from '../sync-handlers-thin';

// Mock electron
vi.mock('electron', () => ({
  ipcMain: {
    handle: vi.fn(),
    removeHandler: vi.fn(),
  },
}));

// Mock dependencies
vi.mock('../../utils/ipcRegistry', () => ({
  registerHandlerSafely: vi.fn((ipcMain, channel, handler) => {
    ipcMain.handle(channel, handler);
  }),
}));

vi.mock('../../utils/organization-context', () => ({
  getCurrentOrganization: vi.fn(() => 'test-org-id'),
}));

vi.mock('../../services/service-locator', () => ({
  ServiceLocator: {
    getCircuitBreakerAuthManager: vi.fn(() => ({
      getHealthStatus: vi.fn(() => ({
        isHealthy: true,
        circuitBreakerOpen: false,
        sessionValid: true,
        networkConnected: true,
        issues: [],
      })),
      getCurrentUser: vi.fn(() => Promise.resolve({ id: 'test-user-id' })),
      getCurrentSession: vi.fn(() => Promise.resolve({ access_token: 'test-token' })),
    })),
  },
}));

describe('SyncHandlersThin', () => {
  let syncHandlersThin: SyncHandlersThin;
  let mockUnifiedSyncManager: UnifiedSyncManager;
  let mockSyncOutboxService: SyncOutboxService;
  let mockSyncStatusManager: SyncStatusManagerService;

  beforeEach(() => {
    // Create mock services
    mockUnifiedSyncManager = {
      isReady: vi.fn(() => true),
      sync: vi.fn(() => Promise.resolve({
        success: true,
        itemsProcessed: 10,
        duration: 1000,
        operation: 'full-sync',
      })),
      getStatus: vi.fn(() => ({
        isRunning: false,
        lastSyncTime: Date.now(),
        config: {
          autoSyncEnabled: true,
          autoSyncInterval: 5,
          realtimeEnabled: false,
          maxRetries: 3,
        },
      })),
      updateConfig: vi.fn(),
      initialize: vi.fn(() => Promise.resolve()),
      stop: vi.fn(),
    } as any;

    mockSyncOutboxService = {
      getPendingChanges: vi.fn(() => []),
      clearAll: vi.fn(),
      clearNotFoundDeleteOperations: vi.fn(() => 0),
    } as any;

    mockSyncStatusManager = {
      getProgress: vi.fn(() => Promise.resolve({ progress: 0 })),
      getMetrics: vi.fn(() => Promise.resolve({ totalSyncs: 0 })),
      getQueueStats: vi.fn(() => Promise.resolve({ queueSize: 0 })),
      getStatusReport: vi.fn(() => ({ status: 'idle' })),
      hasUnsyncedLocalChanges: vi.fn(() => Promise.resolve(false)),
      reset: vi.fn(),
      updateUserActivity: vi.fn(),
    } as any;

    syncHandlersThin = new SyncHandlersThin(
      mockUnifiedSyncManager,
      mockSyncOutboxService,
      mockSyncStatusManager
    );

    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Handler Registration', () => {
    it('should register all sync IPC handlers', () => {
      syncHandlersThin.registerHandlers();

      // Verify that ipcMain.handle was called for each expected channel
      const expectedChannels = [
        'sync:execute',
        'sync:execute-type',
        'sync:get-status',
        'sync:get-config',
        'sync:update-config',
        'sync:get-progress',
        'sync:get-metrics',
        'sync:get-queue-stats',
        'sync:get-status-report',
        'sync:has-unsynced-changes',
        'sync:get-outbox-status',
        'sync:clear-outbox',
        'sync:clear-failed-operations',
        'sync:clear-orphaned-operations',
        'sync:initialize',
        'sync:stop',
        'sync:reset',
        'sync:update-user-activity',
      ];

      expect(ipcMain.handle).toHaveBeenCalledTimes(expectedChannels.length);
      
      expectedChannels.forEach(channel => {
        expect(ipcMain.handle).toHaveBeenCalledWith(
          channel,
          expect.any(Function)
        );
      });
    });
  });

  describe('Response Format', () => {
    beforeEach(() => {
      syncHandlersThin.registerHandlers();
    });

    it('should return standardized IPC response format for successful sync execution', async () => {
      const handler = vi.mocked(ipcMain.handle).mock.calls.find(
        call => call[0] === 'sync:execute'
      )?.[1];

      expect(handler).toBeDefined();
      
      const response = await handler();

      expect(response).toMatchObject({
        success: true,
        data: {
          itemsProcessed: expect.any(Number),
          duration: expect.any(Number),
          operation: expect.any(String),
        },
        userMessage: expect.any(String),
        timestamp: expect.any(Number),
      });
    });

    it('should return standardized error response format when sync fails', async () => {
      // Mock sync failure
      mockUnifiedSyncManager.sync = vi.fn(() => Promise.resolve({
        success: false,
        errors: ['Test error'],
      }));

      const handler = vi.mocked(ipcMain.handle).mock.calls.find(
        call => call[0] === 'sync:execute'
      )?.[1];

      const response = await handler();

      expect(response).toMatchObject({
        success: false,
        error: expect.any(String),
        userMessage: expect.any(String),
        timestamp: expect.any(Number),
      });
    });

    it('should return standardized response format for status queries', async () => {
      const handler = vi.mocked(ipcMain.handle).mock.calls.find(
        call => call[0] === 'sync:get-status'
      )?.[1];

      const response = await handler();

      expect(response).toMatchObject({
        success: true,
        data: expect.any(Object),
        timestamp: expect.any(Number),
      });
    });
  });

  describe('Authentication Validation', () => {
    it('should validate authentication and organization context', async () => {
      const handler = vi.mocked(ipcMain.handle).mock.calls.find(
        call => call[0] === 'sync:execute'
      )?.[1];

      const response = await handler();

      expect(response.success).toBe(true);
      expect(response.data).toHaveProperty('itemsProcessed');
    });

    it('should handle authentication failures gracefully', async () => {
      // Mock authentication failure
      const { ServiceLocator } = await import('../../services/service-locator');
      vi.mocked(ServiceLocator.getCircuitBreakerAuthManager).mockReturnValue({
        getHealthStatus: vi.fn(() => ({
          isHealthy: false,
          circuitBreakerOpen: false,
          issues: ['Network error'],
        })),
        getCurrentUser: vi.fn(),
        getCurrentSession: vi.fn(),
      } as any);

      const handler = vi.mocked(ipcMain.handle).mock.calls.find(
        call => call[0] === 'sync:execute'
      )?.[1];

      const response = await handler();

      expect(response.success).toBe(false);
      expect(response.error).toContain('Authentication system issues');
      expect(response.userMessage).toContain('temporarily unavailable');
    });
  });
});