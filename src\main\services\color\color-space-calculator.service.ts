/**
 * @file color-space-calculator.service.ts
 * @description ColorSpaceCalculator service for color space conversions
 * 
 * This service provides a clean interface for all color space calculations
 * and wraps the pure color conversion functions from the shared utilities.
 * 
 * Key Features:
 * - Pure functional approach (no side effects)
 * - Comprehensive color space support (RGB, HSL, CMYK, LAB)
 * - Null-safe error handling
 * - Performance optimized
 * - Type-safe conversions
 */

import {
  // Types
  RGB,
  HSL,
  CMYK,
  LAB,
  ColorSpaces,
  
  // Conversion functions
  hexToRgb,
  rgbToHex,
  hexToHsl,
  hslToHex,
  hexToCmyk,
  cmykToHex,
  rgbToHsl,
  hslToRgb,
  rgbToCmyk,
  cmykToRgb,
  rgbToLab,
  labToRgb,
  getAllColorSpaces,
  isLightColor,
  isLightColorHsl
} from '../../../shared/utils/color';

/**
 * ColorSpaceCalculator Service
 * 
 * Provides a service layer for color space calculations that wraps
 * the pure color conversion functions with additional error handling
 * and validation.
 */
export class ColorSpaceCalculator {
  
  /**
   * Basic Color Space Conversions
   */
  
  /**
   * Convert hex color to RGB
   * @param hex - Hex color string (e.g., "#FF0000" or "FF0000")
   * @returns RGB object or null if invalid
   */
  hexToRgb(hex: string): RGB | null {
    if (!hex || typeof hex !== 'string') {
      return null;
    }
    
    try {
      return hexToRgb(hex);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting hex to RGB:', error);
      return null;
    }
  }
  
  /**
   * Convert RGB to hex color
   * @param rgb - RGB color object
   * @returns Hex color string in uppercase format
   */
  rgbToHex(rgb: RGB): string {
    try {
      return rgbToHex(rgb);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting RGB to hex:', error);
      // Fallback to black for invalid RGB
      return '#000000';
    }
  }
  
  /**
   * Convert hex color to HSL
   * @param hex - Hex color string
   * @returns HSL object or null if invalid
   */
  hexToHsl(hex: string): HSL | null {
    if (!hex || typeof hex !== 'string') {
      return null;
    }
    
    try {
      return hexToHsl(hex);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting hex to HSL:', error);
      return null;
    }
  }
  
  /**
   * Convert HSL to hex color
   * @param hsl - HSL color object
   * @returns Hex color string in uppercase format
   */
  hslToHex(hsl: HSL): string {
    try {
      return hslToHex(hsl);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting HSL to hex:', error);
      // Fallback to black for invalid HSL
      return '#000000';
    }
  }
  
  /**
   * Convert hex color to CMYK
   * @param hex - Hex color string
   * @returns CMYK object or null if invalid
   */
  hexToCmyk(hex: string): CMYK | null {
    if (!hex || typeof hex !== 'string') {
      return null;
    }
    
    try {
      return hexToCmyk(hex);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting hex to CMYK:', error);
      return null;
    }
  }
  
  /**
   * Convert CMYK to hex color
   * @param cmyk - CMYK color object
   * @returns Hex color string in uppercase format
   */
  cmykToHex(cmyk: CMYK): string {
    try {
      return cmykToHex(cmyk);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting CMYK to hex:', error);
      // Fallback to black for invalid CMYK
      return '#000000';
    }
  }
  
  /**
   * Intermediate Color Space Conversions
   */
  
  /**
   * Convert RGB to HSL
   * @param rgb - RGB color object
   * @returns HSL object
   */
  rgbToHsl(rgb: RGB): HSL {
    try {
      return rgbToHsl(rgb);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting RGB to HSL:', error);
      // Fallback to black HSL
      return { h: 0, s: 0, l: 0 };
    }
  }
  
  /**
   * Convert HSL to RGB
   * @param hsl - HSL color object
   * @returns RGB object
   */
  hslToRgb(hsl: HSL): RGB {
    try {
      return hslToRgb(hsl);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting HSL to RGB:', error);
      // Fallback to black RGB
      return { r: 0, g: 0, b: 0 };
    }
  }
  
  /**
   * Convert RGB to CMYK
   * @param rgb - RGB color object
   * @returns CMYK object
   */
  rgbToCmyk(rgb: RGB): CMYK {
    try {
      return rgbToCmyk(rgb);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting RGB to CMYK:', error);
      // Fallback to black CMYK
      return { c: 0, m: 0, y: 0, k: 100 };
    }
  }
  
  /**
   * Convert CMYK to RGB
   * @param cmyk - CMYK color object
   * @returns RGB object
   */
  cmykToRgb(cmyk: CMYK): RGB {
    try {
      return cmykToRgb(cmyk);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting CMYK to RGB:', error);
      // Fallback to black RGB
      return { r: 0, g: 0, b: 0 };
    }
  }
  
  /**
   * Advanced Color Space Conversions (LAB)
   */
  
  /**
   * Convert RGB to LAB color space
   * @param rgb - RGB color object
   * @returns LAB object
   */
  rgbToLab(rgb: RGB): LAB {
    try {
      return rgbToLab(rgb);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting RGB to LAB:', error);
      // Fallback to black LAB
      return { l: 0, a: 0, b: 0 };
    }
  }
  
  /**
   * Convert LAB to RGB color space
   * @param lab - LAB color object
   * @returns RGB object
   */
  labToRgb(lab: LAB): RGB {
    try {
      return labToRgb(lab);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error converting LAB to RGB:', error);
      // Fallback to black RGB
      return { r: 0, g: 0, b: 0 };
    }
  }
  
  /**
   * Comprehensive Conversion Functions
   */
  
  /**
   * Get all color spaces for a given hex color
   * @param hex - Hex color string
   * @returns All color spaces or null if invalid hex
   */
  getAllColorSpaces(hex: string): ColorSpaces | null {
    if (!hex || typeof hex !== 'string') {
      return null;
    }
    
    try {
      return getAllColorSpaces(hex);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error getting all color spaces:', error);
      return null;
    }
  }
  
  /**
   * Get calculated color spaces at runtime for a hex color
   * This is optimized for ColorService usage where only CMYK is stored
   * and RGB/HSL/LAB are calculated on-demand.
   * 
   * @param hex - Hex color string
   * @returns Object with calculated color spaces
   */
  getCalculatedColorSpaces(hex: string): { rgb: RGB | null; hsl: HSL | null; lab: LAB | null } {
    const rgb = this.hexToRgb(hex);
    if (!rgb) {
      return { rgb: null, hsl: null, lab: null };
    }
    
    const hsl = this.rgbToHsl(rgb);
    const lab = this.rgbToLab(rgb);
    
    return { rgb, hsl, lab };
  }
  
  /**
   * Utility Functions
   */
  
  /**
   * Check if a color is light or dark based on RGB values
   * Uses relative luminance calculation for accurate results
   * 
   * @param rgb - RGB color object
   * @returns true if color is light, false if dark
   */
  isLightColor(rgb: RGB): boolean {
    try {
      return isLightColor(rgb);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error checking light color:', error);
      // Fallback to dark
      return false;
    }
  }
  
  /**
   * Check if a color is light or dark based on HSL lightness value
   * Alternative method using HSL lightness component
   * 
   * @param hsl - HSL color object
   * @returns true if color is light (L > 50%), false if dark
   */
  isLightColorHsl(hsl: HSL): boolean {
    try {
      return isLightColorHsl(hsl);
    } catch (error) {
      console.warn('[ColorSpaceCalculator] Error checking light color HSL:', error);
      // Fallback to dark
      return false;
    }
  }
  
  /**
   * Check if a hex color is light or dark
   * Convenience method that combines hex->rgb conversion with light check
   * 
   * @param hex - Hex color string
   * @returns true if light, false if dark, null if invalid hex
   */
  isLightColorHex(hex: string): boolean | null {
    const rgb = this.hexToRgb(hex);
    if (!rgb) {
      return null;
    }
    
    return this.isLightColor(rgb);
  }
  
  /**
   * Validation Functions
   */
  
  /**
   * Validate if RGB values are in valid range (0-255)
   * @param rgb - RGB color object
   * @returns true if valid, false otherwise
   */
  isValidRgb(rgb: RGB): boolean {
    return (
      typeof rgb.r === 'number' && rgb.r >= 0 && rgb.r <= 255 &&
      typeof rgb.g === 'number' && rgb.g >= 0 && rgb.g <= 255 &&
      typeof rgb.b === 'number' && rgb.b >= 0 && rgb.b <= 255
    );
  }
  
  /**
   * Validate if HSL values are in valid range
   * @param hsl - HSL color object
   * @returns true if valid, false otherwise
   */
  isValidHsl(hsl: HSL): boolean {
    return (
      typeof hsl.h === 'number' && hsl.h >= 0 && hsl.h <= 360 &&
      typeof hsl.s === 'number' && hsl.s >= 0 && hsl.s <= 100 &&
      typeof hsl.l === 'number' && hsl.l >= 0 && hsl.l <= 100
    );
  }
  
  /**
   * Validate if CMYK values are in valid range (0-100)
   * @param cmyk - CMYK color object
   * @returns true if valid, false otherwise
   */
  isValidCmyk(cmyk: CMYK): boolean {
    return (
      typeof cmyk.c === 'number' && cmyk.c >= 0 && cmyk.c <= 100 &&
      typeof cmyk.m === 'number' && cmyk.m >= 0 && cmyk.m <= 100 &&
      typeof cmyk.y === 'number' && cmyk.y >= 0 && cmyk.y <= 100 &&
      typeof cmyk.k === 'number' && cmyk.k >= 0 && cmyk.k <= 100
    );
  }
  
  /**
   * Validate if LAB values are in valid range
   * @param lab - LAB color object
   * @returns true if valid, false otherwise
   */
  isValidLab(lab: LAB): boolean {
    return (
      typeof lab.l === 'number' && lab.l >= 0 && lab.l <= 100 &&
      typeof lab.a === 'number' && lab.a >= -128 && lab.a <= 127 &&
      typeof lab.b === 'number' && lab.b >= -128 && lab.b <= 127
    );
  }
  
  /**
   * Clamp RGB values to valid range
   * @param rgb - RGB color object
   * @returns RGB object with clamped values
   */
  clampRgb(rgb: RGB): RGB {
    return {
      r: Math.round(Math.max(0, Math.min(255, rgb.r))),
      g: Math.round(Math.max(0, Math.min(255, rgb.g))),
      b: Math.round(Math.max(0, Math.min(255, rgb.b)))
    };
  }
  
  /**
   * Clamp CMYK values to valid range
   * @param cmyk - CMYK color object
   * @returns CMYK object with clamped values
   */
  clampCmyk(cmyk: CMYK): CMYK {
    return {
      c: Math.round(Math.max(0, Math.min(100, cmyk.c))),
      m: Math.round(Math.max(0, Math.min(100, cmyk.m))),
      y: Math.round(Math.max(0, Math.min(100, cmyk.y))),
      k: Math.round(Math.max(0, Math.min(100, cmyk.k)))
    };
  }
  
  /**
   * Performance and Debugging Functions
   */
  
  /**
   * Get service information for debugging
   * @returns Service information object
   */
  getServiceInfo(): { name: string; version: string; supportedSpaces: string[] } {
    return {
      name: 'ColorSpaceCalculator',
      version: '1.0.0',
      supportedSpaces: ['RGB', 'HSL', 'CMYK', 'LAB', 'HEX']
    };
  }
  
  /**
   * Test color space calculation performance
   * @param iterations - Number of iterations to test
   * @returns Performance metrics
   */
  testPerformance(iterations: number = 1000): { 
    totalTime: number; 
    averageTime: number; 
    iterationsPerSecond: number 
  } {
    const testHex = '#FF5733';
    
    // Use appropriate timing API based on environment
    const now = () => {
      try {
        if (typeof performance !== 'undefined' && performance?.now) {
          return performance.now();
        }
      } catch {
        // Fall through to Date.now()
      }
      return Date.now();
    };
    
    const start = now();
    
    for (let i = 0; i < iterations; i++) {
      this.getAllColorSpaces(testHex);
    }
    
    const end = now();
    const totalTime = end - start;
    const averageTime = totalTime / iterations;
    const iterationsPerSecond = 1000 / averageTime;
    
    return {
      totalTime,
      averageTime,
      iterationsPerSecond
    };
  }
}