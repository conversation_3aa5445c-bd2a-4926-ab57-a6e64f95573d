/**
 * @file organization-handlers-thin.ts
 * @description Thin IPC handlers for organization operations using OrganizationService
 *
 * This file implements thin handlers that delegate to specialized organization services:
 * - OrganizationService: Main organization operations (CRUD, members, invitations)
 * - OrganizationMemberService: Member management operations (if extracted)
 *
 * Each handler focuses on:
 * 1. Authentication validation using OAuthService
 * 2. Organization context validation where appropriate (some operations don't need org context)
 * 3. Input parameter validation
 * 4. Delegation to appropriate service methods
 * 5. Standardized response formatting
 * 6. Minimal business logic (just coordination and error handling)
 */

import { ipcMain } from 'electron';
import { registerHandlerSafely } from '../utils/ipcRegistry';
import {
  getCurrentOrganization,
  setCurrentOrganization,
} from '../utils/organization-context';
import type {
  Organization,
  OrganizationMember,
  CreateOrganizationRequest,
  InviteMemberRequest,
  UpdateMemberRoleRequest,
} from '../../shared/types/organization.types';

// ===== SERVICE INTERFACES =====
// These interfaces define the expected contracts for the organization services

/**
 * Interface for OrganizationService - handles core organization operations
 */
interface IOrganizationService {
  // Organization CRUD
  createOrganization(name: string, ownerId: string): Promise<Organization>;
  getOrganizationsForUser(userId: string): Promise<Organization[]>;
  getOrganization(orgId: string): Promise<Organization | null>;
  updateOrganization(
    orgId: string,
    updates: Partial<Organization>
  ): Promise<Organization | null>;
  deleteOrganization(
    orgId: string,
    userId: string,
    forceCascade?: boolean
  ): Promise<boolean>;

  // Member operations
  getMembers(orgId: string): Promise<OrganizationMember[]>;
  addMember(
    orgId: string,
    userId: string,
    role: 'admin' | 'member',
    invitedBy?: string
  ): Promise<OrganizationMember>;
  updateMemberRole(
    orgId: string,
    userId: string,
    role: 'admin' | 'member'
  ): Promise<OrganizationMember | null>;
  removeMember(orgId: string, userId: string): Promise<boolean>;
  isMember(orgId: string, userId: string): Promise<boolean>;
  getMember(orgId: string, userId: string): Promise<OrganizationMember | null>;
  getUserRole(orgId: string, userId: string): Promise<string | null>;

  // Invitation operations
  inviteMember(
    orgId: string,
    email: string,
    role: 'admin' | 'member',
    invitedById: string
  ): Promise<{ success: boolean; invitation?: any; error?: string }>;
  acceptInvitation(
    token: string,
    userId: string
  ): Promise<{ success: boolean; organization?: Organization; error?: string }>;
  getPendingInvitations(orgId: string): Promise<any[]>;
  revokeInvitation(orgId: string, invitationId: string): Promise<boolean>;

  // Sync operations
  syncOrganizationsFromSupabase(userId: string): Promise<Organization[]>;
  syncMembersFromSupabase(orgId: string): Promise<void>;
  syncUserProfileToLocal(
    userId: string,
    email?: string,
    fullName?: string
  ): Promise<void>;
}

/**
 * Interface for OAuth service - handles authentication
 */
interface IOAuthService {
  getCurrentUser(): Promise<{ id: string; email?: string } | null>;
}

// ===== RESPONSE TYPES =====

interface IPCResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  userMessage?: string;
  timestamp: number;
}

// ===== UTILITY FUNCTIONS =====

/**
 * Helper function to safely extract error message
 */
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return String(error);
}

/**
 * Create standardized success response
 */
function createSuccessResponse<T>(
  data?: T,
  userMessage?: string
): IPCResponse<T> {
  return {
    success: true,
    data,
    userMessage,
    timestamp: Date.now(),
  };
}

/**
 * Create standardized error response
 */
function createErrorResponse(
  error: unknown,
  userMessage?: string
): IPCResponse {
  const errorMessage = getErrorMessage(error);

  return {
    success: false,
    error: errorMessage,
    userMessage:
      userMessage || 'An unexpected error occurred. Please try again.',
    timestamp: Date.now(),
  };
}

/**
 * Validate that a user is authenticated
 */
async function validateAuthentication(
  oauthService: IOAuthService
): Promise<{
  isValid: boolean;
  user?: { id: string; email?: string };
  error?: IPCResponse;
}> {
  try {
    const user = await oauthService.getCurrentUser();
    if (!user?.id) {
      return {
        isValid: false,
        error: createErrorResponse(
          'User not authenticated',
          'Please sign in to continue.'
        ),
      };
    }
    return { isValid: true, user };
  } catch (error) {
    return {
      isValid: false,
      error: createErrorResponse(
        error,
        'Authentication failed. Please try signing in again.'
      ),
    };
  }
}

/**
 * Validate organization exists (for operations that need org context)
 */
async function validateOrganizationExists(
  organizationService: IOrganizationService,
  orgId: string
): Promise<{
  isValid: boolean;
  organization?: Organization;
  error?: IPCResponse;
}> {
  try {
    const organization = await organizationService.getOrganization(orgId);
    if (!organization) {
      return {
        isValid: false,
        error: createErrorResponse(
          'Organization not found',
          'The selected organization could not be found.'
        ),
      };
    }
    return { isValid: true, organization };
  } catch (error) {
    return {
      isValid: false,
      error: createErrorResponse(error, 'Failed to validate organization.'),
    };
  }
}

// ===== IPC CHANNEL NAMES =====

export enum OrganizationChannels {
  CREATE = 'organization:create',
  GET_ALL = 'organization:getAll',
  GET_BY_ID = 'organization:getById',
  UPDATE = 'organization:update',
  DELETE = 'organization:delete',
  GET_CURRENT = 'organization:getCurrent',
  SET_CURRENT = 'organization:setCurrent',
  GET_MEMBERS = 'organization:getMembers',
  ADD_MEMBER = 'organization:addMember',
  UPDATE_MEMBER_ROLE = 'organization:updateMemberRole',
  REMOVE_MEMBER = 'organization:removeMember',
  INVITE_MEMBER = 'organization:inviteMember',
  ACCEPT_INVITATION = 'organization:acceptInvitation',
  GET_PENDING_INVITATIONS = 'organization:getPendingInvitations',
  REVOKE_INVITATION = 'organization:revokeInvitation',
}

// ===== THIN HANDLERS =====

/**
 * Register all organization IPC handlers with dependency injection
 */
export function registerOrganizationHandlersThin(
  organizationService: IOrganizationService,
  oauthService: IOAuthService
): void {
  console.log(
    '[OrganizationHandlersThin] 🚀 Registering thin organization handlers...'
  );

  // ===== ORGANIZATION CRUD OPERATIONS =====

  /**
   * Create new organization
   * No org context validation needed - this establishes the context
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.CREATE,
    async (
      _event,
      data: CreateOrganizationRequest
    ): Promise<IPCResponse<Organization>> => {
      try {
        console.log(
          '[OrganizationHandlersThin] Creating organization:',
          data.name
        );

        // Validate authentication
        const authResult = await validateAuthentication(oauthService);
        if (!authResult.isValid) {
          return authResult.error!;
        }

        // Validate input
        if (!data.name?.trim()) {
          return createErrorResponse(
            'Organization name is required',
            'Please enter a valid organization name.'
          );
        }

        // Delegate to service
        const organization = await organizationService.createOrganization(
          data.name.trim(),
          authResult.user!.id
        );

        // Set as current organization
        setCurrentOrganization(organization.external_id);

        console.log(
          '[OrganizationHandlersThin] Organization created successfully:',
          organization.external_id
        );
        return createSuccessResponse(
          organization,
          'Organization created successfully!'
        );
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error creating organization:',
          error
        );
        return createErrorResponse(
          error,
          'Failed to create organization. Please try again.'
        );
      }
    }
  );

  /**
   * Get all organizations for current user
   * No org context validation needed - this lists available contexts
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.GET_ALL,
    async (_event): Promise<IPCResponse<Organization[]>> => {
      try {
        console.log(
          '[OrganizationHandlersThin] 🔍 Getting all organizations for user'
        );

        // Validate authentication
        const authResult = await validateAuthentication(oauthService);
        if (!authResult.isValid) {
          console.log(
            '[OrganizationHandlersThin] ❌ Authentication validation failed:',
            authResult.error
          );
          return authResult.error!;
        }

        console.log(
          '[OrganizationHandlersThin] ✅ Authentication validated for user:',
          authResult.user!.id
        );

        // Delegate to service (includes Supabase sync)
        const organizations = await organizationService.getOrganizationsForUser(
          authResult.user!.id
        );

        console.log(
          `[OrganizationHandlersThin] 📊 Found ${organizations.length} organizations for user:`,
          organizations.map(org => ({ id: org.external_id, name: org.name }))
        );

        const response = createSuccessResponse(organizations);
        console.log('[OrganizationHandlersThin] 📤 Returning response:', {
          success: response.success,
          dataLength: response.data?.length,
          hasError: !!response.error,
        });

        return response;
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] ❌ Error getting organizations:',
          error
        );
        return createErrorResponse(
          error,
          'Failed to load organizations. Please try again.'
        );
      }
    }
  );

  /**
   * Get organization by ID
   * No org context validation needed - this can be used to validate org context
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.GET_BY_ID,
    async (
      _event,
      organizationId: string
    ): Promise<IPCResponse<Organization>> => {
      try {
        // Validate input
        if (!organizationId?.trim()) {
          return createErrorResponse(
            'Organization ID is required',
            'Invalid organization ID provided.'
          );
        }

        // Delegate to service
        const organization = await organizationService.getOrganization(
          organizationId.trim()
        );

        if (!organization) {
          return createErrorResponse(
            'Organization not found',
            'The requested organization could not be found.'
          );
        }

        return createSuccessResponse(organization);
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error getting organization:',
          error
        );
        return createErrorResponse(
          error,
          'Failed to load organization details.'
        );
      }
    }
  );

  /**
   * Update organization
   * Requires org context validation
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.UPDATE,
    async (
      _event,
      organizationId: string,
      updates: Partial<Organization>
    ): Promise<IPCResponse<Organization>> => {
      try {
        // Validate input
        if (!organizationId?.trim()) {
          return createErrorResponse(
            'Organization ID is required',
            'Invalid organization ID provided.'
          );
        }

        if (!updates || Object.keys(updates).length === 0) {
          return createErrorResponse(
            'No updates provided',
            'Please provide updates to apply.'
          );
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        // Delegate to service
        const updatedOrganization =
          await organizationService.updateOrganization(organizationId, updates);

        if (!updatedOrganization) {
          return createErrorResponse(
            'Update failed',
            'Failed to update organization. Please try again.'
          );
        }

        return createSuccessResponse(
          updatedOrganization,
          'Organization updated successfully!'
        );
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error updating organization:',
          error
        );
        return createErrorResponse(error, 'Failed to update organization.');
      }
    }
  );

  /**
   * Delete organization
   * Requires auth validation and org existence check
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.DELETE,
    async (
      _event,
      organizationId: string,
      forceCascade: boolean = false
    ): Promise<IPCResponse<boolean>> => {
      try {
        // Validate input
        if (!organizationId?.trim()) {
          return createErrorResponse(
            'Organization ID is required',
            'Invalid organization ID provided.'
          );
        }

        // Validate authentication
        const authResult = await validateAuthentication(oauthService);
        if (!authResult.isValid) {
          return authResult.error!;
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        console.log(
          '[OrganizationHandlersThin] Deleting organization:',
          organizationId,
          'forceCascade:',
          forceCascade
        );

        // Delegate to service
        const success = await organizationService.deleteOrganization(
          organizationId,
          authResult.user!.id,
          forceCascade
        );

        if (!success) {
          return createErrorResponse(
            'Delete failed',
            'Failed to delete organization. Please try again.'
          );
        }

        // Clear current organization if it was deleted
        const currentOrgId = getCurrentOrganization();
        if (currentOrgId === organizationId) {
          setCurrentOrganization(null);
        }

        return createSuccessResponse(
          true,
          'Organization deleted successfully!'
        );
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error deleting organization:',
          error
        );
        return createErrorResponse(error, 'Failed to delete organization.');
      }
    }
  );

  // ===== ORGANIZATION CONTEXT OPERATIONS =====

  /**
   * Get current organization
   * No org context validation needed - this retrieves the current context
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.GET_CURRENT,
    async (_event): Promise<IPCResponse<Organization>> => {
      try {
        const currentOrganizationId = getCurrentOrganization();

        if (!currentOrganizationId) {
          return createErrorResponse(
            'No organization selected',
            'Please select an organization to continue.'
          );
        }

        // Delegate to service to get full org details
        const organization = await organizationService.getOrganization(
          currentOrganizationId
        );

        if (!organization) {
          // Clear invalid org context
          setCurrentOrganization(null);
          return createErrorResponse(
            'Current organization not found',
            'The selected organization no longer exists.'
          );
        }

        return createSuccessResponse(organization);
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error getting current organization:',
          error
        );
        return createErrorResponse(
          error,
          'Failed to load current organization.'
        );
      }
    }
  );

  /**
   * Set current organization
   * No org context validation needed - this sets the context
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.SET_CURRENT,
    async (
      _event,
      organizationId: string
    ): Promise<IPCResponse<Organization>> => {
      try {
        // Validate input
        if (!organizationId?.trim()) {
          return createErrorResponse(
            'Organization ID is required',
            'Invalid organization ID provided.'
          );
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        console.log(
          '[OrganizationHandlersThin] Setting current organization to:',
          organizationId
        );

        // Set organization context
        setCurrentOrganization(organizationId);

        console.log(
          '[OrganizationHandlersThin] Current organization set successfully'
        );
        return createSuccessResponse(
          orgResult.organization!,
          'Organization selected successfully!'
        );
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error setting current organization:',
          error
        );
        return createErrorResponse(error, 'Failed to select organization.');
      }
    }
  );

  // ===== MEMBER MANAGEMENT OPERATIONS =====

  /**
   * Get organization members
   * Requires org validation
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.GET_MEMBERS,
    async (
      _event,
      organizationId: string
    ): Promise<IPCResponse<OrganizationMember[]>> => {
      try {
        // Validate input
        if (!organizationId?.trim()) {
          return createErrorResponse(
            'Organization ID is required',
            'Invalid organization ID provided.'
          );
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        // Delegate to service (includes Supabase sync)
        const members = await organizationService.getMembers(organizationId);

        return createSuccessResponse(members);
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error getting members:',
          error
        );
        return createErrorResponse(
          error,
          'Failed to load organization members.'
        );
      }
    }
  );

  /**
   * Add member to organization
   * Requires org validation and auth
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.ADD_MEMBER,
    async (
      _event,
      organizationId: string,
      userId: string,
      role: string,
      invitedBy?: string
    ): Promise<IPCResponse<OrganizationMember>> => {
      try {
        // Validate input
        if (!organizationId?.trim() || !userId?.trim() || !role?.trim()) {
          return createErrorResponse(
            'Missing required parameters',
            'Organization ID, user ID, and role are required.'
          );
        }

        if (!['admin', 'member'].includes(role)) {
          return createErrorResponse(
            'Invalid role',
            'Role must be either "admin" or "member".'
          );
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        // Delegate to service
        const member = await organizationService.addMember(
          organizationId,
          userId,
          role as 'admin' | 'member',
          invitedBy
        );

        return createSuccessResponse(member, 'Member added successfully!');
      } catch (error) {
        console.error('[OrganizationHandlersThin] Error adding member:', error);
        return createErrorResponse(
          error,
          'Failed to add member to organization.'
        );
      }
    }
  );

  /**
   * Update member role
   * Requires org validation
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.UPDATE_MEMBER_ROLE,
    async (
      _event,
      data: UpdateMemberRoleRequest & { organizationId: string }
    ): Promise<IPCResponse<OrganizationMember>> => {
      try {
        // Validate input
        if (
          !data.organizationId?.trim() ||
          !data.userId?.trim() ||
          !data.role?.trim()
        ) {
          return createErrorResponse(
            'Missing required parameters',
            'Organization ID, user ID, and role are required.'
          );
        }

        if (!['admin', 'member'].includes(data.role)) {
          return createErrorResponse(
            'Invalid role',
            'Role must be either "admin" or "member".'
          );
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          data.organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        // Delegate to service
        const updatedMember = await organizationService.updateMemberRole(
          data.organizationId,
          data.userId,
          data.role as 'admin' | 'member'
        );

        if (!updatedMember) {
          return createErrorResponse(
            'Update failed',
            'Failed to update member role. The member may not exist.'
          );
        }

        return createSuccessResponse(
          updatedMember,
          'Member role updated successfully!'
        );
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error updating member role:',
          error
        );
        return createErrorResponse(error, 'Failed to update member role.');
      }
    }
  );

  /**
   * Remove member from organization
   * Requires org validation
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.REMOVE_MEMBER,
    async (
      _event,
      data: { organizationId: string; userId: string }
    ): Promise<IPCResponse<boolean>> => {
      try {
        // Validate input
        if (!data.organizationId?.trim() || !data.userId?.trim()) {
          return createErrorResponse(
            'Missing required parameters',
            'Organization ID and user ID are required.'
          );
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          data.organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        // Delegate to service
        const success = await organizationService.removeMember(
          data.organizationId,
          data.userId
        );

        if (!success) {
          return createErrorResponse(
            'Remove failed',
            'Failed to remove member. The member may not exist.'
          );
        }

        return createSuccessResponse(true, 'Member removed successfully!');
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error removing member:',
          error
        );
        return createErrorResponse(
          error,
          'Failed to remove member from organization.'
        );
      }
    }
  );

  // ===== INVITATION OPERATIONS =====

  /**
   * Invite member to organization
   * Requires auth and org validation
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.INVITE_MEMBER,
    async (
      _event,
      data: InviteMemberRequest & { organizationId: string }
    ): Promise<IPCResponse> => {
      try {
        // Validate input
        if (
          !data.organizationId?.trim() ||
          !data.email?.trim() ||
          !data.role?.trim()
        ) {
          return createErrorResponse(
            'Missing required parameters',
            'Organization ID, email, and role are required.'
          );
        }

        if (!['admin', 'member'].includes(data.role)) {
          return createErrorResponse(
            'Invalid role',
            'Role must be either "admin" or "member".'
          );
        }

        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
          return createErrorResponse(
            'Invalid email',
            'Please enter a valid email address.'
          );
        }

        // Validate authentication
        const authResult = await validateAuthentication(oauthService);
        if (!authResult.isValid) {
          return authResult.error!;
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          data.organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        console.log('[OrganizationHandlersThin] Inviting member:', data);

        // Delegate to service
        const result = await organizationService.inviteMember(
          data.organizationId,
          data.email,
          data.role as 'admin' | 'member',
          authResult.user!.id
        );

        if (!result.success) {
          return createErrorResponse(
            result.error || 'Unknown error',
            'Failed to send invitation.'
          );
        }

        return createSuccessResponse(
          result.invitation,
          'Invitation sent successfully!'
        );
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error inviting member:',
          error
        );
        return createErrorResponse(error, 'Failed to send invitation.');
      }
    }
  );

  /**
   * Accept invitation
   * Requires auth but no org context (invitation provides context)
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.ACCEPT_INVITATION,
    async (_event, token: string): Promise<IPCResponse> => {
      try {
        // Validate input
        if (!token?.trim()) {
          return createErrorResponse(
            'Invitation token is required',
            'Invalid invitation token provided.'
          );
        }

        // Validate authentication
        const authResult = await validateAuthentication(oauthService);
        if (!authResult.isValid) {
          return authResult.error!;
        }

        // Delegate to service
        const result = await organizationService.acceptInvitation(
          token,
          authResult.user!.id
        );

        if (!result.success) {
          return createErrorResponse(
            result.error || 'Unknown error',
            'Failed to accept invitation.'
          );
        }

        return createSuccessResponse(
          result.organization,
          'Invitation accepted successfully!'
        );
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error accepting invitation:',
          error
        );
        return createErrorResponse(error, 'Failed to accept invitation.');
      }
    }
  );

  /**
   * Get pending invitations for organization
   * Requires org validation
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.GET_PENDING_INVITATIONS,
    async (_event, organizationId: string): Promise<IPCResponse<any[]>> => {
      try {
        // Validate input
        if (!organizationId?.trim()) {
          return createErrorResponse(
            'Organization ID is required',
            'Invalid organization ID provided.'
          );
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        // Delegate to service
        const invitations =
          await organizationService.getPendingInvitations(organizationId);

        return createSuccessResponse(invitations);
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error getting pending invitations:',
          error
        );
        return createErrorResponse(
          error,
          'Failed to load pending invitations.'
        );
      }
    }
  );

  /**
   * Revoke invitation
   * Requires org validation
   */
  registerHandlerSafely(
    ipcMain,
    OrganizationChannels.REVOKE_INVITATION,
    async (
      _event,
      data: { organizationId: string; invitationId: string }
    ): Promise<IPCResponse<boolean>> => {
      try {
        // Validate input
        if (!data.organizationId?.trim() || !data.invitationId?.trim()) {
          return createErrorResponse(
            'Missing required parameters',
            'Organization ID and invitation ID are required.'
          );
        }

        // Validate organization exists
        const orgResult = await validateOrganizationExists(
          organizationService,
          data.organizationId
        );
        if (!orgResult.isValid) {
          return orgResult.error!;
        }

        // Delegate to service
        const success = await organizationService.revokeInvitation(
          data.organizationId,
          data.invitationId
        );

        if (!success) {
          return createErrorResponse(
            'Revoke failed',
            'Failed to revoke invitation. The invitation may not exist.'
          );
        }

        return createSuccessResponse(true, 'Invitation revoked successfully!');
      } catch (error) {
        console.error(
          '[OrganizationHandlersThin] Error revoking invitation:',
          error
        );
        return createErrorResponse(error, 'Failed to revoke invitation.');
      }
    }
  );

  console.log(
    '[OrganizationHandlersThin] 🎉 Thin organization handler registration completed'
  );
}
