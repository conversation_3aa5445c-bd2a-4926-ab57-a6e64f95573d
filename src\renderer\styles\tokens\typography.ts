/**
 * @file typography.ts
 * @description Typography tokens for the design system
 *
 * This file defines all typography-related tokens used throughout the application:
 * - fontFamily: Font stacks for different typographic styles
 * - fontSize: Type scale for consistent sizing
 * - fontWeight: Font weights for creating hierarchy
 * - lineHeight: Line spacing for optimal readability
 *
 * The typography system is Apple-inspired with a focus on clarity and readability.
 *
 * @example
 * // Access typography tokens in component via useTokens hook
 * const { typography } = useTokens();
 * const bodyFont = typography.fontFamily.sans.join(', ');
 * const largeText = typography.fontSize.xl;
 *
 * // Use as CSS variables in Tailwind classes
 * <h2 className="text-[var(--font-size-xl)] font-[var(--font-weight-semibold)]">Heading</h2>
 */

import { TypographyTokens } from './types';

/**
 * Typography token definitions for the application
 */
export const typography: TypographyTokens = {
  /**
   * Font families define the typefaces used in the application
   * Each category provides a fallback stack of fonts
   */
  fontFamily: {
    /**
     * Primary sans-serif font stack
     * Uses SF Pro on Apple devices with appropriate fallbacks for other platforms
     */
    sans: [
      '-apple-system',
      'BlinkMacSystemFont',
      'SF Pro Text',
      'SF Pro Display',
      'Helvetica Neue',
      'Helvetica',
      'Arial',
      'sans-serif',
    ],

    /**
     * Monospace font stack for code and tabular data
     * Uses SF Mono on Apple devices with appropriate fallbacks
     */
    mono: [
      'SF Mono',
      'Menlo',
      'Monaco',
      'Consolas',
      'Liberation Mono',
      'Courier New',
      'monospace',
    ],
  },

  /**
   * Font sizes follow a modular scale for consistency and hierarchy
   * Values are in rem units for accessibility and responsive scaling
   */
  fontSize: {
    /** Extra small text (12px) - Legal text, footnotes, and small labels */
    xs: '0.75rem',
    /** Small text (14px) - Secondary content, captions, input text */
    sm: '0.875rem',
    /** Base text (16px) - Standard body text and default size */
    base: '1rem',
    /** Large text (18px) - Enhanced readability for important body text */
    lg: '1.125rem',
    /** Extra large text (20px) - Subheadings and emphasized content */
    xl: '1.25rem',
    /** 2X large text (24px) - Section headers and card titles */
    '2xl': '1.5rem',
    /** 3X large text (30px) - Major section headers and important titles */
    '3xl': '1.875rem',
    /** 4X large text (36px) - Page titles and hero elements */
    '4xl': '2.25rem',
  },

  /**
   * Font weights define the thickness of the text
   * Values follow standard CSS weight conventions
   */
  fontWeight: {
    /** Normal weight (400) - Standard body text */
    normal: '400',
    /** Medium weight (500) - Slightly emphasized text, UI labels */
    medium: '500',
    /** Semi-bold weight (600) - Subheadings, important labels */
    semibold: '600',
    /** Bold weight (700) - Headlines, buttons, strong emphasis */
    bold: '700',
  },

  /**
   * Line heights define the vertical spacing between lines of text
   * Values are unitless multipliers of the font size
   */
  lineHeight: {
    /** No additional line spacing (1) - For headings and single-line text */
    none: '1',
    /** Tight line spacing (1.25) - For dense text blocks where space is constrained */
    tight: '1.25',
    /** Snug line spacing (1.375) - For readable text blocks in compact spaces */
    snug: '1.375',
    /** Normal line spacing (1.5) - Balanced spacing for body text */
    normal: '1.5',
    /** Relaxed line spacing (1.625) - Enhanced readability for longer text */
    relaxed: '1.625',
    /** Loose line spacing (2) - Maximum readability for complex content */
    loose: '2',
  },
};
