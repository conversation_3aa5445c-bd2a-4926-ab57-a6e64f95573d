/**
 * @file CachePerformancePanel.tsx
 * @description Debug panel component for monitoring cache performance
 */

import React from 'react';
import { useCacheMonitoring } from '../../hooks/useCacheMonitoring';

interface CachePerformancePanelProps {
  className?: string;
  refreshInterval?: number;
}

/**
 * Debug panel component for monitoring cache performance in real-time
 * Shows cache hit rates, API call reduction, memory usage, and other metrics
 */
export const CachePerformancePanel: React.FC<CachePerformancePanelProps> = ({
  className = '',
  refreshInterval = 3000
}) => {
  const {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    refreshMetrics,
    logPerformanceSummary,
    resetMetrics
  } = useCacheMonitoring(refreshInterval);

  if (!metrics) {
    return (
      <div className={`p-4 bg-gray-100 dark:bg-gray-800 rounded-lg ${className}`}>
        <h3 className="text-lg font-semibold mb-2">Cache Performance</h3>
        <p className="text-gray-600 dark:text-gray-400">Loading metrics...</p>
      </div>
    );
  }

  // Calculate performance indicators
  const isPerformanceGood = metrics.hitRate >= 80;
  const isApiReductionGood = parseInt(metrics.apiCallReduction) >= 50;
  const lastUpdateAge = Date.now() - metrics.lastUpdate;

  return (
    <div className={`p-4 bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Cache Performance Monitor
        </h3>
        <div className="flex space-x-2">
          <button
            onClick={isMonitoring ? stopMonitoring : startMonitoring}
            className={`px-3 py-1 text-xs rounded ${
              isMonitoring 
                ? 'bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-300' 
                : 'bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-300'
            }`}
          >
            {isMonitoring ? 'Stop' : 'Start'}
          </button>
          <button
            onClick={refreshMetrics}
            className="px-3 py-1 text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 rounded"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className={`text-2xl font-bold ${isPerformanceGood ? 'text-green-600' : 'text-red-600'}`}>
            {metrics.hitRate.toFixed(1)}%
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Hit Rate</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {metrics.deduplicatedRequests}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Deduplicated</div>
        </div>
        
        <div className="text-center">
          <div className={`text-2xl font-bold ${isApiReductionGood ? 'text-green-600' : 'text-yellow-600'}`}>
            {metrics.apiCallReduction}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">API Reduction</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {metrics.averageResponseTime}ms
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Avg Response</div>
        </div>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="space-y-2">
          <h4 className="font-semibold text-gray-900 dark:text-white">Request Stats</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Total Requests:</span>
              <span className="font-medium text-gray-900 dark:text-white">{metrics.totalRequests}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Cache Hits:</span>
              <span className="font-medium text-green-600">{metrics.hits}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Cache Misses:</span>
              <span className="font-medium text-red-600">{metrics.misses}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">In Flight:</span>
              <span className="font-medium text-blue-600">{metrics.inFlightCount}</span>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-semibold text-gray-900 dark:text-white">Cache Stats</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Cache Size:</span>
              <span className="font-medium text-gray-900 dark:text-white">{metrics.cacheSize} entries</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Memory Usage:</span>
              <span className="font-medium text-purple-600">{metrics.memoryUsage}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Last Update:</span>
              <span className="font-medium text-gray-600 dark:text-gray-400">
                {lastUpdateAge < 1000 ? 'Just now' : `${Math.round(lastUpdateAge / 1000)}s ago`}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Monitoring:</span>
              <span className={`font-medium ${isMonitoring ? 'text-green-600' : 'text-gray-500'}`}>
                {isMonitoring ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-4">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Performance Insights</h4>
        <div className="space-y-1 text-xs">
          {metrics.hitRate >= 90 && (
            <div className="text-green-700 dark:text-green-300">
              🚀 Excellent cache performance! Very few API calls needed.
            </div>
          )}
          {metrics.hitRate >= 70 && metrics.hitRate < 90 && (
            <div className="text-blue-700 dark:text-blue-300">
              ✅ Good cache performance. Most requests served from cache.
            </div>
          )}
          {metrics.hitRate >= 50 && metrics.hitRate < 70 && (
            <div className="text-yellow-700 dark:text-yellow-300">
              ⚠️ Moderate cache performance. Consider increasing TTL.
            </div>
          )}
          {metrics.hitRate < 50 && metrics.totalRequests > 5 && (
            <div className="text-red-700 dark:text-red-300">
              ❌ Poor cache performance. Check cache invalidation logic.
            </div>
          )}
          {metrics.deduplicatedRequests > 0 && (
            <div className="text-purple-700 dark:text-purple-300">
              🔄 {metrics.deduplicatedRequests} duplicate requests prevented by deduplication.
            </div>
          )}
          {metrics.averageResponseTime < 5 && (
            <div className="text-green-700 dark:text-green-300">
              ⚡ Lightning fast response times from cache.
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        <button
          onClick={logPerformanceSummary}
          className="px-3 py-1 text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 rounded"
        >
          Log Summary
        </button>
        <button
          onClick={resetMetrics}
          className="px-3 py-1 text-xs bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-300 rounded"
        >
          Reset Metrics
        </button>
      </div>

      {/* Target Performance Information */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Target Performance</h5>
        <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <div>• Hit Rate: 80%+ (Excellent: 90%+)</div>
          <div>• API Call Reduction: 50%+ (Excellent: 75%+)</div>
          <div>• Response Time: &lt;10ms from cache</div>
          <div>• Goal: Reduce 529+ API calls to &lt;5 per session</div>
        </div>
      </div>
    </div>
  );
};

export default CachePerformancePanel;