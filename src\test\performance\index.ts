/**
 * Performance Testing Module
 * Export all performance testing utilities
 */

export { generateTestColors } from './generateTestData';
export type { GeneratorOptions } from './generateTestData';

export { PerformanceBenchmark, runPerformanceBenchmarks } from './benchmark';
export type { BenchmarkResult } from './benchmark';

export { PerformanceProfiler, analyzePerformance } from './PerformanceProfiler';
export type {
  PerformanceMetrics,
  PerformanceReport,
} from './PerformanceProfiler';

export {
  PerformanceTestRoute,
  quickPerformanceCheck,
  usePerformanceMonitor,
} from './integration';

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  rendering: {
    excellent: 8, // < 8ms (120 FPS)
    good: 16.67, // < 16.67ms (60 FPS)
    acceptable: 33, // < 33ms (30 FPS)
    poor: 50, // > 50ms
  },
  search: {
    excellent: 50, // < 50ms
    good: 100, // < 100ms
    acceptable: 300, // < 300ms
    poor: 500, // > 500ms
  },
  memory: {
    excellent: 100, // < 100MB
    good: 300, // < 300MB
    acceptable: 500, // < 500MB
    poor: 1000, // > 1GB
  },
  export: {
    excellent: 500, // < 500ms
    good: 1000, // < 1s
    acceptable: 2000, // < 2s
    poor: 5000, // > 5s
  },
};

// Quick performance assessment
export function assessPerformance(
  metric: number,
  type: keyof typeof PERFORMANCE_THRESHOLDS
): string {
  const thresholds = PERFORMANCE_THRESHOLDS[type];

  if (metric <= thresholds.excellent) {
    return '🟢 Excellent';
  }
  if (metric <= thresholds.good) {
    return '🟢 Good';
  }
  if (metric <= thresholds.acceptable) {
    return '🟡 Acceptable';
  }
  return '🔴 Poor';
}
