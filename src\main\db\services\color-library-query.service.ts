/**
 * Color Library Query Service
 * Enhanced with efficient loader integration for better performance
 *
 * This service now uses the ColorLibraryLoaderService for optimized loading
 * while maintaining backward compatibility with database queries.
 */

import { executeWithPool } from '../core/connection';
import {
  LIBRARY_CODES,
  type LibraryColor,
} from '../schemas/color-libraries-schema';
import { getColorLibraryLoaderService } from '../../services/color-library-loader.service';
import { ColorEntry } from '../../../shared/types/color.types';

export interface ColorLibrarySearchOptions {
  library?: string;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'code' | 'name' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

export interface ColorLibrarySearchResult {
  colors: LibraryColor[] | ColorEntry[];
  total: number;
  hasMore: boolean;
}

/**
 * Color library query service
 */
export class ColorLibraryQueryService {
  /**
   * Search colors across all libraries or specific library
   */
  async searchColors(
    options: ColorLibrarySearchOptions = {}
  ): Promise<ColorLibrarySearchResult> {
    const {
      library,
      search,
      limit = 50,
      offset = 0,
      sortBy = 'code',
      sortOrder = 'asc',
    } = options;

    return await executeWithPool(db => {
      let whereClause = 'WHERE lc.is_active = TRUE';
      const params: any[] = [];

      // Filter by library
      if (library) {
        whereClause += ' AND cl.code = ?';
        params.push(library);
      }

      // Search functionality
      if (search && search.trim()) {
        const searchTerm = `%${search.trim()}%`;
        whereClause +=
          ' AND (lc.name LIKE ? OR lc.code LIKE ? OR lc.notes LIKE ?)';
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // Sort clause
      let orderClause = '';
      switch (sortBy) {
        case 'name':
          orderClause = `ORDER BY lc.name ${sortOrder.toUpperCase()}`;
          break;
        case 'popularity':
          orderClause = `ORDER BY COALESCE(lcm.popularity_score, 0) ${sortOrder.toUpperCase()}, lc.code ASC`;
          break;
        case 'code':
        default:
          orderClause = `ORDER BY cl.code ASC, lc.sort_order ASC, lc.code ${sortOrder.toUpperCase()}`;
          break;
      }

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM library_colors lc
        INNER JOIN color_libraries cl ON lc.library_id = cl.id
        LEFT JOIN library_color_metadata lcm ON lc.id = lcm.color_id
        ${whereClause}
      `;

      const totalResult = db.prepare(countQuery).get(...params);
      const total = totalResult.total;

      // Get colors with pagination
      const colorsQuery = `
        SELECT 
          lc.id,
          lc.external_id,
          lc.library_id,
          cl.code as library_code,
          cl.name as library_name,
          lc.code,
          lc.name,
          lc.hex,
          lc.cmyk,
          lc.rgb,
          lc.lab,
          lc.hsl,
          lc.notes,
          lc.sort_order,
          COALESCE(lcm.popularity_score, 0) as popularity_score,
          COALESCE(lcm.usage_count, 0) as usage_count
        FROM library_colors lc
        INNER JOIN color_libraries cl ON lc.library_id = cl.id
        LEFT JOIN library_color_metadata lcm ON lc.id = lcm.color_id
        ${whereClause}
        ${orderClause}
        LIMIT ? OFFSET ?
      `;

      const colors = db.prepare(colorsQuery).all(...params, limit, offset);

      return {
        colors,
        total,
        hasMore: offset + colors.length < total,
      };
    });
  }

  /**
   * Get Pantone colors specifically
   */
  async getPantoneColors(
    options: Omit<ColorLibrarySearchOptions, 'library'> = {}
  ): Promise<ColorLibrarySearchResult> {
    return this.searchColors({ ...options, library: LIBRARY_CODES.PANTONE });
  }

  /**
   * Get RAL colors specifically
   */
  async getRalColors(
    options: Omit<ColorLibrarySearchOptions, 'library'> = {}
  ): Promise<ColorLibrarySearchResult> {
    return this.searchColors({ ...options, library: LIBRARY_CODES.RAL });
  }

  /**
   * Get color by external ID
   */
  async getColorByExternalId(externalId: string): Promise<LibraryColor | null> {
    return await executeWithPool(db => {
      const color = db
        .prepare(
          `
        SELECT 
          lc.id,
          lc.external_id,
          lc.library_id,
          cl.code as library_code,
          cl.name as library_name,
          lc.code,
          lc.name,
          lc.hex,
          lc.cmyk,
          lc.rgb,
          lc.lab,
          lc.hsl,
          lc.notes,
          lc.sort_order
        FROM library_colors lc
        INNER JOIN color_libraries cl ON lc.library_id = cl.id
        WHERE lc.external_id = ? AND lc.is_active = TRUE
      `
        )
        .get(externalId);

      return color || null;
    });
  }

  /**
   * Get color by library and code
   */
  async getColorByCode(
    libraryCode: string,
    colorCode: string
  ): Promise<LibraryColor | null> {
    return await executeWithPool(db => {
      const color = db
        .prepare(
          `
        SELECT 
          lc.id,
          lc.external_id,
          lc.library_id,
          cl.code as library_code,
          cl.name as library_name,
          lc.code,
          lc.name,
          lc.hex,
          lc.cmyk,
          lc.rgb,
          lc.lab,
          lc.hsl,
          lc.notes,
          lc.sort_order
        FROM library_colors lc
        INNER JOIN color_libraries cl ON lc.library_id = cl.id
        WHERE cl.code = ? AND lc.code = ? AND lc.is_active = TRUE
      `
        )
        .get(libraryCode, colorCode);

      return color || null;
    });
  }

  // ===== ENHANCED METHODS USING NEW LOADER SERVICE =====

  /**
   * Enhanced search using the new color library loader
   * Provides better performance and lazy loading capabilities
   */
  async searchColorsEnhanced(
    options: ColorLibrarySearchOptions = {}
  ): Promise<ColorLibrarySearchResult> {
    try {
      const loader = getColorLibraryLoaderService();

      if (options.library) {
        // Search in specific library using enhanced loader
        const result = await loader.searchLibraryColors(
          options.library,
          options.search || '',
          {
            limit: options.limit,
            offset: options.offset,
            sortBy: options.sortBy === 'popularity' ? 'name' : options.sortBy,
            sortOrder: options.sortOrder,
          }
        );

        return {
          colors: result.colors,
          total: result.total,
          hasMore: result.hasMore,
        };
      } else {
        // Search across all libraries
        const availableLibraries = loader.getAvailableLibraries();
        const allResults: ColorEntry[] = [];
        let totalCount = 0;

        for (const libraryCode of availableLibraries) {
          const result = await loader.searchLibraryColors(
            libraryCode,
            options.search || '',
            {
              limit: 1000, // Get more results to sort globally
              offset: 0,
              sortBy: options.sortBy === 'popularity' ? 'name' : options.sortBy,
              sortOrder: options.sortOrder,
            }
          );

          allResults.push(...result.colors);
          totalCount += result.total;
        }

        // Global sorting and pagination
        allResults.sort((a, b) => {
          const sortField = options.sortBy === 'name' ? 'name' : 'code';
          const comparison = a[sortField].localeCompare(b[sortField]);
          return options.sortOrder === 'desc' ? -comparison : comparison;
        });

        const offset = options.offset || 0;
        const limit = options.limit || 50;
        const paginatedResults = allResults.slice(offset, offset + limit);

        return {
          colors: paginatedResults,
          total: totalCount,
          hasMore: offset + paginatedResults.length < totalCount,
        };
      }
    } catch (error) {
      console.error(
        '[ColorLibraryQueryService] Enhanced search failed, falling back to database:',
        error
      );
      return this.searchColors(options);
    }
  }

  /**
   * Get library metadata for fast access
   */
  async getLibraryMetadata(libraryCode: string) {
    try {
      const loader = getColorLibraryLoaderService();
      return await loader.getLibraryMetadata(libraryCode);
    } catch (error) {
      console.error(
        '[ColorLibraryQueryService] Failed to get library metadata:',
        error
      );
      return null;
    }
  }

  /**
   * Get all available libraries with metadata
   */
  async getAvailableLibraries() {
    try {
      const loader = getColorLibraryLoaderService();
      const libraries = loader.getAvailableLibraries();

      const librariesWithMetadata = await Promise.all(
        libraries.map(async code => {
          const metadata = await loader.getLibraryMetadata(code);
          return metadata;
        })
      );

      return librariesWithMetadata.filter(Boolean);
    } catch (error) {
      console.error(
        '[ColorLibraryQueryService] Failed to get available libraries:',
        error
      );
      return [];
    }
  }

  /**
   * Load library in chunks for better performance
   */
  async loadLibraryChunk(
    libraryCode: string,
    startIndex: number,
    chunkSize: number = 100
  ) {
    try {
      const loader = getColorLibraryLoaderService();
      return await loader.loadLibraryChunk(libraryCode, startIndex, chunkSize);
    } catch (error) {
      console.error(
        '[ColorLibraryQueryService] Failed to load library chunk:',
        error
      );
      return null;
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats() {
    try {
      const loader = getColorLibraryLoaderService();
      return loader.getCacheStats();
    } catch (error) {
      console.error(
        '[ColorLibraryQueryService] Failed to get cache stats:',
        error
      );
      return null;
    }
  }

  /**
   * Clear cache for better memory management
   */
  clearCache(libraryCode?: string) {
    try {
      const loader = getColorLibraryLoaderService();
      loader.clearCache(libraryCode);
    } catch (error) {
      console.error('[ColorLibraryQueryService] Failed to clear cache:', error);
    }
  }

  /**
   * Full-text search across color names and codes
   */
  async fullTextSearch(
    query: string,
    options: ColorLibrarySearchOptions = {}
  ): Promise<ColorLibrarySearchResult> {
    const { library, limit = 50, offset = 0 } = options;

    return await executeWithPool(db => {
      let whereClause = '';
      const params: any[] = [query];

      if (library) {
        whereClause = 'AND cl.code = ?';
        params.push(library);
      }

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM library_colors_fts fts
        INNER JOIN library_colors lc ON fts.rowid = lc.id
        INNER JOIN color_libraries cl ON lc.library_id = cl.id
        WHERE library_colors_fts MATCH ? AND lc.is_active = TRUE ${whereClause}
      `;

      const totalResult = db.prepare(countQuery).get(...params);
      const total = totalResult.total;

      // Get colors with ranking
      const colorsQuery = `
        SELECT 
          lc.id,
          lc.external_id,
          lc.library_id,
          cl.code as library_code,
          cl.name as library_name,
          lc.code,
          lc.name,
          lc.hex,
          lc.cmyk,
          lc.rgb,
          lc.lab,
          lc.hsl,
          lc.notes,
          lc.sort_order,
          fts.rank
        FROM library_colors_fts fts
        INNER JOIN library_colors lc ON fts.rowid = lc.id
        INNER JOIN color_libraries cl ON lc.library_id = cl.id
        WHERE library_colors_fts MATCH ? AND lc.is_active = TRUE ${whereClause}
        ORDER BY fts.rank
        LIMIT ? OFFSET ?
      `;

      const colors = db.prepare(colorsQuery).all(...params, limit, offset);

      return {
        colors,
        total,
        hasMore: offset + colors.length < total,
      };
    });
  }

  /**
   * Get popular colors (most used)
   */
  async getPopularColors(
    libraryCode?: string,
    limit: number = 20
  ): Promise<LibraryColor[]> {
    return await executeWithPool(db => {
      let whereClause = 'WHERE lc.is_active = TRUE';
      const params: any[] = [];

      if (libraryCode) {
        whereClause += ' AND cl.code = ?';
        params.push(libraryCode);
      }

      const colors = db
        .prepare(
          `
        SELECT 
          lc.id,
          lc.external_id,
          lc.library_id,
          cl.code as library_code,
          cl.name as library_name,
          lc.code,
          lc.name,
          lc.hex,
          lc.cmyk,
          lc.notes,
          COALESCE(lcm.popularity_score, 0) as popularity_score,
          COALESCE(lcm.usage_count, 0) as usage_count
        FROM library_colors lc
        INNER JOIN color_libraries cl ON lc.library_id = cl.id
        LEFT JOIN library_color_metadata lcm ON lc.id = lcm.color_id
        ${whereClause}
        ORDER BY COALESCE(lcm.usage_count, 0) DESC, COALESCE(lcm.popularity_score, 0) DESC
        LIMIT ?
      `
        )
        .all(...params, limit);

      return colors;
    });
  }

  /**
   * Update color usage statistics
   */
  async incrementUsage(externalId: string): Promise<void> {
    await executeWithPool(db => {
      const transaction = db.transaction(() => {
        // Get color ID
        const color = db
          .prepare('SELECT id FROM library_colors WHERE external_id = ?')
          .get(externalId);
        if (!color) {
          return;
        }

        // Insert or update metadata
        db.prepare(
          `
          INSERT INTO library_color_metadata (color_id, usage_count)
          VALUES (?, 1)
          ON CONFLICT(color_id) DO UPDATE SET
            usage_count = usage_count + 1
        `
        ).run(color.id);
      });

      transaction();
    });
  }
}

// Singleton instance
let colorLibraryQueryService: ColorLibraryQueryService | null = null;

/**
 * Get singleton instance of color library query service
 */
export function getColorLibraryQueryService(): ColorLibraryQueryService {
  if (!colorLibraryQueryService) {
    colorLibraryQueryService = new ColorLibraryQueryService();
  }
  return colorLibraryQueryService;
}
