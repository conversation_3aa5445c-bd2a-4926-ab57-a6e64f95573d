/**
 * @file session-manager.ts
 * @description Manages user sessions, timeouts, and activity monitoring
 */

/// <reference types="node" />

import { BrowserWindow } from 'electron';
import { createSafeStore } from '../../utils/store-util';
import {
  LoggerFactory,
  logPerformance,
  logErrors,
  ILogger,
} from '../../utils/logger.service';

export interface SessionConfig {
  sessionTimeoutHours?: number;
  sessionWarningMinutes?: number;
  autoLogoutEnabled?: boolean;
  activityCheckIntervalMs?: number;
}

export interface SessionStatus {
  isActive: boolean;
  isExpired: boolean;
  timeUntilExpiry?: number;
  lastActivityTime: number;
  sessionStartTime?: number;
}

export interface SessionEvents {
  onSessionWarning?: (minutesRemaining: number) => void;
  onSessionExpired?: (reason: 'inactivity' | 'timeout') => void;
  onActivityDetected?: () => void;
}

/**
 * Session manager for handling user session lifecycle and monitoring
 */
export class SessionManager {
  private readonly logger: ILogger;
  private readonly store = createSafeStore<Record<string, any>>({
    name: 'session-manager',
  });

  // Session monitoring
  private sessionMonitoringInterval: NodeJS.Timeout | null = null;
  private lastActivityTime: number = Date.now();
  private warningShown: boolean = false;
  private sessionStartTime: number | null = null;

  // Configuration keys
  private readonly CONFIG_KEYS = {
    SESSION_TIMEOUT: 'session.timeoutHours',
    SESSION_WARNING: 'session.warningMinutes',
    AUTO_LOGOUT_ENABLED: 'session.autoLogoutEnabled',
    ACTIVITY_CHECK_INTERVAL: 'session.activityCheckIntervalMs',
  } as const;

  // Default configuration
  private readonly DEFAULT_CONFIG: Required<SessionConfig> = {
    sessionTimeoutHours: 24,
    sessionWarningMinutes: 5,
    autoLogoutEnabled: true,
    activityCheckIntervalMs: 60000, // 1 minute
  };

  // Events
  private events: SessionEvents = {};

  constructor(logger?: ILogger) {
    this.logger =
      logger || LoggerFactory.getInstance().createLogger('SessionManager');
  }

  /**
   * Start a new session
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('SessionManager'),
    'startSession'
  )
  startSession(events?: SessionEvents): void {
    this.logger.info('Starting new session', { operation: 'startSession' });

    this.events = events || {};
    this.sessionStartTime = Date.now();
    this.lastActivityTime = Date.now();
    this.warningShown = false;

    if (this.isAutoLogoutEnabled()) {
      this.startMonitoring();
    }

    this.logger.info('Session started successfully', {
      autoLogoutEnabled: this.isAutoLogoutEnabled(),
      sessionTimeout: this.getSessionTimeoutHours(),
      operation: 'startSession',
    });
  }

  /**
   * End the current session
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('SessionManager'),
    'endSession'
  )
  endSession(): void {
    this.logger.info('Ending session', { operation: 'endSession' });

    this.stopMonitoring();
    this.sessionStartTime = null;
    this.lastActivityTime = Date.now();
    this.warningShown = false;
    this.events = {};

    this.logger.info('Session ended', { operation: 'endSession' });
  }

  /**
   * Update activity timestamp
   */
  updateActivity(): void {
    const now = Date.now();
    const previousActivity = this.lastActivityTime;
    this.lastActivityTime = now;

    // Reset warning if activity detected after warning was shown
    if (this.warningShown) {
      this.warningShown = false;
      this.logger.debug(
        'Activity detected after warning, resetting warning state'
      );
    }

    // Notify about activity if significant time has passed
    if (now - previousActivity > 5 * 60 * 1000) {
      // 5 minutes
      this.logger.debug('Significant activity detected', {
        inactiveMinutes: Math.round((now - previousActivity) / 60000),
        operation: 'updateActivity',
      });

      if (this.events.onActivityDetected) {
        this.events.onActivityDetected();
      }
    }
  }

  /**
   * Check if session is expired
   */
  isSessionExpired(session?: any): boolean {
    if (!this.sessionStartTime) {
      return false;
    }

    const sessionTimeoutMs = this.getSessionTimeoutHours() * 60 * 60 * 1000;
    const sessionAge = Date.now() - this.sessionStartTime;

    // Check session age timeout
    if (sessionAge > sessionTimeoutMs) {
      this.logger.debug('Session expired due to age', {
        sessionAgeHours: Math.round(sessionAge / (60 * 60 * 1000)),
        timeoutHours: this.getSessionTimeoutHours(),
        operation: 'isSessionExpired',
      });
      return true;
    }

    // Check inactivity timeout
    const inactivityTimeoutMs = this.getSessionTimeoutHours() * 60 * 60 * 1000;
    const inactiveTime = Date.now() - this.lastActivityTime;

    if (inactiveTime > inactivityTimeoutMs) {
      this.logger.debug('Session expired due to inactivity', {
        inactiveHours: Math.round(inactiveTime / (60 * 60 * 1000)),
        timeoutHours: this.getSessionTimeoutHours(),
        operation: 'isSessionExpired',
      });
      return true;
    }

    // Check Supabase session expiry if provided
    if (session?.expires_at) {
      const expiryTime = new Date(session.expires_at).getTime();
      if (Date.now() >= expiryTime) {
        this.logger.debug('Supabase session expired', {
          expiresAt: session.expires_at,
          operation: 'isSessionExpired',
        });
        return true;
      }
    }

    return false;
  }

  /**
   * Get current session status
   */
  getSessionStatus(session?: any): SessionStatus {
    const now = Date.now();
    const isActive = !!this.sessionStartTime;
    const isExpired = isActive ? this.isSessionExpired(session) : false;

    let timeUntilExpiry: number | undefined;
    if (isActive && !isExpired) {
      const sessionTimeoutMs = this.getSessionTimeoutHours() * 60 * 60 * 1000;
      const inactiveTime = now - this.lastActivityTime;
      timeUntilExpiry = sessionTimeoutMs - inactiveTime;
    }

    return {
      isActive,
      isExpired,
      timeUntilExpiry,
      lastActivityTime: this.lastActivityTime,
      sessionStartTime: this.sessionStartTime || undefined,
    };
  }

  /**
   * Force session expiry
   */
  @logErrors(LoggerFactory.getInstance().createLogger('SessionManager'))
  forceExpireSession(reason: 'inactivity' | 'timeout' = 'timeout'): void {
    this.logger.info('Forcing session expiry', {
      reason,
      operation: 'forceExpireSession',
    });

    this.stopMonitoring();

    if (this.events.onSessionExpired) {
      this.events.onSessionExpired(reason);
    }

    // Notify all browser windows
    this.notifyWindows('auth:session-expired', {
      reason,
      timeout: this.getSessionTimeoutHours(),
    });
  }

  /**
   * Configure session settings
   */
  configure(config: SessionConfig): void {
    this.logger.info('Configuring session settings', {
      config,
      operation: 'configure',
    });

    if (config.sessionTimeoutHours !== undefined) {
      this.store.set(
        this.CONFIG_KEYS.SESSION_TIMEOUT,
        config.sessionTimeoutHours
      );
    }

    if (config.sessionWarningMinutes !== undefined) {
      this.store.set(
        this.CONFIG_KEYS.SESSION_WARNING,
        config.sessionWarningMinutes
      );
    }

    if (config.autoLogoutEnabled !== undefined) {
      this.store.set(
        this.CONFIG_KEYS.AUTO_LOGOUT_ENABLED,
        config.autoLogoutEnabled
      );

      // Update monitoring based on setting
      if (config.autoLogoutEnabled && this.sessionStartTime) {
        this.startMonitoring();
      } else {
        this.stopMonitoring();
      }
    }

    if (config.activityCheckIntervalMs !== undefined) {
      this.store.set(
        this.CONFIG_KEYS.ACTIVITY_CHECK_INTERVAL,
        config.activityCheckIntervalMs
      );

      // Restart monitoring with new interval if active
      if (this.sessionMonitoringInterval) {
        this.stopMonitoring();
        this.startMonitoring();
      }
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): Required<SessionConfig> {
    return {
      sessionTimeoutHours: this.getSessionTimeoutHours(),
      sessionWarningMinutes: this.getSessionWarningMinutes(),
      autoLogoutEnabled: this.isAutoLogoutEnabled(),
      activityCheckIntervalMs: this.getActivityCheckInterval(),
    };
  }

  /**
   * Get session statistics
   */
  getSessionStats(): {
    sessionDurationMs: number;
    inactiveDurationMs: number;
    timeUntilWarningMs: number;
    timeUntilExpiryMs: number;
    monitoringActive: boolean;
  } {
    const now = Date.now();
    const sessionDurationMs = this.sessionStartTime
      ? now - this.sessionStartTime
      : 0;
    const inactiveDurationMs = now - this.lastActivityTime;

    const sessionTimeoutMs = this.getSessionTimeoutHours() * 60 * 60 * 1000;
    const warningTimeMs = this.getSessionWarningMinutes() * 60 * 1000;

    const timeUntilExpiryMs = sessionTimeoutMs - inactiveDurationMs;
    const timeUntilWarningMs = timeUntilExpiryMs - warningTimeMs;

    return {
      sessionDurationMs,
      inactiveDurationMs,
      timeUntilWarningMs,
      timeUntilExpiryMs,
      monitoringActive: !!this.sessionMonitoringInterval,
    };
  }

  /**
   * Cleanup session manager
   */
  cleanup(): void {
    this.logger.debug('Cleaning up session manager');

    this.stopMonitoring();
    this.events = {};
    this.sessionStartTime = null;
    this.warningShown = false;
  }

  // Private methods

  private startMonitoring(): void {
    this.stopMonitoring();

    if (!this.isAutoLogoutEnabled()) {
      this.logger.debug('Auto-logout disabled, skipping monitoring');
      return;
    }

    this.logger.debug('Starting session monitoring', {
      checkInterval: this.getActivityCheckInterval(),
      operation: 'startMonitoring',
    });

    this.sessionMonitoringInterval = setInterval(() => {
      this.checkSessionActivity();
    }, this.getActivityCheckInterval());
  }

  private stopMonitoring(): void {
    if (this.sessionMonitoringInterval) {
      clearInterval(this.sessionMonitoringInterval);
      this.sessionMonitoringInterval = null;
      this.logger.debug('Session monitoring stopped');
    }
  }

  @logErrors(LoggerFactory.getInstance().createLogger('SessionManager'))
  private async checkSessionActivity(): Promise<void> {
    const sessionTimeoutMs = this.getSessionTimeoutHours() * 60 * 60 * 1000;
    const warningTimeMs = this.getSessionWarningMinutes() * 60 * 1000;

    const inactiveTime = Date.now() - this.lastActivityTime;
    const timeUntilExpiry = sessionTimeoutMs - inactiveTime;

    this.logger.debug('Checking session activity', {
      inactiveMinutes: Math.round(inactiveTime / 60000),
      timeUntilExpiryMinutes: Math.round(timeUntilExpiry / 60000),
      operation: 'checkSessionActivity',
    });

    if (timeUntilExpiry <= 0) {
      // Session expired
      this.logger.info('Session expired due to inactivity', {
        inactiveHours: Math.round(inactiveTime / (60 * 60 * 1000)),
        operation: 'checkSessionActivity',
      });

      this.forceExpireSession('inactivity');
    } else if (timeUntilExpiry <= warningTimeMs && !this.warningShown) {
      // Show warning
      this.warningShown = true;
      const minutesRemaining = Math.ceil(timeUntilExpiry / 60000);

      this.logger.info('Session expiring soon, showing warning', {
        minutesRemaining,
        operation: 'checkSessionActivity',
      });

      if (this.events.onSessionWarning) {
        this.events.onSessionWarning(minutesRemaining);
      }

      // Notify renderer
      this.notifyWindows('auth:session-warning', {
        minutesRemaining,
      });
    }
  }

  private notifyWindows(channel: string, data: any): void {
    try {
      BrowserWindow.getAllWindows().forEach(window => {
        if (!window.isDestroyed()) {
          window.webContents.send(channel, data);
        }
      });
    } catch (error) {
      this.logger.error('Failed to notify windows', error as Error, {
        channel,
        operation: 'notifyWindows',
      });
    }
  }

  // Configuration getters

  private getSessionTimeoutHours(): number {
    return (
      (this.store.get(this.CONFIG_KEYS.SESSION_TIMEOUT) as number) ||
      this.DEFAULT_CONFIG.sessionTimeoutHours
    );
  }

  private getSessionWarningMinutes(): number {
    return (
      (this.store.get(this.CONFIG_KEYS.SESSION_WARNING) as number) ||
      this.DEFAULT_CONFIG.sessionWarningMinutes
    );
  }

  private isAutoLogoutEnabled(): boolean {
    const stored = this.store.get(this.CONFIG_KEYS.AUTO_LOGOUT_ENABLED);
    return stored !== false; // Default true
  }

  private getActivityCheckInterval(): number {
    return (
      (this.store.get(this.CONFIG_KEYS.ACTIVITY_CHECK_INTERVAL) as number) ||
      this.DEFAULT_CONFIG.activityCheckIntervalMs
    );
  }
}
