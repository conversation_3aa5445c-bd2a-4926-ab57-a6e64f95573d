-- Migration 028: Convert products table to pure UUID primary key
-- This migration transforms the products table from dual-ID system to pure UUID architecture
-- WARNING: This is a breaking change that modifies core table structure
-- Ensure full backup before executing this migration

BEGIN TRANSACTION;

-- Step 1: Create backup table with current data
CREATE TABLE products_backup_pre_uuid_migration AS 
SELECT * FROM products;

-- Step 2: Get all foreign key constraints that reference products.id (integer)
-- We need to handle these relationships during the migration

-- Step 3: Drop existing indexes on products table
DROP INDEX IF EXISTS idx_products_external;
DROP INDEX IF EXISTS idx_products_master;
DROP INDEX IF EXISTS idx_products_org;
DROP INDEX IF EXISTS idx_products_deleted_at;
DROP INDEX IF EXISTS idx_products_org_active;

-- Step 4: Store foreign key data for product_colors junction table
-- We need to preserve these relationships using external_id mappings
CREATE TEMPORARY TABLE product_colors_migration_map AS
SELECT 
    pc.product_id as old_product_id,
    p.external_id as product_uuid,
    pc.color_id,
    pc.display_order,
    pc.organization_id,
    pc.added_at
FROM product_colors pc
JOIN products p ON pc.product_id = p.id;

-- Step 5: Clear product_colors table (we'll repopulate it after migration)
DELETE FROM product_colors;

-- Step 6: Create new products table with UUID primary key
CREATE TABLE products_new (
  id TEXT PRIMARY KEY,  -- This was external_id, now becomes primary key
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  type TEXT,
  sku TEXT,
  website TEXT,
  datasheet_url TEXT,
  price REAL,
  currency TEXT,
  is_master BOOLEAN NOT NULL DEFAULT FALSE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  -- Organization support (UUID)
  organization_id TEXT,
  created_by TEXT,
  user_id TEXT,
  metadata JSON DEFAULT '{}',
  -- Sync fields
  device_id TEXT,
  deleted_at TEXT,
  conflict_resolved_at TEXT,
  is_synced INTEGER DEFAULT 0,
  
  -- Constraints
  CHECK (length(id) = 36),  -- UUID v4 format validation
  CHECK (length(trim(name)) > 0)
);

-- Step 7: Migrate data from old table to new table
INSERT INTO products_new (
    id, name, description, category, type, sku, website, datasheet_url,
    price, currency, is_master, is_active, created_at, updated_at,
    organization_id, created_by, user_id, metadata, device_id,
    deleted_at, conflict_resolved_at, is_synced
)
SELECT 
    external_id as id,  -- external_id becomes the primary key
    name, description, category, type, sku, website, datasheet_url,
    price, currency, is_master, is_active, created_at, updated_at,
    organization_id, created_by, user_id, metadata, device_id,
    deleted_at, conflict_resolved_at, is_synced
FROM products
WHERE external_id IS NOT NULL AND length(external_id) = 36;

-- Step 8: Verify data migration
-- Count records to ensure no data loss
SELECT 
    (SELECT COUNT(*) FROM products WHERE external_id IS NOT NULL) as original_count,
    (SELECT COUNT(*) FROM products_new) as migrated_count;

-- Step 9: Drop old products table and rename new one
DROP TABLE products;
ALTER TABLE products_new RENAME TO products;

-- Step 10: Recreate indexes for UUID primary key
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_org ON products(organization_id);
CREATE INDEX IF NOT EXISTS idx_products_deleted_at ON products(deleted_at);
CREATE INDEX IF NOT EXISTS idx_products_org_active ON products(organization_id, deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_products_is_master ON products(is_master);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_products_updated_at ON products(updated_at);
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku) WHERE sku IS NOT NULL;

-- Step 11: Update product_colors table to use UUID foreign keys
-- First, we need to get the color UUIDs as well
CREATE TEMPORARY TABLE product_colors_uuid_map AS
SELECT 
    pcm.product_uuid,
    c.external_id as color_uuid,
    pcm.display_order,
    pcm.organization_id,
    pcm.added_at
FROM product_colors_migration_map pcm
JOIN colors c ON pcm.color_id = c.id
WHERE pcm.product_uuid IS NOT NULL 
  AND c.external_id IS NOT NULL
  AND length(pcm.product_uuid) = 36
  AND length(c.external_id) = 36;

-- Drop the old product_colors table structure
DROP TABLE product_colors;

-- Create new product_colors table with UUID foreign keys
CREATE TABLE product_colors (
  product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  color_id TEXT NOT NULL,  -- Will reference colors UUID after colors migration
  display_order INTEGER NOT NULL DEFAULT 0,
  organization_id TEXT NOT NULL,
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (product_id, color_id),
  
  -- Constraints
  CHECK (length(product_id) = 36),
  CHECK (length(color_id) = 36),
  CHECK (length(organization_id) = 36)
);

-- Repopulate product_colors with UUID foreign keys
INSERT INTO product_colors (product_id, color_id, display_order, organization_id, added_at)
SELECT 
    product_uuid as product_id,
    color_uuid as color_id,
    display_order,
    organization_id,
    added_at
FROM product_colors_uuid_map;

-- Step 12: Recreate product_colors indexes
CREATE INDEX IF NOT EXISTS idx_product_colors_product ON product_colors(product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org_product ON product_colors(organization_id, product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org_color ON product_colors(organization_id, color_id);

-- Step 13: Update triggers for UUID-based products table
DROP TRIGGER IF EXISTS update_products_timestamp;

CREATE TRIGGER IF NOT EXISTS update_products_timestamp 
AFTER UPDATE ON products
BEGIN
  UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Step 14: Clean up temporary tables
DROP TABLE product_colors_migration_map;
DROP TABLE product_colors_uuid_map;

-- Step 15: Add migration record
INSERT OR IGNORE INTO schema_migrations (version, name) 
VALUES (28, 'migrate_products_to_uuid_primary_key');

-- Verification queries (run after migration)
-- Uncomment these to verify migration success:

-- Check products table structure
-- PRAGMA table_info(products);

-- Verify UUID format in products
-- SELECT COUNT(*) as total_products,
--        COUNT(CASE WHEN length(id) = 36 THEN 1 END) as valid_uuid_products
-- FROM products;

-- Verify product_colors relationships
-- SELECT COUNT(*) as total_relationships,
--        COUNT(CASE WHEN length(product_id) = 36 AND length(color_id) = 36 THEN 1 END) as valid_uuid_relationships
-- FROM product_colors;

-- Check foreign key integrity
-- SELECT p.id, p.name, COUNT(pc.color_id) as color_count
-- FROM products p
-- LEFT JOIN product_colors pc ON p.id = pc.product_id
-- GROUP BY p.id, p.name
-- LIMIT 10;

COMMIT;

-- Post-migration notes:
-- 1. The products table now uses UUID as primary key (id column)
-- 2. All foreign key references have been updated to use UUIDs
-- 3. Product-color relationships are preserved through UUID mapping
-- 4. Backup table (products_backup_pre_uuid_migration) contains original data
-- 5. Next migration should handle colors table similarly
-- 6. Repository layer will need updates to remove external_id→internal_id conversion