/**
 * @file ProductColorTable/index.tsx
 * @description Table component for displaying products and their associated colors
 * Shows only user data (colors added to products), not the Pantone library
 */

import { useEffect } from 'react';
import { useProductStore } from '../../store/product.store';
import { useTokens } from '../../hooks/useTokens';
import { useColorProductMap } from '../../hooks/useColorProductMap';
import { Loader2 } from 'lucide-react';
import ColorSwatch from '../ColorSwatches/ColorSwatch';
import {
  formatHex,
  parseCMYK,
  formatCMYKForDisplay,
} from '../../../shared/utils/color';

interface ProductColorTableProps {
  view?: 'details' | 'reference' | 'flavours';
}

export default function ProductColorTable({
  view = 'details',
}: ProductColorTableProps) {
  const tokens = useTokens();
  const { products, isLoading, error, fetchProductsWithColors } =
    useProductStore();
  const { productMap, isLoading: isProductMapLoading } = useColorProductMap();

  useEffect(() => {
    fetchProductsWithColors();
  }, [fetchProductsWithColors]);

  // Development-only logging for debugging data structure
  useEffect(() => {
    if (
      process.env.NODE_ENV === 'development' &&
      process.env.DEBUG_PRODUCTS &&
      products.length > 0
    ) {
      console.log('[ProductColorTable] Products:', products);
      console.log('[ProductColorTable] First product:', products[0]);
      if (products[0]?.colors && products[0]?.colors.length > 0) {
        console.log('[ProductColorTable] First color:', products[0]?.colors[0]);
        console.log(
          '[ProductColorTable] Color properties:',
          Object.keys(products[0]?.colors[0] ?? {})
        );
      }
    }
  }, [products]);

  // Helper function to clean color code for display
  const getCleanCode = (code: string): string => {
    // Remove the unique identifier suffix (e.g., "939-MB2UE4Z6-8CAA" -> "939")
    const parts = code.split('-');
    return parts[0] ?? code;
  };

  // Guard against tokens not being loaded yet
  if (!tokens || !tokens.colors || !tokens.spacing || !tokens.typography) {
    return (
      <div style={{ padding: '16px', textAlign: 'center' }}>Loading...</div>
    );
  }

  if (isLoading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '400px',
          color: tokens.colors.ui.foreground.secondary,
        }}
      >
        <Loader2 className='animate-spin' size={32} />
      </div>
    );
  }

  if (error) {
    return (
      <div
        style={{
          padding: tokens.spacing[6],
          color: tokens.colors.feedback.error,
          textAlign: 'center',
        }}
      >
        Error loading products: {error}
      </div>
    );
  }

  // Filter to only show products with colors
  const productsWithColors = products.filter(
    p => p.colors && p.colors.length > 0
  );

  if (productsWithColors.length === 0) {
    return (
      <div
        style={{
          padding: tokens.spacing[8],
          textAlign: 'center',
          color: tokens.colors.ui.foreground.secondary,
        }}
      >
        <p>No colors have been added to products yet.</p>
        <p
          style={{
            marginTop: tokens.spacing[2],
            fontSize: tokens.typography.fontSize.sm,
          }}
        >
          Add colors to products in the Products view to see them here.
        </p>
      </div>
    );
  }

  // Get formatted CMYK string
  const getFormattedCmyk = (cmykStr: string): string => {
    try {
      const cmykObj = parseCMYK(cmykStr);
      return formatCMYKForDisplay(cmykObj);
    } catch {
      // If parsing fails, return the original string
      return cmykStr;
    }
  };

  return (
    <div
      style={{
        padding: tokens.spacing[6],
        maxHeight:
          'calc(100vh - var(--spacing-20) - var(--spacing-16) - var(--spacing-12))',
        overflowY: 'auto',
      }}
    >
      {view === 'details' && (
        <table
          style={{
            width: '100%',
            borderCollapse: 'collapse',
            backgroundColor: tokens.colors.ui.background.primary,
            borderRadius: tokens.borderRadius.md,
            overflow: 'hidden',
            boxShadow: tokens.shadows.sm,
          }}
        >
          <thead>
            <tr
              style={{
                backgroundColor: tokens.colors.brand.primary,
                color: 'white',
              }}
            >
              <th style={{ ...headerStyle(tokens), width: '12%' }}>Product</th>
              <th style={{ ...headerStyle(tokens), width: '12%' }}>Name</th>
              <th style={{ ...headerStyle(tokens), width: '12%' }}>Pantone</th>
              <th style={{ ...headerStyle(tokens), width: '12%' }}>Hex</th>
              <th style={{ ...headerStyle(tokens), width: '18%' }}>CMYK</th>
              <th style={{ ...headerStyle(tokens), width: '25%' }}>Notes</th>
              <th style={{ ...headerStyle(tokens), width: '9%' }}>Actions</th>
            </tr>
          </thead>
          <tbody>
            {productsWithColors.flatMap(product =>
              product.colors!.map((color, _index) => (
                <tr
                  key={`${product.id}-${color.id}`}
                  style={{
                    borderBottom: `1px solid ${tokens.colors.ui.border.light}`,
                    backgroundColor: tokens.colors.ui.background.primary,
                  }}
                >
                  <td style={cellStyle(tokens)}>{product.name}</td>
                  <td style={cellStyle(tokens)}>
                    {color.name || getCleanCode(color.code)}
                  </td>
                  <td style={cellStyle(tokens)}>
                    PMS {getCleanCode(color.code)}
                  </td>
                  <td style={cellStyle(tokens)}>
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: tokens.spacing[1],
                      }}
                    >
                      <div
                        style={{
                          width: '32px',
                          height: '32px',
                          backgroundColor: color.hex || '#000',
                          border: `1px solid ${tokens.colors.ui.border.light}`,
                          borderRadius: tokens.borderRadius.sm,
                        }}
                      />
                      <span
                        style={{
                          fontSize: tokens.typography.fontSize.xs,
                          fontFamily:
                            tokens.typography.fontFamily.mono.join(', '),
                        }}
                      >
                        {color.hex}
                      </span>
                    </div>
                  </td>
                  <td
                    style={{
                      ...cellStyle(tokens),
                      fontFamily: tokens.typography.fontFamily.mono.join(', '),
                      fontSize: tokens.typography.fontSize.sm,
                    }}
                  >
                    {getFormattedCmyk(color.cmyk)}
                  </td>
                  <td style={cellStyle(tokens)}>{color.notes || '-'}</td>
                  <td style={cellStyle(tokens)}>
                    <button
                      style={{
                        padding: `${tokens.spacing[1]} ${tokens.spacing[2]}`,
                        fontSize: tokens.typography.fontSize.sm,
                        border: `1px solid ${tokens.colors.ui.border.light}`,
                        borderRadius: tokens.borderRadius.sm,
                        backgroundColor: 'transparent',
                        cursor: 'pointer',
                      }}
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      )}

      {view === 'reference' && (
        <table
          style={{
            width: '100%',
            borderCollapse: 'collapse',
            backgroundColor: tokens.colors.ui.background.primary,
            borderRadius: tokens.borderRadius.md,
            overflow: 'hidden',
            boxShadow: tokens.shadows.sm,
          }}
        >
          <thead>
            <tr
              style={{
                backgroundColor: tokens.colors.brand.primary,
                color: 'white',
              }}
            >
              <th style={{ ...headerStyle(tokens), width: '15%' }}>Pantone</th>
              <th style={{ ...headerStyle(tokens), width: '15%' }}>Hex</th>
              <th style={{ ...headerStyle(tokens), width: '20%' }}>CMYK</th>
              <th style={{ ...headerStyle(tokens), width: '10%' }}>Used</th>
              <th style={{ ...headerStyle(tokens), width: '30%' }}>Products</th>
              <th style={{ ...headerStyle(tokens), width: '10%' }}>Name</th>
            </tr>
          </thead>
          <tbody>
            {(() => {
              // Group colors by color code
              const colorsByCode = new Map<
                string,
                { colors: any[]; products: Set<string> }
              >();

              productsWithColors.forEach(product => {
                product.colors!.forEach(color => {
                  const existing = colorsByCode.get(color.code) || {
                    colors: [],
                    products: new Set(),
                  };
                  existing.colors.push({ ...color, productName: product.name });
                  existing.products.add(product.name);
                  colorsByCode.set(color.code, existing);
                });
              });

              return Array.from(colorsByCode.entries()).map(([code, data]) => {
                const firstColor = data.colors[0];
                const productList = Array.from(data.products);

                return (
                  <tr
                    key={code}
                    style={{
                      borderBottom: `1px solid ${tokens.colors.ui.border.light}`,
                      backgroundColor: tokens.colors.ui.background.primary,
                    }}
                  >
                    <td style={cellStyle(tokens)}>{getCleanCode(code)}</td>
                    <td style={cellStyle(tokens)}>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <div
                          style={{
                            width: '32px',
                            height: '32px',
                            backgroundColor: firstColor.hex || '#000',
                            border: `1px solid ${tokens.colors.ui.border.light}`,
                            borderRadius: tokens.borderRadius.sm,
                          }}
                        />
                        <span
                          style={{
                            fontSize: tokens.typography.fontSize.xs,
                            marginTop: '4px',
                            color: tokens.colors.ui.foreground.secondary,
                          }}
                        >
                          {formatHex(firstColor.hex)}
                        </span>
                      </div>
                    </td>
                    <td
                      style={{
                        ...cellStyle(tokens),
                        fontFamily:
                          tokens.typography.fontFamily.mono.join(', '),
                        fontSize: tokens.typography.fontSize.sm,
                      }}
                    >
                      {getFormattedCmyk(firstColor.cmyk)}
                    </td>
                    <td style={cellStyle(tokens)}>
                      <span
                        style={{
                          backgroundColor:
                            tokens.colors.ui.background.secondary,
                          padding: `${tokens.spacing[1]} ${tokens.spacing[2]}`,
                          borderRadius: tokens.borderRadius.full,
                          fontSize: tokens.typography.fontSize.sm,
                        }}
                      >
                        {data.products.size}
                      </span>
                    </td>
                    <td style={cellStyle(tokens)}>
                      {productList.slice(0, 3).join(', ')}
                      {productList.length > 3 &&
                        ` +${productList.length - 3} more`}
                    </td>
                    <td style={cellStyle(tokens)}>
                      {data.colors
                        .map(c => c.name || c.display_name || '-')
                        .filter((v, i, a) => a.indexOf(v) === i)
                        .join(', ')}
                    </td>
                  </tr>
                );
              });
            })()}
          </tbody>
        </table>
      )}

      {view === 'flavours' && (
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
            gap: tokens.spacing[4],
            padding: tokens.spacing[4],
          }}
        >
          {productsWithColors.flatMap(product =>
            product.colors!.map(color => (
              <ColorSwatch
                key={`${product.id}-${color.id}`}
                entry={{
                  ...color,
                  product: product.name,
                }}
                productMap={productMap}
                isProductMapLoading={isProductMapLoading}
              />
            ))
          )}
        </div>
      )}
    </div>
  );
}

const headerStyle = (tokens: any) => ({
  padding: `${tokens.spacing[3]} ${tokens.spacing[4]}`,
  textAlign: 'left' as const,
  fontSize: tokens.typography.fontSize.sm,
  fontWeight: tokens.typography.fontWeight.medium,
  color: 'white',
  textTransform: 'uppercase' as const,
  letterSpacing: '0.5px',
});

const cellStyle = (tokens: any) => ({
  padding: `${tokens.spacing[3]} ${tokens.spacing[4]}`,
  verticalAlign: 'middle' as const,
  fontSize: tokens.typography.fontSize.base,
  color: tokens.colors.ui.foreground.primary,
});
