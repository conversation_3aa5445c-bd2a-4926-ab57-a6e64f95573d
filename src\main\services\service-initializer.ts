/**
 * @file service-initializer.ts
 * @description Centralized service initialization system
 *
 * This module provides a unified interface for initializing all services
 * in the ChromaSync application. It replaces the scattered service initialization
 * logic that was previously in the main index.ts file.
 */

import { app } from 'electron';
import { getDatabase, initDatabase } from '../db/database';
import { setupSharedFolderIPC } from '../ipc/shared-folder-ipc';
import { SharedFolderManager } from '../shared-folder';
import { serviceLogger } from '../utils/logger';
import { sentryService, SentryService } from './sentry.service';
import { getZohoEmailService, ServiceLocator } from './service-locator';

export interface ServiceInitializationResult {
  success: boolean;
  database: any;
  initTime: number;
  errors: string[];
  warnings: string[];
}

export interface ServiceConfig {
  zoho: {
    clientId?: string;
    clientSecret?: string;
    refreshToken?: string;
    accountId?: string;
    region?: string;
    supportAlias?: string;
  };
  supabase: {
    url?: string;
    anonKey?: string;
  };
}

/**
 * Initialize Sentry error tracking service using singleton pattern
 */
export async function initializeSentry(): Promise<void> {
  try {
    if (SentryService.isGloballyInitialized()) {
      serviceLogger.info('[Services] Sentry already initialized globally', {
        attempts: SentryService.getInitializationAttempts(),
      });
      return;
    }

    await sentryService.initialize();
    serviceLogger.info('[Services] Sentry initialized successfully');
  } catch (error) {
    serviceLogger.error('[Services] Failed to initialize Sentry:', error);
  }
}

/**
 * Initialize the database connection
 */
export async function initializeDatabaseService(): Promise<any> {
  serviceLogger.info('[Services] Starting database initialization...');
  
  try {
    const db = await initDatabase();
    serviceLogger.info(
      '[Services] Database initialization result:',
      db ? 'SUCCESS' : 'NULL'
    );
    
    if (!db) {
      serviceLogger.error('[Services] Database initialization returned null');
      throw new Error('Database initialization failed - initDatabase() returned null');
    }
    
    // Re-check database after potential migration
    const currentDb = getDatabase();
    if (!currentDb) {
      serviceLogger.error('[Services] Database is still null after migration attempt');
      throw new Error('Database is still null after initialization - getDatabase() returned null');
    }
    
    // Verify database connection is working
    try {
      const testQuery = db.prepare('SELECT 1 as test').get();
      if (!testQuery || testQuery.test !== 1) {
        throw new Error('Database connection test failed');
      }
      serviceLogger.info('[Services] Database connection verified successfully');
    } catch (error) {
      serviceLogger.error('[Services] Database connection test failed:', error);
      throw new Error(`Database connection test failed: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    return db;
  } catch (error) {
    serviceLogger.error('[Services] Database initialization failed:', error);
    throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Create service configuration from environment variables
 */
export function createServiceConfig(): ServiceConfig {
  return {
    zoho: {
      clientId: process.env.ZOHO_CLIENT_ID,
      clientSecret: process.env.ZOHO_CLIENT_SECRET,
      refreshToken: process.env.ZOHO_REFRESH_TOKEN,
      accountId: process.env.ZOHO_ACCOUNT_ID,
      region: process.env.ZOHO_REGION || 'US',
      supportAlias: process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>',
    },
    supabase: {
      url: process.env.SUPABASE_URL,
      anonKey: process.env.SUPABASE_ANON_KEY,
    },
  };
}

/**
 * Initialize the ServiceLocator with database and configuration
 */
export async function initializeServiceLocator(
  database: any,
  config: ServiceConfig
): Promise<void> {
  try {
    await ServiceLocator.initialize({
      database,
      config,
    });
    serviceLogger.info('[Services] ServiceLocator initialized successfully');
  } catch (error) {
    serviceLogger.error(
      '[Services] Failed to initialize ServiceLocator:',
      error
    );
    throw error;
  }
}

/**
 * Initialize shared folder management
 */
export function initializeSharedFolderService(): SharedFolderManager {
  serviceLogger.info('[Services] Setting up shared folder management...');

  const sharedFolderConfig = {
    basePath: app.getPath('userData'),
    folderName: 'shared_data',
  };
  const sharedFolderManager = new SharedFolderManager(sharedFolderConfig);
  setupSharedFolderIPC(sharedFolderManager);

  serviceLogger.info(
    '[Services] Shared folder service initialized successfully'
  );
  return sharedFolderManager;
}

/**
 * Initialize email service
 */
export async function initializeEmailService(): Promise<void> {
  try {
    serviceLogger.info('[Services] Initializing email service...');
    const zohoEmailService = getZohoEmailService();
    await zohoEmailService.initialize();
    serviceLogger.info(
      '[Services] ✅ Zoho email service initialized successfully'
    );
  } catch (error) {
    serviceLogger.error(
      '[Services] ❌ Failed to initialize email service:',
      error
    );
    serviceLogger.error(
      '[Services] Team invitation emails will not work until email service is configured'
    );
    // App continues without email functionality
  }
}

/**
 * Main function to initialize all application services
 * This should be called from the main process during startup
 */
export async function initializeAllServices(): Promise<ServiceInitializationResult> {
  const startTime = Date.now();
  const errors: string[] = [];
  const warnings: string[] = [];

  serviceLogger.info(
    '[Services] 🚀 Starting comprehensive service initialization...'
  );

  try {
    // Initialize Sentry first for error tracking
    await initializeSentry();

    // Initialize database
    const database = await initializeDatabaseService();

    // Create service configuration
    const config = createServiceConfig();

    // Initialize ServiceLocator with database and config
    await initializeServiceLocator(database, config);

    // Initialize shared folder management
    initializeSharedFolderService();

    // Initialize email service
    await initializeEmailService();

    const initTime = Date.now() - startTime;
    serviceLogger.info(
      `[Services] ✅ All services initialized successfully in ${initTime}ms`
    );

    return {
      success: true,
      database,
      initTime,
      errors,
      warnings,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    errors.push(errorMessage);
    serviceLogger.error(
      '[Services] ❌ Error during service initialization:',
      error
    );

    const initTime = Date.now() - startTime;
    return {
      success: false,
      database: null,
      initTime,
      errors,
      warnings,
    };
  }
}

/**
 * Initialize services that don't require database or heavy dependencies
 * These can be initialized early in the startup process
 */
export async function initializeEarlyServices(): Promise<void> {
  console.log('[Services] 🚀 Initializing early services...');

  try {
    // Initialize Sentry first
    await initializeSentry();

    console.log('[Services] ✅ Early services initialized successfully');
  } catch (error) {
    console.error('[Services] ❌ Error initializing early services:', error);
    // Continue with startup even if early services fail
  }
}

/**
 * Initialize services that require database connection
 * Should be called after database is initialized
 */
export async function initializeDatabaseDependentServices(
  database: any
): Promise<void> {
  console.log('[Services] 🚀 Initializing database-dependent services...');

  try {
    // Create service configuration
    const config = createServiceConfig();

    // Initialize ServiceLocator with database and config
    await initializeServiceLocator(database, config);

    console.log(
      '[Services] ✅ Database-dependent services initialized successfully'
    );
  } catch (error) {
    console.error(
      '[Services] ❌ Error initializing database-dependent services:',
      error
    );
    throw error;
  }
}

/**
 * Initialize auxiliary services that enhance functionality but aren't critical
 * These services can fail without breaking the core application
 */
export async function initializeAuxiliaryServices(): Promise<void> {
  console.log('[Services] 🚀 Initializing auxiliary services...');

  try {
    // Initialize shared folder management
    initializeSharedFolderService();

    // Initialize email service
    await initializeEmailService();

    console.log('[Services] ✅ Auxiliary services initialized successfully');
  } catch (error) {
    console.error(
      '[Services] ⚠️ Some auxiliary services failed to initialize:',
      error
    );
    // Continue without auxiliary services
  }
}
