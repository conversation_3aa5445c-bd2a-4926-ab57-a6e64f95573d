/**
 * @file test-runner.js
 * @description Advanced test runner for the Migration Test Suite
 * 
 * This utility provides:
 * - Selective test phase execution
 * - Test result analysis and comparison
 * - Performance trend tracking
 * - Test environment management
 * - Automated test scheduling
 * - Integration with CI/CD pipelines
 */

const { MigrationTestSuite, TEST_CONFIG } = require('./migration-test-suite');
const { SchemaInspector } = require('./schema-inspector');
const { DataIntegrityChecker } = require('./data-integrity-checker');
const path = require('path');
const fs = require('fs');

class TestRunner {
  constructor(options = {}) {
    this.options = {
      verbose: options.verbose || false,
      outputDir: options.outputDir || __dirname,
      compareBaseline: options.compareBaseline || false,
      baselinePath: options.baselinePath || null,
      maxRetries: options.maxRetries || 3,
      parallelTests: options.parallelTests || false,
      ...options
    };
    
    this.results = [];
    this.baselineData = null;
  }

  /**
   * Run specific test phases
   */
  async runPhases(phases, testOptions = {}) {
    console.log(`🧪 Running Migration Test Phases: ${phases.join(', ')}`);
    console.log('=' .repeat(50));
    
    const testSuite = new MigrationTestSuite({
      verbose: this.options.verbose,
      ...testOptions
    });
    
    try {
      // Initialize test environment
      if (!await testSuite.initialize()) {
        throw new Error('Failed to initialize test environment');
      }
      
      // Run selected phases
      for (const phase of phases) {
        await this.runPhase(testSuite, phase);
      }
      
      // Generate report for selected phases
      const report = this.generatePhaseReport(testSuite, phases);
      
      // Save results
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const outputPath = path.join(this.options.outputDir, `phase-test-${phases.join('-')}-${timestamp}.json`);
      fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
      
      console.log(`📊 Phase test report saved to: ${outputPath}`);
      
      return report;
      
    } finally {
      await testSuite.cleanup();
    }
  }

  /**
   * Run individual test phase
   */
  async runPhase(testSuite, phase) {
    console.log(`\n🔄 Running Phase: ${phase}`);
    
    switch (phase) {
      case 'schema':
        await testSuite.runSchemaValidationTests();
        break;
      case 'data-generation':
        await testSuite.generateTestData();
        break;
      case 'migration':
        await testSuite.runMigrationPhaseTests();
        break;
      case 'performance':
        await testSuite.runPerformanceBenchmarks();
        break;
      case 'repository':
        await testSuite.runRepositoryTests();
        break;
      case 'service':
        await testSuite.runServiceTests();
        break;
      case 'rollback':
        await testSuite.runRollbackTests();
        break;
      default:
        throw new Error(`Unknown test phase: ${phase}`);
    }
  }

  /**
   * Generate report for selected phases
   */
  generatePhaseReport(testSuite, phases) {
    const report = {
      timestamp: new Date().toISOString(),
      phases: phases,
      results: {},
      summary: {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        totalDuration: 0
      }
    };
    
    phases.forEach(phase => {
      const phaseKey = this.getPhaseKey(phase);
      if (testSuite.testReport.phases[phaseKey]) {
        report.results[phase] = testSuite.testReport.phases[phaseKey];
        
        if (testSuite.testReport.phases[phaseKey].results) {
          testSuite.testReport.phases[phaseKey].results.forEach(result => {
            report.summary.totalTests++;
            if (result.status === 'passed') {
              report.summary.passedTests++;
            } else {
              report.summary.failedTests++;
            }
          });
        }
        
        report.summary.totalDuration += testSuite.testReport.phases[phaseKey].duration || 0;
      }
    });
    
    return report;
  }

  /**
   * Get phase key mapping
   */
  getPhaseKey(phase) {
    const mapping = {
      'schema': 'schemaValidation',
      'data-generation': 'dataGeneration',
      'migration': 'migrationPhases',
      'performance': 'performanceBenchmarks',
      'repository': 'repositoryTests',
      'service': 'serviceTests',
      'rollback': 'rollbackTests'
    };
    
    return mapping[phase] || phase;
  }

  /**
   * Run performance comparison between different configurations
   */
  async runPerformanceComparison(configurations) {
    console.log('🚀 Running Performance Comparison');
    console.log('=' .repeat(40));
    
    const results = {};
    
    for (const [name, config] of Object.entries(configurations)) {
      console.log(`\n📊 Testing configuration: ${name}`);
      
      const testSuite = new MigrationTestSuite({
        verbose: this.options.verbose,
        testDataSize: config.testDataSize || 'medium',
        ...config
      });
      
      try {
        await testSuite.initialize();
        await testSuite.generateTestData();
        await testSuite.runPerformanceBenchmarks();
        
        results[name] = {
          config: config,
          results: testSuite.testReport.phases.performanceBenchmarks.results,
          duration: testSuite.testReport.phases.performanceBenchmarks.duration
        };
        
      } finally {
        await testSuite.cleanup();
      }
    }
    
    // Generate comparison report
    const comparison = this.generateComparisonReport(results);
    
    // Save comparison
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputPath = path.join(this.options.outputDir, `performance-comparison-${timestamp}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(comparison, null, 2));
    
    console.log(`📊 Performance comparison saved to: ${outputPath}`);
    
    return comparison;
  }

  /**
   * Generate performance comparison report
   */
  generateComparisonReport(results) {
    const comparison = {
      timestamp: new Date().toISOString(),
      configurations: Object.keys(results),
      metrics: {},
      summary: {}
    };
    
    // Extract metrics for comparison
    const metricNames = ['queryPerformance', 'insertPerformance', 'updatePerformance', 'deletePerformance', 'joinPerformance'];
    
    metricNames.forEach(metric => {
      comparison.metrics[metric] = {};
      
      Object.entries(results).forEach(([configName, configResults]) => {
        const testResult = configResults.results.find(r => r.test.toLowerCase().includes(metric.toLowerCase()));
        
        if (testResult && testResult.details) {
          comparison.metrics[metric][configName] = {
            integerTime: testResult.details.integerQueryTime || testResult.details.integerInsertTime || testResult.details.integerUpdateTime || testResult.details.integerDeleteTime || testResult.details.integerJoinTime,
            uuidTime: testResult.details.uuidQueryTime || testResult.details.uuidInsertTime || testResult.details.uuidUpdateTime || testResult.details.uuidDeleteTime || testResult.details.uuidJoinTime,
            ratio: testResult.details.performanceRatio,
            avgTime: testResult.details.avgUuidTime || testResult.details.avgIntegerTime
          };
        }
      });
    });
    
    // Generate summary
    comparison.summary = {
      bestConfiguration: this.findBestConfiguration(results),
      worstConfiguration: this.findWorstConfiguration(results),
      recommendations: this.generateRecommendations(results)
    };
    
    return comparison;
  }

  /**
   * Find best performing configuration
   */
  findBestConfiguration(results) {
    let bestConfig = null;
    let bestScore = Infinity;
    
    Object.entries(results).forEach(([configName, configResults]) => {
      const avgRatio = configResults.results
        .filter(r => r.details && r.details.performanceRatio)
        .reduce((sum, r) => sum + r.details.performanceRatio, 0) / 
        configResults.results.filter(r => r.details && r.details.performanceRatio).length;
      
      if (avgRatio < bestScore) {
        bestScore = avgRatio;
        bestConfig = configName;
      }
    });
    
    return { name: bestConfig, score: bestScore };
  }

  /**
   * Find worst performing configuration
   */
  findWorstConfiguration(results) {
    let worstConfig = null;
    let worstScore = 0;
    
    Object.entries(results).forEach(([configName, configResults]) => {
      const avgRatio = configResults.results
        .filter(r => r.details && r.details.performanceRatio)
        .reduce((sum, r) => sum + r.details.performanceRatio, 0) / 
        configResults.results.filter(r => r.details && r.details.performanceRatio).length;
      
      if (avgRatio > worstScore) {
        worstScore = avgRatio;
        worstConfig = configName;
      }
    });
    
    return { name: worstConfig, score: worstScore };
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(results) {
    const recommendations = [];
    
    Object.entries(results).forEach(([configName, configResults]) => {
      configResults.results.forEach(result => {
        if (result.details && result.details.performanceRatio > 3) {
          recommendations.push({
            severity: 'high',
            configuration: configName,
            test: result.test,
            issue: `UUID operations are ${result.details.performanceRatio.toFixed(2)}x slower than integer operations`,
            recommendation: 'Consider optimizing UUID indexes or using a different UUID format'
          });
        } else if (result.details && result.details.performanceRatio > 2) {
          recommendations.push({
            severity: 'medium',
            configuration: configName,
            test: result.test,
            issue: `UUID operations are ${result.details.performanceRatio.toFixed(2)}x slower than integer operations`,
            recommendation: 'Monitor performance in production and consider optimization if needed'
          });
        }
      });
    });
    
    return recommendations;
  }

  /**
   * Run stress tests with increasing data loads
   */
  async runStressTests() {
    console.log('💪 Running Stress Tests');
    console.log('=' .repeat(25));
    
    const stressLevels = [
      { name: 'light', size: 'small', concurrent: 5 },
      { name: 'moderate', size: 'medium', concurrent: 10 },
      { name: 'heavy', size: 'large', concurrent: 20 },
      { name: 'extreme', size: 'large', concurrent: 50 }
    ];
    
    const results = {};
    
    for (const level of stressLevels) {
      console.log(`\n🔥 Running ${level.name} stress test`);
      
      try {
        const testSuite = new MigrationTestSuite({
          verbose: this.options.verbose,
          testDataSize: level.size
        });
        
        await testSuite.initialize();
        await testSuite.generateTestData();
        
        // Run concurrent operations
        const concurrentResults = await this.runConcurrentOperations(testSuite, level.concurrent);
        
        results[level.name] = {
          level: level,
          results: concurrentResults,
          passed: concurrentResults.every(r => r.success)
        };
        
        await testSuite.cleanup();
        
      } catch (error) {
        results[level.name] = {
          level: level,
          error: error.message,
          passed: false
        };
      }
    }
    
    // Save stress test results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputPath = path.join(this.options.outputDir, `stress-test-${timestamp}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
    
    console.log(`💪 Stress test results saved to: ${outputPath}`);
    
    return results;
  }

  /**
   * Run concurrent operations for stress testing
   */
  async runConcurrentOperations(testSuite, concurrentCount) {
    const operations = [];
    
    for (let i = 0; i < concurrentCount; i++) {
      const operation = this.createConcurrentOperation(testSuite.testDb, i);
      operations.push(operation);
    }
    
    try {
      const results = await Promise.all(operations);
      return results.map((result, index) => ({
        operationId: index,
        success: true,
        duration: result.duration,
        result: result.data
      }));
    } catch (error) {
      return [{
        operationId: 'all',
        success: false,
        error: error.message
      }];
    }
  }

  /**
   * Create concurrent operation for stress testing
   */
  async createConcurrentOperation(db, operationId) {
    return new Promise((resolve, reject) => {
      const startTime = performance.now();
      
      try {
        // Simulate concurrent database operations
        const operations = [
          () => db.prepare(`SELECT COUNT(*) FROM organizations`).get(),
          () => db.prepare(`SELECT COUNT(*) FROM products`).get(),
          () => db.prepare(`SELECT COUNT(*) FROM colors`).get(),
          () => db.prepare(`SELECT COUNT(*) FROM product_colors`).get()
        ];
        
        const operation = operations[operationId % operations.length];
        const result = operation();
        
        const duration = performance.now() - startTime;
        
        resolve({
          duration: duration,
          data: result
        });
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Validate test environment before running tests
   */
  async validateEnvironment() {
    console.log('🔍 Validating Test Environment');
    console.log('=' .repeat(35));
    
    const validation = {
      timestamp: new Date().toISOString(),
      checks: [],
      passed: true
    };
    
    // Check 1: Node.js version
    const nodeVersion = process.version;
    const nodeCheck = {
      name: 'Node.js Version',
      passed: true,
      details: nodeVersion,
      message: `Running Node.js ${nodeVersion}`
    };
    validation.checks.push(nodeCheck);
    
    // Check 2: Required dependencies
    const dependencies = ['better-sqlite3', 'uuid'];
    for (const dep of dependencies) {
      try {
        require(dep);
        validation.checks.push({
          name: `Dependency: ${dep}`,
          passed: true,
          message: `${dep} is available`
        });
      } catch (error) {
        validation.checks.push({
          name: `Dependency: ${dep}`,
          passed: false,
          error: error.message,
          message: `${dep} is not available`
        });
        validation.passed = false;
      }
    }
    
    // Check 3: File permissions
    try {
      const testFile = path.join(this.options.outputDir, 'test-write-permission');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      
      validation.checks.push({
        name: 'File Permissions',
        passed: true,
        message: 'Write permissions available'
      });
    } catch (error) {
      validation.checks.push({
        name: 'File Permissions',
        passed: false,
        error: error.message,
        message: 'Write permissions not available'
      });
      validation.passed = false;
    }
    
    // Check 4: Memory availability
    const memUsage = process.memoryUsage();
    const availableMemory = memUsage.heapTotal - memUsage.heapUsed;
    const minRequiredMemory = 100 * 1024 * 1024; // 100MB
    
    validation.checks.push({
      name: 'Memory Availability',
      passed: availableMemory > minRequiredMemory,
      details: {
        available: availableMemory,
        required: minRequiredMemory,
        usage: memUsage
      },
      message: `${Math.round(availableMemory / 1024 / 1024)}MB available memory`
    });
    
    if (availableMemory <= minRequiredMemory) {
      validation.passed = false;
    }
    
    // Display results
    validation.checks.forEach(check => {
      const status = check.passed ? '✅' : '❌';
      console.log(`   ${status} ${check.name}: ${check.message}`);
      if (!check.passed && check.error) {
        console.log(`      Error: ${check.error}`);
      }
    });
    
    console.log(`\n🎯 Environment Status: ${validation.passed ? 'READY' : 'NOT READY'}`);
    
    return validation;
  }

  /**
   * Generate test execution plan
   */
  generateExecutionPlan(testTypes, options = {}) {
    const plan = {
      timestamp: new Date().toISOString(),
      testTypes: testTypes,
      estimatedDuration: 0,
      phases: [],
      resources: {
        memory: '~200MB',
        disk: '~50MB',
        cpu: 'Medium'
      },
      warnings: []
    };
    
    // Define phase durations (in seconds)
    const phaseDurations = {
      'schema': 5,
      'data-generation': 10,
      'migration': 15,
      'performance': 30,
      'repository': 20,
      'service': 25,
      'rollback': 10
    };
    
    testTypes.forEach(testType => {
      if (testType === 'complete') {
        // Add all phases for complete test
        Object.keys(phaseDurations).forEach(phase => {
          plan.phases.push({
            name: phase,
            estimatedDuration: phaseDurations[phase],
            description: this.getPhaseDescription(phase)
          });
          plan.estimatedDuration += phaseDurations[phase];
        });
      } else if (phaseDurations[testType]) {
        plan.phases.push({
          name: testType,
          estimatedDuration: phaseDurations[testType],
          description: this.getPhaseDescription(testType)
        });
        plan.estimatedDuration += phaseDurations[testType];
      }
    });
    
    // Add warnings based on test configuration
    if (options.testDataSize === 'large') {
      plan.warnings.push('Large dataset selected - tests may take significantly longer');
      plan.estimatedDuration *= 2;
    }
    
    if (options.parallelTests) {
      plan.warnings.push('Parallel execution may cause resource contention');
    }
    
    return plan;
  }

  /**
   * Get phase description
   */
  getPhaseDescription(phase) {
    const descriptions = {
      'schema': 'Validate current database schema and structure',
      'data-generation': 'Generate test data for migration testing',
      'migration': 'Test all phases of UUID migration process',
      'performance': 'Benchmark performance between integer and UUID operations',
      'repository': 'Test repository layer CRUD operations',
      'service': 'Test service layer business logic and operations',
      'rollback': 'Test migration rollback and recovery procedures'
    };
    
    return descriptions[phase] || 'Unknown phase';
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    console.log(`
🧪 Migration Test Runner

Usage: node test-runner.js <command> [options]

Commands:
  phases <phase1,phase2,...>     Run specific test phases
  performance <config1,config2>  Run performance comparison
  stress                         Run stress tests
  validate                       Validate test environment
  plan <testtype1,testtype2>     Generate execution plan

Phases:
  schema          - Schema validation tests
  data-generation - Test data generation
  migration       - Migration phase tests
  performance     - Performance benchmarks
  repository      - Repository layer tests
  service         - Service layer tests
  rollback        - Rollback procedure tests

Options:
  --verbose, -v                 Enable verbose logging
  --output-dir <dir>            Set output directory
  --test-data-size <size>       Set test data size (small, medium, large)
  --parallel                    Enable parallel test execution
  --baseline <path>             Compare against baseline results

Examples:
  node test-runner.js phases schema,migration
  node test-runner.js performance small,medium,large
  node test-runner.js stress --verbose
  node test-runner.js validate
  node test-runner.js plan complete --test-data-size large
    `);
    process.exit(0);
  }
  
  const command = args[0];
  const options = {};
  
  // Parse options
  for (let i = 1; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--verbose' || arg === '-v') {
      options.verbose = true;
    } else if (arg === '--output-dir') {
      options.outputDir = args[i + 1];
      i++;
    } else if (arg === '--test-data-size') {
      options.testDataSize = args[i + 1];
      i++;
    } else if (arg === '--parallel') {
      options.parallelTests = true;
    } else if (arg === '--baseline') {
      options.compareBaseline = true;
      options.baselinePath = args[i + 1];
      i++;
    }
  }
  
  const runner = new TestRunner(options);
  
  async function runCommand() {
    try {
      switch (command) {
        case 'phases':
          if (args[1] && !args[1].startsWith('--')) {
            const phases = args[1].split(',');
            await runner.runPhases(phases, options);
          } else {
            console.error('❌ Please specify phases to run');
            process.exit(1);
          }
          break;
          
        case 'performance':
          if (args[1] && !args[1].startsWith('--')) {
            const sizes = args[1].split(',');
            const configurations = {};
            sizes.forEach(size => {
              configurations[size] = { testDataSize: size };
            });
            await runner.runPerformanceComparison(configurations);
          } else {
            console.error('❌ Please specify configurations for performance comparison');
            process.exit(1);
          }
          break;
          
        case 'stress':
          await runner.runStressTests();
          break;
          
        case 'validate':
          const validation = await runner.validateEnvironment();
          process.exit(validation.passed ? 0 : 1);
          break;
          
        case 'plan':
          if (args[1] && !args[1].startsWith('--')) {
            const testTypes = args[1].split(',');
            const plan = runner.generateExecutionPlan(testTypes, options);
            
            console.log('\n📋 Test Execution Plan');
            console.log('=' .repeat(25));
            console.log(`Estimated Duration: ${plan.estimatedDuration} seconds`);
            console.log(`Resource Requirements: ${plan.resources.memory} memory, ${plan.resources.disk} disk`);
            
            if (plan.warnings.length > 0) {
              console.log('\n⚠️  Warnings:');
              plan.warnings.forEach(warning => console.log(`   - ${warning}`));
            }
            
            console.log('\n📋 Execution Phases:');
            plan.phases.forEach((phase, index) => {
              console.log(`   ${index + 1}. ${phase.name} (${phase.estimatedDuration}s) - ${phase.description}`);
            });
            
            // Save plan
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const outputPath = path.join(runner.options.outputDir, `execution-plan-${timestamp}.json`);
            fs.writeFileSync(outputPath, JSON.stringify(plan, null, 2));
            console.log(`\n💾 Execution plan saved to: ${outputPath}`);
          } else {
            console.error('❌ Please specify test types for execution plan');
            process.exit(1);
          }
          break;
          
        default:
          console.error(`❌ Unknown command: ${command}`);
          process.exit(1);
      }
      
    } catch (error) {
      console.error('❌ Test runner failed:', error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  }
  
  runCommand();
}

module.exports = { TestRunner };