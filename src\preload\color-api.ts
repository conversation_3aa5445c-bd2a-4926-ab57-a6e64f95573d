/**
 * @file color-api.ts
 * @description Color API preload script for secure IPC communication
 */

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { ColorChannels, NewColorEntry, UpdateColorEntry } from '../shared/types/color.types';

// Import cache service for invalidation tracking
const isCacheEnabled = true; // Toggle for cache feature flag

// Typed API for color operations
contextBridge.exposeInMainWorld('colorAPI', {
  // Get all colors
  getAll: () => {
    console.log('Preload: calling colorAPI.getAll', isCacheEnabled ? '(cache enabled)' : '(cache disabled)');
    return ipcRenderer.invoke(ColorChannels.GET_ALL);
  },

  // Get color usage counts
  getUsageCounts: () => {
    console.log('Preload: calling colorAPI.getUsageCounts');
    return ipcRenderer.invoke(ColorChannels.GET_USAGE_COUNTS);
  },

  // Get color by ID
  getById: (id: string) => {
    console.log(`Preload: calling colorAPI.getById for ${id}`);
    return ipcRenderer.invoke(ColorChannels.GET_BY_ID, id);
  },

  // Add new color
  add: (color: NewColorEntry) => {
    console.log(`Preload: calling colorAPI.add for ${color.name} ${color.code}`);
    return ipcRenderer.invoke(ColorChannels.ADD, color);
  },

  // Update color
  update: (id: string, updates: UpdateColorEntry) => {
    console.log(`Preload: calling colorAPI.update for ${id}`);
    return ipcRenderer.invoke(ColorChannels.UPDATE, id, updates);
  },

  // Delete color
  delete: (id: string) => {
    console.log(`Preload: calling colorAPI.delete for ${id}`);
    return ipcRenderer.invoke(ColorChannels.DELETE, id);
  },

  // Clear all colors
  clearAll: () => {
    console.log('Preload: calling colorAPI.clearAll');
    return ipcRenderer.invoke(ColorChannels.CLEAR_ALL);
  },

  // Import colors from JSON file
  importColors: (mergeMode?: 'replace' | 'merge', filePath?: string, format?: 'json' | 'csv') => {
    console.log(`Preload: calling colorAPI.importColors with channel "${ColorChannels.IMPORT}" (format: ${format || 'json'})`);
    try {
      return ipcRenderer.invoke(ColorChannels.IMPORT, mergeMode, filePath, format)
        .then(result => {
          console.log(`Import result from main:`, result);
          return result;
        })
        .catch(error => {
          console.error(`Import error from main:`, error);
          throw error;
        });
    } catch (error) {
      console.error(`Error calling importColors:`, error);
      throw error;
    }
  },

  // Export colors to file (now supports JSON and CSV formats)
  exportColors: (filePath?: string, format?: 'json' | 'csv') => {
    console.log(`Preload: calling colorAPI.exportColors with channel "${ColorChannels.EXPORT}" (format: ${format || 'json'})`);
    try {
      return ipcRenderer.invoke(ColorChannels.EXPORT, filePath, format)
        .then(result => {
          console.log(`Export result from main:`, result);
          return result;
        })
        .catch(error => {
          console.error(`Export error from main:`, error);
          throw error;
        });
    } catch (error) {
      console.error(`Error calling exportColors:`, error);
      throw error;
    }
  },

  // Normalize all Pantone codes in the database to a consistent format
  normalizePantoneCodes: () => {
    console.log(`Preload: calling colorAPI.normalizePantoneCodes`);
    return ipcRenderer.invoke(ColorChannels.NORMALIZE_PANTONE_CODES);
  },

  // NEW: Clean implementation methods
  getAllWithUsage: () => {
    console.log('Preload: calling colorAPI.getAllWithUsage (clean implementation)', isCacheEnabled ? '(cache enabled)' : '(cache disabled)');
    return ipcRenderer.invoke(ColorChannels.GET_ALL_WITH_USAGE);
  },

  getProductsByColorName: () => {
    console.log('Preload: calling colorAPI.getProductsByColorName (swatch dropdown fix)');
    return ipcRenderer.invoke(ColorChannels.GET_PRODUCTS_BY_COLOR_NAME);
  },

  clearFrontendState: () => {
    console.log('Preload: calling colorAPI.clearFrontendState (clean implementation)');
    return ipcRenderer.invoke(ColorChannels.CLEAR_FRONTEND_STATE);
  },

  // ADMIN ONLY: Dangerous operations (explicitly marked)
  adminClearAll: (options?: { hardDelete?: boolean }) => {
    console.log('Preload: calling colorAPI.adminClearAll (admin only - dangerous)');
    return ipcRenderer.invoke(ColorChannels.ADMIN_CLEAR_ALL, options);
  },

  // TEMPORARY: Debug function to investigate data corruption
  investigateDataCorruption: () => {
    console.log('Preload: calling debug:investigateDataCorruption');
    return ipcRenderer.invoke('debug:investigateDataCorruption');
  },

  // Migrate gradient data from legacy format to new format
  migrateGradientData: () => {
    console.log('Preload: calling color:migrateGradientData');
    return ipcRenderer.invoke('color:migrateGradientData');
  }
});