/**
 * @file gradient-processor.service.ts
 * @description Service for handling all gradient-related operations
 *
 * This service provides functionality for gradient validation, CSS generation,
 * data processing, analysis, and format conversion. It follows the single
 * responsibility principle by handling only gradient-specific logic.
 *
 * Based on the comprehensive test suite requirements from gradient-processor.test.ts
 */

// Types
export interface GradientStop {
  color: string;
  position: number;
  cmyk?: { c: number; m: number; y: number; k: number };
  colorCode?: string;
}

export interface LegacyGradientInfo {
  gradientStops: GradientStop[];
  gradientCSS?: string;
  angle?: number;
}

export interface GradientValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface GradientAnalysis {
  stopCount: number;
  colorDistribution: { [color: string]: number };
  positionDistribution: number[];
  complexity: 'simple' | 'moderate' | 'complex';
  hasDuplicatePositions: boolean;
  hasColorCodes: boolean;
}

/**
 * GradientProcessor service class
 *
 * Handles all gradient-related operations including validation, CSS generation,
 * data processing, format conversion, and analysis.
 */
export class GradientProcessor {
  // =============================================================================
  // VALIDATION METHODS
  // =============================================================================

  /**
   * Validates an array of gradient stops
   * @param stops - Array of gradient stops to validate
   * @returns Validation result with errors and warnings
   */
  validateGradientStops(stops: GradientStop[]): GradientValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check for empty stops
    if (!stops || stops.length === 0) {
      errors.push('Gradient must have at least one stop');
      return { isValid: false, errors, warnings };
    }

    // Track positions for duplicate detection
    const positions: number[] = [];

    // Validate each stop
    for (const stop of stops) {
      // Validate color format
      if (!this.isValidHexColor(stop.color)) {
        errors.push(`Invalid hex color format: ${stop.color}`);
      }

      // Validate position range
      if (!this.isValidPosition(stop.position)) {
        errors.push(`Position must be between 0 and 1: ${stop.position}`);
      }

      // Track positions for duplicate detection
      positions.push(stop.position);

      // Validate CMYK if present
      if (stop.cmyk) {
        const { c, m, y, k } = stop.cmyk;
        if (
          c < 0 ||
          c > 100 ||
          m < 0 ||
          m > 100 ||
          y < 0 ||
          y > 100 ||
          k < 0 ||
          k > 100
        ) {
          errors.push(`Invalid CMYK values: c=${c}, m=${m}, y=${y}, k=${k}`);
        }
      }
    }

    // Check for duplicate positions
    const uniquePositions = [...new Set(positions)];
    if (positions.length !== uniquePositions.length) {
      warnings.push('Duplicate positions detected and will be resolved');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validates complete gradient info including stops and metadata
   * @param gradientInfo - Complete gradient information to validate
   * @returns Validation result with errors and warnings
   */
  validateGradientInfo(
    gradientInfo: LegacyGradientInfo
  ): GradientValidationResult {
    const stopsResult = this.validateGradientStops(gradientInfo.gradientStops);

    // Additional validation for angle if present
    if (gradientInfo.angle !== undefined) {
      if (gradientInfo.angle < 0 || gradientInfo.angle > 360) {
        stopsResult.errors.push(
          `Angle must be between 0 and 360 degrees: ${gradientInfo.angle}`
        );
        stopsResult.isValid = false;
      }
    }

    return stopsResult;
  }

  // =============================================================================
  // CSS GENERATION METHODS
  // =============================================================================

  /**
   * Generates CSS linear gradient string from gradient stops
   * @param stops - Array of gradient stops
   * @param angle - Optional angle in degrees (defaults to 45)
   * @returns CSS linear gradient string
   */
  generateLinearGradientCSS(stops: GradientStop[], angle: number = 45): string {
    if (!stops || stops.length === 0) {
      return '';
    }

    // Sort stops by position to ensure correct order
    const sortedStops = this.sortStopsByPosition(stops);

    // Generate CSS for each stop
    const stopStrings = sortedStops.map(stop => this.generateStopCSS(stop));

    return `linear-gradient(${angle}deg, ${stopStrings.join(', ')})`;
  }

  /**
   * Generates CSS string for a single gradient stop
   * @param stop - Gradient stop to generate CSS for
   * @returns CSS string for the stop
   */
  generateStopCSS(stop: GradientStop): string {
    const percentage = this.convertPositionToPercentage(stop.position);
    return `${stop.color} ${percentage}%`;
  }

  // =============================================================================
  // DATA PROCESSING METHODS
  // =============================================================================

  /**
   * Creates CSV string of gradient colors
   * @param stops - Array of gradient stops
   * @returns CSV string of colors (with color codes if present)
   */
  createGradientColorsCSV(stops: GradientStop[]): string {
    if (!stops || stops.length === 0) {
      return '';
    }

    return stops
      .map(stop => {
        if (stop.colorCode) {
          return `${stop.color}|${stop.colorCode}`;
        }
        return stop.color;
      })
      .join(',');
  }

  /**
   * Creates JSON string for gradient storage
   * @param stops - Array of gradient stops
   * @param angle - Optional angle (defaults to 45)
   * @returns JSON string representation
   */
  createGradientJSON(stops: GradientStop[], angle: number = 45): string {
    return JSON.stringify({
      stops,
      angle,
    });
  }

  /**
   * Sorts gradient stops by position
   * @param stops - Array of gradient stops to sort
   * @returns New array sorted by position
   */
  sortStopsByPosition(stops: GradientStop[]): GradientStop[] {
    return [...stops].sort((a, b) => a.position - b.position);
  }

  /**
   * Resolves duplicate positions by adding small offsets
   * @param stops - Array of gradient stops
   * @returns Array with unique positions
   */
  resolveDuplicatePositions(stops: GradientStop[]): GradientStop[] {
    const result: GradientStop[] = [];
    const positionCounts: { [key: number]: number } = {};

    // First pass: count occurrences of each position
    stops.forEach(stop => {
      positionCounts[stop.position] = (positionCounts[stop.position] || 0) + 1;
    });

    // Second pass: resolve duplicates
    const positionOffsets: { [key: number]: number } = {};

    stops.forEach(stop => {
      let adjustedPosition = stop.position;

      // If this position has duplicates, apply offset
      if ((positionCounts[stop.position] ?? 0) > 1) {
        const offset = positionOffsets[stop.position] || 0;
        adjustedPosition = stop.position + offset * 0.01; // 1% offset increments
        positionOffsets[stop.position] = offset + 1;

        // Ensure position stays within valid range
        adjustedPosition = Math.max(0, Math.min(1, adjustedPosition));
      }

      result.push({
        ...stop,
        position: adjustedPosition,
      });
    });

    return result;
  }

  // =============================================================================
  // FORMAT CONVERSION METHODS
  // =============================================================================

  /**
   * Normalizes positions to ensure they're within 0-1 range
   * @param stops - Array of gradient stops
   * @returns Array with normalized positions
   */
  normalizePositions(stops: GradientStop[]): GradientStop[] {
    return stops.map(stop => ({
      ...stop,
      position: Math.max(0, Math.min(1, stop.position)),
    }));
  }

  /**
   * Converts position (0-1) to percentage (0-100)
   * @param position - Position value between 0 and 1
   * @returns Percentage value between 0 and 100
   */
  convertPositionToPercentage(position: number): number {
    return position * 100;
  }

  /**
   * Converts percentage (0-100) to position (0-1)
   * @param percentage - Percentage value between 0 and 100
   * @returns Position value between 0 and 1
   */
  convertPercentageToPosition(percentage: number): number {
    return percentage / 100;
  }

  // =============================================================================
  // ANALYSIS METHODS
  // =============================================================================

  /**
   * Analyzes gradient properties and characteristics
   * @param stops - Array of gradient stops to analyze
   * @returns Detailed analysis of the gradient
   */
  analyzeGradient(stops: GradientStop[]): GradientAnalysis {
    if (!stops || stops.length === 0) {
      return {
        stopCount: 0,
        colorDistribution: {},
        positionDistribution: [],
        complexity: 'simple',
        hasDuplicatePositions: false,
        hasColorCodes: false,
      };
    }

    // Calculate color distribution
    const colorDistribution: { [color: string]: number } = {};
    stops.forEach(stop => {
      colorDistribution[stop.color] = (colorDistribution[stop.color] || 0) + 1;
    });

    // Check for duplicate positions
    const positions = stops.map(s => s.position);
    const uniquePositions = [...new Set(positions)];
    const hasDuplicatePositions = positions.length !== uniquePositions.length;

    // Check for color codes
    const hasColorCodes = stops.some(stop => !!stop.colorCode);

    // Calculate complexity
    const complexity = this.calculateComplexity(stops);

    return {
      stopCount: stops.length,
      colorDistribution,
      positionDistribution: positions,
      complexity,
      hasDuplicatePositions,
      hasColorCodes,
    };
  }

  /**
   * Calculates gradient complexity based on stop count and distribution
   * @param stops - Array of gradient stops
   * @returns Complexity classification
   */
  calculateComplexity(
    stops: GradientStop[]
  ): 'simple' | 'moderate' | 'complex' {
    if (!stops || stops.length <= 2) {
      return 'simple';
    } else if (stops.length <= 4) {
      return 'moderate';
    } else {
      return 'complex';
    }
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  /**
   * Validates hex color format
   * @param color - Color string to validate
   * @returns True if valid hex color
   */
  isValidHexColor(color: string): boolean {
    if (!color || typeof color !== 'string') {
      return false;
    }
    return /^#[0-9A-Fa-f]{6}$/.test(color);
  }

  /**
   * Validates position value
   * @param position - Position value to validate
   * @returns True if position is between 0 and 1
   */
  isValidPosition(position: number): boolean {
    return (
      typeof position === 'number' &&
      !isNaN(position) &&
      position >= 0 &&
      position <= 1
    );
  }

  /**
   * Gets service information and metadata
   * @returns Service metadata object
   */
  getServiceInfo(): { name: string; version: string; features: string[] } {
    return {
      name: 'GradientProcessor',
      version: '1.0.0',
      features: [
        'Gradient validation',
        'CSS generation',
        'Data processing',
        'Format conversion',
        'Gradient analysis',
        'Position normalization',
        'Duplicate resolution',
        'Color validation',
        'Complexity analysis',
      ],
    };
  }
}
