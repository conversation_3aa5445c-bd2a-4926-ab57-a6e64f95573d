/**
 * @file index.ts
 * @description AppShell component exports
 */

export { AppShell } from './AppShell';
export { AppProviders } from './AppProviders';
export { AppRouter } from './AppRouter';
export { MainLayout } from './MainLayout';
export { AppToolbar } from './AppToolbar';
export { TableControls } from './TableControls';
export { ContentArea } from './ContentArea';
export { LicenseGuard } from './LicenseGuard';
export { AppStartup } from './AppStartup';
export { DebugPanel } from './DebugPanel';
export { AppModals } from './AppModals';

// Hooks
export { useAppInitialization } from '../../hooks/useAppInitialization';
export { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts';

// Config
// TODO: Re-enable when serviceConfig file is implemented
// export { getDefaultServiceConfiguration, getEnvironmentServiceConfiguration } from './config/serviceConfig';

// Default export
export { AppShell as default } from './AppShell';
