/**
 * @file OrganizationSetup.tsx
 * @description Component for new user organization setup - create or join workspace
 */

import React, { useState } from 'react';
import { useOrganizationStore, useOrganizationStoreWithAliases } from '../../store/organization.store';
import { User } from '@supabase/supabase-js';
import { Building2, UserPlus, ArrowRight, Loader2, ArrowLeft } from 'lucide-react';

interface OrganizationSetupProps {
  user: User | null;
  onComplete: () => void;
  onBack?: () => void;
}

type SetupMode = 'create' | 'join';

export const OrganizationSetup: React.FC<OrganizationSetupProps> = ({ user, onComplete, onBack }) => {
  const [mode, setMode] = useState<SetupMode>('create');
  const [organizationName, setOrganizationName] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { createOrganization } = useOrganizationStore();
  const { joinOrganization } = useOrganizationStoreWithAliases();

  const handleCreateOrganization = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!organizationName.trim()) {
      setError('Organization name is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await createOrganization(organizationName);
      if (result.success) {
        onComplete();
      } else {
        setError(result.error || 'Failed to create organization');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create organization');
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinOrganization = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inviteCode.trim()) {
      setError('Invite code is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await joinOrganization(inviteCode);
      if (result.success) {
        onComplete();
      } else {
        setError(result.error || 'Failed to join organization');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join organization');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg">
      {/* Back Button */}
      {onBack && (
        <button
          onClick={onBack}
          className="mb-4 text-sm text-gray-600 hover:text-gray-800 flex items-center"
          disabled={isLoading}
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to workspace selection
        </button>
      )}
      
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome to ChromaSync!</h1>
        <p className="text-gray-600">Create your team workspace or join an existing one.</p>
        <p className="text-sm text-gray-500 mt-2">Signed in as: {user?.email || 'Loading...'}</p>
      </div>

      {/* Mode Selection */}
      <div className="flex gap-2 mb-6">
        <button
          type="button"
          onClick={() => setMode('create')}
          className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
            mode === 'create'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <Building2 className="inline-block w-5 h-5 mr-2" />
          Create Workspace
        </button>
        <button
          type="button"
          onClick={() => setMode('join')}
          className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors ${
            mode === 'join'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <UserPlus className="inline-block w-5 h-5 mr-2" />
          Join Workspace
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
          {error}
        </div>
      )}

      {/* Create Organization Form */}
      {mode === 'create' && (
        <form onSubmit={handleCreateOrganization} className="space-y-4">
          <div>
            <label htmlFor="org-name" className="block text-sm font-medium text-gray-700 mb-2">
              Organization Name
            </label>
            <input
              id="org-name"
              type="text"
              value={organizationName}
              onChange={(e) => setOrganizationName(e.target.value)}
              placeholder="e.g., Acme Design Studio"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isLoading}
              autoFocus
            />
            <p className="mt-1 text-xs text-gray-500">
              This will be your team's workspace name
            </p>
          </div>

          <button
            type="submit"
            disabled={isLoading || !organizationName.trim()}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center ${
              isLoading || !organizationName.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Creating Workspace...
              </>
            ) : (
              <>
                Create Workspace
                <ArrowRight className="w-5 h-5 ml-2" />
              </>
            )}
          </button>
        </form>
      )}

      {/* Join Organization Form */}
      {mode === 'join' && (
        <form onSubmit={handleJoinOrganization} className="space-y-4">
          <div>
            <label htmlFor="invite-code" className="block text-sm font-medium text-gray-700 mb-2">
              Invite Code
            </label>
            <input
              id="invite-code"
              type="text"
              value={inviteCode}
              onChange={(e) => setInviteCode(e.target.value)}
              placeholder="Enter your invite code"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isLoading}
              autoFocus
            />
            <p className="mt-1 text-xs text-gray-500">
              Ask your team admin for an invite code
            </p>
          </div>

          <button
            type="submit"
            disabled={isLoading || !inviteCode.trim()}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center ${
              isLoading || !inviteCode.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Joining Workspace...
              </>
            ) : (
              <>
                Join Workspace
                <ArrowRight className="w-5 h-5 ml-2" />
              </>
            )}
          </button>
        </form>
      )}

      {/* Help Text */}
      <div className="mt-6 text-center text-sm text-gray-500">
        {mode === 'create' ? (
          <p>
            Create a new workspace to start managing colors with your team.
            You can invite team members after setup.
          </p>
        ) : (
          <p>
            Join an existing workspace using an invite code from your team admin.
          </p>
        )}
      </div>
    </div>
  );
};
