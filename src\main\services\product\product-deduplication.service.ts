/**
 * @file product-deduplication.service.ts
 * @description Advanced service for product deduplication with configurable algorithms
 * 
 * This service provides sophisticated duplicate detection and merging capabilities:
 * - Smart duplicate detection using multiple criteria (name, SKU, metadata)
 * - Configurable matching algorithms (exact, fuzzy, similarity scoring)
 * - Intelligent merge strategies for conflicting data
 * - Color relationship preservation during merges
 * - Backup and rollback capabilities
 * - Performance optimization for large datasets
 */

import Database from 'better-sqlite3';
import { ProductRepository } from '../../db/repositories/product.repository';
import { ProductColorRelationshipService } from './product-color-relationship.service';
import { createServiceErrorHandler, ServiceResult } from '../../utils/service-error-handler';
import { validateRequired } from '../../utils/input-validation';
import { v4 as uuidv4 } from 'uuid';

// === INTERFACES ===

export interface DuplicateDetectionCriteria {
  name: boolean;
  sku: boolean;
  metadata: boolean;
  fuzzyThreshold: number; // 0-1 similarity threshold for fuzzy matching
}

export interface MatchingAlgorithm {
  type: 'exact' | 'fuzzy' | 'similarity';
  threshold: number; // For fuzzy and similarity algorithms
  ignoreCase: boolean;
  ignoreWhitespace: boolean;
  ignoreSpecialChars: boolean;
}

export interface MergeStrategy {
  nameConflictResolution: 'keep_first' | 'keep_last' | 'keep_longest' | 'manual';
  metadataResolution: 'merge_all' | 'prefer_first' | 'prefer_last' | 'manual';
  colorMergeStrategy: 'union' | 'intersection' | 'prefer_primary' | 'manual';
  preserveColorOrder: boolean;
}

export interface DuplicateGroup {
  groupId: string;
  products: ProductDuplicate[];
  primaryProduct: ProductDuplicate; // The product to keep
  confidence: number; // 0-1 confidence score
  reason: string; // Why these are considered duplicates
}

export interface ProductDuplicate {
  id: string;
  name: string;
  sku?: string;
  metadata: any;
  colorCount: number;
  createdAt: string;
  organizationId: string;
  score: number; // Ranking score for merge priority
}

export interface DeduplicationResult {
  success: boolean;
  processedGroups: number;
  mergedProducts: number;
  preservedProducts: number;
  errors: string[];
  warnings: string[];
  performance: {
    executionTimeMs: number;
    groupsAnalyzed: number;
    productsProcessed: number;
  };
  backup?: DeduplicationBackup;
}

export interface DeduplicationBackup {
  backupId: string;
  timestamp: string;
  organizationId: string;
  affectedProducts: Array<{
    id: string;
    originalData: any;
    colorRelationships: Array<{ colorId: string; displayOrder: number }>;
  }>;
}

export interface SimilarityScore {
  overall: number;
  name: number;
  sku: number;
  metadata: number;
}

export interface DeduplicationOptions {
  criteria: DuplicateDetectionCriteria;
  matching: MatchingAlgorithm;
  merge: MergeStrategy;
  dryRun: boolean;
  createBackup: boolean;
  batchSize: number; // For performance optimization
  maxGroupSize: number; // Prevent processing of extremely large duplicate groups
}

// === DEFAULT CONFIGURATIONS ===

export const DEFAULT_DETECTION_CRITERIA: DuplicateDetectionCriteria = {
  name: true,
  sku: true,
  metadata: false,
  fuzzyThreshold: 0.85
};

export const DEFAULT_MATCHING_ALGORITHM: MatchingAlgorithm = {
  type: 'fuzzy',
  threshold: 0.85,
  ignoreCase: true,
  ignoreWhitespace: true,
  ignoreSpecialChars: false
};

export const DEFAULT_MERGE_STRATEGY: MergeStrategy = {
  nameConflictResolution: 'keep_longest',
  metadataResolution: 'merge_all',
  colorMergeStrategy: 'union',
  preserveColorOrder: true
};

export const DEFAULT_DEDUPLICATION_OPTIONS: DeduplicationOptions = {
  criteria: DEFAULT_DETECTION_CRITERIA,
  matching: DEFAULT_MATCHING_ALGORITHM,
  merge: DEFAULT_MERGE_STRATEGY,
  dryRun: false,
  createBackup: true,
  batchSize: 100,
  maxGroupSize: 50
};

// === MAIN SERVICE CLASS ===

export class ProductDeduplicationService {
  private errorHandler: ReturnType<typeof createServiceErrorHandler>;
  private backups: Map<string, DeduplicationBackup> = new Map();

  constructor(
    private db: Database.Database,
    private productRepository: ProductRepository,
    private relationshipService: ProductColorRelationshipService
  ) {
    this.errorHandler = createServiceErrorHandler('ProductDeduplicationService');
  }

  // === PUBLIC METHODS ===

  /**
   * Main deduplication method with configurable options
   */
  deduplicateProducts(
    organizationId: string,
    options: Partial<DeduplicationOptions> = {}
  ): ServiceResult<DeduplicationResult> {
    return this.errorHandler.wrap(() => {
      const startTime = Date.now();
      const sanitizedOrgId = validateRequired('organizationId', organizationId, 'uuid') as string;
      const opts = { ...DEFAULT_DEDUPLICATION_OPTIONS, ...options };

      console.log(`[ProductDeduplicationService] 🔍 Starting enhanced deduplication for organization: ${sanitizedOrgId}`);
      console.log(`[ProductDeduplicationService] Options:`, {
        criteria: opts.criteria,
        matching: opts.matching,
        dryRun: opts.dryRun,
        createBackup: opts.createBackup
      });

      let backup: DeduplicationBackup | undefined;

      // Create backup if requested
      if (opts.createBackup && !opts.dryRun) {
        backup = this.createBackup(sanitizedOrgId);
        console.log(`[ProductDeduplicationService] 💾 Created backup: ${backup.backupId}`);
      }

      // Detect duplicate groups
      const duplicateGroups = this.detectDuplicateGroups(sanitizedOrgId, opts);
      console.log(`[ProductDeduplicationService] 🔍 Found ${duplicateGroups.length} duplicate groups`);

      // Filter groups by size limit
      const processableGroups = duplicateGroups.filter(group => 
        group.products.length <= opts.maxGroupSize
      );
      
      if (processableGroups.length < duplicateGroups.length) {
        console.warn(`[ProductDeduplicationService] ⚠️ Skipped ${duplicateGroups.length - processableGroups.length} groups exceeding max size ${opts.maxGroupSize}`);
      }

      let mergedProducts = 0;
      let preservedProducts = 0;
      const errors: string[] = [];
      const warnings: string[] = [];

      // Process groups in batches for performance
      const batches = this.createBatches(processableGroups, opts.batchSize);
      
      for (const batch of batches) {
        const batchResult = this.processBatch(batch, sanitizedOrgId, opts);
        mergedProducts += batchResult.merged;
        preservedProducts += batchResult.preserved;
        errors.push(...batchResult.errors);
        warnings.push(...batchResult.warnings);
      }

      const endTime = Date.now();

      const result: DeduplicationResult = {
        success: errors.length === 0,
        processedGroups: processableGroups.length,
        mergedProducts,
        preservedProducts,
        errors,
        warnings,
        performance: {
          executionTimeMs: endTime - startTime,
          groupsAnalyzed: duplicateGroups.length,
          productsProcessed: duplicateGroups.reduce((sum, group) => sum + group.products.length, 0)
        },
        backup
      };

      console.log(`[ProductDeduplicationService] ✅ Enhanced deduplication complete:`, {
        processedGroups: result.processedGroups,
        mergedProducts: result.mergedProducts,
        preservedProducts: result.preservedProducts,
        executionTimeMs: result.performance.executionTimeMs
      });

      return result;
    }, 'deduplicateProducts', { organizationId, options });
  }

  /**
   * Find duplicate groups without merging (analysis only)
   */
  analyzeDuplicates(
    organizationId: string,
    options: Partial<DeduplicationOptions> = {}
  ): ServiceResult<DuplicateGroup[]> {
    return this.errorHandler.wrap(() => {
      const sanitizedOrgId = validateRequired('organizationId', organizationId, 'uuid') as string;
      const opts = { ...DEFAULT_DEDUPLICATION_OPTIONS, ...options };

      console.log(`[ProductDeduplicationService] 🔍 Analyzing duplicates for organization: ${sanitizedOrgId}`);

      const duplicateGroups = this.detectDuplicateGroups(sanitizedOrgId, opts);
      
      console.log(`[ProductDeduplicationService] 📊 Analysis complete: ${duplicateGroups.length} duplicate groups found`);
      
      return duplicateGroups;
    }, 'analyzeDuplicates', { organizationId, options });
  }

  /**
   * Restore from backup
   */
  restoreFromBackup(backupId: string): ServiceResult<boolean> {
    return this.errorHandler.wrap(() => {
      const backup = this.backups.get(backupId);
      if (!backup) {
        throw new Error(`Backup ${backupId} not found`);
      }

      console.log(`[ProductDeduplicationService] 🔄 Restoring from backup: ${backupId}`);

      const restoreTransaction = this.db.transaction(() => {
        for (const affectedProduct of backup.affectedProducts) {
          try {
            // Restore product data
            this.restoreProductData(affectedProduct.id, affectedProduct.originalData, backup.organizationId);
            
            // Restore color relationships
            this.restoreColorRelationships(
              affectedProduct.id,
              affectedProduct.colorRelationships,
              backup.organizationId
            );

          } catch (error) {
            console.error(`[ProductDeduplicationService] Failed to restore product ${affectedProduct.id}:`, error);
            throw error;
          }
        }
      });

      restoreTransaction();

      console.log(`[ProductDeduplicationService] ✅ Successfully restored ${backup.affectedProducts.length} products from backup ${backupId}`);
      
      return true;
    }, 'restoreFromBackup', { backupId });
  }

  /**
   * Get available backups for an organization
   */
  getBackups(organizationId: string): ServiceResult<DeduplicationBackup[]> {
    return this.errorHandler.wrap(() => {
      const sanitizedOrgId = validateRequired('organizationId', organizationId, 'uuid') as string;
      
      const orgBackups = Array.from(this.backups.values())
        .filter(backup => backup.organizationId === sanitizedOrgId)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      return orgBackups;
    }, 'getBackups', { organizationId });
  }

  // === PRIVATE METHODS ===

  /**
   * Detect duplicate groups using configured criteria and algorithms
   */
  private detectDuplicateGroups(
    organizationId: string,
    options: DeduplicationOptions
  ): DuplicateGroup[] {
    // Get all products for the organization
    const products = this.getAllProductsWithExtendedData(organizationId);
    
    if (products.length < 2) {
      return []; // Need at least 2 products to have duplicates
    }

    const groups: DuplicateGroup[] = [];
    const processedProducts = new Set<string>();

    // Group products by similarity
    for (let i = 0; i < products.length; i++) {
      const product1 = products[i];
      
      if (!product1 || processedProducts.has(product1.id)) {
        continue; // Already part of a group or invalid product
      }

      const duplicates: ProductDuplicate[] = [product1];
      
      // Find similar products
      for (let j = i + 1; j < products.length; j++) {
        const product2 = products[j];
        
        if (!product2 || processedProducts.has(product2.id)) {
          continue;
        }

        const similarity = this.calculateSimilarity(product1, product2, options);
        
        if (this.areDuplicates(similarity, options)) {
          duplicates.push(product2);
          processedProducts.add(product2.id);
        }
      }

      // Create group if duplicates found
      if (duplicates.length > 1) {
        const primaryProduct = this.selectPrimaryProduct(duplicates, options);
        
        groups.push({
          groupId: uuidv4(),
          products: duplicates,
          primaryProduct,
          confidence: this.calculateGroupConfidence(duplicates, options),
          reason: this.generateDuplicateReason(duplicates, options)
        });

        // Mark primary product as processed
        processedProducts.add(primaryProduct.id);
      }
    }

    return groups;
  }

  /**
   * Get all products with extended data needed for deduplication
   */
  private getAllProductsWithExtendedData(organizationId: string): ProductDuplicate[] {
    const products = this.productRepository.findAll(organizationId);
    
    return products.map(product => {
      // Get color count
      const productColors = this.productRepository.getProductColors(product.id, organizationId);
      
      // Parse metadata
      let metadata = {};
      try {
        metadata = product.metadata ? JSON.parse(product.metadata) : {};
      } catch (error) {
        console.warn(`[ProductDeduplicationService] Failed to parse metadata for product ${product.id}:`, error);
      }

      return {
        id: product.id,
        name: product.name,
        sku: (metadata as any).sku,
        metadata,
        colorCount: productColors.length,
        createdAt: product.created_at,
        organizationId,
        score: this.calculateProductScore(product, productColors.length)
      };
    });
  }

  /**
   * Calculate similarity score between two products
   */
  private calculateSimilarity(
    product1: ProductDuplicate,
    product2: ProductDuplicate,
    options: DeduplicationOptions
  ): SimilarityScore {
    const scores: SimilarityScore = {
      overall: 0,
      name: 0,
      sku: 0,
      metadata: 0
    };

    // Name similarity
    if (options.criteria.name) {
      scores.name = this.calculateStringSimilarity(
        product1.name,
        product2.name,
        options.matching
      );
    }

    // SKU similarity
    if (options.criteria.sku && product1.sku && product2.sku) {
      scores.sku = this.calculateStringSimilarity(
        product1.sku,
        product2.sku,
        options.matching
      );
    }

    // Metadata similarity
    if (options.criteria.metadata) {
      scores.metadata = this.calculateMetadataSimilarity(
        product1.metadata,
        product2.metadata,
        options.matching
      );
    }

    // Calculate overall score
    const weights = {
      name: options.criteria.name ? 0.6 : 0,
      sku: options.criteria.sku ? 0.3 : 0,
      metadata: options.criteria.metadata ? 0.1 : 0
    };

    const totalWeight = weights.name + weights.sku + weights.metadata;
    if (totalWeight > 0) {
      scores.overall = (
        scores.name * weights.name +
        scores.sku * weights.sku +
        scores.metadata * weights.metadata
      ) / totalWeight;
    }

    return scores;
  }

  /**
   * Calculate string similarity using configured algorithm
   */
  private calculateStringSimilarity(
    str1: string,
    str2: string,
    algorithm: MatchingAlgorithm
  ): number {
    if (!str1 || !str2) {
      return 0;
    }

    // Normalize strings
    let normalized1 = str1;
    let normalized2 = str2;

    if (algorithm.ignoreCase) {
      normalized1 = normalized1.toLowerCase();
      normalized2 = normalized2.toLowerCase();
    }

    if (algorithm.ignoreWhitespace) {
      normalized1 = normalized1.replace(/\s+/g, '');
      normalized2 = normalized2.replace(/\s+/g, '');
    }

    if (algorithm.ignoreSpecialChars) {
      normalized1 = normalized1.replace(/[^a-zA-Z0-9]/g, '');
      normalized2 = normalized2.replace(/[^a-zA-Z0-9]/g, '');
    }

    switch (algorithm.type) {
      case 'exact':
        return normalized1 === normalized2 ? 1 : 0;
      
      case 'fuzzy':
        return this.levenshteinSimilarity(normalized1, normalized2);
      
      case 'similarity':
        return this.jaccardSimilarity(normalized1, normalized2);
      
      default:
        return 0;
    }
  }

  /**
   * Calculate Levenshtein similarity (0-1)
   */
  private levenshteinSimilarity(str1: string, str2: string): number {
    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;

    const distance = this.levenshteinDistance(str1, str2);
    return 1 - (distance / maxLength);
  }

  /**
   * Calculate Levenshtein distance
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(0));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0]![i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j]![0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j]![i] = Math.min(
          matrix[j]![i - 1]! + 1, // deletion
          matrix[j - 1]![i]! + 1, // insertion
          matrix[j - 1]![i - 1]! + indicator // substitution
        );
      }
    }

    return matrix[str2.length]![str1.length]!;
  }

  /**
   * Calculate Jaccard similarity for strings
   */
  private jaccardSimilarity(str1: string, str2: string): number {
    const set1 = new Set(str1.split(''));
    const set2 = new Set(str2.split(''));
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return union.size > 0 ? intersection.size / union.size : 0;
  }

  /**
   * Calculate metadata similarity
   */
  private calculateMetadataSimilarity(
    metadata1: any,
    metadata2: any,
    algorithm: MatchingAlgorithm
  ): number {
    if (!metadata1 || !metadata2) {
      return 0;
    }

    const keys1 = Object.keys(metadata1);
    const keys2 = Object.keys(metadata2);
    const allKeys = new Set([...keys1, ...keys2]);

    if (allKeys.size === 0) {
      return 1; // Both empty
    }

    let totalSimilarity = 0;
    let comparableFields = 0;

    for (const key of allKeys) {
      if (key in metadata1 && key in metadata2) {
        const value1 = String(metadata1[key]);
        const value2 = String(metadata2[key]);
        totalSimilarity += this.calculateStringSimilarity(value1, value2, algorithm);
        comparableFields++;
      }
    }

    return comparableFields > 0 ? totalSimilarity / comparableFields : 0;
  }

  /**
   * Determine if products are duplicates based on similarity scores
   */
  private areDuplicates(similarity: SimilarityScore, options: DeduplicationOptions): boolean {
    return similarity.overall >= options.matching.threshold;
  }

  /**
   * Select the primary product from a group of duplicates
   */
  private selectPrimaryProduct(
    duplicates: ProductDuplicate[],
    _options: DeduplicationOptions
  ): ProductDuplicate {
    if (duplicates.length === 0) {
      throw new Error('Cannot select primary product from empty duplicates array');
    }
    
    // Sort by score (highest first)
    const sorted = [...duplicates].sort((a, b) => b.score - a.score);
    return sorted[0]!; // Safe because we already checked duplicates.length > 0
  }

  /**
   * Calculate product score for ranking during merge
   */
  private calculateProductScore(product: any, colorCount: number): number {
    let score = 0;

    // Color count (40% weight)
    score += colorCount * 0.4;

    // Age factor - older products get slight preference (20% weight)
    const ageMs = Date.now() - new Date(product.created_at).getTime();
    const ageDays = ageMs / (1000 * 60 * 60 * 24);
    score += Math.min(ageDays / 365, 1) * 0.2; // Max 1 year age bonus

    // Name length (10% weight) - longer names often more descriptive
    score += Math.min(product.name.length / 50, 1) * 0.1;

    // Metadata richness (20% weight)
    let metadataScore = 0;
    try {
      const metadata = typeof product.metadata === 'string' ? JSON.parse(product.metadata) : product.metadata;
      metadataScore = Object.keys(metadata || {}).length * 0.1;
    } catch (error) {
      // Ignore metadata parsing errors
    }
    score += Math.min(metadataScore, 1) * 0.2;

    // Activity indicator (10% weight)
    const updatedRecently = new Date(product.updated_at).getTime() > Date.now() - (30 * 24 * 60 * 60 * 1000);
    if (updatedRecently) {
      score += 0.1;
    }

    return score;
  }

  /**
   * Calculate confidence score for a duplicate group
   */
  private calculateGroupConfidence(
    duplicates: ProductDuplicate[],
    options: DeduplicationOptions
  ): number {
    if (duplicates.length < 2) {
      return 0;
    }

    let totalConfidence = 0;
    let comparisons = 0;

    // Calculate average similarity within the group
    for (let i = 0; i < duplicates.length; i++) {
      for (let j = i + 1; j < duplicates.length; j++) {
        const productA = duplicates[i];
        const productB = duplicates[j];
        if (!productA || !productB) {
          continue; // Skip if either product is undefined
        }
        const similarity = this.calculateSimilarity(productA, productB, options);
        totalConfidence += similarity.overall;
        comparisons++;
      }
    }

    return comparisons > 0 ? totalConfidence / comparisons : 0;
  }

  /**
   * Generate human-readable reason for duplicate detection
   */
  private generateDuplicateReason(
    duplicates: ProductDuplicate[],
    options: DeduplicationOptions
  ): string {
    const reasons: string[] = [];

    if (options.criteria.name) {
      reasons.push('similar names');
    }
    if (options.criteria.sku) {
      reasons.push('matching SKUs');
    }
    if (options.criteria.metadata) {
      reasons.push('similar metadata');
    }

    const reasonText = reasons.join(', ');
    return `Found ${duplicates.length} products with ${reasonText} (${options.matching.type} matching, threshold: ${options.matching.threshold})`;
  }

  /**
   * Create batches for processing
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Process a batch of duplicate groups
   */
  private processBatch(
    groups: DuplicateGroup[],
    organizationId: string,
    options: DeduplicationOptions
  ): { merged: number; preserved: number; errors: string[]; warnings: string[] } {
    let merged = 0;
    let preserved = 0;
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const group of groups) {
      try {
        if (options.dryRun) {
          console.log(`[ProductDeduplicationService] [DRY RUN] Would merge group ${group.groupId}: ${group.products.length} products`);
          preserved += group.products.length;
        } else {
          const mergeResult = this.mergeProductGroup(group, organizationId, options);
          if (mergeResult.success) {
            merged += group.products.length - 1; // All except primary
            preserved += 1; // Primary product
          } else {
            errors.push(`Failed to merge group ${group.groupId}: ${mergeResult.error}`);
          }
          warnings.push(...mergeResult.warnings);
        }
      } catch (error) {
        const errorMsg = `Error processing group ${group.groupId}: ${error instanceof Error ? error.message : String(error)}`;
        console.error(`[ProductDeduplicationService] ${errorMsg}`);
        errors.push(errorMsg);
      }
    }

    return { merged, preserved, errors, warnings };
  }

  /**
   * Merge a group of duplicate products
   */
  private mergeProductGroup(
    group: DuplicateGroup,
    organizationId: string,
    options: DeduplicationOptions
  ): { success: boolean; error?: string; warnings: string[] } {
    const warnings: string[] = [];

    try {
      const mergeTransaction = this.db.transaction(() => {
        const primaryProduct = group.primaryProduct;
        const duplicateProducts = group.products.filter(p => p.id !== primaryProduct.id);

        console.log(`[ProductDeduplicationService] Merging group ${group.groupId}: keeping ${primaryProduct.id}, merging ${duplicateProducts.length} duplicates`);

        // Merge data into primary product
        const mergedData = this.mergeProductData(group.products, options);
        this.updateProductWithMergedData(primaryProduct.id, mergedData, organizationId);

        // Handle color relationships
        const colorMergeResult = this.mergeColorRelationships(group, organizationId, options);
        warnings.push(...colorMergeResult.warnings);

        // Soft delete duplicate products
        for (const duplicateProduct of duplicateProducts) {
          const deleteSuccess = this.productRepository.softDelete(duplicateProduct.id, organizationId);
          if (!deleteSuccess) {
            warnings.push(`Failed to soft delete duplicate product ${duplicateProduct.id}`);
          }
        }

        console.log(`[ProductDeduplicationService] ✅ Successfully merged group ${group.groupId}`);
      });

      mergeTransaction();

      return { success: true, warnings };

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      console.error(`[ProductDeduplicationService] Failed to merge group ${group.groupId}:`, error);
      return { success: false, error: errorMsg, warnings };
    }
  }

  /**
   * Merge product data according to strategy
   */
  private mergeProductData(products: ProductDuplicate[], options: DeduplicationOptions): any {
    const mergedData: any = {};

    // Merge names
    const names = products.map(p => p.name).filter(Boolean);
    mergedData.name = this.resolveNameConflict(names, options.merge.nameConflictResolution);

    // Merge metadata
    const metadataArray = products.map(p => p.metadata).filter(Boolean);
    mergedData.metadata = this.resolveMetadataConflict(metadataArray, options.merge.metadataResolution);

    return mergedData;
  }

  /**
   * Resolve name conflicts
   */
  private resolveNameConflict(names: string[], strategy: MergeStrategy['nameConflictResolution']): string {
    if (names.length === 0) return '';
    if (names.length === 1) return names[0] ?? '';

    switch (strategy) {
      case 'keep_first':
        return names[0] ?? '';
      case 'keep_last':
        return names[names.length - 1] ?? '';
      case 'keep_longest':
        return names.reduce((longest, current) => current.length > longest.length ? current : longest);
      case 'manual':
        // For now, default to longest
        return names.reduce((longest, current) => current.length > longest.length ? current : longest);
      default:
        return names[0] ?? '';
    }
  }

  /**
   * Resolve metadata conflicts
   */
  private resolveMetadataConflict(metadataArray: any[], strategy: MergeStrategy['metadataResolution']): any {
    if (metadataArray.length === 0) return {};
    if (metadataArray.length === 1) return metadataArray[0];

    switch (strategy) {
      case 'merge_all':
        return metadataArray.reduce((merged, current) => ({ ...merged, ...current }), {});
      case 'prefer_first':
        return metadataArray[0];
      case 'prefer_last':
        return metadataArray[metadataArray.length - 1];
      case 'manual':
        // For now, default to merge_all
        return metadataArray.reduce((merged, current) => ({ ...merged, ...current }), {});
      default:
        return metadataArray[0];
    }
  }

  /**
   * Update product with merged data
   */
  private updateProductWithMergedData(productId: string, mergedData: any, organizationId: string): void {
    const updates: any = {};

    if (mergedData.name) {
      updates.name = mergedData.name;
    }

    if (mergedData.metadata) {
      // Get current metadata and merge
      const current = this.productRepository.findById(productId, organizationId);
      if (current) {
        let currentMetadata = {};
        try {
          currentMetadata = current.metadata ? JSON.parse(current.metadata) : {};
        } catch (error) {
          console.warn(`[ProductDeduplicationService] Failed to parse current metadata for ${productId}`);
        }

        const mergedMetadata = { ...currentMetadata, ...mergedData.metadata };
        updates.description = mergedMetadata.description;
        // Note: Other metadata fields are stored in the metadata JSON
      }
    }

    if (Object.keys(updates).length > 0) {
      this.productRepository.update(productId, updates, organizationId);
    }
  }

  /**
   * Merge color relationships for a group
   */
  private mergeColorRelationships(
    group: DuplicateGroup,
    organizationId: string,
    options: DeduplicationOptions
  ): { warnings: string[] } {
    const warnings: string[] = [];
    const primaryProduct = group.primaryProduct;
    const duplicateProducts = group.products.filter(p => p.id !== primaryProduct.id);

    try {
      // Get current colors for primary product
      const primaryColors = this.relationshipService.getProductColors(primaryProduct.id, organizationId);
      const primaryColorIds = new Set(
        primaryColors.success && primaryColors.data ? primaryColors.data.map(c => c.id) : []
      );

      // Collect all unique colors from duplicate products
      const additionalColors: string[] = [];

      for (const duplicateProduct of duplicateProducts) {
        const duplicateColors = this.relationshipService.getProductColors(duplicateProduct.id, organizationId);
        if (duplicateColors.success && duplicateColors.data) {
          for (const color of duplicateColors.data) {
            if (!primaryColorIds.has(color.id) && !additionalColors.includes(color.id)) {
              additionalColors.push(color.id);
            }
          }
        }
      }

      // Add additional colors to primary product based on strategy
      if (additionalColors.length > 0) {
        switch (options.merge.colorMergeStrategy) {
          case 'union':
            // Add all unique colors
            for (const colorId of additionalColors) {
              const addResult = this.relationshipService.addColorToProduct(primaryProduct.id, colorId, organizationId);
              if (!addResult.success) {
                warnings.push(`Failed to add color ${colorId} to primary product ${primaryProduct.id}: ${addResult.error}`);
              }
            }
            console.log(`[ProductDeduplicationService] Added ${additionalColors.length} additional colors to primary product ${primaryProduct.id}`);
            break;

          case 'intersection':
            // Only keep colors that exist in all products (current behavior is to keep primary's colors)
            console.log(`[ProductDeduplicationService] Using intersection strategy - keeping only primary product's colors`);
            break;

          case 'prefer_primary':
            // Keep only primary product's colors (no action needed)
            console.log(`[ProductDeduplicationService] Using prefer_primary strategy - keeping only primary product's colors`);
            break;

          case 'manual':
            // For now, default to union
            for (const colorId of additionalColors) {
              const addResult = this.relationshipService.addColorToProduct(primaryProduct.id, colorId, organizationId);
              if (!addResult.success) {
                warnings.push(`Failed to add color ${colorId} to primary product ${primaryProduct.id}: ${addResult.error}`);
              }
            }
            warnings.push(`Used union strategy for manual color merge (${additionalColors.length} colors added)`);
            break;
        }
      }

    } catch (error) {
      const warningMsg = `Failed to merge color relationships for group ${group.groupId}: ${error instanceof Error ? error.message : String(error)}`;
      console.warn(`[ProductDeduplicationService] ${warningMsg}`);
      warnings.push(warningMsg);
    }

    return { warnings };
  }

  /**
   * Create a backup before deduplication
   */
  private createBackup(organizationId: string): DeduplicationBackup {
    const backupId = uuidv4();
    const timestamp = new Date().toISOString();

    // Get all products for the organization
    const products = this.productRepository.findAll(organizationId);
    
    const affectedProducts = products.map(product => {
      // Get color relationships
      const colorRelationships = this.productRepository.getProductColors(product.id, organizationId)
        .map(pc => ({
          colorId: pc.color_id,
          displayOrder: pc.display_order
        }));

      return {
        id: product.id,
        originalData: {
          name: product.name,
          description: product.description,
          metadata: product.metadata,
          organizationId: product.organization_id,
          userId: product.user_id,
          isActive: product.is_active,
          isSynced: product.is_synced,
          createdAt: product.created_at,
          updatedAt: product.updated_at,
          deletedAt: product.deleted_at,
          createdBy: product.created_by
        },
        colorRelationships
      };
    });

    const backup: DeduplicationBackup = {
      backupId,
      timestamp,
      organizationId,
      affectedProducts
    };

    this.backups.set(backupId, backup);

    return backup;
  }

  /**
   * Restore product data from backup
   */
  private restoreProductData(productId: string, originalData: any, organizationId: string): void {
    // For simplicity, we'll restore name and metadata via update
    // In a full implementation, you might want to restore all fields
    this.productRepository.update(productId, {
      name: originalData.name,
      description: originalData.description
    }, organizationId);

    // Restore soft delete status
    if (originalData.deletedAt) {
      this.productRepository.softDelete(productId, organizationId);
    }
  }

  /**
   * Restore color relationships from backup
   */
  private restoreColorRelationships(
    productId: string,
    relationships: Array<{ colorId: string; displayOrder: number }>,
    organizationId: string
  ): void {
    // Remove current relationships
    const currentColors = this.productRepository.getProductColors(productId, organizationId);
    for (const pc of currentColors) {
      this.productRepository.removeProductColor(productId, pc.color_id, organizationId);
    }

    // Restore original relationships
    // Note: This simple restoration doesn't preserve display order perfectly
    // In a full implementation, you'd want to restore the exact order
    for (const relationship of relationships) {
      this.productRepository.addProductColor(productId, relationship.colorId, organizationId);
    }
  }
}