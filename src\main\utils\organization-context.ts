/**
 * @file organization-context.ts
 * @description Clean, best-practice organization context management
 */

/**
 * Simple, reliable organization context manager
 * Best practices:
 * - Single source of truth
 * - Synchronous operations
 * - Clear event system
 * - No complex validation layers
 */
export class OrganizationContext {
  private static instance: OrganizationContext;
  private currentOrgId: string | null = null;
  private listeners: Array<(orgId: string | null) => void> = [];

  private constructor() {
    this.loadFromStorage();
  }

  static getInstance(): OrganizationContext {
    if (!OrganizationContext.instance) {
      OrganizationContext.instance = new OrganizationContext();
    }
    return OrganizationContext.instance;
  }

  /**
   * Get current organization ID
   */
  getCurrentOrganization(): string | null {
    return this.currentOrgId;
  }

  /**
   * Set current organization ID
   * Best practice: Synchronous, immediate, with events
   */
  setCurrentOrganization(orgId: string | null): void {
    if (this.currentOrgId !== orgId) {
      this.currentOrgId = orgId;
      this.saveToStorage();
      this.notifyListeners();
      console.log(`[OrganizationContext] Organization changed to: ${orgId}`);
    }
  }

  /**
   * Add organization change listener
   */
  addListener(callback: (orgId: string | null) => void): void {
    this.listeners.push(callback);
  }

  /**
   * Remove organization change listener
   */
  removeListener(callback: (orgId: string | null) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Validate that an organization ID is set
   */
  requireOrganization(): string {
    if (!this.currentOrgId) {
      throw new Error('No organization selected. Please select an organization first.');
    }
    return this.currentOrgId;
  }

  /**
   * Clear organization context
   */
  clear(): void {
    this.setCurrentOrganization(null);
  }

  private loadFromStorage(): void {
    try {
      const { app } = require('electron');
      const fs = require('fs');
      const path = require('path');
      
      const contextFile = path.join(app.getPath('userData'), 'organization-context.json');
      if (fs.existsSync(contextFile)) {
        const data = JSON.parse(fs.readFileSync(contextFile, 'utf8'));
        
        this.currentOrgId = data.currentOrgId || null;
        
        console.log(`[OrganizationContext] Loaded from storage: ${this.currentOrgId}`);
      }
    } catch (error) {
      console.warn('[OrganizationContext] Failed to load from storage:', error);
    }
  }

  private saveToStorage(): void {
    try {
      const { app } = require('electron');
      const fs = require('fs');
      const path = require('path');
      
      const contextFile = path.join(app.getPath('userData'), 'organization-context.json');
      const data = {
        currentOrgId: this.currentOrgId,
        lastUpdated: new Date().toISOString()
      };
      fs.writeFileSync(contextFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.warn('[OrganizationContext] Failed to save to storage:', error);
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      try {
        callback(this.currentOrgId);
      } catch (error) {
        console.error('[OrganizationContext] Error in listener:', error);
      }
    });
  }
}

// Convenience functions for global access
export function getCurrentOrganization(): string | null {
  return OrganizationContext.getInstance().getCurrentOrganization();
}

export function setCurrentOrganization(orgId: string | null): void {
  OrganizationContext.getInstance().setCurrentOrganization(orgId);
}

export function requireOrganization(): string {
  return OrganizationContext.getInstance().requireOrganization();
}

export function onOrganizationChange(callback: (orgId: string | null) => void): void {
  OrganizationContext.getInstance().addListener(callback);
}

export function removeOrganizationChangeListener(callback: (orgId: string | null) => void): void {
  OrganizationContext.getInstance().removeListener(callback);
}
