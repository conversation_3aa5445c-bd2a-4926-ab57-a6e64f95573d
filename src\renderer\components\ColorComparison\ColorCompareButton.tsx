/**
 * @file ColorCompareButton.tsx
 * @description Button to open the color comparison modal
 */

import React, { useState } from 'react';
import { Sparkles } from 'lucide-react';
import { useTokens } from '../../hooks/useTokens';
import ColorComparisonModal from './ColorComparisonModal';

interface ColorCompareButtonProps {
  className?: string;
}

const ColorCompareButton: React.FC<ColorCompareButtonProps> = ({
  className = '',
}) => {
  const tokens = useTokens();
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className={`inline-flex items-center gap-[var(--spacing-2)] px-[var(--spacing-3)] py-[var(--spacing-2)] bg-brand-primary hover:bg-brand-primary/80 active:bg-brand-primary/90 focus:bg-brand-primary focus:ring-2 focus:ring-brand-primary/20 text-white rounded-[var(--radius-md)] text-sm font-medium transition-all duration-200 ease-in-out ${className}`}
        style={{
          transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`,
        }}
      >
        <Sparkles size={16} />
        <span>Analyse Colours</span>
      </button>

      <ColorComparisonModal open={isModalOpen} onOpenChange={setIsModalOpen} />
    </>
  );
};

export default ColorCompareButton;
