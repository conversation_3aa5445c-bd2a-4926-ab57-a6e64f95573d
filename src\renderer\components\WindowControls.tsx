/**
 * @file WindowControls.tsx
 * @description Custom window controls component for frameless window
 */

import { useTokens } from '../hooks/useTokens';
// Import the types to ensure TypeScript recognizes the window object
import '../../shared/types/window.d.ts';

export default function WindowControls() {
  const tokens = useTokens();

  const handleMinimize = () => {
    if (window.electron?.window?.minimize) {
      window.electron.window.minimize();
    }
  };

  const handleMaximize = async () => {
    if (window.electron?.window?.isMaximized && window.electron.window?.maximize && window.electron.window?.unmaximize) {
      const isMaximized = await window.electron.window.isMaximized();
      if (isMaximized) {
        window.electron.window.unmaximize();
      } else {
        window.electron.window.maximize();
      }
    }
  };

  const handleClose = () => {
    if (window.electron?.window?.close) {
      window.electron.window.close();
    }
  };

  const handleToggleDevTools = async () => {
    console.log('[WindowControls] DevTools button clicked');
    console.log('[WindowControls] window.electron available:', !!window.electron);
    console.log('[WindowControls] window.electron.window available:', !!window.electron?.window);
    console.log('[WindowControls] toggleDevTools available:', !!window.electron?.window?.toggleDevTools);
    
    if (window.electron?.window?.toggleDevTools) {
      console.log('[WindowControls] Calling window.electron.window.toggleDevTools()');
      try {
        const result = await window.electron.window.toggleDevTools();
        console.log('[WindowControls] DevTools toggle result:', result ?? 'toggled');
      } catch (error) {
        console.error('[WindowControls] DevTools toggle error:', error);
      }
    } else {
      console.error('[WindowControls] toggleDevTools not available');
      console.error('[WindowControls] Available window methods:', window.electron?.window ? Object.keys(window.electron.window) : 'none');
      
      // Fallback: try to log error
      console.log('[WindowControls] Attempting to log error for debugging');
    }
  };

  // Container classes
  const containerClasses = "window-controls flex items-center -mr-2 z-10";

  // Base button classes
  const baseButtonClasses = "window-control-button p-[var(--spacing-2)]";

  // Regular control button classes (minimize, maximize)
  const controlButtonClasses = `${baseButtonClasses} text-ui-foreground-secondary hover:text-ui-foreground-primary transition-colors`;

  // Close button classes
  const closeButtonClasses = `${baseButtonClasses} text-ui-foreground-secondary hover:text-feedback-error transition-colors`;

  // Common transition styles
  const transitionStyle = {
    transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
  };

  return (
    <div className={containerClasses}>
      {/* DevTools button */}
      <button
        onClick={(e) => {
          console.log('[WindowControls] Button click event fired');
          e.preventDefault();
          e.stopPropagation();
          handleToggleDevTools();
        }}
        className={controlButtonClasses}
        style={transitionStyle}
        title="Toggle DevTools (⌥⌘I)"
        aria-label="Toggle DevTools"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          aria-hidden="true"
        >
          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
        </svg>
      </button>
      {/* Minimize button */}
      <button
        onClick={handleMinimize}
        className={controlButtonClasses}
        style={transitionStyle}
        title="Minimize"
        aria-label="Minimize"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          aria-hidden="true"
        >
          <path d="M14 8H2" />
        </svg>
      </button>

      {/* Maximize/restore button */}
      <button
        onClick={handleMaximize}
        className={controlButtonClasses}
        style={transitionStyle}
        title="Maximize"
        aria-label="Maximize"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          aria-hidden="true"
        >
          <rect x="2" y="2" width="12" height="12" rx="1" />
        </svg>
      </button>

      {/* Close button */}
      <button
        onClick={handleClose}
        className={closeButtonClasses}
        style={transitionStyle}
        title="Close"
        aria-label="Close"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          aria-hidden="true"
        >
          <path d="M4 4L12 12M4 12L12 4" />
        </svg>
      </button>
    </div>
  );
}