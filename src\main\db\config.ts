import { app } from 'electron';
import path from 'path';
import fs from 'fs';
import { createSafeStore } from '../utils/store-util';

interface AppConfig extends Record<string, unknown> {
  location: string;
  useSharedLocation: boolean;
  sharedLocation: string;
  storageModeConfigured: boolean;
  sharedResourceManagerPath: string;
  lastLicenseCheck?: number;
  installDate?: number;
  // New flag for migration status
  useOptimizedDatabase?: boolean;
  // Flag for first run
  isFirstRun?: boolean;
}

// Use our safe store implementation
const configStore = createSafeStore<AppConfig>({
  name: 'chroma-sync-config',
  defaults: {
    location: path.join(app.getPath('userData'), 'chromasync.db'),
    useSharedLocation: false,
    sharedLocation: '',
    storageModeConfigured: false,
    sharedResourceManagerPath: '',
    // Enable optimized database for new installations
    useOptimizedDatabase: true,
  },
});

export { configStore };

export function getDatabasePath(): string {
  if (process.env.E2E_TESTING === 'true' && process.env.TEST_DB_PATH) {
    console.log(
      `[DB Config] Using E2E test database path: ${process.env.TEST_DB_PATH}`
    );
    return process.env.TEST_DB_PATH;
  }

  // Remove check for project directory database - always use user data directory

  const useShared = configStore.get('useSharedLocation');

  if (useShared) {
    const sharedPath = configStore.get('sharedLocation');
    if (sharedPath && fs.existsSync(path.dirname(sharedPath))) {
      return sharedPath;
    }
    console.warn('Shared database location is invalid, falling back to local');
  }

  return configStore.get('location');
}

export function isUsingOptimizedDatabase(): boolean {
  return configStore.get('useOptimizedDatabase', true) ?? true;
}
