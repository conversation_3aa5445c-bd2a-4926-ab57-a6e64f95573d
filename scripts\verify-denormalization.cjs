#!/usr/bin/env node
/**
 * Verify ChromaSync Denormalization Migration
 * This script checks the database state and validates the migration
 */

const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

// Database path
const dbPath = path.join(
  os.homedir(),
  'Library',
  'Application Support',
  'chroma-sync',
  'chromasync.db'
);

console.log('ChromaSync Denormalization Verification\n');
console.log('Database:', dbPath);
console.log('='.repeat(60));

try {
  const db = new Database(dbPath, { readonly: true });

  // Check 1: Verify legacy tables are removed
  console.log('\n1. Checking for legacy tables...');
  const legacyTables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' 
    AND name IN ('color_cmyk', 'color_rgb', 'color_lab', 'color_hsl', 'gradient_stops')
  `).all();

  if (legacyTables.length === 0) {
    console.log('   ✅ All legacy tables removed successfully');
  } else {
    console.log('   ⚠️  Legacy tables still exist:', legacyTables.map(t => t.name).join(', '));
  }

  // Check 2: Verify color_spaces JSON column
  console.log('\n2. Checking color_spaces JSON column...');
  const colorStats = db.prepare(`
    SELECT 
      COUNT(*) as total_colors,
      COUNT(CASE WHEN color_spaces IS NOT NULL THEN 1 END) as has_color_spaces,
      COUNT(CASE WHEN json_valid(color_spaces) THEN 1 END) as valid_json,
      COUNT(CASE WHEN json_extract(color_spaces, '$.cmyk') IS NOT NULL THEN 1 END) as has_cmyk,
      COUNT(CASE WHEN gradient_colors IS NOT NULL THEN 1 END) as gradients
    FROM colors
    WHERE deleted_at IS NULL
  `).get();

  console.log(`   Total colors: ${colorStats.total_colors}`);
  console.log(`   With color_spaces: ${colorStats.has_color_spaces} (${(colorStats.has_color_spaces/colorStats.total_colors*100).toFixed(1)}%)`);
  console.log(`   Valid JSON: ${colorStats.valid_json} (${(colorStats.valid_json/colorStats.total_colors*100).toFixed(1)}%)`);
  console.log(`   With CMYK data: ${colorStats.has_cmyk} (${(colorStats.has_cmyk/colorStats.total_colors*100).toFixed(1)}%)`);
  console.log(`   Gradients: ${colorStats.gradients}`);

  // Check 3: Sample data structure
  console.log('\n3. Sample color data structure...');
  const sampleColor = db.prepare(`
    SELECT 
      code,
      hex,
      color_spaces,
      LENGTH(color_spaces) as json_size,
      gradient_colors
    FROM colors 
    WHERE color_spaces IS NOT NULL 
    AND json_valid(color_spaces)
    LIMIT 1
  `).get();

  if (sampleColor) {
    console.log(`   Code: ${sampleColor.code}`);
    console.log(`   Hex: ${sampleColor.hex}`);
    console.log(`   JSON size: ${sampleColor.json_size} bytes`);
    
    const colorSpaces = JSON.parse(sampleColor.color_spaces);
    console.log(`   Color spaces stored:`, Object.keys(colorSpaces).join(', '));
    
    if (colorSpaces.cmyk) {
      console.log(`   CMYK: C:${colorSpaces.cmyk.c} M:${colorSpaces.cmyk.m} Y:${colorSpaces.cmyk.y} K:${colorSpaces.cmyk.k}`);
    }
  }

  // Check 4: Performance metrics
  console.log('\n4. Performance metrics...');
  
  // Test query performance
  const start = Date.now();
  const allColors = db.prepare(`
    SELECT * FROM colors 
    WHERE deleted_at IS NULL
    LIMIT 1000
  `).all();
  const queryTime = Date.now() - start;
  
  console.log(`   Query 1000 colors: ${queryTime}ms`);
  console.log(`   Average per color: ${(queryTime/1000).toFixed(2)}ms`);

  // Check 5: Data integrity
  console.log('\n5. Data integrity checks...');
  
  // Check for colors without CMYK
  const missingCMYK = db.prepare(`
    SELECT COUNT(*) as count
    FROM colors
    WHERE deleted_at IS NULL
    AND (
      color_spaces IS NULL 
      OR json_extract(color_spaces, '$.cmyk') IS NULL
    )
  `).get();
  
  if (missingCMYK.count === 0) {
    console.log('   ✅ All colors have CMYK data');
  } else {
    console.log(`   ⚠️  ${missingCMYK.count} colors missing CMYK data`);
  }

  // Check for invalid hex values
  const invalidHex = db.prepare(`
    SELECT COUNT(*) as count
    FROM colors
    WHERE deleted_at IS NULL
    AND hex NOT GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'
  `).get();
  
  if (invalidHex.count === 0) {
    console.log('   ✅ All hex values are valid');
  } else {
    console.log(`   ⚠️  ${invalidHex.count} colors with invalid hex values`);
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('MIGRATION STATUS SUMMARY:');
  
  const isFullyMigrated = 
    legacyTables.length === 0 && 
    colorStats.has_cmyk === colorStats.total_colors &&
    missingCMYK.count === 0;
    
  if (isFullyMigrated) {
    console.log('\n✅ Denormalization migration COMPLETE!');
    console.log('   - Legacy tables removed');
    console.log('   - All data migrated to JSON column');
    console.log('   - Ready for production use');
  } else {
    console.log('\n⚠️  Migration INCOMPLETE');
    console.log('   - Review the warnings above');
    console.log('   - Run migration 020_remove_legacy_normalized_tables.sql');
    console.log('   - Update ColorService to remove dual-write logic');
  }

  db.close();
  
} catch (error) {
  console.error('Error:', error.message);
  process.exit(1);
}

console.log('\n');