/**
 * @file InviteMemberForm.tsx
 * @description Form component for inviting new team members
 */

import React, { useState } from 'react';
import { Mail, Loader2, Send } from 'lucide-react';

interface InviteMemberFormProps {
  onInvite: (email: string, role: 'member' | 'admin') => Promise<{ success: boolean; error?: string }>;
  disabled?: boolean;
}

export const InviteMemberForm: React.FC<InviteMemberFormProps> = ({ onInvite, disabled = false }) => {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<'member' | 'admin'>('member');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);

    try {
      const result = await onInvite(email, role);
      if (result.success) {
        setSuccess(true);
        setEmail('');
        setRole('member');
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(result.error || 'Failed to send invitation');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send invitation');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label 
          htmlFor="invite-email" 
          className="block text-sm font-medium mb-2"
          style={{
            fontSize: 'var(--font-size-sm)',
            fontWeight: 'var(--font-weight-medium)',
            color: 'var(--color-ui-foreground-secondary)'
          }}
        >
          Email Address
        </label>
        <div className="relative">
          <Mail 
            className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" 
            style={{ color: 'var(--color-ui-foreground-tertiary)' }}
          />
          <input
            id="invite-email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            className="w-full pl-10 pr-3 py-2 transition-standard"
            style={{
              border: `1px solid var(--form-border)`,
              borderRadius: 'var(--radius-lg)',
              backgroundColor: 'var(--form-background)',
              color: 'var(--color-ui-foreground-primary)',
              fontSize: 'var(--font-size-base)'
            }}
            onFocus={(e) => {
              e.target.style.outline = 'none';
              e.target.style.borderColor = 'var(--form-border-focus)';
              e.target.style.boxShadow = `0 0 0 2px var(--form-border-focus)`;
            }}
            onBlur={(e) => {
              e.target.style.borderColor = 'var(--form-border)';
              e.target.style.boxShadow = 'none';
            }}
            disabled={disabled || isLoading}
            required
          />
        </div>
      </div>

      <div>
        <label 
          htmlFor="invite-role" 
          className="block text-sm font-medium mb-2"
          style={{
            fontSize: 'var(--font-size-sm)',
            fontWeight: 'var(--font-weight-medium)',
            color: 'var(--color-ui-foreground-secondary)'
          }}
        >
          Role
        </label>
        <select
          id="invite-role"
          value={role}
          onChange={(e) => setRole(e.target.value as 'member' | 'admin')}
          className="w-full px-3 py-2 transition-standard"
          style={{
            border: `1px solid var(--form-border)`,
            borderRadius: 'var(--radius-lg)',
            backgroundColor: 'var(--form-background)',
            color: 'var(--color-ui-foreground-primary)',
            fontSize: 'var(--font-size-base)'
          }}
          onFocus={(e) => {
            e.target.style.outline = 'none';
            e.target.style.borderColor = 'var(--form-border-focus)';
            e.target.style.boxShadow = `0 0 0 2px var(--form-border-focus)`;
          }}
          onBlur={(e) => {
            e.target.style.borderColor = 'var(--form-border)';
            e.target.style.boxShadow = 'none';
          }}
          disabled={disabled || isLoading}
        >
          <option value="member">Member - Can view and edit colors</option>
          <option value="admin">Admin - Can manage team and colors</option>
        </select>
      </div>

      {error && (
        <div 
          className="p-3 text-sm"
          style={{
            backgroundColor: 'var(--feedback-bg-error)',
            border: `1px solid var(--feedback-border-error)`,
            color: 'var(--color-feedback-error)',
            borderRadius: 'var(--radius-lg)',
            fontSize: 'var(--font-size-sm)'
          }}
        >
          {error}
        </div>
      )}

      {success && (
        <div 
          className="p-3 text-sm"
          style={{
            backgroundColor: 'var(--feedback-bg-success)',
            border: `1px solid var(--feedback-border-success)`,
            color: 'var(--color-feedback-success)',
            borderRadius: 'var(--radius-lg)',
            fontSize: 'var(--font-size-sm)'
          }}
        >
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0 mt-0.5">
              ✅
            </div>
            <div>
              <p 
                className="font-medium mb-1"
                style={{ 
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-feedback-success)'
                }}
              >
                Invitation sent successfully!
              </p>
              <p 
                className="text-xs"
                style={{
                  fontSize: 'var(--font-size-xs)',
                  color: 'var(--color-feedback-success)'
                }}
              >
                The invitation email includes a manual code that always works, even if email links fail.
              </p>
            </div>
          </div>
        </div>
      )}

      <button
        type="submit"
        disabled={disabled || isLoading || !email.trim()}
        className="w-full py-2 px-4 font-medium transition-standard flex items-center justify-center"
        style={{
          backgroundColor: disabled || isLoading || !email.trim() 
            ? 'var(--button-primary-bg-disabled)' 
            : 'var(--button-primary-bg)',
          color: disabled || isLoading || !email.trim() 
            ? 'var(--button-primary-text-disabled)' 
            : 'var(--button-primary-text)',
          border: 'none',
          borderRadius: 'var(--radius-lg)',
          fontSize: 'var(--font-size-base)',
          fontWeight: 'var(--font-weight-medium)',
          cursor: disabled || isLoading || !email.trim() ? 'not-allowed' : 'pointer'
        }}
        onMouseEnter={(e) => {
          if (!(disabled || isLoading || !email.trim())) {
            e.currentTarget.style.backgroundColor = 'var(--button-primary-bg-hover)';
          }
        }}
        onMouseLeave={(e) => {
          if (!(disabled || isLoading || !email.trim())) {
            e.currentTarget.style.backgroundColor = 'var(--button-primary-bg)';
          }
        }}
        onFocus={(e) => {
          e.target.style.outline = 'none';
          e.target.style.boxShadow = `0 0 0 2px var(--form-border-focus)`;
        }}
        onBlur={(e) => {
          e.target.style.boxShadow = 'none';
        }}
      >
        {isLoading ? (
          <>
            <Loader2 
              className="w-4 h-4 mr-2 animate-spin" 
              style={{ color: 'var(--button-primary-text-disabled)' }}
            />
            Sending Invitation...
          </>
        ) : (
          <>
            <Send 
              className="w-4 h-4 mr-2" 
              style={{ color: 'var(--button-primary-text)' }}
            />
            Send Invitation
          </>
        )}
      </button>
      
      <div 
        className="mt-6 p-4"
        style={{
          backgroundColor: 'var(--feedback-bg-info)',
          border: `1px solid var(--feedback-border-info)`,
          borderRadius: 'var(--radius-lg)'
        }}
      >
        <h4 
          className="text-sm font-medium mb-2"
          style={{
            fontSize: 'var(--font-size-sm)',
            fontWeight: 'var(--font-weight-medium)',
            color: 'var(--color-feedback-info)'
          }}
        >
          📧 How Invitations Work:
        </h4>
        <ul 
          className="text-xs space-y-1"
          style={{
            fontSize: 'var(--font-size-xs)',
            color: 'var(--color-feedback-info)'
          }}
        >
          <li>• Invited users receive an email with a unique invitation code</li>
          <li>• They can join by entering the code in Settings → Team → Join Organization</li>
          <li>• If email links don't work, the manual code always works</li>
          <li>• Failed invitations are automatically retried in the background</li>
        </ul>
      </div>
    </form>
  );
};
