/**
 * @file color-service-e2e.simplified.test.ts
 * @description Simplified end-to-end tests focused on ColorService orchestrator validation
 *
 * This simplified version focuses on testing the core orchestrator pattern functionality
 * without getting caught up in complex database relationships that require extensive setup.
 */

import {
  describe,
  test,
  expect,
  beforeAll,
  afterAll,
  beforeEach,
  vi,
} from 'vitest';
import Database from 'better-sqlite3';
import { ColorService } from '../color.service';
import {
  NewColorEntry,
  UpdateColorEntry,
} from '../../../../shared/types/color.types';

// Mock the sync outbox service
vi.mock('../../../services/sync/sync-outbox.service', () => ({
  syncOutboxService: {
    addToOutbox: vi.fn().mockResolvedValue(undefined),
  },
}));

describe('ColorService E2E Tests - Orchestrator Pattern (Simplified)', () => {
  let db: Database.Database;
  let colorService: ColorService;

  const testOrganizationId = '550e8400-e29b-41d4-a716-446655440000';
  const testUserId = 'user-123';

  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');

    // Set up minimal schema
    db.exec(`
      PRAGMA foreign_keys = ON;
      
      CREATE TABLE color_sources (
        id INTEGER PRIMARY KEY,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        is_system BOOLEAN NOT NULL DEFAULT FALSE
      );

      INSERT INTO color_sources (id, code, name, is_system) VALUES
      (1, 'user', 'User Created', FALSE),
      (2, 'pantone', 'PANTONE®', TRUE),
      (3, 'ral', 'RAL', TRUE);

      CREATE TABLE colors (
        id TEXT PRIMARY KEY,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        source_id INTEGER,
        code TEXT NOT NULL,
        display_name TEXT,
        hex TEXT NOT NULL,
        color_spaces TEXT,
        is_gradient INTEGER DEFAULT 0,
        is_metallic INTEGER DEFAULT 0,
        is_effect INTEGER DEFAULT 0,
        is_library INTEGER DEFAULT 0,
        gradient_colors TEXT,
        notes TEXT,
        tags TEXT,
        properties TEXT,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL,
        FOREIGN KEY (source_id) REFERENCES color_sources(id)
      );

      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL
      );

      CREATE TABLE product_colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id TEXT NOT NULL,
        color_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE organizations (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      INSERT INTO organizations (id, name) VALUES ('${testOrganizationId}', 'Test Organization');
    `);

    // Create ColorService with default dependencies (full integration)
    colorService = new ColorService(db);
  });

  beforeEach(() => {
    // Clear tables before each test
    db.exec(
      `DELETE FROM colors WHERE organization_id = '${testOrganizationId}'`
    );
    db.exec(
      `DELETE FROM product_colors WHERE organization_id = '${testOrganizationId}'`
    );
    db.exec(
      `DELETE FROM products WHERE organization_id = '${testOrganizationId}'`
    );
  });

  afterAll(() => {
    db.close();
  });

  describe('1. Core Orchestrator Functionality', () => {
    test('should orchestrate complete color creation workflow', async () => {
      const newColor: NewColorEntry = {
        name: 'Test Red',
        code: 'TEST-001',
        hex: '#ff0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        source: 'user',
        notes: 'Test color for validation',
        isLibrary: true, // Make it library so it shows up in getAll
      };

      // Execute complete workflow through orchestrator
      const colorId = await colorService.add(
        newColor,
        testUserId,
        testOrganizationId
      );

      // Validate orchestrator results
      expect(colorId).toBeTruthy();
      expect(typeof colorId).toBe('string');

      // Verify color was processed through all services
      const storedColor = colorService.getById(colorId, testOrganizationId);
      expect(storedColor).toBeTruthy();
      expect(storedColor!.name).toBe('Test Red');
      expect(storedColor!.code).toBe('TEST-001');
      expect(storedColor!.hex).toBe('#FF0000'); // Should be standardized by ColorValidator
      expect(storedColor!.isLibrary).toBe(true);

      // Verify color spaces were calculated by ColorSpaceCalculator
      expect(storedColor!.rgb).toBeDefined();
      expect(storedColor!.rgb!.r).toBe(255);
      expect(storedColor!.rgb!.g).toBe(0);
      expect(storedColor!.rgb!.b).toBe(0);

      expect(storedColor!.hsl).toBeDefined();
    });

    test('should validate input through ColorValidator service', async () => {
      const invalidColor: NewColorEntry = {
        name: 'Invalid Color',
        code: 'INV-001',
        hex: 'invalid-hex', // Invalid hex
        source: 'user',
      };

      // Should throw validation error from ColorValidator
      await expect(
        colorService.add(invalidColor, testUserId, testOrganizationId)
      ).rejects.toThrow('Invalid HEX color');
    });

    test('should process gradients through GradientProcessor service', async () => {
      const gradientColor: NewColorEntry = {
        name: 'Sunset Gradient',
        code: 'GRAD-001',
        hex: '#ff6b35',
        gradient: {
          gradientStops: [
            { color: '#ff6b35', position: 0, colorCode: 'SUNSET-1' },
            { color: '#f7931e', position: 0.5, colorCode: 'SUNSET-2' },
            { color: '#ffcd3c', position: 1, colorCode: 'SUNSET-3' },
          ],
          angle: 45,
        },
        source: 'user',
        isLibrary: true,
      };

      const colorId = await colorService.add(
        gradientColor,
        testUserId,
        testOrganizationId
      );
      const storedColor = colorService.getById(colorId, testOrganizationId);

      expect(storedColor).toBeTruthy();
      expect(storedColor!.gradient).toBeDefined();
      expect(storedColor!.gradient!.gradientStops).toHaveLength(3);
      expect(storedColor!.gradient!.angle).toBe(45);

      // Verify GradientProcessor handled the data correctly
      expect(storedColor!.gradient!.gradientStops[0].color).toBe('#ff6b35');
      expect(storedColor!.gradient!.gradientStops[1].position).toBe(0.5);
    });
  });

  describe('2. Service Integration Validation', () => {
    test('should coordinate all services for color updates', async () => {
      // Create initial color
      const initialColor: NewColorEntry = {
        name: 'Original Blue',
        code: 'BLUE-001',
        hex: '#0000ff',
        cmyk: 'C:100 M:100 Y:0 K:0',
        source: 'user',
        isLibrary: true,
      };

      const colorId = await colorService.add(
        initialColor,
        testUserId,
        testOrganizationId
      );

      // Update through orchestrator
      const updates: UpdateColorEntry = {
        hex: '#00ff00', // Change to green
        notes: 'Updated color',
      };

      const success = await colorService.update(
        colorId,
        updates,
        testOrganizationId
      );
      expect(success).toBe(true);

      // Verify orchestrator coordinated all services
      const updatedColor = colorService.getById(colorId, testOrganizationId);
      expect(updatedColor).toBeTruthy();
      expect(updatedColor!.hex).toBe('#00FF00'); // ColorValidator standardization
      expect(updatedColor!.notes).toBe('Updated color');

      // Verify ColorSpaceCalculator recalculated values
      expect(updatedColor!.rgb!.r).toBe(0);
      expect(updatedColor!.rgb!.g).toBe(255);
      expect(updatedColor!.rgb!.b).toBe(0);
    });

    test('should handle soft delete through repository coordination', async () => {
      const testColor: NewColorEntry = {
        name: 'Delete Test',
        code: 'DELETE-001',
        hex: '#ff0000',
        source: 'user',
        isLibrary: true,
      };

      const colorId = await colorService.add(
        testColor,
        testUserId,
        testOrganizationId
      );

      // Verify color exists
      expect(colorService.getById(colorId, testOrganizationId)).toBeTruthy();

      // Soft delete through orchestrator
      const deleteSuccess = await colorService.delete(
        colorId,
        testOrganizationId
      );
      expect(deleteSuccess).toBe(true);

      // Verify color is soft deleted (not returned by getById)
      expect(colorService.getById(colorId, testOrganizationId)).toBeNull();

      // But should be retrievable from soft deleted
      const softDeleted = colorService.getSoftDeleted(testOrganizationId);
      expect(softDeleted).toHaveLength(1);
      expect(softDeleted[0].code).toBe('DELETE-001');
    });

    test('should restore colors through repository coordination', async () => {
      // Create and delete a color
      const testColor: NewColorEntry = {
        name: 'Restore Test',
        code: 'RESTORE-001',
        hex: '#0000ff',
        source: 'user',
        isLibrary: true,
      };

      const colorId = await colorService.add(
        testColor,
        testUserId,
        testOrganizationId
      );
      await colorService.delete(colorId, testOrganizationId);

      // Restore through orchestrator
      const restoreSuccess = colorService.restore(colorId, testOrganizationId);
      expect(restoreSuccess).toBe(true);

      // Verify color is accessible again
      const restoredColor = colorService.getById(colorId, testOrganizationId);
      expect(restoredColor).toBeTruthy();
      expect(restoredColor!.code).toBe('RESTORE-001');
    });
  });

  describe('3. Error Handling Across Service Boundaries', () => {
    test('should handle validation errors gracefully', async () => {
      const invalidColor: NewColorEntry = {
        name: 'Invalid',
        code: 'INV',
        hex: '#invalid', // Invalid hex format
        source: 'user',
      };

      // Should propagate validation error from ColorValidator
      await expect(
        colorService.add(invalidColor, testUserId, testOrganizationId)
      ).rejects.toThrow('Invalid HEX color');
    });

    test('should handle update validation errors', async () => {
      // Create valid color first
      const validColor: NewColorEntry = {
        name: 'Valid Color',
        code: 'VALID-001',
        hex: '#ff0000',
        source: 'user',
        isLibrary: true,
      };

      const colorId = await colorService.add(
        validColor,
        testUserId,
        testOrganizationId
      );

      // Try invalid update
      const invalidUpdate: UpdateColorEntry = {
        hex: 'not-a-hex-color',
      };

      const success = await colorService.update(
        colorId,
        invalidUpdate,
        testOrganizationId
      );
      expect(success).toBe(false); // Should handle validation error gracefully
    });

    test('should handle missing colors gracefully', async () => {
      const nonExistentColor = colorService.getById(
        'non-existent-id',
        testOrganizationId
      );
      expect(nonExistentColor).toBeNull();
    });
  });

  describe('4. Bulk Operations and Performance', () => {
    test('should handle multiple color operations efficiently', async () => {
      const colors: NewColorEntry[] = [];

      // Create 10 test colors
      for (let i = 0; i < 10; i++) {
        colors.push({
          name: `Bulk Color ${i}`,
          code: `BULK-${i.toString().padStart(3, '0')}`,
          hex: `#${((Math.random() * 16777215) | 0).toString(16).padStart(6, '0')}`,
          source: 'user',
          isLibrary: true,
        });
      }

      const startTime = Date.now();
      const colorIds: string[] = [];

      // Add all colors through orchestrator
      for (const color of colors) {
        const colorId = await colorService.add(
          color,
          testUserId,
          testOrganizationId
        );
        colorIds.push(colorId);
      }

      const operationTime = Date.now() - startTime;

      // Verify all colors were created
      expect(colorIds).toHaveLength(10);

      // Retrieve all colors
      const allColors = colorService.getAll(testOrganizationId);
      expect(allColors).toHaveLength(10);

      // Performance should be reasonable (adjust threshold as needed)
      expect(operationTime).toBeLessThan(2000); // Should complete in under 2 seconds

      console.log(
        `Bulk operations: Created and retrieved 10 colors in ${operationTime}ms`
      );
    });

    test('should handle bulk restore operations', async () => {
      // Create and delete multiple colors
      const colorIds: string[] = [];

      for (let i = 0; i < 5; i++) {
        const color: NewColorEntry = {
          name: `Bulk Restore ${i}`,
          code: `BULKRESTORE-${i}`,
          hex: `#${((Math.random() * 16777215) | 0).toString(16).padStart(6, '0')}`,
          source: 'user',
          isLibrary: true,
        };

        const colorId = await colorService.add(
          color,
          testUserId,
          testOrganizationId
        );
        colorIds.push(colorId);

        // Delete the color
        await colorService.delete(colorId, testOrganizationId);
      }

      // Bulk restore through orchestrator
      const restoreResult = colorService.bulkRestore(
        colorIds,
        testOrganizationId
      );

      expect(restoreResult.success).toBe(true);
      expect(restoreResult.restored).toBe(5);

      // Verify all colors are accessible again
      const allColors = colorService.getAll(testOrganizationId);
      expect(allColors).toHaveLength(5);
    });
  });

  describe('5. Orchestrator Pattern Validation', () => {
    test('should ensure no direct database access in service methods', () => {
      // Verify ColorService delegates to repositories and specialized services
      // This is more of a code review test, but we can verify behavior

      const service = colorService as any;

      // Verify dependencies exist
      expect(service.colorRepository).toBeDefined();
      expect(service.colorSpaceCalculator).toBeDefined();
      expect(service.gradientProcessor).toBeDefined();
      expect(service.colorValidator).toBeDefined();
      expect(service.colorSyncService).toBeDefined();
      expect(service.colorAnalyticsService).toBeDefined();
      expect(service.colorMappingService).toBeDefined();
    });

    test('should maintain service isolation through dependency injection', async () => {
      // Test that we can create service with mocked dependencies
      const mockRepository = {
        findAll: vi.fn().mockReturnValue([]),
        findById: vi.fn().mockReturnValue(null),
        insert: vi.fn().mockReturnValue('mock-id'),
        update: vi.fn().mockReturnValue(true),
        softDelete: vi.fn().mockReturnValue(true),
        getUsageCounts: vi.fn().mockReturnValue(new Map()),
        getColorNameProductMap: vi.fn().mockReturnValue(new Map()),
        findUnsynced: vi.fn().mockReturnValue([]),
        findSoftDeleted: vi.fn().mockReturnValue([]),
        restoreRecord: vi.fn().mockReturnValue(true),
        bulkRestoreRecords: vi
          .fn()
          .mockReturnValue({ success: true, restored: 0 }),
        cleanupOldSoftDeleted: vi
          .fn()
          .mockReturnValue({ success: true, cleaned: 0 }),
        clearAll: vi.fn().mockReturnValue(true),
        invalidateOrphans: vi.fn(),
        markAsSynced: vi.fn(),
        getSourceIdByCode: vi.fn().mockReturnValue(1),
        getPreparedStatement: vi.fn(),
      };

      const serviceWithMocks = new ColorService(db, mockRepository as any);

      // Test that orchestrator uses injected dependencies
      serviceWithMocks.getAll(testOrganizationId);
      expect(mockRepository.findAll).toHaveBeenCalledWith(testOrganizationId);
    });
  });
});
