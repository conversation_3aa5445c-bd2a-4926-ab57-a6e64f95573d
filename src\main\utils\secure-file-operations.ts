/**
 * @file secure-file-operations.ts
 * @description Secure file operations with path traversal protection
 */

import * as path from 'path';
import * as fs from 'fs';
import { app } from 'electron';

/**
 * Security configuration for file operations
 */
interface SecurityConfig {
  allowedExtensions?: string[];
  maxFileSize?: number;
  allowedDirectories?: string[];
  blockDangerousPatterns?: boolean;
}

/**
 * Result of path validation
 */
interface PathValidationResult {
  isValid: boolean;
  sanitizedPath?: string;
  error?: string;
  warnings?: string[];
}

/**
 * Default security configuration
 */
const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  allowedExtensions: ['.json', '.txt', '.csv', '.pdf', '.png', '.jpg', '.jpeg', '.svg'],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedDirectories: [
    app.getPath('userData'),
    app.getPath('documents'),
    app.getPath('downloads'),
    app.getPath('temp')
  ],
  blockDangerousPatterns: true
};

/**
 * Dangerous patterns that should be blocked
 */
const DANGEROUS_PATTERNS = [
  /\.\./,           // Path traversal
  /\0/,             // Null bytes
  /[<>:"|?*]/,      // Windows illegal characters
  /[\x00-\x1f]/,    // Control characters
  /^\//,            // Absolute paths starting with /
  /^[a-zA-Z]:\\/,   // Windows absolute paths
  /\\\\|\/\//,     // Double slashes
  /~\//,            // User home directory references
  /\$\{.*\}/,       // Variable expansion patterns
  /%[0-9a-fA-F]{2}/, // URL encoded characters
];

/**
 * System-sensitive directories that should never be accessible
 */
const BLOCKED_DIRECTORIES = [
  '/etc',
  '/bin',
  '/sbin',
  '/usr/bin',
  '/usr/sbin',
  '/sys',
  '/proc',
  '/dev',
  '/root',
  'C:\\Windows',
  'C:\\System32',
  'C:\\Program Files',
  'C:\\ProgramData',
  '/System',
  '/Library/System',
  '/private'
];

/**
 * Secure file operations utility class
 */
export class SecureFileOperations {
  private securityConfig: SecurityConfig;

  constructor(customConfig?: Partial<SecurityConfig>) {
    this.securityConfig = { ...DEFAULT_SECURITY_CONFIG, ...customConfig };
  }

  /**
   * Validate and sanitize a file path
   */
  public validatePath(userPath: string, baseDirectory?: string): PathValidationResult {
    const warnings: string[] = [];

    // Step 1: Basic input validation
    if (!userPath || typeof userPath !== 'string') {
      return { isValid: false, error: 'Invalid path: path must be a non-empty string' };
    }

    if (userPath.length > 260) { // Windows MAX_PATH limitation
      return { isValid: false, error: 'Invalid path: path too long (max 260 characters)' };
    }

    // Step 2: Check for dangerous patterns
    if (this.securityConfig.blockDangerousPatterns) {
      for (const pattern of DANGEROUS_PATTERNS) {
        if (pattern.test(userPath)) {
          return { isValid: false, error: `Dangerous pattern detected in path: ${pattern.source}` };
        }
      }
    }

    // Step 3: Sanitize the path
    let sanitizedPath = this.sanitizePath(userPath);
    
    // Step 4: Resolve to absolute path if base directory provided
    if (baseDirectory) {
      try {
        sanitizedPath = path.resolve(baseDirectory, sanitizedPath);
      } catch (error) {
        return { isValid: false, error: `Failed to resolve path: ${error instanceof Error ? error.message : 'Unknown error'}` };
      }

      // Ensure the resolved path stays within the base directory
      const resolvedBase = path.resolve(baseDirectory);
      if (!sanitizedPath.startsWith(resolvedBase)) {
        return { isValid: false, error: 'Path traversal attempt detected' };
      }
    }

    // Step 5: Check against blocked directories
    const absolutePath = path.resolve(sanitizedPath);
    for (const blockedDir of BLOCKED_DIRECTORIES) {
      const resolvedBlocked = path.resolve(blockedDir);
      if (absolutePath.startsWith(resolvedBlocked)) {
        return { isValid: false, error: `Access to system directory blocked: ${blockedDir}` };
      }
    }

    // Step 6: Check against allowed directories
    if (this.securityConfig.allowedDirectories && this.securityConfig.allowedDirectories.length > 0) {
      const isInAllowedDirectory = this.securityConfig.allowedDirectories.some(allowedDir => {
        const resolvedAllowed = path.resolve(allowedDir);
        return absolutePath.startsWith(resolvedAllowed);
      });

      if (!isInAllowedDirectory) {
        return { isValid: false, error: 'Path not within allowed directories' };
      }
    }

    // Step 7: Validate file extension
    if (this.securityConfig.allowedExtensions && this.securityConfig.allowedExtensions.length > 0) {
      const fileExt = path.extname(sanitizedPath).toLowerCase();
      if (fileExt && !this.securityConfig.allowedExtensions.includes(fileExt)) {
        warnings.push(`File extension ${fileExt} is not in the allowed list`);
      }
    }

    return {
      isValid: true,
      sanitizedPath: absolutePath,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * Sanitize a path by removing dangerous characters and patterns
   */
  private sanitizePath(userPath: string): string {
    // Remove dangerous sequences
    let sanitized = userPath
      .replace(/\.\./g, '')           // Remove .. sequences
      .replace(/\0/g, '')             // Remove null bytes
      .replace(/[<>:"|?*]/g, '')      // Remove illegal characters
      .replace(/[\x00-\x1f]/g, '')    // Remove control characters
      .replace(/\/+/g, '/')           // Normalize multiple slashes
      .replace(/\\+/g, '\\');         // Normalize multiple backslashes

    // Normalize path separators for the current platform
    sanitized = path.normalize(sanitized);

    // Remove leading slashes to prevent absolute path access
    sanitized = sanitized.replace(/^[\/\\]+/, '');

    return sanitized;
  }

  /**
   * Securely read a file with path validation
   */
  public async readFileSecure(filePath: string, baseDirectory?: string): Promise<string> {
    const validation = this.validatePath(filePath, baseDirectory);
    
    if (!validation.isValid) {
      throw new Error(`Secure read failed: ${validation.error}`);
    }

    const securePath = validation.sanitizedPath!;

    // Check if file exists
    if (!fs.existsSync(securePath)) {
      throw new Error(`File not found: ${securePath}`);
    }

    // Check file size if configured
    if (this.securityConfig.maxFileSize) {
      const stats = fs.statSync(securePath);
      if (stats.size > this.securityConfig.maxFileSize) {
        throw new Error(`File too large: ${stats.size} bytes (max: ${this.securityConfig.maxFileSize})`);
      }
    }

    // Verify it's actually a file (not a directory or special file)
    const stats = fs.statSync(securePath);
    if (!stats.isFile()) {
      throw new Error(`Path is not a regular file: ${securePath}`);
    }

    return fs.readFileSync(securePath, 'utf-8');
  }

  /**
   * Securely write a file with path validation
   */
  public async writeFileSecure(filePath: string, content: string, baseDirectory?: string): Promise<void> {
    const validation = this.validatePath(filePath, baseDirectory);
    
    if (!validation.isValid) {
      throw new Error(`Secure write failed: ${validation.error}`);
    }

    const securePath = validation.sanitizedPath!;

    // Check content size if configured
    if (this.securityConfig.maxFileSize) {
      const contentSize = Buffer.byteLength(content, 'utf-8');
      if (contentSize > this.securityConfig.maxFileSize) {
        throw new Error(`Content too large: ${contentSize} bytes (max: ${this.securityConfig.maxFileSize})`);
      }
    }

    // Ensure parent directory exists
    const parentDir = path.dirname(securePath);
    if (!fs.existsSync(parentDir)) {
      fs.mkdirSync(parentDir, { recursive: true });
    }

    // Write file atomically (write to temp file, then rename)
    const tempPath = `${securePath}.tmp.${Date.now()}`;
    try {
      fs.writeFileSync(tempPath, content, 'utf-8');
      fs.renameSync(tempPath, securePath);
    } catch (error) {
      // Clean up temp file if it exists
      if (fs.existsSync(tempPath)) {
        fs.unlinkSync(tempPath);
      }
      throw error;
    }
  }

  /**
   * Securely list files in a directory with path validation
   */
  public async listDirectorySecure(dirPath: string, baseDirectory?: string): Promise<string[]> {
    const validation = this.validatePath(dirPath, baseDirectory);
    
    if (!validation.isValid) {
      throw new Error(`Secure list failed: ${validation.error}`);
    }

    const securePath = validation.sanitizedPath!;

    // Check if directory exists
    if (!fs.existsSync(securePath)) {
      throw new Error(`Directory not found: ${securePath}`);
    }

    // Verify it's actually a directory
    const stats = fs.statSync(securePath);
    if (!stats.isDirectory()) {
      throw new Error(`Path is not a directory: ${securePath}`);
    }

    // List files and filter based on security config
    const files = fs.readdirSync(securePath);
    
    if (this.securityConfig.allowedExtensions && this.securityConfig.allowedExtensions.length > 0) {
      return files.filter(file => {
        const ext = path.extname(file).toLowerCase();
        return this.securityConfig.allowedExtensions!.includes(ext);
      });
    }

    return files;
  }

  /**
   * Check if a file exists securely
   */
  public existsSecure(filePath: string, baseDirectory?: string): boolean {
    const validation = this.validatePath(filePath, baseDirectory);
    
    if (!validation.isValid) {
      return false; // Invalid paths are considered non-existent
    }

    return fs.existsSync(validation.sanitizedPath!);
  }

  /**
   * Get secure absolute path for a file
   */
  public getSecurePath(filePath: string, baseDirectory?: string): string | null {
    const validation = this.validatePath(filePath, baseDirectory);
    return validation.isValid ? validation.sanitizedPath! : null;
  }

  /**
   * Update security configuration
   */
  public updateSecurityConfig(newConfig: Partial<SecurityConfig>): void {
    this.securityConfig = { ...this.securityConfig, ...newConfig };
  }

  /**
   * Get current security configuration
   */
  public getSecurityConfig(): SecurityConfig {
    return { ...this.securityConfig };
  }
}

// Default instance for backward compatibility
export const secureFileOps = new SecureFileOperations();

// Utility functions for common operations
export function validateFilePath(filePath: string, baseDirectory?: string): PathValidationResult {
  return secureFileOps.validatePath(filePath, baseDirectory);
}

export function readFileSecure(filePath: string, baseDirectory?: string): Promise<string> {
  return secureFileOps.readFileSecure(filePath, baseDirectory);
}

export function writeFileSecure(filePath: string, content: string, baseDirectory?: string): Promise<void> {
  return secureFileOps.writeFileSecure(filePath, content, baseDirectory);
}

export function listDirectorySecure(dirPath: string, baseDirectory?: string): Promise<string[]> {
  return secureFileOps.listDirectorySecure(dirPath, baseDirectory);
}

export function getSecurePath(filePath: string, baseDirectory?: string): string | null {
  return secureFileOps.getSecurePath(filePath, baseDirectory);
}