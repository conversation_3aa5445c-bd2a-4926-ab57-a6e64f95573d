/**
 * @file useColorForm.ts
 * @description Custom hook for managing color form state with validation
 */

import { useReducer, useCallback, useEffect } from 'react';
import { ColorEntry, NewColorEntry } from '../../shared/types/color.types';
import { isValidHex, isValidCMYK, hexToRgb, parseCMYK } from '../../shared/utils/color';
import { useOrganizationStore } from '../store/organization.store';

// Types
export interface ColorFormState {
  formData: NewColorEntry;
  validation: {
    product: boolean;
    name: boolean;
    hex: boolean;
    code: boolean;
    cmyk: boolean;
    cmykC: boolean;
    cmykM: boolean;
    cmykY: boolean;
    cmykK: boolean;
    isFormValid: boolean;
  };
  isSubmitting: boolean;
  error: string | null;
  rgb: { r: number; g: number; b: number } | null;
  cmykValues: { c: number; m: number; y: number; k: number } | null;
  isDarkColor: boolean;
}

// Define action types
type ColorFormAction =
  | { type: 'SET_FIELD'; field: keyof NewColorEntry; value: string }
  | { type: 'SET_CMYK_COMPONENT'; component: 'c' | 'm' | 'y' | 'k'; value: string }
  | { type: 'SET_HEX_FROM_PICKER'; value: string }
  | { type: 'SET_FORM_DATA'; formData: NewColorEntry }
  | { type: 'SUBMISSION_STARTED' }
  | { type: 'SUBMISSION_SUCCESSFUL' }
  | { type: 'SUBMISSION_FAILED'; error: string }
  | { type: 'RESET_FORM' }
  | { type: 'VALIDATE_FORM' };

// Default form values factory
export const getDefaultColorData = (defaultProduct: string = 'My Collection', organizationId: string = ''): NewColorEntry => ({
  product: defaultProduct,
  organizationId,
  name: '',
  code: '',
  hex: '#000000',
  cmyk: '',
  notes: '',
});

// Helper function to determine if a color is dark
const calculateIsDarkColor = (rgb: { r: number; g: number; b: number } | null): boolean => {
  if (!rgb) {return false;}

  // Calculate relative luminance using the formula:
  // L = 0.299 * R + 0.587 * G + 0.114 * B
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;

  // Return true if the luminance is less than 0.5 (dark color)
  return luminance < 0.5;
};

// Helper function to validate individual CMYK component
const isValidCMYKComponent = (value: string): boolean => {
  if (value.trim() === '') {
    return true; // Empty is valid (will use 0)
  }
  const num = parseFloat(value);
  return !isNaN(num) && num >= 0 && num <= 100;
};

// Helper function to parse individual CMYK component
const parseCMYKComponent = (value: string): number => {
  if (value.trim() === '') {
    return 0;
  }
  const num = parseFloat(value);
  return Math.max(0, Math.min(100, Math.round(num)));
};

// Helper function to validate the entire form
const validateForm = (data: NewColorEntry, cmykValues?: { c: number; m: number; y: number; k: number } | null): ColorFormState['validation'] => {
  const productValid = data.product.trim() !== '';
  const nameValid = data.name.trim() !== '';
  const hexValid = isValidHex(data.hex);
  const codeValid = data.code.trim() !== '';

  // CMYK is valid if it's either empty (will be auto-derived) or properly formatted
  const cmykValid = data.cmyk.trim() === '' || isValidCMYK(data.cmyk);
  
  // Individual CMYK components are always valid when using individual inputs
  const cmykCValid = true;
  const cmykMValid = true;
  const cmykYValid = true;
  const cmykKValid = true;

  // Form is valid if all required fields are filled (CMYK can be empty)
  const isFormValid = productValid && nameValid && hexValid && codeValid && (cmykValid || (cmykCValid && cmykMValid && cmykYValid && cmykKValid));

  return {
    product: productValid,
    name: nameValid,
    hex: hexValid,
    code: codeValid,
    cmyk: cmykValid,
    cmykC: cmykCValid,
    cmykM: cmykMValid,
    cmykY: cmykYValid,
    cmykK: cmykKValid,
    isFormValid
  };
};

/**
 * Reducer function for color form state
 */
function colorFormReducer(state: ColorFormState, action: ColorFormAction): ColorFormState {
  switch (action.type) {
    case 'SET_FIELD': {
      const updatedFormData = { ...state.formData, [action.field]: action.value };

      // Update validation for specific field
      const fieldValidation = { ...state.validation };

      if (action.field === 'hex') {
        fieldValidation.hex = isValidHex(action.value);
      } else if (action.field === 'cmyk') {
        // CMYK is valid if it's either empty (will be auto-derived) or properly formatted
        fieldValidation.cmyk = action.value.trim() === '' || isValidCMYK(action.value);
      } else if (action.field === 'name') {
        fieldValidation.name = action.value.trim() !== '';
      } else if (action.field === 'code') {
        fieldValidation.code = action.value.trim() !== '';
      } else if (action.field === 'product') {
        fieldValidation.product = action.value.trim() !== '';
      }

      // Update RGB and CMYK values if hex changes
      const rgb = action.field === 'hex' ? hexToRgb(action.value) : state.rgb;
      
      // Parse CMYK values safely
      let cmykValues = state.cmykValues;
      if (action.field === 'cmyk' && action.value.trim() !== '') {
        try {
          cmykValues = parseCMYK(action.value);
        } catch (error) {
          // Keep previous CMYK values if parsing fails
          cmykValues = state.cmykValues;
        }
      }

      // Check if form is valid
      fieldValidation.isFormValid =
        fieldValidation.product &&
        fieldValidation.name &&
        fieldValidation.hex &&
        fieldValidation.code &&
        fieldValidation.cmyk;

      return {
        ...state,
        formData: updatedFormData,
        validation: fieldValidation,
        rgb,
        cmykValues,
        isDarkColor: calculateIsDarkColor(rgb)
      };
    }

    case 'SET_CMYK_COMPONENT': {
      // Update individual CMYK component
      const newValue = parseCMYKComponent(action.value);
      const updatedCmykValues = state.cmykValues ? { ...state.cmykValues } : { c: 0, m: 0, y: 0, k: 0 };
      updatedCmykValues[action.component] = newValue;

      // Convert to CMYK string format for backend compatibility
      const cmykString = `C:${updatedCmykValues.c} M:${updatedCmykValues.m} Y:${updatedCmykValues.y} K:${updatedCmykValues.k}`;
      const updatedFormData = { ...state.formData, cmyk: cmykString };

      // Validate individual component
      const fieldValidation = { ...state.validation };
      fieldValidation[`cmyk${action.component.toUpperCase()}` as keyof typeof fieldValidation] = isValidCMYKComponent(action.value);
      fieldValidation.cmyk = true; // Always valid when using individual inputs

      // Update form validity
      fieldValidation.isFormValid =
        fieldValidation.product &&
        fieldValidation.name &&
        fieldValidation.hex &&
        fieldValidation.code &&
        fieldValidation.cmyk;

      return {
        ...state,
        formData: updatedFormData,
        validation: fieldValidation,
        cmykValues: updatedCmykValues
      };
    }

    case 'SET_HEX_FROM_PICKER': {
      const updatedFormData = { ...state.formData, hex: action.value };
      const rgb = hexToRgb(action.value);

      // Generate validation based on current form values
      const validation = validateForm(updatedFormData);

      return {
        ...state,
        formData: updatedFormData,
        validation,
        rgb,
        isDarkColor: calculateIsDarkColor(rgb)
      };
    }

    case 'SET_FORM_DATA': {
      const validation = validateForm(action.formData);
      const rgb = hexToRgb(action.formData.hex);
      
      // Parse CMYK values safely
      let cmykValues = null;
      if (action.formData.cmyk.trim() !== '') {
        try {
          cmykValues = parseCMYK(action.formData.cmyk);
        } catch (error) {
          // Keep null if parsing fails
          cmykValues = null;
        }
      }

      return {
        ...state,
        formData: action.formData,
        validation,
        rgb,
        cmykValues,
        isDarkColor: calculateIsDarkColor(rgb)
      };
    }

    case 'SUBMISSION_STARTED':
      return {
        ...state,
        isSubmitting: true,
        error: null
      };

    case 'SUBMISSION_SUCCESSFUL':
      return {
        ...state,
        isSubmitting: false
      };

    case 'SUBMISSION_FAILED':
      return {
        ...state,
        isSubmitting: false,
        error: action.error
      };

    case 'RESET_FORM': {
      // Get current organization for reset
      const defaultData = getDefaultColorData('My Collection', state.formData.organizationId);
      return {
        ...state,
        formData: defaultData,
        validation: validateForm(defaultData),
        isSubmitting: false,
        error: null,
        rgb: null,
        cmykValues: { c: 0, m: 0, y: 0, k: 0 },
        isDarkColor: false
      };
    }

    case 'VALIDATE_FORM':
      return {
        ...state,
        validation: validateForm(state.formData)
      };

    default:
      return state;
  }
}

/**
 * Custom hook for managing color form state
 */
export function useColorForm(initialColor?: ColorEntry, defaultProduct?: string) {
  // Get current organization
  const { currentOrganization } = useOrganizationStore();
  const organizationId = currentOrganization?.id || '';

  // Initialize state
  const initialFormData = initialColor
    ? {
        product: initialColor.product,
        organizationId: initialColor.organizationId,
        name: initialColor.name,
        code: initialColor.code,
        hex: initialColor.hex,
        cmyk: initialColor.cmyk,
        notes: initialColor.notes || '',
      }
    : getDefaultColorData(defaultProduct, organizationId);

  // Parse initial CMYK values safely or use defaults
  let initialCmykValues = { c: 0, m: 0, y: 0, k: 0 };
  if (initialFormData.cmyk.trim() !== '') {
    try {
      initialCmykValues = parseCMYK(initialFormData.cmyk);
    } catch (error) {
      // Use default values if parsing fails
      initialCmykValues = { c: 0, m: 0, y: 0, k: 0 };
    }
  }

  const initialState: ColorFormState = {
    formData: initialFormData,
    validation: validateForm(initialFormData),
    isSubmitting: false,
    error: null,
    rgb: hexToRgb(initialFormData.hex),
    cmykValues: initialCmykValues,
    isDarkColor: calculateIsDarkColor(hexToRgb(initialFormData.hex))
  };

  const [state, dispatch] = useReducer(colorFormReducer, initialState);

  // Update form when initialColor changes
  useEffect(() => {
    if (initialColor) {
      dispatch({
        type: 'SET_FORM_DATA',
        formData: {
          product: initialColor.product,
          organizationId: initialColor.organizationId,
          name: initialColor.name,
          code: initialColor.code,
          hex: initialColor.hex,
          cmyk: initialColor.cmyk,
          notes: initialColor.notes || '',
        }
      });
    } else {
      dispatch({ type: 'RESET_FORM' });
    }
  }, [initialColor]);

  // Action handlers
  const setField = useCallback((field: keyof NewColorEntry, value: string) => {
    dispatch({ type: 'SET_FIELD', field, value });
  }, []);

  const setHexFromColorPicker = useCallback((hex: string) => {
    dispatch({ type: 'SET_HEX_FROM_PICKER', value: hex });
  }, []);

  const setCmykComponent = useCallback((component: 'c' | 'm' | 'y' | 'k', value: string) => {
    dispatch({ type: 'SET_CMYK_COMPONENT', component, value });
  }, []);

  const startSubmission = useCallback(() => {
    dispatch({ type: 'VALIDATE_FORM' });

    if (state.validation.isFormValid) {
      dispatch({ type: 'SUBMISSION_STARTED' });
      return true;
    }

    return false;
  }, [state.validation.isFormValid]);

  const submissionSuccessful = useCallback(() => {
    dispatch({ type: 'SUBMISSION_SUCCESSFUL' });
  }, []);

  const submissionFailed = useCallback((error: string) => {
    dispatch({ type: 'SUBMISSION_FAILED', error });
  }, []);

  const resetForm = useCallback(() => {
    dispatch({ type: 'RESET_FORM' });
  }, []);

  return {
    ...state,
    setField,
    setHexFromColorPicker,
    setCmykComponent,
    startSubmission,
    submissionSuccessful,
    submissionFailed,
    resetForm,
    rgbString: state.rgb ? `rgb(${state.rgb.r}, ${state.rgb.g}, ${state.rgb.b})` : '',
  };
}