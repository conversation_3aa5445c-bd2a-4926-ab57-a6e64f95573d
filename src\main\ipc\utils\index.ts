/**
 * @file index.ts
 * @description Main export file for IPC error handling and validation utilities
 * 
 * This file provides a centralized export point for all IPC utilities,
 * making it easy to import the needed functions throughout the application.
 */

// ============================================================================
// CORE ERROR HANDLING UTILITIES
// ============================================================================

export {
  // Error types and enums
  ErrorCategory,
  ErrorSeverity,
} from './ipc-error-handling';

export type {
  IPCError,
  ValidationResult,
  ValidationError,
  
} from './ipc-error-handling';

export {
  // Response creation utilities
  createSuccessResponse,
  createErrorResponse,
  createErrorResponseFromError,
  createValidationErrorResponse,
  
  // Error classification and logging
  classifyError,
  logError,
  
  // Input validation utilities
  validateRequiredFields,
  validateFieldTypes,
  validateStringFormats,
  validateNumericRanges,
  combineValidationResults,
  
  // Context validation utilities
  validateOrganizationContext,
  validateAuthenticationContext,
  validateFullContext,
  
  // Business rule validation helpers
  validateResourceExists,
  validateResourceDoesNotExist,
  validatePermissions,
  
  // Handler wrapper utilities
  withErrorHandling,
  withValidation,
  
  // Convenience objects
  validate,
  respond,
  wrap
} from './ipc-error-handling';

// ============================================================================
// MIGRATION UTILITIES
// ============================================================================

export {
  // Legacy response types
  convertLegacyResponse,
  migrateLegacyHandler,
  createValidationFromLegacy,
  
  // Common migration patterns
  migrateCRUDHandler,
  migrateWithBasicValidation,
  migrateSyncHandler,
  
  // Batch migration utilities
  batchMigrateHandlers,
  exampleMigrationConfigs,
  runExampleMigrations,
  
  // Migration validation
  validateMigratedHandler,
  validateMigrations
} from './migration-helper';

export type { HandlerMigrationConfig } from './migration-helper';

// ============================================================================
// USAGE EXAMPLES (for reference)
// ============================================================================

export {
  // Example handlers
  registerExampleHandlers,
  
  // Utility functions
  createColorValidator,
  createResourceHandler
} from './ipc-usage-examples';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

// Re-export relevant types from shared
export type { IPCResponse, IPCHandlerOptions } from '../../../shared/types/ipc.types';

// ============================================================================
// CONVENIENCE IMPORTS
// ============================================================================

// Import functions for convenience objects
import {
  validateRequiredFields,
  validateFieldTypes, 
  validateStringFormats,
  validateNumericRanges,
  validateAuthenticationContext,
  validateOrganizationContext,
  validateFullContext,
  combineValidationResults,
  createSuccessResponse as createSuccess,
  createErrorResponseFromError as createErrorFromError,
  createValidationErrorResponse as createValidationError,
  withErrorHandling as withError,
  withValidation as withValidate,
  classifyError,
  ValidationResult
} from './ipc-error-handling';

import { IPCResponse } from '../../../shared/types/ipc.types';

import {
  migrateLegacyHandler,
  migrateCRUDHandler,
  migrateSyncHandler,
  batchMigrateHandlers,
  validateMigratedHandler
} from './migration-helper';

/**
 * Quick access to commonly used validation functions
 */
export const quickValidate = {
  /**
   * Validates required fields are present
   * @example quickValidate.required(data, ['name', 'email'])
   */
  required: validateRequiredFields,
  
  /**
   * Validates field types
   * @example quickValidate.types(data, { name: 'string', age: 'number' })
   */
  types: validateFieldTypes,
  
  /**
   * Validates string formats
   * @example quickValidate.formats(data, { email: 'email', color: 'hex' })
   */
  formats: validateStringFormats,
  
  /**
   * Validates numeric ranges
   * @example quickValidate.ranges(data, { age: { min: 0, max: 120 } })
   */
  ranges: validateNumericRanges,
  
  /**
   * Validates authentication context
   * @example await quickValidate.auth()
   */
  auth: validateAuthenticationContext,
  
  /**
   * Validates organization context
   * @example await quickValidate.org()
   */
  org: validateOrganizationContext,
  
  /**
   * Validates both auth and org context
   * @example await quickValidate.context()
   */
  context: validateFullContext
};

/**
 * Quick access to response creation functions
 */
export const quickRespond = {
  /**
   * Creates success response
   * @example quickRespond.ok(data, 'Success message')
   */
  ok: createSuccess,
  
  /**
   * Creates error response from error object
   * @example quickRespond.error(error, 'User friendly message')
   */
  error: createErrorFromError,
  
  /**
   * Creates validation error response
   * @example quickRespond.invalid(validationResult, 'Please check your input')
   */
  invalid: createValidationError
};

/**
 * Quick access to handler wrappers
 */
export const quickWrap = {
  /**
   * Wraps handler with error handling
   * @example quickWrap.safe(handler, { channel: 'my-channel' })
   */
  safe: withError,
  
  /**
   * Wraps handler with validation and error handling
   * @example quickWrap.validated(handler, validator, { channel: 'my-channel' })
   */
  validated: withValidate
};

// ============================================================================
// COMMON VALIDATION PATTERNS
// ============================================================================

/**
 * Pre-configured validation functions for common use cases
 */
export const commonValidators = {
  /**
   * Standard user input validation (name, email required)
   */
  user: (data: { name?: string; email?: string; [key: string]: any }) => 
    combineValidationResults(
      validateRequiredFields(data, ['name', 'email']),
      validateFieldTypes(data, { name: 'string', email: 'string' }),
      validateStringFormats(data, { email: 'email' })
    ),
  
  /**
   * Standard color input validation (name, hex required)
   */
  color: (data: { name?: string; hex?: string; [key: string]: any }) =>
    combineValidationResults(
      validateRequiredFields(data, ['name', 'hex']),
      validateFieldTypes(data, { name: 'string', hex: 'string' }),
      validateStringFormats(data, { hex: 'hex' })
    ),
  
  /**
   * Standard product input validation (name required)
   */
  product: (data: { name?: string; [key: string]: any }) =>
    combineValidationResults(
      validateRequiredFields(data, ['name']),
      validateFieldTypes(data, { name: 'string' })
    ),
  
  /**
   * ID validation (single required ID field)
   */
  id: (data: { id?: string; [key: string]: any }) =>
    combineValidationResults(
      validateRequiredFields(data, ['id']),
      validateFieldTypes(data, { id: 'string' })
    )
};

/**
 * Pre-configured handler options for common scenarios
 */
export const commonOptions = {
  /**
   * Standard authenticated operation (auth + org required)
   */
  authenticated: {
    requireAuth: true,
    requireOrganization: true
  },
  
  /**
   * System operation (no auth/org required)
   */
  system: {
    requireAuth: false,
    requireOrganization: false
  },
  
  /**
   * User operation (auth required, org optional)
   */
  user: {
    requireAuth: true,
    requireOrganization: false
  },
  
  /**
   * Public operation (no requirements)
   */
  public: {
    requireAuth: false,
    requireOrganization: false
  }
};

// ============================================================================
// DEFAULT EXPORT
// ============================================================================

/**
 * Default export provides the most commonly used utilities
 */
export default {
  // Validation
  validate: quickValidate,
  validators: commonValidators,
  
  // Response creation
  respond: quickRespond,
  
  // Handler wrapping
  wrap: quickWrap,
  
  // Common options
  options: commonOptions,
  
  // Error classification
  classifyError,
  
  // Migration helpers
  migrate: {
    legacy: migrateLegacyHandler,
    crud: migrateCRUDHandler,
    sync: migrateSyncHandler,
    batch: batchMigrateHandlers,
    validate: validateMigratedHandler
  }
};

// ============================================================================
// UTILITY TYPES FOR CONSUMERS
// ============================================================================

/**
 * Helper type for creating typed IPC handlers
 */
export type TypedIPCHandler<TRequest, TResponse> = (
  request: TRequest
) => Promise<IPCResponse<TResponse>>;

/**
 * Helper type for validation functions
 */
export type ValidatorFunction<T> = (data: T) => ValidationResult | Promise<ValidationResult>;

/**
 * Helper type for business logic functions (pre-validation)
 */
export type BusinessLogicFunction<TArgs extends any[], TResult> = (
  ...args: TArgs
) => Promise<TResult> | TResult;