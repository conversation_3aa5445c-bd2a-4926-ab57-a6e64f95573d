/**
 * @file color-sync-service-integration.test.ts
 * @description Integration tests for ColorSyncService with Supabase
 *
 * Tests the synchronization operations that will be extracted from ColorService
 * into a dedicated ColorSyncService. These tests prepare for the service extraction
 * by ensuring all sync functionality works correctly with Supabase.
 */

import {
  describe,
  test,
  expect,
  beforeEach,
  afterEach,
  vi,
  Mock,
} from 'vitest';
import { ColorService } from '../../../db/services/color.service';
import { ColorRepository } from '../../../db/repositories/color.repository';
import { ColorSpaceCalculator } from '../color-space-calculator.service';
import { GradientProcessor } from '../gradient-processor.service';
import { ColorValidator } from '../color-validator.service';

// Mock database and repository for testing
const mockDb = {
  prepare: vi.fn(() => ({
    get: vi.fn(() => ({ id: 1 })),
    run: vi.fn(() => ({ changes: 1, lastInsertRowid: 1 })),
    all: vi.fn(() => []),
  })),
  exec: vi.fn(),
  close: vi.fn(),
} as any;

const mockColorRepository = {
  findAll: vi.fn(() => []),
  findById: vi.fn(() => null),
  insert: vi.fn(() => 'test-id'),
  update: vi.fn(() => true),
  softDelete: vi.fn(() => true),
  markAsSynced: vi.fn(() => true),
  invalidateOrphans: vi.fn(() => true),
  clearAll: vi.fn(() => true),
  findSoftDeleted: vi.fn(() => []),
  restoreRecord: vi.fn(() => true),
  bulkRestoreRecords: vi.fn(() => ({ success: true, restored: 0 })),
  cleanupOldSoftDeleted: vi.fn(() => ({ success: true, cleaned: 0 })),
  getUsageCounts: vi.fn(() => new Map()),
  getColorNameProductMap: vi.fn(() => new Map()),
  findUnsynced: vi.fn(() => []),
} as any;

// Mock Supabase client
const mockSupabaseClient = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        data: [],
        error: null,
      })),
      data: [],
      error: null,
    })),
    upsert: vi.fn(() => ({
      data: null,
      error: null,
    })),
    insert: vi.fn(() => ({
      data: null,
      error: null,
    })),
    update: vi.fn(() => ({
      data: null,
      error: null,
    })),
    delete: vi.fn(() => ({
      data: null,
      error: null,
    })),
  })),
};

// Mock sync outbox service
const mockSyncOutboxService = {
  addToOutbox: vi.fn(),
};

// Mock Supabase client module
vi.mock('../../services/supabase-client', () => ({
  getSupabaseClient: () => mockSupabaseClient,
  ensureAuthenticatedSession: () => ({
    session: {
      user: {
        id: 'user-123',
        email: '<EMAIL>',
      },
    },
    error: null,
  }),
}));

// Mock sync outbox service module
vi.mock('../../services/sync/sync-outbox.service', () => ({
  syncOutboxService: mockSyncOutboxService,
}));

// Mock organization service for dependency sync
vi.mock('../../../db/services/organization.service', () => ({
  OrganizationService: class MockOrganizationService {
    constructor() {}
    syncOrganizationsFromSupabase = vi.fn(() => Promise.resolve());
  },
}));

describe('ColorSyncService Integration Tests', () => {
  let colorService: ColorService;
  let colorValidator: ColorValidator;
  let colorSpaceCalculator: ColorSpaceCalculator;
  let gradientProcessor: GradientProcessor;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Create service instances
    colorValidator = new ColorValidator();
    colorSpaceCalculator = new ColorSpaceCalculator();
    gradientProcessor = new GradientProcessor();
    colorService = new ColorService(
      mockDb,
      mockColorRepository,
      colorSpaceCalculator,
      gradientProcessor,
      colorValidator
    );
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('pushColorToSupabase', () => {
    test('should successfully push color to Supabase with authentication', async () => {
      const testColor = {
        id: 'color-123',
        code: 'TEST-001',
        name: 'Test Red',
        hex: '#FF0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        rgb: { r: 255, g: 0, b: 0 },
        hsl: { h: 0, s: 100, l: 50 },
        lab: { l: 53, a: 80, b: 67 },
        product: 'Test Product',
        organizationId: 'org-123',
        isLibrary: false,
        notes: 'Test color',
        tags: 'red,primary',
      };

      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: null,
      });

      await colorService.pushColorToSupabase(
        'color-123',
        'org-123',
        'user-123'
      );

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('colors');
      expect(mockSupabaseClient.from().upsert).toHaveBeenCalledWith({
        id: 'color-123', // Direct UUID mapping
        organization_id: 'org-123',
        user_id: 'user-123',
        name: 'Test Red',
        code: 'TEST-001',
        hex: '#FF0000',
        color_spaces: {
          cmyk: { c: 0, m: 100, y: 100, k: 0 },
          rgb: { r: 255, g: 0, b: 0 },
          hsl: { h: 0, s: 100, l: 50 },
          lab: { l: 53, a: 80, b: 67 },
        },
        is_gradient: false,
        gradient_colors: null,
        notes: 'Test color',
        tags: 'red,primary',
        is_library: false,
        updated_at: expect.any(String),
      });
      expect(mockColorRepository.markAsSynced).toHaveBeenCalledWith(
        'color-123'
      );
    });

    test('should handle gradient colors during Supabase push', async () => {
      const gradientColor = {
        id: 'gradient-123',
        code: 'GRAD-001',
        name: 'Test Gradient',
        hex: '#FF0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        organizationId: 'org-123',
        gradient: {
          gradientStops: [
            { color: '#FF0000', position: 0.0 },
            { color: '#00FF00', position: 0.5 },
            { color: '#0000FF', position: 1.0 },
          ],
          angle: 45,
        },
      };

      mockColorRepository.findById.mockReturnValue(gradientColor);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: null,
      });

      await colorService.pushColorToSupabase(
        'gradient-123',
        'org-123',
        'user-123'
      );

      expect(mockSupabaseClient.from().upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          is_gradient: true,
          gradient_colors: expect.stringContaining('#FF0000,#00FF00,#0000FF'),
        })
      );
    });

    test('should handle Supabase authentication errors', async () => {
      vi.mocked(
        require('../../services/supabase-client').ensureAuthenticatedSession
      ).mockReturnValue({
        session: null,
        error: 'No authenticated session',
      });

      await expect(
        colorService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('No authenticated session');
    });

    test('should handle Supabase upsert errors', async () => {
      const testColor = {
        id: 'color-123',
        code: 'TEST-001',
        name: 'Test Red',
        hex: '#FF0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        organizationId: 'org-123',
      };

      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: { message: 'Database error', code: 'PGRST301' },
      });

      await expect(
        colorService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow();
    });

    test('should handle missing color during push', async () => {
      mockColorRepository.findById.mockReturnValue(null);

      await expect(
        colorService.pushColorToSupabase(
          'nonexistent-123',
          'org-123',
          'user-123'
        )
      ).rejects.toThrow('Color nonexistent-123 not found');
    });
  });

  describe('syncColorsFromSupabase', () => {
    test('should successfully sync colors from Supabase', async () => {
      const supabaseColors = [
        {
          id: 'color-123', // Direct UUID mapping
          code: 'TEST-001',
          display_name: 'Test Red',
          hex: '#FF0000',
          color_spaces: {
            cmyk: { c: 0, m: 100, y: 100, k: 0 },
          },
          gradient_colors: null,
          notes: 'Test color',
          tags: 'red,primary',
          is_library: false,
          created_at: '2023-01-01T00:00:00.000Z',
          updated_at: '2023-01-01T00:00:00.000Z',
        },
        {
          id: 'color-456', // Direct UUID mapping
          code: 'TEST-002',
          display_name: 'Test Blue',
          hex: '#0000FF',
          color_spaces: {
            cmyk: { c: 100, m: 100, y: 0, k: 0 },
          },
          gradient_colors: null,
          notes: null,
          tags: null,
          is_library: true,
          created_at: '2023-01-01T00:00:00.000Z',
          updated_at: '2023-01-01T00:00:00.000Z',
        },
      ];

      // Mock organization exists locally
      mockDb.prepare().get.mockReturnValue({ id: 1 });

      // Mock Supabase responses
      mockSupabaseClient.from().select().eq.mockReturnValue({
        data: supabaseColors,
        error: null,
      });

      mockSupabaseClient.from().select.mockReturnValue({
        count: 2,
        error: null,
      });

      // Mock color doesn't exist locally (will be inserted)
      mockDb.prepare().get.mockReturnValue(undefined);

      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toHaveLength(2);
      expect(syncedColors[0].code).toBe('TEST-001');
      expect(syncedColors[1].code).toBe('TEST-002');
      expect(mockColorRepository.insert).toHaveBeenCalledTimes(2);
    });

    test('should update existing colors during sync', async () => {
      const supabaseColor = {
        id: 'color-123', // Direct UUID mapping
        code: 'TEST-001-UPDATED',
        display_name: 'Updated Red',
        hex: '#FF0000',
        color_spaces: {
          cmyk: { c: 0, m: 100, y: 100, k: 0 },
        },
      };

      // Mock organization exists locally
      mockDb.prepare().get.mockReturnValue({ id: 1 });

      mockSupabaseClient
        .from()
        .select()
        .eq.mockReturnValue({
          data: [supabaseColor],
          error: null,
        });

      mockSupabaseClient.from().select.mockReturnValue({
        count: 1,
        error: null,
      });

      // Mock color exists locally (will be updated)
      mockDb.prepare().get.mockReturnValue({ id: 'existing-id' });

      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(mockColorRepository.update).toHaveBeenCalledWith(
        'color-123',
        expect.objectContaining({
          display_name: 'Updated Red',
        }),
        'org-123',
        true
      );
    });

    test('should handle organization sync when org missing locally', async () => {
      // Mock organization doesn't exist locally
      mockDb.prepare().get.mockReturnValue(undefined);

      mockSupabaseClient.from().select().eq.mockReturnValue({
        data: [],
        error: null,
      });

      mockSupabaseClient.from().select.mockReturnValue({
        count: 0,
        error: null,
      });

      // Mock successful organization sync
      const MockOrganizationService =
        require('../../../db/services/organization.service').OrganizationService;
      const mockOrgService = new MockOrganizationService();

      // After org sync, organization should exist
      mockDb
        .prepare()
        .get.mockReturnValueOnce(undefined)
        .mockReturnValueOnce({ id: 1 });

      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(mockOrgService.syncOrganizationsFromSupabase).toHaveBeenCalledWith(
        'user-123'
      );
      expect(syncedColors).toEqual([]);
    });

    test('should handle authentication failure during sync', async () => {
      vi.mocked(
        require('../../services/supabase-client').ensureAuthenticatedSession
      ).mockReturnValue({
        session: null,
        error: 'Authentication failed',
      });

      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle Supabase query errors during sync', async () => {
      mockSupabaseClient.from().select.mockReturnValue({
        count: null,
        error: { message: 'RLS policy violation', code: 'PGRST116' },
      });

      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle partial sync failures gracefully', async () => {
      const supabaseColors = [
        {
          id: 'color-123', // Direct UUID mapping
          code: 'VALID-001',
          display_name: 'Valid Color',
          hex: '#FF0000',
        },
        {
          id: 'color-456', // Direct UUID mapping
          code: 'INVALID-001',
          // Missing required fields to trigger error
          hex: null,
        },
      ];

      mockDb.prepare().get.mockReturnValue({ id: 1 });
      mockSupabaseClient.from().select().eq.mockReturnValue({
        data: supabaseColors,
        error: null,
      });
      mockSupabaseClient.from().select.mockReturnValue({
        count: 2,
        error: null,
      });

      // Mock repository methods
      mockColorRepository.insert.mockImplementation(colorData => {
        if (!colorData.hex) {
          throw new Error('Invalid color data');
        }
        return 'success-id';
      });

      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      // Should return only successfully synced colors
      expect(syncedColors).toHaveLength(1);
      expect(syncedColors[0].code).toBe('VALID-001');
    });
  });

  describe('Sync Status Management', () => {
    test('should mark colors as synced after successful push', async () => {
      const testColor = {
        id: 'color-123',
        code: 'TEST-001',
        name: 'Test Red',
        hex: '#FF0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        organizationId: 'org-123',
      };

      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: null,
      });

      await colorService.pushColorToSupabase(
        'color-123',
        'org-123',
        'user-123'
      );

      expect(mockColorRepository.markAsSynced).toHaveBeenCalledWith(
        'color-123'
      );
    });

    test('should not mark color as synced on push failure', async () => {
      const testColor = {
        id: 'color-123',
        code: 'TEST-001',
        name: 'Test Red',
        hex: '#FF0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        organizationId: 'org-123',
      };

      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: { message: 'Network error' },
      });

      await expect(
        colorService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow();

      expect(mockColorRepository.markAsSynced).not.toHaveBeenCalled();
    });

    test('should get unsynced colors for batch operations', () => {
      const unsyncedColors = [
        { id: 'color-1', code: 'UNSYNC-001', hex: '#FF0000' }, // Direct UUID mapping
        { id: 'color-2', code: 'UNSYNC-002', hex: '#00FF00' }, // Direct UUID mapping
      ];

      mockColorRepository.findUnsynced.mockReturnValue(unsyncedColors);

      const result = colorService.getUnsynced();

      expect(result).toHaveLength(2);
      expect(mockColorRepository.findUnsynced).toHaveBeenCalled();
    });
  });

  describe('Sync Data Validation', () => {
    test('should validate color data before pushing to Supabase', async () => {
      const invalidColor = {
        id: 'color-123',
        code: '', // Invalid: empty code
        name: '', // Invalid: empty name
        hex: 'invalid-hex', // Invalid: malformed hex
        cmyk: 'C:150 M:0 Y:0 K:0', // Invalid: out of range
        organizationId: 'org-123',
      };

      mockColorRepository.findById.mockReturnValue(invalidColor);

      // The ColorValidator should catch these issues during sync preparation
      const syncData = colorValidator.validateForSync({
        hex: invalidColor.hex,
        externalId: invalidColor.id,
      });

      expect(syncData.isValid).toBe(false);
      expect(syncData.errors.length).toBeGreaterThan(0);
    });

    test('should handle CMYK parsing during sync operations', async () => {
      const colorWithCMYK = {
        id: 'color-123',
        code: 'TEST-001',
        name: 'Test Color',
        hex: '#FF0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        organizationId: 'org-123',
      };

      mockColorRepository.findById.mockReturnValue(colorWithCMYK);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: null,
      });

      await colorService.pushColorToSupabase(
        'color-123',
        'org-123',
        'user-123'
      );

      expect(mockSupabaseClient.from().upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          color_spaces: expect.objectContaining({
            cmyk: { c: 0, m: 100, y: 100, k: 0 },
          }),
        })
      );
    });
  });

  describe('Sync Error Recovery', () => {
    test('should handle network timeouts gracefully', async () => {
      const testColor = {
        id: 'color-123',
        code: 'TEST-001',
        name: 'Test Red',
        hex: '#FF0000',
        organizationId: 'org-123',
      };

      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('Network timeout'));

      await expect(
        colorService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('Network timeout');
    });

    test('should handle RLS policy violations', async () => {
      mockSupabaseClient.from().select.mockReturnValue({
        count: null,
        error: {
          message: 'RLS policy violation',
          code: 'PGRST116',
          hint: 'No policy matched for this request',
        },
      });

      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle invalid session tokens', async () => {
      vi.mocked(
        require('../../services/supabase-client').ensureAuthenticatedSession
      ).mockReturnValue({
        session: null,
        error: 'Invalid JWT token',
      });

      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });
  });

  describe('Sync Performance', () => {
    test('should handle large batches of colors efficiently', async () => {
      const largeColorSet = Array.from({ length: 1000 }, (_, i) => ({
        id: `color-${i}`, // Direct UUID mapping
        code: `BATCH-${i.toString().padStart(3, '0')}`,
        display_name: `Batch Color ${i}`,
        hex: `#${i.toString(16).padStart(6, '0')}`,
        color_spaces: { cmyk: { c: 0, m: 0, y: 0, k: 0 } },
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z',
      }));

      mockDb.prepare().get.mockReturnValue({ id: 1 });
      mockSupabaseClient.from().select().eq.mockReturnValue({
        data: largeColorSet,
        error: null,
      });
      mockSupabaseClient.from().select.mockReturnValue({
        count: 1000,
        error: null,
      });

      const startTime = Date.now();
      const syncedColors = await colorService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );
      const endTime = Date.now();

      expect(syncedColors).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    test('should minimize database queries during sync', async () => {
      const supabaseColors = [
        { id: 'color-1', code: 'TEST-001', hex: '#FF0000' }, // Direct UUID mapping
        { id: 'color-2', code: 'TEST-002', hex: '#00FF00' }, // Direct UUID mapping
        { id: 'color-3', code: 'TEST-003', hex: '#0000FF' }, // Direct UUID mapping
      ];

      mockDb.prepare().get.mockReturnValue({ id: 1 });
      mockSupabaseClient.from().select().eq.mockReturnValue({
        data: supabaseColors,
        error: null,
      });
      mockSupabaseClient.from().select.mockReturnValue({
        count: 3,
        error: null,
      });

      await colorService.syncColorsFromSupabase('user-123', 'org-123');

      // Should prepare statements efficiently
      expect(mockDb.prepare).toHaveBeenCalledTimes(2); // Organization check + color existence check
    });
  });
});
