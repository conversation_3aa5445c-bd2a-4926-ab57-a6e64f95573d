/**
 * Consolidated Color Utilities
 *
 * This module provides a unified interface for all color-related operations
 * including validation, conversion, formatting, and analysis.
 */

// Re-export all types
export * from './types';

// Re-export all validation functions
export {
  // HEX validation
  isValidHex,
  isValidHex6,
  validateHex,

  // CMYK validation
  isValidCMYK,
  validateCMYK,

  // RGB validation
  isValidRGB,

  // Pantone/RAL validation
  isValidPantone,
  isValidRAL,

  // General validation
  isValidColorCode,
  areColorValuesConsistent,
} from './validation';

// Re-export all formatting functions
export {
  // HEX formatting
  formatHex,
  standardizeHex,
  fixCommonHexIssues,

  // CMYK formatting
  parseCMYK,
  formatCMYKForBackend,
  formatCMYKForDisplay,
  standardizeCMYK,
  fixCommonCMYKIssues,

  // RGB formatting
  formatRGB,
  formatRGBForDisplay,
  rgbToHexString,
  parseRGB,

  // HSL formatting
  formatHSL,
  parseHSL,

  // Color code formatting
  standardizeColorCode,
  formatCodeForDisplay,
  standardizeRALCode,
  formatRALForDisplay,

  // General formatting
  formatColorValue,
} from './formatting';

// Re-export all conversion functions
export {
  // Hex ↔ RGB
  hexToRgb,
  rgbToHex,

  // RGB ↔ HSL
  rgbToHsl,
  hslToRgb,

  // RGB ↔ CMYK
  rgbToCmyk,
  cmykToRgb,

  // Hex ↔ HSL
  hexToHsl,
  hslToHex,

  // Hex ↔ CMYK
  hexToCmyk,
  cmykToHex,

  // RGB ↔ LAB (via XYZ)
  rgbToXyz,
  xyzToRgb,
  xyzToLab,
  labToXyz,
  rgbToLab,
  labToRgb,

  // Utility conversions
  getAllColorSpaces,
  isLightColor,
  isLightColorHsl,
} from './conversion';

// Re-export all analysis functions
export {
  // Contrast calculations
  calculateRelativeLuminance,
  calculateContrastRatio,
  getWcagCompliance,
  checkContrast,
  checkContrastHex,

  // Delta E calculations
  calculateDeltaE,
  calculateDeltaERgb,
  interpretDeltaE,

  // Accessibility
  checkAccessibility,
  getAccessibleTextColor,

  // Color harmony
  getComplementaryColor,
  getAnalogousColors,
  getTriadicColors,
  getTetradicColors,
  getSplitComplementaryColors,
  getMonochromaticColors,

  // Color analysis
  isGrayscale,
  getColorTemperature,
  calculateColorSimilarity,
} from './analysis';

/**
 * Convenience exports for common use cases
 */

// Most commonly used validation
export { isValidHex as isHex } from './validation';
export { isValidCMYK as isCMYK } from './validation';

// Most commonly used conversions
export { hexToRgb as hex2rgb } from './conversion';
export { rgbToHex as rgb2hex } from './conversion';
export { hexToCmyk as hex2cmyk } from './conversion';
export { cmykToHex as cmyk2hex } from './conversion';

// Most commonly used formatting
export { formatHex as normalizeHex } from './formatting';
export { standardizeCMYK as normalizeCMYK } from './formatting';
