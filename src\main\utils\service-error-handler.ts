/**
 * @file service-error-handler.ts
 * @description Simple, standardized error handling for service operations
 */

/**
 * Standard error codes for common service errors
 */
export enum ServiceErrorCode {
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  INVALID_INPUT = 'INVALID_INPUT',
  OPERATION_FAILED = 'OPERATION_FAILED',
}

/**
 * Standard service error result
 */
export interface ServiceError {
  success: false;
  error: string;
  code: ServiceErrorCode;
  context?: any;
}

/**
 * Standard service success result
 */
export interface ServiceSuccess<T> {
  success: true;
  data: T;
}

/**
 * Union type for service operation results
 */
export type ServiceResult<T> = ServiceSuccess<T> | ServiceError;

/**
 * Simple error handler for service operations
 */
export class ServiceErrorHandler {
  constructor(private serviceName: string) {}

  /**
   * Wrap an operation in try-catch with standardized error handling
   */
  async wrapAsync<T>(
    operation: () => Promise<T>,
    operationName: string,
    context?: any
  ): Promise<ServiceResult<T>> {
    try {
      const data = await operation();
      return { success: true, data };
    } catch (error) {
      return this.handleError(error, operationName, context);
    }
  }

  /**
   * Wrap a synchronous operation in try-catch
   */
  wrap<T>(
    operation: () => T,
    operationName: string,
    context?: any
  ): ServiceResult<T> {
    try {
      const data = operation();
      return { success: true, data };
    } catch (error) {
      return this.handleError(error, operationName, context);
    }
  }

  /**
   * Handle and categorize errors
   */
  private handleError(
    error: unknown,
    operationName: string,
    context?: any
  ): ServiceError {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Determine error code based on error type/message
    let code = ServiceErrorCode.OPERATION_FAILED;

    if (
      errorMessage.includes('not found') ||
      errorMessage.includes('NOT_FOUND')
    ) {
      code = ServiceErrorCode.NOT_FOUND;
    } else if (
      errorMessage.includes('validation') ||
      errorMessage.includes('invalid')
    ) {
      code = ServiceErrorCode.VALIDATION_ERROR;
    } else if (
      errorMessage.includes('permission') ||
      errorMessage.includes('unauthorized')
    ) {
      code = ServiceErrorCode.PERMISSION_DENIED;
    } else if (
      errorMessage.includes('database') ||
      errorMessage.includes('SQL')
    ) {
      code = ServiceErrorCode.DATABASE_ERROR;
    }

    // Log the error
    console.error(`[${this.serviceName}] ${operationName} failed:`, {
      message: errorMessage,
      code,
      context,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return {
      success: false,
      error: errorMessage,
      code,
      context,
    };
  }

  /**
   * Create a standardized "not found" error
   */
  notFound(entity: string, id: string): ServiceError {
    return {
      success: false,
      error: `${entity} with ID ${id} not found`,
      code: ServiceErrorCode.NOT_FOUND,
      context: { entity, id },
    };
  }

  /**
   * Create a standardized validation error
   */
  validationError(message: string, field?: string): ServiceError {
    return {
      success: false,
      error: message,
      code: ServiceErrorCode.VALIDATION_ERROR,
      context: { field },
    };
  }

  /**
   * Create a standardized permission error
   */
  permissionDenied(operation: string): ServiceError {
    return {
      success: false,
      error: `Permission denied for operation: ${operation}`,
      code: ServiceErrorCode.PERMISSION_DENIED,
      context: { operation },
    };
  }
}

/**
 * Helper functions for common error handling patterns
 */
export class ErrorHelpers {
  /**
   * Safe return for list operations - returns empty array on error
   */
  static safeList<T>(result: ServiceResult<T[]>): T[] {
    return result.success ? result.data : [];
  }

  /**
   * Safe return for single item operations - returns undefined on error
   */
  static safeItem<T>(result: ServiceResult<T>): T | undefined {
    return result.success ? result.data : undefined;
  }

  /**
   * Safe return for boolean operations - returns false on error
   */
  static safeBool(result: ServiceResult<boolean>): boolean {
    return result.success ? result.data : false;
  }

  /**
   * Check if error is a specific type
   */
  static isErrorCode(
    result: ServiceResult<any>,
    code: ServiceErrorCode
  ): boolean {
    return !result.success && result.code === code;
  }

  /**
   * Extract error message from result
   */
  static getErrorMessage(result: ServiceResult<any>): string | undefined {
    return result.success ? undefined : result.error;
  }
}

/**
 * Factory function to create error handler for a service
 */
export function createServiceErrorHandler(
  serviceName: string
): ServiceErrorHandler {
  return new ServiceErrorHandler(serviceName);
}
