import { SharedFolderManager } from '../main/shared-folder';

/**
 * Test script for shared folder functionality
 * Run using: npx ts-node src/test/shared-folder-test.ts
 */
async function testSharedFolder() {
  // Initialize shared folder with Windows path
  const sharedFolderManager = new SharedFolderManager({
    basePath:
      'C:\\Users\\<USER>\\ACMEVAPE LTD\\Creative - Documents\\2. TEAM\\Michael\\Colour Store\\Product colour tracker',
    folderName: 'PantoneTracker',
  });

  console.log('Testing shared folder functionality...');

  try {
    // Test 1: Ensure folder exists
    await sharedFolderManager.ensureExists();
    const folderPath = sharedFolderManager.getPath();
    console.log(`✅ Folder initialized at: ${folderPath}`);

    // Test 2: Write a test file
    const testFileName = 'test-file.txt';
    const testContent = `Test content created at ${new Date().toISOString()}`;
    const writeResult = await sharedFolderManager.writeFile(
      testFileName,
      testContent
    );
    console.log(`✅ File write ${writeResult ? 'successful' : 'failed'}`);

    // Test 3: Read the test file
    const readContent = await sharedFolderManager.readFile(testFileName);
    console.log(`✅ File read successful: ${readContent === testContent}`);

    // Test 4: List files in the folder
    const files = await sharedFolderManager.listFiles();
    console.log(`✅ Listed ${files.length} files in the folder:`);
    files.forEach(file => {
      console.log(
        `  - ${file.name} (${file.isDirectory ? 'Directory' : 'File'})`
      );
    });

    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testSharedFolder().catch(console.error);
