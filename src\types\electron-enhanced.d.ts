/**
 * @file electron-enhanced.d.ts
 * @description Enhanced TypeScript definitions for Electron dependencies and build system optimization
 * Provides comprehensive type safety for external dependencies with enterprise-grade patterns
 */

// ===== ELECTRON-UTIL ENHANCEMENTS =====
declare module 'electron-util' {
  interface ElectronUtilConfig {
    readonly isRenderer: boolean;
    readonly isMain: boolean;
    readonly isDevelopment: boolean;
    readonly isProduction: boolean;
    readonly isUnitTest: boolean;
    readonly isLinux: boolean;
    readonly isMacOS: boolean;
    readonly isWindows: boolean;
    readonly isElectron: boolean;
  }

  export const is: ElectronUtilConfig;

  export interface PlatformInfo {
    platform: string;
    arch: string;
    version: string;
  }

  export function platform(): PlatformInfo;

  export interface ElectronVersion {
    electron: string;
    chrome: string;
    node: string;
  }

  export function getElectronVersion(): ElectronVersion;

  export interface AppConfig {
    isFirstRun: boolean;
    isPackaged: boolean;
    appData: string;
    userData: string;
  }

  export function getAppConfig(): AppConfig;

  // API methods with enhanced typing
  export function openNewGitHubIssue(options: {
    user: string;
    repo: string;
    title?: string;
    body?: string;
    labels?: string[];
  }): Promise<void>;

  export function openUrlMenuItem(options: {
    url: string;
    label: string;
  }): Electron.MenuItem;

  export function aboutMenuItem(options: {
    icon?: string;
    text?: string;
    title?: string;
    website?: string;
    version?: string;
  }): Electron.MenuItem;

  export function debugInfo(): string;

  export function getWindowBoundsCentered(options?: {
    width?: number;
    height?: number;
  }): Electron.Rectangle;

  export function centerWindow(window: Electron.BrowserWindow): void;

  export function enforceMacOSAppLocation(): void;

  export function menuBarHeight(): number;

  export function setContentSecurityPolicy(
    policy: string,
    options?: { devtools?: boolean }
  ): void;

  export function fixPathForAsarUnpack(path: string): string;

  export function isFirstAppLaunch(): boolean;

  export function darkMode(): {
    readonly isEnabled: boolean;
    onChange: (callback: (enabled: boolean) => void) => () => void;
  };

  export function runJS(
    code: string,
    window?: Electron.WebContents
  ): Promise<any>;

  export function chromeVersion(): string;

  export function electronVersion(): string;

  export function nodeVersion(): string;

  export function systemPreferences(): Electron.SystemPreferences;
}

// ===== SENTRY ELECTRON ENHANCEMENTS =====
declare module '@sentry/electron' {
  import * as Sentry from '@sentry/types';
  import { BrowserOptions } from '@sentry/browser';
  import { NodeOptions } from '@sentry/node';

  export interface ElectronOptions extends BrowserOptions, Partial<NodeOptions> {
    // Electron-specific options
    getSessions?: () => Electron.Session[];
    
    // Enhanced configuration
    captureConsoleErrors?: boolean;
    captureUnhandledRejections?: boolean;
    autoSessionTracking?: boolean;
    
    // Process-specific configuration
    getRendererName?: (contents: Electron.WebContents) => string;
    
    // Custom transports
    ipcMode?: 'classic' | 'contextIsolation';
    
    // Enhanced debugging
    debugIntegrations?: boolean;
    showDebugLogs?: boolean;
  }

  export function init(options: ElectronOptions): void;

  // Enhanced exports with proper typing
  export {
    addBreadcrumb,
    addGlobalEventProcessor,
    addIntegration,
    captureEvent,
    captureException,
    captureMessage,
    configureScope,
    createTransport,
    getCurrentHub,
    getHubFromCarrier,
    Hub,
    lastEventId,
    makeMain,
    Scope,
    setContext,
    setExtra,
    setExtras,
    setTag,
    setTags,
    setUser,
    startTransaction,
    withScope,
    Severity,
    SeverityLevel,
    Status,
    Event,
    EventHint,
    Exception,
    Request,
    SdkInfo,
    Session,
    SessionStatus,
    StackFrame,
    Stacktrace,
    Thread,
    User,
    Breadcrumb,
    BreadcrumbHint,
    Integration,
    IntegrationClass,
    EventProcessor,
    Transport,
    TransportOptions,
    Package,
    Runtime,
    SdkMetadata,
    SessionContext,
    SessionFlusher,
    Span,
    SpanContext,
    SpanStatus,
    Transaction,
    TransactionContext
  } from '@sentry/types';

  // Electron-specific integrations
  export class ElectronIntegration {
    constructor(options?: {
      getRendererName?: (contents: Electron.WebContents) => string;
      captureConsoleErrors?: boolean;
    });
  }

  export class ElectronMinidumpIntegration {
    constructor(options?: {
      minidumpDirectory?: string;
      enableInRenderer?: boolean;
    });
  }

  export class ElectronNetIntegration {
    constructor(options?: {
      enableTracing?: boolean;
      enableBreadcrumbs?: boolean;
    });
  }

  // Default integrations with enhanced typing
  export function getDefaultIntegrations(options?: {
    enableNativeCrashHandling?: boolean;
    enableJavaScriptCrashHandling?: boolean;
    enableUnresponsiveAppCrashHandling?: boolean;
  }): Integration[];

  // Enhanced error capturing
  export function captureElectronError(error: Error, options?: {
    processType?: 'main' | 'renderer' | 'worker';
    webContents?: Electron.WebContents;
    level?: SeverityLevel;
    fingerprint?: string[];
  }): string;

  // IPC transport helpers
  export function createIPCTransport(options?: {
    enableTracing?: boolean;
    enableMetrics?: boolean;
  }): Transport;

  // Session management
  export function flush(timeout?: number): Promise<boolean>;
  export function close(timeout?: number): Promise<boolean>;

  // Electron-specific context
  export interface ElectronContext {
    processType: 'main' | 'renderer' | 'worker';
    processId: number;
    webContentsId?: number;
    frameId?: number;
    isDevMode: boolean;
    electronVersion: string;
    chromeVersion: string;
    nodeVersion: string;
  }

  export function setElectronContext(context: Partial<ElectronContext>): void;
  export function getElectronContext(): ElectronContext | undefined;
}

// ===== ELECTRON LOG ENHANCEMENTS =====
declare module 'electron-log' {
  export interface LogLevel {
    error: 0;
    warn: 1;
    info: 2;
    verbose: 3;
    debug: 4;
    silly: 5;
  }

  export type LogLevelValue = LogLevel[keyof LogLevel];
  export type LogLevelString = keyof LogLevel;

  export interface LogMessage {
    level: LogLevelString;
    data: any[];
    date: Date;
    scope?: string;
  }

  export interface LogTransform {
    (message: LogMessage): LogMessage;
  }

  export interface TransformableTransport {
    level: LogLevelString | false;
    format?: string | ((message: LogMessage) => string);
    transforms?: LogTransform[];
  }

  export interface FileTransport extends TransformableTransport {
    file?: string;
    maxSize?: number;
    archiveLog?: (oldLogFile: string) => void;
    writeOptions?: {
      flag?: string;
      mode?: number;
      encoding?: BufferEncoding;
    };
    resolvePathFn?: (variables: {
      electronDefaultDir?: string;
      appName?: string;
      appVersion?: string;
      date?: string;
    }) => string;
  }

  export interface ConsoleTransport extends TransformableTransport {
    useStyles?: boolean;
  }

  export interface IPCTransport extends TransformableTransport {
    // IPC-specific options
  }

  export interface RemoteTransport extends TransformableTransport {
    url?: string;
    requestOptions?: {
      headers?: Record<string, string>;
      timeout?: number;
      method?: string;
    };
  }

  export interface MainTransport extends TransformableTransport {
    // Main process specific options
  }

  export interface RendererTransport extends TransformableTransport {
    // Renderer process specific options
  }

  export interface Logger {
    error(...params: any[]): void;
    warn(...params: any[]): void;
    info(...params: any[]): void;
    verbose(...params: any[]): void;
    debug(...params: any[]): void;
    silly(...params: any[]): void;
    log(...params: any[]): void;

    // Scoped logging
    scope(label: string): Logger;

    // Transport management
    transports: {
      file: FileTransport;
      console: ConsoleTransport;
      ipc: IPCTransport | null;
      remote: RemoteTransport;
      main: MainTransport;
      renderer: RendererTransport;
    };

    // Configuration
    catchErrors(options?: {
      showDialog?: boolean;
      onError?: (error: Error, versions?: any, submitIssue?: () => void) => void;
    }): void;

    // Hooks
    hooks: {
      on(event: 'before-init', handler: (logger: Logger) => void): void;
      on(event: 'after-init', handler: (logger: Logger) => void): void;
    };

    // Variables
    variables: {
      process?: 'main' | 'renderer';
    };

    // Functions
    functions: {
      transformObject?(data: any, message: LogMessage): any;
    };

    // Create child logger
    create(options?: {
      logId?: string;
    }): Logger;

    // Log file path
    transports: {
      file: FileTransport & {
        getFile(): { path: string; size: number };
        clear(): void;
      };
    } & Logger['transports'];
  }

  const logger: Logger;
  export default logger;

  // Additional exports
  export const electronLog: Logger;
  export function create(options?: { logId?: string }): Logger;
}

// ===== BETTER-SQLITE3 ENHANCEMENTS =====
declare module 'better-sqlite3' {
  export interface StatementOptions {
    readonly?: boolean;
    safeIntegers?: boolean | ((value: number) => any);
  }

  export interface PrepareOptions extends StatementOptions {
    // Additional prepare-specific options
  }

  export interface RunResult {
    changes: number;
    lastInsertRowid: number | bigint;
  }

  export interface DatabaseOptions {
    readonly?: boolean;
    fileMustExist?: boolean;
    timeout?: number;
    verbose?: ((message?: any, ...additionalArgs: any[]) => void) | null;
    nativeBinding?: string;
  }

  export interface BackupMetadata {
    totalPages: number;
    remainingPages: number;
  }

  export interface BackupOptions {
    progress?: (info: BackupMetadata) => number;
    attached?: string;
  }

  export interface DatabaseInfo {
    memory: boolean;
    readonly: boolean;
    name: string;
    open: boolean;
    inTransaction: boolean;
  }

  export class Statement<
    BindParameters extends unknown[] | {} = unknown[],
    Result = unknown
  > {
    readonly database: Database;
    readonly source: string;
    readonly reader: boolean;
    readonly busy: boolean;

    run(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): RunResult;
    get(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): Result | undefined;
    all(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): Result[];
    iterate(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): IterableIterator<Result>;
    
    pluck(toggle?: boolean): this;
    expand(toggle?: boolean): this;
    raw(toggle?: boolean): this;
    bind(...params: BindParameters extends unknown[] ? BindParameters : [BindParameters]): this;
    columns(): ColumnDefinition[];
    safeIntegers(toggle?: boolean): this;
  }

  export interface ColumnDefinition {
    name: string;
    column: string | null;
    database: string | null;
    table: string | null;
    type: string | null;
  }

  export interface Transaction<T extends unknown[] = unknown[]> {
    (...args: T): RunResult;
    default(...args: T): RunResult;
    deferred(...args: T): RunResult;
    immediate(...args: T): RunResult;
    exclusive(...args: T): RunResult;
  }

  export interface AggregateOptions {
    start?: unknown;
    step: (total: unknown, next: unknown) => unknown;
    inverse?: (total: unknown, dropped: unknown) => unknown;
    result?: (total: unknown) => unknown;
    safeIntegers?: boolean | ((value: number) => any);
    deterministic?: boolean;
    directOnly?: boolean;
    varargs?: boolean;
  }

  export interface RegistrationOptions {
    safeIntegers?: boolean | ((value: number) => any);
    deterministic?: boolean;
    directOnly?: boolean;
    varargs?: boolean;
  }

  export class Database {
    readonly name: string;
    readonly open: boolean;
    readonly inTransaction: boolean;
    readonly memory: boolean;
    readonly readonly: boolean;

    constructor(filename?: string | Buffer, options?: DatabaseOptions);

    prepare<
      BindParameters extends unknown[] | {} = unknown[],
      Result = unknown
    >(source: string): Statement<BindParameters, Result>;

    exec(source: string): this;
    
    close(): this;
    
    defaultSafeIntegers(toggle?: boolean): this;
    
    loadExtension(path: string, entryPoint?: string): this;
    
    function(name: string, fn: (...args: unknown[]) => unknown): this;
    function(name: string, options: RegistrationOptions, fn: (...args: unknown[]) => unknown): this;
    
    aggregate(name: string, options: AggregateOptions): this;
    
    table(name: string, definition: Record<string, any>): this;
    
    backup(destination: string | Database, options?: BackupOptions): Promise<BackupMetadata>;
    
    serialize(options?: { attached?: string }): Buffer;
    
    transaction<T extends unknown[]>(fn: (...args: T) => void): Transaction<T>;
    
    pragma(source: string, options?: { simple?: boolean }): unknown;
    
    checkpoint(databaseName?: string): this;
    
    unsafeMode(unsafe?: boolean): this;

    // Enhanced methods for ChromaSync
    info(): DatabaseInfo;
    
    // Utility methods
    getDatabasePath(): string;
    getSize(): number;
    vacuum(): this;
    analyze(): this;
    
    // Connection management
    isOpen(): boolean;
    isClosed(): boolean;
  }

  export default Database;

  // Additional exports
  export const SqliteError: typeof Error;
  export interface SqliteError extends Error {
    name: 'SqliteError';
    message: string;
    code: string;
  }
}

// ===== GLOBAL ENVIRONMENT ENHANCEMENTS =====
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // Electron environment
      readonly ELECTRON_IS_DEV?: string;
      readonly ELECTRON_DISABLE_SECURITY_WARNINGS?: string;
      readonly ELECTRON_ENABLE_STACK_DUMPING?: string;
      readonly ELECTRON_ENABLE_LOGGING?: string;
      
      // Sentry configuration
      readonly SENTRY_DSN?: string;
      readonly SENTRY_ENVIRONMENT?: string;
      readonly SENTRY_RELEASE?: string;
      readonly SENTRY_ENABLED?: string;
      readonly SENTRY_SAMPLE_RATE?: string;
      readonly SENTRY_DEBUG?: string;
      
      // Database configuration
      readonly DATABASE_PATH?: string;
      readonly DATABASE_TIMEOUT?: string;
      readonly DATABASE_READONLY?: string;
      
      // Sync configuration
      readonly SUPABASE_URL?: string;
      readonly SUPABASE_ANON_KEY?: string;
      readonly SUPABASE_SERVICE_KEY?: string;
      
      // Build configuration
      readonly NODE_ENV: 'development' | 'production' | 'test';
      readonly BUILD_NUMBER?: string;
      readonly BUILD_TIMESTAMP?: string;
      readonly GIT_COMMIT_SHA?: string;
      readonly GIT_BRANCH?: string;
      
      // Application configuration
      readonly APP_NAME?: string;
      readonly APP_VERSION?: string;
      readonly APP_DESCRIPTION?: string;
      readonly APP_AUTHOR?: string;
      
      // Feature flags
      readonly ENABLE_AUTO_UPDATE?: string;
      readonly ENABLE_CRASH_REPORTING?: string;
      readonly ENABLE_ANALYTICS?: string;
      readonly ENABLE_DEBUG_MODE?: string;
      
      // Logging configuration
      readonly LOG_LEVEL?: 'error' | 'warn' | 'info' | 'verbose' | 'debug' | 'silly';
      readonly LOG_FILE_PATH?: string;
      readonly LOG_MAX_SIZE?: string;
      
      // Security configuration
      readonly CSP_POLICY?: string;
      readonly ENABLE_REMOTE_MODULE?: string;
      readonly ENABLE_NODE_INTEGRATION?: string;
      readonly ENABLE_CONTEXT_ISOLATION?: string;
    }
  }
  
  // Enhanced require interface for bundled environments
  interface NodeRequire {
    main?: {
      filename: string;
      paths: string[];
    };
  }

  // Global error handling enhancement
  interface ErrorEvent {
    readonly colno: number;
    readonly error: Error;
    readonly filename: string;
    readonly lineno: number;
    readonly message: string;
  }

  interface PromiseRejectionEvent {
    readonly promise: Promise<any>;
    readonly reason: any;
  }

  // Enhanced console interface
  interface Console {
    // Existing methods are already defined
    readonly memory?: {
      readonly jsHeapSizeLimit: number;
      readonly totalJSHeapSize: number;
      readonly usedJSHeapSize: number;
    };
  }
}

// ===== BUILD SYSTEM TYPE ENHANCEMENTS =====

// Vite configuration enhancements
declare module 'vite' {
  interface UserConfig {
    // Enhanced type safety for ChromaSync-specific configuration
    chromaSync?: {
      enableSentry?: boolean;
      enableAnalytics?: boolean;
      enableDebugMode?: boolean;
      customBuildOptions?: {
        obfuscation?: boolean;
        minification?: boolean;
        sourceMap?: boolean;
      };
    };
  }
}

// Electron-vite configuration enhancements
declare module 'electron-vite' {
  export interface ElectronViteConfig {
    main?: import('vite').UserConfig;
    preload?: import('vite').UserConfig;
    renderer?: import('vite').UserConfig;
  }

  export function defineConfig(config: ElectronViteConfig): ElectronViteConfig;
  export function externalizeDepsPlugin(options?: {
    include?: string[];
    exclude?: string[];
  }): import('vite').Plugin;
}

// TypeScript compiler API enhancements
declare module 'typescript' {
  interface CompilerOptions {
    // Enhanced options for Electron development
    electronTarget?: 'main' | 'renderer' | 'preload';
    enableElectronTypes?: boolean;
    enableNodeTypes?: boolean;
    enableBrowserTypes?: boolean;
  }
}

// ===== UTILITY TYPE HELPERS =====

/**
 * Enhanced type-safe event emitter for IPC communication
 */
export interface TypedEventEmitter<T extends Record<string, any[]>> {
  on<K extends keyof T>(event: K, listener: (...args: T[K]) => void): this;
  off<K extends keyof T>(event: K, listener: (...args: T[K]) => void): this;
  emit<K extends keyof T>(event: K, ...args: T[K]): boolean;
  once<K extends keyof T>(event: K, listener: (...args: T[K]) => void): this;
  removeAllListeners<K extends keyof T>(event?: K): this;
  listeners<K extends keyof T>(event: K): Array<(...args: T[K]) => void>;
  listenerCount<K extends keyof T>(event: K): number;
}

/**
 * Type-safe IPC communication helpers
 */
export type IPCChannelMap = {
  // Example channels - extend as needed
  'app:ready': [];
  'app:quit': [];
  'sync:started': [{ timestamp: number; operation: string }];
  'sync:completed': [{ timestamp: number; operation: string; result: any }];
  'sync:failed': [{ timestamp: number; operation: string; error: string }];
  'color:created': [{ id: string; name: string; organizationId: string }];
  'color:updated': [{ id: string; changes: Record<string, any> }];
  'color:deleted': [{ id: string; organizationId: string }];
};

/**
 * Enhanced result type for better error handling
 */
export type Result<T, E = Error> = 
  | { success: true; data: T; error?: undefined }
  | { success: false; data?: undefined; error: E };

/**
 * Type-safe configuration loader
 */
export interface ConfigLoader<T> {
  load(): T;
  validate(config: unknown): config is T;
  getDefault(): T;
  merge(base: T, override: Partial<T>): T;
}

/**
 * Enhanced logger interface with structured logging
 */
export interface StructuredLogger {
  error(message: string, error?: Error, context?: Record<string, any>): void;
  warn(message: string, context?: Record<string, any>): void;
  info(message: string, context?: Record<string, any>): void;
  debug(message: string, context?: Record<string, any>): void;
  
  // Scoped logging
  scope(name: string): StructuredLogger;
  
  // Performance logging
  time(label: string): void;
  timeEnd(label: string): void;
  
  // Structured data logging
  child(context: Record<string, any>): StructuredLogger;
}

/**
 * Database transaction helper types
 */
export type TransactionCallback<T> = () => T;
export type TransactionResult<T> = Promise<Result<T, Error>>;

/**
 * Enhanced service container types
 */
export interface ServiceContainer {
  register<T>(name: string, factory: () => T): void;
  get<T>(name: string): T;
  has(name: string): boolean;
  list(): string[];
}

/**
 * Type-safe event handling for Electron processes
 */
export interface ElectronEventMap {
  // Main process events
  'app:ready': [];
  'app:window-all-closed': [];
  'app:before-quit': [Electron.Event];
  'app:will-quit': [Electron.Event];
  'app:activate': [Electron.Event, boolean];
  
  // Window events
  'window:closed': [Electron.Event];
  'window:minimized': [Electron.Event];
  'window:restored': [Electron.Event];
  'window:moved': [Electron.Event];
  'window:resized': [Electron.Event];
  
  // Custom application events
  'auth:login': [{ userId: string; organizationId?: string }];
  'auth:logout': [];
  'sync:start': [{ operation: string }];
  'sync:complete': [{ operation: string; result: any }];
  'sync:error': [{ operation: string; error: string }];
}

// Export for module resolution
export {};