/**
 * @file mainWindow.ts
 * @description Main window creation and management for ChromaSync
 */

import { BrowserWindow, dialog, ipcMain, Menu, nativeTheme } from 'electron';
import path from 'path';
// import { pathToFileURL } from 'url';
import { cspConfiguration } from '../security/csp-configuration';
import { registerHandlerSafely } from '../utils/ipcRegistry';
import { createOptimizedWindow } from '../utils/startup-optimizer';

// Zoom configuration constants
const DEFAULT_ZOOM = 1.0;
const MIN_ZOOM = 0.5;
const MAX_ZOOM = 3.0;
const ZOOM_INCREMENT = 0.1;

// Use electron-vite's recommended method for checking dev environment
const devServerUrl = process.env.ELECTRON_VITE_DEV_SERVER_URL;
const isDevelopment = !!devServerUrl || process.env.NODE_ENV === 'development';

// Fallback to localhost:5173 if dev server URL not set but we're in development
// const fallbackDevUrl = 'http://localhost:5173'; // Currently unused
// const finalDevUrl = devServerUrl || (isDevelopment ? fallbackDevUrl : null);

// Global window reference
let mainWindow: BrowserWindow | null = null;

/**
 * Get the main window instance
 */
export function getMainWindow(): BrowserWindow | null {
  return mainWindow;
}

/**
 * Set up window control handlers (minimize, maximize, close, devtools)
 */
async function setupWindowControlHandlers(): Promise<void> {
  console.log('Registering window control handlers...');

  // Remove any existing listeners first
  ipcMain.removeAllListeners('window:minimize');
  ipcMain.removeAllListeners('window:maximize');
  ipcMain.removeAllListeners('window:unmaximize');
  ipcMain.removeAllListeners('window:close');

  // Window handlers will be registered safely below

  ipcMain.on('window:minimize', () => {
    if (mainWindow) {
      mainWindow.minimize();
    }
  });

  ipcMain.on('window:maximize', () => {
    if (mainWindow && !mainWindow.isMaximized()) {
      mainWindow.maximize();
    }
  });

  ipcMain.on('window:unmaximize', () => {
    if (mainWindow && mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    }
  });

  ipcMain.on('window:close', () => {
    if (mainWindow) {
      mainWindow.close();
    }
  });

  // Import registerHandlerSafely for window handlers
  const { registerHandlerSafely: registerWindowHandler } = await import(
    '../utils/ipcRegistry'
  );

  registerWindowHandler(ipcMain, 'window:isMaximized', () => {
    return mainWindow ? mainWindow.isMaximized() : false;
  });

  // Add DevTools toggle handler
  registerWindowHandler(ipcMain, 'window:toggleDevTools', () => {
    console.log('[Main] DevTools toggle handler called');

    // Get the current active window
    let targetWindow = mainWindow;

    // If mainWindow is null or destroyed, find the focused window
    if (!targetWindow || targetWindow.isDestroyed()) {
      targetWindow = BrowserWindow.getFocusedWindow();
      console.log('[Main] Using focused window:', !!targetWindow);
    }

    // If no focused window, get the first available window
    if (!targetWindow) {
      const windows = BrowserWindow.getAllWindows();
      targetWindow = windows.find(win => !win.isDestroyed()) || null;
      console.log('[Main] Using first available window:', !!targetWindow);
      console.log('[Main] Total available windows:', windows.length);
    }

    if (targetWindow && !targetWindow.isDestroyed()) {
      try {
        const isOpened = targetWindow.webContents.isDevToolsOpened();
        console.log('[Main] DevTools currently opened:', isOpened);
        console.log('[Main] Target window focused:', targetWindow.isFocused());

        if (isOpened) {
          console.log('[Main] Closing DevTools');
          targetWindow.webContents.closeDevTools();
          return false;
        } else {
          console.log('[Main] Opening DevTools with mode: right');
          // Try different modes to ensure DevTools opens
          try {
            targetWindow.webContents.openDevTools({ mode: 'right' });
          } catch (_rightError) {
            console.log('[Main] Right mode failed, trying detach mode');
            targetWindow.webContents.openDevTools({ mode: 'detach' });
          }

          // Focus the window to ensure visibility
          if (!targetWindow.isFocused()) {
            targetWindow.focus();
          }
          return true;
        }
      } catch (error) {
        console.error('[Main] Error toggling dev tools:', error);
        return false;
      }
    } else {
      console.error('[Main] No valid window found for DevTools toggle');
      console.log(
        '[Main] Available windows:',
        BrowserWindow.getAllWindows().length
      );
      return false;
    }
  });

  console.log('Window control handlers registered successfully');
}

/**
 * Set up zoom handlers
 */
async function setupZoomHandlers(): Promise<void> {
  console.log('Registering zoom handlers...');

  // Registry utilities imported on-demand

  // Register zoom handlers using the new safe pattern (no manual removeHandler needed)
  const { registerHandlerSafely } = await import('../utils/ipcRegistry');

  // Use the new safe registration pattern
  registerHandlerSafely(ipcMain, 'zoom:in', () => {
    zoomIn();
    return mainWindow ? mainWindow.webContents.getZoomFactor() : DEFAULT_ZOOM;
  });

  registerHandlerSafely(ipcMain, 'zoom:out', () => {
    zoomOut();
    return mainWindow ? mainWindow.webContents.getZoomFactor() : DEFAULT_ZOOM;
  });

  registerHandlerSafely(ipcMain, 'zoom:reset', () => {
    resetZoom();
    return DEFAULT_ZOOM;
  });

  registerHandlerSafely(ipcMain, 'zoom:get-factor', () => {
    return mainWindow ? mainWindow.webContents.getZoomFactor() : DEFAULT_ZOOM;
  });

  registerHandlerSafely(ipcMain, 'app:get-zoom-factor', () => {
    return mainWindow ? mainWindow.webContents.getZoomFactor() : DEFAULT_ZOOM;
  });

  // Add handlers for the actual channels used by preload script
  ipcMain.on('app:zoom-in', () => {
    zoomIn();
  });

  ipcMain.on('app:zoom-out', () => {
    zoomOut();
  });

  ipcMain.on('app:zoom-reset', () => {
    resetZoom();
  });

  console.log('Zoom handlers registered successfully');
}

/**
 * Zoom in the main window
 */
function zoomIn(): void {
  if (!mainWindow) {
    return;
  }

  const currentZoom = mainWindow.webContents.getZoomFactor();
  if (currentZoom < MAX_ZOOM) {
    const newZoom = Math.min(currentZoom + ZOOM_INCREMENT, MAX_ZOOM);
    mainWindow.webContents.setZoomFactor(newZoom);
  }
}

/**
 * Zoom out the main window
 */
function zoomOut(): void {
  if (!mainWindow) {
    return;
  }

  const currentZoom = mainWindow.webContents.getZoomFactor();
  if (currentZoom > MIN_ZOOM) {
    const newZoom = Math.max(currentZoom - ZOOM_INCREMENT, MIN_ZOOM);
    mainWindow.webContents.setZoomFactor(newZoom);
  }
}

/**
 * Reset zoom to default level
 */
function resetZoom(): void {
  if (!mainWindow) {
    return;
  }
  mainWindow.webContents.setZoomFactor(DEFAULT_ZOOM);
}

/**
 * Create the main application window
 */
export async function createWindow(): Promise<BrowserWindow | null> {
  console.log('[TRACE] Entered createWindow');

  // Prevent multiple window creation
  if (mainWindow && !mainWindow.isDestroyed()) {
    console.log('[TRACE] Window already exists, returning existing window');
    return mainWindow;
  }

  // Set up window control handlers
  await setupWindowControlHandlers();
  console.log('[TRACE] Window control handlers set up');

  // Set up zoom handlers
  await setupZoomHandlers();
  console.log('[TRACE] Zoom handlers set up');

  // Register product datasheet handler
  registerHandlerSafely(
    ipcMain,
    'product:open-datasheet',
    async (_, product) => {
      console.log(`Opening product datasheet for: ${product}`);
      return { success: true, message: `Would open datasheet for ${product}` };
    }
  );

  // Register initial setup handlers
  registerHandlerSafely(ipcMain, 'get-initial-config-status', async () => {
    return { configured: false };
  });

  // Handler for selecting shared folder
  registerHandlerSafely(ipcMain, 'select-shared-folder', async () => {
    if (!mainWindow) {
      return null;
    }

    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory'],
      title: 'Select Shared Folder',
      buttonLabel: 'Select Folder',
    });

    if (result.canceled) {
      return null;
    }

    const selectedPath = result.filePaths[0];
    console.log(`Selected shared folder: ${selectedPath}`);
    return selectedPath;
  });

  // Handler for saving storage config
  registerHandlerSafely(ipcMain, 'save-storage-config', async (_, config) => {
    console.log(`Saving storage config: ${JSON.stringify(config)}`);
    return { success: true };
  });

  // Handler for selecting shared folder path
  registerHandlerSafely(ipcMain, 'setup:getSharedFolderPath', async () => {
    if (!mainWindow) {
      return null;
    }

    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory'],
      title: 'Select Shared Folder',
      buttonLabel: 'Select Folder',
    });

    if (result.canceled) {
      return null;
    }

    const selectedPath = result.filePaths[0];
    console.log(`Selected shared folder: ${selectedPath}`);
    return selectedPath;
  });

  // Handler for letting the renderer signal setup is complete
  ipcMain.removeAllListeners('setup-complete');
  ipcMain.on('setup-complete', () => {
    console.log('[IPC] Received setup-complete signal from renderer.');
  });

  // Register renderer error tracking handler
  registerHandlerSafely(
    ipcMain,
    'track-renderer-error',
    async (_, errorInfo) => {
      console.error('Renderer error:', errorInfo);
      return { success: true };
    }
  );

  // Register console debug logger (clears on startup)
  const debugLogPath = path.join(__dirname, '../../debug-console.log');

  // Clear debug log on startup
  try {
    require('fs').writeFileSync(
      debugLogPath,
      `[${new Date().toISOString()}] Debug console log started\n`
    );
    console.log(
      '[DEBUG] Console debug log cleared and ready at:',
      debugLogPath
    );
  } catch (error) {
    console.error('[DEBUG] Failed to initialize debug log:', error);
  }

  registerHandlerSafely(
    ipcMain,
    'debug-console-log',
    async (_, logData) => {
      try {
        const timestamp = new Date().toISOString();
        const logLine = `[${timestamp}] ${logData.level}: ${logData.message}\n`;
        require('fs').appendFileSync(debugLogPath, logLine);
      } catch (error) {
        console.error('[DEBUG] Failed to write debug log:', error);
      }
      return { success: true };
    },
    {
      rateLimitConfig: {
        maxRequests: 1000, // High frequency limit for debug logging
        windowMs: 60000, // 1 minute window
        message: 'Debug console logging rate limit exceeded',
      },
    }
  );

  // Register test handler for debugging
  registerHandlerSafely(ipcMain, 'test-ipc-available', () => {
    return { success: true, message: 'IPC is working correctly' };
  });

  // Register error logging handler
  registerHandlerSafely(ipcMain, 'log-error', async (_, errorLog) => {
    console.error('Renderer error:', errorLog);
    // TODO: Add file logging or external service logging here
    return { success: true };
  });

  // Register ping handler for health checks
  // Use safe registration pattern (imported earlier)
  registerHandlerSafely(ipcMain, 'ping', async () => {
    return { success: true, timestamp: Date.now() };
  });

  try {
    console.log('[TRACE] Creating main window...');

    // Get optimized window configuration
    const optimizedConfig = createOptimizedWindow();
    console.log(
      `[TRACE] Using optimized window size: ${optimizedConfig.width}x${optimizedConfig.height}`
    );

    // Create the browser window - frameless with no menu
    mainWindow = new BrowserWindow({
      width: optimizedConfig.width,
      height: optimizedConfig.height,
      minWidth: optimizedConfig.minWidth || 1200, // Minimum width for 5-6 columns
      minHeight: optimizedConfig.minHeight || 800, // Minimum height for 2 rows of swatches
      frame: false, // Remove default window frame (cross-platform)
      titleBarStyle: process.platform === 'darwin' ? 'hidden' : undefined, // Changed from 'hiddenInset' to 'hidden'
      webPreferences: {
        ...optimizedConfig.webPreferences,
        preload: path.join(__dirname, '../preload/index.js'),
        // Performance optimizations
        backgroundThrottling: false, // Keep renderer active for better performance
        offscreen: false, // Use main GPU process
      },
      transparent: false, // Disable transparency for debugging
      backgroundColor: '#1A1A1C', // Dark background
      hasShadow: true, // Enable window shadow
      // Platform-specific rounded corners
      ...(process.platform === 'win32' ? { roundedCorners: true } : {}),
      // Additional window settings for better corners
      autoHideMenuBar: true, // Hide menu bar
      resizable: true,
      show: false, // Hide window initially to set up properly
      // Performance optimizations
      useContentSize: true, // Use content size for better performance
      skipTaskbar: false, // Ensure proper window management
      thickFrame: false, // Reduce window decoration overhead
    });

    // Windows-specific fix for rounded corners
    if (process.platform === 'win32') {
      try {
        // Use CSS-based rounded corners instead of native API
        console.log('[TRACE] Using CSS-based rounded corners for Windows');

        // Make sure opacity and transparency are set correctly
        mainWindow.setBackgroundColor('#00000000');
        mainWindow.setOpacity(1.0);

        // Note: We're not using the native Windows API methods as they're not consistently
        // available across all Electron versions and can cause compatibility issues
      } catch (error) {
        console.log('[TRACE] Windows corner customization setup error:', error);
      }
    }

    // Linux-specific transparency handling
    if (process.platform === 'linux') {
      // Some Linux window managers have issues with transparency
      // We'll use a slight background color as fallback
      try {
        // Try transparent first
        mainWindow.setBackgroundColor('#00000000');
      } catch (error) {
        console.log(
          '[TRACE] Linux transparency not supported, using solid background'
        );
        // Fallback to a very dark background that matches the app
        mainWindow.setBackgroundColor('#1A1A1C');
      }
    }

    // Auto-updater will be set up after window is ready to avoid conflicts

    // Show window after all setup is complete
    mainWindow.once('ready-to-show', () => {
      if (mainWindow) {
        mainWindow.show();
        console.log('[TRACE] Main window shown.');
      }
    });

    // Configure vibrancy on macOS if available (electron 8.0+)
    if (process.platform === 'darwin') {
      try {
        // Remove vibrancy to prevent visual artifacts
        // mainWindow.setVibrancy('window');
        console.log('[TRACE] Vibrancy disabled to prevent border artifacts.');
      } catch (_error) {
        console.log(
          '[TRACE] Vibrancy not supported on this platform, continuing without it...'
        );
      }
    }

    // Remove the application menu
    Menu.setApplicationMenu(null);
    console.log('[TRACE] Application menu removed.');

    // Configure CSP using both HTTP headers and meta tag for maximum compatibility
    console.log('[TRACE] Configuring enhanced CSP security...');

    // Set up CSP headers (recommended approach)
    cspConfiguration.setupCSPHeaders();

    // Log CSP status for monitoring
    cspConfiguration.logCSPStatus();

    console.log('[TRACE] ✅ Enhanced CSP configuration complete.');

    // Load the app from built files (works for both development and production)
    console.log(`[TRACE] __dirname: ${__dirname}`);
    const indexPath = path.join(__dirname, '../renderer/index.html');
    console.log(`[TRACE] Loading app from: ${indexPath}`);

    // Verify file exists before loading
    const fs = await import('fs');
    const fileExists = fs.existsSync(indexPath);
    console.log(`[TRACE] File exists: ${fileExists}`);

    if (mainWindow && !mainWindow.isDestroyed() && fileExists) {
      console.log(
        `[TRACE] Using loadFile() for better file:// protocol compatibility`
      );
      await mainWindow.loadFile(indexPath);
      console.log('[TRACE] Loaded app from built files');

      // Open DevTools in development mode
      if (isDevelopment) {
        mainWindow.webContents.openDevTools();
        console.log('[TRACE] DevTools opened.');
      }
    } else if (!fileExists) {
      console.error('[TRACE] Renderer file does not exist at:', indexPath);
      throw new Error(`Renderer file not found: ${indexPath}`);
    }

    // Ensure window is shown even if ready-to-show doesn't fire
    setTimeout(() => {
      if (mainWindow && !mainWindow.isVisible()) {
        console.log('[TRACE] Forcing window to show');
        mainWindow.show();
      }
    }, 1000);

    // Handle window close
    mainWindow.on('closed', () => {
      mainWindow = null;
      console.log('[TRACE] Main window closed.');
    });

    // Register dark mode handler with duplicate check
    registerHandlerSafely(ipcMain, 'window:set-dark-mode', (_, isDarkMode) => {
      if (!mainWindow) {
        return false;
      }

      nativeTheme.themeSource = isDarkMode ? 'dark' : 'light';

      // Optional: Notify renderer about the change if needed
      // mainWindow.webContents.send('theme-updated', isDarkMode);

      return nativeTheme.shouldUseDarkColors; // Return the actual state
    });
    console.log('[TRACE] Dark mode handler registered.');

    // Auto-updater is now set up after all services are initialized

    console.log('[TRACE] Main window created successfully');
    return mainWindow;
  } catch (error) {
    console.error('[TRACE] Error creating main window:', error);
    return null;
  }
}
