/**
 * @file VirtualizedTable/index.tsx
 * @description High-performance virtualized table component for large datasets
 */

import React, { useMemo, useCallback, useRef, useEffect, useState } from 'react';
import { FixedSizeList as List, ListChildComponentProps } from 'react-window';
import { useTokens } from '../../../hooks/useTokens';
import { TableSkeleton } from '../Skeleton';

export interface VirtualizedTableColumn<T = any> {
  key: string;
  header: string;
  width: number | string;
  minWidth?: number;
  maxWidth?: number;
  render: (item: T, index: number) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  align?: 'left' | 'center' | 'right';
  headerAlign?: 'left' | 'center' | 'right';
  className?: string;
  headerClassName?: string;
}

export interface VirtualizedTableProps<T = any> {
  data: T[];
  columns: VirtualizedTableColumn<T>[];
  height: number | string;
  rowHeight?: number;
  headerHeight?: number;
  loading?: boolean;
  emptyMessage?: string;
  onRowClick?: (item: T, index: number) => void;
  onRowDoubleClick?: (item: T, index: number) => void;
  selectedRowIndex?: number;
  keyExtractor?: (item: T, index: number) => string | number;
  className?: string;
  containerClassName?: string;
  overscanCount?: number;
  stickyHeader?: boolean;
  showBorders?: boolean;
  striped?: boolean;
  hoverable?: boolean;
  onSortChange?: (columnKey: string, direction: 'asc' | 'desc' | null) => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc' | null;
  onScrollToTop?: () => void;
  onScrollToBottom?: () => void;
}

interface TableRowProps<T> extends ListChildComponentProps {
  data: {
    items: T[];
    columns: VirtualizedTableColumn<T>[];
    onRowClick?: (item: T, index: number) => void;
    onRowDoubleClick?: (item: T, index: number) => void;
    selectedRowIndex?: number;
    keyExtractor?: (item: T, index: number) => string | number;
    showBorders: boolean;
    striped: boolean;
    hoverable: boolean;
    tokens: any;
  };
}

const TableRow = <T,>({ index, style, data }: TableRowProps<T>) => {
  const {
    items,
    columns,
    onRowClick,
    onRowDoubleClick,
    selectedRowIndex,
    keyExtractor,
    showBorders,
    striped,
    hoverable
  } = data;

  const item = items[index];
  const isSelected = selectedRowIndex === index;
  const isEven = index % 2 === 0;

  const handleClick = useCallback(() => {
    onRowClick?.(item, index);
  }, [onRowClick, item, index]);

  const handleDoubleClick = useCallback(() => {
    onRowDoubleClick?.(item, index);
  }, [onRowDoubleClick, item, index]);

  const rowKey = keyExtractor ? keyExtractor(item, index) : index;

  return (
    <div
      key={rowKey}
      style={style}
      className={`
        flex items-center w-full
        ${showBorders ? 'border-b border-ui-border-light dark:border-ui-border-dark' : ''}
        ${striped && isEven ? 'bg-ui-background-secondary dark:bg-ui-background-secondary' : ''}
        ${hoverable ? 'hover:bg-ui-background-secondary dark:hover:bg-ui-background-secondary cursor-pointer' : ''}
        ${isSelected ? 'bg-brand-primary/10 dark:bg-brand-primary/10' : ''}
        transition-colors duration-150
      `}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      role="row"
      tabIndex={hoverable ? 0 : -1}
      aria-selected={isSelected}
    >
      {columns.map((column, _columnIndex) => {
        const width = typeof column.width === 'number' ? `${column.width}px` : column.width;
        const textAlign = column.align || 'left';

        return (
          <div
            key={column.key}
            className={`
              px-4 py-3 overflow-hidden
              ${column.className || ''}
              text-${textAlign}
            `}
            style={{
              width,
              minWidth: column.minWidth ? `${column.minWidth}px` : undefined,
              maxWidth: column.maxWidth ? `${column.maxWidth}px` : undefined,
              textAlign
            }}
            role="cell"
          >
            {column.render(item, index)}
          </div>
        );
      })}
    </div>
  );
};

const TableHeader = <T,>({
  columns,
  showBorders,
  onSortChange,
  sortColumn,
  sortDirection,
  height
}: {
  columns: VirtualizedTableColumn<T>[];
  showBorders: boolean;
  onSortChange?: (columnKey: string, direction: 'asc' | 'desc' | null) => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc' | null;
  height: number;
  tokens: any;
}) => {
  const handleSort = useCallback((columnKey: string, sortable: boolean) => {
    if (!sortable || !onSortChange) {return;}

    let newDirection: 'asc' | 'desc' | null = 'asc';
    
    if (sortColumn === columnKey) {
      if (sortDirection === 'asc') {
        newDirection = 'desc';
      } else if (sortDirection === 'desc') {
        newDirection = null;
      }
    }

    onSortChange(columnKey, newDirection);
  }, [onSortChange, sortColumn, sortDirection]);

  return (
    <div
      className={`
        flex items-center w-full
        bg-brand-primary dark:bg-brand-primary
        text-ui-foreground-inverse font-medium
        ${showBorders ? 'border-b border-ui-border-light dark:border-ui-border-dark' : ''}
        sticky top-0 z-10
      `}
      style={{ height: `${height}px` }}
      role="row"
    >
      {columns.map((column) => {
        const width = typeof column.width === 'number' ? `${column.width}px` : column.width;
        const textAlign = column.headerAlign || column.align || 'left';
        const isSorted = sortColumn === column.key;
        const sortIcon = isSorted 
          ? sortDirection === 'asc' 
            ? '↑' 
            : sortDirection === 'desc' 
              ? '↓' 
              : ''
          : '';

        return (
          <div
            key={column.key}
            className={`
              px-4 py-3 overflow-hidden
              ${column.sortable ? 'cursor-pointer hover:bg-brand-primary-dark' : ''}
              ${column.headerClassName || ''}
              text-${textAlign}
              flex items-center gap-2
              transition-colors duration-150
            `}
            style={{
              width,
              minWidth: column.minWidth ? `${column.minWidth}px` : undefined,
              maxWidth: column.maxWidth ? `${column.maxWidth}px` : undefined,
              textAlign
            }}
            onClick={() => handleSort(column.key, column.sortable || false)}
            role="columnheader"
            aria-sort={
              isSorted 
                ? sortDirection === 'asc' 
                  ? 'ascending' 
                  : sortDirection === 'desc' 
                    ? 'descending' 
                    : 'none'
                : 'none'
            }
          >
            <span className="truncate">{column.header}</span>
            {column.sortable && (
              <span className="text-xs opacity-70">
                {sortIcon || '↕'}
              </span>
            )}
          </div>
        );
      })}
    </div>
  );
};

export const VirtualizedTable = <T,>({
  data,
  columns,
  height,
  rowHeight = 56,
  headerHeight = 48,
  loading = false,
  emptyMessage = 'No data available',
  onRowClick,
  onRowDoubleClick,
  selectedRowIndex,
  keyExtractor,
  className = '',
  containerClassName = '',
  overscanCount = 5,
  stickyHeader = true,
  showBorders = true,
  striped = false,
  hoverable = true,
  onSortChange,
  sortColumn,
  sortDirection,
  onScrollToTop,
  onScrollToBottom
}: VirtualizedTableProps<T>) => {
  const tokens = useTokens();
  const listRef = useRef<List>(null);
  const [isScrolled, setIsScrolled] = useState(false);

  // Memoize row data to prevent unnecessary re-renders
  const rowData = useMemo(() => ({
    items: data,
    columns,
    onRowClick,
    onRowDoubleClick,
    selectedRowIndex,
    keyExtractor,
    showBorders,
    striped,
    hoverable,
    tokens
  }), [
    data,
    columns,
    onRowClick,
    onRowDoubleClick,
    selectedRowIndex,
    keyExtractor,
    showBorders,
    striped,
    hoverable,
    tokens
  ]);

  // Handle scroll events
  const handleScroll = useCallback(({ scrollOffset }: { scrollOffset: number }) => {
    const newIsScrolled = scrollOffset > 0;
    if (newIsScrolled !== isScrolled) {
      setIsScrolled(newIsScrolled);
    }

    // Call scroll callbacks
    if (scrollOffset === 0 && onScrollToTop) {
      onScrollToTop();
    } else if (scrollOffset >= (data.length * rowHeight) - (typeof height === 'number' ? height : 400) && onScrollToBottom) {
      onScrollToBottom();
    }
  }, [isScrolled, onScrollToTop, onScrollToBottom, data.length, rowHeight, height]);

  // Scroll to selected row
  useEffect(() => {
    if (selectedRowIndex !== undefined && listRef.current) {
      listRef.current.scrollToItem(selectedRowIndex, 'smart');
    }
    
    // Cleanup function (no-op in this case, but good practice)
    return () => {
      // Any scroll-related cleanup would go here
    };
  }, [selectedRowIndex]);

  // Calculate total width
  const totalWidth = useMemo(() => {
    return columns.reduce((acc, column) => {
      const width = typeof column.width === 'number' ? column.width : 200; // Default width
      return acc + width;
    }, 0);
  }, [columns]);

  const containerHeight = typeof height === 'number' ? height : parseInt(height);
  const tableHeight = stickyHeader ? containerHeight - headerHeight : containerHeight;

  if (loading) {
    return (
      <div className={`${containerClassName}`}>
        <TableSkeleton 
          rows={Math.floor(tableHeight / rowHeight)}
          columns={columns.length}
        />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={`${containerClassName}`}>
        {stickyHeader && (
          <TableHeader
            columns={columns}
            showBorders={showBorders}
            onSortChange={onSortChange}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            height={headerHeight}
            tokens={tokens}
          />
        )}
        <div
          className="flex items-center justify-center text-ui-foreground-secondary"
          style={{ height: `${tableHeight}px` }}
        >
          <p className="text-center">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`
        relative overflow-hidden
        bg-ui-background-primary dark:bg-ui-background-tertiary
        rounded-lg
        ${showBorders ? 'border border-ui-border-light dark:border-ui-border-dark' : ''}
        ${containerClassName}
      `}
      style={{ height: `${containerHeight}px` }}
      role="table"
      aria-rowcount={data.length + (stickyHeader ? 1 : 0)}
      aria-colcount={columns.length}
    >
      {stickyHeader && (
        <TableHeader
          columns={columns}
          showBorders={showBorders}
          onSortChange={onSortChange}
          sortColumn={sortColumn}
          sortDirection={sortDirection}
          height={headerHeight}
          tokens={tokens}
        />
      )}
      
      <List
        ref={listRef}
        height={tableHeight}
        width="100%"
        itemCount={data.length}
        itemSize={rowHeight}
        itemData={rowData as any}
        overscanCount={overscanCount}
        onScroll={handleScroll}
        className={`
          ${className}
          ${stickyHeader && isScrolled ? 'shadow-sm' : ''}
        `}
        style={{ width: totalWidth > window.innerWidth ? totalWidth : '100%' }}
      >
        {TableRow}
      </List>
      
      {/* Scroll shadows for visual feedback */}
      {stickyHeader && isScrolled && (
        <div className="absolute top-12 left-0 right-0 h-2 bg-gradient-to-b from-black/5 to-transparent pointer-events-none" />
      )}
    </div>
  );
};

// Memoized version for better performance
export const MemoizedVirtualizedTable = React.memo(VirtualizedTable) as typeof VirtualizedTable;