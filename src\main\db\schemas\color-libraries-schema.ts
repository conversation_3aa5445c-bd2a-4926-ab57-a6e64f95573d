/**
 * Color Libraries Database Schema
 * Handles Pantone, RAL, and other color library data
 */

export const COLOR_LIBRARIES_SCHEMA = `
-- Color libraries table (Pantone, RAL, NCS, etc.)
CREATE TABLE IF NOT EXISTS color_libraries (
  id INTEGER PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_system BOOLEAN NOT NULL DEFAULT TRUE,
  version TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Library colors table (individual colors within libraries)
CREATE TABLE IF NOT EXISTS library_colors (
  id INTEGER PRIMARY KEY,
  external_id TEXT UNIQUE NOT NULL,
  library_id INTEGER NOT NULL REFERENCES color_libraries(id) ON DELETE CASCADE,
  code TEXT NOT NULL,
  name TEXT NOT NULL,
  hex CHAR(7) NOT NULL,
  cmyk TEXT,
  rgb TEXT,
  lab TEXT,
  hsl TEXT,
  notes TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  sort_order INTEGER DEFAULT 0,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  
  -- Constraints
  CHECK (
    length(hex) = 7 AND 
    substr(hex, 1, 1) = '#' AND
    hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'
  ),
  CHECK (length(trim(code)) > 0),
  CHECK (length(trim(name)) > 0),
  UNIQUE (library_id, code)
);

-- Color library metadata (for additional properties)
CREATE TABLE IF NOT EXISTS library_color_metadata (
  color_id INTEGER PRIMARY KEY REFERENCES library_colors(id) ON DELETE CASCADE,
  properties JSON,
  tags TEXT,
  search_terms TEXT,
  popularity_score INTEGER DEFAULT 0,
  usage_count INTEGER DEFAULT 0
) WITHOUT ROWID;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_library_colors_library ON library_colors(library_id);
CREATE INDEX IF NOT EXISTS idx_library_colors_code ON library_colors(library_id, code);
CREATE INDEX IF NOT EXISTS idx_library_colors_hex ON library_colors(hex);
CREATE INDEX IF NOT EXISTS idx_library_colors_active ON library_colors(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_library_colors_search ON library_colors(name, code);

-- Full-text search index for color names and codes
CREATE VIRTUAL TABLE IF NOT EXISTS library_colors_fts USING fts5(
  name, 
  code, 
  notes,
  content='library_colors',
  content_rowid='id'
);

-- Triggers for FTS updates
CREATE TRIGGER IF NOT EXISTS library_colors_fts_insert AFTER INSERT ON library_colors BEGIN
  INSERT INTO library_colors_fts(rowid, name, code, notes) 
  VALUES (new.id, new.name, new.code, new.notes);
END;

CREATE TRIGGER IF NOT EXISTS library_colors_fts_delete AFTER DELETE ON library_colors BEGIN
  INSERT INTO library_colors_fts(library_colors_fts, rowid, name, code, notes) 
  VALUES('delete', old.id, old.name, old.code, old.notes);
END;

CREATE TRIGGER IF NOT EXISTS library_colors_fts_update AFTER UPDATE ON library_colors BEGIN
  INSERT INTO library_colors_fts(library_colors_fts, rowid, name, code, notes) 
  VALUES('delete', old.id, old.name, old.code, old.notes);
  INSERT INTO library_colors_fts(rowid, name, code, notes) 
  VALUES (new.id, new.name, new.code, new.notes);
END;

-- Triggers for updated_at timestamps
CREATE TRIGGER IF NOT EXISTS update_color_libraries_timestamp 
AFTER UPDATE ON color_libraries
BEGIN
  UPDATE color_libraries SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_library_colors_timestamp 
AFTER UPDATE ON library_colors
BEGIN
  UPDATE library_colors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Insert default color libraries
INSERT OR IGNORE INTO color_libraries (id, code, name, description, version) VALUES
(1, 'PANTONE', 'PANTONE®', 'PANTONE Color Matching System', '2024'),
(2, 'RAL', 'RAL Classic', 'RAL Classic Color Collection', '2024'),
(3, 'NCS', 'Natural Color System', 'NCS Natural Color System', '2024'),
(4, 'USER', 'User Colors', 'User-created custom colors', '1.0');

-- Views for easy querying
CREATE VIEW IF NOT EXISTS v_library_colors AS
SELECT 
  lc.id,
  lc.external_id,
  cl.code as library_code,
  cl.name as library_name,
  lc.code,
  lc.name,
  lc.hex,
  lc.cmyk,
  lc.rgb,
  lc.lab,
  lc.hsl,
  lc.notes,
  lc.is_active,
  lc.sort_order,
  lcm.properties,
  lcm.tags,
  lcm.popularity_score,
  lcm.usage_count,
  lc.created_at,
  lc.updated_at
FROM library_colors lc
INNER JOIN color_libraries cl ON lc.library_id = cl.id
LEFT JOIN library_color_metadata lcm ON lc.id = lcm.color_id
WHERE lc.is_active = TRUE
ORDER BY cl.code, lc.sort_order, lc.code;

-- View for Pantone colors specifically
CREATE VIEW IF NOT EXISTS v_pantone_colors AS
SELECT 
  lc.id,
  lc.external_id,
  lc.code,
  lc.name,
  lc.hex,
  lc.cmyk,
  lc.notes,
  lc.sort_order
FROM library_colors lc
INNER JOIN color_libraries cl ON lc.library_id = cl.id
WHERE cl.code = 'PANTONE' AND lc.is_active = TRUE
ORDER BY lc.sort_order, lc.code;

-- View for RAL colors specifically  
CREATE VIEW IF NOT EXISTS v_ral_colors AS
SELECT 
  lc.id,
  lc.external_id,
  lc.code,
  lc.name,
  lc.hex,
  lc.cmyk,
  lc.notes,
  lc.sort_order
FROM library_colors lc
INNER JOIN color_libraries cl ON lc.library_id = cl.id
WHERE cl.code = 'RAL' AND lc.is_active = TRUE
ORDER BY lc.sort_order, lc.code;
`;

/**
 * Color library management utilities
 */
export interface ColorLibrary {
  id: number;
  code: string;
  name: string;
  description?: string;
  version?: string;
}

export interface LibraryColor {
  id?: number;
  external_id: string;
  library_id: number;
  code: string;
  name: string;
  hex: string;
  cmyk?: string;
  rgb?: string;
  lab?: string;
  hsl?: string;
  notes?: string;
  sort_order?: number;
}

export const LIBRARY_CODES = {
  PANTONE: 'PANTONE',
  RAL: 'RAL',
  NCS: 'NCS',
  USER: 'USER',
} as const;

export type LibraryCode = (typeof LIBRARY_CODES)[keyof typeof LIBRARY_CODES];
