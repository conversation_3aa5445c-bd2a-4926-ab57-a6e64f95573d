/**
 * @file color-service-validator-integration.test.ts
 * @description Integration tests for ColorService and ColorValidator
 *
 * Tests that ColorService properly uses the ColorValidator service for
 * validation and standardization operations after the refactoring.
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { ColorService } from '../../../db/services/color.service';
import { ColorValidator } from '../color-validator.service';
import { ColorSpaceCalculator } from '../color-space-calculator.service';
import { GradientProcessor } from '../gradient-processor.service';
import { ColorSyncService } from '../color-sync.service';
import { ColorAnalyticsService } from '../color-analytics.service';

// Mock database and repository for testing
const mockDb = {
  prepare: () => ({
    get: () => ({ id: 1 }),
    run: () => ({ changes: 1, lastInsertRowid: 1 }),
    all: () => [],
  }),
  exec: () => {},
  close: () => {},
} as any;

const mockColorRepository = {
  findAll: () => [],
  findById: () => null,
  insert: () => 'test-id',
  update: () => true,
  softDelete: () => true,
  markAsSynced: () => true,
  invalidateOrphans: () => true,
  clearAll: () => true,
  findSoftDeleted: () => [],
  restoreRecord: () => true,
  bulkRestoreRecords: () => ({ success: true, restored: 0 }),
  cleanupOldSoftDeleted: () => ({ success: true, cleaned: 0 }),
  getUsageCounts: () => new Map(),
  getColorNameProductMap: () => new Map(),
  findUnsynced: () => [],
} as any;

describe('ColorService + ColorValidator Integration', () => {
  let colorService: ColorService;
  let colorValidator: ColorValidator;
  let colorSpaceCalculator: ColorSpaceCalculator;
  let gradientProcessor: GradientProcessor;
  let colorSyncService: ColorSyncService;
  let colorAnalyticsService: ColorAnalyticsService;

  beforeEach(() => {
    colorValidator = new ColorValidator();
    colorSpaceCalculator = new ColorSpaceCalculator();
    gradientProcessor = new GradientProcessor();
    colorSyncService = new ColorSyncService(
      mockDb,
      mockColorRepository,
      colorValidator,
      gradientProcessor
    );
    colorAnalyticsService = new ColorAnalyticsService(
      mockDb,
      mockColorRepository
    );
    colorService = new ColorService(
      mockDb,
      mockColorRepository,
      colorSpaceCalculator,
      gradientProcessor,
      colorValidator,
      colorSyncService,
      colorAnalyticsService
    );
  });

  describe('Service Injection', () => {
    test('should have ColorValidator injected into ColorService', () => {
      expect((colorService as any).colorValidator).toBeInstanceOf(
        ColorValidator
      );
    });

    test('should use default ColorValidator when none provided', () => {
      const serviceWithDefaults = new ColorService(mockDb, mockColorRepository);
      expect((serviceWithDefaults as any).colorValidator).toBeInstanceOf(
        ColorValidator
      );
    });

    test('should use provided ColorValidator instance', () => {
      const customValidator = new ColorValidator();
      const serviceWithCustom = new ColorService(
        mockDb,
        mockColorRepository,
        colorSpaceCalculator,
        gradientProcessor,
        customValidator
      );
      expect((serviceWithCustom as any).colorValidator).toBe(customValidator);
    });

    test('should have ColorSyncService injected into ColorService', () => {
      expect((colorService as any).colorSyncService).toBeInstanceOf(
        ColorSyncService
      );
    });

    test('should delegate sync operations to ColorSyncService', () => {
      // Verify that sync methods delegate to the ColorSyncService
      expect(typeof colorService.pushColorToSupabase).toBe('function');
      expect(typeof colorService.syncColorsFromSupabase).toBe('function');
      expect(typeof colorService.getUnsynced).toBe('function');
    });

    test('should have ColorAnalyticsService injected into ColorService', () => {
      expect((colorService as any).colorAnalyticsService).toBeInstanceOf(
        ColorAnalyticsService
      );
    });

    test('should delegate analytics operations to ColorAnalyticsService', () => {
      // Verify that analytics methods delegate to the ColorAnalyticsService
      expect(typeof colorService.getColorUsageCounts).toBe('function');
      expect(typeof colorService.buildColorNameProductMap).toBe('function');
      expect(typeof colorService.getProductsByColorName).toBe('function');
      expect(typeof colorService.getAllWithUsage).toBe('function');
    });
  });

  describe('Validation Integration', () => {
    test('should validate HEX colors using ColorValidator', () => {
      // Test that ColorValidator methods are available and working
      const hexResult = colorValidator.validateHex('#FF0000');
      expect(hexResult.isValid).toBe(true);
      expect(hexResult.errors).toHaveLength(0);

      const standardized = colorValidator.standardizeHex('#ff0000');
      expect(standardized).toBe('#FF0000');
    });

    test('should validate CMYK values using ColorValidator', () => {
      const cmykResult = colorValidator.validateCMYK('C:0 M:100 Y:100 K:0');
      expect(cmykResult.isValid).toBe(true);
      expect(cmykResult.errors).toHaveLength(0);

      const standardized = colorValidator.standardizeCMYK(
        'c:0 m:100 y:100 k:0'
      );
      expect(standardized).toBe('C:0 M:100 Y:100 K:0');
    });

    test('should normalize color names using ColorValidator', () => {
      const normalized = colorValidator.normalizeColorName('bright red');
      expect(normalized).toBe('Bright Red');
    });

    test('should standardize color codes using ColorValidator', () => {
      const standardized = colorValidator.standardizeColorCode('PMS 186 C');
      expect(standardized).toBe('186 C');
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle validation errors gracefully', () => {
      const invalidHex = colorValidator.validateHex('invalid');
      expect(invalidHex.isValid).toBe(false);
      expect(invalidHex.errors.length).toBeGreaterThan(0);

      const invalidCMYK = colorValidator.validateCMYK('invalid cmyk');
      expect(invalidCMYK.isValid).toBe(false);
      expect(invalidCMYK.errors.length).toBeGreaterThan(0);
    });

    test('should provide detailed error messages', () => {
      const result = colorValidator.validateHex('FF0000'); // Missing #
      expect(result.errors).toContain('HEX color code must start with #');

      const cmykResult = colorValidator.validateCMYK('C:150 M:0 Y:0 K:0'); // Out of range
      expect(cmykResult.errors.some(e => e.includes('out of range'))).toBe(
        true
      );
    });
  });

  describe('Advanced Validation Features', () => {
    test('should support validation options', () => {
      // Test allowEmpty option
      const emptyResult = colorValidator.validateHex('', { allowEmpty: true });
      expect(emptyResult.isValid).toBe(true);

      // Test strict mode
      const strictResult = colorValidator.validateHex('#ff0000', {
        strict: true,
      });
      expect(strictResult.warnings).toContain(
        'HEX color code should be uppercase for consistency'
      );

      // Test allowShortHex option
      const shortHexResult = colorValidator.validateHex('#FFF', {
        allowShortHex: true,
      });
      expect(shortHexResult.isValid).toBe(true);
    });

    test('should support format detection', () => {
      const hexDetection = colorValidator.detectColorFormat('#FF0000');
      expect(hexDetection.format).toBe('hex');
      expect(hexDetection.isValid).toBe(true);

      const cmykDetection = colorValidator.detectColorFormat(
        'C:0 M:100 Y:100 K:0'
      );
      expect(cmykDetection.format).toBe('cmyk');
      expect(cmykDetection.isValid).toBe(true);

      const pantoneDetection = colorValidator.detectColorFormat('186 C');
      expect(pantoneDetection.format).toBe('pantone');
      expect(pantoneDetection.isValid).toBe(true);
    });

    test('should support comprehensive validation', () => {
      const colorData = {
        hex: '#FF0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        name: 'bright red',
        code: 'RED-01',
      };

      const result = colorValidator.validateCompleteColorData(colorData);
      expect(result.errors).toHaveLength(0);
      expect(result.hex).toBe('#FF0000');
      expect(result.cmyk).toBe('C:0 M:100 Y:100 K:0');
      expect(result.name).toBe('Bright Red');
      expect(result.code).toBe('RED-01');
    });

    test('should support business rule validation', () => {
      const databaseData = {
        hex: '#FF0000',
        name: 'Red',
        organizationId: 'org-123',
      };

      const dbResult = colorValidator.validateForDatabase(databaseData);
      expect(dbResult.isValid).toBe(true);

      const syncData = {
        hex: '#FF0000',
        externalId: 'color-123',
      };

      const syncResult = colorValidator.validateForSync(syncData);
      expect(syncResult.isValid).toBe(true);
    });
  });

  describe('Performance and Edge Cases', () => {
    test('should handle null and undefined inputs gracefully', () => {
      expect(() =>
        colorValidator.normalizeColorName(null as any)
      ).not.toThrow();
      expect(() =>
        colorValidator.standardizeColorCode(undefined as any)
      ).not.toThrow();
      expect(() =>
        colorValidator.validateHex('', { allowEmpty: true })
      ).not.toThrow();
    });

    test('should maintain consistent behavior', () => {
      // Test multiple calls return same results
      const hex = '#FF0000';
      const result1 = colorValidator.validateHex(hex);
      const result2 = colorValidator.validateHex(hex);

      expect(result1.isValid).toBe(result2.isValid);
      expect(result1.errors).toEqual(result2.errors);
    });

    test('should provide service metadata', () => {
      const info = colorValidator.getServiceInfo();
      expect(info.name).toBe('ColorValidator');
      expect(info.version).toBeDefined();
      expect(info.features).toBeInstanceOf(Array);
      expect(info.features.length).toBeGreaterThan(0);

      const rules = colorValidator.getValidationRules();
      expect(rules.hex).toBeDefined();
      expect(rules.cmyk).toBeDefined();
      expect(rules.name).toBeDefined();
      expect(rules.code).toBeDefined();

      const formats = colorValidator.getSupportedFormats();
      expect(formats).toContain('hex');
      expect(formats).toContain('cmyk');
    });
  });
});
