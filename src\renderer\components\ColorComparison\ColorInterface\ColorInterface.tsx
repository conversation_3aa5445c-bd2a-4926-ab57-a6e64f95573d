/**
 * Refactored Color Interface Component
 * Applies surgical decomposition with proper separation of concerns
 */

import React, { useState, useCallback, memo, useEffect } from 'react';
import { X } from 'lucide-react';
import { useColorComparisonStore } from '../../../store/colorComparison.store';
// import { useTokens } from '../../../hooks/useTokens';
import { useToast } from '../../../hooks/useToast';

// Custom hooks
import { useColorCalculations } from './hooks/useColorCalculations';
import { useSimilarColors } from './hooks/useSimilarColors';
import { useColorRecommendations } from './hooks/useColorRecommendations';

// Components
import { ComparisonTab } from './components/ComparisonTab';
import { MetricsTab } from './components/MetricsTab';
import { PrintTab } from './components/PrintTab';
import { AccessibilityTab } from './components/AccessibilityTab';
import { HarmonyTab } from './components/HarmonyTab';
import { OutputTab } from './components/OutputTab';
import { BatchAnalysisTab } from './components/BatchAnalysisTab/index';

// Types
import type { SimpleColorEntry, TabType } from './types';

// Tab configuration with lazy loading
const TABS = [
  { id: 'comparison' as const, label: 'Comparison', icon: '⚖️' },
  { id: 'metrics' as const, label: 'Metrics', icon: '📊' },
  { id: 'print' as const, label: 'Print', icon: '🖨️' },
  { id: 'accessibility' as const, label: 'Accessibility', icon: '👁️' },
  { id: 'harmony' as const, label: 'Harmony', icon: '🎨' },
  { id: 'output' as const, label: 'Output', icon: '📤' },
  { id: 'batch' as const, label: 'Batch Analysis', icon: '📋' },
] as const;

const ColorInterface: React.FC = memo(() => {
  // const _tokens = useTokens();
  const { toast } = useToast();
  const {
    comparisonColors,
    activeColorIndex,
    setActiveColorIndex,
    clearComparison,
    removeColorFromComparison,
    harmonyResults,
    selectedHarmonyType,
    setHarmonyType,
    generateHarmonies,
  } = useColorComparisonStore();

  const [activeTab, setActiveTab] = useState<TabType>('comparison');
  const [outputFormat, setOutputFormat] = useState('hex');

  // Get selected colors
  const selectedColor =
    activeColorIndex !== null
      ? comparisonColors[activeColorIndex] || null
      : null;
  const secondaryColor =
    comparisonColors.find((_, idx) => idx !== activeColorIndex) || null;

  // Use custom hooks for calculations
  const {
    primaryMetrics,
    secondaryMetrics: _secondaryMetrics,
    deltaE,
    contrastWithWhite,
    contrastWithBlack,
    contrastWithSecondary,
    inkCoverage,
    dominantInk,
  } = useColorCalculations(
    selectedColor ? convertToSimpleColor(selectedColor) : null,
    secondaryColor ? convertToSimpleColor(secondaryColor) : null
  );

  const { similarColors, nearestPrintable: _nearestPrintable } =
    useSimilarColors(
      selectedColor ? convertToSimpleColor(selectedColor) : null
    );

  const { recommendations } = useColorRecommendations({
    primaryColor: selectedColor ? convertToSimpleColor(selectedColor) : null,
    secondaryColor: secondaryColor
      ? convertToSimpleColor(secondaryColor)
      : null,
    contrastResult: contrastWithSecondary,
    deltaE,
    inkCoverage,
  });

  // Memoized callbacks
  const handleTabChange = useCallback((tab: TabType) => {
    setActiveTab(tab);
  }, []);

  const handleColorSelect = useCallback(
    (index: number) => {
      setActiveColorIndex(index);
    },
    [setActiveColorIndex]
  );

  const handleRemoveColor = useCallback(
    (id: string) => {
      const index = comparisonColors.findIndex(color => color.id === id);
      if (index !== -1) {
        removeColorFromComparison(index);
      }
    },
    [removeColorFromComparison, comparisonColors]
  );

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Tab switching: Cmd/Ctrl + 4-6 (continuing from modal shortcuts 1-3)
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case '4':
            e.preventDefault();
            setActiveTab('comparison');
            break;
          case '5':
            e.preventDefault();
            setActiveTab('harmony');
            break;
          case '6':
            e.preventDefault();
            setActiveTab('output');
            break;
          case 'r':
            // Cmd/Ctrl + R: Regenerate harmony
            e.preventDefault();
            if (activeTab === 'harmony' && comparisonColors.length > 0) {
              generateHarmonies();
              toast({
                title: 'Harmony colors regenerated',
                type: 'success',
              });
            }
            break;
          case 'e':
            // Cmd/Ctrl + E: Export current analysis
            e.preventDefault();
            if (activeTab === 'output') {
              // Trigger export functionality
              const exportButton = document.querySelector(
                '[data-export-button]'
              ) as HTMLButtonElement;
              if (exportButton) {
                exportButton.click();
              }
            }
            break;
        }
      }

      // Space: Quick copy selected color
      if (
        e.key === ' ' &&
        selectedColor &&
        !(
          e.target instanceof HTMLElement && e.target.matches('input, textarea')
        )
      ) {
        e.preventDefault();
        navigator.clipboard.writeText(selectedColor.hex);
        toast({
          title: `Copied ${selectedColor.hex}`,
          type: 'success',
        });
      }

      // Delete: Remove selected color
      if (e.key === 'Delete' && selectedColor) {
        e.preventDefault();
        handleRemoveColor(selectedColor.id);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [
    activeTab,
    selectedColor,
    comparisonColors,
    generateHarmonies,
    handleRemoveColor,
    toast,
    setActiveTab,
  ]);

  const handleClearAll = useCallback(() => {
    clearComparison();
    setActiveTab('comparison');
  }, [clearComparison]);

  // Render tab content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'comparison':
        return (
          <ComparisonTab
            selectedColor={
              selectedColor ? convertToSimpleColor(selectedColor) : null
            }
            secondaryColor={
              secondaryColor ? convertToSimpleColor(secondaryColor) : null
            }
            recommendations={recommendations}
            similarColors={similarColors}
          />
        );

      case 'metrics':
        return (
          <MetricsTab
            selectedColor={
              selectedColor ? convertToSimpleColor(selectedColor) : null
            }
            metrics={primaryMetrics}
          />
        );

      case 'print':
        return (
          <PrintTab
            selectedColor={
              selectedColor ? convertToSimpleColor(selectedColor) : null
            }
            inkCoverage={inkCoverage}
            dominantInk={dominantInk}
          />
        );

      case 'accessibility':
        return (
          <AccessibilityTab
            selectedColor={
              selectedColor ? convertToSimpleColor(selectedColor) : null
            }
            contrastResults={{
              white: contrastWithWhite!,
              black: contrastWithBlack!,
              gray: { ratio: 2.5, level: 'Fail', passes: false }, // Placeholder
            }}
          />
        );

      case 'harmony':
        return (
          <HarmonyTab
            selectedColor={
              selectedColor ? convertToSimpleColor(selectedColor) : null
            }
            harmonyType={selectedHarmonyType}
            harmonyColors={harmonyResults.map(convertToSimpleColor)}
            onHarmonyTypeChange={setHarmonyType}
          />
        );

      case 'output':
        return (
          <OutputTab
            selectedColor={
              selectedColor ? convertToSimpleColor(selectedColor) : null
            }
            outputFormat={outputFormat}
            onFormatChange={setOutputFormat}
          />
        );

      case 'batch':
        return (
          <BatchAnalysisTab
            colors={comparisonColors.map(convertToSimpleColor)}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className='flex-1 bg-ui-background-primary flex flex-col overflow-hidden h-full'>
      {/* Header */}
      <div className='flex items-center justify-between p-3 border-b border-ui-border-light'>
        <h3 className='text-sm font-semibold text-ui-foreground-primary'>
          Color Analysis
        </h3>
        <div className='flex items-center gap-2'>
          {comparisonColors.length > 0 && (
            <button
              onClick={handleClearAll}
              className='text-xs text-ui-foreground-secondary hover:text-ui-foreground-primary transition-colors'
            >
              Clear All
            </button>
          )}
        </div>
      </div>

      {/* Color Selection Bar */}
      {comparisonColors.length > 0 && (
        <div className='flex items-center gap-2 p-3 border-b border-ui-border-light overflow-x-auto'>
          {comparisonColors.map((color, index) => (
            <ColorChip
              key={color.id}
              color={color}
              isActive={index === activeColorIndex}
              onClick={() => handleColorSelect(index)}
              onRemove={() => handleRemoveColor(color.id)}
            />
          ))}
        </div>
      )}

      {/* Tab Navigation */}
      <div className='flex border-b border-ui-border-light overflow-x-auto'>
        {TABS.map(tab => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`px-4 py-2 text-xs font-medium whitespace-nowrap transition-colors ${
              activeTab === tab.id
                ? 'text-brand-primary border-b-2 border-brand-primary'
                : 'text-ui-foreground-secondary hover:text-ui-foreground-primary'
            }`}
          >
            <span className='mr-1'>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className='flex-1 overflow-y-auto min-h-0'>
        {comparisonColors.length === 0 ? (
          <div className='flex items-center justify-center h-full text-ui-foreground-secondary'>
            <div className='text-center'>
              <p className='mb-2'>No colors selected</p>
              <p className='text-xs'>
                Add colors from the library to begin analysis
              </p>
            </div>
          </div>
        ) : (
          renderTabContent()
        )}
      </div>
    </div>
  );
});

// Helper component for color chips
const ColorChip = memo<{
  color: any;
  isActive: boolean;
  onClick: () => void;
  onRemove: () => void;
}>(({ color, isActive, onClick, onRemove }) => (
  <div
    className={`relative group cursor-pointer transition-all ${
      isActive ? 'ring-2 ring-brand-primary' : ''
    }`}
  >
    <div
      onClick={onClick}
      className='w-12 h-12 rounded-md border border-ui-border'
      style={{ backgroundColor: color.hex }}
      title={`${color.code || 'Unnamed'} - ${color.hex}`}
    />
    <button
      onClick={e => {
        e.stopPropagation();
        onRemove();
      }}
      className='absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center'
    >
      <X className='w-3 h-3' />
    </button>
  </div>
));

// Helper function to convert color entries
function convertToSimpleColor(color: any): SimpleColorEntry {
  return {
    id: color.id,
    hex: color.hex,
    pantone: color.code || 'Custom',
    cmyk: color.cmyk || 'N/A',
  };
}

ColorInterface.displayName = 'ColorInterface';
ColorChip.displayName = 'ColorChip';

export default ColorInterface;
