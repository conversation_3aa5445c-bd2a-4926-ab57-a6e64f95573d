/**
 * Database Schema Management
 * Handles schema creation, verification, and updates
 */

// fs and path imports removed - no longer needed after legacy cleanup
import { COMPLETE_SCHEMA } from '../schemas/complete-schema';

/**
 * Schema manager for database table creation and verification
 */
export class SchemaManager {
  private db: any;

  constructor(database: any) {
    this.db = database;
  }

  /**
   * Check if all required tables exist
   */
  async verifyRequiredTables(): Promise<{
    existingTables: string[];
    missingTables: string[];
    needsCompleteSchema: boolean;
  }> {
    const requiredTables = [
      'colors',
      'products',
      'organizations',
      'organization_members',
      'users',
    ];
    const existingTables = this.db
      .prepare(
        `
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN (${requiredTables.map(() => '?').join(',')})
    `
      )
      .all(...requiredTables)
      .map((row: any) => row.name);

    console.log('[SchemaManager] Existing tables:', existingTables);

    const missingTables = requiredTables.filter(
      table => !existingTables.includes(table)
    );
    const needsCompleteSchema =
      missingTables.includes('colors') || missingTables.includes('products');

    return {
      existingTables,
      missingTables,
      needsCompleteSchema,
    };
  }

  /**
   * Create the complete schema with all required tables
   */
  async createCompleteSchema(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    console.log(
      '[SchemaManager] Creating complete schema with organization support...'
    );

    try {
      this.db.exec(COMPLETE_SCHEMA);
      console.log('[SchemaManager] Complete schema created successfully');
    } catch (error) {
      console.error('[SchemaManager] Error creating complete schema:', error);
      throw error;
    }
  }

  // Legacy schema creation removed - use createCompleteSchema() instead

  /**
   * Ensure organization tables exist
   */
  async ensureOrganizationTables(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    console.log('[SchemaManager] Ensuring organization tables exist...');

    try {
      // Create organization tables if they don't exist
      this.db.exec(`
        -- Organizations table (UUID primary key)
        CREATE TABLE IF NOT EXISTS organizations (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          slug TEXT UNIQUE NOT NULL,
          plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
          settings JSON DEFAULT '{}',
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
          
          -- Constraints
          CHECK (length(id) = 36),
          CHECK (length(trim(name)) > 0),
          CHECK (length(trim(slug)) > 0)
        );

        -- Organization members table
        CREATE TABLE IF NOT EXISTS organization_members (
          organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
          user_id TEXT NOT NULL,
          role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
          joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
          invited_by TEXT,
          PRIMARY KEY (organization_id, user_id)
        );

        -- Organization invitations table
        CREATE TABLE IF NOT EXISTS organization_invitations (
          id TEXT PRIMARY KEY,
          organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
          email TEXT NOT NULL,
          role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
          invited_by TEXT NOT NULL,
          invited_at TEXT DEFAULT CURRENT_TIMESTAMP,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          expires_at TEXT NOT NULL,
          accepted_at TEXT,
          token TEXT UNIQUE NOT NULL,
          UNIQUE(organization_id, email)
        );

        -- Users table (local cache of user data)
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          display_name TEXT,
          avatar_url TEXT,
          preferences JSON DEFAULT '{}',
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_org_members_user ON organization_members(user_id);
        CREATE INDEX IF NOT EXISTS idx_org_slug ON organizations(slug);
        CREATE INDEX IF NOT EXISTS idx_organizations_external ON organizations(external_id);
        CREATE INDEX IF NOT EXISTS idx_invitations_email ON organization_invitations(email);
        CREATE INDEX IF NOT EXISTS idx_invitations_token ON organization_invitations(token);
        CREATE INDEX IF NOT EXISTS idx_invitations_org ON organization_invitations(organization_id);
        CREATE UNIQUE INDEX IF NOT EXISTS idx_invitations_external_id ON organization_invitations(external_id);

        -- Triggers for updated_at
        CREATE TRIGGER IF NOT EXISTS update_organizations_timestamp 
        AFTER UPDATE ON organizations
        BEGIN
          UPDATE organizations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;

        CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
        AFTER UPDATE ON users
        BEGIN
          UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;
      `);

      console.log('[SchemaManager] Organization tables created successfully');
    } catch (error) {
      console.error(
        '[SchemaManager] Error creating organization tables:',
        error
      );
      throw error;
    }
  }

  /**
   * Verify and update table schemas
   */
  async verifyAndUpdateSchemas(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    console.log('[SchemaManager] Verifying table schemas...');

    try {
      await this.updateUsersTableSchema();
      await this.addOrganizationColumns();

      console.log('[SchemaManager] Table schemas verified and updated');
    } catch (error) {
      console.error('[SchemaManager] Error verifying table schemas:', error);
      // Don't throw - allow app to continue
    }
  }

  /**
   * Update users table schema to use display_name instead of name
   */
  private async updateUsersTableSchema(): Promise<void> {
    // Check if users table has the correct schema
    const userColumns = this.db
      .prepare(
        `
      SELECT name FROM pragma_table_info('users')
    `
      )
      .all()
      .map((col: any) => col.name);

    console.log('[SchemaManager] Users table columns:', userColumns);

    // The OAuth service expects 'display_name', not 'name'
    if (!userColumns.includes('display_name') && userColumns.includes('name')) {
      console.log('[SchemaManager] Migrating users table schema...');
      this.db.exec(`
        -- Create new users table with correct schema
        CREATE TABLE IF NOT EXISTS users_new (
          id TEXT PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          display_name TEXT,
          avatar_url TEXT,
          preferences JSON DEFAULT '{}',
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Copy data from old table if it exists
        INSERT OR IGNORE INTO users_new (id, email, display_name, avatar_url, preferences, created_at, updated_at)
        SELECT id, email, name as display_name, avatar_url, 
               CASE WHEN metadata IS NOT NULL THEN metadata ELSE '{}' END as preferences, 
               created_at, updated_at FROM users;
        
        -- Drop old table and rename new one
        DROP TABLE users;
        ALTER TABLE users_new RENAME TO users;
        
        -- Recreate trigger
        CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
        AFTER UPDATE ON users
        BEGIN
          UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;
      `);
      console.log('[SchemaManager] Users table schema migrated successfully');
    }
  }

  /**
   * Add organization_id columns to colors and products tables
   */
  private async addOrganizationColumns(): Promise<void> {
    // Verify colors table has organization_id column
    const colorColumns = this.db
      .prepare(
        `
      SELECT name FROM pragma_table_info('colors')
    `
      )
      .all()
      .map((col: any) => col.name);

    if (!colorColumns.includes('organization_id')) {
      console.log('[SchemaManager] Adding organization_id to colors table...');
      this.db.exec(`
        ALTER TABLE colors ADD COLUMN organization_id INTEGER;
        ALTER TABLE colors ADD COLUMN created_by TEXT;
      `);
    }

    // Verify products table has organization_id column
    const productColumns = this.db
      .prepare(
        `
      SELECT name FROM pragma_table_info('products')
    `
      )
      .all()
      .map((col: any) => col.name);

    if (!productColumns.includes('organization_id')) {
      console.log(
        '[SchemaManager] Adding organization_id to products table...'
      );
      this.db.exec(`
        ALTER TABLE products ADD COLUMN organization_id INTEGER;
        ALTER TABLE products ADD COLUMN created_by TEXT;
      `);
    }
  }
}
