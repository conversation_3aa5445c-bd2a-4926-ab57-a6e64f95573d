import { Page } from 'playwright';

export interface RTLTestResult {
  component: string;
  issues: string[];
  passed: boolean;
  screenshot?: string;
}

export class RTLTester {
  private results: RTLTestResult[] = [];

  async setRTLMode(page: Page, enabled: boolean): Promise<void> {
    await page.evaluate(rtlEnabled => {
      // Set the dir attribute on the document element
      document.documentElement.dir = rtlEnabled ? 'rtl' : 'ltr';

      // Dispatch an event to notify components
      window.dispatchEvent(
        new CustomEvent('directionchange', {
          detail: { direction: rtlEnabled ? 'rtl' : 'ltr' },
        })
      );
    }, enabled);
  }

  async checkElementAlignment(
    page: Page,
    selector: string,
    expectedRTLProperty: 'right' | 'left',
    propertyName: string = 'text-align'
  ): Promise<boolean> {
    return page.evaluate(
      ({ selector, expectedRTLProperty, propertyName }) => {
        const element = document.querySelector(selector);
        if (!element) {
          return false;
        }

        const style = window.getComputedStyle(element);
        // Use type assertion to avoid TypeScript error
        return (style as any)[propertyName] === expectedRTLProperty;
      },
      { selector, expectedRTLProperty, propertyName }
    );
  }

  async checkFlexDirection(page: Page, selector: string): Promise<boolean> {
    return page.evaluate(selector => {
      const element = document.querySelector(selector);
      if (!element) {
        return false;
      }

      const style = window.getComputedStyle(element);
      const isRTL = document.documentElement.dir === 'rtl';

      // In RTL, row should be row-reverse
      if (isRTL) {
        return style.flexDirection === 'row-reverse';
      }

      // In LTR, row should be row
      return style.flexDirection === 'row';
    }, selector);
  }

  async checkIconFlip(page: Page, selector: string): Promise<boolean> {
    return page.evaluate(selector => {
      const element = document.querySelector(selector);
      if (!element) {
        return false;
      }

      const style = window.getComputedStyle(element);
      const isRTL = document.documentElement.dir === 'rtl';

      // In RTL, icons that indicate direction should be flipped
      if (isRTL) {
        return (
          style.transform.includes('scale(-1, 1)') ||
          style.transform.includes('rotate(180deg)')
        );
      }

      // In LTR, no transform needed
      return style.transform === 'none' || style.transform === '';
    }, selector);
  }

  async testComponent(
    page: Page,
    component: string,
    _selector: string,
    testCases: Array<() => Promise<boolean>>,
    takeScreenshot: boolean = false
  ): Promise<RTLTestResult> {
    const issues: string[] = [];

    // Set RTL mode
    await this.setRTLMode(page, true);

    // Wait for the component to update
    await page.waitForTimeout(500);

    // Run test cases
    for (let i = 0; i < testCases.length; i++) {
      const result = await testCases[i]();
      if (!result) {
        issues.push(`Test case ${i + 1} failed`);
      }
    }

    let screenshot: string | undefined;
    if (takeScreenshot) {
      screenshot = `rtl-${component}-${Date.now()}.png`;
      await page.screenshot({ path: `screenshots/${screenshot}` });
    }

    // Reset to LTR mode
    await this.setRTLMode(page, false);

    const result: RTLTestResult = {
      component,
      issues,
      passed: issues.length === 0,
      screenshot,
    };

    this.results.push(result);
    return result;
  }

  generateReport(): string {
    if (this.results.length === 0) {
      return 'No RTL test results available.';
    }

    let report = '# RTL Support Test Results\n\n';

    const passedTests = this.results.filter(r => r.passed);
    const failedTests = this.results.filter(r => !r.passed);

    report += `## Summary\n\n`;
    report += `- Total components tested: ${this.results.length}\n`;
    report += `- Passed: ${passedTests.length}\n`;
    report += `- Failed: ${failedTests.length}\n\n`;

    if (failedTests.length > 0) {
      report += `## Failed Components\n\n`;

      failedTests.forEach(result => {
        report += `### ${result.component}\n\n`;
        report += `Issues found:\n`;

        result.issues.forEach(issue => {
          report += `- ${issue}\n`;
        });

        if (result.screenshot) {
          report += `\n[Screenshot](screenshots/${result.screenshot})\n`;
        }

        report += '\n';
      });
    }

    return report;
  }

  getResults(): RTLTestResult[] {
    return this.results;
  }
}

export default RTLTester;
