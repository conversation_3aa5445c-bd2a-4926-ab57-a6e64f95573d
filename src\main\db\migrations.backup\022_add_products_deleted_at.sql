-- Migration: 022_add_products_deleted_at.sql
-- Purpose: Add deleted_at column to products table for soft delete support
-- Issue: Products reappear after deletion due to sync force-activation behavior
-- Date: 2025-06-18
-- Author: ChromaSync Team

-- Add deleted_at column to products table to match Supabase schema
-- This enables proper soft delete functionality aligned with colors table pattern
ALTER TABLE products ADD COLUMN deleted_at TEXT;

-- Create index for performance on deleted_at queries
CREATE INDEX IF NOT EXISTS idx_products_deleted_at ON products(deleted_at);

-- Create composite index for active products per organization (performance optimization)
CREATE INDEX IF NOT EXISTS idx_products_org_active ON products(organization_id, deleted_at) 
WHERE deleted_at IS NULL;