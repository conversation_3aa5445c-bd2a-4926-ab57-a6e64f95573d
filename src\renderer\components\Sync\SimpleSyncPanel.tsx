/**
 * @file SimpleSyncPanel.tsx
 * @description Simplified sync panel component using Zustand stores
 */

import React, { useState, useEffect } from 'react';
import { useSyncStatus, useSyncActions } from '../../store/sync.store';
import { SyncStatus as SyncStatusEnum } from '../../../shared/types/sync.types';

export const SimpleSyncPanel: React.FC = () => {
  const { status, message, lastSyncTime } = useSyncStatus();
  const { syncData } = useSyncActions();
  const [isSyncing, setIsSyncing] = useState(false);

  // Reset syncing state when status changes
  useEffect(() => {
    if (status !== SyncStatusEnum.SYNCING) {
      setIsSyncing(false);
    }
  }, [status]);
  
  // Format timestamp
  const formatTimestamp = (timestamp: number | null): string => {
    if (!timestamp) {return 'Never';}
    
    const date = new Date(timestamp);
    return date.toLocaleString();
  };
  
  // Handle sync button click
  const handleSync = async () => {
    if (isSyncing || status === SyncStatusEnum.SYNCING) {return;}
    
    setIsSyncing(true);
    
    try {
      await syncData();
    } catch (error) {
      console.error('Sync failed:', error);
      setIsSyncing(false);
    }
  };
  
  // Status indicator component
  const StatusIndicator = ({ status }: { status: SyncStatusEnum }) => {
    const getStatusColor = () => {
      switch (status) {
        case SyncStatusEnum.SUCCESS:
          return 'bg-green-500 dark:bg-green-400';
        case SyncStatusEnum.ERROR:
          return 'bg-red-500 dark:bg-red-400';
        case SyncStatusEnum.SYNCING:
          return 'bg-brand-primary dark:bg-brand-primary animate-pulse';
        default:
          return 'bg-gray-500 dark:bg-gray-400';
      }
    };
    
    return (
      <div className={`w-3 h-3 rounded-full ${getStatusColor()}`}></div>
    );
  };
  
  return (
    <div className="flex flex-col space-y-4 bg-ui-background-primary dark:bg-ui-background-tertiary rounded-[var(--radius-md)] p-4">
      <h2 className="text-lg font-semibold text-ui-foreground-primary dark:text-ui-foreground-primary">
        Sync Status
      </h2>
      
      <div className="flex flex-col space-y-2">
        <div className="flex items-center space-x-2">
          <StatusIndicator status={status} />
          <span className="text-sm text-ui-foreground-primary dark:text-ui-foreground-primary">
            {status === SyncStatusEnum.SYNCING ? 'Syncing...' : 
             status === SyncStatusEnum.SUCCESS ? 'Synced' :
             status === SyncStatusEnum.ERROR ? 'Sync failed' : 'Ready to sync'}
          </span>
        </div>
        
        {message && (
          <div className="text-xs text-ui-foreground-secondary dark:text-ui-foreground-secondary">
            {message}
          </div>
        )}
        
        <div className="text-xs text-ui-foreground-secondary dark:text-ui-foreground-secondary">
          Last synced: {formatTimestamp(lastSyncTime)}
        </div>
        
        <button
          onClick={handleSync}
          disabled={isSyncing || status === SyncStatusEnum.SYNCING}
          className={`mt-2 px-3 py-1 text-sm rounded-[var(--radius-md)] ${
            isSyncing || status === SyncStatusEnum.SYNCING
              ? 'bg-ui-background-tertiary dark:bg-ui-background-tertiary text-ui-foreground-secondary dark:text-ui-foreground-secondary cursor-not-allowed'
              : 'bg-brand-primary hover:bg-brand-primary/90 text-ui-foreground-inverse'
          }`}
        >
          {isSyncing || status === SyncStatusEnum.SYNCING ? 'Syncing...' : 'Sync Now'}
        </button>
      </div>
    </div>
  );
};
