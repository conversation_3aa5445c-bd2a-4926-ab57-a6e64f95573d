/**
 * @file sync-status-manager.service.ts
 * @description Centralized sync status manager with request deduplication and intelligent polling
 */

import { EventEmitter } from 'events';
import { syncOutboxService } from './sync-outbox.service';
import { unifiedSyncManager } from './unified-sync-manager';

interface SyncStatusCache {
  hasUnsyncedChanges: { data: any; timestamp: number };
  progress: { data: any; timestamp: number };
  queueStats: { data: any; timestamp: number };
  metrics: { data: any; timestamp: number };
}

interface RequestCoalescing {
  [key: string]: {
    promise: Promise<any>;
    timestamp: number;
  };
}

interface ActivityTracker {
  lastUserActivity: number;
  lastSyncActivity: number;
  isUserActive: boolean;
  isAppFocused: boolean;
}

export class SyncStatusManagerService extends EventEmitter {
  private cache: SyncStatusCache = {
    hasUnsyncedChanges: { data: null, timestamp: 0 },
    progress: { data: null, timestamp: 0 },
    queueStats: { data: null, timestamp: 0 },
    metrics: { data: null, timestamp: 0 }
  };

  private readonly CACHE_TTL = {
    hasUnsyncedChanges: 30000, // 30 seconds
    progress: 5000,            // 5 seconds during sync
    queueStats: 15000,         // 15 seconds
    metrics: 60000             // 1 minute
  };

  private readonly COALESCE_WINDOW = 100; // 100ms window for coalescing
  private requestCoalescing: RequestCoalescing = {};
  
  private activityTracker: ActivityTracker = {
    lastUserActivity: Date.now(),
    lastSyncActivity: 0,
    isUserActive: true,
    isAppFocused: true
  };

  private pollingInterval: NodeJS.Timeout | null = null;
  private currentPollingFrequency = 300000; // Start with 5 minutes
  private apiCallMetrics = {
    totalCalls: 0,
    callsThisHour: 0,
    lastHourReset: Date.now(),
    callHistory: [] as { method: string; timestamp: number; cached: boolean }[]
  };

  // Store bound event handlers for proper cleanup
  private boundEventHandlers: {
    onSyncStarted: () => void;
    onSyncCompleted: () => void;
    onSyncFailed: () => void;
  };

  constructor() {
    super();
    
    // Create bound event handlers for proper cleanup
    this.boundEventHandlers = {
      onSyncStarted: () => {
        this.activityTracker.lastSyncActivity = Date.now();
        this.adjustPollingFrequency();
      },
      onSyncCompleted: () => {
        this.activityTracker.lastSyncActivity = Date.now();
        this.invalidateCache(['hasUnsyncedChanges', 'progress', 'queueStats']);
        this.adjustPollingFrequency();
      },
      onSyncFailed: () => {
        this.activityTracker.lastSyncActivity = Date.now();
        this.adjustPollingFrequency();
      }
    };
    
    this.setupActivityTracking();
    this.startIntelligentPolling();
    this.setupSyncEventListeners();
  }

  /**
   * Clean up resources and event listeners
   */
  public destroy(): void {
    console.log('[SyncStatusManager] 🧹 Cleaning up event listeners and resources');
    
    // Remove event listeners from unifiedSyncManager
    if (unifiedSyncManager) {
      unifiedSyncManager.removeListener('sync_started', this.boundEventHandlers.onSyncStarted);
      unifiedSyncManager.removeListener('sync_completed', this.boundEventHandlers.onSyncCompleted);
      unifiedSyncManager.removeListener('sync_failed', this.boundEventHandlers.onSyncFailed);
    }
    
    // Clear polling interval
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    
    // Clear cache
    this.cache = {
      hasUnsyncedChanges: { data: null, timestamp: 0 },
      progress: { data: null, timestamp: 0 },
      queueStats: { data: null, timestamp: 0 },
      metrics: { data: null, timestamp: 0 }
    };
    
    // Clear request coalescing
    this.requestCoalescing = {};
    
    // Remove all EventEmitter listeners
    this.removeAllListeners();
  }

  /**
   * Setup activity tracking to adjust polling frequency
   */
  private setupActivityTracking(): void {
    // Clean up any existing listeners first to prevent memory leaks
    if (unifiedSyncManager) {
      unifiedSyncManager.removeListener('sync_started', this.boundEventHandlers.onSyncStarted);
      unifiedSyncManager.removeListener('sync_completed', this.boundEventHandlers.onSyncCompleted);
      unifiedSyncManager.removeListener('sync_failed', this.boundEventHandlers.onSyncFailed);
      
      // Add the bound event handlers
      unifiedSyncManager.on('sync_started', this.boundEventHandlers.onSyncStarted);
      unifiedSyncManager.on('sync_completed', this.boundEventHandlers.onSyncCompleted);
      unifiedSyncManager.on('sync_failed', this.boundEventHandlers.onSyncFailed);
      
      console.log('[SyncStatusManager] ✅ Event listeners registered with cleanup mechanism');
    } else {
      console.warn('[SyncStatusManager] ⚠️ unifiedSyncManager not available for event listener setup');
    }
  }

  /**
   * Setup listeners for sync events to invalidate cache
   */
  private setupSyncEventListeners(): void {
    // We'll use polling instead of monkey-patching methods
    // This is safer and more maintainable
    console.log('[SyncStatusManager] Using polling-based cache invalidation for reliability');
  }

  /**
   * Track user activity to adjust polling
   */
  public updateUserActivity(): void {
    this.activityTracker.lastUserActivity = Date.now();
    this.activityTracker.isUserActive = true;
    this.adjustPollingFrequency();
  }

  /**
   * Track app focus state
   */
  public setAppFocused(focused: boolean): void {
    this.activityTracker.isAppFocused = focused;
    this.adjustPollingFrequency();
  }

  /**
   * Adjust polling frequency based on activity
   */
  private adjustPollingFrequency(): void {
    const now = Date.now();
    const timeSinceUserActivity = now - this.activityTracker.lastUserActivity;
    const timeSinceSyncActivity = now - this.activityTracker.lastSyncActivity;

    let newFrequency: number;

    // High frequency during active sync (reduced to prevent resource contention)
    if (timeSinceSyncActivity < 60000) { // Last sync within 1 minute
      newFrequency = 30000; // 30 seconds (reduced from 10s)
    }
    // Medium frequency for active users (reduced to prevent interference)
    else if (this.activityTracker.isAppFocused && timeSinceUserActivity < 300000) { // Active within 5 minutes
      newFrequency = 300000; // 5 minutes (reduced from 1m)
    }
    // Low frequency for inactive users (increased to reduce background load)
    else if (timeSinceUserActivity < 1800000) { // Active within 30 minutes
      newFrequency = 600000; // 10 minutes (increased from 5m)
    }
    // Very low frequency for idle
    else {
      newFrequency = 900000; // 15 minutes
    }

    if (newFrequency !== this.currentPollingFrequency) {
      console.log(`[SyncStatusManager] Adjusting polling frequency from ${this.currentPollingFrequency/1000}s to ${newFrequency/1000}s`);
      this.currentPollingFrequency = newFrequency;
      this.restartPolling();
    }
  }

  /**
   * Start intelligent polling with adaptive frequency
   */
  private startIntelligentPolling(): void {
    this.pollingInterval = setInterval(() => {
      this.performBackgroundStatusCheck();
    }, this.currentPollingFrequency);
  }

  /**
   * Restart polling with new frequency
   */
  private restartPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }
    this.startIntelligentPolling();
  }

  /**
   * Perform background status check with minimal API calls
   */
  private async performBackgroundStatusCheck(): Promise<void> {
    try {
      // Only check if cache is stale and we haven't made too many calls
      if (this.shouldPerformBackgroundCheck()) {
        // Prioritize most important status first
        await this.hasUnsyncedLocalChanges();
        
        // Only check queue stats if there might be pending changes
        const cachedChanges = this.getCachedData('hasUnsyncedChanges');
        if (cachedChanges?.data?.hasChanges) {
          await this.getQueueStats();
        }
      }
    } catch (error) {
      console.warn('[SyncStatusManager] Background status check failed:', error);
    }
  }

  /**
   * Determine if background check should run
   */
  private shouldPerformBackgroundCheck(): boolean {
    // Don't check if app is not focused and user is inactive
    if (!this.activityTracker.isAppFocused && 
        Date.now() - this.activityTracker.lastUserActivity > 600000) { // 10 minutes
      return false;
    }

    // Don't check if we've made too many calls this hour
    this.cleanupCallHistory();
    if (this.apiCallMetrics.callsThisHour > 15) { // Max 15 calls per hour
      return false;
    }

    return true;
  }

  /**
   * Clean up old call history and reset hourly counter
   */
  private cleanupCallHistory(): void {
    const now = Date.now();
    const oneHourAgo = now - 3600000;

    // Reset hourly counter if needed
    if (now - this.apiCallMetrics.lastHourReset > 3600000) {
      this.apiCallMetrics.callsThisHour = 0;
      this.apiCallMetrics.lastHourReset = now;
    }

    // Clean up old call history
    this.apiCallMetrics.callHistory = this.apiCallMetrics.callHistory.filter(
      call => call.timestamp > oneHourAgo
    );
  }

  /**
   * Track API call for metrics
   */
  private trackApiCall(method: string, cached: boolean): void {
    this.apiCallMetrics.totalCalls++;
    if (!cached) {
      this.apiCallMetrics.callsThisHour++;
    }
    
    this.apiCallMetrics.callHistory.push({
      method,
      timestamp: Date.now(),
      cached
    });

    // Keep only last 100 calls in memory
    if (this.apiCallMetrics.callHistory.length > 100) {
      this.apiCallMetrics.callHistory = this.apiCallMetrics.callHistory.slice(-100);
    }
  }

  /**
   * Invalidate specific cache entries
   */
  private invalidateCache(keys: (keyof SyncStatusCache)[]): void {
    keys.forEach(key => {
      this.cache[key].timestamp = 0;
    });
  }

  /**
   * Get cached data if valid
   */
  private getCachedData(key: keyof SyncStatusCache): any | null {
    const cached = this.cache[key];
    const ttl = this.CACHE_TTL[key];
    
    if (cached.data && Date.now() - cached.timestamp < ttl) {
      return cached;
    }
    return null;
  }

  /**
   * Set cache data
   */
  private setCacheData(key: keyof SyncStatusCache, data: any): void {
    this.cache[key] = {
      data,
      timestamp: Date.now()
    };
  }

  /**
   * Coalesce rapid successive calls to the same method
   */
  private async coalesceRequest<T>(key: string, executor: () => Promise<T>): Promise<T> {
    const existing = this.requestCoalescing[key];
    const now = Date.now();

    // If there's a recent request in flight, return its promise
    if (existing && now - existing.timestamp < this.COALESCE_WINDOW) {
      console.log(`[SyncStatusManager] Coalescing ${key} request`);
      return existing.promise;
    }

    // Create new request
    const promise = executor();
    this.requestCoalescing[key] = { promise, timestamp: now };

    // Clean up after completion
    promise.finally(() => {
      if (this.requestCoalescing[key]?.timestamp === now) {
        delete this.requestCoalescing[key];
      }
    });

    return promise;
  }

  /**
   * Check for unsynced local changes with deduplication
   */
  public async hasUnsyncedLocalChanges(): Promise<any> {
    return this.coalesceRequest('hasUnsyncedLocalChanges', async () => {
      // Check cache first
      const cached = this.getCachedData('hasUnsyncedChanges');
      if (cached) {
        this.trackApiCall('hasUnsyncedLocalChanges', true);
        return cached.data;
      }

      this.trackApiCall('hasUnsyncedLocalChanges', false);
      
      const pendingChanges = syncOutboxService.getPendingChanges();
      const result = {
        success: true,
        count: pendingChanges.length,
        hasChanges: pendingChanges.length > 0,
        timestamp: Date.now()
      };

      this.setCacheData('hasUnsyncedChanges', result);
      return result;
    });
  }

  /**
   * Get sync progress with deduplication
   */
  public async getProgress(): Promise<any> {
    return this.coalesceRequest('getProgress', async () => {
      // Use shorter cache during active sync
      const status = unifiedSyncManager.getStatus();
      const isActiveSyncing = status.isSyncing;
      const ttl = isActiveSyncing ? 2000 : this.CACHE_TTL.progress;

      const cached = this.getCachedData('progress');
      if (cached && Date.now() - cached.timestamp < ttl) {
        this.trackApiCall('getProgress', true);
        return cached.data;
      }

      this.trackApiCall('getProgress', false);

      // Calculate progress percentage safely
      let progressPercentage = 0;
      let itemsProcessed = 0;
      let itemsTotal = 0;
      
      if (status.isSyncing && status.currentOperation) {
        progressPercentage = 50; // Indeterminate progress
        itemsProcessed = 1;
        itemsTotal = 1;
      }
      
      // Ensure progress is valid
      if (typeof progressPercentage !== 'number' || isNaN(progressPercentage)) {
        progressPercentage = 0;
      }
      progressPercentage = Math.max(0, Math.min(100, progressPercentage));
      
      const result = {
        success: true,
        progress: {
          phase: status.isSyncing ? (status.currentOperation || 'syncing') : 'idle',
          progress: progressPercentage,
          currentOperation: status.isSyncing ? `Syncing ${status.currentOperation || 'data'}` : 'No sync in progress',
          itemsProcessed,
          itemsTotal,
          errors: [],
          warnings: []
        },
        timestamp: Date.now()
      };

      this.setCacheData('progress', result);
      return result;
    });
  }

  /**
   * Get queue statistics with deduplication
   */
  public async getQueueStats(): Promise<any> {
    return this.coalesceRequest('getQueueStats', async () => {
      const cached = this.getCachedData('queueStats');
      if (cached) {
        this.trackApiCall('getQueueStats', true);
        return cached.data;
      }

      this.trackApiCall('getQueueStats', false);

      const status = unifiedSyncManager.getStatus();
      const result = {
        success: true,
        queueStats: {
          memoryQueue: {
            size: status.queueLength || 0,
            pending: status.queueLength || 0,
            failed: 0
          },
          persistentQueue: {
            size: 0,
            pending: 0,
            failed: 0,
            oldestItemAge: 0
          }
        },
        timestamp: Date.now()
      };

      this.setCacheData('queueStats', result);
      return result;
    });
  }

  /**
   * Get sync metrics with deduplication
   */
  public async getMetrics(): Promise<any> {
    return this.coalesceRequest('getMetrics', async () => {
      const cached = this.getCachedData('metrics');
      if (cached) {
        this.trackApiCall('getMetrics', true);
        return cached.data;
      }

      this.trackApiCall('getMetrics', false);

      // Calculate actual performance metrics
      const recentCalls = this.apiCallMetrics.callHistory.filter(
        call => Date.now() - call.timestamp < 300000 // Last 5 minutes
      );

      const avgResponseTime = recentCalls.length > 0 ? 150 : 0; // Placeholder
      const callsPerMinute = recentCalls.length / 5;
      const errorRate = 0.02; // Placeholder

      const result = {
        success: true,
        metrics: {
          performance: {
            averageResponseTime: avgResponseTime,
            operationsPerMinute: callsPerMinute,
            errorRate: errorRate
          },
          health: {
            status: 'healthy',
            networkQuality: 'good',
            lastHealthCheck: Date.now()
          },
          apiUsage: {
            totalCalls: this.apiCallMetrics.totalCalls,
            callsThisHour: this.apiCallMetrics.callsThisHour,
            cacheHitRate: this.calculateCacheHitRate()
          }
        },
        timestamp: Date.now()
      };

      this.setCacheData('metrics', result);
      return result;
    });
  }

  /**
   * Calculate cache hit rate for performance monitoring
   */
  private calculateCacheHitRate(): number {
    const recentCalls = this.apiCallMetrics.callHistory.filter(
      call => Date.now() - call.timestamp < 3600000 // Last hour
    );

    if (recentCalls.length === 0) return 0;

    const cachedCalls = recentCalls.filter(call => call.cached).length;
    return Math.round((cachedCalls / recentCalls.length) * 100) / 100;
  }

  /**
   * Get comprehensive status report
   */
  public getStatusReport(): any {
    this.cleanupCallHistory();
    
    return {
      cache: {
        entries: Object.keys(this.cache).length,
        staleness: Object.entries(this.cache).map(([key, value]) => ({
          key,
          age: Date.now() - value.timestamp,
          isStale: Date.now() - value.timestamp > this.CACHE_TTL[key as keyof SyncStatusCache]
        }))
      },
      polling: {
        currentFrequency: this.currentPollingFrequency,
        isActive: this.pollingInterval !== null
      },
      activity: {
        lastUserActivity: this.activityTracker.lastUserActivity,
        lastSyncActivity: this.activityTracker.lastSyncActivity,
        isUserActive: this.activityTracker.isUserActive,
        isAppFocused: this.activityTracker.isAppFocused
      },
      apiMetrics: {
        totalCalls: this.apiCallMetrics.totalCalls,
        callsThisHour: this.apiCallMetrics.callsThisHour,
        cacheHitRate: this.calculateCacheHitRate(),
        recentCalls: this.apiCallMetrics.callHistory.slice(-10)
      }
    };
  }

  /**
   * Reset all metrics and cache
   */
  public reset(): void {
    this.cache = {
      hasUnsyncedChanges: { data: null, timestamp: 0 },
      progress: { data: null, timestamp: 0 },
      queueStats: { data: null, timestamp: 0 },
      metrics: { data: null, timestamp: 0 }
    };
    
    this.requestCoalescing = {};
    
    this.apiCallMetrics = {
      totalCalls: 0,
      callsThisHour: 0,
      lastHourReset: Date.now(),
      callHistory: []
    };
  }

  /**
   * Cleanup and shutdown
   */
  public shutdown(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    this.removeAllListeners();
  }
}

export const syncStatusManager = new SyncStatusManagerService();