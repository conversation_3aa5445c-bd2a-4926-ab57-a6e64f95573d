/**
 * @file product-handlers-thin.ts
 * @description Thin IPC handlers for product operations using extracted service modules
 * 
 * This file implements thin handlers that delegate to specialized product services:
 * - ProductCrudService: Basic CRUD operations (create, read, update, delete)
 * - ProductColorService: Product-color relationship management
 * - ProductQueryService: Query, search, and analytics operations
 * 
 * Each handler focuses on:
 * 1. Organization context validation using getValidatedOrganizationId()
 * 2. Input parameter validation
 * 3. Delegation to appropriate service methods
 * 4. Standardized response formatting
 * 5. Minimal business logic (just coordination and error handling)
 */

import { ipcMain } from 'electron';
import { Product, NewProduct, UpdateProduct, ProductChannels } from '../../shared/types/product.types';
import { ColorEntry } from '../../shared/types/color.types';
// import { getValidatedOrganizationId } from '../middleware/organization-context.middleware';
import {
  registerSecureHandler,
  // registerSystemHandler,
  createSuccessResponse,
  createErrorResponse
} from '../utils/ipc-wrapper';
import { IPCResponse } from '../../shared/types/ipc.types';

// ===== SERVICE INTERFACES =====
// These interfaces define the expected contracts for the extracted services

/**
 * Interface for ProductCrudService - handles basic CRUD operations
 */
interface IProductCrudService {
  getAll(organizationId: string): Promise<Product[]>;
  getAllWithColors(organizationId: string): Promise<Record<string, unknown>[]>;
  getById(productId: string, organizationId: string): Promise<Product | null>;
  getWithColors(productId: string, organizationId: string): Promise<any>;  
  create(productData: NewProduct, organizationId: string, userId?: string): Promise<Product>;
  update(productId: string, updates: UpdateProduct, organizationId: string, userId?: string): Promise<Product | null>;
  delete(productId: string, organizationId: string, userId?: string): Promise<boolean>;
  restore(productId: string, organizationId: string, userId?: string): Promise<boolean>;
  getSoftDeleted(organizationId: string, limit?: number, offset?: number): Promise<Product[]>;
  deleteMultiple(productIds: string[], organizationId: string): Promise<{ success: boolean; deletedIds: string[] }>;
}

/**
 * Interface for ProductColorService - handles product-color relationships
 */
interface IProductColorService {
  addColorToProduct(productId: string, colorId: string, organizationId: string): Promise<boolean>;
  removeColorFromProduct(productId: string, colorId: string, organizationId: string): Promise<{ removed: boolean; productId: string; colorId: string }>;
  getProductColors(productId: string, organizationId: string): Promise<ColorEntry[]>;
  getColorProducts(colorId: string, organizationId: string): Promise<Product[]>;
  updateColorDisplayOrder(productId: string, colorId: string, displayOrder: number, organizationId: string): Promise<boolean>;
  bulkUpdateColorAssociations(productId: string, colorIds: string[], organizationId: string): Promise<{ added: number; removed: number; errors: string[] }>;
}

/**
 * Interface for ProductQueryService - handles queries and searches
 */
interface IProductQueryService {
  search(query: string, organizationId: string, options?: any): Promise<Product[]>;
  searchWithColors(query: string, organizationId: string, options?: any): Promise<any[]>;
  getProductsByColor(colorId: string, organizationId: string): Promise<Product[]>;
  getUsageStatistics(organizationId: string): Promise<{
    totalProducts: number;
    productsWithColors: number;
    averageColorsPerProduct: number;
    colorUsageCounts: Record<string, number>;
  }>;
  findDuplicateProducts(organizationId: string): Promise<{ duplicates: Product[][]; totalDuplicates: number }>;
  deduplicateProducts(organizationId: string): Promise<{ success: boolean; deduplicatedCount: number; errors: string[] }>;
  validateProductData(productData: NewProduct | UpdateProduct): Promise<{ isValid: boolean; errors: string[] }>;
}

// ===== SERVICE DEPENDENCIES =====

/**
 * Service dependencies interface for dependency injection
 */
interface ProductServices {
  productCrudService: IProductCrudService;
  productColorService: IProductColorService;
  productQueryService: IProductQueryService;
}

// ===== THIN HANDLER IMPLEMENTATIONS =====

/**
 * Register thin product IPC handlers with dependency injection
 * 
 * @param services - Injected service dependencies
 */
export function registerThinProductHandlers(services: ProductServices): void {
  console.log('[ThinProductIPC] 🚀 Registering thin product handlers with extracted services...');

  // Validate service dependencies
  if (!services.productCrudService) {
    throw new Error('ProductCrudService is required for thin product handlers');
  }
  if (!services.productColorService) {
    throw new Error('ProductColorService is required for thin product handlers');
  }
  if (!services.productQueryService) {
    throw new Error('ProductQueryService is required for thin product handlers');
  }

  console.log('[ThinProductIPC] ✅ All required services are available');

  // ===== CORE CRUD OPERATIONS =====

  /**
   * Get all products - delegates to ProductCrudService
   */
  registerSecureHandler(
    ProductChannels.GET_ALL,
    async (organizationId: string): Promise<IPCResponse<Product[]>> => {
      try {
        const products = await services.productCrudService.getAll(organizationId);
        return createSuccessResponse(products, `Retrieved ${products.length} products`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in GET_ALL:', error);
        return createErrorResponse(error, 'Failed to retrieve products. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to retrieve products. Please try again.'
    }
  );

  /**
   * Get all products with colors - delegates to ProductCrudService
   */
  registerSecureHandler(
    ProductChannels.GET_ALL_WITH_COLORS,
    async (organizationId: string): Promise<IPCResponse<Record<string, unknown>[]>> => {
      try {
        const productsWithColors = await services.productCrudService.getAllWithColors(organizationId);
        return createSuccessResponse(productsWithColors, `Retrieved ${productsWithColors.length} products with color data`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in GET_ALL_WITH_COLORS:', error);
        return createErrorResponse(error, 'Failed to retrieve products with colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to retrieve products with colors. Please try again.'
    }
  );

  /**
   * Get product by ID - delegates to ProductCrudService
   */
  registerSecureHandler(
    ProductChannels.GET_BY_ID,
    async (organizationId: string, productId: string): Promise<IPCResponse<Product | null>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        const product = await services.productCrudService.getById(productId, organizationId);
        return createSuccessResponse(product, product ? 'Product retrieved successfully' : 'Product not found');
      } catch (error) {
        console.error('[ThinProductIPC] Error in GET_BY_ID:', error);
        return createErrorResponse(error, 'Failed to retrieve product. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to retrieve product. Please try again.'
    }
  );

  /**
   * Get product with colors - delegates to ProductCrudService
   */
  registerSecureHandler(
    'product:getWithColors',
    async (organizationId: string, productId: string): Promise<IPCResponse<any>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        const productWithColors = await services.productCrudService.getWithColors(productId, organizationId);
        return createSuccessResponse(productWithColors, productWithColors ? 'Product with colors retrieved successfully' : 'Product not found');
      } catch (error) {
        console.error('[ThinProductIPC] Error in getWithColors:', error);
        return createErrorResponse(error, 'Failed to retrieve product with colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to retrieve product with colors. Please try again.'
    }
  );

  /**
   * Add new product - delegates to ProductCrudService with validation
   */
  registerSecureHandler(
    ProductChannels.ADD,
    async (organizationId: string, productData: NewProduct | { name: string; metadata?: any }): Promise<IPCResponse<Product>> => {
      try {
        if (!productData || typeof productData !== 'object') {
          return createErrorResponse('Invalid product data provided', 'Please provide valid product information.');
        }

        // Normalize product data to NewProduct format
        let normalizedData: NewProduct;
        if ('name' in productData && typeof productData.name === 'string') {
          normalizedData = {
            name: productData.name,
            description: productData.metadata?.description || undefined,
            organizationId
          };
        } else {
          normalizedData = productData as NewProduct;
        }

        // Validate product data using ProductQueryService
        const validation = await services.productQueryService.validateProductData(normalizedData);
        if (!validation.isValid) {
          return createErrorResponse(
            `Validation failed: ${validation.errors.join(', ')}`,
            'Please check your product data and try again.'
          );
        }

        const newProduct = await services.productCrudService.create(normalizedData, organizationId);
        return createSuccessResponse(newProduct, 'Product created successfully');
      } catch (error) {
        console.error('[ThinProductIPC] Error in ADD:', error);
        return createErrorResponse(error, 'Failed to create product. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to create product. Please try again.'
    }
  );

  /**
   * Create product (alternative channel) - delegates to ProductCrudService
   */
  registerSecureHandler(
    'product:create',
    async (organizationId: string, name: string, metadata?: any): Promise<IPCResponse<Product>> => {
      try {
        if (!name || typeof name !== 'string' || name.trim().length === 0) {
          return createErrorResponse('Product name is required', 'Please provide a valid product name.');
        }

        const productData: NewProduct = {
          name: name.trim(),
          description: metadata?.description || undefined,
          organizationId
        };

        const validation = await services.productQueryService.validateProductData(productData);
        if (!validation.isValid) {
          return createErrorResponse(
            `Validation failed: ${validation.errors.join(', ')}`,
            'Please check your product data and try again.'
          );
        }

        const newProduct = await services.productCrudService.create(productData, organizationId);
        return createSuccessResponse(newProduct, 'Product created successfully');
      } catch (error) {
        console.error('[ThinProductIPC] Error in create:', error);
        return createErrorResponse(error, 'Failed to create product. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to create product. Please try again.'
    }
  );

  /**
   * Update product - delegates to ProductCrudService with validation
   */
  registerSecureHandler(
    ProductChannels.UPDATE,
    async (organizationId: string, productId: string, updates: UpdateProduct): Promise<IPCResponse<Product | null>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        if (!updates || typeof updates !== 'object') {
          return createErrorResponse('Invalid update data provided', 'Please provide valid update information.');
        }

        // Validate update data using ProductQueryService
        const validation = await services.productQueryService.validateProductData(updates);
        if (!validation.isValid) {
          return createErrorResponse(
            `Validation failed: ${validation.errors.join(', ')}`,
            'Please check your update data and try again.'
          );
        }

        const updatedProduct = await services.productCrudService.update(productId, updates, organizationId);
        return createSuccessResponse(updatedProduct, updatedProduct ? 'Product updated successfully' : 'Product update failed or product not found');
      } catch (error) {
        console.error('[ThinProductIPC] Error in UPDATE:', error);
        return createErrorResponse(error, 'Failed to update product. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to update product. Please try again.'
    }
  );

  /**
   * Delete product - delegates to ProductCrudService
   */
  registerSecureHandler(
    ProductChannels.DELETE,
    async (organizationId: string, productId: string): Promise<IPCResponse<boolean>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        const success = await services.productCrudService.delete(productId, organizationId);
        return createSuccessResponse(success, success ? 'Product deleted successfully' : 'Product deletion failed or product not found');
      } catch (error) {
        console.error('[ThinProductIPC] Error in DELETE:', error);
        return createErrorResponse(error, 'Failed to delete product. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to delete product. Please try again.'
    }
  );

  /**
   * Delete multiple products - delegates to ProductCrudService
   */
  registerSecureHandler(
    ProductChannels.DELETE_MULTIPLE_PRODUCTS,
    async (organizationId: string, productIds: string[]): Promise<IPCResponse<{ success: boolean; deletedIds: string[] }>> => {
      try {
        if (!Array.isArray(productIds) || productIds.length === 0) {
          return createErrorResponse('Invalid product IDs provided', 'Please provide a valid array of product IDs.');
        }

        const result = await services.productCrudService.deleteMultiple(productIds, organizationId);
        return createSuccessResponse(result, `Deleted ${result.deletedIds.length} of ${productIds.length} products`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in DELETE_MULTIPLE_PRODUCTS:', error);
        return createErrorResponse(error, 'Failed to delete products. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to delete products. Please try again.'
    }
  );

  // ===== PRODUCT-COLOR RELATIONSHIP OPERATIONS =====

  /**
   * Add color to product - delegates to ProductColorService
   */
  registerSecureHandler(
    ProductChannels.ADD_COLOR,
    async (organizationId: string, productId: string, colorId: string): Promise<IPCResponse<boolean>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        if (!colorId || typeof colorId !== 'string') {
          return createErrorResponse('Invalid color ID provided', 'Please provide a valid color ID.');
        }

        const success = await services.productColorService.addColorToProduct(productId, colorId, organizationId);
        return createSuccessResponse(success, success ? 'Color added to product successfully' : 'Failed to add color to product');
      } catch (error) {
        console.error('[ThinProductIPC] Error in ADD_COLOR:', error);
        return createErrorResponse(error, 'Failed to add color to product. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to add color to product. Please try again.'
    }
  );

  /**
   * Remove color from product - delegates to ProductColorService
   */
  registerSecureHandler(
    ProductChannels.REMOVE_COLOR,
    async (organizationId: string, productId: string, colorId: string): Promise<IPCResponse<{ removed: boolean; productId: string; colorId: string }>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        if (!colorId || typeof colorId !== 'string') {
          return createErrorResponse('Invalid color ID provided', 'Please provide a valid color ID.');
        }

        const result = await services.productColorService.removeColorFromProduct(productId, colorId, organizationId);
        return createSuccessResponse(result, result.removed ? 'Color removed from product successfully' : 'Failed to remove color from product');
      } catch (error) {
        console.error('[ThinProductIPC] Error in REMOVE_COLOR:', error);
        return createErrorResponse(error, 'Failed to remove color from product. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to remove color from product. Please try again.'
    }
  );

  /**
   * Get colors for product - delegates to ProductColorService
   */
  registerSecureHandler(
    ProductChannels.GET_COLORS,
    async (organizationId: string, productId: string): Promise<IPCResponse<ColorEntry[]>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        const colors = await services.productColorService.getProductColors(productId, organizationId);
        return createSuccessResponse(colors, `Retrieved ${colors.length} colors for product`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in GET_COLORS:', error);
        return createErrorResponse(error, 'Failed to retrieve product colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to retrieve product colors. Please try again.'
    }
  );

  // ===== QUERY AND SEARCH OPERATIONS =====

  /**
   * Search products - delegates to ProductQueryService
   */
  registerSecureHandler(
    'product:search',
    async (organizationId: string, query: string, options?: any): Promise<IPCResponse<Product[]>> => {
      try {
        if (!query || typeof query !== 'string' || query.trim().length === 0) {
          return createErrorResponse('Search query is required', 'Please provide a valid search query.');
        }

        const results = await services.productQueryService.search(query.trim(), organizationId, options);
        return createSuccessResponse(results, `Found ${results.length} products matching "${query}"`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in search:', error);
        return createErrorResponse(error, 'Failed to search products. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to search products. Please try again.'
    }
  );

  /**
   * Search products with colors - delegates to ProductQueryService
   */
  registerSecureHandler(
    'product:searchWithColors',
    async (organizationId: string, query: string, options?: any): Promise<IPCResponse<any[]>> => {
      try {
        if (!query || typeof query !== 'string' || query.trim().length === 0) {
          return createErrorResponse('Search query is required', 'Please provide a valid search query.');
        }

        const results = await services.productQueryService.searchWithColors(query.trim(), organizationId, options);
        return createSuccessResponse(results, `Found ${results.length} products with colors matching "${query}"`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in searchWithColors:', error);
        return createErrorResponse(error, 'Failed to search products with colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to search products with colors. Please try again.'
    }
  );

  /**
   * Get usage statistics - delegates to ProductQueryService
   */
  registerSecureHandler(
    'product:getUsageStatistics',
    async (organizationId: string): Promise<IPCResponse<any>> => {
      try {
        const stats = await services.productQueryService.getUsageStatistics(organizationId);
        return createSuccessResponse(stats, 'Usage statistics retrieved successfully');
      } catch (error) {
        console.error('[ThinProductIPC] Error in getUsageStatistics:', error);
        return createErrorResponse(error, 'Failed to retrieve usage statistics. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to retrieve usage statistics. Please try again.'
    }
  );

  /**
   * Deduplicate products - delegates to ProductQueryService
   */
  registerSecureHandler(
    'product:deduplicate',
    async (organizationId: string): Promise<IPCResponse<{ success: boolean; deduplicatedCount: number; errors: string[] }>> => {
      try {
        const result = await services.productQueryService.deduplicateProducts(organizationId);
        return createSuccessResponse(
          result,
          result.success 
            ? `Successfully removed ${result.deduplicatedCount} duplicate products`
            : 'Product deduplication completed with issues'
        );
      } catch (error) {
        console.error('[ThinProductIPC] Error in deduplicate:', error);
        return createErrorResponse(error, 'Failed to deduplicate products. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to deduplicate products. Please try again.'
    }
  );

  // ===== SOFT DELETE / RECOVERY OPERATIONS =====

  /**
   * Get soft deleted products - delegates to ProductCrudService
   */
  registerSecureHandler(
    'product:getSoftDeleted',
    async (organizationId: string, limit?: number, offset?: number): Promise<IPCResponse<Product[]>> => {
      try {
        const products = await services.productCrudService.getSoftDeleted(organizationId, limit, offset);
        return createSuccessResponse(products, `Retrieved ${products.length} deleted products`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in getSoftDeleted:', error);
        return createErrorResponse(error, 'Failed to retrieve deleted products. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to retrieve deleted products. Please try again.'
    }
  );

  /**
   * Restore soft deleted product - delegates to ProductCrudService
   */
  registerSecureHandler(
    'product:restore',
    async (organizationId: string, productId: string): Promise<IPCResponse<boolean>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        const success = await services.productCrudService.restore(productId, organizationId);
        return createSuccessResponse(success, success ? 'Product restored successfully' : 'Product restoration failed or product not found');
      } catch (error) {
        console.error('[ThinProductIPC] Error in restore:', error);
        return createErrorResponse(error, 'Failed to restore product. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to restore product. Please try again.'
    }
  );

  console.log('[ThinProductIPC] ✅ All thin product handlers registered successfully');
}

// ===== EXTENDED HANDLERS FOR ADDITIONAL OPERATIONS =====

/**
 * Register extended thin product handlers for advanced operations
 * These handlers provide additional functionality beyond the basic product channels
 * 
 * @param services - Injected service dependencies
 */
export function registerExtendedThinProductHandlers(services: ProductServices): void {
  console.log('[ThinProductIPC] 🚀 Registering extended thin product handlers...');

  /**
   * Find duplicate products - delegates to ProductQueryService
   */
  registerSecureHandler(
    'product:findDuplicates',
    async (organizationId: string): Promise<IPCResponse<{ duplicates: Product[][]; totalDuplicates: number }>> => {
      try {
        const result = await services.productQueryService.findDuplicateProducts(organizationId);
        return createSuccessResponse(result, `Found ${result.totalDuplicates} duplicate products in ${result.duplicates.length} groups`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in findDuplicates:', error);
        return createErrorResponse(error, 'Failed to find duplicate products. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to find duplicate products. Please try again.'
    }
  );

  /**
   * Bulk update color associations - delegates to ProductColorService
   */
  registerSecureHandler(
    'product:bulkUpdateColorAssociations',
    async (organizationId: string, productId: string, colorIds: string[]): Promise<IPCResponse<{ added: number; removed: number; errors: string[] }>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        if (!Array.isArray(colorIds)) {
          return createErrorResponse('Invalid color IDs provided', 'Please provide a valid array of color IDs.');
        }

        const result = await services.productColorService.bulkUpdateColorAssociations(productId, colorIds, organizationId);
        return createSuccessResponse(
          result,
          `Bulk update completed: ${result.added} added, ${result.removed} removed${result.errors.length > 0 ? `, ${result.errors.length} errors` : ''}`
        );
      } catch (error) {
        console.error('[ThinProductIPC] Error in bulkUpdateColorAssociations:', error);
        return createErrorResponse(error, 'Failed to bulk update color associations. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to bulk update color associations. Please try again.'
    }
  );

  /**
   * Update color display order - delegates to ProductColorService
   */
  registerSecureHandler(
    'product:updateColorDisplayOrder',
    async (organizationId: string, productId: string, colorId: string, displayOrder: number): Promise<IPCResponse<boolean>> => {
      try {
        if (!productId || typeof productId !== 'string') {
          return createErrorResponse('Invalid product ID provided', 'Please provide a valid product ID.');
        }

        if (!colorId || typeof colorId !== 'string') {
          return createErrorResponse('Invalid color ID provided', 'Please provide a valid color ID.');
        }

        if (typeof displayOrder !== 'number' || displayOrder < 0) {
          return createErrorResponse('Invalid display order provided', 'Please provide a valid display order (non-negative number).');
        }

        const success = await services.productColorService.updateColorDisplayOrder(productId, colorId, displayOrder, organizationId);
        return createSuccessResponse(success, success ? 'Color display order updated successfully' : 'Failed to update color display order');
      } catch (error) {
        console.error('[ThinProductIPC] Error in updateColorDisplayOrder:', error);
        return createErrorResponse(error, 'Failed to update color display order. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to update color display order. Please try again.'
    }
  );

  /**
   * Get products by color - delegates to ProductQueryService
   */
  registerSecureHandler(
    'product:getProductsByColor',
    async (organizationId: string, colorId: string): Promise<IPCResponse<Product[]>> => {
      try {
        if (!colorId || typeof colorId !== 'string') {
          return createErrorResponse('Invalid color ID provided', 'Please provide a valid color ID.');
        }

        const products = await services.productQueryService.getProductsByColor(colorId, organizationId);
        return createSuccessResponse(products, `Found ${products.length} products using this color`);
      } catch (error) {
        console.error('[ThinProductIPC] Error in getProductsByColor:', error);
        return createErrorResponse(error, 'Failed to retrieve products by color. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinProductIPC',
      customErrorMessage: 'Failed to retrieve products by color. Please try again.'
    }
  );

  console.log('[ThinProductIPC] ✅ Extended thin product handlers registered successfully');
}

/**
 * Unregister all thin product handlers
 */
export function unregisterThinProductHandlers(): void {
  const allChannels = [
    // Core channels
    ...Object.values(ProductChannels),
    // Extended channels
    'product:create',
    'product:getWithColors',
    'product:search',
    'product:searchWithColors',
    'product:getUsageStatistics',
    'product:deduplicate',
    'product:getSoftDeleted',
    'product:restore',
    'product:findDuplicates',
    'product:bulkUpdateColorAssociations',
    'product:updateColorDisplayOrder',
    'product:getProductsByColor'
  ];

  allChannels.forEach(channel => {
    try {
      ipcMain.removeAllListeners(channel);
    } catch (error) {
      console.warn(`[ThinProductIPC] Failed to unregister handler for ${channel}:`, error);
    }
  });

  console.log('[ThinProductIPC] 🧹 All thin product handlers unregistered');
}