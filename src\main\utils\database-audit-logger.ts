/**
 * Database Audit Logger
 * Provides comprehensive audit logging for database operations
 */

export interface DatabaseAuditEvent {
  id: string;
  timestamp: string;
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'TRANSACTION';
  table: string;
  organizationId?: string;
  userId?: string;
  recordId?: string;
  sql: string;
  parameters?: any[];
  rowsAffected?: number;
  executionTime: number;
  success: boolean;
  error?: string;
  context?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface AuditLoggerConfig {
  enabled: boolean;
  logLevel: 'minimal' | 'standard' | 'detailed';
  includeSensitiveData: boolean;
  maxLogRetention: number; // days
  flushInterval: number; // milliseconds
}

/**
 * Database audit logger for security monitoring
 */
export class DatabaseAuditLogger {
  private static instance: DatabaseAuditLogger;
  private auditLog: DatabaseAuditEvent[] = [];
  private config: AuditLoggerConfig;
  private flushTimer?: NodeJS.Timeout;

  private constructor(config: AuditLoggerConfig) {
    this.config = config;
    this.startFlushTimer();
  }

  static getInstance(config?: AuditLoggerConfig): DatabaseAuditLogger {
    if (!DatabaseAuditLogger.instance) {
      const defaultConfig: AuditLoggerConfig = {
        enabled: true,
        logLevel: 'standard',
        includeSensitiveData: false,
        maxLogRetention: 90,
        flushInterval: 30000, // 30 seconds
      };
      DatabaseAuditLogger.instance = new DatabaseAuditLogger(
        config || defaultConfig
      );
    }
    return DatabaseAuditLogger.instance;
  }

  /**
   * Log a database operation
   */
  logOperation(event: Omit<DatabaseAuditEvent, 'id' | 'timestamp'>): void {
    if (!this.config.enabled) {
      return;
    }

    const auditEvent: DatabaseAuditEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      ...event,
    };

    // Sanitize sensitive data if configured
    if (!this.config.includeSensitiveData) {
      auditEvent.sql = this.sanitizeSQL(auditEvent.sql);
      auditEvent.parameters = this.sanitizeParameters(auditEvent.parameters);
    }

    this.auditLog.push(auditEvent);

    // Immediate logging for critical operations
    if (this.isCriticalOperation(event)) {
      this.flushLogs();
    }
  }

  /**
   * Log SELECT operation
   */
  logSelect(
    sql: string,
    table: string,
    parameters?: any[],
    rowsReturned?: number,
    executionTime?: number,
    context?: string,
    organizationId?: string,
    userId?: string
  ): void {
    this.logOperation({
      operation: 'SELECT',
      table,
      sql,
      parameters,
      rowsAffected: rowsReturned,
      executionTime: executionTime || 0,
      success: true,
      context,
      organizationId,
      userId,
    });
  }

  /**
   * Log INSERT operation
   */
  logInsert(
    sql: string,
    table: string,
    recordId: string,
    parameters?: any[],
    executionTime?: number,
    context?: string,
    organizationId?: string,
    userId?: string
  ): void {
    this.logOperation({
      operation: 'INSERT',
      table,
      recordId,
      sql,
      parameters,
      rowsAffected: 1,
      executionTime: executionTime || 0,
      success: true,
      context,
      organizationId,
      userId,
    });
  }

  /**
   * Log UPDATE operation
   */
  logUpdate(
    sql: string,
    table: string,
    recordId: string,
    rowsAffected: number,
    parameters?: any[],
    executionTime?: number,
    context?: string,
    organizationId?: string,
    userId?: string
  ): void {
    this.logOperation({
      operation: 'UPDATE',
      table,
      recordId,
      sql,
      parameters,
      rowsAffected,
      executionTime: executionTime || 0,
      success: true,
      context,
      organizationId,
      userId,
    });
  }

  /**
   * Log DELETE operation
   */
  logDelete(
    sql: string,
    table: string,
    recordId: string,
    rowsAffected: number,
    parameters?: any[],
    executionTime?: number,
    context?: string,
    organizationId?: string,
    userId?: string
  ): void {
    this.logOperation({
      operation: 'DELETE',
      table,
      recordId,
      sql,
      parameters,
      rowsAffected,
      executionTime: executionTime || 0,
      success: true,
      context,
      organizationId,
      userId,
    });
  }

  /**
   * Log failed operation
   */
  logError(
    operation: DatabaseAuditEvent['operation'],
    sql: string,
    table: string,
    error: string,
    parameters?: any[],
    executionTime?: number,
    context?: string,
    organizationId?: string,
    userId?: string
  ): void {
    this.logOperation({
      operation,
      table,
      sql,
      parameters,
      executionTime: executionTime || 0,
      success: false,
      error,
      context,
      organizationId,
      userId,
    });
  }

  /**
   * Get audit logs with filtering
   */
  getAuditLogs(filter?: {
    organizationId?: string;
    userId?: string;
    table?: string;
    operation?: DatabaseAuditEvent['operation'];
    startDate?: Date;
    endDate?: Date;
    onlyErrors?: boolean;
  }): DatabaseAuditEvent[] {
    let filteredLogs = [...this.auditLog];

    if (filter) {
      if (filter.organizationId) {
        filteredLogs = filteredLogs.filter(
          log => log.organizationId === filter.organizationId
        );
      }
      if (filter.userId) {
        filteredLogs = filteredLogs.filter(log => log.userId === filter.userId);
      }
      if (filter.table) {
        filteredLogs = filteredLogs.filter(log => log.table === filter.table);
      }
      if (filter.operation) {
        filteredLogs = filteredLogs.filter(
          log => log.operation === filter.operation
        );
      }
      if (filter.startDate) {
        filteredLogs = filteredLogs.filter(
          log => new Date(log.timestamp) >= filter.startDate!
        );
      }
      if (filter.endDate) {
        filteredLogs = filteredLogs.filter(
          log => new Date(log.timestamp) <= filter.endDate!
        );
      }
      if (filter.onlyErrors) {
        filteredLogs = filteredLogs.filter(log => !log.success);
      }
    }

    return filteredLogs;
  }

  /**
   * Get security analytics
   */
  getSecurityAnalytics(): {
    totalOperations: number;
    failedOperations: number;
    failureRate: number;
    operationsByType: Record<DatabaseAuditEvent['operation'], number>;
    suspiciousActivity: DatabaseAuditEvent[];
    topUsers: Array<{ userId: string; operationCount: number }>;
    topTables: Array<{ table: string; operationCount: number }>;
  } {
    const logs = this.auditLog;
    const failedLogs = logs.filter(log => !log.success);

    // Operation counts by type
    const operationsByType = logs.reduce(
      (acc, log) => {
        acc[log.operation] = (acc[log.operation] || 0) + 1;
        return acc;
      },
      {} as Record<DatabaseAuditEvent['operation'], number>
    );

    // Suspicious activity detection
    const suspiciousActivity = this.detectSuspiciousActivity(logs);

    // Top users by operation count
    const userOperations = logs.reduce(
      (acc, log) => {
        if (log.userId) {
          acc[log.userId] = (acc[log.userId] || 0) + 1;
        }
        return acc;
      },
      {} as Record<string, number>
    );

    const topUsers = Object.entries(userOperations)
      .map(([userId, count]) => ({ userId, operationCount: count }))
      .sort((a, b) => b.operationCount - a.operationCount)
      .slice(0, 10);

    // Top tables by operation count
    const tableOperations = logs.reduce(
      (acc, log) => {
        acc[log.table] = (acc[log.table] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const topTables = Object.entries(tableOperations)
      .map(([table, count]) => ({ table, operationCount: count }))
      .sort((a, b) => b.operationCount - a.operationCount)
      .slice(0, 10);

    return {
      totalOperations: logs.length,
      failedOperations: failedLogs.length,
      failureRate:
        logs.length > 0 ? (failedLogs.length / logs.length) * 100 : 0,
      operationsByType,
      suspiciousActivity,
      topUsers,
      topTables,
    };
  }

  /**
   * Clear old audit logs
   */
  cleanupOldLogs(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.maxLogRetention);

    this.auditLog = this.auditLog.filter(
      log => new Date(log.timestamp) > cutoffDate
    );
    console.log(
      `[DatabaseAuditLogger] Cleaned up logs older than ${this.config.maxLogRetention} days`
    );
  }

  /**
   * Flush logs to persistent storage (implement as needed)
   */
  private flushLogs(): void {
    if (this.auditLog.length === 0) {
      return;
    }

    // For now, just log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `[DatabaseAuditLogger] Flushing ${this.auditLog.length} audit events`
      );
    }

    // TODO: Implement persistent storage (file, database, external service)
    // Example implementations:
    // - Write to a dedicated audit log file
    // - Send to external audit service
    // - Store in separate audit database
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushLogs();
      this.cleanupOldLogs();
    }, this.config.flushInterval);
  }

  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sanitizeSQL(sql: string): string {
    // Remove potential sensitive patterns
    return sql
      .replace(/password\s*=\s*['"][^'"]*['"]/gi, "password='***'")
      .replace(/token\s*=\s*['"][^'"]*['"]/gi, "token='***'")
      .replace(/api_?key\s*=\s*['"][^'"]*['"]/gi, "api_key='***'");
  }

  private sanitizeParameters(parameters?: any[]): any[] | undefined {
    if (!parameters) {
      return parameters;
    }

    return parameters.map(param => {
      if (typeof param === 'string' && param.length > 50) {
        return `${param.substring(0, 50)  }...`;
      }
      return param;
    });
  }

  private isCriticalOperation(
    event: Omit<DatabaseAuditEvent, 'id' | 'timestamp'>
  ): boolean {
    // Critical operations that should be logged immediately
    return (
      event.operation === 'DELETE' ||
      event.table === 'organizations' ||
      event.table === 'users' ||
      !event.success ||
      (event.organizationId === undefined && event.operation !== 'SELECT')
    );
  }

  private detectSuspiciousActivity(
    logs: DatabaseAuditEvent[]
  ): DatabaseAuditEvent[] {
    const suspicious: DatabaseAuditEvent[] = [];
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Recent failed operations
    const recentFailures = logs.filter(
      log => !log.success && new Date(log.timestamp) > oneHourAgo
    );

    // High volume operations from single user
    const userOperationCounts = logs
      .filter(log => new Date(log.timestamp) > oneHourAgo)
      .reduce(
        (acc, log) => {
          if (log.userId) {
            acc[log.userId] = (acc[log.userId] || 0) + 1;
          }
          return acc;
        },
        {} as Record<string, number>
      );

    const highVolumeUsers = Object.entries(userOperationCounts)
      .filter(([_, count]) => count > 100) // More than 100 ops per hour
      .map(([userId]) => userId);

    const highVolumeOps = logs.filter(
      log =>
        log.userId &&
        highVolumeUsers.includes(log.userId) &&
        new Date(log.timestamp) > oneHourAgo
    );

    // Cross-organization access attempts
    const crossOrgAccess = logs.filter(
      log =>
        log.operation !== 'SELECT' &&
        !log.organizationId &&
        new Date(log.timestamp) > oneHourAgo
    );

    suspicious.push(...recentFailures, ...highVolumeOps, ...crossOrgAccess);

    return suspicious;
  }

  /**
   * Shutdown the audit logger
   */
  shutdown(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flushLogs();
  }
}

/**
 * Audit logger middleware for database operations
 */
export function auditDatabase(auditLogger: DatabaseAuditLogger) {
  return {
    /**
     * Wrap a database operation with audit logging
     */
    wrapOperation<T>(
      operation: () => T,
      context: {
        operationType: DatabaseAuditEvent['operation'];
        table: string;
        sql: string;
        parameters?: any[];
        recordId?: string;
        organizationId?: string;
        userId?: string;
        context?: string;
      }
    ): T {
      const startTime = Date.now();

      try {
        const result = operation();
        const executionTime = Date.now() - startTime;

        // Determine rows affected based on operation type and result
        let rowsAffected = 0;
        if (context.operationType === 'SELECT' && Array.isArray(result)) {
          rowsAffected = result.length;
        } else if (
          typeof result === 'object' &&
          result !== null &&
          'changes' in result
        ) {
          rowsAffected = (result as any).changes;
        }

        auditLogger.logOperation({
          operation: context.operationType,
          table: context.table,
          sql: context.sql,
          parameters: context.parameters,
          recordId: context.recordId,
          rowsAffected,
          executionTime,
          success: true,
          organizationId: context.organizationId,
          userId: context.userId,
          context: context.context,
        });

        return result;
      } catch (error) {
        const executionTime = Date.now() - startTime;

        auditLogger.logError(
          context.operationType,
          context.sql,
          context.table,
          error instanceof Error ? error.message : String(error),
          context.parameters,
          executionTime,
          context.context,
          context.organizationId,
          context.userId
        );

        throw error;
      }
    },
  };
}

// Export singleton instance
export const databaseAuditLogger = DatabaseAuditLogger.getInstance();
