/**
 * @file create-web-invitation.ts
 * @description Create a web-based invitation page for better email compatibility
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs/promises';

export async function createWebInvitationPage(
  token: string,
  organizationName: string
): Promise<string> {
  const webInvitationUrl = `https://chromasync.app/download.html?invite=${token}`;

  // For development/testing, you can also use a local server
  // const webInvitationUrl = `http://localhost:3000/invite/${token}`;

  // Create a simple HTML page that will redirect to the app
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Accept ChromaSync Invitation</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .container {
      background: white;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      text-align: center;
      max-width: 400px;
    }
    h1 {
      color: #333;
      margin-bottom: 20px;
    }
    .button {
      display: inline-block;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      padding: 12px 30px;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      margin-top: 20px;
    }
    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    .instructions {
      margin-top: 30px;
      padding-top: 30px;
      border-top: 1px solid #eee;
      color: #666;
      font-size: 14px;
    }
    code {
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: monospace;
      word-break: break-all;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎨 Join ${organizationName}</h1>
    <p>You've been invited to join <strong>${organizationName}</strong> on ChromaSync.</p>
    
    <a href="chromasync://invite/${token}" class="button">
      Open in ChromaSync
    </a>
    
    <div class="instructions">
      <p><strong>Don't have ChromaSync installed?</strong></p>
      <p>Download it from <a href="https://chromasync.app/download.html">chromasync.app</a></p>
      
      <p style="margin-top: 20px;"><strong>Having trouble?</strong></p>
      <p>Copy this invitation code:</p>
      <code>${token}</code>
      <p>Then open ChromaSync and go to Settings → Team → Join Organization</p>
    </div>
  </div>
  
  <script>
    // Try to detect if ChromaSync is installed and redirect
    setTimeout(() => {
      window.location.href = 'chromasync://invite/${token}';
    }, 1000);
  </script>
</body>
</html>`;

  // Save the HTML file temporarily
  const tempDir = app.getPath('temp');
  const htmlPath = path.join(tempDir, `chromasync-invite-${token}.html`);
  await fs.writeFile(htmlPath, htmlContent);

  return webInvitationUrl;
}
