#!/usr/bin/env node

/**
 * @file test-organization-context.js
 * @description Test script to verify organization context switching works properly
 * Run with: node scripts/test-organization-context.js
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 Testing Organization Context Switching...\n');

async function testOrganizationContext() {
  try {
    // Start the app in development mode
    console.log('1. Starting ChromaSync in development mode...');
    const appProcess = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, '..'),
      stdio: 'pipe'
    });

    let output = '';
    appProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    appProcess.stderr.on('data', (data) => {
      output += data.toString();
    });

    // Wait for app to start
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Look for organization context switching logs
    console.log('\n2. Checking for organization context switching patterns...');
    
    const patterns = [
      '🔄 Starting organization switch',
      '📡 Setting backend organization context',
      '✅ Backend organization context verified',
      '🧹 Clearing data stores',
      '📊 Loading data for new organization',
      '🎉 Organization switch completed',
      'Organization context validation:',
      'CALLING getColorUsageCounts',
      'Usage counts updated for'
    ];

    patterns.forEach(pattern => {
      const found = output.includes(pattern);
      console.log(`${found ? '✅' : '❌'} ${pattern}: ${found ? 'FOUND' : 'NOT FOUND'}`);
    });

    // Look for the specific organization mismatch issue
    console.log('\n3. Checking for organization mismatch indicators...');
    
    const mismatchPatterns = [
      'Backend/frontend organization mismatch',
      'Colors loaded for org:',
      'organization_id mismatch',
      'Usage counts result: 0'
    ];

    mismatchPatterns.forEach(pattern => {
      const found = output.includes(pattern);
      console.log(`${found ? '⚠️' : '✅'} ${pattern}: ${found ? 'DETECTED' : 'NOT DETECTED'}`);
    });

    // Terminate the app
    console.log('\n4. Terminating test app...');
    appProcess.kill('SIGTERM');

    console.log('\n🎯 Test Results Summary:');
    console.log('- If you see "FOUND" for organization switch patterns, the fix is working');
    console.log('- If you see "NOT DETECTED" for mismatch patterns, the issue is resolved');
    console.log('- Check the app logs for detailed organization context debugging');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Helper function to check if processes are running
function checkRunningProcesses() {
  console.log('\n🔍 Checking for running ChromaSync processes...');
  
  const { execSync } = require('child_process');
  try {
    const processes = execSync('ps aux | grep -i chromasync | grep -v grep', { encoding: 'utf8' });
    if (processes.trim()) {
      console.log('⚠️  Warning: ChromaSync processes are running:');
      console.log(processes);
      console.log('Please close ChromaSync before running this test.');
      return false;
    } else {
      console.log('✅ No running ChromaSync processes detected');
      return true;
    }
  } catch (error) {
    console.log('✅ No running ChromaSync processes detected');
    return true;
  }
}

// Main execution
if (require.main === module) {
  console.log('ChromaSync Organization Context Test');
  console.log('=====================================\n');
  
  if (checkRunningProcesses()) {
    testOrganizationContext().then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    }).catch(error => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
  } else {
    console.log('\n❌ Test aborted - close running ChromaSync instances first');
    process.exit(1);
  }
}
