/**
 * @file AlertModal.tsx
 * @description Alert modal component to replace window.alert
 */

import React from 'react';
import { Modal } from './Modal';
import { CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';

interface AlertModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  variant?: 'success' | 'error' | 'warning' | 'info';
  buttonText?: string;
}

export const AlertModal: React.FC<AlertModalProps> = ({
  isOpen,
  onClose,
  title,
  message,
  variant = 'info',
  buttonText = 'OK',
}) => {
  const getIcon = () => {
    switch (variant) {
      case 'success':
        return <CheckCircle className='w-6 h-6 text-green-600' />;
      case 'error':
        return <AlertCircle className='w-6 h-6 text-red-600' />;
      case 'warning':
        return <AlertTriangle className='w-6 h-6 text-yellow-600' />;
      case 'info':
      default:
        return <Info className='w-6 h-6 text-blue-600' />;
    }
  };

  const getButtonClass = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-600 hover:bg-green-700 text-white';
      case 'error':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'warning':
        return 'bg-yellow-600 hover:bg-yellow-700 text-white';
      case 'info':
      default:
        return 'bg-blue-600 hover:bg-blue-700 text-white';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size='sm'
      showCloseButton={false}
    >
      <div className='flex items-start space-x-4'>
        <div className='flex-shrink-0'>{getIcon()}</div>
        <div className='flex-1'>
          <p className='text-gray-700 dark:text-gray-300 mb-6'>{message}</p>
          <div className='flex justify-end'>
            <button
              onClick={onClose}
              className={`px-4 py-2 rounded-md transition-colors ${getButtonClass()}`}
            >
              {buttonText}
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};
