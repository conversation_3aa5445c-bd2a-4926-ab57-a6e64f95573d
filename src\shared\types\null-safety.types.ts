/**
 * @file null-safety.types.ts
 * @description Advanced null safety utilities with sophisticated conditional types and type guards
 * Implements cutting-edge TypeScript patterns for bulletproof null safety
 */

import type { Brand } from './advanced-utilities.types';

// ===== BRANDED NULL SAFETY TYPES =====

export type NonNullString = Brand<string, 'NonNullString'>;
export type NonNullNumber = Brand<number, 'NonNullNumber'>;
export type NonNullObject<T extends object> = Brand<T, 'NonNullObject'>;

// ===== ADVANCED NULL SAFETY CONDITIONALS =====

/**
 * Remove null and undefined from a type
 */
export type NonNullable<T> = T extends null | undefined ? never : T;

/**
 * Make a type nullable
 */
export type Nullable<T> = T | null;

/**
 * Make a type optional (undefined)
 */
export type Optional<T> = T | undefined;

/**
 * Make a type maybe (null or undefined)
 */
export type Maybe<T> = T | null | undefined;

/**
 * Extract non-null properties from an object
 */
export type NonNullableKeys<T> = {
  [K in keyof T]: null extends T[K] ? never : undefined extends T[K] ? never : K;
}[keyof T];

/**
 * Extract nullable properties from an object
 */
export type NullableKeys<T> = {
  [K in keyof T]: null extends T[K] ? K : undefined extends T[K] ? K : never;
}[keyof T];

/**
 * Split an object into required and optional properties
 */
export type RequiredProperties<T> = Pick<T, NonNullableKeys<T>>;
export type OptionalProperties<T> = Pick<T, NullableKeys<T>>;

/**
 * Make specified properties non-nullable
 */
export type WithNonNullable<T, K extends keyof T> = T & {
  [P in K]: NonNullable<T[P]>;
};

/**
 * Make specified properties nullable
 */
export type WithNullable<T, K extends keyof T> = T & {
  [P in K]: Nullable<T[P]>;
};

/**
 * Deep non-nullable - removes null/undefined from all nested properties
 */
export type DeepNonNullable<T> = T extends null | undefined
  ? never
  : T extends (infer U)[]
  ? DeepNonNullable<U>[]
  : T extends readonly (infer U)[]
  ? readonly DeepNonNullable<U>[]
  : T extends object
  ? { [K in keyof T]: DeepNonNullable<T[K]> }
  : T;

/**
 * Deep maybe - adds null/undefined to all nested properties
 */
export type DeepMaybe<T> = T extends object
  ? T extends (infer U)[]
    ? Maybe<DeepMaybe<U>>[]
    : T extends readonly (infer U)[]
    ? readonly Maybe<DeepMaybe<U>>[]
    : { [K in keyof T]: Maybe<DeepMaybe<T[K]>> }
  : Maybe<T>;

/**
 * Conditional type to check if a type is nullable
 */
export type IsNullable<T> = null extends T ? true : undefined extends T ? true : false;

/**
 * Conditional type to check if a type is exactly null
 */
export type IsNull<T> = T extends null ? (null extends T ? true : false) : false;

/**
 * Conditional type to check if a type is exactly undefined
 */
export type IsUndefined<T> = T extends undefined ? (undefined extends T ? true : false) : false;

/**
 * Extract the non-null type from a potentially null type
 */
export type ExtractNonNull<T> = T extends null | undefined ? never : T;

// ===== ADVANCED TYPE GUARDS =====

/**
 * Enhanced type guard for non-null values
 */
export function isNonNull<T>(value: T): value is NonNullable<T> {
  return value !== null && value !== undefined;
}

/**
 * Type guard for null values
 */
export function isNull<T>(value: T | null): value is null {
  return value === null;
}

/**
 * Type guard for undefined values
 */
export function isUndefined<T>(value: T | undefined): value is undefined {
  return value === undefined;
}

/**
 * Type guard for null or undefined values
 */
export function isNullish<T>(value: T | null | undefined): value is null | undefined {
  return value === null || value === undefined;
}

/**
 * Type guard for defined values (not undefined)
 */
export function isDefined<T>(value: T | undefined): value is T {
  return value !== undefined;
}

/**
 * Type guard for non-null strings
 */
export function isNonNullString(value: string | null | undefined): value is NonNullString {
  return typeof value === 'string' && value !== null && value !== undefined && value.length > 0;
}

/**
 * Type guard for non-null numbers
 */
export function isNonNullNumber(value: number | null | undefined): value is NonNullNumber {
  return typeof value === 'number' && value !== null && value !== undefined && !isNaN(value);
}

/**
 * Type guard for non-null objects
 */
export function isNonNullObject<T extends object>(value: T | null | undefined): value is NonNullObject<T> {
  return typeof value === 'object' && value !== null && value !== undefined;
}

/**
 * Type guard for non-empty arrays
 */
export function isNonEmptyArray<T>(value: T[] | null | undefined): value is NonNullable<T[]> & { 0: T } {
  return Array.isArray(value) && value.length > 0;
}

/**
 * Advanced type guard for objects with specific properties
 */
export function hasProperty<T extends object, K extends PropertyKey>(
  obj: T | null | undefined,
  key: K
): obj is T & Record<K, unknown> {
  return isNonNullObject(obj) && key in obj;
}

/**
 * Type guard for objects with non-null property values
 */
export function hasNonNullProperty<T extends object, K extends keyof T>(
  obj: T | null | undefined,
  key: K
): obj is T & { [P in K]: NonNullable<T[P]> } {
  return hasProperty(obj, key) && isNonNull(obj[key]);
}

// ===== SAFE ACCESS UTILITIES =====

/**
 * Safe property access with default value
 */
export function safeGet<T extends object, K extends keyof T, D>(
  obj: T | null | undefined,
  key: K,
  defaultValue: D
): NonNullable<T[K]> | D {
  if (isNonNullObject(obj) && hasNonNullProperty(obj, key)) {
    return obj[key] as NonNullable<T[K]>;
  }
  return defaultValue;
}

/**
 * Safe deep property access
 */
export function safeDeepGet<T extends object, D>(
  obj: T | null | undefined,
  path: string,
  defaultValue: D
): unknown | D {
  if (!isNonNullObject(obj)) {
    return defaultValue;
  }

  const keys = path.split('.');
  let current: any = obj;

  for (const key of keys) {
    if (!isNonNullObject(current) || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }

  return isNonNull(current) ? current : defaultValue;
}

/**
 * Safe array access with bounds checking
 */
export function safeArrayGet<T, D>(
  array: T[] | null | undefined,
  index: number,
  defaultValue: D
): T | D {
  if (!Array.isArray(array) || index < 0 || index >= array.length) {
    return defaultValue;
  }
  
  const value = array[index];
  return isNonNull(value) ? value : defaultValue;
}

/**
 * Safe function call with null checking
 */
export function safeCall<TArgs extends readonly unknown[], TReturn, D>(
  fn: ((...args: TArgs) => TReturn) | null | undefined,
  args: TArgs,
  defaultValue: D
): TReturn | D {
  if (typeof fn === 'function') {
    try {
      const result = fn(...args);
      return isNonNull(result) ? result : defaultValue;
    } catch {
      return defaultValue;
    }
  }
  return defaultValue;
}

// ===== ASSERTION UTILITIES =====

/**
 * Assert that a value is non-null and throw if it isn't
 */
export function assertNonNull<T>(
  value: T | null | undefined,
  message = 'Expected non-null value'
): asserts value is NonNullable<T> {
  if (isNullish(value)) {
    throw new Error(message);
  }
}

/**
 * Assert that a value is a non-null string
 */
export function assertNonNullString(
  value: string | null | undefined,
  message = 'Expected non-null string'
): asserts value is NonNullString {
  if (!isNonNullString(value)) {
    throw new Error(message);
  }
}

/**
 * Assert that a value is a non-null number
 */
export function assertNonNullNumber(
  value: number | null | undefined,
  message = 'Expected non-null number'
): asserts value is NonNullNumber {
  if (!isNonNullNumber(value)) {
    throw new Error(message);
  }
}

/**
 * Assert that a value is a non-null object
 */
export function assertNonNullObject<T extends object>(
  value: T | null | undefined,
  message = 'Expected non-null object'
): asserts value is NonNullObject<T> {
  if (!isNonNullObject(value)) {
    throw new Error(message);
  }
}

// ===== CONDITIONAL TRANSFORMATION UTILITIES =====

/**
 * Transform a value if it's non-null, otherwise return default
 */
export function mapNonNull<T, U, D>(
  value: T | null | undefined,
  transform: (value: NonNullable<T>) => U,
  defaultValue: D
): U | D {
  return isNonNull(value) ? transform(value) : defaultValue;
}

/**
 * Filter null values from an array
 */
export function filterNonNull<T>(array: (T | null | undefined)[]): NonNullable<T>[] {
  return array.filter(isNonNull);
}

/**
 * Map array with null filtering
 */
export function mapNonNullArray<T, U>(
  array: (T | null | undefined)[],
  transform: (value: NonNullable<T>) => U
): U[] {
  return filterNonNull(array).map(transform);
}

/**
 * Compact object by removing null/undefined properties
 */
export function compactObject<T extends Record<string, unknown>>(
  obj: T
): { [K in keyof T]: NonNullable<T[K]> } {
  const result: any = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (isNonNull(value)) {
      result[key] = value;
    }
  }
  
  return result;
}

/**
 * Fill null values in an object with defaults
 */
export function fillNulls<T extends Record<string, unknown>, D extends Partial<T>>(
  obj: T,
  defaults: D
): T & D {
  const result: any = { ...obj };
  
  for (const [key, defaultValue] of Object.entries(defaults)) {
    if (isNullish(result[key]) && isNonNull(defaultValue)) {
      result[key] = defaultValue;
    }
  }
  
  return result;
}

// ===== ADVANCED OPTION TYPE IMPLEMENTATION =====

/**
 * Option type for explicit null handling
 */
export abstract class Option<T> {
  abstract isSome(): this is Some<T>;
  abstract isNone(): this is None;
  abstract unwrap(): T;
  abstract unwrapOr(defaultValue: T): T;
  abstract map<U>(fn: (value: T) => U): Option<U>;
  abstract flatMap<U>(fn: (value: T) => Option<U>): Option<U>;
  abstract filter(predicate: (value: T) => boolean): Option<T>;
  abstract fold<U>(onNone: () => U, onSome: (value: T) => U): U;

  static some<T>(value: T): Option<T> {
    return new Some(value);
  }

  static none<T>(): Option<T> {
    return new None();
  }

  static fromNullable<T>(value: T | null | undefined): Option<T> {
    return isNonNull(value) ? Option.some(value) : Option.none();
  }
}

class Some<T> extends Option<T> {
  constructor(private value: T) {
    super();
  }

  isSome(): this is Some<T> {
    return true;
  }

  isNone(): this is None {
    return false;
  }

  unwrap(): T {
    return this.value;
  }

  unwrapOr(_defaultValue: T): T {
    return this.value;
  }

  map<U>(fn: (value: T) => U): Option<U> {
    return Option.some(fn(this.value));
  }

  flatMap<U>(fn: (value: T) => Option<U>): Option<U> {
    return fn(this.value);
  }

  filter(predicate: (value: T) => boolean): Option<T> {
    return predicate(this.value) ? this : Option.none();
  }

  fold<U>(_onNone: () => U, onSome: (value: T) => U): U {
    return onSome(this.value);
  }
}

class None extends Option<never> {
  isSome(): this is Some<never> {
    return false;
  }

  isNone(): this is None {
    return true;
  }

  unwrap(): never {
    throw new Error('Called unwrap on None');
  }

  unwrapOr<T>(defaultValue: T): T {
    return defaultValue;
  }

  map<U>(_fn: (value: never) => U): Option<U> {
    return Option.none();
  }

  flatMap<U>(_fn: (value: never) => Option<U>): Option<U> {
    return Option.none();
  }

  filter(_predicate: (value: never) => boolean): Option<never> {
    return this;
  }

  fold<U>(onNone: () => U, _onSome: (value: never) => U): U {
    return onNone();
  }
}

// ===== UTILITY FUNCTION EXPORTS =====

/**
 * Create an Option from a nullable value
 */
export function option<T>(value: T | null | undefined): Option<T> {
  return Option.fromNullable(value);
}

/**
 * Combine multiple options into a single option containing an array
 */
export function combineOptions<T extends readonly Option<any>[]>(
  ...options: T
): Option<{ [K in keyof T]: T[K] extends Option<infer U> ? U : never }> {
  const values: any[] = [];
  
  for (const opt of options) {
    if (opt.isNone()) {
      return Option.none();
    }
    values.push(opt.unwrap());
  }
  
  return Option.some(values as any);
}

/**
 * Get the first Some value from an array of options
 */
export function firstSome<T>(...options: Option<T>[]): Option<T> {
  for (const opt of options) {
    if (opt.isSome()) {
      return opt;
    }
  }
  return Option.none();
}

// Export all types for module compatibility
export {};