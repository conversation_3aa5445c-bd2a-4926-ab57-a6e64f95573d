-- Migration: 029_fix_product_colors_schema.sql
-- Purpose: Fix product_colors table to use TEXT (UUID) foreign keys instead of INTEGER
-- Issue: product_colors table still uses INTEGER foreign keys while products/colors use TEXT primary keys
-- Date: 2025-07-05
-- Author: ChromaSync Team

-- This migration fixes the schema mismatch that prevents product-color relationships from being created

BEGIN TRANSACTION;

-- Step 1: Create backup of existing product_colors table
CREATE TABLE IF NOT EXISTS product_colors_backup_029 AS SELECT * FROM product_colors;

-- Step 2: Drop the existing product_colors table
DROP TABLE IF EXISTS product_colors;

-- Step 3: Recreate product_colors table with TEXT (UUID) foreign keys
CREATE TABLE product_colors (
  product_id TEXT NOT NULL,  -- UUID foreign key to match products.id
  color_id TEXT NOT NULL,    -- UUID foreign key to match colors.id
  display_order INTEGER NOT NULL DEFAULT 0,
  organization_id TEXT NOT NULL,
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (product_id, color_id),
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
  FOREIGN KEY (color_id) REFERENCES colors(id) ON DELETE CASCADE,
  -- Constraints
  CHECK (length(product_id) = 36),  -- UUID format validation
  CHECK (length(color_id) = 36)     -- UUID format validation
);

-- Step 4: Create indexes for performance
CREATE INDEX idx_product_colors_product_029 ON product_colors(product_id);
CREATE INDEX idx_product_colors_color_029 ON product_colors(color_id);
CREATE INDEX idx_product_colors_org_029 ON product_colors(organization_id);
CREATE INDEX idx_product_colors_org_product_029 ON product_colors(organization_id, product_id);
CREATE INDEX idx_product_colors_org_color_029 ON product_colors(organization_id, color_id);

-- Step 5: Verify the new schema
SELECT 
  'Migration 029 complete. ' ||
  'Product_colors table recreated with UUID foreign keys. ' ||
  'Products: ' || (SELECT COUNT(*) FROM products) || ', ' ||
  'Colors: ' || (SELECT COUNT(*) FROM colors) || ', ' ||
  'Ready for product-color relationships'
  as migration_result;

-- Step 6: Drop backup table (comment out if you want to keep it for safety)
-- DROP TABLE product_colors_backup_029;

COMMIT;