/**
 * @file sampleData.ts
 * @description Sample color data for development purposes
 */

import { ColorEntry } from '../../shared/types/color.types';
import { v4 as uuidv4 } from 'uuid';

const now = new Date().toISOString();

// Sample color data for development
export const sampleColors: ColorEntry[] = [
  {
    id: uuidv4(),
    product: 'Brand Logo',
    organizationId: 'sample-org-id',
    name: 'Primary Blue',
    code: 'PMS 286 C',
    hex: '#1E40AF',
    cmyk: '100,72,0,0',
    notes: 'Primary brand color',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Packaging',
    organizationId: 'sample-org-id',
    name: 'Ocean Blue',
    code: 'PMS 300 C',
    hex: '#005EB8',
    cmyk: '100,44,0,0',
    notes: 'Used for premium products',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Marketing',
    organizationId: 'sample-org-id',
    name: 'Vibrant Green',
    code: 'PMS 355 C',
    hex: '#00AB4E',
    cmyk: '94,0,100,0',
    notes: 'Spring campaign accent',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Website',
    organizationId: 'sample-org-id',
    name: 'Deep Purple',
    code: 'PMS 2685 C',
    hex: '#5F259F',
    cmyk: '85,99,0,0',
    notes: 'Secondary brand color',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Print',
    organizationId: 'sample-org-id',
    name: 'Sunny Yellow',
    code: 'PMS 109 C',
    hex: '#FFD100',
    cmyk: '0,9,100,0',
    notes: 'Highlight color',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Retail',
    organizationId: 'sample-org-id',
    name: 'Coral Orange',
    code: 'PMS 1655 C',
    hex: '#FF5C35',
    cmyk: '0,73,98,0',
    notes: 'Sale signage',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Corporate',
    organizationId: 'sample-org-id',
    name: 'Charcoal Gray',
    code: 'PMS 425 C',
    hex: '#54585A',
    cmyk: '48,29,26,76',
    notes: 'Official documents',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Social Media',
    organizationId: 'sample-org-id',
    name: 'Electric Teal',
    code: 'PMS 3272 C',
    hex: '#00B3A5',
    cmyk: '95,0,37,0',
    notes: 'Digital campaign',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Custom',
    organizationId: 'sample-org-id',
    name: 'Vintage Rose',
    hex: '#C5897E',
    code: 'Custom Rose',
    cmyk: '0,40,25,20',
    notes: 'Special project color match',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Custom',
    organizationId: 'sample-org-id',
    name: 'Midnight Blue',
    hex: '#012E40',
    code: 'Custom Navy',
    cmyk: '100,50,20,80',
    notes: 'Client request for navy variant',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Custom',
    organizationId: 'sample-org-id',
    name: 'Sand Dune',
    hex: '#C8B180',
    code: 'Custom Neutral',
    cmyk: '0,12,36,22',
    notes: 'Neutral backdrop for photography',
    createdAt: now,
    updatedAt: now
  },
  {
    id: uuidv4(),
    product: 'Custom',
    organizationId: 'sample-org-id',
    name: 'Forest Green',
    hex: '#154734',
    code: 'Custom Green',
    cmyk: '85,15,85,60',
    notes: 'Matched to natural materials',
    createdAt: now,
    updatedAt: now
  }
];

// Function to load sample data into the store during development
export const loadSampleData = (organizationId?: string): ColorEntry[] => {
  console.log('Loading sample data:', sampleColors.length, 'user colors');
  
  // If organization ID is provided, use it for all sample colors
  if (organizationId) {
    return sampleColors.map(color => ({
      ...color,
      organizationId
    }));
  }
  
  return sampleColors;
}; 