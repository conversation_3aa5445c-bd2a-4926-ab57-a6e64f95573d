/**
 * @file examples.ts
 * @description Examples demonstrating IPCRouter usage and migration patterns
 *
 * This file shows how to:
 * 1. Migrate existing handlers to the router system
 * 2. Use route patterns with parameters
 * 3. Apply middleware chains effectively
 * 4. Integrate with existing validation and security systems
 * 5. Organize routes with groups
 */

import { createIPCRouter, IPCRequest } from './IPCRouter';
import { ServiceLocator } from '../../services/service-locator';
// import { ColorChannels } from '../../../shared/types/color.types';
// import { ProductChannels } from '../../../shared/types/product.types';

// ============================================================================
// EXAMPLE 1: BASIC ROUTER SETUP WITH EXISTING SYSTEM INTEGRATION
// ============================================================================

/**
 * Example showing basic router setup with middleware integration
 */
export function createBasicRouter() {
  const router = createIPCRouter();

  // Add global middleware (placeholder - implement actual middleware as needed)
  // router.use(loggerMiddleware);
  // router.use(rateLimitMiddleware);
  // router.use(requireOrganizationMiddleware);

  return router;
}

// ============================================================================
// EXAMPLE 2: MIGRATING EXISTING COLOR HANDLERS
// ============================================================================

/**
 * Example showing migration from existing color handlers to router patterns
 */
export function setupColorRoutes(router: ReturnType<typeof createIPCRouter>) {
  // Create an API group for colors
  const colorAPI = router.group('/api/colors');

  // ========================================================================
  // BASIC CRUD ROUTES (migrated from existing handlers)
  // ========================================================================

  // GET /api/colors - Get all colors (equivalent to ColorChannels.GET_ALL)
  colorAPI.get('/', async (request: IPCRequest): Promise<any> => {
    try {
      const organizationId = (request as any).organizationId;
      const colorService = ServiceLocator.getColorService();
      const colors = await colorService.getAll(organizationId);

      return {
        success: true,
        data: colors,
        userMessage: `Retrieved ${colors.length} colors`,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to retrieve colors',
        timestamp: Date.now(),
      };
    }
  });

  // GET /api/colors/with-usage - Get colors with usage data
  colorAPI.get('/with-usage', async (request: IPCRequest): Promise<any> => {
    try {
      const organizationId = (request as any).organizationId;
      const colorService = ServiceLocator.getColorService();
      const result = await colorService.getAllWithUsage(organizationId);

      return {
        success: true,
        data: result,
        userMessage: `Retrieved ${result.colors?.length || 0} colors with usage data`,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to retrieve colors with usage data',
        timestamp: Date.now(),
      };
    }
  });

  // GET /api/colors/:id - Get specific color by ID (new parameterized route)
  colorAPI.get('/:id', async (request: IPCRequest): Promise<any> => {
    try {
      const { id: colorId } = request.params;
      const organizationId = (request as any).organizationId;

      if (!colorId) {
        return {
          success: false,
          error: 'Color ID is required',
          userMessage: 'Please provide a valid color ID',
          timestamp: Date.now(),
        };
      }

      const colorService = ServiceLocator.getColorService();
      const color = await colorService.getById(colorId, organizationId);

      return {
        success: true,
        data: color,
        userMessage: color ? 'Color retrieved successfully' : 'Color not found',
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to retrieve color',
        timestamp: Date.now(),
      };
    }
  });

  // POST /api/colors - Create new color
  colorAPI.post('/', async (request: IPCRequest): Promise<any> => {
    try {
      const organizationId = (request as any).organizationId;
      const colorData = request.data;

      if (!colorData || typeof colorData !== 'object') {
        return {
          success: false,
          error: 'Invalid color data',
          userMessage: 'Please provide valid color information',
          timestamp: Date.now(),
        };
      }

      const colorService = ServiceLocator.getColorService();
      const newColorId = await colorService.add(
        colorData,
        undefined,
        organizationId
      );
      const newColor = await colorService.getById(newColorId, organizationId);

      return {
        success: true,
        data: newColor,
        userMessage: 'Color created successfully',
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to create color',
        timestamp: Date.now(),
      };
    }
  });

  // PUT /api/colors/:id - Update specific color
  colorAPI.put('/:id', async (request: IPCRequest): Promise<any> => {
    try {
      const { id: colorId } = request.params;
      const organizationId = (request as any).organizationId;
      const updates = request.data;

      if (!colorId) {
        return {
          success: false,
          error: 'Color ID is required',
          userMessage: 'Please provide a valid color ID',
          timestamp: Date.now(),
        };
      }

      if (!updates || typeof updates !== 'object') {
        return {
          success: false,
          error: 'Invalid update data',
          userMessage: 'Please provide valid update information',
          timestamp: Date.now(),
        };
      }

      const colorService = ServiceLocator.getColorService();
      const success = await colorService.update(
        colorId,
        updates,
        organizationId
      );

      return {
        success,
        data: success,
        userMessage: success
          ? 'Color updated successfully'
          : 'Color update failed',
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to update color',
        timestamp: Date.now(),
      };
    }
  });

  // DELETE /api/colors/:id - Delete specific color
  colorAPI.delete('/:id', async (request: IPCRequest): Promise<any> => {
    try {
      const { id: colorId } = request.params;
      const organizationId = (request as any).organizationId;

      if (!colorId) {
        return {
          success: false,
          error: 'Color ID is required',
          userMessage: 'Please provide a valid color ID',
          timestamp: Date.now(),
        };
      }

      const colorService = ServiceLocator.getColorService();
      const success = await colorService.delete(colorId, organizationId);

      return {
        success,
        data: success,
        userMessage: success
          ? 'Color deleted successfully'
          : 'Color deletion failed',
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to delete color',
        timestamp: Date.now(),
      };
    }
  });
}

// ============================================================================
// EXAMPLE 3: ADVANCED ROUTE PATTERNS WITH RELATIONSHIPS
// ============================================================================

/**
 * Example showing advanced route patterns for nested resources
 */
export function setupAdvancedColorRoutes(
  router: ReturnType<typeof createIPCRouter>
) {
  const colorAPI = router.group('/api/colors');

  // GET /api/colors/:id/products - Get products using a specific color
  colorAPI.get('/:id/products', async (request: IPCRequest): Promise<any> => {
    try {
      const { id: colorId } = request.params;
      const organizationId = (request as any).organizationId;

      if (!colorId) {
        return {
          success: false,
          error: 'Color ID is required',
          userMessage: 'Please provide a valid color ID',
          timestamp: Date.now(),
        };
      }

      const colorService = ServiceLocator.getColorService();
      const products =
        await colorService.getProductsByColorName(organizationId);

      return {
        success: true,
        data: products,
        userMessage: `Found ${products.length} products using this color`,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to retrieve products for color',
        timestamp: Date.now(),
      };
    }
  });

  // GET /api/colors/:id/similar - Find similar colors
  colorAPI.get('/:id/similar', async (request: IPCRequest): Promise<any> => {
    try {
      const { id: colorId } = request.params;
      const organizationId = (request as any).organizationId;
      // const { tolerance = 10 } = request.data || {};

      if (!colorId) {
        return {
          success: false,
          error: 'Color ID is required',
          userMessage: 'Please provide a valid color ID',
          timestamp: Date.now(),
        };
      }

      const colorService = ServiceLocator.getColorService();
      const color = await colorService.getById(colorId, organizationId);

      if (!color) {
        return {
          success: false,
          error: 'Color not found',
          userMessage: 'The specified color was not found',
          timestamp: Date.now(),
        };
      }

      const similarColors = await colorService
        .getAll(organizationId)
        .filter(c => c.hex.toLowerCase() === color.hex.toLowerCase());

      return {
        success: true,
        data: similarColors,
        userMessage: `Found ${similarColors.length} similar colors`,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to find similar colors',
        timestamp: Date.now(),
      };
    }
  });
}

// ============================================================================
// EXAMPLE 4: PRODUCT ROUTES WITH MIDDLEWARE
// ============================================================================

/**
 * Example showing product routes with custom middleware
 */
export function setupProductRoutes(router: ReturnType<typeof createIPCRouter>) {
  // Custom middleware for product validation (unused in this example)
  /*
  const validateProductAccess = async (request: IPCRequest, next: () => Promise<void>) => {
    const { id: productId } = request.params;
    const organizationId = (request as any).organizationId;

    if (productId) {
      try {
        const productService = ServiceLocator.getProductService();
        const product = await productService.getById(productId, organizationId);
        
        if (!product) {
          return {
            success: false,
            error: 'Product not found',
            userMessage: 'The specified product was not found',
            timestamp: Date.now()
          };
        }
        
        // Add product to request for handlers to use
        (request as any).product = product;
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          userMessage: 'Failed to validate product access',
          timestamp: Date.now()
        };
      }
    }

    await next();
  };
  */

  const productAPI = router.group('/api/products');

  // GET /api/products - Get all products
  productAPI.get('/', async (request: IPCRequest): Promise<any> => {
    try {
      const organizationId = (request as any).organizationId;
      const productService = ServiceLocator.getProductService();
      const products = await productService.getAll(organizationId);

      return {
        success: true,
        data: products,
        userMessage: `Retrieved ${products.length} products`,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to retrieve products',
        timestamp: Date.now(),
      };
    }
  });

  // GET /api/products/:id/colors - Get colors for a specific product
  productAPI.get('/:id/colors', async (request: IPCRequest): Promise<any> => {
    try {
      const { id: productId } = request.params;
      const organizationId = (request as any).organizationId;

      if (!productId) {
        return {
          success: false,
          error: 'Product ID is required',
          userMessage: 'Product ID is missing',
          timestamp: Date.now(),
        };
      }

      if (!organizationId) {
        return {
          success: false,
          error: 'Organization ID is required',
          userMessage: 'Organization context is missing',
          timestamp: Date.now(),
        };
      }

      const productService = ServiceLocator.getProductService();
      const colors = await productService.getColors(productId, organizationId);

      return {
        success: true,
        data: colors,
        userMessage: `Retrieved ${colors.length} colors for product`,
        timestamp: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userMessage: 'Failed to retrieve product colors',
        timestamp: Date.now(),
      };
    }
  });

  // POST /api/products/:id/colors/:colorId - Add color to product
  productAPI.post(
    '/:id/colors/:colorId',
    async (request: IPCRequest): Promise<any> => {
      try {
        const { id: productId, colorId } = request.params;
        const organizationId = (request as any).organizationId;

        if (!productId || !colorId) {
          return {
            success: false,
            error: 'Product ID and Color ID are required',
            userMessage: 'Please provide valid product and color IDs',
            timestamp: Date.now(),
          };
        }

        const productService = ServiceLocator.getProductService();
        const success = await productService.addColor(
          productId,
          colorId,
          organizationId
        );

        return {
          success,
          data: success,
          userMessage: success
            ? 'Color added to product successfully'
            : 'Failed to add color to product',
          timestamp: Date.now(),
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          userMessage: 'Failed to add color to product',
          timestamp: Date.now(),
        };
      }
    }
  );
}

// ============================================================================
// EXAMPLE 5: LEGACY HANDLER MIGRATION
// ============================================================================

/**
 * Example showing how to migrate existing handlers while maintaining compatibility
 */
export function setupLegacyCompatibility(
  router: ReturnType<typeof createIPCRouter>
) {
  // Migrate existing color handlers to router while maintaining channel names

  // Legacy: ColorChannels.GET_ALL
  router.post(
    '/legacy/colors/get-all',
    async (request: IPCRequest): Promise<any> => {
      try {
        const organizationId = (request as any).organizationId;
        const colorService = ServiceLocator.getColorService();
        const colors = await colorService.getAll(organizationId);

        return {
          success: true,
          data: colors,
          userMessage: `Retrieved ${colors.length} colors`,
          timestamp: Date.now(),
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          userMessage: 'Failed to retrieve colors',
          timestamp: Date.now(),
        };
      }
    }
  );

  // Legacy: ColorChannels.GET_ALL_WITH_USAGE
  router.post(
    '/legacy/colors/get-all-with-usage',
    async (request: IPCRequest): Promise<any> => {
      try {
        const organizationId = (request as any).organizationId;
        const colorService = ServiceLocator.getColorService();
        const result = await colorService.getAllWithUsage(organizationId);

        return {
          success: true,
          data: result,
          userMessage: `Retrieved ${result.colors?.length || 0} colors with usage data`,
          timestamp: Date.now(),
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          userMessage: 'Failed to retrieve colors with usage data',
          timestamp: Date.now(),
        };
      }
    }
  );

  // Legacy: ProductChannels.GET_ALL_WITH_COLORS
  router.post(
    '/legacy/products/get-all-with-colors',
    async (request: IPCRequest): Promise<any> => {
      try {
        const organizationId = (request as any).organizationId;
        const productService = ServiceLocator.getProductService();
        const products = await productService.getAll(organizationId);

        return {
          success: true,
          data: products,
          userMessage: `Retrieved ${products.length} products with colors`,
          timestamp: Date.now(),
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          userMessage: 'Failed to retrieve products with colors',
          timestamp: Date.now(),
        };
      }
    }
  );
}

// ============================================================================
// EXAMPLE 6: COMPLETE ROUTER SETUP FUNCTION
// ============================================================================

/**
 * Complete example showing how to set up the entire router with all features
 */
export function setupCompleteRouter() {
  console.log('[RouterExample] 🚀 Setting up complete IPC router...');

  // Create the main router
  const router = createIPCRouter();

  // Add global middleware (placeholder - implement actual middleware as needed)
  // router.use(loggerMiddleware);
  // router.use(rateLimitMiddleware);
  // router.use(requireOrganizationMiddleware);

  // Set up route groups
  setupColorRoutes(router);
  setupAdvancedColorRoutes(router);
  setupProductRoutes(router);
  setupLegacyCompatibility(router);

  // Admin routes with additional middleware
  const adminAPI = router.group('/api/admin');

  // Add admin-specific middleware (example - requires proper middleware implementation)
  // adminAPI.use(async (request: IPCRequest, next: () => Promise<void>) => {
  //   // Check admin permissions
  //   console.log('[RouterExample] 🔒 Admin route accessed');
  //   await next();
  // });

  adminAPI.post(
    '/colors/clear-all',
    async (request: IPCRequest): Promise<any> => {
      try {
        const organizationId = (request as any).organizationId;
        const { hardDelete = false } = request.data || {};

        const colorService = ServiceLocator.getColorService();
        const result = await colorService.clearAll(organizationId, hardDelete);

        return {
          success: true,
          data: result,
          userMessage: `Admin clear completed: ${result ? 'success' : 'failed'}`,
          timestamp: Date.now(),
        };
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          userMessage: 'Failed to perform admin clear operation',
          timestamp: Date.now(),
        };
      }
    }
  );

  // Register all routes with Electron IPC
  router.attachToIpcMain();

  console.log('[RouterExample] ✅ Router setup complete');
  console.log('[RouterExample] 📊 Router stats:', router.getStats());
  console.log('[RouterExample] 📝 Registered routes:', router.getRoutes());

  return router;
}

// Export for use in main process
// Note: setupCompleteRouter is already exported above
