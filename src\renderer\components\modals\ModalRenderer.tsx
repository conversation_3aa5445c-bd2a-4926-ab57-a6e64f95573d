/**
 * @file ModalRenderer.tsx
 * @description Centralized modal renderer for all application modals
 */

import React, { Suspense } from 'react';
import { useModal, ModalType } from '../../context/ModalContext';
import { SettingsModalWithSuspense } from '../../utils/lazyComponents';
import { LicenseDialog } from '../License/LicenseDialog';
import SetupModal from '../SetupModal';
import HelpModal from '../common/HelpModal';

// Import other modals
const ColorFormModal = React.lazy(() => import('../ColorForm'));
const GradientPickerModal = React.lazy(() => import('../GradientPickerModal'));

// Type guard helpers for safe property access
const hasProperty = (obj: any, prop: string): boolean => {
  return obj && typeof obj === 'object' && prop in obj;
};

const getDataProperty = (data: any, prop: string): any => {
  return hasProperty(data, prop) ? data[prop] : undefined;
};

/**
 * Modal renderer component that handles all application modals
 */
export const ModalRenderer: React.FC = () => {
  const { modals, closeModal, getModalData } = useModal();

  const renderModal = (modalType: ModalType) => {
    const modalState = modals.get(modalType);
    if (!modalState?.isOpen) {
      return null;
    }

    const data = getModalData(modalType);
    const onClose = () => closeModal(modalType);

    switch (modalType) {
      case 'settings':
        return (
          <SettingsModalWithSuspense
            isOpen={true}
            onClose={onClose}
            initialTab={getDataProperty(data, 'initialTab')}
          />
        );

      case 'license':
        return (
          <LicenseDialog
            isOpen={true}
            onClose={onClose}
            licenseStatus={
              getDataProperty(data, 'licenseStatus') || {
                isValid: true,
                inTrialMode: false,
                trialDaysRemaining: 0,
              }
            }
          />
        );

      case 'setup':
        return <SetupModal />;

      case 'help':
        return <HelpModal isOpen={true} onClose={onClose} />;

      case 'colorForm':
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <ColorFormModal
              editMode={getDataProperty(data, 'mode') === 'edit'}
              color={getDataProperty(data, 'color')}
              onCancel={onClose}
              isModal={true}
            />
          </Suspense>
        );

      case 'gradientPicker':
        return (
          <Suspense fallback={<div>Loading...</div>}>
            <GradientPickerModal
              isOpen={true}
              onClose={onClose}
              editMode={getDataProperty(data, 'editMode') || false}
              color={getDataProperty(data, 'color')}
              initialValue={getDataProperty(data, 'initialValue')}
              productName={getDataProperty(data, 'productName')}
              onSuccess={getDataProperty(data, 'onSuccess')}
            />
          </Suspense>
        );

      case 'productForm':
      case 'colorComparison':
      case 'acceptInvitation':
        // These modals are not yet implemented
        console.warn(`Modal ${modalType} is not yet implemented`);
        return null;

      case 'export':
        // Export modal would be implemented here
        return null;

      case 'import':
        // Import modal would be implemented here
        return null;

      case 'datasheet':
        // Datasheet modal would be implemented here
        return null;

      case 'colorSpace3D':
        // 3D color space modal would be implemented here
        return null;

      default:
        console.warn(`Unknown modal type: ${modalType}`);
        return null;
    }
  };

  return (
    <>
      {Array.from(modals.keys()).map(modalType => (
        <React.Fragment key={modalType}>
          {renderModal(modalType)}
        </React.Fragment>
      ))}
    </>
  );
};

export default ModalRenderer;
