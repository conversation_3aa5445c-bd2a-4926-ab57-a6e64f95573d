/**
 * @file organization.store.clean.ts
 * @description Clean, best-practice organization store implementation
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { Organization } from '../../shared/types/organization.types';
import { OrganizationService } from '../services/organization.service';
import OrganizationCacheService from '../services/organization-cache.service';
import { storeEventBus } from '../services/store-event-bus.service';

interface OrganizationStoreState {
  // Data
  currentOrganization: Organization | null;
  organizations: Organization[];
  members: any[]; // TODO: Import proper OrganizationMember type
  
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Actions
  loadOrganizations: () => Promise<void>;
  fetchOrganizations: () => Promise<void>; // Alias for loadOrganizations
  loadCurrentOrganization: () => Promise<void>;
  loadOrganizationContext: () => Promise<{ success: boolean; error?: string }>;
  switchOrganization: (orgId: string) => Promise<{ success: boolean; error?: string }>;
  setCurrentOrganization: (org: Organization | null) => void;
  setOrganizations: (orgs: Organization[]) => void;
  createOrganization: (name: string) => Promise<{ success: boolean; data?: Organization; error?: string }>;
  updateOrganization: (orgId: string, updates: Partial<Organization>) => Promise<{ success: boolean; data?: Organization; error?: string }>;
  deleteOrganization: (orgId: string, forceCascade?: boolean) => Promise<{ success: boolean; error?: string }>;
  acceptInvitation: (token: string) => Promise<{ success: boolean; error?: string }>;
  loadMembers: () => Promise<void>;
  inviteMember: (email: string, role?: string) => Promise<{ success: boolean; error?: string }>;
  removeMember: (userId: string) => Promise<{ success: boolean; error?: string }>;
  updateMemberRole: (userId: string, role: string) => Promise<{ success: boolean; error?: string }>;
  clearState: () => void;
}

/**
 * Clean organization store with best practices:
 * - Clear organization switching
 * - Proper data reloading
 * - No race conditions
 * - Clean error handling
 */
export const useOrganizationStore = create<OrganizationStoreState>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentOrganization: null,
      organizations: [],
      members: [],
      isLoading: false,
      error: null,

      /**
       * Load all organizations
       */
      loadOrganizations: async () => {
        console.log('[OrganizationStore] 🔍 Loading organizations...');
        set({ isLoading: true, error: null });

        try {
          console.log('[OrganizationStore] 📡 Calling window.organizationAPI.getOrganizations()...');
          const result = await window.organizationAPI.getOrganizations();
          
          console.log('[OrganizationStore] 📥 Received API result:', {
            success: result?.success,
            hasData: !!result?.data,
            dataLength: result?.data?.length,
            error: result?.error,
            fullResult: result
          });
          
          if (result.success) {
            console.log('[OrganizationStore] 📊 Organizations data:', result.data);
            
            set({ 
              organizations: result.data,
              isLoading: false 
            });
            
            // Auto-select single organization
            if (result.data.length === 1 && !get().currentOrganization) {
              console.log('[OrganizationStore] Auto-selecting single organization');
              get().setCurrentOrganization(result.data[0]);
            }
            
            console.log(`[OrganizationStore] ✅ Loaded ${result.data.length} organizations successfully`);
          } else {
            console.log('[OrganizationStore] ❌ API call failed:', result.error);
            throw new Error(result.error || 'Failed to load organizations');
          }
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error('[OrganizationStore] ❌ Failed to load organizations:', errorMessage);
          console.error('[OrganizationStore] ❌ Full error:', error);
          
          set({ 
            isLoading: false, 
            error: errorMessage 
          });
        }
      },

      /**
       * Alias for loadOrganizations for backward compatibility
       */
      fetchOrganizations: async () => {
        return get().loadOrganizations();
      },

      /**
       * Accept organization invitation
       */
      acceptInvitation: async (token: string) => {
        console.log('[OrganizationStore] 🔄 Accepting invitation with token:', token);
        set({ isLoading: true, error: null });

        try {
          const result = await window.organizationAPI.acceptInvitation(token);
          
          if (result.success) {
            console.log('[OrganizationStore] ✅ Invitation accepted successfully');
            // Reload organizations to include the new one
            await get().loadOrganizations();
            set({ isLoading: false });
            return { success: true };
          } else {
            console.log('[OrganizationStore] ❌ Failed to accept invitation:', result.error);
            set({ isLoading: false, error: result.error || 'Failed to accept invitation' });
            return { success: false, error: result.error || 'Failed to accept invitation' };
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error('[OrganizationStore] ❌ Error accepting invitation:', errorMessage);
          set({ isLoading: false, error: errorMessage });
          return { success: false, error: errorMessage };
        }
      },

      /**
       * Consolidated organization context loading with caching
       * Combines organization loading + current org + auto-selection in single operation
       */
      loadOrganizationContext: async () => {
        console.log('[OrganizationStore] 🔄 Loading organization context with caching...');
        set({ isLoading: true, error: null });

        try {
          // 1. Check cache first
          const cached = OrganizationCacheService.getCachedContext();
          if (cached) {
            set({ 
              organizations: cached.organizations,
              currentOrganization: cached.currentOrganization,
              isLoading: false 
            });

            // Load members for cached current organization
            if (cached.currentOrganization) {
              await get().loadMembers();
            }

            console.log('[OrganizationStore] ✅ Used cached organization context');
            return { success: true };
          }

          // 2. Load from API if no valid cache
          console.log('[OrganizationStore] 📡 Loading from API (no valid cache)...');
          const orgsResult = await window.organizationAPI.getOrganizations();
          if (!orgsResult.success) {
            throw new Error(orgsResult.error || 'Failed to load organizations');
          }

          set({ organizations: orgsResult.data });
          console.log(`[OrganizationStore] ✅ Loaded ${orgsResult.data.length} organizations`);

          // 3. Load current organization from backend
          const currentResult = await window.organizationAPI.getCurrentOrganization();
          let selectedOrg: Organization | null = null;

          if (currentResult.success && currentResult.data) {
            selectedOrg = currentResult.data;
            console.log('[OrganizationStore] ✅ Current organization from backend:', selectedOrg.name);
          } else {
            // 4. Auto-selection logic
            if (orgsResult.data.length === 1) {
              selectedOrg = orgsResult.data[0];
              console.log('[OrganizationStore] 🔄 Auto-selecting single organization:', selectedOrg.name);
              await window.organizationAPI.setCurrentOrganization(selectedOrg.external_id);
            } else if (orgsResult.data.length > 1) {
              // Check localStorage for stored preference
              const storedOrgId = OrganizationCacheService.getLastOrganizationId();
              const matchingOrg = orgsResult.data.find(org => org.external_id === storedOrgId);
              if (matchingOrg) {
                selectedOrg = matchingOrg;
                console.log('[OrganizationStore] 🔄 Restoring stored organization:', selectedOrg.name);
                await window.organizationAPI.setCurrentOrganization(selectedOrg.external_id);
              }
            }
          }

          // 5. Update state
          set({ 
            currentOrganization: selectedOrg,
            isLoading: false 
          });

          // 6. Cache the result
          OrganizationCacheService.setCachedContext(orgsResult.data, selectedOrg);

          // 7. Persist selection and load members
          if (selectedOrg) {
            OrganizationCacheService.setLastOrganizationId(selectedOrg.external_id);
            await get().loadMembers();
          }

          console.log('[OrganizationStore] ✅ Organization context loaded and cached');
          return { success: true };

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error('[OrganizationStore] ❌ Failed to load organization context:', errorMessage);
          
          set({ 
            isLoading: false, 
            error: errorMessage 
          });
          return { success: false, error: errorMessage };
        }
      },

      /**
       * Load current organization from backend
       */
      loadCurrentOrganization: async () => {
        console.log('[OrganizationStore] Loading current organization from backend...');
        
        try {
          const result = await window.organizationAPI.getCurrentOrganization();
          
          if (result.success && result.data) {
            console.log('[OrganizationStore] ✅ Current organization loaded:', result.data.name);
            set({ 
              currentOrganization: result.data,
              error: null 
            });
            
            // Persist the organization
            if (result.data.external_id) {
              localStorage.setItem('chromasync:lastOrganization', result.data.external_id);
            }
            
            // Load members for the organization
            await get().loadMembers();
          } else {
            console.log('[OrganizationStore] ℹ️ No current organization set in backend');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error('[OrganizationStore] ❌ Failed to load current organization:', errorMessage);
          set({ error: errorMessage });
        }
      },

      /**
       * Switch to different organization
       * Simplified: Clear → Switch → Load → Sync if needed
       */
      switchOrganization: async (orgId: string) => {
        console.log(`[OrganizationStore] 🔄 Switching to organization: ${orgId}`);
        
        const org = get().organizations.find(o => o.external_id === orgId);
        if (!org) {
          set({ error: 'Organization not found' });
          return { success: false, error: 'Organization not found' };
        }

        set({ isLoading: true, error: null });

        try {
          // 1. Clear current data to prevent confusion using event bus
          console.log('[OrganizationStore] 🧹 Clearing current data stores...');
          storeEventBus.emit({ type: 'ORGANIZATION_CLEARED' });
          
          // 2. Set backend organization context
          console.log('[OrganizationStore] 📡 Setting backend organization...');
          const setOrgResult = await window.organizationAPI.setCurrentOrganization(orgId);
          
          if (!setOrgResult.success) {
            throw new Error(setOrgResult.error || 'Failed to set organization context');
          }
          
          console.log('[OrganizationStore] ✅ Backend organization context set successfully');
          
          // 3. Update frontend state
          console.log('[OrganizationStore] 📱 Updating frontend state...');
          set({ 
            currentOrganization: org,
            isLoading: false 
          });
          
          // 4. Persist selection and invalidate cache
          OrganizationCacheService.setLastOrganizationId(orgId);
          OrganizationCacheService.invalidateCache();
          
          // 5. Load organization members
          await get().loadMembers();
          
          // 6. Wait for organization context to fully propagate and verify it's set
          console.log('[OrganizationStore] ⏱️ Waiting for organization context to propagate...');
          await new Promise(resolve => setTimeout(resolve, 300));
          
          // Verify organization context is properly set
          const contextResult = await window.organizationAPI.getCurrentOrganization();
          if (!contextResult.success || contextResult.data?.external_id !== orgId) {
            console.warn('[OrganizationStore] ⚠️ Organization context not properly set, retrying...');
            await window.organizationAPI.setCurrentOrganization(orgId);
            await new Promise(resolve => setTimeout(resolve, 200));
          }
          
          // 7. Load data for new organization using event bus
          console.log('[OrganizationStore] 📊 Loading data for new organization...');
          storeEventBus.emit({ type: 'ORGANIZATION_SWITCHED', organizationId: orgId });
          
          const loadData = async () => {
            try {
              console.log('[OrganizationStore] 🔄 Triggering data refresh for organization:', orgId);
              
              // Final verification that organization context is correctly set before loading data
              console.log('[OrganizationStore] 🔍 Final verification of organization context...');
              const finalContextCheck = await window.organizationAPI.getCurrentOrganization();
              if (!finalContextCheck.success || finalContextCheck.data?.external_id !== orgId) {
                throw new Error(`Organization context verification failed. Expected: ${orgId}, Got: ${finalContextCheck.data?.external_id}`);
              }
              console.log('[OrganizationStore] ✅ Organization context verified successfully');
              
              // Wait for data loading to complete (triggered by event bus)
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              console.log('[OrganizationStore] ✅ Data refresh triggered via event bus');
              
              // Data loading is now handled by individual stores through event bus
              console.log('[OrganizationStore] 📊 Data refresh initiated for organization:', orgId);
              
              // ALWAYS check for additional organizations (separate from data sync)
              console.log('[OrganizationStore] 🔄 Checking for additional organizations from Supabase...');
              try {
                const currentOrgCount = get().organizations.length;
                console.log('[OrganizationStore] 📊 Current organizations count:', currentOrgCount);
                
                // Trigger organization sync to get all organizations user has access to
                await get().loadOrganizations();
                
                const newOrgCount = get().organizations.length;
                console.log('[OrganizationStore] 📊 Organizations after sync:', newOrgCount);
                
                if (newOrgCount > currentOrgCount) {
                  console.log(`[OrganizationStore] ✅ Found ${newOrgCount - currentOrgCount} additional organizations`);
                } else {
                  console.log('[OrganizationStore] ℹ️ No additional organizations found');
                }
              } catch (orgSyncError) {
                console.warn('[OrganizationStore] ⚠️ Failed to sync organizations:', orgSyncError);
              }
              
              // Data loading and sync are now handled by individual stores through event bus
              console.log('[OrganizationStore] ✅ Organization context and data refresh setup completed');
              
              console.log('[OrganizationStore] ✅ Data loading completed');
            } catch (error) {
              console.error('[OrganizationStore] ❌ Data loading failed:', error);
            }
          };
          
          // Start data loading - make it blocking to ensure it completes
          await loadData();
          
          console.log('[OrganizationStore] ✅ Organization switch initiated successfully');
          return { success: true };
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error('[OrganizationStore] ❌ Organization switch failed:', errorMessage);
          
          set({ 
            isLoading: false, 
            error: errorMessage 
          });
          return { success: false, error: errorMessage };
        }
      },

      /**
       * Set current organization
       */
      setCurrentOrganization: (org) => {
        set({ currentOrganization: org });
        
        if (org) {
          OrganizationCacheService.setLastOrganizationId(org.external_id);
          // Update cache with new current org
          const { organizations } = get();
          OrganizationCacheService.setCachedContext(organizations, org);
        } else {
          OrganizationCacheService.clearLastOrganizationId();
          OrganizationCacheService.invalidateCache();
        }
      },

      /**
       * Set organizations list
       */
      setOrganizations: (orgs) => {
        set({ organizations: orgs });
      },

      /**
       * Create new organization with separated concerns
       */
      createOrganization: async (name: string) => {
        // 1. Validate input
        const validation = OrganizationService.validateOrganizationName(name);
        if (!validation.valid) {
          set({ error: validation.error });
          return { success: false, error: validation.error };
        }

        set({ isLoading: true, error: null });
        
        try {
          // 2. Create organization via service
          const createResult = await OrganizationService.createOrganization(name.trim());
          
          if (!createResult.success || !createResult.data) {
            set({ 
              error: createResult.error || 'Failed to create organization', 
              isLoading: false 
            });
            return createResult;
          }

          const newOrg = createResult.data;
          
          // 3. Update store state
          set({ 
            organizations: [...get().organizations, newOrg],
            currentOrganization: newOrg,
            isLoading: false 
          });
          
          // 4. Complete setup asynchronously (non-blocking)
          OrganizationService.completeOrganizationSetup(newOrg).catch(error => {
            console.warn('[OrganizationStore] Post-creation setup failed:', error);
            // Don't fail the whole operation - organization was created successfully
          });
          
          console.log('[OrganizationStore] ✅ Organization created successfully:', newOrg.name);
          return { success: true, data: newOrg };
          
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Failed to create organization';
          console.error('[OrganizationStore] Create organization failed:', errorMsg);
          set({ 
            error: errorMsg, 
            isLoading: false 
          });
          return { success: false, error: errorMsg };
        }
      },

      /**
       * Update organization
       */
      updateOrganization: async (orgId: string, updates: Partial<Organization>) => {
        console.log('[OrganizationStore] 🔄 Updating organization:', orgId, updates);
        set({ isLoading: true, error: null });

        try {
          // TODO: Implement updateOrganization method in OrganizationService
          throw new Error('updateOrganization method not implemented');
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Failed to update organization';
          console.error('[OrganizationStore] ❌ Update organization failed:', errorMsg);
          set({ isLoading: false, error: errorMsg });
          return { success: false, error: errorMsg };
        }
      },

      /**
       * Load organization members
       */
      loadMembers: async () => {
        const currentOrg = get().currentOrganization;
        if (!currentOrg) {
          set({ members: [] });
          return;
        }
        
        try {
          const result = await window.organizationAPI.getMembers(currentOrg.external_id);
          
          if (result.success) {
            set({ members: result.data || [] });
          } else {
            set({ members: [], error: result.error || 'Failed to load members' });
          }
        } catch (error) {
          console.error('[OrganizationStore] Failed to load members:', error);
          set({ members: [], error: 'Failed to load members' });
        }
      },

      /**
       * Invite new member
       */
      inviteMember: async (email: string, role: string = 'member') => {
        const currentOrg = get().currentOrganization;
        if (!currentOrg) {
          return { success: false, error: 'No organization selected' };
        }
        
        try {
          const result = await window.organizationAPI.inviteMember({
            organizationId: currentOrg.external_id,
            email,
            role
          });
          
          if (result.success) {
            await get().loadMembers();
            return { success: true };
          } else {
            return { success: false, error: result.error || 'Failed to invite member' };
          }
        } catch (error) {
          console.error('[OrganizationStore] Failed to invite member:', error);
          return { success: false, error: 'Failed to invite member' };
        }
      },

      /**
       * Remove member
       */
      removeMember: async (userId: string) => {
        const currentOrg = get().currentOrganization;
        if (!currentOrg) {
          return { success: false, error: 'No organization selected' };
        }
        
        try {
          const result = await window.organizationAPI.removeMember({
            organizationId: currentOrg.external_id,
            userId
          });
          
          if (result.success) {
            await get().loadMembers();
            return { success: true };
          } else {
            return { success: false, error: result.error || 'Failed to remove member' };
          }
        } catch (error) {
          console.error('[OrganizationStore] Failed to remove member:', error);
          return { success: false, error: 'Failed to remove member' };
        }
      },

      /**
       * Update member role
       */
      updateMemberRole: async (userId: string, role: string) => {
        const currentOrg = get().currentOrganization;
        if (!currentOrg) {
          return { success: false, error: 'No organization selected' };
        }
        
        try {
          const result = await window.organizationAPI.updateMemberRole({
            organizationId: currentOrg.external_id,
            userId,
            role
          });
          
          if (result.success) {
            await get().loadMembers();
            return { success: true };
          } else {
            return { success: false, error: result.error || 'Failed to update member role' };
          }
        } catch (error) {
          console.error('[OrganizationStore] Failed to update member role:', error);
          return { success: false, error: 'Failed to update member role' };
        }
      },

      /**
       * Delete organization
       */
      deleteOrganization: async (orgId: string, forceCascade: boolean = false) => {
        set({ isLoading: true, error: null });
        
        try {
          console.log('[OrganizationStore] Deleting organization:', orgId, 'forceCascade:', forceCascade);
          const result = await window.organizationAPI.deleteOrganization(orgId, forceCascade);
          
          if (result.success) {
            // Remove from organizations list
            const updatedOrgs = get().organizations.filter(org => org.external_id !== orgId);
            set({ 
              organizations: updatedOrgs,
              isLoading: false 
            });
            
            // If this was the current organization, clear it and navigate to org selection
            if (get().currentOrganization?.external_id === orgId) {
              set({ currentOrganization: null });
              localStorage.removeItem('chromasync:lastOrganization');
              
              // Clear other stores using event bus
              storeEventBus.emit({ type: 'ORGANIZATION_CLEARED' });
            }
            
            console.log('[OrganizationStore] ✅ Organization deleted successfully');
            return { success: true };
          } else {
            set({ 
              error: result.error || 'Failed to delete organization', 
              isLoading: false 
            });
            return { success: false, error: result.error };
          }
        } catch (error) {
          const errorMsg = 'Failed to delete organization';
          console.error('[OrganizationStore]', errorMsg, error);
          set({ 
            error: errorMsg, 
            isLoading: false 
          });
          return { success: false, error: errorMsg };
        }
      },

      /**
       * Clear state
       */
      clearState: () => {
        set({
          currentOrganization: null,
          organizations: [],
          members: [],
          isLoading: false,
          error: null
        });
      }
    }),
    {
      name: 'organization-store-clean'
    }
  )
);

// Computed selectors
export const useCurrentOrganization = () => {
  return useOrganizationStore(state => state.currentOrganization);
};

export const useHasOrganization = () => {
  return useOrganizationStore(state => state.currentOrganization !== null);
};

// Selectors for backward compatibility and convenience
export const useCurrentOrg = () => useOrganizationStore(state => state.currentOrganization);
export const useOrganizations = () => useOrganizationStore(state => state.organizations);

// Export the store with both naming conventions
export const useOrganizationStoreWithAliases = () => {
  const store = useOrganizationStore();
  return {
    ...store,
    currentOrg: store.currentOrganization, // Alias for components expecting currentOrg
    // Add joinOrganization method
    joinOrganization: async (inviteCode: string) => {
      // TODO: Implement join by invite code
      console.log('Join organization with code:', inviteCode);
      return { success: false, error: 'Not implemented yet' };
    }
  };
};
