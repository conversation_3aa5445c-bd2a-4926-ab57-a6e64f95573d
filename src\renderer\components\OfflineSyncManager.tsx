import { useState, useEffect } from 'react';
import {
  Wifi,
  WifiOff,
  Ref<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>riangle,
  CheckCircle,
  Clock,
  Database,
  Trash2,
  Upload,
} from 'lucide-react';
import { syncQueue } from '../utils/syncQueue';
import { offlineStorage } from '../utils/offlineStorage';

interface OfflineSyncManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SyncStatus {
  isProcessing: boolean;
  pendingCount: number;
  lastSync: Date | null;
  isOnline: boolean;
}

interface StorageStats {
  offlineQueue: number;
  pendingSync: number;
  cachedColors: number;
  cachedProducts: number;
  lastSync: Date | null;
  isOnline: boolean;
}

export default function OfflineSyncManager({
  isOpen,
  onClose,
}: OfflineSyncManagerProps) {
  const [activeTab, setActiveTab] = useState<
    'status' | 'queue' | 'cache' | 'settings'
  >('status');
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [storageStats, setStorageStats] = useState<StorageStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [syncProgress, setSyncProgress] = useState<{
    total: number;
    completed: number;
    failed: number;
    current?: string;
  } | null>(null);
  const [lastError, setLastError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadData();
      const interval = setInterval(loadData, 2000); // Update every 2 seconds

      // Subscribe to sync progress
      const unsubscribe = syncQueue.addProgressCallback(progress => {
        setSyncProgress(progress);
      });

      return () => {
        clearInterval(interval);
        unsubscribe();
      };
    }

    return undefined;
  }, [isOpen]);

  const loadData = async () => {
    try {
      const [status, stats] = await Promise.all([
        syncQueue.getSyncStatus(),
        offlineStorage.getStorageStats(),
      ]);

      setSyncStatus(status);
      setStorageStats(stats);
    } catch (error) {
      console.error('Failed to load sync data:', error);
      setLastError(String(error));
    }
  };

  const handleForceSync = async () => {
    if (!syncStatus?.isOnline) {
      setLastError('Cannot sync while offline');
      return;
    }

    setIsLoading(true);
    setLastError(null);

    try {
      const result = await syncQueue.forceSyncNow();

      if (!result.success) {
        setLastError(`Sync failed: ${result.errors.join(', ')}`);
      }

      await loadData();
    } catch (error) {
      setLastError(String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearFailedSync = async () => {
    setIsLoading(true);
    setLastError(null);

    try {
      await syncQueue.clearFailedSync();
      await loadData();
    } catch (error) {
      setLastError(String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearCache = async () => {
    setIsLoading(true);
    setLastError(null);

    try {
      await offlineStorage.clearAllData();
      await loadData();
    } catch (error) {
      setLastError(String(error));
    } finally {
      setIsLoading(false);
    }
  };

  const getConnectionStatusIcon = () => {
    if (syncStatus?.isOnline) {
      return <Wifi className='w-5 h-5 text-green-500' />;
    } else {
      return <WifiOff className='w-5 h-5 text-red-500' />;
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) {
      return 'Never';
    }
    return date.toLocaleString();
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col'>
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center space-x-3'>
            <Database className='w-6 h-6 text-blue-500' />
            <h2 className='text-xl font-semibold dark:text-white'>
              Offline Sync Manager
            </h2>
            {getConnectionStatusIcon()}
            <span
              className={`text-sm font-medium ${
                syncStatus?.isOnline ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {syncStatus?.isOnline ? 'Online' : 'Offline'}
            </span>
          </div>
          <button
            onClick={onClose}
            className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
          >
            ×
          </button>
        </div>

        {/* Error Display */}
        {lastError && (
          <div className='mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg'>
            <div className='flex items-center space-x-2'>
              <AlertTriangle className='w-4 h-4 text-red-500' />
              <span className='text-sm text-red-700 dark:text-red-300'>
                {lastError}
              </span>
            </div>
          </div>
        )}

        {/* Sync Progress */}
        {syncProgress && (
          <div className='mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg'>
            <div className='flex items-center justify-between mb-2'>
              <span className='text-sm font-medium text-blue-700 dark:text-blue-300'>
                Syncing... {syncProgress.current}
              </span>
              <span className='text-sm text-blue-600 dark:text-blue-400'>
                {syncProgress.completed + syncProgress.failed}/
                {syncProgress.total}
              </span>
            </div>
            <div className='w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2'>
              <div
                className='bg-blue-500 h-2 rounded-full transition-all duration-300'
                style={{
                  width: `${((syncProgress.completed + syncProgress.failed) / syncProgress.total) * 100}%`,
                }}
              />
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className='flex space-x-4 mb-6 border-b'>
          {[
            { id: 'status', label: 'Status', icon: CheckCircle },
            { id: 'queue', label: 'Sync Queue', icon: Clock },
            { id: 'cache', label: 'Cache', icon: Database },
            { id: 'settings', label: 'Settings', icon: RefreshCw },
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors ${
                activeTab === id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              }`}
            >
              <Icon className='w-4 h-4' />
              <span>{label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className='flex-1 overflow-y-auto'>
          {activeTab === 'status' && syncStatus && storageStats && (
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              {/* Connection Status */}
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3 flex items-center space-x-2'>
                  {getConnectionStatusIcon()}
                  <span>Connection Status</span>
                </h3>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Status:
                    </span>
                    <span
                      className={`text-sm font-medium ${
                        syncStatus.isOnline ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {syncStatus.isOnline ? 'Online' : 'Offline'}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Last Sync:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {formatDate(syncStatus.lastSync)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Sync Active:
                    </span>
                    <span
                      className={`text-sm font-medium ${
                        syncStatus.isProcessing
                          ? 'text-blue-600'
                          : 'text-gray-600'
                      }`}
                    >
                      {syncStatus.isProcessing ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Pending Changes */}
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3 flex items-center space-x-2'>
                  <Clock className='w-4 h-4 text-yellow-500' />
                  <span>Pending Changes</span>
                </h3>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Pending Sync:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {syncStatus.pendingCount}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Total Queue:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {storageStats.offlineQueue}
                    </span>
                  </div>
                </div>
              </div>

              {/* Cache Stats */}
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3 flex items-center space-x-2'>
                  <Database className='w-4 h-4 text-blue-500' />
                  <span>Cached Data</span>
                </h3>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Colors:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {storageStats.cachedColors}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Products:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {storageStats.cachedProducts}
                    </span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3'>
                  Quick Actions
                </h3>
                <div className='space-y-2'>
                  <button
                    onClick={handleForceSync}
                    disabled={
                      !syncStatus.isOnline ||
                      isLoading ||
                      syncStatus.isProcessing
                    }
                    className='w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2'
                  >
                    <Upload className='w-4 h-4' />
                    <span>Force Sync Now</span>
                  </button>

                  <button
                    onClick={loadData}
                    disabled={isLoading}
                    className='w-full px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50 flex items-center justify-center space-x-2'
                  >
                    <RefreshCw className='w-4 h-4' />
                    <span>Refresh Status</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'queue' && (
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <h3 className='font-medium text-gray-900 dark:text-white'>
                  Sync Queue
                </h3>
                <button
                  onClick={handleClearFailedSync}
                  disabled={isLoading}
                  className='px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 flex items-center space-x-1'
                >
                  <Trash2 className='w-3 h-3' />
                  <span>Clear Failed</span>
                </button>
              </div>

              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <p className='text-sm text-gray-600 dark:text-gray-400 mb-4'>
                  Changes made while offline are queued for synchronization when
                  connection is restored.
                </p>

                {syncStatus && (
                  <div className='text-center py-8'>
                    <Clock className='w-12 h-12 text-gray-400 mx-auto mb-3' />
                    <p className='text-gray-600 dark:text-gray-400'>
                      {syncStatus.pendingCount > 0
                        ? `${syncStatus.pendingCount} changes pending sync`
                        : 'No pending changes'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'cache' && storageStats && (
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <h3 className='font-medium text-gray-900 dark:text-white'>
                  Cache Management
                </h3>
                <button
                  onClick={handleClearCache}
                  disabled={isLoading}
                  className='px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 flex items-center space-x-1'
                >
                  <Trash2 className='w-3 h-3' />
                  <span>Clear All</span>
                </button>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                  <h4 className='font-medium text-gray-900 dark:text-white mb-2'>
                    Colors Cache
                  </h4>
                  <p className='text-2xl font-bold text-blue-600 dark:text-blue-400'>
                    {storageStats.cachedColors}
                  </p>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>
                    cached colors
                  </p>
                </div>

                <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                  <h4 className='font-medium text-gray-900 dark:text-white mb-2'>
                    Products Cache
                  </h4>
                  <p className='text-2xl font-bold text-green-600 dark:text-green-400'>
                    {storageStats.cachedProducts}
                  </p>
                  <p className='text-sm text-gray-600 dark:text-gray-400'>
                    cached products
                  </p>
                </div>
              </div>

              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h4 className='font-medium text-gray-900 dark:text-white mb-2'>
                  Cache Info
                </h4>
                <p className='text-sm text-gray-600 dark:text-gray-400'>
                  Cached data allows the application to work offline. Data is
                  automatically synchronized when connection is restored.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className='space-y-4'>
              <h3 className='font-medium text-gray-900 dark:text-white'>
                Sync Settings
              </h3>

              <div className='space-y-4'>
                <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                  <h4 className='font-medium text-gray-900 dark:text-white mb-2'>
                    Auto Sync
                  </h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400 mb-3'>
                    Automatically sync changes when connection is available
                  </p>
                  <label className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      defaultChecked
                      className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                    />
                    <span className='text-sm dark:text-white'>
                      Enable auto sync
                    </span>
                  </label>
                </div>

                <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                  <h4 className='font-medium text-gray-900 dark:text-white mb-2'>
                    Sync Interval
                  </h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400 mb-3'>
                    How often to check for pending changes
                  </p>
                  <select
                    className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white'
                    defaultValue='300'
                  >
                    <option value='60'>1 minute</option>
                    <option value='300'>5 minutes</option>
                    <option value='600'>10 minutes</option>
                    <option value='1800'>30 minutes</option>
                  </select>
                </div>

                <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                  <h4 className='font-medium text-gray-900 dark:text-white mb-2'>
                    Retry Settings
                  </h4>
                  <p className='text-sm text-gray-600 dark:text-gray-400 mb-3'>
                    Number of retry attempts for failed sync operations
                  </p>
                  <select
                    className='w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-800 dark:text-white'
                    defaultValue='3'
                  >
                    <option value='1'>1 retry</option>
                    <option value='3'>3 retries</option>
                    <option value='5'>5 retries</option>
                    <option value='10'>10 retries</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className='flex justify-between items-center mt-6 pt-4 border-t'>
          <div className='text-sm text-gray-500'>
            {storageStats && `Last updated: ${formatDate(new Date())}`}
          </div>
          <button
            onClick={onClose}
            className='px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
