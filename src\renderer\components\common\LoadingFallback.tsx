/**
 * @file LoadingFallback.tsx
 * @description Loading fallback component for lazy-loaded components
 */

import React from 'react';

interface LoadingFallbackProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

/**
 * Loading fallback component
 */
export const LoadingFallback: React.FC<LoadingFallbackProps> = ({
  message = 'Loading...',
  size = 'medium',
  className = '',
}) => {
  const sizeClasses = {
    small: 'h-32',
    medium: 'h-64',
    large: 'h-screen',
  };

  const spinnerSizes = {
    small: 'h-6 w-6',
    medium: 'h-8 w-8',
    large: 'h-12 w-12',
  };

  return (
    <div
      className={`flex items-center justify-center bg-ui-background-primary ${sizeClasses[size]} ${className}`}
    >
      <div className='flex items-center space-x-3'>
        <div
          className={`animate-spin rounded-full border-b-2 border-brand-primary ${spinnerSizes[size]}`}
        />
        <span className='text-ui-foreground-secondary'>{message}</span>
      </div>
    </div>
  );
};

export default LoadingFallback;
