/**
 * @file SettingsModal.tsx
 * @description Settings modal with modular tab-specific components
 */

import React, { useState } from 'react';
import Modal from '../ui/Modal';
import { useOrganizationStoreWithAliases } from '../../store/organization.store';
import { Settings, Database, AlertTriangle, Users } from 'lucide-react';
import GeneralSettingsTab from './tabs/GeneralSettingsTab';
import AdvancedSettingsTab from './tabs/AdvancedSettingsTab';
import TeamSettingsTab from './tabs/TeamSettingsTab';

type SettingsTab = 'general' | 'advanced' | 'team';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialTab?: SettingsTab;
}

/**
 * Refactored Settings Modal with modular tab architecture
 */
const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose,
  initialTab,
}) => {
  const { currentOrg, loadCurrentOrganization } =
    useOrganizationStoreWithAliases();
  const [activeTab, setActiveTab] = useState<SettingsTab>(
    initialTab || 'general'
  );

  // Load organization when modal opens and handle initial tab
  React.useEffect(() => {
    if (isOpen) {
      if (!currentOrg) {
        console.log('[SettingsModal] Modal opened but no org, loading...');
        loadCurrentOrganization();
      }
      if (initialTab) {
        setActiveTab(initialTab);
      }
    }
  }, [isOpen, currentOrg, loadCurrentOrganization, initialTab]);

  // Tab button styling
  const getTabButtonStyle = (tab: SettingsTab) => {
    const isActive = activeTab === tab;
    return {
      backgroundColor: isActive
        ? 'var(--color-ui-background-tertiary)'
        : 'transparent',
      color: isActive
        ? 'var(--color-ui-foreground-primary)'
        : 'var(--color-ui-foreground-secondary)',
      borderRadius: 'var(--radius-md)',
      fontSize: 'var(--font-size-sm)',
      fontWeight: 'var(--font-weight-medium)',
      transition: 'background-color 200ms ease-in-out, color 200ms ease-in-out',
    };
  };

  // Render active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return <GeneralSettingsTab />;
      case 'advanced':
        return <AdvancedSettingsTab />;
      case 'team':
        return <TeamSettingsTab />;
      default:
        return <GeneralSettingsTab />;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title='Settings'
      maxWidth='max-w-4xl'
    >
      <div className='flex h-[70vh] max-h-[600px]'>
        {/* Sidebar Navigation */}
        <div
          className='w-48 p-4'
          style={{
            borderRight: `1px solid var(--color-ui-border-light)`,
          }}
        >
          <nav className='space-y-1'>
            {/* General Tab */}
            <button
              className='flex items-center px-4 py-2'
              style={getTabButtonStyle('general')}
              onClick={() => setActiveTab('general')}
              onMouseEnter={e => {
                if (activeTab !== 'general') {
                  e.currentTarget.style.color =
                    'var(--color-ui-foreground-primary)';
                }
              }}
              onMouseLeave={e => {
                if (activeTab !== 'general') {
                  e.currentTarget.style.color =
                    'var(--color-ui-foreground-secondary)';
                }
              }}
            >
              <Settings size={16} className='mr-2' />
              <span>General</span>
            </button>

            {/* Team Tab (only show if organization exists) */}
            {currentOrg && (
              <button
                className='flex items-center px-4 py-2'
                style={getTabButtonStyle('team')}
                onClick={() => setActiveTab('team')}
                onMouseEnter={e => {
                  if (activeTab !== 'team') {
                    e.currentTarget.style.color =
                      'var(--color-ui-foreground-primary)';
                  }
                }}
                onMouseLeave={e => {
                  if (activeTab !== 'team') {
                    e.currentTarget.style.color =
                      'var(--color-ui-foreground-secondary)';
                  }
                }}
              >
                <Users size={16} className='mr-2' />
                <span>Team</span>
              </button>
            )}

            {/* Placeholder for future database tab */}
            <button
              className='flex items-center px-4 py-2 cursor-not-allowed'
              style={{
                color: 'var(--color-ui-foreground-tertiary)',
                fontSize: 'var(--font-size-sm)',
                fontWeight: 'var(--font-weight-medium)',
              }}
            >
              <Database size={16} className='mr-2' />
              <span>Database</span>
            </button>

            {/* Advanced Tab */}
            <button
              className='flex items-center px-4 py-2'
              style={getTabButtonStyle('advanced')}
              onClick={() => setActiveTab('advanced')}
              onMouseEnter={e => {
                if (activeTab !== 'advanced') {
                  e.currentTarget.style.color =
                    'var(--color-ui-foreground-primary)';
                }
              }}
              onMouseLeave={e => {
                if (activeTab !== 'advanced') {
                  e.currentTarget.style.color =
                    'var(--color-ui-foreground-secondary)';
                }
              }}
            >
              <AlertTriangle size={16} className='mr-2' />
              <span>Advanced</span>
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className='flex-1 p-6 overflow-y-auto'>{renderTabContent()}</div>
      </div>

      {/* Footer */}
      <div
        className='px-6 py-4 flex justify-end'
        style={{
          borderTop: `1px solid var(--color-ui-border-light)`,
        }}
      >
        <button
          onClick={onClose}
          className='px-4 py-2 transition-standard'
          style={{
            backgroundColor: 'var(--button-primary-bg)',
            color: 'var(--button-primary-text)',
            border: 'none',
            borderRadius: 'var(--radius-md)',
            fontSize: 'var(--font-size-sm)',
            fontWeight: 'var(--font-weight-medium)',
            cursor: 'pointer',
          }}
          onMouseEnter={e => {
            e.currentTarget.style.backgroundColor =
              'var(--button-primary-bg-hover)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.backgroundColor = 'var(--button-primary-bg)';
          }}
          onFocus={e => {
            e.target.style.outline = 'none';
            e.target.style.boxShadow = `0 0 0 2px var(--form-border-focus)`;
          }}
          onBlur={e => {
            e.target.style.boxShadow = 'none';
          }}
        >
          Close
        </button>
      </div>
    </Modal>
  );
};

export default SettingsModal;
