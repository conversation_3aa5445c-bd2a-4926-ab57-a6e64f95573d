/**
 * @file color.repository.test.ts
 * @description Unit tests for ColorRepository data access layer
 *
 * Tests all database operations that will be extracted from ColorService
 * into a dedicated repository following the Repository pattern.
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';
import { ColorRepository } from '../color.repository';
import {
  NewColorEntry,
  UpdateColorEntry,
} from '../../../../shared/types/color.types';

describe.sequential('ColorRepository', () => {
  let db: Database.Database;
  let repository: ColorRepository;
  let mockOrganizationId: string;

  beforeEach(() => {
    // Create in-memory SQLite database for testing
    db = new Database(':memory:');

    // Set up test schema
    setupTestSchema(db);

    // Create repository instance
    repository = new ColorRepository(db);

    mockOrganizationId = '550e8400-e29b-41d4-a716-************'; // Valid UUID format

    // Seed test data
    seedTestData(db, mockOrganizationId);
  });

  afterEach(() => {
    if (db && db.open) {
      try {
        db.close();
      } catch (error) {
        // Database might already be closed, ignore the error
        console.warn('Database close error:', error);
      }
    }
  });

  describe('Core CRUD Operations', () => {
    describe('findAll', () => {
      test('should return all colors for organization', () => {
        const colors = repository.findAll(mockOrganizationId);

        expect(colors).toBeInstanceOf(Array);
        expect(colors.length).toBeGreaterThan(0);
        expect(colors[0]).toHaveProperty('id');
        expect(colors[0]).toHaveProperty('code');
        expect(colors[0]).toHaveProperty('hex');
        expect(colors[0]).toHaveProperty('organization_id', mockOrganizationId);
      });

      test('should return empty array for non-existent organization', () => {
        const colors = repository.findAll(
          '550e8400-e29b-41d4-a716-************'
        ); // Different UUID

        expect(colors).toBeInstanceOf(Array);
        expect(colors).toHaveLength(0);
      });

      test('should include product relationships when available', () => {
        const colors = repository.findAll(mockOrganizationId);

        // Should include colors with product relationships
        const colorsWithProducts = colors.filter(color => color.product_name);
        expect(colorsWithProducts.length).toBeGreaterThan(0);
      });

      test('should exclude soft-deleted colors', () => {
        // Soft delete a color using external_id
        const externalId = 'ext-color-1';
        repository.softDelete(externalId, mockOrganizationId);

        const colors = repository.findAll(mockOrganizationId);
        const deletedColor = colors.find(
          color => color.external_id === externalId
        );

        expect(deletedColor).toBeUndefined();
      });

      test('should order results consistently', () => {
        const colors = repository.findAll(mockOrganizationId);

        // Should be ordered by code, then by product name
        for (let i = 1; i < colors.length; i++) {
          if (colors[i - 1].code === colors[i].code) {
            // Same code, should be ordered by product name
            expect(colors[i - 1].product_name <= colors[i].product_name).toBe(
              true
            );
          } else {
            // Different codes, should be ordered by code
            expect(colors[i - 1].code <= colors[i].code).toBe(true);
          }
        }
      });
    });

    describe('findById', () => {
      test('should return color by ID for organization', () => {
        const externalId = 'ext-color-1'; // Use external_id from test data
        const color = repository.findById(externalId, mockOrganizationId);

        expect(color).toBeTruthy();
        expect(color.external_id).toBe(externalId);
        expect(color.organization_id).toBe(mockOrganizationId);
      });

      test('should return null for non-existent color', () => {
        const color = repository.findById('non-existent', mockOrganizationId);

        expect(color).toBeNull();
      });

      test('should return null for wrong organization', () => {
        const externalId = 'ext-color-1';
        const color = repository.findById(
          externalId,
          '550e8400-e29b-41d4-a716-446655440002'
        );

        expect(color).toBeNull();
      });

      test('should return null for soft-deleted color', () => {
        const externalId = 'ext-color-1';

        // Soft delete the color
        repository.softDelete(externalId, mockOrganizationId);

        const color = repository.findById(externalId, mockOrganizationId);
        expect(color).toBeNull();
      });
    });

    describe('insert', () => {
      test('should insert new color and return ID', () => {
        const newColor: NewColorEntry = {
          code: 'TEST001',
          name: 'Test Color',
          hex: '#FF0000',
          cmyk: 'C:0 M:100 Y:100 K:0',
          product: 'Test Product',
          notes: 'Test color',
          tags: 'test,red',
        };

        const colorId = repository.insert(newColor, mockOrganizationId);

        expect(colorId).toBeTruthy();
        expect(typeof colorId).toBe('string');

        // Verify the color was inserted
        const insertedColor = repository.findById(colorId, mockOrganizationId);
        expect(insertedColor).toBeTruthy();
        expect(insertedColor.code).toBe(newColor.code);
        expect(insertedColor.hex).toBe(newColor.hex);
        expect(insertedColor.display_name).toBe(newColor.name);
      });

      test('should generate UUID for external_id if not provided', () => {
        const newColor: NewColorEntry = {
          code: 'TEST002',
          name: 'Test Color 2',
          hex: '#00FF00',
          cmyk: 'C:100 M:0 Y:100 K:0',
          product: 'Test Product 2',
        };

        const colorId = repository.insert(newColor, mockOrganizationId);
        const insertedColor = repository.findById(colorId, mockOrganizationId);

        expect(insertedColor.external_id).toBeTruthy();
        expect(insertedColor.external_id).toMatch(/^[0-9a-f-]{36}$/); // UUID format
      });

      test('should use provided external_id', () => {
        const externalId = 'custom-external-id';
        const newColor: any = {
          code: 'TEST003',
          name: 'Test Color 3',
          hex: '#0000FF',
          cmyk: 'C:100 M:100 Y:0 K:0',
          product: 'Test Product 3',
          external_id: externalId,
        };

        const returnedId = repository.insert(newColor, mockOrganizationId);
        const insertedColor = repository.findById(
          returnedId,
          mockOrganizationId
        );

        expect(returnedId).toBe(externalId);
        expect(insertedColor.external_id).toBe(externalId);
      });

      test('should set default values for optional fields', () => {
        const newColor: NewColorEntry = {
          code: 'TEST004',
          name: 'Test Color 4',
          hex: '#FFFF00',
          cmyk: 'C:0 M:0 Y:100 K:0',
          product: 'Test Product 4',
        };

        const colorId = repository.insert(newColor, mockOrganizationId);
        const insertedColor = repository.findById(colorId, mockOrganizationId);

        expect(insertedColor.is_gradient).toBe(0);
        expect(insertedColor.is_metallic).toBe(0);
        expect(insertedColor.is_effect).toBe(0);
        expect(insertedColor.is_library).toBe(0);
        expect(insertedColor.is_synced).toBe(0);
      });

      test('should handle gradient colors with color_spaces JSON', () => {
        const gradientColorSpaces = JSON.stringify({
          cmyk: { c: 0, m: 50, y: 100, k: 0 },
          gradient: {
            stops: [
              {
                color: '#FF0000',
                position: 0,
                cmyk: { c: 0, m: 100, y: 100, k: 0 },
              },
              {
                color: '#0000FF',
                position: 100,
                cmyk: { c: 100, m: 100, y: 0, k: 0 },
              },
            ],
            angle: 45,
          },
        });

        const newColor: NewColorEntry = {
          code: 'GRAD001',
          display_name: 'Test Gradient',
          hex: '#FF0000',
          is_gradient: true,
          color_spaces: gradientColorSpaces,
          gradient_colors: '#FF0000,#0000FF',
        };

        const colorId = repository.insert(newColor, mockOrganizationId);
        const insertedColor = repository.findById(colorId, mockOrganizationId);

        expect(insertedColor.is_gradient).toBe(true);
        expect(insertedColor.color_spaces).toBe(gradientColorSpaces);
        expect(insertedColor.gradient_colors).toBe('#FF0000,#0000FF');
      });
    });

    describe('update', () => {
      test('should update existing color fields', () => {
        const colorId = 'test-color-1';
        const updates: UpdateColorEntry = {
          display_name: 'Updated Display Name',
          hex: '#FF9900',
          notes: 'Updated notes',
          is_metallic: true,
        };

        const success = repository.update(colorId, updates, mockOrganizationId);

        expect(success).toBe(true);

        const updatedColor = repository.findById(colorId, mockOrganizationId);
        expect(updatedColor.display_name).toBe(updates.display_name);
        expect(updatedColor.hex).toBe(updates.hex);
        expect(updatedColor.notes).toBe(updates.notes);
        expect(updatedColor.is_metallic).toBe(true);
      });

      test('should return false for non-existent color', () => {
        const updates: UpdateColorEntry = { display_name: 'Updated' };
        const success = repository.update(
          'non-existent',
          updates,
          mockOrganizationId
        );

        expect(success).toBe(false);
      });

      test('should return false for wrong organization', () => {
        const colorId = 'test-color-1';
        const updates: UpdateColorEntry = { display_name: 'Updated' };
        const success = repository.update(
          colorId,
          updates,
          '550e8400-e29b-41d4-a716-446655440003'
        );

        expect(success).toBe(false);
      });

      test('should update timestamps', () => {
        const colorId = 'test-color-1';
        const originalColor = repository.findById(colorId, mockOrganizationId);

        // Wait a bit to ensure different timestamp
        const start = Date.now();
        const updates: UpdateColorEntry = { display_name: 'Updated' };
        repository.update(colorId, updates, mockOrganizationId);

        const updatedColor = repository.findById(colorId, mockOrganizationId);
        expect(
          new Date(updatedColor.updated_at).getTime()
        ).toBeGreaterThanOrEqual(start);
      });

      test('should handle partial updates', () => {
        const colorId = 'test-color-1';
        const originalColor = repository.findById(colorId, mockOrganizationId);

        const updates: UpdateColorEntry = { notes: 'Only updating notes' };
        repository.update(colorId, updates, mockOrganizationId);

        const updatedColor = repository.findById(colorId, mockOrganizationId);
        expect(updatedColor.notes).toBe(updates.notes);
        expect(updatedColor.code).toBe(originalColor.code); // Should remain unchanged
        expect(updatedColor.hex).toBe(originalColor.hex); // Should remain unchanged
      });
    });

    describe('softDelete', () => {
      test('should soft delete color', () => {
        const colorId = 'test-color-1';

        const success = repository.softDelete(colorId, mockOrganizationId);
        expect(success).toBe(true);

        // Color should not appear in normal queries
        const color = repository.findById(colorId, mockOrganizationId);
        expect(color).toBeNull();

        const allColors = repository.findAll(mockOrganizationId);
        const deletedColor = allColors.find(c => c.id === colorId);
        expect(deletedColor).toBeUndefined();
      });

      test('should return false for non-existent color', () => {
        const success = repository.softDelete(
          'non-existent',
          mockOrganizationId
        );
        expect(success).toBe(false);
      });

      test('should return false for wrong organization', () => {
        const colorId = 'test-color-1';
        const success = repository.softDelete(
          colorId,
          '550e8400-e29b-41d4-a716-446655440004'
        );
        expect(success).toBe(false);
      });

      test('should set deleted_at timestamp', () => {
        const colorId = 'test-color-1';
        const beforeDelete = Date.now();

        repository.softDelete(colorId, mockOrganizationId);

        // Check soft deleted record directly
        const stmt = db.prepare('SELECT deleted_at FROM colors WHERE id = ?');
        const result = stmt.get(colorId);

        expect(result.deleted_at).toBeTruthy();
        expect(new Date(result.deleted_at).getTime()).toBeGreaterThanOrEqual(
          beforeDelete
        );
      });
    });
  });

  describe('Query Operations', () => {
    describe('getUsageCounts', () => {
      test('should return usage counts for colors', () => {
        const usageCounts = repository.getUsageCounts(mockOrganizationId);

        expect(usageCounts).toBeInstanceOf(Map);
        expect(usageCounts.size).toBeGreaterThan(0);

        // Check structure of usage count data
        const firstEntry = usageCounts.values().next().value;
        expect(firstEntry).toHaveProperty('count');
        expect(firstEntry).toHaveProperty('products');
        expect(typeof firstEntry.count).toBe('number');
        expect(firstEntry.products).toBeInstanceOf(Array);
      });

      test('should return empty map for organization with no colors', () => {
        const usageCounts = repository.getUsageCounts(
          '550e8400-e29b-41d4-a716-446655440005'
        );

        expect(usageCounts).toBeInstanceOf(Map);
        expect(usageCounts.size).toBe(0);
      });

      test('should count products correctly for each color', () => {
        // Add specific test data for counting
        const colorId = repository.insert(
          {
            code: 'COUNT001',
            display_name: 'Count Test',
            hex: '#123456',
          },
          mockOrganizationId
        );

        // Create products and relationships
        const productId1 = 'prod-1';
        const productId2 = 'prod-2';

        // Insert test products (assuming products table exists)
        db.prepare(
          `
          INSERT INTO products (id, name, organization_id, is_active) 
          VALUES (?, ?, ?, 1)
        `
        ).run(productId1, 'Product 1', mockOrganizationId);

        db.prepare(
          `
          INSERT INTO products (id, name, organization_id, is_active) 
          VALUES (?, ?, ?, 1)
        `
        ).run(productId2, 'Product 2', mockOrganizationId);

        // Create color-product relationships
        db.prepare(
          `
          INSERT INTO product_colors (product_id, color_id, organization_id) 
          VALUES (?, ?, ?)
        `
        ).run(productId1, colorId, mockOrganizationId);

        db.prepare(
          `
          INSERT INTO product_colors (product_id, color_id, organization_id) 
          VALUES (?, ?, ?)
        `
        ).run(productId2, colorId, mockOrganizationId);

        const usageCounts = repository.getUsageCounts(mockOrganizationId);
        const colorUsage = usageCounts.get('Count Test');

        expect(colorUsage).toBeTruthy();
        expect(colorUsage.count).toBe(2);
        expect(colorUsage.products).toContain('Product 1');
        expect(colorUsage.products).toContain('Product 2');
      });
    });

    describe('getColorNameProductMap', () => {
      test('should return map of color names to product arrays', () => {
        const colorProductMap =
          repository.getColorNameProductMap(mockOrganizationId);

        expect(colorProductMap).toBeInstanceOf(Map);
        expect(colorProductMap.size).toBeGreaterThan(0);

        // Check structure
        for (const [colorName, products] of colorProductMap) {
          expect(typeof colorName).toBe('string');
          expect(products).toBeInstanceOf(Array);
        }
      });

      test('should group products by color name correctly', () => {
        // Create specific test data
        const colorId = repository.insert(
          {
            code: 'MAP001',
            display_name: 'Map Test Color',
            hex: '#ABCDEF',
          },
          mockOrganizationId
        );

        // Create products
        const productIds = ['map-prod-1', 'map-prod-2'];
        productIds.forEach((prodId, index) => {
          db.prepare(
            `
            INSERT INTO products (id, name, organization_id, is_active) 
            VALUES (?, ?, ?, 1)
          `
          ).run(prodId, `Map Product ${index + 1}`, mockOrganizationId);

          db.prepare(
            `
            INSERT INTO product_colors (product_id, color_id, organization_id) 
            VALUES (?, ?, ?)
          `
          ).run(prodId, colorId, mockOrganizationId);
        });

        const colorProductMap =
          repository.getColorNameProductMap(mockOrganizationId);
        const products = colorProductMap.get('Map Test Color');

        expect(products).toBeTruthy();
        expect(products).toHaveLength(2);
        expect(products).toContain('Map Product 1');
        expect(products).toContain('Map Product 2');
      });
    });

    describe('findUnsynced', () => {
      test('should return colors that need syncing', () => {
        // Mark a color as unsynced
        const colorId = 'test-color-1';
        db.prepare('UPDATE colors SET is_synced = 0 WHERE id = ?').run(colorId);

        const unsyncedColors = repository.findUnsynced();

        expect(unsyncedColors).toBeInstanceOf(Array);
        const unsyncedColor = unsyncedColors.find(c => c.id === colorId);
        expect(unsyncedColor).toBeTruthy();
        expect(unsyncedColor.is_synced).toBe(false);
      });

      test('should not return synced colors', () => {
        // Ensure all colors are synced
        db.prepare('UPDATE colors SET is_synced = 1').run();

        const unsyncedColors = repository.findUnsynced();
        expect(unsyncedColors).toHaveLength(0);
      });

      test('should not return soft-deleted colors', () => {
        const colorId = 'test-color-1';

        // Make unsynced and soft delete
        db.prepare('UPDATE colors SET is_synced = 0 WHERE id = ?').run(colorId);
        repository.softDelete(colorId, mockOrganizationId);

        const unsyncedColors = repository.findUnsynced();
        const deletedColor = unsyncedColors.find(c => c.id === colorId);
        expect(deletedColor).toBeUndefined();
      });
    });
  });

  describe('Soft Delete Operations', () => {
    describe('findSoftDeleted', () => {
      test('should return soft deleted colors', () => {
        const colorId = 'test-color-1';
        repository.softDelete(colorId, mockOrganizationId);

        const softDeleted = repository.findSoftDeleted(mockOrganizationId);

        expect(softDeleted).toBeInstanceOf(Array);
        expect(softDeleted.length).toBeGreaterThan(0);

        const deletedColor = softDeleted.find(c => c.id === colorId);
        expect(deletedColor).toBeTruthy();
        expect(deletedColor.deleted_at).toBeTruthy();
      });

      test('should respect limit parameter', () => {
        // Create and soft delete multiple colors
        const colorIds = [];
        for (let i = 0; i < 5; i++) {
          const colorId = repository.insert(
            {
              code: `DEL${i}`,
              display_name: `Delete Test ${i}`,
              hex: '#000000',
            },
            mockOrganizationId
          );
          colorIds.push(colorId);
          repository.softDelete(colorId, mockOrganizationId);
        }

        const softDeleted = repository.findSoftDeleted(mockOrganizationId, 3);
        expect(softDeleted).toHaveLength(3);
      });

      test('should respect offset parameter', () => {
        // Ensure we have enough soft deleted colors
        const allSoftDeleted = repository.findSoftDeleted(mockOrganizationId);

        if (allSoftDeleted.length >= 2) {
          const offsetResults = repository.findSoftDeleted(
            mockOrganizationId,
            1,
            1
          );
          expect(offsetResults).toHaveLength(1);
          expect(offsetResults[0].id).not.toBe(allSoftDeleted[0].id);
        }
      });

      test('should return empty array for organization with no soft deleted colors', () => {
        const softDeleted = repository.findSoftDeleted('clean-org');
        expect(softDeleted).toHaveLength(0);
      });
    });

    describe('restoreRecord', () => {
      test('should restore soft deleted color', () => {
        const colorId = 'test-color-1';

        // Soft delete first
        repository.softDelete(colorId, mockOrganizationId);
        expect(repository.findById(colorId, mockOrganizationId)).toBeNull();

        // Restore
        const success = repository.restoreRecord(colorId, mockOrganizationId);
        expect(success).toBe(true);

        // Should be available again
        const restoredColor = repository.findById(colorId, mockOrganizationId);
        expect(restoredColor).toBeTruthy();
        expect(restoredColor.id).toBe(colorId);
      });

      test('should return false for non-existent color', () => {
        const success = repository.restoreRecord(
          'non-existent',
          mockOrganizationId
        );
        expect(success).toBe(false);
      });

      test('should return false for wrong organization', () => {
        const colorId = 'test-color-1';
        repository.softDelete(colorId, mockOrganizationId);

        const success = repository.restoreRecord(colorId, 'wrong-org');
        expect(success).toBe(false);
      });

      test('should clear deleted_at timestamp', () => {
        const colorId = 'test-color-1';

        repository.softDelete(colorId, mockOrganizationId);
        repository.restoreRecord(colorId, mockOrganizationId);

        const stmt = db.prepare('SELECT deleted_at FROM colors WHERE id = ?');
        const result = stmt.get(colorId);
        expect(result.deleted_at).toBeNull();
      });
    });

    describe('bulkRestoreRecords', () => {
      test('should restore multiple colors', () => {
        const colorIds = ['test-color-1', 'test-color-2'];

        // Soft delete both
        colorIds.forEach(id => repository.softDelete(id, mockOrganizationId));

        const result = repository.bulkRestoreRecords(
          colorIds,
          mockOrganizationId
        );

        expect(result.success).toBe(true);
        expect(result.restored).toBe(2);

        // Both should be restored
        colorIds.forEach(id => {
          const color = repository.findById(id, mockOrganizationId);
          expect(color).toBeTruthy();
        });
      });

      test('should handle empty array', () => {
        const result = repository.bulkRestoreRecords([], mockOrganizationId);

        expect(result.success).toBe(true);
        expect(result.restored).toBe(0);
      });

      test('should handle mix of valid and invalid IDs', () => {
        const colorId = 'test-color-1';
        repository.softDelete(colorId, mockOrganizationId);

        const colorIds = [colorId, 'non-existent'];
        const result = repository.bulkRestoreRecords(
          colorIds,
          mockOrganizationId
        );

        expect(result.success).toBe(true);
        expect(result.restored).toBe(1); // Only one valid restore
      });
    });

    describe('cleanupOldSoftDeleted', () => {
      test('should remove old soft deleted records', () => {
        const colorId = repository.insert(
          {
            code: 'OLD001',
            display_name: 'Old Color',
            hex: '#999999',
          },
          mockOrganizationId
        );

        // Soft delete and manually set old timestamp
        repository.softDelete(colorId, mockOrganizationId);

        const oldDate = new Date();
        oldDate.setDate(oldDate.getDate() - 35); // 35 days ago

        db.prepare('UPDATE colors SET deleted_at = ? WHERE id = ?').run(
          oldDate.toISOString(),
          colorId
        );

        const result = repository.cleanupOldSoftDeleted(mockOrganizationId, 30);

        expect(result.success).toBe(true);
        expect(result.cleaned).toBeGreaterThan(0);

        // Color should be completely removed
        const stmt = db.prepare('SELECT * FROM colors WHERE id = ?');
        const hardDeletedColor = stmt.get(colorId);
        expect(hardDeletedColor).toBeUndefined();
      });

      test('should not remove recent soft deleted records', () => {
        const colorId = 'test-color-1';
        repository.softDelete(colorId, mockOrganizationId);

        const result = repository.cleanupOldSoftDeleted(mockOrganizationId, 30);

        // Should still exist in soft deleted state
        const softDeleted = repository.findSoftDeleted(mockOrganizationId);
        const stillExists = softDeleted.find(c => c.id === colorId);
        expect(stillExists).toBeTruthy();
      });

      test('should use default days if not specified', () => {
        const result = repository.cleanupOldSoftDeleted(mockOrganizationId);

        expect(result.success).toBe(true);
        expect(typeof result.cleaned).toBe('number');
      });
    });
  });

  describe('Bulk Operations', () => {
    describe('clearAll', () => {
      test('should soft delete all colors when hardDelete is false', () => {
        const beforeCount = repository.findAll(mockOrganizationId).length;
        expect(beforeCount).toBeGreaterThan(0);

        const success = repository.clearAll(mockOrganizationId, false);
        expect(success).toBe(true);

        const afterCount = repository.findAll(mockOrganizationId).length;
        expect(afterCount).toBe(0);

        // Should still exist in soft deleted state
        const softDeletedCount =
          repository.findSoftDeleted(mockOrganizationId).length;
        expect(softDeletedCount).toBe(beforeCount);
      });

      test('should hard delete all colors when hardDelete is true', () => {
        const beforeCount = repository.findAll(mockOrganizationId).length;
        expect(beforeCount).toBeGreaterThan(0);

        const success = repository.clearAll(mockOrganizationId, true);
        expect(success).toBe(true);

        const afterCount = repository.findAll(mockOrganizationId).length;
        expect(afterCount).toBe(0);

        // Should not exist in soft deleted state either
        const softDeletedCount =
          repository.findSoftDeleted(mockOrganizationId).length;
        expect(softDeletedCount).toBe(0);
      });

      test('should only affect specified organization', () => {
        const otherOrgId = 'other-org-123';

        // Create color in other organization
        const otherColorId = repository.insert(
          {
            code: 'OTHER001',
            display_name: 'Other Org Color',
            hex: '#ABCDEF',
          },
          otherOrgId
        );

        repository.clearAll(mockOrganizationId, true);

        // Other org color should still exist
        const otherColor = repository.findById(otherColorId, otherOrgId);
        expect(otherColor).toBeTruthy();
      });
    });

    describe('invalidateOrphans', () => {
      test('should soft delete colors with no product relationships', () => {
        // Create an orphan color (no product relationships)
        const orphanId = repository.insert(
          {
            code: 'ORPHAN001',
            display_name: 'Orphan Color',
            hex: '#DEADBE',
            is_library: false,
          },
          mockOrganizationId
        );

        repository.invalidateOrphans(mockOrganizationId);

        // Orphan should be soft deleted
        const orphan = repository.findById(orphanId, mockOrganizationId);
        expect(orphan).toBeNull();

        // Should appear in soft deleted
        const softDeleted = repository.findSoftDeleted(mockOrganizationId);
        const softDeletedOrphan = softDeleted.find(c => c.id === orphanId);
        expect(softDeletedOrphan).toBeTruthy();
      });

      test('should not invalidate library colors', () => {
        const libraryColorId = repository.insert(
          {
            code: 'LIB001',
            display_name: 'Library Color',
            hex: '#FACADE',
            is_library: true,
          },
          mockOrganizationId
        );

        repository.invalidateOrphans(mockOrganizationId);

        // Library color should remain
        const libraryColor = repository.findById(
          libraryColorId,
          mockOrganizationId
        );
        expect(libraryColor).toBeTruthy();
      });

      test('should not invalidate colors with product relationships', () => {
        // Test colors with relationships should remain
        const colorsWithProducts = repository
          .findAll(mockOrganizationId)
          .filter(c => c.product_name);

        expect(colorsWithProducts.length).toBeGreaterThan(0);

        repository.invalidateOrphans(mockOrganizationId);

        // All should still exist
        colorsWithProducts.forEach(color => {
          const stillExists = repository.findById(color.id, mockOrganizationId);
          expect(stillExists).toBeTruthy();
        });
      });
    });
  });

  describe('Utility Methods', () => {
    describe('markAsSynced', () => {
      test('should mark color as synced', () => {
        const colorId = 'test-color-1';

        // Ensure it's unsynced first
        db.prepare('UPDATE colors SET is_synced = 0 WHERE id = ?').run(colorId);

        repository.markAsSynced(colorId);

        const color = repository.findById(colorId, mockOrganizationId);
        expect(color.is_synced).toBe(true);
      });

      test('should handle non-existent color gracefully', () => {
        expect(() => {
          repository.markAsSynced('non-existent');
        }).not.toThrow();
      });
    });

    describe('getPreparedStatement', () => {
      test('should return prepared statement', () => {
        const sql = 'SELECT * FROM colors WHERE id = ?';
        const stmt = repository.getPreparedStatement(sql);

        expect(stmt).toBeTruthy();
        expect(typeof stmt.get).toBe('function');
      });

      test('should cache prepared statements', () => {
        const sql = 'SELECT * FROM colors WHERE organization_id = ?';
        const stmt1 = repository.getPreparedStatement(sql);
        const stmt2 = repository.getPreparedStatement(sql);

        expect(stmt1).toBe(stmt2); // Should be same instance due to caching
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors gracefully', () => {
      // Close database to simulate error
      db.close();

      expect(() => {
        repository.findAll(mockOrganizationId);
      }).toThrow();
    });

    test('should validate organization ID format', () => {
      expect(() => {
        repository.findAll('');
      }).toThrow();

      expect(() => {
        repository.findAll(null as any);
      }).toThrow();
    });

    test('should handle malformed color data', () => {
      const malformedColor = {
        code: null, // Invalid
        display_name: 'Test',
        hex: 'invalid-hex', // Invalid
      } as any;

      expect(() => {
        repository.insert(malformedColor, mockOrganizationId);
      }).toThrow();
    });
  });

  describe('Performance', () => {
    test('should handle large datasets efficiently', () => {
      // Insert many colors
      const colorIds = [];
      for (let i = 0; i < 100; i++) {
        const colorId = repository.insert(
          {
            code: `PERF${i.toString().padStart(3, '0')}`,
            display_name: `Performance Color ${i}`,
            hex: `#${i.toString(16).padStart(6, '0')}`,
          },
          mockOrganizationId
        );
        colorIds.push(colorId);
      }

      const startTime = Date.now();
      const allColors = repository.findAll(mockOrganizationId);
      const endTime = Date.now();

      expect(allColors.length).toBeGreaterThanOrEqual(100);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete in under 1 second
    });

    test('should reuse prepared statements for better performance', () => {
      const sql =
        'SELECT COUNT(*) as count FROM colors WHERE organization_id = ?';

      // First call
      const start1 = Date.now();
      repository.getPreparedStatement(sql);
      const end1 = Date.now();

      // Second call (should be faster due to caching)
      const start2 = Date.now();
      repository.getPreparedStatement(sql);
      const end2 = Date.now();

      expect(end2 - start2).toBeLessThanOrEqual(end1 - start1);
    });
  });
});

// Helper functions for test setup
function setupTestSchema(db: Database.Database) {
  // Create tables matching the actual schema
  db.exec(`
    CREATE TABLE color_sources (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT NOT NULL,
      name TEXT NOT NULL
    );
    
    INSERT INTO color_sources (code, name) VALUES ('RAL', 'RAL Colors');
    INSERT INTO color_sources (code, name) VALUES ('PAN', 'Pantone');
    
    CREATE TABLE colors (
      id TEXT PRIMARY KEY,
      external_id TEXT NOT NULL,
      organization_id TEXT NOT NULL,
      source_id INTEGER,
      code TEXT NOT NULL,
      display_name TEXT,
      hex TEXT NOT NULL,
      color_spaces TEXT,
      is_gradient BOOLEAN DEFAULT 0,
      is_metallic BOOLEAN DEFAULT 0,
      is_effect BOOLEAN DEFAULT 0,
      is_library BOOLEAN DEFAULT 0,
      gradient_colors TEXT,
      notes TEXT,
      tags TEXT,
      properties TEXT,
      is_synced BOOLEAN DEFAULT 0,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT NULL,
      FOREIGN KEY (source_id) REFERENCES color_sources(id)
    );
    
    CREATE TABLE products (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      organization_id TEXT NOT NULL,
      is_active BOOLEAN DEFAULT 1,
      deleted_at TEXT NULL,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE TABLE product_colors (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      product_id TEXT NOT NULL,
      color_id TEXT NOT NULL,
      organization_id TEXT NOT NULL,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (product_id) REFERENCES products(id),
      FOREIGN KEY (color_id) REFERENCES colors(id)
    );
    
    CREATE INDEX idx_colors_org_deleted ON colors(organization_id, deleted_at);
    CREATE INDEX idx_colors_sync ON colors(is_synced) WHERE deleted_at IS NULL;
    CREATE INDEX idx_product_colors_org ON product_colors(organization_id);
  `);
}

function seedTestData(db: Database.Database, organizationId: string) {
  // Insert test colors
  const testColors = [
    {
      id: 'test-color-1',
      external_id: 'ext-color-1',
      code: 'RAL5002',
      display_name: 'Ultramarine Blue',
      hex: '#20214F',
      color_spaces: JSON.stringify({ cmyk: { c: 100, m: 95, y: 0, k: 69 } }),
      source_id: 1,
    },
    {
      id: 'test-color-2',
      external_id: 'ext-color-2',
      code: 'RAL6018',
      display_name: 'Yellow Green',
      hex: '#57A639',
      color_spaces: JSON.stringify({ cmyk: { c: 48, m: 0, y: 78, k: 35 } }),
      source_id: 1,
    },
  ];

  const insertColor = db.prepare(`
    INSERT INTO colors (id, external_id, organization_id, source_id, code, display_name, hex, color_spaces)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);

  testColors.forEach(color => {
    insertColor.run(
      color.id,
      color.external_id,
      organizationId,
      color.source_id,
      color.code,
      color.display_name,
      color.hex,
      color.color_spaces
    );
  });

  // Insert test products
  const testProducts = [
    { id: 'test-product-1', name: 'Product Alpha' },
    { id: 'test-product-2', name: 'Product Beta' },
  ];

  const insertProduct = db.prepare(`
    INSERT INTO products (id, name, organization_id)
    VALUES (?, ?, ?)
  `);

  testProducts.forEach(product => {
    insertProduct.run(product.id, product.name, organizationId);
  });

  // Create color-product relationships
  const insertRelationship = db.prepare(`
    INSERT INTO product_colors (product_id, color_id, organization_id)
    VALUES (?, ?, ?)
  `);

  insertRelationship.run('test-product-1', 'test-color-1', organizationId);
  insertRelationship.run('test-product-2', 'test-color-1', organizationId);
  insertRelationship.run('test-product-1', 'test-color-2', organizationId);
}
