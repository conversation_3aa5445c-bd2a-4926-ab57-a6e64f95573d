/**
 * @file email-retry-queue.test.ts
 * @description Tests for EmailRetryQueue persistence and retry logic
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EmailRetryQueue } from '../email-retry-queue';

// Mock electron store
vi.mock('../../../utils/electron-store-patched', () => {
  const mockStore = {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
    clear: vi.fn()
  };
  
  return {
    __esModule: true,
    default: vi.fn().mockImplementation(() => mockStore)
  };
});

vi.mock('electron', () => ({
  app: {
    getPath: vi.fn().mockReturnValue('/test/app/data')
  }
}));

describe('EmailRetryQueue', () => {
  let queue: EmailRetryQueue;
  let mockStore: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Get the mock store instance
    const PatchedStore = await import('../../../utils/electron-store-patched');
    const MockedPatchedStore = PatchedStore.default as any;
    mockStore = MockedPatchedStore.mock.results[0].value;
    
    // Mock store defaults
    mockStore.get.mockImplementation((key: string, defaultValue: any) => {
      if (key === 'emails') {return [];}
      if (key === 'lastProcessedTime') {return 0;}
      if (key === 'processedCount') {return 0;}
      if (key === 'failedCount') {return 0;}
      return defaultValue;
    });
    
    queue = new EmailRetryQueue();
    await queue.initialize();
  });

  afterEach(async () => {
    await queue.cleanup();
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      const newQueue = new EmailRetryQueue();
      await expect(newQueue.initialize()).resolves.not.toThrow();
    });

    it('should clean up expired emails on initialization', async () => {
      const oldEmail = {
        id: 'old-email',
        emailData: {
          to: '<EMAIL>',
          subject: 'Old Email',
          content: 'Old content'
        },
        attemptCount: 0,
        maxAttempts: 5,
        nextRetryTime: Date.now(),
        createdAt: Date.now() - (8 * 24 * 60 * 60 * 1000), // 8 days ago
        priority: 'medium' as const
      };

      mockStore.get.mockImplementation((key) => {
        if (key === 'emails') {return [oldEmail];}
        return 0;
      });

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      const newQueue = new EmailRetryQueue();
      await newQueue.initialize();

      expect(mockStore.set).toHaveBeenCalledWith('emails', []);
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Cleaned up 1 expired emails')
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('Queue Management', () => {
    it('should queue email successfully', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        content: 'Test content'
      };

      const emailId = await queue.queueEmail(emailData, 'high', 3);

      expect(emailId).toMatch(/^email_\d+_[a-z0-9]+$/);
      expect(mockStore.set).toHaveBeenCalledWith('emails', 
        expect.arrayContaining([
          expect.objectContaining({
            id: emailId,
            emailData,
            priority: 'high',
            maxAttempts: 3,
            attemptCount: 0
          })
        ])
      );
    });

    it('should remove email from queue', () => {
      const testEmail = {
        id: 'test-email-id',
        emailData: {
          to: '<EMAIL>',
          subject: 'Test',
          content: 'Test'
        },
        attemptCount: 0,
        maxAttempts: 5,
        nextRetryTime: Date.now(),
        createdAt: Date.now(),
        priority: 'medium' as const
      };

      mockStore.get.mockImplementation((key) => {
        if (key === 'emails') {return [testEmail];}
        return 0;
      });

      queue.removeEmail('test-email-id');

      expect(mockStore.set).toHaveBeenCalledWith('emails', []);
    });

    it('should handle queue size limit', async () => {
      // Create many low-priority emails to exceed limit
      const existingEmails = Array.from({ length: 100 }, (_, i) => ({
        id: `email-${i}`,
        emailData: {
          to: '<EMAIL>',
          subject: `Test ${i}`,
          content: 'Test'
        },
        attemptCount: 0,
        maxAttempts: 5,
        nextRetryTime: Date.now(),
        createdAt: Date.now() - i * 1000, // Older emails have smaller index
        priority: 'low' as const
      }));

      mockStore.get.mockImplementation((key) => {
        if (key === 'emails') {return existingEmails;}
        return 0;
      });

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await queue.queueEmail({
        to: '<EMAIL>',
        subject: 'New Email',
        content: 'New content'
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Removed old low-priority email')
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('Retry Logic', () => {
    it('should mark email as failed and schedule retry', async () => {
      const testEmail = {
        id: 'test-email-id',
        emailData: {
          to: '<EMAIL>',
          subject: 'Test',
          content: 'Test'
        },
        attemptCount: 0,
        maxAttempts: 5,
        nextRetryTime: Date.now(),
        createdAt: Date.now(),
        priority: 'medium' as const
      };

      mockStore.get.mockImplementation((key) => {
        if (key === 'emails') {return [testEmail];}
        return 0;
      });

      const result = await queue.markEmailFailed('test-email-id', 'Test error');

      expect(result).toBe(true);
      expect(mockStore.set).toHaveBeenCalledWith('emails', 
        expect.arrayContaining([
          expect.objectContaining({
            id: 'test-email-id',
            attemptCount: 1,
            lastError: 'Test error',
            nextRetryTime: expect.any(Number)
          })
        ])
      );
    });

    it('should remove email after max attempts', async () => {
      const testEmail = {
        id: 'test-email-id',
        emailData: {
          to: '<EMAIL>',
          subject: 'Test',
          content: 'Test'
        },
        attemptCount: 4, // One less than max
        maxAttempts: 5,
        nextRetryTime: Date.now(),
        createdAt: Date.now(),
        priority: 'medium' as const
      };

      mockStore.get.mockImplementation((key) => {
        if (key === 'emails') {return [testEmail];}
        return 0;
      });

      const result = await queue.markEmailFailed('test-email-id', 'Final error');

      expect(result).toBe(false);
      expect(mockStore.set).toHaveBeenCalledWith('failedCount', 1);
    });

    it('should calculate exponential backoff delay', () => {
      const queue_: any = queue;
      
      // Test exponential backoff calculation
      const delay1 = queue_.calculateRetryDelay(1);
      const delay2 = queue_.calculateRetryDelay(2);
      const delay3 = queue_.calculateRetryDelay(3);
      
      expect(delay2).toBeGreaterThan(delay1);
      expect(delay3).toBeGreaterThan(delay2);
      
      // Should not exceed max delay (24 hours)
      const maxDelay = queue_.calculateRetryDelay(20);
      expect(maxDelay).toBeLessThanOrEqual(24 * 60 * 60 * 1000);
    });
  });

  describe('Queue Processing', () => {
    it('should return ready emails sorted by priority', () => {
      const now = Date.now();
      const emails = [
        {
          id: 'low-priority',
          emailData: { to: '<EMAIL>', subject: 'Test 1', content: 'Test' },
          attemptCount: 0,
          maxAttempts: 5,
          nextRetryTime: now - 1000, // Ready
          createdAt: now,
          priority: 'low' as const
        },
        {
          id: 'high-priority',
          emailData: { to: '<EMAIL>', subject: 'Test 2', content: 'Test' },
          attemptCount: 0,
          maxAttempts: 5,
          nextRetryTime: now - 1000, // Ready
          createdAt: now,
          priority: 'high' as const
        },
        {
          id: 'not-ready',
          emailData: { to: '<EMAIL>', subject: 'Test 3', content: 'Test' },
          attemptCount: 0,
          maxAttempts: 5,
          nextRetryTime: now + 60000, // Not ready yet
          createdAt: now,
          priority: 'medium' as const
        }
      ];

      mockStore.get.mockImplementation((key) => {
        if (key === 'emails') {return emails;}
        return 0;
      });

      const readyEmails = queue.getReadyEmails();

      expect(readyEmails).toHaveLength(2);
      expect(readyEmails[0].id).toBe('high-priority'); // High priority first
      expect(readyEmails[1].id).toBe('low-priority');
    });

    it('should provide accurate queue statistics', () => {
      const emails = [
        {
          id: 'ready-email',
          emailData: { to: '<EMAIL>', subject: 'Test', content: 'Test' },
          attemptCount: 0,
          maxAttempts: 5,
          nextRetryTime: Date.now() - 1000, // Ready
          createdAt: Date.now() - 1000,
          priority: 'medium' as const
        },
        {
          id: 'waiting-email',
          emailData: { to: '<EMAIL>', subject: 'Test', content: 'Test' },
          attemptCount: 1,
          maxAttempts: 5,
          nextRetryTime: Date.now() + 60000, // Not ready
          createdAt: Date.now(),
          priority: 'medium' as const
        }
      ];

      mockStore.get.mockImplementation((key) => {
        if (key === 'emails') {return emails;}
        if (key === 'processedCount') {return 10;}
        if (key === 'failedCount') {return 2;}
        return 0;
      });

      const stats = queue.getQueueStats();

      expect(stats.totalQueued).toBe(2);
      expect(stats.readyToProcess).toBe(1);
      expect(stats.processed).toBe(10);
      expect(stats.failed).toBe(2);
      expect(stats.oldestEmail).toBe(emails[0].createdAt);
    });

    it('should start and stop processing', () => {
      const queue_: any = queue;
      
      // Start processing
      queue_.startProcessing();
      expect(queue_.processingInterval).not.toBeNull();
      
      // Stop processing
      queue.stopProcessing();
      expect(queue_.processingInterval).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing email ID gracefully', async () => {
      mockStore.get.mockReturnValue([]);
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      const result = await queue.markEmailFailed('non-existent-id', 'Error');
      
      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        '[EmailRetryQueue] Email non-existent-id not found in queue'
      );
      
      consoleSpy.mockRestore();
    });

    it('should handle store errors gracefully', async () => {
      mockStore.set.mockImplementation(() => {
        throw new Error('Store error');
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await expect(queue.queueEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test'
      })).rejects.toThrow('Store error');
      
      consoleSpy.mockRestore();
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources properly', async () => {
      const queue_: any = queue;
      queue_.startProcessing();
      
      await queue.cleanup();
      
      expect(queue_.processingInterval).toBeNull();
    });
  });
});