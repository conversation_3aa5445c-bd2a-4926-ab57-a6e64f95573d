/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * @file electron.d.ts
 * @description TypeScript declarations for Electron IPC
 */

import {
  ColorEntry,
  NewColorEntry,
  UpdateColorEntry,
  ColorApiResponse,
  ColorWithUsageResponse,
} from './shared/types/color.types';
import type { SyncAPI } from './types/preload';

interface ImportResult {
  imported: boolean;
  count?: number;
  message: string;
}

interface ExportResult {
  exported: boolean;
  count?: number;
  message: string;
}

// Enterprise response format for IPC operations
interface IPCResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  userMessage?: string;
  timestamp?: number;
}

interface ColorAPI {
  getAll: () => Promise<IPCResponse<ColorEntry[]>>;
  getUsageCounts: () => Promise<
    IPCResponse<Record<string, { count: number; products: string[] }>>
  >;
  getById: (id: string) => Promise<IPCResponse<ColorEntry>>;
  add: (color: NewColorEntry) => Promise<IPCResponse<ColorEntry>>;
  update: (
    id: string,
    updates: UpdateColorEntry
  ) => Promise<IPCResponse<ColorEntry>>;
  delete: (id: string) => Promise<IPCResponse<boolean>>;
  clearAll: () => Promise<IPCResponse<boolean>>;
  importColors: (
    mergeMode?: 'replace' | 'merge',
    filePath?: string
  ) => Promise<ImportResult>;
  exportColors: (
    filePath?: string,
    format?: 'json' | 'csv'
  ) => Promise<ExportResult>;

  // NEW: Clean implementation methods
  getAllWithUsage: () => Promise<ColorApiResponse<ColorWithUsageResponse>>;
  getProductsByColorName: () => Promise<
    ColorApiResponse<Record<string, string[]>>
  >;
  clearFrontendState: () => Promise<ColorApiResponse<{ cleared: boolean }>>;

  // ADMIN ONLY: Dangerous operations (explicitly marked)
  adminClearAll: (options?: {
    hardDelete?: boolean;
  }) => Promise<ColorApiResponse<{ cleared: boolean }>>;
}

interface IpcAPI {
  invoke: (channel: string, ...args: unknown[]) => Promise<unknown>;
}

interface ProcessVersions {
  node: string;
  chrome: string;
  electron: string;
}

interface ElectronProcess {
  versions: ProcessVersions;
  platform: string;
  arch: string;
  env: Record<string, string>;
}

interface WindowAPI {
  minimize: () => void;
  maximize: () => void;
  unmaximize: () => void;
  close: () => void;
  isMaximized: () => Promise<boolean>;
  toggleDevTools: () => Promise<boolean>;
}

interface Electron {
  process: ElectronProcess;
  window: WindowAPI;
}

interface AutoSyncConfig {
  enabled: boolean;
  intervalMinutes: number;
  startupSync: boolean;
  focusSync: boolean;
  networkAwareSync: boolean;
  realtimeSync: boolean;
  batteryConservation: boolean;
}

interface AutoSyncStatus {
  isEnabled: boolean;
  isSyncing: boolean;
  lastSyncTime: number | null;
  nextSyncTime: number | null;
  isOnline: boolean;
}

interface AutoSyncAPI {
  getConfig: () => Promise<{
    success: boolean;
    config?: AutoSyncConfig;
    status?: AutoSyncStatus;
    error?: string;
  }>;
  updateConfig: (
    config: Partial<AutoSyncConfig>
  ) => Promise<{ success: boolean; config?: AutoSyncConfig; error?: string }>;
  initialize: (
    config?: Partial<AutoSyncConfig>
  ) => Promise<{
    success: boolean;
    config?: AutoSyncConfig;
    status?: AutoSyncStatus;
    error?: string;
  }>;
  forceSync: () => Promise<{ success: boolean; result?: any; error?: string }>;
  getStatus: () => Promise<{
    success: boolean;
    status?: AutoSyncStatus;
    error?: string;
  }>;
  cleanup: () => Promise<{ success: boolean; error?: string }>;
  onSyncStarted: (callback: (data: any) => void) => void;
  onSyncCompleted: (callback: (data: any) => void) => void;
  onSyncFailed: (callback: (data: any) => void) => void;
  onConfigUpdated: (callback: (data: any) => void) => void;
  removeAllListeners: () => void;
}

interface AppZoomAPI {
  getZoomFactor: () => Promise<number>;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
}

interface AppShortcuts {
  zoomIn?: string;
  zoomOut?: string;
}

interface AppAPI {
  zoom?: AppZoomAPI;
  shortcuts?: AppShortcuts;
}

interface MonitoringAPI {
  trackError: (
    error:
      | Error
      | {
          message: string;
          stack?: string;
          componentStack?: string;
          location?: string;
        }
  ) => Promise<void>;
}

declare global {
  interface Window {
    colorAPI: ColorAPI;
    ipc: IpcAPI;
    electron?: Electron;
    syncAPI?: SyncAPI;
    autoSyncAPI?: AutoSyncAPI;
    app?: AppAPI;
    monitoring?: MonitoringAPI;
  }
}
