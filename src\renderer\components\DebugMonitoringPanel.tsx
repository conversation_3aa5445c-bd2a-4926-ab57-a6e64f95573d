import { useState, useEffect } from 'react';
import {
  Activity,
  AlertTriangle,
  <PERSON><PERSON><PERSON>cle,
  Clock,
  Download,
  Trash2,
  XCircle,
} from 'lucide-react';
import { errorLogger, ErrorLog, PerformanceMetric } from '../utils/errorLogger';
import { healthMonitor, SystemHealth } from '../utils/healthCheck';
import { useMemoryMonitoring } from '../hooks/usePerformanceMonitoring';
import Modal from './ui/Modal';
import CachePerformancePanel from './Debug/CachePerformancePanel';

interface DebugMonitoringPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DebugMonitoringPanel({
  isOpen,
  onClose,
}: DebugMonitoringPanelProps) {
  // Only show debug monitoring panel in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }
  const [activeTab, setActiveTab] = useState<
    'health' | 'errors' | 'performance' | 'cache' | 'export'
  >('health');
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [errorLogs, setErrorLogs] = useState<ErrorLog[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const memoryStats = useMemoryMonitoring(10000); // Update every 10 seconds

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const updateData = () => {
      setSystemHealth(healthMonitor.getSystemHealth());
      setErrorLogs(errorLogger.getLogs());
      setMetrics(errorLogger.getMetrics());
    };

    updateData();
    const interval = setInterval(updateData, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isOpen]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className='w-4 h-4 text-green-500' />;
      case 'warning':
        return <AlertTriangle className='w-4 h-4 text-yellow-500' />;
      case 'critical':
        return <XCircle className='w-4 h-4 text-red-500' />;
      default:
        return <Clock className='w-4 h-4 text-gray-500' />;
    }
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) {
      return '0 Bytes';
    }
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round((bytes / Math.pow(1024, i)) * 100) / 100} ${sizes[i]}`;
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const exportLogs = () => {
    const data = errorLogger.exportLogs();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chromasync-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearLogs = () => {
    if (
      window.confirm('Are you sure you want to clear all logs and metrics?')
    ) {
      errorLogger.clearLogs();
      setErrorLogs([]);
      setMetrics([]);
    }
  };

  const tabButtonClass = (tab: string) =>
    `px-4 py-2 text-sm font-medium rounded-md transition-colors ${
      activeTab === tab
        ? 'bg-brand-primary text-white'
        : 'text-ui-foreground-secondary hover:text-ui-foreground-primary hover:bg-ui-background-secondary'
    }`;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title='System Monitoring'
      maxWidth='max-w-6xl'
    >
      <div className='h-[80vh] flex flex-col'>
        {/* Tab Navigation */}
        <div className='flex space-x-1 p-4 border-b border-ui-border-light'>
          <button
            className={tabButtonClass('health')}
            onClick={() => setActiveTab('health')}
          >
            <Activity className='w-4 h-4 inline mr-2' />
            Health
          </button>
          <button
            className={tabButtonClass('errors')}
            onClick={() => setActiveTab('errors')}
          >
            <AlertTriangle className='w-4 h-4 inline mr-2' />
            Errors ({errorLogs.filter(log => log.level === 'error').length})
          </button>
          <button
            className={tabButtonClass('performance')}
            onClick={() => setActiveTab('performance')}
          >
            <Activity className='w-4 h-4 inline mr-2' />
            Performance
          </button>
          <button
            className={tabButtonClass('cache')}
            onClick={() => setActiveTab('cache')}
          >
            <Clock className='w-4 h-4 inline mr-2' />
            Cache
          </button>
          <button
            className={tabButtonClass('export')}
            onClick={() => setActiveTab('export')}
          >
            <Download className='w-4 h-4 inline mr-2' />
            Export
          </button>
        </div>

        {/* Tab Content */}
        <div className='flex-1 overflow-auto p-4'>
          {activeTab === 'health' && systemHealth && (
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-semibold'>
                  System Health Overview
                </h3>
                <div className='flex items-center space-x-2'>
                  {getStatusIcon(systemHealth.overall)}
                  <span className='capitalize font-medium'>
                    {systemHealth.overall}
                  </span>
                </div>
              </div>

              {memoryStats && (
                <div className='bg-ui-background-secondary rounded-lg p-4'>
                  <h4 className='font-medium mb-2'>Memory Usage</h4>
                  <div className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span>Used: {formatBytes(memoryStats.used)}</span>
                      <span>Total: {formatBytes(memoryStats.total)}</span>
                    </div>
                    <div className='w-full bg-ui-background-tertiary rounded-full h-2'>
                      <div
                        className={`h-2 rounded-full ${
                          memoryStats.percentage > 80
                            ? 'bg-red-500'
                            : memoryStats.percentage > 60
                              ? 'bg-yellow-500'
                              : 'bg-green-500'
                        }`}
                        style={{ width: `${memoryStats.percentage}%` }}
                      />
                    </div>
                    <div className='text-center text-sm text-ui-foreground-secondary'>
                      {memoryStats.percentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
              )}

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {systemHealth.checks.map(check => (
                  <div
                    key={check.name}
                    className='bg-ui-background-secondary rounded-lg p-4 border-l-4'
                    style={{
                      borderLeftColor:
                        check.status === 'healthy'
                          ? '#10b981'
                          : check.status === 'warning'
                            ? '#f59e0b'
                            : check.status === 'critical'
                              ? '#ef4444'
                              : '#6b7280',
                    }}
                  >
                    <div className='flex items-center justify-between mb-2'>
                      <h4 className='font-medium capitalize'>
                        {check.name.replace(/_/g, ' ')}
                      </h4>
                      {getStatusIcon(check.status)}
                    </div>
                    <p className='text-sm text-ui-foreground-secondary mb-1'>
                      {check.message || 'No message'}
                    </p>
                    <div className='text-xs text-ui-foreground-tertiary'>
                      Last checked: {formatTime(check.timestamp)}
                      {check.duration && ` (${check.duration.toFixed(0)}ms)`}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'errors' && (
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-semibold'>Error Logs</h3>
                <button
                  onClick={clearLogs}
                  className='inline-flex items-center px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600'
                >
                  <Trash2 className='w-4 h-4 mr-1' />
                  Clear
                </button>
              </div>

              <div className='space-y-2'>
                {errorLogs
                  .slice(-50)
                  .reverse()
                  .map(log => (
                    <div
                      key={log.id}
                      className={`p-3 rounded-lg border-l-4 ${
                        log.level === 'error'
                          ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                          : log.level === 'warning'
                            ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
                            : 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      }`}
                    >
                      <div className='flex items-start justify-between'>
                        <div className='flex-1'>
                          <div className='flex items-center space-x-2 mb-1'>
                            <span
                              className={`px-2 py-1 text-xs rounded font-medium ${
                                log.level === 'error'
                                  ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                                  : log.level === 'warning'
                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                                    : 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                              }`}
                            >
                              {log.level.toUpperCase()}
                            </span>
                            <span className='text-xs text-ui-foreground-tertiary'>
                              {log.category}
                            </span>
                          </div>
                          <p className='text-sm font-medium mb-1'>
                            {log.message}
                          </p>
                          <div className='text-xs text-ui-foreground-secondary'>
                            {formatTime(log.timestamp)} • {log.source}
                          </div>
                          {log.stack && (
                            <details className='mt-2'>
                              <summary className='cursor-pointer text-xs text-ui-foreground-tertiary'>
                                Stack trace
                              </summary>
                              <pre className='mt-1 text-xs bg-ui-background-tertiary p-2 rounded overflow-auto'>
                                {log.stack}
                              </pre>
                            </details>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                {errorLogs.length === 0 && (
                  <div className='text-center py-8 text-ui-foreground-secondary'>
                    No error logs found
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Performance Metrics</h3>

              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {Array.from(new Set(metrics.map(m => m.name))).map(
                  metricName => {
                    const metricData = metrics
                      .filter(m => m.name === metricName)
                      .slice(-10);
                    const average =
                      metricData.reduce((sum, m) => sum + m.value, 0) /
                      metricData.length;
                    const latest = metricData[metricData.length - 1];

                    return (
                      <div
                        key={metricName}
                        className='bg-ui-background-secondary rounded-lg p-4'
                      >
                        <h4 className='font-medium mb-2'>
                          {metricName.replace(/_/g, ' ')}
                        </h4>
                        <div className='space-y-1'>
                          <div className='text-sm'>
                            Latest: {latest?.value.toFixed(2)} {latest?.unit}
                          </div>
                          <div className='text-sm text-ui-foreground-secondary'>
                            Average: {average.toFixed(2)} {latest?.unit}
                          </div>
                          <div className='text-xs text-ui-foreground-tertiary'>
                            {metricData.length} samples
                          </div>
                        </div>
                      </div>
                    );
                  }
                )}
              </div>
            </div>
          )}

          {activeTab === 'cache' && (
            <div className='space-y-4'>
              <CachePerformancePanel refreshInterval={2000} />
            </div>
          )}

          {activeTab === 'export' && (
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Export Diagnostic Data</h3>

              <div className='bg-ui-background-secondary rounded-lg p-4'>
                <h4 className='font-medium mb-2'>Available Data</h4>
                <ul className='space-y-1 text-sm'>
                  <li>• {errorLogs.length} error logs</li>
                  <li>• {metrics.length} performance metrics</li>
                  <li>• {systemHealth?.checks.length || 0} health checks</li>
                  <li>• System information and timestamps</li>
                </ul>
              </div>

              <div className='space-y-2'>
                <button
                  onClick={exportLogs}
                  className='w-full inline-flex items-center justify-center px-4 py-2 bg-brand-primary text-white rounded hover:bg-brand-primary-dark'
                >
                  <Download className='w-4 h-4 mr-2' />
                  Export All Diagnostic Data
                </button>

                <p className='text-xs text-ui-foreground-secondary text-center'>
                  Exported data includes logs, metrics, and system health
                  information. No sensitive user data is included.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
}
