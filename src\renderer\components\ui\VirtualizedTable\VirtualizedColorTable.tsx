/**
 * @file VirtualizedColorTable.tsx
 * @description Specialized virtualized table for color data, compatible with existing ColorTable
 */

import React, { useMemo, useCallback } from 'react';
import {
  VirtualizedTable,
  VirtualizedTableColumn,
  VirtualizedTableProps,
} from './index';
import { useSorting, useRowSelection, useVirtualTableKeyboard } from './hooks';
import { useTokens } from '../../../hooks/useTokens';
import { Copy, Trash2, Edit3 } from 'lucide-react';

export interface ColorData {
  id: string;
  name: string;
  cmyk: {
    c: number;
    m: number;
    y: number;
    k: number;
  };
  pantone?: string;
  tags: string[];
  organizationId: string;
  hex?: string;
  rgb?: {
    r: number;
    g: number;
    b: number;
  };
  lab?: {
    l: number;
    a: number;
    b: number;
  };
  hsl?: {
    h: number;
    s: number;
    l: number;
  };
}

export interface VirtualizedColorTableProps
  extends Omit<VirtualizedTableProps<ColorData>, 'columns' | 'data'> {
  colors: ColorData[];
  viewMode?: 'details' | 'reference';
  onEditColor?: (color: ColorData) => void;
  onDeleteColor?: (color: ColorData) => void;
  onCopyColor?: (color: ColorData, format: string) => void;
  searchQuery?: string;
  enableKeyboardNavigation?: boolean;
}

/**
 * Color swatch component for table cells
 */
const ColorSwatch: React.FC<{ color: ColorData; size?: number }> = ({
  color,
  size = 32,
}) => {
  const backgroundColor = color.hex || '#000000';

  return (
    <div
      className='rounded-md border border-ui-border-light dark:border-ui-border-dark shadow-sm'
      style={{
        width: size,
        height: size,
        backgroundColor,
      }}
      aria-label={`Color swatch for ${color.name}`}
    />
  );
};

/**
 * Action buttons component for table rows
 */
const ColorActions: React.FC<{
  color: ColorData;
  onEdit?: (color: ColorData) => void;
  onDelete?: (color: ColorData) => void;
  onCopy?: (color: ColorData, format: string) => void;
}> = ({ color, onEdit, onDelete, onCopy }) => {
  const tokens = useTokens();

  const handleEdit = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onEdit?.(color);
    },
    [onEdit, color]
  );

  const handleDelete = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onDelete?.(color);
    },
    [onDelete, color]
  );

  const handleCopyHex = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onCopy?.(color, 'hex');
    },
    [onCopy, color]
  );

  return (
    <div className='flex items-center gap-2'>
      {onCopy && (
        <button
          onClick={handleCopyHex}
          className='p-1 rounded hover:bg-ui-background-secondary transition-colors'
          title='Copy HEX value'
          aria-label={`Copy HEX value for ${color.name}`}
        >
          <Copy size={14} />
        </button>
      )}
      {onEdit && (
        <button
          onClick={handleEdit}
          className='p-1 rounded hover:bg-ui-background-secondary transition-colors'
          title='Edit color'
          aria-label={`Edit ${color.name}`}
        >
          <Edit3 size={14} />
        </button>
      )}
      {onDelete && (
        <button
          onClick={handleDelete}
          className='p-1 rounded hover:bg-ui-background-secondary transition-colors text-ui-foreground-danger'
          title='Delete color'
          aria-label={`Delete ${color.name}`}
        >
          <Trash2 size={14} />
        </button>
      )}
    </div>
  );
};

/**
 * Virtualized Color Table component
 */
export const VirtualizedColorTable: React.FC<VirtualizedColorTableProps> = ({
  colors,
  viewMode = 'details',
  onEditColor,
  onDeleteColor,
  onCopyColor,
  searchQuery = '',
  enableKeyboardNavigation = true,
  height,
  onRowClick,
  onRowDoubleClick,
  className,
  ...tableProps
}) => {
  const tokens = useTokens();

  // Sorting functionality
  const { sortColumn, sortDirection, handleSortChange } = useSorting({
    defaultSortColumn: 'name',
    defaultSortDirection: 'asc',
  });

  // Row selection functionality
  const { selectedRowIndex, selectRow } = useRowSelection();

  // Filter colors based on search query
  const filteredColors = useMemo(() => {
    if (!searchQuery.trim()) {
      return colors;
    }

    const lowerQuery = searchQuery.toLowerCase();
    return colors.filter(
      color =>
        color.name.toLowerCase().includes(lowerQuery) ||
        color.pantone?.toLowerCase().includes(lowerQuery) ||
        color.tags.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
        color.hex?.toLowerCase().includes(lowerQuery)
    );
  }, [colors, searchQuery]);

  // Sort colors
  const sortedColors = useMemo(() => {
    if (!sortColumn || !sortDirection) {
      return filteredColors;
    }

    return [...filteredColors].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortColumn) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'pantone':
          aValue = a.pantone || '';
          bValue = b.pantone || '';
          break;
        case 'hex':
          aValue = a.hex || '';
          bValue = b.hex || '';
          break;
        case 'cmyk':
          aValue = `${a.cmyk.c}-${a.cmyk.m}-${a.cmyk.y}-${a.cmyk.k}`;
          bValue = `${b.cmyk.c}-${b.cmyk.m}-${b.cmyk.y}-${b.cmyk.k}`;
          break;
        case 'tags':
          aValue = a.tags.join(', ');
          bValue = b.tags.join(', ');
          break;
        default:
          return 0;
      }

      const comparison = String(aValue).localeCompare(String(bValue));
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [filteredColors, sortColumn, sortDirection]);

  // Define columns based on view mode
  const columns: VirtualizedTableColumn<ColorData>[] = useMemo(() => {
    const baseColumns: VirtualizedTableColumn<ColorData>[] = [
      {
        key: 'swatch',
        header: '',
        width: 60,
        render: color => <ColorSwatch color={color} />,
        align: 'center',
      },
      {
        key: 'name',
        header: 'Name',
        width: viewMode === 'details' ? '20%' : '25%',
        sortable: true,
        filterable: true,
        render: color => (
          <span className='font-medium text-ui-foreground-primary'>
            {color.name}
          </span>
        ),
      },
    ];

    if (viewMode === 'details') {
      baseColumns.push(
        {
          key: 'pantone',
          header: 'Pantone',
          width: '15%',
          sortable: true,
          filterable: true,
          render: color => (
            <span className='font-mono text-xs text-ui-foreground-secondary'>
              {color.pantone || '—'}
            </span>
          ),
        },
        {
          key: 'hex',
          header: 'HEX',
          width: '12%',
          sortable: true,
          filterable: true,
          render: color => (
            <span className='font-mono text-xs text-ui-foreground-secondary'>
              {color.hex || '—'}
            </span>
          ),
        },
        {
          key: 'cmyk',
          header: 'CMYK',
          width: '18%',
          sortable: true,
          render: color => (
            <span className='font-mono text-xs text-ui-foreground-secondary'>
              {`${color.cmyk.c}/${color.cmyk.m}/${color.cmyk.y}/${color.cmyk.k}`}
            </span>
          ),
        },
        {
          key: 'tags',
          header: 'Tags',
          width: '20%',
          sortable: true,
          filterable: true,
          render: color => (
            <div className='flex flex-wrap gap-1'>
              {color.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className='px-2 py-1 text-xs rounded-full bg-ui-background-secondary text-ui-foreground-secondary'
                >
                  {tag}
                </span>
              ))}
              {color.tags.length > 3 && (
                <span className='px-2 py-1 text-xs rounded-full bg-ui-background-secondary text-ui-foreground-secondary'>
                  +{color.tags.length - 3}
                </span>
              )}
            </div>
          ),
        }
      );
    } else {
      // Reference view - more compact
      baseColumns.push(
        {
          key: 'code',
          header: 'Code',
          width: '25%',
          sortable: true,
          render: color => (
            <div className='space-y-1'>
              {color.pantone && (
                <div className='font-mono text-xs text-ui-foreground-secondary'>
                  {color.pantone}
                </div>
              )}
              {color.hex && (
                <div className='font-mono text-xs text-ui-foreground-secondary'>
                  {color.hex}
                </div>
              )}
            </div>
          ),
        },
        {
          key: 'values',
          header: 'Values',
          width: '30%',
          render: color => (
            <div className='font-mono text-xs text-ui-foreground-secondary space-y-1'>
              <div>
                CMYK:{' '}
                {`${color.cmyk.c}/${color.cmyk.m}/${color.cmyk.y}/${color.cmyk.k}`}
              </div>
              {color.rgb && (
                <div>RGB: {`${color.rgb.r}/${color.rgb.g}/${color.rgb.b}`}</div>
              )}
            </div>
          ),
        }
      );
    }

    // Actions column
    baseColumns.push({
      key: 'actions',
      header: 'Actions',
      width: 120,
      align: 'center',
      render: color => (
        <ColorActions
          color={color}
          onEdit={onEditColor}
          onDelete={onDeleteColor}
          onCopy={onCopyColor}
        />
      ),
    });

    return baseColumns;
  }, [viewMode, onEditColor, onDeleteColor, onCopyColor]);

  // Handle row clicks
  const handleRowClick = useCallback(
    (color: ColorData, index: number) => {
      selectRow(index);
      onRowClick?.(color, index);
    },
    [selectRow, onRowClick]
  );

  // Keyboard navigation
  const { onKeyDown } = useVirtualTableKeyboard({
    data: sortedColors,
    onRowClick: handleRowClick,
    onRowDoubleClick,
    selectedRowIndex,
    onSelectedRowChange: selectRow,
  });

  // Handle container key events if keyboard navigation is enabled
  const containerProps = enableKeyboardNavigation
    ? {
        tabIndex: 0,
        onKeyDown,
        role: 'application',
        'aria-label': 'Color table with keyboard navigation',
      }
    : {};

  return (
    <div {...containerProps} className='focus:outline-none'>
      <VirtualizedTable
        data={sortedColors}
        columns={columns}
        height={height}
        rowHeight={viewMode === 'details' ? 64 : 80}
        headerHeight={48}
        onRowClick={handleRowClick}
        onRowDoubleClick={onRowDoubleClick}
        selectedRowIndex={selectedRowIndex}
        keyExtractor={color => color.id}
        onSortChange={handleSortChange}
        sortColumn={sortColumn}
        sortDirection={sortDirection}
        emptyMessage={
          searchQuery
            ? `No colors found matching "${searchQuery}"`
            : 'No colors available'
        }
        className={className}
        showBorders={true}
        striped={false}
        hoverable={true}
        {...tableProps}
      />
    </div>
  );
};

export default VirtualizedColorTable;
