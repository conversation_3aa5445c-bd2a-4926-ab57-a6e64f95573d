/**
 * @file rate-limiter.ts
 * @description Rate limiting middleware for IPC endpoints following security best practices
 * 
 * Implements configurable rate limiting to prevent abuse of sensitive IPC endpoints
 * and protect against DoS attacks and resource exhaustion.
 */

/**
 * Rate limit configuration interface
 */
export interface RateLimitConfig {
  /** Maximum requests allowed in the time window */
  maxRequests: number;
  /** Time window in milliseconds */
  windowMs: number;
  /** Custom message when rate limit is exceeded */
  message?: string;
  /** Skip rate limiting for certain conditions */
  skip?: (channel: string, args: any[]) => boolean;
  /** Custom key generator for grouping requests */
  keyGenerator?: (channel: string, args: any[]) => string;
  /** Action to take when limit is exceeded */
  onLimitReached?: (channel: string, key: string) => void;
}

/**
 * Rate limit entry tracking requests
 */
interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

/**
 * Rate limiter class for IPC endpoints
 */
export class IPCRateLimiter {
  private static instance: IPCRateLimiter;
  private limits: Map<string, RateLimitEntry> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;
  private defaultConfig: RateLimitConfig;

  private constructor() {
    this.defaultConfig = {
      maxRequests: 100,
      windowMs: 60000, // 1 minute
      message: 'Too many requests, please try again later.',
    };

    // Start cleanup interval to remove expired entries
    this.startCleanup();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): IPCRateLimiter {
    if (!IPCRateLimiter.instance) {
      IPCRateLimiter.instance = new IPCRateLimiter();
    }
    return IPCRateLimiter.instance;
  }

  /**
   * Configure default rate limiting settings
   */
  setDefaultConfig(config: Partial<RateLimitConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
    console.log('[RateLimit] Default configuration updated:', this.defaultConfig);
  }

  /**
   * Check if request should be rate limited
   */
  checkLimit(channel: string, args: any[] = [], config?: Partial<RateLimitConfig>): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
    message?: string;
  } {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    // Handle invalid configurations
    if (finalConfig.maxRequests <= 0) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: Date.now() + finalConfig.windowMs,
        message: finalConfig.message || 'Invalid rate limit configuration'
      };
    }
    
    // Skip if configured to skip
    if (finalConfig.skip && finalConfig.skip(channel, args)) {
      return {
        allowed: true,
        remaining: finalConfig.maxRequests,
        resetTime: Date.now() + finalConfig.windowMs
      };
    }

    // Generate key for tracking
    const key = finalConfig.keyGenerator 
      ? finalConfig.keyGenerator(channel, args)
      : this.defaultKeyGenerator(channel, args);

    const now = Date.now();
    const entry = this.limits.get(key);

    if (!entry) {
      // First request for this key
      this.limits.set(key, {
        count: 1,
        resetTime: now + finalConfig.windowMs,
        firstRequest: now
      });

      return {
        allowed: true,
        remaining: finalConfig.maxRequests - 1,
        resetTime: now + finalConfig.windowMs
      };
    }

    // Check if window has expired
    if (now >= entry.resetTime) {
      // Reset the window
      entry.count = 1;
      entry.resetTime = now + finalConfig.windowMs;
      entry.firstRequest = now;

      return {
        allowed: true,
        remaining: finalConfig.maxRequests - 1,
        resetTime: entry.resetTime
      };
    }

    // Check if limit exceeded
    if (entry.count >= finalConfig.maxRequests) {
      // Trigger limit reached callback
      if (finalConfig.onLimitReached) {
        finalConfig.onLimitReached(channel, key);
      }

      console.warn(`[RateLimit] Limit exceeded for ${channel} (key: ${key})`);
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        message: finalConfig.message
      };
    }

    // Increment count and allow request
    entry.count++;

    return {
      allowed: true,
      remaining: finalConfig.maxRequests - entry.count,
      resetTime: entry.resetTime
    };
  }

  /**
   * Default key generator using channel name
   */
  private defaultKeyGenerator(channel: string, _args: any[]): string {
    // Simple key based on channel - could be enhanced for per-user limiting
    return `channel:${channel}`;
  }

  /**
   * Generate user-specific key (requires user identification)
   */
  generateUserKey(channel: string, userId?: string): string {
    return userId ? `user:${userId}:${channel}` : `channel:${channel}`;
  }

  /**
   * Generate IP-based key (for network-level limiting)
   */
  generateIPKey(channel: string, ip?: string): string {
    return ip ? `ip:${ip}:${channel}` : `channel:${channel}`;
  }

  /**
   * Manually reset rate limit for a specific key
   */
  resetLimit(key: string): void {
    this.limits.delete(key);
    console.log(`[RateLimit] Manually reset limit for key: ${key}`);
  }

  /**
   * Reset all rate limits
   */
  resetAllLimits(): void {
    this.limits.clear();
    console.log('[RateLimit] All rate limits reset');
  }

  /**
   * Get current status for a key
   */
  getStatus(key: string, config?: Partial<RateLimitConfig>): {
    exists: boolean;
    count?: number;
    remaining?: number;
    resetTime?: number;
  } {
    const entry = this.limits.get(key);
    
    if (!entry) {
      return { exists: false };
    }

    const now = Date.now();
    if (now >= entry.resetTime) {
      return { exists: false }; // Expired
    }

    const finalConfig = { ...this.defaultConfig, ...config };

    return {
      exists: true,
      count: entry.count,
      remaining: Math.max(0, finalConfig.maxRequests - entry.count),
      resetTime: entry.resetTime
    };
  }

  /**
   * Get all active limits (for monitoring)
   * Note: This uses default config for remaining calculation since we don't store the config per key
   */
  getAllLimits(): Array<{
    key: string;
    count: number;
    remaining: number;
    resetTime: number;
    timeToReset: number;
  }> {
    const now = Date.now();
    const activeLimits: Array<any> = [];

    for (const [key, entry] of this.limits.entries()) {
      if (now < entry.resetTime) {
        activeLimits.push({
          key,
          count: entry.count,
          remaining: Math.max(0, this.defaultConfig.maxRequests - entry.count),
          resetTime: entry.resetTime,
          timeToReset: entry.resetTime - now
        });
      }
    }

    return activeLimits;
  }

  /**
   * Start cleanup interval to remove expired entries
   */
  private startCleanup(): void {
    // Clean up every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of this.limits.entries()) {
      if (now >= entry.resetTime) {
        this.limits.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`[RateLimit] Cleaned up ${cleanedCount} expired entries`);
    }
  }

  /**
   * Stop cleanup and clear all limits
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.limits.clear();
    console.log('[RateLimit] Rate limiter destroyed');
  }
}

/**
 * Predefined rate limit configurations for different endpoint types
 */
export const RateLimitPresets = {
  // High-frequency operations (like auto-save, real-time updates)
  HIGH_FREQUENCY: {
    maxRequests: 1000,
    windowMs: 60000, // 1 minute
    message: 'High frequency operation rate limit exceeded'
  },

  // Normal operations (CRUD operations, data queries)
  NORMAL: {
    maxRequests: 100,
    windowMs: 60000, // 1 minute
    message: 'Request rate limit exceeded'
  },

  // Sensitive operations (auth, organization changes, etc.)
  SENSITIVE: {
    maxRequests: 20,
    windowMs: 60000, // 1 minute
    message: 'Sensitive operation rate limit exceeded'
  },

  // Very sensitive operations (password changes, user management)
  VERY_SENSITIVE: {
    maxRequests: 5,
    windowMs: 60000, // 1 minute
    message: 'Very sensitive operation rate limit exceeded'
  },

  // File operations (uploads, downloads, imports)
  FILE_OPERATIONS: {
    maxRequests: 50,
    windowMs: 300000, // 5 minutes
    message: 'File operation rate limit exceeded'
  },

  // System operations (settings changes, diagnostics)
  SYSTEM: {
    maxRequests: 30,
    windowMs: 300000, // 5 minutes
    message: 'System operation rate limit exceeded'
  }
} as const;

/**
 * IPC channel categories for rate limiting
 */
export const IPCChannelCategories = {
  // Authentication and user management
  AUTH: [
    'auth:login',
    'auth:logout', 
    'auth:refresh',
    'auth:verify',
    'user:update',
    'user:delete'
  ],

  // Organization management
  ORGANIZATION: [
    'organization:create',
    'organization:update',
    'organization:delete',
    'organization:invite',
    'organization:remove-member',
    'organization:update-role'
  ],

  // Sensitive data operations
  SENSITIVE_DATA: [
    'color:bulk-import',
    'product:bulk-import',
    'sync:force-full',
    'database:reset',
    'settings:update-critical'
  ],

  // File operations
  FILE_OPS: [
    'file:upload',
    'file:download',
    'export:colors',
    'export:products',
    'import:data'
  ],

  // High frequency operations
  HIGH_FREQ: [
    'color:get',
    'product:get',
    'sync:status',
    'ping',
    'health-check',
    'debug-console-log'  // Debug logging should have high frequency limit
  ],

  // Normal operations
  NORMAL: [
    'color:create',
    'color:update',
    'color:delete',
    'product:create',
    'product:update',
    'product:delete'
  ]
} as const;

// Export singleton instance
export const rateLimiter = IPCRateLimiter.getInstance();