/**
 * @file LogoSection.tsx
 * @description Logo management section component
 */

import React from 'react';
import { Upload, Trash2 } from 'lucide-react';
import { useSettingsStore } from '../../../store/settings.store';
import placeholderImage from '../../../assets/placeholder-image.svg';

/**
 * Logo management section component
 */
export const LogoSection: React.FC = () => {
  const settings = useSettingsStore();

  const handleSelectLogo = async () => {
    try {
      const result = await window.api.selectLogoFile();

      if (result.success && result.path) {
        settings.setLogoPath(result.path);
      } else if (result.error) {
        console.error('Failed to select logo file:', result.error);
        // In a production app, you might want to show a toast or error message
      }
      // If result.canceled is true, user simply canceled the dialog
    } catch (error) {
      console.error('Error selecting logo file:', error);
    }
  };

  return (
    <section>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        Logo
      </h3>
      <div className='bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4 mb-4'>
        {settings.logoPath ? (
          <div className='flex items-center justify-between'>
            <div className='flex items-center'>
              <div className='w-16 h-16 bg-ui-background-tertiary dark:bg-zinc-700 rounded-[var(--radius-md)] flex items-center justify-center overflow-hidden'>
                <img
                  src={settings.logoPath}
                  alt='Logo'
                  className='max-w-full max-h-full object-contain'
                  onError={e => {
                    // If image fails to load, show a placeholder
                    (e.target as HTMLImageElement).src = placeholderImage;
                  }}
                />
              </div>
              <span className='ml-4 text-ui-foreground-primary dark:text-white truncate max-w-xs'>
                {settings.logoPath.split('/').pop()}
              </span>
            </div>
            <button
              onClick={() => settings.setLogoPath(null)}
              className='text-ui-foreground-tertiary dark:text-gray-400 hover:text-feedback-error dark:hover:text-red-400 transition-colors'
              aria-label='Remove logo'
            >
              <Trash2 size={20} />
            </button>
          </div>
        ) : (
          <div className='text-center py-6'>
            <p className='text-ui-foreground-secondary dark:text-gray-400 mb-4'>
              No logo selected. Upload a logo to display in the app header.
            </p>
            <button
              onClick={handleSelectLogo}
              className='inline-flex items-center px-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary/80 dark:hover:bg-zinc-600 transition-colors'
            >
              <Upload size={16} className='mr-2' />
              Select Logo
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default LogoSection;
