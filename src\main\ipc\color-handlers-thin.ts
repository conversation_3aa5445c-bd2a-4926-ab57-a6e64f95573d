/**
 * @file color-handlers-thin.ts
 * @description Thin IPC handlers for color operations using extracted service modules
 * 
 * This file implements thin handlers that delegate to specialized color services:
 * - ColorAnalysisService: Color analysis and comparison operations
 * - ColorCrudService: Basic CRUD operations (create, read, update, delete)
 * - ColorDataService: Data transformation and formatting operations
 * - ColorImportService: Import/export operations
 * - ColorQueryService: Query and search operations
 * - ColorValidationService: Input validation and standardization
 * 
 * Each handler focuses on:
 * 1. Organization context validation using getValidatedOrganizationId()
 * 2. Input parameter validation
 * 3. Delegation to appropriate service methods
 * 4. Standardized response formatting
 * 5. Minimal business logic (just coordination and error handling)
 */

import { ipcMain } from 'electron';
import { ColorChannels, NewColorEntry, UpdateColorEntry, ColorWithUsageResponse } from '../../shared/types/color.types';
// import { getValidatedOrganizationId } from '../middleware/organization-context.middleware';
import {
  register<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  registerSys<PERSON><PERSON><PERSON><PERSON>,
  createSuccessResponse,
  createErrorResponse
} from '../utils/ipc-wrapper';
import { IPCResponse } from '../../shared/types/ipc.types';

// ===== SERVICE INTERFACES =====
// These interfaces define the expected contracts for the extracted services

/**
 * Interface for ColorCrudService - handles basic CRUD operations
 */
interface IColorCrudService {
  getAll(organizationId: string): Promise<any[]>;
  getAllWithUsage(organizationId: string): Promise<ColorWithUsageResponse>;
  getById(colorId: string, organizationId: string): Promise<any | null>;
  create(colorData: NewColorEntry, organizationId: string, userId?: string): Promise<string>;
  update(colorId: string, updates: UpdateColorEntry, organizationId: string, userId?: string): Promise<boolean>;
  delete(colorId: string, organizationId: string, userId?: string): Promise<boolean>;
  clearAll(organizationId: string, hardDelete?: boolean): Promise<{ success: boolean; deletedCount: number }>;
}

/**
 * Interface for ColorQueryService - handles queries and searches
 */
interface IColorQueryService {
  getUsageCounts(organizationId: string): Promise<Record<string, { count: number; products: string[] }>>;
  getProductsByColorName(organizationId: string): Promise<Record<string, any[]>>;
  searchColors(organizationId: string, query: string, options?: any): Promise<any[]>;
  findSimilarColors(organizationId: string, hex: string, tolerance?: number): Promise<any[]>;
}

/**
 * Interface for ColorValidationService - handles validation and standardization
 */
interface IColorValidationService {
  validateColorData(colorData: NewColorEntry | UpdateColorEntry): Promise<{ isValid: boolean; errors: string[] }>;
  normalizeColorCodes(organizationId: string, sourceType?: string): Promise<{ success: boolean; message: string; updated: number }>;
  validateHex(hex: string): { isValid: boolean; errors: string[] };
  validateCMYK(cmyk: string): { isValid: boolean; errors: string[] };
}

/**
 * Interface for ColorImportService - handles import/export operations
 */
interface IColorImportService {
  importColors(filePath: string, organizationId: string, mergeMode: 'replace' | 'merge', format: 'json' | 'csv'): Promise<{ added: number; errors: string[] }>;
  exportColors(filePath: string, organizationId: string, format: 'json' | 'csv'): Promise<boolean>;
  validateImportFile(filePath: string, format: 'json' | 'csv'): Promise<{ isValid: boolean; errors: string[]; colorCount: number }>;
}

/**
 * Interface for ColorAnalysisService - handles analysis and comparison operations
 */
interface IColorAnalysisService {
  analyzeColor(hex: string): Promise<{ rgb: any; hsl: any; lab: any; cmyk: any }>;
  compareColors(hex1: string, hex2: string): Promise<{ deltaE: number; similarity: string }>;
  generateColorPalette(baseHex: string, type: 'complementary' | 'triadic' | 'analogous', count?: number): Promise<string[]>;
  analyzeColorHarmony(colors: string[]): Promise<{ harmony: string; score: number; suggestions: string[] }>;
}

/**
 * Interface for ColorDataService - handles data transformation and formatting
 */
interface IColorDataService {
  transformToColorEntry(rawData: any, organizationId: string): any;
  formatColorSpaces(colorData: any): any;
  generateColorMetadata(colorData: any): any;
  optimizeColorData(colors: any[]): any[];
}

// ===== SERVICE DEPENDENCIES =====

/**
 * Service dependencies interface for dependency injection
 */
interface ColorServices {
  colorCrudService: IColorCrudService;
  colorQueryService: IColorQueryService;
  colorValidationService: IColorValidationService;
  colorImportService: IColorImportService;
  colorAnalysisService: IColorAnalysisService;
  colorDataService: IColorDataService;
}

// ===== THIN HANDLER IMPLEMENTATIONS =====

/**
 * Register thin color IPC handlers with dependency injection
 * 
 * @param services - Injected service dependencies
 */
export function registerThinColorHandlers(services: ColorServices): void {
  console.log('[ThinColorIPC] 🚀 Registering thin color handlers with extracted services...');

  // Validate service dependencies
  if (!services.colorCrudService) {
    throw new Error('ColorCrudService is required for thin color handlers');
  }
  if (!services.colorQueryService) {
    throw new Error('ColorQueryService is required for thin color handlers');
  }
  if (!services.colorValidationService) {
    throw new Error('ColorValidationService is required for thin color handlers');
  }
  if (!services.colorImportService) {
    throw new Error('ColorImportService is required for thin color handlers');
  }
  if (!services.colorAnalysisService) {
    throw new Error('ColorAnalysisService is required for thin color handlers');
  }
  if (!services.colorDataService) {
    throw new Error('ColorDataService is required for thin color handlers');
  }

  console.log('[ThinColorIPC] ✅ All required services are available');

  // ===== CORE CRUD OPERATIONS =====

  /**
   * Get all colors - delegates to ColorCrudService
   */
  registerSecureHandler(
    ColorChannels.GET_ALL,
    async (organizationId: string): Promise<IPCResponse<any[]>> => {
      try {
        const colors = await services.colorCrudService.getAll(organizationId);
        return createSuccessResponse(colors, `Retrieved ${colors.length} colors`);
      } catch (error) {
        console.error('[ThinColorIPC] Error in GET_ALL:', error);
        return createErrorResponse(error, 'Failed to retrieve colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to retrieve colors. Please try again.'
    }
  );

  /**
   * Get all colors with usage data - delegates to ColorCrudService
   */
  registerSecureHandler(
    ColorChannels.GET_ALL_WITH_USAGE,
    async (organizationId: string): Promise<IPCResponse<ColorWithUsageResponse>> => {
      try {
        const result = await services.colorCrudService.getAllWithUsage(organizationId);
        return createSuccessResponse(result, `Retrieved ${result.colors?.length || 0} colors with usage data`);
      } catch (error) {
        console.error('[ThinColorIPC] Error in GET_ALL_WITH_USAGE:', error);
        return createErrorResponse(error, 'Failed to retrieve colors with usage data. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to retrieve colors with usage data. Please try again.'
    }
  );

  /**
   * Get color by ID - delegates to ColorCrudService
   */
  registerSecureHandler(
    ColorChannels.GET_BY_ID,
    async (organizationId: string, colorId: string): Promise<IPCResponse<any | null>> => {
      try {
        if (!colorId || typeof colorId !== 'string') {
          return createErrorResponse('Invalid color ID provided', 'Please provide a valid color ID.');
        }

        const color = await services.colorCrudService.getById(colorId, organizationId);
        return createSuccessResponse(color, color ? 'Color retrieved successfully' : 'Color not found');
      } catch (error) {
        console.error('[ThinColorIPC] Error in GET_BY_ID:', error);
        return createErrorResponse(error, 'Failed to retrieve color. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to retrieve color. Please try again.'
    }
  );

  /**
   * Add new color - delegates to ColorCrudService with validation
   * ENFORCES product association requirement
   */
  registerSecureHandler(
    ColorChannels.ADD,
    async (organizationId: string, colorData: NewColorEntry): Promise<IPCResponse<any>> => {
      try {
        if (!colorData || typeof colorData !== 'object') {
          return createErrorResponse('Invalid color data provided', 'Please provide valid color information.');
        }

        // CRITICAL: Enforce product requirement - no orphaned colors allowed
        if (!colorData.product || typeof colorData.product !== 'string' || colorData.product.trim().length === 0) {
          return createErrorResponse(
            'Product association required', 
            'Every color must be associated with a product. Please specify which product this color belongs to.'
          );
        }

        // Validate color data using ColorValidationService
        const validation = await services.colorValidationService.validateColorData(colorData);
        if (!validation.isValid) {
          return createErrorResponse(
            `Validation failed: ${validation.errors.join(', ')}`,
            'Please check your color data and try again.'
          );
        }

        // Get current user ID for product-color association
        let userId: string | undefined;
        try {
          const { ServiceLocator } = await import('../services/service-locator');
          const oauthService = ServiceLocator.getOAuthService();
          const currentUser = await oauthService.getCurrentUser();
          userId = currentUser?.id;
          console.log(`[ThinColorIPC] Current user for color creation: ${userId}`);
        } catch (error) {
          console.warn('[ThinColorIPC] Could not get current user ID:', error);
          // Continue without user ID - the color service will handle this gracefully
        }

        const newColorId = await services.colorCrudService.create(colorData, organizationId, userId);
        const newColor = await services.colorCrudService.getById(newColorId, organizationId);
        
        return createSuccessResponse(newColor, 'Color added successfully with product association');
      } catch (error) {
        console.error('[ThinColorIPC] Error in ADD:', error);
        return createErrorResponse(error, 'Failed to add color. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to add color. Please try again.'
    }
  );

  /**
   * Update color - delegates to ColorCrudService with validation
   */
  registerSecureHandler(
    ColorChannels.UPDATE,
    async (organizationId: string, colorId: string, updates: UpdateColorEntry): Promise<IPCResponse<boolean>> => {
      try {
        if (!colorId || typeof colorId !== 'string') {
          return createErrorResponse('Invalid color ID provided', 'Please provide a valid color ID.');
        }

        if (!updates || typeof updates !== 'object') {
          return createErrorResponse('Invalid update data provided', 'Please provide valid update information.');
        }

        // Validate update data using ColorValidationService
        const validation = await services.colorValidationService.validateColorData(updates);
        if (!validation.isValid) {
          return createErrorResponse(
            `Validation failed: ${validation.errors.join(', ')}`,
            'Please check your update data and try again.'
          );
        }

        const updated = await services.colorCrudService.update(colorId, updates, organizationId);
        return createSuccessResponse(updated, updated ? 'Color updated successfully' : 'Color update failed');
      } catch (error) {
        console.error('[ThinColorIPC] Error in UPDATE:', error);
        return createErrorResponse(error, 'Failed to update color. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to update color. Please try again.'
    }
  );

  /**
   * Delete color - delegates to ColorCrudService
   */
  registerSecureHandler(
    ColorChannels.DELETE,
    async (organizationId: string, colorId: string): Promise<IPCResponse<boolean>> => {
      try {
        if (!colorId || typeof colorId !== 'string') {
          return createErrorResponse('Invalid color ID provided', 'Please provide a valid color ID.');
        }

        const result = await services.colorCrudService.delete(colorId, organizationId);
        return createSuccessResponse(result, result ? 'Color deleted successfully' : 'Color deletion failed');
      } catch (error) {
        console.error('[ThinColorIPC] Error in DELETE:', error);
        return createErrorResponse(error, 'Failed to delete color. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to delete color. Please try again.'
    }
  );

  /**
   * Clear all colors - delegates to ColorCrudService
   */
  registerSecureHandler(
    ColorChannels.CLEAR_ALL,
    async (organizationId: string, hardDelete?: boolean): Promise<IPCResponse<any>> => {
      try {
        const result = await services.colorCrudService.clearAll(organizationId, hardDelete);
        return createSuccessResponse(result, `Cleared ${result.deletedCount || 0} colors successfully`);
      } catch (error) {
        console.error('[ThinColorIPC] Error in CLEAR_ALL:', error);
        return createErrorResponse(error, 'Failed to clear colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to clear colors. Please try again.'
    }
  );

  // ===== QUERY AND ANALYTICS OPERATIONS =====

  /**
   * Get usage counts - delegates to ColorQueryService
   */
  registerSecureHandler(
    ColorChannels.GET_USAGE_COUNTS,
    async (organizationId: string): Promise<IPCResponse<Record<string, { count: number; products: string[] }>>> => {
      try {
        const usageCounts = await services.colorQueryService.getUsageCounts(organizationId);
        return createSuccessResponse(usageCounts, `Retrieved usage data for ${Object.keys(usageCounts).length} colors`);
      } catch (error) {
        console.error('[ThinColorIPC] Error in GET_USAGE_COUNTS:', error);
        return createErrorResponse(error, 'Failed to retrieve color usage data. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to retrieve color usage data. Please try again.'
    }
  );

  /**
   * Get products by color name - delegates to ColorQueryService
   */
  registerSecureHandler(
    ColorChannels.GET_PRODUCTS_BY_COLOR_NAME,
    async (organizationId: string): Promise<IPCResponse<Record<string, any[]>>> => {
      try {
        const productsByColor = await services.colorQueryService.getProductsByColorName(organizationId);
        return createSuccessResponse(productsByColor, `Retrieved product associations for ${Object.keys(productsByColor).length} colors`);
      } catch (error) {
        console.error('[ThinColorIPC] Error in GET_PRODUCTS_BY_COLOR_NAME:', error);
        return createErrorResponse(error, 'Failed to retrieve color-product associations. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to retrieve color-product associations. Please try again.'
    }
  );

  // ===== IMPORT/EXPORT OPERATIONS =====

  /**
   * Import colors - delegates to ColorImportService
   */
  registerSecureHandler(
    ColorChannels.IMPORT,
    async (organizationId: string, mergeMode?: 'replace' | 'merge', filePath?: string, format?: 'json' | 'csv'): Promise<IPCResponse<any>> => {
      try {
        // Validate parameters
        const finalMergeMode = mergeMode || 'merge';
        const finalFormat = format || 'json';

        if (!filePath) {
          return createErrorResponse('File path is required for import', 'Please provide a valid file path.');
        }

        // Validate import file first
        const fileValidation = await services.colorImportService.validateImportFile(filePath, finalFormat);
        if (!fileValidation.isValid) {
          return createErrorResponse(
            `Import file validation failed: ${fileValidation.errors.join(', ')}`,
            'Please check your import file and try again.'
          );
        }

        const importResult = await services.colorImportService.importColors(
          filePath,
          organizationId,
          finalMergeMode,
          finalFormat
        );

        return createSuccessResponse(
          importResult,
          `Import completed: ${importResult.added} colors added${importResult.errors.length > 0 ? `, ${importResult.errors.length} errors` : ''}`
        );
      } catch (error) {
        console.error('[ThinColorIPC] Error in IMPORT:', error);
        return createErrorResponse(error, 'Failed to import colors. Please check your file format and try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to import colors. Please check your file format and try again.'
    }
  );

  /**
   * Export colors - delegates to ColorImportService
   */
  registerSecureHandler(
    ColorChannels.EXPORT,
    async (organizationId: string, filePath?: string, format?: 'json' | 'csv'): Promise<IPCResponse<boolean>> => {
      try {
        if (!filePath) {
          return createErrorResponse('File path is required for export', 'Please provide a valid file path.');
        }

        const finalFormat = format || 'json';
        const success = await services.colorImportService.exportColors(filePath, organizationId, finalFormat);

        return createSuccessResponse(
          success,
          success ? `Colors exported successfully to ${filePath}` : 'Export failed'
        );
      } catch (error) {
        console.error('[ThinColorIPC] Error in EXPORT:', error);
        return createErrorResponse(error, 'Failed to export colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to export colors. Please try again.'
    }
  );

  // ===== VALIDATION AND UTILITY OPERATIONS =====

  /**
   * Normalize Pantone codes - delegates to ColorValidationService
   */
  registerSecureHandler(
    ColorChannels.NORMALIZE_PANTONE_CODES,
    async (organizationId: string): Promise<IPCResponse<any>> => {
      try {
        const result = await services.colorValidationService.normalizeColorCodes(organizationId, 'pantone');
        return createSuccessResponse(result, result.success ? result.message : 'Normalization completed with issues');
      } catch (error) {
        console.error('[ThinColorIPC] Error in NORMALIZE_PANTONE_CODES:', error);
        return createErrorResponse(error, 'Failed to normalize Pantone codes. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to normalize Pantone codes. Please try again.'
    }
  );

  /**
   * Clear frontend state (system-level operation, no service delegation needed)
   */
  registerSystemHandler(
    ColorChannels.CLEAR_FRONTEND_STATE,
    async (): Promise<IPCResponse<boolean>> => {
      try {
        // This is a frontend state clearing operation - no backend action needed
        return createSuccessResponse(true, 'Frontend state cleared');
      } catch (error) {
        console.error('[ThinColorIPC] Error in CLEAR_FRONTEND_STATE:', error);
        return createErrorResponse(error, 'Failed to clear frontend state.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to clear frontend state.'
    }
  );

  /**
   * Admin clear all - delegates to ColorCrudService
   */
  registerSecureHandler(
    ColorChannels.ADMIN_CLEAR_ALL,
    async (organizationId: string, options?: { hardDelete?: boolean }): Promise<IPCResponse<any>> => {
      try {
        const result = await services.colorCrudService.clearAll(organizationId, options?.hardDelete);
        return createSuccessResponse(
          result,
          `Admin clear completed: ${result.deletedCount || 0} colors removed`
        );
      } catch (error) {
        console.error('[ThinColorIPC] Error in ADMIN_CLEAR_ALL:', error);
        return createErrorResponse(error, 'Failed to perform admin clear operation. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to perform admin clear operation. Please try again.'
    }
  );

  console.log('[ThinColorIPC] ✅ All thin color handlers registered successfully');
}

// ===== EXTENDED HANDLERS FOR ADDITIONAL OPERATIONS =====

/**
 * Register extended thin color handlers for advanced operations
 * These handlers provide additional functionality beyond the basic color channels
 * 
 * @param services - Injected service dependencies
 */
export function registerExtendedThinColorHandlers(services: ColorServices): void {
  console.log('[ThinColorIPC] 🚀 Registering extended thin color handlers...');

  /**
   * Analyze color - delegates to ColorAnalysisService
   */
  registerSecureHandler(
    'color:analyze',
    async (_organizationId: string, hex: string): Promise<IPCResponse<any>> => {
      try {
        if (!hex || typeof hex !== 'string') {
          return createErrorResponse('Invalid hex color provided', 'Please provide a valid hex color.');
        }

        // Validate hex using ColorValidationService
        const validation = services.colorValidationService.validateHex(hex);
        if (!validation.isValid) {
          return createErrorResponse(
            `Invalid hex color: ${validation.errors.join(', ')}`,
            'Please provide a valid hex color code.'
          );
        }

        const analysis = await services.colorAnalysisService.analyzeColor(hex);
        return createSuccessResponse(analysis, 'Color analysis completed successfully');
      } catch (error) {
        console.error('[ThinColorIPC] Error in color:analyze:', error);
        return createErrorResponse(error, 'Failed to analyze color. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to analyze color. Please try again.'
    }
  );

  /**
   * Compare colors - delegates to ColorAnalysisService
   */
  registerSecureHandler(
    'color:compare',
    async (_organizationId: string, hex1: string, hex2: string): Promise<IPCResponse<any>> => {
      try {
        if (!hex1 || !hex2 || typeof hex1 !== 'string' || typeof hex2 !== 'string') {
          return createErrorResponse('Two valid hex colors are required', 'Please provide two valid hex colors.');
        }

        // Validate both colors
        const validation1 = services.colorValidationService.validateHex(hex1);
        const validation2 = services.colorValidationService.validateHex(hex2);
        
        if (!validation1.isValid || !validation2.isValid) {
          return createErrorResponse(
            'Invalid hex colors provided',
            'Please provide valid hex color codes for both colors.'
          );
        }

        const comparison = await services.colorAnalysisService.compareColors(hex1, hex2);
        return createSuccessResponse(comparison, 'Color comparison completed successfully');
      } catch (error) {
        console.error('[ThinColorIPC] Error in color:compare:', error);
        return createErrorResponse(error, 'Failed to compare colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to compare colors. Please try again.'
    }
  );

  /**
   * Search colors - delegates to ColorQueryService
   */
  registerSecureHandler(
    'color:search',
    async (organizationId: string, query: string, options?: any): Promise<IPCResponse<any[]>> => {
      try {
        if (!query || typeof query !== 'string' || query.trim().length === 0) {
          return createErrorResponse('Search query is required', 'Please provide a valid search query.');
        }

        const results = await services.colorQueryService.searchColors(organizationId, query.trim(), options);
        return createSuccessResponse(results, `Found ${results.length} colors matching "${query}"`);
      } catch (error) {
        console.error('[ThinColorIPC] Error in color:search:', error);
        return createErrorResponse(error, 'Failed to search colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to search colors. Please try again.'
    }
  );

  /**
   * Find similar colors - delegates to ColorQueryService
   */
  registerSecureHandler(
    'color:findSimilar',
    async (organizationId: string, hex: string, tolerance?: number): Promise<IPCResponse<any[]>> => {
      try {
        if (!hex || typeof hex !== 'string') {
          return createErrorResponse('Hex color is required', 'Please provide a valid hex color.');
        }

        // Validate hex
        const validation = services.colorValidationService.validateHex(hex);
        if (!validation.isValid) {
          return createErrorResponse(
            `Invalid hex color: ${validation.errors.join(', ')}`,
            'Please provide a valid hex color code.'
          );
        }

        const similarColors = await services.colorQueryService.findSimilarColors(organizationId, hex, tolerance);
        return createSuccessResponse(similarColors, `Found ${similarColors.length} similar colors`);
      } catch (error) {
        console.error('[ThinColorIPC] Error in color:findSimilar:', error);
        return createErrorResponse(error, 'Failed to find similar colors. Please try again.');
      }
    },
    ipcMain,
    {
      logChannel: 'ThinColorIPC',
      customErrorMessage: 'Failed to find similar colors. Please try again.'
    }
  );

  console.log('[ThinColorIPC] ✅ Extended thin color handlers registered successfully');
}

/**
 * Unregister all thin color handlers
 */
export function unregisterThinColorHandlers(): void {
  const allChannels = [
    // Core channels
    ...Object.values(ColorChannels),
    // Extended channels
    'color:analyze',
    'color:compare',
    'color:search',
    'color:findSimilar'
  ];

  allChannels.forEach(channel => {
    try {
      ipcMain.removeAllListeners(channel);
    } catch (error) {
      console.warn(`[ThinColorIPC] Failed to unregister handler for ${channel}:`, error);
    }
  });

  console.log('[ThinColorIPC] 🧹 All thin color handlers unregistered');
}