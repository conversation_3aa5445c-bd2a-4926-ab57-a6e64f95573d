/**
 * @file channels.ts
 * @description IPC channel constants for the application
 */

export enum DatasheetChannels {
  GET_BY_PRODUCT = 'datasheet:getByProduct',
  ADD_TO_PRODUCT = 'datasheet:addToProduct',
  ADD_WEB_LINK = 'datasheet:addWebLink',
  REMOVE = 'datasheet:remove',
  OPEN = 'datasheet:open',
  OPEN_ALL = 'datasheet:openAll',
  MIGRATE = 'datasheet:migrate',
}

export enum TestDataChannels {
  CREATE_TEST_PRODUCT = 'test-data:createTestProduct',
  REMOVE_TEST_DATA = 'test-data:removeTestData',
}

export enum ErrorChannels {
  LOG_ERROR = 'log-error',
}

export enum ColorSpace3DChannels {
  OPEN_WINDOW = 'color-space-3d:open-window',
  CLOSE_WINDOW = 'color-space-3d:close-window',
  UPDATE_COLORS = 'color-space-3d:update-colors',
}
