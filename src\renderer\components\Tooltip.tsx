import { useState, ReactNode, useRef, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { useTokens } from '../hooks/useTokens';

interface TooltipProps {
  children: ReactNode;
  content: string;
  position?: 'top' | 'right' | 'bottom' | 'left';
  delay?: number;
  maxWidth?: number;
}

function Tooltip({
  children,
  content,
  position = 'top',
  delay = 300,
  maxWidth = 250,
}: TooltipProps) {
  const tokens = useTokens();
  const [isVisible, setIsVisible] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLSpanElement>(null);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });

  const showTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      updateTooltipPosition();
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Calculate position relative to the viewport
  const updateTooltipPosition = () => {
    if (!triggerRef.current) {
      return;
    }

    const triggerRect = triggerRef.current.getBoundingClientRect();

    // We need to wait for the tooltip to be rendered before getting its size
    setTimeout(() => {
      if (!tooltipRef.current) {
        return;
      }

      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      let top = 0;
      let left = 0;

      switch (position) {
        case 'top':
          top = triggerRect.top - tooltipRect.height - 8;
          left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
          break;
        case 'right':
          top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
          left = triggerRect.right + 8;
          break;
        case 'bottom':
          top = triggerRect.bottom + 8;
          left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
          break;
        case 'left':
          top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
          left = triggerRect.left - tooltipRect.width - 8;
          break;
      }

      // Keep tooltip within viewport bounds
      if (left < 8) {
        left = 8;
      }
      if (left + tooltipRect.width > window.innerWidth - 8) {
        left = window.innerWidth - tooltipRect.width - 8;
      }

      if (top < 8) {
        top = 8;
      }
      if (top + tooltipRect.height > window.innerHeight - 8) {
        top = window.innerHeight - tooltipRect.height - 8;
      }

      setTooltipPosition({ top, left });
    }, 0);
  };

  // Update tooltip position when it becomes visible
  useEffect(() => {
    if (isVisible) {
      updateTooltipPosition();
      // Update position on scroll or resize
      window.addEventListener('scroll', updateTooltipPosition, true);
      window.addEventListener('resize', updateTooltipPosition);

      return () => {
        window.removeEventListener('scroll', updateTooltipPosition, true);
        window.removeEventListener('resize', updateTooltipPosition);
      };
    }

    return undefined;
  }, [isVisible, position]);

  // Render tooltip content through a portal
  const renderTooltip = () => {
    if (!isVisible) {
      return null;
    }

    return ReactDOM.createPortal(
      <div
        ref={tooltipRef}
        className='fixed px-2 py-1 text-xs pointer-events-none'
        style={{
          maxWidth: `${maxWidth}px`,
          transition: `opacity ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`,
          boxShadow: tokens.shadows.md,
          top: `${tooltipPosition.top  }px`,
          left: `${tooltipPosition.left  }px`,
          zIndex: 'var(--z-tooltip)',
          backgroundColor: 'var(--color-ui-background-tertiary)',
          color: 'var(--color-ui-foreground-primary)',
          border: `1px solid var(--color-ui-border-medium)`,
          borderRadius: 'var(--radius-md)',
          fontSize: 'var(--font-size-xs)',
        }}
      >
        {content}
      </div>,
      document.body
    );
  };

  return (
    <>
      <span
        ref={triggerRef}
        className='inline-flex items-center relative cursor-help'
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
      >
        {children}
      </span>
      {renderTooltip()}
    </>
  );
}

export default Tooltip;
