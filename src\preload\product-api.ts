/**
 * @file product-api.ts
 * @description Product API preload script for secure IPC communication
 */

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Typed API for product operations
contextBridge.exposeInMainWorld('productAPI', {
  // Get all products
  getAll: () => {
    console.log('Preload: calling productAPI.getAll');
    return ipcRenderer.invoke('product:getAll');
  },

  // Get all products with their colors
  getAllWithColors: () => {
    console.log('Preload: calling productAPI.getAllWithColors');
    return ipcRenderer.invoke('product:getAllWithColors');
  },

  // Add color to product
  addColor: (productId: string, colorId: string) => {
    console.log(`Preload: calling productAPI.addColor for product ${productId}, color ${colorId}`);
    return ipcRenderer.invoke('product:addColor', productId, colorId);
  },

  // Add library color to product
  addLibraryColor: (productId: string, libraryColor: any, customName?: string) => {
    console.log(`Preload: calling productAPI.addLibraryColor for product ${productId}`);
    return ipcRenderer.invoke('product:addLibraryColor', productId, libraryColor, customName);
  },

  // Remove color from product
  removeColor: (productId: string, colorId: string) => {
    console.log(`Preload: calling productAPI.removeColor for product ${productId}, color ${colorId}`);
    return ipcRenderer.invoke('product:removeColor', productId, colorId);
  },

  // Add new product
  add: (data: { name: string; metadata?: any }) => {
    console.log(`Preload: calling productAPI.add for ${data.name}`);
    return ipcRenderer.invoke('product:add', data);
  },

  // Delete product
  delete: (productId: string) => {
    console.log(`Preload: calling productAPI.delete for ${productId}`);
    return ipcRenderer.invoke('product:delete', productId);
  },

  // Open product datasheet
  openDatasheet: (product: string) => {
    console.log(`Preload: calling productAPI.openDatasheet for ${product}`);
    return ipcRenderer.invoke('product:open-datasheet', product);
  },

  // Deduplicate products
  deduplicate: () => {
    console.log('Preload: calling productAPI.deduplicate');
    return ipcRenderer.invoke('product:deduplicate');
  }
});