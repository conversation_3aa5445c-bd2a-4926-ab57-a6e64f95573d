/**
 * @file SyncDetailedView.tsx
 * @description Detailed sync status modal component extracted from UnifiedSyncIndicator
 */

import React from 'react';

interface SyncProgress {
  phase: string;
  progress: number; // 0-100
  currentOperation: string;
  itemsProcessed: number;
  itemsTotal: number;
  estimatedTimeRemaining?: number;
  errors: string[];
  warnings: string[];
}

interface QueueStats {
  memoryQueue: {
    size: number;
    pending: number;
    failed: number;
  };
  persistentQueue: {
    size: number;
    pending: number;
    failed: number;
    oldestItemAge: number;
  };
}

interface SyncMetrics {
  performance: {
    averageResponseTime: number;
    operationsPerMinute: number;
    errorRate: number;
  };
  health: {
    status: string;
    networkQuality: string;
    lastHealthCheck: number;
  };
}

interface SyncDetailedViewProps {
  isOpen: boolean;
  onClose: () => void;
  syncProgress: SyncProgress | null;
  queueStats: QueueStats | null;
  metrics: SyncMetrics | null;
  lastSync: number | null;
  getStatusText: () => string;
  getStatusColor: () => string;
  getProgressPercentage: () => number;
  formatTimeRemaining: (seconds: number) => string;
}

export const SyncDetailedView: React.FC<SyncDetailedViewProps> = ({
  isOpen,
  onClose,
  syncProgress,
  queueStats,
  metrics,
  lastSync,
  getStatusText,
  getStatusColor,
  getProgressPercentage,
  formatTimeRemaining,
}) => {
  if (!isOpen) {
    return null;
  }

  return (
    <div
      className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'
      onClick={onClose}
    >
      <div
        className='bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl'
        onClick={e => e.stopPropagation()}
      >
        <div className='flex items-center justify-between mb-4'>
          <h3 className='text-lg font-semibold'>Sync Status</h3>
          <button
            onClick={onClose}
            className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
          >
            ✕
          </button>
        </div>

        <div className='space-y-3 text-sm'>
          <div className='flex justify-between'>
            <span>Status:</span>
            <span className={getStatusColor()}>{getStatusText()}</span>
          </div>

          {syncProgress && (
            <>
              <div className='flex justify-between'>
                <span>Phase:</span>
                <span>{syncProgress.phase}</span>
              </div>
              <div className='flex justify-between'>
                <span>Progress:</span>
                <span>
                  {(() => {
                    const percentage = getProgressPercentage();
                    return isNaN(percentage)
                      ? '0%'
                      : `${Math.round(percentage)}%`;
                  })()}
                </span>
              </div>
              {syncProgress.itemsTotal > 0 && (
                <div className='flex justify-between'>
                  <span>Items:</span>
                  <span>
                    {syncProgress.itemsProcessed}/{syncProgress.itemsTotal}
                  </span>
                </div>
              )}
              {syncProgress.estimatedTimeRemaining && (
                <div className='flex justify-between'>
                  <span>Time remaining:</span>
                  <span>
                    {formatTimeRemaining(syncProgress.estimatedTimeRemaining)}
                  </span>
                </div>
              )}
            </>
          )}

          {queueStats && (
            <>
              <div className='flex justify-between'>
                <span>Queue (memory):</span>
                <span>
                  {queueStats.memoryQueue?.pending || 0} pending,{' '}
                  {queueStats.memoryQueue?.failed || 0} failed
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Queue (persistent):</span>
                <span>
                  {queueStats.persistentQueue?.pending || 0} pending,{' '}
                  {queueStats.persistentQueue?.failed || 0} failed
                </span>
              </div>
            </>
          )}

          {metrics && metrics.performance && metrics.health && (
            <>
              <div className='flex justify-between'>
                <span>Response time:</span>
                <span>
                  {Math.round(metrics.performance.averageResponseTime)}ms
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Operations/min:</span>
                <span>
                  {Math.round(metrics.performance.operationsPerMinute)}
                </span>
              </div>
              <div className='flex justify-between'>
                <span>Error rate:</span>
                <span>{(metrics.performance.errorRate * 100).toFixed(1)}%</span>
              </div>
              <div className='flex justify-between'>
                <span>Network quality:</span>
                <span>{metrics.health.networkQuality}</span>
              </div>
            </>
          )}

          {lastSync && (
            <div className='flex justify-between'>
              <span>Last sync:</span>
              <span>{new Date(lastSync).toLocaleString()}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
