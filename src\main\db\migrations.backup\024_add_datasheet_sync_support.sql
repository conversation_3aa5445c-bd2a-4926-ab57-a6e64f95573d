-- Migration 024: Add Datasheet Sync Support
-- Adds missing columns to datasheets table for sync functionality
-- This migration ensures fresh installs and existing databases have sync-compatible datasheet tables

-- First, ensure the datasheets table exists with basic structure
CREATE TABLE IF NOT EXISTS datasheets (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  external_id TEXT NOT NULL UNIQUE,
  product_id INTEGER NOT NULL,
  name TEXT NOT NULL,
  url TEXT NOT NULL,
  file_type TEXT NOT NULL CHECK (file_type IN ('pdf', 'docx', 'xlsx', 'pptx', 'link', 'other')),
  is_external INTEGER NOT NULL DEFAULT 1,
  description TEXT,
  tags TEXT,
  metadata TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by TEXT,
  updated_by TEXT,
  is_active INTEGER NOT NULL DEFAULT 1,
  FOREI<PERSON><PERSON> KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Add sync-required columns
-- Note: SQLite doesn't support IF NOT EXISTS for ADD COLUMN
-- The migration runner handles duplicate column errors gracefully

-- Add organization_id column for multi-tenant support
ALTER TABLE datasheets ADD COLUMN organization_id TEXT;

-- Add user_id column for sync operations
ALTER TABLE datasheets ADD COLUMN user_id TEXT;

-- Add device_id column for conflict resolution
ALTER TABLE datasheets ADD COLUMN device_id TEXT;

-- Add deleted_at column for soft delete functionality
ALTER TABLE datasheets ADD COLUMN deleted_at TEXT;

-- Add sync_version column for incremental sync
ALTER TABLE datasheets ADD COLUMN sync_version INTEGER DEFAULT 1;

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_datasheets_organization_id ON datasheets(organization_id);
CREATE INDEX IF NOT EXISTS idx_datasheets_user_id ON datasheets(user_id);
CREATE INDEX IF NOT EXISTS idx_datasheets_device_id ON datasheets(device_id);
CREATE INDEX IF NOT EXISTS idx_datasheets_deleted_at ON datasheets(deleted_at);
CREATE INDEX IF NOT EXISTS idx_datasheets_sync_version ON datasheets(sync_version);

-- Composite indexes for sync operations
CREATE INDEX IF NOT EXISTS idx_datasheets_org_active ON datasheets(organization_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_datasheets_org_updated ON datasheets(organization_id, updated_at DESC) WHERE deleted_at IS NULL;

-- Update existing records to have proper organization_id and user_id
-- This will be populated from the product's organization context
UPDATE datasheets 
SET organization_id = (
  SELECT p.organization_id 
  FROM products p 
  WHERE p.id = datasheets.product_id
),
user_id = (
  SELECT p.user_id 
  FROM products p 
  WHERE p.id = datasheets.product_id
)
WHERE organization_id IS NULL OR user_id IS NULL;

-- Ensure all existing datasheets have external_id (UUID format)
UPDATE datasheets 
SET external_id = lower(hex(randomblob(4))) || '-' || 
                  lower(hex(randomblob(2))) || '-' || 
                  '4' || substr(lower(hex(randomblob(2))), 2) || '-' || 
                  substr('ab89', abs(random()) % 4 + 1, 1) || 
                  substr(lower(hex(randomblob(2))), 2) || '-' || 
                  lower(hex(randomblob(6)))
WHERE external_id IS NULL OR external_id = '';

-- Create updated trigger for sync_version
DROP TRIGGER IF EXISTS update_datasheets_timestamp;
CREATE TRIGGER update_datasheets_timestamp 
AFTER UPDATE ON datasheets
FOR EACH ROW
WHEN NEW.updated_at = OLD.updated_at
BEGIN
  UPDATE datasheets 
  SET updated_at = CURRENT_TIMESTAMP,
      sync_version = OLD.sync_version + 1
  WHERE id = NEW.id;
END;