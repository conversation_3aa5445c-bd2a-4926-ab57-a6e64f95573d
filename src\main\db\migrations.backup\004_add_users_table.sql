-- Migration: Add users table for organization member display
-- Version: 004
-- Date: 2025-05-30

-- Create users table to store user information for organization members
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY, -- UUID as TEXT in SQLite
    email TEXT NOT NULL UNIQUE,
    name TEXT,
    avatar_url TEXT,
    metadata TEXT DEFAULT '{}', -- <PERSON><PERSON><PERSON> stored as TEXT
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Update trigger for updated_at
CREATE TRIGGER IF NOT EXISTS update_users_updated_at
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Create invitation tables for team member invitations
CREATE TABLE IF NOT EXISTS organization_invitations (
    id INTEGER PRIMARY KEY,
    external_id TEXT UNIQUE NOT NULL DEFAULT (lower(hex(randomblob(16)))),
    organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
    invited_by TEXT NOT NULL, -- UUID of inviter
    token TEXT UNIQUE NOT NULL DEFAULT (lower(hex(randomblob(32)))),
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CHECK (expires_at > created_at),
    UNIQUE (organization_id, email)
);

-- Create index for invitation lookups
CREATE INDEX IF NOT EXISTS idx_invitations_token ON organization_invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_org_email ON organization_invitations(organization_id, email);
CREATE INDEX IF NOT EXISTS idx_invitations_expires ON organization_invitations(expires_at) WHERE accepted_at IS NULL;

-- Clean up expired invitations trigger
CREATE TRIGGER IF NOT EXISTS cleanup_expired_invitations
AFTER INSERT ON organization_invitations
BEGIN
    DELETE FROM organization_invitations 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND accepted_at IS NULL;
END;

-- Add sync marker for users
ALTER TABLE users ADD COLUMN synced_from_supabase BOOLEAN DEFAULT FALSE;
