/**
 * @file base.service.ts
 * @description Base service class for common database operations and patterns
 */

import Database from 'better-sqlite3';
import { databaseAuditLogger, auditDatabase } from '../../utils/database-audit-logger';
import { SafeQueryBuilder, QueryBuilderOptions } from '../../utils/query-builder';

export interface ILogger {
  info(message: string, ...args: unknown[]): void;
  warn(message: string, ...args: unknown[]): void;
  error(message: string, ...args: unknown[]): void;
  debug(message: string, ...args: unknown[]): void;
}

export interface IDatabaseConnection {
  prepare(sql: string): Database.Statement;
  transaction<T>(fn: () => T): T;
  pragma(pragma: string): Database.Database;
  close(): void;
}

export interface IOrganizationContext {
  getLocalOrgId(externalId: string): Promise<number | null>;
  validateOrganizationAccess(userId: string, orgId: string): Promise<boolean>;
}

/**
 * Base service class providing common database patterns and utilities
 */
export abstract class BaseDataService {
  protected readonly preparedStatements = new Map<string, Database.Statement>();
  protected readonly logger: ILogger;
  protected readonly auditWrapper = auditDatabase(databaseAuditLogger);
  
  constructor(
    protected readonly db: IDatabaseConnection,
    logger?: ILogger
  ) {
    this.logger = logger || this.createDefaultLogger();
  }

  /**
   * Get or create a prepared statement with caching
   */
  protected getPreparedStatement(sql: string): Database.Statement {
    if (!this.preparedStatements.has(sql)) {
      const statement = this.db.prepare(sql);
      this.preparedStatements.set(sql, statement);
    }
    return this.preparedStatements.get(sql)!;
  }

  /**
   * Validate SQL identifiers (table/column names) to prevent injection
   */
  protected validateIdentifier(identifier: string, type: 'table' | 'field'): void {
    // Allow alphanumeric, underscore, and must start with letter or underscore
    const validPattern = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
    
    if (!validPattern.test(identifier)) {
      throw new Error(`Invalid ${type} identifier: ${identifier}`);
    }

    // Additional security: check against known safe identifiers
    const allowedTables = ['colors', 'products', 'datasheets', 'organizations', 'users', 'product_colors'];
    const allowedFields = ['id', 'external_id', 'organization_id', 'deleted_at', 'created_at', 'updated_at'];

    if (type === 'table' && !allowedTables.includes(identifier)) {
      throw new Error(`Table not in allowed list: ${identifier}`);
    }

    if (type === 'field' && !allowedFields.includes(identifier) && !identifier.endsWith('_id')) {
      this.logger.warn(`Using non-standard field identifier: ${identifier}`);
    }
  }

  /**
   * Execute a transaction safely with error handling
   */
  protected executeTransaction<T>(fn: () => T, context?: string): T {
    try {
      const transaction = this.db.transaction(fn) as () => T;
      return transaction();
    } catch (error) {
      const contextMsg = context ? ` in ${context}` : '';
      this.logger.error(`Transaction failed${contextMsg}:`, error);
      throw error;
    }
  }

  /**
   * Get local organization ID from external organization ID
   */
  protected async getLocalOrganizationId(externalOrgId: string): Promise<number | null> {
    try {
      const stmt = this.getPreparedStatement(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const result = stmt.get(externalOrgId) as { id: number } | undefined;
      
      if (!result) {
        this.logger.warn(`Organization not found for external_id: ${externalOrgId}`);
        return null;
      }
      
      return result.id;
    } catch (error) {
      this.logger.error(`Error getting local organization ID for ${externalOrgId}:`, error);
      return null;
    }
  }

  /**
   * Validate organization access and get local ID
   */
  protected async validateAndGetOrgId(externalOrgId: string, context: string): Promise<number> {
    const localOrgId = await this.getLocalOrganizationId(externalOrgId);
    
    if (!localOrgId) {
      const availableOrgs = this.getPreparedStatement(
        'SELECT external_id, name FROM organizations LIMIT 10'
      ).all();
      
      this.logger.debug(`Available organizations:`, availableOrgs);
      throw new Error(`Organization not found: ${externalOrgId} (context: ${context})`);
    }
    
    return localOrgId;
  }

  /**
   * Generate standardized timestamps
   */
  protected getCurrentTimestamp(): string {
    return new Date().toISOString();
  }

  /**
   * Parse JSON properties safely
   */
  protected parseProperties<T = Record<string, unknown>>(propertiesString: string | null, defaultValue: T = {} as T): T {
    if (!propertiesString) {return defaultValue;}
    
    try {
      return JSON.parse(propertiesString) as T;
    } catch (error) {
      this.logger.warn(`Failed to parse properties: ${propertiesString}`, error);
      return defaultValue;
    }
  }

  /**
   * Stringify properties safely
   */
  protected stringifyProperties(properties: Record<string, unknown>): string {
    try {
      return JSON.stringify(properties);
    } catch (error) {
      this.logger.warn(`Failed to stringify properties:`, properties, error);
      return '{}';
    }
  }

  /**
   * Execute paginated query
   */
  protected getPaginatedResults<T>(
    baseQuery: string,
    params: unknown[],
    page: number = 1,
    pageSize: number = 100
  ): { data: T[]; totalCount: number; page: number; pageSize: number } {
    try {
      // Get total count
      const countQuery = `SELECT COUNT(*) as count FROM (${baseQuery})`;
      const countResult = this.getPreparedStatement(countQuery).get(...params) as { count: number };
      const totalCount = countResult.count;

      // Get paginated data
      const offset = (page - 1) * pageSize;
      const paginatedQuery = `${baseQuery} LIMIT ? OFFSET ?`;
      const data = this.getPreparedStatement(paginatedQuery).all(...params, pageSize, offset) as T[];

      return {
        data,
        totalCount,
        page,
        pageSize
      };
    } catch (error) {
      this.logger.error('Error executing paginated query:', error);
      return {
        data: [],
        totalCount: 0,
        page,
        pageSize
      };
    }
  }

  /**
   * Perform bulk insert with batch processing
   */
  protected async bulkInsert<T>(
    insertSql: string,
    items: T[],
    mapItemToParams: (item: T) => unknown[],
    options: {
      batchSize?: number;
      onProgress?: (progress: number) => void;
      onBatchComplete?: (batchIndex: number, batchSize: number) => void;
    } = {}
  ): Promise<{ success: number; failed: number; errors: Error[] }> {
    const { batchSize = 100, onProgress, onBatchComplete } = options;
    const insertStmt = this.getPreparedStatement(insertSql);
    const errors: Error[] = [];
    let successCount = 0;
    let failedCount = 0;

    try {
      for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        
        this.db.transaction(() => {
          for (const item of batch) {
            try {
              const params = mapItemToParams(item);
              insertStmt.run(...params);
              successCount++;
            } catch (error) {
              failedCount++;
              errors.push(error as Error);
              this.logger.warn('Failed to insert item:', error, item);
            }
          }
        });

        // Report progress
        if (onProgress) {
          const progress = Math.min(100, Math.round(((i + batch.length) / items.length) * 100));
          onProgress(progress);
        }

        if (onBatchComplete) {
          onBatchComplete(Math.floor(i / batchSize), batch.length);
        }
      }
    } catch (error) {
      this.logger.error('Error in bulk insert:', error);
      errors.push(error as Error);
    }

    return { success: successCount, failed: failedCount, errors };
  }

  /**
   * Soft delete pattern
   */
  protected softDelete(table: string, idField: string, id: string, organizationId?: string, userId?: string): boolean {
    try {
      // Validate table and field names for security
      this.validateIdentifier(table, 'table');
      this.validateIdentifier(idField, 'field');

      let sql = `UPDATE ${table} SET deleted_at = CURRENT_TIMESTAMP WHERE ${idField} = ?`;
      const params: unknown[] = [id];

      if (organizationId) {
        sql += ' AND organization_id = ?';
        params.push(organizationId);
      }

      sql += ' AND deleted_at IS NULL';

      return this.auditWrapper.wrapOperation(
        () => {
          const result = this.getPreparedStatement(sql).run(...params);
          return result.changes > 0;
        },
        {
          operationType: 'UPDATE',
          table,
          sql,
          parameters: params,
          recordId: id,
          organizationId,
          userId,
          context: 'soft_delete'
        }
      );
    } catch (error) {
      this.logger.error(`Error soft deleting from ${table}:`, error);
      return false;
    }
  }

  /**
   * Hard delete pattern
   */
  protected hardDelete(table: string, idField: string, id: string, organizationId?: string, userId?: string): boolean {
    try {
      // Validate table and field names for security
      this.validateIdentifier(table, 'table');
      this.validateIdentifier(idField, 'field');

      let sql = `DELETE FROM ${table} WHERE ${idField} = ?`;
      const params: unknown[] = [id];

      if (organizationId) {
        sql += ' AND organization_id = ?';
        params.push(organizationId);
      }

      return this.auditWrapper.wrapOperation(
        () => {
          const result = this.getPreparedStatement(sql).run(...params);
          return result.changes > 0;
        },
        {
          operationType: 'DELETE',
          table,
          sql,
          parameters: params,
          recordId: id,
          organizationId,
          userId,
          context: 'hard_delete'
        }
      );
    } catch (error) {
      this.logger.error(`Error hard deleting from ${table}:`, error);
      return false;
    }
  }

  /**
   * Check if record exists
   */
  protected recordExists(table: string, idField: string, id: string, organizationId?: string): boolean {
    try {
      // Validate table and field names for security
      this.validateIdentifier(table, 'table');
      this.validateIdentifier(idField, 'field');

      let sql = `SELECT 1 FROM ${table} WHERE ${idField} = ?`;
      const params: unknown[] = [id];

      if (organizationId) {
        sql += ' AND organization_id = ?';
        params.push(organizationId);
      }

      sql += ' AND deleted_at IS NULL';

      const result = this.getPreparedStatement(sql).get(...params);
      return !!result;
    } catch (error) {
      this.logger.error(`Error checking if record exists in ${table}:`, error);
      return false;
    }
  }

  /**
   * Get record count
   */
  protected getRecordCount(table: string, whereClause?: string, params: unknown[] = []): number {
    try {
      // Validate table name for security
      this.validateIdentifier(table, 'table');

      let sql = `SELECT COUNT(*) as count FROM ${table}`;
      
      if (whereClause) {
        // Note: whereClause should be validated by caller or use parameterized conditions
        sql += ` WHERE ${whereClause}`;
      }

      const result = this.getPreparedStatement(sql).get(...params) as { count: number };
      return result.count;
    } catch (error) {
      this.logger.error(`Error getting record count from ${table}:`, error);
      return 0;
    }
  }

  /**
   * Build dynamic update SQL with only provided fields
   */
  protected buildUpdateSQL(
    table: string,
    updates: Record<string, unknown>,
    whereClause: string,
    excludeFields: string[] = []
  ): { sql: string; values: unknown[] } {
    // Validate table name for security
    this.validateIdentifier(table, 'table');

    const updateFields: string[] = [];
    const updateValues: unknown[] = [];

    for (const [field, value] of Object.entries(updates)) {
      if (value !== undefined && !excludeFields.includes(field)) {
        // Validate field name for security
        this.validateIdentifier(field, 'field');
        updateFields.push(`${field} = ?`);
        updateValues.push(value);
      }
    }

    if (updateFields.length === 0) {
      throw new Error('No fields to update');
    }

    // Add updated timestamp
    updateFields.push('updated_at = ?');
    updateValues.push(this.getCurrentTimestamp());

    const sql = `UPDATE ${table} SET ${updateFields.join(', ')} WHERE ${whereClause}`;
    
    return { sql, values: updateValues };
  }

  /**
   * Cleanup prepared statements (call on service shutdown)
   */
  public cleanup(): void {
    try {
      this.preparedStatements.clear();
      this.logger.info(`Cleaned up prepared statements for ${this.constructor.name}`);
    } catch (error) {
      this.logger.error('Error cleaning up prepared statements:', error);
    }
  }

  /**
   * Get service health information
   */
  public getHealthInfo(): {
    serviceName: string;
    preparedStatementsCount: number;
    isHealthy: boolean;
  } {
    return {
      serviceName: this.constructor.name,
      preparedStatementsCount: this.preparedStatements.size,
      isHealthy: true // Override in subclasses for more sophisticated health checks
    };
  }

  /**
   * Execute safe query using QueryBuilder
   */
  protected executeSafeQuery<T>(options: QueryBuilderOptions): T[] {
    const { sql, params } = SafeQueryBuilder.buildSelect(options);
    
    return this.auditWrapper.wrapOperation(
      () => this.getPreparedStatement(sql).all(...params) as T[],
      {
        operationType: 'SELECT',
        table: options.table,
        sql,
        parameters: params,
        context: 'safe_query'
      }
    );
  }

  /**
   * Execute safe insert using QueryBuilder
   */
  protected executeSafeInsert(table: string, data: Record<string, unknown>, context?: string): boolean {
    const { sql, params } = SafeQueryBuilder.buildInsert(table, data);
    
    return this.auditWrapper.wrapOperation(
      () => {
        const result = this.getPreparedStatement(sql).run(...params);
        return result.changes > 0;
      },
      {
        operationType: 'INSERT',
        table,
        sql,
        parameters: params,
        context: context || 'safe_insert'
      }
    );
  }

  /**
   * Execute safe update using QueryBuilder
   */
  protected executeSafeUpdate(
    table: string, 
    data: Record<string, unknown>, 
    where: Record<string, unknown>,
    context?: string
  ): boolean {
    const { sql, params } = SafeQueryBuilder.buildUpdate(table, data, where);
    
    return this.auditWrapper.wrapOperation(
      () => {
        const result = this.getPreparedStatement(sql).run(...params);
        return result.changes > 0;
      },
      {
        operationType: 'UPDATE',
        table,
        sql,
        parameters: params,
        context: context || 'safe_update'
      }
    );
  }

  /**
   * Create default logger implementation
   */
  private createDefaultLogger(): ILogger {
    return {
      info: (message: string, ...args: unknown[]) => console.log(`[${this.constructor.name}]`, message, ...args),
      warn: (message: string, ...args: unknown[]) => console.warn(`[${this.constructor.name}]`, message, ...args),
      error: (message: string, ...args: unknown[]) => console.error(`[${this.constructor.name}]`, message, ...args),
      debug: (message: string, ...args: unknown[]) => console.debug(`[${this.constructor.name}]`, message, ...args)
    };
  }
}

/**
 * Specialized base service for domain entities with common CRUD patterns
 */
export abstract class BaseCrudService<T, TNew, TUpdate> extends BaseDataService {
  protected abstract tableName: string;
  protected abstract idField: string;
  protected abstract externalIdField: string;

  /**
   * Get all records for an organization
   */
  abstract getAll(organizationId: string): Promise<T[]>;

  /**
   * Get record by ID
   */
  abstract getById(id: string, organizationId: string): Promise<T | undefined>;

  /**
   * Create new record
   */
  abstract create(data: TNew, organizationId: string, userId?: string): Promise<T>;

  /**
   * Update existing record
   */
  abstract update(id: string, updates: TUpdate, organizationId: string): Promise<T | undefined>;

  /**
   * Delete record (soft delete by default)
   */
  delete(id: string, organizationId: string, hardDelete: boolean = false): boolean {
    if (hardDelete) {
      return this.hardDelete(this.tableName, this.externalIdField, id, organizationId);
    } else {
      return this.softDelete(this.tableName, this.externalIdField, id, organizationId);
    }
  }

  /**
   * Check if record exists
   */
  exists(id: string, organizationId: string): boolean {
    return this.recordExists(this.tableName, this.externalIdField, id, organizationId);
  }

  /**
   * Get count of records for organization
   */
  getCount(organizationId: string): Promise<number> {
    return Promise.resolve(
      this.getRecordCount(
        this.tableName,
        'organization_id = ? AND deleted_at IS NULL',
        [organizationId]
      )
    );
  }
}