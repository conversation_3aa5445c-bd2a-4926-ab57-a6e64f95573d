-- Migration: Add supabase_id column to colors table for ID mapping
-- Date: 2025-07-05
-- Purpose: Track the original Supabase integer ID for colors to enable proper mapping

BEGIN TRANSACTION;

-- Add supabase_id column to colors table
ALTER TABLE colors ADD COLUMN supabase_id INTEGER NULL;

-- Create index for performance
CREATE INDEX idx_colors_supabase_id ON colors(supabase_id);

-- Add comment for documentation
INSERT OR REPLACE INTO schema_version (version, description, applied_at) 
VALUES (20250705002, 'Add supabase_id column to colors table for ID mapping', datetime('now'));

COMMIT;

-- Verification query
SELECT 'Migration completed. Colors table now has supabase_id column for mapping.';