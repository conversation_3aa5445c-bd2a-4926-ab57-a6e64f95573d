/**
 * @file migration-utils.ts
 * @description Utilities for migrating existing IPC handlers to the router system
 */

import { IPCRouter, IPCHandler, IPCMiddleware } from './IPCRouter';

export interface MigrationConfig {
  channel: string;
  method: 'get' | 'post' | 'put' | 'delete';
  path: string;
  middleware?: IPCMiddleware[];
}

export function migrateHandler(
  router: IPCRouter,
  config: MigrationConfig,
  handler: IPCHandler
): void {
  if (config.middleware) {
    router.register(config.method, config.path, ...config.middleware, handler);
  } else {
    router.register(config.method, config.path, handler);
  }
}

export function createRouteFromChannel(channel: string): { method: 'get' | 'post' | 'put' | 'delete'; path: string } {
  // Convert legacy channel format to router format
  const parts = channel.split(':');
  if (parts.length === 2) {
    const [domain, action] = parts;
    
    if (!action) {
      return { method: 'post', path: `/api/${domain || 'unknown'}` };
    }
    
    // Map actions to HTTP methods
    const methodMap: Record<string, 'get' | 'post' | 'put' | 'delete'> = {
      'getAll': 'get',
      'getById': 'get',
      'get': 'get',
      'create': 'post',
      'add': 'post',
      'update': 'put',
      'delete': 'delete',
      'remove': 'delete'
    };

    const method = methodMap[action] || 'post';
    const path = `/api/${domain}`;
    
    return { method, path };
  }
  
  return { method: 'get', path: `/legacy/${channel}` };
}

export function batchMigrateHandlers(
  router: IPCRouter,
  configs: MigrationConfig[],
  handlers: IPCHandler[]
): void {
  configs.forEach((config, index) => {
    if (handlers[index]) {
      migrateHandler(router, config, handlers[index]);
    }
  });
}