/**
 * @file test-data-migrated.ipc.ts
 * @description Migrated test data IPC handlers using dependency injection + universal wrapper pattern
 */

import { ipcMain } from 'electron';
import { ProductService } from '../db/services/product.service';
import { ColorService } from '../db/services/color.service';
import { TestDataChannels } from '../../shared/constants/channels';
import {
  registerSecureHandler,
  createSuccessResponse,
} from '../utils/ipc-wrapper';
import { ServiceLocator } from '../services/service-locator';

/**
 * Service dependencies interface
 */
interface TestDataServices {
  productService: ProductService;
  colorService: ColorService;
}

/**
 * Register test data IPC handlers with dependency injection and universal wrapper
 *
 * @param services - Optional service bundle for dependency injection
 */
export function registerTestDataHandlers(
  services?: Partial<TestDataServices>
): void {
  console.log(
    '[TestDataIPC] Registering test data handlers with dependency injection'
  );

  // Use dependency injection or fallback to ServiceLocator
  const productService =
    services?.productService || ServiceLocator.getProductService();
  const colorService =
    services?.colorService || ServiceLocator.getColorService();

  console.log('[TestDataIPC] Services available:', {
    productService: !!productService,
    colorService: !!colorService,
  });

  // Create test product with flat and gradient colors
  registerSecureHandler(
    TestDataChannels.CREATE_TEST_PRODUCT,
    async (organizationId: string) => {
      console.log(
        '[TestDataIPC] Creating test product with colors for organization:',
        organizationId
      );

      // 1. Create test product
      const productName = `Test Product ${new Date().toISOString().slice(0, 10)}`;
      const product = await productService.create(
        {
          name: productName,
          metadata: {
            description: 'Test product with flat and gradient colors',
            testData: true,
            created: new Date().toISOString(),
          },
        },
        organizationId
      );

      console.log('[TestDataIPC] Created product:', product);

      const createdColors = [];

      // 2. Create flat color
      try {
        const flatColor = await colorService.add(
          {
            name: 'Test Blue',
            code: `TEST-FLAT-${Date.now()}`,
            hex: '#2563EB',
            cmyk: 'C:85 M:60 Y:0 K:8',
            product: 'Test Product',
          },
          organizationId
        );

        // Associate with product
        await productService.addColor(product.id, flatColor, organizationId);
        createdColors.push({
          type: 'flat',
          color: flatColor,
          success: true,
        });

        console.log('[TestDataIPC] Created flat color:', flatColor);
      } catch (flatColorError) {
        console.error(
          '[TestDataIPC] Error creating flat color:',
          flatColorError
        );
        createdColors.push({
          type: 'flat',
          success: false,
          error:
            flatColorError instanceof Error
              ? flatColorError.message
              : String(flatColorError),
        });
      }

      // 3. Create gradient color
      try {
        const gradientColor = await colorService.add(
          {
            name: 'Test Gradient (Blue to Green)',
            code: `TEST-GRAD-${Date.now()}`,
            hex: '#2563EB', // Primary color
            cmyk: 'C:85 M:60 Y:0 K:8',
            gradient: {
              type: 'linear',
              angle: 45,
              colors: ['#2563EB', '#16A34A'],
            },
            product: 'Test Product',
          },
          organizationId
        );

        // Associate with product
        await productService.addColor(
          product.id,
          gradientColor,
          organizationId
        );
        createdColors.push({
          type: 'gradient',
          color: gradientColor,
          success: true,
        });

        console.log('[TestDataIPC] Created gradient color:', gradientColor);
      } catch (gradientColorError) {
        console.error(
          '[TestDataIPC] Error creating gradient color:',
          gradientColorError
        );
        createdColors.push({
          type: 'gradient',
          success: false,
          error:
            gradientColorError instanceof Error
              ? gradientColorError.message
              : String(gradientColorError),
        });
      }

      // 4. Create additional test colors with various properties
      const additionalColors = [
        {
          name: 'Test Red',
          code: `TEST-RED-${Date.now()}`,
          hex: '#DC2626',
          colorSpaces: {
            hex: '#DC2626',
            rgb: { r: 220, g: 38, b: 38 },
            cmyk: { c: 0, m: 83, y: 83, k: 14 },
            hsl: { h: 0, s: 73, l: 51 },
          },
          tags: ['test', 'primary', 'red'],
          notes: 'Primary red test color',
        },
        {
          name: 'Test Green',
          code: `TEST-GREEN-${Date.now()}`,
          hex: '#16A34A',
          colorSpaces: {
            hex: '#16A34A',
            rgb: { r: 22, g: 163, b: 74 },
            cmyk: { c: 87, m: 0, y: 55, k: 36 },
            hsl: { h: 142, s: 76, l: 36 },
          },
          tags: ['test', 'nature', 'green'],
          notes: 'Primary green test color',
        },
        {
          name: 'Test Radial Gradient',
          code: `TEST-RADIAL-${Date.now()}`,
          hex: '#8B5CF6',
          isGradient: true,
          gradientColors: '#8B5CF6,#F59E0B,#EF4444',
          colorSpaces: {
            hex: '#8B5CF6',
            rgb: { r: 139, g: 92, b: 246 },
            gradient: {
              type: 'radial',
              stops: [
                { position: 0, color: '#8B5CF6', opacity: 1 },
                { position: 50, color: '#F59E0B', opacity: 0.8 },
                { position: 100, color: '#EF4444', opacity: 1 },
              ],
            },
          },
          tags: ['test', 'gradient', 'radial'],
          notes: 'Multi-stop radial gradient test',
        },
      ];

      for (const colorData of additionalColors) {
        try {
          // Transform test data to match NewColorEntry interface
          const newColorEntry = {
            name: colorData.name,
            code: colorData.code,
            hex: colorData.hex,
            cmyk: colorData.colorSpaces?.cmyk
              ? `C:${colorData.colorSpaces.cmyk.c} M:${colorData.colorSpaces.cmyk.m} Y:${colorData.colorSpaces.cmyk.y} K:${colorData.colorSpaces.cmyk.k}`
              : 'C:0 M:0 Y:0 K:0',
            product: product.name,
            source: 'test-data',
            notes: colorData.notes || 'Test color',
            tags: colorData.tags?.join(',') || 'test',
            organizationId,
            gradient: colorData.isGradient
              ? {
                  colors: colorData.gradientColors?.split(',') || [
                    colorData.hex,
                  ],
                  colorCodes: [],
                }
              : undefined,
          };

          const colorId = await colorService.add(
            newColorEntry,
            undefined,
            organizationId
          );
          const color = await colorService.getById(colorId, organizationId);

          if (!color) {
            throw new Error(
              `Failed to retrieve created color with ID: ${colorId}`
            );
          }

          await productService.addColor(product.id, color.id, organizationId);
          createdColors.push({
            type: colorData.isGradient ? 'gradient' : 'flat',
            color,
            success: true,
          });

          console.log(`[TestDataIPC] Created additional color: ${color.name}`);
        } catch (colorError) {
          console.error(
            `[TestDataIPC] Error creating color ${colorData.name}:`,
            colorError
          );
          createdColors.push({
            type: colorData.isGradient ? 'gradient' : 'flat',
            name: colorData.name,
            success: false,
            error:
              colorError instanceof Error
                ? colorError.message
                : String(colorError),
          });
        }
      }

      const successfulColors = createdColors.filter(c => c.success).length;
      const failedColors = createdColors.filter(c => !c.success).length;

      console.log(
        `[TestDataIPC] Test data creation completed: 1 product, ${successfulColors} colors created, ${failedColors} errors`
      );

      return createSuccessResponse(
        {
          product: {
            id: product.id,
            name: product.name,
            metadata: product.metadata,
          },
          colors: createdColors,
          summary: {
            totalColors: createdColors.length,
            successfulColors,
            failedColors,
            flatColors: createdColors.filter(
              c => c.success && c.type === 'flat'
            ).length,
            gradientColors: createdColors.filter(
              c => c.success && c.type === 'gradient'
            ).length,
          },
        },
        `Test data created successfully: 1 product with ${successfulColors} colors`
      );
    },
    ipcMain,
    {
      logChannel: 'TestDataCreate',
      customErrorMessage: 'Failed to create test data. Please try again.',
    }
  );

  // Remove all test data
  registerSecureHandler(
    TestDataChannels.REMOVE_TEST_DATA,
    async (organizationId: string) => {
      console.log(
        '[TestDataIPC] Removing all test data for organization:',
        organizationId
      );

      // Get all products and colors with test metadata
      const allProducts = productService.getAll(organizationId);
      const testProducts = allProducts.filter(
        p => p.metadata?.testData === true
      );

      const allColors = colorService.getAll(organizationId);
      const testColors = allColors.filter(
        c => c.notes?.includes('testData') || c.tags?.includes('testData')
      );

      let removedProducts = 0;
      let removedColors = 0;
      const errors = [];

      // Remove test products
      for (const product of testProducts) {
        try {
          // Remove all color associations first
          const productColors = productService.getColors(
            product.id,
            organizationId
          );
          for (const color of productColors) {
            productService.removeColor(product.id, color.id, organizationId);
          }

          // Remove the product
          productService.delete(product.id, organizationId);
          removedProducts++;
          console.log(`[TestDataIPC] Removed test product: ${product.name}`);
        } catch (error) {
          const errorMsg = `Failed to remove product ${product.name}: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          console.error('[TestDataIPC]', errorMsg);
        }
      }

      // Remove test colors
      for (const color of testColors) {
        try {
          colorService.delete(color.id, organizationId);
          removedColors++;
          console.log(`[TestDataIPC] Removed test color: ${color.name}`);
        } catch (error) {
          const errorMsg = `Failed to remove color ${color.name}: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          console.error('[TestDataIPC]', errorMsg);
        }
      }

      console.log(
        `[TestDataIPC] Test data removal completed: ${removedProducts} products, ${removedColors} colors removed, ${errors.length} errors`
      );

      return createSuccessResponse(
        {
          productsRemoved: removedProducts,
          colorsRemoved: removedColors,
          totalProductsFound: testProducts.length,
          totalColorsFound: testColors.length,
          errors,
        },
        `Test data removal completed: ${removedProducts} products and ${removedColors} colors removed${errors.length > 0 ? ` (${errors.length} errors)` : ''}`
      );
    },
    ipcMain,
    {
      logChannel: 'TestDataRemove',
      customErrorMessage: 'Failed to remove test data. Please try again.',
    }
  );

  console.log('[TestDataIPC] Test data handlers registered successfully');
}

/**
 * Alternative registration using only ServiceLocator
 */
export function registerTestDataHandlersFromLocator(): void {
  console.log(
    '[TestDataIPC] Registering test data handlers using ServiceLocator pattern'
  );
  registerTestDataHandlers();
}
