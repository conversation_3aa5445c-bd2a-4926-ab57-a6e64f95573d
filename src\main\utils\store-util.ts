/**
 * @file store-util.ts
 * @description Utility for creating safe store instances using PatchedStore for robust storage
 */

import PatchedStore from './electron-store-patched';

/**
 * Create a safe store instance using the robust PatchedStore implementation
 * @param options Store options
 * @returns Safe store instance
 */
export function createSafeStore<T extends Record<string, unknown>>(options: {
  name: string;
  defaults?: T;
  encryptionKey?: string;
}): PatchedStore<T> {
  return new PatchedStore<T>({
    name: options.name,
    defaults: options.defaults,
    encryptionKey: options.encryptionKey
  });
}
