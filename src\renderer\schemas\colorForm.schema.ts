/**
 * @file colorForm.schema.ts
 * @description Zod validation schemas for color forms
 */

import { z } from 'zod';

// Base schema for color form validation
export const colorFormSchema = z.object({
  product: z
    .string()
    .min(1, 'Product name is required')
    .max(100, 'Product name must be less than 100 characters'),
  name: z
    .string()
    .min(1, 'Color name is required')
    .max(100, 'Color name must be less than 100 characters'),
  code: z
    .string()
    .min(1, 'Color code is required')
    .max(50, 'Color code must be less than 50 characters'),
  hex: z
    .string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color format (e.g., #FF5733)')
    .transform(val => val.toUpperCase()),
  cmyk: z.string().optional().default(''),
  notes: z.string().optional().default(''),
  cmykC: z.coerce.number().min(0).max(100).optional().default(0),
  cmykM: z.coerce.number().min(0).max(100).optional().default(0),
  cmykY: z.coerce.number().min(0).max(100).optional().default(0),
  cmykK: z.coerce.number().min(0).max(100).optional().default(0),
});

// Schema for editing (product is readonly)
export const colorEditFormSchema = colorFormSchema.omit({ product: true });

// Schema for forms with hidden product field
export const colorFormWithoutProductSchema = colorFormSchema.omit({
  product: true,
});

// Gradient form schema
export const gradientFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Gradient name is required')
    .max(100, 'Gradient name must be less than 100 characters'),
  notes: z.string().min(0).default(''),
  stops: z
    .array(
      z.object({
        color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color'),
        position: z.number().min(0).max(100),
        cmykC: z.coerce.number().min(0).max(100),
        cmykM: z.coerce.number().min(0).max(100),
        cmykY: z.coerce.number().min(0).max(100),
        cmykK: z.coerce.number().min(0).max(100),
        colorCode: z.string().optional(),
      })
    )
    .min(2, 'At least 2 gradient stops are required'),
});

// TypeScript types derived from schemas
export type ColorFormData = z.infer<typeof colorFormSchema>;
export type ColorEditFormData = z.infer<typeof colorEditFormSchema>;
export type ColorFormWithoutProductData = z.infer<
  typeof colorFormWithoutProductSchema
>;
export type GradientFormData = z.infer<typeof gradientFormSchema>;
