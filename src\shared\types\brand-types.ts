/**
 * @file brand-types.ts
 * @description Enhanced brand types for domain modeling with sophisticated validation
 * Implements cutting-edge TypeScript branding patterns for bulletproof type safety
 */

import type { Brand } from './advanced-utilities.types';
import type { ValidationResult } from './advanced-utilities.types';

// ===== CORE BRAND TYPE FACTORY =====

/**
 * Create a brand type with validation
 */
export type BrandWithValidation<
  T,
  TBrand extends string,
  TConstraints = {},
> = Brand<T, TBrand> & { readonly __constraints: TConstraints };

/**
 * Brand factory function type
 */
export type BrandFactory<T, TBrand extends string> = {
  readonly create: (value: T) => Brand<T, TBrand>;
  readonly validate: (value: unknown) => ValidationResult<Brand<T, TBrand>>;
  readonly isValid: (value: unknown) => value is Brand<T, TBrand>;
  readonly unsafe: (value: T) => Brand<T, TBrand>; // Skip validation
};

// ===== DOMAIN-SPECIFIC BRAND TYPES =====

// Identity and Organization Types
export type UserId = Brand<string, 'UserId'>;
export type OrganizationId = Brand<string, 'OrganizationId'>;
export type SessionId = Brand<string, 'SessionId'>;
export type RequestId = Brand<string, 'RequestId'>;

// Color Domain Types
export type ColorId = Brand<string, 'ColorId'>;
export type HexColor = Brand<string, 'HexColor'>;
export type PantoneCode = Brand<string, 'PantoneCode'>;
export type RALCode = Brand<string, 'RALCode'>;
export type ColorName = Brand<string, 'ColorName'>;

// Product Domain Types
export type ProductId = Brand<string, 'ProductId'>;
export type ProductCode = Brand<string, 'ProductCode'>;
export type ProductName = Brand<string, 'ProductName'>;

// Gradient Domain Types
export type GradientId = Brand<string, 'GradientId'>;
export type GradientPosition = Brand<number, 'GradientPosition'>;
export type GradientAngle = Brand<number, 'GradientAngle'>;

// Color Space Types
export type CMYKValue = Brand<number, 'CMYKValue'>;
export type RGBValue = Brand<number, 'RGBValue'>;
export type HSLHue = Brand<number, 'HSLHue'>;
export type HSLSaturation = Brand<number, 'HSLSaturation'>;
export type HSLLightness = Brand<number, 'HSLLightness'>;
export type LABLightness = Brand<number, 'LABLightness'>;
export type LABColorValue = Brand<number, 'LABColorValue'>;

// Measurement Types
export type Opacity = Brand<number, 'Opacity'>;
export type ContrastRatio = Brand<number, 'ContrastRatio'>;
export type DeltaE = Brand<number, 'DeltaE'>;

// File and Path Types
export type FilePath = Brand<string, 'FilePath'>;
export type URL = Brand<string, 'URL'>;
export type Email = Brand<string, 'Email'>;

// Database Types
export type TableName = Brand<string, 'TableName'>;
export type ColumnName = Brand<string, 'ColumnName'>;
export type SQLQuery = Brand<string, 'SQLQuery'>;

// Time and Version Types
export type Timestamp = Brand<string, 'Timestamp'>;
export type Version = Brand<string, 'Version'>;

// ===== VALIDATION FUNCTIONS =====

/**
 * UUID validation
 */
function isValidUUID(value: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
}

/**
 * Hex color validation
 */
function isValidHexColor(value: string): boolean {
  const hexRegex = /^#([0-9a-f]{3}|[0-9a-f]{6})$/i;
  return hexRegex.test(value);
}

/**
 * Pantone code validation
 */
function isValidPantoneCode(value: string): boolean {
  // Examples: "PANTONE 18-1664 TPX", "P 1-8 C", "2945 C"
  const pantoneRegex = /^(PANTONE\s+)?\d+(-\d+)?\s+[A-Z]+$/i;
  return pantoneRegex.test(value);
}

/**
 * RAL code validation
 */
function isValidRALCode(value: string): boolean {
  // Examples: "RAL 1000", "1000"
  const ralRegex = /^(RAL\s+)?\d{4}$/i;
  return ralRegex.test(value);
}

/**
 * Email validation
 */
function isValidEmail(value: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

/**
 * URL validation
 */
function isValidURL(value: string): boolean {
  try {
    new globalThis.URL(value);
    return true;
  } catch {
    return false;
  }
}

/**
 * Semver validation
 */
function isValidSemver(value: string): boolean {
  const semverRegex = /^\d+\.\d+\.\d+(-[a-zA-Z0-9-]+)?(\+[a-zA-Z0-9-]+)?$/;
  return semverRegex.test(value);
}

/**
 * ISO8601 timestamp validation
 */
function isValidISO8601(value: string): boolean {
  const date = new Date(value);
  return !isNaN(date.getTime()) && date.toISOString() === value;
}

/**
 * Number range validation
 */
function isValidNumberRange(
  value: number,
  min: number,
  max: number,
  precision?: number
): boolean {
  if (value < min || value > max) {return false;}
  if (precision !== undefined) {
    const factor = Math.pow(10, precision);
    return Math.round(value * factor) === value * factor;
  }
  return true;
}

// ===== BRAND FACTORIES =====

/**
 * Create UserId brand factory
 */
export const UserIdFactory: BrandFactory<string, 'UserId'> = {
  create: (value: string): Brand<string, 'UserId'> => {
    const result = UserIdFactory.validate(value);
    if (!result.isValid) {
      throw new Error(`Invalid UserId: ${result.errors?.join(', ')}`);
    }
    return result.value!;
  },

  validate: (value: unknown): ValidationResult<Brand<string, 'UserId'>> => {
    if (typeof value !== 'string') {
      return { isValid: false, errors: ['Must be a string'] };
    }
    if (!isValidUUID(value)) {
      return { isValid: false, errors: ['Must be a valid UUID'] };
    }
    return { isValid: true, value: value as Brand<string, 'UserId'> };
  },

  isValid: (value: unknown): value is Brand<string, 'UserId'> => {
    return UserIdFactory.validate(value).isValid;
  },

  unsafe: (value: string): Brand<string, 'UserId'> =>
    value as Brand<string, 'UserId'>,
};

/**
 * Create HexColor brand factory
 */
export const HexColorFactory: BrandFactory<string, 'HexColor'> = {
  create: (value: string): Brand<string, 'HexColor'> => {
    const result = HexColorFactory.validate(value);
    if (!result.isValid) {
      throw new Error(`Invalid HexColor: ${result.errors?.join(', ')}`);
    }
    return result.value!;
  },

  validate: (value: unknown): ValidationResult<Brand<string, 'HexColor'>> => {
    if (typeof value !== 'string') {
      return { isValid: false, errors: ['Must be a string'] };
    }
    if (!isValidHexColor(value)) {
      return {
        isValid: false,
        errors: ['Must be a valid hex color (e.g., #FF0000 or #F00)'],
      };
    }
    return { isValid: true, value: value as Brand<string, 'HexColor'> };
  },

  isValid: (value: unknown): value is Brand<string, 'HexColor'> => {
    return HexColorFactory.validate(value).isValid;
  },

  unsafe: (value: string): Brand<string, 'HexColor'> =>
    value as Brand<string, 'HexColor'>,
};

/**
 * Create PantoneCode brand factory
 */
export const PantoneCodeFactory: BrandFactory<string, 'PantoneCode'> = {
  create: (value: string): Brand<string, 'PantoneCode'> => {
    const result = PantoneCodeFactory.validate(value);
    if (!result.isValid) {
      throw new Error(`Invalid PantoneCode: ${result.errors?.join(', ')}`);
    }
    return result.value!;
  },

  validate: (
    value: unknown
  ): ValidationResult<Brand<string, 'PantoneCode'>> => {
    if (typeof value !== 'string') {
      return { isValid: false, errors: ['Must be a string'] };
    }
    if (!isValidPantoneCode(value)) {
      return {
        isValid: false,
        errors: [
          'Must be a valid Pantone code (e.g., "2945 C", "PANTONE 18-1664 TPX")',
        ],
      };
    }
    return { isValid: true, value: value as Brand<string, 'PantoneCode'> };
  },

  isValid: (value: unknown): value is Brand<string, 'PantoneCode'> => {
    return PantoneCodeFactory.validate(value).isValid;
  },

  unsafe: (value: string): Brand<string, 'PantoneCode'> =>
    value as Brand<string, 'PantoneCode'>,
};

/**
 * Create ContrastRatio brand factory
 */
export const ContrastRatioFactory: BrandFactory<number, 'ContrastRatio'> = {
  create: (value: number): Brand<number, 'ContrastRatio'> => {
    const result = ContrastRatioFactory.validate(value);
    if (!result.isValid) {
      throw new Error(`Invalid ContrastRatio: ${result.errors?.join(', ')}`);
    }
    return result.value!;
  },

  validate: (
    value: unknown
  ): ValidationResult<Brand<number, 'ContrastRatio'>> => {
    if (typeof value !== 'number') {
      return { isValid: false, errors: ['Must be a number'] };
    }
    if (!isValidNumberRange(value, 1, 21, 2)) {
      return {
        isValid: false,
        errors: ['Must be between 1 and 21 with max 2 decimal places'],
      };
    }
    return { isValid: true, value: value as Brand<number, 'ContrastRatio'> };
  },

  isValid: (value: unknown): value is Brand<number, 'ContrastRatio'> => {
    return ContrastRatioFactory.validate(value).isValid;
  },

  unsafe: (value: number): Brand<number, 'ContrastRatio'> =>
    value as Brand<number, 'ContrastRatio'>,
};

// ===== GENERIC BRAND FACTORY CREATOR =====

/**
 * Create a generic brand factory for any type
 */
export function createBrandFactory<T, TBrand extends string>(
  validator: (value: unknown) => ValidationResult<Brand<T, TBrand>>,
  brandName: TBrand
): BrandFactory<T, TBrand> {
  return {
    create: (value: T): Brand<T, TBrand> => {
      const result = validator(value);
      if (!result.isValid) {
        throw new Error(`Invalid ${brandName}: ${result.errors?.join(', ')}`);
      }
      return result.value!;
    },

    validate: validator,

    isValid: (value: unknown): value is Brand<T, TBrand> => {
      return validator(value).isValid;
    },

    unsafe: (value: T): Brand<T, TBrand> => value as Brand<T, TBrand>,
  };
}

// ===== UTILITY FUNCTIONS =====

/**
 * Convert unbranded value to branded type safely
 */
export function toBrand<T, TBrand extends string>(
  value: T,
  factory: BrandFactory<T, TBrand>
): Brand<T, TBrand> {
  return factory.create(value);
}

/**
 * Convert branded value back to unbranded type
 */
export function fromBrand<T, TBrand extends string>(
  value: Brand<T, TBrand>
): T {
  return value as unknown as T;
}

/**
 * Check if a value conforms to a brand type
 */
export function isBrandType<T, TBrand extends string>(
  value: unknown,
  factory: BrandFactory<T, TBrand>
): value is Brand<T, TBrand> {
  return factory.isValid(value);
}

/**
 * Transform array of unbranded values to branded values
 */
export function brandArray<T, TBrand extends string>(
  values: T[],
  factory: BrandFactory<T, TBrand>
): Brand<T, TBrand>[] {
  return values.map(value => factory.create(value));
}

/**
 * Filter out invalid values and convert to branded types
 */
export function filterAndBrand<T, TBrand extends string>(
  values: unknown[],
  factory: BrandFactory<T, TBrand>
): Brand<T, TBrand>[] {
  return values
    .filter(value => factory.isValid(value))
    .map(value => value as Brand<T, TBrand>);
}

// ===== ADVANCED BRAND COMBINATORS =====

/**
 * Create a compound brand type from multiple brands
 */
export type CompoundBrand<T1, T2, TBrand extends string> = Brand<
  { first: T1; second: T2 },
  TBrand
>;

/**
 * Create a compound brand factory
 */
export function createCompoundBrandFactory<
  T1,
  TBrand1 extends string,
  T2,
  TBrand2 extends string,
  TCompoundBrand extends string,
>(
  factory1: BrandFactory<T1, TBrand1>,
  factory2: BrandFactory<T2, TBrand2>,
  compoundBrandName: TCompoundBrand
): BrandFactory<{ first: T1; second: T2 }, TCompoundBrand> {
  return {
    create: (value: {
      first: T1;
      second: T2;
    }): Brand<{ first: T1; second: T2 }, TCompoundBrand> => {
      const firstBrand = factory1.create(value.first);
      const secondBrand = factory2.create(value.second);
      return { first: firstBrand, second: secondBrand } as Brand<
        { first: T1; second: T2 },
        TCompoundBrand
      >;
    },

    validate: (
      value: unknown
    ): ValidationResult<Brand<{ first: T1; second: T2 }, TCompoundBrand>> => {
      if (typeof value !== 'object' || value === null) {
        return { isValid: false, errors: ['Must be an object'] };
      }

      const obj = value as any;
      const firstResult = factory1.validate(obj.first);
      const secondResult = factory2.validate(obj.second);

      if (!firstResult.isValid || !secondResult.isValid) {
        return {
          isValid: false,
          errors: [
            ...(firstResult.errors || []).map(e => `first: ${e}`),
            ...(secondResult.errors || []).map(e => `second: ${e}`),
          ],
        };
      }

      return {
        isValid: true,
        value: {
          first: firstResult.value!,
          second: secondResult.value!,
        } as Brand<{ first: T1; second: T2 }, TCompoundBrand>,
      };
    },

    isValid: (
      value: unknown
    ): value is Brand<{ first: T1; second: T2 }, TCompoundBrand> => {
      if (typeof value !== 'object' || value === null) {return false;}
      const obj = value as any;
      return factory1.isValid(obj.first) && factory2.isValid(obj.second);
    },

    unsafe: (value: {
      first: T1;
      second: T2;
    }): Brand<{ first: T1; second: T2 }, TCompoundBrand> =>
      value as Brand<{ first: T1; second: T2 }, TCompoundBrand>,
  };
}

// Export all types for module compatibility
export {};
