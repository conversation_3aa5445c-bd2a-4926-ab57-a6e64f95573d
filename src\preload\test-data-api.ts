/**
 * @file test-data-api.ts
 * @description Test Data API preload script for secure IPC communication
 */

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { TestDataChannels } from '../shared/constants/channels';

// Typed API for test data operations
contextBridge.exposeInMainWorld('testDataAPI', {
  // Create test product with flat and gradient colors
  createTestProduct: () => {
    console.log('Preload: calling testDataAPI.createTestProduct');
    return ipcRenderer.invoke(TestDataChannels.CREATE_TEST_PRODUCT);
  },

  // Remove all test data
  removeTestData: () => {
    console.log('Preload: calling testDataAPI.removeTestData');
    return ipcRenderer.invoke(TestDataChannels.REMOVE_TEST_DATA);
  }
});