/**
 * Production Database Initialization
 * Direct implementation that avoids dynamic imports and chunking issues
 */

import { loadBetterSqlite3 } from './database-loader';
import { getDatabasePath } from './core/connection';
import { COMPLETE_SCHEMA } from './schemas/complete-schema';

/**
 * Direct database initialization for production builds
 * Bypasses complex initialization system to avoid chunking issues
 */
export async function initializeProductionDatabase(): Promise<any | null> {
  console.log('[ProductionInit] Starting production database initialization...');
  
  try {
    // Load better-sqlite3 directly
    const Database = loadBetterSqlite3();
    if (!Database) {
      throw new Error('Could not load better-sqlite3');
    }
    
    const dbPath = getDatabasePath();
    console.log('[ProductionInit] Database path:', dbPath);
    
    // Create database connection
    const db = new Database(dbPath);
    console.log('[ProductionInit] Database connection established');

    // Check what tables currently exist
    const existingTables = db.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
    `).all().map((row: any) => row.name);

    console.log('[ProductionInit] Existing tables:', existingTables);

    // Define required core tables
    const requiredTables = ['organizations', 'products', 'colors', 'product_colors'];
    const missingTables = requiredTables.filter(table => !existingTables.includes(table));

    if (missingTables.length > 0) {
      console.log('[ProductionInit] Missing core tables:', missingTables);
      console.log('[ProductionInit] Creating complete schema...');

      try {
        // Execute complete schema
        db.exec(COMPLETE_SCHEMA);
        console.log('[ProductionInit] ✅ Complete schema created successfully');

        // Verify schema was created
        const newTables = db.prepare(`
          SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
        `).all().map((row: any) => row.name);

        console.log('[ProductionInit] Tables after schema creation:', newTables);

        const stillMissingTables = requiredTables.filter(table => !newTables.includes(table));
        if (stillMissingTables.length > 0) {
          console.error('[ProductionInit] ❌ Still missing tables after schema creation:', stillMissingTables);
          // Continue anyway - partial schema is better than no schema
        }
      } catch (schemaError) {
        console.error('[ProductionInit] ❌ Schema creation failed:', schemaError);
        console.error('[ProductionInit] Will attempt manual table creation...');
        
        // Fallback: Create minimal required tables manually
        try {
          db.exec(`
            CREATE TABLE IF NOT EXISTS organizations (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              external_id TEXT UNIQUE NOT NULL,
              name TEXT NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              deleted_at DATETIME NULL
            );
            
            CREATE TABLE IF NOT EXISTS products (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              organization_id TEXT NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              deleted_at DATETIME NULL,
              is_active INTEGER DEFAULT 1
            );
            
            CREATE TABLE IF NOT EXISTS colors (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              hex_code TEXT NOT NULL,
              organization_id TEXT NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              deleted_at DATETIME NULL,
              is_active INTEGER DEFAULT 1
            );
            
            CREATE TABLE IF NOT EXISTS product_colors (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              product_id INTEGER NOT NULL,
              color_id INTEGER NOT NULL,
              organization_id TEXT NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (product_id) REFERENCES products(id),
              FOREIGN KEY (color_id) REFERENCES colors(id)
            );
          `);
          console.log('[ProductionInit] ✅ Minimal schema created as fallback');
        } catch (fallbackError) {
          console.error('[ProductionInit] ❌ Fallback schema creation failed:', fallbackError);
          // Still return the database - some operations might work
        }
      }
    } else {
      console.log('[ProductionInit] ✅ All required tables exist');
    }

    // Configure database settings
    db.pragma('journal_mode = WAL');
    db.pragma('synchronous = NORMAL');
    db.pragma('cache_size = 1000');
    db.pragma('temp_store = memory');
    
    console.log('[ProductionInit] ✅ Database initialized successfully');
    return db;

  } catch (error) {
    console.error('[ProductionInit] ❌ Production database initialization failed:', error);
    console.error('[ProductionInit] Error details:', {
      message: (error as Error).message,
      stack: (error as Error).stack,
      code: (error as any).code
    });
    return null;
  }
}