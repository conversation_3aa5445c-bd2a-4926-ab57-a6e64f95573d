/**
 * @file result.types.ts
 * @description Enterprise-grade type-safe result pattern for error handling and data flow
 * Provides comprehensive type safety for API responses and async operations
 */

// ===== CORE RESULT TYPES =====

/**
 * Base Result type for type-safe error handling
 * Follows the Result<T, E> pattern from Rust and functional programming
 */
export type Result<TData, TError = Error> =
  | { readonly success: true; readonly data: TData; readonly error?: undefined }
  | {
      readonly success: false;
      readonly data?: undefined;
      readonly error: TError;
    };

/**
 * Async Result type for Promise-based operations
 */
export type AsyncResult<TData, TError = Error> = Promise<Result<TData, TError>>;

/**
 * Void Result type for operations that don't return data
 */
export type VoidResult<TError = Error> = Result<void, TError>;

/**
 * Async Void Result type for Promise-based operations without return data
 */
export type AsyncVoidResult<TError = Error> = AsyncResult<void, TError>;

// ===== ENHANCED RESULT TYPES =====

/**
 * Result with metadata for enhanced operations
 */
export interface ResultWithMetadata<TData, TError = Error> {
  readonly success: boolean;
  readonly data?: TData;
  readonly error?: TError;
  readonly metadata: {
    readonly timestamp: number;
    readonly operation?: string;
    readonly duration?: number;
    readonly source?: string;
    readonly traceId?: string;
  };
}

/**
 * Paginated Result type for list operations
 */
export interface PaginatedResult<TData> {
  readonly success: true;
  readonly data: {
    readonly items: TData[];
    readonly pagination: {
      readonly page: number;
      readonly limit: number;
      readonly total: number;
      readonly hasNext: boolean;
      readonly hasPrev: boolean;
    };
  };
}

/**
 * Validation Result type with detailed error information
 */
export interface ValidationResult<TData> {
  readonly success: boolean;
  readonly data?: TData;
  readonly errors: {
    readonly field: string;
    readonly message: string;
    readonly code: string;
    readonly value?: any;
  }[];
  readonly warnings?: {
    readonly field: string;
    readonly message: string;
    readonly code: string;
  }[];
}

// ===== RESULT UTILITY FUNCTIONS =====

/**
 * Create a successful result
 */
export function createSuccess<TData>(data: TData): Result<TData, never> {
  return { success: true, data } as const;
}

/**
 * Create a failed result
 */
export function createError<TError>(error: TError): Result<never, TError> {
  return { success: false, error } as const;
}

/**
 * Type guard to check if result is successful
 */
export function isSuccess<TData, TError>(
  result: Result<TData, TError>
): result is { success: true; data: TData; error?: undefined } {
  return result.success;
}

/**
 * Type guard to check if result is an error
 */
export function isError<TData, TError>(
  result: Result<TData, TError>
): result is { success: false; data?: undefined; error: TError } {
  return !result.success;
}

/**
 * Extract data from result or throw error
 */
export function unwrap<TData, TError>(result: Result<TData, TError>): TData {
  if (isSuccess(result)) {
    return result.data;
  }
  throw result.error;
}

/**
 * Extract data from result or return default value
 */
export function unwrapOr<TData, TError>(
  result: Result<TData, TError>,
  defaultValue: TData
): TData {
  if (isSuccess(result)) {
    return result.data;
  }
  return defaultValue;
}

/**
 * Transform successful result data
 */
export function mapData<TData, TNewData, TError>(
  result: Result<TData, TError>,
  mapper: (data: TData) => TNewData
): Result<TNewData, TError> {
  if (isSuccess(result)) {
    return createSuccess(mapper(result.data));
  }
  return result as Result<TNewData, TError>;
}

/**
 * Transform error in result
 */
export function mapError<TData, TError, TNewError>(
  result: Result<TData, TError>,
  mapper: (error: TError) => TNewError
): Result<TData, TNewError> {
  if (isError(result)) {
    return createError(mapper(result.error));
  }
  return result as Result<TData, TNewError>;
}

/**
 * Chain result operations (flatMap)
 */
export function chainResult<TData, TNewData, TError>(
  result: Result<TData, TError>,
  mapper: (data: TData) => Result<TNewData, TError>
): Result<TNewData, TError> {
  if (isSuccess(result)) {
    return mapper(result.data);
  }
  return result as Result<TNewData, TError>;
}

/**
 * Combine multiple results into a single result
 */
export function combineResults<T extends readonly unknown[], TError>(results: {
  [K in keyof T]: Result<T[K], TError>;
}): Result<T, TError> {
  const data: any[] = [];

  for (const result of results) {
    if (isError(result)) {
      return result;
    }
    data.push(result.data);
  }

  return createSuccess(data as unknown as T);
}

/**
 * Convert Promise to AsyncResult
 */
export async function wrapAsync<TData>(
  promise: Promise<TData>
): AsyncResult<TData, Error> {
  try {
    const data = await promise;
    return createSuccess(data);
  } catch (error) {
    return createError(
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * Convert callback-style function to AsyncResult
 */
export function wrapCallback<TData>(
  fn: (callback: (error: Error | null, data?: TData) => void) => void
): AsyncResult<TData, Error> {
  return new Promise(resolve => {
    fn((error, data) => {
      if (error) {
        resolve(createError(error));
      } else if (data !== undefined) {
        resolve(createSuccess(data));
      } else {
        resolve(createError(new Error('No data returned')));
      }
    });
  });
}

// ===== SPECIALIZED RESULT TYPES FOR CHROMASYNC =====

/**
 * API Response type for consistent API communication
 */
export interface ApiResponse<TData> {
  readonly success: boolean;
  readonly data?: TData;
  readonly error?: {
    readonly code: string;
    readonly message: string;
    readonly details?: Record<string, any>;
    readonly stack?: string;
  };
  readonly metadata?: {
    readonly timestamp: number;
    readonly requestId?: string;
    readonly version?: string;
    readonly source?: string;
  };
}

/**
 * Database Operation Result
 */
export interface DbResult<TData> {
  readonly success: boolean;
  readonly data?: TData;
  readonly error?: {
    readonly code: string;
    readonly message: string;
    readonly sqlState?: string;
    readonly constraint?: string;
  };
  readonly metadata: {
    readonly operation:
      | 'SELECT'
      | 'INSERT'
      | 'UPDATE'
      | 'DELETE'
      | 'TRANSACTION';
    readonly rowsAffected?: number;
    readonly duration: number;
    readonly query?: string;
  };
}

/**
 * Sync Operation Result
 */
export interface SyncResult<TData = any> {
  readonly success: boolean;
  readonly data?: TData;
  readonly error?: {
    readonly type: 'NETWORK' | 'AUTH' | 'CONFLICT' | 'VALIDATION' | 'UNKNOWN';
    readonly message: string;
    readonly code?: string;
    readonly retryable: boolean;
  };
  readonly metadata: {
    readonly operation: string;
    readonly direction: 'UPLOAD' | 'DOWNLOAD' | 'BIDIRECTIONAL';
    readonly itemsProcessed: number;
    readonly duration: number;
    readonly conflicts?: number;
  };
}

/**
 * File Operation Result
 */
export interface FileResult<TData = any> {
  readonly success: boolean;
  readonly data?: TData;
  readonly error?: {
    readonly type:
      | 'NOT_FOUND'
      | 'PERMISSION'
      | 'INVALID_FORMAT'
      | 'SIZE_LIMIT'
      | 'UNKNOWN';
    readonly message: string;
    readonly path?: string;
  };
  readonly metadata: {
    readonly operation: 'READ' | 'write' | 'delete' | 'copy' | 'move';
    readonly path: string;
    readonly size?: number;
    readonly mimeType?: string;
  };
}

// ===== TYPE-SAFE ERROR CLASSES =====

/**
 * Base application error with structured data
 */
export abstract class AppError extends Error {
  abstract readonly code: string;
  abstract readonly type: string;
  readonly context?: Record<string, any>;
  readonly timestamp: number;
  readonly traceId?: string;

  constructor(
    message: string,
    context?: Record<string, any>,
    traceId?: string
  ) {
    super(message);
    this.name = this.constructor.name;
    this.context = context;
    this.timestamp = Date.now();
    this.traceId = traceId;

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      type: this.type,
      context: this.context,
      timestamp: this.timestamp,
      traceId: this.traceId,
      stack: this.stack,
    };
  }
}

/**
 * Validation error with field-specific information
 */
export class ValidationError extends AppError {
  readonly code = 'VALIDATION_ERROR';
  readonly type = 'VALIDATION';
  readonly fields: Array<{
    field: string;
    message: string;
    value?: any;
  }>;

  constructor(
    fields: Array<{ field: string; message: string; value?: any }>,
    context?: Record<string, any>
  ) {
    const message = `Validation failed: ${fields.map(f => f.field).join(', ')}`;
    super(message, context);
    this.fields = fields;
  }
}

/**
 * Database operation error
 */
export class DatabaseError extends AppError {
  readonly code = 'DATABASE_ERROR';
  readonly type = 'DATABASE';
  readonly sqlState?: string;
  readonly constraint?: string;

  constructor(
    message: string,
    sqlState?: string,
    constraint?: string,
    context?: Record<string, any>
  ) {
    super(message, context);
    this.sqlState = sqlState;
    this.constraint = constraint;
  }
}

/**
 * Network/API error
 */
export class NetworkError extends AppError {
  readonly code = 'NETWORK_ERROR';
  readonly type = 'NETWORK';
  readonly status?: number;
  readonly url?: string;
  readonly retryable: boolean;

  constructor(
    message: string,
    status?: number,
    url?: string,
    retryable = true,
    context?: Record<string, any>
  ) {
    super(message, context);
    this.status = status;
    this.url = url;
    this.retryable = retryable;
  }
}

/**
 * Authentication/Authorization error
 */
export class AuthError extends AppError {
  readonly code = 'AUTH_ERROR';
  readonly type = 'AUTH';
  readonly reason:
    | 'INVALID_CREDENTIALS'
    | 'TOKEN_EXPIRED'
    | 'INSUFFICIENT_PERMISSIONS'
    | 'SESSION_INVALID';

  constructor(
    reason: AuthError['reason'],
    message?: string,
    context?: Record<string, any>
  ) {
    super(message || `Authentication failed: ${reason}`, context);
    this.reason = reason;
  }
}

// ===== RESULT BUILDERS =====

/**
 * Result builder for database operations
 */
export class DbResultBuilder<TData> {
  private operation: DbResult<TData>['metadata']['operation'] = 'SELECT';
  private duration = 0;
  private rowsAffected?: number;
  private query?: string;

  setOperation(operation: DbResult<TData>['metadata']['operation']): this {
    this.operation = operation;
    return this;
  }

  setDuration(duration: number): this {
    this.duration = duration;
    return this;
  }

  setRowsAffected(rows: number): this {
    this.rowsAffected = rows;
    return this;
  }

  setQuery(query: string): this {
    this.query = query;
    return this;
  }

  success(data: TData): DbResult<TData> {
    return {
      success: true,
      data,
      metadata: {
        operation: this.operation,
        duration: this.duration,
        rowsAffected: this.rowsAffected,
        query: this.query,
      },
    };
  }

  error(error: DbResult<TData>['error']): DbResult<TData> {
    return {
      success: false,
      error,
      metadata: {
        operation: this.operation,
        duration: this.duration,
        rowsAffected: this.rowsAffected,
        query: this.query,
      },
    };
  }
}

/**
 * Factory function to create database result builder
 */
export function createDbResult<TData>(): DbResultBuilder<TData> {
  return new DbResultBuilder<TData>();
}

// Types are already exported inline above

// Legacy compatibility functions for existing code
export function success<T>(data: T): Result<T, never> {
  return createSuccess(data);
}

export function failure<E>(error: E): Result<never, E> {
  return createError(error);
}

export async function tryCatch<T>(
  fn: () => Promise<T>
): Promise<Result<T, Error>> {
  return wrapAsync(fn());
}
