/**
 * Print Tab Component
 * Displays print-specific color information and simulations
 */

import { memo, useState, useMemo } from 'react';
import {
  Printer,
  AlertTriangle,
  Download,
  CheckCircle,
  XCircle,
  Gauge,
  DollarSign,
} from 'lucide-react';
import type { PrintTabProps } from '../types';

const SUBSTRATES = [
  {
    id: 'coated',
    label: 'Coated',
    adjustment: 0,
    description: 'Glossy/matte finish',
    inkLimit: 320,
  },
  {
    id: 'uncoated',
    label: 'Uncoated',
    adjustment: -8,
    description: 'Natural paper',
    inkLimit: 280,
  },
  {
    id: 'newsprint',
    label: 'Newsprint',
    adjustment: -15,
    description: 'High absorption',
    inkLimit: 240,
  },
  {
    id: 'recycled',
    label: 'Recycled',
    adjustment: -10,
    description: 'Eco-friendly',
    inkLimit: 260,
  },
];

// Parse CMYK string to get individual values
const parseCMYK = (
  cmykString: string
): { c: number; m: number; y: number; k: number } => {
  console.log('parseCMYK: Input string =', cmykString);

  // Handle format like "C:100 M:65 Y:0 K:15"
  const colonMatch = cmykString.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/i);
  if (colonMatch) {
    console.log('parseCMYK: Matched colon format');
    return {
      c: parseInt(colonMatch[1] || '0', 10),
      m: parseInt(colonMatch[2] || '0', 10),
      y: parseInt(colonMatch[3] || '0', 10),
      k: parseInt(colonMatch[4] || '0', 10),
    };
  }

  // Handle format like "100, 65, 0, 15"
  const commaMatch = cmykString.match(/(\d+),\s*(\d+),\s*(\d+),\s*(\d+)/);
  if (commaMatch) {
    console.log('parseCMYK: Matched comma format');
    return {
      c: parseInt(commaMatch[1] || '0', 10),
      m: parseInt(commaMatch[2] || '0', 10),
      y: parseInt(commaMatch[3] || '0', 10),
      k: parseInt(commaMatch[4] || '0', 10),
    };
  }

  // Handle format like "cmyk(100, 65, 0, 15)"
  const cmykMatch = cmykString.match(
    /cmyk\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i
  );
  if (cmykMatch) {
    console.log('parseCMYK: Matched cmyk() format');
    return {
      c: parseInt(cmykMatch[1] || '0', 10),
      m: parseInt(cmykMatch[2] || '0', 10),
      y: parseInt(cmykMatch[3] || '0', 10),
      k: parseInt(cmykMatch[4] || '0', 10),
    };
  }

  // Handle format like "100/65/0/15"
  const slashMatch = cmykString.match(/(\d+)\/(\d+)\/(\d+)\/(\d+)/);
  if (slashMatch) {
    console.log('parseCMYK: Matched slash format');
    return {
      c: parseInt(slashMatch[1] || '0', 10),
      m: parseInt(slashMatch[2] || '0', 10),
      y: parseInt(slashMatch[3] || '0', 10),
      k: parseInt(slashMatch[4] || '0', 10),
    };
  }

  // Default fallback
  console.log('parseCMYK: No match found, returning zeros');
  return { c: 0, m: 0, y: 0, k: 0 };
};

export const PrintTab = memo<PrintTabProps>(
  ({
    selectedColor,
    inkCoverage: _inkCoverage, // Unused, we calculate internally
    dominantInk: _dominantInk, // Unused, we calculate internally
  }) => {
    const [selectedSubstrate, setSelectedSubstrate] =
      useState<string>('coated');
    const [showCostEstimator, setShowCostEstimator] = useState(false);
    const [coverageArea, setCoverageArea] = useState('20');
    const [paperSize, setPaperSize] = useState('a4');

    // Parse CMYK values
    const cmykValues = useMemo(() => {
      console.log('PrintTab: selectedColor.cmyk =', selectedColor?.cmyk);
      if (!selectedColor?.cmyk || selectedColor.cmyk === 'N/A') {
        console.log('PrintTab: No CMYK data, returning zeros');
        return { c: 0, m: 0, y: 0, k: 0 };
      }
      const parsed = parseCMYK(selectedColor.cmyk);
      console.log('PrintTab: Parsed CMYK values =', parsed);
      return parsed;
    }, [selectedColor?.cmyk]);

    // Calculate accurate ink coverage
    const actualInkCoverage = useMemo(() => {
      const { c, m, y, k } = cmykValues;
      const total = c + m + y + k;
      console.log('PrintTab: Ink coverage calculation:', { c, m, y, k, total });
      return total;
    }, [cmykValues]);

    if (!selectedColor) {
      return (
        <div className='p-4 text-center text-ui-text-muted'>
          <Printer className='mx-auto mb-2 h-8 w-8 opacity-50' />
          <p>Select a color to view print information</p>
        </div>
      );
    }

    const selectedSubstrateData = SUBSTRATES.find(
      s => s.id === selectedSubstrate
    );
    const inkLimit = selectedSubstrateData?.inkLimit || 300;

    // Enhanced ink status with substrate-specific limits
    const getInkStatus = () => {
      const coverage = actualInkCoverage;
      const limit = inkLimit;

      if (coverage > limit) {
        return {
          severity: 'error' as const,
          icon: XCircle,
          text: `Over Limit (${coverage}% / ${limit}%)`,
          color: 'var(--color-feedback-error)',
        };
      } else if (coverage > limit * 0.9) {
        return {
          severity: 'warning' as const,
          icon: AlertTriangle,
          text: `Near Limit (${coverage}% / ${limit}%)`,
          color: 'var(--color-feedback-warning)',
        };
      }
      return {
        severity: 'success' as const,
        icon: CheckCircle,
        text: `Within Limit (${coverage}% / ${limit}%)`,
        color: 'var(--color-feedback-success)',
      };
    };

    // GCR (Gray Component Replacement) suggestions
    const getGCRSuggestions = () => {
      const { c, m, y, k } = cmykValues;
      const minCMY = Math.min(c, m, y);

      if (minCMY > 20 && actualInkCoverage > inkLimit) {
        const reduction = Math.min(minCMY, 30);
        const newC = c - reduction;
        const newM = m - reduction;
        const newY = y - reduction;
        const newK = Math.min(k + reduction, 100);
        const newTotal = newC + newM + newY + newK;

        return {
          hasGCR: true as const,
          reduction,
          original: { c, m, y, k, total: actualInkCoverage },
          suggested: { c: newC, m: newM, y: newY, k: newK, total: newTotal },
          savings: actualInkCoverage - newTotal,
        };
      }

      return { hasGCR: false as const };
    };

    const status = getInkStatus();
    const StatusIcon = status.icon;
    const gcrSuggestion = getGCRSuggestions();

    // Calculate ink cost estimation
    const calculateInkCost = useMemo(() => {
      const { c, m, y, k } = cmykValues;
      const coverage = parseInt(coverageArea, 10) / 100;

      // Paper dimensions in cm²
      const paperDimensions: Record<string, number> = {
        a4: 623.7, // 21 x 29.7 cm
        letter: 603.2, // 21.6 x 27.9 cm
        a3: 1247.4, // 29.7 x 42 cm
      };

      const area = paperDimensions[paperSize as keyof typeof paperDimensions] ?? paperDimensions.a4;
      const printArea = area * coverage;

      // Ink usage in ml per 1000 sheets (rough estimation)
      // Based on 5% coverage = 1ml per color per 1000 sheets
      const inkUsagePerColor = (printArea / 31.185) * 0.001; // ml per sheet

      // Calculate total ink usage
      const cyanInk = (c / 100) * inkUsagePerColor * 1000;
      const magentaInk = (m / 100) * inkUsagePerColor * 1000;
      const yellowInk = (y / 100) * inkUsagePerColor * 1000;
      const blackInk = (k / 100) * inkUsagePerColor * 1000;

      // Ink prices per ml (based on $35-45/kg, ~1ml = 1g)
      const inkPrices = {
        c: 0.035, // $35/kg
        m: 0.045, // $45/kg
        y: 0.03, // $30/kg
        k: 0.02, // $20/kg
      };

      const totalCost =
        cyanInk * inkPrices.c +
        magentaInk * inkPrices.m +
        yellowInk * inkPrices.y +
        blackInk * inkPrices.k;

      return totalCost.toFixed(2);
    }, [cmykValues, coverageArea, paperSize]);

    return (
      <div className='p-3 space-y-3'>
        {/* Ink Coverage Visual Warning Bar */}
        <div
          className='rounded-lg p-3'
          style={{
            backgroundColor: 'var(--color-ui-background-tertiary)',
            borderRadius: 'var(--radius-lg)',
          }}
        >
          <div className='flex items-center justify-between mb-2'>
            <h4
              className='text-sm font-medium flex items-center gap-2'
              style={{
                fontSize: 'var(--font-size-sm)',
                fontWeight: 'var(--font-weight-medium)',
                color: 'var(--color-ui-foreground-primary)',
              }}
            >
              <Gauge className='h-4 w-4' />
              Ink Coverage Analysis
            </h4>
            <div
              className='flex items-center gap-1 text-xs'
              style={{
                fontSize: 'var(--font-size-xs)',
                color: status.color,
              }}
            >
              <StatusIcon className='h-3 w-3' />
              {status.text}
            </div>
          </div>

          {/* Visual Ink Coverage Bar */}
          <div className='mb-3'>
            {/* Total coverage bar */}
            <div className='mb-2'>
              <div
                className='relative h-10 overflow-hidden'
                style={{
                  backgroundColor: 'var(--progress-bg)',
                  borderRadius: 'var(--radius-lg)',
                  border: `1px solid var(--color-ui-border-light)`,
                }}
              >
                {/* Total ink coverage visualization */}
                <div
                  className='absolute left-0 top-0 bottom-0 transition-standard'
                  style={{
                    width: `${Math.min((actualInkCoverage / 400) * 100, 100)}%`,
                    backgroundColor:
                      actualInkCoverage > inkLimit
                        ? 'var(--status-error-bg)'
                        : actualInkCoverage > inkLimit * 0.9
                          ? 'var(--status-warning-bg)'
                          : 'var(--status-success-bg)',
                  }}
                >
                  <div className='h-full flex items-center justify-end pr-2'>
                    <span
                      className='text-xs font-bold drop-shadow-lg'
                      style={{
                        color: 'var(--color-ui-foreground-inverse)',
                        fontSize: 'var(--font-size-xs)',
                        fontWeight: 'var(--font-weight-bold)',
                      }}
                    >
                      {actualInkCoverage}%
                    </span>
                  </div>
                </div>

                {/* Substrate limit indicator */}
                <div
                  className='absolute top-0 bottom-0 w-1 opacity-50'
                  style={{
                    left: `${(inkLimit / 400) * 100}%`,
                    backgroundColor: 'var(--color-ui-foreground-primary)',
                  }}
                >
                  <div
                    className='absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs font-medium whitespace-nowrap'
                    style={{
                      color: 'var(--color-ui-foreground-primary)',
                      fontSize: 'var(--font-size-xs)',
                      fontWeight: 'var(--font-weight-medium)',
                    }}
                  >
                    Limit: {inkLimit}%
                  </div>
                </div>

                {/* No data message */}
                {actualInkCoverage === 0 && (
                  <div
                    className='absolute inset-0 flex items-center justify-center text-xs'
                    style={{
                      color: 'var(--color-ui-foreground-tertiary)',
                      fontSize: 'var(--font-size-xs)',
                    }}
                  >
                    No CMYK data available
                  </div>
                )}
              </div>

              {/* Scale labels */}
              <div
                className='flex justify-between text-xs mt-1'
                style={{
                  fontSize: 'var(--font-size-xs)',
                  color: 'var(--color-ui-foreground-tertiary)',
                }}
              >
                <span>0%</span>
                <span>100%</span>
                <span>200%</span>
                <span>300%</span>
                <span>400%</span>
              </div>
            </div>

            {/* Individual CMYK breakdown bars */}
            <div className='space-y-1'>
              {/* Cyan */}
              <div className='flex items-center gap-2'>
                <span
                  className='text-xs font-medium w-8'
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    fontWeight: 'var(--font-weight-medium)',
                    color: 'var(--progress-cyan)',
                  }}
                >
                  C:
                </span>
                <div
                  className='flex-1 h-4 overflow-hidden'
                  style={{
                    backgroundColor: 'var(--progress-bg)',
                    borderRadius: 'var(--radius-DEFAULT)',
                  }}
                >
                  <div
                    className='h-full transition-standard'
                    style={{
                      width: `${Math.min(cmykValues.c, 100)}%`,
                      backgroundColor: 'var(--progress-cyan)',
                    }}
                  />
                </div>
                <span
                  className='text-xs w-10 text-right'
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)',
                  }}
                >
                  {cmykValues.c}%
                </span>
              </div>

              {/* Magenta */}
              <div className='flex items-center gap-2'>
                <span
                  className='text-xs font-medium w-8'
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    fontWeight: 'var(--font-weight-medium)',
                    color: 'var(--progress-magenta)',
                  }}
                >
                  M:
                </span>
                <div
                  className='flex-1 h-4 overflow-hidden'
                  style={{
                    backgroundColor: 'var(--progress-bg)',
                    borderRadius: 'var(--radius-DEFAULT)',
                  }}
                >
                  <div
                    className='h-full transition-standard'
                    style={{
                      width: `${Math.min(cmykValues.m, 100)}%`,
                      backgroundColor: 'var(--progress-magenta)',
                    }}
                  />
                </div>
                <span
                  className='text-xs w-10 text-right'
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)',
                  }}
                >
                  {cmykValues.m}%
                </span>
              </div>

              {/* Yellow */}
              <div className='flex items-center gap-2'>
                <span
                  className='text-xs font-medium w-8'
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    fontWeight: 'var(--font-weight-medium)',
                    color: 'var(--progress-yellow)',
                  }}
                >
                  Y:
                </span>
                <div
                  className='flex-1 h-4 overflow-hidden'
                  style={{
                    backgroundColor: 'var(--progress-bg)',
                    borderRadius: 'var(--radius-DEFAULT)',
                  }}
                >
                  <div
                    className='h-full transition-standard'
                    style={{
                      width: `${Math.min(cmykValues.y, 100)}%`,
                      backgroundColor: 'var(--progress-yellow)',
                    }}
                  />
                </div>
                <span
                  className='text-xs w-10 text-right'
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)',
                  }}
                >
                  {cmykValues.y}%
                </span>
              </div>

              {/* Black */}
              <div className='flex items-center gap-2'>
                <span
                  className='text-xs font-medium w-8'
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    fontWeight: 'var(--font-weight-medium)',
                    color: 'var(--progress-black)',
                  }}
                >
                  K:
                </span>
                <div
                  className='flex-1 h-4 overflow-hidden'
                  style={{
                    backgroundColor: 'var(--progress-bg)',
                    borderRadius: 'var(--radius-DEFAULT)',
                  }}
                >
                  <div
                    className='h-full transition-standard'
                    style={{
                      width: `${Math.min(cmykValues.k, 100)}%`,
                      backgroundColor: 'var(--progress-black)',
                    }}
                  />
                </div>
                <span
                  className='text-xs w-10 text-right'
                  style={{
                    fontSize: 'var(--font-size-xs)',
                    color: 'var(--color-ui-foreground-primary)',
                  }}
                >
                  {cmykValues.k}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* GCR Suggestions */}
        {gcrSuggestion.hasGCR && (
          <div
            className='rounded-lg p-3'
            style={{
              backgroundColor: 'var(--feedback-bg-warning)',
              borderRadius: 'var(--radius-lg)',
            }}
          >
            <div className='flex items-center gap-2 mb-2'>
              <AlertTriangle
                className='h-3 w-3'
                style={{ color: 'var(--color-feedback-warning)' }}
              />
              <h4
                className='text-xs font-medium'
                style={{
                  fontSize: 'var(--font-size-xs)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-feedback-warning)',
                }}
              >
                Gray Component Replacement (GCR) Suggestion
              </h4>
            </div>
            <p
              className='text-xs mb-2'
              style={{
                fontSize: 'var(--font-size-xs)',
                color: 'var(--color-feedback-warning)',
              }}
            >
              Reduce ink usage by {gcrSuggestion.savings}% while maintaining
              color appearance:
            </p>
            <div className='grid grid-cols-2 gap-2 text-xs'>
              <div
                className='rounded p-2'
                style={{
                  backgroundColor: 'var(--color-ui-background-secondary)',
                  borderRadius: 'var(--radius-DEFAULT)',
                }}
              >
                <div
                  className='font-medium mb-1'
                  style={{
                    fontWeight: 'var(--font-weight-medium)',
                    color: 'var(--color-ui-foreground-primary)',
                  }}
                >
                  Current
                </div>
                <div className='space-y-0.5'>
                  <div
                    style={{ color: 'var(--color-ui-foreground-secondary)' }}
                  >
                    C: {gcrSuggestion.original?.c}%
                  </div>
                  <div
                    style={{ color: 'var(--color-ui-foreground-secondary)' }}
                  >
                    M: {gcrSuggestion.original?.m}%
                  </div>
                  <div
                    style={{ color: 'var(--color-ui-foreground-secondary)' }}
                  >
                    Y: {gcrSuggestion.original?.y}%
                  </div>
                  <div
                    style={{ color: 'var(--color-ui-foreground-secondary)' }}
                  >
                    K: {gcrSuggestion.original?.k}%
                  </div>
                  <div
                    className='font-medium pt-1'
                    style={{
                      fontWeight: 'var(--font-weight-medium)',
                      borderTop: `1px solid var(--color-ui-border-light)`,
                      color: 'var(--color-ui-foreground-primary)',
                    }}
                  >
                    Total: {gcrSuggestion.original?.total}%
                  </div>
                </div>
              </div>
              <div
                className='rounded p-2'
                style={{
                  backgroundColor: 'var(--feedback-bg-success)',
                  borderRadius: 'var(--radius-DEFAULT)',
                }}
              >
                <div
                  className='font-medium mb-1'
                  style={{
                    fontWeight: 'var(--font-weight-medium)',
                    color: 'var(--color-feedback-success)',
                  }}
                >
                  Suggested
                </div>
                <div className='space-y-0.5'>
                  <div style={{ color: 'var(--color-feedback-success)' }}>
                    C: {gcrSuggestion.suggested?.c}%
                  </div>
                  <div style={{ color: 'var(--color-feedback-success)' }}>
                    M: {gcrSuggestion.suggested?.m}%
                  </div>
                  <div style={{ color: 'var(--color-feedback-success)' }}>
                    Y: {gcrSuggestion.suggested?.y}%
                  </div>
                  <div style={{ color: 'var(--color-feedback-success)' }}>
                    K: {gcrSuggestion.suggested?.k}%
                  </div>
                  <div
                    className='font-medium pt-1'
                    style={{
                      fontWeight: 'var(--font-weight-medium)',
                      borderTop: `1px solid var(--feedback-border-success)`,
                      color: 'var(--color-feedback-success)',
                    }}
                  >
                    Total: {gcrSuggestion.suggested?.total}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Interactive Substrate Preview */}
        <div
          className='rounded-lg p-3'
          style={{
            backgroundColor: 'var(--color-ui-background-tertiary)',
            borderRadius: 'var(--radius-lg)',
          }}
        >
          <div className='flex items-center justify-between mb-2'>
            <h4
              className='text-sm font-medium'
              style={{
                fontSize: 'var(--font-size-sm)',
                fontWeight: 'var(--font-weight-medium)',
                color: 'var(--color-ui-foreground-primary)',
              }}
            >
              Substrate Preview
            </h4>
            <select
              value={selectedSubstrate}
              onChange={e => setSelectedSubstrate(e.target.value)}
              className='text-xs rounded px-2 py-1'
              style={{
                fontSize: 'var(--font-size-xs)',
                backgroundColor: 'var(--color-ui-background-primary)',
                border: `1px solid var(--color-ui-border-medium)`,
                borderRadius: 'var(--radius-DEFAULT)',
                color: 'var(--color-ui-foreground-primary)',
              }}
            >
              {SUBSTRATES.map(substrate => (
                <option key={substrate.id} value={substrate.id}>
                  {substrate.label} ({substrate.inkLimit}% limit)
                </option>
              ))}
            </select>
          </div>

          <div className='flex gap-3'>
            <div className='flex-1'>
              <div
                className='h-16 mb-2'
                style={{
                  backgroundColor: selectedColor.hex,
                  filter:
                    selectedSubstrateData?.adjustment &&
                    selectedSubstrateData.adjustment !== 0
                      ? `brightness(${100 + selectedSubstrateData.adjustment}%) saturate(${100 + selectedSubstrateData.adjustment}%)`
                      : undefined,
                  borderRadius: 'var(--radius-lg)',
                  border: `1px solid var(--color-ui-border-light)`,
                }}
              />
              <div
                className='text-xs'
                style={{
                  fontSize: 'var(--font-size-xs)',
                  color: 'var(--color-ui-foreground-tertiary)',
                }}
              >
                {selectedSubstrateData?.description}
                {selectedSubstrateData?.adjustment &&
                  selectedSubstrateData.adjustment !== 0 &&
                  ` (${selectedSubstrateData.adjustment > 0 ? '+' : ''}${selectedSubstrateData.adjustment}%)`}
              </div>
            </div>

            <div className='flex-1 space-y-1'>
              {SUBSTRATES.map(substrate => (
                <button
                  key={substrate.id}
                  onClick={() => setSelectedSubstrate(substrate.id)}
                  className='w-full h-3 text-xs transition-standard'
                  style={{
                    backgroundColor: selectedColor?.hex || '#000000',
                    filter:
                      substrate.adjustment !== 0
                        ? `brightness(${100 + substrate.adjustment}%) saturate(${100 + substrate.adjustment}%)`
                        : undefined,
                    borderRadius: 'var(--radius-DEFAULT)',
                    border:
                      selectedSubstrate === substrate.id
                        ? `2px solid var(--color-brand-primary)`
                        : `1px solid var(--color-ui-border-light)`,
                    cursor: 'pointer',
                  }}
                  onMouseEnter={e => {
                    if (selectedSubstrate !== substrate.id) {
                      e.currentTarget.style.border = `1px solid var(--color-ui-border-medium)`;
                    }
                  }}
                  onMouseLeave={e => {
                    if (selectedSubstrate !== substrate.id) {
                      e.currentTarget.style.border = `1px solid var(--color-ui-border-light)`;
                    }
                  }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Cost Estimator */}
        <div
          className='rounded-lg p-3'
          style={{
            backgroundColor: 'var(--color-ui-background-tertiary)',
            borderRadius: 'var(--radius-lg)',
          }}
        >
          <button
            onClick={() => setShowCostEstimator(!showCostEstimator)}
            className='w-full flex items-center justify-between mb-2 transition-standard'
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              cursor: 'pointer',
              padding: 0,
            }}
          >
            <h4
              className='text-sm font-medium flex items-center gap-2'
              style={{
                fontSize: 'var(--font-size-sm)',
                fontWeight: 'var(--font-weight-medium)',
                color: 'var(--color-ui-foreground-primary)',
              }}
            >
              <DollarSign className='h-4 w-4' />
              Ink Cost Estimator
            </h4>
            <span
              className='text-xs'
              style={{
                fontSize: 'var(--font-size-xs)',
                color: 'var(--color-ui-foreground-tertiary)',
              }}
            >
              {showCostEstimator ? 'Hide' : 'Show'}
            </span>
          </button>

          {showCostEstimator && (
            <div className='space-y-2'>
              <div className='text-xs text-ui-text-muted'>
                Estimated cost per 1000 sheets (A4/Letter)
              </div>
              <div className='grid grid-cols-2 gap-2 text-xs'>
                <div>
                  <label className='block text-ui-text-muted mb-1'>
                    Coverage area:
                  </label>
                  <select
                    value={coverageArea}
                    onChange={e => setCoverageArea(e.target.value)}
                    className='w-full bg-ui-background border border-ui-border rounded px-2 py-1'
                  >
                    <option value='5'>5% (Text)</option>
                    <option value='20'>20% (Mixed)</option>
                    <option value='50'>50% (Heavy graphics)</option>
                    <option value='100'>100% (Full bleed)</option>
                  </select>
                </div>
                <div>
                  <label className='block text-ui-text-muted mb-1'>
                    Paper size:
                  </label>
                  <select
                    value={paperSize}
                    onChange={e => setPaperSize(e.target.value)}
                    className='w-full bg-ui-background border border-ui-border rounded px-2 py-1'
                  >
                    <option value='a4'>A4</option>
                    <option value='letter'>Letter</option>
                    <option value='a3'>A3</option>
                  </select>
                </div>
              </div>
              <div className='bg-ui-background rounded p-2 text-center'>
                <div className='text-lg font-medium text-green-600 dark:text-green-400'>
                  ${calculateInkCost}
                </div>
                <div className='text-xs text-ui-text-muted'>
                  Per 1000 sheets • {coverageArea}% coverage
                </div>
                <div className='text-xs text-ui-text-muted mt-1'>
                  <span className='text-cyan-600'>C: {cmykValues.c}%</span> •
                  <span className='text-pink-600'> M: {cmykValues.m}%</span> •
                  <span className='text-yellow-600'> Y: {cmykValues.y}%</span> •
                  <span className='text-gray-600'> K: {cmykValues.k}%</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Export Actions */}
        <div className='flex gap-2'>
          <button className='flex-1 flex items-center justify-center gap-2 text-xs bg-ui-background border border-ui-border rounded-lg py-2 hover:bg-ui-background-secondary transition-colors'>
            <Download className='h-3 w-3' />
            Export Print Report
          </button>
          <button className='flex-1 flex items-center justify-center gap-2 text-xs bg-ui-background border border-ui-border rounded-lg py-2 hover:bg-ui-background-secondary transition-colors'>
            <Printer className='h-3 w-3' />
            Print Color Proof
          </button>
        </div>
      </div>
    );
  }
);

PrintTab.displayName = 'PrintTab';
