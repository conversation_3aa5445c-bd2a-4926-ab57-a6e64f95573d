/**
 * @file SecureColorHandlers.ts
 * @description Example implementation of secure IPC handlers using validation middleware
 * 
 * This demonstrates how to use IPCValidationMiddleware with existing color operations
 * following Electron security best practices.
 */

import { IpcMainEvent } from 'electron';
// AJV v6 doesn't export JSONSchemaType, using object instead
import { ipcValidation, IPCValidationMiddleware } from './IPCValidationMiddleware';
import { ServiceLocator } from '../../services/service-locator';
import { getValidatedOrganizationId } from '../../middleware/organization-context.middleware';
import { NewColorEntry } from '../../../shared/types/color.types';

// Type definitions for secure color operations
interface GetAllColorsRequest {
  organizationId: string;
}

interface GetColorByIdRequest {
  organizationId: string;
  colorId: string;
}

interface AddColorRequest {
  organizationId: string;
  colorData: {
    name: string;
    hex: string;
    code?: string;
    notes?: string;
    product?: string;
  };
}

interface UpdateColorRequest {
  organizationId: string;
  colorId: string;
  updates: {
    name?: string;
    hex?: string;
    code?: string;
    notes?: string;
  };
}

interface DeleteColorRequest {
  organizationId: string;
  colorId: string;
}

// JSON Schema definitions for validation (AJV v6 compatible)
const GetAllColorsSchema: object = {
  type: 'object',
  properties: {
    organizationId: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      pattern: '^[a-zA-Z0-9-_]+$' // Only allow safe characters
    }
  },
  required: ['organizationId'],
  additionalProperties: false
};

const GetColorByIdSchema: object = {
  type: 'object',
  properties: {
    organizationId: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      pattern: '^[a-zA-Z0-9-_]+$'
    },
    colorId: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      pattern: '^[a-zA-Z0-9-_]+$'
    }
  },
  required: ['organizationId', 'colorId'],
  additionalProperties: false
};

const AddColorSchema: object = {
  type: 'object',
  properties: {
    organizationId: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      pattern: '^[a-zA-Z0-9-_]+$'
    },
    colorData: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          minLength: 1, 
          maxLength: 100 
        },
        hex: { 
          type: 'string', 
          pattern: '^#[0-9A-Fa-f]{6}$' // Valid hex color
        },
        code: { 
          type: 'string', 
          minLength: 0, 
          maxLength: 50,
          nullable: true
        },
        notes: { 
          type: 'string', 
          minLength: 0, 
          maxLength: 500,
          nullable: true
        },
        product: { 
          type: 'string', 
          minLength: 0, 
          maxLength: 100,
          nullable: true
        }
      },
      required: ['name', 'hex'],
      additionalProperties: false
    }
  },
  required: ['organizationId', 'colorData'],
  additionalProperties: false
};

const UpdateColorSchema: object = {
  type: 'object',
  properties: {
    organizationId: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      pattern: '^[a-zA-Z0-9-_]+$'
    },
    colorId: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      pattern: '^[a-zA-Z0-9-_]+$'
    },
    updates: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          minLength: 1, 
          maxLength: 100,
          nullable: true
        },
        hex: { 
          type: 'string', 
          pattern: '^#[0-9A-Fa-f]{6}$',
          nullable: true
        },
        code: { 
          type: 'string', 
          minLength: 0, 
          maxLength: 50,
          nullable: true
        },
        notes: { 
          type: 'string', 
          minLength: 0, 
          maxLength: 500,
          nullable: true
        }
      },
      required: [],
      additionalProperties: false
    }
  },
  required: ['organizationId', 'colorId', 'updates'],
  additionalProperties: false
};

const DeleteColorSchema: object = {
  type: 'object',
  properties: {
    organizationId: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      pattern: '^[a-zA-Z0-9-_]+$'
    },
    colorId: { 
      type: 'string', 
      minLength: 1, 
      maxLength: 100,
      pattern: '^[a-zA-Z0-9-_]+$'
    }
  },
  required: ['organizationId', 'colorId'],
  additionalProperties: false
};

/**
 * Secure Color Handlers using validation middleware
 */
export class SecureColorHandlers {
  private static instance: SecureColorHandlers;
  private middleware: IPCValidationMiddleware;

  private constructor() {
    this.middleware = ipcValidation;
  }

  static getInstance(): SecureColorHandlers {
    if (!SecureColorHandlers.instance) {
      SecureColorHandlers.instance = new SecureColorHandlers();
    }
    return SecureColorHandlers.instance;
  }

  /**
   * Register all secure color handlers
   */
  registerHandlers(): void {
    console.log('[SecureColorHandlers] 🔒 Registering secure color handlers...');

    // Get All Colors - with rate limiting
    this.middleware.createSecureHandler<GetAllColorsRequest, any[]>(
      'color:getAllSecure',
      {
        channel: 'color:getAllSecure',
        schema: GetAllColorsSchema,
        rateLimit: {
          maxRequests: 10,
          windowMs: 60000 // 10 requests per minute
        },
        requireAuth: true
      },
      this.handleGetAllColors.bind(this)
    );

    // Get Color By ID
    this.middleware.createSecureHandler<GetColorByIdRequest, any>(
      'color:getByIdSecure',
      {
        channel: 'color:getByIdSecure',
        schema: GetColorByIdSchema,
        rateLimit: {
          maxRequests: 50,
          windowMs: 60000 // 50 requests per minute
        },
        requireAuth: true
      },
      this.handleGetColorById.bind(this)
    );

    // Add Color - stricter rate limiting for writes
    this.middleware.createSecureHandler<AddColorRequest, any>(
      'color:addSecure',
      {
        channel: 'color:addSecure',
        schema: AddColorSchema,
        rateLimit: {
          maxRequests: 5,
          windowMs: 60000 // 5 additions per minute
        },
        requireAuth: true
      },
      this.handleAddColor.bind(this)
    );

    // Update Color
    this.middleware.createSecureHandler<UpdateColorRequest, any>(
      'color:updateSecure',
      {
        channel: 'color:updateSecure',
        schema: UpdateColorSchema,
        rateLimit: {
          maxRequests: 10,
          windowMs: 60000 // 10 updates per minute
        },
        requireAuth: true
      },
      this.handleUpdateColor.bind(this)
    );

    // Delete Color - most restrictive
    this.middleware.createSecureHandler<DeleteColorRequest, boolean>(
      'color:deleteSecure',
      {
        channel: 'color:deleteSecure',
        schema: DeleteColorSchema,
        rateLimit: {
          maxRequests: 3,
          windowMs: 60000 // 3 deletions per minute
        },
        requireAuth: true
      },
      this.handleDeleteColor.bind(this)
    );

    console.log('[SecureColorHandlers] ✅ All secure color handlers registered');
  }

  /**
   * Handler for getting all colors
   */
  private async handleGetAllColors(data: GetAllColorsRequest, _event: IpcMainEvent): Promise<any[]> {
    console.log(`[SecureColorHandlers] 🎨 Getting all colors for org: ${data.organizationId}`);
    
    // Verify organization context matches request
    const validatedOrgId = await getValidatedOrganizationId();
    if (validatedOrgId !== data.organizationId) {
      throw new Error('Organization context mismatch');
    }

    const colorService = ServiceLocator.getColorService();
    const colors = await colorService.getAll(data.organizationId);
    
    console.log(`[SecureColorHandlers] ✅ Retrieved ${colors.length} colors`);
    return colors;
  }

  /**
   * Handler for getting color by ID
   */
  private async handleGetColorById(data: GetColorByIdRequest, _event: IpcMainEvent): Promise<any> {
    console.log(`[SecureColorHandlers] 🎨 Getting color ${data.colorId} for org: ${data.organizationId}`);
    
    // Verify organization context
    const validatedOrgId = await getValidatedOrganizationId();
    if (validatedOrgId !== data.organizationId) {
      throw new Error('Organization context mismatch');
    }

    const colorService = ServiceLocator.getColorService();
    const color = await colorService.getById(data.colorId, data.organizationId);
    
    if (!color) {
      throw new Error('Color not found');
    }

    console.log(`[SecureColorHandlers] ✅ Retrieved color: ${color.name}`);
    return color;
  }

  /**
   * Handler for adding a new color
   */
  private async handleAddColor(data: AddColorRequest, _event: IpcMainEvent): Promise<any> {
    console.log(`[SecureColorHandlers] 🎨 Adding color ${data.colorData.name} to org: ${data.organizationId}`);
    
    // Verify organization context
    const validatedOrgId = await getValidatedOrganizationId();
    if (validatedOrgId !== data.organizationId) {
      throw new Error('Organization context mismatch');
    }

    // Additional business logic validation
    if (data.colorData.name.toLowerCase().includes('test') && process.env.NODE_ENV === 'production') {
      throw new Error('Test colors not allowed in production');
    }

    const colorService = ServiceLocator.getColorService();
    
    // Create color entry
    const colorEntry: NewColorEntry = {
      name: data.colorData.name,
      hex: data.colorData.hex.toUpperCase(), // Normalize hex
      code: data.colorData.code || '',
      notes: data.colorData.notes || '',
      product: data.colorData.product || 'default',
      cmyk: '0,0,0,0', // Default CMYK
      organizationId: data.organizationId
    };

    const newColorId = await colorService.add(colorEntry, data.organizationId);
    const newColor = await colorService.getById(newColorId, data.organizationId);
    
    console.log(`[SecureColorHandlers] ✅ Added color with ID: ${newColorId}`);
    return newColor;
  }

  /**
   * Handler for updating a color
   */
  private async handleUpdateColor(data: UpdateColorRequest, _event: IpcMainEvent): Promise<any> {
    console.log(`[SecureColorHandlers] 🎨 Updating color ${data.colorId} in org: ${data.organizationId}`);
    
    // Verify organization context
    const validatedOrgId = await getValidatedOrganizationId();
    if (validatedOrgId !== data.organizationId) {
      throw new Error('Organization context mismatch');
    }

    const colorService = ServiceLocator.getColorService();
    
    // Verify color exists and belongs to organization
    const existingColor = await colorService.getById(data.colorId, data.organizationId);
    if (!existingColor) {
      throw new Error('Color not found or access denied');
    }

    // Normalize updates
    const updates = { ...data.updates };
    if (updates.hex) {
      updates.hex = updates.hex.toUpperCase();
    }

    const success = await colorService.update(data.colorId, updates, data.organizationId);
    if (!success) {
      throw new Error('Color update failed');
    }

    const updatedColor = await colorService.getById(data.colorId, data.organizationId);
    
    console.log(`[SecureColorHandlers] ✅ Updated color: ${updatedColor?.name}`);
    return updatedColor;
  }

  /**
   * Handler for deleting a color
   */
  private async handleDeleteColor(data: DeleteColorRequest, _event: IpcMainEvent): Promise<boolean> {
    console.log(`[SecureColorHandlers] 🎨 Deleting color ${data.colorId} from org: ${data.organizationId}`);
    
    // Verify organization context
    const validatedOrgId = await getValidatedOrganizationId();
    if (validatedOrgId !== data.organizationId) {
      throw new Error('Organization context mismatch');
    }

    const colorService = ServiceLocator.getColorService();
    
    // Verify color exists and belongs to organization
    const existingColor = await colorService.getById(data.colorId, data.organizationId);
    if (!existingColor) {
      throw new Error('Color not found or access denied');
    }

    // Additional safety check - prevent deletion of system colors
    if (existingColor.code?.startsWith('SYS_')) {
      throw new Error('System colors cannot be deleted');
    }

    const success = await colorService.delete(data.colorId, data.organizationId);
    
    console.log(`[SecureColorHandlers] ✅ Deleted color: ${success}`);
    return success;
  }

  /**
   * Get validation statistics
   */
  getStats() {
    return this.middleware.getValidationStats();
  }

  /**
   * Clean up expired rate limits
   */
  cleanup() {
    this.middleware.cleanupRateLimits();
  }
}

// Export singleton instance
export const secureColorHandlers = SecureColorHandlers.getInstance();