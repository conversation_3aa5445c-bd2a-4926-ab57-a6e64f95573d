/**
 * @file sync-error-recovery.test.ts
 * @description Comprehensive tests for the sync error recovery system
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import {
  SyncErrorRecovery,
  SyncError,
  ConflictMetadata,
  ConflictResolution,
  RollbackPlan,
  RollbackAction,
  ConflictType,
  SyncErrorContext
} from '../main/services/sync/sync-error-recovery';
import { ConflictResolutionStrategy } from '../shared/constants/conflict-resolution';

// Mock dependencies
vi.mock('../main/services/supabase-client', () => ({
  ensureAuthenticatedSession: vi.fn(),
  getSupabaseClient: vi.fn(() => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        limit: vi.fn(() => Promise.resolve({ error: null }))
      }))
    }))
  }))
}));

vi.mock('../main/services/service-locator', () => ({
  ServiceLocator: {
    getDatabase: vi.fn(() => ({
      prepare: vi.fn(() => ({
        run: vi.fn()
      }))
    }))
  }
}));

describe('SyncErrorRecovery', () => {
  beforeEach(() => {
    // Clear static storage before each test
    (SyncErrorRecovery as any).conflictStorage.clear();
    (SyncErrorRecovery as any).rollbackPlans.clear();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('analyzeSyncError', () => {
    it('should categorize transaction errors correctly', () => {
      const error = new Error('Transaction rollback failed');
      const context: SyncErrorContext = {
        operation: 'sync_colors',
        table: 'colors',
        recordId: 'test-id',
        organizationId: 'org-1',
        userId: 'user-1',
        transactionId: 'tx-123',
        timestamp: Date.now()
      };

      const result = SyncErrorRecovery.analyzeSyncError(error, context);

      expect(result.type).toBe('transaction');
      expect(result.retryable).toBe(true);
      expect(result.recoveryAction).toBe('rollback_and_retry');
      expect(result.context).toBe(context);
    });

    it('should categorize authentication errors correctly', () => {
      const error = new Error('Authentication failed - invalid token');
      const result = SyncErrorRecovery.analyzeSyncError(error);

      expect(result.type).toBe('authentication');
      expect(result.retryable).toBe(true);
      expect(result.recoveryAction).toBe('refresh_session');
    });

    it('should categorize network errors correctly', () => {
      const error = new Error('Network connection timeout');
      const result = SyncErrorRecovery.analyzeSyncError(error);

      expect(result.type).toBe('network');
      expect(result.retryable).toBe(true);
      expect(result.recoveryAction).toBe('retry_with_backoff');
    });

    it('should categorize conflict errors correctly', () => {
      const error = new Error('Unique constraint violation');
      const result = SyncErrorRecovery.analyzeSyncError(error);

      expect(result.type).toBe('conflict');
      expect(result.retryable).toBe(false);
      expect(result.recoveryAction).toBe('conflict_resolution');
    });

    it('should categorize validation errors correctly', () => {
      const error = new Error('Validation failed - required field missing');
      const result = SyncErrorRecovery.analyzeSyncError(error);

      expect(result.type).toBe('validation');
      expect(result.retryable).toBe(false);
      expect(result.recoveryAction).toBe('data_cleanup');
    });

    it('should categorize unknown errors correctly', () => {
      const error = new Error('Something unexpected happened');
      const result = SyncErrorRecovery.analyzeSyncError(error);

      expect(result.type).toBe('unknown');
      expect(result.retryable).toBe(true);
      expect(result.recoveryAction).toBe('retry_once');
    });
  });

  describe('detectConflicts', () => {
    it('should detect update-update conflicts', () => {
      const localData = { id: '1', name: 'Local Name', color: '#FF0000', updated_at: '2023-01-01' };
      const remoteData = { id: '1', name: 'Remote Name', color: '#00FF00', updated_at: '2023-01-02' };
      const baseData = { id: '1', name: 'Base Name', color: '#0000FF', updated_at: '2023-01-01' };

      const conflict = SyncErrorRecovery.detectConflicts(
        localData,
        remoteData,
        baseData,
        'colors',
        '1'
      );

      expect(conflict).not.toBeNull();
      expect(conflict!.conflictType).toBe(ConflictType.UPDATE_UPDATE);
      expect(conflict!.affectedFields).toContain('name');
      expect(conflict!.affectedFields).toContain('color');
      expect(conflict!.affectedFields).toContain('updated_at');
      expect(conflict!.userResolutionRequired).toBe(true);
    });

    it('should detect delete-update conflicts', () => {
      const localData = { id: '1', name: 'Local Name', deleted_at: '2023-01-01' };
      const remoteData = { id: '1', name: 'Updated Name', deleted_at: null };

      const conflict = SyncErrorRecovery.detectConflicts(
        localData,
        remoteData,
        undefined,
        'colors',
        '1'
      );

      expect(conflict).not.toBeNull();
      expect(conflict!.conflictType).toBe(ConflictType.DELETE_UPDATE);
    });

    it('should detect update-delete conflicts', () => {
      const localData = { id: '1', name: 'Updated Name', deleted_at: null };
      const remoteData = { id: '1', name: 'Remote Name', deleted_at: '2023-01-01' };

      const conflict = SyncErrorRecovery.detectConflicts(
        localData,
        remoteData,
        undefined,
        'colors',
        '1'
      );

      expect(conflict).not.toBeNull();
      expect(conflict!.conflictType).toBe(ConflictType.UPDATE_DELETE);
    });

    it('should return null when no conflicts exist', () => {
      const localData = { id: '1', name: 'Same Name', color: '#FF0000' };
      const remoteData = { id: '1', name: 'Same Name', color: '#FF0000' };

      const conflict = SyncErrorRecovery.detectConflicts(
        localData,
        remoteData,
        undefined,
        'colors',
        '1'
      );

      expect(conflict).toBeNull();
    });

    it('should return null when both records are deleted', () => {
      const localData = { id: '1', name: 'Name', deleted_at: '2023-01-01' };
      const remoteData = { id: '1', name: 'Name', deleted_at: '2023-01-02' };

      const conflict = SyncErrorRecovery.detectConflicts(
        localData,
        remoteData,
        undefined,
        'colors',
        '1'
      );

      expect(conflict).toBeNull();
    });
  });

  describe('performThreeWayMerge', () => {
    it('should auto-merge non-conflicting changes', () => {
      const localData = { id: '1', name: 'Local Change', color: '#FF0000', description: 'Base Desc' };
      const remoteData = { id: '1', name: 'Base Name', color: '#00FF00', description: 'Remote Change' };
      const baseData = { id: '1', name: 'Base Name', color: '#FF0000', description: 'Base Desc' };

      const resolution = SyncErrorRecovery.performThreeWayMerge(localData, remoteData, baseData);

      expect(resolution.strategy).toBe(ConflictResolutionStrategy.MERGE);
      expect(resolution.resolvedData.name).toBe('Local Change'); // Local changed, remote unchanged
      expect(resolution.resolvedData.color).toBe('#00FF00'); // Remote changed, local unchanged
      expect(resolution.resolvedData.description).toBe('Remote Change'); // Remote changed, local unchanged
      expect(resolution.mergeDetails!.autoMergedFields).toContain('name');
      expect(resolution.mergeDetails!.autoMergedFields).toContain('color');
      expect(resolution.mergeDetails!.autoMergedFields).toContain('description');
      expect(resolution.mergeDetails!.manualFields).toHaveLength(0);
    });

    it('should identify manual resolution fields', () => {
      const localData = { id: '1', name: 'Local Name', color: '#FF0000' };
      const remoteData = { id: '1', name: 'Remote Name', color: '#00FF00' };
      const baseData = { id: '1', name: 'Base Name', color: '#0000FF' };

      const resolution = SyncErrorRecovery.performThreeWayMerge(localData, remoteData, baseData);

      expect(resolution.mergeDetails!.manualFields).toContain('name');
      expect(resolution.mergeDetails!.manualFields).toContain('color');
      expect(resolution.mergeDetails!.manualFields).toHaveLength(2);
      expect(resolution.mergeDetails!.autoMergedFields).toHaveLength(0); // No auto-merged fields in this case
      expect(resolution.resolvedData.id).toBe('1'); // ID is handled specially
    });

    it('should handle identical values correctly', () => {
      const localData = { id: '1', name: 'Same Name', color: '#FF0000' };
      const remoteData = { id: '1', name: 'Same Name', color: '#FF0000' };
      const baseData = { id: '1', name: 'Base Name', color: '#0000FF' };

      const resolution = SyncErrorRecovery.performThreeWayMerge(localData, remoteData, baseData);

      expect(resolution.resolvedData.name).toBe('Same Name');
      expect(resolution.resolvedData.color).toBe('#FF0000');
      expect(resolution.mergeDetails!.autoMergedFields).toContain('name');
      expect(resolution.mergeDetails!.autoMergedFields).toContain('color');
      expect(resolution.mergeDetails!.manualFields).toHaveLength(0);
    });
  });

  describe('rollback functionality', () => {
    beforeEach(() => {
      // Ensure clean state for rollback tests
      (SyncErrorRecovery as any).rollbackPlans.clear();
    });

    it('should create rollback plan correctly', () => {
      const actions: RollbackAction[] = [
        {
          id: 'action-1',
          type: 'insert',
          table: 'colors',
          recordId: 'color-1',
          afterData: { id: 'color-1', name: 'New Color' },
          timestamp: Date.now()
        },
        {
          id: 'action-2',
          type: 'update',
          table: 'colors',
          recordId: 'color-2',
          beforeData: { id: 'color-2', name: 'Old Name' },
          afterData: { id: 'color-2', name: 'New Name' },
          timestamp: Date.now()
        }
      ];

      const rollbackPlan = SyncErrorRecovery.createRollbackPlan('tx-123', actions);

      expect(rollbackPlan.transactionId).toBe('tx-123');
      expect(rollbackPlan.actions).toHaveLength(2);
      expect(rollbackPlan.actions[0].id).toBe('action-2'); // Reversed order
      expect(rollbackPlan.actions[1].id).toBe('action-1');
      expect(rollbackPlan.createdAt).toBeDefined();
    });

    it('should retrieve rollback plan by ID', () => {
      const actions: RollbackAction[] = [{
        id: 'action-1',
        type: 'insert',
        table: 'colors',
        recordId: 'color-1',
        timestamp: Date.now()
      }];

      const rollbackPlan = SyncErrorRecovery.createRollbackPlan('tx-123', actions);
      const retrieved = SyncErrorRecovery.getRollbackPlan(rollbackPlan.id);

      expect(retrieved).toBe(rollbackPlan);
    });

    it('should get rollback plans for transaction', () => {
      // Clear any existing plans first
      (SyncErrorRecovery as any).rollbackPlans.clear();
      
      const actions1: RollbackAction[] = [{
        id: 'action-1',
        type: 'insert',
        table: 'colors',
        recordId: 'color-1',
        timestamp: Date.now()
      }];

      const actions2: RollbackAction[] = [{
        id: 'action-2',
        type: 'update',
        table: 'colors',
        recordId: 'color-2',
        timestamp: Date.now()
      }];

      const plan1 = SyncErrorRecovery.createRollbackPlan('tx-123', actions1);
      const plan2 = SyncErrorRecovery.createRollbackPlan('tx-123', actions2);
      const plan3 = SyncErrorRecovery.createRollbackPlan('tx-456', actions1);

      const plansForTx123 = SyncErrorRecovery.getRollbackPlansForTransaction('tx-123');
      
      expect(plansForTx123).toHaveLength(2);
      expect(plansForTx123.every(plan => plan.transactionId === 'tx-123')).toBe(true);
      expect(plansForTx123.map(p => p.id)).toContain(plan1.id);
      expect(plansForTx123.map(p => p.id)).toContain(plan2.id);
      expect(plansForTx123.map(p => p.id)).not.toContain(plan3.id);
    });
  });

  describe('conflict management', () => {
    let testConflict: ConflictMetadata;

    beforeEach(() => {
      const localData = { id: '1', name: 'Local Name', color: '#FF0000' };
      const remoteData = { id: '1', name: 'Remote Name', color: '#00FF00' };
      
      testConflict = SyncErrorRecovery.detectConflicts(
        localData,
        remoteData,
        undefined,
        'colors',
        '1'
      )!;
    });

    it('should store and retrieve conflict metadata', () => {
      const retrieved = SyncErrorRecovery.getConflictMetadata(testConflict.id);
      expect(retrieved).toBe(testConflict);
    });

    it('should get pending conflicts', () => {
      const pending = SyncErrorRecovery.getPendingConflicts();
      expect(pending).toHaveLength(1);
      expect(pending[0]).toBe(testConflict);
    });

    it('should get conflicts for specific table', () => {
      const colorConflicts = SyncErrorRecovery.getConflictsForTable('colors');
      expect(colorConflicts).toHaveLength(1);
      expect(colorConflicts[0]).toBe(testConflict);

      const productConflicts = SyncErrorRecovery.getConflictsForTable('products');
      expect(productConflicts).toHaveLength(0);
    });

    it('should resolve conflict with user input', () => {
      const resolution: ConflictResolution = {
        strategy: ConflictResolutionStrategy.MANUAL,
        resolvedData: { id: '1', name: 'Resolved Name', color: '#FFFF00' }
      };

      const success = SyncErrorRecovery.resolveConflict(testConflict.id, resolution, 'user-123');

      expect(success).toBe(true);
      expect(testConflict.resolution).toBe(resolution);
      expect(testConflict.resolvedBy).toBe('user-123');
      expect(testConflict.resolvedAt).toBeDefined();
      expect(testConflict.userResolutionRequired).toBe(false);
    });

    it('should validate conflict resolution data', () => {
      const validResolution: ConflictResolution = {
        strategy: ConflictResolutionStrategy.MANUAL,
        resolvedData: { id: '1', name: 'Resolved Name', color: '#FFFF00' }
      };

      const validation = SyncErrorRecovery.validateConflictResolution(testConflict, validResolution);
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid conflict resolution data', () => {
      const invalidResolution: ConflictResolution = {
        strategy: 'invalid' as ConflictResolutionStrategy,
        resolvedData: { id: 'wrong-id', name: 'Resolved Name' } // Missing 'color' field and wrong ID
      };

      const validation = SyncErrorRecovery.validateConflictResolution(testConflict, invalidResolution);
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors.some(e => e.includes('Invalid resolution strategy'))).toBe(true);
      expect(validation.errors.some(e => e.includes('ID must match'))).toBe(true);
      expect(validation.errors.some(e => e.includes('Missing resolution for affected field'))).toBe(true);
    });

    it('should clear resolved conflicts', () => {
      // Resolve the conflict
      const resolution: ConflictResolution = {
        strategy: ConflictResolutionStrategy.MANUAL,
        resolvedData: { id: '1', name: 'Resolved Name', color: '#FFFF00' }
      };
      SyncErrorRecovery.resolveConflict(testConflict.id, resolution, 'user-123');

      // Manually set resolved time to past
      testConflict.resolvedAt = Date.now() - (8 * 24 * 60 * 60 * 1000); // 8 days ago

      const clearedCount = SyncErrorRecovery.clearResolvedConflicts(7);
      expect(clearedCount).toBe(1);

      const retrieved = SyncErrorRecovery.getConflictMetadata(testConflict.id);
      expect(retrieved).toBeNull();
    });
  });

  describe('error recovery attempts', () => {
    it('should attempt authentication recovery', async () => {
      const { ensureAuthenticatedSession } = await import('../main/services/supabase-client');
      vi.mocked(ensureAuthenticatedSession).mockResolvedValue({
        session: { user: { id: 'user-1' } } as any,
        error: null
      });

      const error: SyncError = {
        type: 'authentication',
        message: 'Authentication failed',
        retryable: true,
        recoveryAction: 'refresh_session'
      };

      const result = await SyncErrorRecovery.attemptRecovery(error);

      expect(result.success).toBe(true);
      expect(result.recoveryAction).toBe('Session refreshed successfully');
      expect(ensureAuthenticatedSession).toHaveBeenCalled();
    });

    it('should attempt network recovery', async () => {
      const error: SyncError = {
        type: 'network',
        message: 'Network timeout',
        retryable: true,
        recoveryAction: 'retry_with_backoff'
      };

      const result = await SyncErrorRecovery.attemptRecovery(error);

      expect(result.success).toBe(true);
      expect(result.recoveryAction).toBe('Network connectivity restored');
    });

    it('should handle conflict recovery', async () => {
      const error: SyncError = {
        type: 'conflict',
        message: 'Data conflict detected',
        retryable: false,
        recoveryAction: 'conflict_resolution'
      };

      const result = await SyncErrorRecovery.attemptRecovery(error);

      expect(result.success).toBe(false);
      expect(result.recoveryAction).toContain('manual');
    });

    it('should handle transaction recovery', async () => {
      const error: SyncError = {
        type: 'transaction',
        message: 'Transaction failed',
        retryable: true,
        recoveryAction: 'rollback_and_retry',
        context: {
          operation: 'sync_colors',
          transactionId: 'tx-123',
          timestamp: Date.now()
        }
      };

      const result = await SyncErrorRecovery.attemptRecovery(error);

      expect(result.success).toBe(false);
      expect(result.shouldRetry).toBe(true);
      expect(result.rollbackRequired).toBe(true);
    });
  });

  describe('operation history', () => {
    it('should provide operation history', () => {
      // Create some test data
      const localData = { id: '1', name: 'Local Name' };
      const remoteData = { id: '1', name: 'Remote Name' };
      const conflict = SyncErrorRecovery.detectConflicts(localData, remoteData, undefined, 'colors', '1');

      const actions: RollbackAction[] = [{
        id: 'action-1',
        type: 'insert',
        table: 'colors',
        recordId: 'color-1',
        timestamp: Date.now()
      }];
      const rollbackPlan = SyncErrorRecovery.createRollbackPlan('tx-123', actions);

      const history = SyncErrorRecovery.getOperationHistory();

      expect(history.conflicts).toHaveLength(1);
      expect(history.conflicts[0]).toBe(conflict);
      expect(history.rollbackPlans).toHaveLength(1);
      expect(history.rollbackPlans[0]).toBe(rollbackPlan);
    });
  });

  describe('recovery instructions', () => {
    it('should provide appropriate recovery instructions for each error type', () => {
      const authError: SyncError = { type: 'authentication', message: 'Auth failed', retryable: true };
      const networkError: SyncError = { type: 'network', message: 'Network failed', retryable: true };
      const conflictError: SyncError = { type: 'conflict', message: 'Conflict detected', retryable: false };
      const validationError: SyncError = { type: 'validation', message: 'Invalid data', retryable: false };
      const transactionError: SyncError = { type: 'transaction', message: 'Transaction failed', retryable: true };
      const unknownError: SyncError = { type: 'unknown', message: 'Unknown error', retryable: true };

      expect(SyncErrorRecovery.getRecoveryInstructions(authError)).toContain('Authentication expired');
      expect(SyncErrorRecovery.getRecoveryInstructions(networkError)).toContain('Network connection');
      expect(SyncErrorRecovery.getRecoveryInstructions(conflictError)).toContain('Data conflict');
      expect(SyncErrorRecovery.getRecoveryInstructions(validationError)).toContain('Invalid data');
      expect(SyncErrorRecovery.getRecoveryInstructions(transactionError)).toContain('Transaction failed');
      expect(SyncErrorRecovery.getRecoveryInstructions(unknownError)).toContain('unexpected error');
    });
  });
});