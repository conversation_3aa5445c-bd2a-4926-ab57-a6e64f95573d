/**
 * @file service.interfaces.ts
 * @description Common service interfaces for type safety and consistent architecture
 */

/**
 * Standard service result type for consistent error handling
 */
export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Base service interface with common operations
 */
export interface IBaseService {
  /**
   * Initialize the service (if needed)
   */
  initialize?(): Promise<void> | void;

  /**
   * Cleanup service resources
   */
  cleanup?(): Promise<void> | void;

  /**
   * Check if service is healthy
   */
  healthCheck?(): Promise<ServiceResult<boolean>>;
}

/**
 * Repository pattern interface for data access
 */
export interface IRepository<T, ID = string> {
  findById(id: ID, organizationId?: string): T | null;
  findAll(organizationId?: string): T[];
  create(item: Omit<T, 'id'>, organizationId?: string): T;
  update(id: ID, updates: Partial<T>, organizationId?: string): T | null;
  delete(id: ID, organizationId?: string): boolean;
}

/**
 * Sync service interface for data synchronization
 */
export interface ISyncService extends IBaseService {
  /**
   * Sync data to remote storage
   */
  syncToRemote(organizationId: string): Promise<ServiceResult<void>>;

  /**
   * Sync data from remote storage
   */
  syncFromRemote(organizationId: string): Promise<ServiceResult<void>>;

  /**
   * Check sync status
   */
  getSyncStatus(organizationId: string): Promise<ServiceResult<SyncStatus>>;
}

/**
 * Sync status information
 */
export interface SyncStatus {
  lastSyncTime?: number;
  syncInProgress: boolean;
  pendingOperations: number;
  lastError?: string;
}

/**
 * Validation service interface
 */
export interface IValidationService extends IBaseService {
  /**
   * Validate data according to service rules
   */
  validate<T>(data: T, rules?: ValidationRules): ValidationResult;
}

/**
 * Validation rules definition
 */
export interface ValidationRules {
  required?: string[];
  patterns?: Record<string, RegExp>;
  custom?: Record<string, (value: any) => boolean>;
}

/**
 * Validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Validation error details
 */
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

/**
 * Analytics service interface
 */
export interface IAnalyticsService extends IBaseService {
  /**
   * Track an event
   */
  trackEvent(event: AnalyticsEvent): Promise<void>;

  /**
   * Get analytics data
   */
  getAnalytics(query: AnalyticsQuery): Promise<ServiceResult<AnalyticsData>>;
}

/**
 * Analytics event structure
 */
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: number;
  userId?: string;
  organizationId?: string;
}

/**
 * Analytics query parameters
 */
export interface AnalyticsQuery {
  startDate?: Date;
  endDate?: Date;
  eventTypes?: string[];
  organizationId?: string;
}

/**
 * Analytics data result
 */
export interface AnalyticsData {
  events: AnalyticsEvent[];
  metrics: Record<string, number>;
  timeRange: {
    start: Date;
    end: Date;
  };
}

/**
 * Cache service interface
 */
export interface ICacheService extends IBaseService {
  /**
   * Get cached value
   */
  get<T>(key: string): Promise<T | null>;

  /**
   * Set cached value
   */
  set<T>(key: string, value: T, ttl?: number): Promise<void>;

  /**
   * Delete cached value
   */
  delete(key: string): Promise<boolean>;

  /**
   * Clear all cache
   */
  clear(): Promise<void>;
}

/**
 * Configuration service interface
 */
export interface IConfigurationService extends IBaseService {
  /**
   * Get configuration value
   */
  get<T>(key: string, defaultValue?: T): T;

  /**
   * Set configuration value
   */
  set<T>(key: string, value: T): void;

  /**
   * Check if configuration exists
   */
  has(key: string): boolean;
}

/**
 * Error handling service interface
 */
export interface IErrorHandlingService extends IBaseService {
  /**
   * Wrap a function with error handling
   */
  wrap<T, R>(
    fn: (...args: T[]) => R | Promise<R>,
    context?: string,
    metadata?: Record<string, any>
  ): (...args: T[]) => ServiceResult<R> | Promise<ServiceResult<R>>;

  /**
   * Handle error and convert to ServiceResult
   */
  handleError(error: Error | unknown, context?: string): ServiceResult<never>;
}
