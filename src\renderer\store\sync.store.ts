/**
 * @file sync.store.ts
 * @description Zustand store for sync state management
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  SyncStatus,
  SyncConfig,
  SyncAuthState,
  SyncConflict,
  ConflictResolutionStrategy,
} from '../../shared/types/sync.types';
import { storeEventBus } from '../services/store-event-bus.service';

interface SyncState {
  // Status
  status: SyncStatus;
  lastSyncTime: number | null;
  message: string | null;
  error: string | null;
  isOnline: boolean;
  pendingChanges: number;

  // Sync statistics
  syncStats: {
    colors: number;
    products: number;

    datasheets: number;
  };

  // Configuration
  config: SyncConfig;

  // Authentication
  authState: SyncAuthState & {
    user?: {
      id: string;
      email: string;
      name?: string;
      user_metadata?: {
        full_name?: string;
        avatar_url?: string;
        [key: string]: any;
      };
    };
  };

  // Organization context
  organizationStatus?:
    | 'needs_organization_setup'
    | 'needs_organization_selection'
    | 'authenticated';
  organizations?: any[];

  // Conflicts
  conflicts: SyncConflict[];

  // Actions
  initialize: () => Promise<void>;
  login: () => Promise<any>;
  signup: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  syncData: () => Promise<boolean>;
  updateConfig: (config: Partial<SyncConfig>) => Promise<void>;
  resolveConflicts: (
    resolutions: Array<{
      conflictId: string;
      resolution: 'local' | 'remote' | 'merged';
      mergedData?: unknown;
    }>
  ) => Promise<boolean>;
  refreshAuthState: () => Promise<void>;
}

// Default sync configuration
const DEFAULT_SYNC_CONFIG: SyncConfig = {
  autoSync: true,
  syncInterval: 30, // 30 minutes
  conflictResolution: ConflictResolutionStrategy.LAST_WRITE_WINS,
  maxStorageVersions: 5,
};

// Default auth state
const DEFAULT_AUTH_STATE: SyncAuthState = {
  isAuthenticated: false,
  userId: undefined,
  token: undefined,
  tokenExpiry: undefined,
};

export const useSyncStore = create<SyncState>()(
  devtools(
    (set, _get) => ({
      // Initial state
      status: SyncStatus.IDLE,
      lastSyncTime: null,
      message: null,
      error: null,
      isOnline: navigator.onLine,
      pendingChanges: 0,
      syncStats: {
        colors: 0,
        products: 0,
        datasheets: 0,
      },
      config: DEFAULT_SYNC_CONFIG,
      authState: DEFAULT_AUTH_STATE,
      conflicts: [],

      // Initialize the store
      initialize: async (): Promise<void> => {
        try {
          // Check if syncAPI is available
          if (typeof window === 'undefined') {
            console.warn(
              '[SyncStore] Window not available (SSR context), skipping initialization'
            );
            return;
          }

          if (!window.syncAPI) {
            console.warn(
              '[SyncStore] syncAPI not available for sync store initialization'
            );
            return;
          }

          if (typeof (window.syncAPI as any).getAuthState !== 'function') {
            console.warn(
              '[SyncStore] syncAPI.getAuthState not a function, skipping initialization'
            );
            return;
          }

          // Set up network monitoring
          const updateOnlineStatus = () => {
            set({ isOnline: navigator.onLine });
          };

          window.addEventListener('online', updateOnlineStatus);
          window.addEventListener('offline', updateOnlineStatus);

          // Set up listeners for sync events
          const removeStatusListener = window.syncAPI.onStatusUpdate(
            (status: any) => {
              const syncStatus = status.status as SyncStatus;
              set({
                status: syncStatus,
                message: status.message || null,
                error:
                  status.status === 'error'
                    ? status.message || 'Sync failed'
                    : null,
              });

              if (status.status === 'success') {
                set({ lastSyncTime: Date.now() });
              }
            }
          );

          const removeConflictsListener = (window.syncAPI as any).onConflicts
            ? (window.syncAPI as any).onConflicts(
                (conflicts: SyncConflict[]) => {
                  set({ conflicts });
                }
              )
            : null;

          // Get sync configuration
          try {
            if (typeof (window.syncAPI as any).getConfig === 'function') {
              const configResponse = await (window.syncAPI as any).getConfig();
              if (
                configResponse &&
                configResponse.success &&
                configResponse.data
              ) {
                set({ config: configResponse.data });
              }
            }
          } catch (configError) {
            console.warn(
              '[SyncStore] Failed to get sync config during initialization:',
              configError
            );
          }

          // Get auth state - this will restore session if one exists
          const authResponse = await (window.syncAPI as any).getAuthState();
          console.log('[SyncStore] Auth response on initialize:', authResponse);

          // Ensure authResponse is valid before proceeding
          if (authResponse && typeof authResponse === 'object') {
            if (authResponse.isAuthenticated) {
              set({
                authState: {
                  isAuthenticated: true,
                  userId: authResponse.user?.id,
                  token: authResponse.session?.access_token,
                  tokenExpiry: authResponse.session?.expires_at,
                  user: authResponse.user,
                },
                organizationStatus: authResponse.status,
                organizations: Array.isArray(authResponse.organizations)
                  ? authResponse.organizations
                  : [],
              });
            } else {
              // Not authenticated, set safe defaults
              set({
                authState: {
                  isAuthenticated: false,
                  userId: undefined,
                  token: undefined,
                  tokenExpiry: undefined,
                  user: undefined,
                },
                organizationStatus: 'needs_organization_setup',
                organizations: [],
              });
            }
          } else {
            console.warn(
              '[SyncStore] Invalid auth response during initialization:',
              authResponse
            );
          }

          // Get pending changes count
          try {
            if (typeof window.syncAPI.hasUnsyncedLocalChanges === 'function') {
              const pendingResponse =
                await window.syncAPI.hasUnsyncedLocalChanges();
              if (
                pendingResponse &&
                typeof pendingResponse === 'object' &&
                'success' in pendingResponse
              ) {
                const response = pendingResponse as {
                  success: boolean;
                  count: number;
                };
                if (response.success && typeof response.count === 'number') {
                  set({ pendingChanges: response.count });
                }
              }
            }
          } catch (error) {
            console.warn(
              '[SyncStore] Could not get pending changes count:',
              error
            );
          }

          // Store cleanup functions for later use if needed
          // Note: These would need to be called separately if cleanup is required
          // For now, we'll mark them as used by logging
          console.log('[SyncStore] Event listeners set up:', {
            statusListener: typeof removeStatusListener === 'function',
            conflictsListener: removeConflictsListener !== null,
          });
        } catch (error) {
          console.error('Error initializing sync store:', error);
          set({
            error:
              error instanceof Error
                ? error.message
                : 'Failed to initialize sync',
          });
        }
      },

      // Login with Google
      login: async () => {
        try {
          set({
            status: SyncStatus.SYNCING,
            message: 'Signing in with Google...',
            error: null,
          });

          if (!(window.syncAPI as any)?.login) {
            throw new Error('syncAPI.login not available');
          }

          const response = await (window.syncAPI as any).login();
          console.log('[SyncStore] Login response:', response);

          if (response.success) {
            // Handle organization status
            if (response.status) {
              console.log(
                '[SyncStore] Setting organizationStatus:',
                response.status,
                'organizations:',
                response.organizations
              );
              set({
                organizationStatus: response.status,
                organizations: response.organizations,
              });
            }

            // Get updated auth state
            const authResponse = await (window.syncAPI as any)?.getAuthState();
            if (authResponse?.isAuthenticated) {
              set(() => ({
                authState: {
                  isAuthenticated: true,
                  userId: authResponse.user?.id,
                  token: authResponse.session?.access_token,
                  tokenExpiry: authResponse.session?.expires_at,
                  user: authResponse.user,
                },
                status: SyncStatus.SUCCESS,
                message: 'Signed in successfully',
              }));
            }

            // Return an object with all needed information
            return {
              success: true,
              requiresConsent: response.requiresConsent || false,
              status: response.status,
              organizations: response.organizations,
            };
          } else {
            set({
              status: SyncStatus.ERROR,
              error: response.error || 'Sign in failed',
            });
            return { success: false };
          }
        } catch (error) {
          console.error('Error signing in:', error);
          set({
            status: SyncStatus.ERROR,
            error: error instanceof Error ? error.message : 'Sign in failed',
          });
          return { success: false };
        }
      },

      // Refresh auth state
      refreshAuthState: async () => {
        try {
          // Check if syncAPI is available
          if (typeof window === 'undefined') {
            console.warn('[SyncStore] Window not available (SSR context)');
            return;
          }

          if (
            !(window.syncAPI as any)?.getAuthState ||
            typeof (window.syncAPI as any).getAuthState !== 'function'
          ) {
            console.warn('[SyncStore] syncAPI.getAuthState not available yet');
            return;
          }

          const authResponse = await (window.syncAPI as any).getAuthState();
          console.log('[SyncStore] Auth response on refresh:', authResponse);

          // Ensure authResponse is valid before proceeding
          if (!authResponse || typeof authResponse !== 'object') {
            console.warn('[SyncStore] Invalid auth response:', authResponse);
            return;
          }

          // Update auth state and organization info in one call
          set({
            authState: {
              isAuthenticated: authResponse.isAuthenticated || false,
              userId: authResponse.user?.id,
              token: authResponse.session?.access_token,
              tokenExpiry: authResponse.session?.expires_at,
              user: authResponse.user,
            },
            organizationStatus: authResponse.status,
            organizations: Array.isArray(authResponse.organizations)
              ? authResponse.organizations
              : [],
          });
        } catch (error) {
          console.error('[SyncStore] Error refreshing auth state:', error);
          // Don't set an error state for auth refresh failures as it will show on the UI
          // The component will handle the unauthenticated state gracefully

          // Set safe defaults to prevent undefined state
          set({
            authState: {
              isAuthenticated: false,
              userId: undefined,
              token: undefined,
              tokenExpiry: undefined,
              user: undefined,
            },
            organizationStatus: 'needs_organization_setup',
            organizations: [],
          });
        }
      },

      // Signup (not supported with Google-only auth)
      signup: async () => {
        set({
          status: SyncStatus.ERROR,
          error: 'Please use Google sign-in',
        });
        return false;
      },

      // Logout
      logout: async () => {
        try {
          set({
            status: SyncStatus.SYNCING,
            message: 'Logging out...',
            error: null,
          });

          if (!(window.syncAPI as any)?.logout) {
            throw new Error('syncAPI.logout not available');
          }
          const response = await (window.syncAPI as any).logout();

          if (response.success) {
            set({
              authState: DEFAULT_AUTH_STATE,
              status: SyncStatus.SUCCESS,
              message: 'Logged out successfully',
            });
          } else {
            set({
              status: SyncStatus.ERROR,
              error: response.error || 'Logout failed',
            });
          }
        } catch (error) {
          console.error('Error logging out:', error);
          set({
            status: SyncStatus.ERROR,
            error: error instanceof Error ? error.message : 'Logout failed',
          });
        }
      },

      // Sync data
      syncData: async () => {
        try {
          // Set syncing status immediately
          set({
            status: SyncStatus.SYNCING,
            message: 'Syncing data...',
            error: null,
          });

          // Call the sync API using the correct method
          if (!(window.syncAPI as any)?.sync) {
            throw new Error('syncAPI.sync not available');
          }
          const response = await (window.syncAPI as any).sync();

          if (response.success) {
            // Update sync stats with counts from response
            const updateState: any = {
              status: SyncStatus.SUCCESS,
              message: response.message || 'Data synced successfully',
              lastSyncTime: Date.now(),
            };

            // If response includes counts, update syncStats
            if (response.counts) {
              updateState.syncStats = {
                colors: response.counts.colors || 0,
                products: response.counts.products || 0,
                datasheets: response.counts.datasheets || 0,
              };
            }

            set(updateState);

            // Auto-refresh UI data stores after successful sync using event bus
            console.log(
              '[SyncStore] ✅ Sync completed successfully, triggering data refresh...'
            );
            storeEventBus.emit({
              type: 'DATA_REFRESH_REQUESTED',
              source: 'sync-store',
            });

            return true;
          } else {
            // Handle sync failure
            {
              set({
                status: SyncStatus.ERROR,
                error: response.error || 'Sync failed',
              });
            }
            return false;
          }
        } catch (error) {
          console.error('Error syncing data:', error);
          set({
            status: SyncStatus.ERROR,
            error: error instanceof Error ? error.message : 'Sync failed',
          });
          return false;
        }
      },

      // Update config
      updateConfig: async (config: Partial<SyncConfig>) => {
        try {
          if (!(window.syncAPI as any)?.updateConfig) {
            throw new Error('syncAPI.updateConfig not available');
          }
          const response = await (window.syncAPI as any).updateConfig(config);

          if (response.success && response.data) {
            set({ config: response.data });
          } else {
            set({
              error: response.error || 'Failed to update config',
            });
          }
        } catch (error) {
          console.error('Error updating config:', error);
          set({
            error:
              error instanceof Error
                ? error.message
                : 'Failed to update config',
          });
        }
      },

      // Resolve conflicts
      resolveConflicts: async (
        resolutions: Array<{
          conflictId: string;
          resolution: 'local' | 'remote' | 'merged';
          mergedData?: unknown;
        }>
      ) => {
        try {
          set({
            status: SyncStatus.SYNCING,
            message: 'Resolving conflicts...',
            error: null,
          });

          if (!window.syncAPI) {
            throw new Error('syncAPI not available');
          }
          const response = await window.syncAPI.resolveConflicts(resolutions);

          if (response.success) {
            set({
              status: SyncStatus.SUCCESS,
              message: 'Conflicts resolved successfully',
              conflicts: [],
              lastSyncTime: Date.now(),
            });
            return true;
          } else {
            set({
              status: SyncStatus.ERROR,
              error: response.error || 'Failed to resolve conflicts',
            });
            return false;
          }
        } catch (error) {
          console.error('Error resolving conflicts:', error);
          set({
            status: SyncStatus.ERROR,
            error:
              error instanceof Error
                ? error.message
                : 'Failed to resolve conflicts',
          });
          return false;
        }
      },
    }),
    { name: 'sync-store' }
  )
);

// Initialize the store when the module is loaded
// Check if window.syncAPI exists before initializing, with retry mechanism and timeout
if (typeof window !== 'undefined') {
  let retryCount = 0;
  const maxRetries = 50; // 5 seconds max wait time

  const initializeSyncStore = () => {
    if (
      window.syncAPI &&
      typeof (window.syncAPI as any).getAuthState === 'function'
    ) {
      console.log('[SyncStore] syncAPI available, initializing sync store...');
      useSyncStore.getState().initialize();
    } else if (retryCount < maxRetries) {
      retryCount++;
      if (retryCount % 10 === 0) {
        // Log every second
        console.warn(
          `[SyncStore] syncAPI not available yet, retry ${retryCount}/${maxRetries}. Available APIs:`,
          Object.keys(window)
        );
      }
      setTimeout(initializeSyncStore, 100);
    } else {
      console.error(
        '[SyncStore] syncAPI failed to initialize after 5 seconds. Available APIs:',
        Object.keys(window)
      );
      console.error('[SyncStore] window.syncAPI:', window.syncAPI);
      // Set safe defaults and continue without sync functionality
      useSyncStore.setState({
        authState: {
          isAuthenticated: false,
          userId: undefined,
          token: undefined,
          tokenExpiry: undefined,
        },
        organizationStatus: 'needs_organization_setup',
        organizations: [],
      });
    }
  };

  // Try immediate initialization, fall back to delayed if needed
  initializeSyncStore();
} else {
  console.warn(
    '[SyncStore] Not in browser environment, sync store initialization deferred'
  );
}

// Export selectors for common operations
export const useSyncStatus = () =>
  useSyncStore(state => ({
    status: state.status,
    message: state.message,
    error: state.error,
    lastSyncTime: state.lastSyncTime,
    syncStats: state.syncStats,
  }));

export const useSyncAuth = () =>
  useSyncStore(state => ({
    isAuthenticated: state.authState.isAuthenticated,
    userId: state.authState.userId,
    user: state.authState.user,
    organizationStatus: state.organizationStatus,
    organizations: state.organizations,
    login: state.login,
    signup: state.signup,
    logout: state.logout,
  }));

export const useSyncConfig = () =>
  useSyncStore(state => ({
    config: state.config,
    updateConfig: state.updateConfig,
  }));

export const useSyncConflicts = () =>
  useSyncStore(state => ({
    conflicts: state.conflicts,
    resolveConflicts: state.resolveConflicts,
  }));

export const useSyncActions = () =>
  useSyncStore(state => ({
    syncData: state.syncData,
  }));
