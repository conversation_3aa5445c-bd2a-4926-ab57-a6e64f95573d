/**
 * @file mapped-types.ts
 * @description Advanced mapped types for interface transformations and API responses
 * Implements cutting-edge TypeScript patterns for dynamic type transformations
 */

import type {
  Brand,
  DeepPartial,
  UnionToIntersection,
} from './advanced-utilities.types';

import type {
  Result,
  AsyncResult,
} from './error-handling.types';

// ===== FUNDAMENTAL MAPPED TYPE UTILITIES =====

/**
 * Transform all properties to optional with a specific suffix
 */
export type OptionalSuffixed<T, Suffix extends string> = {
  [K in keyof T as `${string & K}${Suffix}`]?: T[K];
};

/**
 * Transform all properties to required with a specific prefix
 */
export type RequiredPrefixed<T, Prefix extends string> = {
  [K in keyof T as `${Prefix}${string & K}`]-?: T[K];
};

/**
 * Transform properties based on their types
 */
export type TransformByType<T, TFrom, TTo> = {
  [K in keyof T]: T[K] extends TFrom ? TTo : T[K];
};

/**
 * Exclude properties by type
 */
export type ExcludeByType<T, TExclude> = {
  [K in keyof T as T[K] extends TExclude ? never : K]: T[K];
};

/**
 * Include only properties by type
 */
export type IncludeByType<T, TInclude> = {
  [K in keyof T as T[K] extends TInclude ? K : never]: T[K];
};

/**
 * Make properties readonly based on a condition
 */
export type ConditionalReadonly<T, TCondition> = {
  readonly [K in keyof T as T[K] extends TCondition ? K : never]: T[K];
} & {
  [K in keyof T as T[K] extends TCondition ? never : K]: T[K];
};

// ===== ADVANCED API TRANSFORMATION TYPES =====

/**
 * Transform interface for API request payload
 */
export type ToApiRequest<T> = {
  [K in keyof T as K extends 'id' | 'createdAt' | 'updatedAt'
    ? never
    : K]: T[K] extends Date
    ? string
    : T[K] extends Brand<infer U, any>
      ? U
      : T[K];
};

/**
 * Transform interface for API response payload
 */
export type ToApiResponse<T> = {
  [K in keyof T]: T[K] extends Date
    ? string
    : T[K] extends object
      ? ToApiResponse<T[K]>
      : T[K];
} & {
  readonly id: string;
  readonly createdAt: string;
  readonly updatedAt: string;
};

/**
 * Transform interface for database storage
 */
export type ToDatabase<T> = {
  [K in keyof T]: T[K] extends Brand<infer U, any>
    ? U
    : T[K] extends Date
      ? string
      : T[K] extends object
        ? string // Serialize objects to JSON
        : T[K];
};

/**
 * Transform database row back to domain object
 */
export type FromDatabase<T, TDomain> = {
  [K in keyof T]: K extends keyof TDomain
    ? TDomain[K] extends Brand<any, any>
      ? TDomain[K]
      : TDomain[K] extends Date
        ? Date
        : TDomain[K] extends object
          ? TDomain[K] // Deserialize from JSON
          : T[K]
    : T[K];
};

// ===== FORM AND VALIDATION TRANSFORMATIONS =====

/**
 * Transform interface for form input with string values
 */
export type ToFormInput<T> = {
  [K in keyof T]: T[K] extends number
    ? string | number
    : T[K] extends boolean
      ? boolean | string
      : T[K] extends Date
        ? string
        : T[K] extends Brand<infer U, any>
          ? U extends string | number
            ? string
            : U
          : T[K] extends object
            ? ToFormInput<T[K]>
            : T[K];
};

/**
 * Transform form values back to typed interface
 */
export type FromFormInput<T, TTarget> = {
  [K in keyof T]: K extends keyof TTarget
    ? TTarget[K] extends number
      ? number
      : TTarget[K] extends boolean
        ? boolean
        : TTarget[K] extends Date
          ? Date
          : TTarget[K] extends Brand<any, any>
            ? TTarget[K]
            : TTarget[K] extends object
              ? FromFormInput<T[K], TTarget[K]>
              : T[K]
    : T[K];
};

/**
 * Create validation schema type from interface
 */
export type ValidationSchema<T> = {
  [K in keyof T]-?: {
    readonly required: boolean;
    readonly type:
      | 'string'
      | 'number'
      | 'boolean'
      | 'date'
      | 'object'
      | 'array';
    readonly validator?: (value: unknown) => Result<T[K]>;
    readonly transform?: (value: unknown) => T[K];
    readonly default?: T[K];
    readonly constraints?: ValidationConstraints<T[K]>;
  };
};

/**
 * Validation constraints based on property type
 */
export type ValidationConstraints<T> = T extends string
  ? {
      minLength?: number;
      maxLength?: number;
      pattern?: RegExp;
      enum?: readonly T[];
    }
  : T extends number
    ? {
        min?: number;
        max?: number;
        step?: number;
        precision?: number;
      }
    : T extends Date
      ? {
          min?: Date;
          max?: Date;
        }
      : T extends any[]
        ? {
            minItems?: number;
            maxItems?: number;
            uniqueItems?: boolean;
          }
        : Record<string, unknown>;

// ===== CRUD OPERATION TRANSFORMATIONS =====

/**
 * Create operation payload type
 */
export type CreatePayload<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Update operation payload type (all optional except id)
 */
export type UpdatePayload<T> = { readonly id: string } & Partial<
  Omit<T, 'id' | 'createdAt' | 'updatedAt'>
>;

/**
 * Patch operation payload type (deeply partial)
 */
export type PatchPayload<T> = { readonly id: string } & DeepPartial<
  Omit<T, 'id' | 'createdAt' | 'updatedAt'>
>;

/**
 * Query filter type
 */
export type QueryFilter<T> = {
  [K in keyof T]?: T[K] extends string
    ?
        | string
        | {
            contains?: string;
            startsWith?: string;
            endsWith?: string;
            equals?: string;
          }
    : T[K] extends number
      ?
          | number
          | {
              gte?: number;
              lte?: number;
              gt?: number;
              lt?: number;
              equals?: number;
            }
      : T[K] extends boolean
        ? boolean
        : T[K] extends Date
          ? Date | { gte?: Date; lte?: Date; gt?: Date; lt?: Date }
          : T[K] extends Brand<infer U, any>
            ? U | QueryFilter<{ value: U }>['value']
            : never;
};

/**
 * Sort specification type
 */
export type SortSpec<T> =
  | {
      [K in keyof T]?: 'asc' | 'desc';
    }
  | Array<{ field: keyof T; direction: 'asc' | 'desc' }>;

/**
 * Pagination parameters
 */
export interface PaginationParams {
  readonly page?: number;
  readonly limit?: number;
  readonly offset?: number;
  readonly cursor?: string;
}

/**
 * Complete query parameters
 */
export type QueryParams<T> = {
  readonly filter?: QueryFilter<T>;
  readonly sort?: SortSpec<T>;
  readonly pagination?: PaginationParams;
  readonly include?: readonly (keyof T)[];
  readonly exclude?: readonly (keyof T)[];
};

// ===== EVENT AND NOTIFICATION TRANSFORMATIONS =====

/**
 * Transform interface to event payload
 */
export type ToEventPayload<T, TEventType extends string> = {
  readonly type: TEventType;
  readonly timestamp: string;
  readonly data: T;
  readonly metadata?: Record<string, unknown>;
};

/**
 * Transform interface to notification
 */
export type ToNotification<T> = {
  readonly id: string;
  readonly title: string;
  readonly message: string;
  readonly type: 'info' | 'success' | 'warning' | 'error';
  readonly data?: T;
  readonly actions?: readonly {
    readonly label: string;
    readonly action: string;
    readonly primary?: boolean;
  }[];
  readonly timestamp: string;
  readonly read: boolean;
};

// ===== CACHE AND STORAGE TRANSFORMATIONS =====

/**
 * Transform interface for caching with TTL
 */
export type ToCacheEntry<T> = {
  readonly key: string;
  readonly value: T;
  readonly timestamp: number;
  readonly ttl?: number;
  readonly metadata?: {
    readonly hits: number;
    readonly lastAccessed: number;
    readonly tags?: readonly string[];
  };
};

/**
 * Transform interface for localStorage serialization
 */
export type ToStorageValue<T> = T extends object
  ? string // JSON.stringify
  : T extends string | number | boolean
    ? string
    : never;

/**
 * Transform stored value back to typed interface
 */
export type FromStorageValue<T, TOriginal> = TOriginal extends object
  ? TOriginal
  : TOriginal extends string | number | boolean
    ? TOriginal
    : never;

// ===== ADVANCED TRANSFORMATION UTILITIES =====

/**
 * Deep transformation with type mapping
 */
export type DeepTransform<
  T,
  TTypeMap extends Record<string, any>,
> = T extends object
  ? T extends any[]
    ? Array<DeepTransform<T[number], TTypeMap>>
    : {
        [K in keyof T]: T[K] extends keyof TTypeMap
          ? TTypeMap[T[K]]
          : DeepTransform<T[K], TTypeMap>;
      }
  : T extends keyof TTypeMap
    ? TTypeMap[T]
    : T;

/**
 * Conditional transformation based on property name pattern
 */
export type ConditionalTransform<T, TPattern extends string, TTransform> = {
  [K in keyof T as K extends `${string}${TPattern}${string}`
    ? K
    : never]: TTransform;
} & {
  [K in keyof T as K extends `${string}${TPattern}${string}` ? never : K]: T[K];
};

/**
 * Flatten nested object structure
 */
export type FlattenObject<T, TPrefix extends string = ''> = T extends object
  ? {
      [K in keyof T as K extends string
        ? T[K] extends object
          ? never
          : TPrefix extends ''
            ? K
            : `${TPrefix}.${K}`
        : never]: T[K];
    } & UnionToIntersection<
      {
        [K in keyof T]: K extends string
          ? T[K] extends object
            ? FlattenObject<T[K], TPrefix extends '' ? K : `${TPrefix}.${K}`>
            : never
          : never;
      }[keyof T]
    >
  : never;

// ===== REPOSITORY PATTERN TRANSFORMATIONS =====

/**
 * Repository interface for any entity
 */
export type Repository<T> = {
  readonly create: (payload: CreatePayload<T>) => AsyncResult<T>;
  readonly findById: (id: string) => AsyncResult<T | null>;
  readonly findMany: (params?: QueryParams<T>) => AsyncResult<{
    readonly items: readonly T[];
    readonly total: number;
    readonly hasMore: boolean;
  }>;
  readonly update: (payload: UpdatePayload<T>) => AsyncResult<T>;
  readonly patch: (payload: PatchPayload<T>) => AsyncResult<T>;
  readonly delete: (id: string) => AsyncResult<boolean>;
  readonly exists: (id: string) => AsyncResult<boolean>;
};

/**
 * Service interface pattern
 */
export type Service<T> = Repository<T> & {
  readonly validate: (data: unknown) => Result<T>;
  readonly transform: {
    readonly toApi: (entity: T) => ToApiResponse<T>;
    readonly fromApi: (data: unknown) => Result<T>;
    readonly toDatabase: (entity: T) => ToDatabase<T>;
    readonly fromDatabase: (row: unknown) => Result<T>;
  };
};

// ===== MIGRATION AND VERSIONING TRANSFORMATIONS =====

/**
 * Version migration transformer
 */
export type MigrationTransform<TFrom, TTo> = {
  readonly version: string;
  readonly up: (data: TFrom) => Result<TTo>;
  readonly down: (data: TTo) => Result<TFrom>;
  readonly validate: (data: unknown) => data is TFrom;
};

/**
 * Versioned entity with migration support
 */
export type VersionedEntity<T> = T & {
  readonly __version: string;
  readonly __migrations?: readonly MigrationTransform<any, any>[];
};

/**
 * Schema evolution tracking
 */
export type SchemaEvolution<T> = {
  readonly currentVersion: string;
  readonly entity: T;
  readonly migrations: Record<string, MigrationTransform<any, any>>;
  readonly migrate: (fromVersion: string, data: unknown) => Result<T>;
};

// ===== UTILITY FUNCTIONS FOR MAPPED TYPES =====

/**
 * Extract keys that match a pattern
 */
export type ExtractKeysMatching<T, TPattern extends string> = {
  [K in keyof T]: K extends `${string}${TPattern}${string}` ? K : never;
}[keyof T];

/**
 * Transform keys with pattern replacement
 */
export type TransformKeys<T, TFrom extends string, TTo extends string> = {
  [K in keyof T as K extends string
    ? K extends `${infer Prefix}${TFrom}${infer Suffix}`
      ? `${Prefix}${TTo}${Suffix}`
      : K
    : K]: T[K];
};

/**
 * Group properties by prefix
 */
export type GroupByPrefix<T, TPrefix extends string> = {
  [K in ExtractKeysMatching<T, TPrefix>]: T[K];
};

/**
 * Remove prefix from property names
 */
export type RemovePrefix<T, TPrefix extends string> = {
  [K in keyof T as K extends `${TPrefix}${infer Suffix}` ? Suffix : K]: T[K];
};

// Export all types for module compatibility
export {};
