/**
 * @file SyncAuth.tsx
 * @description Component for sync authentication with Google and organization setup
 */

import React, { useState, useEffect } from 'react';
import { useSyncAuth, useSyncStore } from '../../store/sync.store';
import { useOrganizationStore } from '../../store/organization.store';
import { GoogleSignIn } from './GoogleSignIn';
import { GDPRConsent } from './GDPRConsent';
import { OrganizationSetup } from '../organization/OrganizationSetup';
import { OrganizationSelection } from '../organization/OrganizationSelection';

interface SyncAuthProps {
  onSuccess?: () => void;
}

export const SyncAuth: React.FC<SyncAuthProps> = ({ onSuccess }) => {
  const { isAuthenticated, user, logout, organizationStatus } = useSyncAuth();
  const refreshAuthState = useSyncStore(state => state.refreshAuthState);
  const { currentOrganization, organizations } = useOrganizationStore();
  const [showGDPRConsent, setShowGDPRConsent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Development-only logging for debugging auth state
  if (process.env.NODE_ENV === 'development' && process.env.DEBUG_AUTH) {
    console.log('[SyncAuth] Current state:', {
      isAuthenticated,
      user: user ? { email: user.email, id: user.id } : null,
      organizationStatus,
      currentOrganization: currentOrganization
        ? {
            name: currentOrganization.name,
            id: currentOrganization.external_id,
          }
        : null,
      organizationsCount: organizations?.length || 0,
    });
  }

  // Check auth state on mount and refresh periodically
  useEffect(() => {
    refreshAuthState();
  }, [refreshAuthState]);

  // Listen for auth-complete events from the main process
  useEffect(() => {
    const handleAuthComplete = (
      event: any,
      result: { success: boolean; error?: string }
    ) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[SyncAuth] Received auth-complete event:', result);
      }
      if (result.success) {
        refreshAuthState();
      } else if (result.error) {
        setError(result.error);
      }
    };

    // @ts-ignore - window.api is defined in preload
    if (window.api?.on) {
      window.api.on('auth-complete', handleAuthComplete);

      return () => {
        window.api.off('auth-complete', handleAuthComplete);
      };
    }

    return undefined;
  }, [refreshAuthState]);

  // Handle Google sign-in success
  const handleSignInSuccess = async (loginResult?: any) => {
    // If the login result indicates GDPR consent is required, show the dialog
    if (loginResult?.requiresConsent) {
      setShowGDPRConsent(true);
      return;
    }

    // Refresh auth state to update UI
    await refreshAuthState();

    // The login response will include organization status
    // which determines what UI to show next
  };

  // Handle organization setup completion
  const handleOrganizationComplete = () => {
    // Refresh auth state and trigger success callback
    refreshAuthState();
    onSuccess?.();
  };

  // Handle GDPR consent
  const handleGDPRAccept = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await window.syncAPI.acceptGDPR();

      if (result.success) {
        setShowGDPRConsent(false);
        // Check if we need organization setup
        if (
          organizationStatus === 'needs_organization_setup' ||
          organizationStatus === 'needs_organization_selection'
        ) {
          // Will be handled by the organization flow
        } else {
          onSuccess?.();
        }
      } else {
        setError(result.error || 'Failed to record consent');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to record consent');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGDPRDecline = () => {
    setShowGDPRConsent(false);
    // Sign out if user declines
    window.syncAPI.logout();
  };

  // Handle logout
  const handleLogout = async () => {
    setIsLoading(true);

    try {
      await logout();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Logout failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle data export
  const handleExportData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await window.syncAPI.exportData();

      if (result.success) {
        // Show success message
        alert('Data exported successfully');
      } else {
        setError(result.error || 'Export failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Export failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle account deletion
  const handleDeleteAccount = async () => {
    const confirmed = confirm(
      'Are you sure you want to delete your account? This will schedule your data for deletion in 30 days.'
    );

    if (!confirmed) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await window.syncAPI.deleteAccount();

      if (result.success) {
        alert(
          'Your account has been scheduled for deletion. You will receive a confirmation email.'
        );
        onSuccess?.();
      } else {
        setError(result.error || 'Deletion failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Deletion failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Show GDPR consent if needed
  if (showGDPRConsent) {
    return (
      <GDPRConsent
        onAccept={handleGDPRAccept}
        onDecline={handleGDPRDecline}
        isLoading={isLoading}
      />
    );
  }

  // Show organization setup if needed
  if (
    isAuthenticated &&
    user &&
    organizationStatus === 'needs_organization_setup'
  ) {
    return (
      <OrganizationSetup
        user={user as any}
        onComplete={handleOrganizationComplete}
      />
    );
  }

  // Show organization selection if needed
  if (
    isAuthenticated &&
    user &&
    organizationStatus === 'needs_organization_selection'
  ) {
    return (
      <OrganizationSelection
        organizations={organizations}
        onSelect={handleOrganizationComplete}
      />
    );
  }

  // Show authenticated state with organization info
  if (isAuthenticated && user && currentOrganization) {
    return (
      <div className='flex flex-col space-y-4'>
        <div className='text-sm text-gray-700'>
          <p>
            Signed in as: <strong>{user.email}</strong>
          </p>
          <p className='text-xs text-gray-500 mt-1'>
            Organization: <strong>{currentOrganization.name}</strong>
          </p>
        </div>

        {error && (
          <div className='text-sm text-red-500 bg-red-100 p-2 rounded'>
            {error}
          </div>
        )}

        <div className='flex flex-col space-y-2'>
          <button
            onClick={handleExportData}
            disabled={isLoading}
            className={`px-4 py-2 rounded text-sm ${
              isLoading
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            Export My Data
          </button>

          <button
            onClick={handleLogout}
            disabled={isLoading}
            className={`px-4 py-2 rounded text-sm ${
              isLoading
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-red-500 hover:bg-red-600 text-white'
            }`}
          >
            {isLoading ? 'Loading...' : 'Sign Out'}
          </button>

          <button
            onClick={handleDeleteAccount}
            disabled={isLoading}
            className={`px-4 py-2 rounded text-sm ${
              isLoading
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-red-100 hover:bg-red-200 text-red-700'
            }`}
          >
            Delete Account
          </button>
        </div>
      </div>
    );
  }

  // Show sign-in state
  return (
    <div className='flex flex-col space-y-4'>
      <div className='text-sm text-gray-700 mb-4'>
        <p>
          Sign in with your Google account to enable real-time sync across all
          your devices.
        </p>
      </div>

      <GoogleSignIn onSuccess={handleSignInSuccess} />
    </div>
  );
};
