-- Migration 017: Denormalize color spaces into JSON column
-- This migration consolidates separate color space tables into a single JSON column
-- for improved performance and simplified sync operations

-- Add color_spaces JSON column to store all color space data
ALTER TABLE colors ADD COLUMN color_spaces JSON DEFAULT '{}';

-- Add missing boolean columns if they don't exist (some may already exist from previous migrations)
-- Note: SQLite doesn't support IF NOT EXISTS for columns, so these may error if they already exist
-- The migration runner handles these gracefully

-- Try to add gradient support columns (may already exist from migration 011)
-- ALTER TABLE colors ADD COLUMN is_gradient BOOLEAN NOT NULL DEFAULT FALSE;
-- ALTER TABLE colors ADD COLUMN is_metallic BOOLEAN NOT NULL DEFAULT FALSE;
-- ALTER TABLE colors ADD COLUMN is_effect BOOLEAN NOT NULL DEFAULT FALSE;

-- Create indexes for JSON queries on color spaces
CREATE INDEX IF NOT EXISTS idx_colors_color_spaces_cmyk ON colors(json_extract(color_spaces, '$.cmyk.c'), json_extract(color_spaces, '$.cmyk.m'), json_extract(color_spaces, '$.cmyk.y'), json_extract(color_spaces, '$.cmyk.k'));
CREATE INDEX IF NOT EXISTS idx_colors_has_gradient ON colors(json_extract(color_spaces, '$.gradient')) WHERE json_extract(color_spaces, '$.gradient') IS NOT NULL;

-- Data Migration: Populate color_spaces JSON from existing normalized tables
-- This will run as a single transaction to ensure data consistency

-- Step 1: Populate basic color space data for all colors
UPDATE colors SET color_spaces = json_object(
  'cmyk', CASE 
    WHEN EXISTS (SELECT 1 FROM color_cmyk WHERE color_id = colors.id) 
    THEN json_object(
      'c', (SELECT c FROM color_cmyk WHERE color_id = colors.id),
      'm', (SELECT m FROM color_cmyk WHERE color_id = colors.id),
      'y', (SELECT y FROM color_cmyk WHERE color_id = colors.id),
      'k', (SELECT k FROM color_cmyk WHERE color_id = colors.id)
    )
    ELSE NULL
  END,
  'rgb', CASE 
    WHEN EXISTS (SELECT 1 FROM color_rgb WHERE color_id = colors.id)
    THEN json_object(
      'r', (SELECT r FROM color_rgb WHERE color_id = colors.id),
      'g', (SELECT g FROM color_rgb WHERE color_id = colors.id),
      'b', (SELECT b FROM color_rgb WHERE color_id = colors.id)
    )
    ELSE NULL
  END,
  'lab', CASE 
    WHEN EXISTS (SELECT 1 FROM color_lab WHERE color_id = colors.id)
    THEN json_object(
      'l', (SELECT l FROM color_lab WHERE color_id = colors.id),
      'a', (SELECT a FROM color_lab WHERE color_id = colors.id),
      'b', (SELECT b FROM color_lab WHERE color_id = colors.id)
    )
    ELSE NULL
  END,
  'hsl', CASE 
    WHEN EXISTS (SELECT 1 FROM color_hsl WHERE color_id = colors.id)
    THEN json_object(
      'h', (SELECT h FROM color_hsl WHERE color_id = colors.id),
      's', (SELECT s FROM color_hsl WHERE color_id = colors.id),
      'l', (SELECT l FROM color_hsl WHERE color_id = colors.id)
    )
    ELSE NULL
  END
)
WHERE color_spaces = '{}' OR color_spaces IS NULL;

-- Step 2: Populate gradient data for gradient colors
UPDATE colors SET color_spaces = json_set(
  color_spaces,
  '$.gradient',
  json_object(
    'stops', (
      SELECT json_group_array(
        json_object(
          'color', hex,
          'position', position * 100  -- Convert from decimal (0-1) to percentage (0-100)
        )
      )
      FROM gradient_stops
      WHERE gradient_id = colors.id
      ORDER BY stop_index
    )
  )
)
WHERE EXISTS (SELECT 1 FROM gradient_stops WHERE gradient_id = colors.id);

-- Step 3: Clean up NULL values in the JSON to keep it tidy
UPDATE colors SET color_spaces = json_remove(
  color_spaces,
  CASE WHEN json_extract(color_spaces, '$.cmyk') IS NULL THEN '$.cmyk' END,
  CASE WHEN json_extract(color_spaces, '$.rgb') IS NULL THEN '$.rgb' END,
  CASE WHEN json_extract(color_spaces, '$.lab') IS NULL THEN '$.lab' END,
  CASE WHEN json_extract(color_spaces, '$.hsl') IS NULL THEN '$.hsl' END
)
WHERE json_extract(color_spaces, '$.cmyk') IS NULL 
   OR json_extract(color_spaces, '$.rgb') IS NULL 
   OR json_extract(color_spaces, '$.lab') IS NULL 
   OR json_extract(color_spaces, '$.hsl') IS NULL;

-- Step 4: Update the is_gradient column based on gradient data
UPDATE colors SET is_gradient = 1 
WHERE json_extract(color_spaces, '$.gradient') IS NOT NULL
  AND json_array_length(json_extract(color_spaces, '$.gradient.stops')) > 0;

-- Create a backup table for the migration (optional safety measure)
CREATE TABLE IF NOT EXISTS color_spaces_migration_backup AS 
SELECT 
  c.id,
  c.external_id,
  c.code,
  c.hex,
  cm.c as cmyk_c, cm.m as cmyk_m, cm.y as cmyk_y, cm.k as cmyk_k,
  cr.r as rgb_r, cr.g as rgb_g, cr.b as rgb_b,
  cl.l as lab_l, cl.a as lab_a, cl.b as lab_b,
  ch.h as hsl_h, ch.s as hsl_s, ch.l as hsl_l,
  (
    SELECT json_group_array(
      json_object(
        'stop_index', stop_index,
        'position', position,
        'hex', hex
      )
    )
    FROM gradient_stops gs 
    WHERE gs.gradient_id = c.id
  ) as gradient_stops_backup
FROM colors c
LEFT JOIN color_cmyk cm ON c.id = cm.color_id
LEFT JOIN color_rgb cr ON c.id = cr.color_id
LEFT JOIN color_lab cl ON c.id = cl.color_id
LEFT JOIN color_hsl ch ON c.id = ch.color_id;

-- Record migration completion
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (17, 'denormalize_color_spaces');

-- Log migration stats
-- Note: These are just comments for manual verification
-- Total colors migrated: SELECT COUNT(*) FROM colors WHERE color_spaces != '{}';
-- Colors with CMYK: SELECT COUNT(*) FROM colors WHERE json_extract(color_spaces, '$.cmyk') IS NOT NULL;
-- Colors with gradients: SELECT COUNT(*) FROM colors WHERE json_extract(color_spaces, '$.gradient') IS NOT NULL;