/**
 * Auto-updater module for the application
 */
import { <PERSON>rowserWindow, ipc<PERSON>ain, app } from 'electron';
import { autoUpdater, UpdateInfo } from 'electron-updater';
import { appLogger } from './utils/logger';
import { is } from 'electron-util';

// Configure electron-updater logging using safe wrapper to prevent IPC handler conflicts
// CRITICAL: Don't pass electron-log instance directly - wrap it to prevent IPC access
autoUpdater.logger = {
  info: (message: string, ...args: any[]) => appLogger.info(message, ...args),
  warn: (message: string, ...args: any[]) => appLogger.warn(message, ...args),
  error: (message: string, ...args: any[]) => appLogger.error(message, ...args),
  debug: (message: string, ...args: any[]) => appLogger.debug(message, ...args),
  // Note: verbose and silly methods not available in electron-log scoped logger
};

// Configure GitHub authentication for private repositories
autoUpdater.autoDownload = false; // Prevent automatic downloads until we confirm security
autoUpdater.allowPrerelease = false; // Only use production releases by default

// GitHub authentication from environment variable
// IMPORTANT: This token requires 'repo' scope (for private repositories)
const setupGitHubAuthentication = () => {
  try {
    // Try to get token from environment variable (most secure)
    const token = process.env.GH_TOKEN || process.env.GITHUB_TOKEN;

    if (token) {
      appLogger.info('Using GitHub token from environment variable');
      // Configure with GitHub token - using the safe way with GithubOptions
      const config: any = {
        provider: 'github',
        private: true,
        token,
      };

      autoUpdater.setFeedURL(config);
      return true;
    }

    appLogger.warn('No GitHub token found in environment variables');
    return false;
  } catch (error) {
    appLogger.error('Error configuring GitHub authentication:', error);
    return false;
  }
};

/**
 * Update check result
 */
interface UpdateCheckResult {
  updateAvailable: boolean;
  updateInfo?: UpdateInfo;
  error?: string;
}

/**
 * Update status values for IPC communication
 */
enum UpdateStatus {
  CHECKING = 'checking',
  AVAILABLE = 'available',
  NOT_AVAILABLE = 'not-available',
  DOWNLOADING = 'downloading',
  DOWNLOADED = 'downloaded',
  ERROR = 'error',
}

/**
 * Set up the auto-updater for the application
 * @param mainWindow The main application window
 */
export async function setupAutoUpdater(
  mainWindow: BrowserWindow
): Promise<void> {
  // Skip update in development mode
  if (is.development) {
    console.log('Auto-updater disabled - running in development mode');
    return;
  }

  // Set up GitHub authentication for private repositories
  const authConfigured = setupGitHubAuthentication();
  if (!authConfigured) {
    appLogger.warn(
      'GitHub authentication not configured. Private repositories may not be accessible.'
    );
    // We continue anyway, in case the repo is public or token is configured another way
  }

  // Configure update events
  autoUpdater.on('checking-for-update', () => {
    try {
      sendStatusToWindow(mainWindow, UpdateStatus.CHECKING);
    } catch (error) {
      appLogger.error('Error in checking-for-update event:', error);
    }
  });

  autoUpdater.on('update-available', (info: UpdateInfo) => {
    try {
      sendStatusToWindow(mainWindow, UpdateStatus.AVAILABLE, info);
    } catch (error) {
      appLogger.error('Error in update-available event:', error);
    }
  });

  autoUpdater.on('update-not-available', (info: UpdateInfo) => {
    try {
      sendStatusToWindow(mainWindow, UpdateStatus.NOT_AVAILABLE, info);
    } catch (error) {
      appLogger.error('Error in update-not-available event:', error);
    }
  });

  autoUpdater.on('download-progress', progressObj => {
    try {
      sendStatusToWindow(mainWindow, UpdateStatus.DOWNLOADING, progressObj);
    } catch (error) {
      appLogger.error('Error in download-progress event:', error);
    }
  });

  autoUpdater.on('update-downloaded', (info: UpdateInfo) => {
    try {
      sendStatusToWindow(mainWindow, UpdateStatus.DOWNLOADED, info);
    } catch (error) {
      appLogger.error('Error in update-downloaded event:', error);
    }
  });

  autoUpdater.on('error', error => {
    try {
      sendStatusToWindow(mainWindow, UpdateStatus.ERROR, {
        error: error.toString(),
      });
      appLogger.error('Updater error:', error);
    } catch (err) {
      appLogger.error('Error in error event handler:', err);
    }
  });

  // Set up IPC handlers for update operations
  setupUpdateIpcHandlers(mainWindow);

  // Check for updates automatically on startup
  try {
    console.log('Checking for application updates...');
    await autoUpdater.checkForUpdatesAndNotify();
  } catch (error) {
    appLogger.error('Error checking for updates:', error);
  }
}

/**
 * Send update status to the renderer process
 */
function sendStatusToWindow(
  mainWindow: BrowserWindow,
  status: UpdateStatus,
  data?: any
): void {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send('update-event', { status, data });
  }
}

/**
 * Set up IPC handlers for update-related operations
 */
function setupUpdateIpcHandlers(_mainWindow: BrowserWindow): void {
  // Check for updates manually
  ipcMain.handle(
    'update:check-for-updates',
    async (): Promise<UpdateCheckResult> => {
      try {
        appLogger.info('Manually checking for updates...');
        const checkResult = await autoUpdater.checkForUpdates();

        if (checkResult && checkResult.updateInfo) {
          return {
            updateAvailable:
              checkResult.updateInfo.version !== app.getVersion(),
            updateInfo: checkResult.updateInfo,
          };
        }

        return { updateAvailable: false };
      } catch (error) {
        appLogger.error('Error checking for updates:', error);
        return {
          updateAvailable: false,
          error:
            error instanceof Error
              ? error.message
              : 'Unknown error checking for updates',
        };
      }
    }
  );

  // Download updates
  ipcMain.handle('update:download-update', async () => {
    try {
      appLogger.info('Starting update download...');
      await autoUpdater.downloadUpdate();
      return { success: true };
    } catch (error) {
      appLogger.error('Error downloading update:', error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error downloading update',
      };
    }
  });

  // Install updates
  ipcMain.handle('update:install-update', () => {
    try {
      appLogger.info('Installing update...');
      autoUpdater.quitAndInstall(false, true);
      return { success: true };
    } catch (error) {
      appLogger.error('Error installing update:', error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error installing update',
      };
    }
  });
}
