/**
 * @file datasheet.service.ts
 * @description Service for managing datasheets in the database
 */

import { Database } from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { shell } from 'electron';
import { DatasheetEntry } from '../../../shared/types/datasheet.types';
import { isWebUrl } from '../../utils/url-utils';
import { openSharePointUrl } from '../../utils/browser-opener';
import { defaultURLValidator } from '../../utils/url-validator';
import { validateFilePath } from '../../utils/secure-file-operations';
import { requireValidOrganizationId } from '../../utils/organization-validation';

/**
 * Service for managing datasheets in the database
 */
export class DatasheetService {
  private db: Database;
  // Removed unused _softDeleteManager property

  constructor(db: Database) {
    console.log('[DatasheetService] Constructor called');
    if (!db) {
      throw new Error('Database instance is null - cannot initialize DatasheetService');
    }
    this.db = db;
    // Soft delete manager removed - functionality moved to repository pattern
    console.log('[DatasheetService] Database instance set');
    this.initTable();
    console.log('[DatasheetService] Constructor completed');
  }

  /**
   * Initializes the datasheets table with optimized schema
   */
  private initTable(): void {
    try {
      console.log('[DatasheetService] Creating optimized datasheets table...');
      
      if (!this.db) {
        throw new Error('Database instance is null in initTable');
      }
      
      // Create the optimized datasheets table with full sync support
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS datasheets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          external_id TEXT NOT NULL UNIQUE,
          product_id TEXT NOT NULL, -- UUID foreign key to match products.id
          name TEXT NOT NULL,
          url TEXT NOT NULL,
          file_type TEXT NOT NULL CHECK (file_type IN ('pdf', 'docx', 'xlsx', 'pptx', 'link', 'other')),
          is_external INTEGER NOT NULL DEFAULT 1,
          description TEXT,
          tags TEXT,
          metadata TEXT,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          created_by TEXT,
          updated_by TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          -- Sync support columns
          organization_id TEXT,
          user_id TEXT,
          device_id TEXT,
          deleted_at TEXT,
          sync_version INTEGER NOT NULL DEFAULT 1,
          FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE -- UUID foreign key
        )
      `);
      
      // Create indexes for performance
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_datasheets_product_id ON datasheets(product_id)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_external_id ON datasheets(external_id)', 
        'CREATE INDEX IF NOT EXISTS idx_datasheets_is_active ON datasheets(is_active)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_file_type ON datasheets(file_type)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_created_at ON datasheets(created_at)',
        // Sync support indexes
        'CREATE INDEX IF NOT EXISTS idx_datasheets_organization_id ON datasheets(organization_id)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_user_id ON datasheets(user_id)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_device_id ON datasheets(device_id)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_deleted_at ON datasheets(deleted_at)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_sync_version ON datasheets(sync_version)',
        // Composite indexes for sync operations
        'CREATE INDEX IF NOT EXISTS idx_datasheets_org_active ON datasheets(organization_id) WHERE deleted_at IS NULL',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_org_updated ON datasheets(organization_id, updated_at DESC) WHERE deleted_at IS NULL'
      ];
      
      for (const indexSql of indexes) {
        this.db.exec(indexSql);
      }
      
      // Create view for easy access with product information
      this.db.exec(`
        CREATE VIEW IF NOT EXISTS v_datasheets AS
        SELECT 
          d.external_id AS id,
          p.id AS product_id,
          p.name AS product_name,
          d.name,
          d.url,
          d.file_type,
          d.is_external,
          d.description,
          d.tags,
          d.metadata,
          d.created_at,
          d.updated_at,
          d.created_by,
          d.updated_by
        FROM datasheets d
        JOIN products p ON d.product_id = p.id
        WHERE d.is_active = 1 AND p.is_active = 1
      `);

      // Create trigger for updated_at timestamp and sync_version
      this.db.exec(`
        CREATE TRIGGER IF NOT EXISTS update_datasheets_timestamp 
        AFTER UPDATE ON datasheets
        FOR EACH ROW
        WHEN NEW.updated_at = OLD.updated_at
        BEGIN
          UPDATE datasheets 
          SET updated_at = CURRENT_TIMESTAMP,
              sync_version = OLD.sync_version + 1
          WHERE id = NEW.id;
        END;
      `);

      console.log('[DatasheetService] Optimized datasheets table created successfully');
    } catch (error) {
      console.error('[DatasheetService] Error initializing table:', error);
      throw error;
    }
  }

  /**
   * Convert database row to DatasheetEntry
   */
  private convertToDatasheetEntry(row: any): DatasheetEntry {
    return {
      id: row.external_id || row.id,
      productId: row.product_id,
      name: row.name,
      path: row.url || row.path,
      url: row.url || row.path,
      fileType: row.file_type as DatasheetEntry['fileType'],
      dateAdded: row.created_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at || row.created_at,
      isExternal: Boolean(row.is_external)
    };
  }

  /**
   * Get all datasheets for a product using external ID
   */
  getDatasheetsByProductId(productExternalId: string, organizationId?: string): DatasheetEntry[] {
    try {
      if (!productExternalId || typeof productExternalId !== 'string') {
        console.error('[DatasheetService] Invalid productId provided:', productExternalId);
        return [];
      }

      // After UUID migration, product_id is now a UUID directly, no conversion needed
      let productQuery: string;
      let productParams: any[];
      
      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.getDatasheetsByProductId');
        // Verify product exists and belongs to organization
        productQuery = `
          SELECT id FROM products 
          WHERE id = ? AND organization_id = ? AND is_active = 1
        `;
        productParams = [productExternalId, validatedOrgId];
      } else {
        console.warn('[DatasheetService] getDatasheetsByProductId called without organization context - security risk');
        productQuery = `
          SELECT id FROM products WHERE id = ? AND is_active = 1
        `;
        productParams = [productExternalId];
      }

      const product = this.db.prepare(productQuery).get(...productParams) as { id: string } | undefined;

      if (!product) {
        console.log(`[DatasheetService] Product not found: ${productExternalId}`);
        return [];
      }

      // Query datasheets using UUID product_id directly
      const rows = this.db.prepare(`
        SELECT * FROM datasheets
        WHERE product_id = ? AND is_active = 1
        ORDER BY created_at DESC
      `).all(productExternalId);

      return rows.map(row => this.convertToDatasheetEntry(row));
    } catch (error) {
      console.error(`[DatasheetService] Error getting datasheets for product ${productExternalId}:`, error);
      return [];
    }
  }

  /**
   * Get datasheets by product name
   */
  getDatasheetsByProductName(productName: string): DatasheetEntry[] {
    try {
      if (!productName || typeof productName !== 'string') {
        console.error('[DatasheetService] Invalid productName provided:', productName);
        return [];
      }

      // Try to use the view first, fall back to direct query if view doesn't exist
      let rows;
      try {
        rows = this.db.prepare(`
          SELECT * FROM v_datasheets
          WHERE product_name = ?
          ORDER BY created_at DESC
        `).all(productName);
      } catch (_viewError) {
        console.log('[DatasheetService] View not available, using direct query');
        // Fall back to direct query
        rows = this.db.prepare(`
          SELECT 
            d.external_id AS id,
            p.id AS product_id,
            p.name AS product_name,
            d.name,
            d.url,
            d.file_type,
            d.is_external,
            d.description,
            d.tags,
            d.metadata,
            d.created_at,
            d.updated_at,
            d.created_by,
            d.updated_by
          FROM datasheets d
          JOIN products p ON d.product_id = p.id
          WHERE d.is_active = 1 AND p.is_active = 1
            AND p.name = ?
          ORDER BY d.created_at DESC
        `).all(productName);
      }

      return rows.map(row => this.convertToDatasheetEntry(row));
    } catch (error) {
      console.error(`[DatasheetService] Error getting datasheets for product name ${productName}:`, error);
      return [];
    }
  }

  /**
   * Get a datasheet by ID with organization scoping
   */
  getDatasheetById(datasheetId: string, organizationId?: string): DatasheetEntry | undefined {
    try {
      if (!datasheetId || typeof datasheetId !== 'string') {
        console.error('[DatasheetService] Invalid datasheetId provided:', datasheetId);
        return undefined;
      }

      // SECURITY FIX: Add organization scoping to prevent cross-org data access
      let query: string;
      let params: any[];

      if (organizationId) {
        // Validate organization ID
        const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.getDatasheetById');
        
        // Query with organization scoping via product relationship
        query = `
          SELECT d.* FROM datasheets d
          INNER JOIN products p ON d.product_id = p.id
          WHERE d.external_id = ? AND p.organization_id = ?
        `;
        params = [datasheetId, validatedOrgId];
      } else {
        // Fallback for backward compatibility (should be avoided)
        console.warn('[DatasheetService] getDatasheetById called without organization context - security risk');
        query = `SELECT * FROM datasheets WHERE external_id = ?`;
        params = [datasheetId];
      }

      const row = this.db.prepare(query).get(...params);

      return row ? this.convertToDatasheetEntry(row) : undefined;
    } catch (error) {
      console.error(`[DatasheetService] Error getting datasheet ${datasheetId}:`, error);
      return undefined;
    }
  }

  /**
   * Add a datasheet to a product
   */
  addDatasheet(
    productExternalId: string,
    datasheet: Omit<DatasheetEntry, 'id' | 'dateAdded' | 'createdAt' | 'updatedAt'>,
    userId?: string,
    organizationId?: string
  ): DatasheetEntry | undefined {
    try {
      // Validate inputs
      if (!productExternalId || typeof productExternalId !== 'string') {
        console.error('[DatasheetService] Invalid productId provided:', productExternalId);
        return undefined;
      }

      if (!datasheet || typeof datasheet !== 'object') {
        console.error('[DatasheetService] Invalid datasheet object provided:', datasheet);
        return undefined;
      }

      if (!datasheet.name || typeof datasheet.name !== 'string') {
        console.error('[DatasheetService] Invalid datasheet name provided:', datasheet.name);
        return undefined;
      }

      const url = datasheet.url || datasheet.path;
      if (!url || typeof url !== 'string') {
        console.error('[DatasheetService] Invalid datasheet URL/path provided:', url);
        return undefined;
      }

      if (!datasheet.fileType || typeof datasheet.fileType !== 'string') {
        console.error('[DatasheetService] Invalid datasheet fileType provided:', datasheet.fileType);
        return undefined;
      }

      // After UUID migration, verify product exists and belongs to organization
      let productQuery: string;
      let productParams: any[];
      
      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.addDatasheet');
        productQuery = `
          SELECT id FROM products 
          WHERE id = ? AND organization_id = ? AND is_active = 1
        `;
        productParams = [productExternalId, validatedOrgId];
      } else {
        console.warn('[DatasheetService] addDatasheet called without organization context - security risk');
        productQuery = `
          SELECT id FROM products WHERE id = ? AND is_active = 1
        `;
        productParams = [productExternalId];
      }

      const product = this.db.prepare(productQuery).get(...productParams) as { id: string } | undefined;

      if (!product) {
        console.error(`[DatasheetService] Product not found: ${productExternalId}`);
        return undefined;
      }

      const externalId = uuidv4();
      const now = new Date().toISOString();
      const isExternal = isWebUrl(url);

      // Prepare tags and metadata if provided
      const tags = datasheet.tags ? JSON.stringify(datasheet.tags) : null;
      const metadata = datasheet.metadata ? JSON.stringify(datasheet.metadata) : null;

      const result = this.db.prepare(`
        INSERT INTO datasheets (
          external_id, product_id, name, url, file_type, 
          is_external, description, tags, metadata, created_by
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        externalId,
        productExternalId, // Use UUID directly
        datasheet.name,
        url,
        datasheet.fileType,
        isExternal ? 1 : 0,
        datasheet.description || null,
        tags,
        metadata,
        userId || null
      );

      if (result.changes > 0) {
        return {
          id: externalId,
          productId: productExternalId,
          name: datasheet.name,
          path: url,
          url: url,
          fileType: datasheet.fileType,
          dateAdded: now,
          createdAt: now,
          updatedAt: now,
          createdBy: userId,
          description: datasheet.description,
          isExternal,
          tags: datasheet.tags,
          metadata: datasheet.metadata
        };
      }

      return undefined;
    } catch (error) {
      console.error(`[DatasheetService] Error adding datasheet:`, error);
      return undefined;
    }
  }

  /**
   * Add a SharePoint or web link to a product
   */
  addWebLink(
    productExternalId: string,
    url: string,
    displayName: string,
    description?: string,
    userId?: string,
    organizationId?: string
  ): DatasheetEntry | undefined {
    try {
      // Validate inputs
      if (!productExternalId || typeof productExternalId !== 'string') {
        console.error('[DatasheetService] Invalid productId provided:', productExternalId);
        return undefined;
      }

      if (!url || typeof url !== 'string') {
        console.error('[DatasheetService] Invalid URL provided:', url);
        return undefined;
      }

      if (!displayName || typeof displayName !== 'string') {
        console.error('[DatasheetService] Invalid displayName provided:', displayName);
        return undefined;
      }

      // Validate and sanitize the URL using comprehensive validation
      const urlValidation = defaultURLValidator.validateURL(url);
      
      if (!urlValidation.isValid) {
        console.error('[DatasheetService] URL validation failed:', urlValidation.error);
        return undefined;
      }

      if (urlValidation.warnings && urlValidation.warnings.length > 0) {
        console.warn('[DatasheetService] URL validation warnings:', urlValidation.warnings);
      }

      const normalizedUrl = urlValidation.sanitizedUrl!;

      // Determine file type from URL if possible
      let fileType: DatasheetEntry['fileType'] = 'link';
      const urlLower = normalizedUrl.toLowerCase();
      if (urlLower.includes('.pdf')) {fileType = 'pdf';}
      else if (urlLower.includes('.docx') || urlLower.includes('.doc')) {fileType = 'docx';}
      else if (urlLower.includes('.xlsx') || urlLower.includes('.xls')) {fileType = 'xlsx';}
      else if (urlLower.includes('.pptx') || urlLower.includes('.ppt')) {fileType = 'pptx';}

      // Add metadata for SharePoint URLs
      const isSharePoint = urlLower.includes('sharepoint.com');
      const metadata = isSharePoint ? { source: 'sharepoint', originalUrl: normalizedUrl } : undefined;

      return this.addDatasheet(
        productExternalId,
        {
          productId: productExternalId,
          name: displayName,
          path: normalizedUrl,
          url: normalizedUrl,
          fileType,
          description,
          metadata,
          tags: isSharePoint ? ['sharepoint'] : undefined
        },
        userId,
        organizationId
      );
    } catch (error) {
      console.error(`[DatasheetService] Error adding web link:`, error);
      return undefined;
    }
  }

  /**
   * Remove a datasheet (soft delete)
   */
  removeDatasheet(datasheetExternalId: string, organizationId?: string): boolean {
    try {
      if (!datasheetExternalId || typeof datasheetExternalId !== 'string') {
        console.error('[DatasheetService] Invalid datasheetId provided:', datasheetExternalId);
        return false;
      }

      let query: string;
      let params: any[];

      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.removeDatasheet');
        // Ensure datasheet belongs to the organization through product relationship
        query = `
          UPDATE datasheets 
          SET is_active = 0, updated_at = datetime('now')
          WHERE external_id = ? AND is_active = 1
          AND product_id IN (
            SELECT id FROM products 
            WHERE organization_id = ? AND is_active = 1
          )
        `;
        params = [datasheetExternalId, validatedOrgId];
      } else {
        console.warn('[DatasheetService] removeDatasheet called without organization context - security risk');
        query = `
          UPDATE datasheets 
          SET is_active = 0, updated_at = datetime('now')
          WHERE external_id = ? AND is_active = 1
        `;
        params = [datasheetExternalId];
      }

      const result = this.db.prepare(query).run(...params);
      return result.changes > 0;
    } catch (error) {
      console.error(`[DatasheetService] Error removing datasheet:`, error);
      return false;
    }
  }

  /**
   * Open a datasheet with organization scoping
   */
  async openDatasheet(datasheetId: string, organizationId: string): Promise<{ success: boolean; message?: string }> {
    try {
      // SECURITY FIX: Validate organization ID to prevent cross-org datasheet access
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.openDatasheet');
      
      console.log(`Attempting to open datasheet with ID: ${datasheetId} for organization: ${validatedOrgId}`);

      // Find the datasheet by ID with organization scoping
      const datasheet = this.getDatasheetById(datasheetId, validatedOrgId);

      if (!datasheet) {
        const message = `Datasheet with ID ${datasheetId} not found`;
        console.warn(message);
        return { success: false, message };
      }

      // Open the file with security validation
      try {
        // Check if it's a web link
        if (isWebUrl(datasheet.path)) {
          // Validate URL before opening
          const urlValidation = defaultURLValidator.validateURL(datasheet.path);
          
          if (!urlValidation.isValid) {
            const message = `Invalid URL detected: ${urlValidation.error}`;
            console.error(message);
            return { success: false, message };
          }

          if (urlValidation.warnings && urlValidation.warnings.length > 0) {
            console.warn('[DatasheetService] URL warnings when opening:', urlValidation.warnings);
          }

          // For web links, use our improved URL handler with validated URL
          const success = await openSharePointUrl(urlValidation.sanitizedUrl!);
          if (!success) {
            const message = `Failed to open web link: ${datasheet.path}`;
            console.error(message);
            return { success: false, message };
          }
        } else {
          // For local files, validate path and use shell.openPath
          const pathValidation = validateFilePath(datasheet.path);
          
          if (!pathValidation.isValid) {
            const message = `Invalid file path: ${pathValidation.error}`;
            console.error(message);
            return { success: false, message };
          }

          await shell.openPath(pathValidation.sanitizedPath!);
        }

        console.log(`Successfully opened datasheet: ${datasheet.name} at ${datasheet.path}`);
        return { success: true };
      } catch (err) {
        const message = `Failed to open datasheet: ${err instanceof Error ? err.message : String(err)}`;
        console.error(message);
        return { success: false, message };
      }
    } catch (error) {
      const message = `Error opening datasheet: ${error instanceof Error ? error.message : String(error)}`;
      console.error(message);
      return { success: false, message };
    }
  }

  /**
   * Open all datasheets for a product
   */
  async openDatasheets(productId: string, organizationId: string): Promise<{ success: boolean; count: number; errors: string[] }> {
    let openCount = 0;
    const errors: string[] = [];
    try {
      const datasheets = this.getDatasheetsByProductId(productId);

      if (datasheets.length === 0) {
        console.log(`No datasheets found for product: ${productId}`);
        return { success: true, count: 0, errors: [] };
      }

      console.log(`Attempting to open ${datasheets.length} datasheets for product: ${productId}`);

      // Use Promise.allSettled to handle potential errors for individual files
      const results = await Promise.allSettled(datasheets.map(async (datasheet) => {
        try {
          const result = await this.openDatasheet(datasheet.id, organizationId);
          if (result.success) {
            return { status: 'fulfilled', path: datasheet.path };
          } else {
            throw new Error(result.message || `Failed to open ${datasheet.path}`);
          }
        } catch (err) {
          const message = `Failed to open datasheet ${datasheet.name} at ${datasheet.path}: ${err instanceof Error ? err.message : String(err)}`;
          console.warn(message);
          return { status: 'rejected', reason: message };
        }
      }));

      // Count successes and collect errors
      results.forEach(result => {
        if (result.status === 'fulfilled') {
          openCount++;
        } else {
          errors.push(result.reason);
        }
      });

      return {
        success: openCount > 0,
        count: openCount,
        errors
      };
    } catch (error) {
      console.error(`Error opening datasheets for product ${productId}:`, error);
      return {
        success: false,
        count: openCount,
        errors: [...errors, `General error: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * Count datasheets for a product
   */
  countDatasheets(productId: string, organizationId?: string): number {
    try {
      if (!productId || typeof productId !== 'string') {
        console.error('Invalid productId provided to countDatasheets:', productId);
        return 0;
      }

      // After UUID migration, verify product exists and belongs to organization
      let productQuery: string;
      let productParams: any[];
      
      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.countDatasheets');
        productQuery = `
          SELECT id FROM products 
          WHERE id = ? AND organization_id = ? AND is_active = 1
        `;
        productParams = [productId, validatedOrgId];
      } else {
        console.warn('[DatasheetService] countDatasheets called without organization context - security risk');
        productQuery = `
          SELECT id FROM products WHERE id = ? AND is_active = 1
        `;
        productParams = [productId];
      }

      const product = this.db.prepare(productQuery).get(...productParams) as { id: string } | undefined;

      if (!product) {
        return 0;
      }

      const result = this.db.prepare(`
        SELECT COUNT(*) as count FROM datasheets 
        WHERE product_id = ? AND is_active = 1
      `).get(productId) as { count: number };

      return result?.count || 0;
    } catch (error) {
      console.error(`Error counting datasheets for product ${productId}:`, error);
      return 0;
    }
  }

  /**
   * Get soft deleted datasheets for recovery
   */
  getSoftDeleted(organizationId: string, limit: number = 100, offset: number = 0): DatasheetEntry[] {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.getSoftDeleted');
      
      // Get soft deleted datasheets via product relationship
      const rows = this.db.prepare(`
        SELECT d.* FROM datasheets d
        INNER JOIN products p ON d.product_id = p.id
        WHERE d.is_active = 0 AND p.organization_id = ?
        ORDER BY d.updated_at DESC
        LIMIT ? OFFSET ?
      `).all(validatedOrgId, limit, offset);
      
      return rows.map(row => this.convertToDatasheetEntry(row));
    } catch (error) {
      console.error('[DatasheetService] Error getting soft deleted datasheets:', error);
      return [];
    }
  }

  /**
   * Restore a soft deleted datasheet
   */
  restoreDatasheet(datasheetId: string, organizationId: string, userId?: string): boolean {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.restoreDatasheet');
      
      // Restore using organization scoping via product relationship
      const result = this.db.prepare(`
        UPDATE datasheets 
        SET is_active = 1, updated_at = datetime('now'), restored_by = ?
        WHERE external_id = ? AND is_active = 0
        AND product_id IN (
          SELECT id FROM products 
          WHERE organization_id = ? AND is_active = 1
        )
      `).run(userId || null, datasheetId, validatedOrgId);
      
      console.log(`[DatasheetService] Restored ${result.changes} datasheet(s)`);
      return result.changes > 0;
    } catch (error) {
      console.error('[DatasheetService] Error restoring datasheet:', error);
      return false;
    }
  }

  /**
   * Cleanup old soft deleted datasheets (permanent deletion)
   */
  cleanupOldSoftDeleted(organizationId: string, daysOld: number = 30): { success: boolean; cleaned: number } {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.cleanupOldSoftDeleted');
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      const cutoffTimestamp = cutoffDate.toISOString();
      
      // Permanently delete old soft-deleted datasheets with organization scoping
      const result = this.db.prepare(`
        DELETE FROM datasheets 
        WHERE is_active = 0 AND updated_at < ?
        AND product_id IN (
          SELECT id FROM products WHERE organization_id = ?
        )
      `).run(cutoffTimestamp, validatedOrgId);
      
      console.log(`[DatasheetService] Permanently deleted ${result.changes} old soft-deleted datasheets (older than ${daysOld} days)`);
      
      return {
        success: true,
        cleaned: result.changes
      };
    } catch (error) {
      console.error('[DatasheetService] Error cleaning up old soft deleted datasheets:', error);
      return { success: false, cleaned: 0 };
    }
  }

  /**
   * Add a datasheet to a product - called by IPC handlers
   * This is a wrapper around the existing addDatasheet method
   */
  async addToProduct(
    productId: string,
    filePath: string,
    organizationId: string,
    displayName?: string
  ): Promise<DatasheetEntry & { displayName?: string; fileName?: string }> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.addToProduct');
      
      // Extract file name from path if displayName not provided
      const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || 'Unknown File';
      const finalDisplayName = displayName || fileName;
      
      // Determine file type from extension
      const extension = filePath.split('.').pop()?.toLowerCase();
      let fileType: DatasheetEntry['fileType'] = 'other';
      
      if (extension === 'pdf') fileType = 'pdf';
      else if (extension === 'docx' || extension === 'doc') fileType = 'docx';
      else if (extension === 'xlsx' || extension === 'xls') fileType = 'xlsx';
      else if (extension === 'pptx' || extension === 'ppt') fileType = 'pptx';
      else if (filePath.startsWith('http')) fileType = 'link';
      
      // Use the existing addDatasheet method
      const result = this.addDatasheet(
        productId,
        {
          productId,
          name: finalDisplayName,
          path: filePath,
          url: filePath,
          fileType
        },
        undefined, // userId
        validatedOrgId
      );
      
      if (result) {
        // Return the DatasheetEntry with additional properties expected by IPC handler
        return {
          ...result,
          displayName: finalDisplayName,
          fileName: fileName
        };
      } else {
        // If the result is undefined, throw an error
        throw new Error('Failed to add datasheet to database');
      }
    } catch (error) {
      console.error('[DatasheetService] Error in addToProduct:', error);
      throw error;
    }
  }

  /**
   * Remove a datasheet - called by IPC handlers
   * This is a wrapper around the existing removeDatasheet method
   */
  async remove(datasheetId: string, organizationId: string): Promise<boolean> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.remove');
      
      // Use the existing removeDatasheet method
      return this.removeDatasheet(datasheetId, validatedOrgId);
    } catch (error) {
      console.error('[DatasheetService] Error in remove:', error);
      return false;
    }
  }

  /**
   * Open a datasheet - called by IPC handlers
   * This is a wrapper around the existing openDatasheet method
   */
  async open(datasheetId: string, organizationId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.open');
      
      // Use the existing openDatasheet method
      return await this.openDatasheet(datasheetId, validatedOrgId);
    } catch (error) {
      console.error('[DatasheetService] Error in open:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Open all datasheets for a product - called by IPC handlers
   * This is a wrapper around the existing openDatasheets method
   */
  async openAllForProduct(
    productId: string,
    organizationId: string
  ): Promise<{ success: boolean; opened?: number; failed?: number; total?: number; errors?: string[]; message?: string }> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.openAllForProduct');
      
      // Use the existing openDatasheets method
      const result = await this.openDatasheets(productId, validatedOrgId);
      
      return {
        success: result.success,
        opened: result.count,
        failed: result.errors.length,
        total: result.count + result.errors.length,
        errors: result.errors,
        message: result.success ? `Opened ${result.count} datasheets` : `Failed to open some datasheets`
      };
    } catch (error) {
      console.error('[DatasheetService] Error in openAllForProduct:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : String(error),
        opened: 0,
        failed: 0,
        total: 0,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

  /**
   * Migrate datasheets from old format - called by IPC handlers
   * This is a stub implementation as the migration logic is not yet defined
   */
  async migrateFromOldFormat(
    organizationId: string
  ): Promise<{ success: boolean; migrated?: number; skipped?: number; errors?: string[]; message?: string }> {
    try {
      const validatedOrgId = requireValidOrganizationId(organizationId, 'DatasheetService.migrateFromOldFormat');
      
      console.log(`[DatasheetService] Migration from old format requested for organization: ${validatedOrgId}`);
      
      // TODO: Implement actual migration logic from old datasheet format
      // For now, return a success response indicating no migration was needed
      return {
        success: true,
        migrated: 0,
        skipped: 0,
        errors: [],
        message: 'No datasheets required migration from old format'
      };
    } catch (error) {
      console.error('[DatasheetService] Error in migrateFromOldFormat:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : String(error),
        migrated: 0,
        skipped: 0,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

}
