#!/usr/bin/env node

/**
 * Test script to simulate fresh database initialization and verify product-color sync
 * This will backup the existing database, create a fresh one, and test the sync flow
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get the database path
const dbDir = path.join(
  process.env.HOME || process.env.USERPROFILE,
  process.platform === 'darwin' ? 'Library/Application Support' : '.config',
  'chroma-sync'
);
const dbPath = path.join(dbDir, 'chromasync.db');
const backupPath = path.join(dbDir, 'chromasync.db.backup');

console.log('🧪 Testing Fresh Database Initialization\n');
console.log(`Database path: ${dbPath}`);
console.log(`Backup path: ${backupPath}\n`);

// Function to count product-color relationships
function countProductColors(db) {
  const hasOrgId = db.prepare("PRAGMA table_info(product_colors)").all()
    .some(col => col.name === 'organization_id');
  
  if (hasOrgId) {
    const counts = db.prepare(`
      SELECT o.name, o.external_id, COUNT(*) as count
      FROM organizations o
      LEFT JOIN product_colors pc ON pc.organization_id = o.id
      GROUP BY o.id
    `).all();
    return counts;
  } else {
    const count = db.prepare("SELECT COUNT(*) as count FROM product_colors").get();
    return [{ name: 'All', external_id: 'N/A', count: count.count }];
  }
}

async function testFreshInit() {
  try {
    // Step 1: Backup existing database
    if (fs.existsSync(dbPath)) {
      console.log('📦 Backing up existing database...');
      fs.copyFileSync(dbPath, backupPath);
      console.log('✅ Database backed up\n');
      
      // Check current state
      const db = new Database(dbPath, { readonly: true });
      console.log('Current product-color relationships:');
      const currentCounts = countProductColors(db);
      currentCounts.forEach(row => {
        console.log(`  - ${row.name}: ${row.count} relationships`);
      });
      db.close();
      
      // Remove existing database
      console.log('\n🗑️  Removing existing database...');
      fs.unlinkSync(dbPath);
      console.log('✅ Database removed\n');
    } else {
      console.log('ℹ️  No existing database found\n');
    }

    // Step 2: Prompt to start ChromaSync
    console.log('📋 INSTRUCTIONS:');
    console.log('1. Start ChromaSync now (npm run dev)');
    console.log('2. Sign in with your Google account');
    console.log('3. Wait for sync to complete (check the console logs)');
    console.log('4. Close ChromaSync');
    console.log('5. Press Enter here to check results\n');
    
    console.log('⏳ Waiting for you to complete the steps above...');
    
    // Wait for user input
    await new Promise(resolve => {
      process.stdin.resume();
      process.stdin.once('data', () => {
        process.stdin.pause();
        resolve();
      });
    });

    // Step 3: Check results
    console.log('\n🔍 Checking results...\n');
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ No database found. ChromaSync may not have started correctly.');
      return;
    }

    const newDb = new Database(dbPath, { readonly: true });
    
    // Check tables
    console.log('📊 Database contents:');
    const tables = newDb.prepare(
      "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
    ).all();
    console.log('Tables:', tables.map(t => t.name).join(', '));
    
    // Check organizations
    const orgs = newDb.prepare("SELECT * FROM organizations").all();
    console.log(`\nOrganizations: ${orgs.length}`);
    orgs.forEach(org => {
      console.log(`  - ${org.name} (id: ${org.id}, external_id: ${org.external_id})`);
    });

    // Check colors
    const colorCounts = newDb.prepare(`
      SELECT o.name, COUNT(c.id) as count
      FROM organizations o
      LEFT JOIN colors c ON c.organization_id = o.id
      GROUP BY o.id
    `).all();
    console.log('\nColors per organization:');
    colorCounts.forEach(row => {
      console.log(`  - ${row.name}: ${row.count} colors`);
    });

    // Check products
    const productCounts = newDb.prepare(`
      SELECT o.name, COUNT(p.id) as count
      FROM organizations o
      LEFT JOIN products p ON p.organization_id = o.id
      GROUP BY o.id
    `).all();
    console.log('\nProducts per organization:');
    productCounts.forEach(row => {
      console.log(`  - ${row.name}: ${row.count} products`);
    });

    // Check product-color relationships
    console.log('\nProduct-color relationships:');
    const pcCounts = countProductColors(newDb);
    pcCounts.forEach(row => {
      console.log(`  - ${row.name}: ${row.count} relationships`);
    });

    // Check if migration 016 ran
    try {
      const migrations = newDb.prepare(
        "SELECT version FROM schema_migrations WHERE version = 16"
      ).all();
      console.log(`\nMigration 016 (add org_id to product_colors): ${
        migrations.length > 0 ? '✅ Applied' : '❌ NOT Applied'
      }`);
    } catch (e) {
      console.log('\nNo schema_migrations table found');
    }

    newDb.close();

    // Summary
    console.log('\n📋 SUMMARY:');
    const totalPC = pcCounts.reduce((sum, row) => sum + row.count, 0);
    if (totalPC === 0) {
      console.log('❌ Product-color relationships were NOT synced automatically');
      console.log('   This needs to be fixed in the initialization flow');
    } else {
      console.log(`✅ Product-color relationships were synced successfully (${totalPC} total)`);
    }

    // Offer to restore backup
    console.log('\n🔄 Would you like to restore the backup? (y/n)');
    
    await new Promise(resolve => {
      process.stdin.resume();
      process.stdin.once('data', (data) => {
        const answer = data.toString().trim().toLowerCase();
        if (answer === 'y' || answer === 'yes') {
          console.log('Restoring backup...');
          if (fs.existsSync(dbPath)) {
            fs.unlinkSync(dbPath);
          }
          fs.copyFileSync(backupPath, dbPath);
          console.log('✅ Backup restored');
        } else {
          console.log('Keeping new database');
        }
        process.stdin.pause();
        resolve();
      });
    });

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the test
console.log('Press Enter to start the test...');
process.stdin.resume();
process.stdin.once('data', () => {
  process.stdin.pause();
  testFreshInit().then(() => process.exit(0));
});