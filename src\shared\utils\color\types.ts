// Color type definitions used across the color utilities

export interface RGB {
  r: number;
  g: number;
  b: number;
}

export interface RGBA extends RGB {
  a: number;
}

export interface HSL {
  h: number;
  s: number;
  l: number;
}

export interface HSLA extends HSL {
  a: number;
}

export interface CMYK {
  c: number;
  m: number;
  y: number;
  k: number;
}

export interface LAB {
  l: number;
  a: number;
  b: number;
}

export interface XYZ {
  x: number;
  y: number;
  z: number;
}

export interface ColorSpaces {
  hex: string;
  rgb: RGB;
  hsl: HSL;
  cmyk: CMYK;
  lab?: LAB;
}

export interface WCAGCompliance {
  aa: {
    normal: boolean;
    large: boolean;
  };
  aaa: {
    normal: boolean;
    large: boolean;
  };
}

export interface ContrastResult {
  ratio: number;
  wcag: WCAGCompliance;
}

export interface DeltaEResult {
  value: number;
  interpretation: string;
  description: string;
}

export interface APCAResult {
  contrast: number;
  level: 'Bronze' | 'Silver' | 'Gold' | 'Fail';
  passes: boolean;
  description: string;
  minFontWeight?: number;
  minFontSize?: number;
}

export type ColorFormat = 'hex' | 'rgb' | 'hsl' | 'cmyk' | 'lab';
