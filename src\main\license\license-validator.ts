import * as os from 'os';
import * as crypto from 'crypto';
import ElectronStore from '../utils/electron-store-patched';

// Store for license validation config
const configStore = new ElectronStore({ name: 'license-config' });

/**
 * Gets a unique device identifier
 * @returns Device ID string
 */
export function getDeviceId(): string {
  const hostname = os.hostname();
  const platform = os.platform();
  const cpus = os.cpus();
  const cpuModel = cpus[0]?.model || 'unknown';

  // Combine system info to create a unique identifier
  const deviceString = `${hostname}-${platform}-${cpuModel}`;
  return crypto.createHash('sha256').update(deviceString).digest('hex');
}

/**
 * Generates a license key based on device ID
 * @param deviceId The device identifier
 * @returns License key string
 */
function generateLicenseKey(deviceId: string): string {
  const secret = 'ChromaSync-2024-Secret-Key'; // In production, this should be in env
  const hash = crypto
    .createHmac('sha256', secret)
    .update(deviceId)
    .digest('hex');

  // Format as XXXX-XXXX-XXXX-XXXX
  return hash.substr(0, 16).toUpperCase().match(/.{4}/g)?.join('-') || '';
}

/**
 * Validates the license key
 * @returns Promise<boolean> True if license is valid
 *
 * Note: This is a temporary stub implementation after Supabase removal.
 * TODO: Implement new license validation system
 */
export async function validateLicense(): Promise<boolean> {
  try {
    const deviceId = getDeviceId();
    // Generate license key based on device ID
    const licenseKey = `CS-${deviceId.slice(0, 8).toUpperCase()}`;

    // For now, always return true to allow the app to run
    // TODO: Implement proper license validation without Supabase
    console.log(
      '[License] Temporary stub - auto-approving license for device:',
      deviceId
    );
    console.log('[License] Generated license key:', licenseKey);

    // Update last successful check timestamp
    configStore.set('lastLicenseCheck', Date.now());

    return true;
  } catch (error) {
    console.error('[License] Error in license validation:', error);
    // Allow app to run even if license check fails
    return true;
  }
}

/**
 * Checks if the device has a valid license
 * Includes offline grace period
 * @returns Promise<boolean>
 */
export async function hasValidLicense(): Promise<boolean> {
  try {
    // First check if we're in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('[License] Development mode - skipping license check');
      return true;
    }

    // Check if we have a recent successful license check
    const lastCheck = configStore.get('lastLicenseCheck', 0) as number;
    const gracePeriodDays = 30; // Allow 30 days offline
    const gracePeriodMs = gracePeriodDays * 24 * 60 * 60 * 1000;

    if (Date.now() - lastCheck < gracePeriodMs) {
      console.log(
        '[License] Within grace period - using cached license status'
      );
      return true;
    }

    // Validate license
    return await validateLicense();
  } catch (error) {
    console.error('[License] Error checking license:', error);

    // Check grace period even on error
    const lastCheck = configStore.get('lastLicenseCheck', 0) as number;
    const gracePeriodMs = 30 * 24 * 60 * 60 * 1000;

    if (Date.now() - lastCheck < gracePeriodMs) {
      console.log('[License] Error but within grace period - allowing access');
      return true;
    }

    return false;
  }
}

/**
 * Gets the current license information
 * @returns License info object
 */
export async function getLicenseInfo(): Promise<{
  isValid: boolean;
  deviceId: string;
  _licenseKey: string;
  lastCheck: Date | null;
  gracePeriodRemaining: number;
}> {
  const deviceId = getDeviceId();
  const licenseKey = `CS-${deviceId.slice(0, 8).toUpperCase()}`;

  const lastCheck = configStore.get('lastLicenseCheck', 0) as number;
  const gracePeriodMs = 30 * 24 * 60 * 60 * 1000;
  const gracePeriodRemaining = Math.max(
    0,
    gracePeriodMs - (Date.now() - lastCheck)
  );

  return {
    isValid: await hasValidLicense(),
    deviceId,
    _licenseKey: licenseKey,
    lastCheck: lastCheck ? new Date(lastCheck) : null,
    gracePeriodRemaining: Math.floor(
      gracePeriodRemaining / (24 * 60 * 60 * 1000)
    ), // Days
  };
}

/**
 * Clears the license cache (for testing)
 */
export function clearLicenseCache(): void {
  configStore.delete('lastLicenseCheck');
  console.log('[License] License cache cleared');
}
/**
 * Activates the device with a license
 * @param licenseKey The license key to activate
 * @returns Promise<boolean> True if activation successful
 *
 * Note: This is a temporary stub implementation after Supabase removal.
 * TODO: Implement new activation system
 */
export async function activateDevice(_licenseKey?: string): Promise<boolean> {
  try {
    console.log('[License] Temporary stub - auto-activating device');
    configStore.set('lastLicenseCheck', Date.now());
    return true;
  } catch (error) {
    console.error('[License] Error activating device:', error);
    return false;
  }
}

/**
 * Deactivates the current device
 * @returns Promise<boolean> True if deactivation successful
 *
 * Note: This is a temporary stub implementation after Supabase removal.
 * TODO: Implement new deactivation system
 */
export async function deactivateDevice(): Promise<boolean> {
  try {
    console.log('[License] Temporary stub - deactivating device');
    configStore.delete('lastLicenseCheck');
    return true;
  } catch (error) {
    console.error('[License] Error deactivating device:', error);
    return false;
  }
}

/**
 * Checks if the device is activated
 * @returns Promise<boolean> True if device is activated
 */
export async function isDeviceActivated(): Promise<boolean> {
  return await hasValidLicense();
}

/**
 * Gets the device's license key
 * @returns Promise<string> The device's license key
 */
export async function getDeviceLicenseKey(): Promise<string> {
  const deviceId = getDeviceId();
  return generateLicenseKey(deviceId);
}
