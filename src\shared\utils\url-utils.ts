/**
 * @file url-utils.ts
 * @description Utility functions for URL handling
 */

/**
 * Determines if a path is a web URL
 * @param path The path to check
 * @returns True if the path is a web URL, false if path is undefined or not a web URL
 */
export function isWebUrl(path?: string): boolean {
  if (!path) {
    return false;
  }

  return (
    path.startsWith('http://') ||
    path.startsWith('https://') ||
    path.startsWith('www.') ||
    path.includes('sharepoint.com') ||
    path.includes('onedrive.com')
  );
}
