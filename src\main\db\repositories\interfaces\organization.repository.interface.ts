/**
 * @file organization.repository.interface.ts
 * @description Interface for OrganizationRepository data access layer
 *
 * Defines the contract for all database operations related to organizations,
 * members, invitations, and user profiles. This interface separates data
 * access concerns from business logic.
 */

import Database from 'better-sqlite3';
// TODO: Re-enable when needed
// import { Organization, OrganizationMember, OrganizationInvite } from '../../../../shared/types/organization.types';

export interface IOrganizationRepository {
  // Organization CRUD Operations
  findAll(): OrganizationRow[];
  findById(organizationId: string): OrganizationRow | null;
  findByExternalId(externalId: string): OrganizationRow | null;
  insert(orgData: CreateOrganizationData): string;
  update(organizationId: string, updates: UpdateOrganizationData): boolean;
  delete(organizationId: string): boolean;
  findBySlug(slug: string): OrganizationRow | null;

  // Organization Query Operations
  findForUser(userId: string): OrganizationWithRoleRow[];
  checkUserMembership(organizationId: string, userId: string): boolean;
  getUserRole(organizationId: string, userId: string): string | null;
  generateUniqueSlug(baseSlug: string): string;

  // Member Management Operations
  findMembers(organizationId: string): OrganizationMemberRow[];
  findMember(
    organizationId: string,
    userId: string
  ): OrganizationMemberRow | null;
  insertMember(
    organizationId: string,
    userId: string,
    role: string,
    invitedBy?: string
  ): boolean;
  updateMemberRole(
    organizationId: string,
    userId: string,
    role: string
  ): boolean;
  removeMember(organizationId: string, userId: string): boolean;

  // Invitation Management Operations
  findInvitation(token: string): InvitationRow | null;
  findPendingInvitations(organizationId: string): InvitationRow[];
  insertInvitation(invitationData: CreateInvitationData): string;
  acceptInvitation(invitationId: string): boolean;
  revokeInvitation(organizationId: string, invitationId: string): boolean;
  checkExistingInvitation(
    organizationId: string,
    email: string
  ): InvitationRow | null;
  deleteInvitation(organizationId: string, email: string): boolean;

  // User Profile Operations
  findUser(userId: string): UserRow | null;
  insertUser(userData: CreateUserData): boolean;
  updateUser(userId: string, userData: UpdateUserData): boolean;
  syncUserProfile(userData: CreateUserData): boolean;
  cleanupPlaceholderEmails(userId: string, realEmail: string): boolean;

  // Bulk Operations
  deleteOrganizationCascade(
    organizationId: string,
    forceCascade: boolean
  ): CascadeDeleteResult;
  getOrganizationDataCounts(organizationId: string): OrganizationDataCounts;

  // Utility Operations
  getPreparedStatement(sql: string): Database.Statement;
}

/**
 * Database row interfaces
 */
export interface OrganizationRow {
  id: string; // UUID primary key (was external_id)
  external_id: string; // Compatibility field (same as id)
  name: string;
  slug: string;
  plan: string;
  settings: string;
  created_at: string;
  updated_at: string;
  member_count?: number;
}

export interface OrganizationWithRoleRow extends OrganizationRow {
  user_role: string;
}

export interface OrganizationMemberRow {
  organization_id: string; // UUID foreign key
  user_id: string;
  role: string;
  joined_at: string;
  invited_by: string | null;
  user_email: string | null;
  user_name: string | null;
  user_display_name: string | null;
}

export interface InvitationRow {
  id: string; // UUID primary key (was external_id)
  external_id: string; // Compatibility field (same as id)
  organization_id: string; // UUID foreign key
  email: string;
  role: string;
  token: string;
  invited_by: string;
  expires_at: string;
  accepted_at: string | null;
  created_at: string;
}

export interface UserRow {
  id: string;
  email: string;
  name: string | null;
  display_name: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Data transfer objects for operations
 */
export interface CreateOrganizationData {
  id: string; // UUID primary key
  name: string;
  slug: string;
  plan: string;
  settings: string;
  ownerId: string;
}

export interface UpdateOrganizationData {
  name?: string;
  plan?: string;
  settings?: string;
}

export interface CreateInvitationData {
  id: string; // UUID primary key
  organization_id: string;
  email: string;
  role: string;
  invited_by: string;
  token: string;
  expires_at: string;
}

export interface CreateUserData {
  id: string;
  email: string;
  name?: string;
  display_name?: string;
}

export interface UpdateUserData {
  email?: string;
  name?: string;
  display_name?: string;
}

export interface CascadeDeleteResult {
  success: boolean;
  deletedData: {
    colors: number;
    products: number;
    datasheets: number;
    relationships: number;
    members: number;
  };
}

export interface OrganizationDataCounts {
  colors: number;
  products: number;
  datasheets: number;
  relationships: number;
}
