/**
 * @file organization-context-validator.ts
 * @description Shared organization context validation utilities for consistent store behavior
 */

import type { Result } from '../../shared/types/result.types';

/**
 * Organization context validation result
 */
export interface OrganizationValidationResult {
  success: boolean;
  organizationId?: string;
  error?: string;
  needsRetry?: boolean;
}

/**
 * Organization context configuration
 */
export interface OrganizationContextConfig {
  retryAttempts?: number;
  retryDelay?: number;
  timeout?: number;
  requiresBackendSync?: boolean;
}

/**
 * Default configuration for organization context validation
 */
const DEFAULT_CONFIG: Required<OrganizationContextConfig> = {
  retryAttempts: 3,
  retryDelay: 200,
  timeout: 5000,
  requiresBackendSync: true,
};

/**
 * Get current organization from the organization store
 * @returns Organization ID or null if not available
 */
async function getCurrentOrganizationId(): Promise<string | null> {
  try {
    // Dynamic import to avoid circular dependencies
    const { useOrganizationStore } = await import(
      '../store/organization.store'
    );
    const orgStore = useOrganizationStore.getState();
    return orgStore.currentOrganization?.external_id || null;
  } catch (error) {
    console.error('[OrgValidator] Failed to get current organization:', error);
    return null;
  }
}

/**
 * Verify organization context with backend
 * @param organizationId - Organization ID to verify
 * @returns Whether backend context matches
 */
async function verifyBackendOrganizationContext(
  organizationId: string
): Promise<boolean> {
  try {
    if (typeof window === 'undefined' || !window.organizationAPI) {
      console.warn('[OrgValidator] organizationAPI not available');
      return false;
    }

    const backendResult = await window.organizationAPI.getCurrentOrganization();

    if (!backendResult.success) {
      console.warn(
        '[OrgValidator] Backend organization query failed:',
        backendResult.error
      );
      return false;
    }

    const backendOrgId = backendResult.data?.external_id;
    const matches = backendOrgId === organizationId;

    if (!matches) {
      console.warn('[OrgValidator] Backend organization mismatch:', {
        expected: organizationId,
        backend: backendOrgId,
      });
    }

    return matches;
  } catch (error) {
    console.error(
      '[OrgValidator] Error verifying backend organization context:',
      error
    );
    return false;
  }
}

/**
 * Synchronize organization context with backend
 * @param organizationId - Organization ID to set in backend
 * @returns Whether synchronization succeeded
 */
async function synchronizeOrganizationContext(
  organizationId: string
): Promise<boolean> {
  try {
    if (typeof window === 'undefined' || !window.organizationAPI) {
      console.warn('[OrgValidator] organizationAPI not available for sync');
      return false;
    }

    console.log(
      '[OrgValidator] Synchronizing backend organization context:',
      organizationId
    );

    await window.organizationAPI.setCurrentOrganization(organizationId);

    // Wait for context to propagate
    await new Promise(resolve => setTimeout(resolve, 100));

    // Verify synchronization worked
    return await verifyBackendOrganizationContext(organizationId);
  } catch (error) {
    console.error(
      '[OrgValidator] Error synchronizing organization context:',
      error
    );
    return false;
  }
}

/**
 * Validate organization context with retry logic
 * @param config - Validation configuration
 * @returns Validation result
 */
export async function validateOrganizationContext(
  config: OrganizationContextConfig = {}
): Promise<OrganizationValidationResult> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  let lastError: string | undefined;

  for (let attempt = 1; attempt <= finalConfig.retryAttempts; attempt++) {
    try {
      console.log(
        `[OrgValidator] Validation attempt ${attempt}/${finalConfig.retryAttempts}`
      );

      // Step 1: Get organization ID from frontend store
      const organizationId = await getCurrentOrganizationId();

      if (!organizationId) {
        lastError = 'No organization selected in frontend store';

        if (attempt < finalConfig.retryAttempts) {
          console.log(
            `[OrgValidator] No organization found, retrying in ${finalConfig.retryDelay}ms...`
          );
          await new Promise(resolve =>
            setTimeout(resolve, finalConfig.retryDelay)
          );
          continue;
        }

        return {
          success: false,
          error: lastError,
          needsRetry: true,
        };
      }

      // Step 2: Verify backend context if required
      if (finalConfig.requiresBackendSync) {
        const backendMatches =
          await verifyBackendOrganizationContext(organizationId);

        if (!backendMatches) {
          console.log(
            `[OrgValidator] Backend context mismatch, attempting synchronization...`
          );

          const syncSuccess =
            await synchronizeOrganizationContext(organizationId);

          if (!syncSuccess) {
            lastError = 'Failed to synchronize backend organization context';

            if (attempt < finalConfig.retryAttempts) {
              console.log(
                `[OrgValidator] Sync failed, retrying in ${finalConfig.retryDelay}ms...`
              );
              await new Promise(resolve =>
                setTimeout(resolve, finalConfig.retryDelay)
              );
              continue;
            }

            return {
              success: false,
              error: lastError,
              needsRetry: true,
            };
          }
        }
      }

      // Success!
      console.log(
        `[OrgValidator] ✅ Organization context validated:`,
        organizationId
      );
      return {
        success: true,
        organizationId,
      };
    } catch (error) {
      lastError = error instanceof Error ? error.message : String(error);
      console.error(
        `[OrgValidator] Validation attempt ${attempt} failed:`,
        error
      );

      if (attempt < finalConfig.retryAttempts) {
        await new Promise(resolve =>
          setTimeout(resolve, finalConfig.retryDelay)
        );
      }
    }
  }

  return {
    success: false,
    error: lastError || 'Organization context validation failed',
    needsRetry: false,
  };
}

/**
 * Simple organization context check without retries
 * @returns Organization ID or null
 */
export async function getValidatedOrganizationId(): Promise<string | null> {
  const result = await validateOrganizationContext({ retryAttempts: 1 });
  return result.success ? result.organizationId! : null;
}

/**
 * Check if organization context is available synchronously
 * @returns Whether organization context appears to be available
 */
export function hasOrganizationContext(): boolean {
  try {
    if (typeof window === 'undefined') {return false;}

    // Check if organization APIs are available
    return !!(
      window.organizationAPI &&
      typeof window.organizationAPI.getCurrentOrganization === 'function'
    );
  } catch {
    return false;
  }
}

/**
 * Wait for organization context to become available
 * @param timeout - Maximum time to wait in milliseconds
 * @returns Whether organization context became available
 */
export async function waitForOrganizationContext(
  timeout = 5000
): Promise<boolean> {
  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    if (hasOrganizationContext()) {
      const orgId = await getCurrentOrganizationId();
      if (orgId) {
        return true;
      }
    }

    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return false;
}

/**
 * Result-based organization validation that doesn't throw
 */
export async function safeValidateOrganizationContext(
  config?: OrganizationContextConfig
): Promise<Result<string, Error>> {
  try {
    const result = await validateOrganizationContext(config);

    if (result.success && result.organizationId) {
      return { success: true, data: result.organizationId };
    } else {
      return {
        success: false,
        error: new Error(result.error || 'Organization validation failed'),
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
    };
  }
}
