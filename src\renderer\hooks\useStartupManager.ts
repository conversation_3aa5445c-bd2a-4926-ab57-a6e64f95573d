/**
 * @file useStartupManager.ts
 * @description Hook to manage application startup sequence and performance
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { preloadCriticalComponents } from '../utils/lazyComponents';
import { useColorStore } from '../store/color.store';
import { useProductStore } from '../store/product.store';
import StartupCacheService from '../services/startup-cache.service';

export type StartupStage =
  | 'initializing'
  | 'loading-database'
  | 'loading-ui'
  | 'preloading-components'
  | 'ready';

interface StartupMetrics {
  startTime: number;
  stageTimings: Record<StartupStage, number>;
  totalTime: number;
}

export const useStartupManager = () => {
  const [stage, setStage] = useState<StartupStage>('initializing');
  const [progress, setProgress] = useState(0);
  const [isReady, setIsReady] = useState(false);
  const [metrics, setMetrics] = useState<StartupMetrics | null>(null);

  const startTimeRef = useRef<number>(Date.now());
  const stageTimingsRef = useRef<Record<StartupStage, number>>({} as any);
  const hasInitializedRef = useRef<boolean>(false);

  // Store actions
  const initializeColors = useColorStore(state => state.loadColorsWithUsage);
  const initializeProducts = useProductStore(
    state => state.fetchProductsWithColors
  );

  const moveToStage = useCallback(
    (newStage: StartupStage, progressValue: number) => {
      const now = Date.now();

      setStage(prevStage => {
        // Record timing for the previous stage
        stageTimingsRef.current[prevStage] = now - startTimeRef.current;
        return newStage;
      });

      setProgress(progressValue);

      console.log(`[Startup] Stage: ${newStage} (${progressValue}%)`);
    },
    []
  );

  const initializeApp = useCallback(async () => {
    // Prevent multiple initializations
    if (hasInitializedRef.current) {
      console.log('[Startup] Already initialized, skipping...');
      return;
    }
    hasInitializedRef.current = true;

    try {
      // Stage 1: Initialize core systems (0-25%)
      moveToStage('initializing', 10);

      // Perform basic initialization checks
      await new Promise(resolve => setTimeout(resolve, 50)); // Faster startup

      // Stage 2: Quick component preparation (25-60%)
      moveToStage('loading-ui', 25);
      setProgress(50);

      // Stage 3: Preload critical components (60-85%)
      moveToStage('preloading-components', 60);

      // Preload critical components in background
      try {
        await preloadCriticalComponents();
      } catch (error) {
        console.warn(
          '[Startup] Component preloading failed, continuing:',
          error
        );
      }
      setProgress(85);

      // Stage 4: Ready (85-100%)
      moveToStage('ready', 95);

      // Calculate final metrics
      const totalTime = Date.now() - startTimeRef.current;
      setMetrics({
        startTime: startTimeRef.current,
        stageTimings: { ...stageTimingsRef.current },
        totalTime,
      });

      // Minimal delay for smooth transition
      await new Promise(resolve => setTimeout(resolve, 100));
      setProgress(100);
      setIsReady(true);

      console.log(`[Startup] Startup manager ready in ${totalTime}ms`);
    } catch (error) {
      console.error('[Startup] Initialization failed:', error);
      // Always proceed to ensure app doesn't hang
      setIsReady(true);
    }
  }, []);

  // Health check system with caching
  const performHealthCheck = useCallback(async () => {
    // Skip health check if we have a valid cache
    if (StartupCacheService.isHealthCheckCacheValid()) {
      console.log('[Startup] ⚡ Skipping health check - using cached result');
      return true;
    }

    try {
      console.log('[Startup] 🔍 Performing health check (no valid cache)...');
      const pingResult = (await window.ipc?.invoke('ping')) as {
        success?: boolean;
      };
      if (!pingResult?.success) {
        console.warn('[Startup] Health check failed - IPC not responding');
        return false;
      }

      // Test database connectivity
      try {
        await window.ipc?.invoke('color:getAll');

        // Cache successful health check
        StartupCacheService.recordSuccessfulHealthCheck();
        console.log('[Startup] ✅ Health check passed and cached');
        return true;
      } catch (err) {
        console.warn('[Startup] Database health check failed:', err);
        return false;
      }
    } catch (err) {
      console.warn('[Startup] Health check error:', err);
      return false;
    }
  }, []);

  // Background optimization
  const performBackgroundOptimization = useCallback(async () => {
    if (!isReady) {
      return;
    }

    // Optimize after startup is complete
    setTimeout(async () => {
      try {
        // Preload remaining components
        await Promise.allSettled([
          import('../components/Products/ProductsPanel'),
          import('../components/ColorComparison/ColorComparisonModal'),
        ]);

        console.log('[Startup] Background optimization complete');
      } catch (err) {
        console.warn('[Startup] Background optimization failed:', err);
      }
    }, 2000); // Wait 2 seconds after app is ready
  }, [isReady]);

  // Initialize on mount
  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  // Run background optimization when ready
  useEffect(() => {
    performBackgroundOptimization();
  }, [performBackgroundOptimization]);

  return {
    stage,
    progress,
    isReady,
    metrics,
    performHealthCheck,
    // Utility functions for debugging
    getStartupTime: () => metrics?.totalTime || 0,
    getStageTimings: () => metrics?.stageTimings || {},
    retryInitialization: initializeApp,
  };
};
