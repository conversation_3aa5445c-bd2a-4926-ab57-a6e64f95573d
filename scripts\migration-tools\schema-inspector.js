/**
 * @file schema-inspector.js
 * @description Database schema inspector for UUID migration planning
 * 
 * This tool analyzes the current database schema to understand:
 * - Current primary key types (integer vs UUID)
 * - Foreign key relationships 
 * - Table dependencies for migration planning
 * - Data types and constraints
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

function getUserDataPath() {
  return path.join(require('os').homedir(), 'Library', 'Application Support', 'chroma-sync');
}

function getDbPath() {
  return path.join(getUserDataPath(), 'chromasync.db');
}

class SchemaInspector {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.db = null;
  }

  connect() {
    try {
      this.db = new Database(this.dbPath);
      console.log(`✅ Connected to database: ${this.dbPath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to connect to database: ${error.message}`);
      return false;
    }
  }

  disconnect() {
    if (this.db && this.db.open) {
      this.db.close();
      console.log('📴 Database connection closed');
    }
  }

  /**
   * Get all table names in the database
   */
  getTables() {
    const query = `
      SELECT name 
      FROM sqlite_master 
      WHERE type='table' 
      AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `;
    
    return this.db.prepare(query).all().map(row => row.name);
  }

  /**
   * Get detailed schema information for a table
   */
  getTableSchema(tableName) {
    const query = `PRAGMA table_info(${tableName})`;
    const columns = this.db.prepare(query).all();
    
    return {
      name: tableName,
      columns: columns.map(col => ({
        name: col.name,
        type: col.type,
        nullable: !col.notnull,
        defaultValue: col.dflt_value,
        primaryKey: !!col.pk
      }))
    };
  }

  /**
   * Get foreign key relationships for a table
   */
  getForeignKeys(tableName) {
    const query = `PRAGMA foreign_key_list(${tableName})`;
    return this.db.prepare(query).all().map(fk => ({
      from: fk.from,
      table: fk.table,
      to: fk.to,
      onUpdate: fk.on_update,
      onDelete: fk.on_delete
    }));
  }

  /**
   * Get indexes for a table
   */
  getIndexes(tableName) {
    const query = `PRAGMA index_list(${tableName})`;
    return this.db.prepare(query).all().map(idx => ({
      name: idx.name,
      unique: !!idx.unique,
      partial: !!idx.partial
    }));
  }

  /**
   * Analyze current ID patterns in the database
   */
  analyzeIdPatterns() {
    const tables = this.getTables();
    const analysis = {
      tablesWithIntegerPK: [],
      tablesWithTextPK: [],
      tablesWithDualId: [],
      foreignKeyPatterns: [],
      recommendedMigrations: []
    };

    tables.forEach(tableName => {
      const schema = this.getTableSchema(tableName);
      const foreignKeys = this.getForeignKeys(tableName);
      
      // Find primary keys
      const primaryKeys = schema.columns.filter(col => col.primaryKey);
      const integerPKs = primaryKeys.filter(col => col.type.toUpperCase().includes('INTEGER'));
      const textPKs = primaryKeys.filter(col => col.type.toUpperCase().includes('TEXT'));
      
      // Check for dual ID pattern (integer id + text external_id)
      const hasId = schema.columns.find(col => col.name === 'id' && col.primaryKey);
      const hasExternalId = schema.columns.find(col => col.name === 'external_id');
      
      if (integerPKs.length > 0) {
        analysis.tablesWithIntegerPK.push({
          table: tableName,
          primaryKeys: integerPKs.map(pk => pk.name)
        });
      }
      
      if (textPKs.length > 0) {
        analysis.tablesWithTextPK.push({
          table: tableName,
          primaryKeys: textPKs.map(pk => pk.name)
        });
      }
      
      if (hasId && hasExternalId) {
        analysis.tablesWithDualId.push({
          table: tableName,
          integerPK: hasId.name,
          uuidField: hasExternalId.name
        });
      }
      
      // Analyze foreign key patterns
      foreignKeys.forEach(fk => {
        analysis.foreignKeyPatterns.push({
          fromTable: tableName,
          fromColumn: fk.from,
          toTable: fk.table,
          toColumn: fk.to
        });
      });
    });

    // Generate recommendations
    analysis.tablesWithDualId.forEach(table => {
      analysis.recommendedMigrations.push({
        table: table.table,
        action: 'Convert to pure UUID',
        description: `Remove integer '${table.integerPK}', make '${table.uuidField}' the primary key`,
        priority: 'HIGH'
      });
    });

    analysis.tablesWithIntegerPK.forEach(table => {
      if (!analysis.tablesWithDualId.find(dual => dual.table === table.table)) {
        analysis.recommendedMigrations.push({
          table: table.table,
          action: 'Add UUID primary key',
          description: `Add UUID primary key, migrate foreign key references`,
          priority: 'MEDIUM'
        });
      }
    });

    return analysis;
  }

  /**
   * Get row counts for all tables
   */
  getRowCounts() {
    const tables = this.getTables();
    const counts = {};
    
    tables.forEach(tableName => {
      try {
        const count = this.db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get();
        counts[tableName] = count.count;
      } catch (error) {
        counts[tableName] = `Error: ${error.message}`;
      }
    });
    
    return counts;
  }

  /**
   * Generate comprehensive schema report
   */
  generateReport() {
    console.log('\n🔍 ChromaSync Database Schema Analysis');
    console.log('=====================================\n');
    
    // Basic database info
    const tables = this.getTables();
    console.log(`📊 Database: ${this.dbPath}`);
    console.log(`📋 Total Tables: ${tables.length}\n`);
    
    // Row counts
    console.log('📈 Table Row Counts:');
    const rowCounts = this.getRowCounts();
    Object.entries(rowCounts).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} rows`);
    });
    console.log();
    
    // ID pattern analysis
    const analysis = this.analyzeIdPatterns();
    
    console.log('🔑 Primary Key Analysis:');
    console.log(`   Tables with Integer PKs: ${analysis.tablesWithIntegerPK.length}`);
    analysis.tablesWithIntegerPK.forEach(table => {
      console.log(`     - ${table.table} (${table.primaryKeys.join(', ')})`);
    });
    
    console.log(`   Tables with Text PKs: ${analysis.tablesWithTextPK.length}`);
    analysis.tablesWithTextPK.forEach(table => {
      console.log(`     - ${table.table} (${table.primaryKeys.join(', ')})`);
    });
    
    console.log(`   Tables with Dual IDs: ${analysis.tablesWithDualId.length}`);
    analysis.tablesWithDualId.forEach(table => {
      console.log(`     - ${table.table}: ${table.integerPK} + ${table.uuidField}`);
    });
    console.log();
    
    // Foreign key relationships
    console.log('🔗 Foreign Key Relationships:');
    if (analysis.foreignKeyPatterns.length === 0) {
      console.log('   No foreign key constraints found');
    } else {
      analysis.foreignKeyPatterns.forEach(fk => {
        console.log(`   ${fk.fromTable}.${fk.fromColumn} → ${fk.toTable}.${fk.toColumn}`);
      });
    }
    console.log();
    
    // Migration recommendations
    console.log('📋 Migration Recommendations:');
    if (analysis.recommendedMigrations.length === 0) {
      console.log('   No migrations needed - already using pure UUID system!');
    } else {
      analysis.recommendedMigrations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec.table} [${rec.priority}]`);
        console.log(`      Action: ${rec.action}`);
        console.log(`      Details: ${rec.description}`);
      });
    }
    console.log();
    
    // Detailed table schemas
    console.log('📋 Detailed Table Schemas:');
    tables.forEach(tableName => {
      const schema = this.getTableSchema(tableName);
      const foreignKeys = this.getForeignKeys(tableName);
      const indexes = this.getIndexes(tableName);
      
      console.log(`\n   Table: ${tableName}`);
      console.log('   Columns:');
      schema.columns.forEach(col => {
        const pk = col.primaryKey ? ' [PK]' : '';
        const nullable = col.nullable ? 'NULL' : 'NOT NULL';
        const defaultVal = col.defaultValue ? ` DEFAULT ${col.defaultValue}` : '';
        console.log(`     ${col.name}: ${col.type} ${nullable}${defaultVal}${pk}`);
      });
      
      if (foreignKeys.length > 0) {
        console.log('   Foreign Keys:');
        foreignKeys.forEach(fk => {
          console.log(`     ${fk.from} → ${fk.table}.${fk.to}`);
        });
      }
      
      if (indexes.length > 0) {
        console.log('   Indexes:');
        indexes.forEach(idx => {
          const unique = idx.unique ? ' [UNIQUE]' : '';
          console.log(`     ${idx.name}${unique}`);
        });
      }
    });
    
    return analysis;
  }

  /**
   * Save analysis to JSON file
   */
  saveAnalysis(outputPath) {
    const analysis = {
      timestamp: new Date().toISOString(),
      database: this.dbPath,
      tables: this.getTables().map(tableName => ({
        name: tableName,
        schema: this.getTableSchema(tableName),
        foreignKeys: this.getForeignKeys(tableName),
        indexes: this.getIndexes(tableName),
        rowCount: this.getRowCounts()[tableName]
      })),
      idPatternAnalysis: this.analyzeIdPatterns()
    };
    
    fs.writeFileSync(outputPath, JSON.stringify(analysis, null, 2));
    console.log(`💾 Analysis saved to: ${outputPath}`);
    
    return analysis;
  }
}

// CLI usage
if (require.main === module) {
  const dbPath = getDbPath();
  const inspector = new SchemaInspector(dbPath);
  
  if (inspector.connect()) {
    try {
      const analysis = inspector.generateReport();
      
      // Save analysis to file
      const outputPath = path.join(__dirname, `schema-analysis-${Date.now()}.json`);
      inspector.saveAnalysis(outputPath);
      
      console.log('\n✅ Schema analysis complete!');
      
    } catch (error) {
      console.error(`❌ Analysis failed: ${error.message}`);
      console.error(error.stack);
    } finally {
      inspector.disconnect();
    }
  }
}

module.exports = { SchemaInspector };