/**
 * Database Initialization
 * Handles database setup, schema creation, and migration integration
 */

import { DatabasePool } from './connection';
import { SchemaManager } from './schema';
import { GradientColumnManager } from '../utils/gradient-columns';
import { getColorLibraryImportService } from '../services/color-library-import.service';
import { createPerformanceInitializer } from '../performance/performance-initializer';
// Migration integration removed - using simplified schema initialization

/**
 * Database initialization manager
 * Coordinates the setup of database, schema, and related systems
 */
export class DatabaseInitializer {
  private db: any = null;
  private schemaManager: SchemaManager | null = null;
  private gradientColumnManager: GradientColumnManager | null = null;

  /**
   * Initialize database with optimized schema and connection pooling
   */
  async initDatabase(): Promise<any | null> {
    if (this.db) {
      console.log(
        '[DatabaseInitializer] Database already initialized, returning existing connection'
      );
      return this.db;
    }

    try {
      console.log('[DatabaseInitializer] Starting database initialization...');

      // Get primary database connection
      console.log(
        '[DatabaseInitializer] Getting database connection from pool...'
      );
      const pool = DatabasePool.getInstance();
      this.db = await pool.getConnection();

      if (!this.db) {
        throw new Error('Failed to get database connection from pool');
      }

      console.log(
        '[DatabaseInitializer] Database connection established successfully'
      );

      // Validate database access
      console.log('[DatabaseInitializer] Validating database access...');
      await this.validateDatabaseAccess();
      console.log('[DatabaseInitializer] Database access validation complete');

      // Initialize managers
      console.log('[DatabaseInitializer] Initializing database managers...');
      this.schemaManager = new SchemaManager(this.db);
      this.gradientColumnManager = new GradientColumnManager(this.db);
      console.log('[DatabaseInitializer] Database managers initialized');

      // Verify and create schema
      console.log('[DatabaseInitializer] Setting up database schema...');
      await this.setupSchema();
      console.log('[DatabaseInitializer] Database schema setup complete');

      // Run migrations (currently disabled)
      console.log('[DatabaseInitializer] Running database migrations...');
      await this.runMigrations();
      console.log('[DatabaseInitializer] Database migrations complete');

      // Ensure gradient columns exist
      console.log('[DatabaseInitializer] Ensuring gradient columns...');
      await this.gradientColumnManager.ensureGradientColumns();
      console.log('[DatabaseInitializer] Gradient columns ensured');

      // Import color libraries if needed
      console.log(
        '[DatabaseInitializer] Checking color library import status...'
      );
      await this.importColorLibrariesIfNeeded();
      console.log('[DatabaseInitializer] Color library check complete');

      // Apply performance optimizations
      console.log(
        '[DatabaseInitializer] Applying performance optimizations...'
      );
      await this.applyPerformanceOptimizations();
      console.log('[DatabaseInitializer] Performance optimizations complete');

      console.log(
        '[DatabaseInitializer] ✅ Database initialization completed successfully'
      );
      return this.db;
    } catch (error) {
      console.error(
        '[DatabaseInitializer] ❌ Failed to initialize database:',
        error
      );
      const errorDetails = error as Error & { code?: string };
      console.error('[DatabaseInitializer] Error details:', {
        message: errorDetails.message,
        stack: errorDetails.stack,
        code: errorDetails.code,
      });

      // Enhance error with user-friendly message
      const enhancedError = this.enhanceErrorMessage(error);
      console.error(
        '[DatabaseInitializer] User-friendly error:',
        enhancedError.message
      );

      this.db = null;
      return null;
    }
  }

  /**
   * Setup database schema
   */
  private async setupSchema(): Promise<void> {
    if (!this.schemaManager) {
      throw new Error('Schema manager not initialized');
    }

    // Check if all required tables exist
    const {
      existingTables: _existingTables,
      missingTables,
      needsCompleteSchema,
    } = await this.schemaManager.verifyRequiredTables();

    if (missingTables.length > 0) {
      console.log('[DatabaseInitializer] Missing tables:', missingTables);

      if (needsCompleteSchema) {
        console.log(
          '[DatabaseInitializer] Core tables missing, creating complete schema...'
        );
        await this.schemaManager.createCompleteSchema();
      } else if (
        missingTables.some(table =>
          ['organizations', 'organization_members', 'users'].includes(table)
        )
      ) {
        console.log(
          '[DatabaseInitializer] Organization/user tables missing, creating them...'
        );
        await this.schemaManager.ensureOrganizationTables();
      }
    } else {
      console.log('[DatabaseInitializer] All required tables exist');

      // Verify table schemas are correct
      await this.schemaManager.verifyAndUpdateSchemas();
    }
  }

  /**
   * Run database migrations
   */
  private async runMigrations(): Promise<void> {
    try {
      console.log('[DatabaseInitializer] Starting migration system...');

      // First, ensure critical migration 017 (denormalized color spaces) is applied
      await this.ensureCriticalMigrations();

      // Temporarily disable migration runner to fix database initialization
      // TODO: Re-enable once migration runner is properly compiled
      console.log(
        '[DatabaseInitializer] Migration system temporarily disabled'
      );
    } catch (error) {
      console.error(
        '[DatabaseInitializer] Migration failed:',
        error instanceof Error ? error.message : error
      );
      // Don't throw here - allow the app to continue with existing schema
    }
  }

  /**
   * Ensure critical migrations are applied directly
   * This is a failsafe to ensure essential migrations like 017 always run
   */
  private async ensureCriticalMigrations(): Promise<void> {
    try {
      console.log('[DatabaseInitializer] Checking critical migrations...');

      // Check if migration 017 (denormalized color spaces) is applied
      const migration17Applied = this.db
        .prepare(
          `
        SELECT 1 FROM schema_migrations WHERE version = 17
      `
        )
        .get();

      if (!migration17Applied) {
        console.log(
          '[DatabaseInitializer] Applying critical migration 017 (denormalized color spaces)...'
        );
        await this.applyCriticalMigration017();
      } else {
        console.log(
          '[DatabaseInitializer] Critical migration 017 already applied'
        );
      }
    } catch (error) {
      console.error(
        '[DatabaseInitializer] Error checking critical migrations:',
        error
      );
    }
  }

  /**
   * Apply migration 017 directly with embedded SQL
   */
  private async applyCriticalMigration017(): Promise<void> {
    const migration17SQL = `
      -- Add color_spaces JSON column to store all color space data
      ALTER TABLE colors ADD COLUMN color_spaces JSON DEFAULT '{}';

      -- Create indexes for JSON queries on color spaces
      CREATE INDEX IF NOT EXISTS idx_colors_color_spaces_cmyk ON colors(json_extract(color_spaces, '$.cmyk.c'), json_extract(color_spaces, '$.cmyk.m'), json_extract(color_spaces, '$.cmyk.y'), json_extract(color_spaces, '$.cmyk.k'));
      CREATE INDEX IF NOT EXISTS idx_colors_has_gradient ON colors(json_extract(color_spaces, '$.gradient')) WHERE json_extract(color_spaces, '$.gradient') IS NOT NULL;

      -- Data Migration: Populate color_spaces JSON from existing normalized tables
      UPDATE colors SET color_spaces = json_object(
        'cmyk', CASE 
          WHEN EXISTS (SELECT 1 FROM color_cmyk WHERE color_id = colors.id) 
          THEN json_object(
            'c', (SELECT c FROM color_cmyk WHERE color_id = colors.id),
            'm', (SELECT m FROM color_cmyk WHERE color_id = colors.id),
            'y', (SELECT y FROM color_cmyk WHERE color_id = colors.id),
            'k', (SELECT k FROM color_cmyk WHERE color_id = colors.id)
          )
          ELSE NULL
        END,
        'rgb', CASE 
          WHEN EXISTS (SELECT 1 FROM color_rgb WHERE color_id = colors.id)
          THEN json_object(
            'r', (SELECT r FROM color_rgb WHERE color_id = colors.id),
            'g', (SELECT g FROM color_rgb WHERE color_id = colors.id),
            'b', (SELECT b FROM color_rgb WHERE color_id = colors.id)
          )
          ELSE NULL
        END,
        'lab', CASE 
          WHEN EXISTS (SELECT 1 FROM color_lab WHERE color_id = colors.id)
          THEN json_object(
            'l', (SELECT l FROM color_lab WHERE color_id = colors.id),
            'a', (SELECT a FROM color_lab WHERE color_id = colors.id),
            'b', (SELECT b FROM color_lab WHERE color_id = colors.id)
          )
          ELSE NULL
        END,
        'hsl', CASE 
          WHEN EXISTS (SELECT 1 FROM color_hsl WHERE color_id = colors.id)
          THEN json_object(
            'h', (SELECT h FROM color_hsl WHERE color_id = colors.id),
            's', (SELECT s FROM color_hsl WHERE color_id = colors.id),
            'l', (SELECT l FROM color_hsl WHERE color_id = colors.id)
          )
          ELSE NULL
        END
      )
      WHERE color_spaces = '{}' OR color_spaces IS NULL;

      -- Populate gradient data for gradient colors
      UPDATE colors SET color_spaces = json_set(
        color_spaces,
        '$.gradient',
        json_object(
          'stops', (
            SELECT json_group_array(
              json_object(
                'color', hex,
                'position', position * 100
              )
            )
            FROM gradient_stops
            WHERE gradient_id = colors.id
            ORDER BY stop_index
          )
        )
      )
      WHERE EXISTS (SELECT 1 FROM gradient_stops WHERE gradient_id = colors.id);

      -- Update the is_gradient column based on gradient data
      UPDATE colors SET is_gradient = 1 
      WHERE json_extract(color_spaces, '$.gradient') IS NOT NULL
        AND json_array_length(json_extract(color_spaces, '$.gradient.stops')) > 0;

      -- Record migration completion
      INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (17, 'denormalize_color_spaces');
    `;

    try {
      // Run the migration in a transaction
      this.db.transaction(() => {
        // Split the SQL into individual statements and execute them
        const statements = migration17SQL
          .split(';')
          .filter(stmt => stmt.trim());
        for (const statement of statements) {
          if (statement.trim()) {
            this.db.exec(statement.trim());
          }
        }
      })();

      console.log(
        '[DatabaseInitializer] Critical migration 017 applied successfully'
      );

      // Verify the migration worked
      const colorCount = this.db
        .prepare(
          `SELECT COUNT(*) as count FROM colors WHERE color_spaces IS NOT NULL AND color_spaces != '{}'`
        )
        .get();
      console.log(
        `[DatabaseInitializer] Migration 017 verified: ${colorCount.count} colors with populated color_spaces`
      );
    } catch (error) {
      console.error(
        '[DatabaseInitializer] Failed to apply critical migration 017:',
        error
      );
      throw error;
    }
  }

  /**
   * Import color libraries if needed (first run)
   */
  private async importColorLibrariesIfNeeded(): Promise<void> {
    try {
      const importService = getColorLibraryImportService();

      // Check if we need to import color libraries
      const needsImport = await importService.needsImport();

      if (needsImport) {
        console.log(
          '[DatabaseInitializer] Color libraries not found, starting import...'
        );

        // Import all color libraries
        const results = await importService.importAllLibraries();

        console.log('[DatabaseInitializer] Color library import completed:', {
          pantone: `${results.pantone.imported} imported, ${results.pantone.skipped} skipped, ${results.pantone.errors} errors`,
          ral: `${results.ral.imported} imported, ${results.ral.skipped} skipped, ${results.ral.errors} errors`,
        });

        // Log statistics
        const stats = await importService.getLibraryStats();
        console.log('[DatabaseInitializer] Color library statistics:', stats);
      } else {
        console.log(
          '[DatabaseInitializer] Color libraries already imported, skipping'
        );
      }
    } catch (error) {
      console.error(
        '[DatabaseInitializer] Failed to import color libraries:',
        error
      );
      // Don't throw - allow app to continue without color libraries
    }
  }

  /**
   * Get the initialized database instance
   */
  getDatabase(): any {
    return this.db;
  }

  /**
   * Get schema manager instance
   */
  getSchemaManager(): SchemaManager | null {
    return this.schemaManager;
  }

  /**
   * Get gradient column manager instance
   */
  getGradientColumnManager(): GradientColumnManager | null {
    return this.gradientColumnManager;
  }

  /**
   * Close database and cleanup resources
   */
  async close(): Promise<void> {
    if (this.db) {
      try {
        // Release the connection back to pool
        const pool = DatabasePool.getInstance();
        pool.releaseConnection(this.db);

        // Close all pool connections
        pool.closeAll();

        this.db = null;
        this.schemaManager = null;
        this.gradientColumnManager = null;

        console.log('[DatabaseInitializer] Database closed successfully');
      } catch (error) {
        console.error('[DatabaseInitializer] Error closing database:', error);
      }
    }
  }

  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    isConnected: boolean;
    tablesExist: boolean;
    canQuery: boolean;
    poolStats: any;
  }> {
    try {
      const isConnected = this.db !== null;

      let tablesExist = false;
      let canQuery = false;

      if (isConnected && this.schemaManager) {
        try {
          const { existingTables } =
            await this.schemaManager.verifyRequiredTables();
          tablesExist = existingTables.length > 0;

          // Test a simple query
          this.db.prepare('SELECT 1').get();
          canQuery = true;
        } catch (error) {
          console.warn(
            '[DatabaseInitializer] Health check query failed:',
            error
          );
        }
      }

      const pool = DatabasePool.getInstance();
      const poolStats = pool.getStats();

      return {
        isConnected,
        tablesExist,
        canQuery,
        poolStats,
      };
    } catch (error) {
      console.error('[DatabaseInitializer] Health check failed:', error);
      return {
        isConnected: false,
        tablesExist: false,
        canQuery: false,
        poolStats: null,
      };
    }
  }

  /**
   * Apply performance optimizations for local-first color browsing
   */
  private async applyPerformanceOptimizations(): Promise<void> {
    if (!this.db) {
      throw new Error(
        'Database connection not available for performance optimization'
      );
    }

    try {
      console.log(
        '[DatabaseInitializer] 🚀 Starting performance optimization...'
      );

      const performanceInitializer = createPerformanceInitializer(this.db);

      const result = await performanceInitializer.initializeOptimizations({
        skipIfOptimized: true,
        refreshAggregationTables: true,
        runAnalysis: false, // Skip analysis during initialization for speed
      });

      if (result.success) {
        console.log(
          '[DatabaseInitializer] ✅ Performance optimizations applied successfully'
        );
        console.log(
          '[DatabaseInitializer] Optimizations:',
          result.optimizationsApplied.join(', ')
        );
      } else {
        console.warn(
          '[DatabaseInitializer] ⚠️  Some performance optimizations failed:',
          result.errors
        );
      }
    } catch (error) {
      console.error(
        '[DatabaseInitializer] ❌ Performance optimization failed:',
        error
      );
      // Don't throw - allow app to continue without optimizations
      console.warn(
        '[DatabaseInitializer] Continuing without performance optimizations'
      );
    }
  }

  /**
   * Validate database access with simple connectivity test
   */
  private async validateDatabaseAccess(): Promise<void> {
    if (!this.db) {
      throw new Error('Database connection not available');
    }

    try {
      // Simple connectivity test
      this.db.prepare('SELECT 1').get();
      console.log(
        '[DatabaseInitializer] Database access validation successful'
      );
    } catch (error) {
      console.error(
        '[DatabaseInitializer] Database access validation failed:',
        error
      );
      throw this.enhanceErrorMessage(error);
    }
  }

  /**
   * Enhance error messages with user-friendly descriptions
   */
  private enhanceErrorMessage(error: any): Error {
    if (!error.code) {
      return error;
    }

    switch (error.code) {
      case 'SQLITE_BUSY':
        return new Error(
          'ChromaSync database is currently in use by another process. ' +
            'Please close any other ChromaSync instances and try again.'
        );

      case 'SQLITE_CORRUPT':
        return new Error(
          'The ChromaSync database file is corrupted. ' +
            'Please go to Settings → Reset Application Data to restore from backup, ' +
            'or contact support if you need help recovering your data.'
        );

      case 'SQLITE_CANTOPEN':
        return new Error(
          'Cannot open the ChromaSync database file. ' +
            'Please check that you have write permissions to the application data directory, ' +
            'or try restarting ChromaSync as an administrator.'
        );

      case 'SQLITE_READONLY':
        return new Error(
          'The ChromaSync database is read-only. ' +
            'Please check file permissions and ensure the application data directory is writable.'
        );

      case 'SQLITE_IOERR':
        return new Error(
          'A disk input/output error occurred while accessing the database. ' +
            'Please check your disk space and try restarting ChromaSync.'
        );

      case 'SQLITE_NOMEM':
        return new Error(
          'ChromaSync ran out of memory while initializing the database. ' +
            'Please close other applications and try restarting ChromaSync.'
        );

      default:
        // Return original error with additional context
        return new Error(
          `Database initialization failed: ${error.message}. ` +
            'If this problem persists, please try resetting application data in Settings.'
        );
    }
  }

  /**
   * Run optional database integrity check (for troubleshooting)
   */
  async runIntegrityCheck(): Promise<{
    passed: boolean;
    message: string;
    details?: string[];
  }> {
    if (!this.db) {
      return {
        passed: false,
        message: 'Database not initialized',
      };
    }

    try {
      console.log('[DatabaseInitializer] Running database integrity check...');

      // Use quick_check for faster validation
      const result = this.db.prepare('PRAGMA quick_check').all();

      const passed = result.length === 1 && result[0].quick_check === 'ok';
      const message = passed
        ? 'Database integrity check passed successfully'
        : 'Database integrity issues detected';

      const details = passed
        ? undefined
        : result.map((row: any) => row.quick_check);

      console.log(
        `[DatabaseInitializer] Integrity check ${passed ? 'passed' : 'failed'}:`,
        { message, details }
      );

      return { passed, message, details };
    } catch (error) {
      console.error('[DatabaseInitializer] Integrity check failed:', error);
      const errorDetails = error as Error;
      return {
        passed: false,
        message: `Integrity check failed: ${errorDetails.message}`,
      };
    }
  }
}

// Singleton instance
let databaseInitializer: DatabaseInitializer | null = null;

/**
 * Get singleton database initializer instance
 */
export function getDatabaseInitializer(): DatabaseInitializer {
  if (!databaseInitializer) {
    databaseInitializer = new DatabaseInitializer();
  }
  return databaseInitializer;
}

/**
 * Initialize database (convenience function)
 */
export async function initDatabase(): Promise<any | null> {
  const initializer = getDatabaseInitializer();
  return await initializer.initDatabase();
}

/**
 * Get initialized database instance (convenience function)
 */
export function getDatabase(): any {
  const initializer = getDatabaseInitializer();
  return initializer.getDatabase();
}
