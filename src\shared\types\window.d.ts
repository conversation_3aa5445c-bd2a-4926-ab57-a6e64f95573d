import { Organization } from './organization.types';

export interface OrganizationAPI {
  createOrganization: (data: { name: string; slug?: string }) => Promise<{
    success: boolean;
    data?: Organization;
    error?: string;
  }>;

  getOrganizations: () => Promise<{
    success: boolean;
    data: Organization[];
    error?: string;
  }>;

  getCurrentOrganization: () => Promise<{
    success: boolean;
    data?: Organization;
    error?: string;
  }>;

  setCurrentOrganization: (organizationId: string) => Promise<{
    success: boolean;
    error?: string;
  }>;

  getMembers: (organizationId: string) => Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }>;

  inviteMember: (data: {
    organizationId: string;
    email: string;
    role: string;
  }) => Promise<{
    success: boolean;
    error?: string;
  }>;

  removeMember: (data: { organizationId: string; userId: string }) => Promise<{
    success: boolean;
    error?: string;
  }>;

  updateMemberRole: (data: {
    organizationId: string;
    userId: string;
    role: string;
  }) => Promise<{
    success: boolean;
    error?: string;
  }>;

  updateSettings: (data: { organizationId: string; settings: any }) => Promise<{
    success: boolean;
    error?: string;
  }>;

  deleteOrganization: (
    organizationId: string,
    forceCascade?: boolean
  ) => Promise<{
    success: boolean;
    error?: string;
  }>;

  acceptInvitation: (token: string) => Promise<{
    success: boolean;
    error?: string;
  }>;

  getPendingInvitations: (organizationId: string) => Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }>;

  revokeInvitation: (data: {
    organizationId: string;
    invitationId: string;
  }) => Promise<{
    success: boolean;
    error?: string;
  }>;
}

declare global {
  interface Window {
    organizationAPI: OrganizationAPI;
  }
}

export {};
