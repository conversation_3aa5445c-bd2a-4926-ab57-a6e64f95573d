/**
 * @file performance-initializer.ts
 * @description Service to initialize and apply all database performance optimizations
 *
 * This service ensures that performance optimizations are properly applied when:
 * - App starts up for the first time
 * - Database is recreated or migrated
 * - User manually triggers optimization
 * - Performance degradation is detected
 */

import Database from 'better-sqlite3';
import { SQLiteQueryOptimizer, QueryAnalysisResult } from './query-optimizer';
import { createOptimizedColorRepository } from '../repositories/optimized-color.repository';

export interface PerformanceInitializationResult {
  success: boolean;
  optimizationsApplied: string[];
  analysisResult: QueryAnalysisResult | null;
  errors: string[];
  timeTakenMs: number;
}

export interface PerformanceInitializationOptions {
  enableMetrics?: boolean;
  skipIfOptimized?: boolean;
  refreshAggregationTables?: boolean;
  runAnalysis?: boolean;
}

/**
 * Service for initializing and managing database performance optimizations
 *
 * This service provides a centralized way to:
 * 1. Apply all performance optimizations to a database
 * 2. Check if optimizations are already in place
 * 3. Refresh materialized aggregation data
 * 4. Monitor optimization effectiveness
 */
export class PerformanceInitializer {
  private optimizer: SQLiteQueryOptimizer;

  constructor(private db: Database.Database) {
    this.optimizer = new SQLiteQueryOptimizer(db);
  }

  /**
   * Main initialization method - applies all performance optimizations
   */
  async initializeOptimizations(
    options: PerformanceInitializationOptions = {}
  ): Promise<PerformanceInitializationResult> {
    console.log(
      '[PerformanceInitializer] 🚀 Starting database performance initialization...'
    );

    const startTime = Date.now();
    const optimizationsApplied: string[] = [];
    const errors: string[] = [];
    let analysisResult: QueryAnalysisResult | null = null;

    try {
      // Step 1: Check if optimizations already exist (if skipIfOptimized is true)
      if (options.skipIfOptimized && (await this.areOptimizationsApplied())) {
        console.log(
          '[PerformanceInitializer] ✅ Optimizations already applied, skipping...'
        );
        return {
          success: true,
          optimizationsApplied: ['Already optimized'],
          analysisResult: null,
          errors: [],
          timeTakenMs: Date.now() - startTime,
        };
      }

      // Step 2: Apply core performance optimizations
      console.log('[PerformanceInitializer] 🔧 Applying core optimizations...');
      await this.optimizer.optimizeColorBrowsingPerformance();
      optimizationsApplied.push('Core SQLite performance optimizations');
      optimizationsApplied.push('Organization-first composite indexes');
      optimizationsApplied.push('Covering indexes for common queries');
      optimizationsApplied.push('Materialized aggregation tables');
      optimizationsApplied.push('SQLite settings optimization');

      // Step 3: Initialize aggregation tables with current data
      if (options.refreshAggregationTables !== false) {
        console.log(
          '[PerformanceInitializer] 📊 Initializing aggregation tables...'
        );
        await this.initializeAggregationTables();
        optimizationsApplied.push('Aggregation tables initialized');
      }

      // Step 4: Run query analysis if requested
      if (options.runAnalysis) {
        console.log(
          '[PerformanceInitializer] 🔍 Running query plan analysis...'
        );
        analysisResult = await this.optimizer.analyzeQueryPlans();
        optimizationsApplied.push('Query plan analysis completed');
      }

      // Step 5: Mark optimizations as applied
      await this.markOptimizationsApplied();
      optimizationsApplied.push('Optimization tracking updated');

      const timeTaken = Date.now() - startTime;
      console.log(
        `[PerformanceInitializer] ✅ Performance initialization completed in ${timeTaken}ms`
      );

      return {
        success: true,
        optimizationsApplied,
        analysisResult,
        errors,
        timeTakenMs: timeTaken,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      console.error(
        '[PerformanceInitializer] ❌ Performance initialization failed:',
        error
      );

      return {
        success: false,
        optimizationsApplied,
        analysisResult,
        errors,
        timeTakenMs: Date.now() - startTime,
      };
    }
  }

  /**
   * Check if performance optimizations have already been applied
   */
  private async areOptimizationsApplied(): Promise<boolean> {
    try {
      // Check for optimization tracking table
      const tableExists = this.db
        .prepare(
          `
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='performance_optimizations'
      `
        )
        .get();

      if (!tableExists) {return false;}

      // Check if current version is applied
      const optimization = this.db
        .prepare(
          `
        SELECT applied_at FROM performance_optimizations 
        WHERE version = ? AND applied = 1
      `
        )
        .get('v1.0.0');

      return !!optimization;
    } catch (error) {
      console.warn(
        '[PerformanceInitializer] Failed to check optimization status:',
        error
      );
      return false;
    }
  }

  /**
   * Initialize aggregation tables with current data for all organizations
   */
  private async initializeAggregationTables(): Promise<void> {
    try {
      // Get all organizations
      const organizations = this.db
        .prepare(
          `
        SELECT id FROM organizations
      `
        )
        .all() as { id: string }[];

      console.log(
        `[PerformanceInitializer] Found ${organizations.length} organizations to process`
      );

      // Initialize aggregation data for each organization
      for (const org of organizations) {
        await this.optimizer.refreshAggregationTables(org.id);
        console.log(
          `[PerformanceInitializer] ✅ Initialized aggregation tables for org: ${org.id}`
        );
      }
    } catch (error) {
      console.error(
        '[PerformanceInitializer] Failed to initialize aggregation tables:',
        error
      );
      throw error;
    }
  }

  /**
   * Mark optimizations as applied in tracking table
   */
  private async markOptimizationsApplied(): Promise<void> {
    try {
      // Create optimization tracking table if it doesn't exist
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS performance_optimizations (
          version TEXT PRIMARY KEY,
          applied BOOLEAN NOT NULL DEFAULT FALSE,
          applied_at TEXT DEFAULT CURRENT_TIMESTAMP,
          description TEXT
        );
      `);

      // Mark current version as applied
      this.db
        .prepare(
          `
        INSERT OR REPLACE INTO performance_optimizations 
        (version, applied, applied_at, description)
        VALUES (?, ?, ?, ?)
      `
        )
        .run(
          'v1.0.0',
          true,
          new Date().toISOString(),
          'Local-first color browsing performance optimizations'
        );
    } catch (error) {
      console.error(
        '[PerformanceInitializer] Failed to mark optimizations as applied:',
        error
      );
      throw error;
    }
  }

  /**
   * Refresh aggregation tables for a specific organization
   */
  async refreshOrganizationAggregations(organizationId: string): Promise<void> {
    console.log(
      `[PerformanceInitializer] 🔄 Refreshing aggregations for organization: ${organizationId}`
    );

    try {
      await this.optimizer.refreshAggregationTables(organizationId);
      console.log(
        `[PerformanceInitializer] ✅ Aggregations refreshed for: ${organizationId}`
      );
    } catch (error) {
      console.error(
        `[PerformanceInitializer] ❌ Failed to refresh aggregations for ${organizationId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Run performance analysis and return recommendations
   */
  async analyzePerformance(): Promise<QueryAnalysisResult> {
    console.log('[PerformanceInitializer] 🔍 Running performance analysis...');

    try {
      const result = await this.optimizer.analyzeQueryPlans();
      console.log('[PerformanceInitializer] ✅ Performance analysis completed');
      return result;
    } catch (error) {
      console.error(
        '[PerformanceInitializer] ❌ Performance analysis failed:',
        error
      );
      throw error;
    }
  }

  /**
   * Check current optimization status
   */
  async getOptimizationStatus(): Promise<{
    isOptimized: boolean;
    version: string | null;
    appliedAt: string | null;
    aggregationStats: Record<string, any>;
  }> {
    try {
      // Check optimization status
      const optimization = this.db
        .prepare(
          `
        SELECT version, applied_at FROM performance_optimizations 
        WHERE applied = 1 ORDER BY applied_at DESC LIMIT 1
      `
        )
        .get() as { version: string; applied_at: string } | undefined;

      // Get aggregation table statistics
      const organizations = this.db
        .prepare(`SELECT id FROM organizations LIMIT 3`)
        .all() as { id: string }[];
      const aggregationStats: Record<string, any> = {};

      for (const org of organizations) {
        try {
          const optimizedRepo = createOptimizedColorRepository(this.db);
          aggregationStats[org.id] = optimizedRepo.getAggregationTableStats(
            org.id
          );
        } catch (error) {
          aggregationStats[org.id] = { error: 'Failed to get stats' };
        }
      }

      return {
        isOptimized: !!optimization,
        version: optimization?.version || null,
        appliedAt: optimization?.applied_at || null,
        aggregationStats,
      };
    } catch (error) {
      console.error(
        '[PerformanceInitializer] Failed to get optimization status:',
        error
      );
      return {
        isOptimized: false,
        version: null,
        appliedAt: null,
        aggregationStats: {},
      };
    }
  }

  /**
   * Reset all optimizations (for testing or troubleshooting)
   */
  async resetOptimizations(): Promise<void> {
    console.log('[PerformanceInitializer] 🔄 Resetting all optimizations...');

    try {
      // Drop optimization-specific indexes
      const optimizationIndexes = [
        'idx_colors_org_active_browsing',
        'idx_colors_org_source_active',
        'idx_products_org_active_name',
        'idx_product_colors_org_product_optimized',
        'idx_product_colors_org_color_optimized',
        'idx_colors_findall_covering',
        'idx_colors_usage_covering',
        'idx_product_colors_usage_covering',
        'idx_products_usage_covering',
      ];

      for (const indexName of optimizationIndexes) {
        try {
          this.db.exec(`DROP INDEX IF EXISTS ${indexName}`);
        } catch (error) {
          console.warn(
            `[PerformanceInitializer] Failed to drop index ${indexName}:`,
            error
          );
        }
      }

      // Drop aggregation tables
      this.db.exec(`DROP TABLE IF EXISTS color_usage_counts`);
      this.db.exec(`DROP TABLE IF EXISTS color_product_map`);

      // Drop optimization tracking
      this.db.exec(`DROP TABLE IF EXISTS performance_optimizations`);

      console.log('[PerformanceInitializer] ✅ Optimizations reset completed');
    } catch (error) {
      console.error(
        '[PerformanceInitializer] ❌ Failed to reset optimizations:',
        error
      );
      throw error;
    }
  }

  /**
   * Get performance metrics for monitoring
   */
  getPerformanceMetrics(): {
    cacheSize: number;
    optimizedStatements: number;
    indexCount: number;
    aggregationTables: string[];
  } {
    try {
      // Get SQLite cache info
      const cacheInfo = this.db.pragma('cache_size');

      // Get index count
      const indexes = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM sqlite_master 
        WHERE type='index' AND name LIKE 'idx_%_org%'
      `
        )
        .get() as { count: number };

      // Get aggregation tables
      const aggTables = this.db
        .prepare(
          `
        SELECT name FROM sqlite_master 
        WHERE type='table' AND (name LIKE '%_usage_%' OR name LIKE '%_map')
      `
        )
        .all() as { name: string }[];

      return {
        cacheSize: typeof cacheInfo === 'number' ? cacheInfo : 0,
        optimizedStatements:
          (createOptimizedColorRepository(this.db).constructor as any)[
            'getStatementCacheSize'
          ]?.() || 0,
        indexCount: indexes.count,
        aggregationTables: aggTables.map(t => t.name),
      };
    } catch (error) {
      console.error(
        '[PerformanceInitializer] Failed to get performance metrics:',
        error
      );
      return {
        cacheSize: 0,
        optimizedStatements: 0,
        indexCount: 0,
        aggregationTables: [],
      };
    }
  }
}

/**
 * Factory function to create performance initializer
 */
export function createPerformanceInitializer(
  db: Database.Database
): PerformanceInitializer {
  return new PerformanceInitializer(db);
}

/**
 * Convenience function to apply optimizations to a database
 */
export async function optimizeDatabase(
  db: Database.Database,
  options: PerformanceInitializationOptions = {}
): Promise<PerformanceInitializationResult> {
  const initializer = createPerformanceInitializer(db);
  return await initializer.initializeOptimizations(options);
}
