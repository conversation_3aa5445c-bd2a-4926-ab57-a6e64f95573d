/**
 * Color Library Import Service
 * Handles importing Pantone, RAL, and other color library data into the database
 */

import { executeWithPool } from '../core/connection';
import {
  COLOR_LIBRARIES_SCHEMA,
  LIBRARY_CODES,
} from '../schemas/color-libraries-schema';

// Static imports for color data
import { pantoneColors } from '../../../shared/data/pantone-colors';
import { ralColors } from '../../../shared/data/ral-colors';

/**
 * Color library import service
 */
export class ColorLibraryImportService {
  /**
   * Initialize color library tables
   */
  async initializeTables(): Promise<void> {
    await executeWithPool(db => {
      console.log('[ColorLibraryImport] Creating color library tables...');
      db.exec(COLOR_LIBRARIES_SCHEMA);
      console.log(
        '[ColorLibraryImport] Color library tables created successfully'
      );
    });
  }

  /**
   * Import Pantone colors from the static file
   */
  async importPantoneColors(): Promise<{
    imported: number;
    skipped: number;
    errors: number;
  }> {
    console.log('[ColorLibraryImport] Starting Pantone color import...');

    try {
      // Use statically imported Pantone colors
      console.log(
        `[ColorLibraryImport] Found ${pantoneColors.length} Pantone colors to import`
      );

      return await executeWithPool(db => {
        let imported = 0;
        let skipped = 0;
        let errors = 0;

        const transaction = db.transaction(() => {
          // Get Pantone library ID
          const pantoneLibrary = db
            .prepare('SELECT id FROM color_libraries WHERE code = ?')
            .get(LIBRARY_CODES.PANTONE);
          if (!pantoneLibrary) {
            throw new Error('Pantone library not found in database');
          }

          const insertStmt = db.prepare(`
            INSERT OR IGNORE INTO library_colors 
            (external_id, library_id, code, name, hex, cmyk, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `);

          const checkStmt = db.prepare(
            'SELECT id FROM library_colors WHERE external_id = ?'
          );

          pantoneColors.forEach((color, index) => {
            try {
              // Check if color already exists
              const existing = checkStmt.get(color.id);
              if (existing) {
                skipped++;
                return;
              }

              // Insert the color
              const result = insertStmt.run(
                color.id,
                pantoneLibrary.id,
                color.code,
                color.name,
                color.hex,
                color.cmyk,
                index
              );

              if (result.changes > 0) {
                imported++;
              } else {
                skipped++;
              }
            } catch (error) {
              console.error(
                `[ColorLibraryImport] Error importing Pantone color ${color.code}:`,
                error
              );
              errors++;
            }
          });
        });

        transaction();

        const result = { imported, skipped, errors };
        console.log(`[ColorLibraryImport] Pantone import completed:`, result);
        return result;
      });
    } catch (error) {
      console.error(
        '[ColorLibraryImport] Failed to import Pantone colors:',
        error
      );
      throw error;
    }
  }

  /**
   * Import RAL colors from the static file
   */
  async importRalColors(): Promise<{
    imported: number;
    skipped: number;
    errors: number;
  }> {
    console.log('[ColorLibraryImport] Starting RAL color import...');

    try {
      // Use statically imported RAL colors
      console.log(
        `[ColorLibraryImport] Found ${ralColors.length} RAL colors to import`
      );

      return await executeWithPool(db => {
        let imported = 0;
        let skipped = 0;
        let errors = 0;

        const transaction = db.transaction(() => {
          // Get RAL library ID
          const ralLibrary = db
            .prepare('SELECT id FROM color_libraries WHERE code = ?')
            .get(LIBRARY_CODES.RAL);
          if (!ralLibrary) {
            throw new Error('RAL library not found in database');
          }

          const insertStmt = db.prepare(`
            INSERT OR IGNORE INTO library_colors 
            (external_id, library_id, code, name, hex, cmyk, notes, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `);

          const checkStmt = db.prepare(
            'SELECT id FROM library_colors WHERE external_id = ?'
          );

          ralColors.forEach((color, index) => {
            try {
              // Check if color already exists
              const existing = checkStmt.get(color.id);
              if (existing) {
                skipped++;
                return;
              }

              // Insert the color
              const result = insertStmt.run(
                color.id,
                ralLibrary.id,
                color.code,
                color.name,
                color.hex,
                color.cmyk,
                color.notes || null,
                index
              );

              if (result.changes > 0) {
                imported++;
              } else {
                skipped++;
              }
            } catch (error) {
              console.error(
                `[ColorLibraryImport] Error importing RAL color ${color.code}:`,
                error
              );
              errors++;
            }
          });
        });

        transaction();

        const result = { imported, skipped, errors };
        console.log(`[ColorLibraryImport] RAL import completed:`, result);
        return result;
      });
    } catch (error) {
      console.error('[ColorLibraryImport] Failed to import RAL colors:', error);
      throw error;
    }
  }

  /**
   * Import all color libraries
   */
  async importAllLibraries(): Promise<{
    pantone: { imported: number; skipped: number; errors: number };
    ral: { imported: number; skipped: number; errors: number };
  }> {
    console.log('[ColorLibraryImport] Starting full color library import...');

    // Initialize tables first
    await this.initializeTables();

    // Import all libraries
    const pantone = await this.importPantoneColors();
    const ral = await this.importRalColors();

    console.log('[ColorLibraryImport] Full import completed:', {
      pantone,
      ral,
    });
    return { pantone, ral };
  }

  /**
   * Get library statistics
   */
  async getLibraryStats(): Promise<{
    libraries: Array<{ code: string; name: string; colorCount: number }>;
    totalColors: number;
  }> {
    return await executeWithPool(db => {
      const libraries = db
        .prepare(
          `
        SELECT 
          cl.code,
          cl.name,
          COUNT(lc.id) as colorCount
        FROM color_libraries cl
        LEFT JOIN library_colors lc ON cl.id = lc.library_id AND lc.is_active = TRUE
        GROUP BY cl.id, cl.code, cl.name
        ORDER BY cl.code
      `
        )
        .all();

      const totalColors = db
        .prepare(
          'SELECT COUNT(*) as count FROM library_colors WHERE is_active = TRUE'
        )
        .get().count;

      return { libraries, totalColors };
    });
  }

  /**
   * Check if libraries need importing
   */
  async needsImport(): Promise<boolean> {
    return await executeWithPool(db => {
      const colorCount = db
        .prepare(
          'SELECT COUNT(*) as count FROM library_colors WHERE is_active = TRUE'
        )
        .get().count;
      return colorCount === 0;
    });
  }

  /**
   * Clear all library data (for re-import)
   */
  async clearLibraryData(): Promise<void> {
    await executeWithPool(db => {
      console.log('[ColorLibraryImport] Clearing existing library data...');
      db.exec(`
        DELETE FROM library_color_metadata;
        DELETE FROM library_colors;
        DELETE FROM color_libraries WHERE code IN ('PANTONE', 'RAL');
      `);
      console.log('[ColorLibraryImport] Library data cleared');
    });
  }
}

// Singleton instance
let colorLibraryImportService: ColorLibraryImportService | null = null;

/**
 * Get singleton instance of color library import service
 */
export function getColorLibraryImportService(): ColorLibraryImportService {
  if (!colorLibraryImportService) {
    colorLibraryImportService = new ColorLibraryImportService();
  }
  return colorLibraryImportService;
}
