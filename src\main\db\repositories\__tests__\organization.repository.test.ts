/**
 * @file organization.repository.test.ts
 * @description Unit tests for OrganizationRepository data access layer
 * 
 * Tests all database operations that were extracted from OrganizationService
 * into a dedicated repository following the Repository pattern.
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';
import { OrganizationRepository } from '../organization.repository';
import { 
  CreateOrganizationData, 
  UpdateOrganizationData,
  CreateInvitationData,
  CreateUserData,
  UpdateUserData 
} from '../interfaces/organization.repository.interface';

describe.sequential('OrganizationRepository', () => {
  let db: Database.Database;
  let repository: OrganizationRepository;
  let mockUserId: string;
  let mockOrgExternalId: string;
  let mockOrgInternalId: number;

  beforeEach(() => {
    // Create in-memory SQLite database for testing
    db = new Database(':memory:');
    
    // Set up test schema
    setupTestSchema(db);
    
    // Create repository instance
    repository = new OrganizationRepository(db);
    
    mockUserId = 'user-550e8400-e29b-41d4-a716-446655440000';
    mockOrgExternalId = 'org-550e8400-e29b-41d4-a716-446655440000';
    mockOrgInternalId = 1;
    
    // Seed test data
    seedTestData(db, mockUserId, mockOrgExternalId, mockOrgInternalId);
  });

  afterEach(() => {
    if (db && db.open) {
      try {
        db.close();
      } catch (error) {
        console.warn('Database close error:', error);
      }
    }
  });

  describe('Organization CRUD Operations', () => {
    describe('findAll', () => {
      test('should return all organizations', () => {
        const organizations = repository.findAll();
        
        expect(organizations).toBeInstanceOf(Array);
        expect(organizations.length).toBeGreaterThan(0);
        expect(organizations[0]).toHaveProperty('id');
        expect(organizations[0]).toHaveProperty('external_id');
        expect(organizations[0]).toHaveProperty('name');
        expect(organizations[0]).toHaveProperty('slug');
      });

      test('should order results by name', () => {
        const organizations = repository.findAll();
        
        for (let i = 1; i < organizations.length; i++) {
          expect(organizations[i-1].name <= organizations[i].name).toBe(true);
        }
      });
    });

    describe('findByExternalId', () => {
      test('should return organization by external ID', () => {
        const org = repository.findByExternalId(mockOrgExternalId);
        
        expect(org).toBeDefined();
        expect(org?.external_id).toBe(mockOrgExternalId);
        expect(org?.name).toBe('Test Organization');
        expect(org).toHaveProperty('member_count');
      });

      test('should return null for non-existent organization', () => {
        const org = repository.findByExternalId('non-existent-org');
        
        expect(org).toBeNull();
      });

      test('should include member count', () => {
        const org = repository.findByExternalId(mockOrgExternalId);
        
        expect(org?.member_count).toBeGreaterThan(0);
      });
    });

    describe('insert', () => {
      test('should create new organization with owner', () => {
        const newOrgData: CreateOrganizationData = {
          external_id: 'new-org-external-id',
          name: 'New Organization',
          slug: 'new-organization',
          plan: 'free',
          settings: '{}',
          ownerId: mockUserId
        };

        const externalId = repository.insert(newOrgData);
        
        expect(externalId).toBe('new-org-external-id');
        
        // Verify organization was created
        const org = repository.findByExternalId(externalId);
        expect(org).toBeDefined();
        expect(org?.name).toBe('New Organization');
        
        // Verify owner membership was created
        const members = repository.findMembers(externalId);
        expect(members).toHaveLength(1);
        expect(members[0].user_id).toBe(mockUserId);
        expect(members[0].role).toBe('owner');
      });

      test('should auto-increment internal ID', () => {
        const newOrgData1: CreateOrganizationData = {
          external_id: 'org-1',
          name: 'Org 1',
          slug: 'org-1',
          plan: 'free',
          settings: '{}',
          ownerId: mockUserId
        };

        const newOrgData2: CreateOrganizationData = {
          external_id: 'org-2',
          name: 'Org 2',
          slug: 'org-2',
          plan: 'free',
          settings: '{}',
          ownerId: mockUserId
        };

        repository.insert(newOrgData1);
        repository.insert(newOrgData2);

        const org1 = repository.findByExternalId('org-1');
        const org2 = repository.findByExternalId('org-2');

        expect(org2?.id).toBeGreaterThan(org1?.id || 0);
      });
    });

    describe('update', () => {
      test('should update organization fields', () => {
        const updates: UpdateOrganizationData = {
          name: 'Updated Organization Name',
          plan: 'team'
        };

        const success = repository.update(mockOrgExternalId, updates);
        
        expect(success).toBe(true);
        
        const org = repository.findByExternalId(mockOrgExternalId);
        expect(org?.name).toBe('Updated Organization Name');
        expect(org?.plan).toBe('team');
      });

      test('should handle JSON settings update', () => {
        const updates: UpdateOrganizationData = {
          settings: { allowedDomains: ['example.com'] }
        };

        const success = repository.update(mockOrgExternalId, updates);
        
        expect(success).toBe(true);
        
        const org = repository.findByExternalId(mockOrgExternalId);
        const settings = JSON.parse(org?.settings || '{}');
        expect(settings.allowedDomains).toEqual(['example.com']);
      });

      test('should return false for non-existent organization', () => {
        const updates: UpdateOrganizationData = { name: 'New Name' };
        const success = repository.update('non-existent-org', updates);
        
        expect(success).toBe(false);
      });

      test('should return false for empty updates', () => {
        const success = repository.update(mockOrgExternalId, {});
        
        expect(success).toBe(false);
      });
    });

    describe('delete', () => {
      test('should delete organization', () => {
        const success = repository.delete(mockOrgExternalId);
        
        expect(success).toBe(true);
        
        const org = repository.findByExternalId(mockOrgExternalId);
        expect(org).toBeNull();
      });

      test('should return false for non-existent organization', () => {
        const success = repository.delete('non-existent-org');
        
        expect(success).toBe(false);
      });
    });

    describe('generateUniqueSlug', () => {
      test('should return base slug if unique', () => {
        const slug = repository.generateUniqueSlug('unique-slug');
        
        expect(slug).toBe('unique-slug');
      });

      test('should append counter for duplicate slugs', () => {
        // Create organization with 'test-slug'
        const orgData: CreateOrganizationData = {
          external_id: 'test-org',
          name: 'Test Org',
          slug: 'test-slug',
          plan: 'free',
          settings: '{}',
          ownerId: mockUserId
        };
        repository.insert(orgData);

        const uniqueSlug = repository.generateUniqueSlug('test-slug');
        
        expect(uniqueSlug).toBe('test-slug-1');
      });
    });
  });

  describe('Organization Query Operations', () => {
    describe('findForUser', () => {
      test('should return organizations for user with roles', () => {
        const organizations = repository.findForUser(mockUserId);
        
        expect(organizations).toBeInstanceOf(Array);
        expect(organizations.length).toBeGreaterThan(0);
        expect(organizations[0]).toHaveProperty('user_role');
        expect(organizations[0]).toHaveProperty('member_count');
        expect(organizations[0].user_id).toBeUndefined(); // Should not include user_id in result
      });

      test('should return empty array for user with no memberships', () => {
        const organizations = repository.findForUser('user-without-memberships');
        
        expect(organizations).toHaveLength(0);
      });

      test('should include correct user role', () => {
        const organizations = repository.findForUser(mockUserId);
        const org = organizations.find(o => o.external_id === mockOrgExternalId);
        
        expect(org?.user_role).toBe('owner');
      });
    });

    describe('checkUserMembership', () => {
      test('should return true for member', () => {
        const isMember = repository.checkUserMembership(mockOrgExternalId, mockUserId);
        
        expect(isMember).toBe(true);
      });

      test('should return false for non-member', () => {
        const isMember = repository.checkUserMembership(mockOrgExternalId, 'non-member-user');
        
        expect(isMember).toBe(false);
      });

      test('should return false for non-existent organization', () => {
        const isMember = repository.checkUserMembership('non-existent-org', mockUserId);
        
        expect(isMember).toBe(false);
      });
    });

    describe('getUserRole', () => {
      test('should return user role', () => {
        const role = repository.getUserRole(mockOrgExternalId, mockUserId);
        
        expect(role).toBe('owner');
      });

      test('should return null for non-member', () => {
        const role = repository.getUserRole(mockOrgExternalId, 'non-member-user');
        
        expect(role).toBeNull();
      });
    });

    describe('getInternalId', () => {
      test('should return internal ID for external ID', () => {
        const internalId = repository.getInternalId(mockOrgExternalId);
        
        expect(internalId).toBe(mockOrgInternalId);
      });

      test('should return null for non-existent external ID', () => {
        const internalId = repository.getInternalId('non-existent-org');
        
        expect(internalId).toBeNull();
      });
    });
  });

  describe('Member Management Operations', () => {
    describe('findMembers', () => {
      test('should return all members for organization', () => {
        const members = repository.findMembers(mockOrgExternalId);
        
        expect(members).toBeInstanceOf(Array);
        expect(members.length).toBeGreaterThan(0);
        expect(members[0]).toHaveProperty('user_id');
        expect(members[0]).toHaveProperty('role');
        expect(members[0]).toHaveProperty('joined_at');
      });

      test('should include user details when available', () => {
        const members = repository.findMembers(mockOrgExternalId);
        const member = members.find(m => m.user_id === mockUserId);
        
        expect(member?.user_email).toBeDefined();
        expect(member?.user_name).toBeDefined();
      });

      test('should order by joined_at DESC', () => {
        const members = repository.findMembers(mockOrgExternalId);
        
        for (let i = 1; i < members.length; i++) {
          expect(members[i-1].joined_at >= members[i].joined_at).toBe(true);
        }
      });
    });

    describe('insertMember', () => {
      test('should add new member', () => {
        const newUserId = 'new-user-id';
        const success = repository.insertMember(mockOrgExternalId, newUserId, 'member', mockUserId);
        
        expect(success).toBe(true);
        
        const member = repository.findMember(mockOrgExternalId, newUserId);
        expect(member).toBeDefined();
        expect(member?.role).toBe('member');
        expect(member?.invited_by).toBe(mockUserId);
      });

      test('should return false for existing member', () => {
        const success = repository.insertMember(mockOrgExternalId, mockUserId, 'admin');
        
        expect(success).toBe(false);
      });

      test('should return false for non-existent organization', () => {
        const success = repository.insertMember('non-existent-org', 'new-user', 'member');
        
        expect(success).toBe(false);
      });
    });

    describe('updateMemberRole', () => {
      test('should update member role', () => {
        // Add a member first
        const memberId = 'member-to-update';
        repository.insertMember(mockOrgExternalId, memberId, 'member');
        
        const success = repository.updateMemberRole(mockOrgExternalId, memberId, 'admin');
        
        expect(success).toBe(true);
        
        const member = repository.findMember(mockOrgExternalId, memberId);
        expect(member?.role).toBe('admin');
      });

      test('should not update owner role', () => {
        const success = repository.updateMemberRole(mockOrgExternalId, mockUserId, 'admin');
        
        expect(success).toBe(false);
        
        const member = repository.findMember(mockOrgExternalId, mockUserId);
        expect(member?.role).toBe('owner');
      });

      test('should return false for non-existent member', () => {
        const success = repository.updateMemberRole(mockOrgExternalId, 'non-existent-user', 'admin');
        
        expect(success).toBe(false);
      });
    });

    describe('removeMember', () => {
      test('should remove member', () => {
        // Add a member first
        const memberId = 'member-to-remove';
        repository.insertMember(mockOrgExternalId, memberId, 'member');
        
        const success = repository.removeMember(mockOrgExternalId, memberId);
        
        expect(success).toBe(true);
        
        const member = repository.findMember(mockOrgExternalId, memberId);
        expect(member).toBeNull();
      });

      test('should not remove owner', () => {
        const success = repository.removeMember(mockOrgExternalId, mockUserId);
        
        expect(success).toBe(false);
        
        const member = repository.findMember(mockOrgExternalId, mockUserId);
        expect(member).toBeDefined();
      });
    });
  });

  describe('Invitation Management Operations', () => {
    let mockInvitationData: CreateInvitationData;

    beforeEach(() => {
      mockInvitationData = {
        external_id: 'invitation-ext-id',
        organization_id: mockOrgInternalId,
        email: '<EMAIL>',
        role: 'member',
        invited_by: mockUserId,
        token: 'invitation-token-123',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
      };
    });

    describe('insertInvitation', () => {
      test('should create invitation', () => {
        const externalId = repository.insertInvitation(mockInvitationData);
        
        expect(externalId).toBe('invitation-ext-id');
        
        const invitation = repository.findInvitation('invitation-token-123');
        expect(invitation).toBeDefined();
        expect(invitation?.email).toBe('<EMAIL>');
        expect(invitation?.role).toBe('member');
      });
    });

    describe('findInvitation', () => {
      test('should return invitation by token', () => {
        repository.insertInvitation(mockInvitationData);
        
        const invitation = repository.findInvitation('invitation-token-123');
        
        expect(invitation).toBeDefined();
        expect(invitation?.email).toBe('<EMAIL>');
        expect(invitation?.accepted_at).toBeNull();
      });

      test('should return null for non-existent token', () => {
        const invitation = repository.findInvitation('non-existent-token');
        
        expect(invitation).toBeNull();
      });

      test('should not return accepted invitations', () => {
        repository.insertInvitation(mockInvitationData);
        repository.acceptInvitation('1'); // Accept the invitation (ID would be 1)
        
        const invitation = repository.findInvitation('invitation-token-123');
        
        expect(invitation).toBeNull();
      });
    });

    describe('findPendingInvitations', () => {
      test('should return pending invitations for organization', () => {
        repository.insertInvitation(mockInvitationData);
        
        const invitations = repository.findPendingInvitations(mockOrgExternalId);
        
        expect(invitations).toHaveLength(1);
        expect(invitations[0].email).toBe('<EMAIL>');
        expect(invitations[0].accepted_at).toBeNull();
      });

      test('should not return expired invitations', () => {
        // Create expired invitation
        const expiredInvitation = {
          ...mockInvitationData,
          external_id: 'expired-invitation',
          token: 'expired-token',
          expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
        };
        repository.insertInvitation(expiredInvitation);
        
        const invitations = repository.findPendingInvitations(mockOrgExternalId);
        
        expect(invitations).toHaveLength(0);
      });
    });

    describe('acceptInvitation', () => {
      test('should mark invitation as accepted', () => {
        repository.insertInvitation(mockInvitationData);
        
        const success = repository.acceptInvitation('1'); // Assuming auto-increment ID
        
        expect(success).toBe(true);
        
        // Should no longer appear in pending invitations
        const pendingInvitations = repository.findPendingInvitations(mockOrgExternalId);
        expect(pendingInvitations).toHaveLength(0);
      });
    });

    describe('revokeInvitation', () => {
      test('should delete invitation', () => {
        repository.insertInvitation(mockInvitationData);
        
        const success = repository.revokeInvitation(mockOrgExternalId, 'invitation-ext-id');
        
        expect(success).toBe(true);
        
        const invitation = repository.findInvitation('invitation-token-123');
        expect(invitation).toBeNull();
      });

      test('should return false for non-existent invitation', () => {
        const success = repository.revokeInvitation(mockOrgExternalId, 'non-existent-invitation');
        
        expect(success).toBe(false);
      });
    });
  });

  describe('User Profile Operations', () => {
    describe('syncUserProfile', () => {
      test('should insert new user', () => {
        const userData: CreateUserData = {
          id: 'new-user-id',
          email: '<EMAIL>',
          name: 'New User',
          display_name: 'New User Display'
        };

        const success = repository.syncUserProfile(userData);
        
        expect(success).toBe(true);
        
        const user = repository.findUser('new-user-id');
        expect(user).toBeDefined();
        expect(user?.email).toBe('<EMAIL>');
        expect(user?.name).toBe('New User');
      });

      test('should update existing user', () => {
        const userData: CreateUserData = {
          id: mockUserId,
          email: '<EMAIL>',
          name: 'Updated Name'
        };

        const success = repository.syncUserProfile(userData);
        
        expect(success).toBe(true);
        
        const user = repository.findUser(mockUserId);
        expect(user?.email).toBe('<EMAIL>');
        expect(user?.name).toBe('Updated Name');
      });
    });

    describe('cleanupPlaceholderEmails', () => {
      test('should update placeholder emails', () => {
        // Insert user with placeholder email
        const userData: CreateUserData = {
          id: 'placeholder-user',
          email: '<EMAIL>'
        };
        repository.insertUser(userData);
        
        const success = repository.cleanupPlaceholderEmails('placeholder-user', '<EMAIL>');
        
        expect(success).toBe(true);
        
        const user = repository.findUser('placeholder-user');
        expect(user?.email).toBe('<EMAIL>');
      });

      test('should not update real emails', () => {
        const success = repository.cleanupPlaceholderEmails(mockUserId, '<EMAIL>');
        
        expect(success).toBe(false); // No changes made
        
        const user = repository.findUser(mockUserId);
        expect(user?.email).toBe('<EMAIL>'); // Original email preserved
      });
    });
  });

  describe('Bulk Operations', () => {
    describe('getOrganizationDataCounts', () => {
      test('should return data counts for organization', () => {
        const counts = repository.getOrganizationDataCounts(mockOrgExternalId);
        
        expect(counts).toHaveProperty('colors');
        expect(counts).toHaveProperty('products');
        expect(counts).toHaveProperty('datasheets');
        expect(counts).toHaveProperty('relationships');
        expect(typeof counts.colors).toBe('number');
        expect(typeof counts.products).toBe('number');
      });
    });

    describe('deleteOrganizationCascade', () => {
      test('should prevent deletion without forceCascade when data exists', () => {
        // Add some test data
        seedOrganizationData(db, mockOrgExternalId);
        
        expect(() => {
          repository.deleteOrganizationCascade(mockOrgExternalId, false);
        }).toThrow('Cannot delete organization with existing data');
      });

      test('should perform cascade deletion when forceCascade is true', () => {
        // Add some test data
        seedOrganizationData(db, mockOrgExternalId);
        
        const result = repository.deleteOrganizationCascade(mockOrgExternalId, true);
        
        expect(result.success).toBe(true);
        expect(result.deletedData).toHaveProperty('colors');
        expect(result.deletedData).toHaveProperty('products');
        expect(result.deletedData).toHaveProperty('members');
        
        // Organization should be deleted
        const org = repository.findByExternalId(mockOrgExternalId);
        expect(org).toBeNull();
      });
    });
  });
});

/**
 * Set up test database schema
 */
function setupTestSchema(db: Database.Database): void {
  // Organizations table
  db.exec(`
    CREATE TABLE organizations (
      id INTEGER PRIMARY KEY,
      external_id TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      plan TEXT DEFAULT 'free',
      settings TEXT DEFAULT '{}',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Users table
  db.exec(`
    CREATE TABLE users (
      id TEXT PRIMARY KEY,
      email TEXT NOT NULL,
      name TEXT,
      display_name TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Organization members table
  db.exec(`
    CREATE TABLE organization_members (
      organization_id INTEGER,
      user_id TEXT,
      role TEXT NOT NULL,
      joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      invited_by TEXT,
      PRIMARY KEY (organization_id, user_id),
      FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
  `);

  // Organization invitations table
  db.exec(`
    CREATE TABLE organization_invitations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      external_id TEXT UNIQUE NOT NULL,
      organization_id INTEGER NOT NULL,
      email TEXT NOT NULL,
      role TEXT NOT NULL,
      token TEXT UNIQUE NOT NULL,
      invited_by TEXT NOT NULL,
      expires_at DATETIME NOT NULL,
      accepted_at DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
      FOREIGN KEY (invited_by) REFERENCES users(id),
      UNIQUE(organization_id, email)
    );
  `);

  // Test tables for cascade deletion
  db.exec(`
    CREATE TABLE colors (
      id INTEGER PRIMARY KEY,
      external_id TEXT UNIQUE NOT NULL,
      organization_id TEXT NOT NULL,
      code TEXT NOT NULL,
      hex TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      deleted_at DATETIME
    );

    CREATE TABLE products (
      id INTEGER PRIMARY KEY,
      external_id TEXT UNIQUE NOT NULL,
      organization_id TEXT NOT NULL,
      name TEXT NOT NULL,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      deleted_at DATETIME
    );

    CREATE TABLE product_colors (
      product_id INTEGER,
      color_id INTEGER,
      organization_id TEXT NOT NULL,
      PRIMARY KEY (product_id, color_id)
    );

    CREATE TABLE datasheets (
      id INTEGER PRIMARY KEY,
      product_id INTEGER,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
  `);
}

/**
 * Seed test data
 */
function seedTestData(
  db: Database.Database, 
  userId: string, 
  orgExternalId: string, 
  orgInternalId: number
): void {
  // Insert test user
  db.prepare(`
    INSERT INTO users (id, email, name, display_name)
    VALUES (?, ?, ?, ?)
  `).run(userId, '<EMAIL>', 'Test User', 'Test Display Name');

  // Insert test organization
  db.prepare(`
    INSERT INTO organizations (id, external_id, name, slug, plan, settings)
    VALUES (?, ?, ?, ?, ?, ?)
  `).run(orgInternalId, orgExternalId, 'Test Organization', 'test-org', 'free', '{}');

  // Insert organization membership
  db.prepare(`
    INSERT INTO organization_members (organization_id, user_id, role)
    VALUES (?, ?, ?)
  `).run(orgInternalId, userId, 'owner');
}

/**
 * Seed organization data for cascade deletion tests
 */
function seedOrganizationData(db: Database.Database, orgExternalId: string): void {
  // Add test colors
  db.prepare(`
    INSERT INTO colors (external_id, organization_id, code, hex)
    VALUES (?, ?, ?, ?)
  `).run('color-1', orgExternalId, 'RED-001', '#FF0000');

  // Add test products
  db.prepare(`
    INSERT INTO products (external_id, organization_id, name)
    VALUES (?, ?, ?)
  `).run('product-1', orgExternalId, 'Test Product');

  // Add test relationships
  db.prepare(`
    INSERT INTO product_colors (product_id, color_id, organization_id)
    VALUES (?, ?, ?)
  `).run(1, 1, orgExternalId);

  // Add test datasheets
  db.prepare(`
    INSERT INTO datasheets (product_id)
    VALUES (?)
  `).run(1);
}