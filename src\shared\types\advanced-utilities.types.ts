/**
 * @file advanced-utilities.types.ts
 * @description Advanced TypeScript utility types for the application
 */

/**
 * Brand type utility - creates nominal types
 */
export type Brand<T, TBrand extends string> = T & { readonly __brand: TBrand };

/**
 * Generic validation result type
 */
export type ValidationResult<T = unknown> = {
  isValid: boolean;
  value?: T;
  errors?: string[];
};

/**
 * Utility type to extract the branded type
 */
export type UnBrand<T> = T extends Brand<infer U, any> ? U : T;

/**
 * Utility type to check if a type is branded
 */
export type IsBranded<T> = T extends Brand<any, any> ? true : false;

/**
 * Deep readonly utility type
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * Deep partial utility type
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * Deep required utility type
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

// NonNullable is a built-in TypeScript utility type

/**
 * Strict omit utility type
 */
export type StrictOmit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

/**
 * Strict pick utility type
 */
export type StrictPick<T, K extends keyof T> = Pick<T, K>;

/**
 * Extract keys of specific type
 */
export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

/**
 * Union to intersection utility type
 */
export type UnionToIntersection<U> = (
  U extends any ? (k: U) => void : never
) extends (k: infer I) => void
  ? I
  : never;

/**
 * Path utility types
 */
export type Path<T> = T extends object
  ? {
      [K in keyof T]: K extends string ? K | `${K}.${Path<T[K]>}` : never;
    }[keyof T]
  : never;

export type PathValue<
  T,
  P extends Path<T>,
> = P extends `${infer K}.${infer Rest}`
  ? K extends keyof T
    ? Rest extends Path<T[K]>
      ? PathValue<T[K], Rest>
      : never
    : never
  : P extends keyof T
    ? T[P]
    : never;

/**
 * Extract functions and properties
 */
export type ExtractFunctions<T> = {
  [K in keyof T]: T[K] extends (...args: any[]) => any ? T[K] : never;
};

export type ExtractProperties<T> = {
  [K in keyof T]: T[K] extends (...args: any[]) => any ? never : T[K];
};

/**
 * UUID type
 */
export type UUID = Brand<string, 'UUID'>;

/**
 * Organization ID type
 */
export type OrganizationId = Brand<string, 'OrganizationId'>;

/**
 * User ID type
 */
export type UserId = Brand<string, 'UserId'>;

/**
 * API response type
 */
export type ApiResponse<T = unknown> = {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
};

/**
 * Utility type for strict object keys
 */
export type StrictKeys<T> = keyof T;

/**
 * Utility type for optional keys
 */
export type OptionalKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
}[keyof T];

/**
 * Utility type for required keys
 */
export type RequiredKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];
