import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';
import { OrganizationService } from './organization.service';
import { OrganizationMemberService } from '../../services/organization/organization-member.service';
import { UserSyncService } from '../../services/organization/user-sync.service';
import { SupabaseClient } from '@supabase/supabase-js';

// Mock Supabase client
const mockSupabaseClient = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  insert: vi.fn().mockReturnThis(),
  update: vi.fn().mockReturnThis(),
  delete: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  single: vi.fn().mockResolvedValue({ data: null, error: null }),
} as any;

// Mock service locator
vi.mock('../../services/service-locator', () => ({
  getZohoEmailService: vi.fn().mockReturnValue({
    sendInvitationEmail: vi.fn().mockResolvedValue(true),
  }),
}));

describe('OrganizationService Integration Tests', () => {
  let db: Database.Database;
  let organizationService: OrganizationService;
  let memberService: OrganizationMemberService;
  let userSyncService: UserSyncService;

  beforeEach(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');

    // Create test schema
    db.exec(`
      CREATE TABLE organizations (
        id INTEGER PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        slug TEXT NOT NULL,
        plan TEXT DEFAULT 'free',
        settings TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE users (
        id INTEGER PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        email TEXT NOT NULL,
        name TEXT,
        display_name TEXT,
        avatar_url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE organization_members (
        id INTEGER PRIMARY KEY,
        organization_id INTEGER NOT NULL,
        user_id TEXT NOT NULL,
        role TEXT NOT NULL,
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        invited_by TEXT,
        FOREIGN KEY (organization_id) REFERENCES organizations (id),
        UNIQUE(organization_id, user_id)
      );

      CREATE TABLE organization_invitations (
        id INTEGER PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        organization_id INTEGER NOT NULL,
        email TEXT NOT NULL,
        role TEXT NOT NULL,
        token TEXT UNIQUE NOT NULL,
        invited_by TEXT NOT NULL,
        expires_at DATETIME NOT NULL,
        accepted_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (organization_id) REFERENCES organizations (id)
      );
    `);

    // Initialize services with dependency injection
    memberService = new OrganizationMemberService(db, mockSupabaseClient);
    userSyncService = new UserSyncService(db, {} as any, mockSupabaseClient);
    organizationService = new OrganizationService(
      db,
      memberService,
      userSyncService
    );
  });

  afterEach(() => {
    db.close();
    vi.clearAllMocks();
  });

  describe('Organization CRUD Operations', () => {
    it('should create organization and delegate member operations', async () => {
      const userId = 'user-123';
      const orgName = 'Test Organization';

      // Create test user first
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(userId, '<EMAIL>', 'Test User');

      // Create organization
      const org = await organizationService.createOrganization(orgName, userId);

      expect(org).toBeDefined();
      expect(org.name).toBe(orgName);
      expect(org.id).toBeDefined();

      // Test member operations delegation
      const members = await organizationService.getMembers(org.id);
      expect(Array.isArray(members)).toBe(true);

      // Test user role delegation
      const userRole = await organizationService.getUserRole(org.id, userId);
      expect(userRole).toBeDefined();

      // Test member check delegation
      const isMember = await organizationService.isMember(org.id, userId);
      expect(isMember).toBe(true);
    });

    it('should handle organization updates', async () => {
      const userId = 'user-123';
      const orgName = 'Original Name';

      // Create test user
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(userId, '<EMAIL>', 'Test User');

      // Create organization
      const org = await organizationService.createOrganization(orgName, userId);

      // Update organization
      const updatedOrg = await organizationService.updateOrganization(org.id, {
        name: 'Updated Name',
        plan: 'premium',
      });

      expect(updatedOrg).toBeDefined();
      expect(updatedOrg!.name).toBe('Updated Name');
      expect(updatedOrg!.plan).toBe('premium');
    });

    it('should handle organization deletion with proper permissions', async () => {
      const ownerId = 'owner-123';
      const orgName = 'Test Organization';

      // Create test user
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(ownerId, '<EMAIL>', 'Owner User');

      // Create organization
      const org = await organizationService.createOrganization(
        orgName,
        ownerId
      );

      // Delete organization
      const deleted = await organizationService.deleteOrganization(
        org.id,
        ownerId,
        true
      );
      expect(deleted).toBe(true);

      // Verify organization is deleted
      const deletedOrg = await organizationService.getOrganization(org.id);
      expect(deletedOrg).toBeNull();
    });
  });

  describe('Member Management Delegation', () => {
    let orgId: string;
    const ownerId = 'owner-123';
    const memberId = 'member-456';

    beforeEach(async () => {
      // Create test users
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(ownerId, '<EMAIL>', 'Owner User');

      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(memberId, '<EMAIL>', 'Member User');

      // Create organization
      const org = await organizationService.createOrganization(
        'Test Org',
        ownerId
      );
      orgId = org.id;
    });

    it('should delegate member addition through orchestrator', async () => {
      // Mock member service response
      vi.spyOn(memberService, 'addMember').mockResolvedValue({
        success: true,
        member: {
          organization_id: orgId,
          user_id: memberId,
          role: 'member',
          joined_at: new Date().toISOString(),
          user: {
            id: memberId,
            email: '<EMAIL>',
            name: 'Member User',
          },
        },
      });

      const member = await organizationService.addMember(
        orgId,
        memberId,
        'member',
        ownerId
      );

      expect(member).toBeDefined();
      expect(member.user_id).toBe(memberId);
      expect(member.role).toBe('member');
      expect(memberService.addMember).toHaveBeenCalledWith({
        organizationId: orgId,
        userId: memberId,
        role: 'member',
        invitedBy: ownerId,
      });
    });

    it('should delegate member role updates through orchestrator', async () => {
      // Mock member service response
      vi.spyOn(memberService, 'updateMemberRole').mockResolvedValue({
        success: true,
        member: {
          organization_id: orgId,
          user_id: memberId,
          role: 'admin',
          joined_at: new Date().toISOString(),
          user: {
            id: memberId,
            email: '<EMAIL>',
            name: 'Member User',
          },
        },
      });

      const updatedMember = await organizationService.updateMemberRole(
        orgId,
        memberId,
        'admin'
      );

      expect(updatedMember).toBeDefined();
      expect(updatedMember!.role).toBe('admin');
      expect(memberService.updateMemberRole).toHaveBeenCalledWith({
        organizationId: orgId,
        userId: memberId,
        newRole: 'admin',
      });
    });

    it('should delegate member removal through orchestrator', async () => {
      // Mock member service response
      vi.spyOn(memberService, 'removeMember').mockResolvedValue({
        success: true,
      });

      const removed = await organizationService.removeMember(orgId, memberId);

      expect(removed).toBe(true);
      expect(memberService.removeMember).toHaveBeenCalledWith(orgId, memberId);
    });
  });

  describe('Invitation Management Delegation', () => {
    let orgId: string;
    const ownerId = 'owner-123';
    const inviteEmail = '<EMAIL>';

    beforeEach(async () => {
      // Create test user
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(ownerId, '<EMAIL>', 'Owner User');

      // Create organization
      const org = await organizationService.createOrganization(
        'Test Org',
        ownerId
      );
      orgId = org.id;
    });

    it('should delegate invitation creation through orchestrator', async () => {
      const mockInvitation = {
        id: 'invitation-123',
        email: inviteEmail,
        role: 'member',
        token: 'token-123',
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      };

      // Mock member service response
      vi.spyOn(memberService, 'createInvitation').mockResolvedValue({
        success: true,
        invitation: mockInvitation,
      });

      const result = await organizationService.inviteMember(
        orgId,
        inviteEmail,
        'member',
        ownerId
      );

      expect(result.success).toBe(true);
      expect(result.invitation).toEqual(mockInvitation);
      expect(memberService.createInvitation).toHaveBeenCalledWith({
        organizationId: orgId,
        email: inviteEmail,
        role: 'member',
        invitedBy: ownerId,
      });
    });

    it('should delegate invitation acceptance through orchestrator', async () => {
      const token = 'invitation-token-123';
      const userId = 'user-789';

      // Mock member service response
      vi.spyOn(memberService, 'acceptInvitation').mockResolvedValue({
        success: true,
        organizationId: orgId,
      });

      const result = await organizationService.acceptInvitation(token, userId);

      expect(result.success).toBe(true);
      expect(memberService.acceptInvitation).toHaveBeenCalledWith(
        token,
        userId
      );
    });

    it('should delegate pending invitations retrieval through orchestrator', async () => {
      const mockInvitations = [
        {
          id: 'invitation-1',
          email: '<EMAIL>',
          role: 'member',
          invitedBy: { id: ownerId, name: 'Owner User' },
          expiresAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
        },
      ];

      // Mock member service response
      vi.spyOn(memberService, 'getPendingInvitations').mockResolvedValue(
        mockInvitations
      );

      const invitations =
        await organizationService.getPendingInvitations(orgId);

      expect(invitations).toEqual(mockInvitations);
      expect(memberService.getPendingInvitations).toHaveBeenCalledWith(orgId);
    });

    it('should delegate invitation revocation through orchestrator', async () => {
      const invitationId = 'invitation-123';

      // Mock member service response
      vi.spyOn(memberService, 'revokeInvitation').mockResolvedValue({
        success: true,
      });

      const revoked = await organizationService.revokeInvitation(
        orgId,
        invitationId
      );

      expect(revoked).toBe(true);
      expect(memberService.revokeInvitation).toHaveBeenCalledWith(
        orgId,
        invitationId
      );
    });
  });

  describe('Service Integration and Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      const userId = 'user-123';
      const orgName = 'Test Organization';

      // Create test user
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(userId, '<EMAIL>', 'Test User');

      // Create organization
      const org = await organizationService.createOrganization(orgName, userId);

      // Mock member service to throw error
      vi.spyOn(memberService, 'getMembers').mockRejectedValue(
        new Error('Service error')
      );

      // Service should handle the error and not crash
      try {
        await organizationService.getMembers(org.id);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });

    it('should maintain proper dependency injection across operations', async () => {
      const userId = 'user-123';
      const orgName = 'Test Organization';

      // Create test user
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(userId, '<EMAIL>', 'Test User');

      // Create organization
      const org = await organizationService.createOrganization(orgName, userId);

      // Verify all services are properly injected and working
      expect(organizationService).toBeDefined();

      // Test that calls to different methods use the same injected services
      const spy1 = vi.spyOn(memberService, 'getMembers').mockResolvedValue([]);
      const spy2 = vi.spyOn(memberService, 'getMember').mockResolvedValue(null);

      await organizationService.getMembers(org.id);
      await organizationService.getMember(org.id, userId);

      expect(spy1).toHaveBeenCalledWith(org.id);
      expect(spy2).toHaveBeenCalledWith(org.id, userId);
    });
  });

  describe('Data Consistency and Organization Context', () => {
    it('should maintain organization context across delegated operations', async () => {
      const userId = 'user-123';
      const orgName = 'Test Organization';

      // Create test user
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(userId, '<EMAIL>', 'Test User');

      // Create organization
      const org = await organizationService.createOrganization(orgName, userId);

      // Verify organization exists and can be retrieved
      const retrievedOrg = await organizationService.getOrganization(org.id);
      expect(retrievedOrg).toBeDefined();
      expect(retrievedOrg!.id).toBe(org.id);
      expect(retrievedOrg!.name).toBe(orgName);

      // Verify user organizations include the created org
      const userOrgs =
        await organizationService.getOrganizationsForUser(userId);
      expect(userOrgs).toHaveLength(1);
      expect(userOrgs[0].id).toBe(org.id);
    });

    it('should handle multiple organizations per user', async () => {
      const userId = 'user-123';

      // Create test user
      db.prepare(
        `
        INSERT INTO users (external_id, email, name) 
        VALUES (?, ?, ?)
      `
      ).run(userId, '<EMAIL>', 'Test User');

      // Create multiple organizations
      const org1 = await organizationService.createOrganization(
        'Organization 1',
        userId
      );
      const org2 = await organizationService.createOrganization(
        'Organization 2',
        userId
      );

      // Verify both organizations are returned for user
      const userOrgs =
        await organizationService.getOrganizationsForUser(userId);
      expect(userOrgs).toHaveLength(2);

      const orgIds = userOrgs.map(org => org.id);
      expect(orgIds).toContain(org1.id);
      expect(orgIds).toContain(org2.id);
    });
  });
});

/*
 * Integration Test Summary:
 *
 * These tests verify that the OrganizationService orchestrator pattern
 * properly delegates operations to specialized services:
 *
 * 1. Member Management -> OrganizationMemberService
 *    - getMembers, getMember, addMember, updateMemberRole, removeMember
 *    - getUserRole, isMember (derived from getMember)
 *
 * 2. Invitation Management -> OrganizationMemberService
 *    - inviteMember, acceptInvitation, getPendingInvitations, revokeInvitation
 *
 * 3. User Synchronization -> UserSyncService (injected for future use)
 *    - syncUserProfileToLocal, syncMembersFromSupabase, etc.
 *
 * The orchestrator maintains backward compatibility while improving:
 * - Single Responsibility Principle adherence
 * - Testability through dependency injection
 * - Modularity and maintainability
 * - Service separation of concerns
 */
