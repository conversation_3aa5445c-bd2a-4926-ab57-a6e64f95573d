/**
 * @file IPCRouter.ts
 * @description Express.js-style IPC Router for Electron with comprehensive middleware support
 *
 * This router system provides:
 * - Express.js-like route registration with HTTP methods and URL patterns
 * - Parameter extraction from route patterns (/api/colors/:id)
 * - Middleware pipeline execution with proper order
 * - Integration with existing ChromaSync security infrastructure
 * - Performance optimizations for large-scale route handling
 */

import { IpcMainEvent, ipcMain } from 'electron';
import { IPCResponse } from '../../../shared/types/ipc.types';
import {
  createSuccessResponse,
  createErrorResponse,
} from '../../utils/ipc-wrapper';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

/**
 * HTTP methods supported by the router
 */
export type HTTPMethod =
  | 'get'
  | 'post'
  | 'put'
  | 'delete'
  | 'patch'
  | 'head'
  | 'options';

/**
 * Route parameters extracted from URL patterns
 */
export interface IPCRouteParams {
  [key: string]: string;
}

/**
 * Enhanced request object passed through middleware chain
 */
export interface IPCRequest {
  channel: string;
  method: HTTPMethod;
  path: string;
  params: IPCRouteParams;
  query: Record<string, any>;
  body: any;
  event: IpcMainEvent;
  args: any[];
  organizationId?: string;
  metadata: {
    timestamp: number;
    requestId: string;
    route?: IPCRoute;
    duration?: number;
  };
  [key: string]: any; // Allow middleware to add custom properties
}

/**
 * Response helper methods for middleware and handlers
 */
export interface IPCResponseHelper {
  success(data?: any, message?: string): IPCResponse;
  error(error: any, message?: string): IPCResponse;
  validation(errors: any, message?: string): IPCResponse;
  status(code: number): IPCResponseHelper;
  json(data: any): IPCResponse;
}

/**
 * Middleware function signature
 */
export type IPCMiddleware = (
  req: IPCRequest,
  res: IPCResponseHelper,
  next: () => void
) => void | Promise<void>;

/**
 * Route handler function signature
 */
export type IPCHandler = (
  req: IPCRequest,
  res?: IPCResponseHelper
) => Promise<any> | any;

/**
 * Route definition
 */
export interface IPCRoute {
  method: HTTPMethod;
  path: string;
  pattern: RegExp;
  paramNames: string[];
  middleware: IPCMiddleware[];
  handler: IPCHandler;
  options?: {
    requireAuth?: boolean;
    requireOrganization?: boolean;
    rateLimit?: {
      maxRequests: number;
      windowMs: number;
    };
  };
}

/**
 * Route matching result
 */
export interface IPCRouteMatch {
  route: IPCRoute;
  params: IPCRouteParams;
}

/**
 * Router statistics for monitoring
 */
export interface IPCRouterStats {
  totalRoutes: number;
  totalMiddleware: number;
  routesByMethod: Record<HTTPMethod, number>;
  averageMiddlewarePerRoute: number;
}

/**
 * Route group for organizing related routes
 */
export interface IPCRouteGroup {
  get(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void;
  post(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void;
  put(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void;
  delete(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void;
  patch(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void;
  use(middleware: IPCMiddleware): void;
}

// ============================================================================
// IPC ROUTER IMPLEMENTATION
// ============================================================================

export class IPCRouter {
  private routes: IPCRoute[] = [];
  private globalMiddleware: IPCMiddleware[] = [];
  private pathMiddleware: Map<string, IPCMiddleware[]> = new Map();
  private patternCache: Map<string, { pattern: RegExp; paramNames: string[] }> =
    new Map();
  private requestCounter = 0;

  /**
   * Valid HTTP methods
   */
  private static readonly VALID_METHODS: HTTPMethod[] = [
    'get',
    'post',
    'put',
    'delete',
    'patch',
    'head',
    'options',
  ];

  /**
   * Register a route with middleware and handler
   */
  register(
    method: HTTPMethod,
    path: string,
    ...handlers: (IPCMiddleware | IPCHandler)[]
  ): void {
    this.validateRoute(method, path);

    // Separate middleware from handler (last function is handler)
    const allHandlers = handlers.filter(h => typeof h === 'function');
    if (allHandlers.length === 0) {
      throw new Error('Route must have at least one handler');
    }

    const handler = allHandlers.pop() as IPCHandler;
    const middleware = allHandlers as IPCMiddleware[];

    // Check for duplicate route
    const routeKey = `${method}:${path}`;
    if (this.routes.some(r => `${r.method}:${r.path}` === routeKey)) {
      throw new Error(`Route already registered: ${routeKey}`);
    }

    // Compile route pattern
    const { pattern, paramNames } = this.compilePattern(path);

    // Create route
    const route: IPCRoute = {
      method,
      path,
      pattern,
      paramNames,
      middleware,
      handler,
    };

    this.routes.push(route);
  }

  /**
   * Register global or path-specific middleware
   */
  use(path: string, middleware: IPCMiddleware): void {
    if (path === '*') {
      this.globalMiddleware.push(middleware);
    } else {
      const existing = this.pathMiddleware.get(path) || [];
      existing.push(middleware);
      this.pathMiddleware.set(path, existing);
    }
  }

  /**
   * HTTP method shortcuts
   */
  get(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void {
    this.register('get', path, ...handlers);
  }

  post(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void {
    this.register('post', path, ...handlers);
  }

  put(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void {
    this.register('put', path, ...handlers);
  }

  delete(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void {
    this.register('delete', path, ...handlers);
  }

  patch(path: string, ...handlers: (IPCMiddleware | IPCHandler)[]): void {
    this.register('patch', path, ...handlers);
  }

  /**
   * Create a route group with common prefix
   */
  group(basePath: string): IPCRouteGroup {
    const normalizedBase = basePath.endsWith('/')
      ? basePath.slice(0, -1)
      : basePath;

    return {
      get: (path: string, ...handlers: (IPCMiddleware | IPCHandler)[]) => {
        this.get(normalizedBase + path, ...handlers);
      },
      post: (path: string, ...handlers: (IPCMiddleware | IPCHandler)[]) => {
        this.post(normalizedBase + path, ...handlers);
      },
      put: (path: string, ...handlers: (IPCMiddleware | IPCHandler)[]) => {
        this.put(normalizedBase + path, ...handlers);
      },
      delete: (path: string, ...handlers: (IPCMiddleware | IPCHandler)[]) => {
        this.delete(normalizedBase + path, ...handlers);
      },
      patch: (path: string, ...handlers: (IPCMiddleware | IPCHandler)[]) => {
        this.patch(normalizedBase + path, ...handlers);
      },
      use: (middleware: IPCMiddleware) => {
        this.use(`${normalizedBase  }/*`, middleware);
      },
    };
  }

  /**
   * Handle incoming IPC request
   */
  async handle(
    channel: string,
    event: IpcMainEvent,
    ...args: any[]
  ): Promise<IPCResponse> {
    try {
      // Validate sender security
      const senderValidation = this.validateSender(event);
      if (!senderValidation.isValid) {
        return createErrorResponse(
          senderValidation.error,
          'Unauthorized request'
        );
      }

      // Parse channel format
      const { method, path } = this.parseChannel(channel);

      // Find matching route
      const match = this.findMatchingRoute(method, path);
      if (!match) {
        return createErrorResponse(
          'Route not found',
          'The requested endpoint was not found. Please check the URL and try again.'
        );
      }

      // Create request object
      const request = this.createRequest(
        channel,
        method,
        path,
        match.params,
        event,
        args
      );
      request.metadata.route = match.route;

      // Create response helper
      const responseHelper = this.createResponseHelper();

      // Execute middleware chain and handler
      const result = await this.executeMiddlewareChain(
        request,
        responseHelper,
        match.route
      );

      // Handle result
      if (result && typeof result === 'object' && 'success' in result) {
        return result as IPCResponse;
      }

      return createSuccessResponse(result);
    } catch (error) {
      console.error('[IPCRouter] Request handling error:', error);
      return createErrorResponse(
        error,
        'Unable to complete the operation. Please try again or contact support if the issue persists.'
      );
    }
  }

  /**
   * Parse channel format (supports legacy and router formats)
   */
  parseChannel(channel: string): { method: HTTPMethod; path: string } {
    if (!channel || typeof channel !== 'string') {
      return { method: 'get', path: '' };
    }

    // Router format: "get:/api/colors" or "post:/api/colors"
    if (channel.includes(':/')) {
      const [methodPart, pathPart] = channel.split(':', 2);
      const method = methodPart?.toLowerCase() as HTTPMethod;

      if (method && IPCRouter.VALID_METHODS.includes(method)) {
        return { method, path: pathPart || '' };
      }
    }

    // Legacy format: "color:getAll" - treat as GET request
    return { method: 'get', path: channel };
  }

  /**
   * Find matching route for method and path
   */
  matchRoute(channel: string): IPCRouteMatch | null {
    const { method, path } = this.parseChannel(channel);
    return this.findMatchingRoute(method, path);
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Validate route parameters
   */
  private validateRoute(method: HTTPMethod, path: string): void {
    if (!IPCRouter.VALID_METHODS.includes(method)) {
      throw new Error(`Invalid HTTP method: ${method}`);
    }

    if (!path || typeof path !== 'string') {
      throw new Error('Route path cannot be empty');
    }

    if (!path.startsWith('/') && !path.includes(':')) {
      // Allow legacy channel formats temporarily
    }
  }

  /**
   * Compile route pattern to RegExp with parameter extraction
   */
  private compilePattern(path: string): {
    pattern: RegExp;
    paramNames: string[];
  } {
    // Check cache first
    if (this.patternCache.has(path)) {
      return this.patternCache.get(path)!;
    }

    const paramNames: string[] = [];

    // Handle wildcard routes
    if (path.endsWith('/*')) {
      const basePattern = path.slice(0, -2);
      const escapedBase = basePattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const pattern = new RegExp(`^${escapedBase}/.*$`);

      const result = { pattern, paramNames };
      this.patternCache.set(path, result);
      return result;
    }

    // Handle parametric routes
    let regexPattern = path.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Replace :param with capture groups
    regexPattern = regexPattern.replace(
      /:([a-zA-Z_][a-zA-Z0-9_]*)/g,
      (_match, paramName) => {
        paramNames.push(paramName);
        return '([^/]+)';
      }
    );

    const pattern = new RegExp(`^${regexPattern}$`);

    const result = { pattern, paramNames };
    this.patternCache.set(path, result);
    return result;
  }

  /**
   * Find matching route and extract parameters
   */
  private findMatchingRoute(
    method: HTTPMethod,
    path: string
  ): IPCRouteMatch | null {
    for (const route of this.routes) {
      if (route.method !== method) {continue;}

      // Exact match first
      if (route.path === path) {
        return { route, params: {} };
      }

      // Pattern match
      const match = path.match(route.pattern);
      if (match) {
        const params: IPCRouteParams = {};

        // Extract parameters
        for (let i = 0; i < route.paramNames.length; i++) {
          const paramValue = match[i + 1];
          if (paramValue) {
            // URL decode and sanitize parameter
            const paramName = route.paramNames[i];
            if (paramName) {
              params[paramName] = this.sanitizeParameter(
                decodeURIComponent(paramValue)
              );
            }
          }
        }

        return { route, params };
      }
    }

    return null;
  }

  /**
   * Sanitize route parameters to prevent injection attacks
   */
  private sanitizeParameter(value: string): string {
    if (typeof value !== 'string') {
      return String(value);
    }

    // Limit length to prevent DoS
    if (value.length > 50) {
      value = value.substring(0, 50);
    }

    // Remove potentially dangerous characters
    return value.replace(/[<>\"']/g, '');
  }

  /**
   * Validate IPC event sender for security
   */
  private validateSender(event: IpcMainEvent): {
    isValid: boolean;
    error?: string;
  } {
    const sender = event.sender;

    if (!sender || sender.isDestroyed()) {
      return { isValid: false, error: 'Invalid or destroyed sender' };
    }

    const senderOrigin = sender.getURL();

    // Check trusted origins
    if (
      !senderOrigin.startsWith('file://') &&
      !senderOrigin.startsWith('chromasync://')
    ) {
      return { isValid: false, error: 'Unauthorized sender origin' };
    }

    return { isValid: true };
  }

  /**
   * Create request object for middleware chain
   */
  private createRequest(
    channel: string,
    method: HTTPMethod,
    path: string,
    params: IPCRouteParams,
    event: IpcMainEvent,
    args: any[]
  ): IPCRequest {
    this.requestCounter++;

    return {
      channel,
      method,
      path,
      params,
      query: {}, // Could be extracted from path in future
      body: args.length === 1 ? args[0] : args,
      event,
      args,
      metadata: {
        timestamp: Date.now(),
        requestId: `req_${this.requestCounter}_${Date.now()}`,
      },
    };
  }

  /**
   * Create response helper with utility methods
   */
  private createResponseHelper(): IPCResponseHelper {
    const helper: IPCResponseHelper = {
      success: (data?: any, message?: string) =>
        createSuccessResponse(data, message),
      error: (error: any, message?: string) =>
        createErrorResponse(error, message),
      validation: (errors: any, message?: string) =>
        createErrorResponse(
          `Validation failed: ${JSON.stringify(errors)}`,
          message || 'Please check your input and try again.'
        ),
      status: (_code: number) => {
        // Status code handling could be added here
        return helper;
      },
      json: (data: any) => createSuccessResponse(data),
    };

    return helper;
  }

  /**
   * Execute middleware chain and handler
   */
  private async executeMiddlewareChain(
    request: IPCRequest,
    responseHelper: IPCResponseHelper,
    route: IPCRoute
  ): Promise<any> {
    const middlewareChain: IPCMiddleware[] = [];

    // Add global middleware
    middlewareChain.push(...this.globalMiddleware);

    // Add path-specific middleware
    for (const [pathPattern, middleware] of this.pathMiddleware.entries()) {
      if (this.matchesPathPattern(request.path, pathPattern)) {
        middlewareChain.push(...middleware);
      }
    }

    // Add route-specific middleware
    middlewareChain.push(...route.middleware);

    let currentIndex = 0;

    const next = async (): Promise<void> => {
      if (currentIndex < middlewareChain.length) {
        const middleware = middlewareChain[currentIndex++];
        if (middleware) {
          await middleware(request, responseHelper, next);
        }
      }
    };

    // Execute middleware chain
    await next();

    // Execute handler
    const result = await route.handler(request, responseHelper);
    return result;
  }

  /**
   * Check if path matches a path pattern (supports wildcards)
   */
  private matchesPathPattern(path: string, pattern: string): boolean {
    if (pattern === '*') {return true;}
    if (pattern.endsWith('/*')) {
      const basePattern = pattern.slice(0, -2);
      return path.startsWith(basePattern);
    }
    return path === pattern;
  }

  // ============================================================================
  // PUBLIC INSPECTION METHODS
  // ============================================================================

  /**
   * Get all registered routes
   */
  getRoutes(): IPCRoute[] {
    return [...this.routes];
  }

  /**
   * Get global middleware
   */
  getGlobalMiddleware(): IPCMiddleware[] {
    return [...this.globalMiddleware];
  }

  /**
   * Get path-specific middleware
   */
  getPathMiddleware(): Map<string, IPCMiddleware[]> {
    return new Map(this.pathMiddleware);
  }

  /**
   * Get router statistics
   */
  getStats(): IPCRouterStats {
    const routesByMethod = IPCRouter.VALID_METHODS.reduce(
      (acc, method) => {
        acc[method] = this.routes.filter(r => r.method === method).length;
        return acc;
      },
      {} as Record<HTTPMethod, number>
    );

    const totalMiddleware =
      this.globalMiddleware.length +
      Array.from(this.pathMiddleware.values()).reduce(
        (sum, mw) => sum + mw.length,
        0
      ) +
      this.routes.reduce((sum, route) => sum + route.middleware.length, 0);

    return {
      totalRoutes: this.routes.length,
      totalMiddleware,
      routesByMethod,
      averageMiddlewarePerRoute:
        this.routes.length > 0 ? totalMiddleware / this.routes.length : 0,
    };
  }

  /**
   * Register router with Electron ipcMain
   */
  attachToIpcMain(): void {
    // Override ipcMain.handle to intercept all IPC calls
    const originalHandle = ipcMain.handle.bind(ipcMain);

    ipcMain.handle = (channel: string, listener: any) => {
      // Check if this is a router-managed channel
      const match = this.matchRoute(channel);
      if (match) {
        // Use router handler instead
        return originalHandle(channel, (event, ...args) =>
          this.handle(channel, event as IpcMainEvent, ...args)
        );
      }

      // Use original handler for non-router channels
      return originalHandle(channel, listener);
    };
  }
}

/**
 * Factory function to create a new IPCRouter instance
 */
export function createIPCRouter(): IPCRouter {
  return new IPCRouter();
}
