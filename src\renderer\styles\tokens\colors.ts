/**
 * @file colors.ts
 * @description Color tokens for the design system
 * 
 * This file defines all color tokens used throughout the application.
 * Colors are organized into semantic categories:
 * - brand: Core brand colors representing the application's identity
 * - ui: Interface colors for backgrounds, text, borders, etc.
 * - feedback: Colors that communicate status and feedback to users
 * 
 * @example
 * // Access colors in component via useTokens hook
 * const { colors } = useTokens();
 * const primaryBrand = colors.brand.primary;
 * 
 * // Use as CSS variables in Tailwind classes
 * <div className="bg-[var(--color-brand-primary)]">...</div>
 */

import { ColorTokens } from './types';

/**
 * Color token definitions for the application
 */
export const colors: ColorTokens = {
  /**
   * Brand colors - Core palette that represents the application's identity
   */
  brand: {
    /** Primary brand color (Deep Blue) - used for key CTAs and brand identification */
    primary: '#1E40AF', // Deep Blue - Professional color management standard
    /** Secondary brand color (Hover Blue) - used for interactive elements and accents */
    secondary: '#1D4ED8', // Hover Blue
    /** Accent brand color - used for highlights and special emphasis */
    accent: '#3B82F6', // Light Blue accent
  },
  
  /**
   * UI colors - Apple-inspired user interface colors
   */
  ui: {
    /**
     * Background colors for different UI layers and surfaces
     */
    background: {
      /** Primary background - main content areas (white in light mode) */
      primary: '#FFFFFF',
      /** Secondary background - subtle distinction for cards, sidebars, etc. */
      secondary: '#F5F5F7', // Light gray like Apple.com
      /** Tertiary background - for inputs, controls, and subtle separation */
      tertiary: '#E9E9EB', // Subtle tertiary background
    },
    /**
     * Foreground colors for text and icons
     */
    foreground: {
      /** Primary text color - highest contrast and most readable */
      primary: '#1D1D1F', // Dark but not pure black (Apple text color)
      /** Secondary text color - less emphasis, descriptions, labels */
      secondary: '#515154', // Apple secondary text
      /** Tertiary text color - subtle hints, placeholders, disabled state */
      tertiary: '#86868B', // Apple tertiary text
      /** Inverse text color - for use on dark backgrounds */
      inverse: '#FFFFFF',
    },
    /**
     * Border colors for separators and boundaries
     */
    border: {
      /** Light border - subtle separation in light mode */
      light: '#E5E5E7',
      /** Medium border - moderate emphasis for separators */
      medium: '#D2D2D7',
      /** Dark border - strong emphasis for boundaries */
      dark: '#86868B',
    },
    /** Focus state color - for keyboard navigation and focus indication */
    focus: '#2563EB', // Focus Blue
  },
  
  /**
   * Feedback colors - Semantic colors for communication states
   */
  feedback: {
    /** Success color - Indicates successful operations */
    success: '#059669', // Professional green
    /** Warning color - Indicates caution or potential issues */
    warning: '#D97706', // Professional amber
    /** Error color - Indicates failures or critical issues */
    error: '#DC2626', // Professional red
    /** Info color - Indicates neutral information */
    info: '#0284C7', // Info blue
  },
}; 