-- Migration: 021_fix_organization_id_consistency.sql
-- Purpose: Fix inconsistent organization_id usage between INTEGER and UUID
-- Issue: Mixed integer/UUID usage causing query failures
-- Date: 2025-06-17
-- Author: ChromaSync Team

-- This migration ensures ALL organization_id columns use TEXT (UUID) format
-- and fixes the product_colors table which incorrectly uses INTEGER

BEGIN TRANSACTION;

-- Check current state
SELECT 
  'colors.organization_id type: ' || type AS colors_org_type,
  'products.organization_id type: ' || (SELECT type FROM pragma_table_info('products') WHERE name = 'organization_id') AS products_org_type,
  'product_colors.organization_id type: ' || (SELECT type FROM pragma_table_info('product_colors') WHERE name = 'organization_id') AS product_colors_org_type
FROM pragma_table_info('colors') 
WHERE name = 'organization_id';

-- Fix product_colors table if it has INTEGER organization_id
-- First check if the column exists and is INTEGER
CREATE TABLE IF NOT EXISTS product_colors_fixed (
  product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  color_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
  display_order INTEGER NOT NULL DEFAULT 0,
  organization_id TEXT NOT NULL, -- Changed from INTEGER to TEXT
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (product_id, color_id)
);

-- Copy data, converting integer org IDs to UUIDs
INSERT OR IGNORE INTO product_colors_fixed (product_id, color_id, display_order, organization_id, added_at)
SELECT 
  pc.product_id,
  pc.color_id,
  pc.display_order,
  CASE 
    -- If organization_id is an integer, convert to UUID
    WHEN CAST(pc.organization_id AS INTEGER) = pc.organization_id THEN
      (SELECT external_id FROM organizations WHERE id = CAST(pc.organization_id AS INTEGER))
    -- Otherwise keep as is (already UUID)
    ELSE pc.organization_id
  END as organization_id,
  pc.added_at
FROM product_colors pc
WHERE EXISTS (SELECT 1 FROM product_colors LIMIT 1);

-- Drop old table and rename new one
DROP TABLE IF EXISTS product_colors;
ALTER TABLE product_colors_fixed RENAME TO product_colors;

-- Recreate indexes
CREATE INDEX IF NOT EXISTS idx_product_colors_product ON product_colors(product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id);

-- Ensure colors table uses UUID organization_id (should already be TEXT)
-- If any integer values remain, convert them
UPDATE colors 
SET organization_id = (
    SELECT external_id 
    FROM organizations 
    WHERE organizations.id = CAST(colors.organization_id AS INTEGER)
)
WHERE organization_id GLOB '[0-9]*' -- Matches pure numeric strings
  AND EXISTS (
    SELECT 1 
    FROM organizations 
    WHERE organizations.id = CAST(colors.organization_id AS INTEGER)
  );

-- Ensure products table uses UUID organization_id
UPDATE products 
SET organization_id = (
    SELECT external_id 
    FROM organizations 
    WHERE organizations.id = CAST(products.organization_id AS INTEGER)
)
WHERE organization_id GLOB '[0-9]*'
  AND EXISTS (
    SELECT 1 
    FROM organizations 
    WHERE organizations.id = CAST(products.organization_id AS INTEGER)
  );

-- Verify migration success
SELECT 
  'Migration complete. Tables using integer org IDs - ' ||
  'Colors: ' || (SELECT COUNT(*) FROM colors WHERE organization_id GLOB '[0-9]*') ||
  ', Products: ' || (SELECT COUNT(*) FROM products WHERE organization_id GLOB '[0-9]*') ||
  ', ProductColors: ' || (SELECT COUNT(*) FROM product_colors WHERE organization_id GLOB '[0-9]*')
  as migration_result;

COMMIT;
