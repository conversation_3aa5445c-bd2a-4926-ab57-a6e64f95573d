/**
 * @file user-operation-coordinator.service.ts
 * @description Coordinates user operations to prevent background sync interference
 * 
 * This service provides a mutex mechanism to ensure that background sync operations
 * don't interfere with active user operations like gradient saves, color updates, etc.
 */

import { EventEmitter } from 'events';

interface UserOperation {
  id: string;
  type: 'color_save' | 'gradient_save' | 'color_update' | 'color_delete' | 'product_save';
  organizationId: string;
  startTime: number;
  maxDuration?: number; // Optional timeout in ms
}

export class UserOperationCoordinatorService extends EventEmitter {
  private activeOperations = new Map<string, UserOperation>();
  // private operationQueue: UserOperation[] = []; // Currently unused
  private readonly DEFAULT_MAX_DURATION = 30000; // 30 seconds max for any operation

  /**
   * Start a user operation and get a mutex lock
   */
  public async startOperation(
    type: UserOperation['type'], 
    organizationId: string, 
    maxDuration?: number
  ): Promise<string> {
    const operationId = `${type}_${organizationId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const operation: UserOperation = {
      id: operationId,
      type,
      organizationId,
      startTime: Date.now(),
      maxDuration: maxDuration || this.DEFAULT_MAX_DURATION
    };

    this.activeOperations.set(operationId, operation);
    
    console.log(`[UserOpCoordinator] 🔒 Started user operation: ${type} (${operationId}) for org: ${organizationId}`);
    
    // Set up automatic timeout cleanup
    setTimeout(() => {
      if (this.activeOperations.has(operationId)) {
        console.warn(`[UserOpCoordinator] ⏰ Operation ${operationId} timed out, force completing`);
        this.completeOperation(operationId);
      }
    }, operation.maxDuration!);

    // Emit event to pause background sync
    this.emit('user_operation_started', { operationId, type, organizationId });
    
    return operationId;
  }

  /**
   * Complete a user operation and release the mutex
   */
  public completeOperation(operationId: string): boolean {
    const operation = this.activeOperations.get(operationId);
    
    if (!operation) {
      console.warn(`[UserOpCoordinator] ⚠️ Attempted to complete non-existent operation: ${operationId}`);
      return false;
    }

    const duration = Date.now() - operation.startTime;
    this.activeOperations.delete(operationId);
    
    console.log(`[UserOpCoordinator] ✅ Completed user operation: ${operation.type} (${operationId}) in ${duration}ms`);
    
    // Emit event to resume background sync
    this.emit('user_operation_completed', { 
      operationId, 
      type: operation.type, 
      organizationId: operation.organizationId,
      duration 
    });
    
    return true;
  }

  /**
   * Check if any user operations are active
   */
  public hasActiveOperations(): boolean {
    return this.activeOperations.size > 0;
  }

  /**
   * Check if user operations are active for a specific organization
   */
  public hasActiveOperationsForOrganization(organizationId: string): boolean {
    for (const operation of this.activeOperations.values()) {
      if (operation.organizationId === organizationId) {
        return true;
      }
    }
    return false;
  }

  /**
   * Check if a specific type of operation is active
   */
  public hasActiveOperationOfType(type: UserOperation['type'], organizationId?: string): boolean {
    for (const operation of this.activeOperations.values()) {
      if (operation.type === type && (!organizationId || operation.organizationId === organizationId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Get all active operations
   */
  public getActiveOperations(): UserOperation[] {
    return Array.from(this.activeOperations.values());
  }

  /**
   * Get active operations for a specific organization
   */
  public getActiveOperationsForOrganization(organizationId: string): UserOperation[] {
    return Array.from(this.activeOperations.values())
      .filter(op => op.organizationId === organizationId);
  }

  /**
   * Force complete all operations (emergency cleanup)
   */
  public forceCompleteAllOperations(): void {
    console.warn(`[UserOpCoordinator] 🚨 Force completing ${this.activeOperations.size} active operations`);
    
    const operationIds = Array.from(this.activeOperations.keys());
    operationIds.forEach(id => this.completeOperation(id));
  }

  /**
   * Wait for all operations to complete (with timeout)
   */
  public async waitForAllOperationsToComplete(timeoutMs: number = 10000): Promise<boolean> {
    if (!this.hasActiveOperations()) {
      return true;
    }

    return new Promise((resolve) => {
      const startTime = Date.now();
      
      const checkInterval = setInterval(() => {
        if (!this.hasActiveOperations()) {
          clearInterval(checkInterval);
          resolve(true);
          return;
        }
        
        if (Date.now() - startTime > timeoutMs) {
          clearInterval(checkInterval);
          console.warn(`[UserOpCoordinator] ⏰ Timeout waiting for operations to complete`);
          resolve(false);
        }
      }, 100);
    });
  }

  /**
   * Get operation metrics for debugging
   */
  public getMetrics(): {
    activeCount: number;
    activeOperations: UserOperation[];
    totalListeners: number;
  } {
    return {
      activeCount: this.activeOperations.size,
      activeOperations: this.getActiveOperations(),
      totalListeners: this.listenerCount('user_operation_started') + this.listenerCount('user_operation_completed')
    };
  }
}

// Export singleton instance
export const userOperationCoordinator = new UserOperationCoordinatorService();