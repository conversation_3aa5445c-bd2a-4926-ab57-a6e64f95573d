-- Migration 030: Convert organizations table to pure UUID primary key
-- This migration transforms the organizations table from dual-ID system to pure UUID architecture
-- WARNING: This is a breaking change that modifies core table structure
-- Must be executed AFTER migrations 028 and 029 (products and colors table migrations)

BEGIN TRANSACTION;

-- Step 1: Create backup table with current data
CREATE TABLE organizations_backup_pre_uuid_migration AS 
SELECT * FROM organizations;

-- Step 2: Store foreign key data that references organizations
-- This includes organization_members and organization_invitations tables
CREATE TEMPORARY TABLE organization_members_migration_map AS
SELECT 
    om.organization_id as old_org_id,  -- Currently UUID (external_id reference)
    o.external_id as org_uuid,         -- Will become the new organization_id
    om.user_id,
    om.role,
    om.joined_at,
    om.invited_by
FROM organization_members om
JOIN organizations o ON om.organization_id = o.external_id;

CREATE TEMPORARY TABLE organization_invitations_migration_map AS
SELECT 
    oi.organization_id as old_org_id,  -- Currently UUID (external_id reference)
    o.external_id as org_uuid,         -- Will become the new organization_id
    oi.id,
    oi.external_id as invitation_external_id,
    oi.email,
    oi.role,
    oi.invited_by,
    oi.invited_at,
    oi.created_at,
    oi.expires_at,
    oi.accepted_at,
    oi.token
FROM organization_invitations oi
JOIN organizations o ON oi.organization_id = o.external_id;

-- Step 3: Drop existing indexes on organizations table
DROP INDEX IF EXISTS idx_org_members_user;
DROP INDEX IF EXISTS idx_org_slug;
DROP INDEX IF EXISTS idx_organizations_external;
DROP INDEX IF EXISTS idx_invitations_email;
DROP INDEX IF EXISTS idx_invitations_token;
DROP INDEX IF EXISTS idx_invitations_org;
DROP INDEX IF EXISTS idx_invitations_external_id;

-- Step 4: Clear related tables temporarily (we'll repopulate them)
DELETE FROM organization_members;
DELETE FROM organization_invitations;

-- Step 5: Create new organizations table with UUID primary key
CREATE TABLE organizations_new (
  id TEXT PRIMARY KEY,  -- This was external_id, now becomes primary key
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
  settings JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  
  -- Constraints
  CHECK (length(id) = 36),  -- UUID v4 format validation
  CHECK (length(trim(name)) > 0),
  CHECK (length(trim(slug)) > 0)
);

-- Step 6: Migrate data from old table to new table
INSERT INTO organizations_new (
    id, name, slug, plan, settings, created_at, updated_at
)
SELECT 
    external_id as id,  -- external_id becomes the primary key
    name, slug, plan, settings, created_at, updated_at
FROM organizations
WHERE external_id IS NOT NULL AND length(external_id) = 36;

-- Step 7: Verify data migration
-- Count records to ensure no data loss
SELECT 
    (SELECT COUNT(*) FROM organizations WHERE external_id IS NOT NULL) as original_count,
    (SELECT COUNT(*) FROM organizations_new) as migrated_count;

-- Step 8: Drop old organizations table and rename new one
DROP TABLE organizations;
ALTER TABLE organizations_new RENAME TO organizations;

-- Step 9: Recreate indexes for UUID primary key
CREATE INDEX IF NOT EXISTS idx_organizations_slug ON organizations(slug);
CREATE INDEX IF NOT EXISTS idx_organizations_name ON organizations(name);
CREATE INDEX IF NOT EXISTS idx_organizations_plan ON organizations(plan);
CREATE INDEX IF NOT EXISTS idx_organizations_created_at ON organizations(created_at);
CREATE INDEX IF NOT EXISTS idx_organizations_updated_at ON organizations(updated_at);

-- Step 10: Update organization_members table to reference UUID primary key directly
-- The organization_id column already stores UUIDs (external_id references)
-- We just need to ensure they're still valid after the primary key change
INSERT INTO organization_members (organization_id, user_id, role, joined_at, invited_by)
SELECT 
    org_uuid as organization_id,  -- Reference UUID primary key directly
    user_id,
    role,
    joined_at,
    invited_by
FROM organization_members_migration_map
WHERE org_uuid IS NOT NULL AND length(org_uuid) = 36;

-- Step 11: Update organization_invitations table to reference UUID primary key directly
INSERT INTO organization_invitations (
    id, external_id, organization_id, email, role, invited_by, 
    invited_at, created_at, expires_at, accepted_at, token
)
SELECT 
    id,
    invitation_external_id as external_id,
    org_uuid as organization_id,  -- Reference UUID primary key directly
    email,
    role,
    invited_by,
    invited_at,
    created_at,
    expires_at,
    accepted_at,
    token
FROM organization_invitations_migration_map
WHERE org_uuid IS NOT NULL AND length(org_uuid) = 36;

-- Step 12: Recreate indexes for related tables
CREATE INDEX IF NOT EXISTS idx_org_members_user ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_org_members_org ON organization_members(organization_id);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON organization_invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON organization_invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_org ON organization_invitations(organization_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_invitations_external_id ON organization_invitations(external_id);

-- Step 13: Update triggers for UUID-based organizations table
DROP TRIGGER IF EXISTS update_organizations_timestamp;

CREATE TRIGGER IF NOT EXISTS update_organizations_timestamp 
AFTER UPDATE ON organizations
BEGIN
  UPDATE organizations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Step 14: Verify that products and colors tables still reference correct organization UUIDs
-- These tables should already have organization_id as UUID from previous data structure
-- No changes needed here since organization_id was already storing UUIDs

-- Step 15: Clean up temporary tables
DROP TABLE organization_members_migration_map;
DROP TABLE organization_invitations_migration_map;

-- Step 16: Add migration record
INSERT OR IGNORE INTO schema_migrations (version, name) 
VALUES (30, 'migrate_organizations_to_uuid_primary_key');

-- Verification queries (run after migration)
-- Uncomment these to verify migration success:

-- Check organizations table structure
-- PRAGMA table_info(organizations);

-- Verify UUID format in organizations
-- SELECT COUNT(*) as total_organizations,
--        COUNT(CASE WHEN length(id) = 36 THEN 1 END) as valid_uuid_organizations
-- FROM organizations;

-- Verify organization_members relationships
-- SELECT COUNT(*) as total_memberships,
--        COUNT(CASE WHEN length(organization_id) = 36 THEN 1 END) as valid_uuid_memberships
-- FROM organization_members;

-- Verify organization_invitations relationships
-- SELECT COUNT(*) as total_invitations,
--        COUNT(CASE WHEN length(organization_id) = 36 THEN 1 END) as valid_uuid_invitations
-- FROM organization_invitations;

-- Check foreign key integrity
-- SELECT o.id, o.name, COUNT(om.user_id) as member_count
-- FROM organizations o
-- LEFT JOIN organization_members om ON o.id = om.organization_id
-- GROUP BY o.id, o.name
-- LIMIT 10;

-- Verify products still reference correct organizations
-- SELECT o.id, o.name, COUNT(p.id) as product_count
-- FROM organizations o
-- LEFT JOIN products p ON o.id = p.organization_id
-- GROUP BY o.id, o.name
-- HAVING product_count > 0
-- LIMIT 10;

-- Verify colors still reference correct organizations
-- SELECT o.id, o.name, COUNT(c.id) as color_count
-- FROM organizations o
-- LEFT JOIN colors c ON o.id = c.organization_id
-- GROUP BY o.id, o.name
-- HAVING color_count > 0
-- LIMIT 10;

COMMIT;

-- Post-migration notes:
-- 1. The organizations table now uses UUID as primary key (id column)
-- 2. All organization-related foreign key references have been updated
-- 3. organization_members and organization_invitations now reference UUID primary key
-- 4. Products and colors tables continue to reference organizations correctly
-- 5. Backup table (organizations_backup_pre_uuid_migration) contains original data
-- 6. All three main tables (products, colors, organizations) are now fully UUID-based
-- 7. Junction table product_colors already uses UUIDs for both foreign keys
-- 8. Repository layer will need updates to remove external_id→internal_id conversion