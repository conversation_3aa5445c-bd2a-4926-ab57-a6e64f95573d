/**
 * @file color-space-calculator.test.ts
 * @description Unit tests for ColorSpaceCalculator service
 * 
 * Tests the ColorSpaceCalculator service that wraps pure color conversion functions
 * and provides a clean interface for color space calculations.
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { ColorSpaceCalculator } from '../color-space-calculator.service';
import { RGB, HSL, CMYK, LAB, ColorSpaces } from '../../../../shared/utils/color';

describe('ColorSpaceCalculator Service', () => {
  let calculator: ColorSpaceCalculator;

  beforeEach(() => {
    calculator = new ColorSpaceCalculator();
  });

  describe('Basic Color Space Conversions', () => {
    describe('Hex to RGB', () => {
      test('should convert standard hex colors to RGB', () => {
        expect(calculator.hexToRgb('#FF0000')).toEqual({ r: 255, g: 0, b: 0 });
        expect(calculator.hexToRgb('#00FF00')).toEqual({ r: 0, g: 255, b: 0 });
        expect(calculator.hexToRgb('#0000FF')).toEqual({ r: 0, g: 0, b: 255 });
        expect(calculator.hexToRgb('#FFFFFF')).toEqual({ r: 255, g: 255, b: 255 });
        expect(calculator.hexToRgb('#000000')).toEqual({ r: 0, g: 0, b: 0 });
      });

      test('should handle 3-character hex codes', () => {
        expect(calculator.hexToRgb('#F00')).toEqual({ r: 255, g: 0, b: 0 });
        expect(calculator.hexToRgb('#0F0')).toEqual({ r: 0, g: 255, b: 0 });
        expect(calculator.hexToRgb('#00F')).toEqual({ r: 0, g: 0, b: 255 });
      });

      test('should handle hex codes without # prefix', () => {
        expect(calculator.hexToRgb('FF0000')).toEqual({ r: 255, g: 0, b: 0 });
        expect(calculator.hexToRgb('F00')).toEqual({ r: 255, g: 0, b: 0 });
      });

      test('should handle lowercase hex codes', () => {
        expect(calculator.hexToRgb('#ff0000')).toEqual({ r: 255, g: 0, b: 0 });
        expect(calculator.hexToRgb('#abc123')).toEqual({ r: 171, g: 193, b: 35 });
      });

      test('should return null for invalid hex codes', () => {
        expect(calculator.hexToRgb('invalid')).toBeNull();
        expect(calculator.hexToRgb('#GG0000')).toBeNull();
        expect(calculator.hexToRgb('')).toBeNull();
        expect(calculator.hexToRgb('#')).toBeNull();
        expect(calculator.hexToRgb('#12345')).toBeNull(); // Invalid length
      });
    });

    describe('RGB to Hex', () => {
      test('should convert RGB to hex correctly', () => {
        expect(calculator.rgbToHex({ r: 255, g: 0, b: 0 })).toBe('#FF0000');
        expect(calculator.rgbToHex({ r: 0, g: 255, b: 0 })).toBe('#00FF00');
        expect(calculator.rgbToHex({ r: 0, g: 0, b: 255 })).toBe('#0000FF');
        expect(calculator.rgbToHex({ r: 255, g: 255, b: 255 })).toBe('#FFFFFF');
        expect(calculator.rgbToHex({ r: 0, g: 0, b: 0 })).toBe('#000000');
      });

      test('should handle values that need padding', () => {
        expect(calculator.rgbToHex({ r: 15, g: 16, b: 17 })).toBe('#0F1011');
        expect(calculator.rgbToHex({ r: 1, g: 2, b: 3 })).toBe('#010203');
      });

      test('should clamp out-of-range values', () => {
        expect(calculator.rgbToHex({ r: 300, g: -10, b: 256 })).toBe('#FF00FF');
        expect(calculator.rgbToHex({ r: -1, g: 0, b: 1 })).toBe('#000001');
      });
    });

    describe('Hex to CMYK', () => {
      test('should convert pure colors correctly', () => {
        const red = calculator.hexToCmyk('#FF0000');
        expect(red).toEqual({ c: 0, m: 100, y: 100, k: 0 });

        const green = calculator.hexToCmyk('#00FF00');
        expect(green).toEqual({ c: 100, m: 0, y: 100, k: 0 });

        const blue = calculator.hexToCmyk('#0000FF');
        expect(blue).toEqual({ c: 100, m: 100, y: 0, k: 0 });
      });

      test('should handle white and black correctly', () => {
        expect(calculator.hexToCmyk('#FFFFFF')).toEqual({ c: 0, m: 0, y: 0, k: 0 });
        expect(calculator.hexToCmyk('#000000')).toEqual({ c: 0, m: 0, y: 0, k: 100 });
      });

      test('should handle gray colors correctly', () => {
        const gray = calculator.hexToCmyk('#808080');
        expect(gray?.c).toBe(0);
        expect(gray?.m).toBe(0);
        expect(gray?.y).toBe(0);
        expect(gray?.k).toBeCloseTo(50, 0);
      });

      test('should return null for invalid hex codes', () => {
        expect(calculator.hexToCmyk('invalid')).toBeNull();
        expect(calculator.hexToCmyk('#GG0000')).toBeNull();
        expect(calculator.hexToCmyk('')).toBeNull();
      });
    });

    describe('CMYK to Hex', () => {
      test('should convert CMYK to hex correctly', () => {
        expect(calculator.cmykToHex({ c: 0, m: 100, y: 100, k: 0 })).toBe('#FF0000');
        expect(calculator.cmykToHex({ c: 100, m: 0, y: 100, k: 0 })).toBe('#00FF00');
        expect(calculator.cmykToHex({ c: 100, m: 100, y: 0, k: 0 })).toBe('#0000FF');
        expect(calculator.cmykToHex({ c: 0, m: 0, y: 0, k: 0 })).toBe('#FFFFFF');
        expect(calculator.cmykToHex({ c: 0, m: 0, y: 0, k: 100 })).toBe('#000000');
      });

      test('should handle intermediate values', () => {
        const result = calculator.cmykToHex({ c: 50, m: 25, y: 75, k: 10 });
        expect(result).toMatch(/^#[0-9A-F]{6}$/);
      });

      test('should not throw for out-of-range values', () => {
        expect(() => calculator.cmykToHex({ c: -10, m: 110, y: 50, k: 25 })).not.toThrow();
        expect(() => calculator.cmykToHex({ c: 50, m: 25, y: -5, k: 120 })).not.toThrow();
      });
    });

    describe('Hex to HSL', () => {
      test('should convert pure colors correctly', () => {
        expect(calculator.hexToHsl('#FF0000')).toEqual({ h: 0, s: 100, l: 50 });
        expect(calculator.hexToHsl('#00FF00')).toEqual({ h: 120, s: 100, l: 50 });
        expect(calculator.hexToHsl('#0000FF')).toEqual({ h: 240, s: 100, l: 50 });
      });

      test('should handle achromatic colors', () => {
        expect(calculator.hexToHsl('#FFFFFF')).toEqual({ h: 0, s: 0, l: 100 });
        expect(calculator.hexToHsl('#000000')).toEqual({ h: 0, s: 0, l: 0 });
        
        const gray = calculator.hexToHsl('#808080');
        expect(gray?.h).toBe(0);
        expect(gray?.s).toBe(0);
        expect(gray?.l).toBeCloseTo(50, 0);
      });

      test('should return null for invalid hex codes', () => {
        expect(calculator.hexToHsl('invalid')).toBeNull();
        expect(calculator.hexToHsl('#GG0000')).toBeNull();
        expect(calculator.hexToHsl('')).toBeNull();
      });
    });

    describe('HSL to Hex', () => {
      test('should convert HSL to hex correctly', () => {
        expect(calculator.hslToHex({ h: 0, s: 100, l: 50 })).toBe('#FF0000');
        expect(calculator.hslToHex({ h: 120, s: 100, l: 50 })).toBe('#00FF00');
        expect(calculator.hslToHex({ h: 240, s: 100, l: 50 })).toBe('#0000FF');
        expect(calculator.hslToHex({ h: 0, s: 0, l: 100 })).toBe('#FFFFFF');
        expect(calculator.hslToHex({ h: 0, s: 0, l: 0 })).toBe('#000000');
      });

      test('should handle edge cases', () => {
        expect(calculator.hslToHex({ h: 0, s: 0, l: 50 })).toBe('#808080');
        expect(calculator.hslToHex({ h: 180, s: 50, l: 75 })).toMatch(/^#[0-9A-F]{6}$/);
      });

      test('should handle hue wraparound', () => {
        expect(calculator.hslToHex({ h: 360, s: 100, l: 50 })).toBe('#FF0000');
        expect(calculator.hslToHex({ h: 720, s: 100, l: 50 })).toMatch(/^#[0-9A-F]{6}$/);
      });
    });

    describe('RGB to LAB', () => {
      test('should convert RGB to LAB correctly', () => {
        const whiteLab = calculator.rgbToLab({ r: 255, g: 255, b: 255 });
        expect(whiteLab.l).toBeCloseTo(100, 0);
        expect(whiteLab.a).toBeCloseTo(0, 0);
        expect(whiteLab.b).toBeCloseTo(0, 0);

        const blackLab = calculator.rgbToLab({ r: 0, g: 0, b: 0 });
        expect(blackLab.l).toBeCloseTo(0, 0);
        expect(blackLab.a).toBeCloseTo(0, 0);
        expect(blackLab.b).toBeCloseTo(0, 0);
      });

      test('should convert colored RGB to LAB', () => {
        const redLab = calculator.rgbToLab({ r: 255, g: 0, b: 0 });
        expect(redLab.l).toBeGreaterThan(0);
        expect(redLab.a).toBeGreaterThan(0); // Red should have positive a
        expect(redLab.b).toBeGreaterThan(60); // Red should have positive b component
      });

      test('should produce LAB values in correct ranges', () => {
        const randomRgb = { r: 123, g: 234, b: 67 };
        const lab = calculator.rgbToLab(randomRgb);
        
        expect(lab.l).toBeGreaterThanOrEqual(0);
        expect(lab.l).toBeLessThanOrEqual(100);
        expect(lab.a).toBeGreaterThanOrEqual(-128);
        expect(lab.a).toBeLessThanOrEqual(127);
        expect(lab.b).toBeGreaterThanOrEqual(-128);
        expect(lab.b).toBeLessThanOrEqual(127);
      });
    });
  });

  describe('Utility Functions', () => {
    describe('getAllColorSpaces', () => {
      test('should return all color spaces for valid hex', () => {
        const result = calculator.getAllColorSpaces('#FF0000');
        
        expect(result).not.toBeNull();
        expect(result?.hex).toBe('#FF0000');
        expect(result?.rgb).toEqual({ r: 255, g: 0, b: 0 });
        expect(result?.hsl).toEqual({ h: 0, s: 100, l: 50 });
        expect(result?.cmyk).toEqual({ c: 0, m: 100, y: 100, k: 0 });
        expect(result?.lab).toBeDefined();
      });

      test('should return null for invalid hex', () => {
        expect(calculator.getAllColorSpaces('invalid')).toBeNull();
        expect(calculator.getAllColorSpaces('#GG0000')).toBeNull();
        expect(calculator.getAllColorSpaces('')).toBeNull();
      });

      test('should handle edge case colors', () => {
        const white = calculator.getAllColorSpaces('#FFFFFF');
        expect(white?.rgb).toEqual({ r: 255, g: 255, b: 255 });
        expect(white?.hsl).toEqual({ h: 0, s: 0, l: 100 });
        expect(white?.cmyk).toEqual({ c: 0, m: 0, y: 0, k: 0 });

        const black = calculator.getAllColorSpaces('#000000');
        expect(black?.rgb).toEqual({ r: 0, g: 0, b: 0 });
        expect(black?.hsl).toEqual({ h: 0, s: 0, l: 0 });
        expect(black?.cmyk).toEqual({ c: 0, m: 0, y: 0, k: 100 });
      });
    });

    describe('isLightColor', () => {
      test('should identify light colors correctly', () => {
        expect(calculator.isLightColor({ r: 255, g: 255, b: 255 })).toBe(true); // White
        expect(calculator.isLightColor({ r: 200, g: 200, b: 200 })).toBe(true); // Light gray
        expect(calculator.isLightColor({ r: 255, g: 255, b: 0 })).toBe(true);   // Yellow
        expect(calculator.isLightColor({ r: 0, g: 255, b: 255 })).toBe(true);   // Cyan
      });

      test('should identify dark colors correctly', () => {
        expect(calculator.isLightColor({ r: 0, g: 0, b: 0 })).toBe(false);     // Black
        expect(calculator.isLightColor({ r: 50, g: 50, b: 50 })).toBe(false);  // Dark gray
        expect(calculator.isLightColor({ r: 255, g: 0, b: 0 })).toBe(false);   // Red
        expect(calculator.isLightColor({ r: 0, g: 0, b: 255 })).toBe(false);   // Blue
      });
    });

    describe('isLightColorHsl', () => {
      test('should identify light colors by HSL lightness', () => {
        expect(calculator.isLightColorHsl({ h: 0, s: 0, l: 100 })).toBe(true);  // White
        expect(calculator.isLightColorHsl({ h: 0, s: 0, l: 75 })).toBe(true);   // Light gray
        expect(calculator.isLightColorHsl({ h: 60, s: 100, l: 60 })).toBe(true); // Light yellow
      });

      test('should identify dark colors by HSL lightness', () => {
        expect(calculator.isLightColorHsl({ h: 0, s: 0, l: 0 })).toBe(false);   // Black
        expect(calculator.isLightColorHsl({ h: 0, s: 0, l: 25 })).toBe(false);  // Dark gray
        expect(calculator.isLightColorHsl({ h: 0, s: 100, l: 50 })).toBe(false); // Pure red
      });

      test('should handle boundary conditions', () => {
        expect(calculator.isLightColorHsl({ h: 0, s: 0, l: 50 })).toBe(false);  // Exactly 50%
        expect(calculator.isLightColorHsl({ h: 0, s: 0, l: 51 })).toBe(true);   // Just above 50%
      });
    });
  });

  describe('Round-trip Conversions', () => {
    test('should maintain accuracy through hex->rgb->hex', () => {
      const testColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000', '#FF5733', '#123ABC'];
      
      testColors.forEach(original => {
        const rgb = calculator.hexToRgb(original);
        expect(rgb).not.toBeNull();
        
        if (rgb) {
          const converted = calculator.rgbToHex(rgb);
          expect(converted).toBe(original);
        }
      });
    });

    test('should maintain reasonable accuracy through hex->hsl->hex', () => {
      const testColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000', '#FF5733'];
      
      testColors.forEach(original => {
        const hsl = calculator.hexToHsl(original);
        expect(hsl).not.toBeNull();
        
        if (hsl) {
          const converted = calculator.hslToHex(hsl);
          expect(converted).toMatch(/^#[0-9A-F]{6}$/);
          
          // For primary colors, should maintain exact conversion
          if (['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000'].includes(original)) {
            expect(converted).toBe(original);
          }
        }
      });
    });

    test('should handle lossy CMYK conversions gracefully', () => {
      const testColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000'];
      
      testColors.forEach(original => {
        const cmyk = calculator.hexToCmyk(original);
        expect(cmyk).not.toBeNull();
        
        if (cmyk) {
          const converted = calculator.cmykToHex(cmyk);
          expect(converted).toMatch(/^#[0-9A-F]{6}$/);
          
          // For pure colors, should maintain exact conversion
          expect(converted).toBe(original);
        }
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle null and undefined inputs gracefully', () => {
      expect(() => calculator.hexToRgb(null as any)).not.toThrow();
      expect(() => calculator.hexToRgb(undefined as any)).not.toThrow();
      expect(calculator.hexToRgb(null as any)).toBeNull();
      expect(calculator.hexToRgb(undefined as any)).toBeNull();
    });

    test('should handle empty string inputs', () => {
      expect(calculator.hexToRgb('')).toBeNull();
      expect(calculator.hexToHsl('')).toBeNull();
      expect(calculator.hexToCmyk('')).toBeNull();
      expect(calculator.getAllColorSpaces('')).toBeNull();
    });

    test('should handle malformed hex codes', () => {
      const malformedCodes = [
        '#',
        '#G',
        '#GG',
        '#GGG',
        '#GGGG',
        '#GGGGG',
        '#GGGGGGG',
        'notahexcode',
        '#notvalid',
        '##FF0000'
      ];

      malformedCodes.forEach(code => {
        expect(calculator.hexToRgb(code)).toBeNull();
        expect(calculator.hexToHsl(code)).toBeNull();
        expect(calculator.hexToCmyk(code)).toBeNull();
        expect(calculator.getAllColorSpaces(code)).toBeNull();
      });
    });
  });

  describe('Performance and Edge Cases', () => {
    test('should handle large numbers of conversions efficiently', () => {
      const now = () => {
        if (typeof performance !== 'undefined' && performance.now) {
          return performance.now();
        } else {
          return Date.now();
        }
      };
      
      const start = now();
      
      for (let i = 0; i < 1000; i++) {
        const hex = `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase()}`;
        calculator.getAllColorSpaces(hex);
      }
      
      const end = now();
      const duration = end - start;
      
      // Should complete 1000 conversions in under 200ms (more generous for CI)
      expect(duration).toBeLessThan(200);
    });

    test('should handle extreme RGB values', () => {
      // Test boundary values
      const extremeRgbs = [
        { r: 0, g: 0, b: 0 },     // Minimum
        { r: 255, g: 255, b: 255 }, // Maximum
        { r: 255, g: 0, b: 0 },    // Pure red
        { r: 0, g: 255, b: 0 },    // Pure green
        { r: 0, g: 0, b: 255 },    // Pure blue
        { r: 128, g: 128, b: 128 }  // Middle gray
      ];

      extremeRgbs.forEach(rgb => {
        expect(() => calculator.rgbToHex(rgb)).not.toThrow();
        expect(() => calculator.rgbToLab(rgb)).not.toThrow();
        
        const hex = calculator.rgbToHex(rgb);
        expect(hex).toMatch(/^#[0-9A-F]{6}$/);
        
        const lab = calculator.rgbToLab(rgb);
        expect(lab.l).toBeGreaterThanOrEqual(0);
        expect(lab.l).toBeLessThanOrEqual(100);
      });
    });

    test('should handle extreme CMYK values', () => {
      const extremeCmyks = [
        { c: 0, m: 0, y: 0, k: 0 },     // White
        { c: 0, m: 0, y: 0, k: 100 },   // Black
        { c: 100, m: 100, y: 100, k: 0 }, // Dark color
        { c: 100, m: 0, y: 0, k: 0 },   // Cyan
        { c: 0, m: 100, y: 0, k: 0 },   // Magenta
        { c: 0, m: 0, y: 100, k: 0 }    // Yellow
      ];

      extremeCmyks.forEach(cmyk => {
        expect(() => calculator.cmykToHex(cmyk)).not.toThrow();
        
        const hex = calculator.cmykToHex(cmyk);
        expect(hex).toMatch(/^#[0-9A-F]{6}$/);
      });
    });

    test('should handle extreme HSL values', () => {
      const extremeHsls = [
        { h: 0, s: 0, l: 0 },      // Black
        { h: 0, s: 0, l: 100 },    // White
        { h: 0, s: 100, l: 50 },   // Pure red
        { h: 120, s: 100, l: 50 }, // Pure green
        { h: 240, s: 100, l: 50 }, // Pure blue
        { h: 360, s: 100, l: 50 }  // Red again (hue wraparound)
      ];

      extremeHsls.forEach(hsl => {
        expect(() => calculator.hslToHex(hsl)).not.toThrow();
        
        const hex = calculator.hslToHex(hsl);
        expect(hex).toMatch(/^#[0-9A-F]{6}$/);
      });
    });

    test('should handle additional extreme edge cases', () => {
      // Test with maximum possible values
      const extremeRgb = { r: 999, g: -100, b: 500 };
      
      expect(() => calculator.rgbToHex(extremeRgb)).not.toThrow();
      expect(() => calculator.rgbToHsl(extremeRgb)).not.toThrow();
      expect(() => calculator.rgbToCmyk(extremeRgb)).not.toThrow();
      expect(() => calculator.rgbToLab(extremeRgb)).not.toThrow();
      
      // Should clamp values to valid ranges
      const clampedRgb = calculator.clampRgb(extremeRgb);
      expect(clampedRgb.r).toBe(255); // Clamped to max
      expect(clampedRgb.g).toBe(0);   // Clamped to min
      expect(clampedRgb.b).toBe(255); // Clamped to max
    });

    test('should handle invalid LAB color space edge cases', () => {
      // Test LAB values outside normal ranges
      const extremeLab = { l: 150, a: 200, b: -200 };
      
      expect(() => calculator.labToRgb(extremeLab)).not.toThrow();
      expect(calculator.isValidLab(extremeLab)).toBe(false);
      
      // Test boundary LAB values
      expect(calculator.isValidLab({ l: 0, a: -128, b: -128 })).toBe(true);
      expect(calculator.isValidLab({ l: 100, a: 127, b: 127 })).toBe(true);
      expect(calculator.isValidLab({ l: -1, a: 0, b: 0 })).toBe(false);
      expect(calculator.isValidLab({ l: 101, a: 0, b: 0 })).toBe(false);
    });

    test('should handle special hex formats comprehensively', () => {
      const specialHexFormats = [
        '#abc',      // 3-char format
        'ABC123',    // No hash, mixed case
        '#ABCDEF',   // Full format
        'abcdef',    // No hash, lowercase
        '#123456'    // Standard format
      ];

      specialHexFormats.forEach(hex => {
        const rgb = calculator.hexToRgb(hex);
        expect(rgb).not.toBeNull();
        
        if (rgb) {
          // Should produce valid RGB values
          expect(calculator.isValidRgb(rgb)).toBe(true);
          
          // Should be able to convert back
          const backToHex = calculator.rgbToHex(rgb);
          expect(backToHex).toMatch(/^#[0-9A-F]{6}$/);
        }
      });
    });

    test('should maintain service reliability under stress', () => {
      // Test many rapid conversions with edge cases
      const testCases = [
        '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
        '#808080', '#C0C0C0', '#400040', '#FFFF00', '#FF00FF'
      ];

      for (let i = 0; i < 100; i++) {
        testCases.forEach(hex => {
          const spaces = calculator.getAllColorSpaces(hex);
          expect(spaces).not.toBeNull();
          
          if (spaces) {
            expect(spaces.hex).toBe(hex);
            expect(calculator.isValidRgb(spaces.rgb)).toBe(true);
            expect(calculator.isValidHsl(spaces.hsl)).toBe(true);
            expect(calculator.isValidCmyk(spaces.cmyk)).toBe(true);
            expect(calculator.isValidLab(spaces.lab)).toBe(true);
          }
        });
      }
    });
  });
});