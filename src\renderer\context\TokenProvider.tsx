import React from 'react';
import { ThemeProvider } from './ThemeContext';

interface TokenProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component that wraps the application with theme context
 * This component should be used near the root of the application
 */
export const TokenProvider: React.FC<TokenProviderProps> = ({ children }) => {
  return <ThemeProvider>{children}</ThemeProvider>;
};
