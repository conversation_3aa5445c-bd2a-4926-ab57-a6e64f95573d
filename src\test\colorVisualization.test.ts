import { describe, it, expect } from 'vitest';
import {
  getCompleteColorData,
  formatRgb,
  formatCmyk,
  formatHsl,
  getAccessibilityMetrics,
  getStatusColor,
  getColorRelationshipDescription,
} from '../renderer/utils/colorVisualization';
import { ColorEntry } from '../shared/types/color.types';

describe('Color Visualization Utilities', () => {
  // Test color entry
  const testColor: ColorEntry = {
    id: 'test-color-1',
    product: 'Test Color',
    name: 'Test',
    code: '123-C',
    hex: '#FF0000', // Red
    cmyk: '0,100,100,0',
    notes: 'Test notes',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  describe('getCompleteColorData', () => {
    it('should return complete color data with all color spaces', () => {
      const data = getCompleteColorData(testColor);

      expect(data.name).toBe(testColor.product);
      expect(data.code).toBe(testColor.code);
      expect(data.hex).toBe(testColor.hex);

      // Validate RGB data
      expect(data.rgb).toEqual({
        r: 255,
        g: 0,
        b: 0,
      });

      // Validate CMYK data
      expect(data.cmyk).toEqual({
        c: 0,
        m: 100,
        y: 100,
        k: 0,
      });

      // Validate HSL data
      expect(data.hsl).toEqual({
        h: 0,
        s: 100,
        l: 50,
      });

      expect(data.isGradient).toBe(false);
    });

    it('should handle gradient colors correctly', () => {
      const gradientColor: ColorEntry = {
        ...testColor,
        gradient: {
          gradientStops: [
            { color: '#FF0000', position: 0 },
            { color: '#0000FF', position: 100 },
          ],
          gradientCSS: 'linear-gradient(90deg, #FF0000 0%, #0000FF 100%)',
        },
      };

      const data = getCompleteColorData(gradientColor);
      expect(data.isGradient).toBe(true);
    });
  });

  describe('formatRgb', () => {
    it('should format RGB values as a string', () => {
      const rgb = { r: 255, g: 0, b: 0 };
      const formatted = formatRgb(rgb);
      expect(formatted).toBe('R:255 G:0 B:0');
    });

    it('should handle null values', () => {
      const formatted = formatRgb(null);
      expect(formatted).toBe('N/A');
    });
  });

  describe('formatCmyk', () => {
    it('should format CMYK values as a string', () => {
      const cmyk = { c: 0, m: 100, y: 100, k: 0 };
      const formatted = formatCmyk(cmyk);
      expect(formatted).toBe('C:0 M:100 Y:100 K:0');
    });

    it('should handle null values', () => {
      const formatted = formatCmyk(null);
      expect(formatted).toBe('N/A');
    });
  });

  describe('formatHsl', () => {
    it('should format HSL values as a string', () => {
      const hsl = { h: 0, s: 100, l: 50 };
      const formatted = formatHsl(hsl);
      expect(formatted).toBe('H:0° S:100% L:50%');
    });

    it('should handle null values', () => {
      const formatted = formatHsl(null);
      expect(formatted).toBe('N/A');
    });
  });

  describe('getAccessibilityMetrics', () => {
    it('should return AAA for high contrast ratios', () => {
      const metrics = getAccessibilityMetrics(8.0);
      expect(metrics.wcagLevel).toBe('AAA');
      expect(metrics.normalTextAAA).toBe(true);
      expect(metrics.normalTextAA).toBe(true);
      expect(metrics.largeTextAA).toBe(true);
    });

    it('should return AA for medium contrast ratios', () => {
      const metrics = getAccessibilityMetrics(5.0);
      expect(metrics.wcagLevel).toBe('AA');
      expect(metrics.normalTextAAA).toBe(false);
      expect(metrics.normalTextAA).toBe(true);
      expect(metrics.largeTextAA).toBe(true);
    });

    it('should return AA-large for lower contrast ratios', () => {
      const metrics = getAccessibilityMetrics(3.5);
      expect(metrics.wcagLevel).toBe('AA-large');
      expect(metrics.normalTextAAA).toBe(false);
      expect(metrics.normalTextAA).toBe(false);
      expect(metrics.largeTextAA).toBe(true);
    });

    it('should return fail for poor contrast ratios', () => {
      const metrics = getAccessibilityMetrics(2.0);
      expect(metrics.wcagLevel).toBe('fail');
      expect(metrics.normalTextAAA).toBe(false);
      expect(metrics.normalTextAA).toBe(false);
      expect(metrics.largeTextAA).toBe(false);
    });
  });

  describe('getStatusColor', () => {
    it('should return green for AAA level', () => {
      const color = getStatusColor('AAA');
      expect(color).toBe('#38a169');
    });

    it('should return orange for AA level', () => {
      const color = getStatusColor('AA');
      expect(color).toBe('#dd6b20');
    });

    it('should return yellow for AA-large level', () => {
      const color = getStatusColor('AA-large');
      expect(color).toBe('#ecc94b');
    });

    it('should return red for fail level', () => {
      const color = getStatusColor('fail');
      expect(color).toBe('#e53e3e');
    });
  });

  describe('getColorRelationshipDescription', () => {
    it('should identify complementary colors', () => {
      const color1: ColorEntry = { ...testColor, hex: '#FF0000' }; // Red
      const color2: ColorEntry = { ...testColor, hex: '#00FFFF' }; // Cyan

      const relationship = getColorRelationshipDescription(color1, color2);
      expect(relationship).toBe('Complementary');
    });

    it('should identify analogous-like colors', () => {
      const color1: ColorEntry = { ...testColor, hex: '#FF0000' }; // Red
      const color2: ColorEntry = { ...testColor, hex: '#FF8000' }; // Orange

      const relationship = getColorRelationshipDescription(color1, color2);
      expect(relationship).toBe('Analogous-like');
    });

    it('should identify triadic-like colors', () => {
      const color1: ColorEntry = { ...testColor, hex: '#FF0000' }; // Red
      const color2: ColorEntry = { ...testColor, hex: '#0000FF' }; // Blue

      const relationship = getColorRelationshipDescription(color1, color2);
      expect(relationship).toBe('Triadic-like');
    });

    it('should handle invalid color inputs', () => {
      const color1: ColorEntry = { ...testColor, hex: 'invalid' };
      const color2: ColorEntry = { ...testColor, hex: '#FF0000' };

      const relationship = getColorRelationshipDescription(color1, color2);
      expect(relationship).toBe('Unknown relationship');
    });
  });
});
