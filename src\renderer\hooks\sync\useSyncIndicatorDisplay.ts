import { useMemo } from 'react';
import { SyncStatus as SyncStatusEnum } from '../../../shared/constants/sync-status';
import { SyncProgress, QueueStats } from './useSyncIndicatorState';

export function useSyncIndicatorDisplay(
  status: SyncStatusEnum,
  isManualSyncing: boolean,
  hasUnsyncedChanges: boolean,
  syncProgress: SyncProgress | null,
  queueStats: QueueStats | null,
  lastSync: number | null,
  currentPhase: string
) {
  const isSyncing = isManualSyncing || status === SyncStatusEnum.SYNCING;

  const getProgressPercentage = (): number => {
    if (!syncProgress) {return 0;}
    
    // Ensure progress is a valid number and handle edge cases
    let progress = syncProgress.progress;
    if (typeof progress !== 'number' || isNaN(progress) || progress === null || progress === undefined) {
      console.warn('[SyncIndicator] Invalid progress value:', progress, 'using 0 instead');
      progress = 0;
    }
    
    // Clamp to valid range
    const clampedProgress = Math.max(0, Math.min(100, progress));
    
    // Final safety check
    if (isNaN(clampedProgress)) {
      console.error('[SyncIndicator] Progress calculation resulted in NaN, using 0');
      return 0;
    }
    
    return clampedProgress;
  };

  const formatLastSyncTime = (timestamp: number | null): string => {
    if (!timestamp) {return 'Never synced';}
    
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) {return 'Just now';}
    if (minutes < 60) {return `${minutes}m ago`;}
    if (hours < 24) {return `${hours}h ago`;}
    return `${days}d ago`;
  };

  const formatQueueInfo = (): string => {
    if (!queueStats?.memoryQueue || !queueStats?.persistentQueue) {return '';}
    
    const memoryQueue = queueStats.memoryQueue || {};
    const persistentQueue = queueStats.persistentQueue || {};
    
    const totalPending = (memoryQueue.pending || 0) + (persistentQueue.pending || 0);
    const totalFailed = (memoryQueue.failed || 0) + (persistentQueue.failed || 0);
    
    if (totalPending === 0 && totalFailed === 0) {return '';}
    
    const parts: string[] = [];
    if (totalPending > 0) {parts.push(`${totalPending} pending`);}
    if (totalFailed > 0) {parts.push(`${totalFailed} failed`);}
    
    return parts.length > 0 ? `Queue: ${parts.join(', ')}` : '';
  };

  const statusIcon = useMemo(() => {
    if (isSyncing) {
      return 'RefreshCw';
    }
    if (queueStats && ((queueStats.memoryQueue?.pending || 0) > 0 || (queueStats.persistentQueue?.pending || 0) > 0)) {
      return 'Database';
    }
    switch (status) {
      case SyncStatusEnum.SUCCESS:
        return hasUnsyncedChanges ? 'Cloud' : 'Check';
      case SyncStatusEnum.ERROR:
        return 'AlertCircle';
      case SyncStatusEnum.IDLE:
      case SyncStatusEnum.OFFLINE:
        return hasUnsyncedChanges ? 'CloudOff' : 'Cloud';
      default:
        return 'Cloud';
    }
  }, [isSyncing, status, hasUnsyncedChanges, queueStats]);

  const statusColor = useMemo(() => {
    if (isSyncing) {
      return 'text-blue-600 dark:text-blue-400';
    }
    if ((queueStats?.memoryQueue?.failed || 0) > 0 || (queueStats?.persistentQueue?.failed || 0) > 0) {
      return 'text-orange-600 dark:text-orange-400';
    }
    if (hasUnsyncedChanges && status !== SyncStatusEnum.SYNCING as any) {
      return 'text-yellow-600 dark:text-yellow-400';
    }
    switch (status) {
      case SyncStatusEnum.SUCCESS:
        return 'text-green-600 dark:text-green-400';
      case SyncStatusEnum.ERROR:
        return 'text-red-600 dark:text-red-400';
      case SyncStatusEnum.OFFLINE:
        return 'text-gray-500 dark:text-gray-400';
      default:
        return 'text-gray-600 dark:text-gray-300';
    }
  }, [isSyncing, status, hasUnsyncedChanges, queueStats]);

  const statusText = useMemo(() => {
    if (isSyncing) {
      if (syncProgress) {
        if (currentPhase) {
          return `${currentPhase}...`;
        }
        const percentage = getProgressPercentage();
        // Ensure we never show NaN in the UI - double check
        if (isNaN(percentage) || typeof percentage !== 'number') {
          console.warn('[SyncIndicator] Invalid percentage in statusText:', percentage);
          return 'Syncing...';
        }
        return `${Math.round(percentage)}%`;
      }
      if (isManualSyncing && status === SyncStatusEnum.SUCCESS) {
        return 'Refreshing UI...';
      }
      return 'Syncing...';
    }
    if (hasUnsyncedChanges) {return 'Changes pending';}
    const queueInfo = formatQueueInfo();
    if (queueInfo) {return queueInfo;}
    if (status === SyncStatusEnum.ERROR) {return 'Sync error';}
    if (status === SyncStatusEnum.OFFLINE) {return 'Offline';}
    return formatLastSyncTime(lastSync);
  }, [isSyncing, status, hasUnsyncedChanges, syncProgress, currentPhase, lastSync, formatLastSyncTime, formatQueueInfo, getProgressPercentage]);

  return {
    statusIcon,
    statusColor,
    statusText,
    getProgressPercentage,
    formatLastSyncTime,
    formatQueueInfo,
  };
}
