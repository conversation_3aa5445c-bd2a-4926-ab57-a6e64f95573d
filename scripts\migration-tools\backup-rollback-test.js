/**
 * @file backup-rollback-test.js
 * @description Test suite for the backup-rollback system
 * 
 * This test suite validates:
 * - Full backup creation and restoration
 * - Incremental backup functionality
 * - Backup validation and integrity checks
 * - Compression and decompression
 * - Rollback scenarios
 * - Cleanup operations
 * - Error handling and edge cases
 */

const { BackupManager } = require('./backup-rollback');
const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const os = require('os');

class BackupRollbackTester {
  constructor() {
    this.testDir = path.join(os.tmpdir(), 'chromasync-backup-test');
    this.testDbPath = path.join(this.testDir, 'test.db');
    this.backupManager = null;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
    this.setupComplete = false;
  }

  /**
   * Setup test environment
   */
  async setup() {
    console.log('🔧 Setting up test environment...');
    
    try {
      // Create test directory
      if (fs.existsSync(this.testDir)) {
        fs.rmSync(this.testDir, { recursive: true, force: true });
      }
      fs.mkdirSync(this.testDir, { recursive: true });

      // Create test database
      await this.createTestDatabase();

      // Initialize backup manager
      this.backupManager = new BackupManager(this.testDbPath, {
        compress: true,
        validate: true,
        maxRetries: 3
      });

      // Override backup directory to use test directory
      this.backupManager.backupDir = path.join(this.testDir, 'backups');
      fs.mkdirSync(this.backupManager.backupDir, { recursive: true });

      this.setupComplete = true;
      console.log('✅ Test environment setup complete');
      
    } catch (error) {
      console.error(`❌ Setup failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create test database with sample data
   */
  async createTestDatabase() {
    console.log('📦 Creating test database...');
    
    const db = new Database(this.testDbPath);
    
    try {
      // Create tables
      db.exec(`
        CREATE TABLE organizations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          external_id TEXT UNIQUE,
          name TEXT NOT NULL,
          slug TEXT UNIQUE NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE TABLE products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          external_id TEXT UNIQUE,
          organization_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          is_active BOOLEAN DEFAULT 1,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (organization_id) REFERENCES organizations(id)
        );
        
        CREATE TABLE colors (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          external_id TEXT UNIQUE,
          organization_id INTEGER NOT NULL,
          code TEXT NOT NULL,
          hex TEXT NOT NULL,
          name TEXT,
          is_gradient BOOLEAN DEFAULT 0,
          is_library BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (organization_id) REFERENCES organizations(id)
        );
        
        CREATE TABLE product_colors (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          product_id INTEGER NOT NULL,
          color_id INTEGER NOT NULL,
          organization_id INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (product_id) REFERENCES products(id),
          FOREIGN KEY (color_id) REFERENCES colors(id),
          FOREIGN KEY (organization_id) REFERENCES organizations(id),
          UNIQUE(product_id, color_id)
        );
      `);

      // Insert sample data
      const insertOrg = db.prepare(`
        INSERT INTO organizations (external_id, name, slug)
        VALUES (?, ?, ?)
      `);
      
      const insertProduct = db.prepare(`
        INSERT INTO products (external_id, organization_id, name, description)
        VALUES (?, ?, ?, ?)
      `);
      
      const insertColor = db.prepare(`
        INSERT INTO colors (external_id, organization_id, code, hex, name)
        VALUES (?, ?, ?, ?, ?)
      `);
      
      const insertProductColor = db.prepare(`
        INSERT INTO product_colors (product_id, color_id, organization_id)
        VALUES (?, ?, ?)
      `);

      // Insert test data
      insertOrg.run('org-1', 'Test Organization', 'test-org');
      insertOrg.run('org-2', 'Another Organization', 'another-org');
      
      insertProduct.run('prod-1', 1, 'Test Product 1', 'Description 1');
      insertProduct.run('prod-2', 1, 'Test Product 2', 'Description 2');
      insertProduct.run('prod-3', 2, 'Test Product 3', 'Description 3');
      
      insertColor.run('color-1', 1, 'RED', '#FF0000', 'Red');
      insertColor.run('color-2', 1, 'GREEN', '#00FF00', 'Green');
      insertColor.run('color-3', 2, 'BLUE', '#0000FF', 'Blue');
      
      insertProductColor.run(1, 1, 1);
      insertProductColor.run(1, 2, 1);
      insertProductColor.run(2, 1, 1);
      insertProductColor.run(3, 3, 2);

      console.log('✅ Test database created with sample data');
      
    } finally {
      db.close();
    }
  }

  /**
   * Run a test case
   */
  async runTest(testName, testFunction) {
    console.log(`\n🧪 Running test: ${testName}`);
    
    try {
      await testFunction();
      console.log(`✅ Test passed: ${testName}`);
      this.testResults.passed++;
    } catch (error) {
      console.error(`❌ Test failed: ${testName}`);
      console.error(`   Error: ${error.message}`);
      this.testResults.failed++;
      this.testResults.errors.push({
        test: testName,
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Test full backup creation
   */
  async testFullBackupCreation() {
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database');
    }

    const metadata = await this.backupManager.createFullBackup('test-backup', 'test-phase');
    
    // Verify backup was created
    if (!metadata) {
      throw new Error('Backup metadata not returned');
    }
    
    if (metadata.type !== 'full') {
      throw new Error(`Expected backup type 'full', got '${metadata.type}'`);
    }
    
    if (!metadata.compressed) {
      throw new Error('Backup should be compressed');
    }
    
    if (!metadata.validated) {
      throw new Error('Backup should be validated');
    }

    // Verify backup file exists
    const backupPath = path.join(this.backupManager.backupDir, metadata.name);
    const compressedPath = backupPath + '.gz';
    
    if (!fs.existsSync(compressedPath)) {
      throw new Error('Compressed backup file not found');
    }
    
    // Verify metadata file exists
    const metadataPath = compressedPath + '.meta';
    if (!fs.existsSync(metadataPath)) {
      throw new Error('Metadata file not found');
    }

    this.backupManager.disconnect();
  }

  /**
   * Test incremental backup creation
   */
  async testIncrementalBackupCreation() {
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database');
    }

    const metadata = await this.backupManager.createIncrementalBackup('test-incremental', 'test-phase', ['organizations', 'products']);
    
    // Verify backup was created
    if (!metadata) {
      throw new Error('Backup metadata not returned');
    }
    
    if (metadata.type !== 'incremental') {
      throw new Error(`Expected backup type 'incremental', got '${metadata.type}'`);
    }
    
    if (!Array.isArray(metadata.tables) || metadata.tables.length === 0) {
      throw new Error('Incremental backup should include table list');
    }
    
    if (!metadata.tables.includes('organizations') || !metadata.tables.includes('products')) {
      throw new Error('Incremental backup should include specified tables');
    }

    this.backupManager.disconnect();
  }

  /**
   * Test backup validation
   */
  async testBackupValidation() {
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database');
    }

    // Create a backup first
    const metadata = await this.backupManager.createFullBackup('validation-test');
    this.backupManager.disconnect();
    
    const backupPath = path.join(this.backupManager.backupDir, metadata.name);
    const compressedPath = backupPath + '.gz';
    
    // Validate the backup
    const isValid = await this.backupManager.validateBackup(compressedPath, metadata);
    
    if (!isValid) {
      throw new Error('Backup validation failed');
    }
  }

  /**
   * Test full database restoration
   */
  async testFullRestoration() {
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database');
    }

    // Create a backup first
    const metadata = await this.backupManager.createFullBackup('restoration-test');
    this.backupManager.disconnect();
    
    // Modify the database
    const db = new Database(this.testDbPath);
    db.exec("INSERT INTO organizations (external_id, name, slug) VALUES ('modified-org', 'Modified Organization', 'modified-org')");
    db.close();
    
    // Restore from backup
    const backupPath = path.join(this.backupManager.backupDir, metadata.name);
    const compressedPath = backupPath + '.gz';
    
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database for restoration');
    }

    await this.backupManager.restoreDatabase(compressedPath, {
      createBackupBeforeRestore: false,
      validateBeforeRestore: true,
      validateAfterRestore: true
    });
    
    // Verify restoration
    const testDb = new Database(this.testDbPath, { readonly: true });
    const modifiedOrgs = testDb.prepare("SELECT * FROM organizations WHERE external_id = 'modified-org'").all();
    testDb.close();
    
    if (modifiedOrgs.length > 0) {
      throw new Error('Database was not properly restored - modified data still exists');
    }

    this.backupManager.disconnect();
  }

  /**
   * Test schema-only restoration
   */
  async testSchemaOnlyRestoration() {
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database');
    }

    // Create a backup first
    const metadata = await this.backupManager.createFullBackup('schema-test');
    this.backupManager.disconnect();
    
    // Drop a table
    const db = new Database(this.testDbPath);
    db.exec("DROP TABLE product_colors");
    db.close();
    
    // Restore schema only
    const backupPath = path.join(this.backupManager.backupDir, metadata.name);
    const compressedPath = backupPath + '.gz';
    
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database for schema restoration');
    }

    await this.backupManager.restoreDatabase(compressedPath, {
      createBackupBeforeRestore: false,
      restoreType: 'schema-only'
    });
    
    // Verify schema was restored
    const testDb = new Database(this.testDbPath, { readonly: true });
    const tables = testDb.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='product_colors'").all();
    testDb.close();
    
    if (tables.length === 0) {
      throw new Error('Schema was not properly restored - table still missing');
    }

    this.backupManager.disconnect();
  }

  /**
   * Test backup listing
   */
  async testBackupListing() {
    // Create several backups
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database');
    }

    await this.backupManager.createFullBackup('list-test-1');
    await this.backupManager.createFullBackup('list-test-2');
    await this.backupManager.createIncrementalBackup('list-test-3');
    
    this.backupManager.disconnect();
    
    // List backups
    const backups = this.backupManager.listBackups({ type: 'full' });
    
    if (backups.length < 2) {
      throw new Error('Expected at least 2 full backups');
    }
    
    const fullBackups = backups.filter(b => b.type === 'full');
    if (fullBackups.length < 2) {
      throw new Error('Type filter not working correctly');
    }
  }

  /**
   * Test backup cleanup
   */
  async testBackupCleanup() {
    // Create several backups
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database');
    }

    await this.backupManager.createFullBackup('cleanup-test-1');
    await this.backupManager.createFullBackup('cleanup-test-2');
    await this.backupManager.createFullBackup('cleanup-test-3');
    
    this.backupManager.disconnect();
    
    const backupsBefore = this.backupManager.listBackups();
    
    // Cleanup keeping only 2 backups
    await this.backupManager.cleanupBackups({ keepCount: 2 });
    
    const backupsAfter = this.backupManager.listBackups();
    
    if (backupsAfter.length > 2) {
      throw new Error('Cleanup did not remove enough backups');
    }
    
    if (backupsAfter.length >= backupsBefore.length) {
      throw new Error('Cleanup did not remove any backups');
    }
  }

  /**
   * Test error handling
   */
  async testErrorHandling() {
    // Test with invalid backup path
    try {
      await this.backupManager.restoreDatabase('/invalid/path/backup.db');
      throw new Error('Should have thrown error for invalid backup path');
    } catch (error) {
      if (!error.message.includes('not found') && !error.message.includes('ENOENT')) {
        throw new Error('Unexpected error type for invalid backup path');
      }
    }
    
    // Test validation of non-existent backup
    const isValid = await this.backupManager.validateBackup('/invalid/path/backup.db');
    if (isValid) {
      throw new Error('Validation should fail for non-existent backup');
    }
  }

  /**
   * Test compression and decompression
   */
  async testCompressionDecompression() {
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to test database');
    }

    // Create compressed backup
    const metadata = await this.backupManager.createFullBackup('compression-test');
    this.backupManager.disconnect();
    
    if (!metadata.compressed) {
      throw new Error('Backup should be compressed');
    }
    
    const backupPath = path.join(this.backupManager.backupDir, metadata.name);
    const compressedPath = backupPath + '.gz';
    
    // Verify compressed file exists and uncompressed doesn't
    if (!fs.existsSync(compressedPath)) {
      throw new Error('Compressed backup file not found');
    }
    
    if (fs.existsSync(backupPath)) {
      throw new Error('Uncompressed backup file should not exist');
    }
    
    // Validate compressed backup (tests decompression)
    const isValid = await this.backupManager.validateBackup(compressedPath, metadata);
    if (!isValid) {
      throw new Error('Compressed backup validation failed');
    }
  }

  /**
   * Test backup report generation
   */
  async testReportGeneration() {
    const report = this.backupManager.generateReport();
    
    if (!report) {
      throw new Error('Report not generated');
    }
    
    if (typeof report.totalBackups !== 'number') {
      throw new Error('Report should include total backup count');
    }
    
    if (typeof report.healthScore !== 'number') {
      throw new Error('Report should include health score');
    }
    
    if (!report.backupDir) {
      throw new Error('Report should include backup directory');
    }
  }

  /**
   * Cleanup test environment
   */
  async cleanup() {
    console.log('\n🧹 Cleaning up test environment...');
    
    try {
      if (this.backupManager) {
        this.backupManager.disconnect();
      }
      
      if (fs.existsSync(this.testDir)) {
        fs.rmSync(this.testDir, { recursive: true, force: true });
      }
      
      console.log('✅ Test environment cleaned up');
    } catch (error) {
      console.warn(`⚠️  Cleanup warning: ${error.message}`);
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting ChromaSync Backup-Rollback Test Suite');
    console.log('=================================================\n');

    try {
      await this.setup();
      
      // Run all test cases
      await this.runTest('Full Backup Creation', () => this.testFullBackupCreation());
      await this.runTest('Incremental Backup Creation', () => this.testIncrementalBackupCreation());
      await this.runTest('Backup Validation', () => this.testBackupValidation());
      await this.runTest('Full Database Restoration', () => this.testFullRestoration());
      await this.runTest('Schema-Only Restoration', () => this.testSchemaOnlyRestoration());
      await this.runTest('Backup Listing', () => this.testBackupListing());
      await this.runTest('Backup Cleanup', () => this.testBackupCleanup());
      await this.runTest('Error Handling', () => this.testErrorHandling());
      await this.runTest('Compression/Decompression', () => this.testCompressionDecompression());
      await this.runTest('Report Generation', () => this.testReportGeneration());
      
      // Display results
      console.log('\n📊 Test Results Summary:');
      console.log('========================');
      console.log(`✅ Passed: ${this.testResults.passed}`);
      console.log(`❌ Failed: ${this.testResults.failed}`);
      console.log(`📊 Total: ${this.testResults.passed + this.testResults.failed}`);
      
      if (this.testResults.failed > 0) {
        console.log('\n❌ Failed Tests:');
        this.testResults.errors.forEach(error => {
          console.log(`   - ${error.test}: ${error.error}`);
        });
      }
      
      const overallResult = this.testResults.failed === 0 ? 'PASSED' : 'FAILED';
      console.log(`\n🎯 Overall Result: ${overallResult}`);
      
      return this.testResults.failed === 0;
      
    } catch (error) {
      console.error(`❌ Test suite failed: ${error.message}`);
      console.error(error.stack);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

// CLI usage
if (require.main === module) {
  const tester = new BackupRollbackTester();
  
  tester.runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error(`❌ Test execution failed: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { BackupRollbackTester };