/**
 * @file product.repository.test.ts
 * @description Unit tests for ProductRepository data access layer
 *
 * Tests all database operations that will be extracted from ProductService
 * into a dedicated repository following the Repository pattern.
 *
 * This test suite prepares for the extraction of ProductRepository from
 * ProductService by testing all data access patterns including:
 * - Core CRUD operations
 * - Complex relationship queries (products with colors)
 * - Soft delete functionality
 * - Organization-based filtering
 * - Performance with large datasets
 * - Transaction management
 * - Bulk operations
 * - Deduplication logic
 * - Sync operations
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import {
  Product,
  NewProduct,
  UpdateProduct,
} from '../../../../shared/types/product.types';
import { ColorEntry } from '../../../../shared/types/color.types';

// Mock ProductRepository class for testing (simulating what will be extracted)
class ProductRepository {
  constructor(private db: Database.Database) {}

  // Core CRUD Operations
  findAll(organizationId: string): Product[] {
    const { where, params } = this.buildActiveRecordsQuery(organizationId);

    const rows = this.db
      .prepare(
        `
      SELECT 
        external_id as id,
        name,
        description,
        metadata,
        created_by,
        user_id,
        created_at as createdAt,
        updated_at as updatedAt
      FROM products
      WHERE ${where} AND is_active = 1
      ORDER BY name ASC
    `
      )
      .all(...params);

    return rows.map((row: any) => this.mapRowToProduct(row, organizationId));
  }

  findById(id: string, organizationId: string): Product | null {
    const row = this.db
      .prepare(
        `
      SELECT 
        external_id as id,
        name,
        description,
        metadata,
        created_by,
        user_id,
        created_at as createdAt,
        updated_at as updatedAt
      FROM products
      WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL AND is_active = 1
    `
      )
      .get(id, organizationId) as any;

    if (!row) return null;
    return this.mapRowToProduct(row, organizationId);
  }

  insert(product: NewProduct, organizationId: string, userId?: string): string {
    const id = uuidv4();
    const now = new Date().toISOString();

    // Check for duplicate name
    const existing = this.findByName(product.name, organizationId);
    if (existing) {
      return existing.id; // Return existing product ID
    }

    this.db
      .prepare(
        `
      INSERT INTO products (external_id, name, metadata, organization_id, user_id, created_at, updated_at, is_synced)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `
      )
      .run(
        id,
        product.name,
        JSON.stringify({
          description: product.description,
          createdBy: userId,
          updatedBy: userId,
        }),
        organizationId,
        userId || null,
        now,
        now,
        0
      );

    return id;
  }

  update(
    id: string,
    updates: UpdateProduct,
    organizationId: string,
    userId?: string
  ): boolean {
    const current = this.db
      .prepare(
        `
      SELECT metadata FROM products WHERE external_id = ? AND organization_id = ?
    `
      )
      .get(id, organizationId) as { metadata: string } | undefined;

    if (!current) return false;

    const currentMetadata = current.metadata
      ? JSON.parse(current.metadata)
      : {};
    const updatedMetadata = {
      ...currentMetadata,
      description:
        updates.description !== undefined
          ? updates.description
          : currentMetadata.description,
      updatedBy: userId,
    };

    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (updates.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(updates.name);
    }

    updateFields.push('metadata = ?', 'updated_at = ?', 'is_synced = ?');
    updateValues.push(
      JSON.stringify(updatedMetadata),
      new Date().toISOString(),
      0,
      id,
      organizationId
    );

    const result = this.db
      .prepare(
        `
      UPDATE products 
      SET ${updateFields.join(', ')} 
      WHERE external_id = ? AND organization_id = ?
    `
      )
      .run(...updateValues);

    return result.changes > 0;
  }

  softDelete(id: string, organizationId: string): boolean {
    const result = this.db
      .prepare(
        `
      UPDATE products 
      SET deleted_at = CURRENT_TIMESTAMP, is_active = 0, updated_at = CURRENT_TIMESTAMP, is_synced = 0 
      WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL
    `
      )
      .run(id, organizationId);

    return result.changes > 0;
  }

  // Relationship Operations
  findWithColors(organizationId: string): any[] {
    const query = `
      SELECT 
        p.external_id as product_id,
        p.name as product_name,
        p.description as product_description,
        p.created_at as product_created_at,
        p.updated_at as product_updated_at,
        c.external_id as color_id,
        c.code as color_code,
        c.display_name as color_name,
        c.hex as color_hex,
        c.color_spaces,
        c.is_gradient,
        c.gradient_colors,
        c.notes as color_notes,
        c.tags as color_tags,
        c.is_library,
        c.created_at as color_created_at,
        c.updated_at as color_updated_at,
        pc.display_order
      FROM products p
      LEFT JOIN product_colors pc ON p.id = pc.product_id
      LEFT JOIN colors c ON pc.color_id = c.id AND c.deleted_at IS NULL AND c.organization_id = ?
      WHERE p.organization_id = ? AND p.deleted_at IS NULL AND p.is_active = 1
      ORDER BY p.name, pc.display_order
    `;

    const rows = this.db.prepare(query).all(organizationId, organizationId);

    // Group results by product
    const productMap = new Map();

    rows.forEach((row: any) => {
      if (!productMap.has(row.product_id)) {
        productMap.set(row.product_id, {
          id: row.product_id,
          name: row.product_name,
          description: row.product_description,
          organizationId,
          createdAt: row.product_created_at,
          updatedAt: row.product_updated_at,
          colors: [],
        });
      }

      if (row.color_id) {
        const product = productMap.get(row.product_id);
        product.colors.push({
          id: row.color_id,
          code: row.color_code,
          name: row.color_name,
          hex: row.color_hex,
          colorSpaces: row.color_spaces ? JSON.parse(row.color_spaces) : null,
          isGradient: Boolean(row.is_gradient),
          gradient: row.gradient_colors
            ? JSON.parse(row.gradient_colors)
            : null,
          notes: row.color_notes,
          tags: row.color_tags,
          isLibrary: Boolean(row.is_library),
          createdAt: row.color_created_at,
          updatedAt: row.color_updated_at,
          organizationId,
        });
      }
    });

    return Array.from(productMap.values());
  }

  findByIdWithColors(id: string, organizationId: string): any | null {
    const product = this.findById(id, organizationId);
    if (!product) return null;

    // Get internal product ID
    const internalProduct = this.db
      .prepare(
        'SELECT id FROM products WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL'
      )
      .get(id, organizationId) as { id: number } | undefined;

    if (!internalProduct) return { ...product, colors: [] };

    // Get colors for this product
    const colorIds = this.db
      .prepare(
        `
      SELECT c.external_id
      FROM product_colors pc
      JOIN colors c ON pc.color_id = c.id
      WHERE pc.product_id = ? AND pc.organization_id = ? AND c.deleted_at IS NULL
      ORDER BY pc.display_order
    `
      )
      .all(internalProduct.id, organizationId) as Array<{
      external_id: string;
    }>;

    const colors = colorIds
      .map(row => this.getColorById(row.external_id, organizationId))
      .filter(color => color !== null);

    return { ...product, colors };
  }

  addColorRelationship(
    productId: string,
    colorId: string,
    organizationId: string
  ): boolean {
    // Get internal IDs
    const product = this.db
      .prepare(
        'SELECT id FROM products WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL'
      )
      .get(productId, organizationId) as { id: number } | undefined;

    const color = this.db
      .prepare(
        'SELECT id FROM colors WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL'
      )
      .get(colorId, organizationId) as { id: number } | undefined;

    if (!product || !color) return false;

    // Get next display order
    const maxOrder = this.db
      .prepare(
        `
      SELECT MAX(display_order) as max_order 
      FROM product_colors 
      WHERE product_id = ?
    `
      )
      .get(product.id) as { max_order: number | null } | undefined;

    const displayOrder = (maxOrder?.max_order || 0) + 1;

    // Insert relationship
    this.db
      .prepare(
        `
      INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order, organization_id)
      VALUES (?, ?, ?, ?)
    `
      )
      .run(product.id, color.id, displayOrder, organizationId);

    return true;
  }

  removeColorRelationship(
    productId: string,
    colorId: string,
    organizationId: string
  ): boolean {
    const product = this.db
      .prepare(
        'SELECT id FROM products WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL'
      )
      .get(productId, organizationId) as { id: number } | undefined;

    const color = this.db
      .prepare(
        'SELECT id FROM colors WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL'
      )
      .get(colorId, organizationId) as { id: number } | undefined;

    if (!product || !color) return false;

    const result = this.db
      .prepare(
        `
      DELETE FROM product_colors 
      WHERE product_id = ? AND color_id = ? AND organization_id = ?
    `
      )
      .run(product.id, color.id, organizationId);

    return result.changes > 0;
  }

  // Soft Delete Operations
  findSoftDeleted(
    organizationId: string,
    limit: number = 100,
    offset: number = 0
  ): Product[] {
    const rows = this.db
      .prepare(
        `
      SELECT 
        external_id as id,
        name,
        description,
        metadata,
        created_by,
        user_id,
        created_at as createdAt,
        updated_at as updatedAt,
        deleted_at as deletedAt
      FROM products
      WHERE organization_id = ? AND deleted_at IS NOT NULL
      ORDER BY deleted_at DESC
      LIMIT ? OFFSET ?
    `
      )
      .all(organizationId, limit, offset);

    return rows.map((row: any) => this.mapRowToProduct(row, organizationId));
  }

  restore(id: string, organizationId: string): boolean {
    const result = this.db
      .prepare(
        `
      UPDATE products 
      SET deleted_at = NULL, is_active = 1, updated_at = ?
      WHERE external_id = ? AND organization_id = ?
    `
      )
      .run(new Date().toISOString(), id, organizationId);

    return result.changes > 0;
  }

  // Bulk Operations
  deleteMultiple(
    ids: string[],
    organizationId: string
  ): { success: boolean; deletedIds: string[] } {
    const deletedIds: string[] = [];

    const deleteTransaction = this.db.transaction(() => {
      for (const id of ids) {
        const success = this.softDelete(id, organizationId);
        if (success) {
          deletedIds.push(id);
        }
      }
    });

    try {
      deleteTransaction();
      return { success: true, deletedIds };
    } catch (error) {
      return { success: false, deletedIds: [] };
    }
  }

  cascadeDelete(id: string, organizationId: string): boolean {
    // Get internal product ID
    const product = this.db
      .prepare(
        `
      SELECT id FROM products WHERE external_id = ? AND organization_id = ?
    `
      )
      .get(id, organizationId) as { id: number } | undefined;

    if (!product) return false;

    const deleteTransaction = this.db.transaction(() => {
      // 1. Soft delete related datasheets
      this.db
        .prepare(
          `
        UPDATE datasheets 
        SET is_active = 0, updated_at = datetime('now')
        WHERE product_id = ? AND is_active = 1
      `
        )
        .run(product.id);

      // 2. Get colors that will become orphaned
      const orphanedColors = this.db
        .prepare(
          `
        SELECT DISTINCT c.id, c.external_id 
        FROM colors c
        JOIN product_colors pc ON c.id = pc.color_id
        WHERE pc.product_id = ? AND c.organization_id = ?
        AND NOT EXISTS (
          SELECT 1 FROM product_colors pc2 
          WHERE pc2.color_id = c.id AND pc2.product_id != ?
        )
      `
        )
        .all(product.id, organizationId, product.id);

      // 3. Remove product-color associations
      this.db
        .prepare(
          `
        DELETE FROM product_colors WHERE product_id = ?
      `
        )
        .run(product.id);

      // 4. Delete orphaned colors
      if (orphanedColors.length > 0) {
        const orphanedColorIds = orphanedColors.map((c: any) => c.id);
        const placeholders = orphanedColorIds.map(() => '?').join(',');
        this.db
          .prepare(
            `
          UPDATE colors 
          SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
          WHERE id IN (${placeholders}) AND organization_id = ?
        `
          )
          .run(...orphanedColorIds, organizationId);
      }

      // 5. Soft delete the product
      return this.softDelete(id, organizationId);
    });

    return deleteTransaction();
  }

  // Deduplication Operations
  deduplicateByName(organizationId: string): {
    success: boolean;
    deduplicatedCount: number;
    errors: string[];
  } {
    try {
      // Get duplicate groups
      const duplicateGroups = this.db
        .prepare(
          `
        SELECT name, COUNT(*) as count, GROUP_CONCAT(external_id) as ids
        FROM products 
        WHERE organization_id = ? AND is_active = 1
        GROUP BY name 
        HAVING COUNT(*) > 1
        ORDER BY count DESC
      `
        )
        .all(organizationId) as Array<{
        name: string;
        count: number;
        ids: string;
      }>;

      let deduplicatedCount = 0;
      const errors: string[] = [];

      const deduplicationTransaction = this.db.transaction(() => {
        for (const group of duplicateGroups) {
          try {
            const productIds = group.ids.split(',');

            // Get detailed info for each product
            const products = productIds
              .map(id => {
                const product = this.db
                  .prepare(
                    `
                SELECT id, external_id, name, created_at
                FROM products 
                WHERE external_id = ? AND organization_id = ?
              `
                  )
                  .get(id.trim(), organizationId) as
                  | {
                      id: number;
                      external_id: string;
                      name: string;
                      created_at: string;
                    }
                  | undefined;

                if (!product) return null;

                // Count colors for this product
                const colorCount = this.db
                  .prepare(
                    `
                SELECT COUNT(*) as count
                FROM product_colors pc
                JOIN colors c ON pc.color_id = c.id
                WHERE pc.product_id = ? AND pc.organization_id = ? AND c.is_active = 1
              `
                  )
                  .get(product.id, organizationId) as
                  | { count: number }
                  | undefined;

                return {
                  ...product,
                  colorCount: colorCount?.count || 0,
                };
              })
              .filter(p => p !== null);

            if (products.length <= 1) continue;

            // Sort: most colors first, then oldest
            products.sort((a, b) => {
              if (a.colorCount !== b.colorCount) {
                return b.colorCount - a.colorCount;
              }
              return (
                new Date(a.created_at).getTime() -
                new Date(b.created_at).getTime()
              );
            });

            const keepProduct = products[0];
            const deleteProducts = products.slice(1);

            // Transfer color associations to kept product
            for (const duplicateProduct of deleteProducts) {
              if (duplicateProduct.colorCount > 0) {
                this.db
                  .prepare(
                    `
                  UPDATE OR IGNORE product_colors 
                  SET product_id = ?
                  WHERE product_id = ? AND organization_id = ?
                `
                  )
                  .run(keepProduct.id, duplicateProduct.id, organizationId);

                this.db
                  .prepare(
                    `
                  DELETE FROM product_colors 
                  WHERE product_id = ? AND organization_id = ?
                `
                  )
                  .run(duplicateProduct.id, organizationId);
              }

              // Soft delete duplicate
              this.db
                .prepare(
                  `
                UPDATE products 
                SET is_active = 0, updated_at = ?
                WHERE external_id = ? AND organization_id = ?
              `
                )
                .run(
                  new Date().toISOString(),
                  duplicateProduct.external_id,
                  organizationId
                );

              deduplicatedCount++;
            }
          } catch (error) {
            errors.push(`Failed to deduplicate ${group.name}: ${error}`);
          }
        }
      });

      deduplicationTransaction();

      return { success: true, deduplicatedCount, errors };
    } catch (error) {
      return {
        success: false,
        deduplicatedCount: 0,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  // Query Operations
  search(query: string, organizationId: string): Product[] {
    const rows = this.db
      .prepare(
        `
      SELECT 
        external_id as id,
        name,
        metadata,
        created_at as createdAt,
        updated_at as updatedAt
      FROM products
      WHERE is_active = 1 AND organization_id = ? AND name LIKE ?
      ORDER BY name ASC
    `
      )
      .all(organizationId, `%${query}%`);

    return rows.map((row: any) => this.mapRowToProduct(row, organizationId));
  }

  findUnsynced(): Product[] {
    const rows = this.db
      .prepare(
        `
      SELECT * FROM products WHERE is_synced = 0 AND deleted_at IS NULL
    `
      )
      .all();

    return rows.map((row: any) =>
      this.mapRowToProduct(row, row.organization_id)
    );
  }

  findByName(name: string, organizationId: string): Product | null {
    const row = this.db
      .prepare(
        `
      SELECT 
        external_id as id,
        name,
        description,
        metadata,
        created_by,
        user_id,
        created_at as createdAt,
        updated_at as updatedAt
      FROM products
      WHERE name = ? AND organization_id = ? AND deleted_at IS NULL AND is_active = 1
    `
      )
      .get(name, organizationId) as any;

    if (!row) return null;
    return this.mapRowToProduct(row, organizationId);
  }

  markAsSynced(id: string): void {
    this.db
      .prepare('UPDATE products SET is_synced = 1 WHERE external_id = ?')
      .run(id);
  }

  // Helper Methods
  private buildActiveRecordsQuery(organizationId: string): {
    where: string;
    params: any[];
  } {
    return {
      where: 'organization_id = ? AND deleted_at IS NULL',
      params: [organizationId],
    };
  }

  private mapRowToProduct(row: any, organizationId: string): Product {
    const metadata = row.metadata ? JSON.parse(row.metadata) : {};
    return {
      id: row.id,
      name: row.name,
      description: row.description || metadata.description || undefined,
      organizationId,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      createdBy: row.created_by || metadata.createdBy || undefined,
      updatedBy: metadata.updatedBy || undefined,
      deletedAt: row.deletedAt || undefined,
    };
  }

  private getColorById(id: string, organizationId: string): ColorEntry | null {
    // Mock color retrieval - in real implementation this would use ColorRepository
    const row = this.db
      .prepare(
        `
      SELECT * FROM colors WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL
    `
      )
      .get(id, organizationId) as any;

    if (!row) return null;

    return {
      id: row.external_id,
      code: row.code,
      name: row.display_name,
      hex: row.hex,
      organizationId,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}

describe.sequential('ProductRepository', () => {
  let db: Database.Database;
  let repository: ProductRepository;
  let mockOrganizationId: string;

  beforeEach(() => {
    // Create in-memory SQLite database for testing
    db = new Database(':memory:');

    // Set up test schema
    setupTestSchema(db);

    // Create repository instance
    repository = new ProductRepository(db);

    mockOrganizationId = '550e8400-e29b-41d4-a716-************'; // Valid UUID format

    // Seed test data
    seedTestData(db, mockOrganizationId);
  });

  afterEach(() => {
    if (db && db.open) {
      try {
        db.close();
      } catch (error) {
        console.warn('Database close error:', error);
      }
    }
  });

  describe('Core CRUD Operations', () => {
    describe('findAll', () => {
      test('should return all products for organization', () => {
        const products = repository.findAll(mockOrganizationId);

        expect(products).toBeInstanceOf(Array);
        expect(products.length).toBeGreaterThan(0);
        expect(products[0]).toHaveProperty('id');
        expect(products[0]).toHaveProperty('name');
        expect(products[0]).toHaveProperty(
          'organizationId',
          mockOrganizationId
        );
      });

      test('should return empty array for non-existent organization', () => {
        const products = repository.findAll(
          '550e8400-e29b-41d4-a716-************'
        );

        expect(products).toBeInstanceOf(Array);
        expect(products).toHaveLength(0);
      });

      test('should exclude soft-deleted products', () => {
        const productId = 'test-product-1';
        repository.softDelete(productId, mockOrganizationId);

        const products = repository.findAll(mockOrganizationId);
        const deletedProduct = products.find(p => p.id === productId);

        expect(deletedProduct).toBeUndefined();
      });

      test('should order results by name', () => {
        const products = repository.findAll(mockOrganizationId);

        for (let i = 1; i < products.length; i++) {
          expect(products[i - 1].name <= products[i].name).toBe(true);
        }
      });
    });

    describe('findById', () => {
      test('should return product by ID for organization', () => {
        const productId = 'test-product-1';
        const product = repository.findById(productId, mockOrganizationId);

        expect(product).toBeTruthy();
        expect(product!.id).toBe(productId);
        expect(product!.organizationId).toBe(mockOrganizationId);
      });

      test('should return null for non-existent product', () => {
        const product = repository.findById('non-existent', mockOrganizationId);
        expect(product).toBeNull();
      });

      test('should return null for wrong organization', () => {
        const productId = 'test-product-1';
        const product = repository.findById(
          productId,
          '550e8400-e29b-41d4-a716-446655440002'
        );
        expect(product).toBeNull();
      });

      test('should return null for soft-deleted product', () => {
        const productId = 'test-product-1';
        repository.softDelete(productId, mockOrganizationId);

        const product = repository.findById(productId, mockOrganizationId);
        expect(product).toBeNull();
      });
    });

    describe('insert', () => {
      test('should insert new product and return ID', () => {
        const newProduct: NewProduct = {
          name: 'Test Product',
          description: 'Test description',
          organizationId: mockOrganizationId,
        };

        const productId = repository.insert(
          newProduct,
          mockOrganizationId,
          'test-user'
        );

        expect(productId).toBeTruthy();
        expect(typeof productId).toBe('string');

        // Verify the product was inserted
        const insertedProduct = repository.findById(
          productId,
          mockOrganizationId
        );
        expect(insertedProduct).toBeTruthy();
        expect(insertedProduct!.name).toBe(newProduct.name);
        expect(insertedProduct!.description).toBe(newProduct.description);
      });

      test('should return existing product ID for duplicate name', () => {
        const existingProduct = repository.findAll(mockOrganizationId)[0];

        const newProduct: NewProduct = {
          name: existingProduct.name, // Same name
          description: 'Different description',
          organizationId: mockOrganizationId,
        };

        const productId = repository.insert(newProduct, mockOrganizationId);
        expect(productId).toBe(existingProduct.id);
      });

      test('should generate UUID for external_id', () => {
        const newProduct: NewProduct = {
          name: 'UUID Test Product',
          organizationId: mockOrganizationId,
        };

        const productId = repository.insert(newProduct, mockOrganizationId);
        expect(productId).toMatch(/^[0-9a-f-]{36}$/); // UUID format
      });

      test('should set default values for optional fields', () => {
        const newProduct: NewProduct = {
          name: 'Minimal Product',
          organizationId: mockOrganizationId,
        };

        const productId = repository.insert(newProduct, mockOrganizationId);
        const insertedProduct = repository.findById(
          productId,
          mockOrganizationId
        );

        expect(insertedProduct!.createdAt).toBeTruthy();
        expect(insertedProduct!.updatedAt).toBeTruthy();
      });
    });

    describe('update', () => {
      test('should update existing product fields', () => {
        const productId = 'test-product-1';
        const updates: UpdateProduct = {
          name: 'Updated Product Name',
          description: 'Updated description',
        };

        const success = repository.update(
          productId,
          updates,
          mockOrganizationId,
          'test-user'
        );

        expect(success).toBe(true);

        const updatedProduct = repository.findById(
          productId,
          mockOrganizationId
        );
        expect(updatedProduct!.name).toBe(updates.name);
        expect(updatedProduct!.description).toBe(updates.description);
      });

      test('should return false for non-existent product', () => {
        const updates: UpdateProduct = { name: 'Updated' };
        const success = repository.update(
          'non-existent',
          updates,
          mockOrganizationId
        );

        expect(success).toBe(false);
      });

      test('should return false for wrong organization', () => {
        const productId = 'test-product-1';
        const updates: UpdateProduct = { name: 'Updated' };
        const success = repository.update(
          productId,
          updates,
          '550e8400-e29b-41d4-a716-446655440003'
        );

        expect(success).toBe(false);
      });

      test('should update timestamps', () => {
        const productId = 'test-product-1';
        const originalProduct = repository.findById(
          productId,
          mockOrganizationId
        );

        const start = Date.now();
        const updates: UpdateProduct = { name: 'Updated Name' };
        repository.update(productId, updates, mockOrganizationId);

        const updatedProduct = repository.findById(
          productId,
          mockOrganizationId
        );
        expect(
          new Date(updatedProduct!.updatedAt).getTime()
        ).toBeGreaterThanOrEqual(start);
      });

      test('should handle partial updates', () => {
        const productId = 'test-product-1';
        const originalProduct = repository.findById(
          productId,
          mockOrganizationId
        );

        const updates: UpdateProduct = {
          description: 'Only updating description',
        };
        repository.update(productId, updates, mockOrganizationId);

        const updatedProduct = repository.findById(
          productId,
          mockOrganizationId
        );
        expect(updatedProduct!.description).toBe(updates.description);
        expect(updatedProduct!.name).toBe(originalProduct!.name); // Should remain unchanged
      });
    });

    describe('softDelete', () => {
      test('should soft delete product', () => {
        const productId = 'test-product-1';

        const success = repository.softDelete(productId, mockOrganizationId);
        expect(success).toBe(true);

        // Product should not appear in normal queries
        const product = repository.findById(productId, mockOrganizationId);
        expect(product).toBeNull();

        const allProducts = repository.findAll(mockOrganizationId);
        const deletedProduct = allProducts.find(p => p.id === productId);
        expect(deletedProduct).toBeUndefined();
      });

      test('should return false for non-existent product', () => {
        const success = repository.softDelete(
          'non-existent',
          mockOrganizationId
        );
        expect(success).toBe(false);
      });

      test('should return false for wrong organization', () => {
        const productId = 'test-product-1';
        const success = repository.softDelete(
          productId,
          '550e8400-e29b-41d4-a716-446655440004'
        );
        expect(success).toBe(false);
      });

      test('should set deleted_at timestamp', () => {
        const productId = 'test-product-1';
        const beforeDelete = Date.now();

        repository.softDelete(productId, mockOrganizationId);

        // Check soft deleted record directly
        const stmt = db.prepare(
          'SELECT deleted_at FROM products WHERE external_id = ?'
        );
        const result = stmt.get(productId) as { deleted_at: string };

        expect(result.deleted_at).toBeTruthy();
        expect(new Date(result.deleted_at).getTime()).toBeGreaterThanOrEqual(
          beforeDelete
        );
      });
    });
  });

  describe('Relationship Operations', () => {
    describe('findWithColors', () => {
      test('should return products with their associated colors', () => {
        const productsWithColors =
          repository.findWithColors(mockOrganizationId);

        expect(productsWithColors).toBeInstanceOf(Array);
        expect(productsWithColors.length).toBeGreaterThan(0);

        // Check structure
        const productWithColors = productsWithColors.find(
          p => p.colors.length > 0
        );
        if (productWithColors) {
          expect(productWithColors).toHaveProperty('id');
          expect(productWithColors).toHaveProperty('name');
          expect(productWithColors).toHaveProperty('colors');
          expect(productWithColors.colors).toBeInstanceOf(Array);
          expect(productWithColors.colors[0]).toHaveProperty('id');
          expect(productWithColors.colors[0]).toHaveProperty('hex');
        }
      });

      test('should return empty colors array for products without colors', () => {
        // Insert a product without colors
        const productId = repository.insert(
          {
            name: 'Product Without Colors',
            organizationId: mockOrganizationId,
          },
          mockOrganizationId
        );

        const productsWithColors =
          repository.findWithColors(mockOrganizationId);
        const productWithoutColors = productsWithColors.find(
          p => p.id === productId
        );

        expect(productWithoutColors).toBeTruthy();
        expect(productWithoutColors.colors).toBeInstanceOf(Array);
        expect(productWithoutColors.colors).toHaveLength(0);
      });

      test('should exclude soft-deleted colors', () => {
        // Soft delete a color
        const colorId = 'test-color-1';
        db.prepare(
          'UPDATE colors SET deleted_at = CURRENT_TIMESTAMP WHERE external_id = ?'
        ).run(colorId);

        const productsWithColors =
          repository.findWithColors(mockOrganizationId);

        // Check that deleted color is not included
        productsWithColors.forEach(product => {
          const deletedColor = product.colors.find(
            (c: any) => c.id === colorId
          );
          expect(deletedColor).toBeUndefined();
        });
      });
    });

    describe('findByIdWithColors', () => {
      test('should return product with its colors', () => {
        const productId = 'test-product-1';
        const productWithColors = repository.findByIdWithColors(
          productId,
          mockOrganizationId
        );

        expect(productWithColors).toBeTruthy();
        expect(productWithColors.id).toBe(productId);
        expect(productWithColors.colors).toBeInstanceOf(Array);
      });

      test('should return null for non-existent product', () => {
        const productWithColors = repository.findByIdWithColors(
          'non-existent',
          mockOrganizationId
        );
        expect(productWithColors).toBeNull();
      });

      test('should return empty colors array when product has no colors', () => {
        // Create product without colors
        const productId = repository.insert(
          {
            name: 'No Colors Product',
            organizationId: mockOrganizationId,
          },
          mockOrganizationId
        );

        const productWithColors = repository.findByIdWithColors(
          productId,
          mockOrganizationId
        );

        expect(productWithColors).toBeTruthy();
        expect(productWithColors.colors).toBeInstanceOf(Array);
        expect(productWithColors.colors).toHaveLength(0);
      });
    });

    describe('addColorRelationship', () => {
      test('should add color to product', () => {
        const productId = 'test-product-1';
        const colorId = 'test-color-2';

        const success = repository.addColorRelationship(
          productId,
          colorId,
          mockOrganizationId
        );
        expect(success).toBe(true);

        // Verify relationship exists
        const productWithColors = repository.findByIdWithColors(
          productId,
          mockOrganizationId
        );
        const associatedColor = productWithColors.colors.find(
          (c: any) => c.id === colorId
        );
        expect(associatedColor).toBeTruthy();
      });

      test('should return false for non-existent product', () => {
        const success = repository.addColorRelationship(
          'non-existent',
          'test-color-1',
          mockOrganizationId
        );
        expect(success).toBe(false);
      });

      test('should return false for non-existent color', () => {
        const success = repository.addColorRelationship(
          'test-product-1',
          'non-existent',
          mockOrganizationId
        );
        expect(success).toBe(false);
      });

      test('should handle duplicate relationships gracefully', () => {
        const productId = 'test-product-1';
        const colorId = 'test-color-1';

        // Add relationship twice
        repository.addColorRelationship(productId, colorId, mockOrganizationId);
        const success = repository.addColorRelationship(
          productId,
          colorId,
          mockOrganizationId
        );

        // Should not fail or create duplicates
        expect(success).toBe(true);
      });
    });

    describe('removeColorRelationship', () => {
      test('should remove color from product', () => {
        const productId = 'test-product-1';
        const colorId = 'test-color-1';

        // First ensure relationship exists
        repository.addColorRelationship(productId, colorId, mockOrganizationId);

        const success = repository.removeColorRelationship(
          productId,
          colorId,
          mockOrganizationId
        );
        expect(success).toBe(true);

        // Verify relationship no longer exists
        const productWithColors = repository.findByIdWithColors(
          productId,
          mockOrganizationId
        );
        const removedColor = productWithColors.colors.find(
          (c: any) => c.id === colorId
        );
        expect(removedColor).toBeUndefined();
      });

      test('should return false for non-existent relationship', () => {
        const success = repository.removeColorRelationship(
          'test-product-1',
          'test-color-2',
          mockOrganizationId
        );
        expect(success).toBe(false);
      });

      test('should return false for non-existent product', () => {
        const success = repository.removeColorRelationship(
          'non-existent',
          'test-color-1',
          mockOrganizationId
        );
        expect(success).toBe(false);
      });
    });
  });

  describe('Soft Delete Operations', () => {
    describe('findSoftDeleted', () => {
      test('should return soft deleted products', () => {
        const productId = 'test-product-1';
        repository.softDelete(productId, mockOrganizationId);

        const softDeleted = repository.findSoftDeleted(mockOrganizationId);

        expect(softDeleted).toBeInstanceOf(Array);
        expect(softDeleted.length).toBeGreaterThan(0);

        const deletedProduct = softDeleted.find(p => p.id === productId);
        expect(deletedProduct).toBeTruthy();
        expect(deletedProduct!.deletedAt).toBeTruthy();
      });

      test('should respect limit parameter', () => {
        // Create and soft delete multiple products
        const productIds = [];
        for (let i = 0; i < 5; i++) {
          const productId = repository.insert(
            {
              name: `Delete Test Product ${i}`,
              organizationId: mockOrganizationId,
            },
            mockOrganizationId
          );
          productIds.push(productId);
          repository.softDelete(productId, mockOrganizationId);
        }

        const softDeleted = repository.findSoftDeleted(mockOrganizationId, 3);
        expect(softDeleted).toHaveLength(3);
      });

      test('should respect offset parameter', () => {
        const allSoftDeleted = repository.findSoftDeleted(mockOrganizationId);

        if (allSoftDeleted.length >= 2) {
          const offsetResults = repository.findSoftDeleted(
            mockOrganizationId,
            1,
            1
          );
          expect(offsetResults).toHaveLength(1);
          expect(offsetResults[0].id).not.toBe(allSoftDeleted[0].id);
        }
      });

      test('should return empty array for organization with no soft deleted products', () => {
        const softDeleted = repository.findSoftDeleted('clean-org');
        expect(softDeleted).toHaveLength(0);
      });
    });

    describe('restore', () => {
      test('should restore soft deleted product', () => {
        const productId = 'test-product-1';

        // Soft delete first
        repository.softDelete(productId, mockOrganizationId);
        expect(repository.findById(productId, mockOrganizationId)).toBeNull();

        // Restore
        const success = repository.restore(productId, mockOrganizationId);
        expect(success).toBe(true);

        // Should be available again
        const restoredProduct = repository.findById(
          productId,
          mockOrganizationId
        );
        expect(restoredProduct).toBeTruthy();
        expect(restoredProduct!.id).toBe(productId);
      });

      test('should return false for non-existent product', () => {
        const success = repository.restore('non-existent', mockOrganizationId);
        expect(success).toBe(false);
      });

      test('should return false for wrong organization', () => {
        const productId = 'test-product-1';
        repository.softDelete(productId, mockOrganizationId);

        const success = repository.restore(productId, 'wrong-org');
        expect(success).toBe(false);
      });

      test('should clear deleted_at timestamp', () => {
        const productId = 'test-product-1';

        repository.softDelete(productId, mockOrganizationId);
        repository.restore(productId, mockOrganizationId);

        const stmt = db.prepare(
          'SELECT deleted_at FROM products WHERE external_id = ?'
        );
        const result = stmt.get(productId) as { deleted_at: string | null };
        expect(result.deleted_at).toBeNull();
      });
    });
  });

  describe('Bulk Operations', () => {
    describe('deleteMultiple', () => {
      test('should delete multiple products', () => {
        const productIds = ['test-product-1', 'test-product-2'];

        const result = repository.deleteMultiple(
          productIds,
          mockOrganizationId
        );

        expect(result.success).toBe(true);
        expect(result.deletedIds).toHaveLength(2);
        expect(result.deletedIds).toContain('test-product-1');
        expect(result.deletedIds).toContain('test-product-2');

        // Verify products are deleted
        productIds.forEach(id => {
          const product = repository.findById(id, mockOrganizationId);
          expect(product).toBeNull();
        });
      });

      test('should handle empty array', () => {
        const result = repository.deleteMultiple([], mockOrganizationId);

        expect(result.success).toBe(true);
        expect(result.deletedIds).toHaveLength(0);
      });

      test('should handle mix of valid and invalid IDs', () => {
        const productIds = ['test-product-1', 'non-existent'];
        const result = repository.deleteMultiple(
          productIds,
          mockOrganizationId
        );

        expect(result.success).toBe(true);
        expect(result.deletedIds).toHaveLength(1);
        expect(result.deletedIds).toContain('test-product-1');
      });
    });

    describe('cascadeDelete', () => {
      test('should delete product and associated relationships', () => {
        const productId = 'test-product-1';

        // Ensure product has color relationships
        repository.addColorRelationship(
          productId,
          'test-color-1',
          mockOrganizationId
        );

        const success = repository.cascadeDelete(productId, mockOrganizationId);
        expect(success).toBe(true);

        // Product should be deleted
        const product = repository.findById(productId, mockOrganizationId);
        expect(product).toBeNull();

        // Color relationships should be removed
        const productColors = db
          .prepare(
            `
          SELECT COUNT(*) as count FROM product_colors WHERE product_id = (
            SELECT id FROM products WHERE external_id = ?
          )
        `
          )
          .get(productId) as { count: number };
        expect(productColors.count).toBe(0);
      });

      test('should handle orphaned colors correctly', () => {
        const productId = 'test-product-1';
        const colorId = 'unique-color-for-cascade-test';

        // Create a color only associated with this product
        db.prepare(
          `
          INSERT INTO colors (external_id, organization_id, code, display_name, hex, is_active)
          VALUES (?, ?, ?, ?, ?, 1)
        `
        ).run(
          colorId,
          mockOrganizationId,
          'CASCADE001',
          'Cascade Test Color',
          '#123456'
        );

        repository.addColorRelationship(productId, colorId, mockOrganizationId);

        const success = repository.cascadeDelete(productId, mockOrganizationId);
        expect(success).toBe(true);

        // Orphaned color should be soft deleted
        const color = db
          .prepare('SELECT deleted_at FROM colors WHERE external_id = ?')
          .get(colorId) as { deleted_at: string | null };
        expect(color.deleted_at).toBeTruthy();
      });

      test('should return false for non-existent product', () => {
        const success = repository.cascadeDelete(
          'non-existent',
          mockOrganizationId
        );
        expect(success).toBe(false);
      });
    });
  });

  describe('Deduplication Operations', () => {
    describe('deduplicateByName', () => {
      test('should merge duplicate products by name', () => {
        // Create duplicate products
        const productName = 'Duplicate Test Product';
        const product1Id = repository.insert(
          { name: productName, organizationId: mockOrganizationId },
          mockOrganizationId
        );
        const product2Id = repository.insert(
          { name: productName, organizationId: mockOrganizationId },
          mockOrganizationId
        );

        // Add colors to second product to test merging logic
        repository.addColorRelationship(
          product2Id,
          'test-color-1',
          mockOrganizationId
        );
        repository.addColorRelationship(
          product2Id,
          'test-color-2',
          mockOrganizationId
        );

        const result = repository.deduplicateByName(mockOrganizationId);

        expect(result.success).toBe(true);
        expect(result.deduplicatedCount).toBeGreaterThan(0);

        // Only one product with that name should remain
        const remainingProducts = repository
          .findAll(mockOrganizationId)
          .filter(p => p.name === productName);
        expect(remainingProducts).toHaveLength(1);

        // The remaining product should have the colors
        const productWithColors = repository.findByIdWithColors(
          remainingProducts[0].id,
          mockOrganizationId
        );
        expect(productWithColors.colors.length).toBeGreaterThan(0);
      });

      test('should keep product with most colors', () => {
        const productName = 'Color Priority Test';

        // Product 1 with 1 color
        const product1Id = repository.insert(
          { name: productName, organizationId: mockOrganizationId },
          mockOrganizationId
        );
        repository.addColorRelationship(
          product1Id,
          'test-color-1',
          mockOrganizationId
        );

        // Product 2 with 2 colors
        const product2Id = repository.insert(
          { name: productName, organizationId: mockOrganizationId },
          mockOrganizationId
        );
        repository.addColorRelationship(
          product2Id,
          'test-color-1',
          mockOrganizationId
        );
        repository.addColorRelationship(
          product2Id,
          'test-color-2',
          mockOrganizationId
        );

        const result = repository.deduplicateByName(mockOrganizationId);

        expect(result.success).toBe(true);

        // The product with more colors should be kept
        const remainingProducts = repository
          .findAll(mockOrganizationId)
          .filter(p => p.name === productName);
        expect(remainingProducts).toHaveLength(1);

        const productWithColors = repository.findByIdWithColors(
          remainingProducts[0].id,
          mockOrganizationId
        );
        expect(productWithColors.colors.length).toBe(2);
      });

      test('should handle products with no duplicates', () => {
        const result = repository.deduplicateByName(mockOrganizationId);

        expect(result.success).toBe(true);
        expect(result.deduplicatedCount).toBeGreaterThanOrEqual(0);
        expect(result.errors).toBeInstanceOf(Array);
      });

      test('should provide error details for failed operations', () => {
        // This test would require creating a scenario that causes errors
        // For now, we'll just verify the error array structure
        const result = repository.deduplicateByName(mockOrganizationId);

        expect(result.errors).toBeInstanceOf(Array);
        // In a real failure scenario, errors would contain descriptive messages
      });
    });
  });

  describe('Query Operations', () => {
    describe('search', () => {
      test('should find products by name substring', () => {
        const searchQuery = 'Alpha';
        const results = repository.search(searchQuery, mockOrganizationId);

        expect(results).toBeInstanceOf(Array);

        // All results should contain the search term
        results.forEach(product => {
          expect(product.name.toLowerCase()).toContain(
            searchQuery.toLowerCase()
          );
        });
      });

      test('should return empty array for no matches', () => {
        const results = repository.search(
          'NonExistentProduct',
          mockOrganizationId
        );
        expect(results).toHaveLength(0);
      });

      test('should be case insensitive', () => {
        const results1 = repository.search('alpha', mockOrganizationId);
        const results2 = repository.search('ALPHA', mockOrganizationId);
        const results3 = repository.search('Alpha', mockOrganizationId);

        expect(results1.length).toBe(results2.length);
        expect(results2.length).toBe(results3.length);
      });

      test('should exclude soft-deleted products', () => {
        const productId = 'test-product-1';
        const productName = repository.findById(
          productId,
          mockOrganizationId
        )!.name;

        // Should find it before deletion
        const beforeDelete = repository.search(productName, mockOrganizationId);
        expect(beforeDelete.length).toBeGreaterThan(0);

        // Soft delete
        repository.softDelete(productId, mockOrganizationId);

        // Should not find it after deletion
        const afterDelete = repository.search(productName, mockOrganizationId);
        expect(afterDelete.length).toBe(beforeDelete.length - 1);
      });
    });

    describe('findByName', () => {
      test('should find product by exact name', () => {
        const productName = 'Product Alpha';
        const product = repository.findByName(productName, mockOrganizationId);

        expect(product).toBeTruthy();
        expect(product!.name).toBe(productName);
      });

      test('should return null for non-existent name', () => {
        const product = repository.findByName(
          'Non-existent Product',
          mockOrganizationId
        );
        expect(product).toBeNull();
      });

      test('should be case sensitive', () => {
        const productName = 'Product Alpha';
        const product1 = repository.findByName(productName, mockOrganizationId);
        const product2 = repository.findByName(
          productName.toLowerCase(),
          mockOrganizationId
        );

        expect(product1).toBeTruthy();
        expect(product2).toBeNull();
      });

      test('should return null for soft-deleted product', () => {
        const productId = 'test-product-1';
        const productName = repository.findById(
          productId,
          mockOrganizationId
        )!.name;

        repository.softDelete(productId, mockOrganizationId);

        const product = repository.findByName(productName, mockOrganizationId);
        expect(product).toBeNull();
      });
    });

    describe('findUnsynced', () => {
      test('should return products that need syncing', () => {
        // Mark a product as unsynced
        const productId = 'test-product-1';
        db.prepare(
          'UPDATE products SET is_synced = 0 WHERE external_id = ?'
        ).run(productId);

        const unsyncedProducts = repository.findUnsynced();

        expect(unsyncedProducts).toBeInstanceOf(Array);
        const unsyncedProduct = unsyncedProducts.find(p => p.id === productId);
        expect(unsyncedProduct).toBeTruthy();
      });

      test('should not return synced products', () => {
        // Ensure all products are synced
        db.prepare('UPDATE products SET is_synced = 1').run();

        const unsyncedProducts = repository.findUnsynced();
        expect(unsyncedProducts).toHaveLength(0);
      });

      test('should not return soft-deleted products', () => {
        const productId = 'test-product-1';

        // Make unsynced and soft delete
        db.prepare(
          'UPDATE products SET is_synced = 0 WHERE external_id = ?'
        ).run(productId);
        repository.softDelete(productId, mockOrganizationId);

        const unsyncedProducts = repository.findUnsynced();
        const deletedProduct = unsyncedProducts.find(p => p.id === productId);
        expect(deletedProduct).toBeUndefined();
      });
    });
  });

  describe('Utility Methods', () => {
    describe('markAsSynced', () => {
      test('should mark product as synced', () => {
        const productId = 'test-product-1';

        // Ensure it's unsynced first
        db.prepare(
          'UPDATE products SET is_synced = 0 WHERE external_id = ?'
        ).run(productId);

        repository.markAsSynced(productId);

        const syncStatus = db
          .prepare('SELECT is_synced FROM products WHERE external_id = ?')
          .get(productId) as { is_synced: number };
        expect(syncStatus.is_synced).toBe(1);
      });

      test('should handle non-existent product gracefully', () => {
        expect(() => {
          repository.markAsSynced('non-existent');
        }).not.toThrow();
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors gracefully', () => {
      // Close database to simulate error
      db.close();

      expect(() => {
        repository.findAll(mockOrganizationId);
      }).toThrow();
    });

    test('should validate organization ID format', () => {
      expect(() => {
        repository.findAll('');
      }).toThrow();
    });

    test('should handle malformed product data', () => {
      const malformedProduct = {
        name: null, // Invalid
        organizationId: 'invalid-org-id',
      } as any;

      expect(() => {
        repository.insert(malformedProduct, mockOrganizationId);
      }).toThrow();
    });
  });

  describe('Performance', () => {
    test('should handle large datasets efficiently', () => {
      // Insert many products
      const productIds = [];
      for (let i = 0; i < 100; i++) {
        const productId = repository.insert(
          {
            name: `Performance Product ${i}`,
            description: `Description for product ${i}`,
            organizationId: mockOrganizationId,
          },
          mockOrganizationId
        );
        productIds.push(productId);
      }

      const startTime = Date.now();
      const allProducts = repository.findAll(mockOrganizationId);
      const endTime = Date.now();

      expect(allProducts.length).toBeGreaterThanOrEqual(100);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete in under 1 second
    });

    test('should efficiently handle complex relationship queries', () => {
      // Create products with many color relationships
      for (let i = 0; i < 10; i++) {
        const productId = repository.insert(
          {
            name: `Relationship Product ${i}`,
            organizationId: mockOrganizationId,
          },
          mockOrganizationId
        );

        // Add multiple colors to each product
        repository.addColorRelationship(
          productId,
          'test-color-1',
          mockOrganizationId
        );
        repository.addColorRelationship(
          productId,
          'test-color-2',
          mockOrganizationId
        );
      }

      const startTime = Date.now();
      const productsWithColors = repository.findWithColors(mockOrganizationId);
      const endTime = Date.now();

      expect(productsWithColors.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(500); // Should be fast even with relationships
    });

    test('should efficiently handle bulk operations', () => {
      // Create many products for bulk operations
      const productIds = [];
      for (let i = 0; i < 50; i++) {
        const productId = repository.insert(
          {
            name: `Bulk Product ${i}`,
            organizationId: mockOrganizationId,
          },
          mockOrganizationId
        );
        productIds.push(productId);
      }

      const startTime = Date.now();
      const result = repository.deleteMultiple(productIds, mockOrganizationId);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.deletedIds.length).toBe(50);
      expect(endTime - startTime).toBeLessThan(1000); // Bulk operations should be efficient
    });
  });

  describe('Transaction Management', () => {
    test('should handle transaction rollback on errors', () => {
      const originalCount = repository.findAll(mockOrganizationId).length;

      // Simulate a transaction that should fail partway through
      const productId = repository.insert(
        {
          name: 'Transaction Test Product',
          organizationId: mockOrganizationId,
        },
        mockOrganizationId
      );

      try {
        // This should succeed in a transaction
        repository.cascadeDelete(productId, mockOrganizationId);
      } catch (error) {
        // If transaction fails, data should be consistent
        const currentCount = repository.findAll(mockOrganizationId).length;
        expect(currentCount).toBe(originalCount);
      }
    });

    test('should maintain referential integrity during bulk operations', () => {
      const productId = repository.insert(
        {
          name: 'Integrity Test Product',
          organizationId: mockOrganizationId,
        },
        mockOrganizationId
      );

      // Add color relationship
      repository.addColorRelationship(
        productId,
        'test-color-1',
        mockOrganizationId
      );

      // Delete the product
      repository.cascadeDelete(productId, mockOrganizationId);

      // Verify no orphaned relationships exist
      const orphanedRelationships = db
        .prepare(
          `
        SELECT COUNT(*) as count 
        FROM product_colors pc
        LEFT JOIN products p ON pc.product_id = p.id AND p.deleted_at IS NULL
        WHERE p.id IS NULL
      `
        )
        .get() as { count: number };

      expect(orphanedRelationships.count).toBe(0);
    });
  });

  describe('Data Validation', () => {
    test('should validate required fields on insert', () => {
      expect(() => {
        repository.insert(
          {
            name: '', // Empty name should fail
            organizationId: mockOrganizationId,
          },
          mockOrganizationId
        );
      }).toThrow();
    });

    test('should validate organization ID format', () => {
      expect(() => {
        repository.insert(
          {
            name: 'Valid Product',
            organizationId: 'invalid-uuid-format',
          },
          'invalid-uuid-format'
        );
      }).toThrow();
    });

    test('should handle metadata parsing errors gracefully', () => {
      // Insert product with malformed metadata directly
      db.prepare(
        `
        INSERT INTO products (external_id, name, metadata, organization_id, created_at, updated_at, is_active)
        VALUES (?, ?, ?, ?, ?, ?, 1)
      `
      ).run(
        'malformed-product',
        'Malformed Metadata Product',
        'invalid-json{',
        mockOrganizationId,
        new Date().toISOString(),
        new Date().toISOString()
      );

      // Should handle gracefully without crashing
      expect(() => {
        const product = repository.findById(
          'malformed-product',
          mockOrganizationId
        );
        expect(product).toBeTruthy();
      }).not.toThrow();
    });
  });
});

// Helper functions for test setup
function setupTestSchema(db: Database.Database) {
  // Create tables matching the actual schema
  db.exec(`
    CREATE TABLE products (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      external_id TEXT NOT NULL,
      organization_id TEXT NOT NULL,
      user_id TEXT,
      name TEXT NOT NULL,
      description TEXT,
      metadata TEXT,
      created_by TEXT,
      is_active BOOLEAN DEFAULT 1,
      is_synced BOOLEAN DEFAULT 0,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT NULL
    );
    
    CREATE TABLE colors (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      external_id TEXT NOT NULL,
      organization_id TEXT NOT NULL,
      source_id INTEGER,
      code TEXT NOT NULL,
      display_name TEXT,
      hex TEXT NOT NULL,
      color_spaces TEXT,
      is_gradient BOOLEAN DEFAULT 0,
      is_metallic BOOLEAN DEFAULT 0,
      is_effect BOOLEAN DEFAULT 0,
      is_library BOOLEAN DEFAULT 0,
      gradient_colors TEXT,
      notes TEXT,
      tags TEXT,
      properties TEXT,
      is_active BOOLEAN DEFAULT 1,
      is_synced BOOLEAN DEFAULT 0,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT NULL
    );
    
    CREATE TABLE product_colors (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      product_id INTEGER NOT NULL,
      color_id INTEGER NOT NULL,
      organization_id TEXT NOT NULL,
      display_order INTEGER DEFAULT 0,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (product_id) REFERENCES products(id),
      FOREIGN KEY (color_id) REFERENCES colors(id)
    );
    
    CREATE TABLE datasheets (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      product_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      path TEXT NOT NULL,
      is_active BOOLEAN DEFAULT 1,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (product_id) REFERENCES products(id)
    );
    
    CREATE INDEX idx_products_org_deleted ON products(organization_id, deleted_at);
    CREATE INDEX idx_products_name ON products(name) WHERE deleted_at IS NULL;
    CREATE INDEX idx_products_sync ON products(is_synced) WHERE deleted_at IS NULL;
    CREATE INDEX idx_product_colors_org ON product_colors(organization_id);
    CREATE INDEX idx_colors_org_deleted ON colors(organization_id, deleted_at);
  `);
}

function seedTestData(db: Database.Database, organizationId: string) {
  // Insert test products
  const testProducts = [
    {
      external_id: 'test-product-1',
      name: 'Product Alpha',
      description: 'First test product',
      metadata: JSON.stringify({ description: 'Alpha product description' }),
    },
    {
      external_id: 'test-product-2',
      name: 'Product Beta',
      description: 'Second test product',
      metadata: JSON.stringify({ description: 'Beta product description' }),
    },
  ];

  const insertProduct = db.prepare(`
    INSERT INTO products (external_id, name, description, metadata, organization_id, is_active, is_synced)
    VALUES (?, ?, ?, ?, ?, 1, 1)
  `);

  testProducts.forEach(product => {
    insertProduct.run(
      product.external_id,
      product.name,
      product.description,
      product.metadata,
      organizationId
    );
  });

  // Insert test colors
  const testColors = [
    {
      external_id: 'test-color-1',
      code: 'RAL5002',
      display_name: 'Ultramarine Blue',
      hex: '#20214F',
      color_spaces: JSON.stringify({ cmyk: { c: 100, m: 95, y: 0, k: 69 } }),
    },
    {
      external_id: 'test-color-2',
      code: 'RAL6018',
      display_name: 'Yellow Green',
      hex: '#57A639',
      color_spaces: JSON.stringify({ cmyk: { c: 48, m: 0, y: 78, k: 35 } }),
    },
  ];

  const insertColor = db.prepare(`
    INSERT INTO colors (external_id, organization_id, code, display_name, hex, color_spaces, is_active)
    VALUES (?, ?, ?, ?, ?, ?, 1)
  `);

  testColors.forEach(color => {
    insertColor.run(
      color.external_id,
      organizationId,
      color.code,
      color.display_name,
      color.hex,
      color.color_spaces
    );
  });

  // Create some product-color relationships
  const insertRelationship = db.prepare(`
    INSERT INTO product_colors (product_id, color_id, organization_id, display_order)
    VALUES (?, ?, ?, ?)
  `);

  // Get internal IDs for relationships
  const product1 = db
    .prepare('SELECT id FROM products WHERE external_id = ?')
    .get('test-product-1') as { id: number };
  const product2 = db
    .prepare('SELECT id FROM products WHERE external_id = ?')
    .get('test-product-2') as { id: number };
  const color1 = db
    .prepare('SELECT id FROM colors WHERE external_id = ?')
    .get('test-color-1') as { id: number };
  const color2 = db
    .prepare('SELECT id FROM colors WHERE external_id = ?')
    .get('test-color-2') as { id: number };

  insertRelationship.run(product1.id, color1.id, organizationId, 1);
  insertRelationship.run(product2.id, color1.id, organizationId, 1);
  insertRelationship.run(product1.id, color2.id, organizationId, 2);
}
