import React from 'react';
import { AlertCircle } from 'lucide-react';
import { ErrorBoundary } from './ErrorBoundary';

interface ComponentErrorBoundaryProps {
  children: React.ReactNode;
  componentName?: string;
  onError?: (error: Error) => void;
}

export function ComponentErrorBoundary({
  children,
  componentName = 'Component',
  onError,
}: ComponentErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={
        <div className='p-4 border border-feedback-error/20 bg-feedback-error/5 rounded-lg'>
          <div className='flex items-start gap-3'>
            <AlertCircle className='w-5 h-5 text-feedback-error mt-0.5 flex-shrink-0' />
            <div className='flex-1'>
              <h3 className='font-medium text-ui-foreground-primary mb-1'>
                {componentName} Error
              </h3>
              <p className='text-sm text-ui-foreground-secondary'>
                This component encountered an error and couldn't be displayed.
                Try refreshing the page or contact support if the problem
                persists.
              </p>
            </div>
          </div>
        </div>
      }
      onError={(error, errorInfo) => {
        console.error(`Error in ${componentName}:`, error, errorInfo);
        onError?.(error);
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
