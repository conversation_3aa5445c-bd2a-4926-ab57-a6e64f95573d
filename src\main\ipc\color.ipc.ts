/**
 * @file color-migrated.ipc.ts
 * @description Migrated color IPC handlers using dependency injection + universal wrapper pattern
 * This demonstrates the complete migration from the existing color.ipc.ts
 */

import { ipcMain, dialog } from 'electron';
import { ColorChannels, NewColorEntry, UpdateColorEntry } from '../../shared/types/color.types';
import { queueImmediateSync } from '../utils/immediate-sync';
import {
  registerSecure<PERSON><PERSON><PERSON>,
  registerSystemHandler,
  createSuccessResponse
} from '../utils/ipc-wrapper';
import { ServiceLocator } from '../services/service-locator';
import { ColorService } from '../db/services/color.service';
import { ColorImportService } from '../db/services/color-import.service';
import { unregisterHandler } from '../utils/ipcRegistry';
import { syncOutboxService } from '../services/sync/sync-outbox.service';
import fs from 'fs';
import path from 'path';

/**
 * Service dependencies interface
 */
interface ColorServices {
  colorService: ColorService;
  colorImportService: ColorImportService;
}

/**
 * Register color IPC handlers with dependency injection and universal wrapper
 * 
 * @param services - Optional service bundle for dependency injection
 */
export function registerColorHandlers(services?: Partial<ColorServices>): void {
  console.log('[ColorIPC] 🚀 STARTING COLOR HANDLER REGISTRATION...');
  console.log('[ColorIPC] Registering color handlers with dependency injection');

  // Use dependency injection or fallback to ServiceLocator
  const colorService = services?.colorService || ServiceLocator.getColorService();
  const colorImportService = services?.colorImportService || ServiceLocator.getColorImportService();

  console.log('[ColorIPC] Services available:', {
    colorService: !!colorService,
    colorImportService: !!colorImportService
  });

  // ===== REMOVE ALL FALLBACK HANDLERS FIRST =====
  console.log('[ColorIPC] 🧹 Removing fallback handlers...');
  
  // Remove fallback handlers for all color channels
  const colorChannelsToUnregister = [
    ColorChannels.GET_ALL,
    ColorChannels.GET_ALL_WITH_USAGE,
    ColorChannels.GET_BY_ID,
    ColorChannels.ADD,
    ColorChannels.UPDATE,
    ColorChannels.DELETE,
    ColorChannels.CLEAR_ALL,
    ColorChannels.GET_USAGE_COUNTS,
    ColorChannels.GET_PRODUCTS_BY_COLOR_NAME,
    ColorChannels.IMPORT,
    ColorChannels.EXPORT,
    ColorChannels.NORMALIZE_PANTONE_CODES,
    ColorChannels.CLEAR_FRONTEND_STATE,
    ColorChannels.ADMIN_CLEAR_ALL
  ];

  colorChannelsToUnregister.forEach(channel => {
    try {
      unregisterHandler(ipcMain, channel);
      console.log(`[ColorIPC] 🧹 Unregistered fallback handler for: ${channel}`);
    } catch (error) {
      console.log(`[ColorIPC] 🧹 No fallback handler to remove for: ${channel}`);
    }
  });

  // ===== CORE COLOR OPERATIONS =====

  // Get all colors
  console.log('[ColorIPC] 📝 Registering GET_ALL handler...');
  registerSecureHandler(
    ColorChannels.GET_ALL,
    async (organizationId: string) => {
      const colors = await colorService.getAll(organizationId);
      await queueImmediateSync();
      return createSuccessResponse(colors, `Retrieved ${colors.length} colors`);
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to retrieve colors. Please try again.',
      skipDuplicateCheck: true
    }
  );
  console.log('[ColorIPC] ✅ GET_ALL handler registered');

  // Get color by ID
  registerSecureHandler(
    ColorChannels.GET_BY_ID,
    async (organizationId: string, colorId: string) => {
      const color = await colorService.getById(colorId, organizationId);
      return createSuccessResponse(color, color ? 'Color retrieved successfully' : 'Color not found');
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to retrieve color. Please try again.'
    }
  );

  // Add new color - with mandatory product association
  registerSecureHandler(
    ColorChannels.ADD,
    async (organizationId: string, colorData: NewColorEntry) => {
      console.log('[ColorIPC] 🎨 Adding color with data:', { 
        name: colorData.name, 
        code: colorData.code, 
        product: colorData.product,
        organizationId 
      });

      // CRITICAL: Enforce product requirement at the IPC level
      if (!colorData.product || typeof colorData.product !== 'string' || colorData.product.trim().length === 0) {
        throw new Error('Product association is required - every color must belong to a product. Please specify which product this color belongs to.');
      }

      // Get current user ID for audit trail
      let userId: string | undefined;
      try {
        const oauthService = ServiceLocator.getOAuthService();
        const currentUser = await oauthService.getCurrentUser();
        userId = currentUser?.id;
        console.log('[ColorIPC] Current user for color creation:', userId);
      } catch (error) {
        console.warn('[ColorIPC] Could not get current user ID:', error);
      }
      
      // ColorService now handles product association automatically
      const newColorId = await colorService.add(colorData, userId, organizationId);
      const newColor = await colorService.getById(newColorId, organizationId);
      
      await syncOutboxService.addToOutbox('colors', 'create', { id: newColor?.id, organizationId });
      await queueImmediateSync();
      return createSuccessResponse(newColor, `Color added successfully and associated with product "${colorData.product}"`);
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to add color. Please try again.'
    }
  );

  // Update color
  registerSecureHandler(
    ColorChannels.UPDATE,
    async (organizationId: string, colorId: string, updates: UpdateColorEntry) => {
      const updated = await colorService.update(colorId, updates, organizationId);
      if (updated) {
        await syncOutboxService.addToOutbox('colors', 'update', { id: colorId, organizationId });
      }
      await queueImmediateSync();
      return createSuccessResponse(updated, 'Color updated successfully');
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to update color. Please try again.'
    }
  );

  // Delete color
  registerSecureHandler(
    ColorChannels.DELETE,
    async (organizationId: string, colorId: string) => {
      const result = await colorService.delete(colorId, organizationId);
      if (result) {
        await syncOutboxService.addToOutbox('colors', 'delete', { id: colorId });
      }
      await queueImmediateSync();
      return createSuccessResponse(result, result ? 'Color deleted successfully' : 'Color deletion failed');
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to delete color. Please try again.'
    }
  );

  // Clear all colors
  registerSecureHandler(
    ColorChannels.CLEAR_ALL,
    async (organizationId: string, hardDelete?: boolean) => {
      const result = await colorService.clearAll(organizationId, hardDelete);
      await queueImmediateSync();
      return createSuccessResponse(result, result ? 'Colors cleared successfully' : 'Failed to clear colors');
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to clear colors. Please try again.'
    }
  );

  // ===== USAGE AND ANALYTICS =====

  // Get usage counts
  registerSecureHandler(
    ColorChannels.GET_USAGE_COUNTS,
    async (organizationId: string) => {
      const usageCounts = await colorService.getUsageCounts(organizationId);
      return createSuccessResponse(usageCounts, `Retrieved usage data for ${Object.keys(usageCounts).length} colors`);
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to retrieve color usage data. Please try again.'
    }
  );

  // Get all colors with usage
  console.log('[ColorIPC] 📝 Registering GET_ALL_WITH_USAGE handler...');
  registerSecureHandler(
    ColorChannels.GET_ALL_WITH_USAGE,
    async (organizationId: string) => {
      console.log('[ColorIPC] 🔄 GET_ALL_WITH_USAGE called for organization:', organizationId);
      const result = await colorService.getAllWithUsage(organizationId);
      console.log('[ColorIPC] 📊 Retrieved colors with usage:', result.colors?.length || 0);
      return createSuccessResponse(result, `Retrieved ${result.colors?.length || 0} colors with usage data`);
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to retrieve colors with usage data. Please try again.',
      skipDuplicateCheck: true
    }
  );
  console.log('[ColorIPC] ✅ GET_ALL_WITH_USAGE handler registered');

  // Get products by color name
  registerSecureHandler(
    ColorChannels.GET_PRODUCTS_BY_COLOR_NAME,
    async (organizationId: string) => {
      const productsByColor = await colorService.getProductsByColorName(organizationId);
      return createSuccessResponse(productsByColor, `Retrieved product associations for ${Object.keys(productsByColor).length} colors`);
    },
    ipcMain,
    {
      logChannel: 'ColorIPC',
      customErrorMessage: 'Failed to retrieve color-product associations. Please try again.'
    }
  );

  // ===== IMPORT/EXPORT OPERATIONS =====

  // Import colors
  registerSecureHandler(
    ColorChannels.IMPORT,
    async (organizationId: string, mergeMode?: 'replace' | 'merge', filePath?: string, format?: 'json' | 'csv') => {
      let selectedFilePath = filePath;
      
      if (!selectedFilePath) {
        // Open file dialog
        const result = await dialog.showOpenDialog({
          title: 'Import Colors',
          filters: [
            { name: 'JSON Files', extensions: ['json'] },
            { name: 'CSV Files', extensions: ['csv'] },
            { name: 'All Files', extensions: ['*'] }
          ],
          properties: ['openFile']
        });
        
        if (result.canceled || !result.filePaths[0]) {
          return createSuccessResponse({ added: 0, errors: ['Import cancelled by user'] }, 'Import cancelled');
        }
        
        selectedFilePath = result.filePaths[0];
      }
      
      // Determine format from file extension if not provided
      const fileFormat = format || (path.extname(selectedFilePath).toLowerCase() === '.csv' ? 'csv' : 'json');
      
      // Read and validate file
      if (!fs.existsSync(selectedFilePath)) {
        throw new Error(`File not found: ${selectedFilePath}`);
      }
      
      const importResult = await colorImportService.importColors(
        selectedFilePath,
        organizationId,
        mergeMode || 'merge',
        fileFormat
      );
      
      await queueImmediateSync();
      
      return createSuccessResponse(
        importResult,
        `Import completed: ${importResult.added} colors added${importResult.errors.length > 0 ? `, ${importResult.errors.length} errors` : ''}`
      );
    },
    ipcMain,
    {
      logChannel: 'ColorImport',
      customErrorMessage: 'Failed to import colors. Please check your file format and try again.'
    }
  );

  // Export colors
  registerSecureHandler(
    ColorChannels.EXPORT,
    async (organizationId: string, filePath?: string, format?: 'json' | 'csv') => {
      let selectedFilePath = filePath;
      
      if (!selectedFilePath) {
        // Open save dialog
        const result = await dialog.showSaveDialog({
          title: 'Export Colors',
          defaultPath: `colors-${new Date().toISOString().split('T')[0]}.json`,
          filters: [
            { name: 'JSON Files', extensions: ['json'] },
            { name: 'CSV Files', extensions: ['csv'] }
          ]
        });
        
        if (result.canceled || !result.filePath) {
          return createSuccessResponse(false, 'Export cancelled');
        }
        
        selectedFilePath = result.filePath;
      }
      
      // Determine format from file extension if not provided
      const fileFormat = format || (path.extname(selectedFilePath).toLowerCase() === '.csv' ? 'csv' : 'json');
      
      const success = await colorImportService.exportColors(
        selectedFilePath,
        organizationId,
        fileFormat
      );
      
      return createSuccessResponse(
        success,
        success ? `Colors exported successfully to ${selectedFilePath}` : 'Export failed'
      );
    },
    ipcMain,
    {
      logChannel: 'ColorExport',
      customErrorMessage: 'Failed to export colors. Please try again.'
    }
  );

  // ===== UTILITY OPERATIONS =====

  // Normalize Pantone codes
  registerSecureHandler(
    ColorChannels.NORMALIZE_PANTONE_CODES,
    async (organizationId: string) => {
      const result = await colorService.normalizePantoneCodes(organizationId);
      await queueImmediateSync();
      return createSuccessResponse(result, result.success ? result.message : 'Normalization completed with issues');
    },
    ipcMain,
    {
      logChannel: 'ColorUtility',
      customErrorMessage: 'Failed to normalize Pantone codes. Please try again.'
    }
  );

  // Clear frontend state (system-level operation, no organization context needed)
  registerSystemHandler(
    ColorChannels.CLEAR_FRONTEND_STATE,
    async () => {
      // This is a frontend state clearing operation - no backend action needed
      return createSuccessResponse(true, 'Frontend state cleared');
    },
    ipcMain,
    {
      logChannel: 'ColorSystem',
      customErrorMessage: 'Failed to clear frontend state.'
    }
  );

  // Admin clear all (system-level operation with organization context)
  registerSecureHandler(
    ColorChannels.ADMIN_CLEAR_ALL,
    async (organizationId: string, options?: { hardDelete?: boolean }) => {
      const result = await colorService.clearAll(organizationId, options?.hardDelete);
      await queueImmediateSync();
      return createSuccessResponse(
        result,
        result ? 'Admin clear completed successfully' : 'Admin clear failed'
      );
    },
    ipcMain,
    {
      logChannel: 'ColorAdmin',
      customErrorMessage: 'Failed to perform admin clear operation. Please try again.'
    }
  );

  // Gradient data migration
  registerSecureHandler(
    'color:migrateGradientData',
    async (_organizationId: string) => {
      console.log('[ColorIPC] Running gradient data migration...');
      try {
        const { runGradientMigration } = await import('../migrations/migrate-gradient-data');
        await runGradientMigration();
        console.log('[ColorIPC] ✅ Gradient migration completed successfully');
        return createSuccessResponse({ success: true }, 'Gradient data migration completed successfully');
      } catch (error) {
        console.error('[ColorIPC] ❌ Gradient migration failed:', error);
        throw new Error(`Migration failed: ${error}`);
      }
    },
    ipcMain,
    { logChannel: 'ColorIPC' }
  );

  console.log('[ColorIPC] ✅ All color handlers registered successfully');
  console.log('[ColorIPC] 🎯 Final handler count verification...');
  
  // Verify critical handlers are registered
  const criticalHandlers = [ColorChannels.GET_ALL, ColorChannels.GET_ALL_WITH_USAGE];
  criticalHandlers.forEach(channel => {
    const handlers = (ipcMain as any)._events;
    const handlerExists = handlers && handlers[channel];
    console.log(`[ColorIPC] 🔍 Handler '${channel}' registered:`, !!handlerExists);
  });
}

/**
 * Alternative registration using only ServiceLocator
 */
export function registerColorHandlersFromLocator(): void {
  console.log('[ColorIPC] Registering color handlers using ServiceLocator pattern');
  registerColorHandlers();
}