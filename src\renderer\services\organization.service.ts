/**
 * @file organization.service.ts
 * @description Service for organization-related operations with separated concerns
 */

import type { Organization } from '../../shared/types/organization.types';

/**
 * Service for handling organization creation and setup
 * Separates concerns from the Zustand store
 */
export class OrganizationService {
  /**
   * Create a new organization via API
   */
  static async createOrganization(
    name: string
  ): Promise<{ success: boolean; data?: Organization; error?: string }> {
    try {
      const result = await window.organizationAPI.createOrganization({ name });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to create organization';
      console.error(
        '[OrganizationService] Create organization failed:',
        errorMessage
      );
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Set organization as current in backend
   */
  static async setCurrentOrganization(orgId: string): Promise<void> {
    await window.organizationAPI.setCurrentOrganization(orgId);
  }

  /**
   * Initialize sync for organization
   */
  static async initializeSyncForOrganization(
    orgId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await window.syncAPI.initialize();
      console.log(
        '[OrganizationService] Sync initialized for organization:',
        orgId
      );
      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to initialize sync';
      console.warn(
        '[OrganizationService] Failed to initialize sync:',
        errorMessage
      );
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Load data for organization (colors and products)
   */
  static async loadOrganizationData(): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Dynamically import to avoid circular dependencies
      const [colorStore, productStore] = await Promise.all([
        import('../store/color.store').then(m => m.useColorStore.getState()),
        import('../store/product.store').then(m =>
          m.useProductStore.getState()
        ),
      ]);

      await Promise.all([
        colorStore.loadColorsWithUsage(),
        productStore.fetchProducts(),
      ]);

      console.log(
        '[OrganizationService] Organization data loaded successfully'
      );
      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to load organization data';
      console.error(
        '[OrganizationService] Error loading organization data:',
        errorMessage
      );
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Complete organization setup (all post-creation tasks)
   */
  static async completeOrganizationSetup(
    organization: Organization
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(
        '[OrganizationService] Completing setup for organization:',
        organization.name
      );

      // 1. Set as current organization in backend
      await this.setCurrentOrganization(organization.external_id);

      // 2. Load initial data
      const dataResult = await this.loadOrganizationData();
      if (!dataResult.success) {
        console.warn(
          '[OrganizationService] Data loading failed, but continuing setup'
        );
      }

      // 3. Initialize sync (non-blocking)
      const syncResult = await this.initializeSyncForOrganization(
        organization.external_id
      );
      if (!syncResult.success) {
        console.warn(
          '[OrganizationService] Sync initialization failed, but continuing setup'
        );
      }

      console.log(
        '[OrganizationService] Organization setup completed successfully'
      );
      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to complete organization setup';
      console.error('[OrganizationService] Setup failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Validate organization name
   */
  static validateOrganizationName(name: string): {
    valid: boolean;
    error?: string;
  } {
    if (!name || name.trim().length === 0) {
      return { valid: false, error: 'Organization name is required' };
    }

    if (name.trim().length < 2) {
      return {
        valid: false,
        error: 'Organization name must be at least 2 characters',
      };
    }

    if (name.trim().length > 100) {
      return {
        valid: false,
        error: 'Organization name must be less than 100 characters',
      };
    }

    // Basic validation for allowed characters
    const validNamePattern = /^[a-zA-Z0-9\s\-_.&()]+$/;
    if (!validNamePattern.test(name.trim())) {
      return {
        valid: false,
        error: 'Organization name contains invalid characters',
      };
    }

    return { valid: true };
  }
}
