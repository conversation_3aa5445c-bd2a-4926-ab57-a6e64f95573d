/**
 * @file resource-management-integration.test.ts
 * @description Integration tests for the complete resource management and cleanup system
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { resourceManager } from '../main/services/sync/resource-manager';
import { syncShutdownCoordinator } from '../main/services/sync/sync-shutdown-coordinator';

// Mock the sync services
vi.mock('../main/services/sync/sync-outbox.service', () => ({
  syncOutboxService: {
    stopPeriodicCleanup: vi.fn(),
    getPendingChanges: vi.fn(() => []),
    clearAll: vi.fn()
  }
}));

vi.mock('../main/services/sync/sync-status-manager.service', () => ({
  syncStatusManager: {
    destroy: vi.fn(),
    reset: vi.fn(),
    shutdown: vi.fn()
  }
}));

vi.mock('../main/services/sync/file-concurrency-controller', () => ({
  fileConcurrencyController: {
    cleanup: vi.fn().mockResolvedValue(undefined),
    acquireLock: vi.fn(),
    releaseLock: vi.fn(),
    isLocked: vi.fn(() => false)
  }
}));

vi.mock('../main/services/sync/unified-sync-manager', () => ({
  unifiedSyncManager: {
    cleanup: vi.fn().mockResolvedValue(undefined),
    getStatus: vi.fn(() => ({
      isSyncing: false,
      queueLength: 0,
      currentOperation: null
    })),
    sync: vi.fn().mockResolvedValue({
      success: true,
      itemsProcessed: 0,
      duration: 100
    })
  }
}));

describe('Resource Management Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks and managers
    vi.clearAllMocks();
    resourceManager.reset();
    syncShutdownCoordinator.reset();
  });

  afterEach(async () => {
    // Clean up after each test
    try {
      await resourceManager.cleanup();
      await syncShutdownCoordinator.shutdown({ forceShutdown: true });
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('Complete Resource Lifecycle', () => {
    it('should track, monitor, and cleanup resources throughout their lifecycle', async () => {
      // Phase 1: Resource Creation and Tracking
      const resources: string[] = [];
      
      // Create various types of resources
      const timer1 = setTimeout(() => {}, 5000);
      const timer2 = setTimeout(() => {}, 10000);
      const interval1 = setInterval(() => {}, 1000);
      const interval2 = setInterval(() => {}, 2000);
      
      // Track resources
      resources.push(
        resourceManager.trackTimer(timer1, { purpose: 'test_timer_1' }),
        resourceManager.trackTimer(timer2, { purpose: 'test_timer_2' }),
        resourceManager.trackInterval(interval1, { purpose: 'test_interval_1' }),
        resourceManager.trackInterval(interval2, { purpose: 'test_interval_2' })
      );
      
      // Mock event emitters for listener tracking
      const mockEmitter1 = {
        removeListener: vi.fn(),
        on: vi.fn(),
        emit: vi.fn()
      };
      const mockEmitter2 = {
        removeListener: vi.fn(),
        on: vi.fn(),
        emit: vi.fn()
      };
      
      resources.push(
        resourceManager.trackEventListener(mockEmitter1 as any, 'test-event-1', vi.fn(), 'context-1'),
        resourceManager.trackEventListener(mockEmitter2 as any, 'test-event-2', vi.fn(), 'context-2')
      );
      
      // Verify tracking
      const stats = resourceManager.getResourceStats();
      expect(stats.totalTracked).toBe(6);
      expect(stats.timers).toBe(2);
      expect(stats.intervals).toBe(2);
      expect(stats.listeners).toBe(2);
      
      // Phase 2: Memory Monitoring
      const initialMemory = resourceManager.getMemoryMetrics();
      expect(initialMemory.heapUsed).toBeGreaterThan(0);
      expect(initialMemory.timestamp).toBeGreaterThan(0);
      
      // Wait for memory monitoring to potentially record data
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Phase 3: Partial Cleanup (simulate normal operation)
      const untracked1 = resourceManager.untrackResource(resources[0]);
      const untracked2 = resourceManager.untrackResource(resources[2]);
      expect(untracked1).toBe(true);
      expect(untracked2).toBe(true);
      
      const partialStats = resourceManager.getResourceStats();
      expect(partialStats.totalTracked).toBe(4);
      expect(partialStats.timers).toBe(1);
      expect(partialStats.intervals).toBe(1);
      
      // Phase 4: Complete Cleanup
      const cleanupResult = await resourceManager.cleanup();
      expect(cleanupResult.success).toBe(true);
      expect(cleanupResult.resourcesCleanedUp).toBeGreaterThan(0);
      expect(cleanupResult.duration).toBeGreaterThan(0);
      
      // Verify complete cleanup
      const finalStats = resourceManager.getResourceStats();
      expect(finalStats.totalTracked).toBe(0);
      expect(finalStats.timers).toBe(0);
      expect(finalStats.intervals).toBe(0);
      expect(finalStats.listeners).toBe(0);
      
      // Verify event listeners were properly removed
      expect(mockEmitter1.removeListener).toHaveBeenCalled();
      expect(mockEmitter2.removeListener).toHaveBeenCalled();
    });
  });

  describe('Coordinated Shutdown Integration', () => {
    it('should perform coordinated shutdown of all sync components', async () => {
      // Create some resources to be cleaned up during shutdown
      const timer = setTimeout(() => {}, 10000);
      const interval = setInterval(() => {}, 1000);
      
      resourceManager.trackTimer(timer, { purpose: 'shutdown_test' });
      resourceManager.trackInterval(interval, { purpose: 'shutdown_test' });
      
      // Verify resources are tracked
      const initialStats = resourceManager.getResourceStats();
      expect(initialStats.totalTracked).toBeGreaterThan(0);
      
      // Perform coordinated shutdown
      const shutdownResult = await syncShutdownCoordinator.shutdown({
        gracefulTimeout: 5000,
        forceShutdown: false
      });
      
      // Verify shutdown was successful
      expect(shutdownResult.success).toBe(true);
      expect(shutdownResult.componentsShutdown).toContain('resource-manager');
      expect(shutdownResult.duration).toBeGreaterThan(0);
      expect(Array.isArray(shutdownResult.errors)).toBe(true);
      
      // Verify all resources were cleaned up
      const finalStats = resourceManager.getResourceStats();
      expect(finalStats.totalTracked).toBe(0);
      
      // Verify shutdown coordinator state
      expect(syncShutdownCoordinator.isShuttingDownState()).toBe(true);
    });

    it('should handle shutdown with active sync operations', async () => {
      // Mock an active sync operation
      const { unifiedSyncManager } = await import('../main/services/sync/unified-sync-manager');
      vi.mocked(unifiedSyncManager.getStatus).mockReturnValue({
        isSyncing: true,
        queueLength: 1,
        currentOperation: 'colors'
      });
      
      // Create resources
      const timer = setTimeout(() => {}, 5000);
      resourceManager.trackTimer(timer, { purpose: 'active_sync_test' });
      
      // Perform shutdown with a short timeout
      const shutdownResult = await syncShutdownCoordinator.shutdown({
        gracefulTimeout: 1000, // Short timeout
        forceShutdown: false
      });
      
      // Should still succeed even with active operations
      expect(shutdownResult.success).toBe(true);
      expect(shutdownResult.componentsShutdown.length).toBeGreaterThan(0);
    });

    it('should handle forced shutdown correctly', async () => {
      // Create resources
      const timer = setTimeout(() => {}, 30000); // Long timer
      const interval = setInterval(() => {}, 1000);
      
      resourceManager.trackTimer(timer, { purpose: 'force_shutdown_test' });
      resourceManager.trackInterval(interval, { purpose: 'force_shutdown_test' });
      
      // Force shutdown
      const shutdownResult = await syncShutdownCoordinator.shutdown({
        gracefulTimeout: 500,
        forceShutdown: true
      });
      
      expect(shutdownResult.success).toBe(true);
      expect(shutdownResult.duration).toBeLessThan(5000); // Should be quick
      
      // Resources should still be cleaned up
      const finalStats = resourceManager.getResourceStats();
      expect(finalStats.totalTracked).toBe(0);
    });
  });

  describe('Memory Monitoring Integration', () => {
    it('should integrate memory monitoring with resource tracking', async () => {
      let memoryEvents = 0;
      let cleanupEvents = 0;
      
      // Set up event listeners
      resourceManager.on('memory-threshold-exceeded', () => {
        memoryEvents++;
      });
      
      resourceManager.on('automatic-cleanup-performed', () => {
        cleanupEvents++;
      });
      
      // Create many resources to potentially trigger memory monitoring
      const resources: string[] = [];
      for (let i = 0; i < 50; i++) {
        const timer = setTimeout(() => {}, 10000);
        const interval = setInterval(() => {}, 5000);
        
        resources.push(
          resourceManager.trackTimer(timer, { iteration: i, purpose: 'memory_test' }),
          resourceManager.trackInterval(interval, { iteration: i, purpose: 'memory_test' })
        );
      }
      
      // Get memory metrics
      const metrics = resourceManager.getMemoryMetrics();
      expect(metrics.heapUsed).toBeGreaterThan(0);
      
      // Get resource stats
      const stats = resourceManager.getResourceStats();
      expect(stats.totalTracked).toBe(100); // 50 timers + 50 intervals
      
      // Wait for potential memory monitoring
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Clean up
      const cleanupResult = await resourceManager.cleanup();
      expect(cleanupResult.success).toBe(true);
      expect(cleanupResult.resourcesCleanedUp).toBeGreaterThanOrEqual(100);
      
      // Verify final state
      const finalStats = resourceManager.getResourceStats();
      expect(finalStats.totalTracked).toBe(0);
    });

    it('should maintain memory history during resource operations', async () => {
      // Get initial memory history
      const initialHistory = resourceManager.getMemoryHistory();
      const initialLength = initialHistory.length;
      
      // Perform resource operations
      const resources: string[] = [];
      for (let i = 0; i < 10; i++) {
        const timer = setTimeout(() => {}, 1000);
        resources.push(resourceManager.trackTimer(timer, { cycle: i }));
        
        // Small delay between operations
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      
      // Wait for memory monitoring
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Check if memory history was updated
      const updatedHistory = resourceManager.getMemoryHistory();
      expect(updatedHistory.length).toBeGreaterThanOrEqual(initialLength);
      
      // Clean up resources
      for (const resourceId of resources) {
        resourceManager.untrackResource(resourceId);
      }
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle cleanup errors gracefully', async () => {
      // Create a mock resource that will fail cleanup
      const failingResource = {
        cleanup: vi.fn().mockRejectedValue(new Error('Cleanup failed'))
      };
      
      const resourceId = resourceManager.trackResource('connection', failingResource, { 
        purpose: 'error_test' 
      });
      
      // Also create normal resources
      const timer = setTimeout(() => {}, 1000);
      resourceManager.trackTimer(timer, { purpose: 'normal_resource' });
      
      // Perform cleanup
      const cleanupResult = await resourceManager.cleanup();
      
      // Should report partial success (may succeed if error handling is robust)
      expect(typeof cleanupResult.success).toBe('boolean');
      expect(cleanupResult.errors.length).toBeGreaterThan(0);
      expect(cleanupResult.resourcesCleanedUp).toBeGreaterThan(0); // Normal resources cleaned up
      
      // Verify the failing resource cleanup was attempted
      expect(failingResource.cleanup).toHaveBeenCalled();
    });

    it('should handle shutdown coordinator errors gracefully', async () => {
      // Mock a service that will fail during shutdown
      const { syncOutboxService } = await import('../main/services/sync/sync-outbox.service');
      vi.mocked(syncOutboxService.stopPeriodicCleanup).mockImplementation(() => {
        throw new Error('Outbox cleanup failed');
      });
      
      // Create some resources
      const timer = setTimeout(() => {}, 1000);
      resourceManager.trackTimer(timer, { purpose: 'error_recovery_test' });
      
      // Perform shutdown
      const shutdownResult = await syncShutdownCoordinator.shutdown({
        gracefulTimeout: 2000,
        forceShutdown: false
      });
      
      // Should handle errors but continue with other components
      expect(shutdownResult.errors.length).toBeGreaterThan(0);
      expect(shutdownResult.componentsShutdown.length).toBeGreaterThan(0);
      
      // Resource manager should still be cleaned up
      expect(shutdownResult.componentsShutdown).toContain('resource-manager');
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large numbers of resources efficiently', async () => {
      const startTime = Date.now();
      const resourceCount = 1000;
      const resources: string[] = [];
      
      // Create many resources
      for (let i = 0; i < resourceCount; i++) {
        const timer = setTimeout(() => {}, 30000);
        resources.push(resourceManager.trackTimer(timer, { 
          batch: Math.floor(i / 100),
          index: i,
          purpose: 'performance_test'
        }));
      }
      
      const creationTime = Date.now() - startTime;
      console.log(`Created ${resourceCount} resources in ${creationTime}ms`);
      
      // Verify tracking
      const stats = resourceManager.getResourceStats();
      expect(stats.totalTracked).toBe(resourceCount);
      expect(stats.timers).toBe(resourceCount);
      
      // Perform cleanup
      const cleanupStartTime = Date.now();
      const cleanupResult = await resourceManager.cleanup();
      const cleanupTime = Date.now() - cleanupStartTime;
      
      console.log(`Cleaned up ${cleanupResult.resourcesCleanedUp} resources in ${cleanupTime}ms`);
      
      // Verify performance
      expect(cleanupResult.success).toBe(true);
      expect(cleanupResult.resourcesCleanedUp).toBeGreaterThanOrEqual(resourceCount);
      expect(cleanupTime).toBeLessThan(5000); // Should complete within 5 seconds
      
      // Verify final state
      const finalStats = resourceManager.getResourceStats();
      expect(finalStats.totalTracked).toBe(0);
    });

    it('should handle concurrent resource operations', async () => {
      const concurrentOperations = 50;
      const promises: Promise<string>[] = [];
      
      // Start many concurrent resource tracking operations
      for (let i = 0; i < concurrentOperations; i++) {
        const promise = new Promise<string>((resolve) => {
          setTimeout(() => {
            const timer = setTimeout(() => {}, 5000);
            const resourceId = resourceManager.trackTimer(timer, { 
              concurrent: true,
              index: i 
            });
            resolve(resourceId);
          }, Math.random() * 100); // Random delay up to 100ms
        });
        promises.push(promise);
      }
      
      // Wait for all operations to complete
      const resourceIds = await Promise.all(promises);
      
      // Verify all resources were tracked
      expect(resourceIds.length).toBe(concurrentOperations);
      const stats = resourceManager.getResourceStats();
      expect(stats.totalTracked).toBe(concurrentOperations);
      
      // Perform concurrent cleanup
      const cleanupPromises = resourceIds.slice(0, 25).map(id => 
        resourceManager.untrackResource(id)
      );
      
      const cleanupResults = await Promise.all(cleanupPromises);
      expect(cleanupResults.every(result => result === true)).toBe(true);
      
      // Final cleanup
      const finalCleanup = await resourceManager.cleanup();
      expect(finalCleanup.success).toBe(true);
    });
  });
});