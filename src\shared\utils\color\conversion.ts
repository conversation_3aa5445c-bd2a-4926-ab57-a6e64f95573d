/**
 * Color Conversion Utilities
 * Functions for converting between different color spaces
 */

import { RGB, HSL, CMYK, LAB, XYZ, ColorSpaces } from './types';

/**
 * Hex ↔ RGB Conversions
 */

// Convert hex color to RGB
export function hexToRgb(hex: string): RGB | null {
  // Remove # if present
  const cleanHex = hex.charAt(0) === '#' ? hex.substring(1) : hex;

  // Handle 3-digit hex
  const finalHex =
    cleanHex.length === 3
      ? (cleanHex[0] ?? '') +
        (cleanHex[0] ?? '') +
        (cleanHex[1] ?? '') +
        (cleanHex[1] ?? '') +
        (cleanHex[2] ?? '') +
        (cleanHex[2] ?? '')
      : cleanHex;

  // Validate hex length
  if (finalHex.length !== 6) {
    return null;
  }

  // Parse hex values
  const result = /^([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(finalHex);

  return result
    ? {
        r: parseInt(result[1] ?? '0', 16),
        g: parseInt(result[2] ?? '0', 16),
        b: parseInt(result[3] ?? '0', 16),
      }
    : null;
}

// Convert RGB to hex color
export function rgbToHex(rgb: RGB): string {
  const toHex = (n: number): string => {
    const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
    return hex.length === 1 ? `0${  hex}` : hex;
  };

  return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`.toUpperCase();
}

/**
 * RGB ↔ HSL Conversions
 */

// Convert RGB to HSL
export function rgbToHsl(rgb: RGB): HSL {
  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const l = (max + min) / 2;

  let h = 0;
  let s = 0;

  if (diff !== 0) {
    s = l > 0.5 ? diff / (2 - max - min) : diff / (max + min);

    switch (max) {
      case r:
        h = (g - b) / diff + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / diff + 2;
        break;
      case b:
        h = (r - g) / diff + 4;
        break;
    }

    h = h * 60;
  }

  return {
    h: Math.round(h),
    s: Math.round(s * 100),
    l: Math.round(l * 100),
  };
}

// Convert HSL to RGB
export function hslToRgb(hsl: HSL): RGB {
  const h = hsl.h / 360;
  const s = hsl.s / 100;
  const l = hsl.l / 100;

  let r: number, g: number, b: number;

  if (s === 0) {
    // Achromatic (gray)
    r = g = b = l;
  } else {
    const hueToRgb = (p: number, q: number, t: number): number => {
      if (t < 0) {
        t += 1;
      }
      if (t > 1) {
        t -= 1;
      }
      if (t < 1 / 6) {
        return p + (q - p) * 6 * t;
      }
      if (t < 1 / 2) {
        return q;
      }
      if (t < 2 / 3) {
        return p + (q - p) * (2 / 3 - t) * 6;
      }
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    r = hueToRgb(p, q, h + 1 / 3);
    g = hueToRgb(p, q, h);
    b = hueToRgb(p, q, h - 1 / 3);
  }

  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(b * 255),
  };
}

/**
 * RGB ↔ CMYK Conversions
 */

// Convert RGB to CMYK
export function rgbToCmyk(rgb: RGB): CMYK {
  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;

  const k = 1 - Math.max(r, g, b);

  if (k === 1) {
    // Pure black
    return { c: 0, m: 0, y: 0, k: 100 };
  }

  const c = (1 - r - k) / (1 - k);
  const m = (1 - g - k) / (1 - k);
  const y = (1 - b - k) / (1 - k);

  return {
    c: Math.round(c * 100),
    m: Math.round(m * 100),
    y: Math.round(y * 100),
    k: Math.round(k * 100),
  };
}

// Convert CMYK to RGB
export function cmykToRgb(cmyk: CMYK): RGB {
  const c = cmyk.c / 100;
  const m = cmyk.m / 100;
  const y = cmyk.y / 100;
  const k = cmyk.k / 100;

  const r = 255 * (1 - c) * (1 - k);
  const g = 255 * (1 - m) * (1 - k);
  const b = 255 * (1 - y) * (1 - k);

  return {
    r: Math.round(r),
    g: Math.round(g),
    b: Math.round(b),
  };
}

/**
 * Hex ↔ HSL Conversions
 */

// Convert hex to HSL
export function hexToHsl(hex: string): HSL | null {
  const rgb = hexToRgb(hex);
  if (!rgb) {
    return null;
  }
  return rgbToHsl(rgb);
}

// Convert HSL to hex
export function hslToHex(hsl: HSL): string {
  const rgb = hslToRgb(hsl);
  return rgbToHex(rgb);
}

/**
 * Hex ↔ CMYK Conversions
 */

// Convert hex to CMYK
export function hexToCmyk(hex: string): CMYK | null {
  const rgb = hexToRgb(hex);
  if (!rgb) {
    return null;
  }
  return rgbToCmyk(rgb);
}

// Convert CMYK to hex
export function cmykToHex(cmyk: CMYK): string {
  const rgb = cmykToRgb(cmyk);
  return rgbToHex(rgb);
}

/**
 * RGB ↔ LAB Conversions (via XYZ color space)
 */

// Convert RGB to XYZ
export function rgbToXyz(rgb: RGB): XYZ {
  let r = rgb.r / 255;
  let g = rgb.g / 255;
  let b = rgb.b / 255;

  // Apply gamma correction
  r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
  g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
  b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;

  // Observer = 2°, Illuminant = D65
  const x = (r * 0.4124564 + g * 0.3575761 + b * 0.1804375) * 100;
  const y = (r * 0.2126729 + g * 0.7151522 + b * 0.072175) * 100;
  const z = (r * 0.0193339 + g * 0.119192 + b * 0.9503041) * 100;

  return { x, y, z };
}

// Convert XYZ to RGB
export function xyzToRgb(xyz: XYZ): RGB {
  const x = xyz.x / 100;
  const y = xyz.y / 100;
  const z = xyz.z / 100;

  // Observer = 2°, Illuminant = D65
  let r = x * 3.2404542 + y * -1.5371385 + z * -0.4985314;
  let g = x * -0.969266 + y * 1.8760108 + z * 0.041556;
  let b = x * 0.0556434 + y * -0.2040259 + z * 1.0572252;

  // Apply reverse gamma correction
  r = r > 0.0031308 ? 1.055 * Math.pow(r, 1 / 2.4) - 0.055 : 12.92 * r;
  g = g > 0.0031308 ? 1.055 * Math.pow(g, 1 / 2.4) - 0.055 : 12.92 * g;
  b = b > 0.0031308 ? 1.055 * Math.pow(b, 1 / 2.4) - 0.055 : 12.92 * b;

  return {
    r: Math.round(Math.max(0, Math.min(255, r * 255))),
    g: Math.round(Math.max(0, Math.min(255, g * 255))),
    b: Math.round(Math.max(0, Math.min(255, b * 255))),
  };
}

// Convert XYZ to LAB
export function xyzToLab(xyz: XYZ): LAB {
  // Reference white D65
  const xn = 95.047;
  const yn = 100.0;
  const zn = 108.883;

  const x = xyz.x / xn;
  const y = xyz.y / yn;
  const z = xyz.z / zn;

  const fx = x > 0.008856 ? Math.pow(x, 1 / 3) : 7.787 * x + 16 / 116;
  const fy = y > 0.008856 ? Math.pow(y, 1 / 3) : 7.787 * y + 16 / 116;
  const fz = z > 0.008856 ? Math.pow(z, 1 / 3) : 7.787 * z + 16 / 116;

  const l = 116 * fy - 16;
  const a = 500 * (fx - fy);
  const b = 200 * (fy - fz);

  return {
    l: Math.max(0, Math.min(100, l)),
    a: Math.max(-128, Math.min(127, a)),
    b: Math.max(-128, Math.min(127, b)),
  };
}

// Convert LAB to XYZ
export function labToXyz(lab: LAB): XYZ {
  // Reference white D65
  const xn = 95.047;
  const yn = 100.0;
  const zn = 108.883;

  const fy = (lab.l + 16) / 116;
  const fx = lab.a / 500 + fy;
  const fz = fy - lab.b / 200;

  const x = fx * fx * fx > 0.008856 ? fx * fx * fx : (fx - 16 / 116) / 7.787;
  const y = fy * fy * fy > 0.008856 ? fy * fy * fy : (fy - 16 / 116) / 7.787;
  const z = fz * fz * fz > 0.008856 ? fz * fz * fz : (fz - 16 / 116) / 7.787;

  return {
    x: x * xn,
    y: y * yn,
    z: z * zn,
  };
}

// Convert RGB to LAB
export function rgbToLab(rgb: RGB): LAB {
  const xyz = rgbToXyz(rgb);
  return xyzToLab(xyz);
}

// Convert LAB to RGB
export function labToRgb(lab: LAB): RGB {
  const xyz = labToXyz(lab);
  return xyzToRgb(xyz);
}

/**
 * Get all color spaces for a given color
 */

export function getAllColorSpaces(hex: string): ColorSpaces | null {
  const rgb = hexToRgb(hex);
  if (!rgb) {
    return null;
  }

  const hsl = rgbToHsl(rgb);
  const cmyk = rgbToCmyk(rgb);
  const lab = rgbToLab(rgb);

  return {
    hex: hex.toUpperCase(),
    rgb,
    hsl,
    cmyk,
    lab,
  };
}

/**
 * Utility functions
 */

// Check if a color is light or dark (useful for text contrast)
export function isLightColor(rgb: RGB): boolean {
  // Calculate relative luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
  return luminance > 0.5;
}

// Alternative method using HSL
export function isLightColorHsl(hsl: HSL): boolean {
  return hsl.l > 50;
}
