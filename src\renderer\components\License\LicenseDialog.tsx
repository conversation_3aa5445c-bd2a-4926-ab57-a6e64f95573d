/**
 * @file LicenseDialog.tsx
 * @description License activation dialog component
 */

import React, { useState, useEffect } from 'react';
import { Key, Cpu, Copy, Check } from 'lucide-react';

interface LicenseDialogProps {
  isOpen: boolean;
  onClose: () => void;
  licenseStatus: {
    isValid: boolean;
    inTrialMode: boolean;
    trialDaysRemaining: number;
    deviceId?: string;
    licenseKey?: string;
  };
}

export const LicenseDialog: React.FC<LicenseDialogProps> = ({
  isOpen,
  onClose,
  licenseStatus
}) => {
  const [isActivating, setIsActivating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [deviceIdCopied, setDeviceIdCopied] = useState(false);
  const [licenseCopied, setLicenseCopied] = useState(false);

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen) {
      setError(null);
      setSuccess(null);
      setIsActivating(false);
      setDeviceIdCopied(false);
      setLicenseCopied(false);
    }
  }, [isOpen]);

  // Copy device ID to clipboard
  const copyDeviceId = () => {
    if (licenseStatus.deviceId) {
      navigator.clipboard.writeText(licenseStatus.deviceId);
      setDeviceIdCopied(true);
      setTimeout(() => setDeviceIdCopied(false), 2000);
    }
  };

  // Copy license key to clipboard
  const copyLicenseKey = () => {
    if (licenseStatus.licenseKey) {
      navigator.clipboard.writeText(licenseStatus.licenseKey);
      setLicenseCopied(true);
      setTimeout(() => setLicenseCopied(false), 2000);
    }
  };

  // Handle license activation
  const handleActivate = async () => {
    setIsActivating(true);
    setError(null);

    try {
      const result = await window.licenseAPI.activateLicense();

      if (result.success) {
        setSuccess(result.message || 'License activated successfully');
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError(result.message || 'Failed to activate license');
      }
    } catch (err) {
      setError('An error occurred while activating the license');
      console.error('License activation error:', err);
    } finally {
      setIsActivating(false);
    }
  };

  // Handle license deactivation
  const handleDeactivate = async () => {
    setIsActivating(true);
    setError(null);

    try {
      const result = await window.licenseAPI.deactivateDevice();

      if (result.success) {
        setSuccess(result.message || 'License deactivated successfully');
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError(result.message || 'Failed to deactivate license');
      }
    } catch (err) {
      setError('An error occurred while deactivating the license');
      console.error('License deactivation error:', err);
    } finally {
      setIsActivating(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-ui-background-primary dark:bg-ui-background-tertiary rounded-[var(--radius-md)] shadow-lg w-full max-w-md">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-ui-foreground-primary dark:text-ui-foreground-primary">
              {licenseStatus.isValid ? 'License Information' : 'Activate License'}
            </h2>
            <button
              onClick={onClose}
              className="text-ui-foreground-secondary hover:text-ui-foreground-primary"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          {licenseStatus.inTrialMode && (
            <div className={`mb-4 p-3 rounded-[var(--radius-md)] ${
              licenseStatus.trialDaysRemaining > 5
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
            }`}>
              <p className="text-sm">
                {licenseStatus.trialDaysRemaining > 0
                  ? `You are using ChromaSync in trial mode. ${licenseStatus.trialDaysRemaining} days remaining.`
                  : 'Your trial period has expired. Please activate a license to continue using ChromaSync.'}
              </p>
            </div>
          )}

          {licenseStatus.isValid && !licenseStatus.inTrialMode ? (
            <div className="mb-4">
              <div className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 p-3 rounded-[var(--radius-md)] mb-4">
                <p className="text-sm">Your license is active and valid.</p>
              </div>

              {/* Device ID */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-ui-foreground-secondary dark:text-ui-foreground-secondary mb-1">
                  Device ID
                </label>
                <div className="relative">
                  <Cpu className="absolute left-3 top-1/2 transform -translate-y-1/2 text-ui-foreground-secondary dark:text-ui-foreground-secondary" size={16} />
                  <input
                    type="text"
                    value={licenseStatus.deviceId || ''}
                    readOnly
                    className="w-full pl-10 pr-10 py-2 border border-ui-border-light dark:border-ui-border-dark rounded-[var(--radius-md)] bg-ui-background-secondary dark:bg-ui-background-tertiary text-ui-foreground-primary dark:text-ui-foreground-primary focus:outline-none"
                  />
                  <button
                    onClick={copyDeviceId}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-ui-foreground-secondary hover:text-ui-foreground-primary"
                    title="Copy to clipboard"
                  >
                    {deviceIdCopied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                  </button>
                </div>
              </div>

              {/* License Key */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-ui-foreground-secondary dark:text-ui-foreground-secondary mb-1">
                  License Key
                </label>
                <div className="relative">
                  <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 text-ui-foreground-secondary dark:text-ui-foreground-secondary" size={16} />
                  <input
                    type="text"
                    value={licenseStatus.licenseKey || ''}
                    readOnly
                    className="w-full pl-10 pr-10 py-2 border border-ui-border-light dark:border-ui-border-dark rounded-[var(--radius-md)] bg-ui-background-secondary dark:bg-ui-background-tertiary text-ui-foreground-primary dark:text-ui-foreground-primary focus:outline-none"
                  />
                  <button
                    onClick={copyLicenseKey}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-ui-foreground-secondary hover:text-ui-foreground-primary"
                    title="Copy to clipboard"
                  >
                    {licenseCopied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                  </button>
                </div>
                <p className="mt-1 text-xs text-ui-foreground-secondary dark:text-ui-foreground-secondary">
                  This is your unique license key based on your device ID.
                </p>
              </div>

              <button
                onClick={handleDeactivate}
                disabled={isActivating}
                className="w-full px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-[var(--radius-md)] hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isActivating ? 'Deactivating...' : 'Deactivate License'}
              </button>
            </div>
          ) : (
            <>
              {/* Device ID */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-ui-foreground-secondary dark:text-ui-foreground-secondary mb-1">
                  Device ID
                </label>
                <div className="relative">
                  <Cpu className="absolute left-3 top-1/2 transform -translate-y-1/2 text-ui-foreground-secondary dark:text-ui-foreground-secondary" size={16} />
                  <input
                    type="text"
                    value={licenseStatus.deviceId || ''}
                    readOnly
                    className="w-full pl-10 pr-10 py-2 border border-ui-border-light dark:border-ui-border-dark rounded-[var(--radius-md)] bg-ui-background-secondary dark:bg-ui-background-tertiary text-ui-foreground-primary dark:text-ui-foreground-primary focus:outline-none"
                  />
                  <button
                    onClick={copyDeviceId}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-ui-foreground-secondary hover:text-ui-foreground-primary"
                    title="Copy to clipboard"
                  >
                    {deviceIdCopied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                  </button>
                </div>
                <p className="mt-1 text-xs text-ui-foreground-secondary dark:text-ui-foreground-secondary">
                  This is your unique device identifier.
                </p>
              </div>

              {/* Auto-generated License Key */}
              {licenseStatus.licenseKey && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-ui-foreground-secondary dark:text-ui-foreground-secondary mb-1">
                    License Key
                  </label>
                  <div className="relative">
                    <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 text-ui-foreground-secondary dark:text-ui-foreground-secondary" size={16} />
                    <input
                      type="text"
                      value={licenseStatus.licenseKey}
                      readOnly
                      className="w-full pl-10 pr-10 py-2 border border-ui-border-light dark:border-ui-border-dark rounded-[var(--radius-md)] bg-ui-background-secondary dark:bg-ui-background-tertiary text-ui-foreground-primary dark:text-ui-foreground-primary focus:outline-none"
                    />
                    <button
                      onClick={copyLicenseKey}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-ui-foreground-secondary hover:text-ui-foreground-primary"
                      title="Copy to clipboard"
                    >
                      {licenseCopied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                    </button>
                  </div>
                  <p className="mt-1 text-xs text-ui-foreground-secondary dark:text-ui-foreground-secondary">
                    This is your automatically generated license key.
                  </p>
                </div>
              )}

              {error && (
                <div className="mb-4 p-3 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded-[var(--radius-md)]">
                  <p className="text-sm">{error}</p>
                </div>
              )}

              {success && (
                <div className="mb-4 p-3 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-[var(--radius-md)]">
                  <p className="text-sm">{success}</p>
                </div>
              )}

              <button
                onClick={handleActivate}
                disabled={isActivating}
                className="w-full px-4 py-2 text-sm font-medium text-white bg-brand-primary rounded-[var(--radius-md)] hover:bg-brand-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isActivating ? 'Activating...' : 'Activate License'}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
