/**
 * Device ID management utility
 * Handles generation, storage, export, and import of device ID
 */

const DEVICE_ID_KEY = 'pantone-tracker-device-id';

/**
 * Generate a new UUID v4
 */
function generateUUID(): string {
  // Simple UUID v4 generator
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Get or create the device ID
 */
export function getOrCreateDeviceId(): string {
  let deviceId = localStorage.getItem(DEVICE_ID_KEY);
  if (!deviceId) {
    deviceId = generateUUID();
    localStorage.setItem(DEVICE_ID_KEY, deviceId);
  }
  return deviceId;
}

/**
 * Export the current device ID (for backup or migration)
 */
export function exportDeviceId(): string | null {
  return localStorage.getItem(DEVICE_ID_KEY);
}

/**
 * Import a device ID (restore identity)
 * @param deviceId The device ID to import
 */
export function importDeviceId(deviceId: string): void {
  if (!deviceId || typeof deviceId !== 'string') {
    throw new Error('Invalid device ID');
  }
  localStorage.setItem(DEVICE_ID_KEY, deviceId);
}
