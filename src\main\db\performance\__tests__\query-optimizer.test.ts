/**
 * @file query-optimizer.test.ts
 * @description Tests for SQLite query optimization functionality
 */

import Database from 'better-sqlite3';
import { SQLiteQueryOptimizer } from '../query-optimizer';
import { createPerformanceInitializer } from '../performance-initializer';
import { createOptimizedColorRepository } from '../../repositories/optimized-color.repository';

describe('SQLite Query Optimizer', () => {
  let db: Database.Database;
  let optimizer: SQLiteQueryOptimizer;

  beforeEach(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    optimizer = new SQLiteQueryOptimizer(db);
    
    // Create minimal test schema
    db.exec(`
      CREATE TABLE organizations (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL
      );
      
      CREATE TABLE colors (
        id TEXT PRIMARY KEY,
        organization_id TEXT NOT NULL,
        display_name TEXT NOT NULL,
        hex TEXT NOT NULL,
        deleted_at TEXT,
        code TEXT,
        color_spaces JSON DEFAULT '{}',
        is_gradient INTEGER DEFAULT 0,
        is_library INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        organization_id TEXT NOT NULL,
        name TEXT NOT NULL,
        deleted_at TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE TABLE product_colors (
        product_id TEXT NOT NULL,
        color_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        added_at TEXT DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_id, color_id)
      );
      
      -- Insert test data
      INSERT INTO organizations (id, name) VALUES ('test-org-1', 'Test Organization');
      
      INSERT INTO colors (id, organization_id, display_name, hex, code) VALUES
        ('color-1', 'test-org-1', 'Test Red', '#FF0000', 'RED-001'),
        ('color-2', 'test-org-1', 'Test Blue', '#0000FF', 'BLUE-001'),
        ('color-3', 'test-org-1', 'Test Green', '#00FF00', 'GREEN-001');
        
      INSERT INTO products (id, organization_id, name) VALUES
        ('product-1', 'test-org-1', 'Test Product 1'),
        ('product-2', 'test-org-1', 'Test Product 2');
        
      INSERT INTO product_colors (product_id, color_id, organization_id) VALUES
        ('product-1', 'color-1', 'test-org-1'),
        ('product-1', 'color-2', 'test-org-1'),
        ('product-2', 'color-1', 'test-org-1');
    `);
  });

  afterEach(() => {
    db.close();
  });

  describe('Organization-first composite indexes', () => {
    test('should create organization-first indexes successfully', async () => {
      await optimizer.createOrganizationIndexes();
      
      // Check that indexes were created
      const indexes = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name LIKE 'idx_%_org%'
      `).all();
      
      expect(indexes.length).toBeGreaterThan(0);
      expect(indexes.some(idx => idx.name.includes('colors_org'))).toBe(true);
      expect(indexes.some(idx => idx.name.includes('products_org'))).toBe(true);
    });

    test('should handle duplicate index creation gracefully', async () => {
      // Create indexes twice
      await optimizer.createOrganizationIndexes();
      await optimizer.createOrganizationIndexes();
      
      // Should not throw error
      expect(true).toBe(true);
    });
  });

  describe('Covering indexes', () => {
    test('should create covering indexes for query optimization', async () => {
      await optimizer.createCoveringIndexes();
      
      const indexes = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name LIKE '%covering%'
      `).all();
      
      expect(indexes.length).toBeGreaterThan(0);
    });
  });

  describe('Materialized aggregation tables', () => {
    test('should create aggregation tables and triggers', async () => {
      await optimizer.createAggregationTables();
      
      // Check aggregation tables exist
      const tables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND (name LIKE '%usage%' OR name LIKE '%map%')
      `).all();
      
      expect(tables.length).toBeGreaterThan(0);
      expect(tables.some(t => t.name === 'color_usage_counts')).toBe(true);
      expect(tables.some(t => t.name === 'color_product_map')).toBe(true);
    });

    test('should refresh aggregation tables with current data', async () => {
      await optimizer.createAggregationTables();
      await optimizer.refreshAggregationTables('test-org-1');
      
      // Check data was populated
      const usageCounts = db.prepare(`
        SELECT COUNT(*) as count FROM color_usage_counts 
        WHERE organization_id = ?
      `).get('test-org-1') as { count: number };
      
      expect(usageCounts.count).toBeGreaterThan(0);
    });
  });

  describe('Query plan analysis', () => {
    test('should analyze query plans and provide recommendations', async () => {
      await optimizer.optimizeColorBrowsingPerformance();
      
      const analysis = await optimizer.analyzeQueryPlans();
      
      expect(analysis.colorFindAllPlan).toBeDefined();
      expect(analysis.usageCountsPlan).toBeDefined();
      expect(analysis.productMapPlan).toBeDefined();
      expect(analysis.recommendations).toBeDefined();
      expect(Array.isArray(analysis.recommendations)).toBe(true);
    });
  });

  describe('Full optimization', () => {
    test('should apply all optimizations successfully', async () => {
      await optimizer.optimizeColorBrowsingPerformance();
      
      // Check that indexes were created
      const indexes = db.prepare(`
        SELECT COUNT(*) as count FROM sqlite_master 
        WHERE type='index' AND name LIKE 'idx_%'
      `).get() as { count: number };
      
      expect(indexes.count).toBeGreaterThan(5);
      
      // Check that aggregation tables exist
      const tables = db.prepare(`
        SELECT COUNT(*) as count FROM sqlite_master 
        WHERE type='table' AND (name LIKE '%usage%' OR name LIKE '%map%')
      `).get() as { count: number };
      
      expect(tables.count).toBeGreaterThan(0);
    });
  });
});

describe('Performance Initializer', () => {
  let db: Database.Database;

  beforeEach(() => {
    db = new Database(':memory:');
    
    // Create minimal schema
    db.exec(`
      CREATE TABLE organizations (id TEXT PRIMARY KEY, name TEXT);
      CREATE TABLE colors (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        display_name TEXT,
        hex TEXT,
        deleted_at TEXT
      );
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        name TEXT,
        deleted_at TEXT,
        is_active INTEGER DEFAULT 1
      );
      CREATE TABLE product_colors (
        product_id TEXT,
        color_id TEXT,
        organization_id TEXT,
        PRIMARY KEY (product_id, color_id)
      );
      
      INSERT INTO organizations VALUES ('test-org', 'Test Org');
    `);
  });

  afterEach(() => {
    db.close();
  });

  test('should initialize performance optimizations', async () => {
    const initializer = createPerformanceInitializer(db);
    
    const result = await initializer.initializeOptimizations({
      refreshAggregationTables: true,
      runAnalysis: false
    });
    
    expect(result.success).toBe(true);
    expect(result.optimizationsApplied.length).toBeGreaterThan(0);
    expect(result.errors.length).toBe(0);
    expect(result.timeTakenMs).toBeGreaterThan(0);
  });

  test('should skip optimizations if already applied', async () => {
    const initializer = createPerformanceInitializer(db);
    
    // Apply optimizations first time
    await initializer.initializeOptimizations();
    
    // Second time should skip
    const result = await initializer.initializeOptimizations({
      skipIfOptimized: true
    });
    
    expect(result.success).toBe(true);
    expect(result.optimizationsApplied).toEqual(['Already optimized']);
  });

  test('should provide optimization status', async () => {
    const initializer = createPerformanceInitializer(db);
    
    // Check status before optimization
    let status = await initializer.getOptimizationStatus();
    expect(status.isOptimized).toBe(false);
    
    // Apply optimizations
    await initializer.initializeOptimizations();
    
    // Check status after optimization
    status = await initializer.getOptimizationStatus();
    expect(status.isOptimized).toBe(true);
    expect(status.version).toBeDefined();
  });
});

describe('Optimized Color Repository', () => {
  let db: Database.Database;
  let repository: any;

  beforeEach(() => {
    db = new Database(':memory:');
    
    // Create test schema and data
    db.exec(`
      CREATE TABLE organizations (id TEXT PRIMARY KEY, name TEXT);
      CREATE TABLE color_sources (id INTEGER PRIMARY KEY, code TEXT, name TEXT);
      CREATE TABLE colors (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        source_id INTEGER DEFAULT 1,
        display_name TEXT,
        code TEXT,
        hex TEXT,
        color_spaces JSON DEFAULT '{}',
        is_gradient INTEGER DEFAULT 0,
        is_library INTEGER DEFAULT 0,
        deleted_at TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        organization_id TEXT,
        name TEXT,
        deleted_at TEXT,
        is_active INTEGER DEFAULT 1
      );
      CREATE TABLE product_colors (
        product_id TEXT,
        color_id TEXT,
        organization_id TEXT,
        PRIMARY KEY (product_id, color_id)
      );
      
      -- Create materialized tables
      CREATE TABLE color_usage_counts (
        organization_id TEXT,
        color_id TEXT,
        color_name TEXT,
        product_count INTEGER DEFAULT 0,
        product_names TEXT,
        last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (organization_id, color_id)
      );
      
      INSERT INTO organizations VALUES ('test-org', 'Test Org');
      INSERT INTO color_sources VALUES (1, 'user', 'User Created');
      
      INSERT INTO colors (id, organization_id, display_name, code, hex) VALUES
        ('color-1', 'test-org', 'Test Red', 'RED-001', '#FF0000'),
        ('color-2', 'test-org', 'Test Blue', 'BLUE-001', '#0000FF');
        
      INSERT INTO products (id, organization_id, name) VALUES
        ('product-1', 'test-org', 'Test Product 1');
        
      INSERT INTO product_colors VALUES ('product-1', 'color-1', 'test-org');
      
      INSERT INTO color_usage_counts VALUES 
        ('test-org', 'color-1', 'Test Red', 1, 'Test Product 1', CURRENT_TIMESTAMP);
    `);
    
    repository = createOptimizedColorRepository(db, true);
  });

  afterEach(() => {
    db.close();
  });

  test('should use optimized findAll method', () => {
    const colors = repository.findAllOptimized('test-org');
    
    expect(Array.isArray(colors)).toBe(true);
    expect(colors.length).toBeGreaterThan(0);
    expect(colors[0].display_name).toBeDefined();
  });

  test('should use optimized usage counts method', () => {
    const usageMap = repository.getUsageCountsOptimized('test-org');
    
    expect(usageMap instanceof Map).toBe(true);
    expect(usageMap.size).toBeGreaterThan(0);
    expect(usageMap.has('Test Red')).toBe(true);
  });

  test('should provide aggregation table statistics', () => {
    const stats = repository.getAggregationTableStats('test-org');
    
    expect(stats.usageCountsRows).toBeGreaterThan(0);
    expect(stats.lastUpdated).toBeDefined();
  });

  test('should support search with optimization', () => {
    const colors = repository.searchColorsOptimized('test-org', 'Red');
    
    expect(Array.isArray(colors)).toBe(true);
    expect(colors.length).toBe(1);
    expect(colors[0].display_name).toBe('Test Red');
  });
});