/**
 * @file oauth-edge-cases.test.ts
 * @description Edge case tests for enhanced PKCE OAuth implementation
 */

import { describe, it, expect } from 'vitest';
import { randomBytes } from 'crypto';

// Simple edge case logic tests without complex mocking
describe('OAuth PKCE Edge Cases', () => {
  describe('State Parameter Security', () => {
    it('should reject expired state parameters', () => {
      const isStateExpired = (
        timestamp: number,
        expiryMs: number = 5 * 60 * 1000
      ) => {
        return Date.now() - timestamp > expiryMs;
      };

      // Recent state (not expired)
      const recentTimestamp = Date.now() - 60000; // 1 minute ago
      expect(isStateExpired(recentTimestamp)).toBe(false);

      // Old state (expired)
      const oldTimestamp = Date.now() - 7 * 60 * 1000; // 7 minutes ago
      expect(isStateExpired(oldTimestamp)).toBe(true);
    });

    it('should generate cryptographically secure state parameters', () => {
      const generateSecureState = () => {
        return randomBytes(32).toString('base64url');
      };

      const state1 = generateSecureState();
      const state2 = generateSecureState();

      // Should be different
      expect(state1).not.toBe(state2);

      // Should be proper length (base64url encoded 32 bytes)
      expect(state1.length).toBeGreaterThan(40);
      expect(state2.length).toBeGreaterThan(40);

      // Should only contain valid base64url characters
      expect(state1).toMatch(/^[A-Za-z0-9_-]+$/);
      expect(state2).toMatch(/^[A-Za-z0-9_-]+$/);
    });

    it('should validate callback URL format correctly', () => {
      const validateCallbackUrl = (url: string): boolean => {
        try {
          // Check URL format
          if (
            !url.startsWith('chromasync://auth/callback') &&
            !url.startsWith('http://localhost:3000/auth/callback')
          ) {
            return false;
          }

          // Parse URL to validate structure
          const urlObj = new URL(url);

          // Check for suspicious parameters
          const params = urlObj.searchParams;
          const hash = urlObj.hash;

          // Check for XSS attempts in parameters
          for (const [key, value] of params.entries()) {
            if (key.includes('javascript:') || value.includes('javascript:')) {
              return false;
            }
          }

          if (hash.includes('javascript:')) {
            return false;
          }

          return true;
        } catch (error) {
          return false;
        }
      };

      // Valid URLs
      expect(
        validateCallbackUrl('chromasync://auth/callback?code=123&state=abc')
      ).toBe(true);
      expect(
        validateCallbackUrl('http://localhost:3000/auth/callback?code=123')
      ).toBe(true);

      // Invalid URLs
      expect(validateCallbackUrl('https://evil.com/callback')).toBe(false);
      expect(
        validateCallbackUrl(
          'chromasync://auth/callback?code=javascript:alert(1)'
        )
      ).toBe(false);
      expect(validateCallbackUrl('not-a-url')).toBe(false);
    });
  });

  describe('Rate Limiting Protection', () => {
    it('should enforce callback attempt limits', () => {
      let callbackAttempts = 0;
      const MAX_CALLBACK_RETRIES = 3;

      const isRateLimited = () => {
        callbackAttempts++;
        return callbackAttempts > MAX_CALLBACK_RETRIES;
      };

      // First few attempts should be allowed
      expect(isRateLimited()).toBe(false); // 1
      expect(isRateLimited()).toBe(false); // 2
      expect(isRateLimited()).toBe(false); // 3

      // Further attempts should be rate limited
      expect(isRateLimited()).toBe(true); // 4 - blocked
      expect(isRateLimited()).toBe(true); // 5 - blocked
    });

    it('should clean up expired state entries', () => {
      const stateStore = new Map();
      const STATE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes

      const cleanupExpiredStates = () => {
        const now = Date.now();
        for (const [key, value] of stateStore.entries()) {
          if (now - value.timestamp > STATE_EXPIRY_MS) {
            stateStore.delete(key);
          }
        }
      };

      // Add some states with different timestamps
      stateStore.set('recent', { timestamp: Date.now() - 60000 }); // 1 min ago
      stateStore.set('old', { timestamp: Date.now() - 7 * 60 * 1000 }); // 7 min ago
      stateStore.set('ancient', { timestamp: Date.now() - 15 * 60 * 1000 }); // 15 min ago

      expect(stateStore.size).toBe(3);

      cleanupExpiredStates();

      // Only recent state should remain
      expect(stateStore.size).toBe(1);
      expect(stateStore.has('recent')).toBe(true);
      expect(stateStore.has('old')).toBe(false);
      expect(stateStore.has('ancient')).toBe(false);
    });
  });

  describe('Network Failure Scenarios', () => {
    it('should handle timeout scenarios correctly', async () => {
      const simulateCodeExchange = (shouldTimeout: boolean) => {
        return new Promise((resolve, reject) => {
          if (shouldTimeout) {
            // Simulate hanging request
            setTimeout(
              () =>
                resolve({
                  data: { session: { user: { email: '<EMAIL>' } } },
                }),
              15000
            );
          } else {
            // Simulate normal response
            setTimeout(
              () =>
                resolve({
                  data: { session: { user: { email: '<EMAIL>' } } },
                }),
              100
            );
          }
        });
      };

      const exchangeWithTimeout = async (shouldTimeout: boolean) => {
        const exchangePromise = simulateCodeExchange(shouldTimeout);
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Code exchange timeout')), 1000)
        );

        try {
          const result = await Promise.race([exchangePromise, timeoutPromise]);
          return { success: true, result };
        } catch (error) {
          return { success: false, error: error.message };
        }
      };

      // Normal case (should succeed)
      const normalResult = await exchangeWithTimeout(false);
      expect(normalResult.success).toBe(true);

      // Timeout case (should fail)
      const timeoutResult = await exchangeWithTimeout(true);
      expect(timeoutResult.success).toBe(false);
      expect(timeoutResult.error).toBe('Code exchange timeout');
    });

    it('should handle network connectivity issues', () => {
      const simulateNetworkError = (errorType: string) => {
        const errors = {
          ENOTFOUND: { code: 'ENOTFOUND', message: 'DNS lookup failed' },
          ECONNRESET: { code: 'ECONNRESET', message: 'Connection reset' },
          ETIMEDOUT: { code: 'ETIMEDOUT', message: 'Request timeout' },
        };
        return (
          errors[errorType] || { code: 'UNKNOWN', message: 'Unknown error' }
        );
      };

      const isRetryableError = (error: any): boolean => {
        return ['ENOTFOUND', 'ECONNRESET', 'ETIMEDOUT'].includes(error.code);
      };

      // Test different network errors
      expect(isRetryableError(simulateNetworkError('ENOTFOUND'))).toBe(true);
      expect(isRetryableError(simulateNetworkError('ECONNRESET'))).toBe(true);
      expect(isRetryableError(simulateNetworkError('ETIMEDOUT'))).toBe(true);
      expect(isRetryableError(simulateNetworkError('UNKNOWN'))).toBe(false);
    });
  });

  describe('Error Sanitization', () => {
    it('should sanitize error messages for security', () => {
      const sanitizeOAuthError = (
        error: string,
        errorDescription?: string
      ): string => {
        // Don't expose sensitive error details
        if (error === 'access_denied') {
          return 'User cancelled authentication';
        }
        if (error === 'invalid_client') {
          return 'Authentication configuration error';
        }
        if (error === 'invalid_grant') {
          return 'Authentication expired - please try again';
        }
        return 'Authentication failed - please try again';
      };

      expect(sanitizeOAuthError('access_denied')).toBe(
        'User cancelled authentication'
      );
      expect(sanitizeOAuthError('invalid_client')).toBe(
        'Authentication configuration error'
      );
      expect(sanitizeOAuthError('invalid_grant')).toBe(
        'Authentication expired - please try again'
      );
      expect(sanitizeOAuthError('unknown_error')).toBe(
        'Authentication failed - please try again'
      );
    });

    it('should prevent information leakage in logs', () => {
      const sanitizeLogData = (data: any) => {
        const sanitized = { ...data };

        // Remove sensitive fields
        delete sanitized.access_token;
        delete sanitized.refresh_token;
        delete sanitized.code;
        delete sanitized.client_secret;

        // Truncate long values
        Object.keys(sanitized).forEach(key => {
          if (
            typeof sanitized[key] === 'string' &&
            sanitized[key].length > 50
          ) {
            sanitized[key] = sanitized[key].substring(0, 47) + '...';
          }
        });

        return sanitized;
      };

      const sensitiveData = {
        access_token: 'very-long-secret-token-that-should-not-be-logged',
        refresh_token: 'another-secret-token',
        code: 'authorization-code',
        client_secret: 'client-secret',
        state: 'safe-to-log',
        error: 'safe-to-log',
      };

      const sanitized = sanitizeLogData(sensitiveData);

      expect(sanitized.access_token).toBeUndefined();
      expect(sanitized.refresh_token).toBeUndefined();
      expect(sanitized.code).toBeUndefined();
      expect(sanitized.client_secret).toBeUndefined();
      expect(sanitized.state).toBe('safe-to-log');
      expect(sanitized.error).toBe('safe-to-log');
    });
  });

  describe('Session Management Edge Cases', () => {
    it('should handle concurrent authentication attempts', () => {
      let activeAuthPromise: any = null;

      const startAuth = () => {
        if (activeAuthPromise) {
          throw new Error('Authentication already in progress');
        }

        activeAuthPromise = new Promise(resolve => {
          setTimeout(() => {
            activeAuthPromise = null;
            resolve({ success: true });
          }, 100);
        });

        return activeAuthPromise;
      };

      // First auth should succeed
      expect(() => startAuth()).not.toThrow();

      // Concurrent auth should fail
      expect(() => startAuth()).toThrow('Authentication already in progress');
    });

    it('should validate session state transitions', () => {
      let sessionState = 'IDLE';

      const transitionTo = (newState: string) => {
        const validTransitions = {
          IDLE: ['AUTHENTICATING'],
          AUTHENTICATING: ['AUTHENTICATED', 'FAILED', 'IDLE'],
          AUTHENTICATED: ['IDLE'],
          FAILED: ['IDLE'],
        };

        if (!validTransitions[sessionState]?.includes(newState)) {
          throw new Error(
            `Invalid state transition: ${sessionState} -> ${newState}`
          );
        }

        sessionState = newState;
        return sessionState;
      };

      // Valid transitions
      expect(transitionTo('AUTHENTICATING')).toBe('AUTHENTICATING');
      expect(transitionTo('AUTHENTICATED')).toBe('AUTHENTICATED');
      expect(transitionTo('IDLE')).toBe('IDLE');

      // Invalid transition
      expect(() => transitionTo('FAILED')).toThrow('Invalid state transition');
    });
  });
});
