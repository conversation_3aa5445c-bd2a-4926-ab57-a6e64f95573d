/**
 * @file typed-ipc.ts
 * @description Type-safe IPC communication bridge for Electron renderer process
 * Provides compile-time type safety for all IPC operations
 */

import type { Result, ApiResponse } from '@shared/types/result.types';

// ===== IPC CHANNEL DEFINITIONS =====

/**
 * Type-safe IPC channel mapping for all application communications
 */
export interface IPCChannelMap {
  // Application lifecycle
  'app:ready': { params: []; return: void };
  'app:quit': { params: []; return: void };
  'app:minimize': { params: []; return: void };
  'app:maximize': { params: []; return: void };
  'app:close': { params: []; return: void };
  'app:is-maximized': { params: []; return: boolean };
  'app:toggle-dev-tools': { params: []; return: void };
  'app:get-info': { params: []; return: ApiResponse<any> };

  // Color management
  'color:get-all': { params: []; return: ApiResponse<any[]> };
  'color:get-by-id': { params: [string]; return: ApiResponse<any> };
  'color:create': { params: [any]; return: ApiResponse<any> };
  'color:update': { params: [string, any]; return: ApiResponse<any> };
  'color:delete': { params: [string]; return: ApiResponse<boolean> };
  'color:import': { params: [string | undefined, ('json' | 'csv') | undefined, ('replace' | 'merge') | undefined]; return: ApiResponse<{ added: number; errors: string[] }> };
  'color:export': { params: [string | undefined, ('json' | 'csv') | undefined]; return: ApiResponse<boolean> };

  // Product management
  'product:get-all': { params: []; return: ApiResponse<any[]> };
  'product:get-all-with-colors': { params: []; return: ApiResponse<any[]> };
  'product:create': { params: [{ name: string; metadata?: any }]; return: ApiResponse<any> };
  'product:delete': { params: [string]; return: ApiResponse<boolean> };
  'product:add-color': { params: [string, string]; return: ApiResponse<any> };
  'product:remove-color': { params: [string, string]; return: ApiResponse<any> };
  'product:deduplicate': { params: []; return: ApiResponse<any> };

  // Organization management
  'organization:get-all': { params: []; return: ApiResponse<any[]> };
  'organization:get-current': { params: []; return: ApiResponse<any> };
  'organization:set-current': { params: [string]; return: ApiResponse<void> };
  'organization:create': { params: [{ name: string; slug?: string }]; return: ApiResponse<any> };
  'organization:delete': { params: [string, boolean?]; return: ApiResponse<boolean> };
  'organization:invite-member': { params: [{ organizationId: string; email: string; role?: string }]; return: ApiResponse<void> };
  'organization:remove-member': { params: [{ organizationId: string; userId: string }]; return: ApiResponse<void> };

  // Sync operations
  'sync:get-config': { params: []; return: ApiResponse<any> };
  'sync:update-config': { params: [any]; return: ApiResponse<void> };
  'sync:get-state': { params: []; return: ApiResponse<any> };
  'sync:get-auth-state': { params: []; return: ApiResponse<any> };
  'sync:login': { params: []; return: ApiResponse<any> };
  'sync:logout': { params: []; return: ApiResponse<void> };
  'sync:sync-data': { params: []; return: ApiResponse<void> };
  'sync:test-connection': { params: []; return: ApiResponse<void> };
  'sync:resolve-conflicts': { params: [any[]]; return: ApiResponse<void> };

  // Database operations
  'db:check-integrity': { params: [string?]; return: ApiResponse<any> };
  'db:get-stats': { params: []; return: ApiResponse<any> };
  'db:optimize': { params: []; return: ApiResponse<void> };
  'db:vacuum': { params: []; return: ApiResponse<void> };

  // File operations
  'file:select-logo': { params: []; return: ApiResponse<string> };
  'file:select-folder': { params: []; return: ApiResponse<string> };
  'file:read': { params: [string]; return: ApiResponse<string> };
  'file:write': { params: [string, string]; return: ApiResponse<void> };

  // Monitoring and logging
  'monitoring:track-error': { params: [Error | { message: string; stack?: string; componentStack?: string; location?: string }]; return: ApiResponse<void> };
  'log:get-application-logs': { params: []; return: ApiResponse<string[]> };
  'log:clear-application-logs': { params: []; return: ApiResponse<void> };
  'log:export-application-logs': { params: []; return: ApiResponse<string> };

  // Auto-updater
  'updater:check-for-updates': { params: []; return: ApiResponse<any> };
  'updater:download-update': { params: []; return: ApiResponse<void> };
  'updater:install-update': { params: []; return: ApiResponse<void> };

  // Color library
  'library:search-colors': { params: [any]; return: ApiResponse<any[]> };
  'library:get-pantone-colors': { params: [any]; return: ApiResponse<any[]> };
  'library:get-ral-colors': { params: [any]; return: ApiResponse<any[]> };
  'library:full-text-search': { params: [string, any]; return: ApiResponse<any[]> };
  'library:get-popular-colors': { params: [string?, number?]; return: ApiResponse<any[]> };
  'library:get-color-by-external-id': { params: [string]; return: ApiResponse<any> };
  'library:get-color-by-code': { params: [string, string]; return: ApiResponse<any> };

  // Testing and development
  'test:create-test-product': { params: []; return: ApiResponse<any> };
  'test:remove-test-data': { params: []; return: ApiResponse<void> };
  'test:run-performance-test': { params: []; return: ApiResponse<any> };
}

/**
 * IPC event mapping for event listeners
 */
export interface IPCEventMap {
  // Sync events
  'sync:status-update': { status: string; message?: string; timestamp?: number; error?: string; phase?: string };
  'sync:data-changed': { table: string; event: string; data: any };
  'sync:conflicts': any[];
  'sync:progress-update': any;
  'sync:queue-update': any;
  'sync:metrics-update': any;
  'sync:slow-operation': any;

  // Auth events
  'auth:session-warning': { minutesRemaining: number };
  'auth:session-expired': { reason: string; timeout: number };
  'auth:loop-detected': { cooldownMinutes: number; cooldownUntil: number; attempts: number; windowMinutes: number };
  'auth:loop-reset': {};

  // Auto-sync events
  'auto-sync:started': any;
  'auto-sync:completed': any;
  'auto-sync:failed': any;
  'auto-sync:config-updated': any;

  // Auto-updater events
  'updater:status-change': { status: string; data?: any; error?: string };

  // Organization events
  'organization:invitation-received': string;

  // Application events
  'app:setup-modal': {};
}

// ===== TYPE-SAFE IPC CLIENT =====

/**
 * Type-safe IPC client for renderer process
 */
export class TypedIPC {
  private static instance: TypedIPC;
  private ipc: typeof window.ipc;

  private constructor() {
    if (!window.ipc) {
      throw new Error('IPC not available - ensure preload script is loaded');
    }
    this.ipc = window.ipc;
  }

  /**
   * Get singleton instance
   */
  static getInstance(): TypedIPC {
    if (!TypedIPC.instance) {
      TypedIPC.instance = new TypedIPC();
    }
    return TypedIPC.instance;
  }

  /**
   * Invoke IPC method with full type safety
   */
  async invoke<TChannel extends keyof IPCChannelMap>(
    channel: TChannel,
    ...args: IPCChannelMap[TChannel]['params']
  ): Promise<IPCChannelMap[TChannel]['return']> {
    try {
      const result = await this.ipc.invoke(channel, ...args);
      return result;
    } catch (error) {
      console.error(`IPC invoke failed for channel "${channel}":`, error);
      throw error;
    }
  }

  /**
   * Listen to IPC events with type safety
   */
  on<TEvent extends keyof IPCEventMap>(
    event: TEvent,
    callback: (data: IPCEventMap[TEvent]) => void
  ): () => void {
    return this.ipc.on(event, callback);
  }

  /**
   * Remove IPC event listener
   */
  off<TEvent extends keyof IPCEventMap>(
    event: TEvent,
    callback: (data: IPCEventMap[TEvent]) => void
  ): void {
    // Note: Electron's ipcRenderer.off implementation
    // We rely on the returned cleanup function from .on()
    console.warn('Use the cleanup function returned from .on() instead of .off()');
  }

  /**
   * Helper method to safely invoke IPC with error handling
   */
  async safeInvoke<TChannel extends keyof IPCChannelMap>(
    channel: TChannel,
    ...args: IPCChannelMap[TChannel]['params']
  ): Promise<Result<IPCChannelMap[TChannel]['return'], Error>> {
    try {
      const result = await this.invoke(channel, ...args);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error))
      };
    }
  }

  /**
   * Helper method to invoke API methods that return ApiResponse
   */
  async invokeAPI<TChannel extends keyof IPCChannelMap>(
    channel: TChannel,
    ...args: IPCChannelMap[TChannel]['params']
  ): Promise<IPCChannelMap[TChannel]['return'] extends ApiResponse<infer T> ? T : never> {
    const response = await this.invoke(channel, ...args) as any;
    
    if (response && typeof response === 'object' && 'success' in response) {
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error?.message || 'API call failed');
      }
    }
    
    return response;
  }

  /**
   * Helper method to safely invoke API methods with error handling
   */
  async safeInvokeAPI<TChannel extends keyof IPCChannelMap>(
    channel: TChannel,
    ...args: IPCChannelMap[TChannel]['params']
  ): Promise<Result<IPCChannelMap[TChannel]['return'] extends ApiResponse<infer T> ? T : never, Error>> {
    try {
      const data = await this.invokeAPI(channel, ...args);
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error(String(error))
      };
    }
  }
}

// ===== TYPED IPC HOOKS FOR REACT =====

import { useCallback, useEffect, useRef, useState } from 'react';

/**
 * Hook for type-safe IPC invocation
 */
export function useIPC() {
  const ipc = useRef(TypedIPC.getInstance());
  return ipc.current;
}

/**
 * Hook for IPC event listening with automatic cleanup
 */
export function useIPCEvent<TEvent extends keyof IPCEventMap>(
  event: TEvent,
  callback: (data: IPCEventMap[TEvent]) => void,
  deps: React.DependencyList = []
) {
  const ipc = useIPC();
  
  useEffect(() => {
    const cleanup = ipc.on(event, callback);
    return cleanup;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);
}

/**
 * Hook for async IPC operations with loading state
 */
export function useIPCQuery<TChannel extends keyof IPCChannelMap>(
  channel: TChannel,
  ...args: IPCChannelMap[TChannel]['params']
) {
  const [data, setData] = useState<IPCChannelMap[TChannel]['return'] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const ipc = useIPC();

  const execute = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await ipc.invoke(channel, ...args);
      setData(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [ipc, channel, ...args]);

  return {
    data,
    loading,
    error,
    execute,
    refetch: execute,
  };
}

/**
 * Hook for IPC mutations with optimistic updates
 */
export function useIPCMutation<TChannel extends keyof IPCChannelMap>(
  channel: TChannel,
  options?: {
    onSuccess?: (data: IPCChannelMap[TChannel]['return']) => void;
    onError?: (error: Error) => void;
    onSettled?: () => void;
  }
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const ipc = useIPC();

  const mutate = useCallback(
    async (...args: IPCChannelMap[TChannel]['params']) => {
      setLoading(true);
      setError(null);

      try {
        const result = await ipc.invoke(channel, ...args);
        options?.onSuccess?.(result);
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);
        options?.onError?.(error);
        throw error;
      } finally {
        setLoading(false);
        options?.onSettled?.();
      }
    },
    [ipc, channel, options]
  );

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
  }, []);

  return {
    mutate,
    loading,
    error,
    reset,
  };
}

/**
 * Hook for real-time IPC data with automatic refetching
 */
export function useIPCLiveQuery<TChannel extends keyof IPCChannelMap>(
  channel: TChannel,
  refreshEvent?: keyof IPCEventMap,
  interval?: number,
  ...args: IPCChannelMap[TChannel]['params']
) {
  const [data, setData] = useState<IPCChannelMap[TChannel]['return'] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const ipc = useIPC();

  const fetchData = useCallback(async () => {
    try {
      setError(null);
      const result = await ipc.invoke(channel, ...args);
      setData(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
    } finally {
      setLoading(false);
    }
  }, [ipc, channel, ...args]);

  // Initial fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Event-based refresh
  useIPCEvent(
    refreshEvent!,
    useCallback(() => {
      fetchData();
    }, [fetchData]),
    refreshEvent ? [] : undefined
  );

  // Interval-based refresh
  useEffect(() => {
    if (!interval) return;

    const intervalId = setInterval(fetchData, interval);
    return () => clearInterval(intervalId);
  }, [fetchData, interval]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
}

// ===== UTILITY FUNCTIONS =====

/**
 * Type-safe wrapper for window IPC availability check
 */
export function isIPCAvailable(): boolean {
  return typeof window !== 'undefined' && 
         window.ipc !== undefined && 
         typeof window.ipc.invoke === 'function';
}

/**
 * Create typed IPC client instance
 */
export function createTypedIPC(): TypedIPC {
  return TypedIPC.getInstance();
}

/**
 * Global typed IPC instance
 */
export const typedIPC = createTypedIPC();

// Re-export for convenience
export default typedIPC;