/**
 * @file ColorTableRow.tsx
 * @description Table row component for displaying a color entry
 */

import { ColorEntry } from '../../../shared/types/color.types';
import { useColorStore } from '../../store/color.store';
import { useTokens } from '../../hooks/useTokens';
import { useModal } from '../../hooks/useModal';
import {
  parseCMYK,
  formatCMYKForDisplay,
  formatHex,
  hexToRgb,
  formatRGBForDisplay,
} from '../../../shared/utils/color';
import { ConfirmModal, AlertModal } from '../ui/Modal/index';

interface ColorTableRowProps {
  entry: ColorEntry;
  onEdit: (entry: ColorEntry) => void;
  onShowGradientDetails: (entry: ColorEntry) => void;
  view?: 'details' | 'reference';
  usageCount?: number;
  usedInProducts?: string[];
  isFocused?: boolean;
  onFocus?: () => void;
}

export default function ColorTableRow({
  entry,
  onEdit,
  onShowGradientDetails,
  view = 'details',
  usageCount,
  usedInProducts,
  isFocused,
  onFocus,
}: ColorTableRowProps) {
  const { deleteColor } = useColorStore();
  const tokens = useTokens();
  const {
    confirmState,
    alertState,
    closeConfirm,
    closeAlert,
    confirmDelete,
    showError,
  } = useModal();

  const handleDelete = async () => {
    confirmDelete(`${entry.name} (${entry.code})`, async () => {
      console.log('[ColorTableRow] Deleting color:', {
        id: entry.id,
        code: entry.code,
        name: entry.name,
      });
      const result = await deleteColor(entry.id);
      if (!result) {
        showError('Failed to delete color. Please try again.');
      }
    });
  };

  // Helper function to clean color code for display
  const getCleanCode = (code: string, stripSuffix: boolean = true): string => {
    if (!code || typeof code !== 'string') {
      return '';
    }

    if (!stripSuffix) {
      // Return the full code without the first part (for gradients)
      const parts = code.split('-');
      return parts.slice(1).join('-') || '';
    }

    // Remove the unique identifier suffix (e.g., "939-MB2UE4Z6-8CAA" -> "939")
    const parts = code.split('-');
    return parts[0] || '';
  };

  // Get table cell border classes
  const getBorderClasses = () => {
    return 'border-b border-ui-border-light dark:border-ui-border-dark';
  };

  // Determine if this is a gradient
  const hasGradient = !!entry.gradient;

  // Get row hover classes (matching PantoneTable)
  const getRowHoverClasses = () => {
    return 'hover:bg-ui-background-secondary text-xs text-ui-foreground-primary';
  };

  // Class for gradient indicator
  const gradientIndicatorClasses =
    'text-xs text-[color:var(--color-brand-primary)] cursor-pointer hover:underline transition-colors duration-150';

  // Transition styles for buttons
  const buttonTransitionStyle = {
    transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`,
  };

  return (
    <>
      <tr
        className={`${getRowHoverClasses()} ${isFocused ? 'ring-2 ring-brand-primary ring-inset' : ''}`}
        tabIndex={-1}
        onFocus={onFocus}
        role='row'
        aria-selected={isFocused}
      >
        {view === 'reference' ? (
          <>
            <td
              className={`py-3 px-4 ${getBorderClasses()} align-middle font-medium`}
            >
              {(() => {
                const cleanCode = getCleanCode(entry.code);
                // Debug logging for gradients
                if (entry.gradient) {
                  console.log('[ColorTableRow] Gradient debug:', {
                    code: entry.code,
                    cleanCode,
                    name: entry.name,
                    hasGradient: !!entry.gradient,
                  });
                }
                // Handle gradient codes differently from Pantone codes
                if (
                  entry.gradient ||
                  cleanCode === 'grad' ||
                  cleanCode === 'Gradient'
                ) {
                  const timestamp = getCleanCode(entry.code, false);
                  return `GRAD ${timestamp}`;
                }
                return cleanCode.includes('PMS')
                  ? cleanCode
                  : `PMS ${cleanCode}`;
              })()}
            </td>
            <td className={`py-3 px-4 ${getBorderClasses()} align-middle`}>
              {entry.name || getCleanCode(entry.code)}
            </td>
            <td
              className={`py-3 px-4 ${getBorderClasses()} text-center align-middle`}
            >
              <div className='flex flex-col items-center justify-center'>
                <div
                  className='w-8 h-8 rounded-[var(--radius-sm)] border border-ui-border-light dark:border-ui-border-dark'
                  style={{
                    background: hasGradient
                      ? entry.gradient?.colors
                        ? `linear-gradient(45deg, ${entry.gradient.colors.join(', ')})`
                        : entry.hex
                      : entry.hex,
                    boxShadow: 'inset 0 0 0 1px rgba(0,0,0,0.05)',
                  }}
                  data-testid={
                    hasGradient
                      ? 'gradient-swatch-preview'
                      : 'color-swatch-preview'
                  }
                />
                {!hasGradient ? (
                  <span className='text-xs mt-1 text-ui-foreground-secondary'>
                    {formatHex(entry.hex)}
                  </span>
                ) : (
                  <span
                    className={gradientIndicatorClasses}
                    onClick={e => {
                      e.stopPropagation();
                      onShowGradientDetails(entry);
                    }}
                  >
                    Gradient
                  </span>
                )}
              </div>
            </td>
            <td
              className={`py-3 px-4 ${getBorderClasses()} font-mono text-xs whitespace-nowrap align-middle`}
            >
              {formatCMYKForDisplay(parseCMYK(entry.cmyk))}
            </td>
            <td
              className={`py-3 px-4 ${getBorderClasses()} font-mono text-xs whitespace-nowrap align-middle`}
            >
              {/* Extract RGB from hex */}
              {(() => {
                const rgb = hexToRgb(entry.hex);
                return rgb ? formatRGBForDisplay(rgb) : 'R:0 G:0 B:0';
              })()}
            </td>
            <td
              className={`py-3 px-4 ${getBorderClasses()} text-center align-middle`}
            >
              <div className='flex flex-col items-center'>
                <span className='text-lg font-semibold text-brand-primary'>
                  {usageCount || 0}
                </span>
                {usedInProducts && usedInProducts.length > 1 && (
                  <span
                    className='text-xs text-ui-foreground-tertiary'
                    title={`Used in: ${usedInProducts.join(', ')}`}
                  >
                    {usedInProducts.length} product
                    {usedInProducts.length !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            </td>
            <td className={`py-3 px-4 ${getBorderClasses()} align-middle`}>
              <button
                onClick={() => {
                  const cmyk = formatCMYKForDisplay(parseCMYK(entry.cmyk));
                  const formattedHex = formatHex(entry.hex);
                  const rgb = hexToRgb(entry.hex);
                  const rgbStr = rgb ? formatRGBForDisplay(rgb) : 'R:0 G:0 B:0';
                  const data = `${entry.code}\nHex: ${formattedHex}\nCMYK: ${cmyk}\nRGB: ${rgbStr}`;
                  navigator.clipboard.writeText(data);
                }}
                className='px-3 py-1 text-xs border border-ui-border-light rounded hover:bg-ui-background-secondary transition-colors'
              >
                Copy
              </button>
            </td>
          </>
        ) : (
          <>
            <td className={`py-3 px-4 ${getBorderClasses()} align-middle`}>
              <span className='flex items-center text-ui-foreground-primary'>
                {entry.product ||
                  (entry.isLibrary ? 'Library Color' : 'No Product')}
              </span>
            </td>
            <td className={`py-3 px-4 ${getBorderClasses()} align-middle`}>
              {entry.name}
            </td>
            <td className={`py-3 px-4 ${getBorderClasses()} align-middle`}>
              {(() => {
                const cleanCode = getCleanCode(entry.code);
                // Handle gradient codes differently from Pantone codes
                if (
                  entry.gradient ||
                  cleanCode === 'grad' ||
                  cleanCode === 'Gradient'
                ) {
                  const timestamp = getCleanCode(entry.code, false);
                  return `GRAD ${timestamp}`;
                }
                return cleanCode.includes('PMS')
                  ? cleanCode
                  : `PMS ${cleanCode}`;
              })()}
            </td>
            <td
              className={`py-3 px-4 ${getBorderClasses()} text-center align-middle`}
            >
              <div className='flex flex-col items-center justify-center'>
                <div
                  className='w-8 h-8 rounded-[var(--radius-sm)] border border-ui-border-light dark:border-ui-border-dark'
                  style={{
                    background: hasGradient
                      ? entry.gradient?.colors
                        ? `linear-gradient(45deg, ${entry.gradient.colors.join(', ')})`
                        : entry.hex
                      : entry.hex,
                    boxShadow: 'inset 0 0 0 1px rgba(0,0,0,0.05)',
                  }}
                  data-testid={
                    hasGradient
                      ? 'gradient-swatch-preview'
                      : 'color-swatch-preview'
                  }
                />
                {!hasGradient ? (
                  <span className='text-xs mt-1 text-ui-foreground-secondary'>
                    {formatHex(entry.hex)}
                  </span>
                ) : (
                  <span
                    className={gradientIndicatorClasses}
                    onClick={e => {
                      e.stopPropagation();
                      onShowGradientDetails(entry);
                    }}
                  >
                    Gradient
                  </span>
                )}
              </div>
            </td>
            <td
              className={`py-3 px-4 ${getBorderClasses()} font-mono text-xs whitespace-nowrap align-middle tracking-tighter overflow-hidden text-ellipsis max-w-[150px]`}
            >
              {!hasGradient ? (
                formatCMYKForDisplay(parseCMYK(entry.cmyk))
              ) : (
                <span className='font-sans'>
                  {entry.gradient?.colors?.length || 0}{' '}
                  {(entry.gradient?.colors?.length || 0) === 1
                    ? 'stop'
                    : 'stops'}
                </span>
              )}
            </td>
            <td className={`py-3 px-4 ${getBorderClasses()} align-middle`}>
              {entry.notes || '-'}
            </td>
            <td className={`py-3 px-4 ${getBorderClasses()} align-middle`}>
              <div className='flex items-center'>
                <button
                  onClick={() => onEdit(entry)}
                  className='text-ui-foreground-secondary hover:text-ui-foreground-primary mr-2 p-1'
                  style={buttonTransitionStyle}
                  aria-label='Edit color'
                  data-action='edit'
                >
                  <svg
                    className='w-4 h-4'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z'
                     />
                  </svg>
                </button>
                <button
                  onClick={handleDelete}
                  className='text-ui-foreground-secondary hover:text-feedback-error p-1'
                  style={buttonTransitionStyle}
                  aria-label='Delete color'
                  data-action='delete'
                >
                  <svg
                    className='w-4 h-4'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth='2'
                      d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
                     />
                  </svg>
                </button>
              </div>
            </td>
          </>
        )}
      </tr>

      {/* Modals */}
      <ConfirmModal
        isOpen={confirmState.isOpen}
        onClose={closeConfirm}
        onConfirm={confirmState.onConfirm || (() => {})}
        title={confirmState.title}
        message={confirmState.message}
        variant={confirmState.variant as 'danger' | 'warning' | 'info'}
        confirmText={confirmState.confirmText}
        cancelText={confirmState.cancelText}
      />

      <AlertModal
        isOpen={alertState.isOpen}
        onClose={closeAlert}
        title={alertState.title}
        message={alertState.message}
        variant={alertState.variant as 'success' | 'error' | 'warning' | 'info'}
      />
    </>
  );
}
