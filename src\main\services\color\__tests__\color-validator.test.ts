/**
 * @file color-validator.test.ts
 * @description Unit tests for ColorValidator service
 *
 * Tests comprehensive color validation rules, standardization, and business logic
 * that will be extracted from the ColorService. This includes existing validation
 * from shared/utils/color/* and new validation rules.
 */

import { describe, test, expect, beforeEach } from 'vitest';
import {
  ColorValidator,
  ColorValidationResult,
  ColorValidationOptions,
  StandardizedColorData,
  ColorFormatValidationResult,
} from '../color-validator.service';

describe('ColorValidator Service', () => {
  let validator: ColorValidator;

  beforeEach(() => {
    validator = new ColorValidator();
  });

  describe('HEX Validation', () => {
    describe('validateHex', () => {
      test('should validate correct HEX colors', () => {
        const validHexes = [
          '#FF0000',
          '#00FF00',
          '#0000FF',
          '#FFFFFF',
          '#000000',
        ];

        for (const hex of validHexes) {
          const result = validator.validateHex(hex);
          expect(result.isValid).toBe(true);
          expect(result.errors).toHaveLength(0);
        }
      });

      test('should reject invalid HEX colors', () => {
        const invalidHexes = [
          'FF0000',
          '#GG0000',
          '#FFF',
          '#1234567',
          'red',
          '',
        ];

        for (const hex of invalidHexes) {
          const result = validator.validateHex(hex);
          expect(result.isValid).toBe(false);
          expect(result.errors.length).toBeGreaterThan(0);
        }
      });

      test('should handle allowEmpty option', () => {
        const result = validator.validateHex('', { allowEmpty: true });
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      test('should handle allowShortHex option', () => {
        const result = validator.validateHex('#FFF', { allowShortHex: true });
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      test('should warn about case in strict mode', () => {
        const result = validator.validateHex('#ff0000', { strict: true });
        expect(result.warnings).toContain(
          'HEX color code should be uppercase for consistency'
        );
      });
    });

    describe('standardizeHex', () => {
      test('should standardize HEX colors', () => {
        expect(validator.standardizeHex('ff0000')).toBe('#FF0000');
        expect(validator.standardizeHex('#ff0000')).toBe('#FF0000');
        expect(validator.standardizeHex('#FFF')).toBe('#FFFFFF');
      });

      test('should handle empty input', () => {
        expect(validator.standardizeHex('')).toBe('');
        expect(validator.standardizeHex('   ')).toBe('#');
      });
    });

    describe('fixHexIssues', () => {
      test('should fix common HEX issues', () => {
        expect(validator.fixHexIssues('0xFF0000')).toBe('#FF0000');
        expect(validator.fixHexIssues('\\xFF0000')).toBe('#FF0000');
        expect(validator.fixHexIssues('ff0000')).toBe('#FF0000');
        expect(validator.fixHexIssues('#FFF')).toBe('#FFFFFF');
      });
    });
  });

  describe('CMYK Validation', () => {
    describe('validateCMYK', () => {
      test('should validate correct CMYK values', () => {
        const validCMYKs = [
          'C:0 M:100 Y:100 K:0',
          'C:50 M:0 Y:75 K:25',
          '0,100,100,0',
          '50 0 75 25',
        ];

        for (const cmyk of validCMYKs) {
          const result = validator.validateCMYK(cmyk);
          expect(result.isValid).toBe(true);
          expect(result.errors).toHaveLength(0);
        }
      });

      test('should reject invalid CMYK values', () => {
        const invalidCMYKs = [
          'C:150 M:0 Y:0 K:0', // Out of range
          'C:-10 M:0 Y:0 K:0', // Negative
          'invalid cmyk', // Invalid format
          'C:0 M:0', // Incomplete
        ];

        for (const cmyk of invalidCMYKs) {
          const result = validator.validateCMYK(cmyk);
          expect(result.isValid).toBe(false);
          expect(result.errors.length).toBeGreaterThan(0);
        }
      });

      test('should warn about decimal precision in strict mode', () => {
        const result = validator.validateCMYK('C:0.5 M:100.7 Y:50.3 K:0.1', {
          strict: true,
        });
        expect(result.warnings.length).toBeGreaterThan(0);
        expect(result.warnings.some(w => w.includes('decimal precision'))).toBe(
          true
        );
      });
    });

    describe('standardizeCMYK', () => {
      test('should standardize CMYK values', () => {
        expect(validator.standardizeCMYK('c:0 m:100 y:100 k:0')).toBe(
          'C:0 M:100 Y:100 K:0'
        );
        expect(validator.standardizeCMYK('0,100,100,0')).toBe(
          'C:0 M:100 Y:100 K:0'
        );
        expect(validator.standardizeCMYK('N/A')).toBe('C:0 M:0 Y:0 K:0');
        expect(validator.standardizeCMYK('')).toBe('C:0 M:0 Y:0 K:0');
      });

      test('should round decimal values', () => {
        expect(validator.standardizeCMYK('C:0.7 M:99.3 Y:50.5 K:0.2')).toBe(
          'C:1 M:99 Y:51 K:0'
        );
      });
    });
  });

  describe('Color Name Validation', () => {
    describe('validateColorName', () => {
      test('should validate correct color names', () => {
        const validNames = [
          'Red',
          'Blue Ocean',
          'Forest Green',
          'Sky Blue-01',
          'Color_Name',
        ];

        for (const name of validNames) {
          const result = validator.validateColorName(name);
          expect(result.isValid).toBe(true);
          expect(result.errors).toHaveLength(0);
        }
      });

      test('should reject invalid color names', () => {
        const invalidNames = [
          'R',
          'a'.repeat(101),
          'Invalid@Name',
          'Name#With#Hash',
        ];

        for (const name of invalidNames) {
          const result = validator.validateColorName(name);
          expect(result.isValid).toBe(false);
          expect(result.errors.length).toBeGreaterThan(0);
        }
      });

      test('should warn about normalization in strict mode', () => {
        const result = validator.validateColorName('red ocean', {
          strict: true,
        });
        expect(result.warnings).toContain(
          'Color name will be normalized to title case'
        );
      });
    });

    describe('normalizeColorName', () => {
      test('should normalize color names to title case', () => {
        expect(validator.normalizeColorName('red ocean')).toBe('Red Ocean');
        expect(validator.normalizeColorName('BLUE SKY')).toBe('Blue Sky');
        expect(validator.normalizeColorName('forest green')).toBe(
          'Forest Green'
        );
      });
    });
  });

  describe('Color Code Validation', () => {
    describe('validateColorCode', () => {
      test('should validate correct color codes', () => {
        const validCodes = ['186 C', 'PMS 2945', '123', 'COLOR-01', 'Code_123'];

        for (const code of validCodes) {
          const result = validator.validateColorCode(code);
          expect(result.isValid).toBe(true);
          expect(result.errors).toHaveLength(0);
        }
      });

      test('should reject invalid color codes', () => {
        const invalidCodes = ['a'.repeat(51), 'Invalid@Code', 'Code#With#Hash'];

        for (const code of invalidCodes) {
          const result = validator.validateColorCode(code);
          expect(result.isValid).toBe(false);
          expect(result.errors.length).toBeGreaterThan(0);
        }
      });
    });

    describe('standardizeColorCode', () => {
      test('should standardize color codes', () => {
        expect(validator.standardizeColorCode('PMS 186 C')).toBe('186 C');
        expect(validator.standardizeColorCode('P 2945 c')).toBe('2945 C');
        expect(validator.standardizeColorCode('123')).toBe('123');
      });
    });
  });

  describe('Format Detection', () => {
    describe('detectColorFormat', () => {
      test('should detect HEX format', () => {
        const result = validator.detectColorFormat('#FF0000');
        expect(result.format).toBe('hex');
        expect(result.isValid).toBe(true);
        expect(result.standardizedValue).toBe('#FF0000');
      });

      test('should detect CMYK format', () => {
        const result = validator.detectColorFormat('C:0 M:100 Y:100 K:0');
        expect(result.format).toBe('cmyk');
        expect(result.isValid).toBe(true);
        expect(result.standardizedValue).toBe('C:0 M:100 Y:100 K:0');
      });

      test('should detect Pantone format', () => {
        const result = validator.detectColorFormat('186 C');
        expect(result.format).toBe('pantone');
        expect(result.isValid).toBe(true);
        expect(result.standardizedValue).toBe('186 C');
      });

      test('should handle unknown formats', () => {
        const result = validator.detectColorFormat('unknown format');
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Unknown color format');
        expect(result.suggestions?.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Comprehensive Validation', () => {
    describe('validateCompleteColorData', () => {
      test('should validate complete color data', () => {
        const colorData = {
          hex: '#FF0000',
          cmyk: 'C:0 M:100 Y:100 K:0',
          name: 'bright red',
          code: 'RED-01',
        };

        const result = validator.validateCompleteColorData(colorData);
        expect(result.errors).toHaveLength(0);
        expect(result.hex).toBe('#FF0000');
        expect(result.cmyk).toBe('C:0 M:100 Y:100 K:0');
        expect(result.name).toBe('Bright Red');
        expect(result.code).toBe('RED-01');
      });

      test('should handle missing hex color', () => {
        const colorData = { name: 'Red' };
        const result = validator.validateCompleteColorData(colorData);
        expect(result.errors).toContain('HEX color value is required');
      });
    });
  });

  describe('Business Rule Validation', () => {
    describe('validateForDatabase', () => {
      test('should validate for database storage', () => {
        const colorData = {
          hex: '#FF0000',
          name: 'Red',
          organizationId: 'org-123',
        };

        const result = validator.validateForDatabase(colorData);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      test('should require organization ID', () => {
        const colorData = { hex: '#FF0000', name: 'Red' };
        const result = validator.validateForDatabase(colorData);
        expect(result.errors).toContain(
          'Organization ID is required for database storage'
        );
      });

      test('should require either name or code', () => {
        const colorData = { hex: '#FF0000', organizationId: 'org-123' };
        const result = validator.validateForDatabase(colorData);
        expect(result.errors).toContain(
          'Either color name or color code is required'
        );
      });
    });

    describe('validateForSync', () => {
      test('should validate for synchronization', () => {
        const colorData = {
          hex: '#FF0000',
          externalId: 'color-123',
        };

        const result = validator.validateForSync(colorData);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      test('should require external ID', () => {
        const colorData = { hex: '#FF0000' };
        const result = validator.validateForSync(colorData);
        expect(result.errors).toContain(
          'External ID is required for synchronization'
        );
      });
    });
  });

  describe('Utility Methods', () => {
    test('should provide validation rules', () => {
      const rules = validator.getValidationRules();
      expect(rules.hex).toBeDefined();
      expect(rules.cmyk).toBeDefined();
      expect(rules.name).toBeDefined();
      expect(rules.code).toBeDefined();
    });

    test('should list supported formats', () => {
      const formats = validator.getSupportedFormats();
      expect(formats).toContain('hex');
      expect(formats).toContain('cmyk');
      expect(formats).toContain('rgb');
      expect(formats).toContain('pantone');
    });

    test('should provide service info', () => {
      const info = validator.getServiceInfo();
      expect(info.name).toBe('ColorValidator');
      expect(info.version).toBeDefined();
      expect(info.features).toBeInstanceOf(Array);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle null and undefined inputs gracefully', () => {
      expect(() =>
        validator.validateHex(null as any, { allowEmpty: true })
      ).not.toThrow();
      expect(() =>
        validator.validateCMYK(undefined as any, { allowEmpty: true })
      ).not.toThrow();
      expect(() => validator.normalizeColorName(null as any)).not.toThrow();
    });

    test('should handle malformed data gracefully', () => {
      const malformedData = {
        hex: null,
        cmyk: undefined,
        name: 123 as any,
        code: [] as any,
      };

      expect(() =>
        validator.validateCompleteColorData(malformedData)
      ).not.toThrow();
      const result = validator.validateCompleteColorData(malformedData);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle very long inputs', () => {
      const longInput = 'a'.repeat(1000);
      const result = validator.validateColorName(longInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        'Color name cannot exceed 100 characters'
      );
    });

    test('should handle unicode and special characters', () => {
      const unicodeInput = 'Röd Färg 🎨';
      const result = validator.validateColorName(unicodeInput);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Color name contains invalid characters');
    });
  });
});
