import { render, screen } from '@testing-library/react';
import { useTokens } from './useTokens';
import { ThemeProvider, ThemeContext } from '../context/ThemeContext';
import { vi, describe, test, expect } from 'vitest';

// Mock component that uses the useTokens hook
const TokenConsumer = () => {
  const tokens = useTokens();

  return (
    <div>
      <div data-testid='theme-value'>{tokens.theme}</div>
      <div data-testid='primary-color'>{tokens.colors.brand.primary}</div>
      <div data-testid='font-family'>
        {tokens.typography.fontFamily.sans.join(',')}
      </div>
    </div>
  );
};

describe('useTokens hook', () => {
  test('should return tokens with light theme when ThemeContext has isDarkMode=false', () => {
    // Create a mock ThemeContext with light mode
    const mockThemeContext = {
      mode: 'light' as const,
      effectiveTheme: 'light' as const,
      setMode: vi.fn(),
      isTransitioning: false,
    };

    render(
      <ThemeContext.Provider value={mockThemeContext}>
        <TokenConsumer />
      </ThemeContext.Provider>
    );

    expect(screen.getByTestId('theme-value')).toHaveTextContent('light');
  });

  test('should return tokens with dark theme when ThemeContext has isDarkMode=true', () => {
    // Create a mock ThemeContext with dark mode
    const mockThemeContext = {
      mode: 'dark' as const,
      effectiveTheme: 'dark' as const,
      setMode: vi.fn(),
      isTransitioning: false,
    };

    render(
      <ThemeContext.Provider value={mockThemeContext}>
        <TokenConsumer />
      </ThemeContext.Provider>
    );

    expect(screen.getByTestId('theme-value')).toHaveTextContent('dark');
  });

  test('should provide access to token values', () => {
    render(
      <ThemeProvider>
        <TokenConsumer />
      </ThemeProvider>
    );

    // Verify token values are accessible
    expect(screen.getByTestId('primary-color')).toHaveTextContent('#');
    expect(screen.getByTestId('font-family')).not.toBeEmptyDOMElement();
  });
});
