#!/usr/bin/env node

/**
 * Test database initialization to identify the issue
 */

const path = require('path');
const os = require('os');

// Set up environment to mimic the main process
process.env.NODE_ENV = 'development';

async function testDatabaseInit() {
  console.log('🧪 Testing Database Initialization');
  console.log('=================================');
  
  try {
    // Import the database initialization function
    const { initDatabase } = require('../out/main/db/database.js');
    
    console.log('[Test] Starting database initialization...');
    const db = await initDatabase();
    
    if (db) {
      console.log('✅ Database initialization successful!');
      
      // Test basic database operations
      const tableCheck = db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
      `).all();
      
      console.log('\n📋 Created Tables:');
      tableCheck.forEach(table => {
        console.log(`  - ${table.name}`);
      });
      
      if (tableCheck.length === 0) {
        console.log('⚠️  No tables were created!');
      }
      
      // Close database
      db.close();
    } else {
      console.log('❌ Database initialization returned null');
    }
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testDatabaseInit();