# AI Assistant Configuration Files

This directory contains configuration files for various AI assistants that work with the ChromaSync codebase.

## Files

- **.roomodes** - Configuration for Roo AI assistant
- **.windsurfrules** - Configuration for Windsurf AI assistant  
- **windsurf.rules** - Additional Windsurf rules

## Important Notes

The following AI configuration files remain in the project root as they are actively used:
- `.ai-rules` - General AI assistant rules
- `.claude-memory` - Claude-specific memory and context

These configuration files help AI assistants understand the project structure, coding standards, and architectural decisions specific to ChromaSync.
