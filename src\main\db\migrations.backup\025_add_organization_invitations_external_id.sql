-- Migration: 025_add_organization_invitations_external_id.sql
-- Purpose: Add external_id column to organization_invitations table if it doesn't exist
-- Date: 2025-06-22
-- Author: ChromaSync Team

BEGIN TRANSACTION;

-- Check if external_id column exists
CREATE TEMPORARY TABLE column_check AS
SELECT COUNT(*) as column_exists 
FROM pragma_table_info('organization_invitations') 
WHERE name = 'external_id';

-- Display current state for logging
SELECT 
  CASE 
    WHEN (SELECT column_exists FROM column_check) > 0 
    THEN 'external_id column already exists in organization_invitations' 
    ELSE 'external_id column needs to be added to organization_invitations' 
  END as status;

-- Clean up
DROP TABLE column_check;

COMMIT;

-- The actual column addition needs to be performed directly
-- This migration handles the case where organization_invitations table
-- was created without the external_id column (early deployments)

-- Add the external_id column if it doesn't exist
-- This will fail silently if column already exists
ALTER TABLE organization_invitations ADD COLUMN external_id TEXT;

-- Create unique index (will be skipped if already exists)
CREATE UNIQUE INDEX IF NOT EXISTS idx_organization_invitations_external_id 
ON organization_invitations(external_id);

-- Populate external_id for any existing records that don't have one
UPDATE organization_invitations 
SET external_id = lower(
  hex(randomblob(4)) || '-' || 
  hex(randomblob(2)) || '-' || 
  '4' || substr(hex(randomblob(2)), 2) || '-' || 
  substr('89ab', abs(random()) % 4 + 1, 1) || substr(hex(randomblob(2)), 2) || '-' || 
  hex(randomblob(6))
)
WHERE external_id IS NULL;