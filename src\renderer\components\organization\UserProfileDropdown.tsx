/**
 * @file UserProfileDropdown.tsx
 * @description User profile dropdown component with logout functionality
 */

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useSyncAuth } from '../../store/sync.store';
import { User, ChevronDown, LogOut } from 'lucide-react';

export const UserProfileDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<{
    top: number;
    right: number;
  } | null>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { isAuthenticated, user, logout } = useSyncAuth();

  // Calculate dropdown position
  const calculateDropdownPosition = () => {
    if (!buttonRef.current) {
      return null;
    }

    const rect = buttonRef.current.getBoundingClientRect();
    return {
      top: rect.bottom + 8, // 8px gap below button
      right: window.innerWidth - rect.right, // Align right edge
    };
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setShowLogoutConfirm(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }

    return undefined;
  }, [isOpen]);

  // Update position on window resize
  useEffect(() => {
    const handleResize = () => {
      if (isOpen) {
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
      }
    };

    if (isOpen) {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }

    return undefined;
  }, [isOpen]);

  // Don't render if not authenticated
  if (!isAuthenticated || !user) {
    return null;
  }

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
      setIsOpen(false);
      setShowLogoutConfirm(false);
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const userName =
    user.user_metadata?.full_name || user.email?.split('@')[0] || 'User';
  const userEmail = user.email || '';
  const avatarUrl = user.user_metadata?.avatar_url;

  const handleToggleDropdown = () => {
    if (!isOpen) {
      const position = calculateDropdownPosition();
      setDropdownPosition(position);
    }
    setIsOpen(!isOpen);
  };

  return (
    <div className='relative'>
      <button
        ref={buttonRef}
        onClick={handleToggleDropdown}
        style={{
          backgroundColor: 'var(--color-ui-background-tertiary)',
          color: 'var(--color-ui-foreground-primary)',
          borderRadius: 'var(--radius-md)',
          padding: 'var(--spacing-1)',
          transition:
            'all var(--transition-duration-200) var(--transition-easing-apple)',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.backgroundColor =
            'var(--color-ui-background-hover)';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor =
            'var(--color-ui-background-tertiary)';
        }}
        className='flex items-center space-x-2'
        title={`Signed in as ${userName}`}
        aria-label={`User menu for ${userName}`}
      >
        {avatarUrl ? (
          <img
            src={avatarUrl}
            alt={userName}
            className='w-6 h-6 rounded-full'
            style={{ borderRadius: 'var(--radius-full)' }}
          />
        ) : (
          <User className='w-4 h-4' />
        )}
        <ChevronDown
          className={`w-3 h-3 transition-transform ${
            isOpen ? 'transform rotate-180' : ''
          }`}
        />
      </button>

      {isOpen &&
        dropdownPosition &&
        createPortal(
          <div
            ref={dropdownRef}
            className='fixed w-64 shadow-lg border'
            style={{
              top: `${dropdownPosition.top}px`,
              right: `${dropdownPosition.right}px`,
              backgroundColor: 'var(--color-ui-background-primary)',
              borderColor: 'var(--color-ui-border-light)',
              borderRadius: 'var(--radius-lg)',
              boxShadow: 'var(--shadow-lg)',
              zIndex: 'var(--z-tooltip)',
            }}
          >
            {!showLogoutConfirm ? (
              <>
                {/* User Info */}
                <div
                  className='p-4 border-b'
                  style={{ borderColor: 'var(--color-ui-border-light)' }}
                >
                  <div className='flex items-center space-x-3'>
                    {avatarUrl ? (
                      <img
                        src={avatarUrl}
                        alt={userName}
                        className='w-10 h-10'
                        style={{ borderRadius: 'var(--radius-full)' }}
                      />
                    ) : (
                      <div
                        className='w-10 h-10 flex items-center justify-center'
                        style={{
                          backgroundColor:
                            'var(--color-ui-background-secondary)',
                          borderRadius: 'var(--radius-full)',
                        }}
                      >
                        <User
                          className='w-5 h-5'
                          style={{
                            color: 'var(--color-ui-foreground-tertiary)',
                          }}
                        />
                      </div>
                    )}
                    <div className='flex-1 min-w-0'>
                      <div
                        className='font-medium truncate'
                        style={{
                          color: 'var(--color-ui-foreground-primary)',
                          fontSize: 'var(--font-size-sm)',
                          fontWeight: 'var(--font-weight-medium)',
                        }}
                      >
                        {userName}
                      </div>
                      <div
                        className='truncate'
                        style={{
                          color: 'var(--color-ui-foreground-secondary)',
                          fontSize: 'var(--font-size-xs)',
                        }}
                      >
                        {userEmail}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Logout Button */}
                <div style={{ padding: 'var(--spacing-2)' }}>
                  <button
                    onClick={() => setShowLogoutConfirm(true)}
                    className='w-full flex items-center space-x-2 text-left transition-colors'
                    style={{
                      padding: 'var(--spacing-3)',
                      borderRadius: 'var(--radius-md)',
                      color: 'var(--color-ui-foreground-secondary)',
                      fontSize: 'var(--font-size-sm)',
                    }}
                    onMouseEnter={e => {
                      e.currentTarget.style.backgroundColor =
                        'var(--color-ui-background-hover)';
                      e.currentTarget.style.color =
                        'var(--color-ui-foreground-primary)';
                    }}
                    onMouseLeave={e => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color =
                        'var(--color-ui-foreground-secondary)';
                    }}
                  >
                    <LogOut className='w-4 h-4' />
                    <span>Sign Out</span>
                  </button>
                </div>
              </>
            ) : (
              /* Logout Confirmation */
              <div style={{ padding: 'var(--spacing-4)' }}>
                <div className='mb-4'>
                  <h3
                    className='font-medium mb-2'
                    style={{
                      color: 'var(--color-ui-foreground-primary)',
                      fontSize: 'var(--font-size-base)',
                      fontWeight: 'var(--font-weight-medium)',
                    }}
                  >
                    Sign Out?
                  </h3>
                  <p
                    style={{
                      color: 'var(--color-ui-foreground-secondary)',
                      fontSize: 'var(--font-size-sm)',
                    }}
                  >
                    Are you sure you want to sign out of ChromaSync?
                  </p>
                </div>

                <div className='flex justify-end space-x-2'>
                  <button
                    onClick={() => setShowLogoutConfirm(false)}
                    disabled={isLoggingOut}
                    className='transition-colors'
                    style={{
                      padding: 'var(--spacing-2) var(--spacing-3)',
                      backgroundColor: 'var(--color-ui-background-tertiary)',
                      color: 'var(--color-ui-foreground-primary)',
                      borderRadius: 'var(--radius-md)',
                      fontSize: 'var(--font-size-sm)',
                    }}
                    onMouseEnter={e => {
                      if (!isLoggingOut) {
                        e.currentTarget.style.backgroundColor =
                          'var(--color-ui-background-secondary)';
                      }
                    }}
                    onMouseLeave={e => {
                      e.currentTarget.style.backgroundColor =
                        'var(--color-ui-background-tertiary)';
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className='flex items-center space-x-2 transition-colors'
                    style={{
                      padding: 'var(--spacing-2) var(--spacing-3)',
                      backgroundColor: 'var(--color-feedback-error)',
                      color: 'white',
                      borderRadius: 'var(--radius-md)',
                      fontSize: 'var(--font-size-sm)',
                      opacity: isLoggingOut ? '0.7' : '1',
                      cursor: isLoggingOut ? 'not-allowed' : 'pointer',
                    }}
                    onMouseEnter={e => {
                      if (!isLoggingOut) {
                        e.currentTarget.style.backgroundColor =
                          'var(--color-feedback-error)';
                        e.currentTarget.style.opacity = '0.9';
                      }
                    }}
                    onMouseLeave={e => {
                      e.currentTarget.style.opacity = isLoggingOut
                        ? '0.7'
                        : '1';
                    }}
                  >
                    {isLoggingOut && (
                      <div className='animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full' />
                    )}
                    <span>{isLoggingOut ? 'Signing Out...' : 'Sign Out'}</span>
                  </button>
                </div>
              </div>
            )}
          </div>,
          document.body
        )}
    </div>
  );
};
