/**
 * @file LibraryColorCustomizer.tsx
 * @description Component to customize a library color before adding it to a collection
 */

import React from 'react';
import { ColorEntry } from '../../../shared/types/color.types';
import { X } from 'lucide-react';
import ColorForm from '../ColorForm';

interface LibraryColorCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  color: ColorEntry;
  collectionName?: string;
}

const LibraryColorCustomizer: React.FC<LibraryColorCustomizerProps> = ({
  isOpen,
  onClose,
  onSuccess,
  color,
  collectionName,
}) => {
  if (!isOpen) {
    return null;
  }

  return (
    <div className='fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-50'>
      <div className='bg-ui-background-primary rounded-lg shadow-xl max-w-xl w-full flex flex-col max-h-[80vh]'>
        <div className='px-6 py-4 border-b border-ui-border-light flex items-center justify-between'>
          <h3 className='text-lg font-medium text-ui-foreground-primary'>
            Add Color from Library to {collectionName || 'Collection'}
          </h3>
          <button
            onClick={onClose}
            className='text-ui-muted hover:text-ui-foreground-primary transition-colors'
            aria-label='Close'
          >
            <X size={20} />
          </button>
        </div>
        <div className='p-6 overflow-auto'>
          <ColorForm
            isModal={true}
            color={{
              ...color,
              // Set the product to the collection name by default
              product: collectionName || color.product,
              // Create a copy with a new ID so it doesn't modify the original library color
              id: '',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }}
            onSuccess={onSuccess}
            onCancel={onClose}
          />
        </div>
      </div>
    </div>
  );
};

export default LibraryColorCustomizer;
