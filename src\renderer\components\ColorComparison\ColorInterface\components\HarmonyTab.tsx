/**
 * Harmony Tab Component
 * Displays color harmony options and results
 */

import { memo } from 'react';
import { Palette } from 'lucide-react';
import HarmonySelector from '../../HarmonySelector';
import type { HarmonyTabProps } from '../types';

export const HarmonyTab = memo<HarmonyTabProps>(({ 
  selectedColor
}) => {
  if (!selectedColor) {
    return (
      <div className="p-4 text-center text-ui-text-muted">
        <Palette className="mx-auto mb-2 h-8 w-8 opacity-50" />
        <p>Select a color to generate harmonies</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <HarmonySelector />
    </div>
  );
});

HarmonyTab.displayName = 'HarmonyTab';