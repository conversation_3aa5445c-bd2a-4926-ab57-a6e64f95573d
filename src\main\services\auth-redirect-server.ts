/**
 * Local redirect server for OAuth callbacks
 * Listens on localhost:3000 and redirects to the Electron app
 */

import { createServer } from 'http';
import { parse } from 'url';

export class AuthRedirectServer {
  private server: any = null;
  private port = 3000;
  private onCallback: ((url: string) => void) | null = null;

  start(onCallback?: (url: string) => void): Promise<void> {
    this.onCallback = onCallback || null;

    return new Promise((resolve, reject) => {
      this.server = createServer((req, res) => {
        const { pathname, query: _query } = parse(req.url || '', true);

        if (pathname === '/auth/callback') {
          // Extract the hash fragment from the referer or handle the callback
          const html = `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="UTF-8">
              <title>ChromaSync - Authentication Success</title>
              <style>
                body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                  margin: 0;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  overflow: hidden;
                }
                .container {
                  text-align: center;
                  padding: 3rem;
                  background: rgba(255, 255, 255, 0.95);
                  border-radius: 16px;
                  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
                  backdrop-filter: blur(10px);
                  animation: slideIn 0.4s ease-out;
                  max-width: 400px;
                }
                @keyframes slideIn {
                  from {
                    opacity: 0;
                    transform: translateY(20px);
                  }
                  to {
                    opacity: 1;
                    transform: translateY(0);
                  }
                }
                .success-icon {
                  width: 72px;
                  height: 72px;
                  margin: 0 auto 1.5rem;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  animation: scaleIn 0.5s ease-out 0.2s both;
                }
                @keyframes scaleIn {
                  from {
                    transform: scale(0);
                  }
                  to {
                    transform: scale(1);
                  }
                }
                .success-icon svg {
                  width: 36px;
                  height: 36px;
                  stroke: white;
                  stroke-width: 3;
                  fill: none;
                  stroke-linecap: round;
                  stroke-linejoin: round;
                }
                h1 { 
                  color: #333;
                  font-size: 1.75rem;
                  margin-bottom: 0.5rem;
                  font-weight: 600;
                }
                p { 
                  color: #666;
                  font-size: 1rem;
                  line-height: 1.5;
                  margin: 0.5rem 0;
                }
                .loader {
                  margin: 2rem auto 0;
                  width: 40px;
                  height: 4px;
                  background: rgba(102, 126, 234, 0.2);
                  border-radius: 2px;
                  overflow: hidden;
                }
                .loader-bar {
                  width: 40%;
                  height: 100%;
                  background: linear-gradient(90deg, #667eea, #764ba2);
                  border-radius: 2px;
                  animation: loading 1.5s ease-in-out infinite;
                }
                @keyframes loading {
                  0% { transform: translateX(-100%); }
                  100% { transform: translateX(250%); }
                }
                .logo {
                  position: absolute;
                  bottom: 2rem;
                  left: 50%;
                  transform: translateX(-50%);
                  font-size: 0.875rem;
                  color: rgba(255, 255, 255, 0.8);
                  font-weight: 500;
                }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="success-icon">
                  <svg viewBox="0 0 24 24">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </div>
                <h1>Authentication Successful!</h1>
                <p>You've been successfully signed in to ChromaSync.</p>
                <p>You can now close this window and return to the app.</p>
                <div class="loader">
                  <div class="loader-bar"></div>
                </div>
              </div>
              <div class="logo">ChromaSync</div>
              <script>
                // Handle both PKCE (query params) and implicit flow (hash)
                const search = window.location.search; // ?code=...&state=...
                const hash = window.location.hash;     // #access_token=... (fallback)
                
                if (search || hash) {
                  // Send the OAuth data back to the server with timeout and retry
                  let retryCount = 0;
                  const maxRetries = 3;
                  const retryDelay = 1000;
                  
                  const sendData = async () => {
                    try {
                      const controller = new AbortController();
                      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
                      
                      const response = await fetch('/auth/complete', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ 
                          search: search, 
                          hash: hash,
                          url: window.location.href
                        }),
                        signal: controller.signal
                      });
                      
                      clearTimeout(timeoutId);
                      
                      if (!response.ok) {
                        throw new Error('Server error: ' + response.status);
                      }
                      
                      console.log('Auth data sent successfully');
                      // Close window after a delay
                      setTimeout(() => {
                        window.close();
                      }, 2000);
                      
                    } catch (err) {
                      console.error('Failed to send auth data (attempt ' + (retryCount + 1) + '):', err);
                      
                      if (retryCount < maxRetries - 1) {
                        retryCount++;
                        setTimeout(sendData, retryDelay * retryCount);
                      } else {
                        console.error('All retry attempts failed, still closing window');
                        // Still try to close window even if data send failed
                        setTimeout(() => {
                          window.close();
                        }, 1000);
                      }
                    }
                  };
                  
                  sendData();
                } else {
                  console.log('No OAuth data found in URL');
                  // Close window even if no data
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                }
              </script>
            </body>
            </html>
          `;

          res.writeHead(200, { 'Content-Type': 'text/html' });
          res.end(html);
        } else if (pathname === '/auth/complete' && req.method === 'POST') {
          // Handle the completion callback
          let body = '';
          req.on('data', chunk => (body += chunk));
          req.on('end', () => {
            try {
              const { search, hash, url } = JSON.parse(body);
              console.log('[AuthRedirectServer] Received OAuth data:', {
                search,
                hash,
                url,
              });

              if (this.onCallback) {
                // PKCE flow uses query parameters, implicit flow uses hash
                const oauthData = search || hash;
                if (oauthData) {
                  const callbackUrl = `chromasync://auth/callback${oauthData}`;
                  console.log(
                    '[AuthRedirectServer] Forwarding to app:',
                    callbackUrl
                  );
                  this.onCallback(callbackUrl);
                } else {
                  console.error('[AuthRedirectServer] No OAuth data found');
                }
              }
              res.writeHead(200, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ success: true }));
            } catch (error) {
              res.writeHead(400);
              res.end('Bad request');
            }
          });
        } else {
          res.writeHead(404);
          res.end('Not found');
        }
      });

      this.server.listen(this.port, () => {
        console.log(
          `[AuthRedirectServer] Listening on http://localhost:${this.port}`
        );
        resolve();
      });

      this.server.on('error', (err: any) => {
        if (err.code === 'EADDRINUSE') {
          console.log(`[AuthRedirectServer] Port ${this.port} already in use`);
          resolve(); // Don't fail if port is already in use
        } else {
          reject(err);
        }
      });
    });
  }

  stop(): void {
    if (this.server) {
      this.server.close();
      this.server = null;
      console.log('[AuthRedirectServer] Stopped');
    }
  }
}
