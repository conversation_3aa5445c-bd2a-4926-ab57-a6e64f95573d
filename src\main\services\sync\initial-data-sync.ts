/**
 * Ensure initial data sync after authentication
 * This fixes the issue where product-color relationships aren't synced on fresh DB
 */

import { getDatabase } from '../../db/database';
import { ColorService } from '../../db/services/color.service';
import { ProductService } from '../../db/services/product.service';
import { OrganizationService } from '../../db/services/organization.service';

export interface InitialSyncResult {
  success: boolean;
  colors: number;
  products: number;
  productColors: number;
  errors: string[];
}

/**
 * Perform complete initial sync for fresh database
 * This ensures all data including product-color relationships are synced
 */
export async function performInitialDataSync(
  userId: string, 
  organizationId: string
): Promise<InitialSyncResult> {
  console.log('[InitialSync] ========== FUNCTION CALLED ==========');
  console.log('[InitialSync] 🚀 Starting complete initial data sync...');
  console.log(`[InitialSync] User: ${userId}, Organization: ${organizationId}`);
  
  const result: InitialSyncResult = {
    success: false,
    colors: 0,
    products: 0,
    productColors: 0,
    errors: []
  };

  try {
    const db = getDatabase();
    if (!db) {
      throw new Error('Database not available');
    }

    // Step 0: CRITICAL - Sync organizations first (required for all other sync operations)
    console.log('[InitialSync] Step 0: Syncing organizations...');
    try {
      const organizationService = new OrganizationService(db);
      await organizationService.syncOrganizationsFromSupabase(userId);
      console.log(`[InitialSync] ✅ Organizations synced for user ${userId}`);
    } catch (error) {
      const errorMsg = `Failed to sync organizations: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`[InitialSync] ❌ ${errorMsg}`);
      result.errors.push(errorMsg);
      // Don't return here - the sync guards in ColorService/ProductService will handle this
    }

    // Step 1: Sync colors from Supabase with proper UUID generation
    console.log('[InitialSync] Step 1: Syncing colors with UUID mapping...');
    console.log('[InitialSync] DEBUG - userId:', userId);
    console.log('[InitialSync] DEBUG - organizationId:', organizationId);
    try {
      const colorService = new ColorService(db);
      console.log('[InitialSync] Created ColorService, calling syncColorsFromSupabase...');
      await colorService.syncColorsFromSupabase(userId, organizationId);
      console.log('[InitialSync] ✅ syncColorsFromSupabase completed with proper UUID generation');
      
      // Count synced colors
      const colorCount = db.prepare(`
        SELECT COUNT(*) as count 
        FROM colors c
        WHERE c.organization_id = ?
      `).get(organizationId) as { count: number };
      
      result.colors = colorCount.count;
      console.log(`[InitialSync] ✅ Synced ${result.colors} colors`);
    } catch (error) {
      const errorMsg = `Failed to sync colors: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`[InitialSync] ❌ ${errorMsg}`);
      result.errors.push(errorMsg);
    }

    // Step 2: Sync products from Supabase
    console.log('[InitialSync] Step 2: Syncing products...');
    try {
      const productService = new ProductService(db);
      await productService.syncProductsFromSupabase(userId, organizationId);
      
      // Count synced products
      const productCount = db.prepare(`
        SELECT COUNT(*) as count 
        FROM products p
        WHERE p.organization_id = ? AND p.deleted_at IS NULL
      `).get(organizationId) as { count: number };
      
      result.products = productCount.count;
      console.log(`[InitialSync] ✅ Synced ${result.products} products`);
    } catch (error) {
      const errorMsg = `Failed to sync products: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`[InitialSync] ❌ ${errorMsg}`);
      result.errors.push(errorMsg);
    }

    // Step 3: Sync product-color relationships
    // This is the critical step that was missing!
    console.log('[InitialSync] Step 3: Syncing product-color relationships...');
    try {
      const productService = new ProductService(db);
      const syncResult = await productService.syncProductColorsFromSupabase(organizationId);
      
      if (syncResult.success) {
        result.productColors = syncResult.syncedCount;
        console.log(`[InitialSync] ✅ Synced ${result.productColors} product-color relationships`);
        
        if (syncResult.errors.length > 0) {
          console.warn(`[InitialSync] ⚠️ Non-critical errors during sync:`, syncResult.errors);
          result.errors.push(...syncResult.errors);
        }
      } else {
        throw new Error(`Product-color sync failed: ${syncResult.errors.join(', ')}`);
      }
    } catch (error) {
      const errorMsg = `Failed to sync product-color relationships: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`[InitialSync] ❌ ${errorMsg}`);
      result.errors.push(errorMsg);
    }

    // Step 4: Verify the sync worked
    console.log('[InitialSync] Step 4: Verifying sync...');
    const verification = db.prepare(`
      SELECT 
        (SELECT COUNT(*) FROM colors WHERE organization_id = ?) as colors,
        (SELECT COUNT(*) FROM products WHERE organization_id = ?) as products,
        (SELECT COUNT(*) FROM product_colors pc 
         JOIN products p ON pc.product_id = p.id 
         WHERE p.organization_id = ?) as product_colors
    `).get(organizationId, organizationId, organizationId) as any;
    
    console.log('[InitialSync] 📊 Verification results:', verification);

    // Mark as successful if we have data
    result.success = result.colors > 0 || result.products > 0 || result.productColors > 0;
    
    if (result.success) {
      console.log('[InitialSync] ✅ Initial data sync completed successfully!');
      console.log(`[InitialSync] 📊 Summary: ${result.colors} colors, ${result.products} products, ${result.productColors} relationships`);
    } else {
      console.warn('[InitialSync] ⚠️ No data was synced - this might be a new organization');
    }

  } catch (error) {
    console.error('[InitialSync] ❌ Fatal error during initial sync:', error);
    console.error('[InitialSync] Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
    result.errors.push(error instanceof Error ? error.message : String(error));
  }

  return result;
}