/**
 * Color Validation Utilities
 * Consolidated validation functions for all color formats
 */

import { CMYK, RGB } from './types';
import { InvalidHexCodeError, InvalidCMYKError } from '../../types/errors';

/**
 * HEX Color Validation
 */

// Validates if a string is a proper HEX color code
// Supports both 3 and 6 digit formats (e.g., #FFF or #FFFFFF)
export function isValidHex(hex: string): boolean {
  if (!hex) {return false;}
  // Support both 3 and 6 digit hex codes
  const hexRegex = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/;
  return hexRegex.test(hex);
}

// Stricter validation for 6-digit hex only (database format)
export function isValidHex6(hex: string): boolean {
  if (!hex) {return false;}
  const hexRegex = /^#[0-9A-Fa-f]{6}$/;
  return hexRegex.test(hex);
}

// Validates a HEX color code and throws an error if invalid
export function validateHex(hex: string): void {
  if (!hex) {
    throw new InvalidHexCodeError(hex, 'HEX code cannot be empty');
  }
  if (!hex.startsWith('#')) {
    throw new InvalidHexCodeError(hex, 'HEX code must start with #');
  }
  
  const hexValue = hex.substring(1);
  
  if (hexValue.length !== 3 && hexValue.length !== 6) {
    throw new InvalidHexCodeError(
      hex,
      'HEX code must be 4 or 7 characters including # (e.g., #FFF or #FFFFFF)'
    );
  }
  
  if (!/^[0-9A-Fa-f]+$/.test(hexValue)) {
    throw new InvalidHexCodeError(
      hex,
      'HEX code must contain only valid hexadecimal characters (0-9, A-F)'
    );
  }
}

/**
 * CMYK Color Validation
 */

// Validates if a string is a proper CMYK color value
// Accepts multiple formats including:
// - "C:0 M:0 Y:0 K:0"
// - "0,0,0,0"
// - "C0, M0, Y0, K0"
// - "0 0 0 0"
// - Empty string (will be auto-derived)
export function isValidCMYK(cmyk: string): boolean {
  if (!cmyk || cmyk.trim() === '') {
    return true; // Empty is valid - will be auto-derived from HEX
  }

  try {
    const parsed = parseCMYKString(cmyk);
    const isValid = (
      parsed.c >= 0 && parsed.c <= 100 &&
      parsed.m >= 0 && parsed.m <= 100 &&
      parsed.y >= 0 && parsed.y <= 100 &&
      parsed.k >= 0 && parsed.k <= 100
    );

    return isValid;
  } catch (_error) {
    return false;
  }
}

// Validates a CMYK color value and throws an error if invalid
export function validateCMYK(cmyk: string): void {
  if (!cmyk) {
    throw new InvalidCMYKError(cmyk, 'CMYK value cannot be empty');
  }
  
  if (!isValidCMYK(cmyk)) {
    throw new InvalidCMYKError(
      cmyk,
      'CMYK must be in format "C:X M:Y Y:Z K:W" (e.g., "C:0 M:50 Y:100 K:0")'
    );
  }
}

/**
 * RGB Color Validation
 */

export function isValidRGB(rgb: RGB): boolean {
  return (
    rgb.r >= 0 && rgb.r <= 255 &&
    rgb.g >= 0 && rgb.g <= 255 &&
    rgb.b >= 0 && rgb.b <= 255
  );
}

/**
 * Pantone Color Validation
 */

// Validates a Pantone color code
// @param pantone - The Pantone code to validate (e.g., "PMS 186 C", "186 C")
export function isValidPantone(pantone: string): boolean {
  if (!pantone) {return false;}
  // Support both "PMS 186 C" and "186 C" formats
  return /^(PMS\s+)?\d{1,4}\s*[CU]?$/i.test(pantone.trim());
}

/**
 * RAL Color Validation
 */

// Validates a RAL color code
// @param ral - The RAL code to validate (e.g., "RAL 9010", "9010")
export function isValidRAL(_code: string): boolean {
  if (!_code) {return false;}
  // Support both "RAL 9010" and "9010" formats
  return /^(RAL\s+)?\d{4}$/i.test(_code.trim());
}

/**
 * Helper function to parse CMYK string
 */
function parseCMYKString(cmyk: string): CMYK {
  // This is a helper function - the actual implementation will be in formatting.ts
  // For now, we'll use a simple regex approach
  
  // Try format "C:0 M:0 Y:0 K:0"
  let match = cmyk.match(/[Cc]:?\s*(\d+(?:\.\d+)?)\s*[Mm]:?\s*(\d+(?:\.\d+)?)\s*[Yy]:?\s*(\d+(?:\.\d+)?)\s*[Kk]:?\s*(\d+(?:\.\d+)?)/);
  if (match && match[1] && match[2] && match[3] && match[4]) {
    return {
      c: parseFloat(match[1]),
      m: parseFloat(match[2]),
      y: parseFloat(match[3]),
      k: parseFloat(match[4])
    };
  }
  
  // Try format "0,0,0,0" or "0 0 0 0"
  match = cmyk.match(/(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)/);
  if (match && match[1] && match[2] && match[3] && match[4]) {
    return {
      c: parseFloat(match[1]),
      m: parseFloat(match[2]),
      y: parseFloat(match[3]),
      k: parseFloat(match[4])
    };
  }
  
  // Try single value - treat as black (K) component with C/M/Y as 0
  match = cmyk.match(/^(\d+(?:\.\d+)?)$/);
  if (match && match[1]) {
    const value = parseFloat(match[1]);
    return {
      c: 0,
      m: 0,
      y: 0,
      k: value
    };
  }
  
  throw new Error(`Cannot parse CMYK value: ${cmyk}`);
}

/**
 * Color Consistency Validation
 */

// Verifies if CMYK and HEX values are consistent with each other
// Uses a tolerance to account for conversion rounding errors
export function areColorValuesConsistent(
  cmyk: string,
  hex: string,
  _tolerance: number = 10
): boolean {
  if (!isValidCMYK(cmyk) || !isValidHex6(hex)) {
    return false;
  }
  
  // This function will need hexToCMYK from conversion module
  // For now, we'll return true - this will be updated after conversion module is created
  return true;
}

/**
 * General Color Validation
 */

// Validates if a color code is valid for any supported format
export function isValidColorCode(code: string, format?: 'hex' | 'cmyk' | 'pantone' | 'ral'): boolean {
  if (!code) {return false;}
  
  if (format) {
    switch (format) {
      case 'hex':
        return isValidHex(code);
      case 'cmyk':
        return isValidCMYK(code);
      case 'pantone':
        return isValidPantone(code);
      case 'ral':
        return isValidRAL(code);
      default:
        return false;
    }
  }
  
  // Try to detect format automatically
  return (
    isValidHex(code) ||
    isValidCMYK(code) ||
    isValidPantone(code) ||
    isValidRAL(code)
  );
}