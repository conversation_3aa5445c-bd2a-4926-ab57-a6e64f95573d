/**
 * @file index.ts
 * @description Main process entry point for Electron application
 */

// CRITICAL: Import logger FIRST to set up IPC handler guard before ANY other module
// This MUST be the first import to prevent duplicate __ELECTRON_LOG__ handler registration
import { configureLogging } from './utils/logger';

import dotenv from 'dotenv';

// Load environment variables from .env file
const dotenvResult = dotenv.config();
console.log('[DEBUG] process.cwd():', process.cwd());
console.log('[DEBUG] dotenv.config() result:', dotenvResult);

// ENHANCED DEBUG: Check environment variables immediately after dotenv
console.log('[DEBUG] Environment after dotenv:');
console.log(
  '[DEBUG]   SUPABASE_URL:',
  process.env.SUPABASE_URL ? 'PRESENT' : 'MISSING'
);
console.log(
  '[DEBUG]   SUPABASE_ANON_KEY:',
  process.env.SUPABASE_ANON_KEY ? 'PRESENT' : 'MISSING'
);
console.log(
  '[DEBUG]   ZOHO_CLIENT_ID:',
  process.env.ZOHO_CLIENT_ID ? 'PRESENT' : 'MISSING'
);
if (process.env.SUPABASE_URL) {
  console.log(
    '[DEBUG]   SUPABASE_URL value (first 50 chars):',
    process.env.SUPABASE_URL.substring(0, 50)
  );
}
if (process.env.SUPABASE_ANON_KEY) {
  console.log(
    '[DEBUG]   SUPABASE_ANON_KEY length:',
    process.env.SUPABASE_ANON_KEY.length
  );
}

// CRITICAL FIX: Clear secure config cache after dotenv loading
// This ensures that any previously cached empty config is invalidated
// and the next access will use the newly loaded environment variables
import { secureConfig } from './utils/secure-config-loader';
console.log('[DEBUG] Clearing secure config cache after dotenv loading...');
secureConfig.clearCache();
console.log(
  '[DEBUG] Secure config cache cleared, next access will reload from environment'
);

// Handle EPIPE errors from broken console pipes (common in development)
process.stdout.on('error', err => {
  if (err.code === 'EPIPE') {
    // Ignore EPIPE errors - they occur when console output is redirected/closed
    return;
  }
  console.error('stdout error:', err);
});

process.stderr.on('error', err => {
  if (err.code === 'EPIPE') {
    // Ignore EPIPE errors - they occur when console output is redirected/closed
    return;
  }
  console.error('stderr error:', err);
});

// Handle uncaught exceptions gracefully
process.on('uncaughtException', err => {
  if ((err as any).code === 'EPIPE') {
    // Ignore EPIPE errors - they occur when console output is redirected/closed
    return;
  }
  console.error('Uncaught Exception:', err);
  // Don't exit process for EPIPE, but log other errors
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit process for unhandled rejections in development
});

import { app, BrowserWindow, nativeTheme } from 'electron';

// Initialize CSP configuration early for security
import { cspConfiguration } from './security/csp-configuration';
import path from 'path';
import fs from 'fs';
// setupAutoUpdater will be imported dynamically to avoid conflicts
import { initializeIpcHandlers } from './ipc/index';
import { initializeAllServices } from './services/service-initializer';

// Module execution guard - prevent multiple main module executions
if ((global as any).__CHROMASYNC_MAIN_LOADED__) {
  console.log('[App] Main module already loaded, skipping duplicate execution');
  process.exit(0);
}
(global as any).__CHROMASYNC_MAIN_LOADED__ = true;

// Initialization guard
let isInitialized = false;

// Ensure better-sqlite3 is properly configured for all platforms
// Create necessary directories for native modules
const setupNativeModulePaths = () => {
  try {
    // Create build directory in the app root
    const buildDir = path.join(process.cwd(), 'build');
    if (!fs.existsSync(buildDir)) {
      fs.mkdirSync(buildDir, { recursive: true });
      console.log(`[DB] Created build directory at: ${buildDir}`);
    }

    // Log the app paths for debugging
    console.log(`[DB] App paths:`);
    console.log(`- process.cwd(): ${process.cwd()}`);
    console.log(`- app.getAppPath(): ${app.getAppPath()}`);
    console.log(`- app.getPath('userData'): ${app.getPath('userData')}`);

    // Log the better-sqlite3 module location
    const possiblePaths = [
      path.join(
        process.cwd(),
        'node_modules',
        'better-sqlite3',
        'build',
        'Release',
        'better_sqlite3.node'
      ),
      path.join(
        app.getAppPath(),
        'node_modules',
        'better-sqlite3',
        'build',
        'Release',
        'better_sqlite3.node'
      ),
    ];

    console.log('[DB] Checking better-sqlite3.node locations:');
    possiblePaths.forEach(p => {
      const exists = fs.existsSync(p);
      console.log(`- ${p} (exists: ${exists})`);
    });
  } catch (error) {
    console.error('[DB] Error setting up native module paths:', error);
  }
};

// Run the setup function
setupNativeModulePaths();

// Set app name
app.name = 'ChromaSync';

// Configure centralized logging (already imported at the top of the file)
configureLogging();

// Set default dark theme
nativeTheme.themeSource = 'dark';

// CRITICAL: Register protocol handler BEFORE checking single instance
// This must happen first so the OS knows this instance handles chromasync:// URLs
if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.setAsDefaultProtocolClient('chromasync', process.execPath, [
      path.resolve(process.argv[1] || __filename),
    ]);
  }
} else {
  app.setAsDefaultProtocolClient('chromasync');
}
console.log('[Protocol] Registered chromasync:// protocol handler');

// Windows/Linux protocol handling - Request single instance lock
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  // We're the second instance - check if we have an OAuth URL to pass
  const oauthUrl = process.argv.find(arg => arg.startsWith('chromasync://'));
  if (oauthUrl) {
    console.log(
      '[App] Second instance with OAuth URL, will pass to primary instance:',
      oauthUrl
    );
    // The primary instance will receive this via the 'second-instance' event
  }
  app.quit();
} else {
  // Handle protocol for OAuth on macOS
  app.on('open-url', async (event, url) => {
    event.preventDefault();
    console.log('[Protocol] Received URL:', url);

    // Handle invitation URLs
    if (url.startsWith('chromasync://invite/')) {
      const token = url.replace('chromasync://invite/', '');
      console.log('[Protocol] Handling invitation with token:', token);

      // Send to renderer when main window is ready
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('handle-invitation', token);
      } else {
        // Store for later if window not ready
        app.once('browser-window-created', () => {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('handle-invitation', token);
          }
        });
      }
    } else if (url.startsWith('chromasync://auth')) {
      // Handle OAuth callback with new OAuth service
      console.log('[Protocol] Handling OAuth callback from URL:', url);
      console.log('[Protocol] Full URL details:', {
        url,
        hasCallback: url.includes('callback'),
        hasHash: url.includes('#'),
        hasQuery: url.includes('?'),
      });

      try {
        const { getOAuthService } = await import('./services/service-locator');
        const oauthService = getOAuthService();
        console.log(
          '[Protocol] OAuth service imported, calling handleCallback'
        );
        // Handle callback through the auth manager
        (oauthService as any).authManager.handleCallback(url);
      } catch (error) {
        console.error('[Protocol] Failed to handle OAuth callback:', error);
      }
    } else {
      console.log('[Protocol] Unknown URL scheme:', url);
    }
  });

  app.on('second-instance', (event, commandLine, _workingDirectory) => {
    console.log(
      '[App] Second instance detected with command line:',
      commandLine
    );

    // Handle OAuth callback on Windows/Linux
    const url = commandLine.find(arg => arg.startsWith('chromasync://'));
    if (url) {
      console.log('[App] Found OAuth URL in second instance:', url);
      app.emit('open-url', event, url);
    }

    // If we have a window, restore it
    if (mainWindow) {
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }
      mainWindow.focus();
    }
  });
}

// Import window management
import { createWindow } from './windows/mainWindow';

// Keep a global reference of the window object to avoid garbage collection
export let mainWindow: BrowserWindow | null = null;

// Window control and zoom functionality extracted to ./windows/mainWindow.ts

// createWindow function extracted to ./windows/mainWindow.ts

/**
 * Perform comprehensive application shutdown
 */
async function performApplicationShutdown(): Promise<void> {
  console.log('[App] 🛑 Starting application shutdown...');
  
  try {
    // Import shutdown coordinator
    const { syncShutdownCoordinator } = await import('./services/sync/sync-shutdown-coordinator');
    
    // Perform coordinated sync shutdown
    console.log('[App] 🔄 Shutting down sync system...');
    const shutdownResult = await syncShutdownCoordinator.shutdown({
      gracefulTimeout: 10000, // 10 seconds
      forceShutdown: false
    });
    
    if (shutdownResult.success) {
      console.log(`[App] ✅ Sync shutdown completed: ${shutdownResult.componentsShutdown.length} components`);
    } else {
      console.warn(`[App] ⚠️ Sync shutdown had errors:`, shutdownResult.errors);
    }
    
    // Cleanup service container
    try {
      const { ServiceLocator } = await import('./services/service-locator');
      await ServiceLocator.cleanup();
      console.log('[App] ✅ Service container cleanup completed');
    } catch (error) {
      console.warn('[App] ⚠️ Service container cleanup failed:', error);
    }
    
    console.log('[App] ✅ Application shutdown completed');
  } catch (error) {
    console.error('[App] ❌ Application shutdown failed:', error);
  }
}

// Initialize the database and enable custom IPC handlers
async function initializeAppServices(): Promise<void> {
  if (isInitialized) {
    console.warn('[Startup] Initialization already completed. Skipping.');
    return;
  }
  console.log('[App] Initializing application services...');

  try {
    // Initialize all services using the centralized service initializer
    const serviceResult = await initializeAllServices();

    if (!serviceResult.success) {
      console.error(
        '[App] Service initialization failed:',
        serviceResult.errors
      );
      // Continue with limited functionality
    }

    // Initialize all IPC handlers using the centralized registry
    console.log('[App] 🚀 Proceeding with IPC handler registration...');
    await initializeIpcHandlers();

    console.log(
      `[App] ✅ Application services and IPC handlers initialized in ${serviceResult.initTime}ms`
    );
    isInitialized = true;
  } catch (error) {
    console.error('[App] Error initializing application services:', error);
    // Mark as initialized even on error to prevent retry loops
    isInitialized = true;
    throw error;
  }
}

/**
 * Initialize unified sync functionality
 */
async function initializeAutoSync(
  mainWindow: BrowserWindow | null
): Promise<void> {
  console.log('[UnifiedSync] Initializing unified sync...');

  try {
    // Set up unified sync event forwarding
    const { setupUnifiedSyncEventForwarding } = await import(
      './ipc/sync-handlers'
    );

    if (mainWindow) {
      setupUnifiedSyncEventForwarding(mainWindow);
      console.log('[UnifiedSync] Event forwarding configured for main window');
    } else {
      console.log(
        '[UnifiedSync] No main window available for event forwarding'
      );
    }

    console.log('[UnifiedSync] Unified sync initialization completed');
  } catch (error) {
    console.error('[UnifiedSync] Failed to initialize unified sync:', error);
    // Continue without auto-sync functionality
  }
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  console.log('[App] Electron ready, creating window...');

  try {
    // Initialize security configurations first
    console.log('[App] 🛡️  Initializing security configurations...');
    cspConfiguration.setupCSPHeaders();
    cspConfiguration.logCSPStatus();
    console.log('[App] ✅ Security configurations initialized');

    // Only initialize if not already initialized
    if (!isInitialized) {
      console.log('[App] 🔍 About to start service initialization...');
      await initializeAppServices();
      console.log('[App] 🔍 Service initialization completed');
    } else {
      console.log(
        '[App] Services already initialized, skipping initialization'
      );
    }

    // Create the main window
    mainWindow = await createWindow();

    // Initialize auto-sync after window is created
    if (mainWindow) {
      await initializeAutoSync(mainWindow);
    }

    // Initialize performance aggregation tables in background after UI loads
    setTimeout(async () => {
      try {
        console.log(
          '[App] 🔄 Starting background aggregation table initialization...'
        );
        const { getDatabase } = require('./db/database');
        const {
          createPerformanceInitializer,
        } = require('./db/performance/performance-initializer');

        const db = getDatabase();
        if (db) {
          const performanceInitializer = createPerformanceInitializer(db);
          const result = await performanceInitializer.initializeOptimizations({
            skipIfOptimized: true,
            refreshAggregationTables: true,
          });

          if (result.success) {
            console.log(
              '[App] ✅ Background aggregation tables initialized successfully'
            );
          } else {
            console.warn(
              '[App] ⚠️ Background aggregation initialization had issues:',
              result.errors
            );
          }
        }
      } catch (error) {
        console.error(
          '[App] ❌ Background aggregation initialization failed:',
          error
        );
        // Don't crash the app for background optimization failures
      }
    }, 3000); // Wait 3 seconds after UI loads

    console.log('[App] ✅ Application startup completed successfully');
  } catch (error) {
    console.error('[App] ❌ Failed to initialize application:', error);
    // Show error dialog and quit
    const { dialog } = await import('electron');
    await dialog.showErrorBox(
      'Startup Error',
      `Failed to start ChromaSync: ${error instanceof Error ? error.message : String(error)}`
    );
    app.quit();
  }
});

// Quit when all windows are closed
app.on('window-all-closed', async () => {
  // On macOS it is common for applications to stay open until explicitly quit
  if (process.platform !== 'darwin') {
    await performApplicationShutdown();
    app.quit();
  }
});

// On macOS, re-create window when dock icon is clicked
app.on('activate', async () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    mainWindow = await createWindow();
  } else if (mainWindow) {
    // If window exists but is minimized/hidden, show it
    mainWindow.show();
  }
});

// Handle second instance
app.on('second-instance', (event, commandLine, _workingDirectory) => {
  // Someone tried to run a second instance, focus our window instead
  if (mainWindow) {
    if (mainWindow.isMinimized()) {mainWindow.restore();}
    mainWindow.focus();

    // Check for OAuth URL in command line arguments
    const oauthUrl = commandLine.find(arg => arg.startsWith('chromasync://'));
    if (oauthUrl) {
      console.log(
        '[App] Second instance with OAuth URL, processing:',
        oauthUrl
      );
      // Trigger the open-url handler
      app.emit('open-url', event, oauthUrl);
    }
  }
});
<<<<<<< HEAD
=======

// Handle application shutdown events
app.on('before-quit', async (event) => {
  console.log('[App] 🛑 Before quit event received');
  
  // Prevent immediate quit to allow cleanup
  event.preventDefault();
  
  try {
    await performApplicationShutdown();
    console.log('[App] ✅ Cleanup completed, quitting application');
    
    // Now allow the app to quit
    app.exit(0);
  } catch (error) {
    console.error('[App] ❌ Shutdown cleanup failed:', error);
    // Force quit even if cleanup fails
    app.exit(1);
  }
});

// Handle process termination signals
process.on('SIGTERM', async () => {
  console.log('[App] 🛑 SIGTERM received');
  await performApplicationShutdown();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('[App] 🛑 SIGINT received');
  await performApplicationShutdown();
  process.exit(0);
});

>>>>>>> main
