import { useEffect } from 'react';
import { useColorStore } from '../store/color.store';

interface ShortcutConfig {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  action: () => void;
  description: string;
}

export const useKeyboardShortcuts = () => {
  const { setViewMode, addColor, toggleDarkMode, setSelectedColor } =
    useColorStore();

  const shortcuts: ShortcutConfig[] = [
    {
      key: '1',
      ctrlKey: true,
      action: () => setViewMode('table'),
      description: 'Switch to Table View',
    },
    {
      key: '2',
      ctrlKey: true,
      action: () => setViewMode('swatches'),
      description: 'Switch to Swatches View',
    },
    {
      key: '3',
      ctrlKey: true,
      action: () => setViewMode('codes'),
      description: 'Switch to Codes View',
    },
    {
      key: 'n',
      ctrlKey: true,
      action: () => {
        // Create empty default color and trigger add color form
        const defaultColor = {
          product: '',
          name: '',
          code: '',
          hex: '#FFFFFF',
          cmyk: '0,0,0,0',
          notes: '',
        };
        addColor(defaultColor);
      },
      description: 'Add New Color',
    },
    {
      key: 'd',
      ctrlKey: true,
      action: toggleDarkMode,
      description: 'Toggle Dark/Light Mode',
    },
    {
      key: 'Escape',
      action: () => setSelectedColor(null),
      description: 'Clear Selection',
    },
  ];

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs or textareas
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement
      ) {
        return;
      }

      for (const shortcut of shortcuts) {
        if (
          event.key === shortcut.key &&
          (shortcut.ctrlKey === undefined ||
            event.ctrlKey === shortcut.ctrlKey) &&
          (shortcut.altKey === undefined || event.altKey === shortcut.altKey) &&
          (shortcut.shiftKey === undefined ||
            event.shiftKey === shortcut.shiftKey)
        ) {
          event.preventDefault();
          shortcut.action();
          break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcuts]);

  return { shortcuts };
};

export default useKeyboardShortcuts;
