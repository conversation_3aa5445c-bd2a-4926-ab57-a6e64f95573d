/**
 * Migration Runner
 * Executes database migrations in order
 */

import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';

export interface MigrationResult {
  success: boolean;
  migrationsRun: string[];
  errors: string[];
}

export class MigrationRunner {
  constructor(private db: Database.Database) {}

  /**
   * Run all pending migrations
   */
  async runMigrations(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migrationsRun: [],
      errors: []
    };

    try {
      // Create migrations tracking table if it doesn't exist
      this.createMigrationsTable();

      // Get list of migration files
      const migrationsDir = path.join(__dirname);
      const migrationFiles = fs.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.sql'))
        .sort(); // Ensure migrations run in order

      console.log(`[MigrationRunner] Found ${migrationFiles.length} migration files`);

      // Run each migration
      for (const file of migrationFiles) {
        const migrationName = path.basename(file, '.sql');
        
        // Check if migration has already been run
        if (this.hasRunMigration(migrationName)) {
          console.log(`[MigrationRunner] Skipping already run migration: ${migrationName}`);
          continue;
        }

        console.log(`[MigrationRunner] Running migration: ${migrationName}`);
        
        try {
          // Read migration file
          const migrationPath = path.join(migrationsDir, file);
          const migrationSQL = fs.readFileSync(migrationPath, 'utf-8');

          // Execute migration
          this.db.exec(migrationSQL);

          // Record successful migration
          this.recordMigration(migrationName);
          result.migrationsRun.push(migrationName);
          
          console.log(`[MigrationRunner] ✅ Successfully ran migration: ${migrationName}`);
        } catch (error) {
          const errorMsg = `Failed to run migration ${migrationName}: ${error}`;
          console.error(`[MigrationRunner] ❌ ${errorMsg}`);
          result.errors.push(errorMsg);
          result.success = false;
          
          // Stop on first error to prevent cascading failures
          break;
        }
      }

      return result;
    } catch (error) {
      console.error('[MigrationRunner] Fatal error:', error);
      result.success = false;
      result.errors.push(`Fatal error: ${error}`);
      return result;
    }
  }

  /**
   * Create migrations tracking table
   */
  private createMigrationsTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS _migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        executed_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);
  }

  /**
   * Check if a migration has been run
   */
  private hasRunMigration(name: string): boolean {
    const stmt = this.db.prepare('SELECT 1 FROM _migrations WHERE name = ?');
    const result = stmt.get(name);
    return !!result;
  }

  /**
   * Record that a migration has been run
   */
  private recordMigration(name: string): void {
    const stmt = this.db.prepare('INSERT INTO _migrations (name) VALUES (?)');
    stmt.run(name);
  }

  /**
   * Get list of applied migrations
   */
  getAppliedMigrations(): string[] {
    const stmt = this.db.prepare('SELECT name FROM _migrations ORDER BY executed_at');
    const rows = stmt.all() as { name: string }[];
    return rows.map(row => row.name);
  }

  /**
   * Check if specific migration has been applied
   */
  isMigrationApplied(name: string): boolean {
    return this.hasRunMigration(name);
  }
}