/**
 * @file OrganizationSelection.tsx
 * @description Component for selecting organization when user has multiple
 */

import React, { useState } from 'react';
import { useOrganizationStore } from '../../store/organization.store';
import { Organization } from '../../../shared/types/organization.types';
import {
  Building2,
  Check,
  Loader2,
  Users,
  Plus,
  Trash2,
  AlertTriangle,
} from 'lucide-react';
import { OrganizationSetup } from './OrganizationSetup';
import { useSyncAuth } from '../../store/sync.store';

interface OrganizationSelectionProps {
  organizations: Organization[];
  onSelect: () => void;
}

export const OrganizationSelection: React.FC<OrganizationSelectionProps> = ({
  organizations,
  onSelect,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrgId, setSelectedOrgId] = useState<string | null>(null);
  const [showCreateOrg, setShowCreateOrg] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState<{
    orgId: string;
    orgName: string;
  } | null>(null);

  const { switchOrganization, deleteOrganization } = useOrganizationStore();
  const { user } = useSyncAuth();

  const handleSelectOrganization = async () => {
    if (!selectedOrgId) {
      setError('Please select an organization');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await switchOrganization(selectedOrgId);
      if (result.success) {
        onSelect();
      } else {
        setError(result.error || 'Failed to switch organization');
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to switch organization'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteOrganization = async () => {
    if (!deleteConfirm) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await deleteOrganization(deleteConfirm.orgId);
      if (result.success) {
        setDeleteConfirm(null);
        // If we deleted the selected org, clear selection
        if (selectedOrgId === deleteConfirm.orgId) {
          setSelectedOrgId(null);
        }
        // Reload organizations list will happen automatically via store
      } else {
        setError(result.error || 'Failed to delete organization');
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to delete organization'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getPlanBadge = (plan: string) => {
    const badges = {
      free: 'bg-gray-100 text-gray-700',
      team: 'bg-blue-100 text-blue-700',
      enterprise: 'bg-purple-100 text-purple-700',
    };
    return badges[plan as keyof typeof badges] || badges.free;
  };

  // Show organization setup if user wants to create new
  if (showCreateOrg && user) {
    return (
      <OrganizationSetup
        user={user as any}
        onComplete={onSelect}
        onBack={() => setShowCreateOrg(false)}
      />
    );
  }

  return (
    <div className='max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg max-h-[90vh] overflow-hidden flex flex-col'>
      <div className='text-center mb-8'>
        <h1 className='text-2xl font-bold text-gray-900 mb-2'>
          Select Workspace
        </h1>
        <p className='text-gray-600'>
          Choose which workspace you'd like to work in.
        </p>
        {organizations.length > 5 && (
          <p className='text-xs text-gray-500 mt-2'>
            Scroll to see all {organizations.length} workspaces
          </p>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className='mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm'>
          {error}
        </div>
      )}

      {/* Organizations List */}
      <div className='space-y-3 mb-6 max-h-96 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100'>
        {organizations.map(org => (
          <div
            key={org.external_id}
            className={`relative w-full p-4 rounded-lg border-2 transition-all ${
              selectedOrgId === org.external_id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300 bg-white'
            }`}
          >
            <button
              onClick={() => setSelectedOrgId(org.external_id)}
              disabled={isLoading}
              className={`w-full text-left ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            >
              <div className='flex items-start justify-between'>
                <div className='flex items-start space-x-3'>
                  <Building2
                    className={`w-5 h-5 mt-0.5 ${
                      selectedOrgId === org.external_id
                        ? 'text-blue-500'
                        : 'text-gray-400'
                    }`}
                  />
                  <div>
                    <h3 className='font-medium text-gray-900'>{org.name}</h3>
                    {org.slug && (
                      <p className='text-xs text-gray-400 mt-0.5'>
                        ID: {org.slug}
                      </p>
                    )}
                    <p className='text-sm text-gray-500 mt-1'>
                      <Users className='inline-block w-3 h-3 mr-1' />
                      {org.memberCount || 1} member
                      {org.memberCount !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  <span
                    className={`text-xs px-2 py-1 rounded-full font-medium ${getPlanBadge(org.plan)}`}
                  >
                    {org.plan}
                  </span>
                  {org.userRole && (
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-medium ${
                        org.userRole === 'owner'
                          ? 'bg-purple-100 text-purple-700'
                          : org.userRole === 'admin'
                            ? 'bg-orange-100 text-orange-700'
                            : 'bg-gray-100 text-gray-700'
                      }`}
                    >
                      {org.userRole}
                    </span>
                  )}
                  {selectedOrgId === org.external_id && (
                    <Check className='w-5 h-5 text-blue-500' />
                  )}
                </div>
              </div>
            </button>

            {/* Delete button - only show for owners */}
            {org.userRole === 'owner' && (
              <button
                onClick={e => {
                  e.stopPropagation();
                  setDeleteConfirm({
                    orgId: org.external_id,
                    orgName: org.name,
                  });
                }}
                disabled={isLoading || organizations.length === 1}
                className={`absolute top-4 right-4 p-1 rounded hover:bg-red-100 ${
                  isLoading || organizations.length === 1
                    ? 'opacity-50 cursor-not-allowed'
                    : 'text-red-500 hover:text-red-700'
                }`}
                title={
                  organizations.length === 1
                    ? "Can't delete your only organization"
                    : 'Delete organization'
                }
              >
                <Trash2 className='w-4 h-4' />
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Continue Button */}
      <button
        onClick={handleSelectOrganization}
        disabled={isLoading || !selectedOrgId}
        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center ${
          isLoading || !selectedOrgId
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-blue-500 text-white hover:bg-blue-600'
        }`}
      >
        {isLoading ? (
          <>
            <Loader2 className='w-5 h-5 mr-2 animate-spin' />
            Loading Workspace...
          </>
        ) : (
          'Continue to Workspace'
        )}
      </button>

      {/* Create New Organization Link */}
      <div className='mt-4 text-center'>
        <button
          onClick={() => setShowCreateOrg(true)}
          disabled={isLoading}
          className='text-sm text-blue-500 hover:text-blue-600 disabled:text-gray-400 flex items-center justify-center w-full'
        >
          <Plus className='w-4 h-4 mr-1' />
          Create a new workspace instead
        </button>
      </div>

      {/* Delete Confirmation Dialog */}
      {deleteConfirm && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
            <div className='flex items-start space-x-3 mb-4'>
              <AlertTriangle className='w-6 h-6 text-orange-500 flex-shrink-0 mt-0.5' />
              <div>
                <h3 className='text-lg font-semibold text-gray-900'>
                  Delete Organization?
                </h3>
                <p className='text-sm text-gray-600 mt-1'>
                  Are you sure you want to delete "{deleteConfirm.orgName}"?
                  This action cannot be undone.
                </p>
                <p className='text-xs text-gray-500 mt-2'>
                  Note: You cannot delete an organization that contains colors
                  or products.
                </p>
              </div>
            </div>

            <div className='flex justify-end space-x-3'>
              <button
                onClick={() => setDeleteConfirm(null)}
                disabled={isLoading}
                className='px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50'
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteOrganization}
                disabled={isLoading}
                className='px-4 py-2 text-white bg-red-500 rounded-lg hover:bg-red-600 disabled:opacity-50 flex items-center'
              >
                {isLoading ? (
                  <>
                    <Loader2 className='w-4 h-4 mr-2 animate-spin' />
                    Deleting...
                  </>
                ) : (
                  'Delete Organization'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
