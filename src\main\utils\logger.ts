/**
 * Centralized logging utility for ChromaSync
 * Provides structured logging with electron-log
 * CRITICAL: This must be the ONLY place that imports electron-log to prevent IPC handler duplication
 */
import { ipcMain } from 'electron';

// CRITICAL: Prevent duplicate __ELECTRON_LOG__ IPC handler registration
// This MUST happen before importing electron-log
const originalHandle = ipcMain.handle.bind(ipcMain);
let electronLogHandlerRegistered = false;

// Override ipcMain.handle to prevent duplicate __ELECTRON_LOG__ registration
(ipcMain as any).handle = function(channel: string, listener: any) {
  if (channel === '__ELECTRON_LOG__') {
    if (electronLogHandlerRegistered) {
      console.warn('[Logger] 🚫 Prevented duplicate __ELECTRON_LOG__ handler registration');
      return; // Silently ignore duplicate registration
    } else {
      electronLogHandlerRegistered = true;
      console.log('[Logger] ✅ Allowing first __ELECTRON_LOG__ handler registration');
    }
  }
  return originalHandle(channel, listener);
};

// CRITICAL: Fix for electron-log's requireMain issue in bundled environments
// electron-log may try to use require.main which doesn't exist in some bundled contexts
if (typeof require !== 'undefined' && !require.main) {
  (require as any).main = {
    filename: process.argv[1] || 'electron',
    paths: []
  };
}

// Import type augmentation for electron-log
import '../../shared/types/electron-log.d.ts';

// Now import electron-log after setting up the guard
import log from 'electron-log';
import { is } from 'electron-util';

// CRITICAL: Prevent ALL electron-log instances from registering IPC handlers
// This must happen immediately after import, before any other operations
if (log.transports.ipc) {
  // Type-safe way to disable IPC transport - module augmentation allows null
  log.transports.ipc = null;
}

// Track if logging has been configured to prevent multiple configurations
let isConfigured = false;

// Configure logging transports
export function configureLogging(): void {
  if (isConfigured) {
    return; // Already configured, skip
  }
  
  // Configure file transport
  log.transports.file.level = is.development ? 'debug' : 'info';
  log.transports.file.format = '{y}-{m}-{d} {h}:{i}:{s}.{ms} [{level}] {text}';
  log.transports.file.maxSize = 10 * 1024 * 1024; // 10MB
  
  // Configure console transport
  log.transports.console.level = is.development ? 'debug' : 'warn';
  log.transports.console.format = '{h}:{i}:{s}.{ms} [{level}] {text}';
  
  isConfigured = true;
  
  // Add timestamp to main log
  log.info(`[Logger] Logging configured for ${is.development ? 'development' : 'production'} mode`);
}

// Create scoped loggers for different modules
export const createLogger = (scope: string) => {
  return {
    info: (...args: any[]) => log.info(`[${scope}]`, ...args),
    warn: (...args: any[]) => log.warn(`[${scope}]`, ...args),
    error: (...args: any[]) => log.error(`[${scope}]`, ...args),
    debug: (...args: any[]) => log.debug(`[${scope}]`, ...args),
    verbose: (...args: any[]) => log.verbose(`[${scope}]`, ...args),
    silly: (...args: any[]) => log.silly(`[${scope}]`, ...args),
    log: (...args: any[]) => log.log(`[${scope}]`, ...args),
  };
};

// Main application logger
export const appLogger = createLogger('app');

// Service loggers
export const dbLogger = createLogger('database');
export const syncLogger = createLogger('sync');
export const organizationLogger = createLogger('organization');
export const productLogger = createLogger('product');
export const colorLogger = createLogger('color');
export const ipcLogger = createLogger('ipc');
export const authLogger = createLogger('auth');
export const licenseLogger = createLogger('license');
export const serviceLogger = createLogger('service');

// Utility function to replace console.log patterns
export function logWithTag(tag: string, level: 'info' | 'error' | 'warn' | 'debug' = 'info', message: string, ...args: unknown[]): void {
  const logger = createLogger(tag.toLowerCase().replace(/[\[\]]/g, ''));
  logger[level](message, ...args);
}

// Helper function for error logging with context
export function logError(scope: string, message: string, error?: unknown): void {
  const logger = createLogger(scope);
  if (error) {
    if (error instanceof Error) {
      logger.error(`${message}: ${error.message}`, { stack: error.stack });
    } else {
      logger.error(`${message}:`, error);
    }
  } else {
    logger.error(message);
  }
}

// Helper function for debug logging that respects environment
export function logDebug(scope: string, message: string, ...args: unknown[]): void {
  if (is.development) {
    const logger = createLogger(scope);
    logger.debug(message, ...args);
  }
}

// Initialize logging when this module is imported
configureLogging();

export default log;