/**
 * @file migration-helper.ts
 * @description Helper functions to migrate existing IPC handlers to use new error handling utilities
 *
 * This file provides utilities to help migrate from old IPC handler patterns
 * to the new standardized error handling and validation system.
 */

import { IpcMain } from 'electron';
import { IPCResponse } from '../../../shared/types/ipc.types';
import {
  wrap,
  validate,
  respond,
  ValidationResult,
} from './ipc-error-handling';
import { registerHandlerSafely } from '../../utils/ipcRegistry';

// ============================================================================
// MIGRATION UTILITIES
// ============================================================================

/**
 * Legacy response format that many existing handlers use
 */
interface LegacyResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: number;
}

/**
 * Converts a legacy response to the new IPCResponse format
 */
export function convertLegacyResponse<T>(
  legacy: LegacyResponse<T>
): IPCResponse<T> {
  return {
    success: legacy.success,
    data: legacy.data,
    error: legacy.error,
    userMessage: legacy.message,
    timestamp: legacy.timestamp || Date.now(),
  };
}

/**
 * Wraps an existing legacy handler to use new error handling patterns
 */
export function migrateLegacyHandler<TArgs extends any[], TResult>(
  legacyHandler: (
    ...args: TArgs
  ) => Promise<LegacyResponse<TResult>> | LegacyResponse<TResult>,
  options: {
    channel: string;
    requireAuth?: boolean;
    requireOrganization?: boolean;
    customErrorMessage?: string;
  }
): (event: any, ...args: TArgs) => Promise<IPCResponse<TResult>> {
  return wrap.withErrorHandling(
    async (...args: TArgs): Promise<TResult> => {
      const legacyResponse = await legacyHandler(...args);

      if (!legacyResponse.success) {
        throw new Error(
          legacyResponse.error || legacyResponse.message || 'Operation failed'
        );
      }

      return legacyResponse.data as TResult;
    },
    {
      channel: options.channel,
      requireAuth: options.requireAuth,
      requireOrganization: options.requireOrganization,
      customErrorMessage: options.customErrorMessage,
    }
  );
}

/**
 * Creates a validation function from legacy validation logic
 */
export function createValidationFromLegacy<T>(
  legacyValidator: (data: T) => { valid: boolean; errors: string[] }
): (data: T) => ValidationResult {
  return (data: T): ValidationResult => {
    const result = legacyValidator(data);

    if (result.valid) {
      return { isValid: true, errors: [] };
    }

    return {
      isValid: false,
      errors: result.errors.map((error, index) => ({
        field: `field_${index}`,
        code: 'LEGACY_VALIDATION_ERROR',
        message: error,
      })),
    };
  };
}

// ============================================================================
// COMMON MIGRATION PATTERNS
// ============================================================================

/**
 * Migrates a simple CRUD handler
 */
export function migrateCRUDHandler<T>(
  operation: 'create' | 'read' | 'update' | 'delete',
  resourceName: string,
  legacyHandler: (...args: any[]) => Promise<LegacyResponse<T>>,
  options: {
    requireAuth?: boolean;
    requireOrganization?: boolean;
    customErrorMessage?: string;
  } = {}
): (...args: any[]) => Promise<IPCResponse<T>> {
  return migrateLegacyHandler(legacyHandler, {
    channel: `${resourceName.toLowerCase()}:${operation}`,
    requireAuth: options.requireAuth !== false,
    requireOrganization: options.requireOrganization !== false,
    customErrorMessage:
      options.customErrorMessage ||
      `Failed to ${operation} ${resourceName.toLowerCase()}`,
  });
}

/**
 * Migrates a handler with basic input validation
 */
export function migrateWithBasicValidation<TArgs extends any[], TResult>(
  legacyHandler: (...args: TArgs) => Promise<LegacyResponse<TResult>>,
  requiredFields: string[],
  options: {
    channel: string;
    requireAuth?: boolean;
    requireOrganization?: boolean;
    customErrorMessage?: string;
  }
): (event: any, ...args: TArgs) => Promise<IPCResponse<TResult>> {
  return wrap.withValidation(
    async (...args: TArgs): Promise<TResult> => {
      const legacyResponse = await legacyHandler(...args);

      if (!legacyResponse.success) {
        throw new Error(
          legacyResponse.error || legacyResponse.message || 'Operation failed'
        );
      }

      return legacyResponse.data as TResult;
    },
    // Simple validation function
    (...args: TArgs) => {
      const data = args[0] as Record<string, any>;
      return validate.required(data, requiredFields);
    },
    {
      channel: options.channel,
      requireAuth: options.requireAuth,
      requireOrganization: options.requireOrganization,
      customErrorMessage: options.customErrorMessage,
    }
  );
}

/**
 * Migrates sync-related handlers with special error handling
 */
export function migrateSyncHandler<TArgs extends any[], TResult>(
  legacyHandler: (...args: TArgs) => Promise<LegacyResponse<TResult>>,
  options: {
    channel: string;
    customErrorMessage?: string;
  }
): (event: any, ...args: TArgs) => Promise<IPCResponse<TResult>> {
  return async (_event: any, ...args: TArgs): Promise<IPCResponse<TResult>> => {
    try {
      // Sync operations always require auth and organization
      const contextValidation = await validate.fullContext();
      if (!contextValidation.isValid) {
        return respond.validationError<TResult>(
          contextValidation,
          'Sync operations require authentication and organization context'
        );
      }

      const legacyResponse = await legacyHandler(...args);

      if (legacyResponse.success) {
        return respond.success(
          legacyResponse.data as TResult,
          'Sync operation completed successfully'
        );
      } else {
        // Handle sync-specific errors
        const errorMessage =
          legacyResponse.error ||
          legacyResponse.message ||
          'Sync operation failed';

        // Classify sync errors
        if (
          errorMessage.toLowerCase().includes('network') ||
          errorMessage.toLowerCase().includes('connection')
        ) {
          return respond.fromError<TResult>(
            new Error(errorMessage),
            'Network connection error. Please check your internet connection and try again.',
            'sync-network-error'
          );
        } else if (errorMessage.toLowerCase().includes('auth')) {
          return respond.fromError<TResult>(
            new Error(errorMessage),
            'Authentication error. Please log in again.',
            'sync-auth-error'
          );
        } else {
          return respond.fromError<TResult>(
            new Error(errorMessage),
            options.customErrorMessage ||
              'Sync operation failed. Please try again.',
            'sync-general-error'
          );
        }
      }
    } catch (error) {
      return respond.fromError<TResult>(
        error,
        options.customErrorMessage || 'Sync operation failed unexpectedly',
        options.channel
      );
    }
  };
}

// ============================================================================
// BATCH MIGRATION UTILITIES
// ============================================================================

/**
 * Configuration for batch migration of handlers
 */
export interface HandlerMigrationConfig {
  channel: string;
  handler: (...args: any[]) => Promise<any>;
  type: 'simple' | 'validation' | 'sync' | 'custom';
  options?: {
    requireAuth?: boolean;
    requireOrganization?: boolean;
    customErrorMessage?: string;
    requiredFields?: string[];
    validator?: (...args: any[]) => ValidationResult;
  };
}

/**
 * Migrates multiple handlers at once
 */
export function batchMigrateHandlers(
  ipcMain: IpcMain,
  configs: HandlerMigrationConfig[]
): void {
  console.log(
    `[Migration] Starting batch migration of ${configs.length} handlers...`
  );

  for (const config of configs) {
    try {
      let migratedHandler: (...args: any[]) => Promise<IPCResponse<any>>;

      switch (config.type) {
        case 'simple':
          migratedHandler = migrateLegacyHandler(config.handler, {
            channel: config.channel,
            requireAuth: config.options?.requireAuth,
            requireOrganization: config.options?.requireOrganization,
            customErrorMessage: config.options?.customErrorMessage,
          });
          break;

        case 'validation':
          if (!config.options?.requiredFields && !config.options?.validator) {
            throw new Error(
              'Validation migration requires requiredFields or validator'
            );
          }

          migratedHandler = config.options.validator
            ? wrap.withValidation(
                async (...args) => {
                  const response = await config.handler(...args);
                  if (!response.success) {
                    throw new Error(response.error || 'Operation failed');
                  }
                  return response.data;
                },
                config.options.validator,
                {
                  channel: config.channel,
                  requireAuth: config.options.requireAuth,
                  requireOrganization: config.options.requireOrganization,
                  customErrorMessage: config.options.customErrorMessage,
                }
              )
            : migrateWithBasicValidation(
                config.handler,
                config.options.requiredFields!,
                {
                  channel: config.channel,
                  requireAuth: config.options.requireAuth,
                  requireOrganization: config.options.requireOrganization,
                  customErrorMessage: config.options.customErrorMessage,
                }
              );
          break;

        case 'sync':
          migratedHandler = migrateSyncHandler(config.handler, {
            channel: config.channel,
            customErrorMessage: config.options?.customErrorMessage,
          });
          break;

        case 'custom':
          // For custom migrations, assume the handler is already properly formatted
          migratedHandler = config.handler;
          break;

        default:
          throw new Error(`Unknown migration type: ${config.type}`);
      }

      // Register the migrated handler
      registerHandlerSafely(ipcMain, config.channel, migratedHandler);
      console.log(
        `[Migration] ✅ Migrated handler for channel: ${config.channel}`
      );
    } catch (error) {
      console.error(
        `[Migration] ❌ Failed to migrate handler for channel: ${config.channel}`,
        error
      );
    }
  }

  console.log('[Migration] ✅ Batch migration completed');
}

// ============================================================================
// EXAMPLE MIGRATION CONFIGURATIONS
// ============================================================================

/**
 * Example migration configurations for common ChromaSync handlers
 */
export const exampleMigrationConfigs: HandlerMigrationConfig[] = [
  {
    channel: 'color:get-all',
    handler: async () => ({ success: true, data: [] }), // Placeholder
    type: 'simple',
    options: {
      requireAuth: true,
      requireOrganization: true,
      customErrorMessage: 'Failed to retrieve colors',
    },
  },
  {
    channel: 'color:create',
    handler: async (colorData: any) => ({ success: true, data: colorData }), // Placeholder
    type: 'validation',
    options: {
      requireAuth: true,
      requireOrganization: true,
      requiredFields: ['name', 'hex'],
      customErrorMessage: 'Failed to create color',
    },
  },
  {
    channel: 'sync:start',
    handler: async () => ({ success: true, data: { itemsProcessed: 0 } }), // Placeholder
    type: 'sync',
    options: {
      customErrorMessage: 'Sync operation failed',
    },
  },
];

/**
 * Helper function to run example migrations (for testing/demonstration)
 */
export function runExampleMigrations(ipcMain: IpcMain): void {
  console.log('[Migration] Running example migrations...');
  batchMigrateHandlers(ipcMain, exampleMigrationConfigs);
}

// ============================================================================
// MIGRATION VALIDATION
// ============================================================================

/**
 * Validates that a migrated handler works correctly
 */
export async function validateMigratedHandler<T>(
  handler: (...args: any[]) => Promise<IPCResponse<T>>,
  testArgs: any[],
  expectedSuccess: boolean = true
): Promise<boolean> {
  try {
    const response = await handler(...testArgs);

    // Check response structure
    if (typeof response.success !== 'boolean') {
      console.error(
        'Migration validation failed: success field missing or invalid'
      );
      return false;
    }

    if (typeof response.timestamp !== 'number') {
      console.error(
        'Migration validation failed: timestamp field missing or invalid'
      );
      return false;
    }

    // Check expected success/failure
    if (response.success !== expectedSuccess) {
      console.error(
        `Migration validation failed: expected success=${expectedSuccess}, got success=${response.success}`
      );
      return false;
    }

    // If successful, check data presence
    if (response.success && response.data === undefined) {
      console.warn(
        'Migration validation warning: successful response has no data'
      );
    }

    // If failed, check error information
    if (!response.success && !response.error && !response.userMessage) {
      console.error(
        'Migration validation failed: error response has no error information'
      );
      return false;
    }

    console.log('Migration validation passed');
    return true;
  } catch (error) {
    console.error('Migration validation failed with exception:', error);
    return false;
  }
}

/**
 * Runs validation tests on multiple migrated handlers
 */
export async function validateMigrations(
  handlers: Array<{
    name: string;
    handler: (...args: any[]) => Promise<IPCResponse<any>>;
    testArgs: any[];
    expectedSuccess?: boolean;
  }>
): Promise<{
  passed: number;
  failed: number;
  results: Array<{ name: string; passed: boolean }>;
}> {
  console.log(
    `[Migration Validation] Testing ${handlers.length} migrated handlers...`
  );

  const results = [];
  let passed = 0;
  let failed = 0;

  for (const { name, handler, testArgs, expectedSuccess } of handlers) {
    console.log(`[Migration Validation] Testing ${name}...`);

    const result = await validateMigratedHandler(
      handler,
      testArgs,
      expectedSuccess
    );
    results.push({ name, passed: result });

    if (result) {
      passed++;
      console.log(`[Migration Validation] ✅ ${name} passed`);
    } else {
      failed++;
      console.log(`[Migration Validation] ❌ ${name} failed`);
    }
  }

  console.log(
    `[Migration Validation] Completed: ${passed} passed, ${failed} failed`
  );
  return { passed, failed, results };
}
