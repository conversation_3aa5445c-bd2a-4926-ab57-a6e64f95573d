/**
 * @file color-store-simplified.ts
 * @description Simplified ColorStore implementation using shared data loading utilities
 * This replaces the complex caching mechanism with ProductStore's reliable pattern
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useMemo } from 'react';
import { createCompleteStoreSetup } from '../utils/store-utilities';

// Color store state interface (no types)
const ColorStoreState = {
  // Extracted data for easy access
  colors: [],
  usageCounts: {},
  pantoneColors: [],
  ralColors: [],

  // UI State
  selectedColor: undefined,
  searchQuery: '',
  viewMode: 'table',
  darkMode: false,

  // State management
  data: undefined,
  isLoading: false,
  error: undefined,
};

/**
 * API call function for colors with usage
 */
const loadColorsAPICall = async (): Promise<any> => {
  if (!window.colorAPI) {
    throw new Error('Color API not available');
  }

  console.log('[ColorStore] 📡 Calling window.colorAPI.getAllWithUsage()...');
  const response = await window.colorAPI.getAllWithUsage();
  console.log('[ColorStore] 📡 Response received');

  return response;
};

/**
 * Validate color response data structure
 */
const validateColorData = (data: any): boolean => {
  return (
    data &&
    typeof data === 'object' &&
    Array.isArray(data.colors) &&
    typeof data.usageCounts === 'object'
  );
};

/**
 * Transform color response data for consistency
 */
const transformColorData = data => {
  return {
    colors: data.colors || [],
    usageCounts: data.usageCounts || {},
    organizationId: data.organizationId || '',
    totalColors: data.totalColors || data.colors?.length || 0,
    colorsWithUsage: data.colorsWithUsage || 0,
  };
};

// Create shared store setup
const storeSetup = createCompleteStoreSetup({
  storeName: 'color-store',
  apiCall: loadColorsAPICall,
  organizationRequired: true,
  eventDelay: 200, // Match ProductStore timing
  validateData: validateColorData,
  transformData: transformColorData,
});

console.log('🚀 SIMPLIFIED COLOR STORE INSTANTIATING...');

/**
 * Simplified ColorStore with shared data loading utilities
 */
export const useColorStoreSimplified = create()(
  devtools(
    (set, get) => {
      // Create state setter that updates both main data and extracted fields
      const setState = updates => {
        const newState: any = { ...updates };

        // Extract colors and usageCounts from data for easy access
        if (updates.data) {
          newState.colors = updates.data.colors || [];
          newState.usageCounts = updates.data.usageCounts || {};
        } else if (updates.data === undefined) {
          newState.colors = [];
          newState.usageCounts = {};
        }

        set(newState);
      };

      // Create common actions using shared utilities
      const commonActions = storeSetup.createActions(setState);

      // Setup event listeners with proper timing
      storeSetup.setupEventListeners(setState);

      return {
        // Initial state
        data: undefined,
        colors: [],
        usageCounts: {},
        pantoneColors: [],
        ralColors: [],
        selectedColor: undefined,
        searchQuery: '',
        viewMode: 'table',
        isLoading: false,
        error: undefined,
        darkMode: false,

        // Main data loading method (simplified)
        loadColorsWithUsageSimple: async () => {
          console.log(
            '🚀 SIMPLIFIED COLOR STORE METHOD CALLED: loadColorsWithUsageSimple'
          );

          try {
            await storeSetup.dataLoader(setState);

            // Emit legacy completion event for compatibility
            const currentState = get();
            if (
              typeof window !== 'undefined' &&
              window.dispatchEvent &&
              currentState.data
            ) {
              window.dispatchEvent(
                new CustomEvent('color-store:refresh-complete', {
                  detail: {
                    success: !currentState.error,
                    timestamp: Date.now(),
                    colorCount: currentState.colors.length,
                    organizationId: currentState.data.organizationId,
                    error: currentState.error,
                  },
                })
              );
            }
          } catch (error) {
            console.error(
              '[ColorStore] Error in loadColorsWithUsageSimple:',
              error
            );
            setState({
              error: error instanceof Error ? error.message : String(error),
              isLoading: false,
            });
          }
        },

        // Legacy API compatibility methods (simplified implementations)
        addColor: async color => {
          try {
            console.log('[ColorStore] 🎨 Adding new color:', color);
            const response = await window.colorAPI.add(color);

            if (response && response.success && response.data) {
              // Trigger refresh after successful add
              await get().loadColorsWithUsageSimple();
              return response.data;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error adding color:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        updateColor: async (id, color) => {
          try {
            const result = await window.colorAPI.update(id, color);
            if (result && result.success) {
              await get().loadColorsWithUsageSimple();
              return true;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error updating color:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        deleteColor: async id => {
          try {
            const response = await window.colorAPI.delete(id);
            if (response && response.success) {
              await get().loadColorsWithUsageSimple();
              return true;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error deleting color:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        importColors: async mergeMode => {
          try {
            const typedMergeMode = mergeMode as 'replace' | 'merge' | undefined;
            const result = await window.colorAPI.importColors(typedMergeMode);
            if (result) {
              await get().loadColorsWithUsageSimple();
            }
            return result;
          } catch (error) {
            console.error('[ColorStore] Error importing colors:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        exportColors: async (filePath, format) => {
          try {
            const typedFormat = format as 'json' | 'csv' | undefined;
            return await window.colorAPI.exportColors(filePath, typedFormat);
          } catch (error) {
            console.error('[ColorStore] Error exporting colors:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        clearColors: async () => {
          try {
            const result = await window.colorAPI.clearAll();
            if (result) {
              await get().loadColorsWithUsageSimple();
              return true;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error clearing colors:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        loadPantoneColors: async () => {
          try {
            const { getEnhancedColorLibraryService } = await import(
              '../services/enhanced-color-library.service'
            );
            const service = getEnhancedColorLibraryService();
            const pantoneColors = await service.getAllPantoneColors();
            set({ pantoneColors });
            return pantoneColors;
          } catch (error) {
            console.error('[ColorStore] Error loading Pantone colors:', error);
            set({ pantoneColors: [] });
            return [];
          }
        },

        loadRalColors: async () => {
          try {
            const { getEnhancedColorLibraryService } = await import(
              '../services/enhanced-color-library.service'
            );
            const service = getEnhancedColorLibraryService();
            const ralColors = await service.getAllRalColors();
            set({ ralColors });
            return ralColors;
          } catch (error) {
            console.error('[ColorStore] Error loading RAL colors:', error);
            set({ ralColors: [] });
            return [];
          }
        },

        // UI action methods
        setSelectedColor: color => {
          set({ selectedColor: color });
        },

        setSearchQuery: query => {
          console.log('[ColorStore] 🔍 Setting search query:', query);
          set({ searchQuery: query });
        },

        setViewMode: mode => {
          set({ viewMode: mode });
        },

        setError: error => {
          set({ error });
        },

        toggleDarkMode: () => {
          set(state => ({ darkMode: !state.darkMode }));
        },

        clearState: () => {
          console.log('[ColorStore] Clearing state...');
          set({
            data: undefined,
            colors: [],
            usageCounts: {},
            selectedColor: undefined,
            searchQuery: '',
            error: undefined,
            isLoading: false,
          });
        },
      };
    },
    {
      name: 'color-store-simplified',
    }
  )
);

// Export existing selectors for compatibility
export const useFilteredColors = () => {
  const { colors, searchQuery } = useColorStoreSimplified();

  if (!searchQuery.trim()) {
    return colors;
  }

  const query = searchQuery.toLowerCase();
  return colors.filter(
    color =>
      color.name?.toLowerCase().includes(query) ||
      color.code?.toLowerCase().includes(query) ||
      color.hex?.toLowerCase().includes(query)
  );
};

export const useColorUsageCount = colorId => {
  const { usageCounts } = useColorStoreSimplified();
  return usageCounts[colorId] || { count: 0, products: [] };
};

// Advanced memoized selector (simplified version)
export const useFilteredColorsAdvanced = () => {
  const { colors, searchQuery } = useColorStoreSimplified();

  return useMemo(() => {
    console.log('[useFilteredColorsAdvanced] 🔍 Search state:', {
      searchQuery,
      totalColors: colors?.length || 0,
      colorsIsArray: Array.isArray(colors),
    });

    if (!colors || !Array.isArray(colors)) {
      return [];
    }
    if (!searchQuery) {
      return colors;
    }

    // Check if it's a product filter query
    if (searchQuery.toLowerCase().startsWith('product:')) {
      const productName = searchQuery.substring(8).replace(/"/g, '').trim();
      return colors.filter(
        color => color.product?.toLowerCase() === productName.toLowerCase()
      );
    }

    // Regular search query
    const query = searchQuery.toLowerCase();

    return colors.filter(color => {
      const productMatch = color.product?.toLowerCase()?.includes(query);
      const nameMatch = color.name?.toLowerCase()?.includes(query);
      const codeMatch = color.code?.toLowerCase()?.includes(query);
      const hexMatch = color.hex?.toLowerCase()?.includes(query);
      const cmykMatch = color.cmyk?.toLowerCase()?.includes(query);
      const notesMatch =
        color.notes && color.notes.toLowerCase().includes(query);

      const basicMatch =
        productMatch ||
        nameMatch ||
        codeMatch ||
        hexMatch ||
        cmykMatch ||
        notesMatch;

      // Check gradient color codes if it's a gradient
      let gradientMatch = false;
      if (
        color.gradient?.colorCodes &&
        Array.isArray(color.gradient.colorCodes)
      ) {
        gradientMatch = color.gradient.colorCodes.some(
          colorCode => colorCode && colorCode.toLowerCase().includes(query)
        );
      }

      return basicMatch || gradientMatch;
    });
  }, [colors, searchQuery]);
};

export const useGroupedByCode = () => {
  const { colors, searchQuery } = useColorStoreSimplified();

  return useMemo(() => {
    if (!colors || colors.length === 0) {
      return [];
    }

    // Implementation remains the same as original but with simplified data source
    const getCleanCode = code => {
      if (!code || typeof code !== 'string') {
        return '';
      }
      const parts = code.split('-');
      return parts[0] || '';
    };

    // Group colors by clean code and apply search filtering
    const allCodeGroups = new Map();

    for (const color of colors) {
      const code = color.code;
      if (!code) {continue;}

      const cleanCode = getCleanCode(code);
      if (!cleanCode) {continue;}

      const existing = allCodeGroups.get(cleanCode);
      if (existing) {
        existing.push(color);
      } else {
        allCodeGroups.set(cleanCode, [color]);
      }
    }

    const codeGroups = [];

    allCodeGroups.forEach((colorsInGroup, cleanCode) => {
      const actualUsageCount = colorsInGroup.length;
      const uniqueProducts = new Set();

      for (const color of colorsInGroup) {
        if (color.product && color.product.trim() !== '') {
          uniqueProducts.add(color.product);
        }
      }

      let representativeColor = colorsInGroup[0];
      if (searchQuery && searchQuery.trim() !== '') {
        const matchingColor = colorsInGroup.find(color => {
          const lowerQuery = searchQuery.toLowerCase();
          return (
            color.name?.toLowerCase()?.includes(lowerQuery) ||
            color.product?.toLowerCase()?.includes(lowerQuery)
          );
        });
        if (matchingColor) {
          representativeColor = matchingColor;
        }
      }

      codeGroups.push({
        cleanCode,
        color: representativeColor,
        usageCount: actualUsageCount,
        products: Array.from(uniqueProducts),
      });
    });

    return codeGroups.sort((a, b) => a.cleanCode.localeCompare(b.cleanCode));
  }, [colors, searchQuery]);
};
