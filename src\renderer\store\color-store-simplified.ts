/**
 * @file color-store-simplified.ts
 * @description Simplified ColorStore implementation using shared data loading utilities
 * This replaces the complex caching mechanism with ProductStore's reliable pattern
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useMemo } from 'react';
<<<<<<< HEAD
import { createCompleteStoreSetup } from '../utils/store-utilities';
=======
>>>>>>> main

// Import color types
import { 
  ColorEntry, 
  NewColorEntry, 
  UpdateColorEntry, 
  ColorWithUsageResponse,
  ColorApiResponse 
} from '../../shared/types/color.types';

// Color store state interface
interface ColorStoreState {
  // Extracted data for easy access
<<<<<<< HEAD
  colors: [],
  usageCounts: {},
  pantoneColors: [],
  ralColors: [],

  // UI State
  selectedColor: undefined,
  searchQuery: '',
  viewMode: 'table',
  darkMode: false,

  // State management
  data: undefined,
  isLoading: false,
  error: undefined,
};
=======
  colors: ColorEntry[];
  usageCounts: Record<string, { count: number; products: string[] }>;
  pantoneColors: ColorEntry[];
  ralColors: ColorEntry[];
  
  // UI State
  selectedColor: ColorEntry | null;
  searchQuery: string;
  viewMode: 'table' | 'swatches' | 'codes' | 'products';
  darkMode: boolean;
  
  // State management
  data: ColorWithUsageResponse | undefined;
  isLoading: boolean;
  error: string | undefined;
}

// Color store actions interface
interface ColorStoreActions {
  // Data loading
  loadColorsWithUsageSimple: () => Promise<void>;
  
  // CRUD operations
  addColor: (color: NewColorEntry) => Promise<ColorEntry | false>;
  updateColor: (id: string, color: UpdateColorEntry) => Promise<boolean>;
  deleteColor: (id: string) => Promise<boolean>;
  
  // Import/Export
  importColors: (mergeMode: 'replace' | 'merge' | undefined) => Promise<boolean>;
  exportColors: (filePath: string, format: 'json' | 'csv' | undefined) => Promise<boolean>;
  clearColors: () => Promise<boolean>;
  
  // Library colors
  loadPantoneColors: () => Promise<ColorEntry[]>;
  loadRalColors: () => Promise<ColorEntry[]>;
  
  // UI actions
  setSelectedColor: (color: ColorEntry | null) => void;
  setSearchQuery: (query: string) => void;
  setViewMode: (mode: 'table' | 'swatches' | 'codes' | 'products') => void;
  setError: (error: string | undefined) => void;
  toggleDarkMode: () => void;
  clearState: () => void;
}

// Complete store interface
type ColorStore = ColorStoreState & ColorStoreActions;
>>>>>>> main

/**
 * API call function for colors with usage
 */
const loadColorsAPICall = async (): Promise<ColorWithUsageResponse> => {
  if (!window.colorAPI) {
    throw new Error('Color API not available');
  }

  console.log('[ColorStore] 📡 Calling window.colorAPI.getAllWithUsage()...');
  const response = await window.colorAPI.getAllWithUsage();
  console.log('[ColorStore] 📡 Response received');
<<<<<<< HEAD

  return response;
=======
  
  // Handle both wrapped and unwrapped responses
  if (response && typeof response === 'object' && 'success' in response) {
    const apiResponse = response as ColorApiResponse<ColorWithUsageResponse>;
    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse.error || 'API call failed');
    }
  }
  
  return response as ColorWithUsageResponse;
>>>>>>> main
};

/**
 * Validate color response data structure
 */
<<<<<<< HEAD
const validateColorData = (data: any): boolean => {
  return (
    data &&
    typeof data === 'object' &&
    Array.isArray(data.colors) &&
    typeof data.usageCounts === 'object'
  );
=======
const validateColorData = (data: unknown): data is ColorWithUsageResponse => {
  return data !== null &&
         data !== undefined &&
         typeof data === 'object' && 
         Array.isArray((data as ColorWithUsageResponse).colors) &&
         typeof (data as ColorWithUsageResponse).usageCounts === 'object';
>>>>>>> main
};

/**
 * Transform color response data for consistency
 */
<<<<<<< HEAD
const transformColorData = data => {
=======
const transformColorData = (data: ColorWithUsageResponse) => {
>>>>>>> main
  return {
    colors: data.colors || [],
    usageCounts: data.usageCounts || {},
    organizationId: data.organizationId || '',
    totalColors: data.totalColors || data.colors?.length || 0,
    colorsWithUsage: data.colorsWithUsage || 0,
  };
};

<<<<<<< HEAD
// Create shared store setup
const storeSetup = createCompleteStoreSetup({
  storeName: 'color-store',
  apiCall: loadColorsAPICall,
  organizationRequired: true,
  eventDelay: 200, // Match ProductStore timing
  validateData: validateColorData,
  transformData: transformColorData,
});

=======
>>>>>>> main
console.log('🚀 SIMPLIFIED COLOR STORE INSTANTIATING...');

/**
 * Simplified ColorStore with direct API calls
 */
export const useColorStoreSimplified = create()(
  devtools(
    (set, get) => {
<<<<<<< HEAD
      // Create state setter that updates both main data and extracted fields
      const setState = updates => {
        const newState: any = { ...updates };

        // Extract colors and usageCounts from data for easy access
        if (updates.data) {
          newState.colors = updates.data.colors || [];
          newState.usageCounts = updates.data.usageCounts || {};
        } else if (updates.data === undefined) {
          newState.colors = [];
          newState.usageCounts = {};
        }

        set(newState);
      };

      // Create common actions using shared utilities
      const commonActions = storeSetup.createActions(setState);

      // Setup event listeners with proper timing
      storeSetup.setupEventListeners(setState);
=======
      // Setup event listeners for organization changes
      if (typeof window !== 'undefined') {
        // Dynamic import to avoid circular dependencies
        import('../services/store-event-bus.service').then(({ storeEventBus }) => {
          // Organization cleared event
          storeEventBus.subscribe('ORGANIZATION_CLEARED', () => {
            console.log('[ColorStore] Organization cleared, clearing state');
            set({
              data: undefined,
              colors: [],
              usageCounts: {},
              isLoading: false,
              error: undefined
            });
          });

          // Organization switched event
          storeEventBus.subscribe('ORGANIZATION_SWITCHED', (event) => {
            if (event.type === 'ORGANIZATION_SWITCHED') {
              console.log('[ColorStore] Organization switched, reloading data');
              setTimeout(() => {
                (get() as ColorStore).loadColorsWithUsageSimple().catch(error => {
                  console.error('[ColorStore] Error reloading after org switch:', error);
                });
              }, 200);
            }
          });
        }).catch(error => {
          console.error('[ColorStore] Failed to setup event listeners:', error);
        });
      }
>>>>>>> main

      return {
        // Initial state
        data: undefined,
        colors: [],
        usageCounts: {},
        pantoneColors: [],
        ralColors: [],
        selectedColor: null,
        searchQuery: '',
        viewMode: 'table' as const,
        isLoading: false,
        error: undefined,
        darkMode: false,

        // Main data loading method (simplified)
        loadColorsWithUsageSimple: async () => {
          console.log(
            '🚀 SIMPLIFIED COLOR STORE METHOD CALLED: loadColorsWithUsageSimple'
          );

          try {
<<<<<<< HEAD
            await storeSetup.dataLoader(setState);

            // Emit legacy completion event for compatibility
            const currentState = get();
            if (
              typeof window !== 'undefined' &&
              window.dispatchEvent &&
              currentState.data
            ) {
              window.dispatchEvent(
                new CustomEvent('color-store:refresh-complete', {
                  detail: {
                    success: !currentState.error,
                    timestamp: Date.now(),
                    colorCount: currentState.colors.length,
                    organizationId: currentState.data.organizationId,
                    error: currentState.error,
                  },
                })
              );
=======
            // Set loading state
            set({ isLoading: true, error: undefined });
            
            // Load data from API
            const rawData = await loadColorsAPICall();
            
            // Validate data structure
            if (!validateColorData(rawData)) {
              throw new Error('Invalid data structure received from API');
            }
            
            // Transform data for consistency
            const transformedData = transformColorData(rawData);
            
            // Update state with successful data
            set({
              data: transformedData,
              colors: transformedData.colors,
              usageCounts: transformedData.usageCounts,
              isLoading: false,
              error: undefined
            });
            
            // Emit legacy completion event for compatibility
            if (typeof window !== 'undefined' && window.dispatchEvent) {
              window.dispatchEvent(new CustomEvent('color-store:refresh-complete', {
                detail: {
                  success: true,
                  timestamp: Date.now(),
                  colorCount: transformedData.colors.length,
                  organizationId: transformedData.organizationId,
                  error: undefined
                }
              }));
>>>>>>> main
            }
            
            console.log('[ColorStore] ✅ Data loaded successfully:', {
              colorCount: transformedData.colors.length,
              organizationId: transformedData.organizationId
            });
            
          } catch (error) {
<<<<<<< HEAD
            console.error(
              '[ColorStore] Error in loadColorsWithUsageSimple:',
              error
            );
            setState({
              error: error instanceof Error ? error.message : String(error),
              isLoading: false,
=======
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error('[ColorStore] Error in loadColorsWithUsageSimple:', error);
            
            set({ 
              error: errorMessage,
              isLoading: false 
>>>>>>> main
            });
            
            // Emit error completion event
            if (typeof window !== 'undefined' && window.dispatchEvent) {
              window.dispatchEvent(new CustomEvent('color-store:refresh-complete', {
                detail: {
                  success: false,
                  timestamp: Date.now(),
                  colorCount: 0,
                  organizationId: undefined,
                  error: errorMessage
                }
              }));
            }
          }
        },

        // Legacy API compatibility methods (simplified implementations)
<<<<<<< HEAD
        addColor: async color => {
          try {
            console.log('[ColorStore] 🎨 Adding new color:', color);
            const response = await window.colorAPI.add(color);

=======
        addColor: async (color: NewColorEntry) => {
          try {
            console.log('[ColorStore] 🎨 Adding new color:', color);
            const response: ColorApiResponse<ColorEntry> = await window.colorAPI.add(color);
            
>>>>>>> main
            if (response && response.success && response.data) {
              // Trigger refresh after successful add
              await (get() as ColorStore).loadColorsWithUsageSimple();
              return response.data;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error adding color:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        updateColor: async (id: string, color: UpdateColorEntry) => {
          try {
            const result = await window.colorAPI.update(id, color);
            if (result && result.success) {
              await (get() as ColorStore).loadColorsWithUsageSimple();
              return true;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error updating color:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

<<<<<<< HEAD
        deleteColor: async id => {
=======
        deleteColor: async (id: string) => {
>>>>>>> main
          try {
            const response: ColorApiResponse<boolean> = await window.colorAPI.delete(id);
            if (response && response.success) {
              await (get() as ColorStore).loadColorsWithUsageSimple();
              return true;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error deleting color:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

<<<<<<< HEAD
        importColors: async mergeMode => {
=======
        importColors: async (mergeMode: 'replace' | 'merge' | undefined) => {
>>>>>>> main
          try {
            const result = await window.colorAPI.importColors(mergeMode);
            if (result) {
              await (get() as ColorStore).loadColorsWithUsageSimple();
            }
            return result;
          } catch (error) {
            console.error('[ColorStore] Error importing colors:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        exportColors: async (filePath: string, format: 'json' | 'csv' | undefined) => {
          try {
            return await window.colorAPI.exportColors(filePath, format);
          } catch (error) {
            console.error('[ColorStore] Error exporting colors:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        clearColors: async () => {
          try {
            const result = await window.colorAPI.clearAll();
            if (result) {
              await (get() as ColorStore).loadColorsWithUsageSimple();
              return true;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error clearing colors:', error);
            set({
              error: error instanceof Error ? error.message : String(error),
            });
            return false;
          }
        },

        loadPantoneColors: async () => {
          try {
            const { getEnhancedColorLibraryService } = await import(
              '../services/enhanced-color-library.service'
            );
            const service = getEnhancedColorLibraryService();
            const pantoneColors = await service.getAllPantoneColors();
            set({ pantoneColors });
            return pantoneColors;
          } catch (error) {
            console.error('[ColorStore] Error loading Pantone colors:', error);
            set({ pantoneColors: [] });
            return [];
          }
        },

        loadRalColors: async () => {
          try {
            const { getEnhancedColorLibraryService } = await import(
              '../services/enhanced-color-library.service'
            );
            const service = getEnhancedColorLibraryService();
            const ralColors = await service.getAllRalColors();
            set({ ralColors });
            return ralColors;
          } catch (error) {
            console.error('[ColorStore] Error loading RAL colors:', error);
            set({ ralColors: [] });
            return [];
          }
        },

        // UI action methods
<<<<<<< HEAD
        setSelectedColor: color => {
          set({ selectedColor: color });
        },

        setSearchQuery: query => {
=======
        setSelectedColor: (color: ColorEntry | null) => {
          set({ selectedColor: color });
        },

        setSearchQuery: (query: string) => {
>>>>>>> main
          console.log('[ColorStore] 🔍 Setting search query:', query);
          set({ searchQuery: query });
        },

<<<<<<< HEAD
        setViewMode: mode => {
          set({ viewMode: mode });
        },

        setError: error => {
=======
        setViewMode: (mode: 'table' | 'swatches' | 'codes' | 'products') => {
          set({ viewMode: mode });
        },

        setError: (error: string | undefined) => {
>>>>>>> main
          set({ error });
        },

        toggleDarkMode: () => {
          set((state: ColorStoreState) => ({ darkMode: !state.darkMode }));
        },

        clearState: () => {
          console.log('[ColorStore] Clearing state...');
          set({
            data: undefined,
            colors: [],
            usageCounts: {},
            selectedColor: null,
            searchQuery: '',
            error: undefined,
            isLoading: false,
          });
        },
      };
    },
    {
      name: 'color-store-simplified',
    }
  )
);

// Export existing selectors for compatibility
export const useFilteredColors = () => {
<<<<<<< HEAD
  const { colors, searchQuery } = useColorStoreSimplified();

=======
  const { colors, searchQuery } = useColorStoreSimplified() as ColorStore;
  
>>>>>>> main
  if (!searchQuery.trim()) {
    return colors;
  }

  const query = searchQuery.toLowerCase();
<<<<<<< HEAD
  return colors.filter(
    color =>
      color.name?.toLowerCase().includes(query) ||
      color.code?.toLowerCase().includes(query) ||
      color.hex?.toLowerCase().includes(query)
  );
};

export const useColorUsageCount = colorId => {
  const { usageCounts } = useColorStoreSimplified();
=======
  return colors.filter((color) => 
    color.name?.toLowerCase().includes(query) ||
    color.code?.toLowerCase().includes(query) ||
    color.hex?.toLowerCase().includes(query)
  );
};

export const useColorUsageCount = (colorId: string) => {
  const { usageCounts } = useColorStoreSimplified() as ColorStore;
>>>>>>> main
  return usageCounts[colorId] || { count: 0, products: [] };
};

// Advanced memoized selector (simplified version)
export const useFilteredColorsAdvanced = () => {
<<<<<<< HEAD
  const { colors, searchQuery } = useColorStoreSimplified();

=======
  const { colors, searchQuery } = useColorStoreSimplified() as ColorStore;
  
>>>>>>> main
  return useMemo(() => {
    console.log('[useFilteredColorsAdvanced] 🔍 Search state:', {
      searchQuery,
      totalColors: colors?.length || 0,
      colorsIsArray: Array.isArray(colors),
    });

    if (!colors || !Array.isArray(colors)) {
      return [];
    }
    if (!searchQuery) {
      return colors;
    }

    // Check if it's a product filter query
    if (searchQuery.toLowerCase().startsWith('product:')) {
      const productName = searchQuery.substring(8).replace(/"/g, '').trim();
      return colors.filter(
        color => color.product?.toLowerCase() === productName.toLowerCase()
      );
    }

    // Regular search query
    const query = searchQuery.toLowerCase();

    return colors.filter(color => {
      const productMatch = color.product?.toLowerCase()?.includes(query);
      const nameMatch = color.name?.toLowerCase()?.includes(query);
      const codeMatch = color.code?.toLowerCase()?.includes(query);
      const hexMatch = color.hex?.toLowerCase()?.includes(query);
      const cmykMatch = color.cmyk?.toLowerCase()?.includes(query);
      const notesMatch =
        color.notes && color.notes.toLowerCase().includes(query);

      const basicMatch =
        productMatch ||
        nameMatch ||
        codeMatch ||
        hexMatch ||
        cmykMatch ||
        notesMatch;

      // Check gradient color codes if it's a gradient
      let gradientMatch = false;
<<<<<<< HEAD
      if (
        color.gradient?.colorCodes &&
        Array.isArray(color.gradient.colorCodes)
      ) {
        gradientMatch = color.gradient.colorCodes.some(
          colorCode => colorCode && colorCode.toLowerCase().includes(query)
=======
      if (color.gradient?.colorCodes && Array.isArray(color.gradient.colorCodes)) {
        gradientMatch = color.gradient.colorCodes.some((colorCode: string) => 
          colorCode && colorCode.toLowerCase().includes(query)
>>>>>>> main
        );
      }

      return basicMatch || gradientMatch;
    });
  }, [colors, searchQuery]);
};

export const useGroupedByCode = () => {
<<<<<<< HEAD
  const { colors, searchQuery } = useColorStoreSimplified();

=======
  const { colors, searchQuery } = useColorStoreSimplified() as ColorStore;
  
>>>>>>> main
  return useMemo(() => {
    if (!colors || colors.length === 0) {
      return [];
    }

    // Implementation remains the same as original but with simplified data source
<<<<<<< HEAD
    const getCleanCode = code => {
=======
    const getCleanCode = (code: string) => {
>>>>>>> main
      if (!code || typeof code !== 'string') {
        return '';
      }
      const parts = code.split('-');
      return parts[0] || '';
    };

    // Group colors by clean code and apply search filtering
    const allCodeGroups = new Map();

    for (const color of colors) {
      const code = color.code;
      if (!code) {continue;}

      const cleanCode = getCleanCode(code);
      if (!cleanCode) {continue;}

      const existing = allCodeGroups.get(cleanCode);
      if (existing) {
        existing.push(color);
      } else {
        allCodeGroups.set(cleanCode, [color]);
      }
    }
<<<<<<< HEAD

    const codeGroups = [];

=======
    
    const codeGroups: Array<{
      cleanCode: string;
      color: ColorEntry;
      usageCount: number;
      products: string[];
    }> = [];
    
>>>>>>> main
    allCodeGroups.forEach((colorsInGroup, cleanCode) => {
      const actualUsageCount = colorsInGroup.length;
      const uniqueProducts = new Set();

      for (const color of colorsInGroup) {
        if (color.product && color.product.trim() !== '') {
          uniqueProducts.add(color.product);
        }
      }

      let representativeColor = colorsInGroup[0];
      if (searchQuery && searchQuery.trim() !== '') {
        const matchingColor = colorsInGroup.find((color: ColorEntry) => {
          const lowerQuery = searchQuery.toLowerCase();
          return (
            color.name?.toLowerCase()?.includes(lowerQuery) ||
            color.product?.toLowerCase()?.includes(lowerQuery)
          );
        });
        if (matchingColor) {
          representativeColor = matchingColor;
        }
      }

      codeGroups.push({
        cleanCode,
        color: representativeColor,
        usageCount: actualUsageCount,
<<<<<<< HEAD
        products: Array.from(uniqueProducts),
=======
        products: Array.from(uniqueProducts) as string[]
>>>>>>> main
      });
    });

    return codeGroups.sort((a, b) => a.cleanCode.localeCompare(b.cleanCode));
  }, [colors, searchQuery]);
};
