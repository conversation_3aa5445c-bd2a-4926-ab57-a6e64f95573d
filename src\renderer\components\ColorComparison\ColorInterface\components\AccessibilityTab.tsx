/**
 * Accessibility Tab Component
 * Displays color accessibility information and simulations
 */

import { memo, useState, useMemo } from 'react';
import { Eye, AlertCircle, Zap } from 'lucide-react';
import type { AccessibilityTabProps } from '../types';
import {
  simulateColorBlindness,
  getColorBlindnessDescription,
  getColorBlindnessPrevalence,
  type ColorBlindnessType,
} from '../../../../../shared/utils/color/colorBlindness';
import { evaluateAPCAContrast } from '../../../../../shared/utils/color/analysis';
import { hexToRgb } from '../../../../../shared/utils/color/conversion';

const COLOR_BLINDNESS_TYPES: Array<{
  id: ColorBlindnessType;
  label: string;
  shortLabel: string;
}> = [
  {
    id: 'protanopia',
    label: 'Protanopia (Red-Blind)',
    shortLabel: 'Red-Blind',
  },
  {
    id: 'deuteranopia',
    label: 'Deuteranopia (Green-Blind)',
    shortLabel: 'Green-Blind',
  },
  {
    id: 'tritanopia',
    label: 'Tritanopia (Blue-Blind)',
    shortLabel: 'Blue-Blind',
  },
  {
    id: 'achromatopsia',
    label: 'Achromatopsia (No Color)',
    shortLabel: 'No Color',
  },
];

export const AccessibilityTab = memo<AccessibilityTabProps>(
  ({ selectedColor, contrastResults }) => {
    const [selectedSimulation, setSelectedSimulation] =
      useState<ColorBlindnessType>('protanopia');
    const [selectedBackground, setSelectedBackground] = useState<
      'white' | 'black' | 'gray'
    >('white');

    // Calculate simulated colors
    const simulatedColors = useMemo(() => {
      if (!selectedColor) {
        return {} as Record<ColorBlindnessType, string>;
      }

      const result: Record<ColorBlindnessType, string> = {} as any;

      // Main types
      COLOR_BLINDNESS_TYPES.forEach(type => {
        result[type.id] = simulateColorBlindness(selectedColor.hex, type.id);
      });

      // Anomaly types
      result['protanomaly'] = simulateColorBlindness(
        selectedColor.hex,
        'protanomaly'
      );
      result['deuteranomaly'] = simulateColorBlindness(
        selectedColor.hex,
        'deuteranomaly'
      );
      result['tritanomaly'] = simulateColorBlindness(
        selectedColor.hex,
        'tritanomaly'
      );

      return result;
    }, [selectedColor?.hex]);

    if (!selectedColor || !contrastResults) {
      return (
        <div className='p-4 text-center text-ui-text-muted'>
          <Eye className='mx-auto mb-2 h-8 w-8 opacity-50' />
          <p>Select a color to view accessibility information</p>
        </div>
      );
    }

    const getWCAGBadge = (level: string) => {
      const styles = {
        AAA: {
          color: 'var(--color-feedback-success)',
          backgroundColor: 'var(--feedback-bg-success)',
          borderColor: 'var(--feedback-border-success)',
        },
        AA: {
          color: 'var(--color-feedback-warning)',
          backgroundColor: 'var(--feedback-bg-warning)',
          borderColor: 'var(--feedback-border-warning)',
        },
        'AA Large': {
          color: 'var(--color-feedback-info)',
          backgroundColor: 'var(--feedback-bg-info)',
          borderColor: 'var(--feedback-border-info)',
        },
        Fail: {
          color: 'var(--color-feedback-error)',
          backgroundColor: 'var(--feedback-bg-error)',
          borderColor: 'var(--feedback-border-error)',
        },
      };

      return styles[level as keyof typeof styles] || styles.Fail;
    };

    const prevalence = getColorBlindnessPrevalence(selectedSimulation);

    // Get current background and contrast based on selection
    const getCurrentBackground = () => {
      switch (selectedBackground) {
        case 'white':
          return { color: '#FFFFFF', result: contrastResults.white };
        case 'black':
          return { color: '#000000', result: contrastResults.black };
        case 'gray':
          return { color: '#808080', result: contrastResults.gray };
      }
    };

    const currentBg = getCurrentBackground();

    // Calculate APCA results for all backgrounds
    const apcaResults = useMemo(() => {
      if (!selectedColor) {
        return null;
      }

      const textRgb = hexToRgb(selectedColor.hex);
      if (!textRgb) {
        return null;
      }

      return {
        white: evaluateAPCAContrast(textRgb, { r: 255, g: 255, b: 255 }),
        black: evaluateAPCAContrast(textRgb, { r: 0, g: 0, b: 0 }),
        gray: evaluateAPCAContrast(textRgb, { r: 128, g: 128, b: 128 }),
      };
    }, [selectedColor]);

    const currentAPCA = apcaResults && apcaResults[selectedBackground];

    const getAPCABadge = (level: string) => {
      const styles = {
        Gold: {
          color: 'var(--color-feedback-warning)',
          backgroundColor: 'var(--feedback-bg-warning)',
          borderColor: 'var(--feedback-border-warning)',
        },
        Silver: {
          color: 'var(--color-ui-foreground-secondary)',
          backgroundColor: 'var(--color-ui-background-secondary)',
          borderColor: 'var(--color-ui-border-medium)',
        },
        Bronze: {
          color: 'var(--color-feedback-info)',
          backgroundColor: 'var(--feedback-bg-info)',
          borderColor: 'var(--feedback-border-info)',
        },
        Fail: {
          color: 'var(--color-feedback-error)',
          backgroundColor: 'var(--feedback-bg-error)',
          borderColor: 'var(--feedback-border-error)',
        },
      };

      return styles[level as keyof typeof styles] || styles.Fail;
    };

    return (
      <div className='p-3 h-full flex flex-col'>
        {/* Combined WCAG & Color Blindness Section */}
        <div className='flex gap-3 h-full'>
          {/* Left Side - WCAG Contrast Analysis */}
          <div
            className='flex-1 rounded-lg p-3 flex flex-col'
            style={{
              backgroundColor: 'var(--color-ui-background-tertiary)',
              borderRadius: 'var(--radius-lg)',
            }}
          >
            <div className='flex items-center justify-between mb-3'>
              <h4
                className='text-sm font-medium flex items-center gap-2'
                style={{
                  fontSize: 'var(--font-size-sm)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-ui-foreground-primary)',
                }}
              >
                <Eye className='h-4 w-4' />
                Contrast Analysis
              </h4>

              {/* Background Toggle */}
              <div
                className='flex items-center gap-1 rounded-md p-0.5'
                style={{
                  backgroundColor: 'var(--color-ui-background-primary)',
                  borderRadius: 'var(--radius-md)',
                }}
              >
                <button
                  onClick={() => setSelectedBackground('white')}
                  className='px-2 py-1 text-xs font-medium transition-standard'
                  style={{
                    backgroundColor:
                      selectedBackground === 'white'
                        ? '#FFFFFF'
                        : 'transparent',
                    color:
                      selectedBackground === 'white'
                        ? '#000000'
                        : 'var(--color-ui-foreground-tertiary)',
                    borderRadius: 'var(--radius-DEFAULT)',
                    fontSize: 'var(--font-size-xs)',
                    fontWeight: 'var(--font-weight-medium)',
                    border: 'none',
                    cursor: 'pointer',
                    boxShadow:
                      selectedBackground === 'white'
                        ? 'var(--shadow-sm)'
                        : 'none',
                  }}
                  onMouseEnter={e => {
                    if (selectedBackground !== 'white') {
                      e.currentTarget.style.color =
                        'var(--color-ui-foreground-primary)';
                    }
                  }}
                  onMouseLeave={e => {
                    if (selectedBackground !== 'white') {
                      e.currentTarget.style.color =
                        'var(--color-ui-foreground-tertiary)';
                    }
                  }}
                >
                  White
                </button>
                <button
                  onClick={() => setSelectedBackground('black')}
                  className='px-2 py-1 text-xs font-medium transition-standard'
                  style={{
                    backgroundColor:
                      selectedBackground === 'black'
                        ? '#000000'
                        : 'transparent',
                    color:
                      selectedBackground === 'black'
                        ? '#FFFFFF'
                        : 'var(--color-ui-foreground-tertiary)',
                    borderRadius: 'var(--radius-DEFAULT)',
                    fontSize: 'var(--font-size-xs)',
                    fontWeight: 'var(--font-weight-medium)',
                    border: 'none',
                    cursor: 'pointer',
                    boxShadow:
                      selectedBackground === 'black'
                        ? 'var(--shadow-sm)'
                        : 'none',
                  }}
                  onMouseEnter={e => {
                    if (selectedBackground !== 'black') {
                      e.currentTarget.style.color =
                        'var(--color-ui-foreground-primary)';
                    }
                  }}
                  onMouseLeave={e => {
                    if (selectedBackground !== 'black') {
                      e.currentTarget.style.color =
                        'var(--color-ui-foreground-tertiary)';
                    }
                  }}
                >
                  Black
                </button>
                <button
                  onClick={() => setSelectedBackground('gray')}
                  className='px-2 py-1 text-xs font-medium transition-standard'
                  style={{
                    backgroundColor:
                      selectedBackground === 'gray' ? '#808080' : 'transparent',
                    color:
                      selectedBackground === 'gray'
                        ? '#FFFFFF'
                        : 'var(--color-ui-foreground-tertiary)',
                    borderRadius: 'var(--radius-DEFAULT)',
                    fontSize: 'var(--font-size-xs)',
                    fontWeight: 'var(--font-weight-medium)',
                    border: 'none',
                    cursor: 'pointer',
                    boxShadow:
                      selectedBackground === 'gray'
                        ? 'var(--shadow-sm)'
                        : 'none',
                  }}
                  onMouseEnter={e => {
                    if (selectedBackground !== 'gray') {
                      e.currentTarget.style.color =
                        'var(--color-ui-foreground-primary)';
                    }
                  }}
                  onMouseLeave={e => {
                    if (selectedBackground !== 'gray') {
                      e.currentTarget.style.color =
                        'var(--color-ui-foreground-tertiary)';
                    }
                  }}
                >
                  Gray
                </button>
              </div>
            </div>

            {/* Large Preview */}
            <div
              className='flex-1 overflow-hidden'
              style={{
                borderRadius: 'var(--radius-lg)',
                border: `1px solid var(--color-ui-border-light)`,
              }}
            >
              <div
                className='h-full p-6'
                style={{ backgroundColor: currentBg.color }}
              >
                <div className='h-full flex flex-col justify-center'>
                  <h2
                    className='text-2xl font-bold mb-3'
                    style={{
                      color: selectedColor.hex,
                      fontSize: 'var(--font-size-2xl)',
                      fontWeight: 'var(--font-weight-bold)',
                    }}
                  >
                    Sample Heading
                  </h2>
                  <p
                    className='text-base leading-relaxed mb-4'
                    style={{
                      color: selectedColor.hex,
                      fontSize: 'var(--font-size-base)',
                      lineHeight: 'var(--line-height-relaxed)',
                    }}
                  >
                    The quick brown fox jumps over the lazy dog. This sample
                    text demonstrates readability at different contrast levels.
                  </p>
                  <p
                    className='text-sm leading-relaxed'
                    style={{
                      color: selectedColor.hex,
                      fontSize: 'var(--font-size-sm)',
                      lineHeight: 'var(--line-height-relaxed)',
                    }}
                  >
                    WCAG guidelines recommend a minimum contrast ratio of 4.5:1
                    for normal text and 3:1 for large text. This helps ensure
                    content is accessible to users with moderately low vision.
                  </p>
                </div>
              </div>
            </div>

            {/* Contrast Info */}
            <div className='mt-3 space-y-3'>
              {/* WCAG 2.1 */}
              <div
                className='rounded-lg p-3'
                style={{
                  backgroundColor: 'var(--color-ui-background-primary)',
                  borderRadius: 'var(--radius-lg)',
                }}
              >
                <div className='flex items-center justify-between mb-2'>
                  <div
                    className='text-sm font-medium'
                    style={{
                      fontSize: 'var(--font-size-sm)',
                      fontWeight: 'var(--font-weight-medium)',
                      color: 'var(--color-ui-foreground-primary)',
                    }}
                  >
                    WCAG 2.1
                  </div>
                  <span
                    className='text-xs px-3 py-1 rounded-full font-medium'
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      fontWeight: 'var(--font-weight-medium)',
                      borderRadius: 'var(--radius-full)',
                      border: `1px solid ${getWCAGBadge(currentBg.result.level).borderColor}`,
                      ...getWCAGBadge(currentBg.result.level),
                    }}
                  >
                    {currentBg.result.level}
                  </span>
                </div>
                <div className='text-sm'>
                  <span
                    style={{
                      color: 'var(--color-ui-foreground-tertiary)',
                      fontSize: 'var(--font-size-sm)',
                    }}
                  >
                    Contrast Ratio:
                  </span>
                  <span
                    className='font-mono font-semibold'
                    style={{
                      fontFamily: 'var(--font-family-mono)',
                      fontWeight: 'var(--font-weight-semibold)',
                      color: 'var(--color-ui-foreground-primary)',
                    }}
                  >
                    {currentBg.result.ratio.toFixed(2)}:1
                  </span>
                </div>
              </div>

              {/* APCA (WCAG 3.0) */}
              {currentAPCA && (
                <div
                  className='p-3'
                  style={{
                    backgroundColor: 'var(--color-ui-background-primary)',
                    borderRadius: 'var(--radius-lg)',
                  }}
                >
                  <div className='flex items-center justify-between mb-2'>
                    <div
                      className='text-sm font-medium flex items-center gap-1'
                      style={{
                        fontSize: 'var(--font-size-sm)',
                        fontWeight: 'var(--font-weight-medium)',
                        color: 'var(--color-ui-foreground-primary)',
                      }}
                    >
                      <Zap className='h-3 w-3' />
                      APCA (WCAG 3.0)
                    </div>
                    <span
                      className='text-xs px-3 py-1 font-medium'
                      style={{
                        fontSize: 'var(--font-size-xs)',
                        fontWeight: 'var(--font-weight-medium)',
                        borderRadius: 'var(--radius-full)',
                        border: `1px solid ${getAPCABadge(currentAPCA.level).borderColor}`,
                        ...getAPCABadge(currentAPCA.level),
                      }}
                    >
                      {currentAPCA.level}
                    </span>
                  </div>
                  <div
                    className='text-sm mb-2'
                    style={{
                      fontSize: 'var(--font-size-sm)',
                      color: 'var(--color-ui-foreground-primary)',
                    }}
                  >
                    <span
                      style={{ color: 'var(--color-ui-foreground-tertiary)' }}
                    >
                      Perceptual Contrast:{' '}
                    </span>
                    <span
                      className='font-mono font-semibold'
                      style={{
                        fontFamily: 'var(--font-family-mono)',
                        fontWeight: 'var(--font-weight-semibold)',
                      }}
                    >
                      {currentAPCA.contrast.toFixed(0)}%
                    </span>
                  </div>
                  <div
                    className='text-xs'
                    style={{
                      fontSize: 'var(--font-size-xs)',
                      color: 'var(--color-ui-foreground-tertiary)',
                    }}
                  >
                    {currentAPCA.description}
                  </div>
                  {(currentAPCA.minFontSize || currentAPCA.minFontWeight) && (
                    <div
                      className='text-xs mt-1'
                      style={{
                        fontSize: 'var(--font-size-xs)',
                        color: 'var(--color-feedback-warning)',
                      }}
                    >
                      Recommend:
                      {currentAPCA.minFontSize &&
                        ` ${currentAPCA.minFontSize}px+`}
                      {currentAPCA.minFontWeight &&
                        ` ${currentAPCA.minFontWeight}+ weight`}
                    </div>
                  )}
                </div>
              )}

              {/* Quick Stats */}
              <div className='grid grid-cols-3 gap-2 text-xs'>
                <div
                  className='text-center p-2'
                  style={{
                    backgroundColor: 'var(--color-ui-background-primary)',
                    borderRadius: 'var(--radius-DEFAULT)',
                    fontSize: 'var(--font-size-xs)',
                  }}
                >
                  <div
                    className='font-medium'
                    style={{
                      fontWeight: 'var(--font-weight-medium)',
                      color: contrastResults.white.passes
                        ? 'var(--color-feedback-success)'
                        : 'var(--color-feedback-error)',
                    }}
                  >
                    {contrastResults.white.ratio.toFixed(1)}:1
                  </div>
                  <div style={{ color: 'var(--color-ui-foreground-tertiary)' }}>
                    vs White
                  </div>
                  {apcaResults && (
                    <div
                      className='text-xs mt-1'
                      style={{
                        fontSize: 'var(--font-size-xs)',
                        color: 'var(--color-ui-foreground-tertiary)',
                      }}
                    >
                      {apcaResults.white.contrast.toFixed(0)}%
                    </div>
                  )}
                </div>
                <div
                  className='text-center p-2'
                  style={{
                    backgroundColor: 'var(--color-ui-background-primary)',
                    borderRadius: 'var(--radius-DEFAULT)',
                    fontSize: 'var(--font-size-xs)',
                  }}
                >
                  <div
                    className='font-medium'
                    style={{
                      fontWeight: 'var(--font-weight-medium)',
                      color: contrastResults.black.passes
                        ? 'var(--color-feedback-success)'
                        : 'var(--color-feedback-error)',
                    }}
                  >
                    {contrastResults.black.ratio.toFixed(1)}:1
                  </div>
                  <div style={{ color: 'var(--color-ui-foreground-tertiary)' }}>
                    vs Black
                  </div>
                  {apcaResults && (
                    <div
                      className='text-xs mt-1'
                      style={{
                        fontSize: 'var(--font-size-xs)',
                        color: 'var(--color-ui-foreground-tertiary)',
                      }}
                    >
                      {apcaResults.black.contrast.toFixed(0)}%
                    </div>
                  )}
                </div>
                <div
                  className='text-center p-2'
                  style={{
                    backgroundColor: 'var(--color-ui-background-primary)',
                    borderRadius: 'var(--radius-DEFAULT)',
                    fontSize: 'var(--font-size-xs)',
                  }}
                >
                  <div
                    className='font-medium'
                    style={{
                      fontWeight: 'var(--font-weight-medium)',
                      color: contrastResults.gray.passes
                        ? 'var(--color-feedback-success)'
                        : 'var(--color-feedback-error)',
                    }}
                  >
                    {contrastResults.gray.ratio.toFixed(1)}:1
                  </div>
                  <div style={{ color: 'var(--color-ui-foreground-tertiary)' }}>
                    vs Gray
                  </div>
                  {apcaResults && (
                    <div
                      className='text-xs mt-1'
                      style={{
                        fontSize: 'var(--font-size-xs)',
                        color: 'var(--color-ui-foreground-tertiary)',
                      }}
                    >
                      {apcaResults.gray.contrast.toFixed(0)}%
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Color Blindness Simulator */}
          <div
            className='flex-1 p-3 flex flex-col'
            style={{
              backgroundColor: 'var(--color-ui-background-tertiary)',
              borderRadius: 'var(--radius-lg)',
            }}
          >
            <div className='flex items-center justify-between mb-3'>
              <h4 className='text-sm font-medium flex items-center gap-2'>
                <AlertCircle className='h-4 w-4' />
                Color Blindness Simulator
              </h4>
            </div>

            {/* Simulation Type Grid */}
            <div className='mb-2'>
              <div className='grid grid-cols-2 gap-2'>
                {COLOR_BLINDNESS_TYPES.map(type => (
                  <button
                    key={type.id}
                    onClick={() => setSelectedSimulation(type.id)}
                    className={`p-2 rounded-lg border transition-all text-left ${
                      selectedSimulation === type.id
                        ? 'border-brand-primary bg-brand-primary/10 text-brand-primary'
                        : 'border-ui-border hover:border-ui-border-hover hover:bg-ui-background-secondary'
                    }`}
                    title={type.label}
                  >
                    <div className='flex items-center gap-2'>
                      <div
                        className='w-4 h-4 rounded border border-ui-border flex-shrink-0'
                        style={{
                          backgroundColor:
                            simulatedColors[type.id] || selectedColor.hex,
                        }}
                      />
                      <div className='min-w-0'>
                        <div className='text-xs font-medium truncate'>
                          {type.shortLabel}
                        </div>
                        <div className='text-xs text-ui-text-muted truncate'>
                          {type.id === 'protanopia' && 'Red-blind'}
                          {type.id === 'deuteranopia' && 'Green-blind'}
                          {type.id === 'tritanopia' && 'Blue-blind'}
                          {type.id === 'achromatopsia' && 'No color'}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}

                {/* Additional anomaly types */}
                <button
                  onClick={() => setSelectedSimulation('protanomaly')}
                  className={`p-2 rounded-lg border transition-all text-left ${
                    selectedSimulation === 'protanomaly'
                      ? 'border-brand-primary bg-brand-primary/10 text-brand-primary'
                      : 'border-ui-border hover:border-ui-border-hover hover:bg-ui-background-secondary'
                  }`}
                  title='Protanomaly (Red-Weak)'
                >
                  <div className='flex items-center gap-2'>
                    <div
                      className='w-4 h-4 rounded border border-ui-border flex-shrink-0'
                      style={{
                        backgroundColor:
                          simulatedColors['protanomaly'] || selectedColor.hex,
                      }}
                    />
                    <div className='min-w-0'>
                      <div className='text-xs font-medium truncate'>
                        Red-Weak
                      </div>
                      <div className='text-xs text-ui-text-muted truncate'>
                        Protanomaly
                      </div>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => setSelectedSimulation('deuteranomaly')}
                  className={`p-2 rounded-lg border transition-all text-left ${
                    selectedSimulation === 'deuteranomaly'
                      ? 'border-brand-primary bg-brand-primary/10 text-brand-primary'
                      : 'border-ui-border hover:border-ui-border-hover hover:bg-ui-background-secondary'
                  }`}
                  title='Deuteranomaly (Green-Weak)'
                >
                  <div className='flex items-center gap-2'>
                    <div
                      className='w-4 h-4 rounded border border-ui-border flex-shrink-0'
                      style={{
                        backgroundColor:
                          simulatedColors['deuteranomaly'] || selectedColor.hex,
                      }}
                    />
                    <div className='min-w-0'>
                      <div className='text-xs font-medium truncate'>
                        Green-Weak
                      </div>
                      <div className='text-xs text-ui-text-muted truncate'>
                        Deuteranomaly
                      </div>
                    </div>
                  </div>
                </button>
              </div>
            </div>

            {/* Main Simulation Display */}
            <div className='flex-1 flex flex-col'>
              {/* Color Comparison */}
              <div className='flex gap-2 mb-2'>
                {/* Original */}
                <div className='flex-1'>
                  <div className='text-xs text-ui-text-muted mb-1'>
                    Original
                  </div>
                  <div
                    className='h-20 rounded-lg border border-ui-border shadow-sm'
                    style={{ backgroundColor: selectedColor.hex }}
                  />
                  <div className='text-xs mt-1 font-mono text-center'>
                    {selectedColor.hex}
                  </div>
                </div>

                {/* Simulated */}
                <div className='flex-1'>
                  <div className='text-xs text-ui-text-muted mb-1'>
                    As Seen By
                  </div>
                  <div
                    className='h-20 rounded-lg border border-ui-border shadow-sm'
                    style={{
                      backgroundColor:
                        simulatedColors[selectedSimulation] ||
                        selectedColor.hex,
                    }}
                  />
                  <div className='text-xs mt-1 font-mono text-center'>
                    {simulatedColors[selectedSimulation] || selectedColor.hex}
                  </div>
                </div>
              </div>

              {/* Preview on Background */}
              <div className='flex-1 rounded-lg border border-ui-border overflow-hidden mb-2'>
                <div
                  className='h-full p-4'
                  style={{ backgroundColor: currentBg.color }}
                >
                  <div className='grid grid-cols-2 gap-3 h-full'>
                    <div>
                      <h5
                        className='text-xs font-medium mb-2'
                        style={{ color: selectedColor.hex }}
                      >
                        Normal Vision
                      </h5>
                      <p
                        className='text-xs leading-relaxed'
                        style={{ color: selectedColor.hex }}
                      >
                        This is how the color appears to people with normal
                        color vision.
                      </p>
                    </div>
                    <div>
                      <h5
                        className='text-xs font-medium mb-2'
                        style={{
                          color:
                            simulatedColors[selectedSimulation] ||
                            selectedColor.hex,
                        }}
                      >
                        {selectedSimulation.charAt(0).toUpperCase() +
                          selectedSimulation.slice(1)}
                      </h5>
                      <p
                        className='text-xs leading-relaxed'
                        style={{
                          color:
                            simulatedColors[selectedSimulation] ||
                            selectedColor.hex,
                        }}
                      >
                        This is the simulated appearance for color blind users.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Prevalence Info */}
              <div
                className='p-2 text-xs'
                style={{
                  backgroundColor: 'var(--color-ui-background-primary)',
                  borderRadius: 'var(--radius-lg)',
                  fontSize: 'var(--font-size-xs)',
                }}
              >
                <div className='flex items-center justify-between mb-1'>
                  <span
                    className='font-medium'
                    style={{
                      fontWeight: 'var(--font-weight-medium)',
                      color: 'var(--color-ui-foreground-primary)',
                    }}
                  >
                    {
                      getColorBlindnessDescription(selectedSimulation).split(
                        ' - '
                      )[0]
                    }
                  </span>
                  <span
                    style={{ color: 'var(--color-ui-foreground-tertiary)' }}
                  >
                    {prevalence.male.toFixed(1)}% ♂ /{' '}
                    {prevalence.female.toFixed(2)}% ♀
                  </span>
                </div>
                <p
                  className='text-xs'
                  style={{
                    color: 'var(--color-ui-foreground-tertiary)',
                    fontSize: 'var(--font-size-xs)',
                  }}
                >
                  {
                    getColorBlindnessDescription(selectedSimulation).split(
                      ' - '
                    )[1]
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

AccessibilityTab.displayName = 'AccessibilityTab';
