/**
 * @file sync-shutdown-coordinator.ts
 * @description Coordinates the shutdown of all sync components in the correct order
 */

import { resourceManager } from './resource-manager';
import { syncOutboxService } from './sync-outbox.service';
import { syncStatusManager } from './sync-status-manager.service';
import { fileConcurrencyController } from './file-concurrency-controller';
import { unifiedSyncManager } from './unified-sync-manager';

export interface ShutdownResult {
  success: boolean;
  componentsShutdown: string[];
  errors: string[];
  duration: number;
}

export interface ShutdownOptions {
  gracefulTimeout?: number; // Time to wait for graceful shutdown
  forceShutdown?: boolean;  // Force shutdown even if operations are in progress
}

/**
 * Coordinates the shutdown of all sync components
 */
export class SyncShutdownCoordinator {
  private static instance: SyncShutdownCoordinator | null = null;
  private isShuttingDown = false;
  private shutdownPromise: Promise<ShutdownResult> | null = null;
  
  private constructor() {}
  
  static getInstance(): SyncShutdownCoordinator {
    if (!SyncShutdownCoordinator.instance) {
      SyncShutdownCoordinator.instance = new SyncShutdownCoordinator();
    }
    return SyncShutdownCoordinator.instance;
  }
  
  /**
   * Perform coordinated shutdown of all sync components
   */
  async shutdown(options: ShutdownOptions = {}): Promise<ShutdownResult> {
    if (this.isShuttingDown) {
      // Return existing shutdown promise if already shutting down
      if (this.shutdownPromise) {
        return await this.shutdownPromise;
      }
    }
    
    this.isShuttingDown = true;
    const startTime = Date.now();
    const componentsShutdown: string[] = [];
    const errors: string[] = [];
    
    const {
      gracefulTimeout = 30000, // 30 seconds default
      forceShutdown = false
    } = options;
    
    console.log('[SyncShutdown] 🛑 Starting coordinated sync shutdown...');
    console.log('[SyncShutdown] ⚙️ Options:', { gracefulTimeout, forceShutdown });
    
    this.shutdownPromise = this.performShutdown(gracefulTimeout, forceShutdown, componentsShutdown, errors);
    
    const result = await this.shutdownPromise;
    result.duration = Date.now() - startTime;
    
    console.log(`[SyncShutdown] ${result.success ? '✅' : '❌'} Shutdown completed in ${result.duration}ms`);
    console.log(`[SyncShutdown] 📊 Components shutdown: ${result.componentsShutdown.length}`);
    if (result.errors.length > 0) {
      console.warn(`[SyncShutdown] ⚠️ Shutdown errors: ${result.errors.length}`, result.errors);
    }
    
    return result;
  }
  
  private async performShutdown(
    gracefulTimeout: number,
    forceShutdown: boolean,
    componentsShutdown: string[],
    errors: string[]
  ): Promise<ShutdownResult> {
    
    // Step 1: Stop accepting new sync operations
    try {
      console.log('[SyncShutdown] 🚫 Stopping new sync operations...');
      if (unifiedSyncManager) {
        // The unified sync manager doesn't have a direct "stop" method,
        // but we can wait for current operations to complete
        const status = unifiedSyncManager.getStatus();
        if (status.isSyncing) {
          console.log('[SyncShutdown] ⏳ Waiting for current sync operation to complete...');
          
          if (!forceShutdown) {
            // Wait for current sync to complete with timeout
            const waitPromise = this.waitForSyncCompletion(gracefulTimeout);
            const timeoutPromise = new Promise<void>((_, reject) => 
              setTimeout(() => reject(new Error('Sync completion timeout')), gracefulTimeout)
            );
            
            try {
              await Promise.race([waitPromise, timeoutPromise]);
              console.log('[SyncShutdown] ✅ Current sync operation completed');
            } catch (error) {
              console.warn('[SyncShutdown] ⚠️ Sync completion timeout, proceeding with shutdown');
              errors.push('Sync completion timeout');
            }
          }
        }
      }
      componentsShutdown.push('sync-operations');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[SyncShutdown] ❌ Failed to stop sync operations:', error);
      errors.push(`Sync operations stop failed: ${errorMessage}`);
    }
    
    // Step 2: Shutdown sync status manager (stop polling)
    try {
      console.log('[SyncShutdown] 📊 Shutting down sync status manager...');
      if (syncStatusManager && typeof syncStatusManager.destroy === 'function') {
        syncStatusManager.destroy();
        componentsShutdown.push('sync-status-manager');
        console.log('[SyncShutdown] ✅ Sync status manager shutdown');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[SyncShutdown] ❌ Failed to shutdown sync status manager:', error);
      errors.push(`Sync status manager shutdown failed: ${errorMessage}`);
    }
    
    // Step 3: Shutdown sync outbox service (stop periodic cleanup)
    try {
      console.log('[SyncShutdown] 📤 Shutting down sync outbox service...');
      if (syncOutboxService && typeof syncOutboxService.stopPeriodicCleanup === 'function') {
        syncOutboxService.stopPeriodicCleanup();
        componentsShutdown.push('sync-outbox-service');
        console.log('[SyncShutdown] ✅ Sync outbox service shutdown');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[SyncShutdown] ❌ Failed to shutdown sync outbox service:', error);
      errors.push(`Sync outbox service shutdown failed: ${errorMessage}`);
    }
    
    // Step 4: Shutdown file concurrency controller
    try {
      console.log('[SyncShutdown] 🔐 Shutting down file concurrency controller...');
      if (fileConcurrencyController && typeof fileConcurrencyController.cleanup === 'function') {
        await fileConcurrencyController.cleanup();
        componentsShutdown.push('file-concurrency-controller');
        console.log('[SyncShutdown] ✅ File concurrency controller shutdown');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[SyncShutdown] ❌ Failed to shutdown file concurrency controller:', error);
      errors.push(`File concurrency controller shutdown failed: ${errorMessage}`);
    }
    
    // Step 5: Shutdown unified sync manager
    try {
      console.log('[SyncShutdown] 🔄 Shutting down unified sync manager...');
      if (unifiedSyncManager && typeof unifiedSyncManager.cleanup === 'function') {
        await unifiedSyncManager.cleanup();
        componentsShutdown.push('unified-sync-manager');
        console.log('[SyncShutdown] ✅ Unified sync manager shutdown');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[SyncShutdown] ❌ Failed to shutdown unified sync manager:', error);
      errors.push(`Unified sync manager shutdown failed: ${errorMessage}`);
    }
    
    // Step 6: Final resource cleanup
    try {
      console.log('[SyncShutdown] 🧹 Performing final resource cleanup...');
      const cleanupResult = await resourceManager.cleanup();
      
      if (cleanupResult.success) {
        componentsShutdown.push('resource-manager');
        console.log(`[SyncShutdown] ✅ Resource cleanup completed: ${cleanupResult.resourcesCleanedUp} resources`);
      } else {
        errors.push(...cleanupResult.errors);
        console.warn('[SyncShutdown] ⚠️ Resource cleanup had errors:', cleanupResult.errors);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[SyncShutdown] ❌ Failed to perform resource cleanup:', error);
      errors.push(`Resource cleanup failed: ${errorMessage}`);
    }
    
    return {
      success: errors.length === 0,
      componentsShutdown,
      errors,
      duration: 0 // Will be set by caller
    };
  }
  
  /**
   * Wait for current sync operation to complete
   */
  private async waitForSyncCompletion(timeout: number): Promise<void> {
    return new Promise<void>((resolve) => {
      const checkInterval = 500; // Check every 500ms
      let elapsed = 0;
      
      const check = () => {
        if (!unifiedSyncManager) {
          resolve();
          return;
        }
        
        const status = unifiedSyncManager.getStatus();
        if (!status.isSyncing) {
          resolve();
          return;
        }
        
        elapsed += checkInterval;
        if (elapsed >= timeout) {
          resolve(); // Timeout reached, resolve anyway
          return;
        }
        
        setTimeout(check, checkInterval);
      };
      
      check();
    });
  }
  
  /**
   * Check if shutdown is in progress
   */
  isShuttingDownState(): boolean {
    return this.isShuttingDown;
  }
  
  /**
   * Reset shutdown state (for testing)
   */
  reset(): void {
    this.isShuttingDown = false;
    this.shutdownPromise = null;
  }
}

// Export singleton instance
export const syncShutdownCoordinator = SyncShutdownCoordinator.getInstance();