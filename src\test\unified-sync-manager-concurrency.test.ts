/**
 * @file unified-sync-manager-concurrency.test.ts
 * @description Integration tests for UnifiedSyncManager with file-based concurrency control
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import { UnifiedSyncManager } from '../main/services/sync/unified-sync-manager';
import { fileConcurrencyController } from '../main/services/sync/file-concurrency-controller';

// Mock electron modules
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-chromasync'),
    getVersion: vi.fn(() => '1.0.0')
  },
  ipcMain: {
    handle: vi.fn(),
    on: vi.fn()
  },
  BrowserWindow: vi.fn()
}));

// Mock main window
vi.mock('../../main/index', () => ({
  mainWindow: {
    webContents: {
      send: vi.fn()
    }
  }
}));

// Mock supabase client
vi.mock('../main/services/supabase-client', () => ({
  ensureAuthenticatedSession: vi.fn().mockResolvedValue({
    session: { user: { id: 'test-user' } }
  })
}));

// Mock sync error recovery
vi.mock('../main/services/sync/sync-error-recovery', () => ({
  handleSyncError: vi.fn().mockResolvedValue({
    message: 'Error handled'
  })
}));

// Mock sync outbox service
vi.mock('../main/services/sync/sync-outbox.service', () => ({
  syncOutboxService: {
    getPendingChanges: vi.fn().mockReturnValue([])
  }
}));

// Mock transaction aware sync manager
vi.mock('../main/services/sync/transaction-aware-sync', () => ({
  transactionAwareSyncManager: {
    processOutboxInTransaction: vi.fn().mockResolvedValue({
      itemsProcessed: 0,
      errors: []
    }),
    executeSyncInTransaction: vi.fn().mockResolvedValue({
      success: true,
      result: { processedCount: 0, errors: [] }
    })
  }
}));

// Mock service locator
vi.mock('../main/services/service-locator', () => ({
  ServiceLocator: {
    getColorService: vi.fn().mockReturnValue({
      getUnsynced: vi.fn().mockResolvedValue([]),
      pushColorToSupabase: vi.fn().mockResolvedValue(undefined),
      syncColorsFromSupabase: vi.fn().mockResolvedValue([])
    }),
    getProductService: vi.fn().mockReturnValue({
      getUnsynced: vi.fn().mockResolvedValue([]),
      pushProductToSupabase: vi.fn().mockResolvedValue(undefined),
      syncProductsFromSupabase: vi.fn().mockResolvedValue([]),
      syncProductColorsFromSupabase: vi.fn().mockResolvedValue({ syncedCount: 0 })
    }),
    getOrganizationService: vi.fn().mockReturnValue({
      syncOrganizationsFromSupabase: vi.fn().mockResolvedValue(undefined)
    }),
    getDatabase: vi.fn().mockReturnValue({})
  }
}));

// Mock user operation coordinator
vi.mock('../main/services/user-operation-coordinator.service', () => ({
  userOperationCoordinator: {
    hasActiveOperations: vi.fn().mockReturnValue(false),
    getActiveOperations: vi.fn().mockReturnValue([]),
    waitForAllOperationsToComplete: vi.fn().mockResolvedValue(true)
  }
}));

// Mock os module
vi.mock('os', () => ({
  hostname: vi.fn(() => 'test-host')
}));

describe('UnifiedSyncManager Concurrency Integration', () => {
  let syncManager: UnifiedSyncManager;
  let testLockDir: string;

  beforeEach(async () => {
    // Create unique test directory
    testLockDir = path.join('/tmp', `test-sync-locks-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
    
    // Mock app.getPath to return our test directory
    vi.mocked(app.getPath).mockReturnValue(path.dirname(testLockDir));
    
    // Create test directory
    if (!fs.existsSync(testLockDir)) {
      fs.mkdirSync(testLockDir, { recursive: true });
    }

    // Get fresh sync manager instance
    syncManager = UnifiedSyncManager.getInstance();
    await syncManager.initialize('test-user', 'test-org');
  });

  afterEach(async () => {
    // Cleanup sync manager
    await syncManager.cleanup();
    
    // Cleanup concurrency controller
    await fileConcurrencyController.cleanup();
    
    // Remove test directory
    if (fs.existsSync(testLockDir)) {
      try {
        fs.rmSync(testLockDir, { recursive: true, force: true });
      } catch (error) {
        console.warn('Failed to cleanup test directory:', error);
      }
    }
  });

  describe('Concurrent Sync Operations', () => {
    it('should prevent concurrent sync operations on same resource', async () => {
      const startTime = Date.now();
      
      // Start two sync operations simultaneously
      const sync1Promise = syncManager.sync('colors', 'bidirectional', 'normal');
      const sync2Promise = syncManager.sync('colors', 'bidirectional', 'normal');
      
      const [result1, result2] = await Promise.all([sync1Promise, sync2Promise]);
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // Both should succeed
      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      
      // Operations should be serialized (not truly concurrent)
      // This is hard to test precisely, but we can check that both completed
      expect(result1.operation.type).toBe('colors');
      expect(result2.operation.type).toBe('colors');
      
      // Total time should be reasonable (not too long due to timeouts)
      expect(totalTime).toBeLessThan(10000); // Less than 10 seconds
    });

    it('should allow concurrent sync operations on different resources', async () => {
      const startTime = Date.now();
      
      // Start sync operations on different resources
      const colorSyncPromise = syncManager.sync('colors', 'bidirectional', 'normal');
      const productSyncPromise = syncManager.sync('products', 'bidirectional', 'normal');
      
      const [colorResult, productResult] = await Promise.all([colorSyncPromise, productSyncPromise]);
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // Both should succeed
      expect(colorResult.success).toBe(true);
      expect(productResult.success).toBe(true);
      
      // Should complete in reasonable time
      expect(totalTime).toBeLessThan(5000); // Less than 5 seconds
    });

    it('should handle sync operation failures gracefully', async () => {
      // Mock service to throw error
      const { ServiceLocator } = await import('../main/services/service-locator');
      vi.mocked(ServiceLocator.getColorService).mockReturnValue({
        getUnsynced: vi.fn().mockRejectedValue(new Error('Database error')),
        pushColorToSupabase: vi.fn().mockRejectedValue(new Error('Network error')),
        syncColorsFromSupabase: vi.fn().mockRejectedValue(new Error('Sync error'))
      });

      const result = await syncManager.sync('colors', 'bidirectional', 'normal');
      
      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors!.length).toBeGreaterThan(0);
    });

    it('should queue operations when sync is in progress', async () => {
      // Start first sync operation
      const firstSyncPromise = syncManager.sync('colors', 'bidirectional', 'normal');
      
      // Immediately start second sync operation (should be queued)
      const secondSyncPromise = syncManager.sync('colors', 'bidirectional', 'high');
      
      const [firstResult, secondResult] = await Promise.all([firstSyncPromise, secondSyncPromise]);
      
      expect(firstResult.success).toBe(true);
      expect(secondResult.success).toBe(true);
      
      // Second operation should have higher priority but still complete
      expect(secondResult.operation.priority).toBe('high');
    });
  });

  describe('Lock Management', () => {
    it('should acquire and release locks properly', async () => {
      const locksBefore = fileConcurrencyController.getLockStatus();
      
      const syncPromise = syncManager.sync('colors', 'bidirectional', 'normal');
      
      // Check that lock is acquired during sync
      // Note: This is timing-dependent and might be flaky
      await new Promise(resolve => setTimeout(resolve, 10)); // Small delay
      
      const result = await syncPromise;
      
      const locksAfter = fileConcurrencyController.getLockStatus();
      
      expect(result.success).toBe(true);
      
      // Locks should be cleaned up after sync
      expect(locksAfter.activeLocks).toBe(locksBefore.activeLocks);
    });

    it('should handle lock acquisition timeout', async () => {
      // Create a long-running lock manually
      const resource = 'sync_colors_test-org';
      const lock = await fileConcurrencyController.acquireLock(resource, 60000);
      
      try {
        // This should timeout quickly since we're using a short timeout in tests
        const result = await syncManager.sync('colors', 'bidirectional', 'normal');
        
        // Should fail due to lock timeout
        expect(result.success).toBe(false);
        expect(result.errors).toBeDefined();
        
      } finally {
        // Clean up the manual lock
        await fileConcurrencyController.releaseLock(lock);
      }
    });
  });

  describe('Multi-Process Simulation', () => {
    it('should handle simulated multi-process scenario', async () => {
      const resource = 'sync_colors_test-org';
      
      // Simulate another process holding a lock
      const lockDir = path.join(path.dirname(testLockDir), 'sync-locks');
      if (!fs.existsSync(lockDir)) {
        fs.mkdirSync(lockDir, { recursive: true });
      }
      
      const lockFilePath = path.join(lockDir, `${resource}.lock`);
      const externalLockMetadata = {
        id: 'external-lock-id',
        resource,
        processId: 99999, // Different process ID
        acquiredAt: Date.now(),
        expiresAt: Date.now() + 1000, // Short expiry
        hostname: 'other-host',
        appVersion: '1.0.0'
      };
      
      fs.writeFileSync(lockFilePath, JSON.stringify(externalLockMetadata, null, 2));
      
      // Wait for lock to expire
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      // Should be able to sync after lock expires
      const result = await syncManager.sync('colors', 'bidirectional', 'normal');
      expect(result.success).toBe(true);
    });

    it('should detect and handle stale locks', async () => {
      const resource = 'sync_products_test-org';
      
      // Create a stale lock file
      const lockDir = path.join(path.dirname(testLockDir), 'sync-locks');
      if (!fs.existsSync(lockDir)) {
        fs.mkdirSync(lockDir, { recursive: true });
      }
      
      const lockFilePath = path.join(lockDir, `${resource}.lock`);
      const staleLockMetadata = {
        id: 'stale-lock-id',
        resource,
        processId: 99999,
        acquiredAt: Date.now() - 120000, // 2 minutes ago
        expiresAt: Date.now() - 60000, // Expired 1 minute ago
        hostname: 'stale-host',
        appVersion: '1.0.0'
      };
      
      fs.writeFileSync(lockFilePath, JSON.stringify(staleLockMetadata, null, 2));
      
      // Should be able to sync despite stale lock
      const result = await syncManager.sync('products', 'bidirectional', 'normal');
      expect(result.success).toBe(true);
      
      // Stale lock file should be cleaned up
      expect(fs.existsSync(lockFilePath)).toBe(false);
    });
  });

  describe('Error Recovery', () => {
    it('should release locks even when sync fails', async () => {
      // Mock service to throw error
      const { ServiceLocator } = await import('../main/services/service-locator');
      vi.mocked(ServiceLocator.getColorService).mockReturnValue({
        getUnsynced: vi.fn().mockRejectedValue(new Error('Critical error')),
        pushColorToSupabase: vi.fn().mockRejectedValue(new Error('Network error')),
        syncColorsFromSupabase: vi.fn().mockRejectedValue(new Error('Sync error'))
      });

      const locksBefore = fileConcurrencyController.getLockStatus();
      
      const result = await syncManager.sync('colors', 'bidirectional', 'normal');
      
      const locksAfter = fileConcurrencyController.getLockStatus();
      
      expect(result.success).toBe(false);
      
      // Locks should still be cleaned up even after failure
      expect(locksAfter.activeLocks).toBe(locksBefore.activeLocks);
    });

    it('should handle lock release failures gracefully', async () => {
      // Mock fileConcurrencyController.releaseLock to throw error
      const originalReleaseLock = fileConcurrencyController.releaseLock;
      vi.spyOn(fileConcurrencyController, 'releaseLock').mockRejectedValue(new Error('Lock release failed'));

      const result = await syncManager.sync('colors', 'bidirectional', 'normal');
      
      // Sync should still complete despite lock release failure
      expect(result.success).toBe(true);
      
      // Restore original method
      fileConcurrencyController.releaseLock = originalReleaseLock;
    });
  });

  describe('Cleanup and Shutdown', () => {
    it('should cleanup all locks on shutdown', async () => {
      // Start a sync operation
      const syncPromise = syncManager.sync('colors', 'bidirectional', 'normal');
      
      // Wait a bit then cleanup
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const cleanupPromise = syncManager.cleanup();
      
      // Both should complete successfully
      const [syncResult] = await Promise.all([syncPromise, cleanupPromise]);
      
      expect(syncResult.success).toBe(true);
      
      // All locks should be cleaned up
      const finalLockStatus = fileConcurrencyController.getLockStatus();
      expect(finalLockStatus.activeLocks).toBe(0);
    });

    it('should wait for current sync to complete before cleanup', async () => {
      const startTime = Date.now();
      
      // Start a sync operation
      const syncPromise = syncManager.sync('colors', 'bidirectional', 'normal');
      
      // Immediately start cleanup
      const cleanupPromise = syncManager.cleanup();
      
      await Promise.all([syncPromise, cleanupPromise]);
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // Should complete in reasonable time
      expect(totalTime).toBeLessThan(5000);
    });
  });
});