#!/usr/bin/env node

/**
 * TypeScript Merge Resolution Validation Script
 * 
 * This script validates TypeScript compilation during merge resolution
 * to ensure incremental changes don't break the build.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkConflictedFiles() {
  log('\n🔍 Checking for remaining merge conflicts...', 'blue');
  
  try {
    const conflictedFiles = execSync('git status --porcelain | findstr "^UU"', { encoding: 'utf8' }).trim();
    
    if (conflictedFiles) {
      log('❌ Merge conflicts still exist:', 'red');
      conflictedFiles.split('\n').forEach(line => {
        const file = line.replace('UU ', '');
        log(`   - ${file}`, 'red');
      });
      return false;
    } else {
      log('✅ No merge conflicts detected', 'green');
      return true;
    }
  } catch (error) {
    log('✅ No merge conflicts detected', 'green');
    return true;
  }
}

function validateTypeScriptCompilation() {
  log('\n🔧 Running TypeScript compilation check...', 'blue');
  
  const tsConfigs = [
    'tsconfig.json',
    'tsconfig.main.json',
    'tsconfig.renderer.json',
    'tsconfig.preload.json'
  ];
  
  let allPassed = true;
  
  for (const config of tsConfigs) {
    if (fs.existsSync(config)) {
      log(`\n📋 Checking ${config}...`, 'cyan');
      
      try {
        execSync(`npx tsc --noEmit --project ${config}`, { 
          stdio: 'pipe',
          encoding: 'utf8'
        });
        log(`✅ ${config} - No TypeScript errors`, 'green');
      } catch (error) {
        log(`❌ ${config} - TypeScript errors found:`, 'red');
        log(error.stdout || error.message, 'red');
        allPassed = false;
      }
    }
  }
  
  return allPassed;
}

function validateEslint() {
  log('\n🔍 Running ESLint validation...', 'blue');
  
  try {
    execSync('npm run lint', { 
      stdio: 'pipe',
      encoding: 'utf8'
    });
    log('✅ ESLint validation passed', 'green');
    return true;
  } catch (error) {
    log('❌ ESLint validation failed:', 'red');
    log(error.stdout || error.message, 'red');
    return false;
  }
}

function validateBuild() {
  log('\n🏗️  Testing build process...', 'blue');
  
  try {
    execSync('npm run build', { 
      stdio: 'pipe',
      encoding: 'utf8'
    });
    log('✅ Build process completed successfully', 'green');
    return true;
  } catch (error) {
    log('❌ Build process failed:', 'red');
    log(error.stdout || error.message, 'red');
    return false;
  }
}

function runQuickTests() {
  log('\n🧪 Running quick test suite...', 'blue');
  
  try {
    execSync('npm test -- --run --reporter=basic', { 
      stdio: 'pipe',
      encoding: 'utf8'
    });
    log('✅ Quick tests passed', 'green');
    return true;
  } catch (error) {
    log('❌ Quick tests failed:', 'red');
    log(error.stdout || error.message, 'red');
    return false;
  }
}

function main() {
  log('🚀 TypeScript Merge Resolution Validation', 'magenta');
  log('==========================================', 'magenta');
  
  const checks = [
    { name: 'Merge Conflicts', fn: checkConflictedFiles, required: true },
    { name: 'TypeScript Compilation', fn: validateTypeScriptCompilation, required: true },
    { name: 'ESLint Validation', fn: validateEslint, required: false },
    { name: 'Build Process', fn: validateBuild, required: false },
    { name: 'Quick Tests', fn: runQuickTests, required: false }
  ];
  
  let allRequired = true;
  let totalPassed = 0;
  
  for (const check of checks) {
    const passed = check.fn();
    if (passed) {
      totalPassed++;
    } else if (check.required) {
      allRequired = false;
    }
  }
  
  log('\n📊 Validation Summary:', 'magenta');
  log(`   Checks passed: ${totalPassed}/${checks.length}`, totalPassed === checks.length ? 'green' : 'yellow');
  
  if (allRequired) {
    log('\n✅ All required validations passed! Merge resolution can proceed.', 'green');
    process.exit(0);
  } else {
    log('\n❌ Required validations failed. Please fix issues before proceeding.', 'red');
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  log('TypeScript Merge Resolution Validation Script', 'cyan');
  log('Usage: node scripts/validate-typescript-merge.js [options]', 'cyan');
  log('Options:', 'cyan');
  log('  --help, -h    Show this help message', 'cyan');
  log('  --conflicts   Check only for merge conflicts', 'cyan');
  log('  --typescript  Check only TypeScript compilation', 'cyan');
  log('  --build       Check only build process', 'cyan');
  process.exit(0);
}

if (args.includes('--conflicts')) {
  checkConflictedFiles() ? process.exit(0) : process.exit(1);
} else if (args.includes('--typescript')) {
  validateTypeScriptCompilation() ? process.exit(0) : process.exit(1);
} else if (args.includes('--build')) {
  validateBuild() ? process.exit(0) : process.exit(1);
} else {
  main();
}