-- Migration 011: Add gradient support columns to colors table
-- This migration adds the missing is_gradient, is_metallic, and is_effect columns

-- Add is_gradient column if it doesn't exist
ALTER TABLE colors ADD COLUMN is_gradient BOOLEAN NOT NULL DEFAULT FALSE;

-- Add is_metallic column if it doesn't exist  
ALTER TABLE colors ADD COLUMN is_metallic BOOLEAN NOT NULL DEFAULT FALSE;

-- Add is_effect column if it doesn't exist
ALTER TABLE colors ADD COLUMN is_effect BOOLEAN NOT NULL DEFAULT FALSE;

-- Create gradient_stops table if it doesn't exist
CREATE TABLE IF NOT EXISTS gradient_stops (
  gradient_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
  stop_index INTEGER NOT NULL,
  position REAL NOT NULL CHECK (position >= 0 AND position <= 1),
  hex CHAR(7) NOT NULL,
  PRIMARY KEY (gradient_id, stop_index)
) WITHOUT ROWID;

-- Create index for gradient stops
CREATE INDEX IF NOT EXISTS idx_gradient_stops_gradient ON gradient_stops(gradient_id);
