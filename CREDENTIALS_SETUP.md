# ChromaSync - Complete Credentials Setup Guide

This document contains all credentials and configuration required to setup ChromaSync on a new system.

## Table of Contents

1. [Environment Variables](#environment-variables)
2. [Third-Party Service Setup](#third-party-service-setup)
3. [Database Configuration](#database-configuration)
4. [OAuth Configuration](#oauth-configuration)
5. [Production Deployment](#production-deployment)
6. [Security Considerations](#security-considerations)

---

## Environment Variables

Create a `.env` file in the project root with the following configuration:

```bash
#================================================================
# ChromaSync Environment Configuration
#================================================================

# Development Mode
NODE_ENV=development

#----------------------------------------------------------------
# Supabase Configuration (Cloud Database & Authentication)
#----------------------------------------------------------------
SUPABASE_URL=https://tzqnxhsnitogmtihhsrq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzODMyMTYsImV4cCI6MjA2Mzk1OTIxNn0.II5OQWN9uplEZLV20CJ1amObYldzZwlaynKMQ4-iIqY
SUPABASE_JWT_SECRET=

#----------------------------------------------------------------
# PowerSync Configuration (Real-time Database Sync)
#----------------------------------------------------------------
ENABLE_POWERSYNC=true
POWERSYNC_URL=https://685e771baad8257e4b385735.powersync.journeyapps.com

#----------------------------------------------------------------
# Google AI Integration (Gemini API)
#----------------------------------------------------------------
GEMINI_API_KEY=AIzaSyCDL7yUzxEYDaxvvC_Te7FrRCbB8OMhYUA

#----------------------------------------------------------------
# OAuth Configuration (Google Sign-In)
#----------------------------------------------------------------
# Note: OAuth is configured in Supabase dashboard with redirect URLs:
# - Development: http://localhost:3000/auth/callback
# - Production: https://chromasync.app/auth/callback

#----------------------------------------------------------------
# Email Service Configuration (Zoho)
#----------------------------------------------------------------
# Optional: Only needed for customer support email automation
ZOHO_CLIENT_ID=your-zoho-client-id
ZOHO_CLIENT_SECRET=your-zoho-client-secret
ZOHO_REFRESH_TOKEN=your-zoho-refresh-token
ZOHO_ACCOUNT_ID=your-zoho-account-id
ZOHO_REGION=EU                              # EU for European accounts, omit for US
ZOHO_SUPPORT_ALIAS=<EMAIL>

#----------------------------------------------------------------
# Development Configuration
#----------------------------------------------------------------
# Database debugging
DEBUG_SQL=false
DEBUG_SYNC=false
DEBUG_AUTH=false

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true

# Build configuration
BUILD_MODE=development
```

---

## Third-Party Service Setup

### 1. Supabase (Database & Authentication)

**Service**: Database, Authentication, Real-time subscriptions  
**Current Project**: `tzqnxhsnitogmtihhsrq`

#### Setup Steps:

1. **Access existing project**: https://supabase.com/dashboard/project/tzqnxhsnitogmtihhsrq
2. **Database Schema**: Already configured with complete schema
3. **Authentication Providers**:
   - Google OAuth enabled
   - Email/password enabled
   - Row Level Security (RLS) enabled

#### Required Database Tables:

- `colors` - Color data with organization scoping
- `products` - Product management
- `product_colors` - Many-to-many relationships
- `organizations` - Multi-tenant organization structure
- `organization_members` - User-organization relationships
- `organization_invitations` - Team invitation system
- `users` - User profiles and preferences

### 2. PowerSync (Real-time Sync)

**Service**: Real-time database synchronization  
**Instance**: `685e771baad8257e4b385735.powersync.journeyapps.com`

#### Configuration:

- **Sync Rules**: Configured for organization-scoped data
- **Supabase Integration**: Connected to main database
- **Schema**: Mirrors Supabase database structure
- **Conflict Resolution**: Last-write-wins with device attribution

### 3. Google AI (Gemini)

**Service**: AI-powered color analysis and recommendations  
**API Key**: `AIzaSyCDL7yUzxEYDaxvvC_Te7FrRCbB8OMhYUA`

#### Features Enabled:

- Color palette generation
- Color harmony analysis
- Smart color recommendations
- Color accessibility checks

### 4. Google OAuth (Authentication)

**Service**: User authentication via Google Sign-In  
**Configuration**: Managed through Supabase dashboard

#### OAuth Settings:

- **Authorized Redirect URIs**:
  - `http://localhost:3000/auth/callback` (Development)
  - `https://chromasync.app/auth/callback` (Production)
- **Scopes**: `openid`, `email`, `profile`
- **Flow Type**: PKCE (Proof Key for Code Exchange)

### 5. Zoho Mail (Optional - Email Service)

**Service**: Customer support email automation  
**Region**: EU (European data residency)

#### Setup Required:

1. **Create Zoho OAuth App**: https://api-console.zoho.eu (EU region)
2. **Scopes Needed**: `ZohoMail.messages.CREATE`, `ZohoMail.accounts.READ`
3. **Generate Refresh Token**: Use OAuth 2.0 flow
4. **Configure Domain**: Add `chromasync.app` domain verification

---

## Database Configuration

### Local SQLite Database

- **File Location**: `~/Library/Application Support/chroma-sync/chromasync.db` (macOS)
- **Schema**: Auto-created through migration system
- **Backup**: Automatic daily backups to `~/Library/Application Support/chroma-sync/backups/`

### Migration System

```bash
# Initialize fresh database
npm run migrate:reset

# Run pending migrations
npm run migrate:up

# Check migration status
npm run migrate:status
```

### Database Schema Key Points:

- **Organization Scoping**: All data filtered by `organization_id`
- **Soft Deletes**: Uses `deleted_at` timestamps
- **Audit Logging**: Automatic `created_at`, `updated_at` tracking
- **UUID Primary Keys**: For cloud sync compatibility
- **Denormalized Design**: Optimized for performance over normalization

---

## OAuth Configuration

### Google Sign-In Setup

#### Development Configuration:

```javascript
// Supabase Dashboard → Authentication → Providers → Google
{
  "clientId": "your-google-client-id.apps.googleusercontent.com",
  "clientSecret": "your-google-client-secret",
  "redirectUrl": "http://localhost:3000/auth/callback"
}
```

#### Production Configuration:

```javascript
{
  "clientId": "your-google-client-id.apps.googleusercontent.com",
  "clientSecret": "your-google-client-secret",
  "redirectUrl": "https://chromasync.app/auth/callback"
}
```

### Security Features:

- **PKCE Flow**: Prevents authorization code interception
- **Hardware Encryption**: Tokens stored using Electron's safeStorage
- **Session Management**: Auto-refresh with secure token rotation
- **GDPR Compliance**: Explicit consent for EU users

---

## Production Deployment

### Build Configuration

```bash
# Set production environment
export NODE_ENV=production
export BUILD_MODE=production

# Build with credentials
npm run build

# Create installers with code signing
npm run package
```

### Production Environment Variables

```bash
# Production overrides
NODE_ENV=production
BUILD_MODE=production

# Production Supabase (same as development)
SUPABASE_URL=https://tzqnxhsnitogmtihhsrq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzODMyMTYsImV4cCI6MjA2Mzk1OTIxNn0.II5OQWN9uplEZLV20CJ1amObYldzZwlaynKMQ4-iIqY

# Production PowerSync
POWERSYNC_URL=https://685e771baad8257e4b385735.powersync.journeyapps.com

# Production AI
GEMINI_API_KEY=AIzaSyCDL7yUzxEYDaxvvC_Te7FrRCbB8OMhYUA
```

### App Signing (macOS)

```bash
# Required for macOS distribution
export CSC_LINK="path/to/certificate.p12"
export CSC_KEY_PASSWORD="certificate-password"

# Apple ID for notarization
export APPLE_ID="<EMAIL>"
export APPLE_ID_PASS="app-specific-password"
export APPLE_TEAM_ID="team-id"
```

### Windows Code Signing

```bash
# Required for Windows distribution
export WIN_CSC_LINK="path/to/certificate.p12"
export WIN_CSC_KEY_PASSWORD="certificate-password"
```

---

## Security Considerations

### API Key Security

- **Environment Variables**: Never commit `.env` files to version control
- **Production Builds**: Credentials embedded in `app-config.json` during build
- **Token Storage**: Uses Electron's hardware-backed encryption
- **Key Rotation**: Supabase keys can be rotated without app updates

### Database Security

- **Row Level Security**: Enabled on all Supabase tables
- **Organization Isolation**: Multi-tenant data separation enforced at database level
- **SQL Injection Prevention**: All queries use parameterized statements
- **Audit Logging**: All database operations logged with user attribution

### Network Security

- **HTTPS Only**: All external API calls use TLS
- **Certificate Pinning**: Supabase and PowerSync connections verified
- **CSP Headers**: Content Security Policy prevents code injection
- **OAuth PKCE**: Prevents authorization code interception attacks

---

## Quick Setup Commands

### 1. Clone and Install

```bash
git clone https://github.com/Tw1sts/chromasync-mac.git
cd chromasync-mac
npm install
```

### 2. Configure Environment

```bash
# Copy this CREDENTIALS_SETUP.md environment section to .env
cp CREDENTIALS_SETUP.md .env
# Edit .env to add any missing credentials
```

### 3. Initialize Database

```bash
# Initialize local database
npm run migrate:reset
npm run migrate:up
```

### 4. Verify Setup

```bash
# Run tests to verify configuration
npm test

# Start development server
npm run dev
```

### 5. Build for Production

```bash
# Build application
npm run build

# Create installer (platform-specific)
npm run package:mac    # macOS
npm run package:win    # Windows
npm run package:linux  # Linux
```

---

## Troubleshooting

### Missing Credentials

```bash
# Check current configuration
npm run check:config

# Verify environment variables
npm run verify:env

# Test database connection
npm run test:db

# Test external APIs
npm run test:apis
```

### Service Connectivity

```bash
# Test Supabase connection
npm run test:supabase

# Test PowerSync connection
npm run test:powersync

# Test Google AI API
npm run test:gemini
```

### Authentication Issues

```bash
# Clear authentication cache
npm run clear:auth

# Reset OAuth configuration
npm run reset:oauth

# Test authentication flow
npm run test:auth
```

---

## Support

For setup assistance:

- **Documentation**: See [QUICK_START.md](./QUICK_START.md) for user-friendly setup
- **Troubleshooting**: Check [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) for common issues
- **Developer Guide**: Read [DEVELOPER_GUIDE.md](./DEVELOPER_GUIDE.md) for comprehensive development info
- **GitHub Issues**: https://github.com/Tw1sts/chromasync-mac/issues

---

_This document contains the complete credential set for ChromaSync. Keep this information secure and do not commit to public repositories._
