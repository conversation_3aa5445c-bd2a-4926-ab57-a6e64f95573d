/**
 * @file color.types.ts
 * @description Type definitions for color data with compatibility layer for migration
 */

// ===== NEW OPTIMIZED TYPES =====

// Color source types
export interface ColorSource {
  id: number;
  code: string;
  name: string;
  is_system: boolean;
  properties?: Record<string, any>;
}

// Main color interface
export interface Color {
  id: number;
  external_id: string;
  source_id: number;
  organization_id: string; // Added for multi-tenant support
  user_id?: string; // Optional - for audit trail
  code: string;
  display_name?: string;
  hex: string;
  is_gradient: boolean;
  is_metallic: boolean;
  is_effect: boolean;
  is_active: boolean;
  properties?: Record<string, any>;
  search_terms?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// Color space interfaces
export interface ColorCMYK {
  color_id: number;
  c: number; // 0-100
  m: number; // 0-100
  y: number; // 0-100
  k: number; // 0-100
}

export interface ColorRGB {
  color_id: number;
  r: number; // 0-255
  g: number; // 0-255
  b: number; // 0-255
}

export interface ColorLAB {
  color_id: number;
  l: number; // 0-100
  a: number; // -128 to 127
  b: number; // -128 to 127
  illuminant: string;
  observer: string;
}

export interface ColorHSL {
  color_id: number;
  h: number; // 0-360
  s: number; // 0-100
  l: number; // 0-100
}

// Complete color with all spaces
export interface ColorWithSpaces extends Color {
  source?: ColorSource;
  cmyk?: ColorCMYK;
  rgb?: ColorRGB;
  lab?: ColorLAB;
  hsl?: ColorHSL;
  gradient_stops?: GradientStopNew[];
}

// Gradient stop interface (new schema)
export interface GradientStopNew {
  gradient_id: number;
  stop_index: number;
  position: number; // 0.0 to 1.0
  hex: string;
}

// Product color junction
export interface ProductColor {
  product_id: number;
  color_id: number;
  display_order: number;
  usage_type: 'standard' | 'primary' | 'accent' | 'variant';
  quantity?: number;
  metadata?: Record<string, any>;
  added_at: string;
}

// Color delta for comparisons
export interface ColorDelta {
  color_a_id: number;
  color_b_id: number;
  delta_cie76?: number;
  delta_cie94?: number;
  delta_cie2000?: number;
  perception: 'imperceptible' | 'barely_perceptible' | 'perceptible' | 'noticeable' | 'different';
  calculated_at: string;
}

// ===== COMPATIBILITY LAYER (OLD TYPES) =====

// Define gradient stop interface (old format)
export interface GradientStop {
  color: string;
  position: number;
  cmyk?: string; // CMYK values for print production
  colorCode?: string | null; // Optional color code (RAL, Pantone, etc.)
}

// Define gradient information interface (simplified reference format)
export interface GradientInfo {
  colors: string[]; // Array of hex colors in order (positions are automatically equal)
  colorCodes?: string[]; // Optional color codes corresponding to each color
  type?: 'linear'; // Always linear for simplicity
  angle?: number; // Always 45deg for consistency
}

// Runtime color space types for efficient conversion
export interface RGB {
  r: number; // 0-255
  g: number; // 0-255
  b: number; // 0-255
}

export interface HSL {
  h: number; // 0-360
  s: number; // 0-100
  l: number; // 0-100
}

export interface LAB {
  l: number; // 0-100
  a: number; // -128 to 127
  b: number; // -128 to 127
}

// ColorEntry interface with generic terms and runtime color spaces
export interface ColorEntry {
  id: string;  // Will map to external_id
  source?: string;  // Color source code (user, pantone, ral, etc.)
  product: string;
  organizationId?: string;  // Added for multi-tenant support
  userId?: string;  // Optional - for audit trail
  name: string;  // Previously "flavour" - display name for the color
  code: string;  // Previously "pantone" - color code/identifier
  hex: string;
  cmyk: string;  // Will be converted from ColorCMYK
  // Runtime-computed color spaces (no database storage)
  rgb?: RGB;
  hsl?: HSL;
  lab?: LAB;
  notes?: string;
  tags?: string;  // User-defined tags
  gradient?: GradientInfo;
  isLibrary?: boolean;
  createdAt: string;
  updatedAt: string;
}

export type NewColorEntry = Omit<ColorEntry, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateColorEntry = Partial<Omit<ColorEntry, 'id' | 'createdAt' | 'updatedAt'>>;

// IPC Channel names (unchanged)
export enum ColorChannels {
  GET_ALL = 'color:getAll',
  GET_ALL_WITH_USAGE = 'color:getAllWithUsage',
  GET_BY_ID = 'color:getById',
  ADD = 'color:add',
  UPDATE = 'color:update',
  DELETE = 'color:delete',
  CLEAR_ALL = 'color:clearAll',
  IMPORT = 'color:import',
  EXPORT = 'color:export',
  GET_USAGE_COUNTS = 'color:getUsageCounts',
  GET_PRODUCTS_BY_COLOR_NAME = 'color:getProductsByColorName',
  NORMALIZE_PANTONE_CODES = 'color:normalizePantoneCodes',
  CLEAR_FRONTEND_STATE = 'color:clearFrontendState',
  ADMIN_CLEAR_ALL = 'color:adminClearAll'
}

/**
 * Response from getAllWithUsage API call
 */
export interface ColorWithUsageResponse {
  colors: ColorEntry[];
  usageCounts: Record<string, { count: number; products: string[] }>;
  organizationId: string;
  totalColors: number;
  colorsWithUsage: number;
}

/**
 * API response wrapper for color operations
 */
export interface ColorApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

// ===== CONVERSION HELPERS =====

// Helper type to convert between old and new formats
export interface ColorConversion {
  toOptimized(entry: ColorEntry): ColorWithSpaces;
  fromOptimized(color: ColorWithSpaces): ColorEntry;
}
