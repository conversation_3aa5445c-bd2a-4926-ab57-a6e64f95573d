/**
 * @file AppToolbar.tsx
 * @description Application toolbar with search, view tabs, and action controls
 */

import React from 'react';
import SearchBar from '../SearchBar';
import ViewTabs from '../ViewTabs';
import { ColorCompareButton } from '../ColorComparison';
import { TableControls } from './TableControls';
import { useColorStore } from '../../store/color.store';

/**
 * Application toolbar component
 */
export const AppToolbar: React.FC = () => {
  const { searchQuery, setSearchQuery, viewMode, setViewMode } =
    useColorStore();

  return (
    <div
      className='flex flex-wrap items-center gap-[var(--spacing-4)] mb-[var(--spacing-6)] bg-[var(--color-ui-background-secondary)] p-[var(--spacing-3)] rounded-[var(--radius-lg)]'
      style={{
        boxShadow: 'var(--shadow-sm)',
        backgroundColor: 'var(--color-ui-background-secondary)',
        transition:
          'background-color var(--transition-duration-300) var(--transition-easing-apple), box-shadow var(--transition-duration-300) var(--transition-easing-apple)',
      }}
    >
      {/* Search bar */}
      <div className='flex-grow min-w-[200px]'>
        <SearchBar
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          viewMode={viewMode}
        />
      </div>

      {/* View tabs */}
      <div className='flex-shrink-0'>
        <ViewTabs activeView={viewMode} onViewChange={setViewMode} />
      </div>

      {/* Action controls */}
      <div className='flex-shrink-0 ml-auto flex items-center gap-2'>
        {/* Color comparison button */}
        <ColorCompareButton />

        {/* Table controls (sync, theme, menu) */}
        <TableControls />
      </div>
    </div>
  );
};

export default AppToolbar;
