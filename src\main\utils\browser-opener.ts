/**
 * @file browser-opener.ts
 * @description Secure utility functions for opening URLs in the default browser with command injection protection
 */

import { <PERSON>rows<PERSON>Window, shell } from 'electron';
import * as childProcess from 'child_process';
import { defaultURLValidator } from './url-validator';

/**
 * Additional dangerous patterns specific to command injection in URLs
 */
const COMMAND_INJECTION_PATTERNS = [
  /[;&|`$(){}[\]]/, // Shell metacharacters
  /\n|\r/, // Newlines
  /\s--/, // Command flags
  /\s-[a-zA-Z]/, // Single letter flags
  /file:\/\//i, // File protocol
  /\\x[0-9a-fA-F]{2}/, // Hex encoded characters
  /%(0|1|2)[0-9a-fA-F]/, // URL encoded control characters
];

/**
 * Validates URL for command injection vulnerabilities
 * @param url The URL to validate
 * @returns True if the URL is safe from command injection
 */
function isSecureForCommand(url: string): boolean {
  // Use comprehensive URL validation first
  const result = defaultURLValidator.validateURL(url);
  if (!result.isValid) {
    return false;
  }

  // Additional check for command injection patterns
  for (const pattern of COMMAND_INJECTION_PATTERNS) {
    if (pattern.test(url)) {
      console.warn(
        `Blocked URL with command injection pattern: ${pattern.source}`
      );
      return false;
    }
  }

  return true;
}

/**
 * Sanitizes a URL by validating and cleaning it
 * @param url The URL to sanitize
 * @returns Sanitized URL or null if invalid
 */
function sanitizeUrl(url: string): string | null {
  if (!url || typeof url !== 'string') {
    return null;
  }

  // Use comprehensive URL validation and sanitization
  const result = defaultURLValidator.validateURL(url);

  if (!result.isValid) {
    console.warn(`URL validation failed: ${result.error}`);
    return null;
  }

  // Additional command injection security check
  if (!isSecureForCommand(result.sanitizedUrl!)) {
    console.warn('URL failed command injection security check');
    return null;
  }

  return result.sanitizedUrl!;
}

/**
 * Checks if a URL is a SharePoint or OneDrive URL
 * @param url The URL to check
 * @returns True if the URL is a SharePoint or OneDrive URL
 */
export function isSharePointOrOneDriveUrl(url: string): boolean {
  // First validate the URL is safe using comprehensive validation
  const result = defaultURLValidator.validateURL(url);
  if (!result.isValid) {
    return false;
  }

  return url.includes('sharepoint.com') || url.includes('onedrive.com');
}

/**
 * Opens a URL in the default browser using a more reliable method for SharePoint URLs
 * @param url The URL to open
 * @returns Promise resolving to true if successful, false otherwise
 */
export async function openSharePointUrl(url: string): Promise<boolean> {
  return new Promise<boolean>(resolve => {
    try {
      // Validate and sanitize the URL first
      const sanitizedUrl = sanitizeUrl(url);
      if (!sanitizedUrl) {
        console.error(`Invalid or unsafe SharePoint URL: ${url}`);
        resolve(false);
        return;
      }

      console.log(`Opening SharePoint URL: ${sanitizedUrl}`);

      // Get the current focused window to restore focus later
      const focusedWindow = BrowserWindow.getFocusedWindow();
      const windowId = focusedWindow?.id;

      // Use the platform-specific approach for better reliability
      const platform = process.platform;
      let command: string;
      let args: string[] = [];

      if (platform === 'win32') {
        // Windows - use start command which is more reliable for SharePoint URLs
        // Note: Using 'start' with empty string for window title to prevent injection
        command = 'cmd.exe';
        args = ['/c', 'start', '""', sanitizedUrl];
      } else if (platform === 'darwin') {
        // macOS - open command is relatively safe as it validates URLs
        command = 'open';
        args = [sanitizedUrl];
      } else {
        // Linux and others - xdg-open with validation
        command = 'xdg-open';
        args = [sanitizedUrl];
      }

      // Additional security: verify command arguments don't contain injection attempts
      const allArgs = [command, ...args].join(' ');
      if (!isSecureForCommand(allArgs)) {
        console.error('Command injection attempt detected in browser opener');
        // Fall back to secure shell method
        shell
          .openExternal(sanitizedUrl, { activate: true })
          .then(() => resolve(true))
          .catch(() => resolve(false));
        return;
      }

      // Execute the command with additional security options
      const childProc = childProcess.spawn(command, args, {
        detached: true,
        stdio: 'ignore',
        shell: false, // Prevent shell interpretation
        windowsHide: true, // Hide command window on Windows
      });

      childProc.on('error', err => {
        console.error(`Error opening SharePoint URL with ${command}:`, err);

        // Fall back to shell.openExternal if the command fails (using sanitized URL)
        shell
          .openExternal(sanitizedUrl, { activate: true })
          .then(() => {
            console.log(
              'Opened SharePoint URL with shell.openExternal as fallback'
            );
            resolve(true);
          })
          .catch(shellError => {
            console.error(
              'Error opening SharePoint URL with fallback method:',
              shellError
            );
            resolve(false);
          });
      });

      // Unref the process to allow the Node.js process to exit
      childProc.unref();

      // Log success
      console.log(`Launched browser for SharePoint URL using ${command}`);

      // Restore focus to the app window after a delay
      setTimeout(() => {
        try {
          if (windowId !== undefined) {
            const win = BrowserWindow.fromId(windowId);
            if (win && !win.isDestroyed()) {
              if (win.isMinimized()) {
                win.restore();
              }
              win.focus();
              console.log(
                'Restored focus to application window after opening SharePoint URL'
              );
            }
          }
        } catch (focusError) {
          console.error(
            'Error restoring focus after opening SharePoint URL:',
            focusError
          );
        }
      }, 2000); // 2 second delay for SharePoint URLs

      // Assume success if no error occurs
      resolve(true);
    } catch (error) {
      console.error('Error opening SharePoint URL:', error);

      // Try the fallback method (re-sanitize the URL for safety)
      try {
        const fallbackSanitizedUrl = sanitizeUrl(url);
        if (fallbackSanitizedUrl) {
          shell
            .openExternal(fallbackSanitizedUrl, { activate: true })
            .then(() => {
              console.log(
                'Opened SharePoint URL with shell.openExternal after error'
              );
              resolve(true);
            })
            .catch(shellError => {
              console.error(
                'Error opening SharePoint URL with all methods:',
                shellError
              );
              resolve(false);
            });
        } else {
          console.error('Cannot use fallback method - URL is not safe');
          resolve(false);
        }
      } catch (finalError) {
        console.error('Final error opening SharePoint URL:', finalError);
        resolve(false);
      }
    }
  });
}

/**
 * Opens a URL in the default browser using platform-specific commands
 * @param url The URL to open
 * @returns Promise resolving to true if successful, false otherwise
 */
export async function openUrlInDefaultBrowser(url: string): Promise<boolean> {
  // Validate and sanitize the URL first
  const sanitizedUrl = sanitizeUrl(url);
  if (!sanitizedUrl) {
    console.error(`Invalid or unsafe URL: ${url}`);
    return false;
  }

  // For SharePoint and OneDrive URLs, use the specialized method
  if (isSharePointOrOneDriveUrl(sanitizedUrl)) {
    return openSharePointUrl(sanitizedUrl);
  }

  return new Promise(resolve => {
    try {
      const platform = process.platform;
      let command: string;
      let args: string[] = [];

      // Determine the command based on the platform
      if (platform === 'win32') {
        // Windows - use start command to prevent explorer.exe issues
        command = 'cmd.exe';
        args = ['/c', 'start', '""', sanitizedUrl];
      } else if (platform === 'darwin') {
        // macOS
        command = 'open';
        args = [sanitizedUrl];
      } else if (platform === 'linux') {
        // Linux
        command = 'xdg-open';
        args = [sanitizedUrl];
      } else {
        console.error(`Unsupported platform: ${platform}`);
        resolve(false);
        return;
      }

      // Additional security check for command arguments
      const allArgs = [command, ...args].join(' ');
      if (!isSecureForCommand(allArgs)) {
        console.error('Command injection attempt detected in URL opener');
        // Fall back to secure shell method
        shell
          .openExternal(sanitizedUrl, { activate: true })
          .then(() => resolve(true))
          .catch(() => resolve(false));
        return;
      }

      // Get the current focused window to restore focus later
      const focusedWindow = BrowserWindow.getFocusedWindow();
      const windowId = focusedWindow?.id;

      // Execute the command with security options
      const childProc = childProcess.spawn(command, args, {
        detached: true,
        stdio: 'ignore',
        shell: false, // Prevent shell interpretation
        windowsHide: true, // Hide command window on Windows
      });

      childProc.on('error', err => {
        console.error(`Error opening URL with ${command}:`, err);
        resolve(false);
      });

      // Unref the process to allow the Node.js process to exit
      childProc.unref();

      // Restore focus to the app window after a delay
      setTimeout(() => {
        if (windowId !== undefined) {
          const win = BrowserWindow.fromId(windowId);
          if (win && !win.isDestroyed()) {
            if (win.isMinimized()) {
              win.restore();
            }
            win.focus();
            console.log('Restored focus to application window');
          }
        }
      }, 1500); // 1.5 second delay

      // Assume success if no error occurs
      resolve(true);
    } catch (error) {
      console.error('Error opening URL in default browser:', error);
      resolve(false);
    }
  });
}
