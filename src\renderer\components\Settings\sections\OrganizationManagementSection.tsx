/**
 * @file OrganizationManagementSection.tsx
 * @description Organization management section with dangerous operations
 */

import React, { useState } from 'react';
import { AlertTriangle, Building2, Trash2, X } from 'lucide-react';
import { useOrganizationStore } from '../../../store/organization.store';
import { useToast } from '../../../hooks/useToast';

/**
 * Organization management section component
 */
export const OrganizationManagementSection: React.FC = () => {
  const [isDeletingOrg, setIsDeletingOrg] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const { currentOrganization, setCurrentOrganization, deleteOrganization } =
    useOrganizationStore();
  const { toast } = useToast();

  // Handle opening delete modal
  const handleOpenDeleteModal = () => {
    if (!currentOrganization) {
      toast({ title: 'No organization selected', type: 'error' });
      return;
    }
    setShowDeleteModal(true);
  };

  // Handle closing delete modal
  const handleCloseDeleteModal = () => {
    setShowDeleteModal(false);
    setConfirmationText('');
  };

  // Handle actual deletion
  const handleConfirmDelete = async () => {
    if (!currentOrganization) {
      return;
    }

    if (confirmationText !== currentOrganization.name) {
      toast({ title: 'Organization name does not match', type: 'error' });
      return;
    }

    setIsDeletingOrg(true);

    try {
      const result = await deleteOrganization(
        currentOrganization.external_id,
        true
      ); // forceCascade=true

      if (result.success) {
        toast({ title: 'Organization deleted successfully', type: 'success' });
        setShowDeleteModal(false);
        setConfirmationText('');
        // The store will handle clearing the current organization and redirecting
      } else {
        toast({
          title: result.error || 'Failed to delete organization',
          type: 'error',
        });
      }
    } catch (error) {
      console.error('Error deleting organization:', error);
      toast({ title: 'An unexpected error occurred', type: 'error' });
    } finally {
      setIsDeletingOrg(false);
    }
  };

  return (
    <section className='border-t border-ui-border-light dark:border-gray-700 pt-8'>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        Organization Management
      </h3>
      <div className='space-y-6'>
        {/* Reset Organization Selection */}
        <div className='space-y-2'>
          <button
            onClick={() => {
              // Clear organization selection
              localStorage.removeItem('chromasync:lastOrganization');
              setCurrentOrganization(null);
              toast({
                title: 'Organization selection reset. App will reload.',
                type: 'info',
              });
              // Reload the app to force organization selection
              setTimeout(() => window.location.reload(), 1000);
            }}
            style={{
              backgroundColor: 'var(--color-feedback-warning)',
              color: 'white',
              borderRadius: 'var(--radius-md)',
              transition: 'background-color 200ms ease-in-out',
            }}
            className='px-4 py-2 hover:bg-orange-600 flex items-center'
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#d97706';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor =
                'var(--color-feedback-warning)';
            }}
          >
            <Building2 size={16} className='mr-2' />
            Reset Organization Selection
          </button>
          <p className='text-sm text-ui-foreground-tertiary dark:text-gray-400'>
            Clears your current organization selection and forces the
            organization picker to appear on next launch. This is useful if
            you're having issues with organization access or need to switch to a
            different workspace.
          </p>
        </div>

        {/* Delete Organization - Danger Zone */}
        {currentOrganization && (
          <div
            className='border border-red-200 dark:border-red-700 bg-red-50 dark:bg-red-900/20 p-4'
            style={{
              borderRadius: 'var(--radius-lg)',
            }}
          >
            <div className='flex items-start space-x-3'>
              <AlertTriangle className='h-5 w-5 text-red-500 mt-0.5 flex-shrink-0' />
              <div className='flex-1'>
                <h4 className='text-sm font-medium text-red-800 dark:text-red-200 mb-2'>
                  Danger Zone: Delete Organization
                </h4>
                <p className='text-sm text-red-700 dark:text-red-300 mb-4'>
                  Permanently delete "{currentOrganization.name}" and all
                  associated data. This includes all colors, products, team
                  members, and settings.
                  <strong className='block mt-1'>
                    This action cannot be undone!
                  </strong>
                </p>
                <button
                  onClick={handleOpenDeleteModal}
                  disabled={isDeletingOrg}
                  style={{
                    backgroundColor: isDeletingOrg ? '#d1d5db' : '#dc2626',
                    color: isDeletingOrg ? '#6b7280' : 'white',
                    borderRadius: 'var(--radius-md)',
                    transition: 'background-color 200ms ease-in-out',
                    cursor: isDeletingOrg ? 'not-allowed' : 'pointer',
                  }}
                  className='px-4 py-2 font-medium flex items-center'
                  onMouseEnter={e => {
                    if (!isDeletingOrg) {
                      e.currentTarget.style.backgroundColor = '#b91c1c';
                    }
                  }}
                  onMouseLeave={e => {
                    if (!isDeletingOrg) {
                      e.currentTarget.style.backgroundColor = '#dc2626';
                    }
                  }}
                >
                  <Trash2 size={16} className='mr-2' />
                  Delete Organization
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && currentOrganization && (
        <div className='fixed inset-0 z-50 flex items-center justify-center'>
          {/* Backdrop */}
          <div
            className='absolute inset-0 bg-black bg-opacity-50'
            onClick={handleCloseDeleteModal}
          />

          {/* Modal */}
          <div
            className='relative bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4'
            style={{
              backgroundColor: 'var(--color-ui-background-primary)',
              borderRadius: 'var(--radius-lg)',
              boxShadow: 'var(--shadow-lg)',
            }}
          >
            {/* Header */}
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg font-semibold text-red-600 dark:text-red-400'>
                Delete Organization
              </h3>
              <button
                onClick={handleCloseDeleteModal}
                className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
              >
                <X size={20} />
              </button>
            </div>

            {/* Content */}
            <div className='mb-6'>
              <p className='text-sm text-ui-foreground-primary dark:text-gray-300 mb-4'>
                This action will permanently delete{' '}
                <strong>"{currentOrganization.name}"</strong> and all associated
                data:
              </p>
              <ul className='text-sm text-ui-foreground-secondary dark:text-gray-400 mb-4 list-disc list-inside space-y-1'>
                <li>All colors and products</li>
                <li>All team members and invitations</li>
                <li>All organization settings</li>
                <li>All sync data in the cloud</li>
              </ul>
              <p className='text-sm text-red-600 dark:text-red-400 font-medium mb-4'>
                This action cannot be undone!
              </p>

              {/* Confirmation Input */}
              <div className='mb-4'>
                <label className='block text-sm font-medium text-ui-foreground-primary dark:text-gray-300 mb-2'>
                  To confirm, type the organization name exactly:{' '}
                  <strong>{currentOrganization.name}</strong>
                </label>
                <input
                  type='text'
                  value={confirmationText}
                  onChange={e => setConfirmationText(e.target.value)}
                  placeholder={currentOrganization.name}
                  className='w-full px-3 py-2 border rounded-md text-sm'
                  style={{
                    backgroundColor: 'var(--color-ui-background-tertiary)',
                    borderColor: 'var(--color-ui-border-medium)',
                    color: 'var(--color-ui-foreground-primary)',
                    borderRadius: 'var(--radius-md)',
                  }}
                  disabled={isDeletingOrg}
                />
              </div>
            </div>

            {/* Actions */}
            <div className='flex justify-end space-x-3'>
              <button
                onClick={handleCloseDeleteModal}
                disabled={isDeletingOrg}
                className='px-4 py-2 text-sm font-medium rounded-md'
                style={{
                  backgroundColor: 'var(--color-ui-background-secondary)',
                  color: 'var(--color-ui-foreground-primary)',
                  borderRadius: 'var(--radius-md)',
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                disabled={
                  isDeletingOrg || confirmationText !== currentOrganization.name
                }
                style={{
                  backgroundColor:
                    isDeletingOrg ||
                    confirmationText !== currentOrganization.name
                      ? '#d1d5db'
                      : '#dc2626',
                  color:
                    isDeletingOrg ||
                    confirmationText !== currentOrganization.name
                      ? '#6b7280'
                      : 'white',
                  borderRadius: 'var(--radius-md)',
                  cursor:
                    isDeletingOrg ||
                    confirmationText !== currentOrganization.name
                      ? 'not-allowed'
                      : 'pointer',
                }}
                className='px-4 py-2 text-sm font-medium flex items-center'
              >
                {isDeletingOrg ? (
                  <>
                    <span className='inline-block w-4 h-4 border-2 border-gray-300 border-t-white rounded-full animate-spin mr-2' />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={16} className='mr-2' />
                    Delete Organization
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default OrganizationManagementSection;
