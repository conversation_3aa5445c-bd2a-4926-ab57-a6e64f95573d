/**
 * Custom hook for generating color recommendations
 * Analyzes color properties and provides actionable suggestions
 */

import { useMemo } from 'react';
import type { SimpleColorEntry, ColorRecommendation, ContrastResult, DeltaEResult } from '../types';
import { safeParseInt } from '../../../../../shared/types/type-guards';

interface RecommendationParams {
  primaryColor: SimpleColorEntry | null;
  secondaryColor: SimpleColorEntry | null;
  contrastResult: ContrastResult | null;
  deltaE: DeltaEResult | null;
  inkCoverage: number;
}

export const useColorRecommendations = ({
  primaryColor,
  secondaryColor,
  contrastResult,
  deltaE,
  inkCoverage
}: RecommendationParams) => {
  
  const recommendations = useMemo<ColorRecommendation[]>(() => {
    const recs: ColorRecommendation[] = [];

    // Contrast recommendations
    if (contrastResult && !contrastResult.passes) {
      recs.push({
        title: "Improve Contrast Ratio",
        description: `The current contrast ratio of ${contrastResult.ratio.toFixed(2)}:1 doesn't meet WCAG AA standards.`,
        actionable: "Consider using colors with greater luminance difference for better readability.",
        severity: 'warning'
      });
    }

    // Color difference recommendations
    if (deltaE && secondaryColor) {
      if (deltaE.value < 2) {
        recs.push({
          title: "Increase Color Distinction",
          description: "These colors are very similar and might be difficult to distinguish.",
          actionable: "Try selecting colors with more visual difference (Delta E > 5).",
          severity: 'warning'
        });
      } else if (deltaE.value > 50) {
        recs.push({
          title: "High Color Contrast",
          description: "These colors have very high contrast which can be jarring.",
          actionable: "Consider using colors with moderate difference for a more harmonious palette.",
          severity: 'info'
        });
      }
    }

    // Print recommendations
    if (inkCoverage > 280) {
      recs.push({
        title: "High Ink Coverage Warning",
        description: `Total ink coverage is ${inkCoverage}%, which may cause printing issues.`,
        actionable: "Reduce one or more CMYK values to stay below 280% total coverage.",
        severity: 'error'
      });
    } else if (inkCoverage > 240) {
      recs.push({
        title: "Moderate Ink Coverage",
        description: `Ink coverage is ${inkCoverage}%, suitable for coated paper only.`,
        actionable: "For uncoated paper, consider reducing ink coverage below 240%.",
        severity: 'warning'
      });
    }

    // Color space recommendations with enhanced null safety
    if (primaryColor?.cmyk && typeof primaryColor.cmyk === 'string') {
      const cmykMatches = primaryColor.cmyk.match(/\d+/g);
      if (cmykMatches && cmykMatches.length >= 4) {
        const cmykValues = cmykMatches.map(val => safeParseInt(val) ?? 0);
        const [c = 0, m = 0, y = 0, k = 0] = cmykValues;
        
        if (k > 0 && (c > 0 || m > 0 || y > 0)) {
          const gcr = Math.min(c, m, y);
          if (gcr > 20) {
            recs.push({
              title: "Consider Gray Component Replacement",
              description: "This color could benefit from GCR for better print stability.",
              actionable: `Replace ${gcr}% of CMY with additional K for more consistent printing.`,
              severity: 'info'
            });
          }
        }
      }
    }

    return recs;
  }, [primaryColor, secondaryColor, contrastResult, deltaE, inkCoverage]);

  const criticalRecommendations = useMemo(() => 
    recommendations.filter(r => r.severity === 'error'),
    [recommendations]
  );

  const warningRecommendations = useMemo(() => 
    recommendations.filter(r => r.severity === 'warning'),
    [recommendations]
  );

  const infoRecommendations = useMemo(() => 
    recommendations.filter(r => r.severity === 'info'),
    [recommendations]
  );

  return {
    recommendations,
    criticalRecommendations,
    warningRecommendations,
    infoRecommendations,
    hasIssues: recommendations.length > 0,
    hasCriticalIssues: criticalRecommendations.length > 0
  };
};