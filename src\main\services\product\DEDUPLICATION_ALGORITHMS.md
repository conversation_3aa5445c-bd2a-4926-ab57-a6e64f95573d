# Product Deduplication Algorithms Documentation

## Overview

The ProductDeduplicationService implements sophisticated algorithms for detecting and merging duplicate products with configurable strategies. This document explains the detection strategies, matching algorithms, and merge techniques used by the service.

## Table of Contents

1. [Detection Strategies](#detection-strategies)
2. [Matching Algorithms](#matching-algorithms)
3. [Similarity Calculations](#similarity-calculations)
4. [Primary Product Selection](#primary-product-selection)
5. [Merge Strategies](#merge-strategies)
6. [Performance Optimizations](#performance-optimizations)
7. [Configuration Examples](#configuration-examples)

## Detection Strategies

### Multi-Criteria Detection

The service supports detecting duplicates based on multiple criteria:

#### 1. Name-Based Detection
- **Purpose**: Identifies products with similar or identical names
- **Use Cases**: Product variations, typos, formatting differences
- **Configuration**: `criteria.name: boolean`

```typescript
{
  criteria: {
    name: true,          // Enable name-based detection
    sku: false,         // Disable SKU detection
    metadata: false,    // Disable metadata detection
    fuzzyThreshold: 0.85 // 85% similarity threshold
  }
}
```

#### 2. SKU-Based Detection
- **Purpose**: Identifies products sharing Stock Keeping Unit codes
- **Use Cases**: Product variants, versioning, data import duplicates
- **Configuration**: `criteria.sku: boolean`

```typescript
{
  criteria: {
    name: false,
    sku: true,           // Enable SKU-based detection
    metadata: false,
    fuzzyThreshold: 0.85
  }
}
```

#### 3. Metadata-Based Detection
- **Purpose**: Identifies products with similar metadata fields
- **Use Cases**: Products with similar descriptions, categories, specifications
- **Configuration**: `criteria.metadata: boolean`

```typescript
{
  criteria: {
    name: false,
    sku: false,
    metadata: true,      // Enable metadata-based detection
    fuzzyThreshold: 0.85
  }
}
```

#### 4. Combined Detection
- **Purpose**: Uses weighted combination of all criteria
- **Weighting**: Name (60%), SKU (30%), Metadata (10%)
- **Benefits**: More accurate detection, reduced false positives

```typescript
{
  criteria: {
    name: true,          // All criteria enabled
    sku: true,
    metadata: true,
    fuzzyThreshold: 0.85
  }
}
```

## Matching Algorithms

### 1. Exact Matching
- **Algorithm**: Direct string comparison
- **Use Case**: Identical product names or SKUs
- **Performance**: O(1) - Very fast
- **Accuracy**: High precision, low recall

```typescript
{
  matching: {
    type: 'exact',
    threshold: 1.0,      // Must be identical
    ignoreCase: true,    // Case-insensitive comparison
    ignoreWhitespace: true,
    ignoreSpecialChars: false
  }
}
```

### 2. Fuzzy Matching (Levenshtein Distance)
- **Algorithm**: Edit distance-based similarity
- **Use Case**: Products with typos, minor variations
- **Performance**: O(n*m) where n,m are string lengths
- **Accuracy**: Good balance of precision and recall

```typescript
{
  matching: {
    type: 'fuzzy',
    threshold: 0.85,     // 85% similarity required
    ignoreCase: true,
    ignoreWhitespace: true,
    ignoreSpecialChars: false
  }
}
```

**Levenshtein Similarity Calculation:**
```
similarity = 1 - (edit_distance / max_length)
```

### 3. Similarity Matching (Jaccard Index)
- **Algorithm**: Set-based character similarity
- **Use Case**: Products with rearranged words, character sets
- **Performance**: O(n+m) - Faster than Levenshtein
- **Accuracy**: Good for character-level similarities

```typescript
{
  matching: {
    type: 'similarity',
    threshold: 0.7,      // 70% character overlap required
    ignoreCase: true,
    ignoreWhitespace: false,
    ignoreSpecialChars: false
  }
}
```

**Jaccard Similarity Calculation:**
```
similarity = |intersection| / |union|
```

## Similarity Calculations

### String Normalization Process

Before similarity calculation, strings undergo normalization:

1. **Case Normalization** (if `ignoreCase: true`)
   ```typescript
   normalized = original.toLowerCase()
   ```

2. **Whitespace Normalization** (if `ignoreWhitespace: true`)
   ```typescript
   normalized = normalized.replace(/\s+/g, '')
   ```

3. **Special Character Removal** (if `ignoreSpecialChars: true`)
   ```typescript
   normalized = normalized.replace(/[^a-zA-Z0-9]/g, '')
   ```

### Weighted Similarity Scoring

For multi-criteria detection, individual scores are combined using weights:

```typescript
const weights = {
  name: criteria.name ? 0.6 : 0,      // 60% weight
  sku: criteria.sku ? 0.3 : 0,        // 30% weight  
  metadata: criteria.metadata ? 0.1 : 0 // 10% weight
};

overallScore = (nameScore * weights.name + 
                skuScore * weights.sku + 
                metadataScore * weights.metadata) / totalWeight;
```

### Metadata Similarity

Metadata similarity compares individual fields:

1. Extract all unique keys from both metadata objects
2. For each common key, calculate string similarity
3. Average the similarities of comparable fields

```typescript
totalSimilarity = 0;
comparableFields = 0;

for (const key of commonKeys) {
  similarity = calculateStringSimilarity(metadata1[key], metadata2[key]);
  totalSimilarity += similarity;
  comparableFields++;
}

metadataScore = comparableFields > 0 ? totalSimilarity / comparableFields : 0;
```

## Primary Product Selection

When duplicates are found, one product must be selected as the primary (to keep). The selection algorithm uses a scoring system:

### Scoring Factors

1. **Color Count (40% weight)**
   ```typescript
   colorScore = colorCount * 0.4;
   ```

2. **Age Factor (20% weight)**
   ```typescript
   ageMs = Date.now() - new Date(createdAt).getTime();
   ageDays = ageMs / (1000 * 60 * 60 * 24);
   ageScore = Math.min(ageDays / 365, 1) * 0.2; // Max 1 year bonus
   ```

3. **Name Descriptiveness (10% weight)**
   ```typescript
   nameScore = Math.min(name.length / 50, 1) * 0.1;
   ```

4. **Metadata Richness (20% weight)**
   ```typescript
   metadataFieldCount = Object.keys(metadata).length;
   metadataScore = Math.min(metadataFieldCount * 0.1, 1) * 0.2;
   ```

5. **Recent Activity (10% weight)**
   ```typescript
   recentlyUpdated = (Date.now() - updatedAt) < 30_days;
   activityScore = recentlyUpdated ? 0.1 : 0;
   ```

### Final Score Calculation

```typescript
totalScore = colorScore + ageScore + nameScore + metadataScore + activityScore;
```

The product with the highest score becomes the primary product.

## Merge Strategies

### Name Conflict Resolution

When duplicate products have different names:

#### 1. Keep First (`keep_first`)
- Uses the name from the first product encountered
- **Use Case**: Chronological preference

#### 2. Keep Last (`keep_last`)
- Uses the name from the most recently processed product
- **Use Case**: Latest data preference

#### 3. Keep Longest (`keep_longest`)
- Uses the longest name among duplicates
- **Use Case**: Most descriptive name preference
- **Default Strategy**

#### 4. Manual (`manual`)
- Currently defaults to `keep_longest`
- **Future**: Could prompt user for selection

### Metadata Conflict Resolution

#### 1. Merge All (`merge_all`)
- Combines all metadata fields from all products
- Later products override earlier ones for same keys
- **Default Strategy**

```typescript
mergedMetadata = products.reduce((merged, current) => ({
  ...merged,
  ...current.metadata
}), {});
```

#### 2. Prefer First (`prefer_first`)
- Uses metadata from the first product only

#### 3. Prefer Last (`prefer_last`)
- Uses metadata from the last product only

#### 4. Manual (`manual`)
- Currently defaults to `merge_all`
- **Future**: Could provide field-by-field selection

### Color Relationship Merge Strategies

#### 1. Union (`union`)
- Combines all unique colors from all products
- **Default Strategy**
- **Benefits**: No data loss, comprehensive color palette

```typescript
// Collect all unique colors from duplicate products
const allColorIds = new Set();
duplicates.forEach(product => {
  product.colors.forEach(color => allColorIds.add(color.id));
});

// Add missing colors to primary product
primaryProduct.colors = Array.from(allColorIds);
```

#### 2. Intersection (`intersection`)
- Keeps only colors present in ALL duplicate products
- **Use Case**: Conservative approach, common colors only

#### 3. Prefer Primary (`prefer_primary`)
- Keeps only the primary product's colors
- **Use Case**: Maintain original color selection

#### 4. Manual (`manual`)
- Currently defaults to `union`
- **Future**: Could provide interactive color selection

## Performance Optimizations

### 1. Batch Processing

Large datasets are processed in configurable batches:

```typescript
{
  batchSize: 100  // Process 100 duplicate groups at a time
}
```

**Benefits:**
- Reduced memory usage
- Better progress tracking
- Easier error recovery

### 2. Group Size Limiting

Extremely large duplicate groups can be skipped:

```typescript
{
  maxGroupSize: 50  // Skip groups with >50 duplicates
}
```

**Benefits:**
- Prevents processing bottlenecks
- Avoids complex merge scenarios
- Maintains reasonable execution times

### 3. Database Optimization

- **Prepared Statements**: Reused for repeated queries
- **Transactions**: Atomic operations for data consistency
- **Indexing**: Leverages existing database indexes

### 4. Algorithm Complexity

| Algorithm | Time Complexity | Space Complexity | Best Use Case |
|-----------|----------------|------------------|---------------|
| Exact | O(1) | O(1) | Identical strings |
| Levenshtein | O(n×m) | O(n×m) | Similar strings |
| Jaccard | O(n+m) | O(n+m) | Character similarity |

### 5. Early Termination

- Stop comparing products once threshold is not met
- Skip empty or null fields in metadata comparison
- Cache similarity calculations for repeated comparisons

## Configuration Examples

### Conservative Deduplication
Minimizes false positives, high precision:

```typescript
const conservativeOptions: DeduplicationOptions = {
  criteria: {
    name: true,
    sku: true,
    metadata: false,
    fuzzyThreshold: 0.95  // Very high threshold
  },
  matching: {
    type: 'exact',        // Exact matching only
    threshold: 1.0,
    ignoreCase: true,
    ignoreWhitespace: true,
    ignoreSpecialChars: false
  },
  merge: {
    nameConflictResolution: 'keep_longest',
    metadataResolution: 'prefer_first',
    colorMergeStrategy: 'prefer_primary',
    preserveColorOrder: true
  },
  dryRun: false,
  createBackup: true,
  batchSize: 50,
  maxGroupSize: 10
};
```

### Aggressive Deduplication
Maximizes duplicate detection, higher recall:

```typescript
const aggressiveOptions: DeduplicationOptions = {
  criteria: {
    name: true,
    sku: true,
    metadata: true,
    fuzzyThreshold: 0.7   // Lower threshold
  },
  matching: {
    type: 'fuzzy',        // Fuzzy matching
    threshold: 0.7,
    ignoreCase: true,
    ignoreWhitespace: true,
    ignoreSpecialChars: true
  },
  merge: {
    nameConflictResolution: 'keep_longest',
    metadataResolution: 'merge_all',
    colorMergeStrategy: 'union',
    preserveColorOrder: true
  },
  dryRun: false,
  createBackup: true,
  batchSize: 100,
  maxGroupSize: 25
};
```

### Analysis Only
For understanding duplicate patterns without making changes:

```typescript
const analysisOptions: DeduplicationOptions = {
  criteria: {
    name: true,
    sku: true,
    metadata: true,
    fuzzyThreshold: 0.8
  },
  matching: {
    type: 'fuzzy',
    threshold: 0.8,
    ignoreCase: true,
    ignoreWhitespace: true,
    ignoreSpecialChars: false
  },
  merge: DEFAULT_MERGE_STRATEGY,  // Not used in analysis
  dryRun: true,                   // No changes made
  createBackup: false,            // No backup needed
  batchSize: 200,
  maxGroupSize: 100
};
```

### High-Performance Processing
For large datasets requiring fast processing:

```typescript
const performanceOptions: DeduplicationOptions = {
  criteria: {
    name: true,
    sku: false,           // Skip SKU for speed
    metadata: false,      // Skip metadata for speed
    fuzzyThreshold: 0.9
  },
  matching: {
    type: 'similarity',   // Fastest algorithm
    threshold: 0.8,
    ignoreCase: true,
    ignoreWhitespace: true,
    ignoreSpecialChars: true
  },
  merge: {
    nameConflictResolution: 'keep_first',  // Fastest resolution
    metadataResolution: 'prefer_first',
    colorMergeStrategy: 'prefer_primary',
    preserveColorOrder: false
  },
  dryRun: false,
  createBackup: false,    // Skip backup for speed
  batchSize: 500,         // Large batches
  maxGroupSize: 20        // Limit complex groups
};
```

## Error Handling and Edge Cases

### 1. Malformed Data
- **Metadata Parsing Errors**: Gracefully handled with fallback to empty object
- **Missing Fields**: Null/undefined values treated as empty strings
- **Invalid UUIDs**: Validation prevents processing invalid identifiers

### 2. Concurrent Access
- **Database Locks**: Transactions prevent race conditions
- **Backup Conflicts**: Unique backup IDs prevent collisions
- **Service Isolation**: Each deduplication operation is independent

### 3. Memory Management
- **Large Groups**: Limited by `maxGroupSize` configuration
- **Batch Processing**: Prevents memory buildup with large datasets
- **Cleanup**: Temporary data structures cleared after processing

### 4. Recovery Scenarios
- **Transaction Rollback**: Failed merges don't leave partial state
- **Backup Restoration**: Complete restoration of original state
- **Error Logging**: Detailed error information for debugging

## Best Practices

### 1. Configuration Selection
- Start with conservative settings for production
- Use analysis mode to understand data patterns
- Gradually increase aggressiveness based on results

### 2. Testing Strategy
- Always run in dry-run mode first
- Create backups for important datasets
- Test with small subsets before full processing

### 3. Monitoring
- Monitor execution times for performance issues
- Track error rates and common failure patterns
- Review merge results for accuracy

### 4. Maintenance
- Regularly clean up old backups
- Update thresholds based on data evolution
- Consider periodic re-deduplication with updated criteria

## Algorithm Performance Benchmarks

### Typical Performance Characteristics

| Dataset Size | Products | Duplicates | Execution Time | Memory Usage |
|-------------|----------|------------|----------------|--------------|
| Small | 100 | 10 | <1s | <10MB |
| Medium | 1,000 | 100 | ~5s | ~50MB |
| Large | 10,000 | 1,000 | ~30s | ~200MB |
| Extra Large | 100,000 | 10,000 | ~5min | ~1GB |

### Optimization Recommendations

- **Small Datasets (<1K products)**: Use any algorithm, prioritize accuracy
- **Medium Datasets (1K-10K products)**: Use Jaccard similarity, batch size 100
- **Large Datasets (10K-100K products)**: Use exact matching when possible, batch size 500
- **Extra Large Datasets (>100K products)**: Consider pre-filtering, use performance configuration

## Future Enhancements

### Planned Algorithm Improvements

1. **Machine Learning Integration**
   - Train models on successful merge decisions
   - Automatic threshold adjustment
   - Improved metadata similarity scoring

2. **Advanced String Matching**
   - Soundex/Metaphone phonetic matching
   - N-gram analysis
   - Word vector similarities

3. **Interactive Conflict Resolution**
   - Web UI for manual merge decisions
   - Preview mode for complex merges
   - User feedback integration

4. **Performance Optimizations**
   - Parallel processing for large datasets
   - Incremental deduplication
   - Smart indexing for similarity searches

5. **Enhanced Backup System**
   - Compressed backup storage
   - Selective field restoration
   - Backup expiration policies