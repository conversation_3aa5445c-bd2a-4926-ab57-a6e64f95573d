/**
 * @file error-handling.service.ts
 * @description Centralized error handling service for consistent error management
 */

import { ServiceResult, IErrorHandlingService } from '../../../shared/interfaces/service.interfaces';
import { LoggerFactory, ILogger } from '../../utils/logger.service';

export class ErrorHandlingService implements IErrorHandlingService {
  private logger: ILogger;

  constructor(private serviceName: string = 'Unknown') {
    this.logger = LoggerFactory.getInstance().createLogger(`ErrorHandler:${serviceName}`);
  }

  /**
   * Wrap a function with comprehensive error handling
   */
  wrap<T extends any[], R>(
    fn: (...args: T) => R | Promise<R>,
    context?: string,
    metadata?: Record<string, any>
  ): (...args: T) => ServiceResult<R> | Promise<ServiceResult<R>> {
    return async (...args: T): Promise<ServiceResult<R>> => {
      try {
        const result = await fn(...args);
        return {
          success: true,
          data: result,
          metadata
        };
      } catch (error) {
        return this.handleError(error, context || fn.name, metadata);
      }
    };
  }

  /**
   * Handle error and convert to standardized ServiceResult
   */
  handleError(
    error: Error | unknown, 
    context?: string, 
    metadata?: Record<string, any>
  ): ServiceResult<never> {
    const errorInfo = this.extractErrorInfo(error);
    const contextInfo = context ? ` in ${context}` : '';
    
    // Log the error with full context
    this.logger.error(
      `${this.serviceName}${contextInfo}: ${errorInfo.message}`,
      error instanceof Error ? error : new Error(String(error)),
      {
        context,
        ...metadata,
        errorType: errorInfo.type,
        stack: errorInfo.stack
      }
    );

    return {
      success: false,
      error: errorInfo.userMessage,
      metadata: {
        errorType: errorInfo.type,
        context,
        timestamp: new Date().toISOString(),
        ...metadata
      }
    };
  }

  /**
   * Create a successful ServiceResult
   */
  success<T>(data: T, metadata?: Record<string, any>): ServiceResult<T> {
    return {
      success: true,
      data,
      metadata
    };
  }

  /**
   * Create a failed ServiceResult
   */
  failure(error: string, metadata?: Record<string, any>): ServiceResult<never> {
    return {
      success: false,
      error,
      metadata
    };
  }

  /**
   * Validate required parameters and return error if missing
   */
  validateRequired<T>(
    value: T | null | undefined, 
    paramName: string, 
    type?: 'string' | 'number' | 'uuid' | 'email'
  ): ServiceResult<T> {
    if (value === null || value === undefined || value === '') {
      return this.failure(`${paramName} is required`);
    }

    if (type) {
      const validationResult = this.validateType(value, type, paramName);
      if (!validationResult.success) {
        return validationResult;
      }
    }

    return this.success(value);
  }

  /**
   * Validate parameter type
   */
  private validateType<T>(
    value: T, 
    type: 'string' | 'number' | 'uuid' | 'email', 
    paramName: string
  ): ServiceResult<T> {
    switch (type) {
      case 'string':
        if (typeof value !== 'string') {
          return this.failure(`${paramName} must be a string`);
        }
        break;
      
      case 'number':
        if (typeof value !== 'number' || isNaN(value as number)) {
          return this.failure(`${paramName} must be a valid number`);
        }
        break;
      
      case 'uuid':
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (typeof value !== 'string' || !uuidRegex.test(value)) {
          return this.failure(`${paramName} must be a valid UUID`);
        }
        break;
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (typeof value !== 'string' || !emailRegex.test(value)) {
          return this.failure(`${paramName} must be a valid email address`);
        }
        break;
    }

    return this.success(value);
  }

  /**
   * Extract comprehensive error information
   */
  private extractErrorInfo(error: Error | unknown): {
    message: string;
    userMessage: string;
    type: string;
    stack?: string;
  } {
    if (error instanceof Error) {
      return {
        message: error.message,
        userMessage: this.createUserFriendlyMessage(error),
        type: error.constructor.name,
        stack: error.stack
      };
    }

    const errorString = String(error);
    return {
      message: errorString,
      userMessage: 'An unexpected error occurred',
      type: 'Unknown',
      stack: undefined
    };
  }

  /**
   * Create user-friendly error messages
   */
  private createUserFriendlyMessage(error: Error): string {
    const message = error.message.toLowerCase();

    // Database errors
    if (message.includes('database') || message.includes('sql')) {
      return 'Database operation failed. Please try again.';
    }

    // Network errors
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return 'Network error. Please check your connection and try again.';
    }

    // Validation errors
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return error.message; // These are already user-friendly
    }

    // Permission errors
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return 'You do not have permission to perform this action.';
    }

    // File system errors
    if (message.includes('file not found') || message.includes('no such file')) {
      return 'Required file not found. Please check your installation.';
    }

    // Default fallback
    return 'An unexpected error occurred. Please try again.';
  }

  /**
   * Check if a ServiceResult represents an error
   */
  static isError<T>(result: ServiceResult<T>): result is ServiceResult<never> {
    return !result.success;
  }

  /**
   * Check if a ServiceResult represents success
   */
  static isSuccess<T>(result: ServiceResult<T>): result is ServiceResult<T> & { data: T } {
    return result.success && result.data !== undefined;
  }

  /**
   * Unwrap a ServiceResult, throwing if it's an error
   */
  static unwrap<T>(result: ServiceResult<T>): T {
    if (!result.success) {
      throw new Error(result.error || 'Operation failed');
    }
    return result.data!;
  }

  /**
   * Combine multiple ServiceResults
   */
  static combine<T extends Record<string, ServiceResult<any>>>(
    results: T
  ): ServiceResult<{ [K in keyof T]: T[K] extends ServiceResult<infer U> ? U : never }> {
    const errors: string[] = [];
    const data: any = {};

    for (const [key, result] of Object.entries(results)) {
      if (!result.success) {
        errors.push(`${key}: ${result.error}`);
      } else {
        data[key] = result.data;
      }
    }

    if (errors.length > 0) {
      return {
        success: false,
        error: errors.join(', ')
      };
    }

    return {
      success: true,
      data
    };
  }
}