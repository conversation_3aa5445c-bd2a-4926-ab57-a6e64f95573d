/**
 * @file index.ts
 * @description Comprehensive sync system exports including resource management
 * 
 * Exports unified sync manager, error recovery, and resource management components
 * with improved TypeScript type definitions
 */

// Core sync functionality
export { SyncErrorRecovery, handleSyncError } from './sync-error-recovery';
export { UnifiedSyncManager, unifiedSyncManager } from './unified-sync-manager';

// Resource management and shutdown coordination
export { ResourceManager, resourceManager } from './resource-manager';
export { SyncShutdownCoordinator, syncShutdownCoordinator } from './sync-shutdown-coordinator';

// Type exports with improved organization
export type {
    SyncOperation,
    SyncResult, UnifiedSyncConfig
} from './unified-sync-manager';

export type {
    CleanupResult, MemoryMetrics, ResourceTracker
} from './resource-manager';

export type {
    ShutdownOptions, ShutdownResult
} from './sync-shutdown-coordinator';

