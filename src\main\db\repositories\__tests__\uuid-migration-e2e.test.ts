/**
 * @file uuid-migration-e2e.test.ts
 * @description End-to-end tests for pure UUID data flow
 *
 * Comprehensive testing of the UUID migration to ensure:
 * - All repositories work with pure UUID primary keys
 * - Foreign key relationships are maintained correctly
 * - Data integrity is preserved across operations
 * - Service layer properly handles UUID operations
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { ColorRepository } from '../color.repository';
import { ProductRepository } from '../product.repository';
import { OrganizationRepository } from '../organization.repository';
import { NewColorEntry } from '../../../../shared/types/color.types';
import { NewProduct } from '../../../../shared/types/product.types';

describe.sequential('UUID Migration End-to-End Tests', () => {
  let db: Database.Database;
  let colorRepo: ColorRepository;
  let productRepo: ProductRepository;
  let organizationRepo: OrganizationRepository;

  let testOrgId: string;
  let testUserId: string;

  beforeEach(() => {
    // Create in-memory SQLite database for testing
    db = new Database(':memory:');

    // Set up pure UUID schema (post-migration)
    setupUUIDSchema(db);

    // Create repository instances
    colorRepo = new ColorRepository(db);
    productRepo = new ProductRepository(db);
    organizationRepo = new OrganizationRepository(db);

    // Generate test UUIDs
    testOrgId = uuidv4();
    testUserId = uuidv4();

    // Create test organization
    setupTestOrganization(db, testOrgId, testUserId);
  });

  afterEach(() => {
    if (db && db.open) {
      try {
        db.close();
      } catch (error) {
        console.warn('Database close error:', error);
      }
    }
  });

  describe('UUID Primary Key Operations', () => {
    test('should create and retrieve organization with UUID primary key', () => {
      // Create organization
      const orgData = {
        external_id: testOrgId,
        name: 'Test Organization',
        slug: 'test-org',
        plan: 'free' as const,
        settings: '{}',
        ownerId: testUserId,
      };

      const createdId = organizationRepo.insert(orgData);
      expect(createdId).toBe(testOrgId);
      expect(createdId).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/
      );

      // Retrieve organization
      const retrieved = organizationRepo.findById(testOrgId);
      expect(retrieved).toBeTruthy();
      expect(retrieved!.id).toBe(testOrgId);
      expect(retrieved!.external_id).toBe(testOrgId); // Should be same as id
      expect(retrieved!.name).toBe('Test Organization');
    });

    test('should create and retrieve product with UUID primary key', () => {
      const productData: NewProduct = {
        name: 'Test Product',
        description: 'A test product for UUID validation',
      };

      const productId = productRepo.insert(productData, testOrgId, testUserId);
      expect(productId).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/
      );

      // Retrieve product
      const retrieved = productRepo.findById(productId, testOrgId);
      expect(retrieved).toBeTruthy();
      expect(retrieved!.id).toBe(productId);
      expect(retrieved!.external_id).toBe(productId); // Should be same as id
      expect(retrieved!.organization_id).toBe(testOrgId);
      expect(retrieved!.name).toBe('Test Product');
    });

    test('should create and retrieve color with UUID primary key', () => {
      const colorData: NewColorEntry = {
        name: 'Test Color',
        hex: '#FF5733',
        source: 'USER',
        code: 'TC001',
        isLibrary: false,
      };

      const colorId = colorRepo.insert(colorData, testOrgId);
      expect(colorId).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/
      );

      // Retrieve color
      const retrieved = colorRepo.findById(colorId, testOrgId);
      expect(retrieved).toBeTruthy();
      expect(retrieved!.id).toBe(colorId);
      expect(retrieved!.external_id).toBe(colorId); // Should be same as id
      expect(retrieved!.organization_id).toBe(testOrgId);
      expect(retrieved!.display_name).toBe('Test Color');
      expect(retrieved!.hex).toBe('#FF5733');
    });
  });

  describe('UUID Foreign Key Relationships', () => {
    test('should maintain product-color relationships with UUID foreign keys', () => {
      // Create product
      const productData: NewProduct = {
        name: 'Test Product',
        description: 'Product for relationship testing',
      };
      const productId = productRepo.insert(productData, testOrgId, testUserId);

      // Create color
      const colorData: NewColorEntry = {
        name: 'Relationship Color',
        hex: '#00FF00',
        source: 'USER',
        code: 'RC001',
        isLibrary: false,
      };
      const colorId = colorRepo.insert(colorData, testOrgId);

      // Create product-color relationship
      const relationshipCreated = productRepo.addProductColor(
        productId,
        colorId,
        testOrgId
      );
      expect(relationshipCreated).toBe(true);

      // Verify relationship exists with UUID foreign keys
      const productColors = productRepo.getProductColors(productId, testOrgId);
      expect(productColors).toHaveLength(1);
      expect(productColors[0].product_id).toBe(productId);
      expect(productColors[0].color_id).toBe(colorId);
      expect(productColors[0].organization_id).toBe(testOrgId);

      // Verify product with colors query
      const productWithColors = productRepo.getProductWithColors(
        productId,
        testOrgId
      );
      expect(productWithColors).toBeTruthy();
      expect(productWithColors!.product_id).toBe(productId);
      expect(productWithColors!.color_id).toBe(colorId);
    });

    test('should handle organization member relationships with UUID foreign keys', () => {
      // Add member to organization
      const memberAdded = organizationRepo.insertMember(
        testOrgId,
        testUserId,
        'owner'
      );
      expect(memberAdded).toBe(true);

      // Retrieve members
      const members = organizationRepo.findMembers(testOrgId);
      expect(members).toHaveLength(1);
      expect(members[0].organization_id).toBe(testOrgId);
      expect(members[0].user_id).toBe(testUserId);
      expect(members[0].role).toBe('owner');

      // Check membership
      const isMember = organizationRepo.checkUserMembership(
        testOrgId,
        testUserId
      );
      expect(isMember).toBe(true);

      // Get user role
      const role = organizationRepo.getUserRole(testOrgId, testUserId);
      expect(role).toBe('owner');
    });
  });

  describe('UUID Data Integrity Validation', () => {
    test('should reject invalid UUID formats', () => {
      const invalidIds = [
        'not-a-uuid',
        '123',
        '',
        'invalid-uuid-format-123',
        '550e8400-e29b-41d4-a716', // Too short
        '550e8400-e29b-41d4-a716-************-extra', // Too long
      ];

      invalidIds.forEach(invalidId => {
        expect(() => {
          organizationRepo.findById(invalidId);
        }).toThrow();
      });
    });

    test('should handle UUID case variations consistently', () => {
      const upperCaseUuid = testOrgId.toUpperCase();
      const lowerCaseUuid = testOrgId.toLowerCase();
      const mixedCaseUuid = testOrgId.replace(/[a-f]/g, c => c.toUpperCase());

      // All should reference the same organization (SQLite is case-insensitive for text)
      const org1 = organizationRepo.findById(upperCaseUuid);
      const org2 = organizationRepo.findById(lowerCaseUuid);
      const org3 = organizationRepo.findById(mixedCaseUuid);

      expect(org1?.id).toBe(org2?.id);
      expect(org2?.id).toBe(org3?.id);
    });

    test('should maintain referential integrity on deletion', () => {
      // Create product and color
      const productData: NewProduct = { name: 'Delete Test Product' };
      const colorData: NewColorEntry = {
        name: 'Delete Test Color',
        hex: '#FF0000',
        source: 'USER',
        isLibrary: false,
      };

      const productId = productRepo.insert(productData, testOrgId);
      const colorId = colorRepo.insert(colorData, testOrgId);

      // Create relationship
      productRepo.addProductColor(productId, colorId, testOrgId);

      // Verify relationship exists
      let productColors = productRepo.getProductColors(productId, testOrgId);
      expect(productColors).toHaveLength(1);

      // Soft delete product (should handle orphaned relationships)
      const deleted = productRepo.softDelete(productId, testOrgId);
      expect(deleted).toBe(true);

      // Verify product is soft deleted
      const deletedProduct = productRepo.findById(productId, testOrgId);
      expect(deletedProduct).toBeNull();

      // Verify relationship is cleaned up
      productColors = productRepo.getProductColors(productId, testOrgId);
      expect(productColors).toHaveLength(0);
    });
  });

  describe('UUID Performance and Scalability', () => {
    test('should handle large datasets with UUID primary keys efficiently', () => {
      const startTime = Date.now();

      // Create multiple products and colors
      const productIds: string[] = [];
      const colorIds: string[] = [];

      for (let i = 0; i < 100; i++) {
        const productId = productRepo.insert(
          {
            name: `Bulk Product ${i}`,
            description: `Product ${i} for performance testing`,
          },
          testOrgId
        );
        productIds.push(productId);

        const colorId = colorRepo.insert(
          {
            name: `Bulk Color ${i}`,
            hex: `#${Math.floor(Math.random() * 16777215)
              .toString(16)
              .padStart(6, '0')}`,
            source: 'USER',
            isLibrary: false,
          },
          testOrgId
        );
        colorIds.push(colorId);

        // Create some relationships
        if (i % 3 === 0) {
          productRepo.addProductColor(productId, colorId, testOrgId);
        }
      }

      const creationTime = Date.now() - startTime;

      // Query all data
      const queryStartTime = Date.now();
      const allProducts = productRepo.findAll(testOrgId);
      const allColors = colorRepo.findAll(testOrgId);
      const queryTime = Date.now() - queryStartTime;

      // Verify data integrity
      expect(allProducts).toHaveLength(100);
      expect(allColors).toHaveLength(100);
      expect(productIds).toHaveLength(100);
      expect(colorIds).toHaveLength(100);

      // Ensure all IDs are valid UUIDs
      productIds.forEach(id => {
        expect(id).toMatch(
          /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/
        );
      });

      console.log(
        `UUID Performance Test - Creation: ${creationTime}ms, Query: ${queryTime}ms`
      );

      // Performance should be reasonable (adjust thresholds as needed)
      expect(creationTime).toBeLessThan(5000); // 5 seconds for 100 records
      expect(queryTime).toBeLessThan(1000); // 1 second for queries
    });
  });

  describe('Cross-Repository UUID Operations', () => {
    test('should handle complex multi-repository operations with UUIDs', () => {
      // Create organization with products and colors
      const productData: NewProduct = {
        name: 'Cross-Repo Product',
        description: 'Testing cross-repository operations',
      };
      const productId = productRepo.insert(productData, testOrgId);

      const colorData: NewColorEntry = {
        name: 'Cross-Repo Color',
        hex: '#PURPLE',
        source: 'PANTONE',
        code: 'PANTONE-123',
        isLibrary: true,
      };
      const colorId = colorRepo.insert(colorData, testOrgId);

      // Create relationship
      productRepo.addProductColor(productId, colorId, testOrgId);

      // Verify all repositories can find their data
      const org = organizationRepo.findById(testOrgId);
      const product = productRepo.findById(productId, testOrgId);
      const color = colorRepo.findById(colorId, testOrgId);

      expect(org).toBeTruthy();
      expect(product).toBeTruthy();
      expect(color).toBeTruthy();

      // Verify foreign key relationships
      expect(product!.organization_id).toBe(testOrgId);
      expect(color!.organization_id).toBe(testOrgId);

      // Test organization query with related data
      const orgMembers = organizationRepo.findMembers(testOrgId);
      const orgProducts = productRepo.findAll(testOrgId);
      const orgColors = colorRepo.findAll(testOrgId);

      expect(orgProducts).toHaveLength(1);
      expect(orgColors).toHaveLength(1);
      expect(orgProducts[0].id).toBe(productId);
      expect(orgColors[0].id).toBe(colorId);
    });
  });
});

/**
 * Setup pure UUID schema for testing (post-migration schema)
 */
function setupUUIDSchema(db: Database.Database) {
  // Organizations table with UUID primary key
  db.exec(`
    CREATE TABLE organizations (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
      settings JSON DEFAULT '{}',
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      CHECK (length(id) = 36)
    );

    CREATE TABLE organization_members (
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      user_id TEXT NOT NULL,
      role TEXT NOT NULL DEFAULT 'member',
      joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
      invited_by TEXT,
      PRIMARY KEY (organization_id, user_id)
    );

    CREATE TABLE color_sources (
      id INTEGER PRIMARY KEY,
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL
    );

    INSERT INTO color_sources (id, code, name) VALUES 
    (1, 'USER', 'User Created'),
    (2, 'PANTONE', 'Pantone Color'),
    (3, 'RAL', 'RAL Color');

    CREATE TABLE products (
      id TEXT PRIMARY KEY,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      user_id TEXT,
      name TEXT NOT NULL,
      description TEXT,
      metadata JSON DEFAULT '{}',
      is_active BOOLEAN NOT NULL DEFAULT TRUE,
      is_synced BOOLEAN NOT NULL DEFAULT FALSE,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT,
      created_by TEXT,
      CHECK (length(id) = 36)
    );

    CREATE TABLE colors (
      id TEXT PRIMARY KEY,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      source_id INTEGER NOT NULL DEFAULT 1 REFERENCES color_sources(id),
      name TEXT NOT NULL,
      display_name TEXT,
      code TEXT,
      hex TEXT NOT NULL,
      color_spaces JSON DEFAULT '{}',
      is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
      is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
      is_effect BOOLEAN NOT NULL DEFAULT FALSE,
      is_library BOOLEAN NOT NULL DEFAULT FALSE,
      gradient_colors TEXT,
      notes TEXT,
      tags TEXT,
      properties JSON DEFAULT '{}',
      is_synced BOOLEAN NOT NULL DEFAULT FALSE,
      version INTEGER NOT NULL DEFAULT 1,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT,
      created_by TEXT,
      user_id TEXT,
      device_id TEXT,
      conflict_resolved_at TEXT,
      CHECK (length(id) = 36),
      CHECK (length(hex) = 7 AND substr(hex, 1, 1) = '#')
    );

    CREATE TABLE product_colors (
      product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
      color_id TEXT NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
      display_order INTEGER NOT NULL DEFAULT 0,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      added_at TEXT DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (product_id, color_id)
    );

    CREATE INDEX idx_products_org ON products(organization_id);
    CREATE INDEX idx_colors_org ON colors(organization_id);
    CREATE INDEX idx_product_colors_product ON product_colors(product_id);
    CREATE INDEX idx_product_colors_color ON product_colors(color_id);
  `);
}

/**
 * Setup test organization for UUID testing
 */
function setupTestOrganization(
  db: Database.Database,
  orgId: string,
  userId: string
) {
  db.prepare(
    `
    INSERT INTO organizations (id, name, slug, plan, settings)
    VALUES (?, ?, ?, ?, ?)
  `
  ).run(orgId, 'Test Organization', 'test-org-uuid', 'free', '{}');

  db.prepare(
    `
    INSERT INTO organization_members (organization_id, user_id, role)
    VALUES (?, ?, ?)
  `
  ).run(orgId, userId, 'owner');
}
