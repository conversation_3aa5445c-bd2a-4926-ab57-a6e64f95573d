/**
 * @file electron-log.d.ts
 * @description Module augmentation for electron-log with enhanced type safety
 * Fixes transport property type issues
 */

declare module 'electron-log' {
  interface Transport {
    // Allow null assignment for disabling transports
    [key: string]: any;
  }

  interface Transports {
    // Allow null/undefined assignment for transport disabling
    ipc: Transport | null | undefined;
    file: Transport;
    console: Transport;
    remote: Transport;
  }

  interface ElectronLog {
    transports: Transports;
    info(...params: any[]): void;
    warn(...params: any[]): void;
    error(...params: any[]): void;
    debug(...params: any[]): void;
    verbose(...params: any[]): void;
    silly(...params: any[]): void;
    log(...params: any[]): void;
  }

  const log: ElectronLog;
  export = log;
}
