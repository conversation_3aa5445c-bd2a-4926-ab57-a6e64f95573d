import { useState, useEffect } from 'react';
import {
  Activity,
  Database,
  Zap,
  Clock,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';

interface PerformanceMetrics {
  pool: {
    utilization: string;
    efficiency: string;
    errorRate: string;
  };
  performance: {
    averageResponseTime: string;
    p50: string;
    p95: string;
    p99: string;
  };
  health: {
    status: 'healthy' | 'warning' | 'critical';
    uptime: number;
  };
}

interface DatabaseStats {
  tableStats: Array<{
    name: string;
    column_count: number;
  }>;
  indexStats: Array<{
    name: string;
    tbl_name: string;
    sql: string;
  }>;
  recommendations: string[];
}

interface PerformanceProfilerProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PerformanceProfiler({
  isOpen,
  onClose,
}: PerformanceProfilerProps) {
  const [activeTab, setActiveTab] = useState<
    'overview' | 'database' | 'queries' | 'recommendations'
  >('overview');
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [dbStats, setDbStats] = useState<DatabaseStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchMetrics();
      const interval = setInterval(fetchMetrics, 5000); // Update every 5 seconds
      return () => clearInterval(interval);
    }

    return undefined;
  }, [isOpen]);

  const fetchMetrics = async () => {
    try {
      setIsLoading(true);

      // In a real implementation, these would be IPC calls to the main process
      // For now, we'll simulate the data structure

      // Simulate performance metrics
      const simulatedMetrics: PerformanceMetrics = {
        pool: {
          utilization: '45%',
          efficiency: '8.5',
          errorRate: '0.1%',
        },
        performance: {
          averageResponseTime: '12.5ms',
          p50: '8.2ms',
          p95: '25.1ms',
          p99: '45.7ms',
        },
        health: {
          status: 'healthy',
          uptime: Date.now() - 1000 * 60 * 60 * 2, // 2 hours
        },
      };

      const simulatedDbStats: DatabaseStats = {
        tableStats: [
          { name: 'colors', column_count: 12 },
          { name: 'products', column_count: 8 },
          { name: 'product_colors', column_count: 4 },
          { name: 'datasheets', column_count: 10 },
          { name: 'color_cmyk', column_count: 6 },
        ],
        indexStats: [
          {
            name: 'idx_colors_external_id',
            tbl_name: 'colors',
            sql: 'CREATE INDEX idx_colors_external_id ON colors(external_id)',
          },
          {
            name: 'idx_colors_hex',
            tbl_name: 'colors',
            sql: 'CREATE INDEX idx_colors_hex ON colors(hex)',
          },
          {
            name: 'idx_products_name',
            tbl_name: 'products',
            sql: 'CREATE INDEX idx_products_name ON products(name)',
          },
        ],
        recommendations: [
          'Database performance is optimal',
          'All critical indexes are in place',
          'Connection pool is operating efficiently',
        ],
      };

      setMetrics(simulatedMetrics);
      setDbStats(simulatedDbStats);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className='w-4 h-4 text-green-500' />;
      case 'warning':
        return <AlertTriangle className='w-4 h-4 text-yellow-500' />;
      case 'critical':
        return <AlertTriangle className='w-4 h-4 text-red-500' />;
      default:
        return <Clock className='w-4 h-4 text-gray-500' />;
    }
  };

  const formatUptime = (uptime: number) => {
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col'>
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center space-x-2'>
            <Activity className='w-6 h-6 text-blue-500' />
            <h2 className='text-xl font-semibold dark:text-white'>
              Performance Profiler
            </h2>
            {lastUpdated && (
              <span className='text-sm text-gray-500'>
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
          >
            ×
          </button>
        </div>

        {/* Tab Navigation */}
        <div className='flex space-x-4 mb-6 border-b'>
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'database', label: 'Database', icon: Database },
            { id: 'queries', label: 'Queries', icon: Zap },
            {
              id: 'recommendations',
              label: 'Recommendations',
              icon: AlertTriangle,
            },
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors ${
                activeTab === id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              }`}
            >
              <Icon className='w-4 h-4' />
              <span>{label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className='flex-1 overflow-y-auto'>
          {isLoading && (
            <div className='flex items-center justify-center py-8'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500' />
            </div>
          )}

          {!isLoading && activeTab === 'overview' && metrics && (
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {/* System Health */}
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <div className='flex items-center justify-between mb-3'>
                  <h3 className='font-medium text-gray-900 dark:text-white'>
                    System Health
                  </h3>
                  {getStatusIcon(metrics.health.status)}
                </div>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Status:
                    </span>
                    <span
                      className={`text-sm font-medium ${
                        metrics.health.status === 'healthy'
                          ? 'text-green-600'
                          : metrics.health.status === 'warning'
                            ? 'text-yellow-600'
                            : 'text-red-600'
                      }`}
                    >
                      {metrics.health.status.charAt(0).toUpperCase() +
                        metrics.health.status.slice(1)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Uptime:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {formatUptime(metrics.health.uptime)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Database Pool */}
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3'>
                  Connection Pool
                </h3>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Utilization:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {metrics.pool.utilization}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Efficiency:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {metrics.pool.efficiency}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Error Rate:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {metrics.pool.errorRate}
                    </span>
                  </div>
                </div>
              </div>

              {/* Response Times */}
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3'>
                  Response Times
                </h3>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      Average:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {metrics.performance.averageResponseTime}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      P50:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {metrics.performance.p50}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      P95:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {metrics.performance.p95}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-sm text-gray-600 dark:text-gray-400'>
                      P99:
                    </span>
                    <span className='text-sm font-medium dark:text-white'>
                      {metrics.performance.p99}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {!isLoading && activeTab === 'database' && dbStats && (
            <div className='space-y-6'>
              {/* Table Statistics */}
              <div>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3'>
                  Table Statistics
                </h3>
                <div className='bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden'>
                  <table className='w-full'>
                    <thead className='bg-gray-100 dark:bg-gray-600'>
                      <tr>
                        <th className='px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white'>
                          Table
                        </th>
                        <th className='px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white'>
                          Columns
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {dbStats.tableStats.map((table, index) => (
                        <tr
                          key={index}
                          className='border-t dark:border-gray-600'
                        >
                          <td className='px-4 py-2 text-sm dark:text-white'>
                            {table.name}
                          </td>
                          <td className='px-4 py-2 text-sm dark:text-white'>
                            {table.column_count}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Index Statistics */}
              <div>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3'>
                  Index Coverage
                </h3>
                <div className='bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden'>
                  <table className='w-full'>
                    <thead className='bg-gray-100 dark:bg-gray-600'>
                      <tr>
                        <th className='px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white'>
                          Index
                        </th>
                        <th className='px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white'>
                          Table
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {dbStats.indexStats.slice(0, 10).map((index, i) => (
                        <tr key={i} className='border-t dark:border-gray-600'>
                          <td className='px-4 py-2 text-sm dark:text-white font-mono text-xs'>
                            {index.name}
                          </td>
                          <td className='px-4 py-2 text-sm dark:text-white'>
                            {index.tbl_name}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {dbStats.indexStats.length > 10 && (
                    <div className='px-4 py-2 text-sm text-gray-500 dark:text-gray-400 border-t dark:border-gray-600'>
                      And {dbStats.indexStats.length - 10} more indexes...
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {!isLoading && activeTab === 'queries' && (
            <div className='space-y-6'>
              <div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-4'>
                <h3 className='font-medium text-gray-900 dark:text-white mb-3'>
                  Query Performance
                </h3>
                <p className='text-sm text-gray-600 dark:text-gray-400'>
                  Query performance monitoring shows prepared statement usage,
                  execution times, and optimization opportunities.
                </p>
                <div className='mt-4 grid grid-cols-1 md:grid-cols-3 gap-4'>
                  <div className='text-center'>
                    <div className='text-2xl font-bold text-blue-600 dark:text-blue-400'>
                      15
                    </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>
                      Warmed Statements
                    </div>
                  </div>
                  <div className='text-center'>
                    <div className='text-2xl font-bold text-green-600 dark:text-green-400'>
                      8.5ms
                    </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>
                      Avg Execution
                    </div>
                  </div>
                  <div className='text-center'>
                    <div className='text-2xl font-bold text-purple-600 dark:text-purple-400'>
                      99.9%
                    </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>
                      Cache Hit Rate
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {!isLoading && activeTab === 'recommendations' && dbStats && (
            <div className='space-y-4'>
              <h3 className='font-medium text-gray-900 dark:text-white'>
                Performance Recommendations
              </h3>
              {dbStats.recommendations.length > 0 ? (
                <div className='space-y-3'>
                  {dbStats.recommendations.map((recommendation, index) => (
                    <div
                      key={index}
                      className='flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg'
                    >
                      <CheckCircle className='w-5 h-5 text-green-500 mt-0.5' />
                      <span className='text-sm text-gray-700 dark:text-gray-300'>
                        {recommendation}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='text-center py-8'>
                  <CheckCircle className='w-12 h-12 text-green-500 mx-auto mb-3' />
                  <p className='text-gray-600 dark:text-gray-400'>
                    No recommendations - performance is optimal!
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className='flex justify-between items-center mt-6 pt-4 border-t'>
          <button
            onClick={fetchMetrics}
            disabled={isLoading}
            className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 flex items-center space-x-2'
          >
            <Activity className='w-4 h-4' />
            <span>{isLoading ? 'Refreshing...' : 'Refresh'}</span>
          </button>
          <button
            onClick={onClose}
            className='px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
