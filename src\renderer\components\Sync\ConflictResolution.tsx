/**
 * @file ConflictResolution.tsx
 * @description Enhanced component for resolving sync conflicts with diff viewer and tooltips
 */

import React, { useState, useMemo } from 'react';
import { AlertTriangle, Info, Clock, User, Monitor, Smartphone, Globe, HelpCircle } from 'lucide-react';
import { useSyncConflicts } from '../../store/sync.store';
import { SyncConflict } from '../../../shared/types/sync.types';
import { DiffViewer } from '../ui/DiffViewer/DiffViewer';
import { useTokens } from '../../hooks/useTokens';

export const ConflictResolution: React.FC = () => {
  const { conflicts, resolveConflicts } = useSyncConflicts();
  const tokens = useTokens();
  const [isResolving, setIsResolving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resolutions, setResolutions] = useState<Record<string, 'local' | 'remote' | 'merged'>>({});
  const [fieldSelections, setFieldSelections] = useState<Record<string, Record<string, 'local' | 'remote'>>>({});
  const [viewMode, setViewMode] = useState<'simple' | 'detailed'>('detailed');
  const [expandedConflicts, setExpandedConflicts] = useState<Set<string>>(new Set());
  
  // No conflicts to resolve
  if (conflicts.length === 0) {
    return (
      <div className="text-sm text-gray-500">
        No conflicts to resolve.
      </div>
    );
  }
  
  // Handle resolution selection
  const handleResolutionChange = (conflictId: string, resolution: 'local' | 'remote' | 'merged') => {
    setResolutions(prev => ({
      ...prev,
      [conflictId]: resolution
    }));
    
    // Clear field selections when changing to non-merged resolution
    if (resolution !== 'merged') {
      setFieldSelections(prev => ({
        ...prev,
        [conflictId]: {}
      }));
    }
  };
  
  // Handle field-level selection for merged resolution
  const handleFieldSelection = (conflictId: string, field: string, source: 'local' | 'remote') => {
    setFieldSelections(prev => ({
      ...prev,
      [conflictId]: {
        ...prev[conflictId],
        [field]: source
      }
    }));
    
    // Automatically set resolution to merged if not already
    if (resolutions[conflictId] !== 'merged') {
      setResolutions(prev => ({
        ...prev,
        [conflictId]: 'merged'
      }));
    }
  };
  
  // Toggle conflict expansion
  const toggleConflictExpansion = (conflictId: string) => {
    setExpandedConflicts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(conflictId)) {
        newSet.delete(conflictId);
      } else {
        newSet.add(conflictId);
      }
      return newSet;
    });
  };
  
  // Handle resolve button click
  const handleResolve = async () => {
    // Check if all conflicts have resolutions
    const unresolvedConflicts = conflicts.filter(
      conflict => !resolutions[conflict.recordId]
    );
    
    if (unresolvedConflicts.length > 0) {
      setError('Please select a resolution for all conflicts');
      return;
    }
    
    // Validate merged resolutions have field selections
    const incompleteMergedConflicts = conflicts.filter(conflict => {
      const resolution = resolutions[conflict.recordId];
      const fieldSelection = fieldSelections[conflict.recordId];
      
      if (resolution === 'merged') {
        const conflictingFields = getConflictingFields(conflict.localData, conflict.remoteData);
        return conflictingFields.some(field => !fieldSelection?.[field]);
      }
      return false;
    });
    
    if (incompleteMergedConflicts.length > 0) {
      setError('Please select local or remote version for all conflicting fields in merged resolutions');
      return;
    }
    
    setIsResolving(true);
    setError(null);
    
    try {
      // Format resolutions for the API
      const resolutionArray = conflicts.map(conflict => {
        const resolution = resolutions[conflict.recordId];
        const fieldSelection = fieldSelections[conflict.recordId];
        
        return {
          conflictId: conflict.recordId,
          resolution,
          mergedData: resolution === 'merged' ? buildMergedData(conflict, fieldSelection) : undefined
        };
      });
      
      const success = await resolveConflicts(resolutionArray);
      
      if (!success) {
        setError('Failed to resolve conflicts');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resolve conflicts');
    } finally {
      setIsResolving(false);
    }
  };
  
  // Helper functions
  const getConflictingFields = (localData: any, remoteData: any): string[] => {
    const allKeys = new Set([...Object.keys(localData || {}), ...Object.keys(remoteData || {})]);
    return Array.from(allKeys).filter(key => 
      JSON.stringify(localData?.[key]) !== JSON.stringify(remoteData?.[key])
    );
  };
  
  const buildMergedData = (conflict: SyncConflict, fieldSelection: Record<string, 'local' | 'remote'>): any => {
    const mergedData = { ...(conflict.localData as Record<string, any> || {}) };
    
    Object.entries(fieldSelection).forEach(([field, source]) => {
      if (source === 'remote') {
        mergedData[field] = (conflict.remoteData as Record<string, any>)?.[field];
      }
      // Local values are already in the merged data
    });
    
    return mergedData;
  };
  
  // Get device icon based on device ID pattern
  const getDeviceIcon = (deviceId: string) => {
    if (deviceId.includes('desktop') || deviceId.includes('mac') || deviceId.includes('windows')) {
      return <Monitor className="w-4 h-4" />;
    }
    if (deviceId.includes('mobile') || deviceId.includes('ios') || deviceId.includes('android')) {
      return <Smartphone className="w-4 h-4" />;
    }
    return <Globe className="w-4 h-4" />;
  };
  
  // Format timestamp for display
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    if (diffDays < 7) return `${diffDays} days ago`;
    
    return date.toLocaleDateString();
  };
  
  // Tooltip component
  const Tooltip: React.FC<{ children: React.ReactNode; content: string }> = ({ children, content }) => {
    const [isVisible, setIsVisible] = useState(false);
    
    return (
      <div className="relative inline-block">
        <div
          onMouseEnter={() => setIsVisible(true)}
          onMouseLeave={() => setIsVisible(false)}
          className="cursor-help"
        >
          {children}
        </div>
        {isVisible && (
          <div className="absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg bottom-full left-1/2 transform -translate-x-1/2 mb-2 whitespace-nowrap max-w-xs">
            {content}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
          </div>
        )}
      </div>
    );
  };
  
  // Render a single conflict with enhanced UI
  const renderConflict = (conflict: SyncConflict) => {
    const { recordId, table, localData, remoteData, localTimestamp, remoteTimestamp } = conflict;
    const isExpanded = expandedConflicts.has(recordId);
    const resolution = resolutions[recordId];
    const conflictingFields = getConflictingFields(localData, remoteData);
    
    return (
      <div key={recordId} className="border border-gray-200 dark:border-gray-700 rounded-lg mb-6 overflow-hidden">
        {/* Conflict Header */}
        <div className="bg-gray-50 dark:bg-gray-800 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {table.charAt(0).toUpperCase() + table.slice(1)} Conflict
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  ID: {recordId.substring(0, 8)}... • {conflictingFields.length} conflicting fields
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {/* View Mode Toggle */}
              <div className="flex bg-white dark:bg-gray-700 rounded-lg p-1 border border-gray-200 dark:border-gray-600">
                <button
                  onClick={() => setViewMode('simple')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    viewMode === 'simple'
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
                  }`}
                >
                  Simple
                </button>
                <button
                  onClick={() => setViewMode('detailed')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    viewMode === 'detailed'
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
                  }`}
                >
                  Detailed
                </button>
              </div>
              
              <button
                onClick={() => toggleConflictExpansion(recordId)}
                className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
              >
                {isExpanded ? 'Collapse' : 'Expand'}
              </button>
            </div>
          </div>
          
          {/* Timestamp Info */}
          <div className="mt-4 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <Tooltip content={`Last modified: ${new Date(localTimestamp.timestamp).toLocaleString()}`}>
                <div className="flex items-center space-x-2">
                  {getDeviceIcon(localTimestamp.deviceId)}
                  <span>Local: {formatTimestamp(localTimestamp.timestamp)}</span>
                </div>
              </Tooltip>
              
              <Tooltip content={`Last modified: ${new Date(remoteTimestamp.timestamp).toLocaleString()}`}>
                <div className="flex items-center space-x-2">
                  {getDeviceIcon(remoteTimestamp.deviceId)}
                  <span>Remote: {formatTimestamp(remoteTimestamp.timestamp)}</span>
                </div>
              </Tooltip>
            </div>
            
            <Tooltip content="How recent changes affect conflict priority">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>
                  {localTimestamp.timestamp > remoteTimestamp.timestamp ? 'Local is newer' : 'Remote is newer'}
                </span>
              </div>
            </Tooltip>
          </div>
        </div>
        
        {/* Resolution Options */}
        <div className="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center space-x-2">
              <span>Resolution Strategy</span>
              <Tooltip content="Choose how to resolve this conflict">
                <HelpCircle className="w-4 h-4 text-gray-400" />
              </Tooltip>
            </h4>
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <label className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ${
              resolution === 'local'
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'
            }`}>
              <input
                type="radio"
                name={`resolution-${recordId}`}
                value="local"
                checked={resolution === 'local'}
                onChange={() => handleResolutionChange(recordId, 'local')}
                className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
                disabled={isResolving}
              />
              <div className="ml-3">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Keep Local</span>
                <p className="text-xs text-gray-600 dark:text-gray-400">Use your local version</p>
              </div>
            </label>
            
            <label className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ${
              resolution === 'remote'
                ? 'border-green-500 bg-green-50 dark:bg-green-900/30'
                : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'
            }`}>
              <input
                type="radio"
                name={`resolution-${recordId}`}
                value="remote"
                checked={resolution === 'remote'}
                onChange={() => handleResolutionChange(recordId, 'remote')}
                className="h-4 w-4 text-green-500 focus:ring-green-500 border-gray-300"
                disabled={isResolving}
              />
              <div className="ml-3">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Use Remote</span>
                <p className="text-xs text-gray-600 dark:text-gray-400">Use the remote version</p>
              </div>
            </label>
            
            <label className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ${
              resolution === 'merged'
                ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/30'
                : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'
            }`}>
              <input
                type="radio"
                name={`resolution-${recordId}`}
                value="merged"
                checked={resolution === 'merged'}
                onChange={() => handleResolutionChange(recordId, 'merged')}
                className="h-4 w-4 text-purple-500 focus:ring-purple-500 border-gray-300"
                disabled={isResolving}
              />
              <div className="ml-3">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Merge Fields</span>
                <p className="text-xs text-gray-600 dark:text-gray-400">Choose per field</p>
              </div>
            </label>
          </div>
        </div>
        
        {/* Detailed View */}
        {isExpanded && (
          <div className="px-6 py-4">
            {viewMode === 'detailed' ? (
              <DiffViewer
                localData={localData as Record<string, any>}
                remoteData={remoteData as Record<string, any>}
                onFieldSelect={resolution === 'merged' ? (field, source) => handleFieldSelection(recordId, field, source) : undefined}
                selectedFields={fieldSelections[recordId] || {}}
                className="border-0"
              />
            ) : (
              /* Simple View */
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span>Local Version</span>
                  </h4>
                  <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4">
                    <pre className="text-sm text-red-900 dark:text-red-200 overflow-auto max-h-60 whitespace-pre-wrap">
                      {JSON.stringify(localData, null, 2)}
                    </pre>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                    <Globe className="w-4 h-4" />
                    <span>Remote Version</span>
                  </h4>
                  <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
                    <pre className="text-sm text-yellow-900 dark:text-yellow-200 overflow-auto max-h-60 whitespace-pre-wrap">
                      {JSON.stringify(remoteData, null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  
  // Summary statistics
  const resolvedCount = Object.keys(resolutions).length;
  const conflictTypes = conflicts.reduce((acc, conflict) => {
    acc[conflict.table] = (acc[conflict.table] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-6 h-6 text-red-500" />
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                Sync Conflicts Detected
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {conflicts.length} conflicts require your attention
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {resolvedCount}/{conflicts.length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">resolved</div>
          </div>
        </div>
        
        {/* Conflict Summary */}
        <div className="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
          <Tooltip content="Types of data that have conflicts">
            <div className="flex items-center space-x-2">
              <Info className="w-4 h-4" />
              <span>Affected: {Object.entries(conflictTypes).map(([type, count]) => `${count} ${type}`).join(', ')}</span>
            </div>
          </Tooltip>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <span className="text-red-700 dark:text-red-200">{error}</span>
          </div>
        </div>
      )}
      
      {/* Conflicts List */}
      <div className="space-y-6">
        {conflicts.map(renderConflict)}
      </div>
      
      {/* Action Buttons */}
      <div className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {resolvedCount === conflicts.length ? (
              <span className="text-green-600 dark:text-green-400 font-medium">
                ✓ All conflicts resolved
              </span>
            ) : (
              <span>
                {conflicts.length - resolvedCount} conflicts remaining
              </span>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => {
                // Auto-resolve all conflicts with local wins
                const autoResolutions: Record<string, 'local' | 'remote' | 'merged'> = {};
                conflicts.forEach(conflict => {
                  autoResolutions[conflict.recordId] = 'local';
                });
                setResolutions(autoResolutions);
              }}
              className="px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg transition-colors"
              disabled={isResolving}
            >
              Keep All Local
            </button>
            
            <button
              onClick={() => {
                // Auto-resolve all conflicts with remote wins
                const autoResolutions: Record<string, 'local' | 'remote' | 'merged'> = {};
                conflicts.forEach(conflict => {
                  autoResolutions[conflict.recordId] = 'remote';
                });
                setResolutions(autoResolutions);
              }}
              className="px-4 py-2 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg transition-colors"
              disabled={isResolving}
            >
              Use All Remote
            </button>
            
            <button
              onClick={handleResolve}
              disabled={isResolving || resolvedCount !== conflicts.length}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                isResolving || resolvedCount !== conflicts.length
                  ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              {isResolving ? 'Resolving...' : 'Apply Resolutions'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
