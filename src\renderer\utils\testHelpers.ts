import { v4 as uuidv4 } from 'uuid';

export interface TestColor {
  id: string;
  name: string;
  pantoneCode: string;
  hexValue: string;
  cmyk: {
    c: number;
    m: number;
    y: number;
    k: number;
  };
  createdAt: number;
  updatedAt: number;
}

/**
 * Generate random hex color
 */
function randomHexColor(): string {
  return `#${Math.floor(Math.random() * 16777215)
    .toString(16)
    .padStart(6, '0')}`;
}

/**
 * Convert hex color to CMYK values
 */
function hexToCMYK(hex: string): {
  c: number;
  m: number;
  y: number;
  k: number;
} {
  // Remove # from hex
  const hexWithoutHash = hex.replace('#', '');

  // Convert hex to RGB
  const r = parseInt(hexWithoutHash.substring(0, 2), 16) / 255;
  const g = parseInt(hexWithoutHash.substring(2, 4), 16) / 255;
  const b = parseInt(hexWithoutHash.substring(4, 6), 16) / 255;

  // Calculate CMYK values
  const k = 1 - Math.max(r, g, b);
  const c = k === 1 ? 0 : (1 - r - k) / (1 - k);
  const m = k === 1 ? 0 : (1 - g - k) / (1 - k);
  const y = k === 1 ? 0 : (1 - b - k) / (1 - k);

  // Round values to percentages
  return {
    c: Math.round(c * 100),
    m: Math.round(m * 100),
    y: Math.round(y * 100),
    k: Math.round(k * 100),
  };
}

/**
 * Generate a random Pantone code
 */
function randomPantoneCode(): string {
  const randomNumber = Math.floor(Math.random() * 9000) + 1000;
  return `PMS ${randomNumber}`;
}

/**
 * Generate a random color name
 */
function randomColorName(): string {
  const adjectives = [
    'Vibrant',
    'Dull',
    'Bright',
    'Dark',
    'Light',
    'Deep',
    'Pale',
    'Vivid',
    'Rich',
    'Soft',
    'Warm',
    'Cool',
  ];

  const colors = [
    'Red',
    'Blue',
    'Green',
    'Yellow',
    'Purple',
    'Orange',
    'Pink',
    'Teal',
    'Gold',
    'Silver',
    'Brown',
    'Black',
    'Ivory',
    'Coral',
    'Aqua',
    'Lime',
    'Maroon',
    'Navy',
  ];

  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const color = colors[Math.floor(Math.random() * colors.length)];

  return `${adjective} ${color}`;
}

/**
 * Generate a single test color entry
 */
export function generateTestColor(): TestColor {
  const now = Date.now();
  const hexValue = randomHexColor();

  return {
    id: uuidv4(),
    name: randomColorName(),
    pantoneCode: randomPantoneCode(),
    hexValue,
    cmyk: hexToCMYK(hexValue),
    createdAt: now,
    updatedAt: now,
  };
}

/**
 * Generate multiple test color entries
 */
export function generateTestColors(count: number): TestColor[] {
  return Array.from({ length: count }, () => generateTestColor());
}

/**
 * Load test data into the app store
 * This function is exposed globally for performance testing
 */
export function loadTestData(count: number): void {
  const testColors = generateTestColors(count);

  // Dispatch to store via IPC
  if (window.electron) {
    // Safe type cast using any since we know the shape at runtime
    const electronAPI = window.electron as any;
    if (electronAPI.ipcRenderer) {
      electronAPI.ipcRenderer.send('import-colors', testColors);
    }
  } else {
    console.warn('IPC not available for test data loading');
  }
}

// Expose test helpers globally for testing without redefining electron
declare global {
  interface Window {
    testHelpers?: {
      loadTestData: (count: number) => void;
    };
  }
}

// Make test helpers available globally in development
if (process.env.NODE_ENV === 'development') {
  window.testHelpers = {
    loadTestData,
  };
}

export default {
  generateTestColor,
  generateTestColors,
  loadTestData,
};
