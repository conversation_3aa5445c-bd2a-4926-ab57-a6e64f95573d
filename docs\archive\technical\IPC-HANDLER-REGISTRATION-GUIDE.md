# IPC Handler Registration Guide

This guide documents the modern IPC handler registration patterns used throughout the ChromaSync application.

## Overview

ChromaSync uses a sophisticated IPC (Inter-Process Communication) system with multiple registration patterns designed for different security levels and use cases. This guide covers all patterns and when to use them.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Registration Patterns](#registration-patterns)
3. [Security Levels](#security-levels)
4. [Migration Guide](#migration-guide)
5. [Best Practices](#best-practices)
6. [Examples](#examples)
7. [Troubleshooting](#troubleshooting)

## Quick Start

For most new IPC handlers, use one of these patterns:

### Simple System Handler
```typescript
import { registerHandlerSafely } from '../utils/ipcRegistry';

registerHandlerSafely(ipcMain, 'my:handler', async (data) => {
  // Your handler logic
  return { success: true, data };
});
```

### Organization-Scoped Handler (Recommended)
```typescript
import { registerSecureHandler, createSuccessResponse } from '../utils/ipc-wrapper';

registerSecureHandler(
  'my:secure-handler',
  async (organizationId: string, data: any) => {
    // Your handler logic with automatic organization context
    return createSuccessResponse(result, 'Operation completed successfully');
  },
  ipcMain,
  {
    logChannel: 'MyService',
    customErrorMessage: 'Operation failed. Please try again.'
  }
);
```

## Registration Patterns

ChromaSync provides three main registration patterns, each designed for different security requirements and use cases.

### 1. Safe Registration Pattern
**File**: `src/main/utils/ipcRegistry.ts`
**Function**: `registerHandlerSafely()`
**Use Case**: Simple handlers that don't require organization context

```typescript
import { registerHandlerSafely } from '../utils/ipcRegistry';

// Basic usage
registerHandlerSafely(ipcMain, 'channel:name', async (data) => {
  return { success: true, data: result };
});

// With middleware
registerHandlerSafely(ipcMain, 'channel:name', withOrganizationContext(
  async (organizationId: string, data: any) => {
    // Handler with organization context
    return service.performOperation(data, organizationId);
  }
));
```

**Features:**
- ✅ Automatic duplicate prevention
- ✅ Automatic cleanup of existing handlers
- ✅ No manual `removeHandler()` or `canRegisterHandler()` needed
- ✅ Works with existing middleware
- ✅ Minimal boilerplate

### 2. Universal Wrapper Pattern (Recommended)
**File**: `src/main/utils/ipc-wrapper.ts`
**Functions**: `registerSecureHandler()`, `registerSystemHandler()`
**Use Case**: Production handlers requiring consistent error handling, logging, and security

```typescript
import { 
  registerSecureHandler, 
  registerSystemHandler,
  createSuccessResponse,
  createErrorResponse 
} from '../utils/ipc-wrapper';

// Organization-scoped handler (most common)
registerSecureHandler(
  'resource:operation',
  async (organizationId: string, data: any) => {
    const result = await service.performOperation(data, organizationId);
    return createSuccessResponse(result, 'Operation completed successfully');
  },
  ipcMain,
  {
    logChannel: 'ResourceService',
    customErrorMessage: 'Failed to perform operation. Please try again.'
  }
);

// System-level handler (no organization context)
registerSystemHandler(
  'system:operation',
  async (data: any) => {
    const result = await systemService.performOperation(data);
    return createSuccessResponse(result, 'System operation completed');
  },
  ipcMain,
  {
    logChannel: 'SystemService',
    customErrorMessage: 'System operation failed. Please try again.'
  }
);
```

**Features:**
- ✅ Automatic organization context validation
- ✅ Consistent error handling and user feedback
- ✅ Structured response formatting
- ✅ Configurable logging channels
- ✅ Built-in security validation
- ✅ Dependency injection support

### 3. Dependency Injection Pattern (Enterprise)
**Use Case**: Complex handlers requiring service dependencies and full enterprise-grade features

```typescript
import { ServiceLocator } from '../services/service-locator';
import { registerSecureHandler, createSuccessResponse } from '../utils/ipc-wrapper';

interface HandlerServices {
  primaryService: PrimaryService;
  secondaryService?: SecondaryService;
}

export function registerMyHandlers(services?: Partial<HandlerServices>): void {
  // Get services from DI or ServiceLocator
  const primaryService = services?.primaryService || ServiceLocator.getPrimaryService();
  const secondaryService = services?.secondaryService || ServiceLocator.getSecondaryService();

  registerSecureHandler(
    'my:complex-operation',
    async (organizationId: string, data: any) => {
      const result = await primaryService.performComplexOperation(data, organizationId);
      await secondaryService.logOperation(result);
      return createSuccessResponse(result, 'Complex operation completed successfully');
    },
    ipcMain,
    {
      logChannel: 'MyService',
      customErrorMessage: 'Complex operation failed. Please try again.'
    }
  );
}

// Usage with dependency injection
export function registerMyHandlersFromLocator(): void {
  registerMyHandlers(); // Uses ServiceLocator automatically
}
```

## Security Levels

### Level 1: System Handlers
- **No organization context required**
- **Use**: `registerSystemHandler()` or `registerHandlerSafely()`
- **Examples**: Authentication, system settings, health checks

### Level 2: Organization-Scoped Handlers  
- **Automatic organization context validation**
- **Use**: `registerSecureHandler()`
- **Examples**: Color management, product operations, user data

### Level 3: Custom Security
- **Manual security implementation**
- **Use**: `registerHandlerSafely()` with custom middleware
- **Examples**: Admin operations, cross-organization features

## Migration Guide

### From Old Pattern to New Pattern

**OLD PATTERN (Deprecated):**
```typescript
// ❌ DON'T USE - Old pattern
try { ipcMain.removeHandler('channel'); } catch { }
if (canRegisterHandler('channel')) {
  ipcMain.handle('channel', async (event, data) => {
    try {
      const result = await service.operation(data);
      return { success: true, data: result };
    } catch (error) {
      console.error('Error:', error);
      return { success: false, error: error.message };
    }
  });
}
```

**NEW PATTERN (Recommended):**
```typescript
// ✅ USE - New pattern
registerSecureHandler(
  'channel',
  async (organizationId: string, data: any) => {
    const result = await service.operation(data, organizationId);
    return createSuccessResponse(result, 'Operation completed successfully');
  },
  ipcMain,
  {
    logChannel: 'ServiceName',
    customErrorMessage: 'Operation failed. Please try again.'
  }
);
```

### Migration Checklist

1. **✅ Add imports**:
   ```typescript
   import { registerSecureHandler, createSuccessResponse } from '../utils/ipc-wrapper';
   // OR
   import { registerHandlerSafely } from '../utils/ipcRegistry';
   ```

2. **✅ Remove old patterns**:
   - Remove `ipcMain.removeHandler()` calls
   - Remove `canRegisterHandler()` checks
   - Remove manual try/catch error handling

3. **✅ Update handler signature**:
   - Add `organizationId` parameter for secure handlers
   - Use structured response format

4. **✅ Add configuration**:
   - Specify `logChannel` for debugging
   - Provide `customErrorMessage` for user feedback

## Best Practices

### 1. Choose the Right Pattern

```typescript
// ✅ System-level operations
registerSystemHandler('system:get-version', async () => {
  return createSuccessResponse(app.getVersion(), 'Version retrieved');
}, ipcMain);

// ✅ Organization-scoped operations  
registerSecureHandler('colors:create', async (organizationId, colorData) => {
  const color = await colorService.create(colorData, organizationId);
  return createSuccessResponse(color, 'Color created successfully');
}, ipcMain, { logChannel: 'ColorService' });

// ✅ Simple operations with existing middleware
registerHandlerSafely(ipcMain, 'legacy:operation', withOrganizationContext(
  async (organizationId, data) => service.legacyOperation(data, organizationId)
));
```

### 2. Use Consistent Response Format

```typescript
// ✅ Always use structured responses
return createSuccessResponse(data, 'User-friendly message');
return createErrorResponse('User-friendly error message');

// ❌ Avoid raw responses
return data;
return { error: 'Raw error' };
```

### 3. Provide Meaningful Configuration

```typescript
registerSecureHandler(
  'users:update-profile',
  handlerFunction,
  ipcMain,
  {
    logChannel: 'UserService',           // For debugging
    customErrorMessage: 'Failed to update user profile. Please check your information and try again.',
    skipDuplicateCheck: false            // Usually keep false
  }
);
```

### 4. Use Dependency Injection for Complex Handlers

```typescript
// ✅ Define service dependencies
interface UserServices {
  userService: UserService;
  auditService: AuditService;
  notificationService?: NotificationService;
}

// ✅ Accept services via DI
export function registerUserHandlers(services?: Partial<UserServices>): void {
  const userService = services?.userService || ServiceLocator.getUserService();
  // ... register handlers
}
```

## Examples

### Complete Handler File Example

```typescript
/**
 * @file example-service.ipc.ts
 * @description Example IPC handlers using modern patterns
 */

import { ipcMain } from 'electron';
import { ServiceLocator } from '../services/service-locator';
import {
  registerSecureHandler,
  registerSystemHandler,
  createSuccessResponse,
  createErrorResponse
} from '../utils/ipc-wrapper';

interface ExampleServices {
  exampleService: ExampleService;
  auditService?: AuditService;
}

/**
 * Register example IPC handlers with dependency injection
 */
export function registerExampleHandlers(services?: Partial<ExampleServices>): void {
  const exampleService = services?.exampleService || ServiceLocator.getExampleService();
  const auditService = services?.auditService || ServiceLocator.getAuditService();

  console.log('[ExampleIPC] Registering example handlers with dependency injection');

  // Organization-scoped operation
  registerSecureHandler(
    'example:create-resource',
    async (organizationId: string, resourceData: any) => {
      const resource = await exampleService.createResource(resourceData, organizationId);
      await auditService.logOperation('create_resource', resource.id, organizationId);
      
      return createSuccessResponse(resource, 'Resource created successfully');
    },
    ipcMain,
    {
      logChannel: 'ExampleService',
      customErrorMessage: 'Failed to create resource. Please check your data and try again.'
    }
  );

  // System-level operation
  registerSystemHandler(
    'example:get-health',
    async () => {
      const health = await exampleService.getSystemHealth();
      return createSuccessResponse(health, 'System health retrieved');
    },
    ipcMain,
    {
      logChannel: 'ExampleService',
      customErrorMessage: 'Failed to retrieve system health. Please try again.'
    }
  );

  console.log('[ExampleIPC] All example handlers registered successfully');
}

/**
 * Register example handlers using ServiceLocator automatically
 */
export function registerExampleHandlersFromLocator(): void {
  registerExampleHandlers(); // Uses ServiceLocator automatically
}
```

### Integration Example

```typescript
// In main/index.ts
import { registerExampleHandlers } from './ipc/example-service.ipc';

// Register with dependency injection
registerExampleHandlers({ 
  exampleService: myCustomExampleService,
  auditService: myCustomAuditService 
});

// OR register with ServiceLocator
registerExampleHandlers(); // Uses ServiceLocator automatically
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Duplicate Registration Warnings
**Problem**: Handler already registered warnings
**Solution**: Use the new patterns - they handle duplicates automatically

```typescript
// ❌ Old way - can cause duplicates
if (canRegisterHandler('channel')) {
  ipcMain.handle('channel', handler);
}

// ✅ New way - handles duplicates automatically
registerHandlerSafely(ipcMain, 'channel', handler);
```

#### 2. Organization Context Errors
**Problem**: "No organization context" errors
**Solution**: Use appropriate security level

```typescript
// ❌ Wrong - system operation using secure handler
registerSecureHandler('system:get-version', handler, ipcMain);

// ✅ Correct - system operation using system handler
registerSystemHandler('system:get-version', handler, ipcMain);
```

#### 3. Missing Error Handling
**Problem**: Unhandled errors crashing the app
**Solution**: Use universal wrapper patterns

```typescript
// ❌ Manual error handling (error-prone)
ipcMain.handle('channel', async (event, data) => {
  try {
    const result = await service.operation(data);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// ✅ Automatic error handling
registerSecureHandler('channel', async (orgId, data) => {
  const result = await service.operation(data, orgId);
  return createSuccessResponse(result, 'Success message');
}, ipcMain, { logChannel: 'Service' });
```

#### 4. Service Not Found Errors
**Problem**: ServiceLocator can't find required services
**Solution**: Ensure services are registered in ServiceContainer

```typescript
// In service-container.ts
await ServiceContainer.register('exampleService', () => new ExampleService(db));

// In handler file
const exampleService = services?.exampleService || ServiceLocator.getExampleService();
```

### Debug Information

Enable debug logging to troubleshoot registration issues:

```typescript
// Add to handler options
{
  logChannel: 'YourService',
  customErrorMessage: 'Error message',
  // Enable debug mode
  debug: true
}
```

### Performance Considerations

1. **Use ServiceLocator for singletons** - Avoid creating new service instances
2. **Register handlers once** - Don't re-register in loops or callbacks  
3. **Use dependency injection** - Better for testing and performance
4. **Batch registrations** - Register all handlers for a service together

## Conclusion

The modern IPC handler registration patterns in ChromaSync provide:

- **🔒 Security**: Automatic organization context validation
- **🛡️ Reliability**: Consistent error handling and duplicate prevention  
- **📊 Observability**: Structured logging and user feedback
- **🏗️ Maintainability**: Dependency injection and consistent patterns
- **⚡ Performance**: Singleton services and efficient registration

Choose the appropriate pattern based on your security requirements:
- **Simple operations**: `registerHandlerSafely()`
- **Production operations**: `registerSecureHandler()` / `registerSystemHandler()`
- **Complex services**: Dependency injection pattern

All new IPC handlers should use these modern patterns. Legacy handlers should be migrated when modified or when performance issues arise.