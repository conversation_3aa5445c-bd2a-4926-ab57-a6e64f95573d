/**
 * @file product.service.ts
 * @description Orchestrator service for product operations
 * 
 * This service acts as a clean orchestrator that delegates to extracted services:
 * - ProductRepository: Database operations and CRUD
 * - ProductSyncService: Supabase synchronization
 * - ProductColorRelationshipService: Product-color relationship management
 * - ColorService: Color-specific operations
 * 
 * Maintains backward compatibility through delegation methods while providing
 * a simplified interface for product management operations.
 */

import Database from 'better-sqlite3';
import { Product, NewProduct, UpdateProduct } from '../../../shared/types/product.types';
import { ColorEntry } from '../../../shared/types/color.types';
import { ColorService } from './color.service';
import { createServiceErrorHandler, ServiceResult } from '../../utils/service-error-handler';
import { ProductRepository } from '../../db/repositories/product.repository';
import { ProductSyncService } from '../../services/product/product-sync.service';
import { ProductColorRelationshipService } from '../../services/product/product-color-relationship.service';
import { ColorRepository } from '../../db/repositories/color.repository';

export class ProductService {
  private errorHandler: ReturnType<typeof createServiceErrorHandler>;
  private productRepository: ProductRepository;
  private productSyncService: ProductSyncService;
  private productColorService: ProductColorRelationshipService;
  private colorRepository: ColorRepository;

  constructor(private db: Database.Database, _colorService?: ColorService) {
    this.errorHandler = createServiceErrorHandler('ProductService');
    
    // Initialize extracted services
    this.productRepository = new ProductRepository(this.db);
    this.colorRepository = new ColorRepository(this.db);
    this.productSyncService = new ProductSyncService(this.db, this.productRepository);
    this.productColorService = new ProductColorRelationshipService(
      this.productRepository,
      this.colorRepository
    );
  }

  /**
   * Get all products with standardized error handling
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getAllWithErrorHandling(organizationId: string): ServiceResult<Product[]> {
    return this.errorHandler.wrap(() => {
      return this.getAll(organizationId);
    }, 'getAllProducts', { organizationId });
  }

  /**
   * Get all products - delegates to repository
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getAll(organizationId: string): Product[] {
    try {
      const rows = this.productRepository.findAll(organizationId);
      return rows.map(row => this.convertRowToProduct(row, organizationId));
    } catch (error) {
      console.error('[ProductService] Error getting all products:', error);
      return [];
    }
  }

  /**
   * Get soft deleted products for recovery - delegates to repository
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   * @param limit - Maximum number of records to return
   * @param offset - Number of records to skip
   */
  getSoftDeleted(organizationId: string, limit: number = 100, offset: number = 0): Product[] {
    try {
      const rows = this.productRepository.findSoftDeleted(organizationId, limit, offset);
      return rows.map(row => this.convertRowToProduct(row, organizationId));
    } catch (error) {
      console.error('[ProductService] Error getting soft deleted products:', error);
      return [];
    }
  }

  /**
   * Restore a soft deleted product - delegates to repository
   * @param id - Product external ID
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   * @param userId - User performing the restore
   */
  restore(id: string, organizationId: string, _userId?: string): boolean {
    try {
      return this.productRepository.restoreRecord(id, organizationId);
    } catch (error) {
      console.error('[ProductService] Error restoring product:', error);
      return false;
    }
  }

  /**
   * Get product by ID - delegates to repository
   * @param id - Product external ID
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getById(id: string, organizationId: string): Product | undefined {
    try {
      const row = this.productRepository.findById(id, organizationId);
      if (!row) return undefined;
      return this.convertRowToProduct(row, organizationId);
    } catch (error) {
      console.error(`[ProductService] Error getting product ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Create a new product - delegates to repository with sync handling
   * @param product - Product data to add
   * @param organizationId - Organization ID for multi-tenant support
   * @param userId - Optional user ID for audit trail
   */
  async add(product: NewProduct, organizationId: string, userId?: string, fromSync: boolean = false): Promise<Product> {
    try {
      const id = this.productRepository.insert(product, organizationId, userId, fromSync);
      
      if (!fromSync) {
        const { syncOutboxService } = await import('../../services/sync/sync-outbox.service');
        await syncOutboxService.addToOutbox('products', 'create', { id: id, organizationId });
      }
      
      // Return the created product
      const createdProduct = this.getById(id, organizationId);
      if (!createdProduct) {
        throw new Error('Failed to retrieve created product');
      }
      return createdProduct;
    } catch (error) {
      console.error('[ProductService] Error adding product:', error);
      throw error;
    }
  }

  /**
   * Update a product - delegates to repository with sync handling
   * @param id - Product external ID
   * @param updates - Fields to update
   * @param organizationId - Organization ID for multi-tenant filtering
   * @param userId - Optional user ID for audit trail
   */
  async update(id: string, updates: UpdateProduct, organizationId: string, userId?: string, fromSync: boolean = false): Promise<Product | undefined> {
    try {
      const success = this.productRepository.update(id, updates, organizationId, userId, fromSync);
      
      if (!success) {
        return undefined;
      }

      if (!fromSync) {
        const { syncOutboxService } = await import('../../services/sync/sync-outbox.service');
        await syncOutboxService.addToOutbox('products', 'update', { id: id, organizationId });
      }

      return this.getById(id, organizationId);
    } catch (error) {
      console.error(`[ProductService] Error updating product ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Delete a product with standardized error handling - delegates to repository
   * @param id - Product external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  deleteWithErrorHandling(id: string, organizationId: string): ServiceResult<boolean> {
    return this.errorHandler.wrap(() => {
      const success = this.productRepository.softDelete(id, organizationId);
      if (success) {
        console.log(`[ProductService] Product ${id} soft deleted locally`);
      }
      return success;
    }, 'deleteProduct', { id, organizationId });
  }

  /**
   * Delete a product (soft delete with cascade) - delegates to repository with sync handling
   * @param id - Product external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  async delete(id: string, organizationId: string, fromSync: boolean = false): Promise<boolean> {
    try {
      const success = this.productRepository.softDelete(id, organizationId);
      
      if (success && !fromSync) {
        const { syncOutboxService } = await import('../../services/sync/sync-outbox.service');
        await syncOutboxService.addToOutbox('products', 'delete', { id: id, organizationId });
      }
      
      return success;
    } catch (error) {
      console.error(`[ProductService] Error deleting product ${id}:`, error);
      return false;
    }
  }


  /**
   * Delete multiple products - delegates to repository
   * @param ids - Array of product external IDs to delete
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  deleteMultiple(ids: string[], organizationId: string): { success: boolean; deletedIds: string[] } {
    try {
      return this.productRepository.deleteMultiple(ids, organizationId);
    } catch (error) {
      console.error('[ProductService] Error deleting multiple products:', error);
      return {
        success: false,
        deletedIds: []
      };
    }
  }

  // =============================================================================
  // LEGACY DISABLED METHODS
  // =============================================================================
  // The following methods were disabled as they depend on SelectionService
  // which has been removed from the system architecture

  /**
   * Get all colors associated with a product - delegates to relationship service
   * @param id - Product external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   * @param __colorService - Color service instance (legacy parameter)
   */
  getColors(id: string, organizationId: string, __colorService?: ColorService): ColorEntry[] {
    try {
      const result = this.productColorService.getProductColors(id, organizationId);
      return result.success && result.data ? result.data : [];
    } catch (error) {
      console.error(`[ProductService] Error getting colors for product ${id}:`, error);
      return [];
    }
  }

  /**
   * Associate a color directly with a product - delegates to relationship service
   * @param productId - Product external ID
   * @param colorId - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  addColor(productId: string, colorId: string, organizationId: string): boolean {
    try {
      const result = this.productColorService.addColorToProduct(productId, colorId, organizationId);
      return result.success && result.data ? result.data : false;
    } catch (error) {
      console.error(`[ProductService] Error adding color ${colorId} to product ${productId}:`, error);
      return false;
    }
  }

  /**
   * Remove a color from a product - delegates to relationship service
   * @param productId - Product external ID
   * @param colorId - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  removeColor(productId: string, colorId: string, organizationId: string): boolean {
    try {
      const result = this.productColorService.removeColorFromProduct(productId, colorId, organizationId);
      return result.success && result.data ? result.data.removed : false;
    } catch (error) {
      console.error(`[ProductService] Error removing color ${colorId} from product ${productId}:`, error);
      return false;
    }
  }

  /**
   * Get all products with their colors - delegates to repository
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getAllProductsWithColors(organizationId: string): Record<string, unknown>[] {
    try {
      const rows = this.productRepository.getAllWithColors(organizationId);
      
      // Group results by product in memory
      const productMap = new Map();
      
      rows.forEach(row => {
        if (!productMap.has(row.product_id)) {
          productMap.set(row.product_id, {
            id: row.product_id,
            name: row.product_name,
            description: row.product_description,
            organizationId,
            createdAt: row.product_created_at,
            updatedAt: row.product_updated_at,
            colors: []
          });
        }
        
        // Add color if exists
        if (row.color_id) {
          const product = productMap.get(row.product_id);
          
          // Safely parse color spaces
          let colorSpaces = null;
          if (row.color_spaces) {
            try {
              colorSpaces = JSON.parse(row.color_spaces);
            } catch (error) {
              console.warn('[ProductService] Failed to parse color_spaces JSON:', error);
              colorSpaces = null;
            }
          }
          
          // Safely parse gradient data
          let gradient = null;
          if (row.gradient_colors) {
            try {
              // Check if it's already a JSON object or a string that needs parsing
              if (typeof row.gradient_colors === 'string') {
                // If it starts with '{' or '[', try to parse as JSON
                if (row.gradient_colors.trim().startsWith('{') || row.gradient_colors.trim().startsWith('[')) {
                  gradient = JSON.parse(row.gradient_colors);
                } else {
                  // If it's a CSS gradient string (like "#0077CC,#FFFFFF"), create proper gradient structure
                  gradient = this.createGradientFromCSS(row.gradient_colors);
                }
              } else {
                gradient = row.gradient_colors;
              }
            } catch (error) {
              console.warn('[ProductService] Failed to parse gradient_colors, treating as CSS string:', error);
              // Fallback: treat as CSS gradient string
              gradient = this.createGradientFromCSS(row.gradient_colors);
            }
          }
          
          product.colors.push({
            id: row.color_id,
            code: row.color_code,
            name: row.color_name,
            hex: row.color_hex,
            colorSpaces,
            isGradient: Boolean(row.is_gradient),
            gradient,
            notes: row.color_notes,
            tags: row.color_tags,
            isLibrary: Boolean(row.is_library),
            createdAt: row.color_created_at,
            updatedAt: row.color_updated_at,
            organizationId
          });
        }
      });
      
      const result = Array.from(productMap.values());
      console.log(`[ProductService] Found ${result.length} products with colors`);
      
      return result;
    } catch (error) {
      console.error('[ProductService] Error getting products with colors:', error);
      return [];
    }
  }

  

  /**
   * Get a single product with its colors - delegates to repository and relationship service
   * @param id - Product external ID
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getProductWithColors(id: string, organizationId: string): any {
    try {
      const product = this.getById(id, organizationId);
      if (!product) return undefined;

      const colorsResult = this.productColorService.getProductColors(id, organizationId);
      const colors = colorsResult.success && colorsResult.data ? colorsResult.data : [];

      return {
        ...product,
        colors
      };
    } catch (error) {
      console.error(`[ProductService] Error getting product with colors ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Add color to product - delegates to relationship service
   * @param productId - Product external ID
   * @param colorId - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  addColorToProduct(productId: string, colorId: string, organizationId: string): boolean {
    return this.addColor(productId, colorId, organizationId);
  }

  /**
   * Remove color from product - delegates to relationship service
   * @param productId - Product external ID
   * @param colorId - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  removeColorFromProduct(productId: string, colorId: string, organizationId: string): boolean {
    return this.removeColor(productId, colorId, organizationId);
  }

  /**
   * Get colors for a product - delegates to relationship service
   * @param productId - Product external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  getProductColors(productId: string, organizationId: string): any[] {
    try {
      const result = this.productColorService.getProductColors(productId, organizationId);
      return result.success && result.data ? result.data : [];
    } catch (error) {
      console.error(`[ProductService] Error getting colors for product ${productId}:`, error);
      return [];
    }
  }

  /**
   * Create a new product - delegates to add method
   * @param data - Product data
   * @param organizationId - Organization ID for multi-tenant support
   * @param userId - Optional user ID for audit trail
   */
  async create(data: { name: string; metadata?: any }, organizationId: string, userId?: string): Promise<Product> {
    return await this.add({ name: data.name, description: data.metadata?.description, organizationId }, organizationId, userId);
  }

  /**
   * Search products by name - delegates to repository
   * @param query - Search query
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  search(query: string, organizationId: string): Product[] {
    try {
      const rows = this.productRepository.search(query, organizationId);
      return rows.map(row => this.convertRowToProduct(row, organizationId));
    } catch (error) {
      console.error('[ProductService] Error searching products:', error);
      return [];
    }
  }

  /**
   * Upsert product from Supabase data - delegates to repository
   * @param supabaseProduct - Product data from Supabase including SKU
   * @param organizationId - Organization ID for multi-tenant filtering
   * @param userId - User ID for audit trail
   */
  upsertFromSupabase(supabaseProduct: any, organizationId: string, userId?: string): Product | undefined {
    try {
      const row = this.productRepository.upsertFromSupabase(supabaseProduct, organizationId, userId);
      if (!row) return undefined;
      return this.convertRowToProduct(row, organizationId);
    } catch (error) {
      console.error('[ProductService] Error upserting product from Supabase:', error);
      throw error;
    }
  }

  /**
   * Deduplicate products - delegates to repository
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  deduplicateProducts(organizationId: string): { success: boolean; deduplicatedCount: number; errors: string[] } {
    try {
      return this.productRepository.deduplicateProducts(organizationId);
    } catch (error) {
      console.error('[ProductService] Error during deduplication:', error);
      return {
        success: false,
        deduplicatedCount: 0,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

  // =============================================================================
  // SYNC DELEGATION METHODS
  // =============================================================================

  /**
   * Sync products from Supabase - delegates to sync service
   * @param userId - User ID for authentication
   * @param organizationId - Organization ID to sync
   */
  async syncProductsFromSupabase(userId: string, organizationId: string): Promise<Product[]> {
    return this.productSyncService.syncProductsFromSupabase(userId, organizationId);
  }

  /**
   * Sync product-color relationships from Supabase - delegates to sync service
   * @param organizationId - Organization ID to sync
   */
  async syncProductColorsFromSupabase(organizationId: string): Promise<any> {
    console.log(`[ProductService] 🔥 SYNC PRODUCT COLORS CALLED for org: ${organizationId}`);
    const result = await this.productSyncService.syncProductColorsFromSupabase(organizationId);
    console.log(`[ProductService] 🔥 SYNC PRODUCT COLORS RESULT:`, result);
    return result;
  }

  /**
   * Push product to Supabase - delegates to sync service
   * @param productId - Product ID to push
   * @param organizationId - Organization ID
   * @param userId - User ID for authentication
   */
  async pushProductToSupabase(productId: string, organizationId: string, userId?: string): Promise<void> {
    return this.productSyncService.pushProductToSupabase(productId, organizationId, userId);
  }

  /**
   * Get unsynced products - delegates to sync service
   */
  getUnsyncedProducts(): Product[] {
    return this.productSyncService.getUnsyncedProducts();
  }

  /**
   * Get unsynced products - compatibility method for UnifiedSyncManager
   */
  getUnsynced(): Product[] {
    return this.getUnsyncedProducts();
  }

  // =============================================================================
  // HELPER METHODS
  // =============================================================================

  /**
   * Create a proper GradientInfo structure from a CSS gradient string
   * @param cssString - CSS gradient string like "#0077CC,#FFFFFF" or "linear-gradient(45deg, #0077CC 0%, #FFFFFF 100%)"
   * @returns GradientInfo object with gradientStops and gradientCSS
   */
  private createGradientFromCSS(cssString: string): any {
    try {
      // If it's a simple comma-separated color list like "#0077CC,#FFFFFF"
      if (cssString.includes(',') && !cssString.includes('linear-gradient') && !cssString.includes('radial-gradient')) {
        const colors = cssString.split(',').map(c => c.trim());
        const gradientStops = colors.map((color, index) => ({
          color: color,
          position: colors.length === 1 ? 50 : (index / (colors.length - 1)) * 100,
          cmyk: null,
          colorCode: null
        }));
        
        // Create CSS gradient from the colors
        const gradientCSS = `linear-gradient(45deg, ${colors.map((color, index) => 
          `${color} ${colors.length === 1 ? '50' : (index / (colors.length - 1)) * 100}%`
        ).join(', ')})`;
        
        return {
          gradientStops,
          gradientCSS
        };
      }
      
      // If it's already a CSS gradient string, use it as-is
      return {
        gradientStops: [], // We can't parse stops from a full CSS gradient easily
        gradientCSS: cssString
      };
    } catch (error) {
      console.warn('[ProductService] Error creating gradient from CSS:', error);
      return {
        gradientStops: [],
        gradientCSS: cssString || 'linear-gradient(45deg, #000000 0%, #ffffff 100%)'
      };
    }
  }

  /**
   * Convert database row to Product object
   * @param row - Database row from repository
   * @param organizationId - Organization ID
   * @returns Product object
   */
  private convertRowToProduct(row: any, organizationId: string): Product {
    // Parse metadata from JSON column
    let metadata: any = {};
    if (row.metadata) {
      try {
        metadata = typeof row.metadata === 'string' 
          ? JSON.parse(row.metadata) 
          : row.metadata;
      } catch (error) {
        console.warn('[ProductService] Failed to parse metadata JSON:', error);
        metadata = {};
      }
    }

    return {
      id: row.id,  // Use row.id directly since it's the UUID primary key
      name: row.name,
      description: row.description || metadata.description || undefined,
      organizationId,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      createdBy: row.created_by || metadata.createdBy || undefined,
      updatedBy: metadata.updatedBy || undefined
    };
  }

  // ============================================================================
  // ROUTER-COMPATIBLE ALIASES
  // These methods provide compatibility with the IPC router expectations
  // ============================================================================

  /**
   * Alias for getAllProductsWithColors to match router expectations
   */
  getAllWithColors(organizationId: string): Record<string, unknown>[] {
    return this.getAllProductsWithColors(organizationId);
  }
}
