/**
 * @file organization-api.ts
 * @description Organization API preload script for secure IPC communication
 */

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Typed API for organization operations
contextBridge.exposeInMainWorld('organizationAPI', {
  // Create new organization
  createOrganization: (data: { name: string; slug?: string }) => {
    console.log('Preload: calling organizationAPI.createOrganization');
    return ipcRenderer.invoke('organization:create', data);
  },

  // Get user's organizations
  getOrganizations: async () => {
    console.log('[OrganizationAPI] 📡 Preload: calling organizationAPI.getOrganizations');
    try {
      const result = await ipcRenderer.invoke('organization:getAll');
      console.log('[OrganizationAPI] 📥 Preload: received result:', {
        success: result?.success,
        hasData: !!result?.data,
        dataLength: result?.data?.length,
        error: result?.error
      });
      return result;
    } catch (error) {
      console.error('[OrganizationAPI] ❌ Preload: IPC call failed:', error);
      throw error;
    }
  },

  // Get current organization
  getCurrentOrganization: () => {
    console.log('Preload: calling organizationAPI.getCurrentOrganization');
    return ipcRenderer.invoke('organization:getCurrent');
  },

  // Set current organization
  setCurrentOrganization: (organizationId: string) => {
    console.log('Preload: calling organizationAPI.setCurrentOrganization');
    return ipcRenderer.invoke('organization:setCurrent', organizationId);
  },

  // Get organization members
  getMembers: (organizationId: string) => {
    console.log('Preload: calling organizationAPI.getMembers');
    return ipcRenderer.invoke('organization:getMembers', organizationId);
  },

  // Invite member
  inviteMember: (data: { organizationId: string; email: string; role?: string }) => {
    console.log('Preload: calling organizationAPI.inviteMember');
    return ipcRenderer.invoke('organization:inviteMember', data);
  },

  // Remove member
  removeMember: (data: { organizationId: string; userId: string }) => {
    console.log('Preload: calling organizationAPI.removeMember');
    return ipcRenderer.invoke('organization:removeMember', data);
  },

  // Update member role
  updateMemberRole: (data: { organizationId: string; userId: string; role: string }) => {
    console.log('Preload: calling organizationAPI.updateMemberRole');
    return ipcRenderer.invoke('organization:updateMemberRole', data);
  },

  // Update organization settings
  updateSettings: (data: { organizationId: string; settings: any }) => {
    console.log('Preload: calling organizationAPI.updateSettings');
    return ipcRenderer.invoke('organization:updateSettings', data);
  },

  // Delete organization
  deleteOrganization: (organizationId: string, forceCascade?: boolean) => {
    console.log('Preload: calling organizationAPI.deleteOrganization');
    return ipcRenderer.invoke('organization:delete', organizationId, forceCascade);
  },
  
  // Accept invitation
  acceptInvitation: (token: string) => {
    console.log('Preload: calling organizationAPI.acceptInvitation');
    return ipcRenderer.invoke('organization:acceptInvitation', token);
  },
  
  // Get pending invitations
  getPendingInvitations: (organizationId: string) => {
    console.log('Preload: calling organizationAPI.getPendingInvitations');
    return ipcRenderer.invoke('organization:getPendingInvitations', organizationId);
  },
  
  // Revoke invitation
  revokeInvitation: (data: { organizationId: string; invitationId: string }) => {
    console.log('Preload: calling organizationAPI.revokeInvitation');
    return ipcRenderer.invoke('organization:revokeInvitation', data);
  },
  
  // Listen for invitation links
  onInvitationReceived: (callback: (token: string) => void) => {
    const listener = (_: any, token: string) => callback(token);
    ipcRenderer.on('handle-invitation', listener);
    return () => {
      ipcRenderer.removeListener('handle-invitation', listener);
    };
  }
});