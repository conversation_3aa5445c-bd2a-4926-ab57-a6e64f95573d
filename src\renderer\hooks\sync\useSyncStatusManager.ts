/**
 * @file useSyncStatusManager.ts
 * @description Smart sync status hook with centralized polling and user activity tracking
 */

import { useEffect, useCallback, useRef } from 'react';
import { SyncProgress, QueueStats, SyncMetrics } from './useSyncIndicatorState';

interface SmartPollingConfig {
  onUnsyncedChanges: (hasChanges: boolean) => void;
  onProgress: (progress: SyncProgress | null) => void;
  onQueueStats: (stats: QueueStats | null) => void;
  onMetrics: (metrics: SyncMetrics | null) => void;
}

export function useSyncStatusManager(config: SmartPollingConfig) {
  const { onUnsyncedChanges, onProgress, onQueueStats, onMetrics } = config;

  const lastActivityUpdate = useRef<number>(0);
  const isPollingRef = useRef<boolean>(false);
  const lastStatusCheck = useRef<number>(0);

  // Track user activity to optimize polling frequency
  const updateUserActivity = useCallback(async () => {
    const now = Date.now();
    // Throttle activity updates to once per 30 seconds
    if (now - lastActivityUpdate.current > 30000) {
      try {
        await window.syncAPI?.updateUserActivity();
        lastActivityUpdate.current = now;
      } catch (error) {
        console.warn(
          '[SyncStatusManager] Failed to update user activity:',
          error
        );
      }
    }
  }, []);

  // Smart status check that respects caching and coalescing
  const performStatusCheck = useCallback(async () => {
    const now = Date.now();

    // Prevent overlapping polls and rate limiting
    if (isPollingRef.current || now - lastStatusCheck.current < 60000) {
      return; // Prevent overlapping polls or calls within 1 minute
    }

    isPollingRef.current = true;
    lastStatusCheck.current = now;

    try {
      // Update user activity first
      await updateUserActivity();

      // Check unsynced changes (most important)
      if (window.syncAPI?.hasUnsyncedLocalChanges) {
        try {
          const result = await window.syncAPI.hasUnsyncedLocalChanges();
          if (result?.success) {
            onUnsyncedChanges(result.hasChanges || false);

            // Only check queue stats if there are changes
            if (result.hasChanges && window.syncAPI?.getQueueStats) {
              try {
                const queueResult = await window.syncAPI.getQueueStats();
                if (queueResult?.success && queueResult.queueStats) {
                  onQueueStats(queueResult.queueStats as QueueStats);
                }
              } catch (error) {
                console.warn(
                  '[SyncStatusManager] Queue stats check failed:',
                  error
                );
              }
            }
          }
        } catch (error) {
          console.warn(
            '[SyncStatusManager] Unsynced changes check failed:',
            error
          );
          onUnsyncedChanges(false);
        }
      }

      // Check progress (lower priority, cached)
      if (window.syncAPI?.getProgress) {
        try {
          const progressResult = await window.syncAPI.getProgress();
          if (progressResult?.success && progressResult.progress) {
            onProgress(progressResult.progress as SyncProgress);
          }
        } catch (error) {
          console.warn('[SyncStatusManager] Progress check failed:', error);
        }
      }

      // Check metrics periodically (lowest priority, long cache)
      if (window.syncAPI?.getMetrics && Math.random() < 0.1) {
        // Only 10% of polls
        try {
          const metricsResult = await window.syncAPI.getMetrics();
          if (metricsResult?.success && metricsResult.metrics) {
            onMetrics(metricsResult.metrics as SyncMetrics);
          }
        } catch (error) {
          console.warn('[SyncStatusManager] Metrics check failed:', error);
        }
      }
    } catch (error) {
      console.warn('[SyncStatusManager] Status check failed:', error);
    } finally {
      isPollingRef.current = false;
    }
  }, [
    updateUserActivity,
    onUnsyncedChanges,
    onProgress,
    onQueueStats,
    onMetrics,
  ]);

  // Setup smart event-driven polling
  useEffect(() => {
    let pollingInterval: ReturnType<typeof setInterval> | null = null;

    // Immediate check on mount
    performStatusCheck();

    // Setup intelligent polling - reduce frequency to prevent interference with user operations
    const startPolling = () => {
      pollingInterval = setInterval(performStatusCheck, 900000); // 15 minutes (aligned with backend sync frequency)
    };

    startPolling();

    // Track user interactions for activity-based polling
    const handleUserInteraction = () => {
      updateUserActivity();
    };

    // Track various user interaction events
    const events = ['click', 'keydown', 'scroll', 'mousemove'];
    let interactionTimeout: ReturnType<typeof setTimeout> | null = null;

    const throttledInteractionHandler = () => {
      if (interactionTimeout) {
        return;
      }

      interactionTimeout = setTimeout(() => {
        handleUserInteraction();
        interactionTimeout = null;
      }, 30000); // Throttle to once per 30 seconds
    };

    events.forEach(event => {
      document.addEventListener(event, throttledInteractionHandler, {
        passive: true,
      });
    });

    // Track page visibility changes
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        updateUserActivity();
        // Perform immediate check when page becomes visible
        setTimeout(performStatusCheck, 100);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Track focus/blur events
    const handleFocus = () => {
      updateUserActivity();
      setTimeout(performStatusCheck, 100);
    };

    const handleBlur = () => {
      updateUserActivity();
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }

      if (interactionTimeout) {
        clearTimeout(interactionTimeout);
      }

      events.forEach(event => {
        document.removeEventListener(event, throttledInteractionHandler);
      });

      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [performStatusCheck, updateUserActivity]);

  // Return utility functions
  return {
    performStatusCheck,
    updateUserActivity,

    // Get status report for debugging
    getStatusReport: useCallback(async () => {
      if (window.syncAPI?.getStatusReport) {
        try {
          const result = (await window.syncAPI.getStatusReport()) as {
            success: boolean;
            report?: any;
            timestamp?: number;
          };
          return result?.report || null;
        } catch (error) {
          console.warn(
            '[SyncStatusManager] Failed to get status report:',
            error
          );
          return null;
        }
      }
      return null;
    }, []),
  };
}
