/**
 * @file ColorForm.refactored.tsx
 * @description Refactored form component using react-hook-form for adding/editing colors
 * Maintains all existing functionality while simplifying state management
 */

import { useCallback, useMemo /*, useEffect*/ } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ColorEntry } from '../../shared/types/color.types';
import { useColorStore } from '../store/color.store';
import { 
  colorFormSchema, 
  colorEditFormSchema, 
  colorFormWithoutProductSchema,
  ColorFormData,
  ColorEditFormData,
  ColorFormWithoutProductData
} from '../schemas/colorForm.schema';
import { parseCMYK, formatCMYKForBackend, standardizeHex, hexToCmyk } from '../../shared/utils/color';
import ColorPreview from './ColorPreview';
import FormInput from './FormInput';
import { safeArrayAccess, getOrDefault } from '../../shared/types/type-guards';

interface ColorFormProps {
  editMode?: boolean;
  color?: ColorEntry;
  onSuccess?: (newColorId?: string) => void;
  onCancel?: () => void;
  isModal?: boolean;
  hideProductField?: boolean;
  defaultProduct?: string;
  prefillMode?: boolean;
}

type FormData = ColorFormData | ColorEditFormData | ColorFormWithoutProductData;

export default function ColorForm({ 
  editMode = false, 
  color, 
  onSuccess, 
  onCancel, 
  isModal = false, 
  hideProductField = false, 
  defaultProduct = '', 
  prefillMode = false 
}: ColorFormProps) {
  const { addColor, updateColor } = useColorStore();

  // Determine which schema to use based on props
  const schema = useMemo(() => {
    if (editMode) return colorEditFormSchema;
    if (hideProductField) return colorFormWithoutProductSchema;
    return colorFormSchema;
  }, [editMode, hideProductField]);

  // Set up default values
  const defaultValues = useMemo(() => {
    const base = {
      name: color?.name || '',
      code: color?.code || '',
      hex: color?.hex || '#FF5733',
      cmyk: color?.cmyk || '',
      notes: color?.notes || '',
      cmykC: 0,
      cmykM: 0,
      cmykY: 0,
      cmykK: 0,
    };

    // Parse CMYK values from existing color
    if (color?.cmyk) {
      const parsed = parseCMYK(color.cmyk);
      base.cmykC = parsed.c;
      base.cmykM = parsed.m;
      base.cmykY = parsed.y;
      base.cmykK = parsed.k;
    }

    // Add product field if not hidden and not in edit mode
    if (!hideProductField && !editMode) {
      return {
        product: color?.product || defaultProduct,
        ...base,
      } as FormData;
    }

    return base as FormData;
  }, [color, defaultProduct, editMode, hideProductField]);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
    setError,
    clearErrors
  } = useForm<any>({
    resolver: zodResolver(schema) as any,
    defaultValues,
    mode: 'onChange'
  });

  // Watch form values for derived calculations
  const watchedHex = watch('hex');
  const watchedCmykC = watch('cmykC');
  const watchedCmykM = watch('cmykM');
  const watchedCmykY = watch('cmykY');
  const watchedCmykK = watch('cmykK');
  const watchedName = watch('name');

  // Calculate if color is dark for preview
  const isDarkColor = useMemo(() => {
    const hex = standardizeHex(watchedHex);
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance < 0.5;
  }, [watchedHex]);

  // Handle color picker changes
  const handleColorPickerChange = useCallback((hexColor: string) => {
    setValue('hex', hexColor, { shouldValidate: true });
    
    // Auto-update CMYK when hex changes
    const derivedCmyk = hexToCmyk(standardizeHex(hexColor));
    if (derivedCmyk) {
      setValue('cmykC', derivedCmyk.c, { shouldValidate: false });
      setValue('cmykM', derivedCmyk.m, { shouldValidate: false });
      setValue('cmykY', derivedCmyk.y, { shouldValidate: false });
      setValue('cmykK', derivedCmyk.k, { shouldValidate: false });
    }
  }, [setValue]);

  // Handle CMYK changes to update hex
  const handleCmykChange = useCallback((component: 'cmykC' | 'cmykM' | 'cmykY' | 'cmykK', value: number) => {
    setValue(component, value, { shouldValidate: false });
    
    // Update hex from CMYK values
    const c = component === 'cmykC' ? value : watchedCmykC;
    const m = component === 'cmykM' ? value : watchedCmykM;
    const y = component === 'cmykY' ? value : watchedCmykY;
    const k = component === 'cmykK' ? value : watchedCmykK;
    
    // Convert CMYK to hex and update
    const hex = cmykToHex(c, m, y, k);
    setValue('hex', hex, { shouldValidate: true });
  }, [setValue, watchedCmykC, watchedCmykM, watchedCmykY, watchedCmykK]);

  // Helper function to convert CMYK to HEX
  const cmykToHex = (c: number, m: number, y: number, k: number): string => {
    c = Math.max(0, Math.min(100, c)) / 100;
    m = Math.max(0, Math.min(100, m)) / 100;
    y = Math.max(0, Math.min(100, y)) / 100;
    k = Math.max(0, Math.min(100, k)) / 100;
    
    const r = Math.round(255 * (1 - c) * (1 - k));
    const g = Math.round(255 * (1 - m) * (1 - k));
    const b = Math.round(255 * (1 - y) * (1 - k));
    
    const toHex = (n: number): string => {
      const hex = n.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
    
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
  };

  // Handle form submission
  const onSubmit = async (data: any) => {
    try {
      clearErrors();

      // Format CMYK from individual values
      const cmykObj = {
        c: data.cmykC || 0,
        m: data.cmykM || 0,
        y: data.cmykY || 0,
        k: data.cmykK || 0
      };
      const formattedCmyk = formatCMYKForBackend(cmykObj);

      // Prepare submit data
      const submitData = {
        product: hideProductField ? defaultProduct : ('product' in data ? data.product : ''),
        name: data.name,
        code: data.code,
        hex: standardizeHex(data.hex),
        cmyk: formattedCmyk,
        notes: data.notes || '',
        // Preserve gradient data when editing
        gradient: editMode && color && color.gradient ? color.gradient : undefined
      };

      if (editMode && color && !prefillMode) {
        // Update existing color
        const updates = Object.entries(submitData)
          .filter(([key, value]) => {
            if (key === 'gradient') return false;
            if (key === 'cmyk') {
              const originalCmykObj = parseCMYK(color.cmyk);
              const newCmykObj = parseCMYK(value as string);
              return (
                originalCmykObj.c !== newCmykObj.c ||
                originalCmykObj.m !== newCmykObj.m ||
                originalCmykObj.y !== newCmykObj.y ||
                originalCmykObj.k !== newCmykObj.k
              );
            }
            return value !== (color as ColorEntry)[key as keyof ColorEntry];
          })
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

        const result = await updateColor(color.id, updates);
        if (result) {
          onSuccess?.();
        }
      } else {
        // Add new color
        const result = await addColor(submitData);
        if (result) {
          onSuccess?.(result.id);
        }
      }
    } catch (err: unknown) {
      setError('root', {
        type: 'manual',
        message: err instanceof Error ? err.message : 'An unknown error occurred'
      });
    }
  };

  // Form header content
  const formHeaderContent = useMemo(() => {
    if (editMode) {
      return {
        title: 'Edit Color',
        description: 'Update the properties of this color entry.'
      };
    } else if (prefillMode && color) {
      return {
        title: `Customize ${color.code || color.name || 'Color'}`,
        description: 'Modify the details as needed for this product. This will create a new color entry.'
      };
    } else {
      return {
        title: 'Add Flat Color',
        description: 'Fill in the details to add a new color to your product.'
      };
    }
  }, [editMode, prefillMode, color]);

  const formClasses = isModal
    ? "rounded-lg shadow-sm w-full"
    : "rounded-lg shadow-sm p-5 w-full";

  return (
    <div
      className={formClasses}
      data-testid="flat-color-form"
      style={{
        backgroundColor: 'var(--color-ui-background-primary)',
        borderRadius: 'var(--radius-lg)',
        boxShadow: 'var(--shadow-sm)',
        border: isModal ? 'none' : `1px solid var(--color-ui-border-light)`
      }}
    >
      <div className="flex flex-col sm:flex-row justify-between items-center gap-3 mb-3">
        <div className="w-full">
          <h2
            className="text-xl font-semibold mb-1"
            data-testid="form-title"
            style={{
              color: 'var(--color-ui-foreground-primary)',
              fontSize: 'var(--font-size-xl)',
              fontWeight: 'var(--font-weight-semibold)'
            }}
          >
            {formHeaderContent.title}
          </h2>
          <p
            className="text-xs"
            style={{
              color: 'var(--color-ui-foreground-secondary)',
              fontSize: 'var(--font-size-xs)'
            }}
          >
            {formHeaderContent.description}
          </p>
        </div>

        <div className="flex-shrink-0">
          <ColorPreview
            hex={watchedHex}
            colorName={watchedName || 'New Color'}
            onChange={handleColorPickerChange}
            isDarkColor={isDarkColor}
            data-testid="color-preview"
          />
        </div>
      </div>

      {errors.root && (
        <div
          className="mb-4 p-3 text-sm flex items-start"
          data-testid="error-message"
          role="alert"
          aria-live="assertive"
          style={{
            backgroundColor: 'var(--feedback-bg-error)',
            border: `1px solid var(--feedback-border-error)`,
            color: 'var(--color-feedback-error)',
            borderRadius: 'var(--radius-lg)',
            fontSize: 'var(--font-size-sm)'
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{errors.root.message}</span>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} data-testid="color-form-element">
        <div className="grid gap-y-3 w-full">
          {/* Product Name - Full Width */}
          {!hideProductField && (
            <div className="w-full">
              <label className="block text-ui-foreground-secondary text-sm mb-1" htmlFor="product">
                Product
              </label>
              {editMode ? (
                <div className="px-4 py-3 bg-gray-100 dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 rounded-md">
                  <span className="text-ui-foreground-primary dark:text-white">{color?.product}</span>
                </div>
              ) : (
                <Controller
                  name="product"
                  control={control}
                  render={({ field }) => (
                    <FormInput
                      id="product"
                      name="product"
                      value={field.value}
                      onChange={field.onChange}
                      required
                      placeholder="Product name"
                      isValid={!(errors as any).product}
                      errorMessage={(errors as any).product?.message}
                      hideLabel={true}
                      aria-label="Product Name"
                      aria-required="true"
                      aria-invalid={!!(errors as any).product}
                      data-testid="product-input"
                      inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
                    />
                  )}
                />
              )}
            </div>
          )}

          {/* First row - Color Name and Hex */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full">
            <div className="w-full">
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <FormInput
                    id="name"
                    name="name"
                    value={field.value}
                    onChange={field.onChange}
                    required
                    placeholder="Color name"
                    isValid={!errors.name}
                    errorMessage={errors.name?.message as string}
                    hideLabel={true}
                    aria-label="Color Name"
                    aria-required="true"
                    aria-invalid={!!errors.name}
                    data-testid="name-input"
                    inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
                  />
                )}
              />
            </div>

            <div className="w-full">
              <Controller
                name="hex"
                control={control}
                render={({ field }) => (
                  <FormInput
                    id="hex"
                    name="hex"
                    value={field.value.replace('#', '')}
                    onChange={(e) => field.onChange('#' + e.target.value)}
                    required
                    placeholder="HEX"
                    isValid={!errors.hex}
                    errorMessage={typeof errors.hex?.message === 'string' ? errors.hex.message : undefined}
                    prefix="#"
                    hideLabel={true}
                    aria-label="HEX Color Code"
                    aria-required="true"
                    aria-invalid={!!errors.hex}
                    data-testid="hex-input"
                    inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
                  />
                )}
              />
            </div>
          </div>

          {/* Second row - Color Code and CMYK */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full">
            <div className="w-full">
              <Controller
                name="code"
                control={control}
                render={({ field }) => (
                  <FormInput
                    id="code"
                    name="code"
                    value={field.value}
                    onChange={field.onChange}
                    required
                    placeholder="Color code"
                    isValid={!errors.code}
                    errorMessage={typeof errors.code?.message === 'string' ? errors.code.message : undefined}
                    hideLabel={true}
                    aria-label="Color Code"
                    aria-required="true"
                    aria-invalid={!!errors.code}
                    data-testid="code-input"
                    inputClassName="px-4 py-3 text-ui-foreground-primary dark:text-white"
                  />
                )}
              />
            </div>

            <div className="w-full">
              <label className="block text-ui-foreground-secondary text-sm mb-2">
                CMYK Values (optional)
              </label>
              <div className="grid grid-cols-4 gap-3">
                {(['cmykC', 'cmykM', 'cmykY', 'cmykK'] as const).map((component, index) => {
                  const letterArray = ['C', 'M', 'Y', 'K'] as const;
                  const letter = letterArray[index] ?? 'X'; // Safe access with guaranteed fallback
                  return (
                    <div key={component}>
                      <Controller
                        name={component}
                        control={control}
                        render={({ field }) => (
                          <input
                            id={`cmyk-${letter.toLowerCase()}`}
                            name={`cmyk-${letter.toLowerCase()}`}
                            type="number"
                            min="0"
                            max="100"
                            step="1"
                            value={field.value?.toString() || '0'}
                            onChange={(e) => handleCmykChange(component, parseInt(e.target.value, 10) || 0)}
                            placeholder="0"
                            className="w-full px-2 py-3 transition-standard text-center"
                            style={{
                              border: `1px solid var(--form-border)`,
                              borderRadius: 'var(--radius-md)',
                              backgroundColor: 'var(--form-background-disabled)',
                              color: 'var(--color-ui-foreground-primary)',
                              fontSize: 'var(--font-size-sm)'
                            }}
                            onFocus={(e) => {
                              e.target.style.outline = 'none';
                              e.target.style.borderColor = 'var(--form-border-focus)';
                              e.target.style.boxShadow = `0 0 0 1px var(--form-border-focus)`;
                            }}
                            onBlur={(e) => {
                              e.target.style.borderColor = 'var(--form-border)';
                              e.target.style.boxShadow = 'none';
                            }}
                            aria-label={`${letter === 'C' ? 'Cyan' : letter === 'M' ? 'Magenta' : letter === 'Y' ? 'Yellow' : letter === 'K' ? 'Black' : 'Color'} (0-100)`}
                            data-testid={`cmyk-${letter.toLowerCase()}-input`}
                          />
                        )}
                      />
                      <div 
                        className="text-xs text-center mt-1"
                        style={{
                          color: 'var(--color-ui-foreground-secondary)',
                          fontSize: 'var(--font-size-xs)'
                        }}
                      >
                        {letter}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Notes Field - Full Width */}
          <div className="mt-3 w-full">
            <Controller
              name="notes"
              control={control}
              render={({ field }) => (
                <textarea
                  id="notes"
                  name="notes"
                  value={field.value || ''}
                  onChange={field.onChange}
                  placeholder="Add any details about this color here..."
                  className="w-full p-3 transition-standard resize-none"
                  style={{
                    border: `1px solid var(--form-border)`,
                    borderRadius: 'var(--radius-md)',
                    backgroundColor: 'var(--form-background-disabled)',
                    color: 'var(--color-ui-foreground-secondary)',
                    fontSize: 'var(--font-size-xs)'
                  }}
                  onFocus={(e) => {
                    e.target.style.outline = 'none';
                    e.target.style.borderColor = 'var(--form-border-focus)';
                    e.target.style.boxShadow = `0 0 0 1px var(--form-border-focus)`;
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = 'var(--form-border)';
                    e.target.style.boxShadow = 'none';
                  }}
                  rows={3}
                  aria-label="Additional Notes"
                  data-testid="notes-input"
                />
              )}
            />
          </div>

          {/* Buttons */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 w-full">
            <button
              type="button"
              className="px-4 py-2 font-medium transition-standard flex items-center justify-center w-full"
              style={{
                backgroundColor: 'var(--button-secondary-bg)',
                border: `1px solid var(--button-secondary-border)`,
                color: 'var(--button-secondary-text)',
                borderRadius: 'var(--radius-md)',
                fontSize: 'var(--font-size-xs)',
                fontWeight: 'var(--font-weight-medium)',
                boxShadow: 'var(--shadow-sm)',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                opacity: isSubmitting ? '0.5' : '1'
              }}
              onMouseEnter={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.backgroundColor = 'var(--button-secondary-bg-hover)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.backgroundColor = 'var(--button-secondary-bg)';
                }
              }}
              onFocus={(e) => {
                e.target.style.outline = 'none';
                e.target.style.boxShadow = `0 0 0 2px var(--form-border-focus)`;
              }}
              onBlur={(e) => {
                e.target.style.boxShadow = 'var(--shadow-sm)';
              }}
              onClick={onCancel}
              disabled={isSubmitting}
              aria-label="Cancel"
              data-testid="cancel-button"
            >
              Cancel
            </button>

            <button
              type="submit"
              className="px-4 py-2 font-medium transition-standard flex items-center justify-center w-full"
              style={{
                backgroundColor: isSubmitting ? 'var(--button-primary-bg-disabled)' : 'var(--button-primary-bg)',
                color: isSubmitting ? 'var(--button-primary-text-disabled)' : 'var(--button-primary-text)',
                border: 'none',
                borderRadius: 'var(--radius-md)',
                fontSize: 'var(--font-size-xs)',
                fontWeight: 'var(--font-weight-medium)',
                boxShadow: 'var(--shadow-sm)',
                cursor: isSubmitting ? 'not-allowed' : 'pointer'
              }}
              onMouseEnter={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.backgroundColor = 'var(--button-primary-bg-hover)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isSubmitting) {
                  e.currentTarget.style.backgroundColor = 'var(--button-primary-bg)';
                }
              }}
              onFocus={(e) => {
                e.target.style.outline = 'none';
                e.target.style.boxShadow = `0 0 0 2px var(--form-border-focus)`;
              }}
              onBlur={(e) => {
                e.target.style.boxShadow = 'var(--shadow-sm)';
              }}
              disabled={isSubmitting}
              aria-label={editMode ? "Update Color" : "Save Color"}
              data-testid="submit-button"
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" style={{ color: 'var(--button-primary-text-disabled)' }}>
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
                  </svg>
                  Saving...
                </span>
              ) : (
                <span>
                  {editMode ? 'Update Color' : 'Save Color'}
                </span>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}