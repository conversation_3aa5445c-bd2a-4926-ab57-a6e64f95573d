/**
 * Micro-Interactions CSS
 * Smooth, delightful interactions for enhanced UX
 */

/* Import animations */
@import './animations.css';

/* Ripple effect animation */
@keyframes rippleExpand {
  from {
    transform: scale(0);
    opacity: 1;
  }
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Base micro-interaction styles */
.micro-interaction {
  position: relative;
  transition: all var(--animation-duration-fast) var(--animation-easing);
  cursor: pointer;
  user-select: none;
  outline: none;
}

.micro-interaction.disabled {
  cursor: not-allowed;
  opacity: 0.6;
  pointer-events: none;
}

/* Button micro-interactions */
.micro-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: all var(--animation-duration-fast) var(--animation-easing);
  transform: translateZ(0);
}

.micro-button:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.micro-button.pressed {
  transform: translateY(0) scale(0.98);
}

.micro-button.focused {
  box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.3);
}

/* Interactive Button Variants */
.interactive-button {
  border: none;
  padding: 0;
  background: none;
  cursor: pointer;
}

.interactive-button .button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all var(--animation-duration-fast) var(--animation-easing);
}

.interactive-button.variant-primary .button-content {
  background: #007acc;
  color: white;
}

.interactive-button.variant-primary:hover:not(.disabled) .button-content {
  background: #005a9e;
}

.interactive-button.variant-secondary .button-content {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.interactive-button.variant-secondary:hover:not(.disabled) .button-content {
  background: #e8e8e8;
  border-color: #ccc;
}

.interactive-button.variant-ghost .button-content {
  background: transparent;
  color: #007acc;
}

.interactive-button.variant-ghost:hover:not(.disabled) .button-content {
  background: rgba(0, 122, 204, 0.1);
}

.interactive-button.variant-danger .button-content {
  background: #e74c3c;
  color: white;
}

.interactive-button.variant-danger:hover:not(.disabled) .button-content {
  background: #c0392b;
}

/* Button Sizes */
.interactive-button.size-small .button-content {
  padding: 4px 8px;
  font-size: 12px;
}

.interactive-button.size-medium .button-content {
  padding: 8px 16px;
  font-size: 14px;
}

.interactive-button.size-large .button-content {
  padding: 12px 24px;
  font-size: 16px;
}

/* Button States */
.interactive-button .button-icon {
  margin-right: 6px;
  display: flex;
  align-items: center;
}

.interactive-button .loading-spinner {
  margin-right: 6px;
}

.interactive-button .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Color Swatch Interactions */
.interactive-swatch {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all var(--animation-duration-fast) var(--animation-easing);
  border: 2px solid transparent;
}

.interactive-swatch:hover:not(.disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.interactive-swatch.selected {
  border-color: #007acc;
  box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.3);
}

.interactive-swatch .swatch-color {
  width: 100%;
  height: 100%;
  transition: all var(--animation-duration-fast) var(--animation-easing);
}

.interactive-swatch.size-small {
  width: 24px;
  height: 24px;
}

.interactive-swatch.size-medium {
  width: 40px;
  height: 40px;
}

.interactive-swatch.size-large {
  width: 64px;
  height: 64px;
}

.interactive-swatch .selection-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: scaleIn var(--animation-duration-fast) var(--animation-easing);
}

.interactive-swatch .hover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px;
  font-size: 10px;
  text-align: center;
  animation: slideInFromLeft var(--animation-duration-fast)
    var(--animation-easing);
}

/* Animated Tooltip */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.animated-tooltip {
  position: absolute;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  transition: all var(--animation-duration-fast) var(--animation-easing);
}

.animated-tooltip.visible {
  opacity: 1;
  animation: scaleIn var(--animation-duration-fast) var(--animation-easing);
}

.animated-tooltip.position-top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
}

.animated-tooltip.position-bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(8px);
}

.animated-tooltip.position-left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%) translateX(-8px);
}

.animated-tooltip.position-right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%) translateX(8px);
}

.animated-tooltip .tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 4px solid transparent;
}

.animated-tooltip.position-top .tooltip-arrow {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: rgba(0, 0, 0, 0.9);
}

.animated-tooltip.position-bottom .tooltip-arrow {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: rgba(0, 0, 0, 0.9);
}

.animated-tooltip.position-left .tooltip-arrow {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: rgba(0, 0, 0, 0.9);
}

.animated-tooltip.position-right .tooltip-arrow {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: rgba(0, 0, 0, 0.9);
}

/* Feedback Toast */
.feedback-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  min-width: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.feedback-toast.entering {
  animation: notificationSlideIn var(--animation-duration-normal)
    var(--animation-easing);
}

.feedback-toast.exiting {
  animation: notificationSlideOut var(--animation-duration-normal)
    var(--animation-easing);
}

.feedback-toast .toast-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: white;
}

.feedback-toast.type-success .toast-content {
  background: #27ae60;
}

.feedback-toast.type-error .toast-content {
  background: #e74c3c;
}

.feedback-toast.type-warning .toast-content {
  background: #f39c12;
}

.feedback-toast.type-info .toast-content {
  background: #3498db;
}

.feedback-toast .toast-icon {
  margin-right: 8px;
  font-size: 16px;
  font-weight: bold;
}

.feedback-toast .toast-message {
  flex: 1;
  font-size: 14px;
}

.feedback-toast .toast-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 12px;
  opacity: 0.7;
  transition: opacity var(--animation-duration-fast) var(--animation-easing);
}

.feedback-toast .toast-close:hover {
  opacity: 1;
}

/* Loading Pulse */
.loading-pulse {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.loading-pulse .pulse-dot {
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite both;
}

.loading-pulse.size-small .pulse-dot {
  width: 4px;
  height: 4px;
}

.loading-pulse.size-medium .pulse-dot {
  width: 6px;
  height: 6px;
}

.loading-pulse.size-large .pulse-dot {
  width: 8px;
  height: 8px;
}

.loading-pulse.color-primary .pulse-dot {
  background: #007acc;
}

.loading-pulse.color-secondary .pulse-dot {
  background: #6c757d;
}

.loading-pulse.color-accent .pulse-dot {
  background: #e74c3c;
}

.loading-pulse .pulse-dot-1 {
  animation-delay: -0.32s;
}

.loading-pulse .pulse-dot-2 {
  animation-delay: -0.16s;
}

.loading-pulse .pulse-dot-3 {
  animation-delay: 0s;
}

/* Progress Ring */
.progress-ring {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.progress-ring-svg {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset var(--animation-duration-normal)
    var(--animation-easing);
}

.progress-ring-text {
  position: absolute;
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

/* Floating Action Button */
.floating-action-button {
  position: fixed;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: #007acc;
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all var(--animation-duration-normal) var(--animation-easing);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-action-button:hover:not(.disabled) {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.floating-action-button.pressed {
  transform: scale(1.05);
}

.floating-action-button.position-bottom-right {
  bottom: 24px;
  right: 24px;
}

.floating-action-button.position-bottom-left {
  bottom: 24px;
  left: 24px;
}

.floating-action-button.position-top-right {
  top: 24px;
  right: 24px;
}

.floating-action-button.position-top-left {
  top: 24px;
  left: 24px;
}

.floating-action-button .fab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

/* Card micro-interactions */
.micro-card {
  border-radius: 8px;
  transition: all var(--animation-duration-fast) var(--animation-easing);
  cursor: pointer;
}

.micro-card:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.micro-card.pressed {
  transform: translateY(0);
}

/* Tab micro-interactions */
.micro-tab {
  position: relative;
  padding: 8px 16px;
  cursor: pointer;
  transition: all var(--animation-duration-fast) var(--animation-easing);
  border-bottom: 2px solid transparent;
}

.micro-tab:hover:not(.disabled) {
  background: rgba(0, 122, 204, 0.05);
}

.micro-tab.hovered {
  color: #007acc;
}

.micro-tab.selected {
  color: #007acc;
  border-bottom-color: #007acc;
}

/* List item micro-interactions */
.micro-list-item {
  padding: 12px 16px;
  transition: all var(--animation-duration-fast) var(--animation-easing);
  cursor: pointer;
  border-left: 3px solid transparent;
}

.micro-list-item:hover:not(.disabled) {
  background: rgba(0, 122, 204, 0.05);
  border-left-color: rgba(0, 122, 204, 0.3);
  transform: translateX(2px);
}

.micro-list-item.selected {
  background: rgba(0, 122, 204, 0.1);
  border-left-color: #007acc;
}

/* Focus styles for accessibility */
.micro-interaction:focus-visible {
  outline: 2px solid #007acc;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .micro-interaction:hover:not(.disabled) {
    outline: 2px solid currentColor;
  }

  .interactive-swatch.selected {
    outline: 3px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .micro-interaction,
  .micro-interaction *,
  .animated-tooltip,
  .feedback-toast,
  .loading-pulse .pulse-dot,
  .progress-ring-circle,
  .floating-action-button {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }

  .micro-interaction:hover:not(.disabled) {
    transform: none;
  }
}

/* Print styles */
@media print {
  .floating-action-button,
  .feedback-toast,
  .animated-tooltip {
    display: none !important;
  }
}
