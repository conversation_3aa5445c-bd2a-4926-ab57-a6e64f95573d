/**
 * Color blindness simulation utilities
 * Based on research from https://www.color-blindness.com/
 */

import { hexToRgb, rgbToHex } from './conversion';
import { calculateContrastRatio } from './analysis';

export type ColorBlindnessType =
  | 'normal'
  | 'protanopia' // Red-blind
  | 'deuteranopia' // Green-blind
  | 'tritanopia' // Blue-blind
  | 'protanomaly' // Red-weak
  | 'deuteranomaly' // Green-weak
  | 'tritanomaly' // Blue-weak
  | 'achromatopsia' // Total color blindness
  | 'achromatomaly'; // Partial color blindness

// Color transformation matrices for different types of color blindness
const COLOR_BLIND_MATRICES: Record<ColorBlindnessType, number[][]> = {
  normal: [
    [1, 0, 0],
    [0, 1, 0],
    [0, 0, 1],
  ],
  protanopia: [
    [0.567, 0.433, 0],
    [0.558, 0.442, 0],
    [0, 0.242, 0.758],
  ],
  protanomaly: [
    [0.817, 0.183, 0],
    [0.333, 0.667, 0],
    [0, 0.125, 0.875],
  ],
  deuteranopia: [
    [0.625, 0.375, 0],
    [0.7, 0.3, 0],
    [0, 0.3, 0.7],
  ],
  deuteranomaly: [
    [0.8, 0.2, 0],
    [0.258, 0.742, 0],
    [0, 0.142, 0.858],
  ],
  tritanopia: [
    [0.95, 0.05, 0],
    [0, 0.433, 0.567],
    [0, 0.475, 0.525],
  ],
  tritanomaly: [
    [0.967, 0.033, 0],
    [0, 0.733, 0.267],
    [0, 0.183, 0.817],
  ],
  achromatopsia: [
    [0.299, 0.587, 0.114],
    [0.299, 0.587, 0.114],
    [0.299, 0.587, 0.114],
  ],
  achromatomaly: [
    [0.618, 0.32, 0.062],
    [0.163, 0.775, 0.062],
    [0.163, 0.32, 0.516],
  ],
};

/**
 * Simulate how a color appears to someone with color blindness
 */
export function simulateColorBlindness(
  hex: string,
  type: ColorBlindnessType
): string {
  if (type === 'normal') {
    return hex;
  }

  const rgb = hexToRgb(hex);
  if (!rgb) {
    return hex;
  }

  const matrix = COLOR_BLIND_MATRICES[type];

  // Apply transformation matrix
  const r = Math.round(
    (matrix[0]?.[0] ?? 0) * rgb.r + (matrix[0]?.[1] ?? 0) * rgb.g + (matrix[0]?.[2] ?? 0) * rgb.b
  );
  const g = Math.round(
    (matrix[1]?.[0] ?? 0) * rgb.r + (matrix[1]?.[1] ?? 0) * rgb.g + (matrix[1]?.[2] ?? 0) * rgb.b
  );
  const b = Math.round(
    (matrix[2]?.[0] ?? 0) * rgb.r + (matrix[2]?.[1] ?? 0) * rgb.g + (matrix[2]?.[2] ?? 0) * rgb.b
  );

  // Clamp values to 0-255
  const clampedR = Math.max(0, Math.min(255, r));
  const clampedG = Math.max(0, Math.min(255, g));
  const clampedB = Math.max(0, Math.min(255, b));

  return rgbToHex({ r: clampedR, g: clampedG, b: clampedB });
}

/**
 * Calculate contrast ratio between two colors
 * Based on WCAG 2.1 guidelines
 */
export function getContrastRatio(hex1: string, hex2: string): number {
  const rgb1 = hexToRgb(hex1);
  const rgb2 = hexToRgb(hex2);

  if (!rgb1 || !rgb2) {
    return 1;
  }

  // Use the shared contrast ratio calculation from analysis.ts
  return calculateContrastRatio(rgb1, rgb2);
}

/**
 * Check if a color combination meets WCAG contrast requirements
 */
export function meetsContrastRequirements(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  fontSize: 'normal' | 'large' = 'normal'
): boolean {
  const ratio = getContrastRatio(foreground, background);

  if (level === 'AA') {
    return fontSize === 'large' ? ratio >= 3 : ratio >= 4.5;
  } else {
    return fontSize === 'large' ? ratio >= 4.5 : ratio >= 7;
  }
}

/**
 * Get a description of the color blindness type
 */
export function getColorBlindnessDescription(type: ColorBlindnessType): string {
  const descriptions: Record<ColorBlindnessType, string> = {
    normal: 'Normal color vision',
    protanopia: 'Red-blind (Protanopia) - Cannot see red light',
    deuteranopia: 'Green-blind (Deuteranopia) - Cannot see green light',
    tritanopia: 'Blue-blind (Tritanopia) - Cannot see blue light',
    protanomaly: 'Red-weak (Protanomaly) - Reduced sensitivity to red light',
    deuteranomaly:
      'Green-weak (Deuteranomaly) - Reduced sensitivity to green light',
    tritanomaly: 'Blue-weak (Tritanomaly) - Reduced sensitivity to blue light',
    achromatopsia: 'Total color blindness - See only in grayscale',
    achromatomaly: 'Partial color blindness - Severely reduced color vision',
  };

  return descriptions[type] || 'Unknown color blindness type';
}

/**
 * Get prevalence statistics for color blindness types
 */
export function getColorBlindnessPrevalence(type: ColorBlindnessType): {
  male: number;
  female: number;
  total: number;
} {
  const prevalence: Record<
    ColorBlindnessType,
    { male: number; female: number }
  > = {
    normal: { male: 92, female: 99.5 },
    protanopia: { male: 1.3, female: 0.02 },
    deuteranopia: { male: 1.2, female: 0.01 },
    tritanopia: { male: 0.001, female: 0.03 },
    protanomaly: { male: 1.3, female: 0.02 },
    deuteranomaly: { male: 5.0, female: 0.35 },
    tritanomaly: { male: 0.01, female: 0.01 },
    achromatopsia: { male: 0.003, female: 0.002 },
    achromatomaly: { male: 0.001, female: 0.001 },
  };

  const stats = prevalence[type] || { male: 0, female: 0 };
  const total = (stats.male + stats.female) / 2;

  return {
    male: stats.male,
    female: stats.female,
    total,
  };
}

/**
 * Suggest alternative colors that work better for color blind users
 */
export function suggestAccessibleColor(
  hex: string,
  background: string,
  type: ColorBlindnessType
): string {
  // Simulate how the color appears to someone with this type of color blindness
  const simulatedColor = simulateColorBlindness(hex, type);
  const simulatedBg = simulateColorBlindness(background, type);

  // Check if contrast is sufficient
  if (meetsContrastRequirements(simulatedColor, simulatedBg, 'AA')) {
    return hex; // Original color is fine
  }

  // Try to adjust the color to improve contrast
  const rgb = hexToRgb(hex);
  const bgRgb = hexToRgb(background);

  if (!rgb || !bgRgb) {
    return hex;
  }

  // Determine if we need to lighten or darken
  const bgLuminance = (bgRgb.r + bgRgb.g + bgRgb.b) / 3;
  const shouldLighten = bgLuminance < 128;

  // Adjust color in steps until we meet contrast requirements
  const adjustedRgb = { ...rgb };
  const step = shouldLighten ? 10 : -10;

  for (let i = 0; i < 20; i++) {
    adjustedRgb.r = Math.max(0, Math.min(255, adjustedRgb.r + step));
    adjustedRgb.g = Math.max(0, Math.min(255, adjustedRgb.g + step));
    adjustedRgb.b = Math.max(0, Math.min(255, adjustedRgb.b + step));

    const adjustedHex = rgbToHex(adjustedRgb);
    const simulatedAdjusted = simulateColorBlindness(adjustedHex, type);

    if (meetsContrastRequirements(simulatedAdjusted, simulatedBg, 'AA')) {
      return adjustedHex;
    }
  }

  // If we can't find a good color, return a high contrast default
  return shouldLighten ? '#FFFFFF' : '#000000';
}
