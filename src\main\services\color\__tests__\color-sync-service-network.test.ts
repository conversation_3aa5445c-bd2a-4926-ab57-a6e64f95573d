/**
 * @file color-sync-service-network.test.ts
 * @description Network failure and retry scenario tests for ColorSyncService
 *
 * Tests all network-related failure scenarios including:
 * - Network timeouts during push/pull operations
 * - Supabase connection failures
 * - Authentication token expiration
 * - RLS policy violations
 * - Batch sync partial failures
 * - Retry mechanisms with exponential backoff
 * - Error recovery and logging
 * - Sync status management during failures
 */

import {
  describe,
  test,
  expect,
  beforeEach,
  afterEach,
  vi,
  Mock,
} from 'vitest';
import { ColorSyncService } from '../color-sync.service';
import { ColorValidator } from '../color-validator.service';
import { GradientProcessor } from '../gradient-processor.service';

// Mock database for testing
const mockDb = {
  prepare: vi.fn(() => ({
    get: vi.fn(() => ({ id: 1 })),
    run: vi.fn(() => ({ changes: 1, lastInsertRowid: 1 })),
    all: vi.fn(() => []),
  })),
  exec: vi.fn(),
  close: vi.fn(),
} as any;

// Mock color repository
const mockColorRepository = {
  findAll: vi.fn(() => []),
  findById: vi.fn(() => null),
  insert: vi.fn(() => 'test-id'),
  update: vi.fn(() => true),
  softDelete: vi.fn(() => true),
  markAsSynced: vi.fn(() => true),
  invalidateOrphans: vi.fn(() => true),
  clearAll: vi.fn(() => true),
  findSoftDeleted: vi.fn(() => []),
  restoreRecord: vi.fn(() => true),
  bulkRestoreRecords: vi.fn(() => ({ success: true, restored: 0 })),
  cleanupOldSoftDeleted: vi.fn(() => ({ success: true, cleaned: 0 })),
  getUsageCounts: vi.fn(() => new Map()),
  getColorNameProductMap: vi.fn(() => new Map()),
  findUnsynced: vi.fn(() => []),
} as any;

// Mock Supabase client with network failure capabilities
const mockUpsert = vi.fn(() => ({
  data: null,
  error: null,
}));

const mockSelect = vi.fn(() => ({
  eq: vi.fn(() => ({
    data: [],
    error: null,
  })),
  data: [],
  error: null,
}));

const mockFrom = vi.fn(() => ({
  select: mockSelect,
  upsert: mockUpsert,
  insert: vi.fn(() => ({
    data: null,
    error: null,
  })),
  update: vi.fn(() => ({
    data: null,
    error: null,
  })),
  delete: vi.fn(() => ({
    data: null,
    error: null,
  })),
}));

const mockSupabaseClient = {
  from: mockFrom,
};

// Mock Supabase client module BEFORE importing the service
vi.mock('../../supabase-client', () => ({
  getSupabaseClient: vi.fn(),
  ensureAuthenticatedSession: vi.fn(),
}));

// Mock organization service for dependency sync
vi.mock('../../../db/services/organization.service', () => ({
  OrganizationService: class MockOrganizationService {
    constructor() {}
    syncOrganizationsFromSupabase = vi.fn(() => Promise.resolve());
  },
}));

describe('ColorSyncService Network Failure Tests', () => {
  let colorSyncService: ColorSyncService;
  let colorValidator: ColorValidator;
  let gradientProcessor: GradientProcessor;

  // Test color data for consistent testing
  const testColor = {
    id: 'color-123',
    code: 'TEST-001',
    name: 'Test Red',
    hex: '#FF0000',
    cmyk: 'C:0 M:100 Y:100 K:0',
    rgb: { r: 255, g: 0, b: 0 },
    hsl: { h: 0, s: 100, l: 50 },
    lab: { l: 53, a: 80, b: 67 },
    product: 'Test Product',
    organizationId: 'org-123',
    isLibrary: false,
    notes: 'Test color',
    tags: 'red,primary',
  };

  // Helper function to create database row format from test color
  const createDbRowFromColor = (color: any) => ({
    id: color.id, // Direct UUID mapping
    code: color.code,
    display_name: color.name,
    hex: color.hex,
    color_spaces: JSON.stringify({
      cmyk: { c: 0, m: 100, y: 100, k: 0 },
      rgb: color.rgb,
      hsl: color.hsl,
      lab: color.lab,
    }),
    organization_id: color.organizationId,
    is_library: color.isLibrary ? 1 : 0,
    notes: color.notes,
    tags: color.tags,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  });

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks();

    // Reset Supabase client mocks to default behavior
    mockUpsert.mockResolvedValue({ data: null, error: null });
    mockSelect.mockReturnValue({
      eq: vi.fn(() => ({
        data: [],
        error: null,
      })),
      data: [],
      error: null,
    });

    // Import and set up the mocked functions
    const { getSupabaseClient, ensureAuthenticatedSession } = await import(
      '../../supabase-client'
    );

    // Setup default mock implementations
    vi.mocked(getSupabaseClient).mockReturnValue(mockSupabaseClient);
    vi.mocked(ensureAuthenticatedSession).mockResolvedValue({
      session: {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
        },
      },
      error: null,
    });

    // Create service instances
    colorValidator = new ColorValidator();
    gradientProcessor = new GradientProcessor();
    colorSyncService = new ColorSyncService(
      mockDb,
      mockColorRepository,
      colorValidator,
      gradientProcessor
    );
  });

  afterEach(() => {
    vi.resetAllMocks();
    vi.clearAllTimers();
  });

  describe('Network Timeout Scenarios', () => {
    test('should handle push operation timeout with retry', async () => {
      mockColorRepository.findById.mockReturnValue(
        createDbRowFromColor(testColor)
      );

      // Mock timeout errors on first two attempts, success on third
      let attemptCount = 0;
      mockUpsert.mockImplementation(() => {
        attemptCount++;
        if (attemptCount <= 2) {
          return Promise.reject(new Error('Network timeout'));
        }
        return Promise.resolve({ data: null, error: null });
      });

      const colorIds = ['color-123'];
      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 3,
          batchSize: 1,
        }
      );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(1);
      expect(result.errors).toHaveLength(0);
      expect(mockUpsert).toHaveBeenCalledTimes(3);
    }, 10000);

    test('should fail after max retry attempts on persistent timeout', async () => {
      mockColorRepository.findById.mockReturnValue(
        createDbRowFromColor(testColor)
      );

      // Mock persistent timeout
      mockUpsert.mockRejectedValue(new Error('Network timeout'));

      const colorIds = ['color-123'];
      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 2,
          batchSize: 1,
        }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(0);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain(
        'Failed to push color color-123 after 2 attempts'
      );
      expect(mockUpsert).toHaveBeenCalledTimes(2);
    }, 10000);

    test('should handle pull operation timeout during sync', async () => {
      // Mock timeout during color sync from Supabase
      mockSupabaseClient.from().select.mockImplementation(() => {
        throw new Error('Connection timeout');
      });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle connection timeout during connectivity check', async () => {
      // Mock organization exists locally
      mockDb.prepare().get.mockReturnValue({ id: 1 });

      // Mock timeout on connectivity test
      mockSupabaseClient
        .from()
        .select.mockRejectedValue(new Error('Connection timeout'));

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });
  });

  describe('Supabase Connection Failures', () => {
    test('should handle DNS resolution failures', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('getaddrinfo ENOTFOUND'));

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('getaddrinfo ENOTFOUND');
    });

    test('should handle SSL certificate errors', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('certificate verify failed'));

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('certificate verify failed');
    });

    test('should handle connection refused errors', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('ECONNREFUSED'));

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('ECONNREFUSED');
    });

    test('should handle HTTP 500 internal server errors', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: {
          message: 'Internal Server Error',
          code: '500',
          details: 'Database connection lost',
        },
      });

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow();
    });

    test('should handle HTTP 502 bad gateway errors', async () => {
      mockSupabaseClient.from().select.mockReturnValue({
        data: null,
        error: {
          message: 'Bad Gateway',
          code: '502',
          details: 'Upstream server error',
        },
      });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle HTTP 503 service unavailable errors', async () => {
      mockSupabaseClient.from().select.mockReturnValue({
        data: null,
        error: {
          message: 'Service Temporarily Unavailable',
          code: '503',
          details: 'Maintenance mode',
        },
      });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });
  });

  describe('Authentication Failures During Sync', () => {
    test('should handle expired JWT token during push', async () => {
      // Mock expired token
      const { ensureAuthenticatedSession } = await import(
        '../../supabase-client'
      );
      vi.mocked(ensureAuthenticatedSession).mockResolvedValue({
        session: null,
        error: 'JWT token expired',
      });

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('No authenticated session');
    });

    test('should handle invalid JWT token during pull', async () => {
      const { ensureAuthenticatedSession } = await import(
        '../../supabase-client'
      );
      vi.mocked(ensureAuthenticatedSession).mockResolvedValue({
        session: null,
        error: 'Invalid JWT signature',
      });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle refresh token expiration', async () => {
      const { ensureAuthenticatedSession } = await import(
        '../../supabase-client'
      );
      vi.mocked(ensureAuthenticatedSession).mockResolvedValue({
        session: null,
        error: 'Refresh token expired',
      });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle OAuth provider errors', async () => {
      const { ensureAuthenticatedSession } = await import(
        '../../supabase-client'
      );
      vi.mocked(ensureAuthenticatedSession).mockResolvedValue({
        session: null,
        error: 'OAuth provider temporarily unavailable',
      });

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('No authenticated session');
    });

    test('should handle user account suspended during sync', async () => {
      mockSupabaseClient.from().select.mockReturnValue({
        data: null,
        error: {
          message: 'User account suspended',
          code: 'PGRST116',
          details: 'Account temporarily disabled',
        },
      });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });
  });

  describe('RLS Policy Violations', () => {
    test('should handle organization access denied', async () => {
      mockSupabaseClient
        .from()
        .select()
        .eq.mockReturnValue({
          data: null,
          error: {
            message: 'RLS policy violation',
            code: 'PGRST116',
            hint: 'Policy check failed for organization access',
          },
        });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle insufficient permissions during push', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: {
          message: 'insufficient_privilege',
          code: 'PGRST116',
          hint: 'Insufficient permissions to insert',
        },
      });

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow();
    });

    test('should handle user not member of organization', async () => {
      mockSupabaseClient
        .from()
        .select()
        .eq.mockReturnValue({
          data: null,
          error: {
            message: 'Permission denied',
            code: 'PGRST116',
            hint: 'User is not a member of this organization',
          },
        });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });

    test('should handle row level security check failure', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: {
          message: 'new row violates row-level security policy',
          code: '42501',
          hint: 'RLS policy check failed',
        },
      });

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow();
    });

    test('should handle organization context missing in RLS', async () => {
      mockSupabaseClient.from().select.mockReturnValue({
        data: null,
        error: {
          message: 'No organization context available',
          code: 'PGRST116',
          hint: 'Current user session lacks organization context',
        },
      });

      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      expect(syncedColors).toEqual([]);
    });
  });

  describe('Batch Sync Failures', () => {
    test('should handle partial batch failures during push', async () => {
      const colorIds = ['color-1', 'color-2', 'color-3'];

      // Mock repository to return different colors
      mockColorRepository.findById.mockImplementation(id => ({
        ...testColor,
        id,
      }));

      // Mock failures for specific colors
      let callCount = 0;
      mockSupabaseClient.from().upsert.mockImplementation(() => {
        callCount++;
        if (callCount === 2) {
          return Promise.resolve({
            data: null,
            error: { message: 'Constraint violation', code: 'PGRST301' },
          });
        }
        return Promise.resolve({ data: null, error: null });
      });

      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 1,
          batchSize: 10,
        }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(2);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('color-2');
    });

    test('should handle database constraint violations in batch', async () => {
      const colorIds = ['color-1', 'color-2'];

      mockColorRepository.findById.mockImplementation(id => ({
        ...testColor,
        id,
      }));

      // Mock constraint violation
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: {
          message: 'duplicate key value violates unique constraint',
          code: '23505',
          details: 'Key (code, organization_id) already exists',
        },
      });

      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 1,
          batchSize: 10,
        }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(0);
      expect(result.errors).toHaveLength(2);
    });

    test('should handle large batch size causing memory issues', async () => {
      const largeColorIds = Array.from(
        { length: 10000 },
        (_, i) => `color-${i}`
      );

      mockColorRepository.findById.mockImplementation(id => ({
        ...testColor,
        id,
      }));

      // Mock memory error for large batches
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('JavaScript heap out of memory'));

      const result = await colorSyncService.pushColorsToSupabase(
        largeColorIds,
        'org-123',
        {
          retryAttempts: 1,
          batchSize: 10000, // Intentionally large
        }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(0);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle concurrent sync conflicts', async () => {
      const colorIds = ['color-1', 'color-2'];

      mockColorRepository.findById.mockImplementation(id => ({
        ...testColor,
        id,
      }));

      // Mock version conflict error
      mockSupabaseClient.from().upsert.mockReturnValue({
        data: null,
        error: {
          message: 'Resource was modified by another user',
          code: 'PGRST116',
          hint: 'Version conflict detected',
        },
      });

      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 1,
          batchSize: 10,
        }
      );

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(2);
    });
  });

  describe('Retry Logic with Exponential Backoff', () => {
    test('should implement exponential backoff correctly', async () => {
      vi.useFakeTimers();

      mockColorRepository.findById.mockReturnValue(testColor);

      // Mock failures that trigger retries
      let attemptCount = 0;
      mockSupabaseClient.from().upsert.mockImplementation(() => {
        attemptCount++;
        if (attemptCount <= 2) {
          return Promise.reject(new Error('Temporary network error'));
        }
        return Promise.resolve({ data: null, error: null });
      });

      const colorIds = ['color-123'];
      const resultPromise = colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 3,
          batchSize: 1,
        }
      );

      // Fast-forward through the delays
      await vi.runAllTimersAsync();

      const result = await resultPromise;

      expect(result.success).toBe(true);
      expect(attemptCount).toBe(3);

      vi.useRealTimers();
    }, 10000);

    test('should respect max retry attempts', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('Persistent error'));

      const colorIds = ['color-123'];
      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 2,
          batchSize: 1,
        }
      );

      expect(result.success).toBe(false);
      expect(mockSupabaseClient.from().upsert).toHaveBeenCalledTimes(2);
    });

    test('should not retry on authentication errors', async () => {
      const { ensureAuthenticatedSession } = await import(
        '../../supabase-client'
      );
      vi.mocked(ensureAuthenticatedSession).mockResolvedValue({
        session: null,
        error: 'Authentication failed',
      });

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('No authenticated session');
    });

    test('should not retry on validation errors', async () => {
      const invalidColor = {
        ...testColor,
        hex: 'invalid-hex',
        code: '',
      };

      mockColorRepository.findById.mockReturnValue(invalidColor);

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow();
    });
  });

  describe('Connection Recovery Scenarios', () => {
    test('should recover after temporary network interruption', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);

      // Mock network recovery after failure
      let networkDown = true;
      mockSupabaseClient.from().upsert.mockImplementation(() => {
        if (networkDown) {
          networkDown = false;
          return Promise.reject(new Error('Network unreachable'));
        }
        return Promise.resolve({ data: null, error: null });
      });

      const colorIds = ['color-123'];
      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 2,
          batchSize: 1,
        }
      );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(1);
    });

    test('should handle intermittent connection drops during pull', async () => {
      // Mock organization exists locally
      mockDb.prepare().get.mockReturnValue({ id: 1 });

      // Mock intermittent failure then success
      let callCount = 0;
      mockSupabaseClient.from().select.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          throw new Error('Connection reset');
        }
        return {
          count: 1,
          error: null,
        };
      });

      mockSupabaseClient
        .from()
        .select()
        .eq.mockReturnValue({
          data: [{ id: 'color-1', code: 'TEST-001', hex: '#FF0000' }], // Direct UUID mapping
          error: null,
        });

      // First call will fail, implementation should handle gracefully
      const syncedColors = await colorSyncService.syncColorsFromSupabase(
        'user-123',
        'org-123'
      );

      // Should return empty array on connection failure
      expect(syncedColors).toEqual([]);
    });

    test('should maintain sync status during recovery', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);

      // Mock failure then success
      let failureOccurred = false;
      mockSupabaseClient.from().upsert.mockImplementation(() => {
        if (!failureOccurred) {
          failureOccurred = true;
          return Promise.reject(new Error('Connection lost'));
        }
        return Promise.resolve({ data: null, error: null });
      });

      const colorIds = ['color-123'];
      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 2,
          batchSize: 1,
        }
      );

      expect(result.success).toBe(true);
      expect(mockColorRepository.markAsSynced).toHaveBeenCalledWith(
        'color-123'
      );
    });
  });

  describe('Sync Status After Failures', () => {
    test('should not mark colors as synced after push failure', async () => {
      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('Network error'));

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('Network error');

      expect(mockColorRepository.markAsSynced).not.toHaveBeenCalled();
    });

    test('should handle sync status reset for failed batches', async () => {
      const colorIds = ['color-1', 'color-2', 'color-3'];

      mockColorRepository.findById.mockImplementation(id => ({
        ...testColor,
        id,
      }));

      // Mock all failures
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('Database error'));

      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 1,
          batchSize: 10,
        }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(0);
      expect(mockColorRepository.markAsSynced).not.toHaveBeenCalled();
    });

    test('should track unsynced colors correctly after failures', () => {
      const unsyncedColors = [
        { id: 'color-1', code: 'FAIL-001', hex: '#FF0000' }, // Direct UUID mapping
        { id: 'color-2', code: 'FAIL-002', hex: '#00FF00' }, // Direct UUID mapping
      ];

      mockColorRepository.findUnsynced.mockReturnValue(unsyncedColors);

      const result = colorSyncService.getUnsyncedColors();

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('color-1');
      expect(result[1].id).toBe('color-2');
    });

    test('should provide accurate sync result statistics', async () => {
      const colorIds = ['color-1', 'color-2', 'color-3', 'color-4'];

      mockColorRepository.findById.mockImplementation(id => ({
        ...testColor,
        id,
      }));

      // Mock mixed success/failure
      let callCount = 0;
      mockSupabaseClient.from().upsert.mockImplementation(() => {
        callCount++;
        if (callCount === 2 || callCount === 4) {
          return Promise.resolve({
            data: null,
            error: { message: 'Validation error' },
          });
        }
        return Promise.resolve({ data: null, error: null });
      });

      const result = await colorSyncService.pushColorsToSupabase(
        colorIds,
        'org-123',
        {
          retryAttempts: 1,
          batchSize: 10,
        }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(2);
      expect(result.errors).toHaveLength(2);
      expect(result.warnings).toHaveLength(0);
    });
  });

  describe('Error Logging and Recovery', () => {
    test('should log network errors with appropriate detail', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      mockColorRepository.findById.mockReturnValue(testColor);
      mockSupabaseClient
        .from()
        .upsert.mockRejectedValue(new Error('ECONNRESET'));

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('ECONNRESET');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          '[ColorSyncService] Error in pushColorToSupabase:'
        ),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });

    test('should log authentication failures appropriately', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const { ensureAuthenticatedSession } = await import(
        '../../supabase-client'
      );
      vi.mocked(ensureAuthenticatedSession).mockResolvedValue({
        session: null,
        error: 'Token expired',
      });

      await expect(
        colorSyncService.pushColorToSupabase('color-123', 'org-123', 'user-123')
      ).rejects.toThrow('No authenticated session');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          'Cannot sync to Supabase - no authenticated session:'
        ),
        'Token expired'
      );

      consoleSpy.mockRestore();
    });

    test('should log partial sync failures in batch operations', async () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const supabaseColors = [
        { id: 'color-1', code: 'VALID-001', hex: '#FF0000' }, // Direct UUID mapping
        { id: 'color-2', code: 'INVALID-001', hex: null }, // Direct UUID mapping
      ];

      mockDb.prepare().get.mockReturnValue({ id: 1 });
      mockSupabaseClient.from().select().eq.mockReturnValue({
        data: supabaseColors,
        error: null,
      });
      mockSupabaseClient.from().select.mockReturnValue({
        count: 2,
        error: null,
      });

      mockColorRepository.insert.mockImplementation(colorData => {
        if (!colorData.hex) {
          throw new Error('Invalid color data');
        }
        return 'success-id';
      });

      await colorSyncService.syncColorsFromSupabase('user-123', 'org-123');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error syncing color INVALID-001:'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });

    test('should provide service capabilities information', () => {
      const serviceInfo = colorSyncService.getServiceInfo();

      expect(serviceInfo.name).toBe('ColorSyncService');
      expect(serviceInfo.version).toBe('1.0.0');
      expect(serviceInfo.capabilities).toContain(
        'Retry logic with exponential backoff'
      );
      expect(serviceInfo.capabilities).toContain('Error recovery and logging');
      expect(serviceInfo.capabilities).toContain('Authentication handling');
      expect(serviceInfo.capabilities).toContain('RLS policy compliance');
    });
  });
});
