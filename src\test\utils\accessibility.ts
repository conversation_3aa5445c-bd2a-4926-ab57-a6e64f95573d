import { Page } from 'playwright';

// Define axe result types since we don't have the library installed yet
interface AxeNode {
  html: string;
  // Add other properties as needed
}

interface AxeViolation {
  id: string;
  impact?: string;
  description: string;
  nodes: AxeNode[];
  help: string;
  helpUrl: string;
}

interface AxeResults {
  violations: AxeViolation[];
  passes: any[];
  incomplete: any[];
  inapplicable: any[];
}

// Mock AxeBuilder until the library is installed
// @ts-ignore - Intentionally unused
class MockAxeBuilder {
  // private _page: Page; // Unused

  constructor({ page }: { page: Page }) {
    // this._page = page; // Unused
    void page; // Acknowledge parameter
  }

  withTags(_tags: string[]): MockAxeBuilder {
    return this;
  }

  async analyze(): Promise<AxeResults> {
    // Return mock results for now
    return {
      violations: [],
      passes: [],
      incomplete: [],
      inapplicable: [],
    };
  }
}

// Use our mock implementation until the real one is available
const AxeBuilder = MockAxeBuilder;

export interface AccessibilityViolation {
  id: string;
  impact: string;
  description: string;
  nodes: string[];
  help: string;
  helpUrl: string;
}

export interface AccessibilityTestResult {
  url: string;
  violations: AccessibilityViolation[];
  passes: number;
  incomplete: number;
  inapplicable: number;
  timestamp: string;
}

export class AccessibilityTester {
  private results: AccessibilityTestResult[] = [];

  async runAudit(
    page: Page,
    _name: string = 'page'
  ): Promise<AccessibilityTestResult> {
    // Run axe accessibility audit
    const axeResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .analyze();

    // Map to simplified violation format
    const violations: AccessibilityViolation[] = axeResults.violations.map(
      (violation: AxeViolation) => ({
        id: violation.id,
        impact: violation.impact || 'minor',
        description: violation.description,
        nodes: violation.nodes.map((node: AxeNode) => node.html),
        help: violation.help,
        helpUrl: violation.helpUrl,
      })
    );

    const result: AccessibilityTestResult = {
      url: page.url(),
      violations,
      passes: axeResults.passes.length,
      incomplete: axeResults.incomplete.length,
      inapplicable: axeResults.inapplicable.length,
      timestamp: new Date().toISOString(),
    };

    this.results.push(result);
    return result;
  }

  async checkKeyboardNavigation(page: Page): Promise<boolean> {
    const interactiveElements = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
    ];

    // Get all focusable elements
    const focusableElements = await page.$$(interactiveElements.join(','));

    // Press Tab key to navigate through all elements
    let canReachAll = true;
    let reachedElements = 0;

    // Start with first element
    await page.keyboard.press('Tab');

    // Track up to 50 tab presses to avoid infinite loops
    for (let i = 0; i < Math.min(focusableElements.length, 50); i++) {
      // Get currently focused element
      const activeElement = await page.evaluate(() => {
        const active = document.activeElement;
        return active ? active.outerHTML : null;
      });

      if (activeElement) {
        reachedElements++;
      }

      // Press Tab to move to next element
      await page.keyboard.press('Tab');
    }

    // Check if we reached a reasonable number of elements
    canReachAll =
      reachedElements > 0 && reachedElements >= focusableElements.length * 0.8;

    return canReachAll;
  }

  async checkColorContrast(page: Page): Promise<boolean> {
    // This check is included in the axe audit, but we could add a specific test
    // that checks text elements specifically for proper color contrast
    const result = await this.runAudit(page, 'color-contrast');

    // Check for color contrast violations specifically
    const contrastViolations = result.violations.filter(
      v => v.id === 'color-contrast'
    );

    return contrastViolations.length === 0;
  }

  generateReport(): string {
    if (this.results.length === 0) {
      return 'No accessibility test results available.';
    }

    let report = '# Accessibility Test Results\n\n';

    this.results.forEach((result, index) => {
      report += `## Test Run ${index + 1} (${result.timestamp})\n`;
      report += `URL: ${result.url}\n`;
      report += `Passes: ${result.passes}\n`;
      report += `Violations: ${result.violations.length}\n\n`;

      if (result.violations.length > 0) {
        report += '### Violations\n\n';

        result.violations.forEach(violation => {
          report += `#### ${violation.id} (${violation.impact})\n`;
          report += `${violation.description}\n`;
          report += `Help: ${violation.help}\n`;
          report += `Help URL: ${violation.helpUrl}\n\n`;

          if (violation.nodes.length > 0) {
            report += 'Affected elements:\n';
            violation.nodes.forEach(node => {
              report += `- \`${node}\`\n`;
            });
          }

          report += '\n';
        });
      }
    });

    return report;
  }

  getResults(): AccessibilityTestResult[] {
    return this.results;
  }
}

export default AccessibilityTester;
