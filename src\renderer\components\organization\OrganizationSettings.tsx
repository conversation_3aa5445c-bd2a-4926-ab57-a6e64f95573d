/**
 * @file OrganizationSettings.tsx
 * @description Organization settings component with tabs for general, members, and security settings
 */

import React, { useState, useEffect } from 'react';
import { useOrganizationStoreWithAliases } from '../../store/organization.store';
import { Settings, Users, Shield, Loader2 } from 'lucide-react';
import { TeamSettings } from './TeamSettings';

interface TabConfig {
  value: string;
  label: string;
  icon: React.ReactNode;
}

export const OrganizationSettings: React.FC = () => {
  const { currentOrg, updateOrganization } = useOrganizationStoreWithAliases();
  const [activeTab, setActiveTab] = useState('general');
  const [isLoading, setIsLoading] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [orgName, setOrgName] = useState('');
  const [orgDescription, setOrgDescription] = useState('');

  useEffect(() => {
    if (currentOrg) {
      setOrgName(currentOrg.name || '');
      setOrgDescription(currentOrg.description || '');
    }
  }, [currentOrg]);

  const tabs: TabConfig[] = [
    {
      value: 'general',
      label: 'General',
      icon: <Settings className='w-4 h-4' />,
    },
    {
      value: 'members',
      label: 'Members',
      icon: <Users className='w-4 h-4' />,
    },
    {
      value: 'security',
      label: 'Security',
      icon: <Shield className='w-4 h-4' />,
    },
  ];

  const handleSaveGeneral = async () => {
    if (!currentOrg || !isDirty) {return;}

    setIsLoading(true);
    try {
      await updateOrganization(currentOrg.id, {
        name: orgName,
        description: orgDescription,
      });
      setIsDirty(false);
    } catch (error) {
      console.error('Failed to update organization:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (field === 'name') {
      setOrgName(value);
    } else if (field === 'description') {
      setOrgDescription(value);
    }
    setIsDirty(true);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className='space-y-6'>
            <h3 className='text-lg font-medium text-gray-900 mb-4'>
              General Settings
            </h3>

            <div className='space-y-4'>
              <div>
                <label
                  htmlFor='org-name'
                  className='block text-sm font-medium text-gray-700 mb-2'
                >
                  Organization Name
                </label>
                <input
                  id='org-name'
                  type='text'
                  value={orgName}
                  onChange={e => handleInputChange('name', e.target.value)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  placeholder='Enter organization name'
                />
              </div>

              <div>
                <label
                  htmlFor='org-description'
                  className='block text-sm font-medium text-gray-700 mb-2'
                >
                  Description
                </label>
                <textarea
                  id='org-description'
                  value={orgDescription}
                  onChange={e =>
                    handleInputChange('description', e.target.value)
                  }
                  rows={3}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  placeholder='Enter organization description'
                />
              </div>
            </div>

            <div className='pt-4'>
              <button
                onClick={handleSaveGeneral}
                disabled={!isDirty || isLoading}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  !isDirty || isLoading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                {isLoading ? (
                  <span className='flex items-center'>
                    <Loader2 className='w-4 h-4 mr-2 animate-spin' />
                    Saving...
                  </span>
                ) : (
                  'Save Changes'
                )}
              </button>
            </div>
          </div>
        );

      case 'members':
        return <TeamSettings />;

      case 'security':
        return (
          <div className='space-y-6'>
            <h3 className='text-lg font-medium text-gray-900 mb-4'>
              Security Settings
            </h3>

            <div className='bg-gray-50 rounded-lg p-4'>
              <h4 className='font-medium text-gray-900 mb-2'>
                Two-Factor Authentication
              </h4>
              <p className='text-sm text-gray-600 mb-3'>
                Require all members to enable 2FA for enhanced security.
              </p>
              <button className='text-sm text-blue-600 hover:text-blue-700'>
                Configure 2FA Requirements
              </button>
            </div>

            <div className='bg-gray-50 rounded-lg p-4'>
              <h4 className='font-medium text-gray-900 mb-2'>
                Allowed Email Domains
              </h4>
              <p className='text-sm text-gray-600 mb-3'>
                Restrict membership to specific email domains.
              </p>
              <button className='text-sm text-blue-600 hover:text-blue-700'>
                Configure Domain Restrictions
              </button>
            </div>

            <div className='bg-gray-50 rounded-lg p-4'>
              <h4 className='font-medium text-gray-900 mb-2'>Data Retention</h4>
              <p className='text-sm text-gray-600 mb-3'>
                Configure how long deleted data is retained.
              </p>
              <button className='text-sm text-blue-600 hover:text-blue-700'>
                Configure Retention Policy
              </button>
            </div>
          </div>
        );

      default:
        return <div>Tab content not found</div>;
    }
  };

  if (!currentOrg) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <Loader2 className='w-8 h-8 animate-spin mx-auto mb-4 text-gray-400' />
          <p className='text-gray-500'>Loading organization settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='flex flex-col h-full bg-white'>
      {/* Header */}
      <div className='border-b border-gray-200 px-6 py-4'>
        <h2 className='text-xl font-semibold text-gray-900'>
          Organization Settings
        </h2>
        <p className='text-sm text-gray-600 mt-1'>{currentOrg.name}</p>
      </div>

      {/* Tabs */}
      <div className='border-b border-gray-200 px-6'>
        <div className='flex space-x-8'>
          {tabs.map(tab => (
            <button
              key={tab.value}
              onClick={() => setActiveTab(tab.value)}
              className={`py-3 px-1 border-b-2 flex items-center space-x-2 transition-colors ${
                activeTab === tab.value
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.icon}
              <span className='font-medium'>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className='flex-1 overflow-auto p-6'>{renderTabContent()}</div>
    </div>
  );
};

export default OrganizationSettings;
