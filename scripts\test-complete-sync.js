#!/usr/bin/env node
/**
 * Complete test script for product-color sync and usage count functionality
 * Tests both the bulletproof sync and the UI display
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

const organizationId = '4047153f-7be8-490b-9cb2-a1e3ed04b92b';

function getDbPath() {
  const platform = os.platform();
  switch (platform) {
    case 'darwin':
      return path.join(os.homedir(), 'Library/Application Support/chroma-sync/chromasync.db');
    case 'win32':
      return path.join(os.homedir(), 'AppData/Roaming/chroma-sync/chromasync.db');
    case 'linux':
      return path.join(os.homedir(), '.config/chroma-sync/chromasync.db');
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
}

function runQuery(query, description) {
  const dbPath = getDbPath();
  
  if (!fs.existsSync(dbPath)) {
    console.log(`❌ Database not found: ${dbPath}`);
    return null;
  }

  try {
    const result = execSync(
      `sqlite3 "${dbPath}" "${query}"`,
      { encoding: 'utf8' }
    ).trim();
    
    console.log(`📊 ${description}: ${result}`);
    return result;
  } catch (error) {
    console.error(`❌ Error running query (${description}):`, error.message);
    return null;
  }
}

function checkProductColorSync() {
  console.log('\n🔗 PRODUCT-COLOR SYNC VERIFICATION');
  console.log('='.repeat(50));

  // Check total product-color relationships
  const totalCount = runQuery(
    "SELECT COUNT(*) FROM product_colors;",
    "Total product-color relationships"
  );

  // Check relationships for our organization
  const orgCount = runQuery(
    `SELECT COUNT(*) FROM product_colors pc JOIN products p ON pc.product_id = p.id JOIN organizations o ON p.organization_id = o.id WHERE o.external_id = '${organizationId}';`,
    "Relationships for target organization"
  );

  // Check if we have products
  const productCount = runQuery(
    `SELECT COUNT(*) FROM products p JOIN organizations o ON p.organization_id = o.id WHERE o.external_id = '${organizationId}';`,
    "Products in organization"
  );

  // Check if we have colors
  const colorCount = runQuery(
    `SELECT COUNT(*) FROM colors c JOIN organizations o ON c.organization_id = o.id WHERE o.external_id = '${organizationId}';`,
    "Colors in organization"
  );

  return {
    totalRelationships: parseInt(totalCount) || 0,
    orgRelationships: parseInt(orgCount) || 0,
    products: parseInt(productCount) || 0,
    colors: parseInt(colorCount) || 0
  };
}

function checkUsageCountsService() {
  console.log('\n🧮 USAGE COUNTS SERVICE VERIFICATION');
  console.log('='.repeat(50));

  // Sample some usage counts by directly querying like the service does
  const sampleResults = runQuery(
    `SELECT 
      c.external_id,
      c.name as color_name,
      COUNT(DISTINCT p.id) as usage_count,
      GROUP_CONCAT(DISTINCT p.name, '|') as product_names
    FROM colors c
    JOIN organizations o ON c.organization_id = o.id
    LEFT JOIN product_colors pc ON pc.color_id = c.id
    LEFT JOIN products p ON pc.product_id = p.id
    WHERE o.external_id = '${organizationId}'
    GROUP BY c.id, c.external_id, c.name
    ORDER BY usage_count DESC
    LIMIT 5;`,
    "Top 5 colors by usage (simulating ColorService.getColorUsageCounts)"
  );

  if (sampleResults) {
    console.log('\n📋 Sample Usage Data:');
    const lines = sampleResults.split('\n').filter(line => line.trim());
    lines.forEach((line, index) => {
      const [external_id, color_name, usage_count, product_names] = line.split('|');
      const products = product_names ? product_names.split(',').slice(0, 3) : [];
      console.log(`   ${index + 1}. ${color_name} (${external_id}): ${usage_count} products [${products.join(', ')}${products.length >= 3 ? '...' : ''}]`);
    });
  }

  return sampleResults ? sampleResults.split('\n').length : 0;
}

function checkDataIntegrity() {
  console.log('\n🔍 DATA INTEGRITY CHECKS');
  console.log('='.repeat(50));

  // Check for orphaned product-color relationships
  const orphanedPCCount = runQuery(
    "SELECT COUNT(*) FROM product_colors pc LEFT JOIN products p ON pc.product_id = p.id LEFT JOIN colors c ON pc.color_id = c.id WHERE p.id IS NULL OR c.id IS NULL;",
    "Orphaned product-color relationships"
  );

  // Check for colors without external_id
  const colorsWithoutExternalId = runQuery(
    `SELECT COUNT(*) FROM colors c JOIN organizations o ON c.organization_id = o.id WHERE o.external_id = '${organizationId}' AND (c.external_id IS NULL OR c.external_id = '');`,
    "Colors without external_id"
  );

  // Check for products without external_id
  const productsWithoutExternalId = runQuery(
    `SELECT COUNT(*) FROM products p JOIN organizations o ON p.organization_id = o.id WHERE o.external_id = '${organizationId}' AND (p.external_id IS NULL OR p.external_id = '');`,
    "Products without external_id"
  );

  return {
    orphanedRelationships: parseInt(orphanedPCCount) || 0,
    colorsWithoutId: parseInt(colorsWithoutExternalId) || 0,
    productsWithoutId: parseInt(productsWithoutExternalId) || 0
  };
}

function main() {
  console.log('🧪 CHROMASYNC PRODUCT-COLOR SYNC & USAGE COUNT TEST');
  console.log('='.repeat(60));
  console.log(`📍 Database: ${getDbPath()}`);
  console.log(`🏢 Organization: ${organizationId}`);

  // Check if database exists
  if (!fs.existsSync(getDbPath())) {
    console.log('\n❌ FAILURE: Database not found');
    console.log('   Ensure ChromaSync has been started and authenticated');
    return;
  }

  // Run all checks
  const syncResults = checkProductColorSync();
  const usageResults = checkUsageCountsService();
  const integrityResults = checkDataIntegrity();

  // Final assessment
  console.log('\n📊 FINAL ASSESSMENT');
  console.log('='.repeat(50));

  let issues = [];
  let successes = [];

  // Check sync results
  if (syncResults.orgRelationships >= 500) {
    successes.push(`✅ Product-color sync: ${syncResults.orgRelationships} relationships`);
  } else if (syncResults.orgRelationships > 0) {
    issues.push(`⚠️  Product-color sync: Only ${syncResults.orgRelationships} relationships (expected 500+)`);
  } else {
    issues.push(`❌ Product-color sync: No relationships found`);
  }

  // Check data integrity
  if (integrityResults.orphanedRelationships === 0) {
    successes.push(`✅ Data integrity: No orphaned relationships`);
  } else {
    issues.push(`⚠️  Data integrity: ${integrityResults.orphanedRelationships} orphaned relationships`);
  }

  if (integrityResults.colorsWithoutId === 0 && integrityResults.productsWithoutId === 0) {
    successes.push(`✅ External IDs: All records have external IDs`);
  } else {
    issues.push(`⚠️  External IDs: ${integrityResults.colorsWithoutId} colors + ${integrityResults.productsWithoutId} products missing IDs`);
  }

  // Check usage service capability
  if (usageResults > 0) {
    successes.push(`✅ Usage counting: Service can calculate usage for ${usageResults} colors`);
  } else {
    issues.push(`❌ Usage counting: Service unable to calculate usage`);
  }

  // Display results
  console.log('\n🎉 SUCCESSES:');
  successes.forEach(success => console.log(`   ${success}`));

  if (issues.length > 0) {
    console.log('\n🚨 ISSUES:');
    issues.forEach(issue => console.log(`   ${issue}`));
  }

  // Overall status
  const criticalIssues = issues.filter(issue => issue.includes('❌'));
  if (criticalIssues.length === 0) {
    if (issues.length === 0) {
      console.log('\n🎉 OVERALL STATUS: PERFECT! All systems working correctly.');
    } else {
      console.log('\n✅ OVERALL STATUS: GOOD. Minor issues detected but core functionality works.');
    }
    console.log('\n💡 NEXT STEPS:');
    console.log('   1. Start ChromaSync: npm run dev');
    console.log('   2. Check that color swatches show correct product counts');
    console.log('   3. Check that color table usage column shows real numbers');
    console.log('   4. Verify dropdowns show actual product lists');
  } else {
    console.log('\n❌ OVERALL STATUS: ISSUES DETECTED. Core functionality may not work.');
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('   1. Restart ChromaSync and complete authentication');
    console.log('   2. Wait for sync to complete');
    console.log('   3. Run this test again');
    console.log('   4. Check application logs for sync errors');
  }

  console.log('\n🧪 Test completed');
}

if (require.main === module) {
  main();
}
