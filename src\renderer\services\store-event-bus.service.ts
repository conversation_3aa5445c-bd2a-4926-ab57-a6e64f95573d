/**
 * @file store-event-bus.service.ts
 * @description Event bus service to decouple store dependencies and break circular imports
 */

export type StoreEvent = 
  | { type: 'ORGANIZATION_SWITCHED'; organizationId: string }
  | { type: 'ORGANIZATION_CLEARED' }
  | { type: 'DATA_REFRESH_REQUESTED'; source: string }
  | { type: 'SYNC_COMPLETED'; success: boolean; source: string };

type EventListener = (event: StoreEvent) => void;

class StoreEventBus {
  private listeners: Map<string, EventListener[]> = new Map();

  /**
   * Subscribe to events of a specific type
   */
  subscribe(eventType: string, listener: EventListener): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    
    this.listeners.get(eventType)!.push(listener);
    
    // Return unsubscribe function
    return () => {
      const eventListeners = this.listeners.get(eventType);
      if (eventListeners) {
        const index = eventListeners.indexOf(listener);
        if (index > -1) {
          eventListeners.splice(index, 1);
        }
      }
    };
  }

  /**
   * Emit an event to all subscribers
   */
  emit(event: StoreEvent): void {
    const eventListeners = this.listeners.get(event.type);
    if (eventListeners) {
      // Use setTimeout to ensure async execution and prevent blocking
      setTimeout(() => {
        eventListeners.forEach(listener => {
          try {
            listener(event);
          } catch (error) {
            console.error(`[StoreEventBus] Error in event listener for ${event.type}:`, error);
          }
        });
      }, 0);
    }
  }

  /**
   * Subscribe to multiple event types with a single listener
   */
  subscribeMultiple(eventTypes: string[], listener: EventListener): () => void {
    const unsubscribeFunctions = eventTypes.map(type => 
      this.subscribe(type, listener)
    );
    
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }

  /**
   * Clear all listeners (useful for cleanup)
   */
  clear(): void {
    this.listeners.clear();
  }

  /**
   * Get the number of listeners for debugging
   */
  getListenerCount(eventType?: string): number {
    if (eventType) {
      return this.listeners.get(eventType)?.length || 0;
    }
    
    let total = 0;
    this.listeners.forEach(listeners => {
      total += listeners.length;
    });
    return total;
  }
}

// Export singleton instance
export const storeEventBus = new StoreEventBus();

// Helper hooks for React components
export const useStoreEvent = (eventType: string, listener: EventListener, deps: any[] = []) => {
  const { useEffect } = require('react');
  
  useEffect(() => {
    const unsubscribe = storeEventBus.subscribe(eventType, listener);
    return unsubscribe;
  }, deps);
};

export const useMultipleStoreEvents = (eventTypes: string[], listener: EventListener, deps: any[] = []) => {
  const { useEffect } = require('react');
  
  useEffect(() => {
    const unsubscribe = storeEventBus.subscribeMultiple(eventTypes, listener);
    return unsubscribe;
  }, deps);
};