#!/usr/bin/env node

/**
 * Test script to verify library colors can be added to products
 * Usage: node scripts/test-library-colors.js <organization-id>
 */

const path = require('path');
const Database = require('better-sqlite3');
const { v4: uuidv4 } = require('uuid');

// Database path
const dbPath = path.join(
  process.env.HOME || process.env.USERPROFILE,
  process.platform === 'darwin' 
    ? 'Library/Application Support/chroma-sync/chromasync.db'
    : process.platform === 'win32'
    ? 'AppData/Roaming/chroma-sync/chromasync.db'
    : '.config/chroma-sync/chromasync.db'
);

console.log('🔍 Testing library color to product functionality...');
console.log(`Database path: ${dbPath}`);

// Connect to database
const db = new Database(dbPath);

// Test organization ID
const TEST_ORG_ID = process.argv[2];

if (!TEST_ORG_ID) {
  console.error('❌ Please provide organization ID as argument');
  console.log('Usage: node scripts/test-library-colors.js <organization-id>');
  
  // Show available organizations
  const orgs = db.prepare('SELECT external_id, name FROM organizations').all();
  console.log('\nAvailable organizations:');
  orgs.forEach(org => console.log(`  - ${org.external_id} (${org.name})`));
  
  process.exit(1);
}

// Get local org ID
const localOrg = db.prepare('SELECT id FROM organizations WHERE external_id = ?').get(TEST_ORG_ID);
if (!localOrg) {
  console.error('❌ Organization not found');
  process.exit(1);
}

console.log(`✅ Found organization (local ID: ${localOrg.id})`);

// Create test product
const testProductId = uuidv4();
const testProductName = `Test Product ${Date.now()}`;

console.log('\n📝 Creating test product...');
db.prepare(`
  INSERT INTO products (
    external_id, organization_id, name, 
    is_active, created_at, updated_at
  ) VALUES (?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
`).run(testProductId, TEST_ORG_ID, testProductName);

console.log('✅ Test product created');
console.log(`  ID: ${testProductId}`);
console.log(`  Name: ${testProductName}`);

// Create test library color
const testColorId = uuidv4();
const testColorName = `Library Color ${Date.now()}`;

console.log('\n🎨 Creating test library color...');
db.prepare(`
  INSERT INTO colors (
    external_id, organization_id, name, hex, code,
    cmyk_optimized, created_at, updated_at
  ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
`).run(
  testColorId, 
  localOrg.id, // Use local org ID for colors
  testColorName,
  '#FF5733',
  'TEST-001',
  JSON.stringify({ c: 0, m: 66, y: 80, k: 0 })
);

console.log('✅ Test color created');
console.log(`  ID: ${testColorId}`);
console.log(`  Name: ${testColorName}`);

// Test addColor functionality
console.log('\n🔗 Testing ProductService.addColor logic...');

// Get internal IDs
const product = db.prepare('SELECT id FROM products WHERE external_id = ? AND organization_id = ?').get(testProductId, localOrg.id);
const color = db.prepare('SELECT id FROM colors WHERE external_id = ? AND organization_id = ?').get(testColorId, localOrg.id);

console.log('  Product internal ID:', product?.id);
console.log('  Color internal ID:', color?.id);

if (!product || !color) {
  console.error('❌ Failed to find product or color with correct organization');
  
  // Debug: Check if color exists in different org
  const colorAnyOrg = db.prepare('SELECT id, organization_id FROM colors WHERE external_id = ?').get(testColorId);
  if (colorAnyOrg) {
    console.error(`  Color exists in org ${colorAnyOrg.organization_id}, expected ${localOrg.id}`);
  }
  
  process.exit(1);
}

// Add color to product
console.log('\n➕ Adding color to product...');
db.prepare(`
  INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order)
  VALUES (?, ?, 1)
`).run(product.id, color.id);

// Verify association
const association = db.prepare(`
  SELECT * FROM product_colors WHERE product_id = ? AND color_id = ?
`).get(product.id, color.id);

if (association) {
  console.log('✅ Color successfully added to product!');
  console.log('  Association:', association);
} else {
  console.error('❌ Failed to add color to product');
}

// Check product colors
console.log('\n📋 Product colors:');
const productColors = db.prepare(`
  SELECT c.external_id, c.name, c.hex, pc.display_order
  FROM product_colors pc
  JOIN colors c ON pc.color_id = c.id
  WHERE pc.product_id = ?
  ORDER BY pc.display_order
`).all(product.id);

productColors.forEach((c, i) => {
  console.log(`  ${i + 1}. ${c.name} (${c.hex}) - Order: ${c.display_order}`);
});

// Cleanup
console.log('\n🧹 Cleaning up test data...');
db.prepare('DELETE FROM product_colors WHERE product_id = ?').run(product.id);
db.prepare('DELETE FROM products WHERE external_id = ?').run(testProductId);
db.prepare('DELETE FROM colors WHERE external_id = ?').run(testColorId);

console.log('✨ Test complete!');
db.close();
