/**
 * @file external-deps.d.ts
 * @description Custom type definitions for external dependencies that lack proper TypeScript support
 * or have incomplete/problematic type definitions
 */

// ===== UNTYPED ELECTRON DEPENDENCIES =====

declare module 'electron-is-dev' {
  const isDev: boolean;
  export = isDev;
}

declare module 'electron-store' {
  export interface Options {
    defaults?: Record<string, any>;
    name?: string;
    cwd?: string;
    encryptionKey?: string | Buffer;
    fileExtension?: string;
    clearInvalidConfig?: boolean;
    serialize?: (value: any) => string;
    deserialize?: (text: string) => any;
    projectSuffix?: string;
    schema?: Record<string, any>;
    migrations?: Record<string, (store: Store) => void>;
    beforeEachMigration?: (
      store: Store,
      context: {
        fromVersion: string;
        toVersion: string;
        finalVersion: string;
        versions: string[];
      }
    ) => void;
    projectVersion?: string;
    accessPropertiesByDotNotation?: boolean;
    watch?: boolean;
  }

  export default class Store<T = Record<string, any>> {
    constructor(options?: Options);

    get<K extends keyof T>(key: K): T[K];
    get<K extends keyof T>(key: K, defaultValue: T[K]): T[K];
    get(key: string): any;
    get(key: string, defaultValue: any): any;

    set<K extends keyof T>(key: K, value: T[K]): void;
    set(key: string, value: any): void;
    set(object: Partial<T>): void;

    has(key: string): boolean;
    delete(key: string): void;
    clear(): void;

    onDidChange<K extends keyof T>(
      key: K,
      callback: (newValue: T[K], oldValue: T[K]) => void
    ): () => void;
    onDidAnyChange(callback: (newValue: T, oldValue: T) => void): () => void;

    size: number;
    store: T;
    path: string;

    openInEditor(): void;
  }
}

declare module 'electron-updater' {
  import { EventEmitter } from 'events';

  export interface ElectronUpdaterInfo {
    version: string;
    files: Array<{
      url: string;
      sha512: string;
      size: number;
    }>;
    path: string;
    sha512: string;
    releaseDate: string;
    releaseName?: string;
    releaseNotes?: string;
    stagingPercentage?: number;
  }

  export interface ElectronProgressInfo {
    total: number;
    delta: number;
    transferred: number;
    percent: number;
    bytesPerSecond: number;
  }

  export interface ElectronUpdateCheckResult {
    updateInfo: ElectronUpdaterInfo;
    downloadPromise?: Promise<any> | null;
    cancellationToken?: any;
    versionInfo: ElectronUpdaterInfo;
  }

  export interface ElectronUpdateDownloadedEvent {
    downloadedFile: string;
    updateInfo: ElectronUpdaterInfo;
  }

  export class ElectronAppUpdater extends EventEmitter {
    autoDownload: boolean;
    autoInstallOnAppQuit: boolean;
    allowPrerelease: boolean;
    fullChangelog: boolean;
    allowDowngrade: boolean;
    currentVersion: string;
    channel: string | null;
    requestHeaders: Record<string, string> | null;

    checkForUpdatesAndNotify(): Promise<ElectronUpdateCheckResult | null>;
    checkForUpdates(): Promise<ElectronUpdateCheckResult | null>;
    downloadUpdate(cancellationToken?: any): Promise<any>;
    quitAndInstall(isSilent?: boolean, isForceRunAfter?: boolean): void;

    on(event: 'checking-for-update', listener: () => void): this;
    on(
      event: 'update-available',
      listener: (info: ElectronUpdaterInfo) => void
    ): this;
    on(
      event: 'update-not-available',
      listener: (info: ElectronUpdaterInfo) => void
    ): this;
    on(event: 'error', listener: (error: Error) => void): this;
    on(
      event: 'download-progress',
      listener: (progress: ElectronProgressInfo) => void
    ): this;
    on(
      event: 'update-downloaded',
      listener: (event: ElectronUpdateDownloadedEvent) => void
    ): this;
  }

  export const autoUpdater: ElectronAppUpdater;
}

// ===== UTILITY LIBRARIES =====

declare module 'node-machine-id' {
  export function machineId(): Promise<string>;
  export function machineIdSync(): string;
}

declare module 'generic-pool' {
  export interface Pool<T> {
    acquire(): Promise<T>;
    release(resource: T): Promise<void>;
    destroy(resource: T): Promise<void>;
    drain(): Promise<void>;
    clear(): Promise<void>;

    size: number;
    available: number;
    borrowed: number;
    pending: number;
    max: number;
    min: number;

    isFull(): boolean;
    start(): void;
    stop(): void;
  }

  export interface Factory<T> {
    create(): Promise<T>;
    destroy(resource: T): Promise<void>;
    validate?(resource: T): Promise<boolean>;
  }

  export interface Options {
    max?: number;
    min?: number;
    acquireTimeoutMillis?: number;
    createTimeoutMillis?: number;
    destroyTimeoutMillis?: number;
    idleTimeoutMillis?: number;
    reapIntervalMillis?: number;
    returnToHead?: boolean;
    priorityRange?: number;
    fifo?: boolean;
    autostart?: boolean;
    evictionRunIntervalMillis?: number;
    numTestsPerEvictionRun?: number;
    softIdleTimeoutMillis?: number;
    testOnBorrow?: boolean;
    testOnReturn?: boolean;
    testWhileIdle?: boolean;
    maxWaitingClients?: number;
    errorMessage?: string;
    Promise?: any;
  }

  export function createPool<T>(
    factory: Factory<T>,
    options?: Options
  ): Pool<T>;
}

declare module 'hotkeys-js' {
  export interface KeyHandler {
    (keyboardEvent: KeyboardEvent, hotkeysEvent: HotkeysEvent): void | boolean;
  }

  export interface HotkeysEvent {
    key: string;
    method: string;
    scope: string;
    shortcut: string;
  }

  export interface FilterEvent {
    (event: KeyboardEvent): boolean;
  }

  export interface Options {
    scope?: string;
    element?: HTMLElement | null;
    keyup?: boolean;
    keydown?: boolean;
    capture?: boolean;
    splitKey?: string;
  }

  function hotkeys(key: string, handler: KeyHandler): void;
  function hotkeys(key: string, options: Options, handler: KeyHandler): void;
  function hotkeys(key: string, scope: string, handler: KeyHandler): void;

  namespace hotkeys {
    function unbind(
      key?: string,
      scope?: string | KeyHandler,
      handler?: KeyHandler
    ): void;
    function setScope(scope: string): void;
    function getScope(): string;
    function deleteScope(scope: string): void;
    function filter(filterFunction: FilterEvent): void;
    function trigger(shortcut: string, scope?: string): void;
    function isPressed(keyCode: number): boolean;
    function isPressed(keyCode: string): boolean;
    function getPressedKeyCodes(): number[];
    function getAllKeyCodes(): { [key: string]: number };
  }

  export = hotkeys;
}

// ===== DATA PROCESSING LIBRARIES =====

declare module 'cheerio' {
  export interface CheerioOptions {
    xmlMode?: boolean;
    decodeEntities?: boolean;
    lowerCaseAttributeNames?: boolean;
    recognizeSelfClosing?: boolean;
    recognizeCDATA?: boolean;
  }

  export interface CheerioSelector {
    (selector: string): CheerioStatic;
    (
      selector: string,
      context?: string | CheerioElement | CheerioElement[] | CheerioStatic
    ): CheerioStatic;
    (element: CheerioElement): CheerioStatic;
    (element: CheerioElement[]): CheerioStatic;
    (html: string): CheerioStatic;
  }

  export interface CheerioElement {
    tagName: string;
    type: string;
    name?: string;
    data?: string;
    children: CheerioElement[];
    parent?: CheerioElement;
    prev?: CheerioElement;
    next?: CheerioElement;
    startIndex?: number;
    endIndex?: number;
    attribs: { [attr: string]: string };
  }

  export interface CheerioStatic {
    // Core jQuery-like methods
    find(selector: string): CheerioStatic;
    parent(selector?: string): CheerioStatic;
    children(selector?: string): CheerioStatic;
    siblings(selector?: string): CheerioStatic;
    next(selector?: string): CheerioStatic;
    prev(selector?: string): CheerioStatic;

    // Content methods
    text(): string;
    text(text: string): CheerioStatic;
    html(): string | null;
    html(html: string): CheerioStatic;

    // Attribute methods
    attr(name: string): string | undefined;
    attr(name: string, value: string): CheerioStatic;
    removeAttr(name: string): CheerioStatic;
    hasClass(className: string): boolean;
    addClass(className: string): CheerioStatic;
    removeClass(className?: string): CheerioStatic;

    // Manipulation
    append(content: string | CheerioStatic): CheerioStatic;
    prepend(content: string | CheerioStatic): CheerioStatic;
    after(content: string | CheerioStatic): CheerioStatic;
    before(content: string | CheerioStatic): CheerioStatic;
    remove(selector?: string): CheerioStatic;
    empty(): CheerioStatic;

    // Iteration
    each(
      func: (index: number, element: CheerioElement) => void | boolean
    ): CheerioStatic;
    map<T>(func: (index: number, element: CheerioElement) => T): T[];

    // Properties
    length: number;
    [index: number]: CheerioElement;

    // Array-like methods
    get(): CheerioElement[];
    get(index: number): CheerioElement;
    eq(index: number): CheerioStatic;
    first(): CheerioStatic;
    last(): CheerioStatic;
    slice(start: number, end?: number): CheerioStatic;
  }

  export function load(html: string, options?: CheerioOptions): CheerioSelector;
  export const html: (options?: CheerioOptions) => string;
  export const xml: (options?: CheerioOptions) => string;
}

declare module 'csv-parser' {
  import { Transform } from 'stream';

  export interface Options {
    separator?: string;
    quote?: string;
    escape?: string;
    newline?: string;
    headers?: boolean | string[];
    mapHeaders?: (header: { header: string; index: number }) => string;
    mapValues?: (header: {
      header: string;
      index: number;
      value: string;
    }) => any;
    maxRowBytes?: number;
    skipLinesWithError?: boolean;
    skipEmptyLines?: boolean;
    strict?: boolean;
  }

  function csv(options?: Options): Transform;

  export = csv;
}

// Note: pdf-parse is not typed, this is a minimal type definition
interface PDFParseOptions {
  pagerender?: (pageData: any) => string;
  max?: number;
  version?: string;
}

interface PDFParseResult {
  numpages: number;
  numrender: number;
  info: any;
  metadata: any;
  text: string;
  version: string;
}

declare module 'pdf-parse' {
  function pdfParse(
    dataBuffer: Buffer,
    options?: PDFParseOptions
  ): Promise<PDFParseResult>;
  export = pdfParse;
}

// ===== UI LIBRARIES =====

// Note: react-window-infinite-loader is not typed, this is a minimal type definition
interface InfiniteLoaderProps {
  isItemLoaded: (index: number) => boolean;
  itemCount: number;
  loadMoreItems: (startIndex: number, stopIndex: number) => Promise<any> | void;
  threshold?: number;
  minimumBatchSize?: number;
  children: (props: {
    onItemsRendered: (props: {
      visibleStartIndex: number;
      visibleStopIndex: number;
      overscanStartIndex: number;
      overscanStopIndex: number;
    }) => void;
    ref: (ref: any) => void;
  }) => any;
}

declare module 'react-window-infinite-loader' {
  const InfiniteLoader: any;
  export default InfiniteLoader;
}

// ===== BUILD SYSTEM ENHANCEMENTS =====

declare module 'electron-packager' {
  export interface Options {
    all?: boolean;
    appBundleId?: string;
    appCategoryType?: string;
    appCopyright?: string;
    appVersion?: string;
    arch?: string | string[];
    asar?: boolean | object;
    buildVersion?: string;
    darwinDarkModeSupport?: boolean;
    derefSymlinks?: boolean;
    dir: string;
    electronVersion?: string;
    executableName?: string;
    extraResource?: string | string[];
    helperBundleId?: string;
    icon?: string;
    ignore?: RegExp | RegExp[] | string | string[] | Function;
    junk?: boolean;
    name?: string;
    out?: string;
    overwrite?: boolean;
    platform?: string | string[];
    portable?: boolean;
    prune?: boolean;
    quiet?: boolean;
    strictSSL?: boolean;
    tmpdir?: string;
    usageDescription?: { [key: string]: string };
    win32metadata?: object;
  }

  export interface FinalizePackageTargetsHookFunction {
    (targets: string[], callback: (err?: Error) => void): void;
  }

  export interface Result {
    finalPath: string;
    platform: string;
    arch: string;
  }

  function packager(options: Options): Promise<Result[]>;
  function packager(
    options: Options,
    callback: (err: Error | null, appPaths: string[]) => void
  ): void;

  export = packager;
}

declare module 'javascript-obfuscator' {
  export interface ObfuscatorOptions {
    compact?: boolean;
    controlFlowFlattening?: boolean;
    controlFlowFlatteningThreshold?: number;
    deadCodeInjection?: boolean;
    deadCodeInjectionThreshold?: number;
    debugProtection?: boolean;
    debugProtectionInterval?: boolean | number;
    disableConsoleOutput?: boolean;
    domainLock?: string[];
    exclude?: string[];
    forceTransformStrings?: string[];
    identifierNamesGenerator?:
      | 'hexadecimalNumber'
      | 'mangled'
      | 'mangledShuffled';
    identifiersDictionary?: string[];
    identifiersPrefix?: string;
    inputFileName?: string;
    log?: boolean;
    numbersToExpressions?: boolean;
    optionsPreset?:
      | 'default'
      | 'low-obfuscation'
      | 'medium-obfuscation'
      | 'high-obfuscation';
    renameGlobals?: boolean;
    renameProperties?: boolean;
    renamePropertiesMode?: 'safe' | 'unsafe';
    reservedNames?: string[];
    reservedStrings?: string[];
    seed?: number;
    selfDefending?: boolean;
    simplify?: boolean;
    sourceMap?: boolean;
    sourceMapBaseUrl?: string;
    sourceMapFileName?: string;
    sourceMapMode?: 'separate' | 'inline';
    splitStrings?: boolean;
    splitStringsChunkLength?: number;
    stringArray?: boolean;
    stringArrayCallsTransform?: boolean;
    stringArrayCallsTransformThreshold?: number;
    stringArrayEncoding?: boolean | string[];
    stringArrayIndexShift?: boolean;
    stringArrayRotate?: boolean;
    stringArrayShuffle?: boolean;
    stringArrayWrappersCount?: number;
    stringArrayWrappersChainedCalls?: boolean;
    stringArrayWrappersParametersMaxCount?: number;
    stringArrayWrappersType?: 'variable' | 'function';
    stringArrayThreshold?: number;
    target?: 'browser' | 'browser-no-eval' | 'node';
    transformObjectKeys?: boolean;
    unicodeEscapeSequence?: boolean;
  }

  export interface ObfuscationResult {
    getObfuscatedCode(): string;
    getSourceMap(): string;
    toString(): string;
  }

  export function obfuscate(
    sourceCode: string,
    options?: ObfuscatorOptions
  ): ObfuscationResult;
}

// ===== ENVIRONMENT ENHANCEMENTS =====

// Extend existing modules with missing types
declare module 'ajv' {
  export interface ValidateFunction<T = unknown> {
    (data: any): data is T;
    errors?: ErrorObject[] | null;
    schema: AnySchema;
    schemaEnv: SchemaEnv;
  }

  export interface ErrorObject {
    keyword: string;
    instancePath: string;
    schemaPath: string;
    data?: any;
    message?: string;
    params?: Record<string, any>;
  }

  export interface AnySchema {
    [key: string]: any;
  }

  export interface SchemaEnv {
    schema: AnySchema;
    root: SchemaEnv;
    baseId: string;
  }
}

// No conflicting module augmentations

// Export for proper module resolution
export {};
