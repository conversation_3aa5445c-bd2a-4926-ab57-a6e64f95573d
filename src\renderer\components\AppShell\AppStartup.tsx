/**
 * @file AppStartup.tsx
 * @description Application startup screen component
 */

import React from 'react';
import SplashScreen from '../SplashScreen';

interface AppStartupProps {
  stage: 'initializing' | 'loading-database' | 'loading-ui' | 'preloading-components' | 'ready';
  progress: number;
}

/**
 * Application startup screen wrapper
 */
export const AppStartup: React.FC<AppStartupProps> = ({ stage, progress }) => {
  return <SplashScreen stage={stage} progress={progress} />;
};

export default AppStartup;