/**
 * @file cache-test.ts
 * @description Simple test utilities to verify cache performance
 */

import { colorCacheService } from '../services/color-cache.service';

/**
 * Test cache performance by simulating multiple API calls
 */
export async function testCachePerformance() {
  console.log('🧪 [Cache Test] Starting cache performance test...');

  // Reset metrics for clean test
  colorCacheService.resetMetrics();

  // Simulate multiple rapid calls (this would normally cause 5 API calls)
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(
      window.colorAPI.getAllWithUsage().then(result => {
        console.log(`🧪 [Cache Test] Call ${i + 1} completed:`, {
          success: result.success,
          colorsCount: result.data?.colors?.length || 0,
        });
        return result;
      })
    );
  }

  const startTime = Date.now();

  try {
    await Promise.all(promises);
    const endTime = Date.now();

    // Get final metrics
    const metrics = colorCacheService.getMetrics();

    console.log('🧪 [Cache Test] Test completed!', {
      duration: `${endTime - startTime}ms`,
      totalRequests: metrics.totalRequests,
      hits: metrics.hits,
      misses: metrics.misses,
      deduplicatedRequests: metrics.deduplicatedRequests,
      hitRate: `${metrics.hitRate}%`,
      apiCallReduction:
        metrics.deduplicatedRequests > 0
          ? `${Math.round((metrics.deduplicatedRequests / metrics.totalRequests) * 100)}%`
          : '0%',
    });

    // Log performance summary
    colorCacheService.logPerformanceSummary();

    return {
      success: true,
      metrics,
      duration: endTime - startTime,
    };
  } catch (error) {
    console.error('🧪 [Cache Test] Test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Test cache invalidation
 */
export async function testCacheInvalidation() {
  console.log('🧪 [Cache Test] Testing cache invalidation...');

  // Make initial call to populate cache
  await window.colorAPI.getAllWithUsage();

  const initialMetrics = colorCacheService.getMetrics();
  console.log('🧪 [Cache Test] Initial cache state:', {
    cacheSize: initialMetrics.cacheSize,
    hits: initialMetrics.hits,
  });

  // Invalidate cache
  colorCacheService.invalidateAll();

  // Make another call (should be a cache miss)
  await window.colorAPI.getAllWithUsage();

  const finalMetrics = colorCacheService.getMetrics();
  console.log('🧪 [Cache Test] Post-invalidation state:', {
    cacheSize: finalMetrics.cacheSize,
    hits: finalMetrics.hits,
    misses: finalMetrics.misses,
  });

  return {
    success: true,
    invalidationWorked: finalMetrics.misses > initialMetrics.misses,
  };
}

/**
 * Test DATA_REFRESH_REQUESTED event cache invalidation
 */
export async function testDataRefreshCacheInvalidation() {
  console.log(
    '🧪 [Cache Test] Testing DATA_REFRESH_REQUESTED cache invalidation...'
  );

  const { storeEventBus } = await import('../services/store-event-bus.service');

  // Reset metrics for clean test
  colorCacheService.resetMetrics();

  // Make initial call to populate cache
  console.log('🧪 [Cache Test] Making initial API call to populate cache...');
  await window.colorAPI.getAllWithUsage();

  const initialMetrics = colorCacheService.getMetrics();
  console.log('🧪 [Cache Test] Initial cache state:', {
    cacheSize: initialMetrics.cacheSize,
    hits: initialMetrics.hits,
    misses: initialMetrics.misses,
  });

  // Emit DATA_REFRESH_REQUESTED event (simulating sync completion)
  console.log('🧪 [Cache Test] Emitting DATA_REFRESH_REQUESTED event...');
  storeEventBus.emit({
    type: 'DATA_REFRESH_REQUESTED',
    source: 'cache-test',
  });

  // Wait for event handling to complete
  await new Promise(resolve => setTimeout(resolve, 200));

  // Check if cache was invalidated by making another call
  console.log(
    '🧪 [Cache Test] Making second API call to check cache invalidation...'
  );
  await window.colorAPI.getAllWithUsage();

  const finalMetrics = colorCacheService.getMetrics();
  console.log('🧪 [Cache Test] Final cache state:', {
    cacheSize: finalMetrics.cacheSize,
    hits: finalMetrics.hits,
    misses: finalMetrics.misses,
  });

  // Cache should have been invalidated, so we should see a miss
  const cacheInvalidated = finalMetrics.misses > initialMetrics.misses;

  return {
    success: true,
    cacheInvalidated,
    initialMetrics,
    finalMetrics,
  };
}

/**
 * Make testing functions available globally in development
 */
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  (window as any).testCachePerformance = testCachePerformance;
  (window as any).testCacheInvalidation = testCacheInvalidation;
  (window as any).testDataRefreshCacheInvalidation =
    testDataRefreshCacheInvalidation;

  console.log('🧪 [Cache Test] Test functions available:');
  console.log('  - window.testCachePerformance()');
  console.log('  - window.testCacheInvalidation()');
  console.log('  - window.testDataRefreshCacheInvalidation()');
}
