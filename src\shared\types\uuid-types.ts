/**
 * @file uuid-types.ts
 * @description UUID-only type definitions for ChromaSync migration
 * 
 * This file contains the new type definitions that will replace the current
 * dual ID system with pure UUID primary keys following best practices.
 */

// ===== CORE UUID TYPES =====

export type UUID = string;

/**
 * Validation function for UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// ===== ORGANIZATION TYPES (UUID-ONLY) =====

export interface OrganizationUUID {
  id: UUID; // Primary key - was external_id
  name: string;
  slug: string;
  plan: 'free' | 'team' | 'enterprise';
  settings: OrganizationSettings;
  created_at: string;
  updated_at: string;
  // Runtime properties
  memberCount?: number;
  userRole?: 'owner' | 'admin' | 'member';
}

export interface OrganizationMemberUUID {
  organization_id: UUID; // Foreign key to OrganizationUUID.id
  user_id: UUID; // Foreign key to UserUUID.id
  role: 'owner' | 'admin' | 'member';
  joined_at: string;
  invited_by?: UUID;
  // Runtime properties
  user?: {
    id: UUID;
    email: string;
    name?: string;
  };
  isCurrentUser?: boolean;
}

export interface OrganizationInvitationUUID {
  id: UUID; // Primary key - was external_id
  organization_id: UUID; // Foreign key to OrganizationUUID.id
  email: string;
  role: 'admin' | 'member';
  invited_by: UUID; // Foreign key to UserUUID.id
  token: string;
  expires_at: string;
  accepted_at?: string;
  created_at: string;
}

export interface OrganizationSettings {
  allowedDomains?: string[];
  colorNamePattern?: string;
  requireApproval?: boolean;
  defaultColorProperties?: Record<string, any>;
}

// ===== COLOR TYPES (UUID-ONLY) =====

export interface ColorSourceUUID {
  id: UUID; // Primary key - new UUID field
  code: string;
  name: string;
  is_system: boolean;
  properties?: Record<string, any>;
}

export interface ColorUUID {
  id: UUID; // Primary key - was external_id
  organization_id: UUID; // Foreign key to OrganizationUUID.id
  source_id: UUID; // Foreign key to ColorSourceUUID.id
  name: string;
  display_name?: string;
  code?: string;
  hex: string;
  color_spaces: Record<string, any>; // JSON field for CMYK, RGB, LAB, etc.
  is_gradient: boolean;
  is_metallic: boolean;
  is_effect: boolean;
  is_library: boolean;
  gradient_colors?: string;
  notes?: string;
  tags?: string;
  properties?: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by?: UUID;
  user_id?: UUID;
  deleted_at?: string;
  device_id?: string;
  conflict_resolved_at?: string;
  is_synced: boolean;
  version: number;
}

export interface ColorLibraryUUID {
  id: UUID; // Primary key - new UUID field
  code: string;
  name: string;
  description?: string;
  is_system: boolean;
  version?: string;
  created_at: string;
  updated_at: string;
}

export interface LibraryColorUUID {
  id: UUID; // Primary key - was external_id
  library_id: UUID; // Foreign key to ColorLibraryUUID.id
  code: string;
  name: string;
  hex: string;
  cmyk?: string;
  rgb?: string;
  lab?: string;
  hsl?: string;
  notes?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface LibraryColorMetadataUUID {
  color_id: UUID; // Primary key - foreign key to LibraryColorUUID.id
  properties?: Record<string, any>;
  tags?: string;
  search_terms?: string;
  popularity_score: number;
  usage_count: number;
}

// ===== PRODUCT TYPES (UUID-ONLY) =====

export interface ProductUUID {
  id: UUID; // Primary key - was external_id
  organization_id: UUID; // Foreign key to OrganizationUUID.id
  name: string;
  description?: string;
  category?: string;
  type?: string;
  sku?: string;
  website?: string;
  datasheet_url?: string;
  price?: number;
  currency?: string;
  is_master: boolean;
  is_active: boolean;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by?: UUID;
  user_id?: UUID;
  deleted_at?: string;
  device_id?: string;
  conflict_resolved_at?: string;
  is_synced: boolean;
}

export interface DatasheetUUID {
  id: UUID; // Primary key - was external_id
  product_id: UUID; // Foreign key to ProductUUID.id
  organization_id: UUID; // Foreign key to OrganizationUUID.id
  name: string;
  url: string;
  file_type: string;
  is_external: boolean;
  description?: string;
  tags?: string;
  metadata?: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by?: UUID;
  updated_by?: UUID;
  user_id?: UUID;
  device_id?: string;
  deleted_at?: string;
  sync_version: number;
}

// ===== JUNCTION TABLES (UUID-ONLY) =====

export interface ProductColorUUID {
  product_id: UUID; // Foreign key to ProductUUID.id
  color_id: UUID; // Foreign key to ColorUUID.id
  organization_id: UUID; // Foreign key to OrganizationUUID.id
  display_order: number;
  added_at: string;
}

// ===== USER TYPES (ALREADY UUID) =====

export interface UserUUID {
  id: UUID; // Primary key - already UUID
  email: string;
  name?: string;
  display_name?: string;
  avatar_url?: string;
  preferences: Record<string, any>;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// ===== CREATE/UPDATE TYPES =====

export interface CreateOrganizationUUID {
  id?: UUID; // Optional - will be generated if not provided
  name: string;
  slug?: string; // Will be generated from name if not provided
  plan?: 'free' | 'team' | 'enterprise';
  settings?: OrganizationSettings;
}

export interface UpdateOrganizationUUID {
  name?: string;
  plan?: 'free' | 'team' | 'enterprise';
  settings?: OrganizationSettings;
}

export interface CreateColorUUID {
  id?: UUID; // Optional - will be generated if not provided
  organization_id: UUID;
  source_id?: UUID; // Optional - will default to user source
  name: string;
  display_name?: string;
  code?: string;
  hex: string;
  color_spaces?: Record<string, any>;
  is_gradient?: boolean;
  is_metallic?: boolean;
  is_effect?: boolean;
  is_library?: boolean;
  gradient_colors?: string;
  notes?: string;
  tags?: string;
  properties?: Record<string, any>;
}

export interface UpdateColorUUID {
  name?: string;
  display_name?: string;
  code?: string;
  hex?: string;
  color_spaces?: Record<string, any>;
  is_gradient?: boolean;
  is_metallic?: boolean;
  is_effect?: boolean;
  is_library?: boolean;
  gradient_colors?: string;
  notes?: string;
  tags?: string;
  properties?: Record<string, any>;
}

export interface CreateProductUUID {
  id?: UUID; // Optional - will be generated if not provided
  organization_id: UUID;
  name: string;
  description?: string;
  category?: string;
  type?: string;
  sku?: string;
  website?: string;
  datasheet_url?: string;
  price?: number;
  currency?: string;
  is_master?: boolean;
  metadata?: Record<string, any>;
}

export interface UpdateProductUUID {
  name?: string;
  description?: string;
  category?: string;
  type?: string;
  sku?: string;
  website?: string;
  datasheet_url?: string;
  price?: number;
  currency?: string;
  is_master?: boolean;
  is_active?: boolean;
  metadata?: Record<string, any>;
}

// ===== QUERY RESULT TYPES =====

export interface ColorWithProductsUUID extends ColorUUID {
  products?: Array<{
    product_id: UUID;
    product_name: string;
    display_order: number;
    added_at: string;
  }>;
}

export interface ProductWithColorsUUID extends ProductUUID {
  colors?: Array<{
    color_id: UUID;
    color_name: string;
    color_hex: string;
    display_order: number;
    added_at: string;
  }>;
}

export interface OrganizationWithStatsUUID extends OrganizationUUID {
  stats?: {
    memberCount: number;
    colorCount: number;
    productCount: number;
    relationshipCount: number;
  };
}

// ===== API RESPONSE TYPES =====

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// ===== MIGRATION HELPER TYPES =====

/**
 * Types for data during migration when both integer and UUID IDs exist
 */
export interface MigrationColorUUID extends Omit<ColorUUID, 'id'> {
  id: UUID;
  legacy_id?: number; // Temporary field during migration
}

export interface MigrationProductUUID extends Omit<ProductUUID, 'id'> {
  id: UUID;
  legacy_id?: number; // Temporary field during migration
}

export interface MigrationOrganizationUUID extends Omit<OrganizationUUID, 'id'> {
  id: UUID;
  legacy_id?: number; // Temporary field during migration
}

// ===== VALIDATION TYPES =====

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// ===== EXPORT CONVENIENCE TYPES =====

// Re-export common types with shorter names for convenience
export type Org = OrganizationUUID;
export type Color = ColorUUID;
export type Product = ProductUUID;
export type User = UserUUID;
export type ProductColor = ProductColorUUID;
export type OrgMember = OrganizationMemberUUID;