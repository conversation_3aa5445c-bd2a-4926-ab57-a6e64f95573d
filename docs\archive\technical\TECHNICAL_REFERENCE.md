# ChromaSync Technical Reference

This document provides a comprehensive technical reference for ChromaSync, including the API reference, terminology, and guidance for AI assistants.

## 1. LLM Reference (for AI Assistants)

This section provides guidance to AI assistants (like <PERSON>) when working with code in this repository.

### 1.1. Essential Commands

#### Development
```bash
npm install          # Install dependencies
npm run dev          # Start dev server with hot reload
npm start            # Build + start with debugging enabled
```

#### Testing
```bash
npm test             # Run all tests (Vitest)
npm run test:watch   # Watch mode for continuous testing
npm run test:coverage # Test coverage report
npm run lint         # Run ESLint
```

#### Building & Packaging
```bash
npm run build        # Build for production
npm run package      # Build installer for all platforms
```

### 1.2. Architecture Overview

ChromaSync is an Electron application with strict process separation:

- **Main Process**: Node.js backend - handles database, file system, cloud sync
- **Renderer Process**: React UI - no direct database/file system access
- **Preload Script**: Security bridge - exposes typed IPC channels only

### 1.3. Critical Architecture Rules

1.  **Database access ONLY in main process**
2.  **All IPC through preload**
3.  **No Node.js APIs in renderer**
4.  **Type safety required** (No `any` types)
5.  **Supabase access ONLY in main process**
6.  **Offline-first design**

### 1.4. Key Services & Patterns

- **Database Services** (`src/main/db/services/`): `ColorService`, `ProductService`, etc.
- **Cloud Sync** (`src/main/services/`): `RealtimeSyncService`, `OAuthService`.
- **State Management** (`src/renderer/store/`): Zustand stores.

## 2. API Reference

### 2.1. IPC API

All IPC calls follow this pattern:
`const result = await window.ipc.invoke('channel:action', ...arguments);`

**Channels:**
- `color:*`
- `product:*`
- `sync:*`
- `oauth:*`
- `datasheet:*`

#### Color API
- `window.ipc.invoke('color:getAll')`
- `window.ipc.invoke('color:add', color)`
- `window.ipc.invoke('color:update', id, updates)`
- `window.ipc.invoke('color:delete', id)`

### 2.2. Database Schema

- **Local**: SQLite
- **Cloud**: PostgreSQL (Supabase)

**Key Tables:**
- `colors`
- `products`
- `product_colors`
- `organizations`

### 2.3. Color Types & Validation

- **Interfaces**: `Color`, `Product`
- **Validation**: `validateHex`, `validateRGB`, etc.

## 3. Terminology

### 3.1. Color Science

- **Color Spaces**: CMYK, RGB, LAB, HSL, Hex
- **Color Properties**: `is_gradient`, `is_metallic`, etc.

### 3.2. Architecture

- **Organization ID**: UUID-based tenant identifier.
- **External ID**: Public-facing UUID.
- **Soft Delete**: Records are marked as deleted instead of being removed.

