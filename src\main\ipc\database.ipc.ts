/**
 * @file database.ipc.ts
 * @description IPC handlers for direct database operations
 */

import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import { getDatabase } from '../db/database';
import { getErrorMessage } from '../utils/error-utils';
import { registerHandlerSafely } from '../utils/ipcRegistry';

/**
 * Register IPC handlers for direct database operations
 * These handlers should only be used for maintenance/repair operations
 */
export function registerDatabaseHandlers(
  __mainWindow: BrowserWindow | null
): void {
  console.log('Registering database IPC handlers...');

  // Execute a SQL script for repair operations
  registerHandlerSafely(
    ipcMain,
    'database:executeScript',
    async (_event, { script }) => {
      try {
        if (!script) {
          return {
            success: false,
            message: 'No script provided',
          };
        }

        console.log('[database.ipc] Executing SQL repair script');

        // Get the database instance
        const db = getDatabase();

        // Split the script into statements
        const statements = script
          .split(';')
          .filter((stmt: string) => stmt.trim().length > 0);

        // Log of executed statements and their results
        const log = [];

        // Execute each statement in a transaction
        try {
          // Start a transaction to ensure all statements execute or none
          db.exec('BEGIN TRANSACTION;');

          for (const stmt of statements) {
            try {
              // Skip comment lines
              if (stmt.trim().startsWith('--')) {
                continue;
              }

              // Execute the statement
              const trimmedStmt = stmt.trim();
              if (trimmedStmt.length > 0) {
                if (trimmedStmt.toLowerCase().startsWith('select')) {
                  // For SELECT statements, capture the results
                  const results = db.prepare(trimmedStmt).all();
                  log.push({
                    statement:
                      trimmedStmt.substring(0, 100) +
                      (trimmedStmt.length > 100 ? '...' : ''),
                    results:
                      results.length > 10
                        ? `Returned ${results.length} rows. First 10: ${JSON.stringify(results.slice(0, 10))}`
                        : JSON.stringify(results),
                  });
                } else {
                  // For non-SELECT statements, execute and log changes
                  const info = db.prepare(trimmedStmt).run();
                  log.push({
                    statement:
                      trimmedStmt.substring(0, 100) +
                      (trimmedStmt.length > 100 ? '...' : ''),
                    changes: info.changes,
                  });
                }
              }
            } catch (stmtError) {
              // Log the error but continue with other statements
              log.push({
                statement:
                  stmt.trim().substring(0, 100) +
                  (stmt.trim().length > 100 ? '...' : ''),
                error:
                  stmtError instanceof Error
                    ? stmtError.message
                    : String(stmtError),
              });
            }
          }

          // Commit the transaction
          db.exec('COMMIT;');

          console.log('[database.ipc] Successfully executed SQL repair script');

          return {
            success: true,
            data: { log },
          };
        } catch (transactionError) {
          // Roll back the transaction on error
          db.exec('ROLLBACK;');
          throw transactionError;
        }
      } catch (error) {
        console.error('[database.ipc] Error executing SQL script:', error);
        return {
          success: false,
          message: `Failed to execute SQL script: ${getErrorMessage(error)}`,
        };
      }
    }
  );
}
