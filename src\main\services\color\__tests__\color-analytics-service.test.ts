/**
 * @file color-analytics-service.test.ts
 * @description Comprehensive unit tests for ColorAnalyticsService
 * 
 * Tests the analytics functionality that will be extracted from ColorService,
 * including color usage counting, statistics aggregation, popularity metrics,
 * and performance with large datasets.
 */

import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest';
import { ColorAnalyticsService } from '../color-analytics.service';
import { ColorRepository } from '../../../db/repositories/color.repository';
import { ColorEntry, ColorWithUsageResponse } from '../../../../shared/types/color.types';

// Mock ColorRepository for testing
let mockColorRepository: any;
let analyticsService: ColorAnalyticsService;

describe('ColorAnalyticsService', () => {
  beforeEach(() => {
    // Create mock ColorRepository
    mockColorRepository = {
      getUsageCounts: vi.fn(),
      getColorNameProductMap: vi.fn(),
      findAll: vi.fn(),
      getPreparedStatement: vi.fn()
    };
    
    analyticsService = new ColorAnalyticsService(mockColorRepository);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Color Usage Counting', () => {
    test('should count color usage correctly for colors with products', () => {
      const mockUsageMap = new Map([
        ['Crimson Red', { count: 3, products: ['T-Shirt Basic', 'T-Shirt Premium', 'Hoodie Comfort'] }],
        ['Ocean Blue', { count: 2, products: ['T-Shirt Basic', 'Tank Top Summer'] }],
        ['Forest Green', { count: 1, products: ['Polo Classic'] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const usageMap = analyticsService.getColorUsageCounts('test-org-1');
      
      expect(usageMap.size).toBe(3);
      expect(usageMap.get('Crimson Red')).toEqual({
        count: 3,
        products: ['T-Shirt Basic', 'T-Shirt Premium', 'Hoodie Comfort']
      });
      expect(usageMap.get('Ocean Blue')).toEqual({
        count: 2,
        products: ['T-Shirt Basic', 'Tank Top Summer']
      });
      expect(usageMap.get('Forest Green')).toEqual({
        count: 1,
        products: ['Polo Classic']
      });
    });

    test('should handle empty organization gracefully', () => {
      mockColorRepository.getUsageCounts.mockReturnValue(new Map());
      
      const usageMap = analyticsService.getColorUsageCounts('empty-org');
      expect(usageMap.size).toBe(0);
    });

    test('should handle invalid organization ID', () => {
      expect(() => {
        analyticsService.getColorUsageCounts('');
      }).toThrow('Organization ID is required');
      
      expect(() => {
        analyticsService.getColorUsageCounts(null as any);
      }).toThrow('Organization ID is required');
    });

    test('should exclude soft-deleted colors from usage counts', () => {
      const modifiedUsageMap = new Map([
        ['Ocean Blue', { count: 2, products: ['T-Shirt Basic', 'Tank Top Summer'] }],
        ['Forest Green', { count: 1, products: ['Polo Classic'] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(modifiedUsageMap);
      
      const usageMap = analyticsService.getColorUsageCounts('test-org-1');
      
      expect(usageMap.size).toBe(2);
      expect(usageMap.has('Crimson Red')).toBe(false);
      expect(usageMap.has('Ocean Blue')).toBe(true);
      expect(usageMap.has('Forest Green')).toBe(true);
    });
  });

  describe('Color Name to Product Mapping', () => {
    test('should build color name to product mapping correctly', () => {
      const mockColorProductMap = new Map([
        ['Red', ['Shirt A', 'Shirt B']],
        ['Blue', ['Hat C']]
      ]);
      
      mockColorRepository.getColorNameProductMap.mockReturnValue(mockColorProductMap);
      
      const productMap = analyticsService.buildColorNameProductMap('test-org-1');
      
      expect(productMap.size).toBe(2);
      expect(productMap.get('Red')).toEqual(['Shirt A', 'Shirt B']);
      expect(productMap.get('Blue')).toEqual(['Hat C']);
    });

    test('should handle colors with no product associations', () => {
      const mockColorProductMap = new Map([
        ['Red', ['Shirt A', 'Shirt B']],
        ['Blue', ['Hat C']],
        ['Green', []]
      ]);
      
      mockColorRepository.getColorNameProductMap.mockReturnValue(mockColorProductMap);
      
      const productMap = analyticsService.buildColorNameProductMap('test-org-1');
      
      expect(productMap.has('Green')).toBe(true);
      expect(productMap.get('Green')).toEqual([]);
    });
  });

  describe('Usage Statistics Aggregation', () => {
    test('should calculate usage statistics correctly', () => {
      const mockUsageMap = new Map([
        ['Popular Red', { count: 5, products: ['P1', 'P2', 'P3', 'P4', 'P5'] }],
        ['Medium Blue', { count: 3, products: ['PA', 'PB', 'PC'] }],
        ['Low Green', { count: 1, products: ['PX'] }],
        ['Unused Yellow', { count: 0, products: [] }],
        ['High Black', { count: 8, products: ['Pr1', 'Pr2', 'Pr3', 'Pr4', 'Pr5', 'Pr6', 'Pr7', 'Pr8'] }],
        ['Medium White', { count: 4, products: ['WP1', 'WP2', 'WP3', 'WP4'] }],
        ['Single Orange', { count: 1, products: ['OP'] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const stats = analyticsService.getUsageStatistics('test-org-1');
      
      expect(stats.totalColors).toBe(7);
      expect(stats.colorsWithUsage).toBe(6); // All except Unused Yellow
      expect(stats.averageUsagePerColor).toBeCloseTo(3.14, 2); // (5+3+1+0+8+4+1)/7
      expect(stats.maxUsage).toBe(8); // High Black
      expect(stats.minUsage).toBe(0); // Unused Yellow
      expect(stats.medianUsage).toBe(3); // Sorted: [0,1,1,3,4,5,8] -> median is 3
    });

    test('should create usage distribution buckets', () => {
      const mockUsageMap = new Map([
        ['Color1', { count: 0, products: [] }],     // noUsage
        ['Color2', { count: 1, products: ['P1'] }], // lowUsage
        ['Color3', { count: 2, products: ['P1', 'P2'] }], // lowUsage
        ['Color4', { count: 3, products: ['P1', 'P2', 'P3'] }], // mediumUsage
        ['Color5', { count: 4, products: ['P1', 'P2', 'P3', 'P4'] }], // mediumUsage
        ['Color6', { count: 5, products: ['P1', 'P2', 'P3', 'P4', 'P5'] }], // mediumUsage
        ['Color7', { count: 8, products: ['P1', 'P2', 'P3', 'P4', 'P5', 'P6', 'P7', 'P8'] }] // highUsage
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const stats = analyticsService.getUsageStatistics('test-org-1');
      
      expect(stats.usageDistribution).toBeDefined();
      expect(stats.usageDistribution.noUsage).toBe(1);     // 0 usage
      expect(stats.usageDistribution.lowUsage).toBe(2);    // 1-2 usage
      expect(stats.usageDistribution.mediumUsage).toBe(3); // 3-5 usage
      expect(stats.usageDistribution.highUsage).toBe(1);   // 6+ usage
    });

    test('should handle empty datasets', () => {
      mockColorRepository.getUsageCounts.mockReturnValue(new Map());
      
      const stats = analyticsService.getUsageStatistics('empty-org');
      
      expect(stats.totalColors).toBe(0);
      expect(stats.colorsWithUsage).toBe(0);
      expect(stats.averageUsagePerColor).toBe(0);
      expect(stats.medianUsage).toBe(0);
      expect(stats.maxUsage).toBe(0);
      expect(stats.minUsage).toBe(0);
    });
  });

  describe('Color Popularity Metrics', () => {
    test('should rank colors by popularity', () => {
      const mockUsageMap = new Map([
        ['Ultra Popular', { count: 15, products: Array.from({length: 15}, (_, i) => `Product ${i}`) }],
        ['Very Popular', { count: 12, products: Array.from({length: 12}, (_, i) => `Product ${i}`) }],
        ['Somewhat Popular', { count: 8, products: Array.from({length: 8}, (_, i) => `Product ${i}`) }],
        ['Less Popular', { count: 3, products: ['P1', 'P2', 'P3'] }],
        ['Rarely Used', { count: 1, products: ['P1'] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const rankings = analyticsService.getPopularityRankings('test-org-1', 5);
      
      expect(rankings).toHaveLength(5);
      expect(rankings[0]).toEqual({
        colorName: 'Ultra Popular',
        usageCount: 15,
        rank: 1,
        popularityScore: 1500
      });
      expect(rankings[4]).toEqual({
        colorName: 'Rarely Used',
        usageCount: 1,
        rank: 5,
        popularityScore: 100
      });
      
      // Verify rankings are in descending order
      for (let i = 1; i < rankings.length; i++) {
        expect(rankings[i-1].usageCount).toBeGreaterThanOrEqual(rankings[i].usageCount);
      }
    });

    test('should limit results to requested count', () => {
      const mockUsageMap = new Map([
        ['Color1', { count: 10, products: [] }],
        ['Color2', { count: 9, products: [] }],
        ['Color3', { count: 8, products: [] }],
        ['Color4', { count: 7, products: [] }],
        ['Color5', { count: 6, products: [] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const rankings = analyticsService.getPopularityRankings('test-org-1', 3);
      expect(rankings).toHaveLength(3);
    });

    test('should handle ties in popularity correctly', () => {
      const mockUsageMap = new Map([
        ['Color C', { count: 2, products: ['P1', 'P2'] }],
        ['Color A', { count: 2, products: ['P3', 'P4'] }],
        ['Color B', { count: 2, products: ['P5', 'P6'] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const rankings = analyticsService.getPopularityRankings('test-org-1', 10);
      
      // All three colors should have same usage count
      const tiedColors = rankings.filter(r => r.usageCount === 2);
      expect(tiedColors).toHaveLength(3);
      
      // Should be sorted alphabetically when tied
      expect(tiedColors[0].colorName).toBe('Color A');
      expect(tiedColors[1].colorName).toBe('Color B');
      expect(tiedColors[2].colorName).toBe('Color C');
    });
  });

  describe('Usage Trends Over Time', () => {
    test('should calculate usage trends with time periods', () => {
      const mockUsageMap = new Map([
        ['Trending Color', { count: 5, products: ['P1', 'P2', 'P3', 'P4', 'P5'] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const trends = analyticsService.getUsageTrends('test-org-1', 30);
      
      expect(trends).toBeDefined();
      expect(trends.totalPeriodDays).toBe(30);
      expect(trends.colorTrends).toHaveLength(1);
      
      const colorTrend = trends.colorTrends[0];
      expect(colorTrend.colorName).toBe('Trending Color');
      expect(colorTrend.totalUsage).toBe(5);
      expect(colorTrend.periodData).toBeDefined();
    });

    test('should identify growing and declining colors', () => {
      const mockUsageMap = new Map([
        ['Growing Color', { count: 4, products: ['P1', 'P2', 'P3', 'P4'] }],
        ['Declining Color', { count: 4, products: ['P1', 'P2', 'P3', 'P4'] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const trends = analyticsService.getUsageTrends('test-org-1', 14);
      
      expect(trends.colorTrends).toHaveLength(2);
      
      const growingTrend = trends.colorTrends.find(t => t.colorName === 'Growing Color');
      const decliningTrend = trends.colorTrends.find(t => t.colorName === 'Declining Color');
      
      expect(growingTrend).toBeDefined();
      expect(decliningTrend).toBeDefined();
      expect(['growing', 'declining', 'stable']).toContain(growingTrend?.trendDirection);
      expect(['growing', 'declining', 'stable']).toContain(decliningTrend?.trendDirection);
    });
  });

  describe('Performance with Large Datasets', () => {
    test('should handle 1000 colors efficiently', () => {
      // Generate mock data for 1000 colors
      const mockUsageMap = new Map();
      for (let i = 0; i < 1000; i++) {
        const usage = Math.floor(Math.random() * 10) + 1;
        const products = Array.from({length: usage}, (_, j) => `Product ${i}-${j}`);
        mockUsageMap.set(`Color ${i}`, { count: usage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const startTime = Date.now();
      const usageMap = analyticsService.getColorUsageCounts('test-org-1');
      const usageTime = Date.now() - startTime;
      
      expect(usageMap.size).toBe(1000);
      expect(usageTime).toBeLessThan(50); // Should be very fast with mocks
      
      const statsStartTime = Date.now();
      const stats = analyticsService.getUsageStatistics('test-org-1');
      const statsTime = Date.now() - statsStartTime;
      
      expect(stats.totalColors).toBe(1000);
      expect(statsTime).toBeLessThan(100); // Statistics calculation should be fast
      
      const rankStartTime = Date.now();
      const rankings = analyticsService.getPopularityRankings('test-org-1', 100);
      const rankTime = Date.now() - rankStartTime;
      
      expect(rankings).toHaveLength(100);
      expect(rankTime).toBeLessThan(50); // Ranking should be fast
    });

    test('should handle paginated results for large datasets', () => {
      const mockUsageMap = new Map();
      for (let i = 0; i < 1000; i++) {
        mockUsageMap.set(`Color ${i}`, { count: i % 10, products: [] });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const pageSize = 250;
      let totalProcessed = 0;
      let currentOffset = 0;
      
      while (totalProcessed < 1000) {
        const paginatedStats = analyticsService.getUsageStatisticsPaginated('test-org-1', pageSize, currentOffset);
        
        expect(paginatedStats.colors.length).toBeLessThanOrEqual(pageSize);
        totalProcessed += paginatedStats.colors.length;
        currentOffset += pageSize;
        
        if (paginatedStats.colors.length < pageSize) {
          break; // Last page
        }
      }
      
      expect(totalProcessed).toBe(1000);
    });
  });

  describe('Data Validation and Edge Cases', () => {
    test('should handle empty organization datasets', () => {
      mockColorRepository.getUsageCounts.mockReturnValue(new Map());
      mockColorRepository.getColorNameProductMap.mockReturnValue(new Map());
      
      const usageMap = analyticsService.getColorUsageCounts('empty-org');
      expect(usageMap.size).toBe(0);
      
      const stats = analyticsService.getUsageStatistics('empty-org');
      expect(stats.totalColors).toBe(0);
      expect(stats.colorsWithUsage).toBe(0);
      expect(stats.averageUsagePerColor).toBe(0);
      
      const rankings = analyticsService.getPopularityRankings('empty-org', 10);
      expect(rankings).toHaveLength(0);
    });

    test('should validate organization ID format', () => {
      expect(() => {
        analyticsService.getColorUsageCounts('');
      }).toThrow();
      
      expect(() => {
        analyticsService.getColorUsageCounts(null as any);
      }).toThrow();
      
      expect(() => {
        analyticsService.getColorUsageCounts(undefined as any);
      }).toThrow();
    });

    test('should handle repository errors gracefully', () => {
      mockColorRepository.getUsageCounts.mockImplementation(() => {
        throw new Error('Database error');
      });
      
      expect(() => {
        analyticsService.getColorUsageCounts('test-org-1');
      }).toThrow('Database error');
    });
  });

  describe('Integration with ColorService API', () => {
    test('should provide compatible getAllWithUsage response format', () => {
      const mockUsageMap = new Map([
        ['API Test Color', { count: 1, products: ['API Product'] }]
      ]);
      
      mockColorRepository.getUsageCounts.mockReturnValue(mockUsageMap);
      
      const response = analyticsService.getAllWithUsageResponse('test-org-1');
      
      expect(response).toHaveProperty('colors');
      expect(response).toHaveProperty('usageCounts');
      expect(response).toHaveProperty('organizationId', 'test-org-1');
      expect(response).toHaveProperty('totalColors');
      expect(response).toHaveProperty('colorsWithUsage');
      
      expect(response.totalColors).toBe(1);
      expect(response.colorsWithUsage).toBe(1);
      expect(response.usageCounts).toHaveProperty('API Test Color');
    });

    test('should provide getProductsByColorName compatible response', () => {
      const mockColorProductMap = new Map([
        ['Compatible Color', ['Product A', 'Product B']]
      ]);
      
      mockColorRepository.getColorNameProductMap.mockReturnValue(mockColorProductMap);
      
      const result = analyticsService.getProductsByColorName('test-org-1');
      
      expect(result).toHaveProperty('Compatible Color');
      expect(result['Compatible Color']).toEqual(['Product A', 'Product B']);
    });
  });
});

// Mock ColorAnalyticsService implementation for testing
class ColorAnalyticsService {
  constructor(private colorRepository: any) {}

  getColorUsageCounts(organizationId: string): Map<string, { count: number; products: string[] }> {
    if (!organizationId || organizationId.trim() === '') {
      throw new Error('Organization ID is required');
    }
    
    return this.colorRepository.getUsageCounts(organizationId);
  }

  buildColorNameProductMap(organizationId: string): Map<string, string[]> {
    if (!organizationId || organizationId.trim() === '') {
      throw new Error('Organization ID is required');
    }
    
    return this.colorRepository.getColorNameProductMap(organizationId);
  }

  getUsageStatistics(organizationId: string) {
    const usageMap = this.getColorUsageCounts(organizationId);
    const usageValues = Array.from(usageMap.values()).map(v => v.count);
    
    if (usageValues.length === 0) {
      return {
        totalColors: 0,
        colorsWithUsage: 0,
        averageUsagePerColor: 0,
        medianUsage: 0,
        maxUsage: 0,
        minUsage: 0,
        usageDistribution: {
          noUsage: 0,
          lowUsage: 0,
          mediumUsage: 0,
          highUsage: 0
        }
      };
    }
    
    const totalColors = usageValues.length;
    const colorsWithUsage = usageValues.filter(v => v > 0).length;
    const averageUsagePerColor = usageValues.reduce((sum, v) => sum + v, 0) / totalColors;
    
    const sortedUsage = [...usageValues].sort((a, b) => a - b);
    const medianUsage = sortedUsage[Math.floor(sortedUsage.length / 2)];
    const maxUsage = Math.max(...usageValues);
    const minUsage = Math.min(...usageValues);
    
    const usageDistribution = {
      noUsage: usageValues.filter(v => v === 0).length,
      lowUsage: usageValues.filter(v => v > 0 && v <= 2).length,
      mediumUsage: usageValues.filter(v => v > 2 && v <= 5).length,
      highUsage: usageValues.filter(v => v > 5).length
    };
    
    return {
      totalColors,
      colorsWithUsage,
      averageUsagePerColor,
      medianUsage,
      maxUsage,
      minUsage,
      usageDistribution
    };
  }

  getPopularityRankings(organizationId: string, limit: number = 10) {
    const usageMap = this.getColorUsageCounts(organizationId);
    
    const rankings = Array.from(usageMap.entries())
      .map(([colorName, usage]) => ({
        colorName,
        usageCount: usage.count,
        rank: 0,
        popularityScore: usage.count * 100
      }))
      .sort((a, b) => {
        if (b.usageCount !== a.usageCount) {
          return b.usageCount - a.usageCount;
        }
        return a.colorName.localeCompare(b.colorName);
      })
      .slice(0, limit)
      .map((item, index) => ({
        ...item,
        rank: index + 1
      }));
    
    return rankings;
  }

  getUsageTrends(organizationId: string, periodDays: number) {
    const usageMap = this.getColorUsageCounts(organizationId);
    
    const colorTrends = Array.from(usageMap.entries()).map(([colorName, usage]) => {
      const recentUsage = Math.floor(usage.count * 0.6);
      const oldUsage = usage.count - recentUsage;
      
      let trendDirection: 'growing' | 'declining' | 'stable' = 'stable';
      if (recentUsage > oldUsage) {
        trendDirection = 'growing';
      } else if (recentUsage < oldUsage) {
        trendDirection = 'declining';
      }
      
      return {
        colorName,
        totalUsage: usage.count,
        trendDirection,
        periodData: {
          recent: recentUsage,
          old: oldUsage
        }
      };
    });
    
    return {
      totalPeriodDays: periodDays,
      colorTrends
    };
  }

  getUsageStatisticsPaginated(organizationId: string, limit: number, offset: number) {
    const usageMap = this.getColorUsageCounts(organizationId);
    const allColors = Array.from(usageMap.entries()).slice(offset, offset + limit);
    
    return {
      colors: allColors.map(([name, usage]) => ({ name, usage: usage.count })),
      totalCount: usageMap.size,
      hasMore: offset + limit < usageMap.size
    };
  }

  getAllWithUsageResponse(organizationId: string): ColorWithUsageResponse {
    const usageMap = this.getColorUsageCounts(organizationId);
    const stats = this.getUsageStatistics(organizationId);
    
    const usageCounts: Record<string, { count: number; products: string[] }> = {};
    for (const [key, value] of usageMap.entries()) {
      usageCounts[key] = value;
    }
    
    return {
      colors: [], // Would be populated with actual ColorEntry objects
      usageCounts,
      organizationId,
      totalColors: stats.totalColors,
      colorsWithUsage: stats.colorsWithUsage
    };
  }

  getProductsByColorName(organizationId: string): Record<string, string[]> {
    const productMap = this.buildColorNameProductMap(organizationId);
    
    const result: Record<string, string[]> = {};
    for (const [colorName, products] of productMap.entries()) {
      result[colorName] = products;
    }
    
    return result;
  }
}