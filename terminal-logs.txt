12:43:05.509 [info]  [Logger] Logging configured for development mode
Sentry logging sink initialized
[SecureConfig] Environment check: {
  hasSupabase: false,
  hasZoho: false,
  supabaseUrl: 'missing',
  supabaseKey: 'missing',
  zohoId: undefined
}
[SecureConfig] No valid configuration found - services may be limited
[2025-07-14T11:43:05.538Z] [INFO] [service:OAuthService] OAuth service initialization
[2025-07-14T11:43:05.539Z] [INFO] [service:OAuthService] OAuth service initialized with modular architecture
[2025-07-14T11:43:05.569Z] [INFO] [service:ZohoEmailService] Zoho Email Service initialized with modular architecture
[UnifiedSync] 🚀 Unified Sync Manager created
[SyncStatusManager] ✅ Event listeners registered with cleanup mechanism
[SyncStatusManager] Using polling-based cache invalidation for reliability
[DEBUG] process.cwd(): /Users/<USER>/Desktop/Code Base/chromasync-mac
[DEBUG] dotenv.config() result: {
  parsed: {
    GEMINI_API_KEY: 'AIzaSyCDL7yUzxEYDaxvvC_Te7FrRCbB8OMhYUA',
    ENABLE_POWERSYNC: 'true',
    POWERSYNC_URL: 'https://685e771baad8257e4b385735.powersync.journeyapps.com',
    SUPABASE_URL: 'https://tzqnxhsnitogmtihhsrq.supabase.co',
    SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzODMyMTYsImV4cCI6MjA2Mzk1OTIxNn0.II5OQWN9uplEZLV20CJ1amObYldzZwlaynKMQ4-iIqY',
    SUPABASE_JWT_SECRET: ''
  }
}
[DEBUG] Environment after dotenv:
[DEBUG]   SUPABASE_URL: PRESENT
[DEBUG]   SUPABASE_ANON_KEY: PRESENT
[DEBUG]   ZOHO_CLIENT_ID: MISSING
[DEBUG]   SUPABASE_URL value (first 50 chars): https://tzqnxhsnitogmtihhsrq.supabase.co
[DEBUG]   SUPABASE_ANON_KEY length: 208
[DEBUG] Clearing secure config cache after dotenv loading...
[DEBUG] Secure config cache cleared, next access will reload from environment
[DB] App paths:
- process.cwd(): /Users/<USER>/Desktop/Code Base/chromasync-mac
- app.getAppPath(): /Users/<USER>/Desktop/Code Base/chromasync-mac
- app.getPath('userData'): /Users/<USER>/Library/Application Support/chroma-sync
[DB] Checking better-sqlite3.node locations:
- /Users/<USER>/Desktop/Code Base/chromasync-mac/node_modules/better-sqlite3/build/Release/better_sqlite3.node (exists: true)
- /Users/<USER>/Desktop/Code Base/chromasync-mac/node_modules/better-sqlite3/build/Release/better_sqlite3.node (exists: true)
[Protocol] Registered chromasync:// protocol handler
File logging initialized: /Users/<USER>/Library/Logs/ChromaSync
(node:24989) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Electron --trace-deprecation ...` to show where the warning was created)
[App] Electron ready, creating window...
[App] 🛡️  Initializing security configurations...
[CSP] Configuring CSP for production environment
[CSP] Generated CSP header: default-src 'none'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: file:; font-src 'self' data:; connect-src 'self' https://*.supabase.co https://fonts.googleapis.com wss://*.supabase.co; media-src 'none'; object-src 'none'; child-src 'none'; frame-src 'none'; worker-src 'self' blob:; manifest-src 'self'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests; block-all-mixed-content
[CSP] ✅ CSP headers configured via webRequest.onHeadersReceived
[CSP] 🛡️  Content Security Policy Status (Production)
[CSP] Valid: ✅
[CSP] ⚠️  Warnings:
[CSP]   - Production: 'unsafe-inline' in script-src reduces security - consider using nonces or hashes
[App] ✅ Security configurations initialized
[App] 🔍 About to start service initialization...
[App] Initializing application services...
12:43:05.646 [info]  [service] [Services] 🚀 Starting comprehensive service initialization...
[2025-07-14T11:43:05.647Z] [INFO] [service:SentryService] Sentry disabled or no DSN provided
12:43:05.647 [info]  [service] [Services] Sentry initialized successfully
12:43:05.647 [info]  [service] [Services] Starting database initialization...
[DB] Starting simplified database initialization...
[DB] Calling initializeDatabase function...
[DB] Initializing database at: /Users/<USER>/Library/Application Support/chroma-sync/chromasync.db
[DB] Loading better-sqlite3 module...
[DB] better-sqlite3 module loaded successfully
[DB] Creating database connection...
[DB] Database path: /Users/<USER>/Library/Application Support/chroma-sync/chromasync.db
[DB] Database connection created successfully
[DB] Configuring database settings...
[DB] Database configuration complete
[DB] Existing database found (schema version: 30)
[DB] ✅ Database verified in 16ms
[DB] Initializing performance optimizations...
[PerformanceInitializer] 🚀 Starting database performance initialization...
[PerformanceInitializer] 🔧 Applying core optimizations...
[QueryOptimizer] 🚀 Starting color/product browsing performance optimization...
[QueryOptimizer] 📊 Creating organization-first composite indexes...
[QueryOptimizer] ✅ Created index: idx_colors_org_active_browsing
[QueryOptimizer] ✅ Created index: idx_colors_org_source_active
[QueryOptimizer] ✅ Created index: idx_products_org_active_name
[QueryOptimizer] ✅ Created index: idx_product_colors_org_product_optimized
[QueryOptimizer] ✅ Created index: idx_product_colors_org_color_optimized
[QueryOptimizer] 📚 Creating covering indexes for query optimization...
[QueryOptimizer] ✅ Created covering index: idx_colors_findall_covering
[QueryOptimizer] ✅ Created covering index: idx_colors_usage_covering
[QueryOptimizer] ✅ Created covering index: idx_product_colors_usage_covering
[QueryOptimizer] ✅ Created covering index: idx_products_usage_covering
[QueryOptimizer] 🗂️  Creating materialized aggregation tables...
[QueryOptimizer] ✅ Created aggregation trigger
[QueryOptimizer] ✅ Created aggregation trigger
[QueryOptimizer] ✅ Materialized aggregation tables created
[QueryOptimizer] ⚙️  Optimizing SQLite settings for read-heavy workload...
[QueryOptimizer] ✅ SQLite settings optimized
[QueryOptimizer] 📈 Updating table statistics for query planner...
[QueryOptimizer] ✅ Table statistics updated
[QueryOptimizer] ✅ Performance optimization completed successfully
[PerformanceInitializer] Failed to mark optimizations as applied: TypeError: SQLite3 can only bind numbers, strings, bigints, buffers, and null
    at PerformanceInitializer.markOptimizationsApplied (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:174733:10)
    at PerformanceInitializer.initializeOptimizations (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:174653:18)
    at async initializeDatabase (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:132423:22)
    at async initDatabase (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:132491:10)
    at async initializeDatabaseService (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180006:15)
    at async initializeAllServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180078:22)
    at async initializeAppServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180790:27)
    at async /Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180828:7
[PerformanceInitializer] ❌ Performance initialization failed: TypeError: SQLite3 can only bind numbers, strings, bigints, buffers, and null
    at PerformanceInitializer.markOptimizationsApplied (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:174733:10)
    at PerformanceInitializer.initializeOptimizations (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:174653:18)
    at async initializeDatabase (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:132423:22)
    at async initDatabase (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:132491:10)
    at async initializeDatabaseService (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180006:15)
    at async initializeAllServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180078:22)
    at async initializeAppServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180790:27)
    at async /Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180828:7
[DB] ⚠️ Performance optimizations failed: [
  'SQLite3 can only bind numbers, strings, bigints, buffers, and null'
]
[DB] initializeDatabase function returned: SUCCESS
[DB] ✅ Database initialized successfully with pure UUID schema
12:43:05.672 [info]  [service] [Services] Database initialization result: SUCCESS
[2025-07-14T11:43:05.672Z] [INFO] [service:ServiceContainer, op:initialize] Initializing service container
[2025-07-14T11:43:05.672Z] [INFO] [service:ServiceContainer, op:configure] Configuring services
[ServiceContainer] Initializing database services with valid database connection
[DatasheetService] Constructor called
[DatasheetService] Database instance set
[DatasheetService] Creating optimized datasheets table...
[DatasheetService] Optimized datasheets table created successfully
[DatasheetService] Constructor completed
[2025-07-14T11:43:05.673Z] [INFO] [service:ServiceContainer] OAuth service initialization
[2025-07-14T11:43:05.673Z] [INFO] [service:ServiceContainer] OAuth service initialized with modular architecture
[SecureConfig] Environment check: {
  hasSupabase: true,
  hasZoho: false,
  supabaseUrl: 'present',
  supabaseKey: 'present',
  zohoId: undefined
}
[SecureConfig] Using environment variables
[2025-07-14T11:43:05.674Z] [INFO] [service:ServiceContainer, op:initialize] Initializing Zoho Token Manager
[2025-07-14T11:43:05.705Z] [ERROR] [service:ServiceContainer] Missing Zoho configuration
[2025-07-14T11:43:05.705Z] [ERROR] [service:ServiceContainer, op:initialize] Token manager initialization failed Error: Zoho Token Manager not configured
    at ZohoTokenManager.validateConfiguration (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:170196:13)
    at ZohoTokenManager.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:170089:12)
    at async descriptor.value (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:4732:18)
    at async descriptor.value (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:4712:26)
    at async ServiceContainer.initializeEmailServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171719:7)
    at async ServiceContainer.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171416:7)
    at async ServiceLocator.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171837:5)
    at async initializeServiceLocator (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180039:5)
    at async initializeAllServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180080:5)
    at async initializeAppServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180790:27)
[2025-07-14T11:43:05.705Z] [ERROR] [service:ZohoTokenManager, op:initialize] Unhandled error in ZohoTokenManager.initialize Error: Zoho Token Manager not configured
    at ZohoTokenManager.validateConfiguration (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:170196:13)
    at ZohoTokenManager.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:170089:12)
    at async descriptor.value (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:4732:18)
    at async descriptor.value (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:4712:26)
    at async ServiceContainer.initializeEmailServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171719:7)
    at async ServiceContainer.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171416:7)
    at async ServiceLocator.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171837:5)
    at async initializeServiceLocator (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180039:5)
    at async initializeAllServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180080:5)
    at async initializeAppServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180790:27)
[2025-07-14T11:43:05.705Z] [INFO] [service:ZohoTokenManager, op:timer] Timer "ZohoTokenManager.initialize": 31.65ms
[2025-07-14T11:43:05.705Z] [ERROR] [service:ZohoTokenManager, op:initialize] Operation failed: initialize Error: Zoho Token Manager not configured
    at ZohoTokenManager.validateConfiguration (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:170196:13)
    at ZohoTokenManager.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:170089:12)
    at async descriptor.value (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:4732:18)
    at async descriptor.value (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:4712:26)
    at async ServiceContainer.initializeEmailServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171719:7)
    at async ServiceContainer.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171416:7)
    at async ServiceLocator.initialize (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171837:5)
    at async initializeServiceLocator (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180039:5)
    at async initializeAllServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180080:5)
    at async initializeAppServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180790:27)
[2025-07-14T11:43:05.705Z] [WARN] [service:ServiceContainer] Email services initialization failed, continuing without email
[2025-07-14T11:43:05.705Z] [INFO] [service:ServiceContainer, op:initialize] Service container initialized successfully
12:43:05.705 [info]  [service] [Services] ServiceLocator initialized successfully
12:43:05.706 [info]  [service] [Services] Setting up shared folder management...
[SharedFolderManager] Resolved path: /Users/<USER>/Library/Application Support/chroma-sync/shared_data
[IPC Registry] Registered handler for 'shared-folder:get-path' with rate limiting: true
[IPC Registry] Registered handler for 'shared-folder:ensure-exists' with rate limiting: true
[IPC Registry] Registered handler for 'shared-folder:read-file' with rate limiting: true
[IPC Registry] Registered handler for 'shared-folder:write-file' with rate limiting: true
[IPC Registry] Registered handler for 'shared-folder:list-files' with rate limiting: true
12:43:05.706 [info]  [service] [Services] Shared folder service initialized successfully
12:43:05.706 [info]  [service] [Services] Initializing email service...
12:43:05.706 [error] [service] [Services] ❌ Failed to initialize email service: Error: Service 'zohoEmailService' not found. Make sure the container is initialized.
    at ServiceContainer.get (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171437:13)
    at ServiceLocator.getZohoEmailService (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:171973:29)
    at getZohoEmailService (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:172031:50)
    at initializeEmailService (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180063:30)
    at initializeAllServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180082:11)
    at async initializeAppServices (/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180790:27)
    at async /Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js:180828:7
12:43:05.706 [error] [service] [Services] Team invitation emails will not work until email service is configured
12:43:05.706 [info]  [service] [Services] ✅ All services initialized successfully in 60ms
[App] 🚀 Proceeding with IPC handler registration...
[IPC Registry] Removed handler for 'shared-folder:get-path'
[IPC Registry] Removed handler for 'shared-folder:ensure-exists'
[IPC Registry] Removed handler for 'shared-folder:read-file'
[IPC Registry] Removed handler for 'shared-folder:write-file'
[IPC Registry] Removed handler for 'shared-folder:list-files'
[IPC Registry] Registry reset completed
[IPC] Registry cleared
[IPC] 🚨 Registering critical handlers...
[IPC Registry] Registered handler for 'color:getAllWithUsage' with rate limiting: true
[IPC Registry] Registered handler for 'product:getAllWithColors' with rate limiting: true
[IPC Registry] Registered handler for 'color:getAll' with rate limiting: true
[IPC Registry] Registered handler for 'color:getProductsByColorName' with rate limiting: true
[IPC] 🔒 Debug handlers disabled in production mode for security
[IPC] ✅ Critical handlers registered successfully
[IPC] 🚀 Registering early handlers...
[IPC Registry] Registered handler for 'sync:login' with rate limiting: true
[IPC Registry] Registered handler for 'sync:accept-gdpr' with rate limiting: true
[IPC Registry] Registered handler for 'sync:start' with rate limiting: true
[IPC Registry] Registered handler for 'sync:status' with rate limiting: true
[IPC Registry] Registered handler for 'sync:logout' with rate limiting: true
[IPC Registry] Registered handler for 'sync:user' with rate limiting: true
[IPC Registry] Registered handler for 'sync:get-organizations' with rate limiting: true
[IPC Registry] Registered handler for 'sync:set-organization' with rate limiting: true
[IPC Registry] Registered handler for 'sync:get-auth-state' with rate limiting: true
[IPC Registry] Registered handler for 'sync:sync' with rate limiting: true
[IPC Registry] Registered handler for 'sync:get-progress' with rate limiting: true
[IPC Registry] Registered handler for 'sync:has-unsynced-local-changes' with rate limiting: true
[IPC Registry] Registered handler for 'sync:clear-not-found-deletes' with rate limiting: true
[IPC Registry] Registered handler for 'sync:get-queue-stats' with rate limiting: true
[IPC Registry] Registered handler for 'sync:get-config' with rate limiting: true
[IPC Registry] Registered handler for 'sync:get-metrics' with rate limiting: true
[IPC Registry] Registered handler for 'sync:get-status-report' with rate limiting: true
[IPC Registry] Registered handler for 'sync:update-user-activity' with rate limiting: true
[IPC Registry] Registered handler for 'sync:clear-outbox' with rate limiting: true
[IPC Registry] Registered handler for 'sync:debug-outbox' with rate limiting: true
[Sync] ✅ Simplified sync handlers registered
[IPC] Sync handlers registered
[IPC] License handlers registered
[IPC] 3D color space window handlers registered
[IPC] ✅ Early handlers registered successfully
[IPC] 🚀 Starting comprehensive handler registration...
[OrganizationIPC] 🚀 Starting organization handler registration...
[IPC Registry] Registered handler for 'organization:create' with rate limiting: true
[IPC Registry] Registered handler for 'organization:getAll' with rate limiting: true
[IPC Registry] Registered handler for 'organization:getById' with rate limiting: true
[IPC Registry] Registered handler for 'organization:getCurrent' with rate limiting: true
[IPC Registry] Registered handler for 'organization:setCurrent' with rate limiting: true
[IPC Registry] Registered handler for 'organization:getMembers' with rate limiting: true
[IPC Registry] Registered handler for 'organization:addMember' with rate limiting: true
[IPC Registry] Registered handler for 'organization:updateMemberRole' with rate limiting: true
[IPC Registry] Registered handler for 'organization:removeMember' with rate limiting: true
[IPC Registry] Registered handler for 'organization:updateSettings' with rate limiting: true
[IPC Registry] Registered handler for 'organization:delete' with rate limiting: true
[IPC Registry] Registered handler for 'organization:inviteMember' with rate limiting: true
[IPC Registry] Registered handler for 'organization:acceptInvitation' with rate limiting: true
[IPC Registry] Registered handler for 'organization:getPendingInvitations' with rate limiting: true
[IPC Registry] Registered handler for 'organization:revokeInvitation' with rate limiting: true
[OrganizationIPC] 🎉 Organization handler registration completed
[IPC] Organization handlers registered
[DatasheetIPC] Registering datasheet handlers with dependency injection
[DatasheetIPC] Services available: { datasheetService: true }
[IPC Registry] Registered handler for 'datasheet:getByProduct' with rate limiting: true
[IPC Registry] Registered handler for 'datasheet:addToProduct' with rate limiting: true
[IPC Registry] Registered handler for 'datasheet:addWebLink' with rate limiting: true
[IPC Registry] Registered handler for 'datasheet:remove' with rate limiting: true
[IPC Registry] Registered handler for 'datasheet:open' with rate limiting: true
[IPC Registry] Registered handler for 'datasheet:openAll' with rate limiting: true
[IPC Registry] Registered handler for 'datasheet:migrate' with rate limiting: true
[DatasheetIPC] Datasheet handlers registered successfully
[IPC] Datasheet handlers registered
[ColorIPC] 🚀 STARTING COLOR HANDLER REGISTRATION...
[ColorIPC] Registering color handlers with dependency injection
[ColorIPC] Services available: { colorService: true, colorImportService: true }
[ColorIPC] 🧹 Removing fallback handlers...
[ColorIPC] 🧹 Unregistered fallback handler for: color:getAll
[ColorIPC] 🧹 Unregistered fallback handler for: color:getAllWithUsage
[ColorIPC] 🧹 Unregistered fallback handler for: color:getById
[ColorIPC] 🧹 Unregistered fallback handler for: color:add
[ColorIPC] 🧹 Unregistered fallback handler for: color:update
[ColorIPC] 🧹 Unregistered fallback handler for: color:delete
[ColorIPC] 🧹 Unregistered fallback handler for: color:clearAll
[ColorIPC] 🧹 Unregistered fallback handler for: color:getUsageCounts
[ColorIPC] 🧹 Unregistered fallback handler for: color:getProductsByColorName
[ColorIPC] 🧹 Unregistered fallback handler for: color:import
[ColorIPC] 🧹 Unregistered fallback handler for: color:export
[ColorIPC] 🧹 Unregistered fallback handler for: color:normalizePantoneCodes
[ColorIPC] 🧹 Unregistered fallback handler for: color:clearFrontendState
[ColorIPC] 🧹 Unregistered fallback handler for: color:adminClearAll
[ColorIPC] 📝 Registering GET_ALL handler...
[ColorIPC] ✅ GET_ALL handler registered
[IPC Registry] Registered handler for 'color:getById' with rate limiting: true
[IPC Registry] Registered handler for 'color:add' with rate limiting: true
[IPC Registry] Registered handler for 'color:update' with rate limiting: true
[IPC Registry] Registered handler for 'color:delete' with rate limiting: true
[IPC Registry] Registered handler for 'color:clearAll' with rate limiting: true
[IPC Registry] Registered handler for 'color:getUsageCounts' with rate limiting: true
[ColorIPC] 📝 Registering GET_ALL_WITH_USAGE handler...
[ColorIPC] ✅ GET_ALL_WITH_USAGE handler registered
[IPC Registry] Registered handler for 'color:getProductsByColorName' with rate limiting: true
[IPC Registry] Registered handler for 'color:import' with rate limiting: true
[IPC Registry] Registered handler for 'color:export' with rate limiting: true
[IPC Registry] Registered handler for 'color:normalizePantoneCodes' with rate limiting: true
[IPC Registry] Registered handler for 'color:clearFrontendState' with rate limiting: true
[IPC Registry] Registered handler for 'color:adminClearAll' with rate limiting: true
[IPC Registry] Registered handler for 'color:migrateGradientData' with rate limiting: true
[ColorIPC] ✅ All color handlers registered successfully
[ColorIPC] 🎯 Final handler count verification...
[ColorIPC] 🔍 Handler 'color:getAll' registered: false
[ColorIPC] 🔍 Handler 'color:getAllWithUsage' registered: false
[IPC] Color handlers registered
[ProductIPC] 🚀 STARTING PRODUCT HANDLER REGISTRATION...
[ProductIPC] ProductService available: true
[ProductIPC] ColorService available: true
[ProductIPC] Registering product IPC handlers...
[ProductIPC] 🧹 Removing fallback handlers...
[ProductIPC] 🧹 Unregistered fallback handler for: product:getAll
[ProductIPC] 🧹 Unregistered fallback handler for: product:getAllWithColors
[ProductIPC] 🧹 Unregistered fallback handler for: product:getById
[ProductIPC] 🧹 Unregistered fallback handler for: product:getWithColors
[ProductIPC] 🧹 Unregistered fallback handler for: product:create
[ProductIPC] 🧹 Unregistered fallback handler for: product:add
[ProductIPC] 🧹 Unregistered fallback handler for: product:update
[ProductIPC] 🧹 Unregistered fallback handler for: product:delete
[ProductIPC] 🧹 Unregistered fallback handler for: product:addColor
[ProductIPC] 🧹 Unregistered fallback handler for: product:removeColor
[ProductIPC] 🧹 Unregistered fallback handler for: product:getColors
[ProductIPC] 🧹 Unregistered fallback handler for: product:addLibraryColor
[ProductIPC] 🧹 Unregistered fallback handler for: product:syncColorsFromSupabase
[ProductIPC] 📝 Registering getAllWithColors handler...
[ProductIPC] ✅ getAllWithColors handler registered
[ProductIPC] ✅ Product IPC handlers registered successfully.
[ProductIPC] 🎯 Final handler count verification...
[ProductIPC] 🔍 Handler 'product:getAllWithColors' registered: false
[ProductIPC] 🔍 Handler 'product:delete' registered: false
[ProductIPC] 🔍 Handler 'product:getAll' registered: false
[IPC] Product handlers registered
[TestDataIPC] Registering test data handlers with dependency injection
[TestDataIPC] Services available: { productService: true, colorService: true }
[IPC Registry] Registered handler for 'test-data:createTestProduct' with rate limiting: true
[IPC Registry] Registered handler for 'test-data:removeTestData' with rate limiting: true
[TestDataIPC] Test data handlers registered successfully
[IPC] Test data handlers registered
[SampleDataIPC] Registering sample data handlers with dependency injection
[SampleDataIPC] Services available: { productService: true, colorService: true }
[IPC Registry] Registered handler for 'sampleData:createAll' with rate limiting: true
[IPC Registry] Registered handler for 'sampleData:removeAll' with rate limiting: true
[SampleDataIPC] Sample data handlers registered successfully
[IPC] Sample data handlers registered
[IPC Registry] Registered handler for 'audit:get-logs' with rate limiting: true
[IPC Registry] Registered handler for 'audit:get-analytics' with rate limiting: true
[IPC Registry] Registered handler for 'audit:get-suspicious-activity' with rate limiting: true
[IPC Registry] Registered handler for 'audit:export-logs' with rate limiting: true
[IPC Registry] Registered handler for 'audit:cleanup-logs' with rate limiting: true
[Audit IPC] Audit log handlers registered
[DatasheetService] Constructor called
[DatasheetService] Database instance set
[DatasheetService] Creating optimized datasheets table...
[DatasheetService] Optimized datasheets table created successfully
[DatasheetService] Constructor completed
[IPC Registry] Registered handler for 'colors:get-soft-deleted' with rate limiting: true
[IPC Registry] Registered handler for 'colors:restore' with rate limiting: true
[IPC Registry] Registered handler for 'colors:bulk-restore' with rate limiting: true
[IPC Registry] Registered handler for 'colors:cleanup-old-soft-deleted' with rate limiting: true
[IPC Registry] Registered handler for 'products:get-soft-deleted' with rate limiting: true
[IPC Registry] Registered handler for 'products:restore' with rate limiting: true
[IPC Registry] Registered handler for 'datasheets:get-soft-deleted' with rate limiting: true
[IPC Registry] Registered handler for 'datasheets:restore' with rate limiting: true
[IPC Registry] Registered handler for 'datasheets:cleanup-old-soft-deleted' with rate limiting: true
[IPC] Soft delete handlers registered
[IPC Registry] Registered handler for 'integrity:run-checks' with rate limiting: true
[IPC Registry] Registered handler for 'integrity:fix-color-spaces' with rate limiting: true
[IPC] Relationship integrity handlers registered
[IPC Registry] Registered handler for 'settings:open-dev-tools' with rate limiting: true
[IPC Registry] Registered handler for 'settings:run-performance-test' with rate limiting: true
[IPC Registry] Registered handler for 'settings:check-database-integrity' with rate limiting: true
[IPC Registry] Registered handler for 'settings:get-database-stats' with rate limiting: true
[IPC Registry] Registered handler for 'settings:optimize-database' with rate limiting: true
[IPC Registry] Registered handler for 'settings:vacuum-database' with rate limiting: true
[IPC Registry] Registered handler for 'settings:run-enhanced-integrity-check' with rate limiting: true
[IPC Registry] Registered handler for 'settings:get-application-logs' with rate limiting: true
[IPC Registry] Registered handler for 'settings:clear-application-logs' with rate limiting: true
[IPC Registry] Registered handler for 'settings:export-application-logs' with rate limiting: true
[IPC Registry] Registered handler for 'settings:select-logo-file' with rate limiting: true
[IPC Registry] Registered handler for 'settings:reset-application-data' with rate limiting: true
[IPC Registry] Registered handler for 'settings:get-app-info' with rate limiting: true
[IPC] Settings handlers registered
[IPC] ✅ All IPC handlers registered successfully
[IPC] All handlers registered successfully in 2ms
[App] ✅ Application services and IPC handlers initialized in 60ms
[App] 🔍 Service initialization completed
[TRACE] Entered createWindow
Registering window control handlers...
[IPC Registry] Registered handler for 'window:isMaximized' with rate limiting: true
[IPC Registry] Registered handler for 'window:toggleDevTools' with rate limiting: true
Window control handlers registered successfully
[TRACE] Window control handlers set up
Registering zoom handlers...
[IPC Registry] Registered handler for 'zoom:in' with rate limiting: true
[IPC Registry] Registered handler for 'zoom:out' with rate limiting: true
[IPC Registry] Registered handler for 'zoom:reset' with rate limiting: true
[IPC Registry] Registered handler for 'zoom:get-factor' with rate limiting: true
[IPC Registry] Registered handler for 'app:get-zoom-factor' with rate limiting: true
Zoom handlers registered successfully
[TRACE] Zoom handlers set up
[IPC Registry] Registered handler for 'product:open-datasheet' with rate limiting: true
[IPC Registry] Registered handler for 'get-initial-config-status' with rate limiting: true
[IPC Registry] Registered handler for 'select-shared-folder' with rate limiting: true
[IPC Registry] Registered handler for 'save-storage-config' with rate limiting: true
[IPC Registry] Registered handler for 'setup:getSharedFolderPath' with rate limiting: true
[IPC Registry] Registered handler for 'track-renderer-error' with rate limiting: true
[DEBUG] Console debug log cleared and ready at: /Users/<USER>/Desktop/Code Base/chromasync-mac/debug-console.log
[IPC Registry] Registered handler for 'debug-console-log' with rate limiting: true
[IPC Registry] Registered handler for 'test-ipc-available' with rate limiting: true
[IPC Registry] Registered handler for 'log-error' with rate limiting: true
[IPC Registry] Registered handler for 'ping' with rate limiting: true
[TRACE] Creating main window...
[TRACE] Using optimized window size: 1600x838
[TRACE] Vibrancy disabled to prevent border artifacts.
[TRACE] Application menu removed.
[TRACE] Configuring enhanced CSP security...
[CSP] Configuring CSP for production environment
[CSP] Generated CSP header: default-src 'none'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: file:; font-src 'self' data:; connect-src 'self' https://*.supabase.co https://fonts.googleapis.com wss://*.supabase.co; media-src 'none'; object-src 'none'; child-src 'none'; frame-src 'none'; worker-src 'self' blob:; manifest-src 'self'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests; block-all-mixed-content
[CSP] ✅ CSP headers configured via webRequest.onHeadersReceived
[CSP] 🛡️  Content Security Policy Status (Production)
[CSP] Valid: ✅
[CSP] ⚠️  Warnings:
[CSP]   - Production: 'unsafe-inline' in script-src reduces security - consider using nonces or hashes
[TRACE] ✅ Enhanced CSP configuration complete.
[TRACE] __dirname: /Users/<USER>/Desktop/Code Base/chromasync-mac/out/main
[TRACE] Loading app from: /Users/<USER>/Desktop/Code Base/chromasync-mac/out/renderer/index.html
[TRACE] File exists: true
[TRACE] Using loadFile() for better file:// protocol compatibility
[OrganizationContext] Loaded from storage: 550e8400-e29b-41d4-a716-************
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorService] Using base repository for reliable product associations
[ColorService] Retrieved 4 colors for organization 550e8400-e29b-41d4-a716-************
[ImmediateSync] 🚀 Manual full sync requested
[ImmediateSync] Unified sync manager not initialized
[TRACE] Loaded app from built files
[IPC Registry] Registered handler for 'window:set-dark-mode' with rate limiting: true
[TRACE] Dark mode handler registered.
[TRACE] Main window created successfully
[UnifiedSync] Initializing unified sync...
[SyncEvents] Setting up unified sync event forwarding to renderer
[SyncEvents] Sync manager not ready, deferring event forwarding setup
[UnifiedSync] Event forwarding configured for main window
[UnifiedSync] Unified sync initialization completed
[App] ✅ Application startup completed successfully
[Supabase] ENHANCED DEBUG - Direct environment check:
[Supabase]   process.env.SUPABASE_URL: PRESENT
[Supabase]   process.env.SUPABASE_ANON_KEY: PRESENT
[Supabase] About to call secureConfig.getConfigValue...
[Supabase] Calling loadConfig first...
[Supabase] Config loaded: [
  'ZOHO_CLIENT_ID',
  'ZOHO_CLIENT_SECRET',
  'ZOHO_ACCOUNT_ID',
  'ZOHO_REFRESH_TOKEN',
  'ZOHO_REGION',
  'ZOHO_SUPPORT_ALIAS',
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'SENTRY_DSN',
  'SENTRY_ENABLED',
  'SENTRY_SAMPLE_RATE',
  'SENTRY_DEBUG',
  'JWT_SECRET',
  'AUTH_REDIRECT_URL',
  'ALLOWED_ORIGIN',
  'CLOUDFLARE_ANALYTICS_TOKEN'
]
[Supabase] SUPABASE_URL in config: PRESENT
[Supabase] SUPABASE_ANON_KEY in config: PRESENT
[Supabase] Configuration check: {
  hasUrl: true,
  hasKey: true,
  url: 'https://tzqnxhsnitogmtihhsrq.s...',
  encryptionAvailable: true,
  urlLength: 40,
  keyLength: 208
}
[Supabase] Client created with PKCE flow and secure storage
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Auth state changed: INITIAL_SESSION <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[TRACE] Main window shown.
Renderer error: {
  id: '1752493385962_3dvejgs8x1t',
  timestamp: 1752493385962,
  level: 'info',
  stack: undefined,
  context: {
    timestamp: 1752493385962,
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) ChromaSync/2.0.0-dev Chrome/134.0.6998.205 Electron/35.7.0 Safari/537.36',
    viewport: '1600x838'
  },
  source: 'renderer',
  category: 'general',
  sessionId: 'session_1752493385920_aos99p143e',
  version: '1.0.0',
  platform: 'MacIntel',
  userId: undefined,
  message: 'Application started'
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationService] getOrganizationsForUser called with userId: "bdcfcd21-ddb3-4386-bfb3-e6d5906babbf" (type: string)
[OrganizationService] Query returned 4 organizations for userId: "bdcfcd21-ddb3-4386-bfb3-e6d5906babbf"
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
  name: 'Default Organization',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    external_id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    name: 'Default Organization',
    slug: 'default-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
  name: 'IVG',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    external_id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    name: 'IVG',
    slug: 'ivg',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
  name: 'Test sync 2',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    external_id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    name: 'Test sync 2',
    slug: 'test-sync-2',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[SyncHandlers] has-unsynced-local-changes called - using centralized status manager
[OrganizationService] getOrganizationsForUser called with userId: "bdcfcd21-ddb3-4386-bfb3-e6d5906babbf" (type: string)
[OrganizationService] Query returned 4 organizations for userId: "bdcfcd21-ddb3-4386-bfb3-e6d5906babbf"
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
  name: 'Default Organization',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    external_id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    name: 'Default Organization',
    slug: 'default-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
  name: 'IVG',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    external_id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    name: 'IVG',
    slug: 'ivg',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
  name: 'Test sync 2',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    external_id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    name: 'Test sync 2',
    slug: 'test-sync-2',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationService] getOrganizationsForUser called with userId: "bdcfcd21-ddb3-4386-bfb3-e6d5906babbf" (type: string)
[OrganizationService] Query returned 4 organizations for userId: "bdcfcd21-ddb3-4386-bfb3-e6d5906babbf"
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
  name: 'Default Organization',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    external_id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    name: 'Default Organization',
    slug: 'default-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
  name: 'IVG',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    external_id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    name: 'IVG',
    slug: 'ivg',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
  name: 'Test sync 2',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    external_id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    name: 'Test sync 2',
    slug: 'test-sync-2',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:20:11',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationIPC] Handling GET_ALL request
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationIPC] Getting organizations for user: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
[OrganizationIPC] 🔄 Starting Supabase sync for user: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
[OrganizationService] 🔍 Starting background organization sync for user: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
[OrganizationService] Attempting background sync (no auth required)
[OrganizationService] 🔍 Checking organization_members table for any records...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationService] 🔍 Found 4 total organization memberships: [
  { user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', role: 'owner' },
  { user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', role: 'owner' },
  { user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', role: 'owner' },
  { user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', role: 'owner' }
]
[OrganizationService] 🔍 Querying with provided user ID: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationService] ✅ User query returned 4 memberships
[OrganizationService] 🔍 DEBUG: Processing organization: {
  id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
  name: 'Default Organization',
  slug: 'default-org',
  plan: 'free',
  settings: {},
  settingsType: 'object',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '{}',
  fullOrg: {
    id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    name: 'Default Organization',
    plan: 'free',
    slug: 'default-org',
    settings: {},
    created_at: '2025-05-30T08:34:08+00:00',
    updated_at: '2025-05-30T08:34:08+00:00'
  }
}
[OrganizationService] Updated organization: 9d70a48a-4604-43ff-2bbe-70202e8de65f
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
  name: 'Default Organization',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    external_id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    name: 'Default Organization',
    slug: 'default-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Processing organization: {
  id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
  name: 'IVG',
  slug: 'ivg',
  plan: 'free',
  settings: {},
  settingsType: 'object',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '{}',
  fullOrg: {
    id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    name: 'IVG',
    plan: 'free',
    slug: 'ivg',
    settings: {},
    created_at: '2025-05-30T19:56:55.183302+00:00',
    updated_at: '2025-05-30T19:56:55.183302+00:00'
  }
}
[OrganizationService] Updated organization: 4047153f-7be8-490b-9cb2-a1e3ed04b92b
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
  name: 'IVG',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    external_id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    name: 'IVG',
    slug: 'ivg',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Processing organization: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  slug: 'test-debug-org',
  plan: 'free',
  settings: {},
  settingsType: 'object',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '{}',
  fullOrg: {
    id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    plan: 'free',
    slug: 'test-debug-org',
    settings: {},
    created_at: '2025-06-29T15:30:00+00:00',
    updated_at: '2025-06-29T15:30:00+00:00'
  }
}
[OrganizationService] Updated organization: 550e8400-e29b-41d4-a716-************
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Processing organization: {
  id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
  name: 'Test sync 2',
  slug: 'test-sync-2',
  plan: 'free',
  settings: {},
  settingsType: 'object',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '{}',
  fullOrg: {
    id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    name: 'Test sync 2',
    plan: 'free',
    slug: 'test-sync-2',
    settings: {},
    created_at: '2025-06-29T16:09:52.114+00:00',
    updated_at: '2025-06-29T16:09:52.114+00:00'
  }
}
[OrganizationService] Updated organization: 91f8ab58-42e0-4593-a3f8-ff295fe35388
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
  name: 'Test sync 2',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    external_id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    name: 'Test sync 2',
    slug: 'test-sync-2',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationIPC] ✅ Supabase sync completed successfully
[OrganizationService] getOrganizationsForUser called with userId: "bdcfcd21-ddb3-4386-bfb3-e6d5906babbf" (type: string)
[OrganizationService] Query returned 4 organizations for userId: "bdcfcd21-ddb3-4386-bfb3-e6d5906babbf"
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
  name: 'Default Organization',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    external_id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    name: 'Default Organization',
    slug: 'default-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
  name: 'IVG',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    external_id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    name: 'IVG',
    slug: 'ivg',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
  name: 'Test sync 2',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    external_id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    name: 'Test sync 2',
    slug: 'test-sync-2',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06',
    user_role: 'owner',
    member_count: 1
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationIPC] Found 4 organizations for user bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] Starting sync for organization: 550e8400-e29b-41d4-a716-************
[OrganizationService] Found local organization ID: 550e8400-e29b-41d4-a716-************
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationService] Found 1 members in Supabase
[OrganizationService] Members data: [
  {
    user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
    role: 'owner',
    joined_at: '2025-06-29T15:30:00+00:00',
    invited_by: null
  }
]
[OrganizationService] Syncing member: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf {
  user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  role: 'owner',
  joined_at: '2025-06-29T15:30:00+00:00',
  invited_by: null
}
[OrganizationService] Verified local user: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf (<EMAIL>)
[OrganizationService] Getting members for organization: 550e8400-e29b-41d4-a716-************
[OrganizationService] Found 1 members in database
[OrganizationService] Getting current user ID locally (local-first approach)
[Sync] 🚀 Manual sync requested
[Sync] ⚠️ Sync manager not ready, attempting to initialize...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Sync] 🔧 Initializing sync manager with user and org context...
[UnifiedSync] 🔧 Initializing with config: {
  autoSyncEnabled: true,
  autoSyncInterval: 5,
  realtimeEnabled: false,
  maxRetries: 3
}
[UnifiedSync] ⏰ Auto-sync enabled (5 minutes)
[SyncEvents] Setting up unified sync event forwarding to renderer
[SyncEvents] ✅ Unified sync event forwarding configured
[UnifiedSync] ✅ Initialized successfully
[Sync] ✅ Sync manager initialized successfully
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Sync] Data check result: { colors: 4, products: 1, product_colors: 1 }
[Sync] Needs initial sync: false
[Sync] 🚀 Starting unified sync process...
[Sync] 🔍 Sync manager status before sync: {
  isInitialized: true,
  isReady: true,
  isSyncing: false,
  currentOperation: null,
  queueLength: 0,
  lastSyncTime: 0,
  config: {
    autoSyncEnabled: true,
    autoSyncInterval: 5,
    realtimeEnabled: false,
    maxRetries: 3
  }
}
[Sync] 🔍 Sync manager ready?: true
[Sync] 🎯 About to call unifiedSyncManager.sync() with args: {
  type: 'full',
  direction: 'bidirectional',
  priority: 'normal',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  organizationId: '550e8400-e29b-41d4-a716-************'
}
[UnifiedSync] 🚀 sync() method called with: { type: 'full', direction: 'bidirectional', priority: 'normal' }
[UnifiedSync] 🔍 Manager state: {
  isInitialized: true,
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  organizationId: '550e8400-e29b-41d4-a716-************',
  isSyncing: false
}
[UnifiedSync] 🔍 Starting comprehensive sync readiness validation...
[UnifiedSync] 🔧 About to import ServiceLocator...
[UnifiedSync] ✅ ServiceLocator imported successfully
[UnifiedSync] 🔍 Validating service dependencies...
[UnifiedSync] 📊 Service availability status: {
  colorService: true,
  productService: true,
  organizationService: true,
  database: true
}
[UnifiedSync] 🚀 Starting full sync (bidirectional)
[SyncStatusManager] Adjusting polling frequency from 300s to 30s
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1644
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[UnifiedSync] 🔀 Executing full sync with bidirectional direction
[UnifiedSync] 📋 Outbox contains 0 pending items
[UnifiedSync] ➡️ Calling syncFull() - comprehensive outbox + database sync
[UnifiedSync] 🔄 Starting full sync...
[UnifiedSync] 🎨 Syncing colors...
[ColorSyncService] 🔍 Starting color sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1644
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ColorSyncService] ✅ Authenticated session confirmed for user: <EMAIL>
[ColorSyncService] 🔍 Testing basic Supabase connection with authenticated session...
[Storage] Setting item: chromasync-auth-session, value length: 3175
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ColorSyncService] ✅ Total colors accessible: 472
[ColorSyncService] 🔍 Querying colors for organization: 550e8400-e29b-41d4-a716-************
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ColorSyncService] Found 4 colors in Supabase, syncing to local database...
[ColorSyncService] 🔄 Using Supabase UUID dd73e182-dbe4-45ca-8058-b5fd22f62e7a for color test 2
[ColorSyncService] ✅ Updated existing local color: test 2 (UUID: dd73e182-dbe4-45ca-8058-b5fd22f62e7a)
[ColorSyncService] 🔄 Using Supabase UUID 3a71a075-913e-4a18-9785-f37a7e483a64 for color Test Item
[ColorSyncService] ✅ Updated existing local color: Test Item (UUID: 3a71a075-913e-4a18-9785-f37a7e483a64)
[ColorSyncService] 🔄 Using Supabase UUID 114963cb-5d6b-4c5e-b0c7-c989c144bf3c for color Purple
[ColorSyncService] ✅ Updated existing local color: Purple (UUID: 114963cb-5d6b-4c5e-b0c7-c989c144bf3c)
[ColorSyncService] 🔄 Using Supabase UUID d53330db-6c88-4d18-870a-f4b469c77042 for color test 3
[ColorSyncService] ✅ Updated existing local color: test 3 (UUID: d53330db-6c88-4d18-870a-f4b469c77042)
[ColorSyncService] Successfully synced 4 colors from Supabase
[UnifiedSync] 📦 Syncing products...
[ProductSyncService] 🔍 Starting product sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1643
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ProductSyncService] ✅ Authenticated session confirmed for user: <EMAIL>
[ProductSyncService] 🔍 Testing basic Supabase connection with authenticated session...
[Storage] Setting item: chromasync-auth-session, value length: 3175
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ProductSyncService] ✅ Total products accessible: 26
[ProductSyncService] 🔍 DEBUG: Checking local products before sync...
[ProductSyncService] 🔍 DEBUG: Total local products before sync: 1
[ProductSyncService] 🔍 DEBUG: Deleted products before sync: 0 []
[ProductSyncService] 🔍 DEBUG: Fetching ALL products from Supabase (including deleted)...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ProductSyncService] 🔍 DEBUG: Total products in Supabase: 1
[ProductSyncService] 🔍 DEBUG: Deleted products in Supabase: 0 []
[ProductSyncService] 🔍 FETCHING PRODUCTS FROM SUPABASE - FILTERING BY deleted_at IS NULL
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ProductSyncService] 🔍 SUPABASE RETURNED 1 PRODUCTS (should exclude deleted)
[ProductSyncService] 🔍 Product: Test Product (deleted_at: null)
[ProductSyncService] Found 1 products in Supabase, syncing to local database...
[ProductSyncService] Updated local product: Test Product
[ProductSyncService] Successfully synced 1 products from Supabase
[ProductSyncService] 🔍 Checking for products deleted in Supabase but active locally...
[ProductSyncService] 🔍 DEBUG: Checking local products after sync...
[ProductSyncService] 🔍 DEBUG: Total local products after sync: 1
[ProductSyncService] 🔍 DEBUG: Deleted products after sync: 0 []
[ProductSyncService] 🔧 Running post-sync deduplication...
[ProductSyncService] ✅ Deduplication successful: 0 duplicates removed
[ProductService] 🔥 SYNC PRODUCT COLORS CALLED for org: 550e8400-e29b-41d4-a716-************
[ProductSyncService] 🔍 Starting product-color relationships sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1643
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ProductSyncService] ✅ Authenticated session confirmed for product-color sync: <EMAIL>
[ProductSyncService] 📡 Fetching products, colors, and relationships from Supabase...
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ProductSyncService] 📊 Supabase data: 1 products, 4 colors, 0 relationships
[ProductSyncService] No product-color relationships found in Supabase
[ProductService] 🔥 SYNC PRODUCT COLORS RESULT: { success: true, syncedCount: 0, errors: [] }
[UnifiedSync] ✅ full sync completed: { itemsProcessed: 5, duration: 867, success: true }
[Sync] 🎯 unifiedSyncManager.sync() returned result: {
  success: true,
  operation: {
    id: 'sync_1752493386877',
    type: 'full',
    direction: 'bidirectional',
    priority: 'normal',
    timestamp: 1752493386877
  },
  itemsProcessed: 5,
  duration: 867,
  errors: undefined
}
[Sync] ✅ Sync completed successfully - processed 5 items in 867ms
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1643
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[Sync] 🔍 Running verification queries...
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Sync] Verification queries completed:
[Sync] - Colors: null (error: column colors.deleted_at does not exist)
[Sync] - Products: 1 (error: none)
[Sync] Local verification - Colors: 4, Products: 1
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorIPC] 🔄 GET_ALL_WITH_USAGE called for organization: 550e8400-e29b-41d4-a716-************
[ColorService] Using base repository for reliable product associations
[ColorService] Retrieved 4 colors for organization 550e8400-e29b-41d4-a716-************
[ColorAnalyticsService] Using optimized usage counts for local-first performance
[OptimizedColorRepository] Query Metrics: {
  queryName: 'getUsageCountsOptimized',
  executionTimeMs: 0,
  rowsReturned: 1,
  indexesUsed: [],
  planAnalysis: 'Plan analysis failed'
}
[OptimizedColorRepository] ⚠️  Query not using indexes: getUsageCountsOptimized
[ColorService] getAllWithUsage returning 4 colors with 1 having usage data
[ColorIPC] 📊 Retrieved colors with usage: 4
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] 🔄 getAllWithColors called for organization: 550e8400-e29b-41d4-a716-************
[ProductService] Found 1 products with colors
[ProductIPC] 📊 Found products with colors: 1
[SyncHandlers] has-unsynced-local-changes called - using centralized status manager
[SyncHandlers] get-progress called - using centralized status manager
[App] 🔄 Starting background aggregation table initialization...
[App] ❌ Background aggregation initialization failed: Error: Cannot find module './db/database'
Require stack:
- /Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1408:15)
    at s._resolveFilename (node:electron/js2c/browser_init:2:129227)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1064:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1069:22)
    at Module._load (node:internal/modules/cjs/loader:1218:37)
    at c._load (node:electron/js2c/node_init:2:17950)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
    at Module.require (node:internal/modules/cjs/loader:1494:12)
    at require (node:internal/modules/helpers:135:16) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/Users/<USER>/Desktop/Code Base/chromasync-mac/out/main/index.js'
  ]
}
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] 🔄 getAllWithColors called for organization: 550e8400-e29b-41d4-a716-************
[ProductService] Found 1 products with colors
[ProductIPC] 📊 Found products with colors: 1
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorAnalyticsService] getProductsByColorName returning 4 color-product mappings
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorIPC] 🎨 Adding color with data: {
  name: 'Test 5',
  code: 'Green',
  product: 'Test Product',
  organizationId: '550e8400-e29b-41d4-a716-************'
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ColorIPC] Current user for color creation: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
[ColorService] Color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 added locally
[ColorService] 🔗 About to call createProductColorAssociation with params: {
  colorId: 'bb9c8623-d4ec-4f46-9bfe-f495c7b60c08',
  productName: 'Test Product',
  organizationId: '550e8400-e29b-41d4-a716-************',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  productNameLength: 12,
  productNameType: 'string'
}
[ColorService] Creating product-color association: color=bb9c8623-d4ec-4f46-9bfe-f495c7b60c08, product="Test Product", org=550e8400-e29b-41d4-a716-************
[ColorService] Found existing product: 18b7b530-7aba-4ef7-91eb-a0019b492c4c (Test Product)
[ColorService] 🎯 About to call relationshipService.addColorToProduct with: {
  productId: '18b7b530-7aba-4ef7-91eb-a0019b492c4c',
  colorId: 'bb9c8623-d4ec-4f46-9bfe-f495c7b60c08',
  organizationId: '550e8400-e29b-41d4-a716-************'
}
[ProductColorRelationshipService] 🔗 About to call productRepository.addProductColor with: {
  productId: '18b7b530-7aba-4ef7-91eb-a0019b492c4c',
  colorId: 'bb9c8623-d4ec-4f46-9bfe-f495c7b60c08',
  organizationId: '550e8400-e29b-41d4-a716-************'
}
[ProductColorRelationshipService] 📊 productRepository.addProductColor returned: {
  success: true,
  productId: '18b7b530-7aba-4ef7-91eb-a0019b492c4c',
  colorId: 'bb9c8623-d4ec-4f46-9bfe-f495c7b60c08'
}
[ProductColorRelationshipService] ✅ Successfully added color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 to product 18b7b530-7aba-4ef7-91eb-a0019b492c4c
[ColorService] 📊 addColorToProduct result: { success: true, data: true, error: undefined }
[ColorService] ✅ Successfully created product-color association: product=18b7b530-7aba-4ef7-91eb-a0019b492c4c, color=bb9c8623-d4ec-4f46-9bfe-f495c7b60c08
[ColorService] ✅ createProductColorAssociation completed for color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08
[SyncOutbox] Added create on colors to outbox.
[SyncOutbox] Added create on colors to outbox.
[ImmediateSync] 🚀 Manual full sync requested
[UnifiedSync] 🚀 sync() method called with: { type: 'colors', direction: 'bidirectional', priority: 'high' }
[UnifiedSync] 🔍 Manager state: {
  isInitialized: true,
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  organizationId: '550e8400-e29b-41d4-a716-************',
  isSyncing: false
}
[UnifiedSync] 🚀 sync() method called with: { type: 'products', direction: 'bidirectional', priority: 'high' }
[UnifiedSync] 🔍 Manager state: {
  isInitialized: true,
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  organizationId: '550e8400-e29b-41d4-a716-************',
  isSyncing: false
}
[UnifiedSync] 🚀 sync() method called with: { type: 'organizations', direction: 'bidirectional', priority: 'high' }
[UnifiedSync] 🔍 Manager state: {
  isInitialized: true,
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  organizationId: '550e8400-e29b-41d4-a716-************',
  isSyncing: false
}
[UnifiedSync] 🔧 About to import ServiceLocator...
[UnifiedSync] 🔧 About to import ServiceLocator...
[UnifiedSync] 🔧 About to import ServiceLocator...
[UnifiedSync] ✅ ServiceLocator imported successfully
[UnifiedSync] 🚀 Starting colors sync (bidirectional)
[Supabase] 🔍 Checking for authenticated session...
[UnifiedSync] ✅ ServiceLocator imported successfully
[UnifiedSync] ⏳ Sync already in progress, queuing operation
[UnifiedSync] ✅ ServiceLocator imported successfully
[UnifiedSync] ⏳ Sync already in progress, queuing operation
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1622
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[UnifiedSync] 🔀 Executing colors sync with bidirectional direction
[UnifiedSync] 📋 Outbox contains 2 pending items
[UnifiedSync] ➡️ Calling syncColors() - includes outbox processing
[UnifiedSync] 🎨 Syncing colors...
[UnifiedSync] 📤 Found 2 color items in outbox to push.
[Supabase] 🔍 Checking for authenticated session...
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1622
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ColorSyncService] Error pushing color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 to Supabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[ColorSyncService] Error in pushColorToSupabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[UnifiedSync] ❌ Failed to process color outbox item sync_1752493408200_xbdze5vs7: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1622
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ColorSyncService] Error pushing color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 to Supabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[ColorSyncService] Error in pushColorToSupabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[UnifiedSync] ❌ Failed to process color outbox item sync_1752493408213_hiinf5jhw: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[UnifiedSync] Found 1 pending color changes to push.
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1622
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ColorSyncService] Error pushing color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 to Supabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[ColorSyncService] Error in pushColorToSupabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[UnifiedSync] Failed to push color change bb9c8623-d4ec-4f46-9bfe-f495c7b60c08: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[ColorSyncService] 🔍 Starting color sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1622
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ColorSyncService] ✅ Authenticated session confirmed for user: <EMAIL>
[ColorSyncService] 🔍 Testing basic Supabase connection with authenticated session...
[Storage] Setting item: chromasync-auth-session, value length: 3175
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ColorSyncService] ✅ Total colors accessible: 472
[ColorSyncService] 🔍 Querying colors for organization: 550e8400-e29b-41d4-a716-************
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ColorSyncService] Found 4 colors in Supabase, syncing to local database...
[ColorSyncService] 🔄 Using Supabase UUID dd73e182-dbe4-45ca-8058-b5fd22f62e7a for color test 2
[ColorSyncService] ✅ Updated existing local color: test 2 (UUID: dd73e182-dbe4-45ca-8058-b5fd22f62e7a)
[ColorSyncService] 🔄 Using Supabase UUID 3a71a075-913e-4a18-9785-f37a7e483a64 for color Test Item
[ColorSyncService] ✅ Updated existing local color: Test Item (UUID: 3a71a075-913e-4a18-9785-f37a7e483a64)
[ColorSyncService] 🔄 Using Supabase UUID 114963cb-5d6b-4c5e-b0c7-c989c144bf3c for color Purple
[ColorSyncService] ✅ Updated existing local color: Purple (UUID: 114963cb-5d6b-4c5e-b0c7-c989c144bf3c)
[ColorSyncService] 🔄 Using Supabase UUID d53330db-6c88-4d18-870a-f4b469c77042 for color test 3
[ColorSyncService] ✅ Updated existing local color: test 3 (UUID: d53330db-6c88-4d18-870a-f4b469c77042)
[ColorSyncService] Successfully synced 4 colors from Supabase
[UnifiedSync] ✅ colors sync completed: { itemsProcessed: 4, duration: 579, success: false }
[UnifiedSync] 🚀 Starting products sync (bidirectional)
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1622
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[UnifiedSync] 🔀 Executing products sync with bidirectional direction
[UnifiedSync] 📋 Outbox contains 2 pending items
[UnifiedSync] ➡️ Calling syncProducts() - includes outbox processing
[UnifiedSync] 📦 Syncing products...
[UnifiedSync] Found 0 pending product changes to push.
[ProductSyncService] 🔍 Starting product sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Checking for authenticated session...
(node:24994) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 auto-sync:sync-completed listeners added to [IpcRenderer]. MaxListeners is 10. Use emitter.setMaxListeners() to increase limit
(Use `Electron Helper (Renderer) --trace-warnings ...` to show where the warning was created)
(node:24994) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 auto-sync:sync-failed listeners added to [IpcRenderer]. MaxListeners is 10. Use emitter.setMaxListeners() to increase limit
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1622
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ProductSyncService] ✅ Authenticated session confirmed for user: <EMAIL>
[ProductSyncService] 🔍 Testing basic Supabase connection with authenticated session...
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorIPC] 🔄 GET_ALL_WITH_USAGE called for organization: 550e8400-e29b-41d4-a716-************
[ColorService] Using base repository for reliable product associations
[ColorService] Retrieved 5 colors for organization 550e8400-e29b-41d4-a716-************
[ColorAnalyticsService] Using optimized usage counts for local-first performance
[OptimizedColorRepository] Query Metrics: {
  queryName: 'getUsageCountsOptimized',
  executionTimeMs: 0,
  rowsReturned: 2,
  indexesUsed: [],
  planAnalysis: 'Plan analysis failed'
}
[OptimizedColorRepository] ⚠️  Query not using indexes: getUsageCountsOptimized
[ColorService] getAllWithUsage returning 5 colors with 2 having usage data
[ColorIPC] 📊 Retrieved colors with usage: 5
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ProductSyncService] ✅ Total products accessible: 26
[ProductSyncService] 🔍 DEBUG: Checking local products before sync...
[ProductSyncService] 🔍 DEBUG: Total local products before sync: 1
[ProductSyncService] 🔍 DEBUG: Deleted products before sync: 0 []
[ProductSyncService] 🔍 DEBUG: Fetching ALL products from Supabase (including deleted)...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] 🔄 getAllWithColors called for organization: 550e8400-e29b-41d4-a716-************
[ProductService] Found 1 products with colors
[ProductIPC] 📊 Found products with colors: 1
[ProductSyncService] 🔍 DEBUG: Total products in Supabase: 1
[ProductSyncService] 🔍 DEBUG: Deleted products in Supabase: 0 []
[ProductSyncService] 🔍 FETCHING PRODUCTS FROM SUPABASE - FILTERING BY deleted_at IS NULL
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ProductSyncService] 🔍 SUPABASE RETURNED 1 PRODUCTS (should exclude deleted)
[ProductSyncService] 🔍 Product: Test Product (deleted_at: null)
[ProductSyncService] Found 1 products in Supabase, syncing to local database...
[ProductSyncService] Updated local product: Test Product
[ProductSyncService] Successfully synced 1 products from Supabase
[ProductSyncService] 🔍 Checking for products deleted in Supabase but active locally...
[ProductSyncService] 🔍 DEBUG: Checking local products after sync...
[ProductSyncService] 🔍 DEBUG: Total local products after sync: 1
[ProductSyncService] 🔍 DEBUG: Deleted products after sync: 0 []
[ProductSyncService] 🔧 Running post-sync deduplication...
[ProductSyncService] ✅ Deduplication successful: 0 duplicates removed
[ProductService] 🔥 SYNC PRODUCT COLORS CALLED for org: 550e8400-e29b-41d4-a716-************
[ProductSyncService] 🔍 Starting product-color relationships sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1621
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ProductSyncService] ✅ Authenticated session confirmed for product-color sync: <EMAIL>
[ProductSyncService] 📡 Fetching products, colors, and relationships from Supabase...
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ProductSyncService] 📊 Supabase data: 1 products, 4 colors, 0 relationships
[ProductSyncService] No product-color relationships found in Supabase
[ProductService] 🔥 SYNC PRODUCT COLORS RESULT: { success: true, syncedCount: 0, errors: [] }
[UnifiedSync] ✅ products sync completed: { itemsProcessed: 1, duration: 399, success: true }
[UnifiedSync] 🚀 Starting organizations sync (bidirectional)
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ImmediateSync] ✅ Manual sync completed: 2/3 successful
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1621
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[UnifiedSync] 🔀 Executing organizations sync with bidirectional direction
[UnifiedSync] 📋 Outbox contains 2 pending items
[UnifiedSync] ➡️ Calling syncOrganizations() - database flags only
[UnifiedSync] 🏢 Syncing organizations...
[OrganizationService] 🔍 Starting background organization sync for user: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
[OrganizationService] Attempting background sync (no auth required)
[OrganizationService] 🔍 Checking organization_members table for any records...
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorAnalyticsService] getProductsByColorName returning 5 color-product mappings
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] Adding color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 to product 18b7b530-7aba-4ef7-91eb-a0019b492c4c for organization: 550e8400-e29b-41d4-a716-************
[ProductColorRelationshipService] Color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 already associated with product 18b7b530-7aba-4ef7-91eb-a0019b492c4c
[SyncOutbox] Added create on product_colors to outbox.
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationService] 🔍 Found 4 total organization memberships: [
  { user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', role: 'owner' },
  { user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', role: 'owner' },
  { user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', role: 'owner' },
  { user_id: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', role: 'owner' }
]
[OrganizationService] 🔍 Querying with provided user ID: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ImmediateSync] 🚀 PUSH-ONLY sync: upsert on product_colors (18b7b530-7aba-4ef7-91eb-a0019b492c4c-bb9c8623-d4ec-4f46-9bfe-f495c7b60c08)
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1621
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:06'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorIPC] 🔄 GET_ALL_WITH_USAGE called for organization: 550e8400-e29b-41d4-a716-************
[ColorService] Using base repository for reliable product associations
[ColorService] Retrieved 5 colors for organization 550e8400-e29b-41d4-a716-************
[ColorAnalyticsService] Using optimized usage counts for local-first performance
[OptimizedColorRepository] Query Metrics: {
  queryName: 'getUsageCountsOptimized',
  executionTimeMs: 0,
  rowsReturned: 2,
  indexesUsed: [],
  planAnalysis: 'Plan analysis failed'
}
[OptimizedColorRepository] ⚠️  Query not using indexes: getUsageCountsOptimized
[ColorService] getAllWithUsage returning 5 colors with 2 having usage data
[ColorIPC] 📊 Retrieved colors with usage: 5
[OrganizationService] ✅ User query returned 4 memberships
[OrganizationService] 🔍 DEBUG: Processing organization: {
  id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
  name: 'Default Organization',
  slug: 'default-org',
  plan: 'free',
  settings: {},
  settingsType: 'object',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '{}',
  fullOrg: {
    id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    name: 'Default Organization',
    plan: 'free',
    slug: 'default-org',
    settings: {},
    created_at: '2025-05-30T08:34:08+00:00',
    updated_at: '2025-05-30T08:34:08+00:00'
  }
}
[OrganizationService] Updated organization: 9d70a48a-4604-43ff-2bbe-70202e8de65f
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
  name: 'Default Organization',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    external_id: '9d70a48a-4604-43ff-2bbe-70202e8de65f',
    name: 'Default Organization',
    slug: 'default-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:29'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Processing organization: {
  id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
  name: 'IVG',
  slug: 'ivg',
  plan: 'free',
  settings: {},
  settingsType: 'object',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '{}',
  fullOrg: {
    id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    name: 'IVG',
    plan: 'free',
    slug: 'ivg',
    settings: {},
    created_at: '2025-05-30T19:56:55.183302+00:00',
    updated_at: '2025-05-30T19:56:55.183302+00:00'
  }
}
[OrganizationService] Updated organization: 4047153f-7be8-490b-9cb2-a1e3ed04b92b
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
  name: 'IVG',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    external_id: '4047153f-7be8-490b-9cb2-a1e3ed04b92b',
    name: 'IVG',
    slug: 'ivg',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:29'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Processing organization: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  slug: 'test-debug-org',
  plan: 'free',
  settings: {},
  settingsType: 'object',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '{}',
  fullOrg: {
    id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    plan: 'free',
    slug: 'test-debug-org',
    settings: {},
    created_at: '2025-06-29T15:30:00+00:00',
    updated_at: '2025-06-29T15:30:00+00:00'
  }
}
[OrganizationService] Updated organization: 550e8400-e29b-41d4-a716-************
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:29'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationService] 🔍 DEBUG: Processing organization: {
  id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
  name: 'Test sync 2',
  slug: 'test-sync-2',
  plan: 'free',
  settings: {},
  settingsType: 'object',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '{}',
  fullOrg: {
    id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    name: 'Test sync 2',
    plan: 'free',
    slug: 'test-sync-2',
    settings: {},
    created_at: '2025-06-29T16:09:52.114+00:00',
    updated_at: '2025-06-29T16:09:52.114+00:00'
  }
}
[OrganizationService] Updated organization: 91f8ab58-42e0-4593-a3f8-ff295fe35388
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
  name: 'Test sync 2',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    external_id: '91f8ab58-42e0-4593-a3f8-ff295fe35388',
    name: 'Test sync 2',
    slug: 'test-sync-2',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:29'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[UnifiedSync] ✅ organizations sync completed: { itemsProcessed: 1, duration: 146, success: true }
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] 🔄 getAllWithColors called for organization: 550e8400-e29b-41d4-a716-************
[ProductService] Found 1 products with colors
[ProductIPC] 📊 Found products with colors: 1
[ImmediateSync] Failed to upsert products:18b7b530-7aba-4ef7-91eb-a0019b492c4c-bb9c8623-d4ec-4f46-9bfe-f495c7b60c08: {
  code: 'PGRST204',
  details: null,
  hint: null,
  message: "Could not find the 'data' column of 'products' in the schema cache"
}
[ProductIPC] ✅ Immediate sync completed for product-color relationship 18b7b530-7aba-4ef7-91eb-a0019b492c4c-bb9c8623-d4ec-4f46-9bfe-f495c7b60c08
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:29'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorIPC] 🔄 GET_ALL_WITH_USAGE called for organization: 550e8400-e29b-41d4-a716-************
[ColorService] Using base repository for reliable product associations
[ColorService] Retrieved 5 colors for organization 550e8400-e29b-41d4-a716-************
[ColorAnalyticsService] Using optimized usage counts for local-first performance
[OptimizedColorRepository] Query Metrics: {
  queryName: 'getUsageCountsOptimized',
  executionTimeMs: 0,
  rowsReturned: 2,
  indexesUsed: [],
  planAnalysis: 'Plan analysis failed'
}
[OptimizedColorRepository] ⚠️  Query not using indexes: getUsageCountsOptimized
[ColorService] getAllWithUsage returning 5 colors with 2 having usage data
[ColorIPC] 📊 Retrieved colors with usage: 5
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] 🔄 getAllWithColors called for organization: 550e8400-e29b-41d4-a716-************
[ProductService] Found 1 products with colors
[ProductIPC] 📊 Found products with colors: 1
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] 🔄 getAllWithColors called for organization: 550e8400-e29b-41d4-a716-************
[ProductService] Found 1 products with colors
[ProductIPC] 📊 Found products with colors: 1
[Sync] 🚀 Manual sync requested
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Sync] Data check result: { colors: 5, products: 1, product_colors: 2 }
[Sync] Needs initial sync: false
[Sync] 🚀 Starting unified sync process...
[Sync] 🔍 Sync manager status before sync: {
  isInitialized: true,
  isReady: true,
  isSyncing: false,
  currentOperation: null,
  queueLength: 0,
  lastSyncTime: 1752493409341,
  config: {
    autoSyncEnabled: true,
    autoSyncInterval: 5,
    realtimeEnabled: false,
    maxRetries: 3
  }
}
[Sync] 🔍 Sync manager ready?: true
[Sync] 🎯 About to call unifiedSyncManager.sync() with args: {
  type: 'full',
  direction: 'bidirectional',
  priority: 'normal',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  organizationId: '550e8400-e29b-41d4-a716-************'
}
[UnifiedSync] 🚀 sync() method called with: { type: 'full', direction: 'bidirectional', priority: 'normal' }
[UnifiedSync] 🔍 Manager state: {
  isInitialized: true,
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  organizationId: '550e8400-e29b-41d4-a716-************',
  isSyncing: false
}
[UnifiedSync] 🔍 Starting comprehensive sync readiness validation...
[UnifiedSync] 🔧 About to import ServiceLocator...
[UnifiedSync] ✅ ServiceLocator imported successfully
[UnifiedSync] 🔍 Validating service dependencies...
[UnifiedSync] 📊 Service availability status: {
  colorService: true,
  productService: true,
  organizationService: true,
  database: true
}
[UnifiedSync] 🚀 Starting full sync (bidirectional)
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1620
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[UnifiedSync] 🔀 Executing full sync with bidirectional direction
[UnifiedSync] 📋 Outbox contains 3 pending items
[UnifiedSync] ➡️ Calling syncFull() - comprehensive outbox + database sync
[UnifiedSync] 🔄 Starting full sync...
[UnifiedSync] 📤 Found 3 items in the sync outbox to push.
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1620
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ColorSyncService] Error pushing color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 to Supabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[ColorSyncService] Error in pushColorToSupabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[UnifiedSync] ❌ Failed to process outbox item sync_1752493408200_xbdze5vs7: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1619
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[Storage] Setting item: chromasync-auth-session, value length: 3175
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ColorSyncService] Error pushing color bb9c8623-d4ec-4f46-9bfe-f495c7b60c08 to Supabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[ColorSyncService] Error in pushColorToSupabase: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[UnifiedSync] ❌ Failed to process outbox item sync_1752493408213_hiinf5jhw: {
  code: '23505',
  details: null,
  hint: null,
  message: 'duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
}
[ProductService] 🔥 SYNC PRODUCT COLORS CALLED for org: 550e8400-e29b-41d4-a716-************
[ProductSyncService] 🔍 Starting product-color relationships sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1619
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ProductSyncService] ✅ Authenticated session confirmed for product-color sync: <EMAIL>
[ProductSyncService] 📡 Fetching products, colors, and relationships from Supabase...
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ProductSyncService] 📊 Supabase data: 1 products, 4 colors, 0 relationships
[ProductSyncService] No product-color relationships found in Supabase
[ProductService] 🔥 SYNC PRODUCT COLORS RESULT: { success: true, syncedCount: 0, errors: [] }
[UnifiedSync] 🎨 Syncing colors...
[ColorSyncService] 🔍 Starting color sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1619
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ColorSyncService] ✅ Authenticated session confirmed for user: <EMAIL>
[ColorSyncService] 🔍 Testing basic Supabase connection with authenticated session...
[Storage] Setting item: chromasync-auth-session, value length: 3175
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ColorSyncService] ✅ Total colors accessible: 472
[ColorSyncService] 🔍 Querying colors for organization: 550e8400-e29b-41d4-a716-************
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ColorSyncService] Found 4 colors in Supabase, syncing to local database...
[ColorSyncService] 🔄 Using Supabase UUID dd73e182-dbe4-45ca-8058-b5fd22f62e7a for color test 2
[ColorSyncService] ✅ Updated existing local color: test 2 (UUID: dd73e182-dbe4-45ca-8058-b5fd22f62e7a)
[ColorSyncService] 🔄 Using Supabase UUID 3a71a075-913e-4a18-9785-f37a7e483a64 for color Test Item
[ColorSyncService] ✅ Updated existing local color: Test Item (UUID: 3a71a075-913e-4a18-9785-f37a7e483a64)
[ColorSyncService] 🔄 Using Supabase UUID 114963cb-5d6b-4c5e-b0c7-c989c144bf3c for color Purple
[ColorSyncService] ✅ Updated existing local color: Purple (UUID: 114963cb-5d6b-4c5e-b0c7-c989c144bf3c)
[ColorSyncService] 🔄 Using Supabase UUID d53330db-6c88-4d18-870a-f4b469c77042 for color test 3
[ColorSyncService] ✅ Updated existing local color: test 3 (UUID: d53330db-6c88-4d18-870a-f4b469c77042)
[ColorSyncService] Successfully synced 4 colors from Supabase
[UnifiedSync] 📦 Syncing products...
[ProductSyncService] 🔍 Starting product sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1619
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ProductSyncService] ✅ Authenticated session confirmed for user: <EMAIL>
[ProductSyncService] 🔍 Testing basic Supabase connection with authenticated session...
[Storage] Setting item: chromasync-auth-session, value length: 3175
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ProductSyncService] ✅ Total products accessible: 26
[ProductSyncService] 🔍 DEBUG: Checking local products before sync...
[ProductSyncService] 🔍 DEBUG: Total local products before sync: 1
[ProductSyncService] 🔍 DEBUG: Deleted products before sync: 0 []
[ProductSyncService] 🔍 DEBUG: Fetching ALL products from Supabase (including deleted)...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ProductSyncService] 🔍 DEBUG: Total products in Supabase: 1
[ProductSyncService] 🔍 DEBUG: Deleted products in Supabase: 0 []
[ProductSyncService] 🔍 FETCHING PRODUCTS FROM SUPABASE - FILTERING BY deleted_at IS NULL
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[ProductSyncService] 🔍 SUPABASE RETURNED 1 PRODUCTS (should exclude deleted)
[ProductSyncService] 🔍 Product: Test Product (deleted_at: null)
[ProductSyncService] Found 1 products in Supabase, syncing to local database...
[ProductSyncService] Updated local product: Test Product
[ProductSyncService] Successfully synced 1 products from Supabase
[ProductSyncService] 🔍 Checking for products deleted in Supabase but active locally...
[ProductSyncService] 🔍 DEBUG: Checking local products after sync...
[ProductSyncService] 🔍 DEBUG: Total local products after sync: 1
[ProductSyncService] 🔍 DEBUG: Deleted products after sync: 0 []
[ProductSyncService] 🔧 Running post-sync deduplication...
[ProductSyncService] ✅ Deduplication successful: 0 duplicates removed
[ProductService] 🔥 SYNC PRODUCT COLORS CALLED for org: 550e8400-e29b-41d4-a716-************
[ProductSyncService] 🔍 Starting product-color relationships sync for organization: 550e8400-e29b-41d4-a716-************
[Supabase] 🔍 Starting comprehensive session validation...
[Supabase] Debug flags: {
  DEBUG_AUTH: false,
  VERBOSE_SYNC_LOGGING: true,
  SYNC_ERROR_ELEVATION: true
}
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3175
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1619
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[ProductSyncService] ✅ Authenticated session confirmed for product-color sync: <EMAIL>
[ProductSyncService] 📡 Fetching products, colors, and relationships from Supabase...
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[ProductSyncService] 📊 Supabase data: 1 products, 4 colors, 0 relationships
[ProductSyncService] No product-color relationships found in Supabase
[ProductService] 🔥 SYNC PRODUCT COLORS RESULT: { success: true, syncedCount: 0, errors: [] }
[UnifiedSync] ✅ full sync completed: { itemsProcessed: 6, duration: 752, success: true }
[Sync] 🎯 unifiedSyncManager.sync() returned result: {
  success: true,
  operation: {
    id: 'sync_1752493410924',
    type: 'full',
    direction: 'bidirectional',
    priority: 'normal',
    timestamp: 1752493410924
  },
  itemsProcessed: 6,
  duration: 752,
  errors: [
    'Failed to sync colors create for ID bb9c8623-d4ec-4f46-9bfe-f495c7b60c08: duplicate key value violates unique constraint "colors_user_id_source_id_code_key"',
    'Failed to sync colors create for ID bb9c8623-d4ec-4f46-9bfe-f495c7b60c08: duplicate key value violates unique constraint "colors_user_id_source_id_code_key"'
  ]
}
[Sync] ✅ Sync completed successfully - processed 6 items in 752ms
[Supabase] 🔍 Checking for authenticated session...
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Supabase] Current session check: {
  hasSession: true,
  sessionError: undefined,
  userEmail: '<EMAIL>',
  userId: 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  accessToken: 'eyJhbGciOiJIUzI1NiIs...',
  refreshToken: 'present',
  expiresAt: 1752495031,
  expiresIn: 1619
}
[Supabase] 🔑 Session has JWT access token for authenticated requests
[Supabase] ✅ Valid session found: <EMAIL>
[Sync] 🔍 Running verification queries...
[Storage] Setting item: chromasync-auth-session, value length: 3176
[Storage] Encrypting data for chromasync-auth-session
[Storage] Successfully encrypted and stored chromasync-auth-session
[Supabase] Auth state changed: SIGNED_IN <EMAIL>
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:29'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorIPC] 🔄 GET_ALL_WITH_USAGE called for organization: 550e8400-e29b-41d4-a716-************
[ColorService] Using base repository for reliable product associations
[ColorService] Retrieved 5 colors for organization 550e8400-e29b-41d4-a716-************
[ColorAnalyticsService] Using optimized usage counts for local-first performance
[OptimizedColorRepository] Query Metrics: {
  queryName: 'getUsageCountsOptimized',
  executionTimeMs: 0,
  rowsReturned: 2,
  indexesUsed: [],
  planAnalysis: 'Plan analysis failed'
}
[OptimizedColorRepository] ⚠️  Query not using indexes: getUsageCountsOptimized
[ColorService] getAllWithUsage returning 5 colors with 2 having usage data
[ColorIPC] 📊 Retrieved colors with usage: 5
[Sync] Verification queries completed:
[Sync] - Colors: null (error: column colors.deleted_at does not exist)
[Sync] - Products: 1 (error: none)
[Sync] Local verification - Colors: 5, Products: 1
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] 🔄 getAllWithColors called for organization: 550e8400-e29b-41d4-a716-************
[ProductService] Found 1 products with colors
[ProductIPC] 📊 Found products with colors: 1
[OrganizationService] 🔍 DEBUG: Mapping organization row: {
  id: '550e8400-e29b-41d4-a716-************',
  name: 'Test Debug Org',
  settings: '{}',
  settingsType: 'string',
  settingsIsNull: false,
  settingsIsUndefined: false,
  settingsStringified: '"{}"',
  fullRow: {
    id: '550e8400-e29b-41d4-a716-************',
    external_id: '550e8400-e29b-41d4-a716-************',
    name: 'Test Debug Org',
    slug: 'test-debug-org',
    plan: 'free',
    settings: '{}',
    created_at: '2025-07-07 11:09:33',
    updated_at: '2025-07-14 11:43:29'
  }
}
[OrganizationService] 🔍 DEBUG: Parsed settings from string: {}
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ColorIPC] 🔄 GET_ALL_WITH_USAGE called for organization: 550e8400-e29b-41d4-a716-************
[ColorService] Using base repository for reliable product associations
[ColorService] Retrieved 5 colors for organization 550e8400-e29b-41d4-a716-************
[ColorAnalyticsService] Using optimized usage counts for local-first performance
[OptimizedColorRepository] Query Metrics: {
  queryName: 'getUsageCountsOptimized',
  executionTimeMs: 0,
  rowsReturned: 2,
  indexesUsed: [],
  planAnalysis: 'Plan analysis failed'
}
[OptimizedColorRepository] ⚠️  Query not using indexes: getUsageCountsOptimized
[ColorService] getAllWithUsage returning 5 colors with 2 having usage data
[ColorIPC] 📊 Retrieved colors with usage: 5
[OrganizationContext] ✅ Final organization ID: 550e8400-e29b-41d4-a716-************
[ProductIPC] 🔄 getAllWithColors called for organization: 550e8400-e29b-41d4-a716-************
[ProductService] Found 1 products with colors
[ProductIPC] 📊 Found products with colors: 1
[DatabaseAuditLogger] Cleaned up logs older than 90 days
[Storage] Getting item: chromasync-auth-session
[Storage] Found encrypted data for chromasync-auth-session, length: 4252
[Storage] Decrypting data for chromasync-auth-session
[Storage] Successfully decrypted chromasync-auth-session, length: 3176
[Main] DevTools toggle handler called
[Main] DevTools currently opened: false
[Main] Target window focused: true
[Main] Opening DevTools with mode: right
[24989:0714/124340.337669:ERROR:CONSOLE(0)] "Refused to apply style from 'devtools://theme/colors.css?sets=ui,chrome&version=1752493420329' because its MIME type ('') is not a supported stylesheet MIME type, and strict MIME checking is enabled.", source: devtools://devtools/bundled/devtools_app.html?remoteBase=https://chrome-devtools-frontend.appspot.com/serve_file/@2c6731af524b45a009157e44187f9bc00ef4ad09/&can_dock=true&toolbarColor=rgba(223,223,223,1)&textColor=rgba(0,0,0,1)&experiments=true (0)
[24989:0714/124340.442274:ERROR:CONSOLE(1)] "Request Autofill.enable failed. {"code":-32601,"message":"'Autofill.enable' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[24989:0714/124340.442302:ERROR:CONSOLE(1)] "Request Autofill.setAddresses failed. {"code":-32601,"message":"'Autofill.setAddresses' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
