/**
 * Helper functions for safe migrations
 */

export class MigrationHelpers {
  private db: any;

  constructor(database: any) {
    this.db = database;
  }

  /**
   * Safely add a column to a table
   */
  addColumnSafe(
    tableName: string,
    columnName: string,
    columnDefinition: string
  ): boolean {
    const exists = this.columnExists(tableName, columnName);
    if (exists) {
      console.log(
        `[Migration] Column ${tableName}.${columnName} already exists, skipping`
      );
      return false;
    }

    try {
      this.db.exec(
        `ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDefinition}`
      );
      console.log(`[Migration] Added column ${tableName}.${columnName}`);
      return true;
    } catch (error) {
      console.error(
        `[Migration] Failed to add column ${tableName}.${columnName}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Safely create an index
   */
  createIndexSafe(
    indexName: string,
    tableName: string,
    columns: string,
    unique: boolean = false
  ): boolean {
    const exists = this.indexExists(indexName);
    if (exists) {
      console.log(`[Migration] Index ${indexName} already exists, skipping`);
      return false;
    }

    try {
      const uniqueClause = unique ? 'UNIQUE' : '';
      this.db.exec(
        `CREATE ${uniqueClause} INDEX ${indexName} ON ${tableName}(${columns})`
      );
      console.log(`[Migration] Created index ${indexName}`);
      return true;
    } catch (error) {
      console.error(`[Migration] Failed to create index ${indexName}:`, error);
      throw error;
    }
  }

  /**
   * Safely create a table
   */
  createTableSafe(tableName: string, tableDefinition: string): boolean {
    const exists = this.tableExists(tableName);
    if (exists) {
      console.log(`[Migration] Table ${tableName} already exists, skipping`);
      return false;
    }

    try {
      this.db.exec(`CREATE TABLE ${tableName} ${tableDefinition}`);
      console.log(`[Migration] Created table ${tableName}`);
      return true;
    } catch (error) {
      console.error(`[Migration] Failed to create table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Check if a column exists
   */
  columnExists(tableName: string, columnName: string): boolean {
    const result = this.db
      .prepare(
        `
      SELECT COUNT(*) as count 
      FROM pragma_table_info(?) 
      WHERE name = ?
    `
      )
      .get(tableName, columnName) as { count: number };

    return result.count > 0;
  }

  /**
   * Check if a table exists
   */
  tableExists(tableName: string): boolean {
    const result = this.db
      .prepare(
        `
      SELECT COUNT(*) as count 
      FROM sqlite_master 
      WHERE type='table' AND name = ?
    `
      )
      .get(tableName) as { count: number };

    return result.count > 0;
  }

  /**
   * Check if an index exists
   */
  indexExists(indexName: string): boolean {
    const result = this.db
      .prepare(
        `
      SELECT COUNT(*) as count 
      FROM sqlite_master 
      WHERE type='index' AND name = ?
    `
      )
      .get(indexName) as { count: number };

    return result.count > 0;
  }

  /**
   * Get all columns for a table
   */
  getTableColumns(tableName: string): string[] {
    const columns = this.db
      .prepare(
        `
      SELECT name FROM pragma_table_info(?)
    `
      )
      .all(tableName) as Array<{ name: string }>;

    return columns.map(col => col.name);
  }

  /**
   * Check if a foreign key exists
   */
  foreignKeyExists(
    tableName: string,
    columnName: string,
    referencedTable: string
  ): boolean {
    const fks = this.db
      .prepare(
        `
      SELECT * FROM pragma_foreign_key_list(?)
    `
      )
      .all(tableName) as Array<{ from: string; table: string }>;

    return fks.some(
      fk => fk.from === columnName && fk.table === referencedTable
    );
  }

  /**
   * Batch add multiple columns safely
   */
  addColumnsSafe(
    tableName: string,
    columns: Array<{ name: string; definition: string }>
  ): number {
    let added = 0;

    for (const column of columns) {
      if (this.addColumnSafe(tableName, column.name, column.definition)) {
        added++;
      }
    }

    return added;
  }

  /**
   * Rename a column safely (SQLite 3.25.0+)
   */
  renameColumnSafe(
    tableName: string,
    oldName: string,
    newName: string
  ): boolean {
    if (!this.columnExists(tableName, oldName)) {
      console.log(
        `[Migration] Column ${tableName}.${oldName} does not exist, skipping rename`
      );
      return false;
    }

    if (this.columnExists(tableName, newName)) {
      console.log(
        `[Migration] Column ${tableName}.${newName} already exists, skipping rename`
      );
      return false;
    }

    try {
      this.db.exec(
        `ALTER TABLE ${tableName} RENAME COLUMN ${oldName} TO ${newName}`
      );
      console.log(
        `[Migration] Renamed column ${tableName}.${oldName} to ${newName}`
      );
      return true;
    } catch (error) {
      console.error(`[Migration] Failed to rename column:`, error);
      throw error;
    }
  }

  /**
   * Drop a column safely (SQLite 3.35.0+)
   */
  dropColumnSafe(tableName: string, columnName: string): boolean {
    if (!this.columnExists(tableName, columnName)) {
      console.log(
        `[Migration] Column ${tableName}.${columnName} does not exist, skipping drop`
      );
      return false;
    }

    try {
      this.db.exec(`ALTER TABLE ${tableName} DROP COLUMN ${columnName}`);
      console.log(`[Migration] Dropped column ${tableName}.${columnName}`);
      return true;
    } catch (error) {
      console.error(`[Migration] Failed to drop column:`, error);
      throw error;
    }
  }
}
