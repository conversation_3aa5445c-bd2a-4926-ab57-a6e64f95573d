/**
 * @file ContentArea.tsx
 * @description Content area component that renders different views based on current view mode
 */

import React from 'react';
import { useColorStore } from '../../store/color.store';
import {
  ColorTableWithSuspense,
  ColorSwatchesWithSuspense,
  ProductsPanelWithSuspense,
} from '../../utils/lazyComponents';

/**
 * Content area component with view routing
 */
export const ContentArea: React.FC = () => {
  const { viewMode } = useColorStore();

  // Render view based on current view mode
  const renderView = () => {
    switch (viewMode) {
      case 'table':
        return <ColorTableWithSuspense view='details' />;
      case 'codes':
        return <ColorTableWithSuspense view='reference' />;
      case 'swatches':
        return <ColorSwatchesWithSuspense />;
      case 'products':
        return <ProductsPanelWithSuspense />;
      default:
        return <ColorTableWithSuspense view='details' />;
    }
  };

  // Check if current view requires complex rendering optimizations
  const isComplexView = viewMode === 'table' || viewMode === 'codes';

  return (
    <div className='flex-1 overflow-auto'>
      <div className={`${isComplexView ? 'complex-list' : ''} h-full`}>
        {renderView()}
      </div>
    </div>
  );
};

export default ContentArea;
