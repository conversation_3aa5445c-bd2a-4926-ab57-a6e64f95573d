/**
 * Failsafe Database Initialization
 * Ensures database schema is created even if main initialization fails
 */

import Database from 'better-sqlite3';
import { getDatabasePath } from './core/connection';
import { COMPLETE_SCHEMA } from './schemas/complete-schema';

/**
 * Failsafe database initialization with comprehensive error handling
 * This function will always ensure basic schema exists
 */
export async function failsafeInitDatabase(): Promise<Database.Database | null> {
  const dbPath = getDatabasePath();
  console.log('[FailsafeInit] Starting failsafe database initialization...');
  console.log('[FailsafeInit] Database path:', dbPath);

  try {
    // Create database connection
    const db = new Database(dbPath);
    console.log('[FailsafeInit] Database connection established');

    // Check what tables currently exist
    const existingTables = db
      .prepare(
        `
      SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
    `
      )
      .all()
      .map((row: any) => row.name);

    console.log('[FailsafeInit] Existing tables:', existingTables);

    // Define required core tables
    const requiredTables = [
      'organizations',
      'products',
      'colors',
      'product_colors',
    ];
    const missingTables = requiredTables.filter(
      table => !existingTables.includes(table)
    );

    if (missingTables.length > 0) {
      console.log('[FailsafeInit] Missing core tables:', missingTables);
      console.log('[FailsafeInit] Creating complete schema...');

      try {
        // Execute complete schema
        db.exec(COMPLETE_SCHEMA);
        console.log('[FailsafeInit] ✅ Complete schema created successfully');

        // Verify schema was created
        const newTables = db
          .prepare(
            `
          SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
        `
          )
          .all()
          .map((row: any) => row.name);

        console.log('[FailsafeInit] Tables after schema creation:', newTables);

        const stillMissing = requiredTables.filter(
          table => !newTables.includes(table)
        );
        if (stillMissing.length > 0) {
          console.error(
            '[FailsafeInit] ❌ Still missing tables after schema creation:',
            stillMissing
          );
          // Fall back to manual table creation
          await createBasicTablesManually(db);
        }
      } catch (schemaError) {
        console.error(
          '[FailsafeInit] ❌ Complete schema creation failed:',
          schemaError
        );
        console.log('[FailsafeInit] Falling back to manual table creation...');

        // Fall back to creating basic tables manually
        await createBasicTablesManually(db);
      }
    } else {
      console.log('[FailsafeInit] ✅ All required tables exist');
    }

    // Ensure database has basic indexes
    await ensureBasicIndexes(db);

    console.log(
      '[FailsafeInit] ✅ Failsafe initialization completed successfully'
    );
    return db;
  } catch (error) {
    console.error('[FailsafeInit] ❌ Failsafe initialization failed:', error);
    return null;
  }
}

/**
 * Create basic tables manually as fallback
 */
async function createBasicTablesManually(db: Database.Database): Promise<void> {
  console.log('[FailsafeInit] Creating basic tables manually...');

  try {
    // Organizations table
    db.exec(`
      CREATE TABLE IF NOT EXISTS organizations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        slug TEXT UNIQUE,
        plan TEXT DEFAULT 'free',
        settings TEXT DEFAULT '{}',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Products table
    db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        sku TEXT,
        metadata TEXT DEFAULT '{}',
        organization_id TEXT NOT NULL,
        created_by TEXT,
        user_id TEXT,
        deleted_at TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Colors table
    db.exec(`
      CREATE TABLE IF NOT EXISTS colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT UNIQUE NOT NULL,
        code TEXT,
        display_name TEXT NOT NULL,
        hex TEXT NOT NULL,
        color_spaces TEXT,
        gradient_colors TEXT,
        notes TEXT,
        tags TEXT,
        is_library INTEGER DEFAULT 0,
        organization_id TEXT NOT NULL,
        created_by TEXT,
        user_id TEXT,
        deleted_at TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Product_colors junction table
    db.exec(`
      CREATE TABLE IF NOT EXISTS product_colors (
        product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
        color_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
        display_order INTEGER NOT NULL DEFAULT 0,
        organization_id TEXT NOT NULL,
        added_at TEXT DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_id, color_id)
      );
    `);

    // Users table
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        display_name TEXT,
        avatar_url TEXT,
        preferences TEXT DEFAULT '{}',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Organization members table
    db.exec(`
      CREATE TABLE IF NOT EXISTS organization_members (
        organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
        user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        role TEXT NOT NULL DEFAULT 'member',
        joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
        invited_by TEXT,
        PRIMARY KEY (organization_id, user_id)
      );
    `);

    // Schema migrations table for tracking
    db.exec(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        version INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        applied_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);

    console.log('[FailsafeInit] ✅ Basic tables created manually');
  } catch (error) {
    console.error('[FailsafeInit] ❌ Manual table creation failed:', error);
    throw error;
  }
}

/**
 * Ensure basic indexes exist for performance
 */
async function ensureBasicIndexes(db: Database.Database): Promise<void> {
  try {
    console.log('[FailsafeInit] Creating basic indexes...');

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_products_org ON products(organization_id)',
      'CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active, deleted_at)',
      'CREATE INDEX IF NOT EXISTS idx_colors_org ON colors(organization_id)',
      'CREATE INDEX IF NOT EXISTS idx_colors_active ON colors(deleted_at)',
      'CREATE INDEX IF NOT EXISTS idx_product_colors_product ON product_colors(product_id)',
      'CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id)',
      'CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id)',
    ];

    for (const indexSql of indexes) {
      db.exec(indexSql);
    }

    console.log('[FailsafeInit] ✅ Basic indexes created');
  } catch (error) {
    console.warn(
      '[FailsafeInit] ⚠️ Index creation failed (non-critical):',
      error
    );
  }
}

/**
 * Verify database integrity after initialization
 */
export function verifyDatabaseIntegrity(db: Database.Database): {
  valid: boolean;
  tables: string[];
  errors: string[];
} {
  try {
    const tables = db
      .prepare(
        `
      SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
    `
      )
      .all()
      .map((row: any) => row.name);

    const requiredTables = [
      'organizations',
      'products',
      'colors',
      'product_colors',
    ];
    const missingTables = requiredTables.filter(
      table => !tables.includes(table)
    );

    return {
      valid: missingTables.length === 0,
      tables,
      errors: missingTables.map(table => `Missing required table: ${table}`),
    };
  } catch (error) {
    return {
      valid: false,
      tables: [],
      errors: [`Database integrity check failed: ${(error as Error).message}`],
    };
  }
}
