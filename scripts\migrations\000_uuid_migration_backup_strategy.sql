-- UUID Migration Backup Strategy
-- Execute this BEFORE running any UUID migration scripts (028, 029, 030, 031)
-- This creates comprehensive backups and validation tools for safe migration

BEGIN TRANSACTION;

-- Step 1: Create backup schema with timestamp
-- This allows us to restore the exact state before migration
CREATE TABLE schema_backup_info (
    backup_id TEXT PRIMARY KEY,
    backup_timestamp TEXT NOT NULL,
    migration_phase TEXT NOT NULL,
    backup_description TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Insert backup record
INSERT INTO schema_backup_info (backup_id, backup_timestamp, migration_phase, backup_description)
VALUES (
    'uuid_migration_' || datetime('now', 'localtime'),
    datetime('now', 'localtime'),
    'pre_uuid_migration',
    'Complete backup before UUID migration - includes all table structures and data'
);

-- Step 2: Create comprehensive data backups for all main tables

-- Backup products table with full schema
CREATE TABLE products_full_backup_uuid_migration AS 
SELECT * FROM products;

-- Verify products backup
SELECT 
    'products_backup' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(updated_at) as latest_update
FROM products_full_backup_uuid_migration;

-- Backup colors table with full schema  
CREATE TABLE colors_full_backup_uuid_migration AS 
SELECT * FROM colors;

-- Verify colors backup
SELECT 
    'colors_backup' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(updated_at) as latest_update
FROM colors_full_backup_uuid_migration;

-- Backup organizations table with full schema
CREATE TABLE organizations_full_backup_uuid_migration AS 
SELECT * FROM organizations;

-- Verify organizations backup
SELECT 
    'organizations_backup' as table_name,
    COUNT(*) as record_count,
    MIN(created_at) as earliest_record,
    MAX(updated_at) as latest_update
FROM organizations_full_backup_uuid_migration;

-- Backup product_colors junction table
CREATE TABLE product_colors_full_backup_uuid_migration AS 
SELECT * FROM product_colors;

-- Verify product_colors backup
SELECT 
    'product_colors_backup' as table_name,
    COUNT(*) as record_count,
    MIN(added_at) as earliest_record
FROM product_colors_full_backup_uuid_migration;

-- Backup organization_members table
CREATE TABLE organization_members_full_backup_uuid_migration AS 
SELECT * FROM organization_members;

-- Backup organization_invitations table  
CREATE TABLE organization_invitations_full_backup_uuid_migration AS 
SELECT * FROM organization_invitations;

-- Backup color_sources reference table
CREATE TABLE color_sources_full_backup_uuid_migration AS 
SELECT * FROM color_sources;

-- Step 3: Create schema structure backup
-- This preserves the exact table structure before migration
CREATE TABLE schema_structure_backup (
    table_name TEXT,
    sql_definition TEXT,
    backup_timestamp TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Store table schemas
INSERT INTO schema_structure_backup (table_name, sql_definition)
SELECT name, sql FROM sqlite_master WHERE type = 'table' AND name IN (
    'products', 'colors', 'organizations', 'product_colors', 
    'organization_members', 'organization_invitations'
);

-- Store index schemas
INSERT INTO schema_structure_backup (table_name, sql_definition)
SELECT name, sql FROM sqlite_master WHERE type = 'index' AND tbl_name IN (
    'products', 'colors', 'organizations', 'product_colors',
    'organization_members', 'organization_invitations'
);

-- Store trigger schemas
INSERT INTO schema_structure_backup (table_name, sql_definition)
SELECT name, sql FROM sqlite_master WHERE type = 'trigger' AND tbl_name IN (
    'products', 'colors', 'organizations', 'product_colors',
    'organization_members', 'organization_invitations'
);

-- Step 4: Create data integrity validation checksums
-- This allows us to verify data integrity after migration
CREATE TABLE data_integrity_checksums (
    table_name TEXT,
    record_count INTEGER,
    checksum_field TEXT,
    checksum_value TEXT,
    backup_timestamp TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Products table checksums
INSERT INTO data_integrity_checksums (table_name, record_count, checksum_field, checksum_value)
SELECT 
    'products' as table_name,
    COUNT(*) as record_count,
    'name_concat' as checksum_field,
    GROUP_CONCAT(name ORDER BY external_id) as checksum_value
FROM products WHERE external_id IS NOT NULL;

-- Colors table checksums
INSERT INTO data_integrity_checksums (table_name, record_count, checksum_field, checksum_value)
SELECT 
    'colors' as table_name,
    COUNT(*) as record_count,
    'name_hex_concat' as checksum_field,
    GROUP_CONCAT(name || ':' || hex ORDER BY external_id) as checksum_value
FROM colors WHERE external_id IS NOT NULL;

-- Organizations table checksums
INSERT INTO data_integrity_checksums (table_name, record_count, checksum_field, checksum_value)
SELECT 
    'organizations' as table_name,
    COUNT(*) as record_count,
    'name_slug_concat' as checksum_field,
    GROUP_CONCAT(name || ':' || slug ORDER BY external_id) as checksum_value
FROM organizations WHERE external_id IS NOT NULL;

-- Product-colors relationships checksum
INSERT INTO data_integrity_checksums (table_name, record_count, checksum_field, checksum_value)
SELECT 
    'product_colors' as table_name,
    COUNT(*) as record_count,
    'relationship_count' as checksum_field,
    CAST(COUNT(*) AS TEXT) as checksum_value
FROM product_colors;

-- Step 5: Create rollback script template
CREATE TABLE rollback_script_template (
    step_number INTEGER,
    rollback_sql TEXT,
    description TEXT
);

INSERT INTO rollback_script_template (step_number, rollback_sql, description) VALUES
(1, 'DROP TABLE IF EXISTS products;', 'Remove migrated products table'),
(2, 'ALTER TABLE products_full_backup_uuid_migration RENAME TO products;', 'Restore original products table'),
(3, 'DROP TABLE IF EXISTS colors;', 'Remove migrated colors table'),
(4, 'ALTER TABLE colors_full_backup_uuid_migration RENAME TO colors;', 'Restore original colors table'),
(5, 'DROP TABLE IF EXISTS organizations;', 'Remove migrated organizations table'),
(6, 'ALTER TABLE organizations_full_backup_uuid_migration RENAME TO organizations;', 'Restore original organizations table'),
(7, 'DROP TABLE IF EXISTS product_colors;', 'Remove migrated product_colors table'),
(8, 'ALTER TABLE product_colors_full_backup_uuid_migration RENAME TO product_colors;', 'Restore original product_colors table'),
(9, 'DROP TABLE IF EXISTS organization_members;', 'Remove migrated organization_members table'),
(10, 'ALTER TABLE organization_members_full_backup_uuid_migration RENAME TO organization_members;', 'Restore original organization_members table'),
(11, 'DROP TABLE IF EXISTS organization_invitations;', 'Remove migrated organization_invitations table'),
(12, 'ALTER TABLE organization_invitations_full_backup_uuid_migration RENAME TO organization_invitations;', 'Restore original organization_invitations table');

-- Step 6: Create migration validation queries
CREATE VIEW v_migration_validation_queries AS
SELECT 
    'Pre-Migration Data Counts' as validation_type,
    (SELECT COUNT(*) FROM products WHERE external_id IS NOT NULL) as products_count,
    (SELECT COUNT(*) FROM colors WHERE external_id IS NOT NULL) as colors_count,
    (SELECT COUNT(*) FROM organizations WHERE external_id IS NOT NULL) as organizations_count,
    (SELECT COUNT(*) FROM product_colors) as relationships_count,
    datetime('now') as check_timestamp;

-- Step 7: Create disk space check
-- This helps ensure we have enough space for backups and migration
SELECT 
    'Disk Space Check' as check_type,
    -- Approximate backup size calculation
    (SELECT COUNT(*) * 1000 FROM products) +  -- Estimate 1KB per product
    (SELECT COUNT(*) * 500 FROM colors) +     -- Estimate 500B per color
    (SELECT COUNT(*) * 200 FROM organizations) + -- Estimate 200B per org
    (SELECT COUNT(*) * 100 FROM product_colors)  -- Estimate 100B per relationship
    as estimated_backup_size_bytes,
    'Ensure sufficient disk space before migration' as recommendation;

COMMIT;

-- Final backup verification
-- Run these to verify backup completion:

-- Check all backup tables exist and have data
SELECT 
    'Backup Verification' as check_type,
    (SELECT COUNT(*) FROM products_full_backup_uuid_migration) as products_backup_count,
    (SELECT COUNT(*) FROM colors_full_backup_uuid_migration) as colors_backup_count,
    (SELECT COUNT(*) FROM organizations_full_backup_uuid_migration) as organizations_backup_count,
    (SELECT COUNT(*) FROM product_colors_full_backup_uuid_migration) as relationships_backup_count;

-- Display backup info
SELECT * FROM schema_backup_info ORDER BY created_at DESC LIMIT 1;

-- Show data integrity checksums for later validation
SELECT * FROM data_integrity_checksums;

-- INSTRUCTIONS:
-- 1. Execute this script FIRST, before any UUID migration scripts
-- 2. Verify all backup tables are created and populated
-- 3. Note the backup_id from schema_backup_info for rollback reference
-- 4. Only proceed with UUID migration after confirming backups are complete
-- 5. Keep this backup until UUID migration is fully validated and tested

-- ROLLBACK INSTRUCTIONS (if needed):
-- 1. Execute rollback_script_template steps in order
-- 2. Verify data integrity using data_integrity_checksums
-- 3. Rebuild indexes and triggers from schema_structure_backup
-- 4. Test application functionality thoroughly

-- CLEANUP INSTRUCTIONS (after successful migration):
-- Run these commands only after UUID migration is fully validated:
-- DROP TABLE products_full_backup_uuid_migration;
-- DROP TABLE colors_full_backup_uuid_migration;
-- DROP TABLE organizations_full_backup_uuid_migration;
-- DROP TABLE product_colors_full_backup_uuid_migration;
-- DROP TABLE organization_members_full_backup_uuid_migration;
-- DROP TABLE organization_invitations_full_backup_uuid_migration;
-- DROP TABLE color_sources_full_backup_uuid_migration;
-- DROP TABLE schema_structure_backup;
-- DROP TABLE data_integrity_checksums;
-- DROP TABLE rollback_script_template;
-- DROP TABLE schema_backup_info;