/**
 * @file migration-integration.ts
 * @description Integration layer for the enterprise migration system
 * Provides the interface expected by initialization.ts
 */

import * as path from 'path';
import * as fs from 'fs';
import { EnterpriseMigrationRunner, MigrationDefinition } from './enterprise-migration-runner';
import { getDatabase } from '../../database';

export interface MigrationIntegration {
  runEnhancedMigrations(): Promise<boolean>;
  getMigrationStatus(): any;
  rollbackMigration(migrationId: number): Promise<any>;
  verifyIntegrity(): { valid: boolean; errors: string[] };
}

// Embedded migration 017 as fallback for production builds
const MIGRATION_017_SQL = `-- Migration 017: Denormalize color spaces into JSON column
-- This migration consolidates separate color space tables into a single JSON column
-- for improved performance and simplified sync operations

-- Add color_spaces JSON column to store all color space data
ALTER TABLE colors ADD COLUMN color_spaces JSON DEFAULT '{}';

-- Create indexes for JSON queries on color spaces
CREATE INDEX IF NOT EXISTS idx_colors_color_spaces_cmyk ON colors(json_extract(color_spaces, '$.cmyk.c'), json_extract(color_spaces, '$.cmyk.m'), json_extract(color_spaces, '$.cmyk.y'), json_extract(color_spaces, '$.cmyk.k'));
CREATE INDEX IF NOT EXISTS idx_colors_has_gradient ON colors(json_extract(color_spaces, '$.gradient')) WHERE json_extract(color_spaces, '$.gradient') IS NOT NULL;

-- Data Migration: Populate color_spaces JSON from existing normalized tables
-- Step 1: Populate basic color space data for all colors
UPDATE colors SET color_spaces = json_object(
  'cmyk', CASE 
    WHEN EXISTS (SELECT 1 FROM color_cmyk WHERE color_id = colors.id) 
    THEN json_object(
      'c', (SELECT c FROM color_cmyk WHERE color_id = colors.id),
      'm', (SELECT m FROM color_cmyk WHERE color_id = colors.id),
      'y', (SELECT y FROM color_cmyk WHERE color_id = colors.id),
      'k', (SELECT k FROM color_cmyk WHERE color_id = colors.id)
    )
    ELSE NULL
  END,
  'rgb', CASE 
    WHEN EXISTS (SELECT 1 FROM color_rgb WHERE color_id = colors.id)
    THEN json_object(
      'r', (SELECT r FROM color_rgb WHERE color_id = colors.id),
      'g', (SELECT g FROM color_rgb WHERE color_id = colors.id),
      'b', (SELECT b FROM color_rgb WHERE color_id = colors.id)
    )
    ELSE NULL
  END,
  'lab', CASE 
    WHEN EXISTS (SELECT 1 FROM color_lab WHERE color_id = colors.id)
    THEN json_object(
      'l', (SELECT l FROM color_lab WHERE color_id = colors.id),
      'a', (SELECT a FROM color_lab WHERE color_id = colors.id),
      'b', (SELECT b FROM color_lab WHERE color_id = colors.id)
    )
    ELSE NULL
  END,
  'hsl', CASE 
    WHEN EXISTS (SELECT 1 FROM color_hsl WHERE color_id = colors.id)
    THEN json_object(
      'h', (SELECT h FROM color_hsl WHERE color_id = colors.id),
      's', (SELECT s FROM color_hsl WHERE color_id = colors.id),
      'l', (SELECT l FROM color_hsl WHERE color_id = colors.id)
    )
    ELSE NULL
  END
)
WHERE color_spaces = '{}' OR color_spaces IS NULL;

-- Step 2: Populate gradient data for gradient colors
UPDATE colors SET color_spaces = json_set(
  color_spaces,
  '$.gradient',
  json_object(
    'stops', (
      SELECT json_group_array(
        json_object(
          'color', hex,
          'position', position * 100
        )
      )
      FROM gradient_stops
      WHERE gradient_id = colors.id
      ORDER BY stop_index
    )
  )
)
WHERE EXISTS (SELECT 1 FROM gradient_stops WHERE gradient_id = colors.id);

-- Step 3: Update the is_gradient column based on gradient data
UPDATE colors SET is_gradient = 1 
WHERE json_extract(color_spaces, '$.gradient') IS NOT NULL
  AND json_array_length(json_extract(color_spaces, '$.gradient.stops')) > 0;

-- Record migration completion
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (17, 'denormalize_color_spaces');`;

/**
 * Create migration integration instance
 */
export function createMigrationIntegration(
  db?: any,
  progressCallback?: (progress: any) => void
): MigrationIntegration {
  // If no database provided, get it from the global instance
  const database = db || getDatabase();
  
  if (!database) {
    throw new Error('Database not initialized. Cannot create migration integration.');
  }

  const runner = new EnterpriseMigrationRunner(database, progressCallback);
  
  return {
    /**
     * Run all pending migrations
     */
    async runEnhancedMigrations(): Promise<boolean> {
      try {
        // Get migrations directory path - handle both development and production builds
        let migrationsPath;
        if (process.env.NODE_ENV === 'development') {
          migrationsPath = path.join(__dirname, '..');
        } else {
          // In production, migrations are copied to out/src/main/db/migrations by build script
          const { app } = require('electron');
          const appPath = app.getAppPath();
          
          // Try the copied migrations location first (correct path)
          migrationsPath = path.join(appPath, 'src', 'main', 'db', 'migrations');
          console.log(`[MigrationIntegration] Trying primary path: ${migrationsPath}, exists: ${require('fs').existsSync(migrationsPath)}`);
          
          // Fallback: try relative to __dirname (in case of different build structure)
          if (!require('fs').existsSync(migrationsPath)) {
            migrationsPath = path.join(__dirname, '..');
            console.log(`[MigrationIntegration] Trying relative path: ${migrationsPath}, exists: ${require('fs').existsSync(migrationsPath)}`);
          }
          
          // Another fallback: try from process.cwd()
          if (!require('fs').existsSync(migrationsPath)) {
            migrationsPath = path.join(process.cwd(), 'src', 'main', 'db', 'migrations');
            console.log(`[MigrationIntegration] Trying cwd path: ${migrationsPath}, exists: ${require('fs').existsSync(migrationsPath)}`);
          }
          
          // Final fallback: try from app path directly
          if (!require('fs').existsSync(migrationsPath)) {
            migrationsPath = path.join(appPath, 'main', 'db', 'migrations');
            console.log(`[MigrationIntegration] Trying final fallback: ${migrationsPath}, exists: ${require('fs').existsSync(migrationsPath)}`);
          }
        }
        
        console.log(`[MigrationIntegration] Starting enhanced migrations from: ${migrationsPath}`);
        console.log(`[MigrationIntegration] Migration path exists: ${fs.existsSync(migrationsPath)}`);
        
        if (fs.existsSync(migrationsPath)) {
          const files = fs.readdirSync(migrationsPath).filter(f => f.endsWith('.sql'));
          console.log(`[MigrationIntegration] Found ${files.length} migration files: ${files.join(', ')}`);
        }
        
        // Check if migration files exist, use fallback if not
        let result;
        if (fs.existsSync(migrationsPath)) {
          console.log(`[MigrationIntegration] Running migrations from files...`);
          // Run all migrations from files
          result = await runner.runAllMigrations(migrationsPath);
        } else {
          console.log(`[MigrationIntegration] Migration files not found at ${migrationsPath}, using embedded fallback for critical migrations`);
          
          // Use embedded migration as fallback
          const embeddedMigrations: MigrationDefinition[] = [
            {
              version: 17,
              name: 'denormalize_color_spaces',
              filename: '017_denormalize_color_spaces.sql',
              sql: MIGRATION_017_SQL
            }
          ];
          
          // Run embedded migrations
          let successful = 0;
          let failed = 0;
          const results = [];
          
          for (const migration of embeddedMigrations) {
            const migrationResult = await runner.runMigration(migration);
            results.push(migrationResult);
            
            if (migrationResult.success) {
              successful++;
            } else {
              failed++;
            }
          }
          
          result = { successful, failed, results };
        }
        
        console.log(`[MigrationIntegration] Migration results: ${result.successful} successful, ${result.failed} failed`);
        
        // Return success if no failures
        return result.failed === 0;
        
      } catch (error) {
        console.error('[MigrationIntegration] Error running enhanced migrations:', error);
        return false;
      }
    },

    /**
     * Get current migration status
     */
    getMigrationStatus() {
      try {
        return runner.getMigrationStatus();
      } catch (error) {
        console.error('[MigrationIntegration] Error getting migration status:', error);
        return {
          appliedMigrations: new Set(),
          pendingMigrations: [],
          failedMigrations: new Map(),
          databaseVersion: 0
        };
      }
    },

    /**
     * Rollback a specific migration
     */
    async rollbackMigration(migrationId: number) {
      try {
        return await runner.rollbackMigration(migrationId);
      } catch (error) {
        console.error(`[MigrationIntegration] Error rolling back migration ${migrationId}:`, error);
        return {
          success: false,
          migrationId,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    },

    /**
     * Verify database integrity
     */
    verifyIntegrity() {
      try {
        return runner.verifyDatabaseIntegrity();
      } catch (error) {
        console.error('[MigrationIntegration] Error verifying database integrity:', error);
        return {
          valid: false,
          errors: [error instanceof Error ? error.message : 'Unknown error']
        };
      }
    }
  };
}

/**
 * Convenience function to run migrations with progress logging
 */
export async function runMigrationsWithProgress(): Promise<boolean> {
  const integration = createMigrationIntegration((progress) => {
    console.log(`[Migration Progress] ${progress.phase}: ${progress.currentMigration}/${progress.totalMigrations} (${progress.progress}%)`);
    
    if (progress.warnings.length > 0) {
      console.warn('[Migration Warnings]:', progress.warnings);
    }
    
    if (progress.errors.length > 0) {
      console.error('[Migration Errors]:', progress.errors);
    }
  });

  const success = await integration.runEnhancedMigrations();
  
  if (success) {
    // Verify integrity after successful migration
    const integrity = integration.verifyIntegrity();
    if (!integrity.valid) {
      console.error('[MigrationIntegration] Database integrity check failed after migration:', integrity.errors);
      return false;
    }
    console.log('[MigrationIntegration] Database integrity verified after migration');
  }
  
  return success;
}