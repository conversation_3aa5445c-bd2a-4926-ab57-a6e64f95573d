/**
 * @file color.store.ts
 * @description Clean, best-practice color store implementation
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useMemo } from 'react';
import type { ColorEntry } from '../../shared/types/color.types';
import { storeEventBus } from '../services/store-event-bus.service';
import { colorCacheService } from '../services/color-cache.service';

// Import cache testing utilities in development
if (process.env.NODE_ENV === 'development') {
  import('../utils/cache-test');
}

interface ColorStoreState {
  // Data
  colors: ColorEntry[];
  usageCounts: Record<string, { count: number; products: string[] }>;
  pantoneColors: ColorEntry[];
  ralColors: ColorEntry[];

  // UI State
  selectedColor: ColorEntry | null;
  searchQuery: string;
  viewMode: 'table' | 'swatches' | 'codes' | 'products';
  isLoading: boolean;
  error: string | null;

  // Actions
  loadColorsWithUsage: () => Promise<void>;
  emitRefreshComplete: (
    success: boolean,
    colorCount: number,
    organizationId?: string,
    error?: string
  ) => void;
  addColor: (color: any) => Promise<ColorEntry | false>;
  updateColor: (id: string, color: any) => Promise<boolean>;
  deleteColor: (id: string) => Promise<boolean>;
  importColors: (mergeMode: string) => Promise<any>;
  exportColors: (filePath?: string, format?: string) => Promise<any>;
  clearColors: () => Promise<boolean>;
  loadPantoneColors: () => Promise<ColorEntry[]>;
  loadRalColors: () => Promise<ColorEntry[]>;
  darkMode: boolean;
  toggleDarkMode: () => void;
  clearState: () => void;
  setSelectedColor: (color: ColorEntry | null) => void;
  setSearchQuery: (query: string) => void;
  setViewMode: (mode: 'table' | 'swatches' | 'codes' | 'products') => void;
  setError: (error: string | null) => void;
  getCacheMetrics: () => any;
  logCachePerformance: () => void;
}

/**
 * Color store with best practices:
 * - Single data loading method
 * - Clear error handling
 * - No dangerous backend calls
 * - Proper async/await patterns
 */
console.log('🚀 COLOR STORE INSTANTIATING...');

export const useColorStore = create<ColorStoreState>()(
  devtools(
    (set, get) => {
      // Set up event bus listeners when store is created
      if (typeof window !== 'undefined') {
        storeEventBus.subscribe('ORGANIZATION_CLEARED', () => {
          console.log(
            '[ColorStore] Received ORGANIZATION_CLEARED event, clearing colors and cache'
          );
          colorCacheService.invalidateAll();
          set({
            colors: [],
            usageCounts: {},
            selectedColor: null,
            searchQuery: '',
            error: null,
          });
        });

        storeEventBus.subscribe('ORGANIZATION_SWITCHED', event => {
          if (event.type === 'ORGANIZATION_SWITCHED') {
            console.log(
              '[ColorStore] Received ORGANIZATION_SWITCHED event, invalidating cache and loading colors for:',
              event.organizationId
            );
            // Invalidate cache for organization switch
            colorCacheService.invalidateAll();
            // Trigger color loading asynchronously with cache warming
            setTimeout(async () => {
              try {
                await get().loadColorsWithUsage();
                // Warm cache for the new organization
                if (event.organizationId) {
                  colorCacheService.warmCache(event.organizationId);
                }
              } catch (error) {
                console.error(
                  '[ColorStore] Error loading colors after organization switch:',
                  error
                );
              }
            }, 100);
          }
        });

        storeEventBus.subscribe('DATA_REFRESH_REQUESTED', event => {
          if (
            event.type === 'DATA_REFRESH_REQUESTED' &&
            event.source !== 'color-store'
          ) {
            console.log(
              '[ColorStore] Received DATA_REFRESH_REQUESTED event from:',
              event.source
            );

            // Invalidate cache before reloading to ensure fresh data
            setTimeout(async () => {
              try {
                // Get current organization to invalidate cache
                const orgStore = await import('./organization.store').then(m =>
                  m.useOrganizationStore.getState()
                );
                if (orgStore.currentOrganization?.external_id) {
                  console.log(
                    '[ColorStore] Invalidating cache for organization:',
                    orgStore.currentOrganization.external_id
                  );
                  colorCacheService.invalidateOrganization(
                    orgStore.currentOrganization.external_id
                  );
                }

                // Now reload with fresh data
                await get().loadColorsWithUsage();
              } catch (error) {
                console.error(
                  '[ColorStore] Error handling DATA_REFRESH_REQUESTED:',
                  error
                );
              }
            }, 100);
          }
        });
      }

      return {
        // Initial state
        colors: [],
        usageCounts: {},
        pantoneColors: [],
        ralColors: [],
        selectedColor: null,
        searchQuery: '',
        viewMode: 'table',
        isLoading: false,
        error: null,
        darkMode: false,

        /**
         * Load colors with usage counts with aggressive caching and request deduplication
         * Features: 45s TTL, organization-aware cache keys, automatic invalidation
         */
        loadColorsWithUsage: async () => {
          console.log('🚀 COLOR STORE METHOD CALLED: loadColorsWithUsage');
          set({ isLoading: true, error: null });

          try {
            // Get organization context
            const orgStore = await import('./organization.store').then(m =>
              m.useOrganizationStore.getState()
            );
            const organizationId = orgStore.currentOrganization?.external_id;

            if (!organizationId) {
              console.log(
                '[ColorStore] ℹ️ No organization selected yet, returning empty data'
              );
              set({
                colors: [],
                usageCounts: {},
                isLoading: false,
                error: null,
              });
              return;
            }

            console.log(
              '[ColorStore] 🔄 Loading colors for organization:',
              organizationId
            );

            // 1. Check cache first
            const cachedResult = colorCacheService.get(organizationId);
            if (cachedResult && cachedResult.success && cachedResult.data) {
              const {
                colors,
                usageCounts,
                organizationId: cachedOrgId,
              } = cachedResult.data;

              console.log('[ColorStore] ⚡ Using cached data:', {
                colorsCount: colors.length,
                usageCountsTotal: Object.keys(usageCounts).length,
                organizationId: cachedOrgId,
              });

              set({
                colors,
                usageCounts,
                isLoading: false,
                error: null,
              });

              get().emitRefreshComplete(true, colors.length, cachedOrgId);
              return;
            }

            // 2. Check if request is already in flight (deduplication)
            const inFlightResult =
              await colorCacheService.waitForInFlightRequest(organizationId);
            if (
              inFlightResult &&
              inFlightResult.success &&
              inFlightResult.data
            ) {
              const {
                colors,
                usageCounts,
                organizationId: inFlightOrgId,
              } = inFlightResult.data;

              console.log(
                '[ColorStore] 🔄 Using deduplicated request result:',
                {
                  colorsCount: colors.length,
                  organizationId: inFlightOrgId,
                }
              );

              set({
                colors,
                usageCounts,
                isLoading: false,
                error: null,
              });

              get().emitRefreshComplete(true, colors.length, inFlightOrgId);
              return;
            }

            // 3. Make API call with deduplication tracking
            console.log(
              '[ColorStore] 📡 Making fresh API call with caching...'
            );

            // Verify backend organization context
            try {
              const backendOrgResult =
                await window.organizationAPI.getCurrentOrganization();
              if (
                !backendOrgResult.success ||
                backendOrgResult.data?.external_id !== organizationId
              ) {
                console.warn(
                  '[ColorStore] ⚠️ Backend organization context mismatch, synchronizing...'
                );
                await window.organizationAPI.setCurrentOrganization(
                  organizationId
                );
                await new Promise(resolve => setTimeout(resolve, 100));
              }
            } catch (error) {
              console.warn(
                '[ColorStore] ⚠️ Failed to verify backend organization context:',
                error
              );
            }

            // Create and register the API promise for deduplication
            const apiPromise = window.colorAPI.getAllWithUsage();
            colorCacheService.registerInFlightRequest(
              organizationId,
              apiPromise
            );

            const result = await apiPromise;

            if (result.success && result.data) {
              const {
                colors,
                usageCounts,
                organizationId: resultOrgId,
              } = result.data;

              // Cache the successful result with 45s TTL
              colorCacheService.set(organizationId, result, 45000);

              console.log('[ColorStore] ✅ Fresh data loaded and cached:', {
                colorsCount: colors.length,
                usageCountsTotal: Object.keys(usageCounts).length,
                organizationId: resultOrgId,
              });

              set({
                colors,
                usageCounts,
                isLoading: false,
                error: null,
              });

              get().emitRefreshComplete(true, colors.length, resultOrgId);
            } else {
              throw new Error(result.error || 'Failed to load colors');
            }
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';

            console.error(
              '[ColorStore] ❌ Failed to load colors:',
              errorMessage
            );

            set({
              isLoading: false,
              error: errorMessage,
              colors: [],
              usageCounts: {},
            });

            get().emitRefreshComplete(false, 0, undefined, errorMessage);
          }
        },

        // Helper method to emit refresh complete events
        emitRefreshComplete: (
          success: boolean,
          colorCount: number,
          organizationId?: string,
          error?: string
        ) => {
          if (typeof window !== 'undefined' && window.dispatchEvent) {
            window.dispatchEvent(
              new CustomEvent('color-store:refresh-complete', {
                detail: {
                  success,
                  timestamp: Date.now(),
                  colorCount,
                  organizationId,
                  error,
                  fromCache: !!organizationId, // Indicates if data came from cache
                },
              })
            );
          }
        },

        /**
         * Clear frontend state only (safe)
         * Best practice: No backend operations from frontend state management
         */
        clearState: () => {
          console.log('[ColorStore] Clearing frontend state...');
          set({
            colors: [],
            usageCounts: {},
            selectedColor: null,
            searchQuery: '',
            error: null,
          });
        },

        /**
         * Set selected color
         */
        setSelectedColor: color => {
          set({ selectedColor: color });
        },

        /**
         * Set search query
         */
        setSearchQuery: query => {
          console.log(
            '[ColorStore] 🔍 Setting search query:',
            JSON.stringify({ from: get().searchQuery, to: query }, null, 2)
          );
          set({ searchQuery: query });
        },

        /**
         * Set view mode
         */
        setViewMode: mode => {
          set({ viewMode: mode });
        },

        /**
         * Set error state
         */
        setError: error => {
          set({ error });
        },

        /**
         * Add a new color using typed API with cache invalidation
         */
        addColor: async color => {
          try {
            console.log('[ColorStore] 🎨 Adding new color:', color);

            // Check organization context
            const orgStore = await import('./organization.store').then(m =>
              m.useOrganizationStore.getState()
            );
            if (!orgStore.currentOrganization?.external_id) {
              console.error(
                '[ColorStore] ❌ No organization context for adding color'
              );
              return false;
            }

            console.log('[ColorStore] 📡 Calling colorAPI.add...');
            // Use typed colorAPI
            const response = await window.colorAPI.add(color);
            console.log('[ColorStore] 📡 Response received:', response);

            if (response && response.success && response.data) {
              const addedColor = response.data;
              console.log(
                '[ColorStore] ✅ Color added successfully:',
                addedColor
              );

              // Invalidate cache on mutation
              colorCacheService.invalidateOrganization(
                orgStore.currentOrganization.external_id
              );
              console.log('[ColorStore] Cache invalidated after adding color');

              // Also invalidate product map cache since color-product associations may have changed
              const { refreshProductMapForOrganization } = await import(
                '../hooks/useColorProductMap'
              );
              refreshProductMapForOrganization(
                orgStore.currentOrganization.external_id
              );
              console.log(
                '[ColorStore] Product map cache refresh triggered after adding color'
              );

              // Optimistic update: add color to store immediately
              const currentState = get();
              set({
                colors: [...currentState.colors, addedColor],
                usageCounts: {
                  ...currentState.usageCounts,
                  [addedColor.id]: { count: 0, products: [] },
                },
              });
              return addedColor;
            } else {
              console.error(
                '[ColorStore] ❌ Failed to add color - invalid response:',
                response
              );
              return false;
            }
          } catch (error) {
            console.error('[ColorStore] ❌ Error adding color:', error);
            return false;
          }
        },

        /**
         * Update an existing color using typed API with cache invalidation
         */
        updateColor: async (id, color) => {
          try {
            // Use typed colorAPI
            const result = await window.colorAPI.update(id, color);
            if (result && result.success) {
              // Invalidate cache on mutation
              const orgStore = await import('./organization.store').then(m =>
                m.useOrganizationStore.getState()
              );
              if (orgStore.currentOrganization?.external_id) {
                colorCacheService.invalidateOrganization(
                  orgStore.currentOrganization.external_id
                );
                console.log(
                  '[ColorStore] Cache invalidated after updating color'
                );
              }

              // Reload colors with usage to get updated data (will use fresh API call due to invalidation)
              await get().loadColorsWithUsage();
              return true;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error updating color:', error);
            return false;
          }
        },

        /**
         * Delete a color using typed API with cache invalidation
         */
        deleteColor: async id => {
          try {
            // Use typed colorAPI
            console.log('[ColorStore] Deleting color with ID:', id);
            const response = await window.colorAPI.delete(id);
            console.log('[ColorStore] Delete response:', response);

            if (response && response.success) {
              console.log(
                '[ColorStore] Delete successful, invalidating cache and reloading data...'
              );

              // Invalidate cache on mutation
              const orgStore = await import('./organization.store').then(m =>
                m.useOrganizationStore.getState()
              );
              if (orgStore.currentOrganization?.external_id) {
                colorCacheService.invalidateOrganization(
                  orgStore.currentOrganization.external_id
                );
                console.log(
                  '[ColorStore] Cache invalidated after deleting color'
                );
              }

              // Reload colors with usage to get updated data
              await get().loadColorsWithUsage();

              // Also refresh products since relationships may have changed
              try {
                const { fetchProductsWithColors } = await import(
                  './product.store'
                ).then(m => m.useProductStore.getState());
                await fetchProductsWithColors();
              } catch (error) {
                console.error(
                  '[ColorStore] Error refreshing products after color deletion:',
                  error
                );
              }

              return true;
            }
            console.warn('[ColorStore] Delete failed - response:', response);
            return false;
          } catch (error) {
            console.error('[ColorStore] Error deleting color:', error);
            return false;
          }
        },

        /**
         * Import colors using typed API with cache invalidation
         */
        importColors: async mergeMode => {
          try {
            // Cast mergeMode to the correct type
            const typedMergeMode = mergeMode as 'replace' | 'merge' | undefined;
            const result = await window.colorAPI.importColors(typedMergeMode);
            if (result) {
              // Invalidate cache on bulk mutation
              const orgStore = await import('./organization.store').then(m =>
                m.useOrganizationStore.getState()
              );
              if (orgStore.currentOrganization?.external_id) {
                colorCacheService.invalidateOrganization(
                  orgStore.currentOrganization.external_id
                );
                console.log(
                  '[ColorStore] Cache invalidated after importing colors'
                );
              }

              // Reload colors with usage to get updated data
              await get().loadColorsWithUsage();
            }
            return result;
          } catch (error) {
            console.error('[ColorStore] Error importing colors:', error);
            return false;
          }
        },

        /**
         * Export colors using typed API
         */
        exportColors: async (filePath, format) => {
          try {
            // Cast format to the correct type
            const typedFormat = format as 'json' | 'csv' | undefined;
            return await window.colorAPI.exportColors(filePath, typedFormat);
          } catch (error) {
            console.error('[ColorStore] Error exporting colors:', error);
            return false;
          }
        },

        /**
         * Clear all colors using typed API with cache invalidation
         */
        clearColors: async () => {
          try {
            // Use typed colorAPI
            const result = await window.colorAPI.clearAll();
            if (result) {
              // Invalidate cache on bulk deletion
              const orgStore = await import('./organization.store').then(m =>
                m.useOrganizationStore.getState()
              );
              if (orgStore.currentOrganization?.external_id) {
                colorCacheService.invalidateOrganization(
                  orgStore.currentOrganization.external_id
                );
                console.log(
                  '[ColorStore] Cache invalidated after clearing colors'
                );
              }

              // Reload colors with usage to get updated data
              await get().loadColorsWithUsage();
              return true;
            }
            return false;
          } catch (error) {
            console.error('[ColorStore] Error clearing colors:', error);
            return false;
          }
        },

        /**
         * Get cache performance metrics
         */
        getCacheMetrics: () => {
          return colorCacheService.getMetrics();
        },

        /**
         * Log cache performance summary
         */
        logCachePerformance: () => {
          colorCacheService.logPerformanceSummary();
        },

        /**
         * Load Pantone colors using enhanced service
         */
        loadPantoneColors: async () => {
          try {
            const { getEnhancedColorLibraryService } = await import(
              '../services/enhanced-color-library.service'
            );
            const service = getEnhancedColorLibraryService();
            const pantoneColors = await service.getAllPantoneColors();
            set({ pantoneColors });
            return pantoneColors;
          } catch (error) {
            console.error('[ColorStore] Error loading Pantone colors:', error);
            // Fallback to empty array
            set({ pantoneColors: [] });
            return [];
          }
        },

        /**
         * Load RAL colors using enhanced service
         */
        loadRalColors: async () => {
          try {
            const { getEnhancedColorLibraryService } = await import(
              '../services/enhanced-color-library.service'
            );
            const service = getEnhancedColorLibraryService();
            const ralColors = await service.getAllRalColors();
            set({ ralColors });
            return ralColors;
          } catch (error) {
            console.error('[ColorStore] Error loading RAL colors:', error);
            // Fallback to empty array
            set({ ralColors: [] });
            return [];
          }
        },

        /**
         * Toggle dark mode
         */
        toggleDarkMode: () => {
          set(state => ({ darkMode: !state.darkMode }));
        },
      };
    },
    {
      name: 'color-store',
    }
  )
);

// Computed selectors
export const useFilteredColors = () => {
  const { colors, searchQuery } = useColorStore();

  if (!searchQuery.trim()) {
    return colors;
  }

  const query = searchQuery.toLowerCase();
  return colors.filter(
    color =>
      color.name?.toLowerCase().includes(query) ||
      color.code?.toLowerCase().includes(query) ||
      color.hex?.toLowerCase().includes(query)
  );
};

export const useColorUsageCount = (colorId: string) => {
  const { usageCounts } = useColorStore();
  return usageCounts[colorId] || { count: 0, products: [] };
};

// Advanced memoized selectors for complex data transformations

/**
 * Memoized selector for filtered colors with optimized search logic
 */
export const useFilteredColorsAdvanced = () => {
  const { colors, searchQuery } = useColorStore();

  return useMemo(() => {
    // DEBUG: Log search state
    console.log(
      '[useFilteredColorsAdvanced] 🔍 Search state:',
      JSON.stringify(
        {
          searchQuery,
          totalColors: colors?.length || 0,
          colorsIsArray: Array.isArray(colors),
          sampleColors: colors
            ?.slice(0, 3)
            ?.map(c => ({ name: c.name, code: c.code, product: c.product })),
        },
        null,
        2
      )
    );

    if (!colors || !Array.isArray(colors)) {
      console.log(
        '[useFilteredColorsAdvanced] ❌ No colors or colors not array, returning empty'
      );
      return [];
    }
    if (!searchQuery) {
      console.log(
        '[useFilteredColorsAdvanced] ℹ️ No search query, returning all colors:',
        colors.length
      );
      return colors;
    }

    // Check if it's a product filter query
    if (searchQuery.toLowerCase().startsWith('product:')) {
      const productName = searchQuery.substring(8).replace(/"/g, '').trim();
      console.log(
        '[useFilteredColorsAdvanced] 🎯 Product filter:',
        productName
      );
      const filtered = colors.filter(
        color => color.product?.toLowerCase() === productName.toLowerCase()
      );
      console.log(
        '[useFilteredColorsAdvanced] 🎯 Product filter results:',
        filtered.length
      );
      return filtered;
    }

    // Regular search query
    const query = searchQuery.toLowerCase();
    console.log('[useFilteredColorsAdvanced] 🔍 Regular search query:', query);

    const filtered = colors.filter(color => {
      // Check basic color properties individually for better debugging
      const productMatch = color.product?.toLowerCase()?.includes(query);
      const nameMatch = color.name?.toLowerCase()?.includes(query);
      const codeMatch = color.code?.toLowerCase()?.includes(query);
      const hexMatch = color.hex?.toLowerCase()?.includes(query);
      const cmykMatch = color.cmyk?.toLowerCase()?.includes(query);
      const notesMatch =
        color.notes && color.notes.toLowerCase().includes(query);

      const basicMatch =
        productMatch ||
        nameMatch ||
        codeMatch ||
        hexMatch ||
        cmykMatch ||
        notesMatch;

      // Check gradient color codes if it's a gradient (NEW FORMAT)
      let gradientMatch = false;
      if (
        color.gradient?.colorCodes &&
        Array.isArray(color.gradient.colorCodes)
      ) {
        gradientMatch = color.gradient.colorCodes.some(
          colorCode => colorCode && colorCode.toLowerCase().includes(query)
        );
      }

      const matches = basicMatch || gradientMatch;

      // DEBUG: Log all matches for investigation queries, with detailed breakdown
      if (
        matches &&
        (query === 'orange' || query === 'strawberry' || query === 'blue')
      ) {
        console.log(
          '[useFilteredColorsAdvanced] ✅ DETAILED Match found:',
          JSON.stringify(
            {
              query,
              colorName: color.name,
              colorCode: color.code,
              colorProduct: color.product,
              colorHex: color.hex,
              colorCmyk: color.cmyk,
              colorNotes: color.notes,
              // Individual match breakdown
              productMatch,
              nameMatch,
              codeMatch,
              hexMatch,
              cmykMatch,
              notesMatch,
              gradientMatch,
            },
            null,
            2
          )
        );
      }

      return matches;
    });

    console.log(
      '[useFilteredColorsAdvanced] 🔍 Search results:',
      JSON.stringify(
        {
          query,
          totalMatches: filtered.length,
          matchingColors: filtered
            .slice(0, 5)
            .map(c => ({ name: c.name, code: c.code, product: c.product })),
        },
        null,
        2
      )
    );

    return filtered;
  }, [colors, searchQuery]);
};

/**
 * Memoized selector for grouped colors by clean code (reference view)
 */
export const useGroupedByCode = () => {
  const { colors, searchQuery } = useColorStore();

  return useMemo(() => {
    // Early exit for empty colors to prevent unnecessary computation
    if (!colors || colors.length === 0) {
      return [];
    }

    // Helper function to get clean code (same as in ColorTableRow)
    const getCleanCode = (code: string): string => {
      if (!code || typeof code !== 'string') {
        return '';
      }
      const parts = code.split('-');
      return parts[0] || '';
    };

    // Helper function to check if any color in a group matches search query
    const groupMatchesSearch = (
      colorsInGroup: ColorEntry[],
      query: string
    ): boolean => {
      if (!query || query.trim() === '') {
        return true; // No search = show all
      }

      const lowerQuery = query.toLowerCase();

      // Check if it's a product filter query
      if (lowerQuery.startsWith('product:')) {
        const productName = query
          .substring(8)
          .replace(/"/g, '')
          .trim()
          .toLowerCase();
        return colorsInGroup.some(
          color => color.product?.toLowerCase() === productName
        );
      }

      // Regular search - check if ANY color in the group matches
      return colorsInGroup.some(color => {
        // Check basic color properties
        const basicMatch =
          color.product?.toLowerCase()?.includes(lowerQuery) ||
          color.name?.toLowerCase()?.includes(lowerQuery) ||
          color.code?.toLowerCase()?.includes(lowerQuery) ||
          color.hex?.toLowerCase()?.includes(lowerQuery) ||
          color.cmyk?.toLowerCase()?.includes(lowerQuery) ||
          (color.notes && color.notes.toLowerCase().includes(lowerQuery));

        // Check gradient color codes if it's a gradient (NEW FORMAT)
        let gradientMatch = false;
        if (
          color.gradient?.colorCodes &&
          Array.isArray(color.gradient.colorCodes)
        ) {
          gradientMatch = color.gradient.colorCodes.some(
            colorCode =>
              colorCode && colorCode.toLowerCase().includes(lowerQuery)
          );
        }

        return basicMatch || gradientMatch;
      });
    };

    // STEP 1: Group ALL colors by clean code first (no filtering)
    // Use Map for better memory efficiency and faster lookups
    const allCodeGroups = new Map<string, ColorEntry[]>();

    // Process colors in batches to prevent blocking the main thread
    for (const color of colors) {
      const code = color.code;
      if (!code) {
        continue; // Skip colors without codes
      }

      const cleanCode = getCleanCode(code);
      if (!cleanCode) {
        continue; // Skip if getCleanCode returns empty
      }

      // Use get/set pattern to avoid double lookup
      const existing = allCodeGroups.get(cleanCode);
      if (existing) {
        existing.push(color);
      } else {
        allCodeGroups.set(cleanCode, [color]);
      }
    }

    // STEP 2: Filter groups where any color matches search, and build final result
    const codeGroups: Array<{
      cleanCode: string;
      color: ColorEntry;
      usageCount: number;
      products: string[];
    }> = [];

    allCodeGroups.forEach((colorsInGroup, cleanCode) => {
      // Check if this group should be included based on search
      if (!groupMatchesSearch(colorsInGroup, searchQuery)) {
        return; // Skip this group - no colors match search
      }

      // Calculate actual usage count by clean code (total instances)
      const actualUsageCount = colorsInGroup.length;

      // Collect all unique products that use this clean code (optimized)
      const uniqueProducts = new Set<string>();
      for (const color of colorsInGroup) {
        if (color.product && color.product.trim() !== '') {
          uniqueProducts.add(color.product);
        }
      }
      const allProductsForCode = Array.from(uniqueProducts);

      // Use first color as representative for display (prefer one that matches search if possible)
      let representativeColor = colorsInGroup[0];
      if (searchQuery && searchQuery.trim() !== '') {
        const matchingColor = colorsInGroup.find(color => {
          const lowerQuery = searchQuery.toLowerCase();
          return (
            color.name?.toLowerCase()?.includes(lowerQuery) ||
            color.product?.toLowerCase()?.includes(lowerQuery)
          );
        });
        if (matchingColor) {
          representativeColor = matchingColor;
        }
      }

      codeGroups.push({
        cleanCode,
        color: representativeColor,
        usageCount: actualUsageCount, // Full count of all instances
        products: allProductsForCode,
      });
    });

    // STEP 3: Sort groups by clean code for consistent display
    return codeGroups.sort((a, b) => a.cleanCode.localeCompare(b.cleanCode));
  }, [colors, searchQuery]);
};
