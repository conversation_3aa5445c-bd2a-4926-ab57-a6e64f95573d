/**
 * @file sync-error-recovery.ts
 * @description Comprehensive error recovery service for sync operations
 *
 * This service handles various sync failure scenarios and provides
 * recovery strategies to ensure data consistency and user experience.
 */

import { ConflictResolutionStrategy } from '../../../shared/constants/conflict-resolution';
import { ensureAuthenticatedSession } from '../supabase-client';

export interface SyncError {
  type: 'authentication' | 'network' | 'conflict' | 'validation' | 'transaction' | 'unknown';
  message: string;
  retryable: boolean;
  recoveryAction?: string;
  context?: SyncErrorContext;
}

export interface SyncErrorContext {
  operation: string;
  table?: string;
  recordId?: string;
  organizationId?: string;
  userId?: string;
  transactionId?: string;
  timestamp: number;
}

export interface RecoveryResult {
  success: boolean;
  error?: string;
  recoveryAction?: string;
  shouldRetry?: boolean;
  rollbackRequired?: boolean;
}

export interface ConflictMetadata {
  id: string;
  table: string;
  recordId: string;
  conflictType: ConflictType;
  detectedAt: number;
  localData: any;
  remoteData: any;
  baseData?: any;
  affectedFields: string[];
  resolutionStrategy: ConflictResolutionStrategy;
  userResolutionRequired: boolean;
  resolvedAt?: number;
  resolvedBy?: string;
  resolution?: ConflictResolution;
}

export interface ConflictResolution {
  strategy: ConflictResolutionStrategy;
  resolvedData: any;
  mergeDetails?: {
    autoMergedFields: string[];
    manualFields: string[];
    conflicts: FieldConflict[];
  };
}

export interface FieldConflict {
  field: string;
  localValue: any;
  remoteValue: any;
  baseValue?: any;
  resolution: 'local' | 'remote' | 'merged' | 'manual';
  mergedValue?: any;
}

export enum ConflictType {
  UPDATE_UPDATE = 'update_update',
  UPDATE_DELETE = 'update_delete',
  DELETE_UPDATE = 'delete_update',
  CREATE_CREATE = 'create_create',
  FIELD_LEVEL = 'field_level'
}

export interface RollbackAction {
  id: string;
  type: 'insert' | 'update' | 'delete';
  table: string;
  recordId: string;
  beforeData?: any;
  afterData?: any;
  timestamp: number;
}

export interface RollbackPlan {
  id: string;
  transactionId: string;
  actions: RollbackAction[];
  createdAt: number;
  executedAt?: number;
  success?: boolean;
  errors?: string[];
}

/**
 * Comprehensive sync error recovery service
 */
export class SyncErrorRecovery {
  private static readonly MAX_RETRY_ATTEMPTS = 3;
  private static readonly RETRY_DELAY_MS = 2000;
  private static conflictStorage = new Map<string, ConflictMetadata>();
  private static rollbackPlans = new Map<string, RollbackPlan>();
  private static rollbackPlanCounter = 0;

  /**
   * Analyze and categorize sync errors with enhanced context
   */
  static analyzeSyncError(error: any, context?: SyncErrorContext): SyncError {
    const errorMessage = error?.message || String(error);
    const errorString = errorMessage.toLowerCase();

    // Transaction errors
    if (errorString.includes('transaction') || 
        errorString.includes('rollback') || 
        errorString.includes('deadlock') ||
        errorString.includes('lock timeout')) {
      return {
        type: 'transaction',
        message: errorMessage,
        retryable: true,
        recoveryAction: 'rollback_and_retry',
        context
      };
    }

    // Authentication errors
    if (
      errorString.includes('authentication') ||
      errorString.includes('unauthorized') ||
      errorString.includes('invalid_grant') ||
      errorString.includes('session') ||
      errorString.includes('token')
    ) {
      return {
        type: 'authentication',
        message: errorMessage,
        retryable: true,
        recoveryAction: 'refresh_session',
        context
      };
    }

    // Network errors
    if (
      errorString.includes('network') ||
      errorString.includes('connection') ||
      errorString.includes('timeout') ||
      errorString.includes('fetch')
    ) {
      return {
        type: 'network',
        message: errorMessage,
        retryable: true,
        recoveryAction: 'retry_with_backoff',
        context
      };
    }

    // Conflict errors
    if (
      errorString.includes('conflict') ||
      errorString.includes('duplicate') ||
      errorString.includes('unique constraint')
    ) {
      return {
        type: 'conflict',
        message: errorMessage,
        retryable: false,
        recoveryAction: 'conflict_resolution',
        context
      };
    }

    // Validation errors
    if (
      errorString.includes('validation') ||
      errorString.includes('invalid') ||
      errorString.includes('required')
    ) {
      return {
        type: 'validation',
        message: errorMessage,
        retryable: false,
        recoveryAction: 'data_cleanup',
        context
      };
    }

    // Unknown errors
    return {
      type: 'unknown',
      message: errorMessage,
      retryable: true,
      recoveryAction: 'retry_once',
      context
    };
  }

  /**
   * Attempt to recover from sync errors with enhanced handling
   */
  static async attemptRecovery(error: SyncError): Promise<RecoveryResult> {
    console.log(
      `[SyncRecovery] Attempting recovery for ${error.type} error:`,
      error.message
    );

    switch (error.type) {
      case 'authentication':
        return this.recoverFromAuthError();

      case 'network':
        return this.recoverFromNetworkError();

      case 'conflict':
        return this.recoverFromConflictError(error);

      case 'validation':
        return this.recoverFromValidationError(error);
      
      case 'transaction':
        return this.recoverFromTransactionError(error);
      
      default:
        return this.recoverFromUnknownError(error);
    }
  }

  /**
   * Detect conflicts between local and remote data
   */
  static detectConflicts(
    localData: any,
    remoteData: any,
    baseData?: any,
    table?: string,
    recordId?: string
  ): ConflictMetadata | null {
    if (!localData || !remoteData) {
      return null;
    }

    const conflictId = `${table}_${recordId}_${Date.now()}`;
    const affectedFields: string[] = [];
    let conflictType: ConflictType;

    // Determine conflict type
    if (localData.deleted_at && remoteData.deleted_at) {
      return null; // Both deleted, no conflict
    } else if (localData.deleted_at && !remoteData.deleted_at) {
      conflictType = ConflictType.DELETE_UPDATE;
    } else if (!localData.deleted_at && remoteData.deleted_at) {
      conflictType = ConflictType.UPDATE_DELETE;
    } else {
      conflictType = ConflictType.UPDATE_UPDATE;
    }

    // Find affected fields
    const allFields = new Set([
      ...Object.keys(localData),
      ...Object.keys(remoteData)
    ]);

    for (const field of allFields) {
      if (field === 'id' || field === 'created_at') continue;
      
      const localValue = localData[field];
      const remoteValue = remoteData[field];
      
      if (JSON.stringify(localValue) !== JSON.stringify(remoteValue)) {
        affectedFields.push(field);
      }
    }

    if (affectedFields.length === 0) {
      return null; // No actual conflicts
    }

    const conflictMetadata: ConflictMetadata = {
      id: conflictId,
      table: table || 'unknown',
      recordId: recordId || 'unknown',
      conflictType,
      detectedAt: Date.now(),
      localData,
      remoteData,
      baseData,
      affectedFields,
      resolutionStrategy: ConflictResolutionStrategy.MANUAL,
      userResolutionRequired: true
    };

    // Store conflict for later resolution
    this.conflictStorage.set(conflictId, conflictMetadata);

    console.log(`[SyncRecovery] Conflict detected:`, {
      id: conflictId,
      type: conflictType,
      table,
      recordId,
      affectedFields
    });

    return conflictMetadata;
  }

  /**
   * Perform three-way merge for conflict resolution
   */
  static performThreeWayMerge(
    localData: any,
    remoteData: any,
    baseData: any
  ): ConflictResolution {
    const mergedData = { ...baseData };
    const autoMergedFields: string[] = [];
    const manualFields: string[] = [];
    const conflicts: FieldConflict[] = [];

    const allFields = new Set([
      ...Object.keys(localData),
      ...Object.keys(remoteData),
      ...Object.keys(baseData)
    ]);

    for (const field of allFields) {
      if (field === 'id' || field === 'created_at') {
        mergedData[field] = baseData[field] || localData[field] || remoteData[field];
        continue;
      }

      const localValue = localData[field];
      const remoteValue = remoteData[field];
      const baseValue = baseData[field];

      const fieldConflict: FieldConflict = {
        field,
        localValue,
        remoteValue,
        baseValue,
        resolution: 'manual'
      };

      // Three-way merge logic
      if (JSON.stringify(localValue) === JSON.stringify(remoteValue)) {
        // No conflict - both sides have same value
        mergedData[field] = localValue;
        fieldConflict.resolution = 'merged';
        fieldConflict.mergedValue = localValue;
        autoMergedFields.push(field);
      } else if (JSON.stringify(localValue) === JSON.stringify(baseValue)) {
        // Local unchanged, remote changed - use remote
        mergedData[field] = remoteValue;
        fieldConflict.resolution = 'remote';
        fieldConflict.mergedValue = remoteValue;
        autoMergedFields.push(field);
      } else if (JSON.stringify(remoteValue) === JSON.stringify(baseValue)) {
        // Remote unchanged, local changed - use local
        mergedData[field] = localValue;
        fieldConflict.resolution = 'local';
        fieldConflict.mergedValue = localValue;
        autoMergedFields.push(field);
      } else {
        // Both changed differently - manual resolution required
        manualFields.push(field);
        fieldConflict.resolution = 'manual';
        // Default to local value for now
        mergedData[field] = localValue;
        fieldConflict.mergedValue = localValue;
      }

      conflicts.push(fieldConflict);
    }

    return {
      strategy: ConflictResolutionStrategy.MERGE,
      resolvedData: mergedData,
      mergeDetails: {
        autoMergedFields,
        manualFields,
        conflicts
      }
    };
  }

  /**
   * Create rollback plan for sync operations
   */
  static createRollbackPlan(
    transactionId: string,
    actions: RollbackAction[]
  ): RollbackPlan {
    this.rollbackPlanCounter++;
    const rollbackPlan: RollbackPlan = {
      id: `rollback_${transactionId}_${Date.now()}_${this.rollbackPlanCounter}`,
      transactionId,
      actions: actions.reverse(), // Reverse order for rollback
      createdAt: Date.now()
    };

    this.rollbackPlans.set(rollbackPlan.id, rollbackPlan);
    
    console.log(`[SyncRecovery] Rollback plan created:`, {
      id: rollbackPlan.id,
      transactionId,
      actionsCount: actions.length
    });

    return rollbackPlan;
  }

  /**
   * Execute rollback plan
   */
  static async executeRollback(rollbackPlanId: string): Promise<RecoveryResult> {
    const rollbackPlan = this.rollbackPlans.get(rollbackPlanId);
    if (!rollbackPlan) {
      return {
        success: false,
        error: `Rollback plan not found: ${rollbackPlanId}`
      };
    }

    console.log(`[SyncRecovery] Executing rollback plan: ${rollbackPlanId}`);

    try {
      const { ServiceLocator } = await import('../service-locator');
      const database = ServiceLocator.getDatabase();
      const errors: string[] = [];

      // Execute rollback actions in reverse order
      for (const action of rollbackPlan.actions) {
        try {
          switch (action.type) {
            case 'insert':
              // Rollback insert by deleting the record
              // Multi-tenant safety: Include organization_id if available
              let deleteWhere = 'id = ?';
              const deleteValues = [action.recordId];
              if (action.beforeData?.organization_id) {
                deleteWhere += ' AND organization_id = ?';
                deleteValues.push(action.beforeData.organization_id);
              }
              database.prepare(`DELETE FROM ${action.table} WHERE ${deleteWhere}`)
                .run(...deleteValues);
              break;
            
            case 'update':
              // Rollback update by restoring previous data
              if (action.beforeData) {
                const fields = Object.keys(action.beforeData).filter(f => f !== 'id');
                const setClause = fields.map(f => `${f} = ?`).join(', ');
                const values = fields.map(f => action.beforeData![f]);
                values.push(action.recordId);
                
                // Multi-tenant safety: Include organization_id in WHERE clause
                let updateWhere = 'id = ?';
                if (action.beforeData.organization_id) {
                  updateWhere += ' AND organization_id = ?';
                  values.push(action.beforeData.organization_id);
                }
                
                database.prepare(`UPDATE ${action.table} SET ${setClause} WHERE ${updateWhere}`)
                  .run(...values);
              }
              break;
            
            case 'delete':
              // Rollback delete by restoring the record
              if (action.beforeData) {
                const fields = Object.keys(action.beforeData);
                const fieldNames = fields.join(', ');
                const placeholders = fields.map(() => '?').join(', ');
                const values = fields.map(f => action.beforeData![f]);
                
                database.prepare(`INSERT INTO ${action.table} (${fieldNames}) VALUES (${placeholders})`)
                  .run(...values);
              }
              break;
          }
        } catch (actionError) {
          const errorMessage = actionError instanceof Error ? actionError.message : String(actionError);
          console.error(`[SyncRecovery] Rollback action failed:`, actionError);
          errors.push(`Failed to rollback ${action.type} on ${action.table}: ${errorMessage}`);
        }
      }

      // Update rollback plan status
      rollbackPlan.executedAt = Date.now();
      rollbackPlan.success = errors.length === 0;
      rollbackPlan.errors = errors;

      if (errors.length === 0) {
        console.log(`[SyncRecovery] ✅ Rollback completed successfully: ${rollbackPlanId}`);
        return {
          success: true,
          recoveryAction: 'Transaction rolled back successfully'
        };
      } else {
        console.error(`[SyncRecovery] ❌ Rollback completed with errors:`, errors);
        return {
          success: false,
          error: `Rollback completed with ${errors.length} errors`,
          recoveryAction: 'Partial rollback completed. Manual intervention may be required.'
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[SyncRecovery] Rollback execution failed:`, error);
      
      rollbackPlan.executedAt = Date.now();
      rollbackPlan.success = false;
      rollbackPlan.errors = [errorMessage];

      return {
        success: false,
        error: `Rollback execution failed: ${errorMessage}`,
        recoveryAction: 'Rollback failed. Manual database recovery may be required.'
      };
    }
  }

  /**
   * Recover from transaction errors
   */
  private static async recoverFromTransactionError(error: SyncError): Promise<RecoveryResult> {
    console.log('[SyncRecovery] Transaction error detected - attempting rollback...');
    
    if (error.context?.transactionId) {
      // Look for existing rollback plan
      const rollbackPlan = Array.from(this.rollbackPlans.values())
        .find(plan => plan.transactionId === error.context!.transactionId);
      
      if (rollbackPlan) {
        return await this.executeRollback(rollbackPlan.id);
      }
    }
    
    return {
      success: false,
      error: error.message,
      recoveryAction: 'Transaction failed. Please retry the operation.',
      shouldRetry: true,
      rollbackRequired: true
    };
  }

  /**
   * Recover from authentication errors
   */
  private static async recoverFromAuthError(): Promise<RecoveryResult> {
    try {
      console.log('[SyncRecovery] Attempting authentication recovery...');

      const authResult = await ensureAuthenticatedSession();

      if (authResult.session) {
        console.log('[SyncRecovery] ✅ Authentication recovered successfully');
        return {
          success: true,
          recoveryAction: 'Session refreshed successfully',
        };
      } else {
        console.log(
          '[SyncRecovery] ❌ Authentication recovery failed:',
          authResult.error
        );
        return {
          success: false,
          error: authResult.error || 'Failed to refresh authentication',
          recoveryAction: 'Please re-authenticate to continue syncing',
        };
      }
    } catch (error) {
      console.error('[SyncRecovery] Authentication recovery error:', error);
      return {
        success: false,
        error: 'Authentication recovery failed',
        recoveryAction: 'Please restart the application and re-authenticate',
      };
    }
  }

  /**
   * Recover from network errors
   */
  private static async recoverFromNetworkError(): Promise<RecoveryResult> {
    console.log(
      '[SyncRecovery] Attempting network recovery with exponential backoff...'
    );

    for (let attempt = 1; attempt <= this.MAX_RETRY_ATTEMPTS; attempt++) {
      try {
        // Simple network connectivity test
        const { getSupabaseClient } = await import('../supabase-client');
        const supabase = getSupabaseClient();

        // Test basic connectivity
        const { error } = await supabase
          .from('organizations')
          .select('id')
          .limit(1);

        if (!error) {
          console.log(
            `[SyncRecovery] ✅ Network connectivity restored on attempt ${attempt}`
          );
          return {
            success: true,
            recoveryAction: 'Network connectivity restored',
          };
        }

        console.log(
          `[SyncRecovery] Network test failed on attempt ${attempt}:`,
          error.message
        );

        // Wait with exponential backoff
        if (attempt < this.MAX_RETRY_ATTEMPTS) {
          const delay = this.RETRY_DELAY_MS * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      } catch (error) {
        console.log(
          `[SyncRecovery] Network recovery attempt ${attempt} failed:`,
          error
        );
      }
    }

    return {
      success: false,
      error: 'Network connectivity could not be restored',
      recoveryAction: 'Please check your internet connection and try again',
    };
  }

  /**
   * Recover from conflict errors
   */
  private static async recoverFromConflictError(
    error: SyncError
  ): Promise<RecoveryResult> {
    console.log(
      '[SyncRecovery] Conflict error detected - manual resolution required'
    );

    return {
      success: false,
      error: error.message,
      recoveryAction:
        'Data conflict detected. Please resolve conflicts manually and try syncing again.',
    };
  }

  /**
   * Recover from validation errors
   */
  private static async recoverFromValidationError(
    error: SyncError
  ): Promise<RecoveryResult> {
    console.log(
      '[SyncRecovery] Validation error detected - data cleanup required'
    );

    return {
      success: false,
      error: error.message,
      recoveryAction:
        'Invalid data detected. Please check your data and try again.',
    };
  }

  /**
   * Recover from unknown errors
   */
  private static async recoverFromUnknownError(
    error: SyncError
  ): Promise<RecoveryResult> {
    console.log('[SyncRecovery] Unknown error - attempting single retry');

    // For unknown errors, we'll recommend a single retry
    return {
      success: false,
      error: error.message,
      recoveryAction: 'Unknown error occurred. Please try syncing again.',
    };
  }

  /**
   * Get stored conflict metadata
   */
  static getConflictMetadata(conflictId: string): ConflictMetadata | null {
    return this.conflictStorage.get(conflictId) || null;
  }

  /**
   * Get all pending conflicts
   */
  static getPendingConflicts(): ConflictMetadata[] {
    return Array.from(this.conflictStorage.values())
      .filter(conflict => !conflict.resolvedAt);
  }

  /**
   * Get conflicts for a specific table
   */
  static getConflictsForTable(table: string): ConflictMetadata[] {
    return Array.from(this.conflictStorage.values())
      .filter(conflict => conflict.table === table);
  }

  /**
   * Resolve conflict with user input
   */
  static resolveConflict(
    conflictId: string,
    resolution: ConflictResolution,
    resolvedBy: string
  ): boolean {
    const conflict = this.conflictStorage.get(conflictId);
    if (!conflict) {
      console.error(`[SyncRecovery] Conflict not found: ${conflictId}`);
      return false;
    }

    conflict.resolution = resolution;
    conflict.resolvedAt = Date.now();
    conflict.resolvedBy = resolvedBy;
    conflict.userResolutionRequired = false;

    console.log(`[SyncRecovery] Conflict resolved:`, {
      id: conflictId,
      strategy: resolution.strategy,
      resolvedBy
    });

    return true;
  }

  /**
   * Apply resolved conflict to database
   */
  static async applyConflictResolution(conflictId: string): Promise<RecoveryResult> {
    const conflict = this.conflictStorage.get(conflictId);
    if (!conflict || !conflict.resolution) {
      return {
        success: false,
        error: 'Conflict not found or not resolved'
      };
    }

    try {
      const { ServiceLocator } = await import('../service-locator');
      const database = ServiceLocator.getDatabase();

      // Apply the resolved data to the database
      const resolvedData = conflict.resolution.resolvedData;
      const fields = Object.keys(resolvedData).filter(f => f !== 'id');
      const setClause = fields.map(f => `${f} = ?`).join(', ');
      const values = fields.map(f => resolvedData[f]);
      values.push(conflict.recordId);

      // Multi-tenant safety: Include organization_id in WHERE clause if available
      let whereClause = 'id = ?';
      if (resolvedData.organization_id) {
        whereClause += ' AND organization_id = ?';
        values.push(resolvedData.organization_id);
      }

      database.prepare(`UPDATE ${conflict.table} SET ${setClause} WHERE ${whereClause}`)
        .run(...values);

      console.log(`[SyncRecovery] ✅ Conflict resolution applied: ${conflictId}`);

      return {
        success: true,
        recoveryAction: 'Conflict resolved and applied to database'
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[SyncRecovery] Failed to apply conflict resolution:`, error);

      return {
        success: false,
        error: `Failed to apply conflict resolution: ${errorMessage}`,
        recoveryAction: 'Manual database update may be required'
      };
    }
  }

  /**
   * Clear resolved conflicts older than specified days
   */
  static clearResolvedConflicts(olderThanDays: number = 7): number {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    let clearedCount = 0;

    for (const [conflictId, conflict] of this.conflictStorage.entries()) {
      if (conflict.resolvedAt && conflict.resolvedAt < cutoffTime) {
        this.conflictStorage.delete(conflictId);
        clearedCount++;
      }
    }

    console.log(`[SyncRecovery] Cleared ${clearedCount} resolved conflicts older than ${olderThanDays} days`);
    return clearedCount;
  }

  /**
   * Get rollback plan by ID
   */
  static getRollbackPlan(rollbackPlanId: string): RollbackPlan | null {
    return this.rollbackPlans.get(rollbackPlanId) || null;
  }

  /**
   * Get all rollback plans for a transaction
   */
  static getRollbackPlansForTransaction(transactionId: string): RollbackPlan[] {
    return Array.from(this.rollbackPlans.values())
      .filter(plan => plan.transactionId === transactionId);
  }

  /**
   * Clear executed rollback plans older than specified days
   */
  static clearExecutedRollbackPlans(olderThanDays: number = 7): number {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    let clearedCount = 0;

    for (const [planId, plan] of this.rollbackPlans.entries()) {
      if (plan.executedAt && plan.executedAt < cutoffTime) {
        this.rollbackPlans.delete(planId);
        clearedCount++;
      }
    }

    console.log(`[SyncRecovery] Cleared ${clearedCount} executed rollback plans older than ${olderThanDays} days`);
    return clearedCount;
  }

  /**
   * Get operation history for debugging
   */
  static getOperationHistory(): {
    conflicts: ConflictMetadata[];
    rollbackPlans: RollbackPlan[];
  } {
    return {
      conflicts: Array.from(this.conflictStorage.values()),
      rollbackPlans: Array.from(this.rollbackPlans.values())
    };
  }

  /**
   * Validate conflict resolution data
   */
  static validateConflictResolution(
    conflict: ConflictMetadata,
    resolution: ConflictResolution
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate strategy
    if (!Object.values(ConflictResolutionStrategy).includes(resolution.strategy)) {
      errors.push(`Invalid resolution strategy: ${resolution.strategy}`);
    }

    // Validate resolved data has required fields
    if (!resolution.resolvedData || typeof resolution.resolvedData !== 'object') {
      errors.push('Resolved data is required and must be an object');
    } else {
      // Check that resolved data has the same ID as the conflict
      if (resolution.resolvedData.id !== conflict.recordId) {
        errors.push('Resolved data ID must match conflict record ID');
      }

      // Validate that all affected fields are addressed
      for (const field of conflict.affectedFields) {
        if (!(field in resolution.resolvedData)) {
          errors.push(`Missing resolution for affected field: ${field}`);
        }
      }
    }

    // Validate merge details if using merge strategy
    if (resolution.strategy === ConflictResolutionStrategy.MERGE) {
      if (!resolution.mergeDetails) {
        errors.push('Merge details are required for merge strategy');
      } else {
        const { conflicts: fieldConflicts } = resolution.mergeDetails;
        const manualConflicts = fieldConflicts.filter(fc => fc.resolution === 'manual');
        
        if (manualConflicts.length > 0) {
          errors.push(`Manual resolution required for fields: ${manualConflicts.map(fc => fc.field).join(', ')}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get user-friendly error message and recovery instructions
   */
  static getRecoveryInstructions(error: SyncError): string {
    switch (error.type) {
      case 'authentication':
        return 'Authentication expired. Please log out and log back in.';

      case 'network':
        return 'Network connection issue. Please check your internet connection.';

      case 'conflict':
        return 'Data conflict detected. Some changes may need manual review.';

      case 'validation':
        return 'Invalid data detected. Please check your data for errors.';

      case 'transaction':
        return 'Transaction failed. The operation will be retried automatically.';
      
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

/**
 * Helper function to handle sync errors with automatic recovery
 */
export async function handleSyncError(error: any): Promise<{
  recovered: boolean;
  message: string;
  shouldRetry: boolean;
}> {
  const syncError = SyncErrorRecovery.analyzeSyncError(error);

  if (syncError.retryable) {
    const recoveryResult = await SyncErrorRecovery.attemptRecovery(syncError);

    if (recoveryResult.success) {
      return {
        recovered: true,
        message: recoveryResult.recoveryAction || 'Recovery successful',
        shouldRetry: true,
      };
    } else {
      return {
        recovered: false,
        message:
          recoveryResult.recoveryAction ||
          recoveryResult.error ||
          'Recovery failed',
        shouldRetry: false,
      };
    }
  } else {
    return {
      recovered: false,
      message: SyncErrorRecovery.getRecoveryInstructions(syncError),
      shouldRetry: false,
    };
  }
}
