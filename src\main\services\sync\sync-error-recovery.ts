/**
 * @file sync-error-recovery.ts
 * @description Comprehensive error recovery service for sync operations
 * 
 * This service handles various sync failure scenarios and provides
 * recovery strategies to ensure data consistency and user experience.
 */

import { ensureAuthenticatedSession } from '../supabase-client';

export interface SyncError {
  type: 'authentication' | 'network' | 'conflict' | 'validation' | 'unknown';
  message: string;
  retryable: boolean;
  recoveryAction?: string;
}

export interface RecoveryResult {
  success: boolean;
  error?: string;
  recoveryAction?: string;
}

/**
 * Comprehensive sync error recovery service
 */
export class SyncErrorRecovery {
  private static readonly MAX_RETRY_ATTEMPTS = 3;
  private static readonly RETRY_DELAY_MS = 2000;

  /**
   * Analyze and categorize sync errors
   */
  static analyzeSyncError(error: any): SyncError {
    const errorMessage = error?.message || String(error);
    const errorString = errorMessage.toLowerCase();

    // Authentication errors
    if (errorString.includes('authentication') || 
        errorString.includes('unauthorized') || 
        errorString.includes('invalid_grant') ||
        errorString.includes('session') ||
        errorString.includes('token')) {
      return {
        type: 'authentication',
        message: errorMessage,
        retryable: true,
        recoveryAction: 'refresh_session'
      };
    }

    // Network errors
    if (errorString.includes('network') || 
        errorString.includes('connection') || 
        errorString.includes('timeout') ||
        errorString.includes('fetch')) {
      return {
        type: 'network',
        message: errorMessage,
        retryable: true,
        recoveryAction: 'retry_with_backoff'
      };
    }

    // Conflict errors
    if (errorString.includes('conflict') || 
        errorString.includes('duplicate') || 
        errorString.includes('unique constraint')) {
      return {
        type: 'conflict',
        message: errorMessage,
        retryable: false,
        recoveryAction: 'manual_resolution'
      };
    }

    // Validation errors
    if (errorString.includes('validation') || 
        errorString.includes('invalid') || 
        errorString.includes('required')) {
      return {
        type: 'validation',
        message: errorMessage,
        retryable: false,
        recoveryAction: 'data_cleanup'
      };
    }

    // Unknown errors
    return {
      type: 'unknown',
      message: errorMessage,
      retryable: true,
      recoveryAction: 'retry_once'
    };
  }

  /**
   * Attempt to recover from sync errors
   */
  static async attemptRecovery(error: SyncError): Promise<RecoveryResult> {
    console.log(`[SyncRecovery] Attempting recovery for ${error.type} error:`, error.message);

    switch (error.type) {
      case 'authentication':
        return this.recoverFromAuthError();
      
      case 'network':
        return this.recoverFromNetworkError();
      
      case 'conflict':
        return this.recoverFromConflictError(error);
      
      case 'validation':
        return this.recoverFromValidationError(error);
      
      default:
        return this.recoverFromUnknownError(error);
    }
  }

  /**
   * Recover from authentication errors
   */
  private static async recoverFromAuthError(): Promise<RecoveryResult> {
    try {
      console.log('[SyncRecovery] Attempting authentication recovery...');
      
      const authResult = await ensureAuthenticatedSession();
      
      if (authResult.session) {
        console.log('[SyncRecovery] ✅ Authentication recovered successfully');
        return {
          success: true,
          recoveryAction: 'Session refreshed successfully'
        };
      } else {
        console.log('[SyncRecovery] ❌ Authentication recovery failed:', authResult.error);
        return {
          success: false,
          error: authResult.error || 'Failed to refresh authentication',
          recoveryAction: 'Please re-authenticate to continue syncing'
        };
      }
    } catch (error) {
      console.error('[SyncRecovery] Authentication recovery error:', error);
      return {
        success: false,
        error: 'Authentication recovery failed',
        recoveryAction: 'Please restart the application and re-authenticate'
      };
    }
  }

  /**
   * Recover from network errors
   */
  private static async recoverFromNetworkError(): Promise<RecoveryResult> {
    console.log('[SyncRecovery] Attempting network recovery with exponential backoff...');
    
    for (let attempt = 1; attempt <= this.MAX_RETRY_ATTEMPTS; attempt++) {
      try {
        // Simple network connectivity test
        const { getSupabaseClient } = await import('../supabase-client');
        const supabase = getSupabaseClient();
        
        // Test basic connectivity
        const { error } = await supabase.from('organizations').select('id').limit(1);
        
        if (!error) {
          console.log(`[SyncRecovery] ✅ Network connectivity restored on attempt ${attempt}`);
          return {
            success: true,
            recoveryAction: 'Network connectivity restored'
          };
        }
        
        console.log(`[SyncRecovery] Network test failed on attempt ${attempt}:`, error.message);
        
        // Wait with exponential backoff
        if (attempt < this.MAX_RETRY_ATTEMPTS) {
          const delay = this.RETRY_DELAY_MS * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
      } catch (error) {
        console.log(`[SyncRecovery] Network recovery attempt ${attempt} failed:`, error);
      }
    }
    
    return {
      success: false,
      error: 'Network connectivity could not be restored',
      recoveryAction: 'Please check your internet connection and try again'
    };
  }

  /**
   * Recover from conflict errors
   */
  private static async recoverFromConflictError(error: SyncError): Promise<RecoveryResult> {
    console.log('[SyncRecovery] Conflict error detected - manual resolution required');
    
    return {
      success: false,
      error: error.message,
      recoveryAction: 'Data conflict detected. Please resolve conflicts manually and try syncing again.'
    };
  }

  /**
   * Recover from validation errors
   */
  private static async recoverFromValidationError(error: SyncError): Promise<RecoveryResult> {
    console.log('[SyncRecovery] Validation error detected - data cleanup required');
    
    return {
      success: false,
      error: error.message,
      recoveryAction: 'Invalid data detected. Please check your data and try again.'
    };
  }

  /**
   * Recover from unknown errors
   */
  private static async recoverFromUnknownError(error: SyncError): Promise<RecoveryResult> {
    console.log('[SyncRecovery] Unknown error - attempting single retry');
    
    // For unknown errors, we'll recommend a single retry
    return {
      success: false,
      error: error.message,
      recoveryAction: 'Unknown error occurred. Please try syncing again.'
    };
  }

  /**
   * Get user-friendly error message and recovery instructions
   */
  static getRecoveryInstructions(error: SyncError): string {
    switch (error.type) {
      case 'authentication':
        return 'Authentication expired. Please log out and log back in.';
      
      case 'network':
        return 'Network connection issue. Please check your internet connection.';
      
      case 'conflict':
        return 'Data conflict detected. Some changes may need manual review.';
      
      case 'validation':
        return 'Invalid data detected. Please check your data for errors.';
      
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

/**
 * Helper function to handle sync errors with automatic recovery
 */
export async function handleSyncError(error: any): Promise<{
  recovered: boolean;
  message: string;
  shouldRetry: boolean;
}> {
  const syncError = SyncErrorRecovery.analyzeSyncError(error);
  
  if (syncError.retryable) {
    const recoveryResult = await SyncErrorRecovery.attemptRecovery(syncError);
    
    if (recoveryResult.success) {
      return {
        recovered: true,
        message: recoveryResult.recoveryAction || 'Recovery successful',
        shouldRetry: true
      };
    } else {
      return {
        recovered: false,
        message: recoveryResult.recoveryAction || recoveryResult.error || 'Recovery failed',
        shouldRetry: false
      };
    }
  } else {
    return {
      recovered: false,
      message: SyncErrorRecovery.getRecoveryInstructions(syncError),
      shouldRetry: false
    };
  }
}