/**
 * @file sync-outbox.service.ts
 * @description A persistent queue for sync operations.
 */

import PatchedStore from '../../utils/electron-store-patched';
import { app } from 'electron';

export type SyncableTable = 'colors' | 'products' | 'product_colors';
export type SyncAction = 'create' | 'update' | 'delete';

export interface SyncOutboxItem {
  id: string;
  table: SyncableTable;
  action: SyncAction;
  data: any;
  timestamp: number;
  attemptCount: number;
  maxAttempts: number;
  nextAttemptTime: number;
  lastError?: string;
}

interface SyncOutboxState extends Record<string, unknown> {
  pendingChanges: SyncOutboxItem[];
}

export class SyncOutboxService {
  private store: PatchedStore<SyncOutboxState>;
  private readonly MAX_QUEUE_SIZE = 1000;
  private readonly DEFAULT_MAX_ATTEMPTS = 5;
  private readonly BASE_RETRY_DELAY = 60000; // 1 minute base delay
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.store = new PatchedStore<SyncOutboxState>({
      name: 'sync-outbox',
      cwd: app.getPath('userData'),
      defaults: {
        pendingChanges: [],
      },
      clearInvalidConfig: true,
    });

    // Auto-cleanup failed delete operations on startup
    this.performStartupCleanup();

    // Start periodic cleanup every 10 minutes
    this.startPeriodicCleanup();
  }

  private performStartupCleanup(): void {
    try {
      const removedCount = this.clearNotFoundDeleteOperations();
      if (removedCount > 0) {
        console.log(
          `[SyncOutbox] Startup cleanup: removed ${removedCount} stale operations`
        );
      }

      // Additional cleanup: remove very old items (older than 24 hours)
      const oldItemsRemoved = this.clearOldItems();
      if (oldItemsRemoved > 0) {
        console.log(
          `[SyncOutbox] Startup cleanup: removed ${oldItemsRemoved} old items`
        );
      }
    } catch (error) {
      console.warn('[SyncOutbox] Error during startup cleanup:', error);
    }
  }

  /**
   * Start periodic cleanup of stale outbox items
   */
  private startPeriodicCleanup(): void {
    // Clean up every 10 minutes
    this.cleanupInterval = setInterval(() => {
      try {
        const removedCount = this.clearNotFoundDeleteOperations();
        const oldItemsRemoved = this.clearOldItems();
        const totalRemoved = removedCount + oldItemsRemoved;

        if (totalRemoved > 0) {
          console.log(
            `[SyncOutbox] Periodic cleanup: removed ${totalRemoved} stale items`
          );
        }
      } catch (error) {
        console.warn('[SyncOutbox] Error during periodic cleanup:', error);
      }
    }, 600000); // 10 minutes
  }

  /**
   * Stop periodic cleanup (for shutdown)
   */
  public stopPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Remove items older than 24 hours to prevent infinite accumulation
   */
  private clearOldItems(): number {
    const pendingChanges = this.getPendingChanges();
    const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
    const initialCount = pendingChanges.length;

    const filteredChanges = pendingChanges.filter(item => {
      if (item.timestamp < oneDayAgo) {
        console.log(
          `[SyncOutbox] Removing old ${item.action} operation for ${item.table} (${Math.round((Date.now() - item.timestamp) / (60 * 60 * 1000))}h old)`
        );
        return false;
      }
      return true;
    });

    const removedCount = initialCount - filteredChanges.length;
    if (removedCount > 0) {
      this.store.set('pendingChanges', filteredChanges);
    }

    return removedCount;
  }

  async addToOutbox(
    table: SyncableTable,
    action: SyncAction,
    data: any
  ): Promise<string> {
    const id = `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = Date.now();

    const newItem: SyncOutboxItem = {
      id,
      table,
      action,
      data,
      timestamp: now,
      attemptCount: 0,
      maxAttempts: this.DEFAULT_MAX_ATTEMPTS,
      nextAttemptTime: now,
    };

    const pendingChanges = this.getPendingChanges();
    if (pendingChanges.length >= this.MAX_QUEUE_SIZE) {
      console.warn('[SyncOutbox] Queue is full, removing oldest item.');
      pendingChanges.shift();
    }

    pendingChanges.push(newItem);
    this.store.set('pendingChanges', pendingChanges);

    console.log(`[SyncOutbox] Added ${action} on ${table} to outbox.`);
    return id;
  }

  getPendingChanges(): SyncOutboxItem[] {
    const allChanges = this.store.get('pendingChanges', []);

    // Filter out items that are past their next attempt time and have exceeded max attempts
    const validChanges = allChanges.filter(item => {
      // Remove items that have exceeded max attempts
      if (item.attemptCount >= item.maxAttempts) {
        console.log(
          `[SyncOutbox] Filtering out exhausted item: ${item.action} on ${item.table} (${item.attemptCount}/${item.maxAttempts} attempts)`
        );
        return false;
      }

      // Remove items with "not found" errors (these are effectively completed)
      if (
        item.lastError &&
        (item.lastError.includes('not found') ||
          item.lastError.includes('does not exist') ||
          (item.lastError.includes('Product') &&
            item.lastError.includes('not found')))
      ) {
        console.log(
          `[SyncOutbox] Filtering out "not found" item: ${item.action} on ${item.table}`
        );
        return false;
      }

      return true;
    });

    // Update the store if we filtered out any items
    if (validChanges.length !== allChanges.length) {
      this.store.set('pendingChanges', validChanges);
      console.log(
        `[SyncOutbox] Auto-cleaned ${allChanges.length - validChanges.length} stale items from outbox`
      );
    }

    return validChanges;
  }

  markAsSynced(id: string): void {
    const pendingChanges = this.getPendingChanges().filter(
      item => item.id !== id
    );
    this.store.set('pendingChanges', pendingChanges);
  }

  clearAll(): void {
    console.log('[SyncOutbox] Clearing all pending changes');
    this.store.set('pendingChanges', []);
  }

  /**
   * Clear failed delete operations for items that no longer exist
   * These operations fail with "not found" errors but should be considered successful
   * since the goal (deletion) has already been achieved
   */
  clearNotFoundDeleteOperations(): number {
    const pendingChanges = this.getPendingChanges();
    const initialCount = pendingChanges.length;

    const filteredChanges = pendingChanges.filter(item => {
      // Remove operations that failed with "not found" errors (any operation type)
      if (
        item.lastError &&
        (item.lastError.includes('not found') ||
          (item.lastError.includes('Product') &&
            item.lastError.includes('not found')) ||
          item.lastError.includes('does not exist'))
      ) {
        console.log(
          `[SyncOutbox] Removing failed ${item.action} operation for ${item.table}: ${item.lastError}`
        );
        return false;
      }

      // Remove operations that have exceeded max attempts
      if (item.attemptCount >= item.maxAttempts) {
        console.log(
          `[SyncOutbox] Removing exhausted ${item.action} operation for ${item.table} (${item.attemptCount}/${item.maxAttempts} attempts)`
        );
        return false;
      }

      return true;
    });

    const removedCount = initialCount - filteredChanges.length;
    if (removedCount > 0) {
      this.store.set('pendingChanges', filteredChanges);
      console.log(
        `[SyncOutbox] Cleared ${removedCount} failed/exhausted operations`
      );
    }

    return removedCount;
  }

  /**
   * Clear all outbox items (for emergency cleanup)
   */
  clearAllOutbox(): number {
    const pendingChanges = this.getPendingChanges();
    const count = pendingChanges.length;
    this.store.set('pendingChanges', []);
    console.log(
      `[SyncOutbox] Emergency clear: removed ${count} pending operations`
    );
    return count;
  }

  markAsFailed(id: string, error: string): void {
    const pendingChanges = this.getPendingChanges();
    const itemIndex = pendingChanges.findIndex(item => item.id === id);

    if (itemIndex === -1) {
      return;
    }

    const item = pendingChanges[itemIndex];
    if (!item) {return;}

    item.attemptCount++;
    item.lastError = error;

    // Auto-clear delete operations that fail with "not found" errors
    // Since the goal (deletion) has already been achieved
    if (item.action === 'delete' && error && error.includes('not found')) {
      console.log(
        `[SyncOutbox] Auto-clearing delete operation for non-existent ${item.table}: ${error}`
      );
      this.markAsSynced(id); // Remove from queue as successful
      return;
    }

    if (item.attemptCount >= item.maxAttempts) {
      console.error(`[SyncOutbox] Item ${id} exceeded max attempts.`);
      this.markAsSynced(id); // Remove from queue
      return;
    }

    const delay = this.BASE_RETRY_DELAY * Math.pow(2, item.attemptCount - 1);
    item.nextAttemptTime = Date.now() + delay;

    pendingChanges[itemIndex] = item;
    this.store.set('pendingChanges', pendingChanges);
  }
}

export const syncOutboxService = new SyncOutboxService();
