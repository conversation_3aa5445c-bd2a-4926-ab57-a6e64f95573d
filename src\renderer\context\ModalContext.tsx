/**
 * @file ModalContext.tsx
 * @description Centralized modal management system
 */

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from 'react';

export type ModalType =
  | 'settings'
  | 'license'
  | 'colorForm'
  | 'productForm'
  | 'gradientPicker'
  | 'colorComparison'
  | 'setup'
  | 'acceptInvitation'
  | 'help'
  | 'export'
  | 'import'
  | 'datasheet'
  | 'colorSpace3D';

// Define specific modal data types
export interface ColorFormData {
  color?: {
    id: string;
    name: string;
    hex: string;
    cmyk?: string;
    product?: string;
  };
  editMode?: boolean;
}

export interface ProductFormData {
  product?: {
    id: string;
    name: string;
    description?: string;
  };
  editMode?: boolean;
}

export interface GradientPickerData {
  initialValue?: string;
  productName?: string;
  colorEntry?: { id: string; name: string };
}

export interface AcceptInvitationData {
  token: string;
  organizationName?: string;
}

export interface DatasheetData {
  url: string;
  title?: string;
  productId?: string;
}

export interface ColorSpace3DData {
  colors: Array<{ id: string; name: string; hex: string }>;
  selectedColorId?: string;
}

export type ModalData =
  | ColorFormData
  | ProductFormData
  | GradientPickerData
  | AcceptInvitationData
  | DatasheetData
  | ColorSpace3DData
  | Record<string, unknown>
  | undefined;

export interface ModalState {
  type: ModalType;
  isOpen: boolean;
  data?: ModalData;
  onClose?: () => void;
}

export interface ModalContextValue {
  modals: Map<ModalType, ModalState>;
  openModal: (type: ModalType, data?: ModalData, onClose?: () => void) => void;
  closeModal: (type: ModalType) => void;
  closeAllModals: () => void;
  isModalOpen: (type: ModalType) => boolean;
  getModalData: (type: ModalType) => ModalData;
}

const ModalContext = createContext<ModalContextValue | undefined>(undefined);

interface ModalProviderProps {
  children: ReactNode;
}

/**
 * Modal Provider component
 */
export const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {
  const [modals, setModals] = useState<Map<ModalType, ModalState>>(new Map());

  const openModal = useCallback(
    (type: ModalType, data?: ModalData, onClose?: () => void) => {
      setModals(prev => {
        const newModals = new Map(prev);
        newModals.set(type, {
          type,
          isOpen: true,
          data,
          onClose,
        });
        return newModals;
      });
    },
    []
  );

  const closeModal = useCallback((type: ModalType) => {
    setModals(prev => {
      const newModals = new Map(prev);
      const modal = newModals.get(type);

      if (modal) {
        // Call onClose callback if provided
        modal.onClose?.();

        // Remove modal from state
        newModals.delete(type);
      }

      return newModals;
    });
  }, []);

  const closeAllModals = useCallback(() => {
    setModals(prev => {
      // Call onClose callbacks for all open modals
      prev.forEach(modal => {
        if (modal.isOpen) {
          modal.onClose?.();
        }
      });

      return new Map();
    });
  }, []);

  const isModalOpen = useCallback(
    (type: ModalType) => {
      return modals.get(type)?.isOpen ?? false;
    },
    [modals]
  );

  const getModalData = useCallback(
    (type: ModalType) => {
      return modals.get(type)?.data;
    },
    [modals]
  );

  const value: ModalContextValue = {
    modals,
    openModal,
    closeModal,
    closeAllModals,
    isModalOpen,
    getModalData,
  };

  return (
    <ModalContext.Provider value={value}>{children}</ModalContext.Provider>
  );
};

/**
 * Hook to use modal context
 */
export const useModal = () => {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

/**
 * Hook for managing a specific modal
 */
export const useModalState = (type: ModalType) => {
  const { openModal, closeModal, isModalOpen, getModalData } = useModal();

  const open = useCallback(
    (data?: any, onClose?: () => void) => {
      openModal(type, data, onClose);
    },
    [openModal, type]
  );

  const close = useCallback(() => {
    closeModal(type);
  }, [closeModal, type]);

  return {
    isOpen: isModalOpen(type),
    data: getModalData(type),
    open,
    close,
  };
};

export default ModalContext;
