/**
 * @file ResetSection.tsx
 * @description Reset application data section component
 */

import React, { useState } from 'react';
import { AlertTriangle, RotateCcw } from 'lucide-react';

/**
 * Reset section component
 */
export const ResetSection: React.FC = () => {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [resetStatus, setResetStatus] = useState<{ success?: boolean; message?: string }>({});

  const handleResetData = async () => {
    try {
      setIsResetting(true);
      setResetStatus({});
      
      const result = await window.api.resetApplicationData();
      
      if (result.success) {
        setResetStatus({ success: true, message: result.message || 'Application data has been reset successfully' });
        setShowConfirmation(false);
        // Note: In a complete implementation, the app would restart here
      } else {
        setResetStatus({ success: false, message: result.error || 'Reset failed' });
      }
    } catch (error) {
      console.error('Failed to reset application data:', error);
      setResetStatus({ success: false, message: 'Reset failed: ' + (error instanceof Error ? error.message : String(error)) });
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <section>
      <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
        Reset Application Data
      </h3>
      
      <div className="bg-feedback-warning/10 dark:bg-yellow-900/20 border border-feedback-warning dark:border-yellow-600 rounded-[var(--radius-lg)] p-4 mb-4">
        <div className="flex items-start">
          <AlertTriangle size={20} className="text-feedback-warning dark:text-yellow-400 mr-3 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-feedback-warning dark:text-yellow-400 font-medium mb-2">
              Warning: Destructive Action
            </h4>
            <p className="text-ui-foreground-primary dark:text-gray-300 text-sm leading-relaxed">
              This action will permanently delete all your local data including colors, products, 
              settings, and authentication tokens. This action cannot be undone.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
          <h4 className="text-ui-foreground-primary dark:text-white font-medium mb-2">
            What will be reset:
          </h4>
          <ul className="space-y-1 text-sm text-ui-foreground-secondary dark:text-gray-400">
            <li>• All color palettes and collections</li>
            <li>• Product database and associations</li>
            <li>• User preferences and settings</li>
            <li>• Authentication tokens and sync data</li>
            <li>• Application logs and cache</li>
          </ul>
        </div>

        {!showConfirmation ? (
          <button
            onClick={() => setShowConfirmation(true)}
            className="w-full flex items-center justify-center px-4 py-3 bg-feedback-error dark:bg-red-600 text-white rounded-[var(--radius-md)] hover:bg-feedback-error/90 dark:hover:bg-red-700 transition-colors"
          >
            <RotateCcw size={16} className="mr-2" />
            Reset Application Data
          </button>
        ) : (
          <div className="space-y-3">
            <p className="text-center text-ui-foreground-primary dark:text-white font-medium">
              Are you absolutely sure?
            </p>
            <p className="text-center text-sm text-ui-foreground-secondary dark:text-gray-400">
              Type "RESET" to confirm this action
            </p>
            <input
              type="text"
              placeholder="Type RESET to confirm"
              className="w-full px-3 py-2 bg-ui-background-tertiary dark:bg-zinc-700 border border-ui-border-light dark:border-zinc-600 rounded-[var(--radius-md)] text-ui-foreground-primary dark:text-white text-center"
              onChange={(e) => {
                // Enable the button only when "RESET" is typed exactly
                const button = e.target.parentElement?.querySelector('button[data-confirm]') as HTMLButtonElement;
                if (button) {
                  button.disabled = e.target.value !== 'RESET';
                }
              }}
            />
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmation(false)}
                className="flex-1 px-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary/80 dark:hover:bg-zinc-600 transition-colors"
              >
                Cancel
              </button>
              <button
                data-confirm
                disabled={isResetting}
                onClick={handleResetData}
                className="flex-1 px-4 py-2 bg-feedback-error dark:bg-red-600 text-white rounded-[var(--radius-md)] hover:bg-feedback-error/90 dark:hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isResetting ? 'Resetting...' : 'Confirm Reset'}
              </button>
            </div>
          </div>
        )}

        {/* Reset Status Feedback */}
        {resetStatus.message && (
          <div className={`mt-4 p-4 rounded-[var(--radius-md)] ${
            resetStatus.success 
              ? 'bg-feedback-success/10 border border-feedback-success text-feedback-success dark:bg-green-900/20 dark:border-green-600 dark:text-green-400'
              : 'bg-feedback-error/10 border border-feedback-error text-feedback-error dark:bg-red-900/20 dark:border-red-600 dark:text-red-400'
          }`}>
            <p className="text-sm">{resetStatus.message}</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default ResetSection;