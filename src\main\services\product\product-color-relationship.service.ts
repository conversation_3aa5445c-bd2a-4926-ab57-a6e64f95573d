/**
 * @file product-color-relationship.service.ts
 * @description Service for managing product-color relationships
 *
 * This service handles all business logic related to product-color relationships,
 * including validation, bulk operations, usage analysis, and color inheritance.
 * Uses ProductRepository and ColorRepository for data access.
 */

import { ProductRepository } from '../../db/repositories/product.repository';
import { ColorRepository } from '../../db/repositories/color.repository';
import { ColorEntry } from '../../../shared/types/color.types';
import {
  createServiceErrorHandler,
  ServiceResult,
} from '../../utils/service-error-handler';
import { validateRequired } from '../../utils/input-validation';

export interface ProductColorAssociation {
  productId: string;
  colorId: string;
  displayOrder: number;
  organizationId: string;
}

export interface ColorUsageAnalysis {
  colorId: string;
  colorName: string;
  usageCount: number;
  usedInProducts: string[];
  isOrphan: boolean;
  isLibraryColor: boolean;
}

export interface BulkAssignmentResult {
  success: boolean;
  successfulAssignments: number;
  failedAssignments: number;
  errors: string[];
}

export interface SimilarProductSuggestion {
  productId: string;
  productName: string;
  sharedColors: string[];
  similarity: number; // 0-1 score
}

export class ProductColorRelationshipService {
  private errorHandler: ReturnType<typeof createServiceErrorHandler>;

  constructor(
    private productRepository: ProductRepository,
    private colorRepository: ColorRepository
  ) {
    this.errorHandler = createServiceErrorHandler(
      'ProductColorRelationshipService'
    );
  }

  /**
   * Add a color to a product with validation
   */
  addColorToProduct(
    productId: string,
    colorId: string,
    organizationId: string
  ): ServiceResult<boolean> {
    return this.errorHandler.wrap(
      () => {
        // Validate inputs
        const sanitizedProductId = validateRequired(
          'productId',
          productId,
          'uuid'
        ) as string;
        const sanitizedColorId = validateRequired(
          'colorId',
          colorId,
          'uuid'
        ) as string;
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        // Verify product exists
        const product = this.productRepository.findById(
          sanitizedProductId,
          sanitizedOrgId
        );
        if (!product) {
          throw new Error(`Product ${sanitizedProductId} not found`);
        }

        // Verify color exists
        const color = this.colorRepository.findById(
          sanitizedColorId,
          sanitizedOrgId
        );
        if (!color) {
          throw new Error(`Color ${sanitizedColorId} not found`);
        }

        // Check if association already exists
        const existingColors = this.productRepository.getProductColors(
          sanitizedProductId,
          sanitizedOrgId
        );
        const alreadyAssociated = existingColors.some(
          pc => pc.color_id === sanitizedColorId
        );

        if (alreadyAssociated) {
          console.log(
            `[ProductColorRelationshipService] Color ${sanitizedColorId} already associated with product ${sanitizedProductId}`
          );
          return true; // Consider this a success
        }

        // Add the association
        console.log(
          `[ProductColorRelationshipService] 🔗 About to call productRepository.addProductColor with:`,
          {
            productId: sanitizedProductId,
            colorId: sanitizedColorId,
            organizationId: sanitizedOrgId,
          }
        );

        const success = this.productRepository.addProductColor(
          sanitizedProductId,
          sanitizedColorId,
          sanitizedOrgId
        );

        console.log(
          `[ProductColorRelationshipService] 📊 productRepository.addProductColor returned:`,
          {
            success,
            productId: sanitizedProductId,
            colorId: sanitizedColorId,
          }
        );

        if (success) {
          console.log(
            `[ProductColorRelationshipService] ✅ Successfully added color ${sanitizedColorId} to product ${sanitizedProductId}`
          );
        } else {
          console.log(
            `[ProductColorRelationshipService] ❌ Failed to add color ${sanitizedColorId} to product ${sanitizedProductId}`
          );
        }

        return success;
      },
      'addColorToProduct',
      { productId, colorId, organizationId }
    );
  }

  /**
   * Remove a color from a product with cascade logic
   */
  removeColorFromProduct(
    productId: string,
    colorId: string,
    organizationId: string,
    options: { deleteOrphanedColor?: boolean } = {}
  ): ServiceResult<{ removed: boolean; colorDeleted?: boolean }> {
    return this.errorHandler.wrap(
      () => {
        // Validate inputs
        const sanitizedProductId = validateRequired(
          'productId',
          productId,
          'uuid'
        ) as string;
        const sanitizedColorId = validateRequired(
          'colorId',
          colorId,
          'uuid'
        ) as string;
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        // Remove the association
        const removed = this.productRepository.removeProductColor(
          sanitizedProductId,
          sanitizedColorId,
          sanitizedOrgId
        );

        if (!removed) {
          return { removed: false };
        }

        let colorDeleted = false;

        // Check if color becomes orphaned and handle cascade deletion
        if (options.deleteOrphanedColor) {
          const usageAnalysis = this.analyzeColorUsage(
            sanitizedColorId,
            sanitizedOrgId
          );

          if (
            usageAnalysis.success &&
            usageAnalysis.data &&
            usageAnalysis.data.isOrphan &&
            !usageAnalysis.data.isLibraryColor
          ) {
            colorDeleted = this.colorRepository.softDelete(
              sanitizedColorId,
              sanitizedOrgId
            );
            if (colorDeleted) {
              console.log(
                `[ProductColorRelationshipService] 🗑️ Orphaned color ${sanitizedColorId} soft deleted`
              );
            }
          }
        }

        console.log(
          `[ProductColorRelationshipService] ✅ Successfully removed color ${sanitizedColorId} from product ${sanitizedProductId}`
        );

        return { removed: true, colorDeleted };
      },
      'removeColorFromProduct',
      { productId, colorId, organizationId, options }
    );
  }

  /**
   * Bulk color assignment to multiple products
   */
  bulkAssignColors(
    productIds: string[],
    colorIds: string[],
    organizationId: string
  ): ServiceResult<BulkAssignmentResult> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        let successfulAssignments = 0;
        let failedAssignments = 0;
        const errors: string[] = [];

        for (const productId of productIds) {
          for (const colorId of colorIds) {
            try {
              const result = this.addColorToProduct(
                productId,
                colorId,
                sanitizedOrgId
              );
              if (result.success && result.data) {
                successfulAssignments++;
              } else {
                failedAssignments++;
                errors.push(
                  `Failed to assign color ${colorId} to product ${productId}: ${!result.success ? result.error : 'Unknown error'}`
                );
              }
            } catch (error) {
              failedAssignments++;
              errors.push(
                `Error assigning color ${colorId} to product ${productId}: ${error instanceof Error ? error.message : String(error)}`
              );
            }
          }
        }

        console.log(
          `[ProductColorRelationshipService] 📊 Bulk assignment complete: ${successfulAssignments} successful, ${failedAssignments} failed`
        );

        return {
          success: successfulAssignments > 0,
          successfulAssignments,
          failedAssignments,
          errors: errors.slice(0, 10), // Limit error array size
        };
      },
      'bulkAssignColors',
      {
        productIds: productIds.length,
        colorIds: colorIds.length,
        organizationId,
      }
    );
  }

  /**
   * Bulk remove colors from multiple products
   */
  bulkRemoveColors(
    productIds: string[],
    colorIds: string[],
    organizationId: string,
    options: { deleteOrphanedColors?: boolean } = {}
  ): ServiceResult<BulkAssignmentResult> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        let successfulAssignments = 0;
        let failedAssignments = 0;
        const errors: string[] = [];

        for (const productId of productIds) {
          for (const colorId of colorIds) {
            try {
              const result = this.removeColorFromProduct(
                productId,
                colorId,
                sanitizedOrgId,
                { deleteOrphanedColor: options.deleteOrphanedColors }
              );
              if (result.success && result.data?.removed) {
                successfulAssignments++;
              } else {
                failedAssignments++;
                errors.push(
                  `Failed to remove color ${colorId} from product ${productId}: ${!result.success ? result.error : 'Unknown error'}`
                );
              }
            } catch (error) {
              failedAssignments++;
              errors.push(
                `Error removing color ${colorId} from product ${productId}: ${error instanceof Error ? error.message : String(error)}`
              );
            }
          }
        }

        console.log(
          `[ProductColorRelationshipService] 📊 Bulk removal complete: ${successfulAssignments} successful, ${failedAssignments} failed`
        );

        return {
          success: successfulAssignments > 0,
          successfulAssignments,
          failedAssignments,
          errors: errors.slice(0, 10),
        };
      },
      'bulkRemoveColors',
      {
        productIds: productIds.length,
        colorIds: colorIds.length,
        organizationId,
      }
    );
  }

  /**
   * Analyze color usage across products
   */
  analyzeColorUsage(
    colorId: string,
    organizationId: string
  ): ServiceResult<ColorUsageAnalysis> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedColorId = validateRequired(
          'colorId',
          colorId,
          'uuid'
        ) as string;
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        // Get color details
        const color = this.colorRepository.findById(
          sanitizedColorId,
          sanitizedOrgId
        );
        if (!color) {
          throw new Error(`Color ${sanitizedColorId} not found`);
        }

        // Get usage statistics
        const usageMap = this.colorRepository.getUsageCounts(sanitizedOrgId);
        const usage = usageMap.get(color.display_name || color.code) || {
          count: 0,
          products: [],
        };

        const analysis: ColorUsageAnalysis = {
          colorId: sanitizedColorId,
          colorName: color.display_name || color.code,
          usageCount: usage.count,
          usedInProducts: usage.products,
          isOrphan: usage.count === 0,
          isLibraryColor: Boolean(color.is_library),
        };

        return analysis;
      },
      'analyzeColorUsage',
      { colorId, organizationId }
    );
  }

  /**
   * Get all colors for a product with proper formatting
   */
  getProductColors(
    productId: string,
    organizationId: string
  ): ServiceResult<ColorEntry[]> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedProductId = validateRequired(
          'productId',
          productId,
          'uuid'
        ) as string;
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        // Get product information
        const product = this.productRepository.findById(
          sanitizedProductId,
          sanitizedOrgId
        );
        if (!product) {
          throw new Error(`Product ${sanitizedProductId} not found`);
        }

        // Get product-color relationships
        const productColors = this.productRepository.getProductColors(
          sanitizedProductId,
          sanitizedOrgId
        );

        // Get full color details for each relationship
        const colors: ColorEntry[] = [];

        for (const pc of productColors) {
          const color = this.colorRepository.findById(
            pc.color_id,
            sanitizedOrgId
          );
          if (color) {
            // Parse color_spaces JSON to extract CMYK
            let colorSpaces: any = {};
            if (color.color_spaces) {
              try {
                colorSpaces = JSON.parse(color.color_spaces);
              } catch (error) {
                console.warn('Failed to parse color_spaces JSON:', error);
              }
            }

            // Extract CMYK from parsed data
            let cmyk = 'C:0 M:0 Y:0 K:0';
            if (colorSpaces.cmyk) {
              const cmykObj = colorSpaces.cmyk;
              cmyk = `C:${cmykObj.c || 0} M:${cmykObj.m || 0} Y:${cmykObj.y || 0} K:${cmykObj.k || 0}`;
            }

            // Convert ColorRow to ColorEntry format
            const colorEntry: ColorEntry = {
              id: color.id,
              code: color.code,
              name: color.display_name || color.code,
              hex: color.hex,
              source: color.source || undefined,
              organizationId: sanitizedOrgId,
              cmyk,
              product: product.name || '',
              gradient: color.gradient_colors
                ? JSON.parse(color.gradient_colors)
                : undefined,
              notes: color.notes || undefined,
              tags: color.tags || undefined,
              isLibrary: color.is_library,
              createdAt: color.created_at,
              updatedAt: color.updated_at,
            };
            colors.push(colorEntry);
          }
        }

        return colors;
      },
      'getProductColors',
      { productId, organizationId }
    );
  }

  /**
   * Validate product-color relationship constraints
   */
  validateRelationship(
    productId: string,
    colorId: string,
    organizationId: string
  ): ServiceResult<{ valid: boolean; issues: string[] }> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedProductId = validateRequired(
          'productId',
          productId,
          'uuid'
        ) as string;
        const sanitizedColorId = validateRequired(
          'colorId',
          colorId,
          'uuid'
        ) as string;
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        const issues: string[] = [];

        // Check if product exists
        const product = this.productRepository.findById(
          sanitizedProductId,
          sanitizedOrgId
        );
        if (!product) {
          issues.push(`Product ${sanitizedProductId} not found`);
        }

        // Check if color exists
        const color = this.colorRepository.findById(
          sanitizedColorId,
          sanitizedOrgId
        );
        if (!color) {
          issues.push(`Color ${sanitizedColorId} not found`);
        }

        // Check organization consistency
        if (product && color) {
          if (product.organization_id !== sanitizedOrgId) {
            issues.push(
              `Product belongs to different organization: ${product.organization_id}`
            );
          }
          if (color.organization_id !== sanitizedOrgId) {
            issues.push(
              `Color belongs to different organization: ${color.organization_id}`
            );
          }
        }

        // Check if relationship already exists
        if (product && color) {
          const existingColors = this.productRepository.getProductColors(
            sanitizedProductId,
            sanitizedOrgId
          );
          const alreadyExists = existingColors.some(
            pc => pc.color_id === sanitizedColorId
          );
          if (alreadyExists) {
            issues.push(
              `Relationship already exists between product and color`
            );
          }
        }

        return {
          valid: issues.length === 0,
          issues,
        };
      },
      'validateRelationship',
      { productId, colorId, organizationId }
    );
  }

  /**
   * Find products with similar color palettes
   */
  findSimilarProducts(
    targetProductId: string,
    organizationId: string,
    minimumSimilarity: number = 0.3
  ): ServiceResult<SimilarProductSuggestion[]> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedProductId = validateRequired(
          'targetProductId',
          targetProductId,
          'uuid'
        ) as string;
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        // Get colors for target product
        const targetColors = this.getProductColors(
          sanitizedProductId,
          sanitizedOrgId
        );
        if (
          !targetColors.success ||
          !targetColors.data ||
          targetColors.data.length === 0
        ) {
          return []; // No colors to compare
        }

        const targetColorIds = new Set(targetColors.data.map(c => c.id));

        // Get all products with their colors
        const allProductsWithColors =
          this.productRepository.getAllWithColors(sanitizedOrgId);

        // Group colors by product
        const productColorMap = new Map<string, string[]>();
        const productNameMap = new Map<string, string>();

        allProductsWithColors.forEach(row => {
          if (row.product_id !== sanitizedProductId) {
            // Exclude target product
            if (!productColorMap.has(row.product_id)) {
              productColorMap.set(row.product_id, []);
              productNameMap.set(row.product_id, row.product_name);
            }
            if (row.color_id) {
              productColorMap.get(row.product_id)!.push(row.color_id);
            }
          }
        });

        // Calculate similarity scores
        const suggestions: SimilarProductSuggestion[] = [];

        for (const [productId, colorIds] of productColorMap.entries()) {
          const productColorSet = new Set(colorIds);

          // Find shared colors
          const sharedColors = colorIds.filter(colorId =>
            targetColorIds.has(colorId)
          );

          if (sharedColors.length > 0) {
            // Calculate Jaccard similarity: intersection / union
            const intersection = sharedColors.length;
            const union =
              targetColorIds.size + productColorSet.size - intersection;
            const similarity = union > 0 ? intersection / union : 0;

            if (similarity >= minimumSimilarity) {
              suggestions.push({
                productId,
                productName: productNameMap.get(productId) || 'Unknown',
                sharedColors,
                similarity,
              });
            }
          }
        }

        // Sort by similarity (highest first)
        suggestions.sort((a, b) => b.similarity - a.similarity);

        console.log(
          `[ProductColorRelationshipService] 🔍 Found ${suggestions.length} similar products for ${sanitizedProductId}`
        );

        return suggestions;
      },
      'findSimilarProducts',
      { targetProductId, organizationId, minimumSimilarity }
    );
  }

  /**
   * Suggest color inheritance from similar products
   */
  suggestColorInheritance(
    targetProductId: string,
    organizationId: string
  ): ServiceResult<{
    suggestedColors: ColorEntry[];
    sourceProducts: string[];
  }> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedProductId = validateRequired(
          'targetProductId',
          targetProductId,
          'uuid'
        ) as string;
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        // Find similar products
        const similarProducts = this.findSimilarProducts(
          sanitizedProductId,
          sanitizedOrgId,
          0.2
        );
        if (
          !similarProducts.success ||
          !similarProducts.data ||
          similarProducts.data.length === 0
        ) {
          return { suggestedColors: [], sourceProducts: [] };
        }

        // Get current colors for target product
        const currentColors = this.getProductColors(
          sanitizedProductId,
          sanitizedOrgId
        );
        const currentColorIds = new Set(
          currentColors.success && currentColors.data
            ? currentColors.data.map(c => c.id)
            : []
        );

        // Collect colors from similar products that target doesn't have
        const suggestedColorMap = new Map<
          string,
          { color: ColorEntry; sources: string[] }
        >();

        for (const similarProduct of similarProducts.data) {
          const productColors = this.getProductColors(
            similarProduct.productId,
            sanitizedOrgId
          );
          if (productColors.success && productColors.data) {
            for (const color of productColors.data) {
              if (!currentColorIds.has(color.id)) {
                if (suggestedColorMap.has(color.id)) {
                  suggestedColorMap
                    .get(color.id)!
                    .sources.push(similarProduct.productName);
                } else {
                  suggestedColorMap.set(color.id, {
                    color,
                    sources: [similarProduct.productName],
                  });
                }
              }
            }
          }
        }

        // Sort suggestions by frequency (colors used in more similar products first)
        const suggestions = Array.from(suggestedColorMap.values()).sort(
          (a, b) => b.sources.length - a.sources.length
        );

        const suggestedColors = suggestions.map(s => s.color);
        const sourceProducts = [
          ...new Set(suggestions.flatMap(s => s.sources)),
        ];

        console.log(
          `[ProductColorRelationshipService] 💡 Suggested ${suggestedColors.length} colors for inheritance from ${sourceProducts.length} similar products`
        );

        return { suggestedColors, sourceProducts };
      },
      'suggestColorInheritance',
      { targetProductId, organizationId }
    );
  }

  /**
   * Get comprehensive relationship statistics for an organization
   */
  getRelationshipStatistics(organizationId: string): ServiceResult<{
    totalProducts: number;
    totalColors: number;
    totalRelationships: number;
    productsWithoutColors: number;
    orphanedColors: number;
    averageColorsPerProduct: number;
    mostUsedColors: Array<{
      colorId: string;
      colorName: string;
      usageCount: number;
    }>;
  }> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        // Get basic counts
        const allProducts = this.productRepository.findAll(sanitizedOrgId);
        const allColors = this.colorRepository.findAll(sanitizedOrgId);

        // Get usage statistics
        const usageMap = this.colorRepository.getUsageCounts(sanitizedOrgId);

        // Calculate derived statistics
        let totalRelationships = 0;
        let productsWithoutColors = 0;

        for (const product of allProducts) {
          const productColors = this.productRepository.getProductColors(
            product.id,
            sanitizedOrgId
          );
          totalRelationships += productColors.length;
          if (productColors.length === 0) {
            productsWithoutColors++;
          }
        }

        const orphanedColors = allColors.filter(
          color =>
            !color.is_library &&
            (!usageMap.has(color.display_name || color.code) ||
              usageMap.get(color.display_name || color.code)!.count === 0)
        ).length;

        const averageColorsPerProduct =
          allProducts.length > 0 ? totalRelationships / allProducts.length : 0;

        // Get most used colors
        const mostUsedColors = Array.from(usageMap.entries())
          .sort((a, b) => b[1].count - a[1].count)
          .slice(0, 10)
          .map(([colorName, usage]) => {
            const color = allColors.find(
              c => (c.display_name || c.code) === colorName
            );
            return {
              colorId: color?.id || '',
              colorName,
              usageCount: usage.count,
            };
          });

        const statistics = {
          totalProducts: allProducts.length,
          totalColors: allColors.length,
          totalRelationships,
          productsWithoutColors,
          orphanedColors,
          averageColorsPerProduct:
            Math.round(averageColorsPerProduct * 100) / 100,
          mostUsedColors,
        };

        console.log(
          `[ProductColorRelationshipService] 📊 Generated relationship statistics for organization ${sanitizedOrgId}:`,
          {
            totalProducts: statistics.totalProducts,
            totalColors: statistics.totalColors,
            totalRelationships: statistics.totalRelationships,
          }
        );

        return statistics;
      },
      'getRelationshipStatistics',
      { organizationId }
    );
  }

  /**
   * Reorder colors for a product
   */
  reorderProductColors(
    productId: string,
    colorIds: string[],
    organizationId: string
  ): ServiceResult<boolean> {
    return this.errorHandler.wrap(
      () => {
        const sanitizedProductId = validateRequired(
          'productId',
          productId,
          'uuid'
        ) as string;
        const sanitizedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;

        // Get current product colors
        const currentColors = this.productRepository.getProductColors(
          sanitizedProductId,
          sanitizedOrgId
        );
        const currentColorIds = new Set(currentColors.map(pc => pc.color_id));

        // Validate that all provided color IDs are currently associated with the product
        for (const colorId of colorIds) {
          if (!currentColorIds.has(colorId)) {
            throw new Error(
              `Color ${colorId} is not associated with product ${sanitizedProductId}`
            );
          }
        }

        // Remove all current associations and re-add in new order
        let success = true;

        // First remove all associations
        for (const colorId of Array.from(currentColorIds)) {
          const removed = this.productRepository.removeProductColor(
            sanitizedProductId,
            colorId,
            sanitizedOrgId
          );
          if (!removed) {
            console.warn(
              `[ProductColorRelationshipService] Failed to remove color ${colorId} during reorder`
            );
          }
        }

        // Re-add in specified order
        for (let i = 0; i < colorIds.length; i++) {
          const added = this.productRepository.addProductColor(
            sanitizedProductId,
            colorIds[i] ?? '',
            sanitizedOrgId
          );
          if (!added) {
            console.error(
              `[ProductColorRelationshipService] Failed to re-add color ${colorIds[i]} during reorder`
            );
            success = false;
          }
        }

        if (success) {
          console.log(
            `[ProductColorRelationshipService] ✅ Successfully reordered ${colorIds.length} colors for product ${sanitizedProductId}`
          );
        }

        return success;
      },
      'reorderProductColors',
      { productId, colorCount: colorIds.length, organizationId }
    );
  }
}
