/**
 * @file color-library-api.ts
 * @description Color Library API preload script for secure IPC communication
 */

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Typed API for color library operations
contextBridge.exposeInMainWorld('colorLibraryAPI', {
  // Search colors across libraries
  searchColors: (options: any) => {
    console.log('Preload: calling colorLibraryAPI.searchColors');
    return ipcRenderer.invoke('color-library:search-colors', options);
  },

  // Get Pantone colors
  getPantoneColors: (options: any) => {
    console.log('Preload: calling colorLibraryAPI.getPantoneColors');
    return ipcRenderer.invoke('color-library:get-pantone-colors', options);
  },

  // Get RAL colors
  getRalColors: (options: any) => {
    console.log('Preload: calling colorLibraryAPI.getRalColors');
    return ipcRenderer.invoke('color-library:get-ral-colors', options);
  },

  // Full-text search
  fullTextSearch: (query: string, options: any) => {
    console.log('Preload: calling colorLibraryAPI.fullTextSearch');
    return ipcRenderer.invoke('color-library:full-text-search', query, options);
  },

  // Get popular colors
  getPopularColors: (libraryCode?: string, limit?: number) => {
    console.log('Preload: calling colorLibraryAPI.getPopularColors');
    return ipcRenderer.invoke(
      'color-library:get-popular-colors',
      libraryCode,
      limit
    );
  },

  // Get color by external ID
  getColorByExternalId: (externalId: string) => {
    console.log('Preload: calling colorLibraryAPI.getColorByExternalId');
    return ipcRenderer.invoke(
      'color-library:get-color-by-external-id',
      externalId
    );
  },

  // Get color by library and code
  getColorByCode: (libraryCode: string, colorCode: string) => {
    console.log('Preload: calling colorLibraryAPI.getColorByCode');
    return ipcRenderer.invoke(
      'color-library:get-color-by-code',
      libraryCode,
      colorCode
    );
  },

  // Increment usage count
  incrementUsage: (externalId: string) => {
    console.log('Preload: calling colorLibraryAPI.incrementUsage');
    return ipcRenderer.invoke('color-library:increment-usage', externalId);
  },

  // Get library statistics
  getStats: () => {
    console.log('Preload: calling colorLibraryAPI.getStats');
    return ipcRenderer.invoke('color-library:get-stats');
  },

  // Force re-import (admin function)
  forceReimport: () => {
    console.log('Preload: calling colorLibraryAPI.forceReimport');
    return ipcRenderer.invoke('color-library:force-reimport');
  },

  // Check if libraries need importing
  needsImport: () => {
    console.log('Preload: calling colorLibraryAPI.needsImport');
    return ipcRenderer.invoke('color-library:needs-import');
  },

  // ===== ENHANCED METHODS FOR OPTIMIZED PERFORMANCE =====

  // Enhanced search with better performance
  searchColorsEnhanced: (options: any) => {
    console.log('Preload: calling colorLibraryAPI.searchColorsEnhanced');
    return ipcRenderer.invoke('color-library:search-colors-enhanced', options);
  },

  // Get library metadata
  getLibraryMetadata: (libraryCode: string) => {
    console.log('Preload: calling colorLibraryAPI.getLibraryMetadata');
    return ipcRenderer.invoke(
      'color-library:get-library-metadata',
      libraryCode
    );
  },

  // Get all available libraries
  getAvailableLibraries: () => {
    console.log('Preload: calling colorLibraryAPI.getAvailableLibraries');
    return ipcRenderer.invoke('color-library:get-available-libraries');
  },

  // Load library chunk for virtual scrolling
  loadLibraryChunk: (
    libraryCode: string,
    startIndex: number,
    chunkSize?: number
  ) => {
    console.log('Preload: calling colorLibraryAPI.loadLibraryChunk');
    return ipcRenderer.invoke(
      'color-library:load-library-chunk',
      libraryCode,
      startIndex,
      chunkSize
    );
  },

  // Get cache statistics
  getCacheStats: () => {
    console.log('Preload: calling colorLibraryAPI.getCacheStats');
    return ipcRenderer.invoke('color-library:get-cache-stats');
  },

  // Clear cache
  clearCache: (libraryCode?: string) => {
    console.log('Preload: calling colorLibraryAPI.clearCache');
    return ipcRenderer.invoke('color-library:clear-cache', libraryCode);
  },
});
