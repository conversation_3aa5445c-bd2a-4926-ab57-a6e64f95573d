/**
 * @file IPCRouter.simple.test.ts
 * @description Basic tests for IPC Router to verify core functionality
 */

import { IPCRouter } from '../IPCRouter';
import { IpcMainEvent } from 'electron';
import { describe, test, expect, beforeEach, vi } from 'vitest';

describe('IPCRouter - Basic Functionality', () => {
  let router: IPCRouter;

  beforeEach(() => {
    router = new IPCRouter();
  });

  test('should create router instance', () => {
    expect(router).toBeInstanceOf(IPCRouter);
  });

  test('should register basic route', () => {
    const handler = vi.fn();
    router.register('get', '/api/test', handler);

    const routes = router.getRoutes();
    expect(routes).toHaveLength(1);
    expect(routes[0].method).toBe('get');
    expect(routes[0].path).toBe('/api/test');
  });

  test('should use HTTP method shortcuts', () => {
    const handler = vi.fn();

    router.get('/api/get', handler);
    router.post('/api/post', handler);
    router.put('/api/put', handler);
    router.delete('/api/delete', handler);

    const routes = router.getRoutes();
    expect(routes).toHaveLength(4);

    const methods = routes.map(r => r.method);
    expect(methods).toContain('get');
    expect(methods).toContain('post');
    expect(methods).toContain('put');
    expect(methods).toContain('delete');
  });

  test('should parse channel formats correctly', () => {
    // Router format
    const routerResult = router.parseChannel('get:/api/colors');
    expect(routerResult).toEqual({ method: 'get', path: '/api/colors' });

    // Legacy format
    const legacyResult = router.parseChannel('color:getAll');
    expect(legacyResult).toEqual({ method: 'get', path: 'color:getAll' });

    // Malformed
    const malformedResult = router.parseChannel('malformed');
    expect(malformedResult).toEqual({ method: 'get', path: 'malformed' });
  });

  test('should match routes correctly', () => {
    const handler = vi.fn();

    router.register('get', '/api/colors', handler);
    router.register('get', '/api/colors/:id', handler);

    // Exact match
    const exactMatch = router.matchRoute('get:/api/colors');
    expect(exactMatch).not.toBeNull();
    expect(exactMatch!.route.path).toBe('/api/colors');
    expect(exactMatch!.params).toEqual({});

    // Parameter match
    const paramMatch = router.matchRoute('get:/api/colors/123');
    expect(paramMatch).not.toBeNull();
    expect(paramMatch!.route.path).toBe('/api/colors/:id');
    expect(paramMatch!.params).toEqual({ id: '123' });

    // No match
    const noMatch = router.matchRoute('get:/api/nonexistent');
    expect(noMatch).toBeNull();
  });

  test('should prevent duplicate route registration', () => {
    const handler = vi.fn();

    router.register('get', '/api/test', handler);

    expect(() => {
      router.register('get', '/api/test', handler);
    }).toThrow('Route already registered');
  });

  test('should validate route parameters', () => {
    const handler = vi.fn();

    expect(() => {
      router.register('invalid' as any, '/api/test', handler);
    }).toThrow('Invalid HTTP method');

    expect(() => {
      router.register('get', '', handler);
    }).toThrow('Route path cannot be empty');
  });

  test('should provide router statistics', () => {
    const handler = vi.fn();

    router.get('/api/test1', handler);
    router.post('/api/test2', handler);

    const stats = router.getStats();
    expect(stats.totalRoutes).toBe(2);
    expect(stats.routesByMethod.get).toBe(1);
    expect(stats.routesByMethod.post).toBe(1);
  });

  test('should create route groups', () => {
    const handler = vi.fn();

    const apiGroup = router.group('/api');
    apiGroup.get('/colors', handler);
    apiGroup.post('/colors', handler);

    const routes = router.getRoutes();
    expect(routes).toHaveLength(2);
    expect(routes[0].path).toBe('/api/colors');
    expect(routes[1].path).toBe('/api/colors');
  });

  test('should register global middleware', () => {
    const middleware = vi.fn();

    router.use('*', middleware);

    const globalMiddleware = router.getGlobalMiddleware();
    expect(globalMiddleware).toHaveLength(1);
  });

  test('should register path-specific middleware', () => {
    const middleware = vi.fn();

    router.use('/api/*', middleware);

    const pathMiddleware = router.getPathMiddleware();
    expect(pathMiddleware.has('/api/*')).toBe(true);
  });
});
