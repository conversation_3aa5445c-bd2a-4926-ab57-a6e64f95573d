/**
 * @file supabase-client.ts - Updated with PKCE and secure storage
 * @description Supabase client configuration for ChromaSync with PKCE flow and custom secure storage
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { safeStorage } from 'electron';
import { createSafeStore } from '../utils/store-util';
import { secureConfig } from '../utils/secure-config-loader';

const store = createSafeStore<Record<string, any>>({
  name: 'supabase-client'
});

// Custom secure storage adapter using Electron's safeStorage
const customSecureStorage = {
  getItem: async (key: string): Promise<string | null> => {
    try {
      console.log(`[Storage] Getting item: ${key}`);
      const encrypted = store.get(key) as string;
      if (!encrypted) {
        console.log(`[Storage] No encrypted data found for key: ${key}`);
        return null;
      }
      
      console.log(`[Storage] Found encrypted data for ${key}, length: ${encrypted.length}`);
      
      // Only decrypt if encryption is available
      if (safeStorage.isEncryptionAvailable()) {
        console.log(`[Storage] Decrypting data for ${key}`);
        const decrypted = safeStorage.decryptString(Buffer.from(encrypted, 'base64'));
        console.log(`[Storage] Successfully decrypted ${key}, length: ${decrypted.length}`);
        return decrypted;
      }
      
      // Fallback to plain storage if encryption not available
      console.log(`[Storage] Using plain storage for ${key} (encryption not available)`);
      return encrypted;
    } catch (error) {
      console.error(`[Storage] Failed to get item ${key}:`, error);
      return null;
    }
  },
  
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      console.log(`[Storage] Setting item: ${key}, value length: ${value.length}`);
      
      // Only encrypt if encryption is available
      if (safeStorage.isEncryptionAvailable()) {
        console.log(`[Storage] Encrypting data for ${key}`);
        const encrypted = safeStorage.encryptString(value);
        store.set(key, encrypted.toString('base64'));
        console.log(`[Storage] Successfully encrypted and stored ${key}`);
      } else {
        // Fallback to plain storage
        console.log(`[Storage] Using plain storage for ${key} (encryption not available)`);
        store.set(key, value);
      }
    } catch (error) {
      console.error(`[Storage] Failed to set item ${key}:`, error);
    }
  },
  
  removeItem: async (key: string): Promise<void> => {
    console.log(`[Storage] Removing item: ${key}`);
    store.delete(key);
  }
};

let supabaseClient: SupabaseClient | null = null;

export function getSupabaseClient(): SupabaseClient {
  if (!supabaseClient) {
    // ENHANCED DEBUG: Check environment variables directly first
    console.log('[Supabase] ENHANCED DEBUG - Direct environment check:');
    console.log('[Supabase]   process.env.SUPABASE_URL:', process.env.SUPABASE_URL ? 'PRESENT' : 'MISSING');
    console.log('[Supabase]   process.env.SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'PRESENT' : 'MISSING');
    
    // DEBUGGING: Check secure config loading step by step
    console.log('[Supabase] About to call secureConfig.getConfigValue...');
    console.log('[Supabase] Calling loadConfig first...');
    const config = secureConfig.loadConfig();
    console.log('[Supabase] Config loaded:', Object.keys(config));
    console.log('[Supabase] SUPABASE_URL in config:', config.SUPABASE_URL ? 'PRESENT' : 'MISSING');
    console.log('[Supabase] SUPABASE_ANON_KEY in config:', config.SUPABASE_ANON_KEY ? 'PRESENT' : 'MISSING');
    
    // Use standardized secure configuration loading
    const supabaseUrl = secureConfig.getConfigValue('SUPABASE_URL', 'supabase-client') as string;
    const supabaseAnonKey = secureConfig.getConfigValue('SUPABASE_ANON_KEY', 'supabase-client') as string;
    
    console.log('[Supabase] Configuration check:', {
      hasUrl: !!supabaseUrl,
      hasKey: !!supabaseAnonKey,
      url: supabaseUrl?.substring(0, 30) + '...',
      encryptionAvailable: safeStorage.isEncryptionAvailable(),
      urlLength: supabaseUrl?.length || 0,
      keyLength: supabaseAnonKey?.length || 0
    });
    
    // ENHANCED DEBUG: Show actual values if missing
    if (!supabaseUrl) {
      console.log('[Supabase] MISSING SUPABASE_URL - Raw value:', supabaseUrl);
    }
    if (!supabaseAnonKey) {
      console.log('[Supabase] MISSING SUPABASE_ANON_KEY - Raw value:', supabaseAnonKey);
    }
    
    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase configuration missing. Please ensure SUPABASE_URL and SUPABASE_ANON_KEY are properly configured');
    }
    
    // Supabase client with PKCE and secure storage optimized for Electron
    supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true, // Enable session persistence
        detectSessionInUrl: false, // Critical: Manual handling for Electron
        flowType: 'pkce', // Enable PKCE flow for security
        storage: customSecureStorage, // Use our secure storage adapter
        storageKey: 'chromasync-auth-session' // Custom storage key
      }
    });
    
    console.log('[Supabase] Client created with PKCE flow and secure storage');
    
    // Set up auth state change listener
    supabaseClient.auth.onAuthStateChange((event, session) => {
      console.log('[Supabase] Auth state changed:', event, session?.user?.email);
    });
  }
  
  return supabaseClient;
}

/**
 * Ensure the Supabase client has a valid session before making authenticated calls
 * This is critical for sync operations that require RLS authentication
 * 
 * FIXED: Added proper session expiry check and retry logic
 */
export async function ensureAuthenticatedSession(): Promise<{
  session: any | null;
  error: string | null;
}> {
  const client = getSupabaseClient();
  
  try {
    const isDebugAuth = process.env.DEBUG_AUTH === 'true';
    const verboseLogging = process.env.VERBOSE_SYNC_LOGGING === 'true';
    
    if (verboseLogging) {
      console.log('[Supabase] 🔍 Starting comprehensive session validation...');
      console.log('[Supabase] Debug flags:', { 
        DEBUG_AUTH: isDebugAuth, 
        VERBOSE_SYNC_LOGGING: verboseLogging,
        SYNC_ERROR_ELEVATION: process.env.SYNC_ERROR_ELEVATION === 'true'
      });
    } else {
      console.log('[Supabase] 🔍 Checking for authenticated session...');
    }
    
    // First, try to get the current session
    const { data: { session }, error: sessionError } = await client.auth.getSession();
    
    console.log('[Supabase] Current session check:', {
      hasSession: !!session,
      sessionError: sessionError?.message,
      userEmail: session?.user?.email,
      userId: session?.user?.id,
      accessToken: session?.access_token ? `${session.access_token.substring(0, 20)}...` : 'missing',
      refreshToken: session?.refresh_token ? 'present' : 'missing',
      expiresAt: session?.expires_at,
      expiresIn: session?.expires_at ? Math.floor((session.expires_at * 1000 - Date.now()) / 1000) : null
    });
    
    // DEBUG: Check if the client is using the session correctly
    if (session?.access_token) {
      console.log('[Supabase] 🔑 Session has JWT access token for authenticated requests');
      // Set the auth header explicitly
      client.auth.setSession({
        access_token: session.access_token,
        refresh_token: session.refresh_token || ''
      });
    }
    
    // CRITICAL FIX: Check if session is expired or about to expire (within 5 minutes)
    const now = Math.floor(Date.now() / 1000);
    const expiryBuffer = 300; // 5 minutes buffer
    const isExpired = session?.expires_at && (session.expires_at - now) < expiryBuffer;
    
    if (session && !sessionError && !isExpired) {
      console.log('[Supabase] ✅ Valid session found:', session.user?.email);
      
      // Enhanced debugging for RLS policy validation
      if (process.env.DEBUG_RLS_POLICIES === 'true') {
        console.log('[Supabase] 🛡️ RLS Debug Info:', {
          userId: session.user?.id,
          userRole: session.user?.role,
          tokenType: session.token_type,
          timeToExpiry: session.expires_at ? (session.expires_at - now) : 'unknown',
          jwtClaims: session.access_token ? 'JWT token present for RLS' : 'NO JWT TOKEN'
        });
        
        if (verboseLogging) {
          console.log('[Supabase] 🔍 Session will be used for RLS policy enforcement in Supabase queries');
        }
      }
      
      return { session, error: null };
    }
    
    if (isExpired) {
      console.log('[Supabase] ⏰ Session expires soon, proactively refreshing...');
    }
    
    // If no session, expired, or error, try to refresh
    console.log('[Supabase] 🔄 Attempting session refresh...');
    const { data: refreshData, error: refreshError } = await client.auth.refreshSession();
    
    console.log('[Supabase] Refresh attempt result:', {
      hasRefreshData: !!refreshData?.session,
      refreshError: refreshError?.message,
      userEmail: refreshData?.session?.user?.email
    });
    
    if (refreshData?.session && !refreshError) {
      console.log('[Supabase] ✅ Session refreshed successfully:', refreshData.session.user?.email);
      return { session: refreshData.session, error: null };
    }
    
    // If refresh failed, return the error with specific context
    const errorMsg = refreshError?.message || sessionError?.message || 'No session available';
    console.warn('[Supabase] ❌ Session validation failed:', errorMsg);
    
    // ENHANCEMENT: Return specific error context for better debugging
    return { 
      session: null, 
      error: `Authentication failed: ${errorMsg}. Please re-authenticate to continue syncing.`
    };
    
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : 'Session validation error';
    console.error('[Supabase] ❌ Error ensuring authenticated session:', errorMsg);
    return { 
      session: null, 
      error: `Session validation error: ${errorMsg}. Please restart the application.`
    };
  }
}

export function closeSupabaseClient(): void {
  if (supabaseClient) {
    // Clean up any subscriptions
    supabaseClient.removeAllChannels();
    supabaseClient = null;
  }
}
