/**
 * @file OrganizationSwitcher.tsx
 * @description Dropdown component for switching between organizations
 */

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useOrganizationStoreWithAliases } from '../../store/organization.store';
import { Building2, ChevronDown, Plus, Settings, Mail } from 'lucide-react';
import { CreateOrganizationModal } from './CreateOrganizationModal';
import { showAcceptInvitationModal } from './AcceptInvitation';
import { useSettingsModal } from '../../hooks/useModals';

export const OrganizationSwitcher: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number } | null>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const { currentOrg, organizations, switchOrganization } = useOrganizationStoreWithAliases();

  // Calculate dropdown position
  const calculateDropdownPosition = () => {
    if (!buttonRef.current) {return null;}
    
    const rect = buttonRef.current.getBoundingClientRect();
    return {
      top: rect.bottom + 8, // 8px gap below button
      left: rect.left // Align left edge
    };
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current && 
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
    
    return undefined;
  }, [isOpen]);

  // Update position on window resize
  useEffect(() => {
    const handleResize = () => {
      if (isOpen) {
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
      }
    };

    if (isOpen) {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
    
    return undefined;
  }, [isOpen]);

  const handleSwitch = async (orgId: string) => {
    if (orgId === currentOrg?.external_id) {
      setIsOpen(false);
      return;
    }

    const result = await switchOrganization(orgId);
    if (result.success) {
      setIsOpen(false);
    }
  };

  const handleCreateNew = () => {
    setIsOpen(false);
    setShowCreateModal(true);
  };

  const settingsModal = useSettingsModal();
  
  const handleManageTeam = () => {
    setIsOpen(false);
    // Open settings modal with team tab selected
    settingsModal.openWithTab('team');
  };

  const handleToggleDropdown = () => {
    if (!isOpen) {
      const position = calculateDropdownPosition();
      setDropdownPosition(position);
    }
    setIsOpen(!isOpen);
  };

  // Show a "Select Organization" button if no org is selected
  if (!currentOrg) {
    return (
      <div className="relative">
        <button
          ref={buttonRef}
          onClick={handleToggleDropdown}
          className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-[var(--color-ui-background-hover)] transition-colors text-red-600"
        >
          <Building2 className="w-4 h-4" />
          <span className="text-sm font-medium">Select Organization</span>
          <ChevronDown className={`w-4 h-4 transition-transform ${
            isOpen ? 'transform rotate-180' : ''
          }`} />
        </button>

        {isOpen && dropdownPosition && createPortal(
          <div 
            ref={dropdownRef}
            className="fixed w-64 shadow-lg border"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              backgroundColor: 'var(--color-ui-background-primary)',
              borderColor: 'var(--color-ui-border-light)',
              borderRadius: 'var(--radius-lg)',
              boxShadow: 'var(--shadow-lg)',
              zIndex: 'var(--z-tooltip)'
            }}
          >
            {/* Organizations List */}
            {organizations.length > 0 ? (
              <div className="max-h-64 overflow-y-auto">
                {organizations.map((org) => (
                  <button
                    key={org.external_id}
                    onClick={() => handleSwitch(org.external_id)}
                    className="w-full text-left px-4 py-2 hover:bg-[var(--color-ui-background-hover)] flex items-center justify-between group"
                  >
                    <span className="text-sm text-[var(--color-ui-foreground-secondary)] group-hover:text-[var(--color-ui-foreground-primary)]">
                      {org.name}
                    </span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getPlanBadge(org.plan)}`}>
                      {org.plan}
                    </span>
                  </button>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-sm text-[var(--color-ui-foreground-tertiary)]">
                No organizations found
              </div>
            )}

            {/* Actions */}
            <div className="border-t border-[var(--color-ui-border-light)] p-2">
              <button
                onClick={handleCreateNew}
                className="w-full text-left px-3 py-2 hover:bg-[var(--color-ui-background-hover)] rounded flex items-center space-x-2 text-sm"
              >
                <Plus className="w-4 h-4 text-[var(--color-ui-foreground-tertiary)]" />
                <span>Create New Workspace</span>
              </button>
            </div>
          </div>,
          document.body
        )}

        {showCreateModal && (
          <CreateOrganizationModal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
          />
        )}
      </div>
    );
  }

  const getPlanBadge = (plan: string) => {
    const badges = {
      free: 'bg-[var(--color-ui-background-secondary)] text-[var(--color-ui-foreground-secondary)]',
      team: 'bg-blue-100 text-blue-600',
      enterprise: 'bg-purple-100 text-purple-600'
    };
    return badges[plan as keyof typeof badges] || badges.free;
  };

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={handleToggleDropdown}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-[var(--color-ui-background-hover)] transition-colors"
      >
        <Building2 className="w-4 h-4 text-[var(--color-ui-foreground-secondary)]" />
        <span className="text-sm font-medium text-[var(--color-ui-foreground-primary)] max-w-[150px] truncate">
          {currentOrg.name}
        </span>
        <ChevronDown className={`w-4 h-4 text-[var(--color-ui-foreground-tertiary)] transition-transform ${
          isOpen ? 'transform rotate-180' : ''
        }`} />
      </button>

      {isOpen && dropdownPosition && createPortal(
        <div 
          ref={dropdownRef}
          className="fixed w-64 shadow-lg border"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            backgroundColor: 'var(--color-ui-background-primary)',
            borderColor: 'var(--color-ui-border-light)',
            borderRadius: 'var(--radius-lg)',
            boxShadow: 'var(--shadow-lg)',
            zIndex: 'var(--z-tooltip)'
          }}
        >
          {/* Current Organization */}
          <div className="p-3 border-b border-[var(--color-ui-border-light)]">
            <div className="text-xs text-[var(--color-ui-foreground-tertiary)] uppercase tracking-wider mb-1">
              Current Workspace
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium text-[var(--color-ui-foreground-primary)]">{currentOrg.name}</span>
              <span className={`text-xs px-2 py-0.5 rounded-full ${getPlanBadge(currentOrg.plan)}`}>
                {currentOrg.plan}
              </span>
            </div>
          </div>

          {/* Organizations List */}
          {organizations.length > 1 && (
            <div className="py-2 border-b border-[var(--color-ui-border-light)]">
              <div className="px-3 text-xs text-[var(--color-ui-foreground-tertiary)] uppercase tracking-wider mb-2">
                Switch Workspace
              </div>
              {organizations
                .filter(org => org.external_id !== currentOrg.external_id)
                .map(org => (
                  <button
                    key={org.external_id}
                    onClick={() => handleSwitch(org.external_id)}
                    className="w-full px-3 py-2 text-left hover:bg-[var(--color-ui-background-hover)] transition-colors flex items-center justify-between group"
                  >
                    <span className="text-sm text-[var(--color-ui-foreground-secondary)] group-hover:text-[var(--color-ui-foreground-primary)]">
                      {org.name}
                    </span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getPlanBadge(org.plan)}`}>
                      {org.plan}
                    </span>
                  </button>
                ))}
            </div>
          )}

          {/* Actions */}
          <div className="py-2">
            <button
              onClick={handleManageTeam}
              className="w-full px-3 py-2 text-left hover:bg-[var(--color-ui-background-hover)] transition-colors flex items-center space-x-2 text-sm text-[var(--color-ui-foreground-secondary)] hover:text-[var(--color-ui-foreground-primary)]"
            >
              <Settings className="w-4 h-4" />
              <span>Manage Team</span>
            </button>
            <button
              onClick={handleCreateNew}
              className="w-full px-3 py-2 text-left hover:bg-[var(--color-ui-background-hover)] transition-colors flex items-center space-x-2 text-sm text-[var(--color-ui-foreground-secondary)] hover:text-[var(--color-ui-foreground-primary)]"
            >
              <Plus className="w-4 h-4" />
              <span>Create New Workspace</span>
            </button>
            <button
              onClick={() => {
                setIsOpen(false);
                showAcceptInvitationModal();
              }}
              className="w-full px-3 py-2 text-left hover:bg-[var(--color-ui-background-hover)] transition-colors flex items-center space-x-2 text-sm text-[var(--color-ui-foreground-secondary)] hover:text-[var(--color-ui-foreground-primary)]"
            >
              <Mail className="w-4 h-4" />
              <span>Join by Invitation</span>
            </button>
          </div>
        </div>,
        document.body
      )}
      
      {/* Create Organization Modal */}
      <CreateOrganizationModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={(org) => {
          // The organization is already set as current in the modal
          console.log('New organization created:', org.name);
        }}
      />
    </div>
  );
};
