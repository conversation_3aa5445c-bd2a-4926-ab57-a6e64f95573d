/**
 * Color Formatting Utilities
 * Functions for parsing, formatting, and standardizing color values
 */

import { CMYK, RGB, HSL } from './types';
import { isValidHex, isValidCMYK } from './validation';

/**
 * HEX Color Formatting
 */

// Format hex color (ensure # prefix and uppercase)
export function formatHex(hex: string): string {
  if (!hex) {return '';}
  
  hex = hex.trim().replace(/^#/, '');
  
  // Expand 3-digit hex to 6-digit
  if (hex.length === 3) {
    hex = hex.split('').map(c => c + c).join('');
  }
  
  return '#' + hex.toUpperCase();
}

// Standardize hex to 6-digit format with # prefix
export function standardizeHex(hex: string): string {
  if (!hex) {return '';}
  
  hex = hex.trim();
  const formattedHex = hex.startsWith('#') ? hex : `#${hex}`;
  
  // Expand 3-digit hex to 6-digit
  if (formattedHex.length === 4) {
    const r = formattedHex.charAt(1);
    const g = formattedHex.charAt(2);
    const b = formattedHex.charAt(3);
    return `#${r}${r}${g}${g}${b}${b}`.toUpperCase();
  }
  
  if (formattedHex.length !== 7) {
    throw new Error(`Invalid hex code length: ${formattedHex} (expected 7 characters)`);
  }
  
  return formattedHex.toUpperCase();
}

// Attempts to fix common issues with HEX codes
export function fixCommonHexIssues(hex: string): string {
  if (!hex) {return '';}
  
  let fixedHex = hex.trim();
  
  // Add # if missing
  if (!fixedHex.startsWith('#')) {
    fixedHex = `#${fixedHex}`;
  }
  
  // Expand 3-digit hex
  if (fixedHex.length === 4 && /^#[0-9A-Fa-f]{3}$/.test(fixedHex)) {
    const r = fixedHex.charAt(1);
    const g = fixedHex.charAt(2);
    const b = fixedHex.charAt(3);
    fixedHex = `#${r}${r}${g}${g}${b}${b}`;
  }
  
  // Standardize case
  fixedHex = fixedHex.toUpperCase();
  
  // Return original if fix didn't work
  return isValidHex(fixedHex) ? fixedHex : hex;
}

/**
 * CMYK Color Formatting
 */

// Parse CMYK string to object
// Supports formats: "C:0 M:0 Y:0 K:0", "0,0,0,0", "C0 M0 Y0 K0", "0 0 0 0", "0" (single value treated as K)
// Returns default values for empty input
export function parseCMYK(cmyk: string): CMYK {
  if (!cmyk || cmyk.trim() === '') {
    return { c: 0, m: 0, y: 0, k: 0 }; // Return default instead of throwing
  }
  
  cmyk = cmyk.trim();
  
  // Handle N/A values by returning default CMYK
  if (cmyk.toLowerCase() === 'n/a' || cmyk.toLowerCase() === 'na') {
    return { c: 0, m: 0, y: 0, k: 0 };
  }
  
  // Handle case where values contain "null"
  if (cmyk.includes('null')) {
    console.warn('CMYK contains null values, using default: 0,0,0,0');
    return { c: 0, m: 0, y: 0, k: 0 };
  }
  
  // Try format "C:0 M:0 Y:0 K:0" or "C0 M0 Y0 K0"
  let match = cmyk.match(/[Cc]:?\s*(\d+(?:\.\d+)?)\s*[Mm]:?\s*(\d+(?:\.\d+)?)\s*[Yy]:?\s*(\d+(?:\.\d+)?)\s*[Kk]:?\s*(\d+(?:\.\d+)?)/);
  if (match && match[1] && match[2] && match[3] && match[4]) {
    return {
      c: Math.round(parseFloat(match[1])),
      m: Math.round(parseFloat(match[2])),
      y: Math.round(parseFloat(match[3])),
      k: Math.round(parseFloat(match[4]))
    };
  }
  
  // Try format "0,0,0,0" or "0 0 0 0"
  match = cmyk.match(/(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)/);
  if (match && match[1] && match[2] && match[3] && match[4]) {
    return {
      c: Math.round(parseFloat(match[1])),
      m: Math.round(parseFloat(match[2])),
      y: Math.round(parseFloat(match[3])),
      k: Math.round(parseFloat(match[4]))
    };
  }
  
  // Try single value - treat as black (K) component with C/M/Y as 0
  match = cmyk.match(/^(\d+(?:\.\d+)?)$/);
  if (match && match[1]) {
    const value = Math.round(parseFloat(match[1]));
    return {
      c: 0,
      m: 0,
      y: 0,
      k: value
    };
  }
  
  throw new Error(`Cannot parse CMYK value: ${cmyk}`);
}

// Format CMYK for backend/database storage
export function formatCMYKForBackend(cmyk: CMYK): string {
  return `C:${cmyk.c} M:${cmyk.m} Y:${cmyk.y} K:${cmyk.k}`;
}

// Format CMYK for display (industry standard format)
export function formatCMYKForDisplay(cmyk: CMYK): string {
  return `C:${cmyk.c} M:${cmyk.m} Y:${cmyk.y} K:${cmyk.k}`;
}

// Standardize CMYK string to backend format
export function standardizeCMYK(cmyk: string): string {
  if (!cmyk) {return '';}
  
  // Handle N/A values gracefully
  if (cmyk.trim().toLowerCase() === 'n/a' || cmyk.trim().toLowerCase() === 'na') {
    return 'C:0 M:0 Y:0 K:0';
  }
  
  try {
    const values = parseCMYK(cmyk);
    return formatCMYKForBackend(values);
  } catch (error) {
    console.warn('Error standardizing CMYK value:', error);
    return cmyk;
  }
}

// Attempts to fix common CMYK format issues
export function fixCommonCMYKIssues(cmyk: string): string {
  if (!cmyk) {return '';}
  
  const standardized = standardizeCMYK(cmyk);
  
  if (isValidCMYK(standardized)) {
    return standardized;
  }
  
  return cmyk;
}

/**
 * RGB Color Formatting
 */

// Format RGB to string
export function formatRGB(rgb: RGB): string {
  return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`;
}

// Format RGB for display (industry standard format)
export function formatRGBForDisplay(rgb: RGB): string {
  return `R:${rgb.r} G:${rgb.g} B:${rgb.b}`;
}

// Format RGB to hex string
export function rgbToHexString(rgb: RGB): string {
  const toHex = (n: number) => {
    const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`.toUpperCase();
}

// Parse RGB string to object
export function parseRGB(rgb: string): RGB {
  const match = rgb.match(/rgba?\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)/);
  if (!match) {
    throw new Error(`Cannot parse RGB value: ${rgb}`);
  }
  
  return {
    r: parseInt(match[1] || '0', 10),
    g: parseInt(match[2] || '0', 10),
    b: parseInt(match[3] || '0', 10)
  };
}

/**
 * HSL Color Formatting
 */

// Format HSL to string
export function formatHSL(hsl: HSL): string {
  return `hsl(${Math.round(hsl.h)}, ${Math.round(hsl.s)}%, ${Math.round(hsl.l)}%)`;
}

// Parse HSL string to object
export function parseHSL(hsl: string): HSL {
  const match = hsl.match(/hsla?\s*\(\s*(\d+)\s*,\s*(\d+)%?\s*,\s*(\d+)%?/);
  if (!match) {
    throw new Error(`Cannot parse HSL value: ${hsl}`);
  }
  
  return {
    h: parseInt(match[1] || '0', 10),
    s: parseInt(match[2] || '0', 10),
    l: parseInt(match[3] || '0', 10)
  };
}

/**
 * Pantone Color Formatting
 */

// Standardize Pantone code formatting
export function standardizeColorCode(code: string): string {
  if (!code) {return '';}
  
  code = code.trim();
  
  // Remove leading 'P' or 'PMS' prefix
  const standardized = code.replace(/^(PMS\s*|P\s*)/i, '');
  
  // Extract number and suffix
  const matches = standardized.match(/(\d+)\s*([A-Za-z]+)?/);
  if (!matches) {return code;}
  
  const numbers = matches[1] || '';
  const suffix = matches[2] || '';
  
  // Format as "number suffix" (e.g., "186 C")
  if (suffix) {
    return `${numbers} ${suffix.trim().toUpperCase()}`;
  }
  
  return numbers;
}

// Format color code for display (with PMS prefix for Pantone codes)
export function formatCodeForDisplay(code: string): string {
  const standardized = standardizeColorCode(code);
  // Add PMS prefix if it looks like a Pantone code (numeric with optional suffix)
  if (/^\d+\s*[A-Za-z]*$/.test(standardized)) {
    return `PMS ${standardized}`;
  }
  return standardized;
}

/**
 * RAL Color Formatting
 */

// Standardize RAL code formatting
export function standardizeRALCode(ralCode: string): string {
  if (!ralCode) {return '';}
  
  ralCode = ralCode.trim();
  
  // Remove 'RAL' prefix if present
  const standardized = ralCode.replace(/^RAL\s*/i, '');
  
  // Ensure it's 4 digits
  const match = standardized.match(/(\d{4})/);
  if (!match) {return ralCode;}
  
  return match[1] || ralCode;
}

// Format RAL for display (with RAL prefix)
export function formatRALForDisplay(ralCode: string): string {
  const standardized = standardizeRALCode(ralCode);
  return `RAL ${standardized}`;
}

/**
 * General Color Formatting
 */

// Format color value based on type
export function formatColorValue(value: string, type: 'hex' | 'cmyk' | 'rgb' | 'hsl' | 'pantone' | 'ral'): string {
  switch (type) {
    case 'hex':
      return formatHex(value);
    case 'cmyk':
      return standardizeCMYK(value);
    case 'rgb':
      return value; // Already formatted
    case 'hsl':
      return value; // Already formatted
    case 'pantone':
      return formatCodeForDisplay(value);
    case 'ral':
      return formatRALForDisplay(value);
    default:
      return value;
  }
}