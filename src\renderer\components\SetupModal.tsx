/**
 * @file SetupModal.tsx
 * @description Modal displayed on first run to configure storage location.
 * Modified to use server-based sync by default.
 */

import React, { useState, useEffect } from 'react';
import { Cloud } from 'lucide-react';

const SetupModal: React.FC = () => {
  // Start with modal closed, will be opened based on config status
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Dev mode flag to force show the modal for testing
  const [devModeForced, setDevModeForced] = useState(false);

  useEffect(() => {
    // Add debug info in dev mode
    if (process.env.NODE_ENV === 'development') {
      console.log('SetupModal initialized in development mode');

      // Add keyboard shortcut to toggle modal in dev mode (Alt+Shift+S)
      const handleDevModeKeyPress = (e: KeyboardEvent) => {
        if (e.altKey && e.shiftKey && e.code === 'KeyS') {
          console.log(
            'DEV MODE: Toggling setup modal visibility with keyboard shortcut'
          );
          setDevModeForced(prev => !prev);
          setIsOpen(prev => !prev);
        }
      };

      window.addEventListener('keydown', handleDevModeKeyPress);
      return () => window.removeEventListener('keydown', handleDevModeKeyPress);
    }

    return undefined;
  }, []);

  useEffect(() => {
    const cleanup = window.setupAPI.onShowSetupModal(() => {
      console.log('Received show-setup-modal signal');
      setIsOpen(true);
    });

    // REMOVED: Initial config check - this is now handled by the main process
    // which sends the 'show-setup-modal' signal if needed.

    return cleanup;
  }, []);

  // No longer need mode selection handlers as we're using server-based sync by default

  const handleConfirm = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Save configuration with server-based sync mode
      const result = await window.setupAPI.saveStorageConfig({
        mode: 'server-sync', // New mode for server-based sync
      });

      if (result.success) {
        console.log('Configuration saved successfully.');
        setIsOpen(false);
        setDevModeForced(false); // Also disable dev mode forcing when config is saved
        window.setupAPI.sendSetupComplete();
      } else {
        throw new Error(result.error || 'Failed to save configuration.');
      }
    } catch (err) {
      console.error('Error saving configuration:', err);
      setError(
        `Failed to save configuration: ${err instanceof Error ? err.message : 'Unknown error'}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Dev mode close button handler
  const handleDevClose = () => {
    console.log('DEV MODE: Manually closing setup modal');
    setIsOpen(false);
    setDevModeForced(false);
  };

  if (!isOpen && !devModeForced) {
    return null;
  }

  const modalOverlayClasses =
    'fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-[9999]';
  const modalContentClasses =
    'bg-ui-background-primary rounded-[var(--radius-lg)] shadow-[var(--shadow-xl)] w-full max-w-lg overflow-hidden';
  const confirmButtonClasses = `mt-[var(--spacing-4)] w-full py-[var(--spacing-2)] px-[var(--spacing-4)] rounded-[var(--radius-md)] text-white font-medium transition-colors ${isLoading ? 'bg-gray-400 cursor-not-allowed' : 'bg-brand-primary hover:bg-brand-secondary'}`;

  return (
    <div
      className={modalOverlayClasses}
      role='dialog'
      aria-modal='true'
      aria-labelledby='setup-modal-title'
    >
      <div className={modalContentClasses}>
        {process.env.NODE_ENV === 'development' &&
          (devModeForced || isOpen) && (
            <div className='bg-yellow-100 text-yellow-800 p-2 text-xs text-center'>
              {devModeForced
                ? 'Dev Mode: Modal manually triggered'
                : 'Dev Mode: Modal shown due to configuration'}
              <button
                onClick={handleDevClose}
                className='ml-2 px-2 py-1 bg-yellow-200 hover:bg-yellow-300 rounded text-xs'
              >
                Close
              </button>
              <div className='text-[10px] mt-1'>
                Press Alt+Shift+S to toggle modal visibility for testing
              </div>
            </div>
          )}
        <div className='p-[var(--spacing-6)]'>
          <h2
            id='setup-modal-title'
            className='text-xl font-semibold text-ui-foreground-primary mb-[var(--spacing-4)]'
          >
            Welcome to ChromaSync!
          </h2>

          <div className='flex flex-col items-center justify-center mb-[var(--spacing-6)]'>
            <Cloud
              size={64}
              className='text-brand-primary mb-[var(--spacing-4)]'
            />

            <p className='text-center text-ui-foreground-secondary mb-[var(--spacing-2)]'>
              ChromaSync uses automatic cloud synchronization to keep your data
              in sync across all your devices.
            </p>

            <p className='text-center text-ui-foreground-secondary'>
              Click "Get Started" below to begin using the application with
              automatic sync enabled.
            </p>
          </div>

          {error && (
            <p className='text-sm text-feedback-error mb-[var(--spacing-4)]'>
              {error}
            </p>
          )}

          <button
            className={confirmButtonClasses}
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading ? 'Setting up...' : 'Get Started'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SetupModal;
