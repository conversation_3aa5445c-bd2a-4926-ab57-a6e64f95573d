/**
 * @file sync-ipc-integration.test.ts
 * @description Integration test for sync IPC communication functionality
 * Tests that all sync IPC handlers are properly registered and communication works
 */

import { ipcMain } from 'electron';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type { SyncOutboxService } from '../../services/sync/sync-outbox.service';
import type { SyncStatusManagerService } from '../../services/sync/sync-status-manager.service';
import type { UnifiedSyncManager } from '../../services/sync/unified-sync-manager';
import { SyncHandlersThin } from '../sync-handlers-thin';

// Mock electron
vi.mock('electron', () => ({
  ipcMain: {
    handle: vi.fn(),
    removeHandler: vi.fn(),
  },
}));

// Mock dependencies
vi.mock('../../utils/ipcRegistry', () => ({
  registerHandlerSafely: vi.fn((ipcMain, channel, handler) => {
    ipcMain.handle(channel, handler);
  }),
}));

vi.mock('../../utils/organization-context', () => ({
  getCurrentOrganization: vi.fn(() => 'test-org-id'),
}));

vi.mock('../../services/service-locator', () => ({
  ServiceLocator: {
    getCircuitBreakerAuthManager: vi.fn(() => ({
      getHealthStatus: vi.fn(() => ({
        isHealthy: true,
        circuitBreakerOpen: false,
        sessionValid: true,
        networkConnected: true,
        issues: [],
      })),
      getCurrentUser: vi.fn(() => Promise.resolve({ id: 'test-user-id', email: '<EMAIL>' })),
      getCurrentSession: vi.fn(() => Promise.resolve({ access_token: 'test-token' })),
    })),
  },
}));

describe('Sync IPC Integration', () => {
  let syncHandlersThin: SyncHandlersThin;
  let mockUnifiedSyncManager: UnifiedSyncManager;
  let mockSyncOutboxService: SyncOutboxService;
  let mockSyncStatusManager: SyncStatusManagerService;
  let registeredHandlers: Map<string, Function>;

  beforeEach(() => {
    // Create mock services
    mockUnifiedSyncManager = {
      isReady: vi.fn(() => true),
      sync: vi.fn(() => Promise.resolve({
        success: true,
        itemsProcessed: 10,
        duration: 1000,
        operation: 'full-sync',
      })),
      getStatus: vi.fn(() => ({
        isRunning: false,
        lastSyncTime: Date.now(),
        config: {
          autoSyncEnabled: true,
          autoSyncInterval: 5,
          realtimeEnabled: false,
          maxRetries: 3,
        },
      })),
      updateConfig: vi.fn(),
      initialize: vi.fn(() => Promise.resolve()),
      stop: vi.fn(),
    } as any;

    mockSyncOutboxService = {
      getPendingChanges: vi.fn(() => [
        { id: '1', operation: 'create', table: 'colors', data: {} },
        { id: '2', operation: 'update', table: 'products', data: {} },
      ]),
      clearAll: vi.fn(),
      clearNotFoundDeleteOperations: vi.fn(() => 3),
    } as any;

    mockSyncStatusManager = {
      getProgress: vi.fn(() => Promise.resolve({ 
        progress: 75, 
        currentOperation: 'syncing colors',
        itemsProcessed: 150,
        totalItems: 200
      })),
      getMetrics: vi.fn(() => Promise.resolve({ 
        totalSyncs: 42,
        successfulSyncs: 40,
        failedSyncs: 2,
        averageDuration: 2500
      })),
      getQueueStats: vi.fn(() => Promise.resolve({ 
        queueSize: 5,
        processingItems: 2,
        failedItems: 1
      })),
      getStatusReport: vi.fn(() => ({ 
        status: 'syncing',
        lastSync: new Date().toISOString(),
        nextSync: new Date(Date.now() + 300000).toISOString(),
        health: 'good'
      })),
      hasUnsyncedLocalChanges: vi.fn(() => Promise.resolve(true)),
      reset: vi.fn(),
      updateUserActivity: vi.fn(),
    } as any;

    syncHandlersThin = new SyncHandlersThin(
      mockUnifiedSyncManager,
      mockSyncOutboxService,
      mockSyncStatusManager
    );

    // Track registered handlers
    registeredHandlers = new Map();
    vi.mocked(ipcMain.handle).mockImplementation((channel: string, handler: Function) => {
      registeredHandlers.set(channel, handler);
    });

    // Clear all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
    registeredHandlers.clear();
  });

  describe('Handler Registration', () => {
    it('should register all expected sync IPC handlers', () => {
      syncHandlersThin.registerHandlers();

      const expectedChannels = [
        'sync:execute',
        'sync:execute-type',
        'sync:get-status',
        'sync:get-config',
        'sync:update-config',
        'sync:get-progress',
        'sync:get-metrics',
        'sync:get-queue-stats',
        'sync:get-status-report',
        'sync:has-unsynced-changes',
        'sync:get-outbox-status',
        'sync:clear-outbox',
        'sync:clear-failed-operations',
        'sync:clear-orphaned-operations',
        'sync:initialize',
        'sync:stop',
        'sync:reset',
        'sync:update-user-activity',
      ];

      expect(ipcMain.handle).toHaveBeenCalledTimes(expectedChannels.length);
      
      expectedChannels.forEach(channel => {
        expect(registeredHandlers.has(channel)).toBe(true);
        expect(typeof registeredHandlers.get(channel)).toBe('function');
      });
    });
  });

  describe('Renderer-Main Communication', () => {
    beforeEach(() => {
      syncHandlersThin.registerHandlers();
    });

    it('should handle sync execution requests from renderer', async () => {
      const handler = registeredHandlers.get('sync:execute');
      expect(handler).toBeDefined();

      const response = await handler!();

      expect(response).toMatchObject({
        success: true,
        data: {
          itemsProcessed: 10,
          duration: 1000,
          operation: 'full-sync',
        },
        userMessage: 'Sync completed successfully',
        timestamp: expect.any(Number),
      });

      expect(mockUnifiedSyncManager.sync).toHaveBeenCalledWith('full', 'bidirectional', 'normal');
    });

    it('should handle sync type execution requests from renderer', async () => {
      const handler = registeredHandlers.get('sync:execute-type');
      expect(handler).toBeDefined();

      const response = await handler!(null, 'colors', 'push');

      expect(response).toMatchObject({
        success: true,
        data: {
          itemsProcessed: 10,
          duration: 1000,
          operation: 'full-sync',
        },
        userMessage: 'colors sync completed successfully',
        timestamp: expect.any(Number),
      });

      expect(mockUnifiedSyncManager.sync).toHaveBeenCalledWith('colors', 'push', 'normal');
    });

    it('should handle sync status requests from renderer', async () => {
      const handler = registeredHandlers.get('sync:get-status');
      expect(handler).toBeDefined();

      const response = await handler!();

      expect(response).toMatchObject({
        success: true,
        data: {
          isRunning: false,
          lastSyncTime: expect.any(Number),
          config: expect.any(Object),
        },
        timestamp: expect.any(Number),
      });

      expect(mockUnifiedSyncManager.getStatus).toHaveBeenCalled();
    });

    it('should handle sync progress requests from renderer', async () => {
      const handler = registeredHandlers.get('sync:get-progress');
      expect(handler).toBeDefined();

      const response = await handler!();

      expect(response).toMatchObject({
        success: true,
        data: {
          progress: 75,
          currentOperation: 'syncing colors',
          itemsProcessed: 150,
          totalItems: 200,
        },
        timestamp: expect.any(Number),
      });

      expect(mockSyncStatusManager.getProgress).toHaveBeenCalled();
    });

    it('should handle sync metrics requests from renderer', async () => {
      const handler = registeredHandlers.get('sync:get-metrics');
      expect(handler).toBeDefined();

      const response = await handler!();

      expect(response).toMatchObject({
        success: true,
        data: {
          totalSyncs: 42,
          successfulSyncs: 40,
          failedSyncs: 2,
          averageDuration: 2500,
        },
        timestamp: expect.any(Number),
      });

      expect(mockSyncStatusManager.getMetrics).toHaveBeenCalled();
    });

    it('should handle outbox status requests from renderer', async () => {
      const handler = registeredHandlers.get('sync:get-outbox-status');
      expect(handler).toBeDefined();

      const response = await handler!();

      expect(response).toMatchObject({
        success: true,
        data: {
          pendingChanges: expect.arrayContaining([
            expect.objectContaining({ id: '1', operation: 'create' }),
            expect.objectContaining({ id: '2', operation: 'update' }),
          ]),
          count: 2,
          hasChanges: true,
        },
        timestamp: expect.any(Number),
      });

      expect(mockSyncOutboxService.getPendingChanges).toHaveBeenCalled();
    });

    it('should handle config update requests from renderer', async () => {
      const handler = registeredHandlers.get('sync:update-config');
      expect(handler).toBeDefined();

      const newConfig = {
        autoSyncEnabled: false,
        autoSyncInterval: 600000, // 10 minutes in milliseconds
        realtimeEnabled: true,
        maxRetries: 5,
      };

      const response = await handler!(null, newConfig);

      expect(response).toMatchObject({
        success: true,
        data: expect.any(Object),
        userMessage: 'Sync configuration updated successfully',
        timestamp: expect.any(Number),
      });

      // Verify config was converted from milliseconds to minutes
      expect(mockUnifiedSyncManager.updateConfig).toHaveBeenCalledWith({
        autoSyncEnabled: false,
        autoSyncInterval: 10, // Converted to minutes
        realtimeEnabled: true,
        maxRetries: 5,
      });
    });
  });

  describe('Sync Status Updates', () => {
    beforeEach(() => {
      syncHandlersThin.registerHandlers();
    });

    it('should provide real-time sync status updates to renderer', async () => {
      const statusHandler = registeredHandlers.get('sync:get-status-report');
      expect(statusHandler).toBeDefined();

      const response = await statusHandler!();

      expect(response).toMatchObject({
        success: true,
        data: {
          status: 'syncing',
          lastSync: expect.any(String),
          nextSync: expect.any(String),
          health: 'good',
        },
        timestamp: expect.any(Number),
      });

      expect(mockSyncStatusManager.getStatusReport).toHaveBeenCalled();
    });

    it('should handle unsynced changes detection for renderer', async () => {
      const handler = registeredHandlers.get('sync:has-unsynced-changes');
      expect(handler).toBeDefined();

      const response = await handler!();

      expect(response).toMatchObject({
        success: true,
        data: true,
        timestamp: expect.any(Number),
      });

      expect(mockSyncStatusManager.hasUnsyncedLocalChanges).toHaveBeenCalled();
    });

    it('should handle queue statistics requests for renderer', async () => {
      const handler = registeredHandlers.get('sync:get-queue-stats');
      expect(handler).toBeDefined();

      const response = await handler!();

      expect(response).toMatchObject({
        success: true,
        data: {
          queueSize: 5,
          processingItems: 2,
          failedItems: 1,
        },
        timestamp: expect.any(Number),
      });

      expect(mockSyncStatusManager.getQueueStats).toHaveBeenCalled();
    });
  });

  describe('Error Handling in IPC Communication', () => {
    beforeEach(() => {
      syncHandlersThin.registerHandlers();
    });

    it('should handle sync failures gracefully and return proper error response', async () => {
      // Mock sync failure
      mockUnifiedSyncManager.sync = vi.fn(() => Promise.resolve({
        success: false,
        errors: ['Network connection failed'],
      }));

      const handler = registeredHandlers.get('sync:execute');
      const response = await handler!();

      expect(response).toMatchObject({
        success: false,
        error: 'Network connection failed',
        userMessage: 'Sync operation failed',
        timestamp: expect.any(Number),
      });
    });

    it('should handle service exceptions and return standardized error response', async () => {
      // Mock service exception
      mockSyncStatusManager.getProgress = vi.fn(() => Promise.reject(new Error('Service unavailable')));

      const handler = registeredHandlers.get('sync:get-progress');
      const response = await handler!();

      expect(response).toMatchObject({
        success: false,
        error: 'Service unavailable',
        timestamp: expect.any(Number),
      });
    });
  });
});