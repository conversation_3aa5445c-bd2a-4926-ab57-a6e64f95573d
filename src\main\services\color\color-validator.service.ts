/**
 * @file color-validator.service.ts
 * @description Service for comprehensive color validation, standardization, and business rules
 * 
 * This service consolidates all color validation logic previously scattered across
 * the application into a single, well-tested service following single responsibility principle.
 * 
 * Features:
 * - HEX color validation and standardization
 * - CMYK color validation and parsing
 * - Color name normalization
 * - Color code standardization
 * - Format detection and auto-correction
 * - Business rule validation for database and sync operations
 * - Comprehensive error reporting with actionable suggestions
 */

// Types
export interface ColorValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ColorValidationOptions {
  allowEmpty?: boolean;
  strict?: boolean;
  autoFix?: boolean;
  allowShortHex?: boolean;
}

export interface StandardizedColorData {
  hex: string;
  cmyk: string;
  name: string;
  code: string;
  errors: string[];
  warnings: string[];
}

export interface ColorFormatValidationResult {
  originalValue: string;
  isValid: boolean;
  format?: 'hex' | 'cmyk' | 'rgb' | 'hsl' | 'pantone' | 'ral';
  standardizedValue?: string;
  errors: string[];
  suggestions?: string[];
}

export interface CMYK {
  c: number;
  m: number;
  y: number;
  k: number;
}

/**
 * ColorValidator service class
 * 
 * Provides comprehensive color validation, standardization, and business rule enforcement.
 * Consolidates validation logic from shared utilities and adds new validation capabilities.
 */
export class ColorValidator {
  
  // =============================================================================
  // HEX VALIDATION METHODS
  // =============================================================================
  
  /**
   * Validates HEX color codes with configurable options
   * @param hex - HEX color code to validate
   * @param options - Validation options
   * @returns Validation result with errors and warnings
   */
  validateHex(hex: string, options: ColorValidationOptions = {}): ColorValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!hex && !options.allowEmpty) {
      errors.push('HEX color code cannot be empty');
      return { isValid: false, errors, warnings };
    }
    
    if (!hex && options.allowEmpty) {
      return { isValid: true, errors, warnings };
    }
    
    if (!hex.startsWith('#')) {
      if (options.autoFix) {
        warnings.push('Missing # prefix - will be auto-fixed');
      } else {
        errors.push('HEX color code must start with #');
      }
    }
    
    const hexValue = hex.replace('#', '');
    
    if (!options.allowShortHex && hexValue.length !== 6) {
      errors.push(`HEX color code must be 6 characters (got ${hexValue.length})`);
    } else if (options.allowShortHex && hexValue.length !== 3 && hexValue.length !== 6) {
      errors.push(`HEX color code must be 3 or 6 characters (got ${hexValue.length})`);
    }
    
    if (!/^[0-9A-Fa-f]+$/.test(hexValue)) {
      errors.push('HEX color code contains invalid characters');
    }
    
    if (options.strict && hexValue !== hexValue.toUpperCase()) {
      warnings.push('HEX color code should be uppercase for consistency');
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  /**
   * Standardizes HEX color codes to consistent format
   * @param hex - HEX color code to standardize
   * @param options - Standardization options
   * @returns Standardized HEX color code
   */
  standardizeHex(hex: string, _options: ColorValidationOptions = {}): string {
    if (!hex) return '';
    
    let standardized = hex.trim();
    
    // Add # prefix if missing
    if (!standardized.startsWith('#')) {
      standardized = `#${standardized}`;
    }
    
    // Expand 3-digit hex to 6-digit
    if (standardized.length === 4) {
      const r = standardized.charAt(1);
      const g = standardized.charAt(2);
      const b = standardized.charAt(3);
      standardized = `#${r}${r}${g}${g}${b}${b}`;
    }
    
    return standardized.toUpperCase();
  }
  
  /**
   * Attempts to fix common HEX color code issues
   * @param hex - HEX color code to fix
   * @returns Fixed HEX color code
   */
  fixHexIssues(hex: string): string {
    if (!hex) return '';
    
    let fixed = hex.trim();
    
    // Remove common prefixes
    fixed = fixed.replace(/^(0x|\\x)/i, '');
    
    // Add # if missing
    if (!fixed.startsWith('#')) {
      fixed = `#${fixed}`;
    }
    
    // Expand 3-digit hex
    if (fixed.length === 4 && /^#[0-9A-Fa-f]{3}$/.test(fixed)) {
      const r = fixed.charAt(1);
      const g = fixed.charAt(2);
      const b = fixed.charAt(3);
      fixed = `#${r}${r}${g}${g}${b}${b}`;
    }
    
    return fixed.toUpperCase();
  }
  
  // =============================================================================
  // CMYK VALIDATION METHODS
  // =============================================================================
  
  /**
   * Validates CMYK color values with range and format checking
   * @param cmyk - CMYK color value to validate
   * @param options - Validation options
   * @returns Validation result with errors and warnings
   */
  validateCMYK(cmyk: string, options: ColorValidationOptions = {}): ColorValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!cmyk && !options.allowEmpty) {
      errors.push('CMYK color value cannot be empty');
      return { isValid: false, errors, warnings };
    }
    
    if (!cmyk && options.allowEmpty) {
      return { isValid: true, errors, warnings };
    }
    
    try {
      const parsed = this.parseCMYK(cmyk);
      
      // Validate ranges
      if (parsed.c < 0 || parsed.c > 100) errors.push(`C value out of range (0-100): ${parsed.c}`);
      if (parsed.m < 0 || parsed.m > 100) errors.push(`M value out of range (0-100): ${parsed.m}`);
      if (parsed.y < 0 || parsed.y > 100) errors.push(`Y value out of range (0-100): ${parsed.y}`);
      if (parsed.k < 0 || parsed.k > 100) errors.push(`K value out of range (0-100): ${parsed.k}`);
      
      // Check for precision warnings
      if (options.strict) {
        if (parsed.c % 1 !== 0) warnings.push('C value has decimal precision - will be rounded');
        if (parsed.m % 1 !== 0) warnings.push('M value has decimal precision - will be rounded');
        if (parsed.y % 1 !== 0) warnings.push('Y value has decimal precision - will be rounded');
        if (parsed.k % 1 !== 0) warnings.push('K value has decimal precision - will be rounded');
      }
      
    } catch (error) {
      errors.push(`Cannot parse CMYK format: ${cmyk}`);
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  /**
   * Standardizes CMYK values to consistent format
   * @param cmyk - CMYK value to standardize
   * @returns Standardized CMYK string
   */
  standardizeCMYK(cmyk: string): string {
    if (!cmyk) return 'C:0 M:0 Y:0 K:0';
    
    // Handle N/A values
    if (cmyk.trim().toLowerCase() === 'n/a') {
      return 'C:0 M:0 Y:0 K:0';
    }
    
    try {
      const parsed = this.parseCMYK(cmyk);
      return `C:${Math.round(parsed.c)} M:${Math.round(parsed.m)} Y:${Math.round(parsed.y)} K:${Math.round(parsed.k)}`;
    } catch {
      return cmyk; // Return original if parsing fails
    }
  }
  
  /**
   * Parses CMYK string into component values
   * @param cmyk - CMYK string to parse
   * @returns Parsed CMYK object
   * @private
   */
  private parseCMYK(cmyk: string): CMYK {
    if (!cmyk) throw new Error('Empty CMYK value');
    
    const trimmed = cmyk.trim();
    
    // Format: "C:0 M:0 Y:0 K:0"
    let match = trimmed.match(/[Cc]:?\s*(\d+(?:\.\d+)?)\s*[Mm]:?\s*(\d+(?:\.\d+)?)\s*[Yy]:?\s*(\d+(?:\.\d+)?)\s*[Kk]:?\s*(\d+(?:\.\d+)?)/);
    if (match) {
      return {
        c: parseFloat(match[1] ?? '0'),
        m: parseFloat(match[2] ?? '0'),
        y: parseFloat(match[3] ?? '0'),
        k: parseFloat(match[4] ?? '0')
      };
    }
    
    // Format: "0,0,0,0" or "0 0 0 0"
    match = trimmed.match(/(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)[,\s]+(\d+(?:\.\d+)?)/);
    if (match) {
      return {
        c: parseFloat(match[1] ?? '0'),
        m: parseFloat(match[2] ?? '0'),
        y: parseFloat(match[3] ?? '0'),
        k: parseFloat(match[4] ?? '0')
      };
    }
    
    throw new Error(`Cannot parse CMYK format: ${cmyk}`);
  }
  
  // =============================================================================
  // COLOR NAME VALIDATION METHODS
  // =============================================================================
  
  /**
   * Validates color names with length and character restrictions
   * @param name - Color name to validate
   * @param options - Validation options
   * @returns Validation result with errors and warnings
   */
  validateColorName(name: string, options: ColorValidationOptions = {}): ColorValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!name && !options.allowEmpty) {
      errors.push('Color name cannot be empty');
      return { isValid: false, errors, warnings };
    }
    
    if (!name && options.allowEmpty) {
      return { isValid: true, errors, warnings };
    }
    
    if (name.length < 2) {
      errors.push('Color name must be at least 2 characters long');
    }
    
    if (name.length > 100) {
      errors.push('Color name cannot exceed 100 characters');
    }
    
    if (!/^[a-zA-Z0-9\s\-_()]+$/.test(name)) {
      errors.push('Color name contains invalid characters');
    }
    
    if (options.strict) {
      if (name !== this.normalizeColorName(name)) {
        warnings.push('Color name will be normalized to title case');
      }
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  /**
   * Normalizes color names to title case
   * @param name - Color name to normalize
   * @returns Normalized color name
   */
  normalizeColorName(name: string): string {
    if (!name || typeof name !== 'string') return '';
    
    return name
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
      .trim();
  }
  
  // =============================================================================
  // COLOR CODE VALIDATION METHODS
  // =============================================================================
  
  /**
   * Validates color codes (Pantone, RAL, etc.)
   * @param code - Color code to validate
   * @param options - Validation options
   * @returns Validation result with errors and warnings
   */
  validateColorCode(code: string, options: ColorValidationOptions = {}): ColorValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!code && !options.allowEmpty) {
      errors.push('Color code cannot be empty');
      return { isValid: false, errors, warnings };
    }
    
    if (!code && options.allowEmpty) {
      return { isValid: true, errors, warnings };
    }
    
    if (code.length > 50) {
      errors.push('Color code cannot exceed 50 characters');
    }
    
    if (!/^[a-zA-Z0-9\s\-_()]+$/.test(code)) {
      errors.push('Color code contains invalid characters');
    }
    
    if (options.strict) {
      if (code !== this.standardizeColorCode(code)) {
        warnings.push('Color code will be standardized');
      }
    }
    
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  /**
   * Standardizes color codes (removes prefixes, formats consistently)
   * @param code - Color code to standardize
   * @returns Standardized color code
   */
  standardizeColorCode(code: string): string {
    if (!code) return '';
    
    // If it doesn't look like a Pantone code, return as-is
    if (!/^(PMS\s*|P\s*)?\d+\s*[A-Za-z]*$/i.test(code)) {
      return code;
    }
    
    // Remove leading P/PMS prefix
    const cleaned = code.replace(/^(PMS\s*|P\s*)/i, '');
    
    // Extract numbers and suffix
    const match = cleaned.match(/(\d+)\s*([A-Za-z]+)?/);
    if (!match) return code;
    
    const numbers = match[1];
    const suffix = match[2] || '';
    
    return suffix ? `${numbers} ${suffix.toUpperCase()}` : numbers ?? '';
  }
  
  // =============================================================================
  // FORMAT DETECTION AND VALIDATION
  // =============================================================================
  
  /**
   * Detects color format and validates accordingly
   * @param value - Color value to analyze
   * @returns Format detection result with validation and suggestions
   */
  detectColorFormat(value: string): ColorFormatValidationResult {
    // const _errors: string[] = [];
    // const _suggestions: string[] = [];
    
    if (!value) {
      return {
        originalValue: value,
        isValid: false,
        errors: ['Color value cannot be empty'],
        suggestions: ['Provide a valid color value']
      };
    }
    
    const trimmed = value.trim();
    
    // Test HEX format
    if (trimmed.startsWith('#') || /^[0-9A-Fa-f]{3,6}$/.test(trimmed)) {
      const hexResult = this.validateHex(trimmed.startsWith('#') ? trimmed : `#${trimmed}`);
      return {
        originalValue: value,
        isValid: hexResult.isValid,
        format: 'hex',
        standardizedValue: hexResult.isValid ? this.standardizeHex(trimmed) : undefined,
        errors: hexResult.errors,
        suggestions: hexResult.errors.length > 0 ? ['Check HEX format (e.g., #FF0000)'] : []
      };
    }
    
    // Test CMYK format
    if (/[Cc].*[Mm].*[Yy].*[Kk]/.test(trimmed) || /^\d+[,\s]+\d+[,\s]+\d+[,\s]+\d+$/.test(trimmed)) {
      const cmykResult = this.validateCMYK(trimmed);
      return {
        originalValue: value,
        isValid: cmykResult.isValid,
        format: 'cmyk',
        standardizedValue: cmykResult.isValid ? this.standardizeCMYK(trimmed) : undefined,
        errors: cmykResult.errors,
        suggestions: cmykResult.errors.length > 0 ? ['Check CMYK format (e.g., C:0 M:50 Y:100 K:0)'] : []
      };
    }
    
    // Test RGB format
    if (/rgb\s*\(/.test(trimmed)) {
      return {
        originalValue: value,
        isValid: /rgb\s*\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)/.test(trimmed),
        format: 'rgb',
        standardizedValue: trimmed,
        errors: [],
        suggestions: ['Check RGB format (e.g., rgb(255, 0, 0))']
      };
    }
    
    // Test Pantone format
    if (/^(PMS\s*)?\d+\s*[A-Za-z]*$/.test(trimmed)) {
      return {
        originalValue: value,
        isValid: true,
        format: 'pantone',
        standardizedValue: this.standardizeColorCode(trimmed),
        errors: [],
        suggestions: []
      };
    }
    
    return {
      originalValue: value,
      isValid: false,
      errors: ['Unknown color format'],
      suggestions: ['Use HEX (#FF0000), CMYK (C:0 M:100 Y:100 K:0), or Pantone (186 C) format']
    };
  }
  
  // =============================================================================
  // COMPREHENSIVE COLOR VALIDATION
  // =============================================================================
  
  /**
   * Validates and standardizes complete color data
   * @param colorData - Complete color data to validate
   * @param options - Validation options
   * @returns Standardized color data with errors and warnings
   */
  validateCompleteColorData(colorData: {
    hex?: string;
    cmyk?: string;
    name?: string;
    code?: string;
  }, options: ColorValidationOptions = {}): StandardizedColorData {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Validate HEX (required)
    if (!colorData.hex) {
      errors.push('HEX color value is required');
    } else {
      const hexResult = this.validateHex(colorData.hex, options);
      errors.push(...hexResult.errors);
      warnings.push(...hexResult.warnings);
    }
    
    // Validate CMYK (optional)
    let standardizedCMYK = 'C:0 M:0 Y:0 K:0';
    if (colorData.cmyk) {
      const cmykResult = this.validateCMYK(colorData.cmyk, { ...options, allowEmpty: true });
      errors.push(...cmykResult.errors);
      warnings.push(...cmykResult.warnings);
      if (cmykResult.isValid) {
        standardizedCMYK = this.standardizeCMYK(colorData.cmyk);
      }
    }
    
    // Validate name (optional)
    let standardizedName = '';
    if (colorData.name) {
      const nameResult = this.validateColorName(colorData.name, { ...options, allowEmpty: true });
      errors.push(...nameResult.errors);
      warnings.push(...nameResult.warnings);
      if (nameResult.isValid) {
        standardizedName = this.normalizeColorName(colorData.name);
      }
    }
    
    // Validate code (optional)
    let standardizedCode = '';
    if (colorData.code) {
      const codeResult = this.validateColorCode(colorData.code, { ...options, allowEmpty: true });
      errors.push(...codeResult.errors);
      warnings.push(...codeResult.warnings);
      if (codeResult.isValid) {
        standardizedCode = this.standardizeColorCode(colorData.code);
      }
    }
    
    return {
      hex: colorData.hex ? this.standardizeHex(colorData.hex) : '',
      cmyk: standardizedCMYK,
      name: standardizedName,
      code: standardizedCode,
      errors,
      warnings
    };
  }
  
  // =============================================================================
  // BUSINESS RULE VALIDATION
  // =============================================================================
  
  /**
   * Validates color data for database storage with strict requirements
   * @param colorData - Color data for database storage
   * @returns Validation result for database operations
   */
  validateForDatabase(colorData: {
    hex?: string;
    cmyk?: string;
    name?: string;
    code?: string;
    organizationId?: string;
  }): ColorValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Database-specific validations
    if (!colorData.organizationId) {
      errors.push('Organization ID is required for database storage');
    }
    
    if (!colorData.hex) {
      errors.push('HEX color is required for database storage');
    }
    
    if (!colorData.name && !colorData.code) {
      errors.push('Either color name or color code is required');
    }
    
    // Validate individual fields with strict rules
    const standardized = this.validateCompleteColorData(colorData, { strict: true, allowEmpty: false });
    errors.push(...standardized.errors);
    warnings.push(...standardized.warnings);
    
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  /**
   * Validates color data for synchronization with relaxed requirements
   * @param colorData - Color data for synchronization
   * @returns Validation result for sync operations
   */
  validateForSync(colorData: {
    hex?: string;
    cmyk?: string;
    name?: string;
    code?: string;
    externalId?: string;
  }): ColorValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Sync-specific validations
    if (!colorData.externalId) {
      errors.push('External ID is required for synchronization');
    }
    
    // Relaxed validation for sync (data might come from external sources)
    const standardized = this.validateCompleteColorData(colorData, { strict: false, allowEmpty: true, autoFix: true });
    errors.push(...standardized.errors);
    warnings.push(...standardized.warnings);
    
    return { isValid: errors.length === 0, errors, warnings };
  }
  
  // =============================================================================
  // UTILITY METHODS
  // =============================================================================
  
  /**
   * Gets validation rules configuration
   * @returns Validation rules object
   */
  getValidationRules(): Record<string, any> {
    return {
      hex: {
        required: true,
        format: '#RRGGBB',
        pattern: /^#[0-9A-Fa-f]{6}$/,
        autoFix: true
      },
      cmyk: {
        required: false,
        format: 'C:X M:Y Y:Z K:W',
        pattern: /[Cc]:?\s*\d+\s*[Mm]:?\s*\d+\s*[Yy]:?\s*\d+\s*[Kk]:?\s*\d+/,
        ranges: { c: [0, 100], m: [0, 100], y: [0, 100], k: [0, 100] }
      },
      name: {
        required: false,
        minLength: 2,
        maxLength: 100,
        pattern: /^[a-zA-Z0-9\s\-_()]+$/,
        normalize: true
      },
      code: {
        required: false,
        maxLength: 50,
        pattern: /^[a-zA-Z0-9\s\-_()]+$/,
        standardize: true
      }
    };
  }
  
  /**
   * Gets list of supported color formats
   * @returns Array of supported format names
   */
  getSupportedFormats(): string[] {
    return ['hex', 'cmyk', 'rgb', 'hsl', 'pantone', 'ral'];
  }
  
  /**
   * Gets service information and metadata
   * @returns Service metadata object
   */
  getServiceInfo(): { name: string; version: string; features: string[] } {
    return {
      name: 'ColorValidator',
      version: '1.0.0',
      features: [
        'HEX validation and standardization',
        'CMYK validation and parsing',
        'Color name normalization',
        'Color code standardization',
        'Format detection',
        'Business rule validation',
        'Database validation',
        'Sync validation',
        'Auto-fix capabilities',
        'Comprehensive error reporting'
      ]
    };
  }
}