/**
 * @file product.repository.interface.ts
 * @description Interface for ProductRepository data access layer
 * 
 * Defines the contract for all database operations related to products.
 * This interface separates data access concerns from business logic.
 */

import Database from 'better-sqlite3';
import { NewProduct, UpdateProduct } from '../../../../shared/types/product.types';

export interface IProductRepository {
  // Core CRUD Operations
  findAll(organizationId: string): ProductRow[];
  findById(productId: string, organizationId: string): ProductRow | null;
  insert(productData: NewProduct, organizationId: string, userId?: string, fromSync?: boolean): string;
  update(id: string, updates: UpdateProduct, organizationId: string, userId?: string, fromSync?: boolean): boolean;
  softDelete(productId: string, organizationId: string): boolean;
  
  // Query Operations
  search(query: string, organizationId: string): ProductRow[];
  findUnsynced(): ProductRow[];
  getAllWithColors(organizationId: string): ProductWithColorsRow[];
  getProductWithColors(productId: string, organizationId: string): ProductWithColorsRow | null;
  
  // Product-Color Relationship Operations
  addProductColor(productId: string, colorId: string, organizationId: string): boolean;
  removeProductColor(productId: string, colorId: string, organizationId: string): boolean;
  getProductColors(productId: string, organizationId: string): ProductColorRow[];
  
  // Soft Delete Operations
  findSoftDeleted(organizationId: string, limit?: number, offset?: number): ProductRow[];
  restoreRecord(productId: string, organizationId: string): boolean;
  
  // Bulk Operations
  deleteMultiple(productIds: string[], organizationId: string): { success: boolean; deletedIds: string[] };
  upsertFromSupabase(supabaseProduct: any, organizationId: string, userId?: string): ProductRow | null;
  deduplicateProducts(organizationId: string): { success: boolean; deduplicatedCount: number; errors: string[] };
  
  // Utility Operations
  markAsSynced(productId: string): void;
  
  // Prepared Statement Cache
  getPreparedStatement(sql: string): Database.Statement;
}

/**
 * Database row interface for products (updated for UUID primary key)
 */
export interface ProductRow {
  id: string;               // UUID primary key (was external_id)
  external_id: string;      // Compatibility field (same as id)
  organization_id: string;  // UUID foreign key
  user_id: string | null;
  name: string;
  description: string | null;
  metadata: string | null;
  is_active: boolean;
  is_synced: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  created_by: string | null;
}

/**
 * Product with colors combined result
 */
export interface ProductWithColorsRow {
  // Product fields
  product_id: string;
  product_name: string;
  product_description: string | null;
  product_created_at: string;
  product_updated_at: string;
  // Color fields (nullable for products without colors)
  color_id: string | null;
  color_code: string | null;
  color_name: string | null;
  color_hex: string | null;
  color_spaces: string | null;
  is_gradient: boolean | null;
  gradient_colors: string | null;
  color_notes: string | null;
  color_tags: string | null;
  is_library: boolean | null;
  color_created_at: string | null;
  color_updated_at: string | null;
  // Relationship fields
  display_order: number | null;
}

/**
 * Product-color relationship result (updated for UUID foreign keys)
 */
export interface ProductColorRow {
  product_id: string;       // UUID foreign key
  color_id: string;         // UUID foreign key  
  color_external_id: string; // Compatibility field (same as color_id)
  display_order: number;
  organization_id: string;   // UUID foreign key
}

/**
 * Deduplication group result
 */
export interface DuplicationGroupRow {
  name: string;
  count: number;
  ids: string;
}

/**
 * Product detail with color count for deduplication (updated for UUID primary key)
 */
export interface ProductDetailRow {
  id: string;               // UUID primary key (was external_id)
  external_id: string;      // Compatibility field (same as id)
  name: string;
  created_at: string;
  colorCount: number;
}