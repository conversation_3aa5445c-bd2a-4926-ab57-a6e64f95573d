/**
 * @file ThemeSection.tsx
 * @description Theme preferences section component
 */

import React from 'react';
import { useTheme } from '../../../context/ThemeContext';
import { useSettingsStore } from '../../../store/settings.store';

/**
 * Theme preferences section component
 */
export const ThemeSection: React.FC = () => {
  const theme = useTheme();
  const settings = useSettingsStore();

  return (
    <section>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        Theme Preferences
      </h3>
      <div className='space-y-6'>
        {/* Preferred Light Theme */}
        <div>
          <label className='block text-ui-foreground-primary dark:text-white mb-2'>
            Preferred Light Theme
          </label>
          <div className='flex items-center space-x-4'>
            <label className='flex items-center cursor-pointer'>
              <input
                type='radio'
                className='form-radio h-4 w-4 text-brand-primary'
                checked={settings.preferredLightTheme === 'light'}
                onChange={() => {
                  settings.setPreferredLightTheme('light');
                  if (theme.mode === 'light') {
                    theme.setMode('light');
                  }
                }}
              />
              <span className='ml-2 text-ui-foreground-primary dark:text-gray-300'>
                Standard Light
              </span>
            </label>
            <label className='flex items-center cursor-pointer'>
              <input
                type='radio'
                className='form-radio h-4 w-4 text-brand-primary'
                checked={settings.preferredLightTheme === 'system-light'}
                onChange={() => {
                  settings.setPreferredLightTheme('system-light');
                  if (theme.mode === 'light') {
                    theme.setMode('light');
                  }
                }}
              />
              <span className='ml-2 text-ui-foreground-primary dark:text-gray-300'>
                System Light
              </span>
            </label>
          </div>
        </div>

        {/* Preferred Dark Theme */}
        <div>
          <label className='block text-ui-foreground-primary dark:text-white mb-2'>
            Preferred Dark Theme
          </label>
          <div className='flex items-center space-x-4'>
            <label className='flex items-center cursor-pointer'>
              <input
                type='radio'
                className='form-radio h-4 w-4 text-brand-primary'
                checked={settings.preferredDarkTheme === 'dark'}
                onChange={() => {
                  settings.setPreferredDarkTheme('dark');
                  if (theme.mode === 'dark') {
                    theme.setMode('dark');
                  }
                }}
              />
              <span className='ml-2 text-ui-foreground-primary dark:text-gray-300'>
                Standard Dark
              </span>
            </label>
            <label className='flex items-center cursor-pointer'>
              <input
                type='radio'
                className='form-radio h-4 w-4 text-brand-primary'
                checked={settings.preferredDarkTheme === 'new-york'}
                onChange={() => {
                  settings.setPreferredDarkTheme('new-york');
                  if (theme.mode === 'dark') {
                    theme.setMode('dark');
                  }
                }}
              />
              <span className='ml-2 text-ui-foreground-primary dark:text-gray-300'>
                New York
              </span>
            </label>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ThemeSection;
