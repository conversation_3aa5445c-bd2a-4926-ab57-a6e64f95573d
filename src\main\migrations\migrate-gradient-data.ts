/**
 * @file migrate-gradient-data.ts
 * @description Migration script to convert legacy gradient data to new format
 * 
 * This migration converts:
 * - Legacy: gradient.stops array format
 * - Legacy: gradient.gradientStops array format
 * To:
 * - New: gradient.colors array format
 */

import Database from 'better-sqlite3';
import path from 'path';
import { app } from 'electron';

interface LegacyGradientStop {
  color: string;
  position: number;
  cmyk?: string;
  colorCode?: string | null;
}

interface LegacyGradientData {
  stops?: LegacyGradientStop[];
  gradientStops?: LegacyGradientStop[];
  angle?: number;
}

interface NewGradientData {
  colors: string[];
  colorCodes?: string[];
  type: 'linear';
  angle: number;
}

export class GradientMigration {
  private db: Database.Database;

  constructor(dbPath?: string) {
    const finalDbPath = dbPath || this.getDefaultDbPath();
    this.db = new Database(finalDbPath);
  }

  private getDefaultDbPath(): string {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'chromasync.db');
  }

  /**
   * Run the complete gradient migration
   */
  async migrateAllGradients(): Promise<{ success: boolean; migrated: number; errors: string[] }> {
    console.log('[GradientMigration] Starting gradient data migration...');
    
    const errors: string[] = [];
    let migratedCount = 0;

    try {
      // Find all colors with gradient data that needs migration
      const gradientColors = this.findColorsWithLegacyGradients();
      console.log(`[GradientMigration] Found ${gradientColors.length} colors with legacy gradient data`);

      // Migrate each color
      for (const color of gradientColors) {
        try {
          const migrated = await this.migrateColorGradient(color);
          if (migrated) {
            migratedCount++;
            console.log(`[GradientMigration] ✅ Migrated: ${color.display_name} (${color.id})`);
          }
        } catch (error) {
          const errorMsg = `Failed to migrate ${color.display_name}: ${error}`;
          errors.push(errorMsg);
          console.error(`[GradientMigration] ❌ ${errorMsg}`);
        }
      }

      console.log(`[GradientMigration] Migration completed: ${migratedCount} colors migrated, ${errors.length} errors`);
      
      return {
        success: errors.length === 0,
        migrated: migratedCount,
        errors
      };

    } catch (error) {
      console.error('[GradientMigration] Migration failed:', error);
      return {
        success: false,
        migrated: migratedCount,
        errors: [`Migration failed: ${error}`]
      };
    }
  }

  /**
   * Find all colors that have legacy gradient data
   */
  private findColorsWithLegacyGradients(): Array<{ id: string; display_name: string; color_spaces: string; properties: string }> {
    const stmt = this.db.prepare(`
      SELECT id, display_name, color_spaces, properties
      FROM colors 
      WHERE is_gradient = 1 
        AND (
          color_spaces LIKE '%"stops"%' OR 
          properties LIKE '%gradientStops%' OR
          (color_spaces NOT LIKE '%"colors"%' AND color_spaces LIKE '%gradient%')
        )
        AND deleted_at IS NULL
    `);

    return stmt.all() as Array<{ id: string; display_name: string; color_spaces: string; properties: string }>;
  }

  /**
   * Migrate a single color's gradient data
   */
  private async migrateColorGradient(color: { id: string; display_name: string; color_spaces: string; properties: string }): Promise<boolean> {
    try {
      // Parse existing data
      const colorSpaces = color.color_spaces ? JSON.parse(color.color_spaces) : {};
      const properties = color.properties ? JSON.parse(color.properties) : {};

      // Check if already in new format
      if (colorSpaces.gradient?.colors && Array.isArray(colorSpaces.gradient.colors)) {
        console.log(`[GradientMigration] ⏭️ ${color.display_name} already in NEW format`);
        return false;
      }

      // Extract legacy gradient data
      const legacyGradient = this.extractLegacyGradientData(colorSpaces, properties);
      
      if (!legacyGradient) {
        console.log(`[GradientMigration] ⚠️ No legacy gradient data found for ${color.display_name}`);
        return false;
      }

      // Convert to new format
      const newGradient = this.convertToNewFormat(legacyGradient);
      
      // Update color_spaces with new format
      const updatedColorSpaces = {
        ...colorSpaces,
        gradient: newGradient
      };

      // Update gradient_colors CSV
      const gradientColorsCsv = newGradient.colors.join(',');

      // Update database
      const updateStmt = this.db.prepare(`
        UPDATE colors 
        SET 
          color_spaces = ?,
          gradient_colors = ?,
          updated_at = datetime('now')
        WHERE id = ?
      `);

      updateStmt.run(
        JSON.stringify(updatedColorSpaces),
        gradientColorsCsv,
        color.id
      );

      return true;

    } catch (error) {
      throw new Error(`Migration failed for ${color.display_name}: ${error}`);
    }
  }

  /**
   * Extract legacy gradient data from various sources
   */
  private extractLegacyGradientData(colorSpaces: any, properties: any): LegacyGradientData | null {
    // Check colorSpaces.gradient.stops (most common legacy format)
    if (colorSpaces.gradient?.stops && Array.isArray(colorSpaces.gradient.stops)) {
      return {
        stops: colorSpaces.gradient.stops,
        angle: colorSpaces.gradient.angle || 45
      };
    }

    // Check properties.gradient.gradientStops
    if (properties.gradient?.gradientStops && Array.isArray(properties.gradient.gradientStops)) {
      return {
        gradientStops: properties.gradient.gradientStops,
        angle: properties.gradient.angle || 45
      };
    }

    return null;
  }

  /**
   * Convert legacy gradient data to new format
   */
  private convertToNewFormat(legacyGradient: LegacyGradientData): NewGradientData {
    const stops = legacyGradient.stops || legacyGradient.gradientStops || [];
    
    const colors = stops.map(stop => stop.color);
    const colorCodes = stops
      .map(stop => stop.colorCode)
      .filter((code): code is string => code !== null && code !== undefined && code.trim() !== '');

    return {
      colors,
      colorCodes: colorCodes.length > 0 ? colorCodes : undefined,
      type: 'linear',
      angle: legacyGradient.angle || 45
    };
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<{ totalGradients: number; legacyGradients: number; newFormatGradients: number }> {
    const totalStmt = this.db.prepare('SELECT COUNT(*) as count FROM colors WHERE is_gradient = 1 AND deleted_at IS NULL');
    const totalResult = totalStmt.get() as { count: number };

    const legacyStmt = this.db.prepare(`
      SELECT COUNT(*) as count FROM colors 
      WHERE is_gradient = 1 
        AND (
          color_spaces LIKE '%"stops"%' OR 
          properties LIKE '%gradientStops%' OR
          (color_spaces NOT LIKE '%"colors"%' AND color_spaces LIKE '%gradient%')
        )
        AND deleted_at IS NULL
    `);
    const legacyResult = legacyStmt.get() as { count: number };

    const newFormatStmt = this.db.prepare(`
      SELECT COUNT(*) as count FROM colors 
      WHERE is_gradient = 1 
        AND color_spaces LIKE '%"colors"%'
        AND deleted_at IS NULL
    `);
    const newFormatResult = newFormatStmt.get() as { count: number };

    return {
      totalGradients: totalResult.count,
      legacyGradients: legacyResult.count,
      newFormatGradients: newFormatResult.count
    };
  }

  /**
   * Close database connection
   */
  close(): void {
    this.db.close();
  }
}

/**
 * Run migration from command line or programmatically
 */
export async function runGradientMigration(dbPath?: string): Promise<void> {
  const migration = new GradientMigration(dbPath);
  
  try {
    console.log('[GradientMigration] Checking current status...');
    const status = await migration.getMigrationStatus();
    console.log('[GradientMigration] Current status:', status);

    if (status.legacyGradients === 0) {
      console.log('[GradientMigration] ✅ No legacy gradients found - migration not needed');
      return;
    }

    console.log(`[GradientMigration] Starting migration of ${status.legacyGradients} legacy gradients...`);
    const result = await migration.migrateAllGradients();

    if (result.success) {
      console.log(`[GradientMigration] ✅ Migration completed successfully - ${result.migrated} gradients migrated`);
    } else {
      console.error(`[GradientMigration] ❌ Migration completed with errors - ${result.migrated} gradients migrated, ${result.errors.length} errors`);
      result.errors.forEach(error => console.error(`  - ${error}`));
    }

  } finally {
    migration.close();
  }
}