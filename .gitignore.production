# Production .gitignore additions for credential security
# Add these lines to .gitignore when ready for production deployment

# Configuration files containing sensitive credentials
out/app-config.json
dist/app-config.json
build/app-config.json

# Environment files with credentials
.env.production
.env.local
.env.*.local

# Backup environment files
.env.backup
.env.*.backup

# Secret management files
secrets/
*.key
*.pem
*.p12
*.pfx

# Security audit logs
security-audit.log
access-audit.log

# Credential dumps or exports
credentials.json
secrets.json
config-export.json

# Development credential backups
.env.dev.backup
config.dev.json

# Database backups that might contain sensitive data
*.db.backup
backup.db
chromasync-backup-*.db

# Log files that might contain sensitive information
logs/sensitive/
audit-logs/
security-logs/

# Temporary credential files
temp-credentials.*
tmp-config.*
.credentials-temp

# IDE/Editor specific files that might cache credentials
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# OS specific files
.DS_Store
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs that might contain embedded credentials
dist/
build/
out/
.next/
.nuxt/

# Testing artifacts that might contain test credentials
coverage/
.nyc_output/
test-results/
test-credentials.*

# Docker files with embedded secrets
docker-compose.override.yml
.env.docker

# Kubernetes secrets
k8s-secrets/
*.secret.yaml
*-secret.yml

# Terraform state files (may contain sensitive values)
*.tfstate
*.tfstate.*
.terraform/

# Cloud provider credential files
gcp-service-account.json
aws-credentials.json
azure-credentials.json

# Certificate files
*.crt
*.cert
*.ca-bundle

# License files with embedded keys
license.key
activation.key

# Backup archives that might contain credentials
backup-*.tar.gz
backup-*.zip
credentials-backup.*

# Deployment artifacts
deploy-config.json
production-secrets.env
staging-secrets.env

# Security scan results
security-report.*
vulnerability-scan.*
penetration-test.*

# Monitoring and analytics keys
analytics-keys.json
monitoring-config.json
telemetry-secrets.env