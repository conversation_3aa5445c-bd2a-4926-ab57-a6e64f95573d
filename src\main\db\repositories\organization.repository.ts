/**
 * @file organization.repository.ts
 * @description Repository for organization data access operations
 *
 * Handles all database operations for organizations, members, invitations,
 * and user profiles following the Repository pattern. Separates data access
 * concerns from business logic in OrganizationService.
 */

import Database from 'better-sqlite3';
// TODO: Re-enable when UUID generation is needed
// import { v4 as uuidv4 } from 'uuid';
import {
  IOrganizationRepository,
  OrganizationRow,
  OrganizationWithRoleRow,
  OrganizationMemberRow,
  InvitationRow,
  UserRow,
  CreateOrganizationData,
  UpdateOrganizationData,
  CreateInvitationData,
  CreateUserData,
  UpdateUserData,
  CascadeDeleteResult,
  OrganizationDataCounts,
} from './interfaces/organization.repository.interface';

export class OrganizationRepository implements IOrganizationRepository {
  private static preparedStatements = new Map<string, Database.Statement>();

  constructor(private db: Database.Database) {}

  // Organization CRUD Operations
  findAll(): OrganizationRow[] {
    const stmt = this.getPreparedStatement(`
      SELECT 
        id,
        id as external_id,
        name,
        slug,
        plan,
        settings,
        created_at,
        updated_at
      FROM organizations
      ORDER BY name ASC
    `);

    return stmt.all() as OrganizationRow[];
  }

  findById(organizationId: string): OrganizationRow | null {
    const stmt = this.getPreparedStatement(`
      SELECT 
        id,
        id as external_id,
        name,
        slug,
        plan,
        settings,
        created_at,
        updated_at
      FROM organizations
      WHERE id = ?
    `);

    const result = stmt.get(organizationId) as OrganizationRow | undefined;
    return result || null;
  }

  findByExternalId(externalId: string): OrganizationRow | null {
    const stmt = this.getPreparedStatement(`
      SELECT 
        o.id,
        o.id as external_id,
        o.name,
        o.slug,
        o.plan,
        o.settings,
        o.created_at,
        o.updated_at,
        COUNT(om.user_id) as member_count
      FROM organizations o
      LEFT JOIN organization_members om ON o.id = om.organization_id
      WHERE o.id = ?
      GROUP BY o.id
    `);

    const result = stmt.get(externalId) as OrganizationRow | undefined;
    return result || null;
  }

  insert(orgData: CreateOrganizationData): string {
    const { id, name, slug, plan, settings, ownerId } = orgData;

    // Validate all fields before attempting insert
    if (!id || typeof id !== 'string' || id.length !== 36) {
      throw new Error(
        `Invalid id: must be a 36-character UUID, got: ${JSON.stringify(id)}`
      );
    }

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      throw new Error(
        `Invalid name: must be a non-empty string, got: ${JSON.stringify(name)}`
      );
    }

    if (!slug || typeof slug !== 'string' || slug.trim().length === 0) {
      throw new Error(
        `Invalid slug: must be a non-empty string, got: ${JSON.stringify(slug)}`
      );
    }

    if (!ownerId || typeof ownerId !== 'string') {
      throw new Error(
        `Invalid ownerId: must be a non-empty string, got: ${JSON.stringify(ownerId)}`
      );
    }

    const transaction = this.db.transaction(() => {
      // Create organization using UUID as primary key
      this.getPreparedStatement(
        `
        INSERT INTO organizations (id, name, slug, plan, settings)
        VALUES (?, ?, ?, ?, ?)
      `
      ).run(id, name, slug, plan, settings);

      // Add owner as first member
      this.getPreparedStatement(
        `
        INSERT INTO organization_members (organization_id, user_id, role)
        VALUES (?, ?, 'owner')
      `
      ).run(id, ownerId);
    });

    transaction();
    return id;
  }

  update(organizationId: string, updates: UpdateOrganizationData): boolean {
    const allowedUpdates = ['name', 'plan', 'settings'];
    const updateFields = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .map(key => `${key} = ?`);

    if (updateFields.length === 0) {
      return false;
    }

    const values = updateFields.map(field => {
      const key = field.split(' = ')[0] as keyof UpdateOrganizationData;
      return key === 'settings' && typeof updates[key] === 'object'
        ? JSON.stringify(updates[key])
        : updates[key];
    });

    const stmt = this.getPreparedStatement(`
      UPDATE organizations
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(...values, organizationId);
    return result.changes > 0;
  }

  delete(organizationId: string): boolean {
    const stmt = this.getPreparedStatement(`
      DELETE FROM organizations WHERE id = ?
    `);

    const result = stmt.run(organizationId);
    return result.changes > 0;
  }

  findBySlug(slug: string): OrganizationRow | null {
    const stmt = this.getPreparedStatement(`
      SELECT 
        id,
        id as external_id,
        name,
        slug,
        plan,
        settings,
        created_at,
        updated_at
      FROM organizations
      WHERE slug = ?
    `);

    const result = stmt.get(slug) as OrganizationRow | undefined;
    return result || null;
  }

  // Organization Query Operations
  findForUser(userId: string): OrganizationWithRoleRow[] {
    const stmt = this.getPreparedStatement(`
      SELECT 
        o.id,
        o.id as external_id,
        o.name,
        o.slug,
        o.plan,
        o.settings,
        o.created_at,
        o.updated_at,
        om.role as user_role,
        COUNT(om2.user_id) as member_count
      FROM organizations o
      JOIN organization_members om ON o.id = om.organization_id
      LEFT JOIN organization_members om2 ON o.id = om2.organization_id
      WHERE om.user_id = ?
      GROUP BY o.id
      ORDER BY o.name ASC
    `);

    return stmt.all(userId) as OrganizationWithRoleRow[];
  }

  checkUserMembership(organizationId: string, userId: string): boolean {
    const stmt = this.getPreparedStatement(`
      SELECT 1 FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      WHERE o.id = ? AND om.user_id = ?
    `);

    const result = stmt.get(organizationId, userId);
    return !!result;
  }

  getUserRole(organizationId: string, userId: string): string | null {
    const stmt = this.getPreparedStatement(`
      SELECT om.role 
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      WHERE o.id = ? AND om.user_id = ?
    `);

    const result = stmt.get(organizationId, userId) as
      | { role: string }
      | undefined;
    return result?.role || null;
  }

  generateUniqueSlug(baseSlug: string): string {
    // Validate input - ensure baseSlug is not empty
    if (
      !baseSlug ||
      typeof baseSlug !== 'string' ||
      baseSlug.trim().length === 0
    ) {
      baseSlug = 'organization';
    }

    let slug = baseSlug;
    let counter = 1;

    while (this.findBySlug(slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  // Member Management Operations
  findMembers(organizationId: string): OrganizationMemberRow[] {
    const stmt = this.getPreparedStatement(`
      SELECT 
        om.organization_id,
        om.user_id,
        om.role,
        om.joined_at,
        om.invited_by,
        u.email as user_email,
        u.name as user_name,
        u.display_name as user_display_name
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      LEFT JOIN users u ON om.user_id = u.id
      WHERE o.id = ?
      ORDER BY om.joined_at DESC
    `);

    return stmt.all(organizationId) as OrganizationMemberRow[];
  }

  findMember(
    organizationId: string,
    userId: string
  ): OrganizationMemberRow | null {
    const stmt = this.getPreparedStatement(`
      SELECT 
        om.organization_id,
        om.user_id,
        om.role,
        om.joined_at,
        om.invited_by,
        u.email as user_email,
        u.name as user_name,
        u.display_name as user_display_name
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      LEFT JOIN users u ON om.user_id = u.id
      WHERE o.id = ? AND om.user_id = ?
    `);

    const result = stmt.get(organizationId, userId) as
      | OrganizationMemberRow
      | undefined;
    return result || null;
  }

  insertMember(
    organizationId: string,
    userId: string,
    role: string,
    invitedBy?: string
  ): boolean {
    const org = this.findById(organizationId);
    if (!org) {
      return false;
    }

    // Check if user is already a member
    const existing = this.getPreparedStatement(
      `
      SELECT 1 FROM organization_members 
      WHERE organization_id = ? AND user_id = ?
    `
    ).get(org.id, userId);

    if (existing) {
      return false; // User is already a member
    }

    const stmt = this.getPreparedStatement(`
      INSERT INTO organization_members (organization_id, user_id, role, invited_by)
      VALUES (?, ?, ?, ?)
    `);

    const result = stmt.run(org.id, userId, role, invitedBy || null);
    return result.changes > 0;
  }

  updateMemberRole(
    organizationId: string,
    userId: string,
    role: string
  ): boolean {
    const org = this.findById(organizationId);
    if (!org) {
      return false;
    }

    // Can't change owner role
    const member = this.getPreparedStatement(
      `
      SELECT role FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `
    ).get(org.id, userId) as { role: string } | undefined;

    if (!member || member.role === 'owner') {
      return false;
    }

    const stmt = this.getPreparedStatement(`
      UPDATE organization_members
      SET role = ?
      WHERE organization_id = ? AND user_id = ?
    `);

    const result = stmt.run(role, org.id, userId);
    return result.changes > 0;
  }

  removeMember(organizationId: string, userId: string): boolean {
    const org = this.findById(organizationId);
    if (!org) {
      return false;
    }

    // Can't remove owner
    const member = this.getPreparedStatement(
      `
      SELECT role FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `
    ).get(org.id, userId) as { role: string } | undefined;

    if (!member || member.role === 'owner') {
      return false;
    }

    const stmt = this.getPreparedStatement(`
      DELETE FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `);

    const result = stmt.run(org.id, userId);
    return result.changes > 0;
  }

  // Invitation Management Operations
  findInvitation(token: string): InvitationRow | null {
    const stmt = this.getPreparedStatement(`
      SELECT * FROM organization_invitations 
      WHERE token = ? AND accepted_at IS NULL
    `);

    const result = stmt.get(token) as InvitationRow | undefined;
    return result || null;
  }

  findPendingInvitations(organizationId: string): InvitationRow[] {
    const org = this.findById(organizationId);
    if (!org) {
      return [];
    }

    const stmt = this.getPreparedStatement(`
      SELECT 
        id,
        id as external_id,
        organization_id,
        email,
        role,
        token,
        invited_by,
        expires_at,
        accepted_at,
        created_at
      FROM organization_invitations
      WHERE organization_id = ? 
      AND accepted_at IS NULL 
      AND expires_at > CURRENT_TIMESTAMP
      ORDER BY created_at DESC
    `);

    return stmt.all(org.id) as InvitationRow[];
  }

  insertInvitation(invitationData: CreateInvitationData): string {
    const { id, organization_id, email, role, invited_by, token, expires_at } =
      invitationData;

    const stmt = this.getPreparedStatement(`
      INSERT INTO organization_invitations 
      (id, organization_id, email, role, invited_by, token, expires_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(id, organization_id, email, role, invited_by, token, expires_at);
    return id;
  }

  acceptInvitation(invitationId: string): boolean {
    const stmt = this.getPreparedStatement(`
      UPDATE organization_invitations 
      SET accepted_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(invitationId);
    return result.changes > 0;
  }

  revokeInvitation(organizationId: string, invitationId: string): boolean {
    const org = this.findById(organizationId);
    if (!org) {
      return false;
    }

    const stmt = this.getPreparedStatement(`
      DELETE FROM organization_invitations 
      WHERE organization_id = ? AND id = ? AND accepted_at IS NULL
    `);

    const result = stmt.run(org.id, invitationId);
    return result.changes > 0;
  }

  checkExistingInvitation(
    organizationId: string,
    email: string
  ): InvitationRow | null {
    const org = this.findById(organizationId);
    if (!org) {
      return null;
    }

    const stmt = this.getPreparedStatement(`
      SELECT id, email, accepted_at, expires_at FROM organization_invitations 
      WHERE organization_id = ? AND email = ?
    `);

    const result = stmt.get(org.id, email) as InvitationRow | undefined;
    return result || null;
  }

  deleteInvitation(organizationId: string, email: string): boolean {
    const org = this.findById(organizationId);
    if (!org) {
      return false;
    }

    const stmt = this.getPreparedStatement(`
      DELETE FROM organization_invitations 
      WHERE organization_id = ? AND email = ?
    `);

    const result = stmt.run(org.id, email);
    return result.changes > 0;
  }

  // User Profile Operations
  findUser(userId: string): UserRow | null {
    const stmt = this.getPreparedStatement(`
      SELECT id, email, name, display_name, created_at, updated_at
      FROM users WHERE id = ?
    `);

    const result = stmt.get(userId) as UserRow | undefined;
    return result || null;
  }

  insertUser(userData: CreateUserData): boolean {
    const { id, email, name, display_name } = userData;

    const stmt = this.getPreparedStatement(`
      INSERT INTO users (id, email, name, display_name, created_at, updated_at)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);

    try {
      const result = stmt.run(id, email, name || null, display_name || null);
      return result.changes > 0;
    } catch (error) {
      // Handle duplicate key constraint
      return false;
    }
  }

  updateUser(userId: string, userData: UpdateUserData): boolean {
    const updateFields: string[] = [];
    const values: any[] = [];

    if (userData.email !== undefined) {
      updateFields.push('email = ?');
      values.push(userData.email);
    }

    if (userData.name !== undefined) {
      updateFields.push('name = ?');
      values.push(userData.name);
    }

    if (userData.display_name !== undefined) {
      updateFields.push('display_name = ?');
      values.push(userData.display_name);
    }

    if (updateFields.length === 0) {
      return false;
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(userId);

    const stmt = this.getPreparedStatement(`
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `);

    const result = stmt.run(...values);
    return result.changes > 0;
  }

  syncUserProfile(userData: CreateUserData): boolean {
    const existingUser = this.findUser(userData.id);

    if (!existingUser) {
      return this.insertUser(userData);
    } else {
      return this.updateUser(userData.id, {
        email: userData.email,
        name: userData.name,
        display_name: userData.display_name,
      });
    }
  }

  cleanupPlaceholderEmails(userId: string, realEmail: string): boolean {
    const stmt = this.getPreparedStatement(`
      UPDATE users 
      SET email = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND (email LIKE '%@local.app' OR email = 'Unknown Email')
    `);

    const result = stmt.run(realEmail, userId);
    return result.changes > 0;
  }

  // Bulk Operations
  deleteOrganizationCascade(
    organizationId: string,
    forceCascade: boolean
  ): CascadeDeleteResult {
    const org = this.findById(organizationId);
    if (!org) {
      throw new Error('Organization not found');
    }

    // Get data counts before deletion
    const dataCounts = this.getOrganizationDataCounts(organizationId);
    const totalData =
      dataCounts.colors +
      dataCounts.products +
      dataCounts.datasheets +
      dataCounts.relationships;

    if (totalData > 0 && !forceCascade) {
      throw new Error(
        `Cannot delete organization with existing data. Use forceCascade to delete all data.`
      );
    }

    const deleteTransaction = this.db.transaction(() => {
      const deletedData = {
        colors: 0,
        products: 0,
        datasheets: 0,
        relationships: 0,
        members: 0,
      };

      if (forceCascade && totalData > 0) {
        // 1. Delete product-color relationships
        const relationshipResult = this.getPreparedStatement(
          `
          DELETE FROM product_colors WHERE organization_id = ?
        `
        ).run(organizationId);
        deletedData.relationships = relationshipResult.changes;

        // 2. Soft delete datasheets
        const datasheetResult = this.getPreparedStatement(
          `
          UPDATE datasheets 
          SET is_active = 0, updated_at = datetime('now')
          WHERE product_id IN (
            SELECT id FROM products WHERE organization_id = ? AND is_active = 1
          )
        `
        ).run(organizationId);
        deletedData.datasheets = datasheetResult.changes;

        // 3. Soft delete colors
        const colorResult = this.getPreparedStatement(
          `
          UPDATE colors 
          SET deleted_at = ?, updated_at = ?
          WHERE organization_id = ? AND deleted_at IS NULL
        `
        ).run(
          new Date().toISOString(),
          new Date().toISOString(),
          organizationId
        );
        deletedData.colors = colorResult.changes;

        // 4. Soft delete products
        const productResult = this.getPreparedStatement(
          `
          UPDATE products 
          SET deleted_at = ?, updated_at = ?, is_active = 0
          WHERE organization_id = ? AND deleted_at IS NULL
        `
        ).run(
          new Date().toISOString(),
          new Date().toISOString(),
          organizationId
        );
        deletedData.products = productResult.changes;
      }

      // 5. Delete organization memberships
      const memberResult = this.getPreparedStatement(
        `
        DELETE FROM organization_members WHERE organization_id = ?
      `
      ).run(org.id);
      deletedData.members = memberResult.changes;

      // 6. Delete organization
      this.getPreparedStatement(
        `
        DELETE FROM organizations WHERE id = ?
      `
      ).run(org.id);

      return { success: true, deletedData };
    });

    return deleteTransaction();
  }

  getOrganizationDataCounts(organizationId: string): OrganizationDataCounts {
    const stmt = this.getPreparedStatement(`
      SELECT 
        (SELECT COUNT(*) FROM colors WHERE organization_id = ?) as colors,
        (SELECT COUNT(*) FROM products WHERE organization_id = ?) as products,
        (SELECT COUNT(*) FROM datasheets WHERE product_id IN (
          SELECT id FROM products WHERE organization_id = ?
        )) as datasheets,
        (SELECT COUNT(*) FROM product_colors WHERE organization_id = ?) as relationships
    `);

    const result = stmt.get(
      organizationId,
      organizationId,
      organizationId,
      organizationId
    ) as OrganizationDataCounts;
    return result;
  }

  // Utility Operations
  getPreparedStatement(sql: string): Database.Statement {
    if (!OrganizationRepository.preparedStatements.has(sql)) {
      OrganizationRepository.preparedStatements.set(sql, this.db.prepare(sql));
    }
    return OrganizationRepository.preparedStatements.get(sql)!;
  }
}
