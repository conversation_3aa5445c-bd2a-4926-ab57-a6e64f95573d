#!/bin/bash

# Verify ChromaSync Database Initialization Script
# This script checks the database state after fresh initialization

set -e

APP_DATA_DIR="$HOME/Library/Application Support/chroma-sync"
DB_FILE="$APP_DATA_DIR/chromasync.db"

echo "🔍 ChromaSync Database Verification"
echo "===================================="

# Check if database exists
if [ ! -f "$DB_FILE" ]; then
    echo "❌ Database file not found: $DB_FILE"
    echo "   Run the application first to initialize the database."
    exit 1
fi

echo "✅ Database file found: $DB_FILE"
echo "📊 Database size: $(ls -lh "$DB_FILE" | awk '{print $5}')"

# Use sqlite3 to check database structure
echo ""
echo "🔍 Checking database structure..."

# Check if sqlite3 is available
if ! command -v sqlite3 &> /dev/null; then
    echo "❌ sqlite3 command not found. Please install SQLite to verify database structure."
    exit 1
fi

# Function to run SQL query and display results
run_query() {
    local description="$1"
    local query="$2"
    echo ""
    echo "📋 $description:"
    sqlite3 "$DB_FILE" "$query" 2>/dev/null || echo "   ❌ Query failed: $query"
}

# Check migrations table
run_query "Applied Migrations" "SELECT version, name FROM schema_migrations ORDER BY version;"

# Check if datasheets table exists and has sync columns
run_query "Datasheets Table Structure" "PRAGMA table_info(datasheets);"

# Check if datasheets table has sync-required columns
echo ""
echo "🔍 Checking Datasheet Sync Support..."

SYNC_COLUMNS=("organization_id" "user_id" "device_id" "deleted_at" "sync_version")
MISSING_COLUMNS=()

for column in "${SYNC_COLUMNS[@]}"; do
    if sqlite3 "$DB_FILE" "PRAGMA table_info(datasheets);" | grep -q "$column"; then
        echo "   ✅ $column column exists"
    else
        echo "   ❌ $column column missing"
        MISSING_COLUMNS+=("$column")
    fi
done

echo ""
if [ ${#MISSING_COLUMNS[@]} -eq 0 ]; then
    echo "✅ All sync columns present - Datasheet sync fully supported!"
else
    echo "⚠️  Missing sync columns: ${MISSING_COLUMNS[*]}"
    echo "   Run migration 024 to add missing columns."
fi

# Check other important tables
run_query "Available Tables" "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"

# Check indexes on datasheets table
run_query "Datasheets Table Indexes" "SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='datasheets';"

echo ""
echo "📊 Database Statistics:"
sqlite3 "$DB_FILE" "
    SELECT 
        'Organizations: ' || COUNT(*) as stat FROM organizations
    UNION ALL
    SELECT 
        'Products: ' || COUNT(*) as stat FROM products  
    UNION ALL
    SELECT 
        'Colors: ' || COUNT(*) as stat FROM colors
    UNION ALL
    SELECT 
        'Datasheets: ' || COUNT(*) as stat FROM datasheets
    UNION ALL
    SELECT 
        'Users: ' || COUNT(*) as stat FROM users;
" 2>/dev/null || echo "   ❌ Error reading database statistics"

echo ""
echo "✅ Database verification complete!"