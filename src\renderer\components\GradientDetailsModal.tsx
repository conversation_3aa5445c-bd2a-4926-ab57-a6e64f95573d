/**
 * @file GradientDetailsModal.tsx
 * @description Reusable modal component for displaying gradient details with CMYK values
 */

import React, { useState, useEffect, useRef } from 'react';
import { GradientStop } from '../../shared/types/color.types';
import { hexToCmyk, formatCMYKForDisplay } from '../../shared/utils/color';

interface GradientDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  gradient: {
    colors: string[];
    type?: 'linear';
    angle?: number;
  };
  product: string;
  colorEntry?: any; // Full color entry for editing
  onEdit?: (colorEntry: any) => void; // Callback to trigger edit mode
}

interface InfoTooltipProps {
  content: string;
}

const InfoTooltip: React.FC<InfoTooltipProps> = ({ content }) => {
  return (
    <span
      className='relative group ml-1'
      tabIndex={0}
      role='button'
      aria-label='Information tooltip'
    >
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='12'
        height='12'
        fill='none'
        viewBox='0 0 24 24'
        stroke='currentColor'
      >
        <path
          strokeLinecap='round'
          strokeLinejoin='round'
          strokeWidth={2}
          d='M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
        />
      </svg>
      <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 rounded hidden group-hover:block group-focus:block z-50 bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 shadow-md text-xs'>
        {content}
      </div>
    </span>
  );
};

export const GradientDetailsModal: React.FC<GradientDetailsModalProps> = ({
  isOpen,
  gradient,
  product,
  colorEntry,
  onEdit,
  onClose,
}) => {
  // Token system is always enabled, no feature flag needed
  const [copiedField, setCopiedField] = useState<{
    index: number;
    field: 'hex' | 'cmyk' | 'colorCode';
  } | null>(null);
  const [displayStops, setDisplayStops] = useState<GradientStop[]>([]);
  const [isVisible, setIsVisible] = useState<boolean>(false);

  // Create ref for the modal dialog to manage focus
  const modalRef = useRef<HTMLDivElement>(null);
  const initialFocusRef = useRef<HTMLButtonElement>(null);

  // Handle modal visibility with animation
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      return undefined;
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 200); // Match transition duration
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen || !gradient || !gradient.colors) {
      return;
    }

    // Initialize displayStops from simplified gradient format
    const stops = gradient.colors.map((color, index) => ({
      color,
      position:
        gradient.colors.length === 1
          ? 50
          : (index / (gradient.colors.length - 1)) * 100,
      cmyk: generateCmykFromHex(color),
      colorCode: (gradient as any).colorCodes?.[index] || null,
    }));
    setDisplayStops(stops);
  }, [gradient, isOpen]);

  // Generate CMYK string from hex color
  const generateCmykFromHex = (hexColor: string): string => {
    const cmyk = hexToCmyk(hexColor);
    if (cmyk) {
      return formatCMYKForDisplay(cmyk);
    }
    return 'C0, M0, Y0, K100'; // Default to black if conversion fails
  };

  // Copy value to clipboard
  const copyToClipboard = (
    value: string,
    index: number,
    field: 'hex' | 'cmyk' | 'colorCode'
  ) => {
    navigator.clipboard
      .writeText(value)
      .then(() => {
        setCopiedField({ index, field });
        setTimeout(() => setCopiedField(null), 1500);
      })
      .catch(err => {
        console.error('Could not copy to clipboard: ', err);
      });
  };

  // Handle escape key press
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);

      // Set focus to the close button when modal opens
      if (initialFocusRef.current) {
        initialFocusRef.current.focus();
      }
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  // Add the focus trap effect
  useEffect(() => {
    if (!isOpen || !modalRef.current) {
      return;
    }

    const focusableElements =
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
    const modal = modalRef.current;
    const firstFocusableElement = modal.querySelectorAll(
      focusableElements
    )[0] as HTMLElement;
    const focusableContent = modal.querySelectorAll(focusableElements);
    const lastFocusableElement = focusableContent[
      focusableContent.length - 1
    ] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstFocusableElement) {
            lastFocusableElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastFocusableElement) {
            firstFocusableElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);

    // Save the previously focused element
    const previouslyFocusedElement = document.activeElement as HTMLElement;

    // Focus the first focusable element
    initialFocusRef.current
      ? initialFocusRef.current.focus()
      : firstFocusableElement.focus();

    return () => {
      document.removeEventListener('keydown', handleTabKey);
      // Restore focus to the previously focused element when the modal closes
      if (previouslyFocusedElement) {
        previouslyFocusedElement.focus();
      }
    };
  }, [isOpen]);

  if (!isOpen && !isVisible) {
    return null;
  }

  // Design token-based styling
  const backdropStyles = {
    position: 'fixed' as const,
    inset: 0,
    backgroundColor: 'var(--backdrop-primary)',
    zIndex: 'var(--z-modal)',
    transition: `opacity var(--transition-duration-200) var(--transition-easing-out)`,
    opacity: isOpen ? 1 : 0,
  };

  const modalStyles = {
    position: 'fixed' as const,
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    backgroundColor: 'var(--color-ui-background-primary)',
    zIndex: 'calc(var(--z-modal) + 10)',
    borderRadius: 'var(--radius-lg)',
    boxShadow: 'var(--shadow-xl)',
    border: '1px solid var(--color-ui-border-light)',
    width: '100%',
    maxWidth: '40rem',
    overflow: 'hidden',
    transition: `opacity var(--transition-duration-200) var(--transition-easing-out)`,
    opacity: isOpen ? 1 : 0,
  };

  // Render with token-based styling
  return (
    <>
      <div
        style={backdropStyles}
        onClick={onClose}
        aria-hidden='true'
        tabIndex={-1}
      />
      <div
        ref={modalRef}
        style={modalStyles}
        onClick={e => e.stopPropagation()}
        role='dialog'
        aria-labelledby='gradient-details-title'
        aria-modal='true'
        tabIndex={-1}
      >
        {/* Header */}
        <div
          style={{
            padding: 'var(--spacing-4) var(--spacing-6)',
            borderBottom: '1px solid var(--color-ui-border-light)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <div>
            <h3
              id='gradient-details-title'
              style={{
                fontSize: 'var(--font-size-lg)',
                fontWeight: 'var(--font-weight-medium)',
                color: 'var(--color-ui-foreground-primary)',
                margin: 0,
              }}
            >
              {gradient.type === 'linear' ? 'Linear' : 'Radial'} Gradient
              Details
            </h3>
            <p
              style={{
                fontSize: 'var(--font-size-xs)',
                color: 'var(--color-ui-foreground-secondary)',
                margin: 'var(--spacing-1) 0 0 0',
              }}
            >
              Product: {product || 'Unknown'}
            </p>
          </div>
          <button
            ref={initialFocusRef}
            style={{
              color: 'var(--color-ui-foreground-tertiary)',
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: 'var(--spacing-1)',
              borderRadius: 'var(--radius-md)',
              transition: `color var(--transition-duration-200) var(--transition-easing-out)`,
            }}
            onClick={onClose}
            aria-label='Close gradient details'
            onMouseEnter={e => {
              e.currentTarget.style.color =
                'var(--color-ui-foreground-secondary)';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.color =
                'var(--color-ui-foreground-tertiary)';
            }}
          >
            <svg
              style={{ width: '20px', height: '20px' }}
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div
          style={{
            padding: 'var(--spacing-4) var(--spacing-6)',
            overflowY: 'auto',
            maxHeight: 'calc(80vh - 120px)',
          }}
        >
          {/* Gradient Preview */}
          <div
            style={{
              height: '4rem',
              width: '100%',
              marginBottom: 'var(--spacing-6)',
              borderRadius: 'var(--radius-md)',
              position: 'relative',
              background: `linear-gradient(${gradient.angle || 45}deg, ${gradient.colors
                .map((color, index) => {
                  const position =
                    gradient.colors.length === 1
                      ? 50
                      : (index / (gradient.colors.length - 1)) * 100;
                  return `${color} ${position}%`;
                })
                .join(', ')})`,
            }}
            aria-label='Gradient preview'
          >
            {/* Gradient stop indicators */}
            <div
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
              }}
            >
              <div style={{ position: 'relative', height: '8px' }}>
                {displayStops.map((stop, index) => (
                  <div
                    key={index}
                    style={{
                      position: 'absolute',
                      top: 0,
                      height: '8px',
                      width: '4px',
                      backgroundColor: stop.color,
                      border: '1px solid var(--color-ui-border-medium)',
                      left: `${stop.position}%`,
                    }}
                    title={`Stop at ${stop.position}%`}
                   />
                ))}
              </div>
            </div>
          </div>

          {/* Table header */}
          <div
            style={{
              display: 'flex',
              gap: 'var(--spacing-3)',
              marginBottom: 'var(--spacing-2)',
              padding: `0 var(--spacing-2)`,
              alignItems: 'center',
            }}
          >
            <div style={{ width: '32px' }} />
            <div style={{ width: '56px', flexShrink: 0 }}>
              <h4
                style={{
                  fontSize: 'var(--font-size-xs)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-ui-foreground-tertiary)',
                  margin: 0,
                }}
              >
                Position
              </h4>
            </div>
            <div style={{ width: '80px', flexShrink: 0 }}>
              <h4
                style={{
                  fontSize: 'var(--font-size-xs)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-ui-foreground-tertiary)',
                  margin: 0,
                }}
              >
                HEX
              </h4>
            </div>
            <div style={{ width: '176px', flexShrink: 0 }}>
              <h4
                style={{
                  fontSize: 'var(--font-size-xs)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-ui-foreground-tertiary)',
                  margin: 0,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                CMYK
                <InfoTooltip content='CMYK values represent Cyan, Magenta, Yellow, and Key (Black) percentages used in print production.' />
              </h4>
            </div>
            <div style={{ flex: 1 }}>
              <h4
                style={{
                  fontSize: 'var(--font-size-xs)',
                  fontWeight: 'var(--font-weight-medium)',
                  color: 'var(--color-ui-foreground-tertiary)',
                  margin: 0,
                }}
              >
                Color Code
              </h4>
            </div>
          </div>

          {/* Color stops */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 'var(--spacing-3)',
            }}
          >
            {displayStops.map((stop, index) => (
              <div
                key={index}
                style={{
                  border: '1px solid var(--color-ui-border-light)',
                  borderRadius: 'var(--radius-md)',
                  padding: 'var(--spacing-3)',
                  backgroundColor: 'var(--color-ui-background-secondary)',
                  transition: `background-color var(--transition-duration-200) var(--transition-easing-out)`,
                  cursor: 'default',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor =
                    'var(--color-ui-background-tertiary)';
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor =
                    'var(--color-ui-background-secondary)';
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    gap: 'var(--spacing-3)',
                    alignItems: 'center',
                  }}
                >
                  {/* Color swatch */}
                  <div style={{ width: '32px', flexShrink: 0 }}>
                    <div
                      style={{
                        height: '32px',
                        width: '32px',
                        borderRadius: '50%',
                        border: '1px solid var(--color-ui-border-medium)',
                        backgroundColor: stop.color,
                      }}
                     />
                  </div>

                  {/* Position */}
                  <div style={{ width: '56px', flexShrink: 0 }}>
                    <div
                      style={{
                        fontSize: 'var(--font-size-sm)',
                        color: 'var(--color-ui-foreground-secondary)',
                      }}
                    >
                      {stop.position}%
                    </div>
                  </div>

                  {/* HEX value */}
                  <div style={{ width: '80px', flexShrink: 0 }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <button
                        style={{
                          marginRight: 'var(--spacing-1)',
                          padding: 'var(--spacing-1)',
                          borderRadius: '50%',
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer',
                          flexShrink: 0,
                          transition: `background-color var(--transition-duration-200) var(--transition-easing-out)`,
                        }}
                        onClick={() =>
                          copyToClipboard(stop.color, index, 'hex')
                        }
                        title='Copy HEX value'
                        aria-label={`Copy HEX value: ${stop.color}`}
                        onMouseEnter={e => {
                          e.currentTarget.style.backgroundColor =
                            'var(--color-ui-background-tertiary)';
                        }}
                        onMouseLeave={e => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          style={{
                            height: '16px',
                            width: '16px',
                            color: 'var(--color-ui-foreground-tertiary)',
                          }}
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2'
                          />
                        </svg>
                      </button>
                      <span
                        style={{
                          fontSize: 'var(--font-size-xs)',
                          fontFamily: 'monospace',
                          color: 'var(--color-ui-foreground-primary)',
                        }}
                      >
                        {stop.color.toUpperCase()}
                      </span>
                      {copiedField?.index === index &&
                        copiedField?.field === 'hex' && (
                          <span
                            style={{
                              fontSize: 'var(--font-size-xs)',
                              color: 'var(--color-feedback-success)',
                              marginLeft: 'var(--spacing-1)',
                            }}
                          >
                            Copied!
                          </span>
                        )}
                    </div>
                  </div>

                  {/* CMYK value */}
                  <div style={{ width: '176px', flexShrink: 0 }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <button
                        style={{
                          marginRight: 'var(--spacing-1)',
                          padding: 'var(--spacing-1)',
                          borderRadius: '50%',
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer',
                          flexShrink: 0,
                          transition: `background-color var(--transition-duration-200) var(--transition-easing-out)`,
                        }}
                        onClick={() =>
                          copyToClipboard(stop.cmyk!, index, 'cmyk')
                        }
                        title='Copy CMYK value'
                        aria-label={`Copy CMYK value: ${stop.cmyk}`}
                        onMouseEnter={e => {
                          e.currentTarget.style.backgroundColor =
                            'var(--color-ui-background-tertiary)';
                        }}
                        onMouseLeave={e => {
                          e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                      >
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          style={{
                            height: '16px',
                            width: '16px',
                            color: 'var(--color-ui-foreground-tertiary)',
                          }}
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2'
                          />
                        </svg>
                      </button>
                      <span
                        style={{
                          fontSize: 'var(--font-size-xs)',
                          fontFamily: 'monospace',
                          color: 'var(--color-ui-foreground-primary)',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {stop.cmyk || 'Not set'}
                      </span>
                      {copiedField?.index === index &&
                        copiedField?.field === 'cmyk' && (
                          <span
                            style={{
                              fontSize: 'var(--font-size-xs)',
                              color: 'var(--color-feedback-success)',
                              marginLeft: 'var(--spacing-1)',
                            }}
                          >
                            Copied!
                          </span>
                        )}
                    </div>
                  </div>

                  {/* Color Code value */}
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      {stop.colorCode ? (
                        <>
                          <button
                            style={{
                              marginRight: 'var(--spacing-1)',
                              padding: 'var(--spacing-1)',
                              borderRadius: '50%',
                              background: 'none',
                              border: 'none',
                              cursor: 'pointer',
                              flexShrink: 0,
                              transition: `background-color var(--transition-duration-200) var(--transition-easing-out)`,
                            }}
                            onClick={() =>
                              copyToClipboard(
                                stop.colorCode!,
                                index,
                                'colorCode'
                              )
                            }
                            title='Copy Color Code'
                            aria-label={`Copy Color Code: ${stop.colorCode}`}
                            onMouseEnter={e => {
                              e.currentTarget.style.backgroundColor =
                                'var(--color-ui-background-tertiary)';
                            }}
                            onMouseLeave={e => {
                              e.currentTarget.style.backgroundColor =
                                'transparent';
                            }}
                          >
                            <svg
                              xmlns='http://www.w3.org/2000/svg'
                              style={{
                                height: '16px',
                                width: '16px',
                                color: 'var(--color-ui-foreground-tertiary)',
                              }}
                              fill='none'
                              viewBox='0 0 24 24'
                              stroke='currentColor'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={2}
                                d='M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2'
                              />
                            </svg>
                          </button>
                          <span
                            style={{
                              fontSize: 'var(--font-size-sm)',
                              fontFamily: 'monospace',
                              color: 'var(--color-ui-foreground-primary)',
                            }}
                          >
                            {stop.colorCode}
                          </span>
                          {copiedField?.index === index &&
                            copiedField?.field === 'colorCode' && (
                              <span
                                style={{
                                  fontSize: 'var(--font-size-xs)',
                                  color: 'var(--color-feedback-success)',
                                  marginLeft: 'var(--spacing-1)',
                                }}
                              >
                                Copied!
                              </span>
                            )}
                        </>
                      ) : (
                        <span
                          style={{
                            fontSize: 'var(--font-size-sm)',
                            color: 'var(--color-ui-foreground-tertiary)',
                            fontStyle: 'italic',
                          }}
                        >
                          Not set
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div
          style={{
            padding: 'var(--spacing-4) var(--spacing-6)',
            borderTop: '1px solid var(--color-ui-border-light)',
            backgroundColor: 'var(--color-ui-background-tertiary)',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 'var(--spacing-3)',
          }}
        >
          {/* Edit button - only show if edit callback is provided */}
          {onEdit && colorEntry && (
            <button
              type='button'
              style={{
                padding: 'var(--spacing-2) var(--spacing-4)',
                fontSize: 'var(--font-size-sm)',
                fontWeight: 'var(--font-weight-medium)',
                borderRadius: 'var(--radius-md)',
                backgroundColor: 'var(--color-brand-primary)',
                color: 'white',
                border: 'none',
                cursor: 'pointer',
                transition: `background-color var(--transition-duration-200) var(--transition-easing-out)`,
              }}
              onClick={() => {
                onEdit(colorEntry);
                onClose(); // Close this modal when opening edit mode
              }}
              onMouseEnter={e => {
                e.currentTarget.style.opacity = '0.9';
              }}
              onMouseLeave={e => {
                e.currentTarget.style.opacity = '1';
              }}
            >
              Edit Gradient
            </button>
          )}
          <button
            type='button'
            style={{
              padding: 'var(--spacing-2) var(--spacing-4)',
              fontSize: 'var(--font-size-sm)',
              fontWeight: 'var(--font-weight-medium)',
              borderRadius: 'var(--radius-md)',
              border: '1px solid var(--color-ui-border-medium)',
              color: 'var(--color-ui-foreground-primary)',
              backgroundColor: 'var(--color-ui-background-primary)',
              cursor: 'pointer',
              transition: `background-color var(--transition-duration-200) var(--transition-easing-out)`,
            }}
            onClick={onClose}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor =
                'var(--color-ui-background-secondary)';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor =
                'var(--color-ui-background-primary)';
            }}
          >
            Close
          </button>
        </div>
      </div>
    </>
  );
};

export default GradientDetailsModal;
