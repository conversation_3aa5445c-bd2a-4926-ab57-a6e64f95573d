/**
 * @file ColorTable/index.tsx
 * @description Table component for displaying color entries in a structured format
 */

import { useState, useRef, useCallback, useEffect /*, useMemo*/ } from 'react';
import { ColorEntry } from '../../../shared/types/color.types';
import {
  useColorStore,
  useFilteredColorsAdvanced,
  useGroupedByCode,
} from '../../store/color.store';
import ColorTableRow from './ColorTableRow';
import ColorForm from '../ColorForm';
import GradientPickerModal from '../GradientPickerModal';
import GradientDetailsModal from '../GradientDetailsModal';
import { useTokens } from '../../hooks/useTokens';
import { TableSkeleton } from '../ui/Skeleton';
import {
  isNotNullish,
  safeArrayAccess,
} from '../../../shared/types/type-guards';

interface ColorTableProps {
  view?: 'details' | 'reference';
}

export default function ColorTable({ view = 'details' }: ColorTableProps) {
  const {
    colors: _colors,
    searchQuery,
    isLoading,
    error,
    loadColorsWithUsage,
    usageCounts = {},
  } = useColorStore();
  const [editingEntry, setEditingEntry] = useState<ColorEntry | null>(null);
  const [showGradientModal, setShowGradientModal] = useState<boolean>(false);
  const [showGradientDetails, setShowGradientDetails] =
    useState<ColorEntry | null>(null);
  const [focusedRowIndex, setFocusedRowIndex] = useState<number>(-1);
  const tokens = useTokens();
  const tableRef = useRef<HTMLTableElement>(null);

  // Use memoized selectors from store
  const filteredColors = useFilteredColorsAdvanced();
  const groupedByCode = useGroupedByCode();

  const handleEdit = (entry: ColorEntry | null | undefined) => {
    if (!entry || !isNotNullish(entry)) {
      console.warn('Invalid entry provided to handleEdit');
      return;
    }

    // Check if entry is a gradient
    if (entry.gradient) {
      setEditingEntry(entry);
      setShowGradientModal(true);
    } else {
      setEditingEntry(entry);
    }
  };

  const handleEditSuccess = async (updatedColorId?: string) => {
    console.log('🎯 handleEditSuccess called:', {
      updatedColorId,
      editingEntry: editingEntry?.id,
    });

    try {
      // Store the updated color ID for verification
      const targetColorId = updatedColorId || editingEntry?.id;

      // Clear modal state immediately
      setEditingEntry(null);
      setShowGradientModal(false);

      // Force cache invalidation and refresh after gradient edit
      const { colorCacheService } = await import(
        '../../services/color-cache.service'
      );
      const { useOrganizationStore } = await import(
        '../../store/organization.store'
      );
      const { currentOrganization } = useOrganizationStore.getState();
      const currentOrg = currentOrganization;

      if (currentOrg?.id) {
        console.log('🧹 Invalidating cache for organization:', currentOrg.id);
        colorCacheService.invalidateOrganization(currentOrg.id);

        // Also invalidate all cache to be safe
        colorCacheService.invalidateAll();
      }

      // Add a small delay to ensure any pending operations complete
      await new Promise(resolve => setTimeout(resolve, 200));

      // Force refresh of colors after gradient edit
      console.log('🔄 Reloading colors with usage...');
      await loadColorsWithUsage();

      // Verify the updated color is present in the new data
      if (targetColorId) {
        console.log('🔍 Verifying updated color in fresh data...');

        // Give the store a moment to update
        await new Promise(resolve => setTimeout(resolve, 100));

        // Check if the updated color is in the current colors array
        const { colors } = useColorStore.getState();
        const updatedColor = colors?.find(c => c?.id === targetColorId);

        if (updatedColor && isNotNullish(updatedColor)) {
          console.log('✅ Updated color found in fresh data:', {
            id: updatedColor.id,
            name: updatedColor.name,
            isGradient: updatedColor.gradient ? 'YES' : 'NO',
            gradientColors: updatedColor.gradient?.colors?.length || 0,
          });
        } else {
          console.warn('⚠️ Updated color not found in fresh data');
        }
      }

      console.log('✅ handleEditSuccess completed successfully');
    } catch (error) {
      console.error('❌ Error in handleEditSuccess:', error);

      // Still try to refresh the UI even if verification fails
      try {
        await loadColorsWithUsage();
      } catch (fallbackError) {
        console.error('❌ Fallback refresh failed:', fallbackError);
      }
    }
  };

  const handleEditCancel = () => {
    setEditingEntry(null);
    setShowGradientModal(false);
  };

  const handleShowGradientDetails = (entry: ColorEntry | null | undefined) => {
    if (!entry || !isNotNullish(entry)) {
      console.warn('Invalid entry provided to handleShowGradientDetails');
      return;
    }
    setShowGradientDetails(entry);
  };

  const handleGradientEdit = (entry: ColorEntry | null | undefined) => {
    if (!entry || !isNotNullish(entry)) {
      console.warn('Invalid entry provided to handleGradientEdit');
      return;
    }
    setShowGradientDetails(null); // Close details modal
    setEditingEntry(entry); // Set editing entry
    setShowGradientModal(true); // Open gradient editor
  };

  // Keyboard navigation handler with null safety
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTableElement>) => {
      const rows = tableRef.current?.querySelectorAll('tbody tr');
      if (!rows || rows.length === 0) {
        return;
      }

      const maxIndex = rows.length - 1;
      let newIndex = focusedRowIndex;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          newIndex =
            focusedRowIndex < maxIndex ? focusedRowIndex + 1 : focusedRowIndex;
          break;
        case 'ArrowUp':
          e.preventDefault();
          newIndex = focusedRowIndex > 0 ? focusedRowIndex - 1 : 0;
          break;
        case 'Home':
          e.preventDefault();
          newIndex = 0;
          break;
        case 'End':
          e.preventDefault();
          newIndex = maxIndex;
          break;
        case 'Enter':
        case ' ':
          e.preventDefault();
          if (focusedRowIndex >= 0 && focusedRowIndex <= maxIndex) {
            const focusedRow = safeArrayAccess(
              Array.from(rows),
              focusedRowIndex
            );
            const editButton = focusedRow?.querySelector(
              '[data-action="edit"]'
            ) as HTMLButtonElement | null;
            editButton?.click();
          }
          break;
        case 'Delete':
          e.preventDefault();
          if (focusedRowIndex >= 0 && focusedRowIndex <= maxIndex) {
            const focusedRow = safeArrayAccess(
              Array.from(rows),
              focusedRowIndex
            );
            const deleteButton = focusedRow?.querySelector(
              '[data-action="delete"]'
            ) as HTMLButtonElement | null;
            deleteButton?.click();
          }
          break;
      }

      if (newIndex !== focusedRowIndex) {
        setFocusedRowIndex(newIndex);
        // Focus the row with null safety
        const newRow = safeArrayAccess(Array.from(rows), newIndex) as
          | HTMLTableRowElement
          | undefined;
        newRow?.focus();
        // Scroll into view if needed
        newRow?.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
      }
    },
    [focusedRowIndex]
  );

  // Set initial focus when table receives focus
  const handleTableFocus = useCallback(() => {
    if (focusedRowIndex === -1 && filteredColors.length > 0) {
      setFocusedRowIndex(0);
    }
  }, [focusedRowIndex, filteredColors.length]);

  // Cleanup large data structures and state when component unmounts
  useEffect(() => {
    return () => {
      setFocusedRowIndex(-1);
      setEditingEntry(null);
      setShowGradientModal(false);
      setShowGradientDetails(null);
    };
  }, []);

  // Reset focus when filtered colors change significantly
  useEffect(() => {
    if (filteredColors.length === 0 && focusedRowIndex !== -1) {
      setFocusedRowIndex(-1);
    } else if (focusedRowIndex >= filteredColors.length) {
      setFocusedRowIndex(Math.max(0, filteredColors.length - 1));
    }
  }, [filteredColors.length, focusedRowIndex]);

  // Get header classes using token system (matching PantoneTable)
  const getHeaderClasses = () => {
    return 'bg-brand-primary dark:bg-brand-primary text-ui-foreground-inverse py-3 px-4 text-xs font-semibold text-left';
  };

  // Get modal overlay classes using token system (matching PantoneTable)
  const getModalOverlayClasses = () => {
    return 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[var(--zIndex-modal)]';
  };

  // Get modal content classes using token system (matching PantoneTable)
  const getModalContentClasses = () => {
    return 'bg-ui-background-primary dark:bg-ui-background-primary p-[var(--spacing-6)] rounded-[var(--radius-lg)] max-w-xl w-full';
  };

  // Get table container classes using token system (matching PantoneTable)
  const getTableContainerClasses = () => {
    return 'bg-ui-background-primary dark:bg-ui-background-tertiary rounded-[var(--radius-lg)] shadow-md overflow-hidden';
  };

  // @ts-ignore - Intentionally unused
  // Get border classes using token system (matching PantoneTable)
  const _getBorderClasses = () => {
    return 'border-b border-ui-border-light dark:border-ui-border-dark';
  };

  // Modal transition styles
  const modalTransitionStyle = {
    animation: `fadeIn ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
  };

  // Modal content transition styles
  const modalContentTransitionStyle = {
    animation: `scaleIn ${tokens.transitions.duration[300]} ${tokens.transitions.easing.apple}`,
  };

  // Define CSS animation classes with memory leak prevention
  useEffect(() => {
    // Check if styles already exist to prevent duplicates
    const existingStyle = document.getElementById('color-table-animations');
    if (existingStyle) {
      return; // Styles already exist, no cleanup needed
    }

    // Create a style element with unique ID
    const styleEl = document.createElement('style');
    styleEl.id = 'color-table-animations';
    styleEl.innerHTML = `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      @keyframes scaleIn {
        from { transform: scale(0.95); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }
    `;

    // Append to document head
    document.head.appendChild(styleEl);

    // Clean up when component unmounts, but only if we created it
    return () => {
      if (document.getElementById('color-table-animations')) {
        document.head.removeChild(styleEl);
      }
    };
  }, []);

  return (
    <div className='relative table-view-container'>
      <div className={getTableContainerClasses()}>
        {isLoading ? (
          <div className='p-4'>
            <TableSkeleton rows={8} columns={view === 'reference' ? 7 : 6} />
          </div>
        ) : error ? (
          <div className='p-4 text-center'>
            <p className='text-feedback-error'>Error: {error}</p>
          </div>
        ) : filteredColors.length === 0 ? (
          <div className='p-4 text-center'>
            <p className='text-ui-foreground-secondary'>
              {searchQuery
                ? 'No colours match your search criteria.'
                : 'No colours found. Add some colours to get started!'}
            </p>
          </div>
        ) : (
          <div className='overflow-x-auto pb-4 max-h-[calc(100vh-var(--spacing-20)-var(--spacing-16)-var(--spacing-12))] scrollable-content'>
            <table
              ref={tableRef}
              className='w-full table-fixed bg-ui-background-primary dark:bg-ui-background-tertiary border border-ui-border-light dark:border-ui-border-dark rounded-[var(--radius-lg)] overflow-hidden'
              role='table'
              tabIndex={0}
              onKeyDown={handleKeyDown}
              onFocus={handleTableFocus}
              aria-label='Color entries table'
            >
              <thead>
                <tr>
                  {view === 'reference' ? (
                    <>
                      <th className={`${getHeaderClasses()  } w-[18%]`}>Code</th>
                      <th className={`${getHeaderClasses()  } w-[18%]`}>Name</th>
                      <th
                        className={`${getHeaderClasses()} text-center w-[12%]`}
                      >
                        Hex
                      </th>
                      <th className={`${getHeaderClasses()  } w-[18%]`}>CMYK</th>
                      <th className={`${getHeaderClasses()  } w-[12%]`}>RGB</th>
                      <th
                        className={`${getHeaderClasses()} text-center w-[12%]`}
                      >
                        Usage
                      </th>
                      <th className={`${getHeaderClasses()  } w-[10%]`}>
                        Actions
                      </th>
                    </>
                  ) : (
                    <>
                      <th className={`${getHeaderClasses()  } w-[12%]`}>
                        Product
                      </th>
                      <th className={`${getHeaderClasses()  } w-[12%]`}>Name</th>
                      <th className={`${getHeaderClasses()  } w-[12%]`}>Code</th>
                      <th
                        className={`${getHeaderClasses()} text-center w-[12%]`}
                      >
                        Hex
                      </th>
                      <th className={`${getHeaderClasses()  } w-[18%]`}>CMYK</th>
                      <th className={`${getHeaderClasses()  } w-[25%]`}>Notes</th>
                      <th className={`${getHeaderClasses()  } w-[9%]`}>
                        Actions
                      </th>
                    </>
                  )}
                </tr>
              </thead>
              <tbody>
                {view === 'reference'
                  ? // Show grouped colors by code with usage count
                    groupedByCode.map((group, index) => (
                      <ColorTableRow
                        key={group.color.code}
                        entry={group.color}
                        onEdit={handleEdit}
                        onShowGradientDetails={handleShowGradientDetails}
                        view={view}
                        usageCount={group.usageCount}
                        usedInProducts={group.products}
                        isFocused={focusedRowIndex === index}
                        onFocus={() => setFocusedRowIndex(index)}
                      />
                    ))
                  : // Default details view
                    filteredColors.map((color, index) => {
                      // Get usage data for this color with null safety
                      const usageKey = color?.id;
                      const usageData = (usageKey && usageCounts[usageKey]) || {
                        count: 0,
                        products: [],
                      };

                      return (
                        <ColorTableRow
                          key={color.id}
                          entry={color}
                          onEdit={handleEdit}
                          onShowGradientDetails={handleShowGradientDetails}
                          view={view}
                          usageCount={usageData.count}
                          usedInProducts={usageData.products}
                          isFocused={focusedRowIndex === index}
                          onFocus={() => setFocusedRowIndex(index)}
                        />
                      );
                    })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Edit modal for flat colors */}
      {editingEntry && !showGradientModal && (
        <div className={getModalOverlayClasses()} style={modalTransitionStyle}>
          <div
            className={getModalContentClasses()}
            style={modalContentTransitionStyle}
          >
            <h2 className='text-xl font-bold mb-4 text-ui-foreground-primary dark:text-ui-foreground-inverse'>
              Edit Color
            </h2>
            <ColorForm
              editMode={true}
              color={editingEntry}
              onSuccess={handleEditSuccess}
              onCancel={handleEditCancel}
            />
          </div>
        </div>
      )}

      {/* Edit modal for gradients */}
      {editingEntry && showGradientModal && (
        <GradientPickerModal
          isOpen={showGradientModal}
          onClose={handleEditCancel}
          onSuccess={handleEditSuccess}
          editMode={true}
          color={editingEntry}
          initialValue={editingEntry.gradient}
          productName={editingEntry.product}
        />
      )}

      {/* Gradient details modal */}
      {showGradientDetails && showGradientDetails.gradient && (
        <GradientDetailsModal
          isOpen={true}
          onClose={() => setShowGradientDetails(null)}
          gradient={{
            colors: showGradientDetails.gradient?.colors || [
              '#0077CC',
              '#FFFFFF',
            ],
            type: 'linear',
            angle: 45,
          }}
          product={showGradientDetails.product}
          colorEntry={showGradientDetails}
          onEdit={handleGradientEdit}
        />
      )}
    </div>
  );
}
