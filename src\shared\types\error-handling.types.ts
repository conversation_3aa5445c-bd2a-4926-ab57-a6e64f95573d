/**
 * @file error-handling.types.ts
 * @description Advanced error handling with discriminated unions and sophisticated type safety
 * Implements cutting-edge TypeScript patterns for robust error management
 */

<<<<<<< HEAD
import type {
  Brand,
  UUID,
=======
import type { 
  Brand, 
  UUID,
  ApiResponse
>>>>>>> main
} from './advanced-utilities.types';

// Define missing types locally
export type OrganizationId = Brand<string, 'OrganizationId'>;
export type UserId = Brand<string, 'UserId'>;

// ===== BRANDED ERROR TYPES =====

export type ErrorCode = Brand<string, 'ErrorCode'>;
export type ErrorMessage = Brand<string, 'ErrorMessage'>;
export type StackTrace = Brand<string, 'StackTrace'>;
export type ErrorId = Brand<string, 'ErrorId'>;

// ===== ERROR SEVERITY AND CATEGORIES =====

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';
export type ErrorCategory =
  | 'validation'
  | 'network'
  | 'database'
  | 'authentication'
  | 'authorization'
  | 'sync'
  | 'filesystem'
  | 'runtime'
  | 'configuration'
  | 'business_logic'
  | 'integration'
  | 'performance';

// ===== BASE ERROR INTERFACE =====

export interface BaseError {
  readonly id: ErrorId;
  readonly code: ErrorCode;
  readonly message: ErrorMessage;
  readonly severity: ErrorSeverity;
  readonly category: ErrorCategory;
  readonly timestamp: string;
  readonly stackTrace?: StackTrace;
  readonly userId?: string;
  readonly organizationId?: string;
  readonly context?: Record<string, unknown>;
  readonly correlationId?: UUID;
}

// ===== DISCRIMINATED UNION ERROR TYPES =====

/**
 * Validation error with specific field information
 */
export interface ValidationError extends BaseError {
  readonly category: 'validation';
  readonly field: string;
  readonly value: unknown;
  readonly constraint: string;
  readonly allowedValues?: readonly unknown[];
}

/**
 * Network error with connection details
 */
export interface NetworkError extends BaseError {
  readonly category: 'network';
  readonly url?: string;
  readonly method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  readonly status?: number;
  readonly timeout?: number;
  readonly retryCount?: number;
  readonly isOnline: boolean;
}

/**
 * Database error with query information
 */
export interface DatabaseError extends BaseError {
  readonly category: 'database';
  readonly table?: string;
  readonly operation:
    | 'SELECT'
    | 'INSERT'
    | 'UPDATE'
    | 'DELETE'
    | 'MIGRATION'
    | 'BACKUP';
  readonly constraint?: string;
  readonly affectedRows?: number;
  readonly transactionId?: string;
}

/**
 * Authentication error with credential information
 */
export interface AuthenticationError extends BaseError {
  readonly category: 'authentication';
  readonly provider?: 'google' | 'microsoft' | 'local' | 'api_key';
  readonly tokenExpired?: boolean;
  readonly refreshAvailable?: boolean;
  readonly redirectUrl?: string;
}

/**
 * Authorization error with permission details
 */
export interface AuthorizationError extends BaseError {
  readonly category: 'authorization';
  readonly resource: string;
  readonly action: string;
  readonly requiredRole?: string;
  readonly currentRole?: string;
  readonly permissions?: readonly string[];
}

/**
 * Sync error with synchronization context
 */
export interface SyncError extends BaseError {
  readonly category: 'sync';
  readonly operation: 'upload' | 'download' | 'merge' | 'conflict_resolution';
  readonly localVersion?: number;
  readonly remoteVersion?: number;
  readonly conflictingFields?: readonly string[];
  readonly autoResolvable?: boolean;
}

/**
 * Filesystem error with path information
 */
export interface FilesystemError extends BaseError {
  readonly category: 'filesystem';
  readonly path: string;
  readonly operation: 'read' | 'write' | 'delete' | 'create' | 'move' | 'copy';
  readonly permissions?: string;
  readonly diskSpace?: number;
  readonly fileSize?: number;
}

/**
 * Runtime error with execution context
 */
export interface RuntimeError extends BaseError {
  readonly category: 'runtime';
  readonly function?: string;
  readonly module?: string;
  readonly line?: number;
  readonly column?: number;
  readonly memoryUsage?: number;
}

/**
 * Configuration error with setting details
 */
export interface ConfigurationError extends BaseError {
  readonly category: 'configuration';
  readonly setting: string;
  readonly expectedType: string;
  readonly actualType: string;
  readonly defaultValue?: unknown;
  readonly configPath?: string;
}

/**
 * Business logic error with domain context
 */
export interface BusinessLogicError extends BaseError {
  readonly category: 'business_logic';
  readonly domain:
    | 'color'
    | 'product'
    | 'organization'
    | 'user'
    | 'sync'
    | 'gradient';
  readonly rule: string;
  readonly violatedConstraint: string;
  readonly suggestedAction?: string;
}

/**
 * Integration error with external service details
 */
export interface IntegrationError extends BaseError {
  readonly category: 'integration';
  readonly service:
    | 'supabase'
    | 'google_auth'
    | 'microsoft_auth'
    | 'file_system';
  readonly endpoint?: string;
  readonly serviceVersion?: string;
  readonly rateLimited?: boolean;
  readonly retryAfter?: number;
}

/**
 * Performance error with metrics
 */
export interface PerformanceError extends BaseError {
  readonly category: 'performance';
  readonly operation: string;
  readonly duration: number;
  readonly threshold: number;
  readonly memoryDelta?: number;
  readonly cpuUsage?: number;
  readonly optimizationSuggestion?: string;
}

// ===== DISCRIMINATED UNION TYPE =====

/**
 * Complete error union type with discriminated unions
 */
export type ApplicationError =
  | ValidationError
  | NetworkError
  | DatabaseError
  | AuthenticationError
  | AuthorizationError
  | SyncError
  | FilesystemError
  | RuntimeError
  | ConfigurationError
  | BusinessLogicError
  | IntegrationError
  | PerformanceError;

// ===== ERROR RESULT TYPES =====

/**
 * Result type with error handling
 */
export type Result<
  TSuccess,
  TError extends ApplicationError = ApplicationError,
> =
  | { readonly success: true; readonly data: TSuccess; readonly error?: never }
  | { readonly success: false; readonly data?: never; readonly error: TError };

/**
 * Async result type
 */
export type AsyncResult<
  TSuccess,
  TError extends ApplicationError = ApplicationError,
> = Promise<Result<TSuccess, TError>>;

/**
 * Multiple errors result type
 */
export type MultiErrorResult<
  TSuccess,
  TError extends ApplicationError = ApplicationError,
> =
  | { readonly success: true; readonly data: TSuccess; readonly errors?: never }
  | {
      readonly success: false;
      readonly data?: never;
      readonly errors: readonly TError[];
    };

// ===== ERROR FACTORY FUNCTIONS =====

/**
 * Base error factory with common properties
 */
function createBaseError(
  category: ErrorCategory,
  code: string,
  message: string,
  severity: ErrorSeverity = 'medium',
  context?: Record<string, unknown>
): BaseError {
  return {
    id: crypto.randomUUID() as ErrorId,
    code: code as ErrorCode,
    message: message as ErrorMessage,
    severity,
    category,
    timestamp: new Date().toISOString(),
    context,
    correlationId: crypto.randomUUID() as UUID,
  };
}

/**
 * Validation error factory
 */
export function createValidationError(
  field: string,
  value: unknown,
  constraint: string,
  allowedValues?: readonly unknown[],
  context?: Record<string, unknown>
): ValidationError {
  return {
    ...createBaseError(
      'validation',
      'VALIDATION_FAILED',
      `Validation failed for field: ${field}`,
      'medium',
      context
    ),
    category: 'validation',
    field,
    value,
    constraint,
    allowedValues,
  };
}

/**
 * Network error factory
 */
export function createNetworkError(
  url: string,
  method: NetworkError['method'] = 'GET',
  status?: number,
  context?: Record<string, unknown>
): NetworkError {
  return {
    ...createBaseError(
      'network',
      'NETWORK_ERROR',
      `Network request failed: ${method} ${url}`,
      'high',
      context
    ),
    category: 'network',
    url,
    method,
    status,
    isOnline: navigator.onLine,
  };
}

/**
 * Database error factory
 */
export function createDatabaseError(
  operation: DatabaseError['operation'],
  table?: string,
  context?: Record<string, unknown>
): DatabaseError {
  return {
    ...createBaseError(
      'database',
      'DATABASE_ERROR',
      `Database ${operation} operation failed`,
      'high',
      context
    ),
    category: 'database',
    operation,
    table,
  };
}

/**
 * Authentication error factory
 */
export function createAuthenticationError(
  provider?: AuthenticationError['provider'],
  tokenExpired = false,
  context?: Record<string, unknown>
): AuthenticationError {
  return {
    ...createBaseError(
      'authentication',
      'AUTH_FAILED',
      'Authentication failed',
      'high',
      context
    ),
    category: 'authentication',
    provider,
    tokenExpired,
    refreshAvailable: tokenExpired,
  };
}

/**
 * Business logic error factory
 */
export function createBusinessLogicError(
  domain: BusinessLogicError['domain'],
  rule: string,
  violatedConstraint: string,
  context?: Record<string, unknown>
): BusinessLogicError {
  return {
    ...createBaseError(
      'business_logic',
      'BUSINESS_RULE_VIOLATION',
      `Business rule violated: ${rule}`,
      'medium',
      context
    ),
    category: 'business_logic',
    domain,
    rule,
    violatedConstraint,
  };
}

// ===== TYPE GUARDS =====

/**
 * Type guard for validation errors
 */
export function isValidationError(
  error: ApplicationError
): error is ValidationError {
  return error.category === 'validation';
}

/**
 * Type guard for network errors
 */
export function isNetworkError(error: ApplicationError): error is NetworkError {
  return error.category === 'network';
}

/**
 * Type guard for database errors
 */
export function isDatabaseError(
  error: ApplicationError
): error is DatabaseError {
  return error.category === 'database';
}

/**
 * Type guard for authentication errors
 */
export function isAuthenticationError(
  error: ApplicationError
): error is AuthenticationError {
  return error.category === 'authentication';
}

/**
 * Type guard for authorization errors
 */
export function isAuthorizationError(
  error: ApplicationError
): error is AuthorizationError {
  return error.category === 'authorization';
}

/**
 * Type guard for sync errors
 */
export function isSyncError(error: ApplicationError): error is SyncError {
  return error.category === 'sync';
}

/**
 * Type guard for business logic errors
 */
export function isBusinessLogicError(
  error: ApplicationError
): error is BusinessLogicError {
  return error.category === 'business_logic';
}

// ===== ERROR UTILITIES =====

/**
 * Extract error category from application error
 */
export function getErrorCategory(error: ApplicationError): ErrorCategory {
  return error.category;
}

/**
 * Check if error is retryable
 */
export function isRetryableError(error: ApplicationError): boolean {
  if (isNetworkError(error)) {
    return !error.status || error.status >= 500 || error.status === 429;
  }
  if (isDatabaseError(error)) {
    return (
      error.code === ('CONNECTION_LOST' as ErrorCode) ||
      error.code === ('TIMEOUT' as ErrorCode)
    );
  }
  if (isAuthenticationError(error)) {
    return error.tokenExpired === true && error.refreshAvailable === true;
  }
  return false;
}

/**
 * Get error severity level
 */
export function getErrorSeverity(error: ApplicationError): ErrorSeverity {
  return error.severity;
}

/**
 * Format error for display
 */
export function formatErrorForDisplay(error: ApplicationError): string {
  const prefix = `[${error.category.toUpperCase()}]`;
  const suffix = error.code ? ` (${error.code})` : '';
  return `${prefix} ${error.message}${suffix}`;
}

/**
 * Convert JavaScript Error to ApplicationError
 */
export function fromJavaScriptError(
  jsError: Error,
  category: ErrorCategory = 'runtime'
): RuntimeError {
  return {
    ...createBaseError(category, 'JS_ERROR', jsError.message, 'high'),
    category: 'runtime',
    stackTrace: jsError.stack as StackTrace,
    function: jsError.name,
  };
}

/**
 * Aggregate multiple errors into a summary
 */
export function aggregateErrors(errors: readonly ApplicationError[]): {
  readonly total: number;
  readonly byCategory: Record<ErrorCategory, number>;
  readonly bySeverity: Record<ErrorSeverity, number>;
  readonly critical: readonly ApplicationError[];
} {
  const byCategory: Record<string, number> = {};
  const bySeverity: Record<string, number> = {};
  const critical: ApplicationError[] = [];

  for (const error of errors) {
    byCategory[error.category] = (byCategory[error.category] || 0) + 1;
    bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;

    if (error.severity === 'critical') {
      critical.push(error);
    }
  }

  return {
    total: errors.length,
    byCategory: byCategory as Record<ErrorCategory, number>,
    bySeverity: bySeverity as Record<ErrorSeverity, number>,
    critical,
  };
}

// ===== SAFE OPERATION WRAPPERS =====

/**
 * Safe async operation wrapper
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  errorCategory: ErrorCategory = 'runtime'
): AsyncResult<T> {
  try {
    const data = await operation();
    return { success: true, data };
  } catch (error) {
    const appError =
      error instanceof Error
        ? fromJavaScriptError(error, errorCategory)
        : (createBaseError(
            errorCategory,
            'UNKNOWN_ERROR',
            'Unknown error occurred',
            'high'
          ) as RuntimeError);

    return { success: false, error: appError };
  }
}

/**
 * Safe sync operation wrapper
 */
export function safe<T>(
  operation: () => T,
  errorCategory: ErrorCategory = 'runtime'
): Result<T> {
  try {
    const data = operation();
    return { success: true, data };
  } catch (error) {
    const appError =
      error instanceof Error
        ? fromJavaScriptError(error, errorCategory)
        : (createBaseError(
            errorCategory,
            'UNKNOWN_ERROR',
            'Unknown error occurred',
            'high'
          ) as RuntimeError);

    return { success: false, error: appError };
  }
}

/**
 * Chain operations with error propagation
 */
export function chain<T1, T2>(
  result1: Result<T1>,
  operation: (data: T1) => Result<T2>
): Result<T2> {
  if (!result1.success) {
    return result1;
  }
  return operation(result1.data);
}

/**
 * Async chain operations with error propagation
 */
export async function chainAsync<T1, T2>(
  result1: AsyncResult<T1>,
  operation: (data: T1) => AsyncResult<T2>
): AsyncResult<T2> {
  const resolved1 = await result1;
  if (!resolved1.success) {
    return resolved1;
  }
  return operation(resolved1.data);
}

// Export all types for module compatibility
export {};
