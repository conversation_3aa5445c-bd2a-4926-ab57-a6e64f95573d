#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to verify that the organization fix is working
 */

const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

// Database path
const dbPath = path.join(os.homedir(), 'Library', 'Application Support', 'Electron', 'chromasync.db');

console.log('ChromaSync Organization Fix Verification');
console.log('=======================================');
console.log(`Database path: ${dbPath}`);

try {
  const db = new Database(dbPath, { readonly: true });
  
  console.log('\n1. Testing organization query for user bdcfcd21-ddb3-4386-bfb3-e6d5906babbf...');
  
  // This is the exact query used by OrganizationService.getOrganizationsForUser()
  const organizationsQuery = `
    SELECT o.*, om.role as user_role, COUNT(om2.user_id) as member_count
    FROM organizations o
    JOIN organization_members om ON o.id = om.organization_id
    LEFT JOIN organization_members om2 ON o.id = om2.organization_id
    WHERE om.user_id = ?
    GROUP BY o.id
  `;
  
  const userId = 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf';
  const organizations = db.prepare(organizationsQuery).all(userId);
  
  console.log(`✓ Found ${organizations.length} organizations for user:`);
  organizations.forEach(org => {
    console.log(`  - ${org.name} (${org.slug})`);
    console.log(`    External ID: ${org.external_id}`);
    console.log(`    Role: ${org.user_role}`);
    console.log(`    Members: ${org.member_count}`);
    console.log(`    Plan: ${org.plan}`);
  });
  
  // Check if the user exists in users table now
  console.log('\n2. Checking user profile...');
  const user = db.prepare(`
    SELECT * FROM users WHERE external_id = ? OR id = ?
  `).get(userId, userId);
  
  if (user) {
    console.log(`✓ User profile exists:`);
    console.log(`  Email: ${user.email}`);
    console.log(`  Name: ${user.name}`);
    console.log(`  ID: ${user.external_id || user.id}`);
  } else {
    console.log(`❌ User profile still missing`);
  }
  
  // Check what should happen during authentication
  console.log('\n3. Simulating authentication flow...');
  
  if (organizations.length === 0) {
    console.log(`🟡 Status: needs_organization_setup (no organizations)`);
  } else if (organizations.length === 1) {
    console.log(`🟢 Status: authenticated (single org auto-selected)`);
    console.log(`   Current org would be: ${organizations[0].name} (${organizations[0].external_id})`);
  } else {
    console.log(`🟡 Status: needs_organization_selection (multiple orgs)`);
    console.log(`   User should see organization selection screen with:`);
    organizations.forEach(org => {
      console.log(`     - ${org.name}`);
    });
  }
  
  // Check what the auth service expects
  console.log('\n4. Expected authentication result...');
  console.log(`<NAME_EMAIL> should now:`);
  if (organizations.length === 1) {
    console.log(`  - Be automatically logged into ${organizations[0].name}`);
    console.log(`  - Skip the organization setup screen`);
    console.log(`  - Go directly to the main app interface`);
  } else if (organizations.length > 1) {
    console.log(`  - See an organization selection screen`);
    console.log(`  - Be able to choose between ${organizations.length} organizations`);
  }
  
  db.close();
  
} catch (error) {
  console.error('Error:', error.message);
}