/**
 * @file TableControls.tsx
 * @description Table control buttons (theme toggle, actions menu)
 * Sync functionality moved to UnifiedSyncIndicator
 */

import React, { useState, useRef, useEffect } from 'react';
import { Settings } from 'lucide-react';
import { useTokens } from '../../hooks/useTokens';
// import { useColorStore } from '../../store/color.store';
import { useTheme } from '../../context/ThemeContext';
import { SettingsModalWithSuspense } from '../../utils/lazyComponents';

/**
 * Table controls component for theme and actions
 */
export const TableControls: React.FC = () => {
  const tokens = useTokens();
  // const { importColors, exportColors, clearColors } = useColorStore();
  const { mode, setMode } = useTheme();
  const [menuOpen, setMenuOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Control button styling
  const controlButtonClass = "p-[var(--spacing-2)] rounded-[var(--radius-md)] bg-ui-background-tertiary dark:bg-ui-background-tertiary text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-secondary dark:hover:bg-ui-background-tertiary/80 transition-colors";

  // Theme toggle handler
  const handleThemeToggle = () => {
    console.log('Toggling theme from', mode, 'to', mode === 'dark' ? 'light' : 'dark');
    setMode(mode === 'dark' ? 'light' : 'dark');
  };

  // Outside click handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    };

    if (menuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuOpen]);

  // Menu item handler
  const handleMenuAction = (action: () => void) => {
    action();
    setMenuOpen(false);
  };

  return (
    <div className="flex items-center gap-2">
      {/* Theme toggle */}
      <button
        onClick={handleThemeToggle}
        className={controlButtonClass}
        title={mode === 'dark' ? "Switch to light theme" : "Switch to dark theme"}
        aria-label={mode === 'dark' ? "Switch to light theme" : "Switch to dark theme"}
      >
        {mode === 'dark' ? (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        ) : (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        )}
      </button>

      {/* Settings menu */}
      <div className="relative" ref={menuRef}>
        <button
          onClick={() => setMenuOpen(!menuOpen)}
          className={controlButtonClass}
          title="More options"
          aria-label="More options"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>

        {/* Dropdown menu */}
        {menuOpen && (
          <div 
            className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-ui-background-primary ring-1 ring-black ring-opacity-5 z-10"
            style={{
              boxShadow: tokens.shadows.lg
            }}
          >
            <div className="py-1" role="menu" aria-orientation="vertical">
              <button
                onClick={() => handleMenuAction(() => setSettingsOpen(true))}
                className="flex items-center w-full px-4 py-2 text-sm text-ui-foreground-primary hover:bg-ui-background-secondary"
                role="menuitem"
              >
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Settings Modal */}
      {settingsOpen && (
        <SettingsModalWithSuspense
          isOpen={settingsOpen}
          onClose={() => setSettingsOpen(false)}
        />
      )}
    </div>
  );
};

export default TableControls;
