import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { ColorBlindnessType } from '../../shared/utils/color/colorBlindness';

interface ColorBlindnessContextType {
  mode: ColorBlindnessType;
  setMode: (mode: ColorBlindnessType) => void;
  isEnabled: boolean;
  setIsEnabled: (enabled: boolean) => void;
}

const ColorBlindnessContext = createContext<
  ColorBlindnessContextType | undefined
>(undefined);

export function ColorBlindnessProvider({ children }: { children: ReactNode }) {
  const [mode, setMode] = useState<ColorBlindnessType>('normal');
  const [isEnabled, setIsEnabled] = useState(false);

  // Load saved preferences
  useEffect(() => {
    const savedMode = localStorage.getItem(
      'colorBlindnessMode'
    ) as ColorBlindnessType;
    const savedEnabled =
      localStorage.getItem('colorBlindnessEnabled') === 'true';

    if (savedMode) {
      setMode(savedMode);
    }
    setIsEnabled(savedEnabled);
  }, []);

  // Save preferences
  useEffect(() => {
    localStorage.setItem('colorBlindnessMode', mode);
    localStorage.setItem('colorBlindnessEnabled', String(isEnabled));
  }, [mode, isEnabled]);

  const value = {
    mode: isEnabled ? mode : 'normal',
    setMode,
    isEnabled,
    setIsEnabled,
  };

  return (
    <ColorBlindnessContext.Provider value={value}>
      {children}
    </ColorBlindnessContext.Provider>
  );
}

export function useColorBlindness() {
  const context = useContext(ColorBlindnessContext);
  if (context === undefined) {
    throw new Error(
      'useColorBlindness must be used within a ColorBlindnessProvider'
    );
  }
  return context;
}
