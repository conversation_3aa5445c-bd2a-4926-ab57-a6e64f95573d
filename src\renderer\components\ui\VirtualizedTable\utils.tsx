/**
 * @file VirtualizedTable/utils.ts
 * @description Utility functions for virtualized table operations
 */

import React from 'react';
import { VirtualizedTableColumn } from './index';

/**
 * Sort data based on column configuration
 */
export const sortTableData = <T,>(
  data: T[],
  columns: VirtualizedTableColumn<T>[],
  sortColumn: string,
  sortDirection: 'asc' | 'desc'
): T[] => {
  const column = columns.find(col => col.key === sortColumn);
  if (!column) {return data;}

  return [...data].sort((a, b) => {
    // Extract raw values for comparison
    const aValue = extractSortValue(column.render(a, 0));
    const bValue = extractSortValue(column.render(b, 0));

    // Handle different data types
    const comparison = compareSortValues(aValue, bValue);
    
    return sortDirection === 'asc' ? comparison : -comparison;
  });
};

/**
 * Extract sortable value from rendered content
 */
const extractSortValue = (renderedContent: React.ReactNode): string | number => {
  if (typeof renderedContent === 'string' || typeof renderedContent === 'number') {
    return renderedContent;
  }

  if (React.isValidElement(renderedContent)) {
    // Try to extract text content from React elements
    const textContent = extractTextFromElement(renderedContent);
    return textContent || '';
  }

  return String(renderedContent || '');
};

/**
 * Recursively extract text content from React elements
 */
const extractTextFromElement = (element: React.ReactElement): string => {
  if (typeof element.props.children === 'string') {
    return element.props.children;
  }

  if (Array.isArray(element.props.children)) {
    return element.props.children
      .map((child: React.ReactNode) => {
        if (typeof child === 'string') {return child;}
        if (React.isValidElement(child)) {return extractTextFromElement(child);}
        return String(child || '');
      })
      .join('');
  }

  if (React.isValidElement(element.props.children)) {
    return extractTextFromElement(element.props.children);
  }

  return String(element.props.children || '');
};

/**
 * Compare two values for sorting
 */
const compareSortValues = (a: string | number, b: string | number): number => {
  // Handle null/undefined values
  if (a == null && b == null) {return 0;}
  if (a == null) {return 1;}
  if (b == null) {return -1;}

  // Try numeric comparison first
  const numA = Number(a);
  const numB = Number(b);
  
  if (!isNaN(numA) && !isNaN(numB)) {
    return numA - numB;
  }

  // Try date comparison
  const dateA = new Date(a);
  const dateB = new Date(b);
  
  if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
    return dateA.getTime() - dateB.getTime();
  }

  // String comparison (case-insensitive)
  const strA = String(a).toLowerCase();
  const strB = String(b).toLowerCase();
  
  return strA.localeCompare(strB);
};

/**
 * Calculate optimal column widths based on content
 */
export const calculateOptimalColumnWidths = <T,>(
  data: T[],
  columns: VirtualizedTableColumn<T>[],
  containerWidth: number,
  sampleSize: number = 50
): VirtualizedTableColumn<T>[] => {
  if (data.length === 0) {return columns;}

  // Sample data for width calculation
  const sampleData = data.slice(0, sampleSize);
  
  return columns.map(column => {
    // Calculate content width for this column
    const contentWidths = sampleData.map(item => {
      const content = column.render(item, 0);
      return estimateContentWidth(content);
    });

    // Include header width
    const headerWidth = estimateTextWidth(column.header) + 32; // Add padding
    const maxContentWidth = Math.max(...contentWidths, headerWidth);

    // Apply constraints
    let optimalWidth = maxContentWidth;
    if (column.minWidth) {optimalWidth = Math.max(optimalWidth, column.minWidth);}
    if (column.maxWidth) {optimalWidth = Math.min(optimalWidth, column.maxWidth);}

    return {
      ...column,
      width: optimalWidth
    };
  });
};

/**
 * Estimate content width for rendered React content
 */
const estimateContentWidth = (content: React.ReactNode): number => {
  if (typeof content === 'string') {
    return estimateTextWidth(content) + 32; // Add padding
  }

  if (typeof content === 'number') {
    return estimateTextWidth(String(content)) + 32;
  }

  if (React.isValidElement(content)) {
    const textContent = extractTextFromElement(content);
    return estimateTextWidth(textContent) + 32;
  }

  return 100; // Default width
};

/**
 * Estimate text width in pixels (rough approximation)
 */
const estimateTextWidth = (text: string): number => {
  // Rough approximation: average character width of 8px
  return text.length * 8;
};

/**
 * Generate a unique key for table rows
 */
export const generateRowKey = <T,>(
  item: T,
  index: number,
  keyExtractor?: (item: T, index: number) => string | number
): string => {
  if (keyExtractor) {
    return String(keyExtractor(item, index));
  }

  // Try to find a unique identifier in the item
  if (item && typeof item === 'object') {
    const obj = item as Record<string, unknown>;
    if ('id' in obj && obj.id) {return String(obj.id);}
    if ('key' in obj && obj.key) {return String(obj.key);}
    if ('uuid' in obj && obj.uuid) {return String(obj.uuid);}
  }

  return String(index);
};

/**
 * Filter data based on search query across all filterable columns
 */
export const filterTableData = <T,>(
  data: T[],
  columns: VirtualizedTableColumn<T>[],
  searchQuery: string
): T[] => {
  if (!searchQuery.trim()) {return data;}

  const lowerQuery = searchQuery.toLowerCase();
  const filterableColumns = columns.filter(col => col.filterable !== false);

  return data.filter(item => {
    return filterableColumns.some(column => {
      const content = column.render(item, 0);
      const textContent = extractSortValue(content);
      return String(textContent).toLowerCase().includes(lowerQuery);
    });
  });
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: number;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait) as unknown as number;
  };
};

/**
 * Throttle function for scroll events
 */
export const throttle = <T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

/**
 * Calculate visible row range for performance optimization
 */
export const calculateVisibleRange = (
  scrollTop: number,
  containerHeight: number,
  rowHeight: number,
  totalRows: number,
  overscan: number = 5
): { start: number; end: number } => {
  const start = Math.max(0, Math.floor(scrollTop / rowHeight) - overscan);
  const visibleRows = Math.ceil(containerHeight / rowHeight);
  const end = Math.min(totalRows - 1, start + visibleRows + overscan * 2);

  return { start, end };
};

/**
 * Format cell content for display
 */
export const formatCellContent = (
  content: React.ReactNode,
  maxLength: number = 100
): React.ReactNode => {
  if (typeof content === 'string' && content.length > maxLength) {
    return (
      <span title={content}>
        {content.substring(0, maxLength)}...
      </span>
    );
  }

  return content;
};

/**
 * Validate column configuration
 */
export const validateColumns = <T,>(columns: VirtualizedTableColumn<T>[]): string[] => {
  const errors: string[] = [];

  // Check for duplicate keys
  const keys = columns.map(col => col.key);
  const duplicateKeys = keys.filter((key, index) => keys.indexOf(key) !== index);
  if (duplicateKeys.length > 0) {
    errors.push(`Duplicate column keys found: ${duplicateKeys.join(', ')}`);
  }

  // Check for empty keys
  const emptyKeys = columns.filter(col => !col.key.trim());
  if (emptyKeys.length > 0) {
    errors.push('Column keys cannot be empty');
  }

  // Check for invalid widths
  columns.forEach((col, index) => {
    if (typeof col.width === 'number' && col.width <= 0) {
      errors.push(`Column ${col.key} (index ${index}) has invalid width: ${col.width}`);
    }
  });

  return errors;
};

/**
 * Export table data to CSV format
 */
export const exportToCSV = <T,>(
  data: T[],
  columns: VirtualizedTableColumn<T>[],
  filename: string = 'table-data.csv'
): void => {
  // Create CSV header
  const headers = columns.map(col => col.header);
  const csvContent = [headers];

  // Add data rows
  data.forEach(item => {
    const row = columns.map(col => {
      const content = col.render(item, 0);
      const textContent = extractSortValue(content);
      // Escape quotes and wrap in quotes if contains comma
      const escaped = String(textContent).replace(/"/g, '""');
      return escaped.includes(',') ? `"${escaped}"` : escaped;
    });
    csvContent.push(row);
  });

  // Convert to CSV string
  const csvString = csvContent.map(row => row.join(',')).join('\n');

  // Download file
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};