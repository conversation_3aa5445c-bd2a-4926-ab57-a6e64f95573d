/**
 * @file recursive-types.ts
 * @description Recursive type definitions for complex nested data structures
 * Implements advanced TypeScript patterns for tree structures, graphs, and hierarchical data
 */

import type { Brand } from './advanced-utilities.types';
import type { ApplicationError, Result } from './error-handling.types';
import type { NonNullable } from './null-safety.types';

// ===== BASIC RECURSIVE STRUCTURE TYPES =====

/**
 * Generic tree node with recursive children
 */
export interface TreeNode<T, TId extends string | number = string> {
  readonly id: TId;
  readonly data: T;
  readonly parent?: TId | null;
  readonly children: readonly TreeNode<T, TId>[];
  readonly depth: number;
  readonly metadata?: Record<string, unknown>;
}

/**
 * Generic graph node with bidirectional relationships
 */
export interface GraphNode<T, TId extends string | number = string> {
  readonly id: TId;
  readonly data: T;
  readonly edges: readonly GraphEdge<TId>[];
  readonly metadata?: Record<string, unknown>;
}

/**
 * Graph edge definition
 */
export interface GraphEdge<TId extends string | number = string> {
  readonly from: TId;
  readonly to: TId;
  readonly weight?: number;
  readonly directed: boolean;
  readonly label?: string;
  readonly metadata?: Record<string, unknown>;
}

/**
 * Linked list node
 */
export interface LinkedListNode<T> {
  readonly data: T;
  readonly next: LinkedListNode<T> | null;
  readonly previous?: LinkedListNode<T> | null; // For doubly linked lists
}

// ===== ADVANCED RECURSIVE TYPES =====

/**
 * Nested object with arbitrary depth
 */
export type NestedObject<T> = {
  [K in keyof T]: T[K] extends object 
    ? T[K] extends any[]
      ? T[K]
      : NestedObject<T[K]>
    : T[K];
};

/**
 * Recursive key paths for nested objects
 */
export type RecursivePath<T> = T extends object
  ? {
      [K in keyof T]: K extends string
        ? T[K] extends object
          ? T[K] extends any[]
            ? K | `${K}[${number}]` | `${K}[${number}].${RecursivePath<T[K][number]>}`
            : K | `${K}.${RecursivePath<T[K]>}`
          : K
        : never;
    }[keyof T]
  : never;

/**
 * Get nested value type by path
 */
export type RecursivePathValue<T, P extends RecursivePath<T>> = 
  P extends `${infer K}.${infer Rest}`
    ? K extends keyof T
      ? Rest extends RecursivePath<T[K]>
        ? RecursivePathValue<T[K], Rest>
        : never
      : never
    : P extends `${infer K}[${infer Index}]`
      ? K extends keyof T
        ? T[K] extends readonly (infer U)[]
          ? Index extends `${number}`
            ? U
            : never
          : never
        : never
      : P extends `${infer K}[${infer Index}].${infer Rest}`
        ? K extends keyof T
          ? T[K] extends readonly (infer U)[]
            ? Index extends `${number}`
              ? Rest extends RecursivePath<U>
                ? RecursivePathValue<U, Rest>
                : never
              : never
            : never
          : never
        : P extends keyof T
          ? T[P]
          : never;

/**
 * Recursive flattening of nested structures
 */
export type RecursiveFlatten<T> = T extends readonly (infer U)[]
  ? RecursiveFlatten<U>[]
  : T extends object
    ? {
        [K in keyof T]: RecursiveFlatten<T[K]>;
      }
    : T;

/**
 * Maximum recursion depth control
 */
export type RecursiveDepth = readonly [never, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9];

/**
 * Depth-limited recursive type
 */
export type LimitedRecursive<T, TDepth extends RecursiveDepth[number] = 5> = TDepth extends never
  ? never
  : T extends object
    ? {
        [K in keyof T]: T[K] extends object
          ? LimitedRecursive<T[K], RecursiveDepth[TDepth]>
          : T[K];
      }
    : T;

// ===== HIERARCHICAL DATA STRUCTURES =====

/**
 * Organization hierarchy node
 */
export interface OrganizationNode {
  readonly id: Brand<string, 'OrganizationId'>;
  readonly name: string;
  readonly type: 'company' | 'division' | 'department' | 'team' | 'project';
  readonly parent?: Brand<string, 'OrganizationId'> | null;
  readonly children: readonly OrganizationNode[];
  readonly members: readonly {
    readonly userId: Brand<string, 'UserId'>;
    readonly role: string;
    readonly permissions: readonly string[];
  }[];
  readonly depth: number;
  readonly path: readonly Brand<string, 'OrganizationId'>[];
}

/**
 * Product category tree
 */
export interface ProductCategoryNode {
  readonly id: Brand<string, 'CategoryId'>;
  readonly name: string;
  readonly slug: string;
  readonly description?: string;
  readonly parent?: Brand<string, 'CategoryId'> | null;
  readonly children: readonly ProductCategoryNode[];
  readonly products: readonly Brand<string, 'ProductId'>[];
  readonly depth: number;
  readonly isLeaf: boolean;
  readonly breadcrumb: readonly string[];
}

/**
 * Color palette hierarchy
 */
export interface ColorPaletteNode {
  readonly id: Brand<string, 'PaletteId'>;
  readonly name: string;
  readonly type: 'collection' | 'palette' | 'group' | 'variant';
  readonly parent?: Brand<string, 'PaletteId'> | null;
  readonly children: readonly ColorPaletteNode[];
  readonly colors: readonly Brand<string, 'ColorId'>[];
  readonly tags: readonly string[];
  readonly depth: number;
  readonly colorCount: number; // Recursive count including children
}

// ===== TREE OPERATIONS =====

/**
 * Tree traversal strategies
 */
export type TraversalStrategy = 'depth-first' | 'breadth-first' | 'pre-order' | 'post-order' | 'in-order';

/**
 * Tree visitor function
 */
export type TreeVisitor<T, TId extends string | number = string> = (
  node: TreeNode<T, TId>,
  depth: number,
  path: readonly TId[]
) => void | 'skip' | 'stop';

/**
 * Tree transformer function
 */
export type TreeTransformer<T, U, TId extends string | number = string> = (
  node: TreeNode<T, TId>,
  depth: number,
  path: readonly TId[]
) => TreeNode<U, TId>;

/**
 * Tree filter predicate
 */
export type TreeFilter<T, TId extends string | number = string> = (
  node: TreeNode<T, TId>,
  depth: number,
  path: readonly TId[]
) => boolean;

/**
 * Tree operations interface
 */
export interface TreeOperations<T, TId extends string | number = string> {
  readonly find: (predicate: TreeFilter<T, TId>) => TreeNode<T, TId> | null;
  readonly findAll: (predicate: TreeFilter<T, TId>) => readonly TreeNode<T, TId>[];
  readonly traverse: (visitor: TreeVisitor<T, TId>, strategy?: TraversalStrategy) => void;
  readonly transform: <U>(transformer: TreeTransformer<T, U, TId>) => TreeNode<U, TId>;
  readonly filter: (predicate: TreeFilter<T, TId>) => TreeNode<T, TId> | null;
  readonly getPath: (nodeId: TId) => readonly TId[] | null;
  readonly getAncestors: (nodeId: TId) => readonly TreeNode<T, TId>[];
  readonly getDescendants: (nodeId: TId) => readonly TreeNode<T, TId>[];
  readonly getSiblings: (nodeId: TId) => readonly TreeNode<T, TId>[];
  readonly getDepth: (nodeId: TId) => number;
  readonly isAncestor: (ancestorId: TId, descendantId: TId) => boolean;
  readonly isDescendant: (descendantId: TId, ancestorId: TId) => boolean;
  readonly getLowestCommonAncestor: (nodeId1: TId, nodeId2: TId) => TreeNode<T, TId> | null;
}

// ===== GRAPH OPERATIONS =====

/**
 * Graph traversal algorithms
 */
export type GraphTraversalStrategy = 'depth-first' | 'breadth-first' | 'dijkstra' | 'a-star';

/**
 * Graph visitor function
 */
export type GraphVisitor<T, TId extends string | number = string> = (
  node: GraphNode<T, TId>,
  distance: number,
  path: readonly TId[]
) => void | 'skip' | 'stop';

/**
 * Graph path finding result
 */
export interface GraphPath<TId extends string | number = string> {
  readonly path: readonly TId[];
  readonly distance: number;
  readonly weight: number;
  readonly edges: readonly GraphEdge<TId>[];
}

/**
 * Graph operations interface
 */
export interface GraphOperations<T, TId extends string | number = string> {
  readonly find: (predicate: (node: GraphNode<T, TId>) => boolean) => GraphNode<T, TId> | null;
  readonly traverse: (startId: TId, visitor: GraphVisitor<T, TId>, strategy?: GraphTraversalStrategy) => void;
  readonly findPath: (fromId: TId, toId: TId, strategy?: GraphTraversalStrategy) => GraphPath<TId> | null;
  readonly findShortestPath: (fromId: TId, toId: TId) => GraphPath<TId> | null;
  readonly findAllPaths: (fromId: TId, toId: TId, maxDepth?: number) => readonly GraphPath<TId>[];
  readonly getNeighbors: (nodeId: TId) => readonly GraphNode<T, TId>[];
  readonly getConnectedComponents: () => readonly (readonly TId[])[];
  readonly hasCycle: () => boolean;
  readonly isConnected: (fromId: TId, toId: TId) => boolean;
  readonly getDistance: (fromId: TId, toId: TId) => number | null;
  readonly topologicalSort: () => readonly TId[] | null; // null if cycle exists
}

// ===== RECURSIVE VALIDATION =====

/**
 * Recursive validation schema
 */
export interface RecursiveValidationSchema<T> {
  readonly type: 'object' | 'array' | 'primitive';
  readonly required?: boolean;
  readonly properties?: T extends object
    ? {
        [K in keyof T]: RecursiveValidationSchema<T[K]>;
      }
    : never;
  readonly items?: T extends readonly (infer U)[]
    ? RecursiveValidationSchema<U>
    : never;
  readonly validator?: (value: unknown) => Result<T>;
  readonly transform?: (value: unknown) => T;
  readonly children?: RecursiveValidationSchema<any>[];
}

/**
 * Recursive validation result
 */
export interface RecursiveValidationResult<T> {
  readonly isValid: boolean;
  readonly value?: T;
  readonly errors: readonly {
    readonly path: string;
    readonly message: string;
    readonly code: string;
  }[];
  readonly children?: readonly RecursiveValidationResult<any>[];
}

// ===== MEMOIZATION FOR RECURSIVE OPERATIONS =====

/**
 * Memoized recursive function wrapper
 */
export interface MemoizedRecursive<TArgs extends readonly unknown[], TReturn> {
  readonly fn: (...args: TArgs) => TReturn;
  readonly cache: Map<string, TReturn>;
  readonly clear: () => void;
  readonly size: number;
  readonly keyGenerator?: (...args: TArgs) => string;
}

/**
 * Create memoized recursive function
 */
export function createMemoizedRecursive<TArgs extends readonly unknown[], TReturn>(
  fn: (...args: TArgs) => TReturn,
  keyGenerator?: (...args: TArgs) => string
): MemoizedRecursive<TArgs, TReturn> {
  const cache = new Map<string, TReturn>();
  const defaultKeyGen = (...args: TArgs): string => JSON.stringify(args);
  const keygen = keyGenerator || defaultKeyGen;

  const memoizedFn = (...args: TArgs): TReturn => {
    const key = keygen(...args);
    if (cache.has(key)) {
      return cache.get(key)!;
    }
    const result = fn(...args);
    cache.set(key, result);
    return result;
  };

  return {
    fn: memoizedFn,
    cache,
    clear: () => cache.clear(),
    get size() { return cache.size; },
    keyGenerator: keygen
  };
}

// ===== RECURSIVE BUILDERS =====

/**
 * Tree builder with fluent interface
 */
export interface TreeBuilder<T, TId extends string | number = string> {
  readonly root: (id: TId, data: T) => TreeBuilder<T, TId>;
  readonly child: (id: TId, data: T, parentId: TId) => TreeBuilder<T, TId>;
  readonly sibling: (id: TId, data: T) => TreeBuilder<T, TId>;
  readonly metadata: (nodeId: TId, metadata: Record<string, unknown>) => TreeBuilder<T, TId>;
  readonly build: () => Result<TreeNode<T, TId>>;
  readonly validate: () => readonly string[];
}

/**
 * Graph builder with fluent interface
 */
export interface GraphBuilder<T, TId extends string | number = string> {
  readonly node: (id: TId, data: T) => GraphBuilder<T, TId>;
  readonly edge: (from: TId, to: TId, weight?: number, directed?: boolean) => GraphBuilder<T, TId>;
  readonly metadata: (nodeId: TId, metadata: Record<string, unknown>) => GraphBuilder<T, TId>;
  readonly build: () => Result<readonly GraphNode<T, TId>[]>;
  readonly validate: () => readonly string[];
}

// ===== RECURSIVE SERIALIZATION =====

/**
 * Serialization options for recursive structures
 */
export interface RecursiveSerializationOptions {
  readonly maxDepth?: number;
  readonly includeMetadata?: boolean;
  readonly circularRefStrategy?: 'error' | 'skip' | 'reference';
  readonly dateFormat?: 'iso' | 'timestamp' | 'custom';
  readonly customTransforms?: Record<string, (value: unknown) => unknown>;
}

/**
 * Serialized recursive structure
 */
export interface SerializedRecursive {
  readonly type: 'tree' | 'graph' | 'list';
  readonly version: string;
  readonly metadata: Record<string, unknown>;
  readonly nodes: readonly {
    readonly id: string | number;
    readonly data: unknown;
    readonly relationships: Record<string, unknown>;
  }[];
  readonly options: RecursiveSerializationOptions;
}

// ===== TYPE-SAFE RECURSIVE QUERIES =====

/**
 * Recursive query builder for nested data
 */
export type RecursiveQuery<T> = T extends object
  ? {
      [K in keyof T]?: T[K] extends object
        ? T[K] extends any[]
          ? {
              readonly some?: RecursiveQuery<T[K][number]>;
              readonly every?: RecursiveQuery<T[K][number]>;
              readonly none?: RecursiveQuery<T[K][number]>;
              readonly length?: {
                readonly gte?: number;
                readonly lte?: number;
                readonly equals?: number;
              };
            }
          : RecursiveQuery<T[K]> | {
              readonly is?: RecursiveQuery<T[K]>;
              readonly isNot?: RecursiveQuery<T[K]>;
            }
        : T[K] extends string
          ? {
              readonly equals?: T[K];
              readonly contains?: string;
              readonly startsWith?: string;
              readonly endsWith?: string;
              readonly matches?: RegExp;
            }
          : T[K] extends number
            ? {
                readonly equals?: T[K];
                readonly gte?: number;
                readonly lte?: number;
                readonly gt?: number;
                readonly lt?: number;
              }
            : T[K] extends boolean
              ? {
                  readonly equals?: T[K];
                }
              : {
                  readonly equals?: T[K];
                };
    }
  : never;

// Export all types for module compatibility
export {};