/**
 * @file AppModals.tsx
 * @description Global application modals using centralized modal management
 */

import React from 'react';
import { ModalRenderer } from '../modals/ModalRenderer';

/**
 * App Modals component using centralized modal management
 * Note: ModalProvider is now provided at the AppShell level
 */
export const AppModals: React.FC = () => {
  return <ModalRenderer />;
};

export default AppModals;
