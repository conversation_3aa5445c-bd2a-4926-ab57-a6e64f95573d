/**
 * @file gradient-debug.ts
 * @description Debug utility to investigate gradient update issues in ChromaSync
 */

export class GradientDebugger {
  static async debugGradientUpdate(colorId: string, expectedGradientData: any) {
    console.log('🔍 Starting gradient update debug for color:', colorId);

    try {
      // 1. Check color before update
      const beforeUpdate = await window.colorAPI.getById(colorId);
      console.log('📋 Color before update:', {
        id: beforeUpdate?.data?.id,
        name: beforeUpdate?.data?.name,
        isGradient: beforeUpdate?.data?.gradient ? 'YES' : 'NO',
        gradientColors: beforeUpdate?.data?.gradient?.colors,
        hex: beforeUpdate?.data?.hex,
        colorSpaces: {
          rgb: beforeUpdate?.data?.rgb,
          hsl: beforeUpdate?.data?.hsl,
          lab: beforeUpdate?.data?.lab,
        },
      });

      // 2. Simulate update
      console.log('🔄 Expected gradient data:', expectedGradientData);

      // 3. Check if IPC is working
      const ipcTest = await window.colorAPI.getAll();
      console.log(
        '📡 IPC communication test:',
        ipcTest?.success ? 'WORKING' : 'FAILED'
      );

      // 4. Check organization context
      const { useOrganizationStore } = await import(
        '../store/organization.store'
      );
      const orgStore = useOrganizationStore.getState();
      console.log('🏢 Organization context:', {
        currentOrg: orgStore.currentOrganization?.id,
        hasOrg: !!orgStore.currentOrganization,
      });

      return {
        beforeUpdate: beforeUpdate?.data,
        ipcWorking: !!ipcTest?.success,
        organizationContext: orgStore.currentOrganization?.id,
      };
    } catch (error) {
      console.error('❌ Gradient debug failed:', error);
      return { error };
    }
  }

  static async verifyGradientInDatabase(colorId: string) {
    console.log('🔍 Verifying gradient in database for color:', colorId);

    try {
      // Fetch the color directly from the API
      const colorResponse = await window.colorAPI.getById(colorId);

      if (!colorResponse?.success || !colorResponse.data) {
        console.error('❌ Color not found in database');
        return { found: false };
      }

      const color = colorResponse.data;
      const verification = {
        found: true,
        id: color.id,
        name: color.name,
        isGradient: !!color.gradient,
        gradientColors: color.gradient?.colors || [],
        gradientColorCount: color.gradient?.colors?.length || 0,
        hasGradientData: !!(
          color.gradient &&
          color.gradient.colors &&
          color.gradient.colors.length > 0
        ),
        colorSpaces: (color as any).color_spaces,
        properties: (color as any).properties,
      };

      console.log('📊 Database verification result:', verification);
      return verification;
    } catch (error) {
      console.error('❌ Database verification failed:', error);
      return { error, found: false };
    }
  }

  static async testFullGradientFlow(colorId: string, _gradientData: any) {
    console.log('🧪 Testing full gradient update flow for:', colorId);

    const results: {
      preUpdate: any;
      updateResult: any;
      postUpdate: any;
      cacheCleared: boolean;
      dataRefreshed: boolean;
      error: any;
    } = {
      preUpdate: null,
      updateResult: null,
      postUpdate: null,
      cacheCleared: false,
      dataRefreshed: false,
      error: null,
    };

    try {
      // 1. Pre-update verification
      results.preUpdate = await this.verifyGradientInDatabase(colorId);

      // 2. Test cache operations
      const { colorCacheService } = await import(
        '../services/color-cache.service'
      );
      const { useOrganizationStore } = await import(
        '../store/organization.store'
      );
      const orgStore = useOrganizationStore.getState();

      if (orgStore.currentOrganization?.id) {
        colorCacheService.invalidateOrganization(
          orgStore.currentOrganization.id
        );
        results.cacheCleared = true;
      }

      // 3. Test data refresh
      const { useColorStore } = await import('../store/color.store');
      await useColorStore.getState().loadColorsWithUsage();
      results.dataRefreshed = true;

      // 4. Post-update verification
      await new Promise(resolve => setTimeout(resolve, 200));
      results.postUpdate = await this.verifyGradientInDatabase(colorId);

      console.log('🧪 Full gradient flow test results:', results);
      return results;
    } catch (error) {
      console.error('❌ Gradient flow test failed:', error);
      results.error = error;
      return results;
    }
  }
}

// Export for use in development console
(window as any).GradientDebugger = GradientDebugger;
