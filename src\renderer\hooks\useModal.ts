/**
 * @file useModal.ts
 * @description Hook for managing modal state and providing alert/confirm functionality
 */

import { useState, useCallback } from 'react';

interface ModalState {
  isOpen: boolean;
  title: string;
  message: string;
  variant?: 'success' | 'error' | 'warning' | 'info' | 'danger';
  onConfirm?: () => void;
  confirmText?: string;
  cancelText?: string;
}

export const useModal = () => {
  const [alertState, setAlertState] = useState<ModalState>({
    isOpen: false,
    title: '',
    message: ''
  });

  const [confirmState, setConfirmState] = useState<ModalState>({
    isOpen: false,
    title: '',
    message: ''
  });

  // Alert functionality to replace window.alert
  const showAlert = useCallback((
    message: string, 
    title: string = 'Alert',
    variant: 'success' | 'error' | 'warning' | 'info' = 'info'
  ) => {
    setAlertState({
      isOpen: true,
      title,
      message,
      variant
    });
  }, []);

  const closeAlert = useCallback(() => {
    setAlertState(prev => ({ ...prev, isOpen: false }));
  }, []);

  // Confirm functionality to replace window.confirm
  const showConfirm = useCallback((
    message: string,
    onConfirm: () => void,
    title: string = 'Confirm',
    variant: 'danger' | 'warning' | 'info' = 'info',
    confirmText: string = 'Confirm',
    cancelText: string = 'Cancel'
  ) => {
    setConfirmState({
      isOpen: true,
      title,
      message,
      variant,
      onConfirm,
      confirmText,
      cancelText
    });
  }, []);

  const closeConfirm = useCallback(() => {
    setConfirmState(prev => ({ ...prev, isOpen: false }));
  }, []);

  // Convenience methods for common use cases
  const showSuccess = useCallback((message: string, title: string = 'Success') => {
    showAlert(message, title, 'success');
  }, [showAlert]);

  const showError = useCallback((message: string, title: string = 'Error') => {
    showAlert(message, title, 'error');
  }, [showAlert]);

  const showWarning = useCallback((message: string, title: string = 'Warning') => {
    showAlert(message, title, 'warning');
  }, [showAlert]);

  const showInfo = useCallback((message: string, title: string = 'Information') => {
    showAlert(message, title, 'info');
  }, [showAlert]);

  const confirmDelete = useCallback((
    itemName: string,
    onConfirm: () => void,
    customMessage?: string
  ) => {
    const message = customMessage || `Are you sure you want to delete "${itemName}"? This action cannot be undone.`;
    showConfirm(
      message,
      onConfirm,
      'Delete Confirmation',
      'danger',
      'Delete',
      'Cancel'
    );
  }, [showConfirm]);

  const confirmAction = useCallback((
    action: string,
    onConfirm: () => void,
    message?: string
  ) => {
    const confirmMessage = message || `Are you sure you want to ${action}?`;
    showConfirm(
      confirmMessage,
      onConfirm,
      'Confirm Action',
      'warning',
      'Continue',
      'Cancel'
    );
  }, [showConfirm]);

  return {
    // Alert state and controls
    alertState,
    showAlert,
    closeAlert,
    
    // Confirm state and controls
    confirmState,
    showConfirm,
    closeConfirm,
    
    // Convenience methods
    showSuccess,
    showError,
    showWarning,
    showInfo,
    confirmDelete,
    confirmAction
  };
};