/**
 * @file template-literal-types.ts
 * @description Sophisticated conditional types with template literal types for advanced string manipulation
 * Implements cutting-edge TypeScript patterns for compile-time string processing
 */

// ===== BASIC STRING MANIPULATION TYPES =====

/**
 * Capitalize first letter of string literal type
 */
export type Capitalize<S extends string> =
  S extends `${infer First}${infer Rest}` ? `${Uppercase<First>}${Rest}` : S;

/**
 * Uncapitalize first letter of string literal type
 */
export type Uncapitalize<S extends string> =
  S extends `${infer First}${infer Rest}` ? `${Lowercase<First>}${Rest}` : S;

/**
 * Convert string to kebab-case
 */
export type KebabCase<S extends string> =
  S extends `${infer First}${infer Rest}`
    ? First extends Lowercase<First>
      ? `${First}${KebabCase<Rest>}`
      : `-${Lowercase<First>}${KebabCase<Rest>}`
    : S;

/**
 * Convert string to snake_case
 */
export type SnakeCase<S extends string> =
  S extends `${infer First}${infer Rest}`
    ? First extends Lowercase<First>
      ? `${First}${SnakeCase<Rest>}`
      : `_${Lowercase<First>}${SnakeCase<Rest>}`
    : S;

/**
 * Convert string to PascalCase
 */
export type PascalCase<S extends string> = Capitalize<CamelCase<S>>;

/**
 * Convert string to camelCase
 */
export type CamelCase<S extends string> =
  S extends `${infer First}_${infer Rest}`
    ? `${Lowercase<First>}${PascalCase<Rest>}`
    : S extends `${infer First}-${infer Rest}`
      ? `${Lowercase<First>}${PascalCase<Rest>}`
      : Lowercase<S>;

// ===== ADVANCED STRING PATTERN MATCHING =====

/**
 * Extract all words from a string
 */
export type ExtractWords<S extends string> =
  S extends `${infer Word} ${infer Rest}`
    ? [Word, ...ExtractWords<Rest>]
    : S extends ''
      ? []
      : [S];

/**
 * Join array of strings with separator
 */
export type Join<
  T extends readonly string[],
  Separator extends string,
> = T extends readonly [infer First, ...infer Rest]
  ? First extends string
    ? Rest extends readonly string[]
      ? Rest['length'] extends 0
        ? First
        : `${First}${Separator}${Join<Rest, Separator>}`
      : First
    : ''
  : '';

/**
 * Split string by separator
 */
export type Split<
  S extends string,
  Separator extends string,
> = S extends `${infer First}${Separator}${infer Rest}`
  ? [First, ...Split<Rest, Separator>]
  : [S];

/**
 * Replace all occurrences of a substring
 */
export type ReplaceAll<
  S extends string,
  Search extends string,
  Replace extends string,
> = S extends `${infer Before}${Search}${infer After}`
  ? `${Before}${Replace}${ReplaceAll<After, Search, Replace>}`
  : S;

/**
 * Remove all occurrences of a substring
 */
export type RemoveAll<S extends string, Remove extends string> = ReplaceAll<
  S,
  Remove,
  ''
>;

/**
 * Trim whitespace from both ends
 */
export type Trim<S extends string> = S extends ` ${infer Rest}`
  ? Trim<Rest>
  : S extends `${infer Rest} `
    ? Trim<Rest>
    : S;

// ===== PATH AND URL MANIPULATION =====

/**
 * Extract file extension from filename
 */
export type FileExtension<S extends string> = S extends `${string}.${infer Ext}`
  ? Ext extends `${string}.${infer LastExt}`
    ? FileExtension<Ext>
    : Ext
  : never;

/**
 * Extract filename without extension
 */
export type FileNameWithoutExt<S extends string> =
  S extends `${infer Name}.${string}`
    ? Name extends `${string}.${string}`
      ? FileNameWithoutExt<Name>
      : Name
    : S;

/**
 * Extract directory path from file path
 */
export type DirectoryPath<S extends string> =
  S extends `${infer Dir}/${infer File}`
    ? File extends `${string}/${string}`
      ? `${Dir}/${DirectoryPath<File>}`
      : Dir
    : '';

/**
 * Extract filename from file path
 */
export type FileName<S extends string> = S extends `${string}/${infer File}`
  ? File extends `${string}/${string}`
    ? FileName<File>
    : File
  : S;

/**
 * Join path segments
 */
export type JoinPath<T extends readonly string[]> = T extends readonly [
  infer First,
  ...infer Rest,
]
  ? First extends string
    ? Rest extends readonly string[]
      ? Rest['length'] extends 0
        ? First
        : `${First}/${JoinPath<Rest>}`
      : First
    : ''
  : '';

// ===== CSS AND STYLING TYPES =====

/**
 * CSS property names (subset for demonstration)
 */
export type CSSProperty =
  | 'color'
  | 'background-color'
  | 'font-size'
  | 'margin'
  | 'padding'
  | 'border'
  | 'width'
  | 'height'
  | 'display'
  | 'position'
  | 'top'
  | 'left'
  | 'right'
  | 'bottom'
  | 'z-index';

/**
 * CSS selector patterns
 */
export type CSSSelector =
  | `#${string}` // ID selector
  | `.${string}` // Class selector
  | `[${string}]` // Attribute selector
  | `${string}:${string}` // Pseudo selector
  | `${string} ${string}` // Descendant selector
  | `${string} > ${string}` // Child selector
  | `${string} + ${string}` // Adjacent sibling
  | `${string} ~ ${string}`; // General sibling

/**
 * CSS color formats
 */
export type CSSColor =
  | `#${string}` // Hex
  | `rgb(${string})` // RGB
  | `rgba(${string})` // RGBA
  | `hsl(${string})` // HSL
  | `hsla(${string})` // HSLA
  | 'transparent'
  | 'inherit'
  | 'initial'
  | 'unset';

/**
 * CSS size units
 */
export type CSSSizeUnit =
  | 'px'
  | 'em'
  | 'rem'
  | '%'
  | 'vh'
  | 'vw'
  | 'vmin'
  | 'vmax'
  | 'ch'
  | 'ex';

/**
 * CSS size value
 */
export type CSSSize =
  | `${number}${CSSSizeUnit}`
  | '0'
  | 'auto'
  | 'inherit'
  | 'initial'
  | 'unset';

// ===== DATABASE AND SQL TYPES =====

/**
 * SQL table name pattern (snake_case)
 */
export type SQLTableName = `${Lowercase<string>}_${string}` | Lowercase<string>;

/**
 * SQL column name pattern (snake_case)
 */
export type SQLColumnName =
  | `${Lowercase<string>}_${string}`
  | Lowercase<string>;

/**
 * SQL join types
 */
export type SQLJoinType =
  | 'INNER JOIN'
  | 'LEFT JOIN'
  | 'RIGHT JOIN'
  | 'FULL OUTER JOIN';

/**
 * SQL comparison operators
 */
export type SQLOperator =
  | '='
  | '!='
  | '<'
  | '>'
  | '<='
  | '>='
  | 'LIKE'
  | 'IN'
  | 'NOT IN'
  | 'IS NULL'
  | 'IS NOT NULL';

/**
 * SQL order direction
 */
export type SQLOrder = 'ASC' | 'DESC';

/**
 * Build SELECT query type
 */
export type SelectQuery<
  TColumns extends string,
  TTable extends SQLTableName,
  TWhere extends string = never,
  TOrderBy extends string = never,
> = `SELECT ${TColumns} FROM ${TTable}${TWhere extends never ? '' : ` WHERE ${TWhere}`}${TOrderBy extends never ? '' : ` ORDER BY ${TOrderBy}`}`;

// ===== API ENDPOINT TYPES =====

/**
 * HTTP methods
 */
export type HttpMethod =
  | 'GET'
  | 'POST'
  | 'PUT'
  | 'PATCH'
  | 'DELETE'
  | 'HEAD'
  | 'OPTIONS';

/**
 * REST API endpoint pattern
 */
export type RestEndpoint<
  TResource extends string,
  TId extends string = never,
> = TId extends never ? `/api/${TResource}` : `/api/${TResource}/${TId}`;

/**
 * API versioning pattern
 */
export type VersionedEndpoint<
  TVersion extends string,
  TEndpoint extends string,
> = `/api/v${TVersion}${TEndpoint}`;

/**
 * Query parameter pattern
 */
export type QueryParam<
  TKey extends string,
  TValue extends string,
> = `${TKey}=${TValue}`;

/**
 * Build query string
 */
export type QueryString<T extends readonly string[]> = T extends readonly [
  infer First,
  ...infer Rest,
]
  ? First extends string
    ? Rest extends readonly string[]
      ? Rest['length'] extends 0
        ? `?${First}`
        : `?${First}&${Join<Rest, '&'>}`
      : `?${First}`
    : ''
  : '';

// ===== VALIDATION PATTERN TYPES =====

/**
 * Email pattern validation
 */
export type EmailPattern = `${string}@${string}.${string}`;

/**
 * UUID pattern validation
 */
export type UUIDPattern = `${string}-${string}-${string}-${string}-${string}`;

/**
 * Hex color pattern validation
 */
export type HexColorPattern = `#${string}`;

/**
 * Semantic version pattern
 */
export type SemVerPattern = `${number}.${number}.${number}`;

/**
 * IP address pattern (simplified)
 */
export type IPAddressPattern = `${number}.${number}.${number}.${number}`;

/**
 * URL pattern validation
 */
export type URLPattern = `http${'s' | ''}://${string}`;

// ===== ADVANCED TEMPLATE LITERAL UTILITIES =====

/**
 * Check if string matches pattern
 */
export type MatchesPattern<
  S extends string,
  Pattern extends string,
> = S extends Pattern ? true : false;

/**
 * Extract groups from pattern match
 */
export type ExtractGroups<
  S extends string,
  Pattern extends string,
> = Pattern extends `${infer Before}(${infer Group})${infer After}`
  ? S extends `${string}${infer Matched}${string}`
    ? [Matched, ...ExtractGroups<S, After>]
    : []
  : [];

/**
 * Validate string format
 */
export type ValidateFormat<
  S extends string,
  Format extends 'email' | 'uuid' | 'hex' | 'url',
> = Format extends 'email'
  ? S extends EmailPattern
    ? true
    : false
  : Format extends 'uuid'
    ? S extends UUIDPattern
      ? true
      : false
    : Format extends 'hex'
      ? S extends HexColorPattern
        ? true
        : false
      : Format extends 'url'
        ? S extends URLPattern
          ? true
          : false
        : false;

/**
 * Transform string based on format
 */
export type FormatString<
  S extends string,
  Format extends 'upper' | 'lower' | 'kebab' | 'snake' | 'pascal' | 'camel',
> = Format extends 'upper'
  ? Uppercase<S>
  : Format extends 'lower'
    ? Lowercase<S>
    : Format extends 'kebab'
      ? KebabCase<S>
      : Format extends 'snake'
        ? SnakeCase<S>
        : Format extends 'pascal'
          ? PascalCase<S>
          : Format extends 'camel'
            ? CamelCase<S>
            : S;

// ===== OBJECT KEY MANIPULATION WITH TEMPLATE LITERALS =====

/**
 * Transform object keys with template literals
 */
export type TransformObjectKeys<
  T,
  TTransform extends 'upper' | 'lower' | 'kebab' | 'snake' | 'pascal' | 'camel',
> = {
  [K in keyof T as K extends string ? FormatString<K, TTransform> : K]: T[K];
};

/**
 * Prefix all object keys
 */
export type PrefixKeys<T, TPrefix extends string> = {
  [K in keyof T as K extends string ? `${TPrefix}${K}` : K]: T[K];
};

/**
 * Suffix all object keys
 */
export type SuffixKeys<T, TSuffix extends string> = {
  [K in keyof T as K extends string ? `${K}${TSuffix}` : K]: T[K];
};

/**
 * Wrap keys with template
 */
export type WrapKeys<T, TTemplate extends string> = {
  [K in keyof T as K extends string
    ? TTemplate extends `${infer Before}$${infer After}`
      ? `${Before}${K}${After}`
      : `${TTemplate}${K}`
    : K]: T[K];
};

// ===== EVENT AND MESSAGE TYPES =====

/**
 * Event name pattern
 */
export type EventName<
  TDomain extends string,
  TAction extends string,
> = `${TDomain}:${TAction}`;

/**
 * Message type pattern
 */
export type MessageType<
  TService extends string,
  TOperation extends string,
> = `${TService}.${TOperation}`;

/**
 * Channel name pattern
 */
export type ChannelName<
  TNamespace extends string,
  TChannel extends string,
> = `${TNamespace}:${TChannel}`;

/**
 * Topic pattern for pub/sub
 */
export type TopicPattern<
  TService extends string,
  TEntity extends string,
  TAction extends string,
> = `${TService}.${TEntity}.${TAction}`;

// ===== CONFIGURATION AND ENVIRONMENT TYPES =====

/**
 * Environment variable pattern
 */
export type EnvVarName<
  TPrefix extends string,
  TName extends string,
> = `${Uppercase<TPrefix>}_${Uppercase<TName>}`;

/**
 * Configuration key pattern
 */
export type ConfigKey<
  TSection extends string,
  TKey extends string,
> = `${TSection}.${TKey}`;

/**
 * Feature flag pattern
 */
export type FeatureFlag<TFeature extends string> =
  `FEATURE_${Uppercase<TFeature>}_ENABLED`;

// ===== RUNTIME VALIDATION HELPERS =====

/**
 * Runtime validation function for template literal types
 */
export function createValidator<T extends string>(
  pattern: RegExp,
  errorMessage: string = 'Invalid format'
) {
  return (value: string): value is T => {
    return pattern.test(value);
  };
}

/**
 * Common validators
 */
export const validators = {
  email: createValidator<EmailPattern>(
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    'Invalid email format'
  ),
  uuid: createValidator<UUIDPattern>(
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    'Invalid UUID format'
  ),
  hexColor: createValidator<HexColorPattern>(
    /^#[0-9a-f]{3,6}$/i,
    'Invalid hex color format'
  ),
  url: createValidator<URLPattern>(/^https?:\/\/.+/, 'Invalid URL format'),
} as const;

/**
 * Transform string at runtime based on format
 */
export function transformString<
  TFormat extends 'upper' | 'lower' | 'kebab' | 'snake' | 'pascal' | 'camel',
>(str: string, format: TFormat): string {
  switch (format) {
    case 'upper':
      return str.toUpperCase();
    case 'lower':
      return str.toLowerCase();
    case 'kebab':
      return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
    case 'snake':
      return str.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();
    case 'pascal':
      return str
        .replace(/(?:^|[\s-_])(\w)/g, (_, char) => char.toUpperCase())
        .replace(/[\s-_]/g, '');
    case 'camel':
      const pascal = transformString(str, 'pascal');
      return pascal.charAt(0).toLowerCase() + pascal.slice(1);
    default:
      return str;
  }
}

// Export all types for module compatibility
export {};
