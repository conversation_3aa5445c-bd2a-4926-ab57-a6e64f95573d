/**
 * @file auth-notification.service.ts
 * @description Handles authentication-related UI notifications and progress windows
 */

import { BrowserWindow, nativeTheme, screen } from 'electron';
import {
  LoggerFactory,
  /* logPerformance, */ ILogger,
} from '../../utils/logger.service';

export interface NotificationOptions {
  title?: string;
  message?: string;
  type?: 'info' | 'success' | 'warning' | 'error' | 'progress';
  autoClose?: boolean;
  autoCloseDelay?: number;
  showProgress?: boolean;
  position?:
    | 'center'
    | 'top-right'
    | 'top-left'
    | 'bottom-right'
    | 'bottom-left';
}

export interface ProgressOptions {
  title: string;
  message: string;
  showSpinner?: boolean;
  allowCancel?: boolean;
  onCancel?: () => void;
}

/**
 * Authentication notification service for managing UI notifications
 */
export class AuthNotificationService {
  private readonly logger: ILogger;
  private notificationWindow: BrowserWindow | null = null;
  private progressWindow: BrowserWindow | null = null;

  constructor(logger?: ILogger) {
    this.logger =
      logger ||
      LoggerFactory.getInstance().createLogger('AuthNotificationService');
  }

  /**
   * Show authentication progress notification
   * TODO: Re-enable decorator when decorator signatures are fixed
   */
  // @logPerformance(LoggerFactory.getInstance().createLogger('AuthNotificationService'), 'showAuthProgress')
  showAuthProgress(options: ProgressOptions): void {
    this.logger.info('Showing authentication progress', {
      title: options.title,
      operation: 'showAuthProgress',
    });

    this.closeProgressWindow();

    const { title, message, showSpinner = true, allowCancel = false } = options;

    this.progressWindow = new BrowserWindow({
      width: 450,
      height: 320,
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      frame: true,
      titleBarStyle: 'default',
      title: 'ChromaSync - Authentication',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true,
      },
    });

    const isDark = nativeTheme.shouldUseDarkColors;
    const html = this.createProgressHTML(
      title,
      message,
      showSpinner,
      allowCancel,
      isDark
    );

    this.progressWindow.loadURL(
      `data:text/html;charset=utf-8,${encodeURIComponent(html)}`
    );

    // Handle cancel button if enabled
    if (allowCancel && options.onCancel) {
      this.progressWindow.webContents.on('dom-ready', () => {
        this.progressWindow?.webContents.executeJavaScript(`
          document.getElementById('cancelBtn')?.addEventListener('click', () => {
            window.close();
          });
        `);
      });

      this.progressWindow.on('closed', () => {
        if (options.onCancel) {
          options.onCancel();
        }
        this.progressWindow = null;
      });
    }

    this.progressWindow.on('closed', () => {
      this.progressWindow = null;
    });

    // Center the window
    this.centerWindow(this.progressWindow);
  }

  /**
   * Update progress window message
   */
  updateProgressMessage(message: string): void {
    if (this.progressWindow && !this.progressWindow.isDestroyed()) {
      this.progressWindow.webContents.executeJavaScript(`
        const messageEl = document.getElementById('progressMessage');
        if (messageEl) messageEl.textContent = '${message.replace(/'/g, "\\'")}';
      `);
    }
  }

  /**
   * Close authentication progress window
   */
  closeAuthProgress(): void {
    this.logger.debug('Closing authentication progress window');
    this.closeProgressWindow();
  }

  /**
   * Show notification window
   * TODO: Re-enable decorator when decorator signatures are fixed
   */
  // @logPerformance(LoggerFactory.getInstance().createLogger('AuthNotificationService'), 'showNotification')
  showNotification(options: NotificationOptions): void {
    const {
      title = 'ChromaSync',
      message = '',
      type = 'info',
      autoClose = true,
      autoCloseDelay = 5000,
      position = 'top-right',
    } = options;

    this.logger.info('Showing notification', {
      title,
      type,
      autoClose,
      operation: 'showNotification',
    });

    this.closeNotificationWindow();

    const isDark = nativeTheme.shouldUseDarkColors;

    this.notificationWindow = new BrowserWindow({
      width: 380,
      height: 120,
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      frame: false,
      transparent: true,
      titleBarStyle: 'hidden',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true,
      },
    });

    const html = this.createNotificationHTML(title, message, type, isDark);
    this.notificationWindow.loadURL(
      `data:text/html;charset=utf-8,${encodeURIComponent(html)}`
    );

    // Position the window
    this.positionNotificationWindow(this.notificationWindow, position);

    // Auto-close if enabled
    if (autoClose) {
      setTimeout(() => {
        this.closeNotificationWindow();
      }, autoCloseDelay);
    }

    // Handle window close
    this.notificationWindow.on('closed', () => {
      this.notificationWindow = null;
    });

    // Add click to close
    this.notificationWindow.webContents.on('dom-ready', () => {
      this.notificationWindow?.webContents.executeJavaScript(`
        document.addEventListener('click', () => {
          window.close();
        });
      `);
    });
  }

  /**
   * Show error notification
   */
  showError(title: string, message: string, persistent: boolean = false): void {
    this.showNotification({
      title,
      message,
      type: 'error',
      autoClose: !persistent,
      autoCloseDelay: persistent ? 0 : 8000,
    });
  }

  /**
   * Show success notification
   */
  showSuccess(title: string, message: string): void {
    this.showNotification({
      title,
      message,
      type: 'success',
      autoClose: true,
      autoCloseDelay: 4000,
    });
  }

  /**
   * Show warning notification
   */
  showWarning(title: string, message: string): void {
    this.showNotification({
      title,
      message,
      type: 'warning',
      autoClose: true,
      autoCloseDelay: 6000,
    });
  }

  /**
   * Show session warning notification
   */
  showSessionWarning(minutesRemaining: number): void {
    this.showWarning(
      'Session Expiring Soon',
      `Your session will expire in ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''}. Please save your work.`
    );
  }

  /**
   * Show session expired notification
   */
  showSessionExpired(reason: string): void {
    this.showError(
      'Session Expired',
      `Your session has expired due to ${reason}. Please sign in again to continue.`,
      true
    );
  }

  /**
   * Show circuit breaker notification
   */
  showCircuitBreakerNotification(cooldownMinutes: number): void {
    this.showError(
      'Authentication Temporarily Disabled',
      `Too many failed sign-in attempts. Please wait ${cooldownMinutes} minutes before trying again.`,
      true
    );
  }

  /**
   * Show recovery notification
   */
  showRecoveryNotification(): void {
    this.showSuccess(
      'Authentication Reset',
      'The authentication system has been reset. You can now try signing in again.'
    );
  }

  /**
   * Cleanup all notification windows
   */
  cleanup(): void {
    this.logger.debug('Cleaning up notification service');
    this.closeProgressWindow();
    this.closeNotificationWindow();
  }

  // Private methods

  private closeProgressWindow(): void {
    if (this.progressWindow && !this.progressWindow.isDestroyed()) {
      this.progressWindow.close();
      this.progressWindow = null;
    }
  }

  private closeNotificationWindow(): void {
    if (this.notificationWindow && !this.notificationWindow.isDestroyed()) {
      this.notificationWindow.close();
      this.notificationWindow = null;
    }
  }

  private centerWindow(window: BrowserWindow): void {
    const { width, height } = window.getBounds();
    const { workArea } = screen.getPrimaryDisplay();

    const x = Math.round((workArea.width - width) / 2);
    const y = Math.round((workArea.height - height) / 2);

    window.setBounds({ x, y, width, height });
  }

  private positionNotificationWindow(
    window: BrowserWindow,
    position: string
  ): void {
    const { width, height } = window.getBounds();
    const { workArea } = screen.getPrimaryDisplay();

    let x: number, y: number;

    switch (position) {
      case 'top-right':
        x = workArea.x + workArea.width - width - 20;
        y = workArea.y + 20;
        break;
      case 'top-left':
        x = workArea.x + 20;
        y = workArea.y + 20;
        break;
      case 'bottom-right':
        x = workArea.x + workArea.width - width - 20;
        y = workArea.y + workArea.height - height - 20;
        break;
      case 'bottom-left':
        x = workArea.x + 20;
        y = workArea.y + workArea.height - height - 20;
        break;
      case 'center':
      default:
        x = Math.round((workArea.width - width) / 2);
        y = Math.round((workArea.height - height) / 2);
        break;
    }

    window.setBounds({ x, y, width, height });
  }

  private createProgressHTML(
    title: string,
    message: string,
    showSpinner: boolean,
    allowCancel: boolean,
    isDark: boolean
  ): string {
    const bgColor = isDark ? '#1e1e1e' : '#ffffff';
    const textColor = isDark ? '#ffffff' : '#333333';
    const borderColor = isDark ? '#404040' : '#e0e0e0';
    const buttonBg = isDark ? '#404040' : '#f0f0f0';
    const buttonHover = isDark ? '#505050' : '#e0e0e0';

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 24px;
            background: ${bgColor};
            color: ${textColor};
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 272px;
            box-sizing: border-box;
          }
          .container {
            text-align: center;
            background: ${bgColor};
            border-radius: 12px;
            padding: 24px;
            border: 1px solid ${borderColor};
            box-shadow: 0 8px 32px rgba(0, 0, 0, ${isDark ? '0.3' : '0.1'});
          }
          h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
          }
          p {
            margin: 8px 0;
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.9;
          }
          .spinner {
            width: 32px;
            height: 32px;
            border: 3px solid ${isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.1)'};
            border-top-color: #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
          }
          @keyframes spin {
            to { transform: rotate(360deg); }
          }
          .footer {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 16px;
          }
          .cancel-btn {
            background: ${buttonBg};
            border: 1px solid ${borderColor};
            color: ${textColor};
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 16px;
            transition: background-color 0.2s;
          }
          .cancel-btn:hover {
            background: ${buttonHover};
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h3>🔐 ${title}</h3>
          <p id="progressMessage">${message}</p>
          ${showSpinner ? '<div class="spinner"></div>' : ''}
          <p class="footer">This window will close automatically when complete</p>
          ${allowCancel ? '<button id="cancelBtn" class="cancel-btn">Cancel</button>' : ''}
        </div>
      </body>
      </html>
    `;
  }

  private createNotificationHTML(
    title: string,
    message: string,
    type: string,
    isDark: boolean
  ): string {
    const bgColor = isDark ? '#2d2d2d' : '#ffffff';
    const textColor = isDark ? '#ffffff' : '#333333';
    const borderColor = isDark ? '#404040' : '#e0e0e0';

    // Type-specific colors
    let iconColor = '#007AFF';
    let icon = 'ℹ️';

    switch (type) {
      case 'success':
        iconColor = '#28a745';
        icon = '✅';
        break;
      case 'warning':
        iconColor = '#ffc107';
        icon = '⚠️';
        break;
      case 'error':
        iconColor = '#dc3545';
        icon = '❌';
        break;
      case 'progress':
        iconColor = '#007AFF';
        icon = '⏳';
        break;
    }

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 16px;
            background: ${bgColor};
            color: ${textColor};
            border-radius: 8px;
            border: 1px solid ${borderColor};
            box-shadow: 0 4px 16px rgba(0, 0, 0, ${isDark ? '0.3' : '0.1'});
            cursor: pointer;
            transition: opacity 0.3s ease;
          }
          body:hover {
            opacity: 0.95;
          }
          .notification {
            display: flex;
            align-items: flex-start;
            gap: 12px;
          }
          .icon {
            font-size: 20px;
            flex-shrink: 0;
            margin-top: 2px;
          }
          .content {
            flex: 1;
            min-width: 0;
          }
          .title {
            font-weight: 600;
            font-size: 14px;
            margin: 0 0 4px 0;
            color: ${iconColor};
          }
          .message {
            font-size: 13px;
            margin: 0;
            opacity: 0.9;
            line-height: 1.3;
            word-wrap: break-word;
          }
        </style>
      </head>
      <body>
        <div class="notification">
          <div class="icon">${icon}</div>
          <div class="content">
            <div class="title">${title}</div>
            <div class="message">${message}</div>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
