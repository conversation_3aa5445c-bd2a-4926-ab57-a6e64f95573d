/**
 * @file dialog-utils.ts
 * @description Extended dialog utilities for Electron
 */

import { dialog, BrowserWindow, ipcMain } from 'electron';
import path from 'path';

/**
 * Options for the input box dialog
 */
export interface InputBoxOptions {
  title: string;
  message: string;
  placeholder?: string;
  value?: string;
  inputValidator?: (input: string) => string | null;
  cancelId?: number;
}

/**
 * Result of the input box dialog
 */
export interface InputBoxResult {
  canceled: boolean;
  response: string | null;
}

/**
 * Shows an input box dialog using a custom Electron window
 * @param options Options for the input box
 * @returns Promise resolving to the input box result
 */
export async function showInputBox(
  options: InputBoxOptions
): Promise<InputBoxResult> {
  // Get the focused window or the first window
  const parentWindow =
    BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];

  return new Promise(resolve => {
    // Create a custom input dialog window
    const inputWindow = new BrowserWindow({
      parent: parentWindow,
      modal: true,
      width: 450,
      height: 250,
      resizable: false,
      minimizable: false,
      maximizable: false,
      show: false,
      title: options.title,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/input-dialog-preload.js'),
      },
    });

    // Generate a unique channel for this dialog
    const dialogId = `input-dialog-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Handle dialog response
    const handleResponse = (
      _event: any,
      response: { canceled: boolean; value: string | null }
    ) => {
      ipcMain.removeListener(`${dialogId}-response`, handleResponse);
      inputWindow.close();

      if (response.canceled || response.value === null) {
        resolve({ canceled: true, response: null });
        return;
      }

      // Validate input if validator is provided
      if (options.inputValidator) {
        const validationError = options.inputValidator(response.value);
        if (validationError) {
          // Show error dialog and retry
          dialog
            .showMessageBox(parentWindow || inputWindow, {
              type: 'error',
              title: 'Invalid Input',
              message: validationError,
              buttons: ['OK'],
            })
            .then(() => {
              // Retry the input dialog
              showInputBox(options).then(resolve);
            });
          return;
        }
      }

      resolve({ canceled: false, response: response.value });
    };

    // Listen for dialog response
    ipcMain.once(`${dialogId}-response`, handleResponse);

    // Handle window close event
    inputWindow.on('closed', () => {
      ipcMain.removeListener(`${dialogId}-response`, handleResponse);
      resolve({ canceled: true, response: null });
    });

    // Create the HTML content for the input dialog
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${options.title}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            display: flex;
            flex-direction: column;
            justify-content: center;
            height: 100vh;
            box-sizing: border-box;
          }
          .dialog-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          }
          .message {
            margin-bottom: 16px;
            font-size: 14px;
            color: #333;
            line-height: 1.4;
          }
          .input-field {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 20px;
            box-sizing: border-box;
          }
          .input-field:focus {
            outline: none;
            border-color: #007ACC;
            box-shadow: 0 0 0 2px rgba(0,122,204,0.2);
          }
          .button-group {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
          }
          .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
          }
          .btn-primary {
            background-color: #007ACC;
            color: white;
          }
          .btn-primary:hover {
            background-color: #005a9e;
          }
          .btn-secondary {
            background-color: #e1e1e1;
            color: #333;
          }
          .btn-secondary:hover {
            background-color: #d1d1d1;
          }
        </style>
      </head>
      <body>
        <div class="dialog-content">
          <div class="message">${options.message}</div>
          <input 
            type="text" 
            class="input-field" 
            id="inputField"
            placeholder="${options.placeholder || ''}"
            value="${options.value || ''}"
            autocomplete="off"
          />
          <div class="button-group">
            <button class="btn btn-secondary" onclick="cancel()">Cancel</button>
            <button class="btn btn-primary" onclick="submit()">OK</button>
          </div>
        </div>
        
        <script>
          const { ipcRenderer } = require('electron');
          const dialogId = '${dialogId}';
          
          function submit() {
            const value = document.getElementById('inputField').value;
            ipcRenderer.send(dialogId + '-response', { canceled: false, value });
          }
          
          function cancel() {
            ipcRenderer.send(dialogId + '-response', { canceled: true, value: null });
          }
          
          // Handle Enter key
          document.getElementById('inputField').addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              submit();
            } else if (e.key === 'Escape') {
              e.preventDefault();
              cancel();
            }
          });
          
          // Focus the input field when loaded
          window.addEventListener('DOMContentLoaded', () => {
            document.getElementById('inputField').focus();
            document.getElementById('inputField').select();
          });
        </script>
      </body>
      </html>
    `;

    // Load the HTML content
    inputWindow.loadURL(
      `data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`
    );

    // Show the window when ready
    inputWindow.once('ready-to-show', () => {
      inputWindow.show();
    });
  });
}
