/**
 * Fix for sync initialization on app startup
 * 
 * Issue: Sync manager is not initialized when app starts with existing session
 * This causes sync errors when trying to sync data
 */

import { unifiedSyncManager } from '../services/sync/unified-sync-manager';
import { getOAuthService } from '../services/service-locator';
import { getCurrentOrganization } from '../utils/organization-context';

/**
 * Initialize sync system for authenticated users on app startup
 * This should be called after database initialization
 */
export async function initializeSyncOnStartup() {
  try {
    console.log('[SyncStartup] Checking for authenticated session...');
    
    // Get OAuth service
    const oauthService = getOAuthService();
    
    // Check if user is authenticated
    const user = await oauthService.getCurrentUser();
    if (!user) {
      console.log('[SyncStartup] No authenticated user, skipping sync initialization');
      return;
    }
    
    // Get current organization
    const currentOrgId = getCurrentOrganization();
    if (!currentOrgId) {
      console.log('[SyncStartup] No organization selected, skipping sync initialization');
      return;
    }
    
    console.log(`[SyncStartup] Authenticated user ${user.email} in organization ${currentOrgId}`);
    
    // Skip GDPR consent check - if user is authenticated, consent is assumed
    // The login flow would have handled GDPR consent already
    console.log('[SyncStartup] User is authenticated, proceeding with sync initialization');
    
    // Initialize unified sync manager
    console.log('[SyncStartup] Initializing unified sync manager...');
    await unifiedSyncManager.initialize(user.id, currentOrgId);
    
    console.log('[SyncStartup] ✅ Unified sync manager initialized successfully');
    
    // Set up real-time event forwarding
    const { initializeSyncEventForwarding } = await import('./sync-handlers');
    initializeSyncEventForwarding();
    
    // The sync system will process any pending items automatically
    console.log('[SyncStartup] Sync system ready to process pending items');
    
  } catch (error) {
    console.error('[SyncStartup] ❌ Failed to initialize sync on startup:', error);
    // Don't throw - app should work offline without sync
  }
}

// Export for use in main process
export function getSyncSystem() {
  return unifiedSyncManager;
}

// Add this to main/index.ts after database initialization:
// import { initializeSyncOnStartup } from './ipc/sync-startup';
// 
// app.whenReady().then(async () => {
//   // ... existing database initialization ...
//   
//   // Initialize sync for authenticated users
//   await initializeSyncOnStartup();
// });
