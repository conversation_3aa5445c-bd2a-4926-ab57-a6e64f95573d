/**
 * Fix for sync initialization on app startup
 *
 * Issue: Sync manager is not initialized when app starts with existing session
 * This causes sync errors when trying to sync data
 */

import { ServiceLocator } from '../services/service-locator';
import { unifiedSyncManager } from '../services/sync/unified-sync-manager';
import { getCurrentOrganization } from '../utils/organization-context';

/**
 * Initialize sync system for authenticated users on app startup
 * This should be called after database initialization
 */
export async function initializeSyncOnStartup() {
  try {
    console.log('[SyncStartup] Checking for authenticated session with enhanced auth manager...');
    
    // Get CircuitBreakerAuthManager for enhanced authentication validation
    const circuitBreakerAuthManager = ServiceLocator.getCircuitBreakerAuthManager();
    
    // Check authentication health status first
    const healthStatus = circuitBreakerAuthManager.getHealthStatus();
    if (!healthStatus.isHealthy) {
      console.log('[SyncStartup] Authentication system not healthy, skipping sync initialization:', healthStatus.issues);
      return;
    }

    // Check if circuit breaker is open
    if (healthStatus.circuitBreakerOpen) {
      console.log('[SyncStartup] Circuit breaker is open, skipping sync initialization');
      return;
    }

    // Check network connectivity
    if (!healthStatus.networkConnected) {
      console.log('[SyncStartup] Network not connected, skipping sync initialization');
      return;
    }
    
    // Check if user is authenticated with session validation
    const user = await circuitBreakerAuthManager.getCurrentUser();
    if (!user) {
      console.log(
        '[SyncStartup] No authenticated user, skipping sync initialization'
      );
      return;
    }

    // Validate current session
    const session = await circuitBreakerAuthManager.getCurrentSession();
    if (!session) {
      console.log('[SyncStartup] No valid session found, skipping sync initialization');
      return;
    }
    
    // Get current organization
    const currentOrgId = getCurrentOrganization();
    if (!currentOrgId) {
      console.log(
        '[SyncStartup] No organization selected, skipping sync initialization'
      );
      return;
    }
    
    console.log(`[SyncStartup] Authenticated user ${user.email} in organization ${currentOrgId}`);
    console.log(`[SyncStartup] Session valid: ${healthStatus.sessionValid}, Network connected: ${healthStatus.networkConnected}`);
    
    // Skip GDPR consent check - if user is authenticated, consent is assumed
    // The login flow would have handled GDPR consent already
    console.log('[SyncStartup] User is authenticated with valid session, proceeding with sync initialization');
    
    // Initialize unified sync manager
    console.log('[SyncStartup] Initializing unified sync manager...');
    await unifiedSyncManager.initialize(user.id, currentOrgId);

    console.log(
      '[SyncStartup] ✅ Unified sync manager initialized successfully'
    );

    // Set up real-time event forwarding
    const { initializeSyncEventForwarding } = await import('./sync-handlers');
    initializeSyncEventForwarding();

    // The sync system will process any pending items automatically
    console.log('[SyncStartup] Sync system ready to process pending items');
  } catch (error) {
    console.error(
      '[SyncStartup] ❌ Failed to initialize sync on startup:',
      error
    );
    // Don't throw - app should work offline without sync
  }
}

// Export for use in main process
export function getSyncSystem() {
  return unifiedSyncManager;
}

// Add this to main/index.ts after database initialization:
// import { initializeSyncOnStartup } from './ipc/sync-startup';
//
// app.whenReady().then(async () => {
//   // ... existing database initialization ...
//
//   // Initialize sync for authenticated users
//   await initializeSyncOnStartup();
// });
