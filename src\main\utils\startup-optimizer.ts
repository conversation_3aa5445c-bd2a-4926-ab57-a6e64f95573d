/**
 * @file startup-optimizer.ts
 * @description Utilities for optimizing application startup performance
 */

import { app, screen } from 'electron';
import { performance } from 'perf_hooks';
import * as fs from 'fs';
import * as path from 'path';
import {
  Result,
  success,
  failure,
} from '../../shared/types/result.types';
import {
  toError,
} from '../../shared/types/type-guards';

interface StartupMetrics {
  appReadyTime: number;
  windowCreationTime: number;
  databaseInitTime: number;
  ipcHandlerRegTime: number;
  totalStartupTime: number;
}

class StartupOptimizer {
  private metrics: Partial<StartupMetrics> = {};
  private startTime: number = performance.now();

  markStage(stage: keyof StartupMetrics, value?: number) {
    this.metrics[stage] = value ?? performance.now() - this.startTime;
    console.log(`[Startup] ${stage}: ${this.metrics[stage]}ms`);
  }

  getMetrics(): StartupMetrics {
    return {
      appReadyTime: 0,
      windowCreationTime: 0,
      databaseInitTime: 0,
      ipcHandlerRegTime: 0,
      totalStartupTime: 0,
      ...this.metrics,
    };
  }

  async logStartupSummary(): Promise<void> {
    const metrics = this.getMetrics();
    console.log('\n=== ChromaSync Startup Performance ===');
    console.log(`App Ready Time: ${metrics.appReadyTime}ms`);
    console.log(`Window Creation: ${metrics.windowCreationTime}ms`);
    console.log(`Database Init: ${metrics.databaseInitTime}ms`);
    console.log(`IPC Handlers: ${metrics.ipcHandlerRegTime}ms`);
    console.log(`Total Startup: ${metrics.totalStartupTime}ms`);
    console.log('=====================================\n');

    // Save metrics to file for analysis
    await this.saveMetricsToFile(metrics);
  }

  // Synchronous version for backward compatibility
  logStartupSummarySync(): void {
    const metrics = this.getMetrics();
    console.log('\n=== ChromaSync Startup Performance ===');
    console.log(`App Ready Time: ${metrics.appReadyTime}ms`);
    console.log(`Window Creation: ${metrics.windowCreationTime}ms`);
    console.log(`Database Init: ${metrics.databaseInitTime}ms`);
    console.log(`IPC Handlers: ${metrics.ipcHandlerRegTime}ms`);
    console.log(`Total Startup: ${metrics.totalStartupTime}ms`);
    console.log('=====================================\n');

    // Save metrics to file for analysis (fire and forget)
    this.saveMetricsToFile(metrics).catch(error => {
      console.warn('[Startup] Failed to save metrics asynchronously:', error);
    });
  }

  private async saveMetricsToFile(metrics: StartupMetrics): Promise<void> {
    try {
      const userDataPath = app.getPath('userData');
      const metricsFile = path.join(userDataPath, 'startup-metrics.json');

      let historicalMetrics: Array<
        StartupMetrics & {
          timestamp: number;
          platform: string;
          version: string;
        }
      > = [];

      // Try to read existing metrics
      if (fs.existsSync(metricsFile)) {
        try {
          const data = fs.readFileSync(metricsFile, 'utf8');
          const parsed = JSON.parse(data);
          if (Array.isArray(parsed)) {
            historicalMetrics = parsed;
          }
        } catch (error) {
          console.warn(
            '[Startup] Failed to parse existing metrics file, starting fresh'
          );
        }
      }

      // Add new metrics
      historicalMetrics.push({
        ...metrics,
        timestamp: Date.now(),
        platform: process.platform,
        version: app.getVersion(),
      });

      // Keep only last 50 startup metrics
      if (historicalMetrics.length > 50) {
        historicalMetrics = historicalMetrics.slice(-50);
      }

      // Write updated metrics
      fs.writeFileSync(metricsFile, JSON.stringify(historicalMetrics, null, 2));
    } catch (error) {
      console.warn(
        '[Startup] Failed to save metrics:',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  // Optimization utilities
  optimizeWindowCreation() {
    // Pre-allocate resources before window creation
    return {
      // Optimize window size based on screen
      getOptimalWindowSize: () => {
        // screen is now imported at the top
        const primaryDisplay = screen.getPrimaryDisplay();
        const { width, height } = primaryDisplay.workAreaSize;

        // Use 85% of screen size, but ensure minimum height for 2 rows of swatches
        // With header, controls, and 2 rows of swatches (h-36 each), we need at least 800px
        return {
          width: Math.min(Math.floor(width * 0.85), 1600),
          height: Math.min(Math.floor(height * 0.85), 1000),
          minWidth: 1200,
          minHeight: 800,
        };
      },

      // Pre-configure common window settings
      getOptimizedWebPreferences: () => ({
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: false, // Required for file:// protocol in production builds
        webSecurity: true, // Re-enabled for security
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        backgroundThrottling: false,
        offscreen: false,
      }),
    };
  }

  // Memory optimization
  optimizeMemory() {
    if (global.gc) {
      global.gc();
      console.log('[Startup] Garbage collection triggered');
    }
  }

  // Async initialization helper with proper Promise type handling
  async initializeInParallel<T>(
    tasks: Array<() => Promise<T>>
  ): Promise<Result<T[], Error>> {
    try {
      const startTime = performance.now();
      const results = await Promise.allSettled(tasks.map(task => task()));
      const endTime = performance.now();

      console.log(
        `[Startup] Parallel initialization completed in ${endTime - startTime}ms`
      );

      // Log any failures
      const failed: Array<{ index: number; reason: unknown }> = [];
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.warn(`[Startup] Task ${index} failed:`, result.reason);
          failed.push({ index, reason: result.reason });
        }
      });

      // Extract successful results with proper type guards
      const successfulResults: T[] = [];
      for (const result of results) {
        if (result.status === 'fulfilled') {
          successfulResults.push(result.value);
        }
      }

      // If we have failures but some successes, log warning but return successes
      if (failed.length > 0 && successfulResults.length > 0) {
        console.warn(
          `[Startup] ${failed.length} of ${tasks.length} tasks failed, but ${successfulResults.length} succeeded`
        );
      }

      return success(successfulResults);
    } catch (error) {
      return failure(error instanceof Error ? error : new Error(String(error)));
    }
  }

  // Type-safe async initialization with individual error handling
  async initializeInParallelSafe<T>(
    tasks: Array<{ name: string; task: () => Promise<T> }>
  ): Promise<Result<Array<{ name: string; result: Result<T, Error> }>, Error>> {
    try {
      const startTime = performance.now();

      const taskResults = await Promise.allSettled(
        tasks.map(async ({ name, task }) => {
          try {
            const result = await task();
            return { name, result: success(result) };
          } catch (error) {
            return {
              name,
              result: failure(
                error instanceof Error ? error : new Error(String(error))
              ),
            };
          }
        })
      );

      const endTime = performance.now();
      console.log(
        `[Startup] Safe parallel initialization completed in ${endTime - startTime}ms`
      );

      // Extract all results (both successful and failed task executions)
      const allResults: Array<{ name: string; result: Result<T, Error> }> = [];
      for (const taskResult of taskResults) {
        if (taskResult.status === 'fulfilled') {
          allResults.push(taskResult.value);
        } else {
          // This should rarely happen as we're wrapping individual tasks in try/catch
          console.error('[Startup] Task wrapper failed:', taskResult.reason);
        }
      }

      return success(allResults);
    } catch (error) {
      return failure(error instanceof Error ? error : new Error(String(error)));
    }
  }
}

// Singleton instance
export const startupOptimizer = new StartupOptimizer();

// Helper functions for common optimizations
export const createOptimizedWindow = () => {
  const optimizer = startupOptimizer.optimizeWindowCreation();
  return {
    ...optimizer.getOptimalWindowSize(),
    webPreferences: optimizer.getOptimizedWebPreferences(),
  };
};

export const benchmarkAsync = async <T>(
  name: string,
  operation: () => Promise<T>
): Promise<Result<T, Error>> => {
  const start = performance.now();

  try {
    const result = await operation();
    const end = performance.now();
    console.log(`[Startup] ${name} completed in ${end - start}ms`);
    return success(result);
  } catch (error) {
    const end = performance.now();
    const errorObj = error instanceof Error ? error : new Error(String(error));
    console.error(
      `[Startup] ${name} failed after ${end - start}ms:`,
      errorObj.message
    );
    return failure(errorObj);
  }
};

// Legacy version that throws for backward compatibility
export const benchmarkAsyncThrows = async <T>(
  name: string,
  operation: () => Promise<T>
): Promise<T> => {
  const start = performance.now();
  try {
    const result = await operation();
    const end = performance.now();
    console.log(`[Startup] ${name} completed in ${end - start}ms`);
    return result;
  } catch (error) {
    const end = performance.now();
    console.error(`[Startup] ${name} failed after ${end - start}ms:`, error);
    throw error;
  }
};

// Performance optimization utilities
export interface PerformanceEntry {
  name: string;
  startTime: number;
  duration: number;
  success: boolean;
  error?: string;
}

/**
 * Create a performance monitor for tracking multiple operations
 */
export function createPerformanceMonitor() {
  const entries: PerformanceEntry[] = [];

  return {
    /**
     * Track a synchronous operation
     */
    trackSync<T>(name: string, operation: () => T): Result<T, Error> {
      const start = performance.now();
      try {
        const result = operation();
        const duration = performance.now() - start;
        entries.push({ name, startTime: start, duration, success: true });
        console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
        return success(result);
      } catch (error) {
        const duration = performance.now() - start;
        const errorObj = toError(error);
        entries.push({
          name,
          startTime: start,
          duration,
          success: false,
          error: errorObj.message,
        });
        console.error(
          `[Performance] ${name} failed after ${duration.toFixed(2)}ms:`,
          errorObj.message
        );
        return failure(errorObj);
      }
    },

    /**
     * Track an asynchronous operation
     */
    async trackAsync<T>(
      name: string,
      operation: () => Promise<T>
    ): Promise<Result<T, Error>> {
      const start = performance.now();
      try {
        const result = await operation();
        const duration = performance.now() - start;
        entries.push({ name, startTime: start, duration, success: true });
        console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
        return success(result);
      } catch (error) {
        const duration = performance.now() - start;
        const errorObj =
          error instanceof Error ? error : new Error(String(error));
        entries.push({
          name,
          startTime: start,
          duration,
          success: false,
          error: errorObj.message,
        });
        console.error(
          `[Performance] ${name} failed after ${duration.toFixed(2)}ms:`,
          errorObj.message
        );
        return failure(errorObj);
      }
    },

    /**
     * Get all performance entries
     */
    getEntries: () => [...entries],

    /**
     * Get performance summary
     */
    getSummary: () => ({
      totalOperations: entries.length,
      successfulOperations: entries.filter(e => e.success).length,
      failedOperations: entries.filter(e => !e.success).length,
      totalTime: entries.reduce((sum, e) => sum + e.duration, 0),
      averageTime:
        entries.length > 0
          ? entries.reduce((sum, e) => sum + e.duration, 0) / entries.length
          : 0,
      longestOperation:
        entries.length > 0 ? Math.max(...entries.map(e => e.duration)) : 0,
      shortestOperation:
        entries.length > 0 ? Math.min(...entries.map(e => e.duration)) : 0,
    }),

    /**
     * Clear all entries
     */
    clear: () => entries.splice(0, entries.length),
  };
}

/**
 * Utility for managing async operation timeouts with proper error handling
 */
export async function withTimeout<T>(
  operation: () => Promise<T>,
  timeoutMs: number,
  timeoutMessage?: string
): Promise<Result<T, Error>> {
  try {
    const result = await Promise.race([
      operation(),
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(
            new Error(
              timeoutMessage || `Operation timed out after ${timeoutMs}ms`
            )
          );
        }, timeoutMs);
      }),
    ]);
    return success(result);
  } catch (error) {
    return failure(error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * Utility for retrying async operations with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelayMs: number = 1000,
  maxDelayMs: number = 10000
): Promise<Result<T, Error>> {
  let lastError: Error = new Error('No attempts made');

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const result = await operation();
      if (attempt > 1) {
        console.log(`[Startup] Operation succeeded on attempt ${attempt}`);
      }
      return success(result);
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
    }

    if (attempt < maxAttempts) {
      const delay = Math.min(
        baseDelayMs * Math.pow(2, attempt - 1),
        maxDelayMs
      );
      console.warn(
        `[Startup] Attempt ${attempt} failed, retrying in ${delay}ms:`,
        lastError.message
      );
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  console.error(
    `[Startup] All ${maxAttempts} attempts failed. Last error:`,
    lastError.message
  );
  return failure(lastError);
}
