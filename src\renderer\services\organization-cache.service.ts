/**
 * @file organization-cache.service.ts
 * @description Caching service for organization context to reduce redundant API calls
 */

import type { Organization } from '../../shared/types/organization.types';

interface OrganizationCacheEntry {
  organizations: Organization[];
  currentOrganization: Organization | null;
  timestamp: number;
  expiresAt: number;
}

interface OrganizationContextResult {
  organizations: Organization[];
  currentOrganization: Organization | null;
  fromCache: boolean;
}

class OrganizationCacheService {
  private static readonly CACHE_KEY = 'chromasync:org-cache';
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private static readonly LAST_ORG_KEY = 'chromasync:lastOrganization';

  /**
   * Get cached organization context if valid
   */
  static getCachedContext(): OrganizationContextResult | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (!cached) {
        return null;
      }

      const entry: OrganizationCacheEntry = JSON.parse(cached);

      // Check if cache is expired
      if (Date.now() > entry.expiresAt) {
        console.log('[OrgCache] Cache expired, removing...');
        localStorage.removeItem(this.CACHE_KEY);
        return null;
      }

      console.log('[OrgCache] ✅ Using cached organization context');
      return {
        organizations: entry.organizations,
        currentOrganization: entry.currentOrganization,
        fromCache: true,
      };
    } catch (error) {
      console.error('[OrgCache] Error reading cache:', error);
      localStorage.removeItem(this.CACHE_KEY);
      return null;
    }
  }

  /**
   * Cache organization context
   */
  static setCachedContext(
    organizations: Organization[],
    currentOrganization: Organization | null
  ): void {
    try {
      const entry: OrganizationCacheEntry = {
        organizations,
        currentOrganization,
        timestamp: Date.now(),
        expiresAt: Date.now() + this.CACHE_TTL,
      };

      localStorage.setItem(this.CACHE_KEY, JSON.stringify(entry));
      console.log(
        `[OrgCache] ✅ Cached ${organizations.length} organizations, expires in ${this.CACHE_TTL / 1000}s`
      );
    } catch (error) {
      console.error('[OrgCache] Error writing cache:', error);
    }
  }

  /**
   * Invalidate organization cache
   */
  static invalidateCache(): void {
    localStorage.removeItem(this.CACHE_KEY);
    console.log('[OrgCache] 🗑️ Cache invalidated');
  }

  /**
   * Get the last selected organization ID
   */
  static getLastOrganizationId(): string | null {
    return localStorage.getItem(this.LAST_ORG_KEY);
  }

  /**
   * Set the last selected organization ID
   */
  static setLastOrganizationId(orgId: string): void {
    localStorage.setItem(this.LAST_ORG_KEY, orgId);
  }

  /**
   * Clear the last selected organization ID
   */
  static clearLastOrganizationId(): void {
    localStorage.removeItem(this.LAST_ORG_KEY);
  }

  /**
   * Check if cache is valid (not expired)
   */
  static isCacheValid(): boolean {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (!cached) {
        return false;
      }

      const entry: OrganizationCacheEntry = JSON.parse(cached);
      return Date.now() <= entry.expiresAt;
    } catch {
      return false;
    }
  }

  /**
   * Get cache age in milliseconds
   */
  static getCacheAge(): number | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (!cached) {
        return null;
      }

      const entry: OrganizationCacheEntry = JSON.parse(cached);
      return Date.now() - entry.timestamp;
    } catch {
      return null;
    }
  }

  /**
   * Get time until cache expires in milliseconds
   */
  static getTimeUntilExpiry(): number | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (!cached) {
        return null;
      }

      const entry: OrganizationCacheEntry = JSON.parse(cached);
      return Math.max(0, entry.expiresAt - Date.now());
    } catch {
      return null;
    }
  }
}

export default OrganizationCacheService;
