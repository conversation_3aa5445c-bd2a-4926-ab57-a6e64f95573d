{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    /* Main Process Specific Configuration */
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "types": ["node", "better-sqlite3", "electron"],
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    
    /* Node.js Environment */
    "skipLibCheck": false,
    "declaration": false,
    "sourceMap": true,
    "outDir": "./out/main",
    "rootDir": "./src/main",
    
    /* Enhanced Type Safety for Main Process */
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    
    /* Main Process Path Mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@main/*": ["src/main/*"],
      "@shared/*": ["src/shared/*"],
      "@types/*": ["src/types/*"],
      "@db/*": ["src/main/db/*"],
      "@services/*": ["src/main/services/*"],
      "@utils/*": ["src/main/utils/*"]
    }
  },
  "include": [
    "src/main/**/*.ts",
    "src/shared/**/*.ts",
    "src/types/**/*.d.ts",
    "src/preload/**/*.ts"
  ],
  "exclude": [
    "src/renderer/**/*",
    "src/test/**/*",
    "**/*.test.ts",
    "**/__tests__/**/*",
    "node_modules"
  ]
}