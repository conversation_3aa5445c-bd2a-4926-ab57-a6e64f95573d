/**
 * @file index.ts
 * @description Main export file for the IPC Router system
 *
 * This file provides a centralized export point for the IPC Router,
 * middleware, and helper utilities, making it easy to set up routing
 * throughout the ChromaSync application.
 */

// Core router exports
export {
  IPCRouter,
  type HTTPMethod,
  type IPCRoute,
  type IPCRequest,
  type IPCResponse<PERSON>elper,
  type IPCMiddleware,
  type IPCHandler,
  type IPCRouteParams,
  type IPCRouteMatch,
  type IPCRouterStats,
  type IPCRouteGroup,
} from './IPCRouter';

// Middleware exports
export {
  requireOrganization,
  optionalOrganization,
  requireAuth,
  optionalAuth,
  validateRequest,
  validation,
  rateLimit,
  logger,
  performanceMonitor,
  debug,
  errorHandler,
  middleware,
} from './middleware';

// Migration utilities
export {
  migrateHandler,
  createRouteFromChannel,
  batchMigrateHandlers,
  type MigrationConfig,
} from './migration-utils';

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

import { IPCRouter, IPCRequest } from './IPCRouter';
import { middleware } from './middleware';
import { ServiceLocator } from '../../services/service-locator';
import { UnifiedSyncManager } from '../../services/sync';

/**
 * Create a new IPC Router with standard middleware
 */
export function createIPCRouter(
  options: {
    enableLogging?: boolean;
    performanceThreshold?: number;
    globalRateLimit?: { maxRequests: number; windowMs: number };
  } = {}
): IPCRouter {
  const {
    enableLogging = true,
    performanceThreshold = 500,
    globalRateLimit,
  } = options;

  const router = new IPCRouter();

  // Add global middleware
  if (enableLogging) {
    router.use('*', middleware.logger('ChromaSync'));
  }

  router.use('*', middleware.performanceMonitor(performanceThreshold));
  router.use('*', middleware.errorHandler);

  if (globalRateLimit) {
    router.use(
      '*',
      middleware.rateLimit(
        globalRateLimit.maxRequests,
        globalRateLimit.windowMs
      )
    );
  }

  // Add debug middleware in development
  if (process.env.NODE_ENV === 'development') {
    router.use('*', middleware.debug);
  }

  return router;
}

/**
 * Set up the complete ChromaSync router with all routes
 */
export async function setupChromaSyncRouter(
  options: {
    maintainLegacy?: boolean;
    autoMigrate?: boolean;
    developmentMode?: boolean;
  } = {}
): Promise<{ router: IPCRouter; stats: any }> {
  const {
    maintainLegacy = true,
    autoMigrate = false,
    developmentMode = process.env.NODE_ENV === 'development',
  } = options;

  console.log('[IPCRouter] Setting up ChromaSync router...');

  // Create router with standard configuration
  const router = createIPCRouter({
    enableLogging: true,
    performanceThreshold: developmentMode ? 100 : 500,
    globalRateLimit: { maxRequests: 100, windowMs: 60000 },
  });

  // Set up route groups
  await setupAuthRoutes(router);
  await setupOrganizationRoutes(router);
  await setupColorRoutes(router);
  await setupProductRoutes(router);
  await setupSyncRoutes(router);

  // Migration handling
  if (autoMigrate) {
    await migrateExistingHandlers(router, { maintainLegacy });
  }

  // Attach to ipcMain
  router.attachToIpcMain();

  const stats = router.getStats();
  console.log(
    `[IPCRouter] ✅ Router setup complete: ${stats.totalRoutes} routes, ${stats.totalMiddleware} middleware`
  );

  return { router, stats };
}

// ============================================================================
// ROUTE SETUP FUNCTIONS
// ============================================================================

/**
 * Set up authentication routes
 */
async function setupAuthRoutes(router: IPCRouter): Promise<void> {
  const authGroup = router.group('/api/auth');

  // Login/logout routes (no auth required)
  authGroup.post('/login', async (req: IPCRequest) => {
    const authService = ServiceLocator.getAuthenticationManager();
    return await authService.login(req.body);
  });

  authGroup.post(
    '/logout',
    middleware.optionalAuth,
    async (_req: IPCRequest) => {
      const authService = ServiceLocator.getAuthenticationManager();
      return await authService.logout();
    }
  );

  // User info routes (auth required)
  authGroup.get('/user', middleware.requireAuth, async (req: IPCRequest) => {
    return { user: req.user };
  });

  authGroup.get('/state', middleware.optionalAuth, async (_req: IPCRequest) => {
    const authService = ServiceLocator.getAuthenticationManager();
    return await authService.getAuthState();
  });

  authGroup.post(
    '/accept-gdpr',
    middleware.requireAuth,
    async (_req: IPCRequest) => {
      const authService = ServiceLocator.getAuthenticationManager();
      return await authService.acceptGDPR();
    }
  );
}

/**
 * Set up organization routes
 */
async function setupOrganizationRoutes(router: IPCRouter): Promise<void> {
  const orgGroup = router.group('/api/organizations');

  // Organization CRUD
  orgGroup.get('/', middleware.requireAuth, async (_req: IPCRequest) => {
    const orgService = ServiceLocator.getOrganizationService();
    return await orgService.getAll();
  });

  orgGroup.post(
    '/',
    middleware.requireAuth,
    middleware.validation.organizationId,
    async (req: IPCRequest) => {
      const orgService = ServiceLocator.getOrganizationService();
      return await orgService.create(req.body);
    }
  );

  orgGroup.get('/:id', middleware.requireAuth, async (req: IPCRequest) => {
    const orgService = ServiceLocator.getOrganizationService();
    const orgId = req.params.id;
    if (!orgId) {
      throw new Error('Organization ID is required');
    }
    return await orgService.getById(orgId);
  });

  // Current organization management
  orgGroup.get('/current', middleware.requireAuth, async (_req: IPCRequest) => {
    const orgService = ServiceLocator.getOrganizationService();
    return await orgService.getCurrent();
  });

  orgGroup.post('/current', middleware.requireAuth, async (req: IPCRequest) => {
    const orgService = ServiceLocator.getOrganizationService();
    return await orgService.setCurrent(req.body.organizationId);
  });

  // Member management
  orgGroup.get(
    '/:id/members',
    middleware.requireAuth,
    middleware.requireOrganization,
    async (req: IPCRequest) => {
      const orgService = ServiceLocator.getOrganizationService();
      const orgId = req.params.id;
      if (!orgId) {
        throw new Error('Organization ID is required');
      }
      return await orgService.getMembers(orgId);
    }
  );

  orgGroup.post(
    '/:id/members',
    middleware.requireAuth,
    middleware.requireOrganization,
    async (req: IPCRequest) => {
      const orgService = ServiceLocator.getOrganizationService();
      const orgId = req.params.id;
      if (!orgId) {
        throw new Error('Organization ID is required');
      }
      return await orgService.addMember(orgId, req.body);
    }
  );
}

/**
 * Helper function to safely get organization ID from request
 */
function getOrganizationId(req: IPCRequest): string {
  const orgId = req.organizationId;
  if (!orgId) {
    throw new Error('Organization ID is required');
  }
  return orgId;
}

/**
 * Helper function to safely get parameter from request
 */
function getRequiredParam(req: IPCRequest, paramName: string): string {
  const param = req.params[paramName];
  if (!param) {
    throw new Error(`${paramName} parameter is required`);
  }
  return param;
}

/**
 * Set up color routes
 */
async function setupColorRoutes(router: IPCRouter): Promise<void> {
  const colorGroup = router.group('/api/colors');

  // Apply standard middleware to all color routes
  colorGroup.use(middleware.requireAuth);
  colorGroup.use(middleware.requireOrganization);

  // Color CRUD
  colorGroup.get('/', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.getAll(getOrganizationId(req));
  });

  colorGroup.get('/with-usage', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.getAllWithUsage(getOrganizationId(req));
  });

  colorGroup.get('/:id', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.getById(
      getRequiredParam(req, 'id'),
      getOrganizationId(req)
    );
  });

  colorGroup.post(
    '/',
    middleware.validation.custom({
      type: 'object',
      properties: {
        name: { type: 'string', minLength: 1, maxLength: 100 },
        hex: { type: 'string', pattern: '^#[0-9A-Fa-f]{6}$' },
      },
      required: ['name', 'hex'],
      additionalProperties: false,
    }),
    async (req: IPCRequest) => {
      const colorService = ServiceLocator.getColorService();
      return await colorService.add(req.body, req.organizationId!);
    }
  );

  colorGroup.put('/:id', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.update(
      getRequiredParam(req, 'id'),
      req.body,
      getOrganizationId(req)
    );
  });

  colorGroup.delete('/:id', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.delete(
      getRequiredParam(req, 'id'),
      getOrganizationId(req)
    );
  });

  // Color operations
  colorGroup.get('/products-by-name', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.getProductsByColorName(getOrganizationId(req));
  });

  colorGroup.post('/analyze', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.analyzeColor(req.body, req.organizationId!);
  });

  colorGroup.post('/import', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.importColors(req.body, req.organizationId!);
  });

  colorGroup.get('/export', async (req: IPCRequest) => {
    const colorService = ServiceLocator.getColorService();
    return await colorService.exportColors(
      req.query || {},
      req.organizationId!
    );
  });
}

/**
 * Set up product routes
 */
async function setupProductRoutes(router: IPCRouter): Promise<void> {
  const productGroup = router.group('/api/products');

  // Apply standard middleware
  productGroup.use(middleware.requireAuth);
  productGroup.use(middleware.requireOrganization);

  // Product CRUD
  productGroup.get('/', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.getAll(req.organizationId!);
  });

  productGroup.get('/with-colors', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.getAllWithColors(req.organizationId!);
  });

  productGroup.get('/:id', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.getById(
      getRequiredParam(req, 'id'),
      getOrganizationId(req)
    );
  });

  productGroup.post('/', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.add(req.body, getOrganizationId(req));
  });

  productGroup.put('/:id', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.update(
      getRequiredParam(req, 'id'),
      req.body,
      getOrganizationId(req)
    );
  });

  productGroup.delete('/:id', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.delete(
      getRequiredParam(req, 'id'),
      getOrganizationId(req)
    );
  });

  // Product-color relationships
  productGroup.get('/:id/colors', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.getColors(
      getRequiredParam(req, 'id'),
      getOrganizationId(req)
    );
  });

  productGroup.post('/:id/colors', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.addColor(
      getRequiredParam(req, 'id'),
      req.body.colorId,
      getOrganizationId(req)
    );
  });

  productGroup.delete('/:id/colors/:colorId', async (req: IPCRequest) => {
    const productService = ServiceLocator.getProductService();
    return await productService.removeColor(
      getRequiredParam(req, 'id'),
      getRequiredParam(req, 'colorId'),
      getOrganizationId(req)
    );
  });
}

/**
 * Set up sync routes
 */
async function setupSyncRoutes(router: IPCRouter): Promise<void> {
  const syncGroup = router.group('/api/sync');

  // Apply auth middleware
  syncGroup.use(middleware.requireAuth);

  // Sync operations
  syncGroup.post(
    '/start',
    middleware.requireOrganization,
    async (_req: IPCRequest) => {
      const syncService = ServiceLocator.getSyncSystem() as UnifiedSyncManager;
      return await syncService.sync('full', 'bidirectional', 'normal');
    }
  );

  syncGroup.get('/status', async (_req: IPCRequest) => {
    const syncService = ServiceLocator.getSyncSystem() as UnifiedSyncManager;
    return await syncService.getStatus();
  });

  syncGroup.get('/progress', async (_req: IPCRequest) => {
    const syncService = ServiceLocator.getSyncSystem() as UnifiedSyncManager;
    const status = await syncService.getStatus();
    return {
      success: true,
      progress: {
        isInitialized: status.isInitialized,
        isReady: status.isReady,
        isSyncing: status.isSyncing,
        currentOperation: status.currentOperation,
        queueLength: status.queueLength,
        lastSyncTime: status.lastSyncTime,
      },
      timestamp: Date.now(),
    };
  });

  syncGroup.get('/metrics', async (_req: IPCRequest) => {
    const syncService = ServiceLocator.getSyncSystem() as UnifiedSyncManager;
    const status = await syncService.getStatus();
    return {
      success: true,
      metrics: {
        syncOperations: status.queueLength || 0,
        lastSyncTime: status.lastSyncTime,
        isHealthy: status.isReady && status.isInitialized,
        currentOperation: status.currentOperation,
      },
      timestamp: Date.now(),
    };
  });

  // Maintenance operations
  syncGroup.post(
    '/clear-outbox',
    middleware.requireOrganization,
    async (_req: IPCRequest) => {
      // Clear outbox functionality not available in UnifiedSyncManager
      // This would need to be implemented at the service level
      return {
        success: true,
        message: 'Outbox clear operation not implemented in UnifiedSyncManager',
        removedCount: 0,
        timestamp: Date.now(),
      };
    }
  );

  syncGroup.get('/queue-stats', async (_req: IPCRequest) => {
    const syncService = ServiceLocator.getSyncSystem() as UnifiedSyncManager;
    const status = await syncService.getStatus();
    return {
      success: true,
      queueStats: {
        totalItems: status.queueLength || 0,
        pendingItems: status.queueLength || 0,
        isProcessing: status.isSyncing,
        lastUpdate: status.lastSyncTime,
      },
      timestamp: Date.now(),
    };
  });
}

// ============================================================================
// MIGRATION UTILITIES
// ============================================================================

/**
 * Migrate existing IPC handlers to router system
 */
async function migrateExistingHandlers(
  _router: IPCRouter,
  _options: {
    maintainLegacy?: boolean;
  } = {}
): Promise<void> {
  console.log('[IPCRouter] Migrating existing handlers...');

  // This would import and convert existing handlers
  // Implementation would be added based on specific migration needs

  console.log('[IPCRouter] Handler migration complete');
}

// ============================================================================
// DEFAULT EXPORT
// ============================================================================

export default {
  IPCRouter,
  createIPCRouter,
  setupChromaSyncRouter,
  middleware,
};
