/**
 * @file DatabaseMaintenanceSection.tsx
 * @description Database maintenance and optimization section component
 */

import React, { useState } from 'react';
import { Database, HardDrive, RefreshCw, AlertTriangle } from 'lucide-react';
import { useSettingsStore } from '../../../store/settings.store';

interface DatabaseStats {
  totalSize: string;
  colorCount: number;
  productCount: number;
  lastVacuum: string | null;
  fragmentationLevel: number;
}

/**
 * Database maintenance section component
 */
export const DatabaseMaintenanceSection: React.FC = () => {
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const settings = useSettingsStore();

  const fetchDatabaseStats = async () => {
    setIsLoading(true);
    try {
      // This would call the IPC method to get database statistics
      const response = await (window as any).api.getDatabaseStats();
      if (response.success && response.stats) {
        setStats(response.stats);
      } else {
        console.error('Failed to fetch database stats:', response.error);
        setStats(null);
      }
    } catch (error) {
      console.error('Failed to fetch database stats:', error);
      setStats(null);
    } finally {
      setIsLoading(false);
    }
  };

  const optimizeDatabase = async () => {
    setIsOptimizing(true);
    try {
      await (window as any).api.optimizeDatabase();
      // Refresh stats after optimization
      await fetchDatabaseStats();
    } catch (error) {
      console.error('Failed to optimize database:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  const vacuumDatabase = async () => {
    try {
      await (window as any).api.vacuumDatabase();
      await fetchDatabaseStats();
    } catch (error) {
      console.error('Failed to vacuum database:', error);
    }
  };

  React.useEffect(() => {
    fetchDatabaseStats();
  }, []);

  const getFragmentationColor = (level: number) => {
    if (level < 20) {
      return 'text-feedback-success';
    }
    if (level < 50) {
      return 'text-feedback-warning';
    }
    return 'text-feedback-error';
  };

  const getFragmentationText = (level: number) => {
    if (level < 20) {
      return 'Low';
    }
    if (level < 50) {
      return 'Medium';
    }
    return 'High';
  };

  return (
    <section>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        Database Maintenance
      </h3>

      <div className='space-y-4'>
        {/* Database Statistics */}
        <div className='bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4'>
          <div className='flex items-center justify-between mb-3'>
            <h4 className='text-ui-foreground-primary dark:text-white font-medium'>
              Database Statistics
            </h4>
            <button
              onClick={fetchDatabaseStats}
              disabled={isLoading}
              className='flex items-center text-ui-foreground-secondary dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-gray-300 disabled:opacity-50'
            >
              <RefreshCw
                size={16}
                className={`mr-1 ${isLoading ? 'animate-spin' : ''}`}
              />
              Refresh
            </button>
          </div>

          {isLoading ? (
            <div className='text-center text-ui-foreground-secondary dark:text-gray-400 py-4'>
              Loading database statistics...
            </div>
          ) : stats ? (
            <div className='space-y-3'>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Total Size:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {stats.totalSize}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Colors:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {(stats.colorCount || 0).toLocaleString()}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Products:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {(stats.productCount || 0).toLocaleString()}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Fragmentation:
                </span>
                <span
                  className={getFragmentationColor(
                    stats.fragmentationLevel || 0
                  )}
                >
                  {getFragmentationText(stats.fragmentationLevel || 0)} (
                  {stats.fragmentationLevel || 0}%)
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Last Vacuum:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {stats.lastVacuum
                    ? new Date(stats.lastVacuum).toLocaleDateString()
                    : 'Never'}
                </span>
              </div>
            </div>
          ) : (
            <div className='text-center text-ui-foreground-secondary dark:text-gray-400 py-4'>
              Failed to load database statistics
            </div>
          )}
        </div>

        {/* Maintenance Actions */}
        <div className='space-y-2'>
          <h4 className='text-ui-foreground-primary dark:text-white font-medium'>
            Maintenance Actions
          </h4>

          <button
            onClick={optimizeDatabase}
            disabled={isOptimizing}
            className='w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors disabled:opacity-50'
          >
            <div className='flex items-center'>
              <Database
                size={16}
                className='text-brand-primary dark:text-blue-400 mr-2'
              />
              <div className='text-left'>
                <span className='text-ui-foreground-primary dark:text-gray-300 block'>
                  {isOptimizing
                    ? 'Optimizing Database...'
                    : 'Optimize Database'}
                </span>
                <span className='text-sm text-ui-foreground-secondary dark:text-gray-400'>
                  Rebuild indexes and improve query performance
                </span>
              </div>
            </div>
            {isOptimizing && (
              <RefreshCw
                size={16}
                className='animate-spin text-brand-primary'
              />
            )}
          </button>

          <button
            onClick={vacuumDatabase}
            className='w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors'
          >
            <div className='flex items-center'>
              <HardDrive
                size={16}
                className='text-ui-foreground-secondary dark:text-gray-400 mr-2'
              />
              <div className='text-left'>
                <span className='text-ui-foreground-primary dark:text-gray-300 block'>
                  Vacuum Database
                </span>
                <span className='text-sm text-ui-foreground-secondary dark:text-gray-400'>
                  Reclaim unused space and defragment
                </span>
              </div>
            </div>
          </button>
        </div>

        {/* Fragmentation Warning */}
        {stats && (stats.fragmentationLevel || 0) > 50 && (
          <div className='bg-feedback-warning/10 dark:bg-yellow-900/20 border border-feedback-warning dark:border-yellow-600 rounded-[var(--radius-lg)] p-4'>
            <div className='flex items-start'>
              <AlertTriangle
                size={16}
                className='text-feedback-warning dark:text-yellow-400 mr-2 mt-0.5 flex-shrink-0'
              />
              <div>
                <span className='text-ui-foreground-primary dark:text-white font-medium block'>
                  High Fragmentation Detected
                </span>
                <p className='text-sm text-ui-foreground-secondary dark:text-gray-400 mt-1'>
                  Your database has high fragmentation (
                  {stats.fragmentationLevel || 0}%). Consider running the vacuum
                  operation to improve performance.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Maintenance Schedule */}
        <div className='bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4'>
          <h4 className='text-ui-foreground-primary dark:text-white font-medium mb-3'>
            Automatic Maintenance
          </h4>
          <div className='space-y-3'>
            <label className='flex items-center justify-between cursor-pointer'>
              <div>
                <span className='text-ui-foreground-primary dark:text-gray-300 block'>
                  Auto-optimize on startup
                </span>
                <span className='text-sm text-ui-foreground-secondary dark:text-gray-400'>
                  Automatically optimize database when app starts
                </span>
              </div>
              <input
                type='checkbox'
                className='form-checkbox h-5 w-5 text-brand-primary rounded'
                checked={settings.autoOptimizeOnStartup}
                onChange={e =>
                  settings.setAutoOptimizeOnStartup(e.target.checked)
                }
              />
            </label>

            <label className='flex items-center justify-between cursor-pointer'>
              <div>
                <span className='text-ui-foreground-primary dark:text-gray-300 block'>
                  Weekly vacuum
                </span>
                <span className='text-sm text-ui-foreground-secondary dark:text-gray-400'>
                  Automatically vacuum database weekly
                </span>
              </div>
              <input
                type='checkbox'
                className='form-checkbox h-5 w-5 text-brand-primary rounded'
                checked={settings.weeklyVacuum}
                onChange={e => settings.setWeeklyVacuum(e.target.checked)}
              />
            </label>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DatabaseMaintenanceSection;
