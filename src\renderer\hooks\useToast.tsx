import { useState } from 'react';
import { createRoot } from 'react-dom/client';
import { X } from 'lucide-react';

export interface ToastOptions {
  title: string;
  description?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

interface ToastState {
  id: string;
  title: string;
  description?: string;
  type: 'success' | 'error' | 'warning' | 'info';
}

const createToastContainer = () => {
  const container = document.getElementById('toast-container');
  if (container) {
    return container;
  }

  const toastContainer = document.createElement('div');
  toastContainer.id = 'toast-container';
  toastContainer.className = 'fixed top-4 right-4 flex flex-col gap-2';
  toastContainer.style.zIndex = 'var(--z-tooltip)';
  document.body.appendChild(toastContainer);
  return toastContainer;
};

const ToastComponent = ({
  toast,
  onClose,
}: {
  toast: ToastState;
  onClose: (id: string) => void;
}) => {
  const getBackgroundColor = (type: ToastState['type']): string => {
    switch (type) {
      case 'success':
        return 'var(--feedback-bg-success)';
      case 'error':
        return 'var(--feedback-bg-error)';
      case 'warning':
        return 'var(--feedback-bg-warning)';
      case 'info':
        return 'var(--feedback-bg-info)';
      default:
        return 'var(--feedback-bg-info)';
    }
  };

  const getBorderColor = (type: ToastState['type']): string => {
    switch (type) {
      case 'success':
        return 'var(--feedback-border-success)';
      case 'error':
        return 'var(--feedback-border-error)';
      case 'warning':
        return 'var(--feedback-border-warning)';
      case 'info':
        return 'var(--feedback-border-info)';
      default:
        return 'var(--feedback-border-info)';
    }
  };

  const getTextColor = (type: ToastState['type']): string => {
    switch (type) {
      case 'success':
        return 'var(--color-feedback-success)';
      case 'error':
        return 'var(--color-feedback-error)';
      case 'warning':
        return 'var(--color-feedback-warning)';
      case 'info':
        return 'var(--color-feedback-info)';
      default:
        return 'var(--color-feedback-info)';
    }
  };

  return (
    <div
      className='w-80 p-4 transition-standard'
      style={{
        backgroundColor: getBackgroundColor(toast.type),
        border: `1px solid ${getBorderColor(toast.type)}`,
        borderRadius: 'var(--radius-lg)',
        boxShadow: 'var(--shadow-md)',
        color: getTextColor(toast.type),
      }}
      role='alert'
    >
      <div className='flex justify-between items-start'>
        <div className='flex-1'>
          <h3
            className='text-sm font-medium'
            style={{
              color: getTextColor(toast.type),
              fontWeight: 'var(--font-weight-medium)',
            }}
          >
            {toast.title}
          </h3>
          {toast.description && (
            <p
              className='mt-1 text-xs opacity-90'
              style={{
                color: getTextColor(toast.type),
                fontSize: 'var(--font-size-xs)',
              }}
            >
              {toast.description}
            </p>
          )}
        </div>
        <button
          className='ml-4 inline-flex opacity-70 hover:opacity-100 transition-standard'
          style={{
            color: getTextColor(toast.type),
            backgroundColor: 'transparent',
            border: 'none',
            padding: '0.25rem',
            borderRadius: 'var(--radius-sm)',
            cursor: 'pointer',
          }}
          onClick={() => onClose(toast.id)}
          onMouseEnter={e => {
            e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
        >
          <X className='w-4 h-4' />
        </button>
      </div>
    </div>
  );
};

export const useToast = () => {
  const [toasts, setToasts] = useState<ToastState[]>([]);

  const toast = (options: ToastOptions) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast: ToastState = {
      id,
      title: options.title,
      description: options.description,
      type: options.type || 'info',
    };

    setToasts(prev => [...prev, newToast]);

    // Create container and render toast
    const container = createToastContainer();
    const toastElement = document.createElement('div');
    toastElement.id = `toast-${id}`;
    container.appendChild(toastElement);

    const root = createRoot(toastElement);

    root.render(<ToastComponent toast={newToast} onClose={closeToast} />);

    // Auto-remove after duration
    if (options.duration !== 0) {
      const duration = options.duration || 3000;
      setTimeout(() => {
        closeToast(id);
      }, duration);
    }

    return id;
  };

  const closeToast = (id: string) => {
    const element = document.getElementById(`toast-${id}`);
    if (element) {
      element.classList.add('opacity-0', 'translate-x-5');
      setTimeout(() => {
        element.remove();
      }, 300);
    }

    setToasts(prev => prev.filter(t => t.id !== id));
  };

  return { toast, closeToast, toasts };
};

export default useToast;
