/* eslint-disable */
/* postcss-import */
/* tailwindcss/base */
/* tailwindcss/components */
/* tailwindcss/utilities */

/* Import design tokens CSS variables */
@import './styles/tokens.css';
@import './styles/index.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Debug helper to check dark mode */
:root[class*="dark"] {
  --is-dark-mode: true;
}

/* Set color-scheme appropriately - default to dark */
:root {
  color-scheme: dark;
}

:root.light {
  color-scheme: light;
}

:root.dark {
  color-scheme: dark;
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    var(--color-ui-background-tertiary) 25%,
    var(--color-ui-background-secondary) 50%,
    var(--color-ui-background-tertiary) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Keyboard Navigation and Accessibility */
/* Focus visible for keyboard navigation */
.keyboard-navigation *:focus {
  outline: none;
}

.keyboard-navigation *:focus-visible {
  outline: 2px solid var(--color-brand-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Specific focus styles for different elements */
.keyboard-navigation button:focus-visible,
.keyboard-navigation a:focus-visible,
.keyboard-navigation input:focus-visible,
.keyboard-navigation select:focus-visible,
.keyboard-navigation textarea:focus-visible {
  outline: 2px solid var(--color-brand-primary);
  outline-offset: 2px;
}

/* Table row focus styles */
.keyboard-navigation tr:focus,
.keyboard-navigation tr:focus-visible {
  outline: 2px solid var(--color-brand-primary);
  outline-offset: -2px;
}

/* Skip to main content link */
.skip-to-main {
  position: absolute;
  left: -999px;
  width: 1px;
  height: 1px;
  top: auto;
}

.skip-to-main:focus {
  position: fixed;
  top: 0;
  left: 0;
  width: auto;
  height: auto;
  padding: var(--spacing-4);
  background: var(--color-ui-background-primary);
  color: var(--color-ui-foreground-primary);
  z-index: var(--zIndex-popover);
  outline: 2px solid var(--color-brand-primary);
}

/* Base styles using design tokens - Apple-inspired */
body {
  margin: 0;
  font-family: var(--font-family-sans, -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue',
    sans-serif);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-ui-foreground-primary);
  background-color: var(--color-ui-background-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Optimized theme transitions for core UI elements */
.app-container,
.card,
.header,
.sidebar,
button,
input,
select,
textarea {
  transition: background-color 200ms ease-out,
              color 200ms ease-out,
              border-color 200ms ease-out;
}

/* Prevent transitions on certain elements */
.no-transition,
.no-transition * {
  transition: none !important;
}

code {
  font-family: var(--font-family-mono, 'SF Mono', Menlo, Monaco, Consolas, 'Courier New',
    monospace);
}

/* Add custom layer components - Apple-inspired */
@layer components {
  /* Apple-style buttons */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg transition-all duration-300 font-medium text-sm;
  }
  
  .btn-primary {
    @apply bg-brand-secondary text-white hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary shadow-sm;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 shadow-sm;
  }

  .btn-danger {
    @apply bg-feedback-error text-white hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-feedback-error shadow-sm;
  }
  
  /* Apple-style cards with subtle shadow */
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden border border-ui-border-light;
  }
  
  /* Apple-style inputs with subtle rounding */
  .input {
    @apply w-full px-4 py-2 border border-ui-border-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-secondary focus:border-transparent bg-white transition-all duration-200;
  }
  
  /* Apple-style labels */
  .label {
    @apply block text-sm font-medium text-ui-foreground-secondary mb-1;
  }

  /* Apple-style select dropdown */
  .select {
    @apply w-full px-4 py-2 pr-10 border border-ui-border-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-secondary focus:border-transparent bg-white appearance-none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2386868B'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
  }

  /* Apple-style checkbox */
  .checkbox {
    @apply h-5 w-5 text-brand-secondary border-ui-border-medium rounded focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary transition-colors duration-200;
  }
}

/* Make app container with rounded corners */
html, body, #root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

/* Set the background color for transparent window */
body {
  background-color: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Root div with rounded corners */
#root {
  border-radius: 12px;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
  background-color: transparent !important;
  /* Add subtle stroke for visibility using CSS variable */
  border: 1px solid var(--window-border-color, rgba(0, 0, 0, 0.15));
  box-sizing: border-box;
  transition: border-color var(--transition-duration-200) var(--transition-easing-apple);
}

/* Specific dark mode fixes for window borders */
.dark #root,
html.dark #root,
body.dark #root {
  /* Use CSS variable for consistent border color */
  border: 1px solid var(--window-border-color, rgba(255, 255, 255, 0.12)) !important;
  outline: none !important;
  /* Keep transparent background */
  background-color: transparent !important;
}
body.dark #root {
  border-radius: 12px !important;
  overflow: hidden !important;
  background-color: transparent !important;
  /* Ensure border follows rounded corners */
  border: 1px solid var(--window-border-color, rgba(255, 255, 255, 0.12)) !important;
}

/* Ensure app container gets the full dark theme */
.app-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  overflow: auto;
  /* Hide scrollbar for desktop while allowing scrolling */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  background-color: var(--color-ui-background-primary, #FFFFFF);
  border-radius: 12px;
}

/* Dark mode app container is fully transparent */
.dark .app-container,
html.dark .app-container,
body.dark .app-container {
  background-color: transparent !important;
  border-radius: 12px !important;
  overflow: hidden !important; /* Changed from auto to hidden to prevent double scrollbar */
}

/* Dark inner container has the actual dark background */
.dark-inner-container {
  background-color: var(--color-ui-background-primary, #1D1D1F) !important;
  border-radius: 12px !important;
  /* Margin to ensure corners are visible */
  margin: 2px !important;
  overflow: auto !important;
  height: calc(100% - 4px) !important; /* Account for margin */
  width: calc(100% - 4px) !important; /* Account for margin */
}

/* Hide the scrollbar for Chrome, Safari and Opera */
.app-container::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbars but keep scroll functionality for desktop */
.overflow-auto::-webkit-scrollbar,
main::-webkit-scrollbar,
.scrollable-content::-webkit-scrollbar {
  display: none;
}

.overflow-auto,
main,
.scrollable-content {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Mobile optimizations for scrolling */
@media (max-width: 768px) {
  html, body, #root, .app-container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    height: auto;
    min-height: 100%;
  }
  
  .scrollable-content {
    overflow-x: auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Show scrollbars on mobile */
  .app-container::-webkit-scrollbar,
  .overflow-auto::-webkit-scrollbar,
  main::-webkit-scrollbar,
  .scrollable-content::-webkit-scrollbar {
    display: block;
    width: 3px;
  }
  
  .app-container,
  .overflow-auto,
  main,
  .scrollable-content {
    scrollbar-width: thin;
    -ms-overflow-style: auto;
  }
}

/* Dark mode specific styles */
.dark {
  color-scheme: dark;
}

.dark .bg-ui-background-primary {
  background-color: var(--color-ui-background-primary);
}

/* Add explicit dark mode styles */
html.dark, body.dark, .dark {
  background-color: #1D1D1F !important;
  color: #FFFFFF !important;
}

/* Fix for dark mode window corners */
html.dark, 
body.dark {
  border-radius: 12px !important;
  overflow: auto !important;
  /* Remove clip-path as it might interfere with proper corners */
  clip-path: none !important;
  background-color: transparent !important;
}

/* Dark mode must maintain full transparency */
html.dark .app-container, 
body.dark .app-container, 
.dark .app-container {
  background-color: transparent !important;
  border-radius: 12px !important;
  overflow: auto !important;
}

html.dark .app-title-bar,
body.dark .app-title-bar,
.dark .app-title-bar {
  background-color: #2C2C2E !important;
}

html.dark img[alt="IVG Logo"],
body.dark img[alt="IVG Logo"],
.dark img[alt="IVG Logo"] {
  filter: brightness(1) !important;
  opacity: 1 !important;
}

/* --- New York Theme (Apple-inspired dark blue/gray) --- */
.new-york,
html.new-york,
body.new-york {
  background-color: #0F172A !important;
  color: #E0E7EF !important;
}

.new-york .app-container,
html.new-york .app-container,
body.new-york .app-container {
  background-color: #0F172A !important;
  color: #E0E7EF !important;
}

.new-york .bg-ui-background-primary,
html.new-york .bg-ui-background-primary,
body.new-york .bg-ui-background-primary {
  background-color: #16213A !important;
}

.new-york .bg-ui-background-secondary,
html.new-york .bg-ui-background-secondary,
body.new-york .bg-ui-background-secondary {
  background-color: #1E293B !important;
}

.new-york .text-ui-foreground-primary,
html.new-york .text-ui-foreground-primary,
body.new-york .text-ui-foreground-primary {
  color: #E0E7EF !important;
}

.new-york .text-ui-foreground-secondary,
html.new-york .text-ui-foreground-secondary,
body.new-york .text-ui-foreground-secondary {
  color: #A0AEC0 !important;
}

.new-york .btn-primary,
html.new-york .btn-primary,
body.new-york .btn-primary {
  background-color: #2563EB !important;
  color: #FFFFFF !important;
}

.new-york .btn-secondary,
html.new-york .btn-secondary,
body.new-york .btn-secondary {
  background-color: #334155 !important;
  color: #E0E7EF !important;
}

.new-york .card,
html.new-york .card,
body.new-york .card {
  background-color: #16213A !important;
  border-color: #334155 !important;
  color: #E0E7EF !important;
}

.new-york .input,
html.new-york .input,
body.new-york .input {
  background-color: #1E293B !important;
  color: #E0E7EF !important;
  border-color: #334155 !important;
}

.new-york .select,
html.new-york .select,
body.new-york .select {
  background-color: #1E293B !important;
  color: #E0E7EF !important;
  border-color: #334155 !important;
}

.new-york .app-title-bar,
html.new-york .app-title-bar,
body.new-york .app-title-bar {
  background-color: #16213A !important;
}

.new-york img[alt="IVG Logo"],
html.new-york img[alt="IVG Logo"],
body.new-york img[alt="IVG Logo"] {
  filter: brightness(1.1) !important;
  opacity: 1 !important;
}

/* --- System Light Theme (softer whites, neutral grays, blue accent) --- */
.system-light,
html.system-light,
body.system-light {
  background-color: #F6F8FA !important;
  color: #222C3A !important;
}

.system-light .app-container,
html.system-light .app-container,
body.system-light .app-container {
  background-color: #F6F8FA !important;
  color: #222C3A !important;
}

.system-light .bg-ui-background-primary,
html.system-light .bg-ui-background-primary,
body.system-light .bg-ui-background-primary {
  background-color: #FFFFFF !important;
}

.system-light .bg-ui-background-secondary,
html.system-light .bg-ui-background-secondary,
body.system-light .bg-ui-background-secondary {
  background-color: #F1F5F9 !important;
}

.system-light .text-ui-foreground-primary,
html.system-light .text-ui-foreground-primary,
body.system-light .text-ui-foreground-primary {
  color: #222C3A !important;
}

.system-light .text-ui-foreground-secondary,
html.system-light .text-ui-foreground-secondary,
body.system-light .text-ui-foreground-secondary {
  color: #6B7280 !important;
}

.system-light .btn-primary,
html.system-light .btn-primary,
body.system-light .btn-primary {
  background-color: #2563EB !important;
  color: #FFFFFF !important;
}

.system-light .btn-secondary,
html.system-light .btn-secondary,
body.system-light .btn-secondary {
  background-color: #E5E7EB !important;
  color: #222C3A !important;
}

.system-light .card,
html.system-light .card,
body.system-light .card {
  background-color: #FFFFFF !important;
  border-color: #E5E7EB !important;
  color: #222C3A !important;
}

.system-light .input,
html.system-light .input,
body.system-light .input {
  background-color: #F1F5F9 !important;
  color: #222C3A !important;
  border-color: #E5E7EB !important;
}

.system-light .select,
html.system-light .select,
body.system-light .select {
  background-color: #F1F5F9 !important;
  color: #222C3A !important;
  border-color: #E5E7EB !important;
}

.system-light .app-title-bar,
html.system-light .app-title-bar,
body.system-light .app-title-bar {
  background-color: #F6F8FA !important;
}

.system-light img[alt="IVG Logo"],
html.system-light img[alt="IVG Logo"],
body.system-light img[alt="IVG Logo"] {
  filter: brightness(1) !important;
  opacity: 1 !important;
}
