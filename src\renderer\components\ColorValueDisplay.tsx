/**
 * @file ColorValueDisplay.tsx
 * @description Component for displaying color values with copy capability
 */

import { useTokens } from '../hooks/useTokens';

interface ColorValueDisplayProps {
  label: string;
  value: string;
  colorPreview?: string;
  isCopied: boolean;
  onCopy: () => void;
}

export default function ColorValueDisplay({
  label,
  value,
  colorPreview,
  isCopied,
  onCopy,
}: ColorValueDisplayProps) {
  const tokens = useTokens();

  // Container classes
  const containerClasses =
    'flex items-center justify-between mb-[var(--spacing-2)] bg-ui-background-primary rounded-[var(--radius-md)] p-[var(--spacing-2)] shadow-sm border border-ui-border-light';

  // Preview classes
  const previewClasses =
    'w-4 h-4 rounded-[var(--radius-sm)] mr-[var(--spacing-2)]';

  // Label classes
  const labelClasses = 'text-sm font-medium text-ui-foreground-primary';

  // Value code classes
  const codeClasses =
    'text-sm bg-ui-background-secondary px-[var(--spacing-2)] py-[var(--spacing-1)] rounded-[var(--radius-sm)] mr-[var(--spacing-1)]';

  // Copy button classes
  const getCopyButtonClasses = () => {
    return isCopied
      ? 'text-feedback-success hover:text-feedback-success hover:bg-feedback-success/10 p-[var(--spacing-1)] rounded-full transition-colors'
      : 'text-ui-foreground-secondary hover:text-brand-primary transition-colors p-[var(--spacing-1)] rounded-full hover:bg-brand-primary/10';
  };

  return (
    <div
      className={containerClasses}
      style={{
        transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
      }}
    >
      <div className='flex items-center'>
        {colorPreview && (
          <div
            className={previewClasses}
            style={{ backgroundColor: colorPreview }}
           />
        )}
        <span className={labelClasses}>{label}:</span>
      </div>
      <div className='flex items-center'>
        <code className={codeClasses}>{value}</code>
        <button
          className={getCopyButtonClasses()}
          onClick={onCopy}
          title={`Copy ${label}`}
          aria-label={`Copy ${label} value`}
        >
          {isCopied ? (
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='h-5 w-5'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M5 13l4 4L19 7'
              />
            </svg>
          ) : (
            <svg
              xmlns='http://www.w3.org/2000/svg'
              className='h-5 w-5'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'
            >
              <rect x='9' y='9' width='13' height='13' rx='2' ry='2' />
              <path d='M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1' />
            </svg>
          )}
        </button>
      </div>
    </div>
  );
}
