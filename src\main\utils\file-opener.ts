/**
 * @file file-opener.ts
 * @description Secure utility functions for opening files and URLs with path traversal protection
 */

import { shell, BrowserWindow } from 'electron';
import * as fs from 'fs';
import {
  openUrlInDefaultBrowser,
  isSharePointOrOneDriveUrl,
} from './browser-opener';
import { validateFilePath } from './secure-file-operations';
import { defaultURLValidator } from './url-validator';

/**
 * URL validation patterns for security
 */
// const ALLOWED_URL_PROTOCOLS = ['http:', 'https:'];
// const BLOCKED_URL_PATTERNS = [
//   /javascript:/i,
//   /data:/i,
//   /vbscript:/i,
//   /file:/i,
//   /ftp:/i,
//   /<script/i,
//   /onload=/i,
//   /onerror=/i
// ];

/**
 * Validates if a URL is safe to open using comprehensive validation
 * @param url The URL to validate
 * @returns True if the URL is safe to open
 */
export function isValidSafeUrl(url: string): boolean {
  const result = defaultURLValidator.validateURL(url);

  if (!result.isValid) {
    console.warn(`Blocked unsafe URL: ${result.error}`);
    return false;
  }

  if (result.warnings && result.warnings.length > 0) {
    console.warn(`URL validation warnings: ${result.warnings.join(', ')}`);
  }

  return true;
}

/**
 * Determines if a path is a web URL
 * @param path The path to check
 * @returns True if the path is a web URL
 */
export function isWebUrl(path: string): boolean {
  return (
    path.startsWith('http://') ||
    path.startsWith('https://') ||
    path.startsWith('www.') ||
    path.includes('sharepoint.com') ||
    path.includes('onedrive.com')
  );
}

/**
 * Opens a file or URL with the appropriate application
 * @param path The file path or URL to open
 * @returns Promise resolving to true if successful, false otherwise
 */
export async function openFileOrUrl(path: string): Promise<boolean> {
  try {
    // Input validation
    if (!path || typeof path !== 'string') {
      console.error('Invalid path provided to openFileOrUrl');
      return false;
    }

    // Get the current focused window
    const focusedWindow = BrowserWindow.getFocusedWindow();

    if (isWebUrl(path)) {
      // Validate URL security before opening
      if (!isValidSafeUrl(path)) {
        console.error(`Blocked unsafe URL: ${path}`);
        return false;
      }

      // It's a web URL, open in browser
      console.log(`Opening URL in browser: ${path}`);

      // For SharePoint and OneDrive URLs, use our specialized handler
      if (isSharePointOrOneDriveUrl(path)) {
        console.log(`Using specialized SharePoint URL handler for: ${path}`);
        return await openUrlInDefaultBrowser(path); // This will use the specialized method
      }

      // For other URLs, use the standard approach
      return await openUrlInDefaultBrowser(path);
    } else {
      // It's a local file path - validate for security
      const validation = validateFilePath(path);
      if (!validation.isValid) {
        console.error(`Invalid file path: ${validation.error}`);
        return false;
      }

      // Use the sanitized path
      const safePath = validation.sanitizedPath!;

      // Check if the file exists before trying to open it
      if (fs.existsSync(safePath)) {
        // Additional security check - ensure it's a regular file
        const stats = fs.statSync(safePath);
        if (!stats.isFile()) {
          console.error(`Path is not a regular file: ${safePath}`);
          return false;
        }

        // It's a local file, open with default application
        console.log(`Opening local file: ${safePath}`);
        await shell.openPath(safePath);

        // Restore focus to the app window after a short delay
        setTimeout(() => {
          if (focusedWindow && !focusedWindow.isDestroyed()) {
            if (focusedWindow.isMinimized()) {
              focusedWindow.restore();
            }
            focusedWindow.focus();
            console.log('Restored focus to application window');
          }
        }, 1000); // 1 second delay

        return true;
      } else {
        console.error(`File does not exist: ${safePath}`);
        return false;
      }
    }
  } catch (error) {
    console.error(`Error opening file or URL: ${path}`, error);

    // As a fallback, try multiple methods (with same security validation)
    try {
      if (isWebUrl(path)) {
        // Re-validate URL security in fallback
        if (!isValidSafeUrl(path)) {
          console.error(`Blocked unsafe URL in fallback: ${path}`);
          return false;
        }

        // Try the direct shell method first
        try {
          await shell.openExternal(path, { activate: true });

          // Restore focus to the app window after a short delay
          const focusedWindow = BrowserWindow.getFocusedWindow();
          setTimeout(() => {
            if (focusedWindow && !focusedWindow.isDestroyed()) {
              if (focusedWindow.isMinimized()) {
                focusedWindow.restore();
              }
              focusedWindow.focus();
              console.log('Restored focus to application window (fallback)');
            }
          }, 1500); // 1.5 second delay

          return true;
        } catch (shellError) {
          console.error(`Shell method failed: ${shellError}`);
        }

        // Try the platform-specific command as a last resort
        const success = await openUrlInDefaultBrowser(path);
        if (success) {
          return true;
        }
      }
    } catch (fallbackError) {
      console.error(`All fallback methods failed: ${fallbackError}`);
    }

    return false;
  }
}
