/**
 * @file authentication-manager.ts
 * @description Core authentication manager responsible for OAuth flows and token exchange
 */

import { shell } from 'electron';
import { getSupabaseClient } from '../supabase-client';
import { LoggerFactory, ILogger } from '../../utils/logger.service';
import { AuthRedirectServer } from '../auth-redirect-server';
import { Result, success, failure, AsyncResult, tryCatch } from '../../../shared/types/result.types';
import { toError } from '../../../shared/types/type-guards';
import crypto from 'crypto';

export interface AuthFlowResult {
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  user?: any;
  error?: string;
  session?: any;
}

/**
 * Auth operation result types using Result pattern
 */
export type AuthResult<T> = Result<T, AuthError>;

/**
 * Authentication specific error type
 */
export interface AuthError {
  readonly type: 'NETWORK_ERROR' | 'TIMEOUT' | 'VALIDATION_ERROR' | 'AUTH_FAILED' | 'REDIRECT_ERROR';
  readonly message: string;
  readonly code?: string;
  readonly details?: Record<string, unknown>;
}

export interface AuthFlowOptions {
  provider: 'google';
  redirectTo?: string;
  useLocalRedirect?: boolean;
  timeoutMs?: number;
}

export interface AuthState {
  state: string;
  codeVerifier?: string;
  nonce: string;
  provider: string;
  timestamp: number;
}

export interface IAuthRedirectHandler {
  start(callback: (url: string) => void): Promise<void>;
  stop(): void;
}

/**
 * Core authentication manager responsible for OAuth flows
 */
export class AuthenticationManager {
  private readonly logger: ILogger;
  private redirectServer: AuthRedirectServer | null = null;
  private stateStore = new Map<string, AuthState>();
  
  // Security constants
  private readonly STATE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes
  private readonly DEFAULT_TIMEOUT_MS = 120000; // 2 minutes
  private readonly MAX_CALLBACK_RETRIES = 3;
  private callbackAttempts = 0;

  constructor(logger?: ILogger) {
    this.logger = logger || LoggerFactory.getInstance().createLogger('AuthenticationManager');
  }

  /**
   * Initiate OAuth flow with specified provider
   */
  async initiateOAuthFlow(options: AuthFlowOptions): Promise<{
    authUrl: string;
    state: string;
    redirectHandler: IAuthRedirectHandler | null;
  }> {
    const startTime = performance.now();
    try {
      const { provider, useLocalRedirect = true, timeoutMs = this.DEFAULT_TIMEOUT_MS } = options;
      
      // Log comprehensive device and environment information
      const deviceInfo = {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        electronVersion: process.versions.electron,
        isPackaged: require('electron').app.isPackaged,
        userAgent: 'ChromaSync-Electron',
        machineId: require('os').hostname(),
        networkInterfaces: Object.keys(require('os').networkInterfaces()),
      };
    
      this.logger.info('Initiating OAuth flow with device info', {
        provider,
        useLocalRedirect,
        timeoutMs,
        deviceInfo,
        operation: 'initiateOAuthFlow'
      });

      // Cleanup any existing state
      this.cleanup();
      this.callbackAttempts = 0;

      // Generate security parameters
      const state = this.generateSecureState();
      const nonce = this.generateSecureNonce();

      // Store state for validation
      this.storeAuthState(state, { provider, nonce });

      // Determine redirect URL
      let redirectTo: string;
      let redirectHandler: IAuthRedirectHandler | null = null;

      if (useLocalRedirect) {
        try {
          this.redirectServer = new AuthRedirectServer();
          redirectHandler = this.redirectServer;
          await this.redirectServer.start(() => {}); // Callback will be set by caller
          redirectTo = 'http://localhost:3000/auth/callback';
          
          this.logger.info('Local redirect server started', {
            redirectTo,
            operation: 'initiateOAuthFlow'
          });
        } catch (error) {
          this.logger.warn('Failed to start local redirect server, falling back to web', {
            error: error as Error,
            operation: 'initiateOAuthFlow'
          });
          redirectTo = options.redirectTo || 'https://auth.chromasync.app/auth/callback';
        }
      } else {
        redirectTo = options.redirectTo || 'https://auth.chromasync.app/auth/callback';
      }

      // Start OAuth flow with Supabase
      const authUrl = await this.createOAuthUrl(provider, redirectTo);

      const result = {
        authUrl,
        state,
        redirectHandler
      };
      
      const endTime = performance.now();
      this.logger.debug(`Operation completed: initiateOAuthFlow`, { 
        operation: 'initiateOAuthFlow', 
        duration: endTime - startTime 
      });
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      this.logger.error(`Operation failed: initiateOAuthFlow`, error as Error, { 
        operation: 'initiateOAuthFlow', 
        duration: endTime - startTime 
      });
      throw error;
    }
  }

  /**
   * Handle OAuth callback and exchange code for tokens
   */
  async handleCallback(callbackUrl: string): Promise<AuthFlowResult> {
    const startTime = performance.now();
    try {
      this.logger.info('Processing OAuth callback', {
        callbackUrl: callbackUrl.substring(0, 50) + '...',
        operation: 'handleCallback'
      });

      // Rate limiting
      this.callbackAttempts++;
      if (this.callbackAttempts > this.MAX_CALLBACK_RETRIES) {
        const error = 'Too many callback attempts - possible security threat';
        this.logger.error(error, undefined, { operation: 'handleCallback' });
        return { success: false, error };
      }

      try {
        // Validate callback URL
        if (!this.validateCallbackUrl(callbackUrl)) {
          throw new Error('Invalid or suspicious callback URL');
        }

        // Parse callback parameters
        const { code, state, error: oauthError } = this.parseCallbackParams(callbackUrl);

        // Check for OAuth errors
        if (oauthError) {
          throw new Error(oauthError === 'access_denied' ? 'access_denied' : oauthError);
        }

        // Validate state parameter
        if (state && !this.validateState(state)) {
          throw new Error('Invalid or expired state parameter');
        }

        // Exchange authorization code for tokens
        if (code) {
          const result = await this.exchangeCodeForSession(code);
          const endTime = performance.now();
          this.logger.debug(`Operation completed: handleCallback`, { 
            operation: 'handleCallback', 
            duration: endTime - startTime 
          });
          return result;
        }

        // Fallback: handle implicit flow tokens
        const { access_token, refresh_token } = this.parseTokensFromCallback(callbackUrl);
        if (access_token && refresh_token) {
          const result = await this.setSessionFromTokens(access_token, refresh_token);
          const endTime = performance.now();
          this.logger.debug(`Operation completed: handleCallback`, { 
            operation: 'handleCallback', 
            duration: endTime - startTime 
          });
          return result;
        }

        throw new Error('No authorization code or tokens found in callback');

      } catch (error) {
        const endTime = performance.now();
        this.logger.error('Callback processing failed', error as Error, {
          operation: 'handleCallback',
          duration: endTime - startTime
        });
        
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Callback processing failed'
        };
      }
    } catch (error) {
      const endTime = performance.now();
      this.logger.error(`Operation failed: handleCallback`, error as Error, { 
        operation: 'handleCallback', 
        duration: endTime - startTime 
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Callback processing failed'
      };
    }
  }

  /**
   * Exchange authorization code for session (PKCE flow)
   */
  private async exchangeCodeForSession(code: string): Promise<AuthFlowResult> {
    const startTime = performance.now();
    try {
      this.logger.debug('Exchanging authorization code for session', {
        operation: 'exchangeCodeForSession'
      });

      const supabase = getSupabaseClient();

      // Add timeout protection
      const exchangePromise = supabase.auth.exchangeCodeForSession(code);
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Code exchange timeout')), 30000)
      );

      const { data, error } = await Promise.race([
        exchangePromise,
        timeoutPromise
      ]) as any;

      if (error) {
        this.logger.error('Code exchange failed', error, {
          operation: 'exchangeCodeForSession'
        });
        throw new Error('Authentication failed - please try again');
      }

      if (!data?.session) {
        throw new Error('No session returned from code exchange');
      }

      this.logger.info('PKCE code exchange successful', {
        userEmail: data.session.user?.email,
        operation: 'exchangeCodeForSession'
      });

      // CRITICAL: Ensure the session is set in the Supabase client for subsequent calls
      // The exchangeCodeForSession automatically sets it, but we explicitly verify here
      try {
        const { data: { session: currentSession } } = await supabase.auth.getSession();
        if (!currentSession) {
          this.logger.warn('Session not found after code exchange, setting explicitly');
          await supabase.auth.setSession({
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token
          });
        }
        this.logger.info('Session verified in Supabase client');
      } catch (sessionError) {
        this.logger.error('Failed to verify/set session after code exchange', sessionError instanceof Error ? sessionError : undefined);
        // Don't throw - the auth still succeeded, this is a secondary verification
      }

      const result = {
        success: true,
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        user: data.session.user,
        session: data.session
      };
      
      const endTime = performance.now();
      this.logger.debug(`Operation completed: exchangeCodeForSession`, { 
        operation: 'exchangeCodeForSession', 
        duration: endTime - startTime 
      });
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      this.logger.error(`Operation failed: exchangeCodeForSession`, error as Error, { 
        operation: 'exchangeCodeForSession', 
        duration: endTime - startTime 
      });
      throw error;
    }
  }

  /**
   * Set session from tokens (implicit flow fallback)
   */
  private async setSessionFromTokens(accessToken: string, refreshToken: string): Promise<AuthFlowResult> {
    const startTime = performance.now();
    try {
      this.logger.debug('Setting session from tokens (implicit flow)', {
        operation: 'setSessionFromTokens'
      });

      const supabase = getSupabaseClient();
      const { data, error } = await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken
      });

      if (error) {
        this.logger.error('Failed to set session from tokens', error, {
          operation: 'setSessionFromTokens'
        });
        throw error;
      }

      this.logger.info('Implicit flow session established', {
        userEmail: data.session?.user?.email,
        operation: 'setSessionFromTokens'
      });

      const result = {
        success: true,
        accessToken,
        refreshToken,
        user: data.session?.user,
        session: data.session
      };
      
      const endTime = performance.now();
      this.logger.debug(`Operation completed: setSessionFromTokens`, { 
        operation: 'setSessionFromTokens', 
        duration: endTime - startTime 
      });
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      this.logger.error(`Operation failed: setSessionFromTokens`, error as Error, { 
        operation: 'setSessionFromTokens', 
        duration: endTime - startTime 
      });
      throw error;
    }
  }

  /**
   * Get current session
   */
  async getCurrentSession(): Promise<any> {
    const supabase = getSupabaseClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    // Log session status for debugging
    if (session) {
      this.logger.debug('Current session found', {
        userEmail: session.user?.email,
        hasAccessToken: !!session.access_token,
        operation: 'getCurrentSession'
      });
    } else {
      this.logger.debug('No current session found', { operation: 'getCurrentSession' });
    }
    
    return session;
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<any> {
    const supabase = getSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  }

  /**
   * Sign out user
   */
  async signOut(): Promise<void> {
    const startTime = performance.now();
    try {
      this.logger.info('Signing out user', { operation: 'signOut' });

      const supabase = getSupabaseClient();
      await supabase.auth.signOut();
      
      // Clean up state
      this.cleanup();
      this.stateStore.clear();
      this.callbackAttempts = 0;

      this.logger.info('Sign out completed', { operation: 'signOut' });
      
      const endTime = performance.now();
      this.logger.debug(`Operation completed: signOut`, { 
        operation: 'signOut', 
        duration: endTime - startTime 
      });
    } catch (error) {
      const endTime = performance.now();
      this.logger.error('Sign out failed', error as Error, { 
        operation: 'signOut', 
        duration: endTime - startTime 
      });
      throw error;
    }
  }

  /**
   * Alias for signOut() to match router expectations
   */
  async logout(): Promise<void> {
    return this.signOut();
  }

  /**
   * Simplified login method that initiates OAuth flow and returns result
   */
  async login(options: { provider?: 'google' } = {}): Promise<AuthFlowResult> {
    const provider = options.provider || 'google';
    
    try {
      const oauthResult = await this.initiateOAuthFlow({ provider });
      
      // For now, return the auth URL - in a real implementation,
      // this would handle the full flow including callback
      return {
        success: true,
        user: null,
        error: `Please visit: ${oauthResult.authUrl}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed'
      };
    }
  }

  /**
   * Get current authentication state
   */
  async getAuthState(): Promise<{ isAuthenticated: boolean; user?: any; session?: any }> {
    try {
      const session = await this.getCurrentSession();
      const user = await this.getCurrentUser();
      
      return {
        isAuthenticated: !!session && !!user,
        user,
        session
      };
    } catch (error) {
      this.logger.error('Error getting auth state:', error as Error);
      return {
        isAuthenticated: false
      };
    }
  }

  /**
   * Accept GDPR consent (placeholder implementation)
   */
  async acceptGDPR(): Promise<{ success: boolean }> {
    // TODO: Implement actual GDPR consent logic
    this.logger.info('GDPR consent accepted');
    return { success: true };
  }

  /**
   * Refresh current session
   */
  async refreshSession(): Promise<AuthFlowResult> {
    const startTime = performance.now();
    try {
      this.logger.debug('Refreshing session', { operation: 'refreshSession' });

      const supabase = getSupabaseClient();
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        const authError = toError(error.message || 'Session refresh failed');
        this.logger.error('Session refresh failed', authError, { operation: 'refreshSession' });
        throw authError;
      }

      if (!data?.session) {
        throw toError('No session returned from refresh');
      }

      this.logger.debug('Session refreshed successfully', {
        userEmail: data.session.user?.email,
        operation: 'refreshSession'
      });

      const result = {
        success: true,
        accessToken: data.session.access_token,
        refreshToken: data.session.refresh_token,
        user: data.session.user,
        session: data.session
      };
      
      const endTime = performance.now();
      this.logger.debug(`Operation completed: refreshSession`, { 
        operation: 'refreshSession', 
        duration: endTime - startTime 
      });
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      this.logger.error('Session refresh failed', error as Error, { 
        operation: 'refreshSession', 
        duration: endTime - startTime 
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Session refresh failed'
      };
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.redirectServer) {
      this.redirectServer.stop();
      this.redirectServer = null;
    }
    
    this.cleanupExpiredStates();
    
    this.logger.debug('Authentication manager cleanup completed');
  }

  // Private helper methods

  private generateSecureState(): string {
    return crypto.randomBytes(32).toString('base64url');
  }

  private generateSecureNonce(): string {
    return crypto.randomBytes(16).toString('base64url');
  }

  private storeAuthState(state: string, authState: Partial<AuthState>): void {
    this.stateStore.set(state, {
      state,
      timestamp: Date.now(),
      nonce: this.generateSecureNonce(),
      provider: 'google',
      ...authState
    });
  }

  private validateState(receivedState: string): boolean {
    const stored = this.stateStore.get(receivedState);
    if (!stored) {
      this.logger.error('Invalid state parameter - not found', undefined, { operation: 'validateState' });
      return false;
    }

    const isExpired = Date.now() - stored.timestamp > this.STATE_EXPIRY_MS;
    if (isExpired) {
      this.logger.error('State parameter expired', undefined, { operation: 'validateState' });
      this.stateStore.delete(receivedState);
      return false;
    }

    this.stateStore.delete(receivedState);
    return true;
  }

  private cleanupExpiredStates(): void {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [key, value] of this.stateStore.entries()) {
      if (now - value.timestamp > this.STATE_EXPIRY_MS) {
        this.stateStore.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.logger.debug('Cleaned up expired states', { count: cleaned });
    }
  }

  private validateCallbackUrl(url: string): boolean {
    try {
      if (!url.startsWith('chromasync://auth/callback') && 
          !url.startsWith('http://localhost:3000/auth/callback') &&
          !url.startsWith('https://auth.chromasync.app/auth/callback')) {
        this.logger.error('Invalid callback URL format', undefined, {
          urlStart: url.substring(0, 50),
          operation: 'validateCallbackUrl'
        });
        return false;
      }

      const urlObj = new URL(url);
      const params = urlObj.searchParams;
      const hash = urlObj.hash;
      
      if (params.get('javascript:') || hash.includes('javascript:')) {
        this.logger.error('Potential XSS attempt in callback URL', undefined, {
          operation: 'validateCallbackUrl'
        });
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('URL parsing error', error as Error, {
        operation: 'validateCallbackUrl'
      });
      return false;
    }
  }

  private parseCallbackParams(url: string): { code?: string; state?: string; error?: string } {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;
    const hashParams = new URLSearchParams(urlObj.hash.substring(1));

    return {
      code: params.get('code') || hashParams.get('code') || undefined,
      state: params.get('state') || hashParams.get('state') || undefined,
      error: params.get('error') || hashParams.get('error') || undefined
    };
  }

  private parseTokensFromCallback(url: string): { access_token?: string; refresh_token?: string } {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;
    const hashParams = new URLSearchParams(urlObj.hash.substring(1));

    return {
      access_token: params.get('access_token') || hashParams.get('access_token') || undefined,
      refresh_token: params.get('refresh_token') || hashParams.get('refresh_token') || undefined
    };
  }

  private async createOAuthUrl(provider: string, redirectTo: string): Promise<string> {
    this.logger.info('Creating OAuth URL', {
      provider,
      redirectTo,
      operation: 'createOAuthUrl'
    });

    const supabase = getSupabaseClient();
    
    // Log Supabase client status
    this.logger.info('Supabase client status', {
      hasClient: !!supabase,
      hasAuth: !!supabase?.auth,
      operation: 'createOAuthUrl'
    });
    
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: provider as any,
      options: {
        redirectTo,
        skipBrowserRedirect: true, // Critical for Electron: we handle the browser manually
        queryParams: {
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });

    if (error || !data.url) {
      const errorMessage = error?.message || 'Failed to get OAuth URL';
      const authError = toError(errorMessage);
      this.logger.error('Failed to create OAuth URL', authError, {
        provider,
        redirectTo,
        errorCode: error?.status,
        errorMessage: error?.message,
        hasData: !!data,
        dataUrl: data?.url,
        operation: 'createOAuthUrl'
      });
      throw authError;
    }

    this.logger.info('OAuth URL created successfully', {
      provider,
      redirectTo,
      urlLength: data.url.length,
      operation: 'createOAuthUrl'
    });

    return data.url;
  }

  /**
   * Open OAuth URL in system browser
   */
  async openOAuthUrlInBrowser(url: string): Promise<void> {
    this.logger.info('Opening OAuth URL in system browser', { operation: 'openOAuthUrlInBrowser' });
    
    try {
      await shell.openExternal(url);
    } catch (error) {
      const authError = toError('Failed to open browser');
      this.logger.error('Failed to open browser', authError, {
        operation: 'openOAuthUrlInBrowser',
        originalError: error instanceof Error ? error.message : String(error)
      });
      throw authError;
    }
  }

  /**
   * Initiate OAuth flow using Result pattern
   */
  async initiateOAuthFlowResult(options: AuthFlowOptions): AsyncResult<{
    authUrl: string;
    state: string;
    redirectHandler: IAuthRedirectHandler | null;
  }, AuthError> {
    try {
      const result = await this.initiateOAuthFlow(options);
      return success(result);
    } catch (error) {
      return failure({
        type: 'AUTH_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
        code: 'OAUTH_INIT_FAILED'
      });
    }
  }

  /**
   * Handle callback using Result pattern
   */
  async handleCallbackResult(callbackUrl: string): AsyncResult<AuthFlowResult, AuthError> {
    try {
      const result = await this.handleCallback(callbackUrl);
      return success(result);
    } catch (error) {
      return failure({
        type: 'AUTH_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
        code: 'CALLBACK_FAILED'
      });
    }
  }

  /**
   * Refresh session using Result pattern
   */
  async refreshSessionResult(): AsyncResult<AuthFlowResult, AuthError> {
    try {
      const result = await this.refreshSession();
      return success(result);
    } catch (error) {
      return failure({
        type: 'AUTH_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
        code: 'REFRESH_FAILED'
      });
    }
  }

  /**
   * Sign out using Result pattern
   */
  async signOutResult(): AsyncResult<void, AuthError> {
    try {
      await this.signOut();
      return success(undefined);
    } catch (error) {
      return failure({
        type: 'AUTH_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
        code: 'SIGNOUT_FAILED'
      });
    }
  }

  /**
   * Get authentication manager health status
   */
  getHealthStatus(): {
    healthy: boolean;
    activeStates: number;
    redirectServerRunning: boolean;
    callbackAttempts: number;
  } {
    return {
      healthy: this.callbackAttempts < this.MAX_CALLBACK_RETRIES,
      activeStates: this.stateStore.size,
      redirectServerRunning: !!this.redirectServer,
      callbackAttempts: this.callbackAttempts
    };
  }
}