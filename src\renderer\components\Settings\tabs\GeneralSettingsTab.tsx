/**
 * @file GeneralSettingsTab.tsx
 * @description General settings tab component with theme, logo, and accessibility options
 */

import React from 'react';
import ThemeSection from '../sections/ThemeSection';
import ImportExportSection from '../sections/ImportExportSection';
import AccessibilitySection from '../sections/AccessibilitySection';
import AboutSection from '../sections/AboutSection';
import ResetSection from '../sections/ResetSection';
import SyncStatusSection from '../sections/SyncStatusSection';
import SyncSettingsSection from '../sections/SyncSettingsSection';

/**
 * General settings tab component
 */
export const GeneralSettingsTab: React.FC = () => {
  return (
    <div className='space-y-8'>
      {/* Appearance Settings */}
      <ThemeSection />
      <AccessibilitySection />

      {/* Data & Sync Settings */}
      <SyncStatusSection />
      <SyncSettingsSection />
      <ImportExportSection />

      {/* Application Info & Actions */}
      <AboutSection />
      <ResetSection />
    </div>
  );
};

export default GeneralSettingsTab;
