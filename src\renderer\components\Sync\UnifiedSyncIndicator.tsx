import React from 'react';
import { RefreshCw, Cloud, CloudOff, AlertCircle, Check, Clock, Database, Zap } from 'lucide-react';
import { useSyncActions } from '../../store/sync.store';
import { SyncDetailedView } from './SyncDetailedView';
import { useSyncIndicatorState, SyncMetrics } from '../../hooks/sync/useSyncIndicatorState';
import { useSyncIndicatorEvents } from '../../hooks/sync/useSyncIndicatorEvents';
import { useSyncIndicatorDisplay } from '../../hooks/sync/useSyncIndicatorDisplay';
import { SyncStatus as SyncStatusEnum } from '../../../shared/constants/sync-status';

const iconMap = {
  RefreshCw,
  Cloud,
  CloudOff,
  AlertCircle,
  Check,
  Clock,
  Database,
  Zap,
};

export const UnifiedSyncIndicator: React.FC = () => {
  const { syncData } = useSyncActions();
  const {
    status,
    isManualSyncing,
    setIsManualSyncing,
    hasUnsyncedChanges,
    setHasUnsyncedChanges,
    lastSync,
    setLastSync,
    tooltip,
    setTooltip,
    storeRefreshStatus,
    setStoreRefreshStatus,
    syncProgress,
    setSyncProgress,
    queueStats,
    setQueueStats,
    metrics,
    setMetrics,
    currentPhase,
    setCurrentPhase,
    detailedMessage,
    setDetailedMessage,
    showDetailedView,
    setShowDetailedView,
  } = useSyncIndicatorState();

  useSyncIndicatorEvents(
    isManualSyncing,
    setIsManualSyncing,
    setHasUnsyncedChanges,
    setLastSync,
    setSyncProgress,
    setQueueStats,
    (metrics: SyncMetrics | null) => {
      if (metrics) {
        setMetrics(metrics);
      }
    },
    setCurrentPhase,
    setDetailedMessage,
    setStoreRefreshStatus
  );

  const {
    statusIcon,
    statusColor,
    statusText,
    getProgressPercentage,
    formatLastSyncTime,
    formatQueueInfo,
  } = useSyncIndicatorDisplay(
    status,
    isManualSyncing,
    hasUnsyncedChanges,
    syncProgress,
    queueStats,
    lastSync,
    currentPhase
  );

  const handleManualSync = async () => {
    if (isManualSyncing || status === SyncStatusEnum.SYNCING) {return;}

    setIsManualSyncing(true);
    setStoreRefreshStatus({ colorStore: false, productStore: false });
    
    try {
      await syncData();
    } catch (error) {
      console.error('[UnifiedSyncIndicator] ❌ Manual sync failed:', error);
      setIsManualSyncing(false);
      setStoreRefreshStatus({ colorStore: false, productStore: false });
    }
  };

  const StatusIcon = iconMap[statusIcon as keyof typeof iconMap] || Cloud;

  return (
    <div className="flex items-center gap-2">
      <div 
        className={`flex items-center gap-1.5 cursor-pointer ${statusColor}`}
        onClick={() => setShowDetailedView(!showDetailedView)}
        title="Click for detailed view"
      >
        <StatusIcon className={`w-4 h-4 ${isManualSyncing || status === SyncStatusEnum.SYNCING ? 'animate-spin' : ''}`} />
        <span className="text-sm font-medium">{statusText}</span>
        
        {syncProgress && (isManualSyncing || status === SyncStatusEnum.SYNCING) && (
          <div className="w-16 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden ml-2">
            <div 
              className="h-full bg-blue-500 transition-all duration-300 ease-out"
              style={{ width: `${getProgressPercentage()}%` }}
            />
          </div>
        )}
      </div>

      {metrics && metrics.health && metrics.health.status !== 'healthy' && (
        <div className="flex items-center" title={`Network: ${metrics.health.networkQuality}`}>
          <Zap className="w-3 h-3 text-orange-500" />
        </div>
      )}

      {queueStats && ((queueStats.memoryQueue?.pending || 0) > 0 || (queueStats.persistentQueue?.pending || 0) > 0) && (
        <div className="flex items-center" title={formatQueueInfo()}>
          <Clock className="w-3 h-3 text-yellow-500" />
        </div>
      )}

      <button
        onClick={handleManualSync}
        disabled={isManualSyncing || status === SyncStatusEnum.SYNCING}
        className={`
          p-1.5 rounded-md transition-all duration-200
          ${isManualSyncing || status === SyncStatusEnum.SYNCING
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-gray-100 dark:hover:bg-gray-800 active:scale-95'
          }
          ${hasUnsyncedChanges 
            ? 'text-yellow-600 dark:text-yellow-400' 
            : 'text-gray-600 dark:text-gray-300'
          }
        `}
        title={tooltip}
        aria-label="Sync now"
      >
        <RefreshCw 
          className={`w-4 h-4 ${
            isManualSyncing || status === SyncStatusEnum.SYNCING ? 'animate-spin' : ''
          }`} 
        />
      </button>

      <SyncDetailedView
        isOpen={showDetailedView}
        onClose={() => setShowDetailedView(false)}
        syncProgress={syncProgress}
        queueStats={queueStats}
        metrics={metrics}
        lastSync={lastSync}
        getStatusText={() => statusText}
        getStatusColor={() => statusColor}
        getProgressPercentage={getProgressPercentage}
        formatTimeRemaining={(seconds: number) => `${seconds}s`}
      />
    </div>
  );
};