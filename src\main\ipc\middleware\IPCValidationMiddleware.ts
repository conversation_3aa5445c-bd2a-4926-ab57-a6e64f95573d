/**
 * @file IPCValidationMiddleware.ts
 * @description IPC validation middleware following Electron security guidelines
 *
 * This middleware implements:
 * 1. Input validation and sanitization for all IPC requests
 * 2. Sender verification to prevent unauthorized access
 * 3. Channel whitelisting to only allow known IPC channels
 * 4. Rate limiting to prevent abuse
 *
 * Reference: https://www.electronjs.org/docs/latest/tutorial/security#12-verify-webview-options-before-creation
 */

import { ipcMain, IpcMainEvent } from 'electron';
import Ajv, { ValidateFunction } from 'ajv';
import addFormats from 'ajv-formats';

// Types for validation
// interface IPCRequest<T = any> {
//   channel: string;
//   data: T;
//   timestamp: number;
//   requestId: string;
// }

interface IPCResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: number;
  requestId: string;
}

interface ValidationRule {
  channel: string;
  schema: object; // Use object for AJV v6 compatibility
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
  requireAuth?: boolean;
  allowedOrigins?: string[];
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

/**
 * IPC Validation Middleware following Electron security best practices
 */
export class IPCValidationMiddleware {
  private static instance: IPCValidationMiddleware;
  private ajv: any;
  private validationRules = new Map<string, ValidationRule>();
  private rateLimitMap = new Map<string, RateLimitEntry>();
  private allowedChannels = new Set<string>();

  private constructor() {
    this.ajv = new Ajv({
      allErrors: true,
      removeAdditional: true, // Remove additional properties for security
      useDefaults: true,
      coerceTypes: false, // Strict type checking
    });
    addFormats(this.ajv);
  }

  static getInstance(): IPCValidationMiddleware {
    if (!IPCValidationMiddleware.instance) {
      IPCValidationMiddleware.instance = new IPCValidationMiddleware();
    }
    return IPCValidationMiddleware.instance;
  }

  /**
   * Register a validation rule for an IPC channel
   */
  registerValidation(rule: ValidationRule): void {
    console.log(
      `[IPCValidation] Registering validation for channel: ${rule.channel}`
    );

    // Compile schema for performance
    const validator = this.ajv.compile(rule.schema);

    this.validationRules.set(rule.channel, {
      ...rule,
      schema: rule.schema,
    });

    this.allowedChannels.add(rule.channel);

    // Store compiled validator separately for performance
    (this.validationRules.get(rule.channel) as any).validator = validator;
  }

  /**
   * Validate sender origin and permissions
   * Following Electron security guideline: "always validate incoming IPC messages sender property"
   */
  private validateSender(event: IpcMainEvent, channel: string): boolean {
    const sender = event.sender;

    // Verify sender is from our application
    if (!sender || sender.isDestroyed()) {
      console.warn(`[IPCValidation] ❌ Invalid sender for channel: ${channel}`);
      return false;
    }

    // Check if sender is from a trusted context
    const senderOrigin = sender.getURL();
    console.log(
      `[IPCValidation] 🔍 Validating sender origin: ${senderOrigin} for channel: ${channel}`
    );

    // For Electron apps, we expect file:// or our app's protocol
    if (
      !senderOrigin.startsWith('file://') &&
      !senderOrigin.startsWith('chromasync://')
    ) {
      console.warn(
        `[IPCValidation] ❌ Untrusted sender origin: ${senderOrigin}`
      );
      return false;
    }

    return true;
  }

  /**
   * Check rate limiting for a channel and sender
   */
  private checkRateLimit(
    senderId: number,
    channel: string,
    rateLimit?: ValidationRule['rateLimit']
  ): boolean {
    if (!rateLimit) {return true;}

    const key = `${senderId}:${channel}`;
    const now = Date.now();
    const entry = this.rateLimitMap.get(key);

    if (!entry || now > entry.resetTime) {
      // Reset or create new entry
      this.rateLimitMap.set(key, {
        count: 1,
        resetTime: now + rateLimit.windowMs,
      });
      return true;
    }

    if (entry.count >= rateLimit.maxRequests) {
      console.warn(
        `[IPCValidation] ❌ Rate limit exceeded for ${key}: ${entry.count}/${rateLimit.maxRequests}`
      );
      return false;
    }

    entry.count++;
    return true;
  }

  /**
   * Validate and sanitize IPC request data
   */
  private validateData<T>(
    channel: string,
    data: any
  ): { isValid: boolean; sanitizedData?: T; error?: string } {
    const rule = this.validationRules.get(channel);
    if (!rule) {
      return {
        isValid: false,
        error: `No validation rule found for channel: ${channel}`,
      };
    }

    const validator = (rule as any).validator as ValidateFunction;

    try {
      // Clone data to avoid mutations
      const dataToValidate = JSON.parse(JSON.stringify(data));

      const isValid = validator(dataToValidate);

      if (!isValid) {
        const errors = validator.errors
          ?.map(err => `${err.instancePath || err.schemaPath} ${err.message}`)
          .join(', ');
        return { isValid: false, error: `Validation failed: ${errors}` };
      }

      return { isValid: true, sanitizedData: dataToValidate as T };
    } catch (error) {
      return {
        isValid: false,
        error: `Validation error: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Middleware function to validate IPC requests
   */
  validateRequest<T>(
    event: IpcMainEvent,
    channel: string,
    ...args: any[]
  ): IPCResponse<T> | null {
    console.log(
      `[IPCValidation] 🔍 Validating request for channel: ${channel}`
    );

    // 1. Check if channel is allowed
    if (!this.allowedChannels.has(channel)) {
      console.warn(`[IPCValidation] ❌ Channel not allowed: ${channel}`);
      return {
        success: false,
        error: 'Channel not allowed',
        message: 'This IPC channel is not registered or allowed',
        timestamp: Date.now(),
        requestId: this.generateRequestId(),
      };
    }

    // 2. Validate sender
    if (!this.validateSender(event, channel)) {
      return {
        success: false,
        error: 'Unauthorized sender',
        message: 'Request from unauthorized sender',
        timestamp: Date.now(),
        requestId: this.generateRequestId(),
      };
    }

    // 3. Get validation rule
    const rule = this.validationRules.get(channel);
    if (!rule) {
      return {
        success: false,
        error: 'No validation rule',
        message: 'No validation rule configured for this channel',
        timestamp: Date.now(),
        requestId: this.generateRequestId(),
      };
    }

    // 4. Check rate limiting
    if (!this.checkRateLimit(event.sender.id, channel, rule.rateLimit)) {
      return {
        success: false,
        error: 'Rate limit exceeded',
        message: 'Too many requests for this channel',
        timestamp: Date.now(),
        requestId: this.generateRequestId(),
      };
    }

    // 5. Validate request data
    const requestData = args.length === 1 ? args[0] : args;
    const validation = this.validateData<T>(channel, requestData);

    if (!validation.isValid) {
      console.warn(
        `[IPCValidation] ❌ Data validation failed for ${channel}: ${validation.error}`
      );
      return {
        success: false,
        error: 'Invalid request data',
        message: validation.error || 'Request data failed validation',
        timestamp: Date.now(),
        requestId: this.generateRequestId(),
      };
    }

    console.log(
      `[IPCValidation] ✅ Request validated successfully for channel: ${channel}`
    );
    return null; // null means validation passed, continue processing
  }

  /**
   * Create a secure wrapper for IPC handlers
   */
  createSecureHandler<T, R>(
    channel: string,
    validationRule: ValidationRule,
    handler: (data: T, event: IpcMainEvent) => Promise<R> | R
  ): void {
    // Register validation rule
    this.registerValidation(validationRule);

    // Create secure handler
    ipcMain.handle(channel, async (event: any, ...args: any[]) => {
      try {
        // Validate request
        const validationResult = this.validateRequest<T>(
          event,
          channel,
          ...args
        );

        if (validationResult) {
          // Validation failed, return error response
          return validationResult;
        }

        // Extract validated data
        const requestData = args.length === 1 ? args[0] : args;
        const validation = this.validateData<T>(channel, requestData);

        if (!validation.isValid || !validation.sanitizedData) {
          throw new Error('Data validation failed after middleware check');
        }

        // Call the actual handler with validated data
        const result = await handler(validation.sanitizedData, event);

        return {
          success: true,
          data: result,
          timestamp: Date.now(),
          requestId: this.generateRequestId(),
        } as IPCResponse<R>;
      } catch (error) {
        console.error(
          `[IPCValidation] ❌ Handler error for ${channel}:`,
          error
        );
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          message: 'Internal server error',
          timestamp: Date.now(),
          requestId: this.generateRequestId(),
        } as IPCResponse<R>;
      }
    });

    console.log(
      `[IPCValidation] ✅ Secure handler registered for channel: ${channel}`
    );
  }

  /**
   * Generate unique request ID for tracking
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clean up expired rate limit entries
   */
  cleanupRateLimits(): void {
    const now = Date.now();
    for (const [key, entry] of this.rateLimitMap.entries()) {
      if (now > entry.resetTime) {
        this.rateLimitMap.delete(key);
      }
    }
  }

  /**
   * Get validation statistics for monitoring
   */
  getValidationStats(): {
    totalChannels: number;
    activeRateLimits: number;
    allowedChannels: string[];
  } {
    return {
      totalChannels: this.validationRules.size,
      activeRateLimits: this.rateLimitMap.size,
      allowedChannels: Array.from(this.allowedChannels),
    };
  }
}

// Common validation schemas
export const CommonSchemas = {
  organizationId: {
    type: 'object' as const,
    properties: {
      organizationId: { type: 'string' as const, minLength: 1, maxLength: 100 },
    },
    required: ['organizationId' as const],
    additionalProperties: false,
  },

  colorId: {
    type: 'object' as const,
    properties: {
      organizationId: { type: 'string' as const, minLength: 1, maxLength: 100 },
      colorId: { type: 'string' as const, minLength: 1, maxLength: 100 },
    },
    required: ['organizationId' as const, 'colorId' as const],
    additionalProperties: false,
  },

  productId: {
    type: 'object' as const,
    properties: {
      organizationId: { type: 'string' as const, minLength: 1, maxLength: 100 },
      productId: { type: 'string' as const, minLength: 1, maxLength: 100 },
    },
    required: ['organizationId' as const, 'productId' as const],
    additionalProperties: false,
  },
};

// Export singleton instance
export const ipcValidation = IPCValidationMiddleware.getInstance();
