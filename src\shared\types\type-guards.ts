/**
 * @file type-guards.ts
 * @description Type guard utilities for strict type checking and null safety
 */

/**
 * Type guard to check if value is not null or undefined
 */
export function isNotNullish<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Type guard to check if value is a non-empty string
 */
export function isNonEmptyString(value: unknown): value is string {
  return typeof value === 'string' && value.length > 0;
}

/**
 * Type guard to check if value is a valid number
 */
export function isValidNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

/**
 * Type guard to check if value is a non-empty array
 */
export function isNonEmptyArray<T>(
  value: T[] | null | undefined
): value is T[] {
  return Array.isArray(value) && value.length > 0;
}

/**
 * Type guard to check if value is a valid object
 */
export function isValidObject(
  value: unknown
): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Type guard to check if string is a valid UUID
 */
export function isValidUUID(value: string): boolean {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
}

/**
 * Type guard to check if string is a valid hex color
 */
export function isValidHexColor(value: string): boolean {
  const hexRegex = /^#[0-9A-Fa-f]{6}$/;
  return hexRegex.test(value);
}

/**
 * Type guard to check if string is a valid email
 */
export function isValidEmail(value: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

/**
 * Assert that value is not null or undefined, throw error if it is
 */
export function assertNotNullish<T>(
  value: T | null | undefined,
  message?: string
): asserts value is T {
  if (value === null || value === undefined) {
    throw new Error(message || 'Value is null or undefined');
  }
}

/**
 * Assert that value is a non-empty string, throw error if it's not
 */
export function assertNonEmptyString(
  value: unknown,
  message?: string
): asserts value is string {
  if (!isNonEmptyString(value)) {
    throw new Error(message || 'Value is not a non-empty string');
  }
}

/**
 * Assert that value is a valid number, throw error if it's not
 */
export function assertValidNumber(
  value: unknown,
  message?: string
): asserts value is number {
  if (!isValidNumber(value)) {
    throw new Error(message || 'Value is not a valid number');
  }
}

/**
 * Get non-null value or default
 */
export function getOrDefault<T>(
  value: T | null | undefined,
  defaultValue: T
): T {
  return value ?? defaultValue;
}

/**
 * Get non-null value or throw error
 */
export function getOrThrow<T>(
  value: T | null | undefined,
  message?: string
): T {
  if (value === null || value === undefined) {
    throw new Error(message || 'Value is required but was null or undefined');
  }
  return value;
}

/**
 * Safely access array element
 */
export function safeArrayAccess<T>(
  array: T[] | null | undefined,
  index: number
): T | undefined {
  if (!Array.isArray(array) || index < 0 || index >= array.length) {
    return undefined;
  }
  return array[index];
}

/**
 * Safely access object property
 */
export function safePropAccess<T, K extends keyof T>(
  obj: T | null | undefined,
  key: K
): T[K] | undefined {
  if (!obj || typeof obj !== 'object') {
    return undefined;
  }
  return obj[key];
}

/**
 * Type-safe JSON parsing
 */
export function safeJsonParse<T = unknown>(json: string): T | null {
  try {
    return JSON.parse(json) as T;
  } catch {
    return null;
  }
}

/**
 * Type-safe string to number conversion
 */
export function safeParseInt(value: string | number): number | null {
  if (typeof value === 'number') {
    return isValidNumber(value) ? value : null;
  }
  const parsed = parseInt(value, 10);
  return isValidNumber(parsed) ? parsed : null;
}

/**
 * Type-safe string to float conversion
 */
export function safeParseFloat(value: string | number): number | null {
  if (typeof value === 'number') {
    return isValidNumber(value) ? value : null;
  }
  const parsed = parseFloat(value);
  return isValidNumber(parsed) ? parsed : null;
}

/**
 * Check if error is an instance of Error
 */
export function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * Convert unknown error to Error instance
 */
export function toError(error: unknown): Error {
  if (isError(error)) {
    return error;
  }
  return new Error(String(error));
}

/**
 * Type predicate for objects with specific properties
 */
export function hasProperty<
  T extends Record<string, unknown>,
  K extends string,
>(obj: T, prop: K): obj is T & Record<K, unknown> {
  return prop in obj;
}

/**
 * Type predicate for objects with required properties
 */
export function hasRequiredProperties<
  T extends Record<string, unknown>,
  K extends keyof T,
>(obj: T, props: K[]): obj is T & Required<Pick<T, K>> {
  return props.every(prop => obj[prop] !== undefined && obj[prop] !== null);
}

/**
 * Create a typed error with additional properties
 */
export function createTypedError<T extends Record<string, unknown>>(
  message: string,
  properties: T
): Error & T {
  const error = new Error(message) as Error & T;
  Object.assign(error, properties);
  return error;
}

/**
 * Exhaustiveness check for switch statements
 */
export function assertNever(value: never): never {
  throw new Error(`Unexpected value: ${JSON.stringify(value)}`);
}

/**
 * Type-safe environment variable access
 */
export function getEnvVar(name: string, defaultValue?: string): string {
  const value = process.env[name];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${name} is required but not set`);
  }
  return value;
}

/**
 * Validate and convert unknown value to expected type
 */
export function validateType<T>(
  value: unknown,
  validator: (val: unknown) => val is T,
  errorMessage?: string
): T {
  if (!validator(value)) {
    throw new Error(
      errorMessage || `Invalid type for value: ${JSON.stringify(value)}`
    );
  }
  return value;
}
