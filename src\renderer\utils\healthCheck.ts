/**
 * Application health monitoring system
 */

import { errorLogger } from './errorLogger';

export interface HealthCheck {
  name: string;
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  message?: string;
  timestamp: number;
  duration?: number;
  context?: Record<string, any>;
}

export interface SystemHealth {
  overall: 'healthy' | 'warning' | 'critical';
  checks: HealthCheck[];
  lastUpdated: number;
}

class HealthMonitor {
  private static instance: HealthMonitor;
  private checks: Map<string, HealthCheck> = new Map();
  private checkInterval = 60000; // 1 minute
  private intervalId?: number;

  private constructor() {
    this.initializeHealthChecks();
    this.startMonitoring();
  }

  static getInstance(): HealthMonitor {
    if (!HealthMonitor.instance) {
      HealthMonitor.instance = new HealthMonitor();
    }
    return HealthMonitor.instance;
  }

  private async initializeHealthChecks(): Promise<void> {
    // Register all health checks
    this.registerCheck('database_connection', this.checkDatabaseConnection);
    this.registerCheck('memory_usage', this.checkMemoryUsage);
    this.registerCheck('error_rate', this.checkErrorRate);
    this.registerCheck('ipc_communication', this.checkIPCCommunication);
    this.registerCheck('local_storage', this.checkLocalStorage);
    this.registerCheck('window_api', this.checkWindowAPI);

    // Run initial health check
    await this.runAllChecks();
  }

  private registerCheck(
    name: string,
    checkFunction: () => Promise<HealthCheck>
  ): void {
    // Store the check function for later execution
    (this as Record<string, any>)[`_check_${name}`] = checkFunction.bind(this);
  }

  private async runAllChecks(): Promise<void> {
    const checkPromises: Promise<void>[] = [];

    // Get all registered check functions
    for (const [name] of this.checks) {
      const checkFunction = (this as Record<string, any>)[`_check_${name}`];
      if (checkFunction) {
        checkPromises.push(this.runSingleCheck(name, checkFunction));
      }
    }

    // Also check for new checks
    const checkMethods = Object.getOwnPropertyNames(
      Object.getPrototypeOf(this)
    ).filter(
      method =>
        method.startsWith('check') &&
        typeof (this as Record<string, any>)[method] === 'function'
    );

    for (const method of checkMethods) {
      const name = method
        .replace(/([A-Z])/g, '_$1')
        .toLowerCase()
        .substring(6); // Convert checkDatabaseConnection to database_connection
      if (!this.checks.has(name)) {
        checkPromises.push(
          this.runSingleCheck(
            name,
            (this as Record<string, any>)[method].bind(this)
          )
        );
      }
    }

    await Promise.allSettled(checkPromises);
  }

  private async runSingleCheck(
    name: string,
    checkFunction: () => Promise<HealthCheck>
  ): Promise<void> {
    try {
      const result = await Promise.race([
        checkFunction(),
        new Promise<HealthCheck>((_, reject) =>
          setTimeout(() => reject(new Error('Health check timeout')), 5000)
        ),
      ]);
      this.checks.set(name, result);
    } catch (error) {
      this.checks.set(name, {
        name,
        status: 'critical',
        message: `Health check failed: ${String(error)}`,
        timestamp: Date.now(),
      });
    }
  }

  private async checkDatabaseConnection(): Promise<HealthCheck> {
    const start = performance.now();
    try {
      if (!window.ipc) {
        return {
          name: 'database_connection',
          status: 'critical',
          message: 'IPC not available',
          timestamp: Date.now(),
        };
      }

      // Test database connection by fetching a simple query
      await window.colorAPI.getAll();

      const duration = performance.now() - start;
      return {
        name: 'database_connection',
        status: duration > 1000 ? 'warning' : 'healthy',
        message:
          duration > 1000
            ? 'Database connection slow'
            : 'Database connection healthy',
        timestamp: Date.now(),
        duration,
      };
    } catch (error) {
      return {
        name: 'database_connection',
        status: 'critical',
        message: `Database connection failed: ${String(error)}`,
        timestamp: Date.now(),
        duration: performance.now() - start,
      };
    }
  }

  private async checkMemoryUsage(): Promise<HealthCheck> {
    const memory = (performance as any).memory;
    if (!memory) {
      return {
        name: 'memory_usage',
        status: 'unknown',
        message: 'Memory API not available',
        timestamp: Date.now(),
      };
    }

    const used = memory.usedJSHeapSize;
    const total = memory.totalJSHeapSize;
    const percentage = (used / total) * 100;

    let status: HealthCheck['status'] = 'healthy';
    let message = `Memory usage: ${percentage.toFixed(1)}%`;

    if (percentage > 90) {
      status = 'critical';
      message = `Critical memory usage: ${percentage.toFixed(1)}%`;
    } else if (percentage > 75) {
      status = 'warning';
      message = `High memory usage: ${percentage.toFixed(1)}%`;
    }

    return {
      name: 'memory_usage',
      status,
      message,
      timestamp: Date.now(),
      context: { used, total, percentage },
    };
  }

  private async checkErrorRate(): Promise<HealthCheck> {
    const healthStatus = errorLogger.getHealthStatus();

    return {
      name: 'error_rate',
      status: healthStatus.status,
      message: `${healthStatus.errors} errors, ${healthStatus.warnings} warnings in last 5 minutes`,
      timestamp: Date.now(),
      context: {
        errors: healthStatus.errors,
        warnings: healthStatus.warnings,
        recentErrors: healthStatus.recentErrors.length,
      },
    };
  }

  private async checkIPCCommunication(): Promise<HealthCheck> {
    const start = performance.now();
    try {
      if (!window.ipc) {
        return {
          name: 'ipc_communication',
          status: 'critical',
          message: 'IPC not available',
          timestamp: Date.now(),
        };
      }

      // Test IPC with a simple ping
      const response = await window.ipc.invoke('ping');
      const duration = performance.now() - start;

      return {
        name: 'ipc_communication',
        status: duration > 500 ? 'warning' : 'healthy',
        message:
          duration > 500
            ? 'IPC communication slow'
            : 'IPC communication healthy',
        timestamp: Date.now(),
        duration,
        context: { response },
      };
    } catch (error) {
      return {
        name: 'ipc_communication',
        status: 'critical',
        message: `IPC communication failed: ${String(error)}`,
        timestamp: Date.now(),
        duration: performance.now() - start,
      };
    }
  }

  private async checkLocalStorage(): Promise<HealthCheck> {
    try {
      const testKey = '_health_check_test';
      const testValue = Date.now().toString();

      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);

      if (retrieved === testValue) {
        return {
          name: 'local_storage',
          status: 'healthy',
          message: 'Local storage functioning normally',
          timestamp: Date.now(),
        };
      } else {
        return {
          name: 'local_storage',
          status: 'warning',
          message: 'Local storage read/write mismatch',
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      return {
        name: 'local_storage',
        status: 'critical',
        message: `Local storage failed: ${String(error)}`,
        timestamp: Date.now(),
      };
    }
  }

  private async checkWindowAPI(): Promise<HealthCheck> {
    const available: string[] = [];

    // Check window.api
    try {
      if (window.api !== undefined) {available.push('window.api');}
    } catch (error) {
      // Ignore access errors
    }

    // Check window.ipc
    try {
      if (window.ipc !== undefined) {available.push('window.ipc');}
    } catch (error) {
      // Ignore access errors
    }

    // Check window.licenseAPI
    try {
      if (window.licenseAPI !== undefined) {available.push('window.licenseAPI');}
    } catch (error) {
      // Ignore access errors
    }

    const expectedApis = ['window.api', 'window.ipc', 'window.licenseAPI'];
    const status =
      available.length === 3
        ? 'healthy'
        : available.length > 0
          ? 'warning'
          : 'critical';

    return {
      name: 'window_api',
      status,
      message: `${available.length}/${expectedApis.length} APIs available`,
      timestamp: Date.now(),
      context: {
        available,
        missing: expectedApis.filter(api => !available.includes(api)),
      },
    };
  }

  getSystemHealth(): SystemHealth {
    const checks = Array.from(this.checks.values());
    const criticalCount = checks.filter(c => c.status === 'critical').length;
    const warningCount = checks.filter(c => c.status === 'warning').length;

    let overall: SystemHealth['overall'] = 'healthy';
    if (criticalCount > 0) {
      overall = 'critical';
    } else if (warningCount > 0) {
      overall = 'warning';
    }

    return {
      overall,
      checks,
      lastUpdated: Date.now(),
    };
  }

  startMonitoring(): void {
    if (this.intervalId) {
      window.clearInterval(this.intervalId);
    }

    this.intervalId = window.setInterval(() => {
      this.runAllChecks().catch(error => {
        errorLogger.logError({
          message: 'Health check monitoring failed',
          stack: error instanceof Error ? error.stack : undefined,
          category: 'general',
          context: { error: String(error) },
        });
      });
    }, this.checkInterval);
  }

  stopMonitoring(): void {
    if (this.intervalId) {
      window.clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }
}

// Export singleton instance
export const healthMonitor = HealthMonitor.getInstance();
