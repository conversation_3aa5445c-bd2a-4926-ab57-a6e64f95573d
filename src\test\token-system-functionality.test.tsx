/**
 * @file token-system-functionality.test.tsx
 * @description Integration tests for verifying token system functionality
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
// FeatureFlagContext removed - token system is always enabled
import { TokenProvider } from '../renderer/context/TokenProvider';

// Mock the store dependencies
vi.mock('../renderer/store/color.store', () => ({
  useColorStore: () => ({
    fetchColors: vi.fn(),
    colors: [],
    searchQuery: '',
    setSearchQuery: vi.fn(),
    viewMode: 'table',
    setViewMode: vi.fn(),
    darkMode: false,
    toggleDarkMode: vi.fn(),
  }),
}));

// Mock any required native API calls
vi.mock('../preload/index', () => ({
  ipcRenderer: {
    invoke: vi.fn(),
    on: vi.fn(),
    once: vi.fn(),
    removeListener: vi.fn(),
  },
}));

// Create a basic test component to verify token system
const TestComponent = () => {
  return (
    <div data-testid='test-component'>
      <span data-testid='token-system-status'>enabled</span>
    </div>
  );
};

describe('Token System Integration', () => {
  beforeEach(() => {
    // Mock localStorage for feature flags
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    });
  });

  test('token system is enabled by default', () => {
    // Set up localStorage mock to return null (simulating first visit)
    vi.mocked(window.localStorage.getItem).mockReturnValue(null);

    render(
      <TokenProvider>
        <TestComponent />
      </TokenProvider>
    );

    // Verify token system is enabled by default
    expect(screen.getByTestId('token-system-status')).toHaveTextContent(
      'enabled'
    );
  });

  test('components use token-based CSS variables when token system is enabled', () => {
    // Mock localStorage to ensure token system is enabled
    vi.mocked(window.localStorage.getItem).mockReturnValue('true');

    // Render with a limited App simulation
    render(
      <TokenProvider>
        <div
          data-testid='test-container'
          className='p-[var(--spacing-4)] bg-ui-primary text-[var(--color-ui-foreground-primary)]'
        >
          Token System Test
        </div>
      </TokenProvider>
    );

    // Get the rendered element
    const container = screen.getByTestId('test-container');

    // Check that token CSS classes are applied
    expect(container.className).toContain('p-[var(--spacing-4)]');
    expect(container.className).toContain('bg-ui-primary');
    expect(container.className).toContain(
      'text-[var(--color-ui-foreground-primary)]'
    );
  });

  test('token CSS variables are injected into the document', () => {
    // Mock document.documentElement.style.getPropertyValue to check for CSS variables
    const getPropertyValueMock = vi.fn();
    const originalGetPropertyValue = window.getComputedStyle(
      document.documentElement
    ).getPropertyValue;

    // Override getPropertyValue method
    Object.defineProperty(window, 'getComputedStyle', {
      value: () => ({
        getPropertyValue: getPropertyValueMock,
      }),
      writable: true,
    });

    // Expect CSS variable for primary brand color to exist
    getPropertyValueMock.mockReturnValue('#ff0000');

    // Need to render something with the token provider
    render(
      <TokenProvider>
        <div>Token System Test</div>
      </TokenProvider>
    );

    // Check if CSS variables would be retrieved correctly
    expect(getPropertyValueMock('--color-brand-primary')).toBe('#ff0000');

    // Restore original method
    Object.defineProperty(window, 'getComputedStyle', {
      value: () => ({
        getPropertyValue: originalGetPropertyValue,
      }),
      writable: true,
    });
  });
});
