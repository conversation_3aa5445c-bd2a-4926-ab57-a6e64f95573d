/**
 * @file store-event-completion.ts
 * @description Standardized event completion utilities for store coordination
 */

/**
 * Standard completion event detail structure
 */
export interface StoreCompletionDetail<T = any> {
  success: boolean;
  timestamp: number;
  storeName: string;
  operation: string;
  data?: T;
  error?: string;
  duration?: number;
  metadata?: Record<string, any>;
}

/**
 * Emit a standardized store completion event
 * @param storeName - Name of the store (e.g., 'color-store', 'product-store')
 * @param operation - Operation that completed (e.g., 'refresh', 'load', 'sync')
 * @param success - Whether the operation succeeded
 * @param data - Optional data from the operation
 * @param error - Optional error message if operation failed
 * @param metadata - Optional additional metadata
 */
export function emitStoreCompletionEvent<T = any>(
  storeName: string,
  operation: string,
  success: boolean,
  data?: T,
  error?: string,
  metadata?: Record<string, any>
): void {
  if (typeof window === 'undefined' || !window.dispatchEvent) {
    console.warn(
      `[${storeName}] Cannot emit completion event - window.dispatchEvent not available`
    );
    return;
  }

  const eventName = `${storeName}:${operation}-complete`;
  const detail: StoreCompletionDetail<T> = {
    success,
    timestamp: Date.now(),
    storeName,
    operation,
    data,
    error,
    metadata,
  };

  try {
    const event = new CustomEvent(eventName, { detail });
    window.dispatchEvent(event);

    console.log(`[${storeName}] ✅ Emitted completion event:`, {
      eventName,
      success,
      dataSize: data ? (Array.isArray(data) ? data.length : 'object') : 'none',
      error: error ? error.substring(0, 100) : undefined,
    });
  } catch (eventError) {
    console.error(
      `[${storeName}] Failed to emit completion event:`,
      eventError
    );
  }
}

/**
 * Listen for store completion events with type safety
 * @param storeName - Name of the store to listen to
 * @param operation - Operation to listen for
 * @param callback - Callback to execute when event is received
 * @returns Cleanup function to remove the listener
 */
export function listenForStoreCompletion<T = any>(
  storeName: string,
  operation: string,
  callback: (detail: StoreCompletionDetail<T>) => void
): () => void {
  if (typeof window === 'undefined') {
    console.warn(
      `Cannot listen for ${storeName}:${operation} completion - window not available`
    );
    return () => {};
  }

  const eventName = `${storeName}:${operation}-complete`;

  const eventListener = (event: CustomEvent<StoreCompletionDetail<T>>) => {
    try {
      callback(event.detail);
    } catch (error) {
      console.error(
        `Error in completion event callback for ${eventName}:`,
        error
      );
    }
  };

  window.addEventListener(eventName, eventListener as EventListener);

  // Return cleanup function
  return () => {
    window.removeEventListener(eventName, eventListener as EventListener);
  };
}

/**
 * Wait for a store operation to complete
 * @param storeName - Name of the store
 * @param operation - Operation to wait for
 * @param timeout - Timeout in milliseconds (default: 10000)
 * @returns Promise that resolves with the completion detail
 */
export function waitForStoreCompletion<T = any>(
  storeName: string,
  operation: string,
  timeout = 10000
): Promise<StoreCompletionDetail<T>> {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      cleanup();
      reject(
        new Error(
          `Timeout waiting for ${storeName}:${operation} completion after ${timeout}ms`
        )
      );
    }, timeout);

    const cleanup = listenForStoreCompletion<T>(
      storeName,
      operation,
      detail => {
        clearTimeout(timeoutId);
        cleanup();
        resolve(detail);
      }
    );
  });
}

/**
 * Emit a refresh completion event (common pattern)
 */
export function emitRefreshComplete<T = any>(
  storeName: string,
  success: boolean,
  data?: T,
  error?: string,
  itemCount?: number
): void {
  emitStoreCompletionEvent(
    storeName,
    'refresh',
    success,
    data,
    error,
    itemCount !== undefined ? { itemCount } : undefined
  );
}

/**
 * Performance timing utility for store operations
 */
export class StoreOperationTimer {
  private startTime: number;
  private storeName: string;
  private operation: string;

  constructor(storeName: string, operation: string) {
    this.storeName = storeName;
    this.operation = operation;
    this.startTime = performance.now();
  }

  /**
   * Complete the operation and emit timing event
   */
  complete<T = any>(success: boolean, data?: T, error?: string): void {
    const duration = performance.now() - this.startTime;

    emitStoreCompletionEvent(
      this.storeName,
      this.operation,
      success,
      data,
      error,
      { duration: Math.round(duration) }
    );
  }

  /**
   * Get elapsed time without completing
   */
  getElapsed(): number {
    return performance.now() - this.startTime;
  }
}

/**
 * Create a store operation timer
 */
export function createStoreTimer(
  storeName: string,
  operation: string
): StoreOperationTimer {
  return new StoreOperationTimer(storeName, operation);
}

/**
 * Standardized logging for store operations
 */
export function logStoreOperation(
  storeName: string,
  operation: string,
  status: 'start' | 'success' | 'error',
  details?: any
): void {
  const timestamp = new Date().toISOString();
  const prefix = `[${storeName}]`;

  switch (status) {
    case 'start':
      console.log(`${prefix} 🚀 Starting ${operation}`, details || '');
      break;
    case 'success':
      console.log(
        `${prefix} ✅ ${operation} completed successfully`,
        details || ''
      );
      break;
    case 'error':
      console.error(`${prefix} ❌ ${operation} failed`, details || '');
      break;
  }
}
