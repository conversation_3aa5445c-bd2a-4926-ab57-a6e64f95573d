/**
 * @file ColorSwatches/index.tsx
 * @description Grid component for displaying color entries as swatches
 */

import { useMemo, useEffect, memo } from 'react';
import { useColorStore, useFilteredColorsAdvanced } from '../../store/color.store';
import ColorSwatch from './ColorSwatch';
import { useTokens } from '../../hooks/useTokens';
import { SwatchSkeleton } from '../ui/Skeleton';
import { useColorProductMap } from '../../hooks/useColorProductMap';

const ColorSwatches = memo(function ColorSwatches() {
  const { searchQuery, isLoading, error } = useColorStore();
  const filteredColors = useFilteredColorsAdvanced(); // Use centralized filtering logic
  const tokens = useTokens();
  
  // Move useColorProductMap to parent component to prevent 529 API calls
  const { productMap, isLoading: isProductMapLoading } = useColorProductMap();

  // Deduplicate colors by name to show one swatch per color
  const uniqueColors = useMemo(() => {
    const seen = new Set();
    const colors = filteredColors.filter(color => {
      const colorName = color.name;
      if (!colorName || seen.has(colorName)) {
        return false;
      }
      seen.add(colorName);
      return true;
    });
    return colors;
  }, [filteredColors]);

  // Development-only logging for debugging color filtering
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG_COLORS) {
      console.log('Search query changed:', searchQuery);
      console.log('Filtered colors updated:', filteredColors.length);
      console.log('Unique colors updated:', uniqueColors.length);
    }
  }, [searchQuery, filteredColors, uniqueColors]);

  const productName = useMemo(() => {
    if (searchQuery && searchQuery.startsWith('product:')) {
      return searchQuery.substring(8).trim();
    }
    return null;
  }, [searchQuery]);

  // Style classes with improved layout and spacing
  const containerClasses = "relative table-view-container w-full h-full";
  const contentContainerClasses = "bg-ui-background-primary shadow-md h-full flex flex-col";
  const scrollContainerClasses = "overflow-y-auto flex-1 scrollable-content";
  
  // Updated grid layout with consistent spacing and better alignment using design tokens - always 5-6 columns on desktop
  const gridClasses = `grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-6 gap-[var(--spacing-4)] p-[var(--spacing-4)]`;
  
  const textClasses = "text-ui-foreground-secondary p-[var(--spacing-4)] text-center";
  
  const errorClasses = "text-feedback-error p-[var(--spacing-4)] text-center";
  
  // const _loadingContainerClasses = "flex justify-center py-8";
  // const _spinnerClasses = "animate-spin rounded-full h-8 w-8 border-b-2 border-ui-border-dark";
  const emptyStateClasses = "text-center py-[var(--spacing-8)]";
  const helperTextClasses = "text-xs text-ui-foreground-tertiary py-[var(--spacing-3)] px-[var(--spacing-5)] text-center border-t border-ui-border-light";

  // Check if we're in product filter mode
  const isProductMode = searchQuery && searchQuery.toLowerCase().startsWith('product:');

  // Use consistent styling across all swatches - optimized for 5-6 columns on desktop
  const gridContainerStyle = {
    padding: tokens.spacing[4],
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))',
    gap: tokens.spacing[4],
    '@media (min-width: 1024px)': {
      gridTemplateColumns: 'repeat(5, 1fr)',
    },
    '@media (min-width: 1280px)': {
      gridTemplateColumns: 'repeat(6, 1fr)',
    }
  };
  
  return (
    <div className={`${containerClasses} h-full flex flex-col`} key={searchQuery}>
      {isLoading ? (
        <div className={`${contentContainerClasses} flex flex-col h-full`}>
          <div className={scrollContainerClasses}>
            <div className={gridClasses} style={gridContainerStyle}>
              {Array.from({ length: 12 }).map((_, i) => (
                <SwatchSkeleton key={i} />
              ))}
            </div>
          </div>
        </div>
      ) : error ? (
        <div className={`${contentContainerClasses} flex flex-col h-full`}>
          <p className={errorClasses}>Error loading colors: {error}</p>
        </div>
      ) : uniqueColors.length === 0 ? (
        <div className={`${contentContainerClasses} flex flex-col h-full`}>
          <div className={emptyStateClasses}>
            <p className={textClasses}>
              {isProductMode 
                ? `No colours found in product "${productName}".`
                : searchQuery 
                  ? "No colours match your search query."
                  : "No colours found. Add your first colour using the form."
              }
            </p>
          </div>
        </div>
      ) : (
        <div className={`${contentContainerClasses} flex flex-col h-full`} style={{ marginTop: 0, padding: 0 }}>
          {isProductMode && (
            <div className="py-[var(--spacing-3)] px-[var(--spacing-4)] bg-ui-background-secondary border-b border-ui-border-light">
              <h2 className="text-sm font-medium text-ui-foreground-primary dark:text-gray-100 flex items-center">
                <svg className="w-4 h-4 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                </svg>
                Product: {productName} ({uniqueColors.length} colors)
              </h2>
            </div>
          )}
          <div className={scrollContainerClasses}>
            <div className="h-full">
              <div className={gridClasses} key={uniqueColors.length}>
                {uniqueColors.map((color) => (
                  <ColorSwatch 
                    key={color.id} 
                    entry={color} 
                    productMap={productMap}
                    isProductMapLoading={isProductMapLoading}
                  />
                ))}
              </div>
            </div>
          </div>
          <div className={helperTextClasses}>
            Click on color values to copy • Click on gradients to view details
            {isProductMode && (
              <span className="ml-2">• <button 
                className="text-brand-primary hover:text-brand-secondary underline focus:outline-none" 
                onClick={() => useColorStore.getState().setSearchQuery('')}
              >
                Clear product filter
              </button></span>
            )}
          </div>
        </div>
      )}
    </div>
  );
});

ColorSwatches.displayName = 'ColorSwatches';

export default ColorSwatches;
