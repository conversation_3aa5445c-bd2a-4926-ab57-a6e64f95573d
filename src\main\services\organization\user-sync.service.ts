// import { Database } from 'better-sqlite3';
import { SupabaseClient } from '@supabase/supabase-js';
// import { OrganizationRepository } from '../../db/repositories/organization.repository';

/**
 * Result of user profile synchronization operation
 */
export interface UserSyncResult {
  success: boolean;
  action?: 'created' | 'updated' | 'skipped';
  reason?: string;
  error?: string;
}

/**
 * Result of members synchronization operation
 */
export interface MembersSyncResult {
  success: boolean;
  syncedCount: number;
  errorCount?: number;
  errors?: string[];
  results: UserSyncResult[];
}

/**
 * Result of organizations synchronization operation
 */
export interface OrganizationsSyncResult {
  success: boolean;
  syncedCount: number;
  organizations?: Array<{
    id: string;
    name: string;
    role: string;
    settings: any;
  }>;
  error?: string;
}

/**
 * User profile data structure for synchronization
 */
export interface UserProfile {
  external_id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  updated_at: string;
}

/**
 * Service responsible for synchronizing user profiles and organization data
 * between local SQLite database and Supabase cloud storage.
 *
 * This service handles:
 * - User profile synchronization (bidirectional)
 * - Organization member synchronization
 * - User organization membership synchronization
 *
 * @example
 * ```typescript
 * const userSyncService = new UserSyncService(database, orgRepository, supabaseClient);
 *
 * // Sync user profile from Supabase to local
 * const result = await userSyncService.syncUserProfileToLocal('user-123');
 *
 * // Sync all organization members
 * const membersResult = await userSyncService.syncMembersFromSupabase('org-456');
 * ```
 */
export class UserSyncService {
  constructor(
    // private db: Database,
    // private organizationRepository: OrganizationRepository,
    private supabaseClient: SupabaseClient
  ) {}

  /**
   * Synchronizes a user profile from Supabase to the local database.
   * Creates new user if not exists, updates if Supabase version is newer.
   *
   * @param userId - The external user ID from Supabase
   * @returns Promise<UserSyncResult> - Result of the synchronization operation
   */
  async syncUserProfileToLocal(userId: string): Promise<UserSyncResult> {
    try {
      // Fetch user from Supabase auth
      const {
        data: { user: supabaseUser },
        error: supabaseError,
      } = await this.supabaseClient.auth.getUser();

      if (supabaseError) {
        return {
          success: false,
          error: `Failed to fetch user from Supabase: ${supabaseError.message}`,
        };
      }

      if (!supabaseUser || supabaseUser.id !== userId) {
        return {
          success: false,
          error: 'User not found in Supabase or user ID mismatch',
        };
      }

      // Validate user data
      if (!this.isValidUserData(supabaseUser)) {
        return {
          success: false,
          error: 'Invalid user data received from Supabase',
        };
      }

      // Check if user exists locally
      // TODO: Implement getUserById in OrganizationRepository
      const existingUser = null; // await this.organizationRepository.getUserById(userId);

      if (existingUser) {
        // TODO: Implement when getUserById is available
        // Compare timestamps to determine if update is needed
        // const supabaseTimestamp = new Date(supabaseUser.updated_at).getTime();
        // const localTimestamp = new Date(existingUser.updated_at).getTime();
        //
        // if (localTimestamp >= supabaseTimestamp) {
        //   return {
        //     success: true,
        //     action: 'skipped',
        //     reason: 'Local user is newer'
        //   };
        // }
        //
        // // Update existing user
        // await this.organizationRepository.updateUser(userId, {
        //   email: supabaseUser.email,
        //   name: supabaseUser.user_metadata?.full_name || supabaseUser.email
        // });
        //
        // return {
        //   success: true,
        //   action: 'updated'
        // };

        // Temporary return until implementation is complete
        return {
          success: true,
          action: 'skipped',
        };
      } else {
        // Create new user
        // TODO: Implement createUser in OrganizationRepository
        /*
        await this.organizationRepository.createUser({
          external_id: userId,
          email: supabaseUser.email,
          name: supabaseUser.user_metadata?.full_name || supabaseUser.email,
          updated_at: supabaseUser.updated_at || new Date().toISOString()
        });
        */

        return {
          success: true,
          action: 'created',
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `Error during user sync: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Synchronizes all members of an organization from Supabase to local database.
   *
   * @param organizationId - The organization ID to sync members for
   * @returns Promise<MembersSyncResult> - Result of the synchronization operation
   */
  async syncMembersFromSupabase(
    organizationId: string
  ): Promise<MembersSyncResult> {
    try {
      // Fetch organization members from Supabase
      const { data: members, error: membersError } = await this.supabaseClient
        .from('organization_members')
        .select('user_id, role')
        .eq('organization_id', organizationId);

      if (membersError) {
        return {
          success: false,
          syncedCount: 0,
          errorCount: 1,
          errors: [
            `Failed to fetch members from Supabase: ${membersError.message}`,
          ],
          results: [],
        };
      }

      if (!members || members.length === 0) {
        return {
          success: true,
          syncedCount: 0,
          results: [],
        };
      }

      // Sync each member's user profile
      const results: UserSyncResult[] = [];
      const errors: string[] = [];

      for (const member of members) {
        const result = await this.syncUserProfileToLocal(member.user_id);
        results.push(result);

        if (!result.success && result.error) {
          errors.push(`${member.user_id}: ${result.error}`);
        }
      }

      const successCount = results.filter(r => r.success).length;
      const errorCount = results.filter(r => !r.success).length;

      return {
        success: errorCount === 0,
        syncedCount: successCount,
        errorCount: errorCount > 0 ? errorCount : undefined,
        errors: errors.length > 0 ? errors : undefined,
        results,
      };
    } catch (error) {
      return {
        success: false,
        syncedCount: 0,
        errorCount: 1,
        errors: [
          `Error during members sync: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ],
        results: [],
      };
    }
  }

  /**
   * Synchronizes user's organization memberships from Supabase to local database.
   *
   * @param userId - The user ID to sync organizations for
   * @returns Promise<OrganizationsSyncResult> - Result of the synchronization operation
   */
  async syncOrganizationsFromSupabase(
    userId: string
  ): Promise<OrganizationsSyncResult> {
    try {
      // Fetch user's organizations from Supabase
      const { data: memberships, error: membershipsError } =
        await this.supabaseClient
          .from('organization_members')
          .select(
            `
          organization_id,
          role,
          organizations (
            id,
            name,
            settings
          )
        `
          )
          .eq('user_id', userId);

      if (membershipsError) {
        return {
          success: false,
          syncedCount: 0,
          error: `Failed to fetch organizations from Supabase: ${membershipsError.message}`,
        };
      }

      if (!memberships || memberships.length === 0) {
        return {
          success: true,
          syncedCount: 0,
          organizations: [],
        };
      }

      // Transform and prepare organization data
      const organizations = memberships.map((membership: any) => ({
        id: membership.organization_id,
        name: membership.organizations?.name || 'Unknown',
        role: membership.role,
        settings: membership.organizations?.settings || {},
      }));

      // TODO: Store organizations locally if needed
      // This could involve updating local organization cache or user preferences

      return {
        success: true,
        syncedCount: organizations.length,
        organizations,
      };
    } catch (error) {
      return {
        success: false,
        syncedCount: 0,
        error: `Error during organizations sync: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Synchronizes a local user profile to Supabase.
   *
   * @param userId - The user ID to sync to Supabase
   * @returns Promise<UserSyncResult> - Result of the synchronization operation
   */
  async syncUserProfileToSupabase(_userId: string): Promise<UserSyncResult> {
    try {
      // Get local user data
      // TODO: Implement getUserById in OrganizationRepository
      const localUser = null; // await this.organizationRepository.getUserById(userId);

      if (!localUser) {
        return {
          success: false,
          error: 'User not found in local database',
        };
      }

      // TODO: Implement when getUserById is available
      // Upsert to Supabase
      // const { data: _data, error } = await this.supabaseClient
      //   .from('users')
      //   .upsert({
      //     id: localUser.external_id,
      //     email: localUser.email,
      //     name: localUser.name,
      //     avatar_url: localUser.avatar_url,
      //     updated_at: localUser.updated_at
      //   })
      //   .select()
      //   .single();

      const error: unknown = null; // Placeholder for when implemented

      if (error) {
        return {
          success: false,
          error: `Failed to sync user to Supabase: ${error instanceof Error ? error.message : String(error)}`,
        };
      }

      return {
        success: true,
        action: 'updated',
      };
    } catch (error) {
      return {
        success: false,
        error: `Error during user sync to Supabase: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Validates user data received from Supabase to ensure required fields are present.
   *
   * @param userData - User data to validate
   * @returns boolean - True if valid, false otherwise
   */
  private isValidUserData(userData: any): userData is UserProfile {
    return (
      userData &&
      typeof userData.id === 'string' &&
      typeof userData.email === 'string' &&
      userData.email.includes('@') &&
      typeof userData.updated_at === 'string'
    );
  }

  /**
   * Performs a full bidirectional sync for a user.
   * This is a convenience method that syncs both directions.
   *
   * @param userId - The user ID to perform full sync for
   * @returns Promise<{ toLocal: UserSyncResult; toSupabase: UserSyncResult }> - Results of both sync operations
   */
  async performFullUserSync(userId: string): Promise<{
    toLocal: UserSyncResult;
    toSupabase: UserSyncResult;
  }> {
    const [toLocal, toSupabase] = await Promise.all([
      this.syncUserProfileToLocal(userId),
      this.syncUserProfileToSupabase(userId),
    ]);

    return { toLocal, toSupabase };
  }

  /**
   * Bulk synchronizes multiple users from Supabase to local database.
   *
   * @param userIds - Array of user IDs to sync
   * @returns Promise<MembersSyncResult> - Result of the bulk synchronization operation
   */
  async bulkSyncUsersFromSupabase(
    userIds: string[]
  ): Promise<MembersSyncResult> {
    const results: UserSyncResult[] = [];
    const errors: string[] = [];

    for (const userId of userIds) {
      const result = await this.syncUserProfileToLocal(userId);
      results.push(result);

      if (!result.success && result.error) {
        errors.push(`${userId}: ${result.error}`);
      }
    }

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    return {
      success: errorCount === 0,
      syncedCount: successCount,
      errorCount: errorCount > 0 ? errorCount : undefined,
      errors: errors.length > 0 ? errors : undefined,
      results,
    };
  }
}
