/**
 * Export helper functions for various file formats
 */

import { jsPDF } from 'jspdf';
import <PERSON> from 'papapar<PERSON>';
import { SimpleColorEntry } from '../types';
import {
  hexToRgb,
  rgbToHsl,
} from '../../../../../shared/utils/color/conversion';
import { calculateContrastRatio } from '../../../../../shared/utils/color/analysis';

/**
 * Generate PDF export with color swatches and analysis
 */
export async function exportToPDF(
  colors: SimpleColorEntry[],
  includeAnalysis: boolean = true
): Promise<void> {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  const swatchSize = 30;
  const swatchesPerRow = Math.floor(
    (pageWidth - 2 * margin) / (swatchSize + 10)
  );

  // Title
  pdf.setFontSize(20);
  pdf.text('Color Analysis Report', margin, margin);
  pdf.setFontSize(10);
  pdf.text(
    `Generated: ${new Date().toLocaleDateString()}`,
    margin,
    margin + 10
  );

  let yPosition = margin + 25;
  let _currentRow = 0;
  let currentCol = 0;

  colors.forEach((color, _index) => {
    const xPosition = margin + currentCol * (swatchSize + 10);

    // Check if we need a new page
    if (yPosition + swatchSize + 20 > pageHeight - margin) {
      pdf.addPage();
      yPosition = margin;
      _currentRow = 0;
      currentCol = 0;
    }

    // Draw color swatch
    const rgb = hexToRgb(color.hex);
    if (rgb) {
      pdf.setFillColor(rgb.r, rgb.g, rgb.b);
      pdf.rect(xPosition, yPosition, swatchSize, swatchSize, 'F');
    }

    // Add color info
    pdf.setFontSize(8);
    pdf.text(color.pantone || 'Custom', xPosition, yPosition + swatchSize + 5);
    pdf.text(color.hex, xPosition, yPosition + swatchSize + 10);

    currentCol++;
    if (currentCol >= swatchesPerRow) {
      currentCol = 0;
      _currentRow++;
      yPosition += swatchSize + 20;
    }
  });

  // Add analysis page if requested
  if (includeAnalysis && colors.length >= 2) {
    pdf.addPage();
    pdf.setFontSize(16);
    pdf.text('Contrast Analysis', margin, margin);

    yPosition = margin + 20;
    pdf.setFontSize(10);

    // Create contrast matrix
    for (let i = 0; i < colors.length; i++) {
      for (let j = i + 1; j < colors.length; j++) {
        const rgb1 = hexToRgb(colors[i]?.hex ?? '');
        const rgb2 = hexToRgb(colors[j]?.hex ?? '');

        if (rgb1 && rgb2) {
          const ratio = calculateContrastRatio(rgb1, rgb2);
          const passesAA = ratio >= 4.5;

          pdf.text(
            `${colors[i]?.pantone ?? 'Custom'} vs ${colors[j]?.pantone ?? 'Custom'}: ${ratio.toFixed(2)}:1 ${passesAA ? '✓ AA' : '✗ Fail'}`,
            margin,
            yPosition
          );
          yPosition += 8;

          if (yPosition > pageHeight - margin) {
            pdf.addPage();
            yPosition = margin;
          }
        }
      }
    }
  }

  // Save the PDF
  pdf.save(`color-analysis-${Date.now()}.pdf`);
}

/**
 * Export colors to CSV format
 */
export function exportToCSV(colors: SimpleColorEntry[]): void {
  const data = colors.map(color => {
    const rgb = hexToRgb(color.hex);
    const hsl = rgb ? rgbToHsl(rgb) : null;
    const cmyk = parseCMYK(color.cmyk);

    return {
      'Color Name': color.pantone || 'Custom',
      HEX: color.hex,
      R: rgb?.r || 0,
      G: rgb?.g || 0,
      B: rgb?.b || 0,
      H: hsl?.h || 0,
      S: hsl?.s || 0,
      L: hsl?.l || 0,
      C: cmyk.c,
      M: cmyk.m,
      Y: cmyk.y,
      K: cmyk.k,
    };
  });

  const csv = Papa.unparse(data);
  downloadFile(csv, `colors-${Date.now()}.csv`, 'text/csv');
}

/**
 * Export to Adobe Swatch Exchange format
 */
export function exportToASE(colors: SimpleColorEntry[]): void {
  // ASE file structure (simplified version)
  const buffer = new ArrayBuffer(12 + colors.length * 38);
  const view = new DataView(buffer);

  // ASE Header
  view.setUint32(0, 0x41534546, false); // 'ASEF'
  view.setUint16(4, 1, false); // Version 1.0
  view.setUint16(6, 0, false);
  view.setUint32(8, colors.length, false); // Number of blocks

  let offset = 12;

  colors.forEach(color => {
    // Color entry
    view.setUint16(offset, 0x0001, false); // Color entry
    view.setUint32(offset + 2, 32, false); // Block length

    // Color name length (UTF-16)
    const name = color.pantone || 'Custom';
    view.setUint16(offset + 6, name.length + 1, false);

    // Write name (simplified - ASCII only)
    for (let i = 0; i < name.length; i++) {
      view.setUint16(offset + 8 + i * 2, name.charCodeAt(i), false);
    }
    view.setUint16(offset + 8 + name.length * 2, 0, false); // Null terminator

    // Color model (RGB)
    const modelOffset = offset + 8 + (name.length + 1) * 2;
    view.setUint32(modelOffset, 0x52474220, false); // 'RGB '

    // Color values
    const rgb = hexToRgb(color.hex);
    if (rgb) {
      view.setFloat32(modelOffset + 4, rgb.r / 255, false);
      view.setFloat32(modelOffset + 8, rgb.g / 255, false);
      view.setFloat32(modelOffset + 12, rgb.b / 255, false);
    }

    // Color type (0 = Global)
    view.setUint16(modelOffset + 16, 0, false);

    offset += 38;
  });

  const blob = new Blob([buffer], { type: 'application/octet-stream' });
  downloadFile(blob, `colors-${Date.now()}.ase`, 'application/octet-stream');
}

/**
 * Export to JSON format
 */
export function exportToJSON(
  colors: SimpleColorEntry[],
  includeAnalysis: boolean = true
): void {
  const data: any = {
    metadata: {
      created: new Date().toISOString(),
      version: '1.0',
      colorCount: colors.length,
    },
    colors: colors.map(color => {
      const rgb = hexToRgb(color.hex);
      const hsl = rgb ? rgbToHsl(rgb) : null;
      const cmyk = parseCMYK(color.cmyk);

      return {
        id: color.id,
        name: color.pantone || 'Custom',
        hex: color.hex,
        rgb: rgb || { r: 0, g: 0, b: 0 },
        hsl: hsl || { h: 0, s: 0, l: 0 },
        cmyk,
      };
    }),
  };

  // Add contrast analysis if requested
  if (includeAnalysis && colors.length >= 2) {
    data.analysis = {
      contrasts: [],
    };

    for (let i = 0; i < colors.length; i++) {
      for (let j = i + 1; j < colors.length; j++) {
        const rgb1 = hexToRgb(colors[i]?.hex ?? '');
        const rgb2 = hexToRgb(colors[j]?.hex ?? '');

        if (rgb1 && rgb2) {
          const ratio = calculateContrastRatio(rgb1, rgb2);
          data.analysis.contrasts.push({
            color1: colors[i]?.pantone ?? 'Custom',
            color2: colors[j]?.pantone ?? 'Custom',
            ratio: parseFloat(ratio.toFixed(2)),
            passesAA: ratio >= 4.5,
            passesAAA: ratio >= 7,
          });
        }
      }
    }
  }

  const json = JSON.stringify(data, null, 2);
  downloadFile(json, `color-analysis-${Date.now()}.json`, 'application/json');
}

/**
 * Helper function to parse CMYK string
 */
function parseCMYK(cmykString: string): {
  c: number;
  m: number;
  y: number;
  k: number;
} {
  if (!cmykString || cmykString === 'N/A') {
    return { c: 0, m: 0, y: 0, k: 0 };
  }

  // Handle format like "C:100 M:65 Y:0 K:15"
  const colonMatch = cmykString.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/i);
  if (colonMatch) {
    return {
      c: parseInt(colonMatch[1] ?? '0', 10),
      m: parseInt(colonMatch[2] ?? '0', 10),
      y: parseInt(colonMatch[3] ?? '0', 10),
      k: parseInt(colonMatch[4] ?? '0', 10),
    };
  }

  // Handle format like "100, 65, 0, 15"
  const commaMatch = cmykString.match(/(\d+),\s*(\d+),\s*(\d+),\s*(\d+)/);
  if (commaMatch) {
    return {
      c: parseInt(commaMatch[1] ?? '0', 10),
      m: parseInt(commaMatch[2] ?? '0', 10),
      y: parseInt(commaMatch[3] ?? '0', 10),
      k: parseInt(commaMatch[4] ?? '0', 10),
    };
  }

  return { c: 0, m: 0, y: 0, k: 0 };
}

/**
 * Helper function to download a file
 */
function downloadFile(
  content: string | Blob,
  filename: string,
  mimeType: string
): void {
  const blob =
    content instanceof Blob ? content : new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
