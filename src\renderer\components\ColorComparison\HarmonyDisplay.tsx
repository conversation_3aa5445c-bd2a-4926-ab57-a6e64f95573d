/**
 * @file HarmonyDisplay.tsx
 * @description Component for displaying harmony color results with world-class aesthetics
 */

import React, { useState } from 'react';
import { useColorComparisonStore } from '../../store/colorComparison.store';
import { useTokens } from '../../hooks/useTokens';
import {
  Check,
  Info,
  ClipboardCopy,
  EyeOff,
  Eye,
  Copy,
  FileText,
  Code,
} from 'lucide-react';
import { getContrastRatio, getWCAGLevel } from './utils';
import Tooltip from '../Tooltip';
import { formatCMYKForDisplay, parseCMYK } from '../../../shared/utils/color';
import { useToast } from '../../hooks/useToast';
import { useClickOutside } from '../../utils/useClickOutside';

// Function removed as it's not used

const HarmonyDisplay: React.FC = () => {
  const tokens = useTokens();
  const { harmonyResults: harmonyColors, selectedHarmonyType: harmonyType } =
    useColorComparisonStore();
  const [copiedColor, setCopiedColor] = useState<string | null>(null);
  const [activeColor, setActiveColor] = useState<number | null>(null);
  const [showDetails, setShowDetails] = useState<boolean>(true);
  const [showCopyMenu, setShowCopyMenu] = useState<boolean>(false);
  const { toast } = useToast();
  const copyMenuRef = React.useRef<HTMLDivElement>(null);

  useClickOutside(copyMenuRef, () => setShowCopyMenu(false));

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedColor(text);
    setTimeout(() => setCopiedColor(null), 1500);
  };

  const copyAllColors = (format: 'hex' | 'rgb' | 'json') => {
    if (!harmonyColors || harmonyColors.length === 0) {
      return;
    }

    let copyText = '';

    switch (format) {
      case 'hex':
        copyText = harmonyColors.map(c => c.hex).join(', ');
        break;
      case 'rgb':
        copyText = harmonyColors
          .map(c => (c.rgb ? `rgb(${c.rgb.r}, ${c.rgb.g}, ${c.rgb.b})` : c.hex))
          .join(', ');
        break;
      case 'json':
        copyText = JSON.stringify(
          harmonyColors.map(c => ({
            hex: c.hex,
            rgb: c.rgb,
            name: c.name || c.code || undefined,
            cmyk: c.cmyk || undefined,
          })),
          null,
          2
        );
        break;
    }

    navigator.clipboard.writeText(copyText);
    toast({
      title: `Copied ${harmonyColors.length} colors`,
      description: `Colors copied as ${format.toUpperCase()} format`,
      type: 'success',
    });
    setShowCopyMenu(false);
  };

  if (!harmonyColors || harmonyColors.length === 0) {
    return (
      <div
        className='text-center flex flex-col items-center justify-center h-48 border-dashed'
        style={{
          padding: 'var(--spacing-4)',
          backgroundColor: 'var(--color-ui-background-secondary)',
          borderRadius: 'var(--radius-lg)',
          border: `1px dashed var(--color-ui-border-light)`,
          color: 'var(--color-ui-foreground-tertiary)',
        }}
      >
        <div style={{ marginBottom: 'var(--spacing-2)' }}>
          <Info
            size={20}
            className='opacity-40'
            style={{ color: 'var(--color-ui-foreground-tertiary)' }}
          />
        </div>
        <p
          className='font-medium'
          style={{
            fontSize: '0.6rem',
            fontWeight: 'var(--font-weight-medium)',
            color: 'var(--color-ui-foreground-secondary)',
          }}
        >
          No harmony colors generated yet
        </p>
        <p
          style={{
            fontSize: '0.55rem',
            marginTop: 'var(--spacing-1)',
            color: 'var(--color-ui-foreground-tertiary)',
          }}
        >
          Select a base color and harmony type, then click the refresh button
        </p>
      </div>
    );
  }

  return (
    <div className='harmony-display'>
      <div className='flex items-center justify-between mb-[var(--spacing-3)]'>
        <h3
          className='text-xs font-medium flex items-center'
          style={{
            fontSize: 'var(--font-size-xs)',
            fontWeight: 'var(--font-weight-medium)',
            color: 'var(--color-ui-foreground-primary)',
          }}
        >
          {harmonyType.charAt(0).toUpperCase() + harmonyType.slice(1)} Colour
          Harmony
          <Tooltip content='Color harmonies are combinations of colors that create visual balance and are appealing to the eye.'>
            <button style={{ marginLeft: 'var(--spacing-1)' }}>
              <Info
                size={12}
                style={{ color: 'var(--color-ui-foreground-tertiary)' }}
              />
            </button>
          </Tooltip>
        </h3>
        <div className='flex items-center gap-[var(--spacing-2)]'>
          <div className='relative' ref={copyMenuRef}>
            <button
              onClick={() => setShowCopyMenu(!showCopyMenu)}
              className='flex items-center transition-colors'
              style={{
                gap: 'var(--spacing-1)',
                fontSize: '0.5rem',
                color: 'var(--color-ui-foreground-tertiary)',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.color =
                  'var(--color-ui-foreground-primary)';
              }}
              onMouseLeave={e => {
                e.currentTarget.style.color =
                  'var(--color-ui-foreground-tertiary)';
              }}
            >
              <Copy size={10} />
              <span>Copy all</span>
            </button>
            {showCopyMenu && (
              <div
                className='absolute top-full right-0 mt-1 py-1 z-10 min-w-[120px]'
                style={{
                  backgroundColor: 'var(--color-ui-background-primary)',
                  border: `1px solid var(--color-ui-border-light)`,
                  borderRadius: 'var(--radius-md)',
                  boxShadow: 'var(--shadow-lg)',
                }}
              >
                <button
                  onClick={() => copyAllColors('hex')}
                  className='flex items-center gap-2 w-full px-3 py-1.5 transition-colors'
                  style={{
                    fontSize: '0.55rem',
                    color: 'var(--color-ui-foreground-primary)',
                    backgroundColor: 'transparent',
                    border: 'none',
                    cursor: 'pointer',
                  }}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor =
                      'var(--color-ui-background-secondary)';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <Code size={12} />
                  HEX values
                </button>
                <button
                  onClick={() => copyAllColors('rgb')}
                  className='flex items-center gap-2 w-full px-3 py-1.5 transition-colors'
                  style={{
                    fontSize: '0.55rem',
                    color: 'var(--color-ui-foreground-primary)',
                    backgroundColor: 'transparent',
                    border: 'none',
                    cursor: 'pointer',
                  }}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor =
                      'var(--color-ui-background-secondary)';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <FileText size={12} />
                  RGB values
                </button>
                <button
                  onClick={() => copyAllColors('json')}
                  className='flex items-center gap-2 w-full px-3 py-1.5 transition-colors'
                  style={{
                    fontSize: '0.55rem',
                    color: 'var(--color-ui-foreground-primary)',
                    backgroundColor: 'transparent',
                    border: 'none',
                    cursor: 'pointer',
                  }}
                  onMouseEnter={e => {
                    e.currentTarget.style.backgroundColor =
                      'var(--color-ui-background-secondary)';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <Code size={12} />
                  JSON format
                </button>
              </div>
            )}
          </div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className='flex items-center transition-colors'
            style={{
              gap: 'var(--spacing-1)',
              fontSize: '0.5rem',
              color: 'var(--color-ui-foreground-tertiary)',
              backgroundColor: 'transparent',
              border: 'none',
              cursor: 'pointer',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.color =
                'var(--color-ui-foreground-primary)';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.color =
                'var(--color-ui-foreground-tertiary)';
            }}
          >
            {showDetails ? (
              <>
                <EyeOff size={10} />
                <span>Hide details</span>
              </>
            ) : (
              <>
                <Eye size={10} />
                <span>Show details</span>
              </>
            )}
          </button>
        </div>
      </div>

      <div className='harmony-colors grid grid-cols-5 gap-[var(--spacing-3)]'>
        {harmonyColors.map((color, index) => {
          const blackContrast = getContrastRatio(color.hex, '#000000');
          const whiteContrast = getContrastRatio(color.hex, '#ffffff');
          // Removed unused variable: bestTextColor
          // const bestTextColor = whiteContrast > blackContrast ? '#ffffff' : '#000000';
          const blackLevel = getWCAGLevel(blackContrast);
          const whiteLevel = getWCAGLevel(whiteContrast);

          const isActive = activeColor === index;

          return (
            <div
              key={index}
              className={`harmony-color relative rounded-[var(--radius-lg)] overflow-hidden group ${isActive ? 'ring-2 ring-brand-primary' : ''}`}
              style={{
                boxShadow: isActive ? tokens.shadows.lg : tokens.shadows.md,
                transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
              }}
              onMouseEnter={() => setActiveColor(index)}
              onMouseLeave={() => setActiveColor(null)}
            >
              <div
                className='color-preview aspect-square flex flex-col items-center justify-center cursor-pointer relative overflow-hidden'
                style={{ backgroundColor: color.hex }}
                onClick={() => copyToClipboard(color.hex)}
              >
                {/* Pantone Code and Hex Display */}
                <div className='absolute inset-0 flex flex-col items-center justify-center p-2'>
                  {color.code && (
                    <div
                      className='text-white text-[0.55rem] font-semibold mb-1'
                      style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}
                    >
                      {color.code}
                    </div>
                  )}
                  <div
                    className='text-white text-[0.5rem] font-mono font-bold'
                    style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}
                  >
                    {color.hex}
                  </div>
                </div>

                {/* Hover overlay */}
                <div
                  className='absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200'
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.4)',
                  }}
                >
                  <ClipboardCopy className='text-white' size={16} />
                </div>

                {/* Overlay for focus state */}
                <div
                  className={`absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 ${copiedColor === color.hex ? 'bg-opacity-20' : ''}`}
                 />

                {/* Copy feedback */}
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${copiedColor === color.hex ? 'opacity-100' : 'opacity-0'}`}
                  style={{ backgroundColor: 'rgba(0,0,0,0.2)' }}
                >
                  <div
                    className='rounded-full p-1.5'
                    style={{
                      transform: 'scale(1.2)',
                      backgroundColor: 'var(--color-ui-background-primary)',
                      boxShadow: 'var(--shadow-lg)',
                    }}
                  >
                    <Check
                      size={14}
                      style={{ color: 'var(--color-brand-primary)' }}
                    />
                  </div>
                </div>
              </div>

              {showDetails && (
                <div
                  className='color-info border-t'
                  style={{
                    padding: 'var(--spacing-1.5)',
                    backgroundColor: 'var(--color-ui-background-primary)',
                    borderTopColor: 'var(--color-ui-border-light)',
                  }}
                >
                  {/* RGB Values */}
                  {color.rgb && (
                    <div
                      className='text-center'
                      style={{
                        fontSize: '0.45rem',
                        color: 'var(--color-ui-foreground-secondary)',
                        marginBottom: 'var(--spacing-1)',
                      }}
                    >
                      RGB: {color.rgb.r}, {color.rgb.g}, {color.rgb.b}
                    </div>
                  )}

                  <div className='grid grid-cols-2 gap-[var(--spacing-1)]'>
                    <div
                      className='flex items-center justify-center'
                      style={{
                        fontSize: '0.45rem',
                        backgroundColor: 'var(--color-ui-background-secondary)',
                        paddingTop: 'var(--spacing-0.5)',
                        paddingBottom: 'var(--spacing-0.5)',
                        paddingLeft: 'var(--spacing-0.5)',
                        paddingRight: 'var(--spacing-0.5)',
                        borderRadius: 'var(--radius-sm)',
                      }}
                    >
                      <span
                        className='w-1.5 h-1.5 rounded-full flex-shrink-0'
                        style={{
                          backgroundColor: '#ffffff',
                          border: `1px solid var(--color-ui-border-light)`,
                          marginRight: 'var(--spacing-0.5)',
                        }}
                       />
                      <span
                        style={{
                          color:
                            whiteLevel === 'AAA'
                              ? 'var(--color-feedback-success)'
                              : whiteLevel === 'AA'
                                ? 'var(--color-feedback-warning)'
                                : 'var(--color-feedback-error)',
                        }}
                      >
                        {whiteLevel}
                      </span>
                    </div>

                    <div
                      className='flex items-center justify-center'
                      style={{
                        fontSize: '0.45rem',
                        backgroundColor: 'var(--color-ui-background-secondary)',
                        paddingTop: 'var(--spacing-0.5)',
                        paddingBottom: 'var(--spacing-0.5)',
                        paddingLeft: 'var(--spacing-0.5)',
                        paddingRight: 'var(--spacing-0.5)',
                        borderRadius: 'var(--radius-sm)',
                      }}
                    >
                      <span
                        className='w-1.5 h-1.5 rounded-full flex-shrink-0'
                        style={{
                          backgroundColor: '#000000',
                          marginRight: 'var(--spacing-0.5)',
                        }}
                       />
                      <span
                        style={{
                          color:
                            blackLevel === 'AAA'
                              ? 'var(--color-feedback-success)'
                              : blackLevel === 'AA'
                                ? 'var(--color-feedback-warning)'
                                : 'var(--color-feedback-error)',
                        }}
                      >
                        {blackLevel}
                      </span>
                    </div>

                    {color.cmyk && (
                      <div
                        className='col-span-2 text-center truncate'
                        style={{
                          fontSize: '0.45rem',
                          color: 'var(--color-ui-foreground-tertiary)',
                          marginTop: 'var(--spacing-0.5)',
                        }}
                        title={formatCMYKForDisplay(parseCMYK(color.cmyk))}
                      >
                        {formatCMYKForDisplay(parseCMYK(color.cmyk))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Color harmony explanation */}
      <div
        className='border'
        style={{
          marginTop: 'var(--spacing-4)',
          padding: 'var(--spacing-2)',
          backgroundColor: 'var(--color-ui-background-secondary)',
          borderRadius: 'var(--radius-md)',
          borderColor: 'var(--color-ui-border-light)',
          fontSize: '0.6rem',
          color: 'var(--color-ui-foreground-secondary)',
        }}
      >
        <p style={{ marginBottom: 'var(--spacing-1)' }}>
          <strong
            className='font-medium'
            style={{
              fontWeight: 'var(--font-weight-medium)',
              color: 'var(--color-ui-foreground-primary)',
            }}
          >
            {harmonyType.charAt(0).toUpperCase() + harmonyType.slice(1)} harmony
          </strong>{' '}
          is a colour scheme that {getHarmonyDescription(harmonyType)}
        </p>
        <p
          style={{
            fontSize: '0.5rem',
            color: 'var(--color-ui-foreground-tertiary)',
          }}
        >
          Click on any color to copy its hex value to your clipboard
        </p>
      </div>
    </div>
  );
};

// Helper function to get descriptions based on harmony type
const getHarmonyDescription = (harmonyType: string): string => {
  switch (harmonyType) {
    case 'complementary':
      return 'uses colors opposite each other on the color wheel, creating high contrast and visual impact.';
    case 'analogous':
      return 'uses colors adjacent to each other on the color wheel, creating a cohesive and harmonious feel.';
    case 'triadic':
      return 'uses three colors evenly spaced around the color wheel, offering vibrant contrast while maintaining balance.';
    case 'tetradic':
      return 'uses four colors arranged in two complementary pairs, offering rich color possibilities with balanced contrast.';
    case 'splitComplementary':
      return 'uses a base color and two colors adjacent to its complement, creating visual interest with less tension than complementary schemes.';
    case 'monochromatic':
      return 'uses different shades, tones and tints of a single color, creating a cohesive look with visual depth.';
    case 'shades':
      return 'uses variations of a base color mixed with black, creating a sophisticated and cohesive palette.';
    case 'tints':
      return 'uses variations of a base color mixed with white, creating a light and airy palette with visual cohesion.';
    case 'compound':
      return 'combines analogous and complementary colors for a rich palette with both harmony and contrast.';
    default:
      return 'creates a balanced and visually appealing combination of colors based on color theory principles.';
  }
};

export default HarmonyDisplay;
