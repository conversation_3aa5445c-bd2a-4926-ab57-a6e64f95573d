#!/usr/bin/env node

/**
 * Test script to verify product deletion persistence
 * Usage: node scripts/test-product-deletion.js
 */

const path = require('path');
const Database = require('better-sqlite3');
const { v4: uuidv4 } = require('uuid');

// Database path
const dbPath = path.join(
  process.env.HOME || process.env.USERPROFILE,
  process.platform === 'darwin' 
    ? 'Library/Application Support/chroma-sync/chromasync.db'
    : process.platform === 'win32'
    ? 'AppData/Roaming/chroma-sync/chromasync.db'
    : '.config/chroma-sync/chromasync.db'
);

console.log('🔍 Testing product deletion persistence...');
console.log(`Database path: ${dbPath}`);

// Connect to database
const db = new Database(dbPath);

// Test organization ID (replace with your actual organization ID)
const TEST_ORG_ID = process.argv[2];

if (!TEST_ORG_ID) {
  console.error('❌ Please provide organization ID as argument');
  console.log('Usage: node scripts/test-product-deletion.js <organization-id>');
  
  // Show available organizations
  const orgs = db.prepare('SELECT external_id, name FROM organizations').all();
  console.log('\nAvailable organizations:');
  orgs.forEach(org => console.log(`  - ${org.external_id} (${org.name})`));
  
  process.exit(1);
}

// Test product data
const testProductId = uuidv4();
const testProductName = `Test Product ${Date.now()}`;

console.log('\n📝 Creating test product...');
console.log(`  ID: ${testProductId}`);
console.log(`  Name: ${testProductName}`);

try {
  // Create test product
  db.prepare(`
    INSERT INTO products (
      external_id, organization_id, name, 
      is_active, created_at, updated_at
    ) VALUES (?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `).run(testProductId, TEST_ORG_ID, testProductName);
  
  console.log('✅ Test product created');
  
  // Verify creation
  const created = db.prepare(`
    SELECT * FROM products WHERE external_id = ?
  `).get(testProductId);
  
  console.log('📊 Product state after creation:');
  console.log(`  - is_active: ${created.is_active}`);
  console.log(`  - deleted_at: ${created.deleted_at}`);
  
  // Delete the product
  console.log('\n🗑️ Deleting test product...');
  db.prepare(`
    UPDATE products 
    SET deleted_at = CURRENT_TIMESTAMP, is_active = 0 
    WHERE external_id = ?
  `).run(testProductId);
  
  // Verify deletion
  const deleted = db.prepare(`
    SELECT * FROM products WHERE external_id = ?
  `).get(testProductId);
  
  console.log('✅ Product deleted locally');
  console.log('📊 Product state after deletion:');
  console.log(`  - is_active: ${deleted.is_active}`);
  console.log(`  - deleted_at: ${deleted.deleted_at}`);
  
  // Check all deleted products
  console.log('\n📋 All deleted products in organization:');
  const deletedProducts = db.prepare(`
    SELECT external_id, name, is_active, deleted_at 
    FROM products 
    WHERE organization_id = ? AND (deleted_at IS NOT NULL OR is_active = 0)
  `).all(TEST_ORG_ID);
  
  deletedProducts.forEach(p => {
    console.log(`  - ${p.name} (ID: ${p.external_id})`);
    console.log(`    is_active: ${p.is_active}, deleted_at: ${p.deleted_at}`);
  });
  
  console.log('\n✨ Test complete!');
  console.log('\nNext steps:');
  console.log('1. Run the app and trigger a sync');
  console.log('2. Check if the test product remains deleted');
  console.log('3. Look for DEBUG logs in the console showing deletion preservation');
  
} catch (error) {
  console.error('❌ Test failed:', error);
} finally {
  db.close();
}
