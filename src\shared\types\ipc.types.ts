import { ColorEntry } from './color.types';

/**
 * Standard response interface for all IPC operations
 * Provides consistent structure across all IPC handlers
 */
export interface IPCResponse<T = any> {
  /** Indicates if the operation was successful */
  success: boolean;
  /** The response data if successful */
  data?: T;
  /** Technical error message for debugging */
  error?: string;
  /** User-friendly message for display */
  userMessage?: string;
  /** Timestamp when the response was created */
  timestamp: number;
}

/**
 * Type for IPC handler functions with organization context
 */
export type OrgIPCHandler<TArgs extends any[], TResult> = (
  organizationId: string,
  ...args: TArgs
) => Promise<TResult> | TResult;

/**
 * Type for IPC handler functions without organization context
 */
export type SystemIPCHandler<TArgs extends any[], TResult> = (
  ...args: TArgs
) => Promise<TResult> | TResult;

/**
 * Configuration options for IPC handler wrappers
 */
export interface IPCHandlerOptions {
  /** Whether organization context is required */
  requireOrganization?: boolean;
  /** Custom log channel name for debugging, or false to disable logging */
  logChannel?: string | false;
  /** Custom error message for users */
  customErrorMessage?: string;
  /** Whether to skip duplicate handler registration checks */
  skipDuplicateCheck?: boolean;
  /** Whether authentication is required */
  requireAuth?: boolean;
}

export interface IpcChannels {
  // Color operations
  'color:getAll': () => Promise<ColorEntry[]>;
  'color:add': (entry: Omit<ColorEntry, 'id' | 'createdAt' | 'updatedAt'>) => Promise<ColorEntry>;
  'color:update': (id: string, updates: Partial<ColorEntry>) => Promise<ColorEntry>;
  'color:delete': (id: string) => Promise<boolean>;
  
  // Import/Export operations
  'data:import': (mergeMode?: 'replace' | 'merge') => Promise<{ added: number; errors: string[] }>;
  'data:export': (filePath: string, format: 'csv' | 'json') => Promise<boolean>;
  'data:clear': () => Promise<boolean>;
} 