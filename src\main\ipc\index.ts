/**
 * @file index.ts
 * @description Centralized IPC handler registration system
 *
 * This module provides a unified interface for registering all IPC handlers
 * in the ChromaSync application. It replaces the scattered handler registration
 * logic that was previously in the main index.ts file.
 */

import { ipcMain } from 'electron';
import { registerHandlerSafely, resetRegistry } from '../utils/ipcRegistry';
import { ServiceLocator } from '../services/service-locator';

// Import all IPC handler modules
import { registerDatasheetHandlers } from './datasheet.ipc';
import { registerOrganizationHandlers } from './organization.ipc';
import { registerAuditIpcHandlers } from './audit.ipc';
import { registerSoftDeleteIpcHandlers } from './soft-delete.ipc';
import { registerIntegrityIpcHandlers } from './integrity.ipc';
import { registerSettingsIpcHandlers } from './settings.ipc';
import { registerLicenseHandlers } from './license-handlers';
import { initColorSpace3DWindowHandlers } from '../windows/color-space-3d-window';
import { registerProductHandlers } from './product.ipc';
import { registerTestDataHandlers } from './test-data.ipc';
import { registerSyncHandlers } from './sync-handlers';
import { registerSampleDataHandlers } from './sample-data.ipc';
// import { registerColorLibraryIPC } from './color-library.ipc';
import { registerColorHandlers } from './color.ipc';
import { getValidatedOrganizationId } from '../middleware/organization-context.middleware';

/**
 * Register critical handlers that need to be available immediately
 * These handlers are registered before the main initialization to ensure
 * they're available for early renderer process communication
 */
export async function registerCriticalHandlers(): Promise<void> {
  console.log('[IPC] 🚨 Registering critical handlers...');

  // Register critical color handler - CONSOLIDATED VERSION
  registerHandlerSafely(ipcMain, 'color:getAllWithUsage', async _event => {
    try {
      console.log(
        '[DirectIPC] 🔄 GET_ALL_WITH_USAGE called (consolidated handler)'
      );
      const organizationId = await getValidatedOrganizationId();

      if (!organizationId) {
        return {
          success: false,
          error: 'No organization context',
          timestamp: Date.now(),
        };
      }

      const colorService = ServiceLocator.getColorService();
      const result = await colorService.getAllWithUsage(organizationId);

      return {
        success: true,
        data: result,
        userMessage: `Retrieved ${result.colors?.length || 0} colors with usage data`,
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error(
        '[DirectIPC] ❌ Error in consolidated getAllWithUsage:',
        error
      );
      return {
        success: false,
        error: (error as Error).message,
        userMessage: 'Failed to retrieve colors with usage data',
        timestamp: Date.now(),
      };
    }
  });

  // Register critical product handler - CONSOLIDATED VERSION
  registerHandlerSafely(ipcMain, 'product:getAllWithColors', async _event => {
    try {
      console.log(
        '[DirectIPC] 🔄 PRODUCT getAllWithColors called (consolidated handler)'
      );
      const organizationId = await getValidatedOrganizationId();

      if (!organizationId) {
        return {
          success: false,
          error: 'No organization context',
          timestamp: Date.now(),
        };
      }

      const productService = ServiceLocator.getProductService();
      const products =
        await productService.getAllProductsWithColors(organizationId);

      return {
        success: true,
        data: products,
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error(
        '[DirectIPC] ❌ Error in consolidated getAllWithColors:',
        error
      );
      return {
        success: false,
        error: (error as Error).message,
        timestamp: Date.now(),
      };
    }
  });

  // Register color:getAll handler (alias for getAllWithUsage)
  registerHandlerSafely(ipcMain, 'color:getAll', async _event => {
    try {
      console.log('[DirectIPC] 🔄 GET_ALL called (alias for getAllWithUsage)');
      const organizationId = await getValidatedOrganizationId();

      if (!organizationId) {
        return {
          success: false,
          error: 'No organization context',
          timestamp: Date.now(),
        };
      }

      const colorService = ServiceLocator.getColorService();

      console.log(
        '[DirectIPC] 🌈 Fetching colors for organization:',
        organizationId
      );
      const colors = await colorService.getAllWithUsage(organizationId);

      console.log(
        `[DirectIPC] ✅ Successfully fetched ${Array.isArray(colors) ? colors.length : 'unknown'} colors`
      );
      return {
        success: true,
        data: colors,
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error('[DirectIPC] ❌ Error in GET_ALL handler:', error);
      return {
        success: false,
        error: (error as Error).message,
        timestamp: Date.now(),
      };
    }
  });

  // Register critical getProductsByColorName handler
  registerHandlerSafely(
    ipcMain,
    'color:getProductsByColorName',
    async _event => {
      try {
        console.log(
          '[DirectIPC] 🔄 GET_PRODUCTS_BY_COLOR_NAME called (consolidated handler)'
        );
        const organizationId = await getValidatedOrganizationId();

        if (!organizationId) {
          return {
            success: false,
            error: 'No organization context',
            timestamp: Date.now(),
          };
        }

        const colorService = ServiceLocator.getColorService();
        const productsByColor =
          await colorService.getProductsByColorName(organizationId);

        return {
          success: true,
          data: productsByColor,
          userMessage: `Retrieved product associations for ${Object.keys(productsByColor).length} colors`,
          timestamp: Date.now(),
        };
      } catch (error) {
        console.error(
          '[DirectIPC] ❌ Error in consolidated getProductsByColorName:',
          error
        );
        return {
          success: false,
          error: (error as Error).message,
          userMessage: 'Failed to retrieve color-product associations',
          timestamp: Date.now(),
        };
      }
    }
  );

  // Register debug handlers for development only - SECURITY FIX
  // These handlers expose sensitive data and should never be available in production
  if (
    process.env.NODE_ENV === 'development' ||
    process.env.DEBUG_HANDLERS === 'true'
  ) {
    console.log('[IPC] 🔧 Registering debug handlers (development mode)');

    registerHandlerSafely(
      ipcMain,
      'debug:check-supabase-data',
      async (_event: any) => {
        try {
          console.log('[Debug] 🔍 Checking Supabase data...');

          // Get current user and organization
          const { getOAuthService } = await import(
            '../services/service-locator'
          );
          const { getSupabaseClient } = await import(
            '../services/supabase-client'
          );
          const { getCurrentOrganization } = await import(
            '../utils/organization-context'
          );

          const oauthService = getOAuthService();
          const supabase = getSupabaseClient();
          const currentUser = await oauthService.getCurrentUser();
          const currentOrgId = getCurrentOrganization();

          if (!currentUser) {
            return { error: 'No authenticated user' };
          }

          console.log(
            '[Debug] Current user:',
            currentUser.email,
            'ID:',
            currentUser.id
          );
          console.log('[Debug] Current org:', currentOrgId);

          // Check colors in Supabase for current organization
          const { count: orgColorCount, data: orgColors } = await supabase
            .from('colors')
            .select('id, code, organization_id, user_id, created_at', {
              count: 'exact',
            })
            .eq('organization_id', currentOrgId)
            .is('deleted_at', null)
            .limit(5);

          // Check colors for current user across all organizations
          const { count: userColorCount, data: userColors } = await supabase
            .from('colors')
            .select('id, code, organization_id, user_id, created_at', {
              count: 'exact',
            })
            .eq('user_id', currentUser.id)
            .is('deleted_at', null)
            .limit(5);

          // Get organization distribution of all colors by this user
          const { data: allUserColors } = await supabase
            .from('colors')
            .select('organization_id')
            .eq('user_id', currentUser.id)
            .is('deleted_at', null);

          const orgDistribution: Record<string, number> = {};
          allUserColors?.forEach(color => {
            const orgId = color.organization_id || 'null';
            orgDistribution[orgId] = (orgDistribution[orgId] || 0) + 1;
          });

          // Check all organizations this user has access to
          const { data: allOrgs } = await supabase
            .from('organizations')
            .select('*');

          const result = {
            currentUser: {
              id: currentUser.id,
              email: currentUser.email,
            },
            currentOrganization: currentOrgId,
            orgColorCount: orgColorCount || 0,
            userColorCount: userColorCount || 0,
            orgColors: orgColors || [],
            userColors: userColors || [],
            orgDistribution,
            allOrganizations: allOrgs || [],
            summary: `User has ${userColorCount || 0} colors total, ${orgColorCount || 0} in current org`,
          };

          console.log('[Debug] 📊 Results:', result);
          return result;
        } catch (error) {
          console.error('[Debug] ❌ Error checking Supabase data:', error);
          return {
            error: error instanceof Error ? error.message : String(error),
          };
        }
      }
    );

    // Register debug handler for auth session checking
    registerHandlerSafely(
      ipcMain,
      'debug:check-auth-session',
      async (_event: any) => {
        try {
          console.log('[Debug] 🔐 Checking authentication session...');

          const { ensureAuthenticatedSession, getSupabaseClient } =
            await import('../services/supabase-client');
          const { getOAuthService } = await import(
            '../services/service-locator'
          );

          // Check current OAuth state
          const oauthService = getOAuthService();
          const currentUser = await oauthService.getCurrentUser();

          // Check Supabase session
          const { session, error: sessionError } =
            await ensureAuthenticatedSession();

          // Test actual query with auth
          let queryTest = null;
          if (session) {
            try {
              const supabase = getSupabaseClient();
              const { data, error: queryError } = await supabase
                .from('colors')
                .select('count')
                .limit(1);
              queryTest = {
                success: !queryError,
                error: queryError?.message,
                data,
              };
            } catch (error) {
              queryTest = {
                success: false,
                error: error instanceof Error ? error.message : String(error),
              };
            }
          }

          const result = {
            status: session ? 'authenticated' : 'not_authenticated',
            sessionError: sessionError || null,
            userEmail: session?.user?.email || currentUser?.email || null,
            userId: session?.user?.id || currentUser?.id || null,
            accessToken: session?.access_token ? 'present' : 'missing',
            oauthUser: currentUser
              ? { id: currentUser.id, email: currentUser.email }
              : null,
            queryTest,
          };

          console.log('[Debug] 🔐 Auth session result:', result);
          return result;
        } catch (error) {
          console.error('[Debug] ❌ Error checking auth session:', error);
          return {
            status: 'error',
            error: error instanceof Error ? error.message : String(error),
          };
        }
      }
    );
  } else {
    console.log(
      '[IPC] 🔒 Debug handlers disabled in production mode for security'
    );
  }

  console.log('[IPC] ✅ Critical handlers registered successfully');
}

/**
 * Register all IPC handlers in the correct order
 * This function should be called after services are initialized
 */
export async function registerAllHandlers(): Promise<void> {
  console.log('[IPC] 🚀 Starting comprehensive handler registration...');

  try {
    // Get services from ServiceLocator
    let colorService: any, productService: any, organizationService: any;
    try {
      colorService = ServiceLocator.getColorService();
      productService = ServiceLocator.getProductService();
      organizationService = ServiceLocator.getOrganizationService();
    } catch (error) {
      console.error('[IPC] Failed to get services from ServiceLocator:', error);
      console.warn('[IPC] Handlers will register with limited functionality');
    }

    // Register organization handlers first (needed for context)
    try {
      if (organizationService) {
        registerOrganizationHandlers(
          organizationService,
          ServiceLocator.getDatabase()
        );
        console.log('[IPC] Organization handlers registered');
      } else {
        console.warn(
          '[IPC] Skipping organization handlers - service not available'
        );
      }
    } catch (error) {
      console.error('[IPC] Failed to register organization handlers:', error);
    }

    // Register datasheet handlers
    try {
      const datasheetService = ServiceLocator.getDatasheetService();
      if (datasheetService) {
        registerDatasheetHandlers({ datasheetService });
        console.log('[IPC] Datasheet handlers registered');
      } else {
        console.warn(
          '[IPC] DatasheetService not available, skipping registration'
        );
      }
    } catch (error) {
      console.error('[IPC] Error registering datasheet handlers:', error);
    }

    // Register color handlers
    try {
      if (colorService) {
        const colorImportService = ServiceLocator.getColorImportService();
        registerColorHandlers({ colorService, colorImportService });
        console.log('[IPC] Color handlers registered');
      } else {
        console.warn('[IPC] Skipping color handlers - service not available');
      }
    } catch (error) {
      console.error('[IPC] Failed to register color handlers:', error);
    }

    // Register product handlers
    try {
      if (productService) {
        registerProductHandlers(productService, colorService);
        console.log('[IPC] Product handlers registered');
      } else {
        console.warn('[IPC] Skipping product handlers - service not available');
      }
    } catch (error) {
      console.error('[IPC] Failed to register product handlers:', error);
    }

    // Register test data handlers
    try {
      if (productService && colorService) {
        registerTestDataHandlers({ productService, colorService });
        console.log('[IPC] Test data handlers registered');
      } else {
        console.warn(
          '[IPC] Skipping test data handlers - services not available'
        );
      }
    } catch (error) {
      console.error('[IPC] Failed to register test data handlers:', error);
    }

    // Register sample data handlers
    try {
      if (productService && colorService) {
        registerSampleDataHandlers({ productService, colorService });
        console.log('[IPC] Sample data handlers registered');
      } else {
        console.warn(
          '[IPC] Skipping sample data handlers - services not available'
        );
      }
    } catch (error) {
      console.error('[IPC] Failed to register sample data handlers:', error);
    }

    // Register audit, integrity, and settings handlers
    registerAuditIpcHandlers();
    registerSoftDeleteIpcHandlers();
    registerIntegrityIpcHandlers();
    registerSettingsIpcHandlers();

    console.log('[IPC] ✅ All IPC handlers registered successfully');
  } catch (error) {
    console.error('[IPC] Error during handler registration:', error);
    throw error;
  }
}

/**
 * Initialize IPC handlers that don't require database services
 * These can be registered early in the startup process
 */
export async function registerEarlyHandlers(): Promise<void> {
  console.log('[IPC] 🚀 Registering early handlers...');

  try {
    // Register sync handlers first (doesn't need database)
    try {
      registerSyncHandlers();
      console.log('[IPC] Sync handlers registered');
    } catch (error) {
      console.log('[IPC] Sync handlers already registered, skipping');
    }

    // Register license handlers (doesn't need database initially)
    try {
      registerLicenseHandlers(null);
      console.log('[IPC] License handlers registered');
    } catch (error) {
      console.log('[IPC] License handlers already registered, skipping');
    }

    // Register 3D color space window handlers (doesn't need database)
    try {
      initColorSpace3DWindowHandlers();
      console.log('[IPC] 3D color space window handlers registered');
    } catch (error) {
      console.log(
        '[IPC] 3D color space window handlers already registered, skipping'
      );
    }

    console.log('[IPC] ✅ Early handlers registered successfully');
  } catch (error) {
    console.error('[IPC] Error registering early handlers:', error);
    throw error;
  }
}

/**
 * Main function to initialize all IPC handlers
 * This should be called from the main process after services are initialized
 */
export async function initializeIpcHandlers(): Promise<void> {
  const startTime = Date.now();

  // Clear IPC registry to prevent duplicate handlers during development
  resetRegistry(ipcMain);
  console.log('[IPC] Registry cleared');

  // Register handlers in order
  await registerCriticalHandlers();
  await registerEarlyHandlers();
  await registerAllHandlers();

  const initTime = Date.now() - startTime;
  console.log(`[IPC] All handlers registered successfully in ${initTime}ms`);
}
