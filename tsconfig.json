{
  "compilerOptions": {
    /* Build Performance Optimization */
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable", "ES2021.String", "ES2021.Promise"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Enhanced Type-Checking - 2025 Best Practice Balance */
    "strict": true,
    "noUncheckedIndexedAccess": false,
    "noImplicitOverride": true,
    "exactOptionalPropertyTypes": false,
    "noImplicitReturns": true,
    "noPropertyAccessFromIndexSignature": false,
    "noUncheckedSideEffectImports": false,

    /* External Dependency Support */
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "typeRoots": ["./node_modules/@types", "./src/types"],

    /* Code Quality Enforcement - Practical Settings */
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,

    /* Module Resolution Enhancement */
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "verbatimModuleSyntax": false,
    "allowArbitraryExtensions": false,

    /* Build Optimization */
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo",
    "composite": false,
    "declaration": false,
    "declarationMap": false,
    "sourceMap": false,

    /* Performance Optimization */
    "assumeChangesOnlyAffectDirectDependencies": true,

    /* Experimental Options for Electron */
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,

    /* Enhanced Path Mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@main/*": ["src/main/*"],
      "@renderer/*": ["src/renderer/*"],
      "@shared/*": ["src/shared/*"],
      "@types/*": ["src/types/*"],
      "@components/*": ["src/renderer/components/*"],
      "@hooks/*": ["src/renderer/hooks/*"],
      "@utils/*": ["src/shared/utils/*"],
      "@services/*": ["src/main/services/*"],
      "@db/*": ["src/main/db/*"]
    }
  },
  "ts-node": {
    "esm": true,
    "experimentalSpecifierResolution": "node",
    "transpileOnly": true,
    "files": true,
    "compilerOptions": {
      "module": "CommonJS",
      "target": "ES2020"
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.d.ts",
    "src/types/**/*.d.ts",
    "electron.vite.config.ts",
    "electron-vite.config.ts",
    "vite.config.ts",
    "vitest.config.ts",
    "tailwind.config.js"
  ],
  "exclude": [
    "scripts/**/*.ts",
    "src/test/**/*.ts",
    "src/test/**/*.tsx",
    "**/__tests__/**/*",
    "**/*.test.ts",
    "**/*.test.tsx",
    "src/main/db/migrations.backup/**/*",
    "src/main/db/performance-optimizer.ts",
    "node_modules",
    "dist",
    "out",
    "build",
    ".electron-vite",
    "release"
  ]
}
