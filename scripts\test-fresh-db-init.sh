#!/bin/bash

# Test Fresh Database Initialization Script
# This script simulates a fresh installation by backing up and removing the local database
# then starting the application to test fresh database initialization

set -e

echo "🔧 ChromaSync Fresh Database Initialization Test"
echo "================================================="

# Define paths
APP_DATA_DIR="$HOME/Library/Application Support/chroma-sync"
BACKUP_DIR="$APP_DATA_DIR/backups/fresh-init-test-$(date +%Y%m%d_%H%M%S)"
DB_FILE="$APP_DATA_DIR/chromasync.db"
DB_SHM_FILE="$APP_DATA_DIR/chromasync.db-shm"
DB_WAL_FILE="$APP_DATA_DIR/chromasync.db-wal"
CONFIG_FILE="$APP_DATA_DIR/config.json"
ORG_CONTEXT_FILE="$APP_DATA_DIR/organization-context.json"

# Check if app data directory exists
if [ ! -d "$APP_DATA_DIR" ]; then
    echo "❌ ChromaSync app data directory not found: $APP_DATA_DIR"
    echo "   Run the application at least once before testing fresh initialization."
    exit 1
fi

echo "📁 Found ChromaSync app data directory: $APP_DATA_DIR"

# Create backup directory
echo "📦 Creating backup directory: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# Function to backup and remove file if it exists
backup_and_remove() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        echo "📦 Backing up $description: $(basename "$file")"
        cp "$file" "$BACKUP_DIR/"
        echo "🗑️  Removing $description: $(basename "$file")"
        rm "$file"
    else
        echo "ℹ️  $description not found (already clean): $(basename "$file")"
    fi
}

# Backup and remove database files
echo ""
echo "🔄 Backing up and removing database files..."
backup_and_remove "$DB_FILE" "main database"
backup_and_remove "$DB_SHM_FILE" "shared memory file"  
backup_and_remove "$DB_WAL_FILE" "write-ahead log"

# Backup and remove configuration that could affect initialization
echo ""
echo "🔄 Backing up and removing configuration files..."
backup_and_remove "$CONFIG_FILE" "application config"
backup_and_remove "$ORG_CONTEXT_FILE" "organization context"

# Optional: Remove other state files for complete fresh start
echo ""
echo "🔄 Backing up and removing additional state files..."
backup_and_remove "$APP_DATA_DIR/sync-queue.json" "sync queue"
backup_and_remove "$APP_DATA_DIR/startup-metrics.json" "startup metrics"
backup_and_remove "$APP_DATA_DIR/license-config.json" "license config"
backup_and_remove "$APP_DATA_DIR/email-retry-queue.json" "email retry queue"

echo ""
echo "✅ Fresh database simulation setup complete!"
echo ""
echo "📊 Backup Summary:"
echo "   Backup location: $BACKUP_DIR"
echo "   Backed up files:"
ls -la "$BACKUP_DIR" 2>/dev/null || echo "   (No files were backed up - already fresh)"

echo ""
echo "🚀 Next Steps:"
echo "   1. Run: npm run dev"
echo "   2. Observe fresh database initialization in console logs"
echo "   3. Verify all migrations are applied correctly"
echo "   4. Test datasheet sync functionality"
echo "   5. Restore from backup if needed: scripts/restore-from-backup.sh $BACKUP_DIR"

echo ""
echo "🔍 Expected Behavior:"
echo "   ✅ Database should initialize with complete schema"
echo "   ✅ All 19 migrations should be marked as applied"
echo "   ✅ Datasheet table should include sync columns"
echo "   ✅ Application should start without errors"

echo ""
echo "⚠️  IMPORTANT: This removes your local data!"
echo "   Your data is safely backed up in: $BACKUP_DIR"
echo "   To restore: Use the restore script or manually copy files back"