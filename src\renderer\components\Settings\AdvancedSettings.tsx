/**
 * @file AdvancedSettings.tsx
 * @description Advanced settings tab for the settings modal
 */

import React, { useState } from 'react';
import {} from '../../hooks/useTokens';
import { AlertTriangle, FileText, Trash2, Building2 } from 'lucide-react';
import { useOrganizationStore } from '../../store/organization.store';
import { useModal } from '../../hooks/useModal';
import { ConfirmModal, AlertModal } from '../ui/Modal/index';

const AdvancedSettings: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [isLoadingLogs, setIsLoadingLogs] = useState(false);
  const [isClearingLogs, setIsClearingLogs] = useState(false);

  const { setCurrentOrganization } = useOrganizationStore();
  const {
    confirmState,
    alertState,
    closeConfirm,
    closeAlert,
    showConfirm,
    showSuccess,
  } = useModal();

  // Handle viewing error logs
  const handleViewLogs = async () => {
    setIsLoadingLogs(true);
    try {
      // This would use an IPC call to fetch logs from the main process
      // For now, we'll simulate loading logs
      await new Promise(resolve => setTimeout(resolve, 1000));
      setLogs([
        '[2024-07-26 10:15:32] INFO: Application started',
        '[2024-07-26 10:15:33] INFO: Database connection established',
        '[2024-07-26 10:16:45] WARN: Sync attempt failed, retrying in 30s',
        '[2024-07-26 10:17:15] INFO: Sync completed successfully',
        '[2024-07-26 11:23:01] ERROR: Failed to load color data: TypeError: Cannot read property "id" of undefined',
        '[2024-07-26 12:45:22] INFO: User imported 24 colors from JSON file',
      ]);
    } catch (error) {
      console.error('Error loading logs:', error);
    } finally {
      setIsLoadingLogs(false);
    }
  };

  // Handle clearing error logs
  const handleClearLogs = async () => {
    showConfirm(
      'Are you sure you want to clear all error logs? This cannot be undone.',
      async () => {
        setIsClearingLogs(true);
        try {
          // This would use an IPC call to clear logs in the main process
          // For now, we'll simulate clearing logs
          await new Promise(resolve => setTimeout(resolve, 1000));
          setLogs([]);
          showSuccess('Error logs cleared successfully');
        } catch (error) {
          console.error('Error clearing logs:', error);
        } finally {
          setIsClearingLogs(false);
        }
      },
      'Clear Error Logs',
      'warning',
      'Clear',
      'Cancel'
    );
  };

  return (
    <div className='space-y-8'>
      {/* Warning Banner */}
      <div className='bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-[var(--radius-lg)] p-4'>
        <div className='flex'>
          <div className='flex-shrink-0'>
            <AlertTriangle
              className='h-5 w-5 text-yellow-400'
              aria-hidden='true'
            />
          </div>
          <div className='ml-3'>
            <h3 className='text-sm font-medium text-yellow-800 dark:text-yellow-200'>
              Advanced Settings
            </h3>
            <div className='mt-2 text-sm text-yellow-700 dark:text-yellow-300'>
              <p>
                These settings are intended for troubleshooting and debugging
                purposes. Changes in this section may affect application
                stability.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Error Logs */}
      <section>
        <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
          Error Logs
        </h3>
        <div className='space-y-4'>
          <div className='flex space-x-2'>
            <button
              onClick={handleViewLogs}
              disabled={isLoadingLogs}
              className={`px-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary/80 dark:hover:bg-zinc-600 transition-colors flex items-center ${
                isLoadingLogs ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              <FileText size={16} className='mr-2' />
              {isLoadingLogs ? 'Loading...' : 'View Logs'}
            </button>

            <button
              onClick={handleClearLogs}
              disabled={isClearingLogs || logs.length === 0}
              className={`px-4 py-2 bg-feedback-error text-white rounded-[var(--radius-md)] hover:bg-red-600 transition-colors flex items-center ${
                isClearingLogs || logs.length === 0
                  ? 'opacity-70 cursor-not-allowed'
                  : ''
              }`}
            >
              <Trash2 size={16} className='mr-2' />
              {isClearingLogs ? 'Clearing...' : 'Clear Logs'}
            </button>
          </div>

          {logs.length > 0 && (
            <div className='mt-4'>
              <div className='bg-ui-background-tertiary dark:bg-zinc-800 rounded-[var(--radius-md)] p-4 max-h-[300px] overflow-y-auto font-mono text-sm'>
                {logs.map((log, index) => (
                  <div
                    key={index}
                    className={`py-1 ${
                      log.includes('ERROR')
                        ? 'text-feedback-error'
                        : log.includes('WARN')
                          ? 'text-feedback-warning'
                          : 'text-ui-foreground-primary dark:text-gray-300'
                    }`}
                  >
                    {log}
                  </div>
                ))}
              </div>
            </div>
          )}

          {logs.length === 0 && !isLoadingLogs && (
            <p className='text-ui-foreground-tertiary dark:text-gray-400 italic'>
              No logs to display. Click "View Logs" to load the application
              logs.
            </p>
          )}
        </div>
      </section>

      {/* Developer Tools */}
      <section>
        <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
          Developer Tools
        </h3>
        <div className='space-y-4'>
          <div className='flex items-center'>
            <input
              type='checkbox'
              id='devTools'
              className='h-4 w-4 text-brand-primary rounded border-ui-border-medium focus:ring-brand-primary'
              // This would be connected to an actual setting
            />
            <label
              htmlFor='devTools'
              className='ml-2 text-ui-foreground-primary dark:text-white'
            >
              Show Developer Tools in Production
            </label>
          </div>
          <p className='text-sm text-ui-foreground-tertiary dark:text-gray-400'>
            When enabled, developer tools will be available in production mode.
            This setting requires an application restart to take effect.
          </p>
        </div>
      </section>

      {/* Database Maintenance */}
      <section>
        <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
          Database Maintenance
        </h3>
        <div className='space-y-4'>
          <button
            onClick={() => {
              showConfirm(
                'Are you sure you want to optimize the database? The application will be unresponsive during this process.',
                () => {
                  // This would trigger an IPC call to optimize the database
                  showSuccess('Database optimization complete.');
                },
                'Optimize Database',
                'warning'
              );
            }}
            className='px-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary/80 dark:hover:bg-zinc-600 transition-colors'
          >
            Optimize Database
          </button>
          <p className='text-sm text-ui-foreground-tertiary dark:text-gray-400'>
            Optimizes the local SQLite database to improve performance. This
            process may take several minutes depending on the size of your
            database.
          </p>
        </div>
      </section>

      {/* Organization Management */}
      <section className='border-t border-ui-border-light dark:border-gray-700 pt-8'>
        <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
          Organization Management
        </h3>
        <div className='space-y-4'>
          <button
            onClick={() => {
              showConfirm(
                'Are you sure you want to reset your organization selection? This will require you to select an organization again on next launch.',
                () => {
                  // Clear organization selection
                  localStorage.removeItem('chromasync:lastOrganization');
                  setCurrentOrganization(null);
                  // Reload the app to force organization selection
                  window.location.reload();
                },
                'Reset Organization Selection',
                'warning'
              );
            }}
            className='px-4 py-2 bg-feedback-warning text-white rounded-[var(--radius-md)] hover:bg-orange-600 transition-colors flex items-center'
          >
            <Building2 size={16} className='mr-2' />
            Reset Organization Selection
          </button>
          <p className='text-sm text-ui-foreground-tertiary dark:text-gray-400'>
            Clears your current organization selection and forces the
            organization picker to appear on next launch. This is useful if
            you're having issues with organization access or need to switch to a
            different workspace.
          </p>
        </div>
      </section>

      {/* Modals */}
      <ConfirmModal
        isOpen={confirmState.isOpen}
        onClose={closeConfirm}
        onConfirm={confirmState.onConfirm || (() => {})}
        title={confirmState.title}
        message={confirmState.message}
        variant={confirmState.variant as 'danger' | 'warning' | 'info'}
        confirmText={confirmState.confirmText}
        cancelText={confirmState.cancelText}
      />

      <AlertModal
        isOpen={alertState.isOpen}
        onClose={closeAlert}
        title={alertState.title}
        message={alertState.message}
        variant={alertState.variant as 'success' | 'error' | 'warning' | 'info'}
      />
    </div>
  );
};

export default AdvancedSettings;
