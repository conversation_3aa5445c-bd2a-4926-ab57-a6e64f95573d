/**
 * @file product.ipc.ts
 * @description IPC handlers for product operations with enterprise-grade organization context validation
 * Refactored for 9.5+ rating standards with comprehensive error handling and validation
 */

import { ipcMain } from 'electron';
// Note: getCurrentUserId functionality moved to oauth service
import { queueImmediateSync } from '../utils/immediate-sync';
import { ProductService } from '../db/services/product.service';
import { ColorService } from '../db/services/color.service';
import {
  validateOrganizationContext,
  OrganizationContextError,
} from '../middleware/organization-context.middleware';
// Removed getSyncSystem import - now using RealtimeSyncService
import { standardizeHex } from '../../shared/utils/color';
import { unregisterHandler } from '../utils/ipcRegistry';
import { syncOutboxService } from '../services/sync/sync-outbox.service';

/**
 * Standard response interface for IPC operations
 */
interface IPCResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  userMessage?: string;
  timestamp: number;
}

/**
 * Type for handling unknown errors
 */
type ErrorWithMessage = {
  message: string;
};

/**
 * Helper function to safely extract error message
 * @param error - Any error object
 * @returns Normalized error message
 */
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  if (isErrorWithMessage(error)) {
    return error.message;
  }
  return String(error);
}

/**
 * Type guard for ErrorWithMessage
 */
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

/**
 * Create standardized error response
 */
function createErrorResponse(
  error: unknown,
  userMessage?: string
): IPCResponse {
  const errorMessage = getErrorMessage(error);

  return {
    success: false,
    error: errorMessage,
    userMessage:
      userMessage || 'An unexpected error occurred. Please try again.',
    timestamp: Date.now(),
  };
}

/**
 * Create standardized success response
 */
function createSuccessResponse<T>(
  data?: T,
  userMessage?: string
): IPCResponse<T> {
  return {
    success: true,
    data,
    userMessage,
    timestamp: Date.now(),
  };
}

/**
 * Enterprise-grade organization context validation wrapper
 * @param handler - The handler function to wrap
 * @returns Wrapped handler with organization context validation
 */
function withOrganizationContext<T extends any[], R>(
  handler: (organizationId: string, ...args: T) => Promise<R> | R
) {
  return async (
    _event: any,
    ...args: T
  ): Promise<IPCResponse<R> | OrganizationContextError> => {
    try {
      // Validate organization context
      const validation = await validateOrganizationContext();

      if (!validation.isValid) {
        console.warn(
          '[ProductIPC] Organization context validation failed:',
          validation.error
        );
        return validation.error!;
      }

      // Call handler with validated organization ID
      const result = await handler(validation.organizationId!, ...args);
      return createSuccessResponse(result);
    } catch (error) {
      console.error('[ProductIPC] Handler error:', error);
      return createErrorResponse(
        error,
        'Failed to process product operation. Please try again.'
      );
    }
  };
}

// Removed safeQueueSyncItem - now using RealtimeSyncService for immediate sync

export function registerProductHandlers(
  productService: ProductService,
  colorService: ColorService
): void {
  console.log('[ProductIPC] 🚀 STARTING PRODUCT HANDLER REGISTRATION...');
  console.log('[ProductIPC] ProductService available:', !!productService);
  console.log('[ProductIPC] ColorService available:', !!colorService);
  console.log('[ProductIPC] Registering product IPC handlers...');

  // ===== REMOVE ALL FALLBACK HANDLERS FIRST =====
  console.log('[ProductIPC] 🧹 Removing fallback handlers...');

  const productChannelsToUnregister = [
    'product:getAll',
    'product:getAllWithColors',
    'product:getById',
    'product:getWithColors',
    'product:create',
    'product:add',
    'product:update',
    'product:delete',
    'product:addColor',
    'product:removeColor',
    'product:getColors',
    'product:addLibraryColor',
    'product:syncColorsFromSupabase',
  ];

  productChannelsToUnregister.forEach(channel => {
    try {
      unregisterHandler(ipcMain, channel);
      console.log(
        `[ProductIPC] 🧹 Unregistered fallback handler for: ${channel}`
      );
    } catch (error) {
      console.log(
        `[ProductIPC] 🧹 No fallback handler to remove for: ${channel}`
      );
    }
  });

  // Get all products - Enterprise-grade validation
  ipcMain.removeHandler('product:getAll');
  ipcMain.handle(
    'product:getAll',
    withOrganizationContext(async (organizationId: string) => {
      console.log(
        '[ProductIPC] Getting all products for organization:',
        organizationId
      );
      return productService.getAll(organizationId);
    })
  );

  // Get product by ID - Enterprise-grade validation
  ipcMain.removeHandler('product:getById');
  ipcMain.handle(
    'product:getById',
    withOrganizationContext(async (organizationId: string, id: string) => {
      console.log(
        `[ProductIPC] Getting product ${id} for organization:`,
        organizationId
      );
      return productService.getById(id, organizationId);
    })
  );

  // Get product with colors - Enterprise-grade validation
  ipcMain.removeHandler('product:getWithColors');
  ipcMain.handle(
    'product:getWithColors',
    withOrganizationContext(async (organizationId: string, id: string) => {
      console.log(
        `[ProductIPC] Getting product with colors ${id} for organization:`,
        organizationId
      );
      return productService.getProductWithColors(id, organizationId);
    })
  );

  // Get all products with colors - Enterprise-grade validation
  console.log('[ProductIPC] 📝 Registering getAllWithColors handler...');
  ipcMain.handle(
    'product:getAllWithColors',
    withOrganizationContext(async (organizationId: string) => {
      console.log(
        '[ProductIPC] 🔄 getAllWithColors called for organization:',
        organizationId
      );
      const products = productService.getAllProductsWithColors(organizationId);
      console.log(
        '[ProductIPC] 📊 Found products with colors:',
        products?.length || 0
      );

      return products;
    })
  );
  console.log('[ProductIPC] ✅ getAllWithColors handler registered');

  // Create product - Enterprise-grade validation
  ipcMain.removeHandler('product:create');
  ipcMain.handle(
    'product:create',
    withOrganizationContext(
      async (organizationId: string, name: string, metadata?: any) => {
        console.log(
          `[ProductIPC] Creating product "${name}" for organization:`,
          organizationId
        );
        // Get current user ID from OAuth service
        const { getOAuthService } = await import('../services/service-locator');
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        const userId = user?.id;
        const product = await productService.create(
          { name, metadata },
          organizationId
        );

        await syncOutboxService.addToOutbox('products', 'create', {
          id: product.id,
          organizationId,
        });

        // Perform immediate realtime sync
        await queueImmediateSync('products', 'upsert', product.id, {
          data: product,
          isNew: true,
          optimistic: true,
          organizationId,
          userId,
        });

        return product;
      }
    )
  );

  // Create product (legacy handler name) - Enterprise-grade validation
  ipcMain.removeHandler('product:add');
  ipcMain.handle(
    'product:add',
    withOrganizationContext(
      async (
        organizationId: string,
        productData: { name: string; metadata?: any }
      ) => {
        console.log(
          `[ProductIPC] Adding product "${productData.name}" for organization:`,
          organizationId
        );
        // Get current user ID from OAuth service
        const { getOAuthService } = await import('../services/service-locator');
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        const userId = user?.id;
        const product = await productService.create(
          { name: productData.name, metadata: productData.metadata },
          organizationId,
          userId
        );

        await syncOutboxService.addToOutbox('products', 'create', {
          id: product.id,
          organizationId,
        });

        // Perform immediate realtime sync
        await queueImmediateSync('products', 'upsert', product.id, {
          data: product,
          isNew: true,
          optimistic: true,
          organizationId,
          userId,
        });

        return product;
      }
    )
  );

  // Update product - Enterprise-grade validation
  ipcMain.removeHandler('product:update');
  ipcMain.handle(
    'product:update',
    withOrganizationContext(
      async (organizationId: string, id: string, updates: any) => {
        console.log(
          `[ProductIPC] Updating product ${id} for organization:`,
          organizationId
        );
        // Get current user ID from OAuth service
        const { getOAuthService } = await import('../services/service-locator');
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        const userId = user?.id;
        const product = productService.update(
          id,
          updates,
          organizationId,
          userId
        );

        await syncOutboxService.addToOutbox('products', 'update', {
          id,
          organizationId,
        });

        // Perform immediate realtime sync
        await queueImmediateSync('products', 'upsert', id, {
          data: product,
          isNew: false,
          optimistic: true,
          organizationId,
          userId,
        });

        return product;
      }
    )
  );

  // Delete product - Enterprise-grade validation
  ipcMain.removeHandler('product:delete');
  ipcMain.handle(
    'product:delete',
    withOrganizationContext(async (organizationId: string, id: string) => {
      console.log(
        `[ProductIPC] 🚨 DELETE HANDLER CALLED! Product ${id} for organization:`,
        organizationId
      );
      console.log(
        `[ProductIPC] Deleting product ${id} for organization:`,
        organizationId
      );
      const success = await productService.delete(id, organizationId);
      console.log(`[ProductIPC] Delete result for product ${id}:`, success);

      if (success) {
        await syncOutboxService.addToOutbox('products', 'delete', { id });
      }

      // Perform immediate realtime sync for deletion
      if (success) {
        console.log(
          `[ProductIPC] ✅ Delete successful, performing immediate realtime sync...`
        );

        try {
          await queueImmediateSync('products', 'delete', id, {
            data: { external_id: id, deleted_at: new Date().toISOString() },
            optimistic: false, // Don't show optimistic UI updates for deletes
            organizationId,
          });

          console.log(
            `[ProductIPC] ✅ Immediate delete sync completed for product ${id}`
          );
        } catch (error) {
          console.error(
            `[ProductIPC] ❌ Failed to sync deletion immediately:`,
            error
          );
          // The RealtimeSyncService will automatically queue for retry if needed
        }
      } else {
        console.log(`[ProductIPC] ❌ Delete failed, NOT syncing to Supabase`);
      }

      console.log(`[ProductIPC] Returning delete result:`, success);
      return success;
    })
  );

  // Add color to product - Enterprise-grade validation
  ipcMain.removeHandler('product:addColor');
  ipcMain.handle(
    'product:addColor',
    withOrganizationContext(
      async (organizationId: string, productId: string, colorId: string) => {
        console.log(
          `[ProductIPC] Adding color ${colorId} to product ${productId} for organization:`,
          organizationId
        );
        const success = productService.addColor(
          productId,
          colorId,
          organizationId
        );

        if (success) {
          await syncOutboxService.addToOutbox('product_colors', 'create', {
            product_id: productId,
            color_id: colorId,
          });
        }

        // Perform immediate realtime sync for product-color association
        if (success) {
          try {
            // Get current user ID from OAuth service
            const { getOAuthService } = await import(
              '../services/service-locator'
            );
            const oauthService = getOAuthService();
            const user = await oauthService.getCurrentUser();
            const userId = user?.id;

            // Sync the product-color relationship with complete data
            await queueImmediateSync(
              'product_colors',
              'upsert',
              `${productId}-${colorId}`,
              {
                data: {
                  product_id: productId,
                  color_id: colorId,
                  organization_id: organizationId,
                  display_order: 0, // Default display order
                  external_id: `${productId}-${colorId}`, // Ensure we have an external ID for tracking
                },
                isNew: true,
                optimistic: true,
                organizationId,
                userId,
              }
            );

            console.log(
              `[ProductIPC] ✅ Immediate sync completed for product-color relationship ${productId}-${colorId}`
            );
          } catch (error) {
            console.error(
              `[ProductIPC] ❌ Failed to sync product-color association immediately:`,
              error
            );
            // The RealtimeSyncService will automatically queue for retry if needed
          }
        }

        return success;
      }
    )
  );

  // Remove color from product - Enterprise-grade validation
  ipcMain.removeHandler('product:removeColor');
  ipcMain.handle(
    'product:removeColor',
    withOrganizationContext(
      async (organizationId: string, productId: string, colorId: string) => {
        console.log(
          `[ProductIPC] Removing color ${colorId} from product ${productId} for organization:`,
          organizationId
        );
        const success = productService.removeColor(
          productId,
          colorId,
          organizationId
        );

        if (success) {
          await syncOutboxService.addToOutbox('product_colors', 'delete', {
            product_id: productId,
            color_id: colorId,
          });
        }

        // Perform immediate realtime sync for product-color association removal
        if (success) {
          try {
            // Get current user ID from OAuth service
            const { getOAuthService } = await import(
              '../services/service-locator'
            );
            const oauthService = getOAuthService();
            const user = await oauthService.getCurrentUser();
            const userId = user?.id;

            // Sync the product-color relationship deletion with complete data
            await queueImmediateSync(
              'product_colors',
              'delete',
              `${productId}-${colorId}`,
              {
                data: {
                  product_id: productId,
                  color_id: colorId,
                  organization_id: organizationId,
                  external_id: `${productId}-${colorId}`,
                  deleted_at: new Date().toISOString(),
                },
                optimistic: false, // Don't show optimistic UI updates for deletes
                organizationId,
                userId,
              }
            );

            console.log(
              `[ProductIPC] ✅ Immediate sync completed for product-color relationship removal ${productId}-${colorId}`
            );
          } catch (error) {
            console.error(
              `[ProductIPC] ❌ Failed to sync product-color removal immediately:`,
              error
            );
            // The RealtimeSyncService will automatically queue for retry if needed
          }
        }

        return success;
      }
    )
  );

  // Get colors for product - Enterprise-grade validation
  ipcMain.removeHandler('product:getColors');
  ipcMain.handle(
    'product:getColors',
    withOrganizationContext(
      async (organizationId: string, productId: string) => {
        console.log(
          `[ProductIPC] Getting colors for product ${productId} for organization:`,
          organizationId
        );
        return productService.getProductColors(productId, organizationId);
      }
    )
  );

  // Add library color to product (creates a new color entry) - Enterprise-grade validation
  ipcMain.removeHandler('product:addLibraryColor');
  ipcMain.handle(
    'product:addLibraryColor',
    withOrganizationContext(
      async (
        organizationId: string,
        productId: string,
        libraryColor: any,
        customName?: string
      ) => {
        console.log('[ProductIPC] Adding library color to product:', {
          productId,
          libraryColor,
          customName,
          organizationId,
        });

        // Get current user ID from OAuth service
        const { getOAuthService } = await import('../services/service-locator');
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        const userId = user?.id;

        // Create a new color entry based on the library color
        const newColor = {
          product: '', // Will be set by association
          name: customName || libraryColor.name || libraryColor.code,
          code: libraryColor.code,
          hex: standardizeHex(libraryColor.hex), // Ensure hex is 6-digit format
          cmyk: libraryColor.cmyk,
          notes: `Added from ${libraryColor.isLibrary ? 'library' : 'saved colors'}`,
          gradient: libraryColor.gradient,
          isLibrary: false, // This will be a user color
        };

        // Create the color
        const createdColorId = await colorService.add(
          newColor,
          userId,
          organizationId
        );

        if (createdColorId) {
          // Associate with product
          const success = productService.addColorToProduct(
            productId,
            createdColorId,
            organizationId
          );
          if (success) {
            // Get the full color object to return
            const createdColor = colorService.getById(
              createdColorId,
              organizationId
            );
            return createdColor;
          } else {
            // If association failed, delete the color
            colorService.delete(createdColorId, organizationId);
            throw new Error('Failed to associate color with product');
          }
        }

        throw new Error('Failed to create color');
      }
    )
  );

  // Sync product-color relationships from Supabase - For debugging sync issues
  ipcMain.removeHandler('product:syncColorsFromSupabase');
  ipcMain.handle(
    'product:syncColorsFromSupabase',
    withOrganizationContext(
      async (organizationId: string): Promise<IPCResponse> => {
        try {
          console.log(
            `[ProductIPC] Syncing product-color relationships from Supabase for organization: ${organizationId}`
          );

          const result =
            await productService.syncProductColorsFromSupabase(organizationId);

          return {
            success: result.success,
            data: {
              syncedCount: result.syncedCount,
              errors: result.errors,
            },
            userMessage: result.success
              ? `Successfully synced ${result.syncedCount} product-color relationships`
              : `Failed to sync product-color relationships: ${result.errors.join(', ')}`,
            timestamp: Date.now(),
          };
        } catch (error) {
          const errorMessage = getErrorMessage(error);
          console.error(
            '[ProductIPC] Error syncing product-color relationships:',
            errorMessage
          );

          return {
            success: false,
            error: errorMessage,
            userMessage:
              'Failed to sync product-color relationships from cloud',
            timestamp: Date.now(),
          };
        }
      }
    )
  );

  // Deduplicate products - Manual deduplication trigger
  ipcMain.removeHandler('product:deduplicate');
  ipcMain.handle(
    'product:deduplicate',
    withOrganizationContext(async (organizationId: string) => {
      try {
        console.log(
          `[ProductIPC] 🔧 Manual deduplication triggered for organization:`,
          organizationId
        );
        const result = productService.deduplicateProducts(organizationId);

        return {
          success: result.success,
          data: {
            deduplicatedCount: result.deduplicatedCount,
            errors: result.errors,
          },
          userMessage: result.success
            ? `Successfully removed ${result.deduplicatedCount} duplicate products`
            : 'Failed to deduplicate products',
          timestamp: Date.now(),
        };
      } catch (error) {
        console.error(
          `[ProductIPC] ❌ Error during manual deduplication:`,
          error
        );
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';

        return {
          success: false,
          error: errorMessage,
          userMessage: 'Failed to deduplicate products',
          timestamp: Date.now(),
        };
      }
    })
  );

  console.log('[ProductIPC] ✅ Product IPC handlers registered successfully.');
  console.log('[ProductIPC] 🎯 Final handler count verification...');

  // Verify critical handlers are registered
  const criticalHandlers = [
    'product:getAllWithColors',
    'product:delete',
    'product:getAll',
  ];
  criticalHandlers.forEach(channel => {
    const handlers = (ipcMain as any)._events;
    const handlerExists = handlers && handlers[channel];
    console.log(
      `[ProductIPC] 🔍 Handler '${channel}' registered:`,
      !!handlerExists
    );
  });
}
