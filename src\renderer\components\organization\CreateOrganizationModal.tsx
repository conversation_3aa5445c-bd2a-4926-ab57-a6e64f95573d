/**
 * @file CreateOrganizationModal.tsx
 * @description Modal for creating a new organization from within the app
 */

import React, { useState } from 'react';
import { useOrganizationStore } from '../../store/organization.store';
import { Building2, X, Loader2 } from 'lucide-react';

interface CreateOrganizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (organization: any) => void;
}

export const CreateOrganizationModal: React.FC<CreateOrganizationModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { createOrganization, setCurrentOrganization } = useOrganizationStore();
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      setError('Organization name is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await createOrganization(name.trim());
      
      if (result.success && result.data) {
        // Set as current organization
        setCurrentOrganization(result.data);
        
        // Call success callback
        if (onSuccess) {
          onSuccess(result.data);
        }
        
        // Close modal
        onClose();
        
        // Reset form
        setName('');
      } else {
        setError(result.error || 'Failed to create organization');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create organization');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setName('');
      setError(null);
      onClose();
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div 
      className="fixed inset-0 flex items-center justify-center"
      style={{ 
        backgroundColor: 'var(--backdrop-primary)',
        zIndex: 'var(--z-modal)'
      }}
    >
      <div 
        className="w-full max-w-md mx-4"
        style={{
          backgroundColor: 'var(--color-ui-background-primary)',
          borderRadius: 'var(--radius-lg)',
          boxShadow: 'var(--shadow-xl)',
          border: `1px solid var(--color-ui-border-light)`
        }}
      >
        {/* Header */}
        <div 
          className="flex items-center justify-between p-6"
          style={{ borderBottom: `1px solid var(--color-ui-border-light)` }}
        >
          <h2 
            className="text-xl font-semibold flex items-center"
            style={{ 
              color: 'var(--color-ui-foreground-primary)',
              fontWeight: 'var(--font-weight-semibold)'
            }}
          >
            <Building2 className="w-5 h-5 mr-2" />
            Create New Workspace
          </h2>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="transition-standard"
            style={{
              color: 'var(--color-ui-foreground-tertiary)',
              backgroundColor: 'transparent',
              border: 'none',
              padding: '0.25rem',
              borderRadius: 'var(--radius-sm)',
              cursor: isLoading ? 'not-allowed' : 'pointer'
            }}
            onMouseEnter={(e) => {
              if (!isLoading) {
                e.currentTarget.style.color = 'var(--color-ui-foreground-secondary)';
                e.currentTarget.style.backgroundColor = 'var(--color-ui-background-hover)';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = 'var(--color-ui-foreground-tertiary)';
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          {error && (
            <div 
              className="mb-4 p-3 text-sm"
              style={{
                backgroundColor: 'var(--feedback-bg-error)',
                border: `1px solid var(--feedback-border-error)`,
                color: 'var(--color-feedback-error)',
                borderRadius: 'var(--radius-DEFAULT)'
              }}
            >
              {error}
            </div>
          )}

          <div className="mb-4">
            <label 
              htmlFor="org-name" 
              className="block text-sm font-medium mb-2"
              style={{ 
                color: 'var(--color-ui-foreground-primary)',
                fontWeight: 'var(--font-weight-medium)'
              }}
            >
              Workspace Name
            </label>
            <input
              id="org-name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Acme Corp"
              className="w-full px-3 py-2 transition-standard"
              style={{
                backgroundColor: 'var(--form-background)',
                border: `1px solid var(--form-border)`,
                borderRadius: 'var(--radius-DEFAULT)',
                color: 'var(--color-ui-foreground-primary)',
                fontSize: 'var(--font-size-base)'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = 'var(--form-border-focus)';
                e.target.style.outline = `2px solid var(--form-border-focus)`;
                e.target.style.outlineOffset = '2px';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = 'var(--form-border)';
                e.target.style.outline = 'none';
              }}
              disabled={isLoading}
              autoFocus
              required
            />
            <p 
              className="mt-2 text-xs"
              style={{ color: 'var(--color-ui-foreground-tertiary)' }}
            >
              Choose a name for your team's workspace
            </p>
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="px-4 py-2 transition-standard"
              style={{
                backgroundColor: 'var(--button-secondary-bg)',
                border: `1px solid var(--button-secondary-border)`,
                color: 'var(--button-secondary-text)',
                borderRadius: 'var(--radius-DEFAULT)',
                fontSize: 'var(--font-size-sm)',
                fontWeight: 'var(--font-weight-medium)',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                opacity: isLoading ? '0.5' : '1'
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--button-secondary-bg-hover)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.currentTarget.style.backgroundColor = 'var(--button-secondary-bg)';
                }
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !name.trim()}
              className="px-4 py-2 font-medium transition-standard flex items-center"
              style={{
                backgroundColor: isLoading || !name.trim() 
                  ? 'var(--button-primary-bg-disabled)' 
                  : 'var(--button-primary-bg)',
                color: isLoading || !name.trim()
                  ? 'var(--button-primary-text-disabled)'
                  : 'var(--button-primary-text)',
                border: 'none',
                borderRadius: 'var(--radius-DEFAULT)',
                fontSize: 'var(--font-size-sm)',
                fontWeight: 'var(--font-weight-medium)',
                cursor: isLoading || !name.trim() ? 'not-allowed' : 'pointer'
              }}
              onMouseEnter={(e) => {
                if (!isLoading && name.trim()) {
                  e.currentTarget.style.backgroundColor = 'var(--button-primary-bg-hover)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading && name.trim()) {
                  e.currentTarget.style.backgroundColor = 'var(--button-primary-bg)';
                }
              }}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Workspace'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
