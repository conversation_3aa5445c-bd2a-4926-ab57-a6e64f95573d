#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to trigger a sync from Supabase to populate local database
 */

const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

// Use the correct Electron userData path
const dbPath = path.join(os.homedir(), 'Library', 'Application Support', 'Electron', 'chromasync.db');

console.log('ChromaSync Supabase Sync Trigger');
console.log('================================');
console.log(`Local database: ${dbPath}`);

try {
  const db = new Database(dbPath, { readonly: true });
  
  console.log('\n1. Current local database state:');
  
  // Check current state
  const orgs = db.prepare('SELECT * FROM organizations').all();
  console.log(`Organizations: ${orgs.length}`);
  
  for (const org of orgs) {
    const colorCount = db.prepare('SELECT COUNT(*) as count FROM colors WHERE organization_id = ?').get(org.id);
    const productCount = db.prepare('SELECT COUNT(*) as count FROM products WHERE organization_id = ?').get(org.id);
    
    console.log(`  - ${org.name} (${org.external_id})`);
    console.log(`    Colors: ${colorCount.count}, Products: ${productCount.count}`);
  }
  
  // Check user
  const user = db.prepare("SELECT * FROM users WHERE email = '<EMAIL>'").get();
  console.log(`\nUser <EMAIL>: ${user ? 'EXISTS' : 'MISSING'}`);
  if (user) {
    console.log(`  ID: ${user.external_id}`);
  }
  
  db.close();
  
  console.log('\n2. Expected behavior:');
  console.log('- Local database has organizations but no colors/products');
  console.log('- Supabase should have the actual data');
  console.log('- App needs to sync from Supabase to populate local data');
  
  console.log('\n3. What should happen when user signs in:');
  console.log('1. User authenticates with Google OAuth');
  console.log('2. App calls syncOrganizationsFromSupabase()');
  console.log('3. App finds user organizations (IVG + Default)');
  console.log('4. App should show organization selection screen');
  console.log('5. User selects IVG');
  console.log('6. App triggers sync to pull colors/products from Supabase');
  
  console.log('\n4. Troubleshooting steps:');
  console.log('a) Check if user is actually authenticated');
  console.log('b) Check if sync is being triggered properly');
  console.log('c) Check Supabase connection and data');
  console.log('d) Look for sync errors in console logs');
  
  console.log('\n5. Manual sync test:');
  console.log('You can test the sync by:');
  console.log('- Opening ChromaSync with dev tools');
  console.log('- Signing <NAME_EMAIL>');
  console.log('- Checking console for sync logs');
  console.log('- Looking for calls to syncData() or sync API');
  
  console.log('\n6. If sync is not working:');
  console.log('- Check Supabase credentials in .env');
  console.log('- Verify RLS policies allow user access');
  console.log('- Check network connectivity');
  console.log('- Look for authentication token issues');
  
} catch (error) {
  console.error('Error:', error.message);
}