/**
 * @file color-service-gradient-integration.test.ts
 * @description Integration tests for ColorService and GradientProcessor
 *
 * Tests that ColorService properly delegates gradient operations to
 * the GradientProcessor service after the refactoring.
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { ColorService } from '../../../db/services/color.service';
import { GradientProcessor } from '../gradient-processor.service';
import { ColorSpaceCalculator } from '../color-space-calculator.service';

// Mock database and repository for testing
const mockDb = {
  prepare: () => ({
    get: () => ({ id: 1 }),
    run: () => ({ changes: 1, lastInsertRowid: 1 }),
    all: () => [],
  }),
  exec: () => {},
  close: () => {},
} as any;

const mockColorRepository = {
  findAll: () => [],
  findById: () => null,
  insert: () => 'test-id',
  update: () => true,
  softDelete: () => true,
  markAsSynced: () => true,
  invalidateOrphans: () => true,
  clearAll: () => true,
  findSoftDeleted: () => [],
  restoreRecord: () => true,
  bulkRestoreRecords: () => ({ success: true, restored: 0 }),
  cleanupOldSoftDeleted: () => ({ success: true, cleaned: 0 }),
  getUsageCounts: () => new Map(),
  getColorNameProductMap: () => new Map(),
  findUnsynced: () => [],
} as any;

describe('ColorService + GradientProcessor Integration', () => {
  let colorService: ColorService;
  let gradientProcessor: GradientProcessor;
  let colorSpaceCalculator: ColorSpaceCalculator;

  beforeEach(() => {
    gradientProcessor = new GradientProcessor();
    colorSpaceCalculator = new ColorSpaceCalculator();
    colorService = new ColorService(
      mockDb,
      mockColorRepository,
      colorSpaceCalculator,
      gradientProcessor
    );
  });

  describe('Gradient Processing Delegation', () => {
    test('should use GradientProcessor for gradient colors CSV generation', () => {
      const gradientStops = [
        { color: '#FF0000', position: 0.0 },
        { color: '#00FF00', position: 0.5, colorCode: 'GRN-01' },
        { color: '#0000FF', position: 1.0 },
      ];

      // Test that the same method exists and produces expected output
      const result = gradientProcessor.createGradientColorsCSV(gradientStops);
      expect(result).toBe('#FF0000,#00FF00|GRN-01,#0000FF');
    });

    test('should use GradientProcessor for gradient CSS generation', () => {
      const gradientStops = [
        { color: '#FF0000', position: 0.0 },
        { color: '#0000FF', position: 1.0 },
      ];

      const result = gradientProcessor.generateLinearGradientCSS(
        gradientStops,
        90
      );
      expect(result).toBe('linear-gradient(90deg, #FF0000 0%, #0000FF 100%)');
    });

    test('should use GradientProcessor for duplicate position resolution', () => {
      const duplicateStops = [
        { color: '#FF0000', position: 0.0 },
        { color: '#00FF00', position: 0.5 },
        { color: '#0000FF', position: 0.5 },
        { color: '#FFFF00', position: 0.5 },
      ];

      const result =
        gradientProcessor.resolveDuplicatePositions(duplicateStops);

      // Should have unique positions
      const positions = result.map(s => s.position);
      const uniquePositions = [...new Set(positions)];
      expect(positions).toHaveLength(uniquePositions.length);

      // First occurrence should keep original position
      expect(result[1].position).toBe(0.5);

      // Subsequent duplicates should have offset positions
      expect(result[2].position).toBeCloseTo(0.51, 1);
      expect(result[3].position).toBeCloseTo(0.52, 1);
    });

    test('should use GradientProcessor for gradient sorting', () => {
      const unsortedStops = [
        { color: '#0000FF', position: 1.0 },
        { color: '#FF0000', position: 0.0 },
        { color: '#00FF00', position: 0.5 },
      ];

      const result = gradientProcessor.sortStopsByPosition(unsortedStops);
      expect(result.map(s => s.position)).toEqual([0.0, 0.5, 1.0]);
      expect(result.map(s => s.color)).toEqual([
        '#FF0000',
        '#00FF00',
        '#0000FF',
      ]);
    });

    test('should use GradientProcessor for gradient validation', () => {
      const validStops = [
        { color: '#FF0000', position: 0.0 },
        { color: '#00FF00', position: 0.5 },
        { color: '#0000FF', position: 1.0 },
      ];

      const result = gradientProcessor.validateGradientStops(validStops);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should handle gradient validation errors through GradientProcessor', () => {
      const invalidStops = [
        { color: 'invalid-color', position: 0.0 },
        { color: '#FF0000', position: -0.1 },
      ];

      const result = gradientProcessor.validateGradientStops(invalidStops);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(
        'Invalid hex color format: invalid-color'
      );
      expect(result.errors).toContain('Position must be between 0 and 1: -0.1');
    });
  });

  describe('Service Integration', () => {
    test('should have GradientProcessor injected into ColorService', () => {
      expect((colorService as any).gradientProcessor).toBeInstanceOf(
        GradientProcessor
      );
    });

    test('should use default GradientProcessor when none provided', () => {
      const serviceWithDefaults = new ColorService(mockDb, mockColorRepository);
      expect((serviceWithDefaults as any).gradientProcessor).toBeInstanceOf(
        GradientProcessor
      );
    });

    test('should use provided GradientProcessor instance', () => {
      const customProcessor = new GradientProcessor();
      const serviceWithCustom = new ColorService(
        mockDb,
        mockColorRepository,
        colorSpaceCalculator,
        customProcessor
      );
      expect((serviceWithCustom as any).gradientProcessor).toBe(
        customProcessor
      );
    });
  });

  describe('Gradient Processing Features', () => {
    test('should handle position conversion correctly', () => {
      expect(gradientProcessor.convertPositionToPercentage(0.5)).toBe(50);
      expect(gradientProcessor.convertPercentageToPosition(75)).toBe(0.75);
    });

    test('should analyze gradient complexity', () => {
      const simpleGradient = [
        { color: '#FF0000', position: 0.0 },
        { color: '#0000FF', position: 1.0 },
      ];

      const complexGradient = [
        { color: '#FF0000', position: 0.0 },
        { color: '#00FF00', position: 0.2 },
        { color: '#0000FF', position: 0.4 },
        { color: '#FFFF00', position: 0.6 },
        { color: '#FF00FF', position: 0.8 },
        { color: '#00FFFF', position: 1.0 },
      ];

      expect(gradientProcessor.calculateComplexity(simpleGradient)).toBe(
        'simple'
      );
      expect(gradientProcessor.calculateComplexity(complexGradient)).toBe(
        'complex'
      );
    });

    test('should normalize positions to valid range', () => {
      const stopsWithInvalidPositions = [
        { color: '#FF0000', position: -0.1 },
        { color: '#00FF00', position: 0.5 },
        { color: '#0000FF', position: 1.2 },
      ];

      const normalized = gradientProcessor.normalizePositions(
        stopsWithInvalidPositions
      );
      expect(normalized[0].position).toBe(0.0);
      expect(normalized[1].position).toBe(0.5);
      expect(normalized[2].position).toBe(1.0);
    });
  });
});
