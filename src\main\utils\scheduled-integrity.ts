/**
 * @file scheduled-integrity.ts
 * @description Scheduled integrity checks and automated cleanup
 */

import Database from 'better-sqlite3';
import {
  createRelationshipIntegrityManager,
  IntegrityReport,
} from './relationship-integrity';
import { createSoftDeleteManager } from './soft-delete-patterns';

export interface ScheduledIntegrityConfig {
  enabled: boolean;
  intervalHours: number;
  autoCleanup: boolean;
  maxOrphanAge: number; // days
  notifyOnIssues: boolean;
}

export interface IntegrityScheduleResult {
  timestamp: string;
  organizationsChecked: number;
  totalIssuesFound: number;
  totalIssuesCleaned: number;
  reports: IntegrityReport[];
  errors: string[];
}

/**
 * Scheduled integrity checker for automated maintenance
 */
export class ScheduledIntegrityChecker {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor(
    private db: Database.Database,
    private config: ScheduledIntegrityConfig
  ) {}

  /**
   * Start scheduled integrity checks
   */
  start(): void {
    if (this.intervalId || !this.config.enabled) {
      console.log('[ScheduledIntegrity] Already running or disabled');
      return;
    }

    const intervalMs = this.config.intervalHours * 60 * 60 * 1000;

    console.log(
      `[ScheduledIntegrity] Starting scheduled checks every ${this.config.intervalHours} hours`
    );

    // Run initial check after 5 minutes
    setTimeout(() => this.runScheduledCheck(), 5 * 60 * 1000);

    // Set up recurring checks
    this.intervalId = setInterval(() => {
      this.runScheduledCheck();
    }, intervalMs);
  }

  /**
   * Stop scheduled integrity checks
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('[ScheduledIntegrity] Stopped scheduled checks');
    }
  }

  /**
   * Run integrity checks for all organizations
   */
  async runScheduledCheck(): Promise<IntegrityScheduleResult> {
    if (this.isRunning) {
      console.log('[ScheduledIntegrity] Check already in progress, skipping');
      return {
        timestamp: new Date().toISOString(),
        organizationsChecked: 0,
        totalIssuesFound: 0,
        totalIssuesCleaned: 0,
        reports: [],
        errors: ['Check already in progress'],
      };
    }

    this.isRunning = true;

    try {
      console.log('[ScheduledIntegrity] Starting scheduled integrity check');

      const integrityManager = createRelationshipIntegrityManager(this.db);
      const softDeleteManager = createSoftDeleteManager(this.db);

      // Get all active organizations
      const organizations = this.db
        .prepare(
          `
        SELECT external_id FROM organizations 
        WHERE created_at IS NOT NULL
        ORDER BY created_at ASC
      `
        )
        .all() as { external_id: string }[];

      const reports: IntegrityReport[] = [];
      const errors: string[] = [];
      let totalIssuesFound = 0;
      let totalIssuesCleaned = 0;

      for (const org of organizations) {
        try {
          // Run integrity checks for this organization
          const report = integrityManager.runIntegrityChecks(org.external_id);
          reports.push(report);

          totalIssuesFound += report.totalIssues;
          totalIssuesCleaned += report.totalCleaned;

          // If auto-cleanup is enabled, perform additional cleanup
          if (this.config.autoCleanup) {
            // Clean up old soft-deleted records
            const colorCleanup = softDeleteManager.cleanupOldSoftDeleted(
              'colors',
              this.config.maxOrphanAge,
              org.external_id
            );
            const datasheetCleanup = this.cleanupOldDatasheets(org.external_id);

            if (colorCleanup.success) {
              totalIssuesCleaned += colorCleanup.affected;
            }

            if (datasheetCleanup.success) {
              totalIssuesCleaned += datasheetCleanup.cleaned;
            }
          }

          // If significant issues found and notifications enabled, log for external notification
          if (this.config.notifyOnIssues && report.totalIssues > 10) {
            console.warn(
              `[ScheduledIntegrity] Organization ${org.external_id} has ${report.totalIssues} integrity issues`
            );
            // Here you could implement external notification (email, webhook, etc.)
          }
        } catch (orgError) {
          const errorMsg = `Failed to check organization ${org.external_id}: ${orgError instanceof Error ? orgError.message : String(orgError)}`;
          errors.push(errorMsg);
          console.error('[ScheduledIntegrity]', errorMsg);
        }
      }

      const result: IntegrityScheduleResult = {
        timestamp: new Date().toISOString(),
        organizationsChecked: organizations.length,
        totalIssuesFound,
        totalIssuesCleaned,
        reports,
        errors,
      };

      console.log(
        `[ScheduledIntegrity] Completed check: ${organizations.length} orgs, ${totalIssuesFound} issues found, ${totalIssuesCleaned} cleaned`
      );

      return result;
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      console.error(
        '[ScheduledIntegrity] Error during scheduled check:',
        error
      );

      return {
        timestamp: new Date().toISOString(),
        organizationsChecked: 0,
        totalIssuesFound: 0,
        totalIssuesCleaned: 0,
        reports: [],
        errors: [errorMsg],
      };
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Clean up old datasheets for an organization
   */
  private cleanupOldDatasheets(organizationId: string): {
    success: boolean;
    cleaned: number;
  } {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.maxOrphanAge);
      const cutoffTimestamp = cutoffDate.toISOString();

      const result = this.db
        .prepare(
          `
        DELETE FROM datasheets 
        WHERE is_active = 0 
        AND updated_at < ?
        AND product_id IN (
          SELECT id FROM products WHERE organization_id = ?
        )
      `
        )
        .run(cutoffTimestamp, organizationId);

      return {
        success: true,
        cleaned: result.changes,
      };
    } catch (error) {
      console.error(
        `[ScheduledIntegrity] Error cleaning old datasheets for org ${organizationId}:`,
        error
      );
      return { success: false, cleaned: 0 };
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ScheduledIntegrityConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // Restart if interval changed and we're currently running
    if (this.intervalId && newConfig.intervalHours) {
      this.stop();
      if (this.config.enabled) {
        this.start();
      }
    }

    // Start/stop based on enabled status
    if (newConfig.enabled !== undefined) {
      if (newConfig.enabled && !this.intervalId) {
        this.start();
      } else if (!newConfig.enabled && this.intervalId) {
        this.stop();
      }
    }
  }

  /**
   * Get current status
   */
  getStatus(): {
    enabled: boolean;
    running: boolean;
    config: ScheduledIntegrityConfig;
    nextCheckEstimate?: string;
  } {
    let nextCheckEstimate: string | undefined;

    if (this.intervalId && this.config.enabled) {
      const nextCheck = new Date();
      nextCheck.setMilliseconds(
        nextCheck.getMilliseconds() + this.config.intervalHours * 60 * 60 * 1000
      );
      nextCheckEstimate = nextCheck.toISOString();
    }

    return {
      enabled: this.config.enabled,
      running: this.isRunning,
      config: this.config,
      nextCheckEstimate,
    };
  }
}

/**
 * Default configuration for scheduled integrity checks
 */
export const DEFAULT_INTEGRITY_CONFIG: ScheduledIntegrityConfig = {
  enabled: true,
  intervalHours: 24, // Daily checks
  autoCleanup: true,
  maxOrphanAge: 30, // 30 days
  notifyOnIssues: true,
};

/**
 * Factory function to create a scheduled integrity checker
 */
export function createScheduledIntegrityChecker(
  db: Database.Database,
  config: ScheduledIntegrityConfig = DEFAULT_INTEGRITY_CONFIG
): ScheduledIntegrityChecker {
  return new ScheduledIntegrityChecker(db, config);
}
