/**
 * @file unified-sync-manager.ts
 * @description Single, unified sync system to replace multiple competing sync services
 *
 * This consolidates SyncEngine, RealtimeSyncService, AutoSyncManager, and direct
 * service syncing into one coherent system that prevents race conditions.
 */

import { EventEmitter } from 'events';
import { mainWindow } from '../../index';
import { ensureAuthenticatedSession } from '../supabase-client';
import { fileConcurrencyController, Lock } from './file-concurrency-controller';
import { resourceManager } from './resource-manager';
import { handleSyncError } from './sync-error-recovery';
import { syncOutboxService } from './sync-outbox.service';
import { transactionAwareSyncManager } from './transaction-aware-sync';

export interface UnifiedSyncConfig {
  autoSyncEnabled: boolean;
  autoSyncInterval: number; // minutes
  realtimeEnabled: boolean;
  maxRetries: number;
}

export interface SyncOperation {
  id: string;
  type: 'colors' | 'products' | 'organizations' | 'full';
  direction: 'push' | 'pull' | 'bidirectional';
  priority: 'low' | 'normal' | 'high';
  timestamp: number;
}

export interface SyncResult {
  success: boolean;
  operation: SyncOperation;
  itemsProcessed: number;
  duration: number;
  errors?: string[];
}

/**
 * Unified Sync Manager - Single source of truth for all sync operations
 */
export class UnifiedSyncManager extends EventEmitter {
  private static instance: UnifiedSyncManager | null = null;

  private userId: string | null = null;
  private organizationId: string | null = null;
  private isInitialized = false;
  private isSyncing = false;
  private lastSyncTime = 0;

  // Configuration
  private config: UnifiedSyncConfig = {
    autoSyncEnabled: true,
    autoSyncInterval: 15, // 15 minutes (reduced from 5 to prevent interference with user operations)
    realtimeEnabled: true,
    maxRetries: 3,
  };

  // Operation queue
  private operationQueue: SyncOperation[] = [];
  private currentOperation: SyncOperation | null = null;

  // Auto-sync timer
  private autoSyncTimer: NodeJS.Timeout | null = null;

  // Singleton pattern
  static getInstance(): UnifiedSyncManager {
    if (!UnifiedSyncManager.instance) {
      UnifiedSyncManager.instance = new UnifiedSyncManager();
    }
    return UnifiedSyncManager.instance;
  }

  private constructor() {
    super();
    console.log('[UnifiedSync] 🚀 Unified Sync Manager created');
  }

  /**
   * Initialize the unified sync manager
   */
  async initialize(
    userId: string,
    organizationId: string,
    config?: Partial<UnifiedSyncConfig>
  ): Promise<void> {
    if (this.isInitialized) {
      console.log('[UnifiedSync] Already initialized, updating context');
      this.userId = userId;
      this.organizationId = organizationId;
      return;
    }

    this.userId = userId;
    this.organizationId = organizationId;

    if (config) {
      this.config = { ...this.config, ...config };
    }

    console.log('[UnifiedSync] 🔧 Initializing with config:', this.config);

    // Start auto-sync if enabled
    if (this.config.autoSyncEnabled) {
      this.startAutoSync();
    }

    this.isInitialized = true;
    this.emit('initialized');

    console.log('[UnifiedSync] ✅ Initialized successfully');
  }

  /**
   * Cleanup sync manager and release all resources
   */
  async cleanup(): Promise<void> {
    console.log('[UnifiedSync] 🧹 Starting cleanup...');
    
    // Stop auto-sync and untrack timer
    this.stopAutoSync();
    
    // Wait for current sync to complete
    if (this.isSyncing && this.currentOperation) {
      console.log('[UnifiedSync] ⏳ Waiting for current sync operation to complete...');
      await new Promise<void>((resolve) => {
        const checkComplete = () => {
          if (!this.isSyncing) {
            resolve();
          } else {
            setTimeout(checkComplete, 100);
          }
        };
        checkComplete();
      });
    }
    
    // Clear operation queue
    this.operationQueue = [];
    
    // Remove all event listeners from this EventEmitter
    this.removeAllListeners();
    
    // Cleanup concurrency controller
    await fileConcurrencyController.cleanup();
    
    // Reset state
    this.isInitialized = false;
    this.userId = null;
    this.organizationId = null;
    
    console.log('[UnifiedSync] ✅ Cleanup completed');
  }
  
  /**
   * Start a sync operation
   */
  async sync(
    type: SyncOperation['type'] = 'full',
    direction: SyncOperation['direction'] = 'bidirectional',
    priority: SyncOperation['priority'] = 'normal'
  ): Promise<SyncResult> {
    // Enhanced service readiness validation
    const verboseLogging = process.env.VERBOSE_SYNC_LOGGING === 'true';

    console.log('[UnifiedSync] 🚀 sync() method called with:', {
      type,
      direction,
      priority,
    });
    console.log('[UnifiedSync] 🔍 Manager state:', {
      isInitialized: this.isInitialized,
      userId: this.userId,
      organizationId: this.organizationId,
      isSyncing: this.isSyncing,
    });

    // Check for active user operations and defer sync if needed
    try {
      const { userOperationCoordinator } = await import(
        '../user-operation-coordinator.service'
      );

      if (userOperationCoordinator.hasActiveOperations()) {
        const activeOps = userOperationCoordinator.getActiveOperations();
        console.log(
          '[UnifiedSync] ⏸️ User operations in progress, deferring sync:',
          {
            activeOperations: activeOps.length,
            operations: activeOps.map(op => ({
              type: op.type,
              organizationId: op.organizationId,
            })),
            syncType: type,
            priority,
          }
        );

        // For high priority sync, wait a short time for user operations to complete
        if (priority === 'high') {
          console.log(
            '[UnifiedSync] ⏳ High priority sync - waiting up to 5s for user operations...'
          );
          const completed =
            await userOperationCoordinator.waitForAllOperationsToComplete(5000);
          if (!completed) {
            console.log(
              '[UnifiedSync] ⏰ Timeout waiting for user operations, proceeding with sync'
            );
          }
        } else {
          // For normal/low priority, skip this sync cycle
          console.log(
            '[UnifiedSync] 🔄 Skipping sync cycle due to active user operations'
          );
          return {
            success: false,
            operation: {
              id: '',
              type,
              direction,
              priority,
              timestamp: Date.now(),
            },
            itemsProcessed: 0,
            duration: 0,
            errors: ['Sync deferred due to active user operations'],
          };
        }
      }
    } catch (coordError) {
      console.warn(
        '[UnifiedSync] ⚠️ Could not check user operation status:',
        coordError
      );
      // Continue with sync if coordination is unavailable
    }

    if (verboseLogging) {
      console.log(
        '[UnifiedSync] 🔍 Starting comprehensive sync readiness validation...'
      );
    }

    if (!this.isInitialized) {
      throw new Error('Sync manager not initialized. Call initialize() first.');
    }

    if (!this.userId) {
      throw new Error(
        'User authentication required. Please authenticate before syncing.'
      );
    }

    if (!this.organizationId) {
      throw new Error(
        'Organization context required. Please select an organization before syncing.'
      );
    }

    // Service readiness validation
    console.log('[UnifiedSync] 🔧 About to import ServiceLocator...');
    try {
      const { ServiceLocator } = await import('../service-locator');
      console.log('[UnifiedSync] ✅ ServiceLocator imported successfully');

      if (verboseLogging) {
        console.log('[UnifiedSync] 🔍 Validating service dependencies...');
      }

      // Check if core services are available
      const servicesStatus = {
        colorService: false,
        productService: false,
        organizationService: false,
        database: false,
      };

      try {
        const colorService = ServiceLocator.getColorService();
        servicesStatus.colorService = !!colorService;
      } catch (error) {
        console.warn(
          '[UnifiedSync] ⚠️ ColorService not available:',
          error instanceof Error ? error.message : String(error)
        );
      }

      try {
        const productService = ServiceLocator.getProductService();
        servicesStatus.productService = !!productService;
      } catch (error) {
        console.warn(
          '[UnifiedSync] ⚠️ ProductService not available:',
          error instanceof Error ? error.message : String(error)
        );
      }

      try {
        const orgService = ServiceLocator.getOrganizationService();
        servicesStatus.organizationService = !!orgService;
      } catch (error) {
        console.warn(
          '[UnifiedSync] ⚠️ OrganizationService not available:',
          error instanceof Error ? error.message : String(error)
        );
      }

      try {
        const database = ServiceLocator.getDatabase();
        servicesStatus.database = !!database;
      } catch (error) {
        console.warn(
          '[UnifiedSync] ⚠️ Database not available:',
          error instanceof Error ? error.message : String(error)
        );
      }

      if (verboseLogging) {
        console.log(
          '[UnifiedSync] 📊 Service availability status:',
          servicesStatus
        );
      }

      // Check if minimum required services are available
      const requiredServices = ['database', 'organizationService'];
      const missingServices = requiredServices.filter(
        service => !servicesStatus[service as keyof typeof servicesStatus]
      );

      if (missingServices.length > 0) {
        const errorMessage = `Critical services not available: ${missingServices.join(', ')}`;
        console.error(`[UnifiedSync] ❌ ${errorMessage}`);
        throw new Error(`SYNC_SERVICE_ERROR: ${errorMessage}`);
      }
    } catch (error) {
      const errorMessage = `Service validation failed: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`[UnifiedSync] ❌ ${errorMessage}`);

      if (process.env.SYNC_ERROR_ELEVATION === 'true') {
        throw new Error(`SYNC_READINESS_ERROR: ${errorMessage}`);
      } else {
        console.warn(
          `[UnifiedSync] ⚠️ Continuing despite service validation warning`
        );
      }
    }

    // Prevent concurrent syncing
    if (this.isSyncing) {
      console.log(
        '[UnifiedSync] ⏳ Sync already in progress, queuing operation'
      );

      const operation: SyncOperation = {
        id: `sync_${Date.now()}`,
        type,
        direction,
        priority,
        timestamp: Date.now(),
      };

      return new Promise(resolve => {
        this.operationQueue.push(operation);
        this.once(`sync_complete_${operation.id}`, resolve);
      });
    }

    const operation: SyncOperation = {
      id: `sync_${Date.now()}`,
      type,
      direction,
      priority,
      timestamp: Date.now(),
    };

    return this.executeSyncOperation(operation);
  }

  /**
   * Execute a sync operation with file-based concurrency control
   */
  private async executeSyncOperation(
    operation: SyncOperation
  ): Promise<SyncResult> {
    this.isSyncing = true;
    this.currentOperation = operation;
    const startTime = Date.now();

    console.log(
      `[UnifiedSync] 🚀 Starting ${operation.type} sync (${operation.direction})`
    );
    this.emit('sync_started', operation);
    
    // Acquire file-based lock for cross-process synchronization
    const lockResource = `sync_${operation.type}_${this.organizationId}`;
    let lock: Lock | null = null;
    
    try {
      console.log(`[UnifiedSync] 🔐 Acquiring lock for resource: ${lockResource}`);
      lock = await fileConcurrencyController.acquireLock(lockResource, 60000); // 1 minute timeout
      console.log(`[UnifiedSync] ✅ Lock acquired: ${lock.id}`);
      
      // Check authentication after acquiring lock
      const authCheck = await ensureAuthenticatedSession();
      if (!authCheck.session) {
        throw new Error(`Authentication required: ${authCheck.error}`);
      }

      let result: SyncResult;

      // Execute based on operation type
      console.log(
        `[UnifiedSync] 🔀 Executing ${operation.type} sync with ${operation.direction} direction`
      );
      const outboxItemsCount = syncOutboxService.getPendingChanges().length;
      console.log(
        `[UnifiedSync] 📋 Outbox contains ${outboxItemsCount} pending items`
      );

      switch (operation.type) {
        case 'colors':
          console.log(
            `[UnifiedSync] ➡️ Calling syncColors() - includes outbox processing`
          );
          result = await this.syncColors(operation);
          break;
        case 'products':
          console.log(
            `[UnifiedSync] ➡️ Calling syncProducts() - includes outbox processing`
          );
          result = await this.syncProducts(operation);
          break;
        case 'organizations':
          console.log(
            `[UnifiedSync] ➡️ Calling syncOrganizations() - database flags only`
          );
          result = await this.syncOrganizations(operation);
          break;
        case 'full':
          console.log(
            `[UnifiedSync] ➡️ Calling syncFull() - comprehensive outbox + database sync`
          );
          result = await this.syncFull(operation);
          break;
        default:
          throw new Error(`Unknown sync type: ${operation.type}`);
      }

      result.duration = Date.now() - startTime;
      this.lastSyncTime = Date.now();

      console.log(`[UnifiedSync] ✅ ${operation.type} sync completed:`, {
        itemsProcessed: result.itemsProcessed,
        duration: result.duration,
        success: result.success,
      });

      this.emit('sync_completed', result);
      this.emit(`sync_complete_${operation.id}`, result);

      return result;
    } catch (error) {
      console.error(`[UnifiedSync] ❌ ${operation.type} sync failed:`, error);

      // Attempt error recovery
      const recoveryResult = await handleSyncError(error);

      const result: SyncResult = {
        success: false,
        operation,
        itemsProcessed: 0,
        duration: Date.now() - startTime,
        errors: [recoveryResult.message],
      };

      this.emit('sync_failed', result);
      this.emit(`sync_complete_${operation.id}`, result);

      return result;
    } finally {
      // Release the file-based lock
      if (lock) {
        try {
          await fileConcurrencyController.releaseLock(lock);
          console.log(`[UnifiedSync] 🔓 Lock released: ${lock.id}`);
        } catch (lockError) {
          console.error(`[UnifiedSync] ❌ Failed to release lock ${lock.id}:`, lockError);
        }
      }
      
      this.isSyncing = false;
      this.currentOperation = null;

      // Process next operation in queue
      this.processQueue();
    }
  }

  /**
   * Sync colors with transaction support
   */
  private async syncColors(operation: SyncOperation): Promise<SyncResult> {
    console.log('[UnifiedSync] 🎨 Syncing colors with transaction support...');
    
    const { ServiceLocator } = await import('../service-locator');
    const colorService = ServiceLocator.getColorService();
    
    let totalItemsProcessed = 0;
    const allErrors: string[] = [];
    
    if (operation.direction === 'push' || operation.direction === 'bidirectional') {
      // Step 1: Process outbox items for colors in transaction
      this.sendProgressUpdate('Processing Color Outbox', 0, 1, 'Checking for queued color changes...');
      const pendingOutboxChanges = syncOutboxService.getPendingChanges().filter(item => 
        item.table === 'colors'
      );
      const pendingOutboxChanges = syncOutboxService
        .getPendingChanges()
        .filter(item => item.table === 'colors');

      if (pendingOutboxChanges.length > 0) {
        console.log(`[UnifiedSync] 📤 Processing ${pendingOutboxChanges.length} color items in transaction...`);
        
        const outboxResult = await transactionAwareSyncManager.processOutboxInTransaction(
          pendingOutboxChanges,
          this.organizationId!,
          this.userId!,
          async (item, context) => {
            this.sendProgressUpdate('Pushing Color Changes', 0, pendingOutboxChanges.length, `Syncing ${item.table} ${item.action}`);
            
            const colorId = item.data?.id;
            if (!colorId) {
              throw new Error(
                `Invalid color data in outbox: ${JSON.stringify(item.data)}`
              );
            }
            
            console.log(`[TransactionSync] Processing color ${item.action} for ID ${colorId} in transaction ${context.transactionId}`);
            await colorService.pushColorToSupabase(colorId, this.organizationId!);
          }
        );
        
        totalItemsProcessed += outboxResult.itemsProcessed;
        if (outboxResult.errors.length > 0) {
          allErrors.push(...outboxResult.errors);
        }
        
        console.log(`[UnifiedSync] Outbox processing completed: ${outboxResult.itemsProcessed} items processed, ${outboxResult.errors.length} errors`);
      }
      
      // Step 2: Process database flag changes for colors in transaction
      const pendingChanges = await colorService.getUnsynced();
      if (pendingChanges.length > 0) {
        console.log(`[UnifiedSync] Processing ${pendingChanges.length} pending color changes in transaction...`);
        
        const flagSyncResult = await transactionAwareSyncManager.executeSyncInTransaction(
          async (context) => {
            let processedCount = 0;
            const errors: string[] = [];
            
            for (const [index, change] of pendingChanges.entries()) {
              try {
                this.sendProgressUpdate('Pushing Colors', index + 1, pendingChanges.length, `Pushing color ${change.name}`);
                await colorService.pushColorToSupabase(change.id, change.organizationId);
                processedCount++;
              } catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                console.error(`[UnifiedSync] Failed to push color change ${change.id}:`, error);
                errors.push(`Failed to push color ${change.name}: ${errorMessage}`);
              }
            }
            
            return { processedCount, errors };
          },
          this.organizationId!,
          this.userId!
        );
        
        if (flagSyncResult.success && flagSyncResult.result) {
          totalItemsProcessed += flagSyncResult.result.processedCount;
          if (flagSyncResult.result.errors.length > 0) {
            allErrors.push(...flagSyncResult.result.errors);
          }
        } else if (flagSyncResult.error) {
          allErrors.push(`Flag sync failed: ${flagSyncResult.error.message}`);
        }
      }
    }
    
    if (operation.direction === 'pull' || operation.direction === 'bidirectional') {
      // Pull remote changes from Supabase (non-transactional as it's read-only)
      try {
        this.sendProgressUpdate('Pulling Colors', 0, 1, 'Pulling remote color changes...');
        const syncedColors = await colorService.syncColorsFromSupabase(this.userId!, this.organizationId!);
        totalItemsProcessed += syncedColors.length;
        this.sendProgressUpdate('Pulling Colors', 1, 1, 'Colors synced.');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[UnifiedSync] Failed to pull colors:`, error);
        allErrors.push(`Failed to pull colors: ${errorMessage}`);
      }
    }

    return {
      success: allErrors.length === 0,
      operation,
      itemsProcessed: totalItemsProcessed,
      duration: 0, // Will be set by caller
errors: allErrors.length > 0 ? allErrors : undefined
    };
  }

  /**
   * Sync products with transaction support
   */
  private async syncProducts(operation: SyncOperation): Promise<SyncResult> {
    console.log('[UnifiedSync] 📦 Syncing products with transaction support...');
    
    const { ServiceLocator } = await import('../service-locator');
    const productService = ServiceLocator.getProductService();
    
    let totalItemsProcessed = 0;
    const allErrors: string[] = [];
    
    if (operation.direction === 'push' || operation.direction === 'bidirectional') {
      // Step 1: Process outbox items for products in transaction
      this.sendProgressUpdate('Processing Product Outbox', 0, 1, 'Checking for queued product changes...');
      const pendingOutboxChanges = syncOutboxService.getPendingChanges().filter(item => 
        item.table === 'products' || item.table === 'product_colors'
      );
      const pendingOutboxChanges = syncOutboxService
        .getPendingChanges()
        .filter(
          item => item.table === 'products' || item.table === 'product_colors'
        );

      if (pendingOutboxChanges.length > 0) {
        console.log(`[UnifiedSync] 📤 Processing ${pendingOutboxChanges.length} product items in transaction...`);
        
        const outboxResult = await transactionAwareSyncManager.processOutboxInTransaction(
          pendingOutboxChanges,
          this.organizationId!,
          this.userId!,
          async (item, context) => {
            this.sendProgressUpdate('Pushing Product Changes', 0, pendingOutboxChanges.length, `Syncing ${item.table} ${item.action}`);
            
            switch (item.table) {
              case 'products':
                const productId = item.data?.id;
                if (!productId) {
                  throw new Error(
                    `Invalid product data in outbox: ${JSON.stringify(item.data)}`
                  );
                }
                console.log(`[TransactionSync] Processing product ${item.action} for ID ${productId} in transaction ${context.transactionId}`);
                await productService.pushProductToSupabase(productId, this.organizationId!);
                break;
              case 'product_colors':
                console.log(`[TransactionSync] Processing product_colors ${item.action} in transaction ${context.transactionId}`);
                await productService.syncProductColorsFromSupabase(this.organizationId!);
                break;
            }
          }
        );
        
        totalItemsProcessed += outboxResult.itemsProcessed;
        if (outboxResult.errors.length > 0) {
          allErrors.push(...outboxResult.errors);
        }
        
        console.log(`[UnifiedSync] Product outbox processing completed: ${outboxResult.itemsProcessed} items processed, ${outboxResult.errors.length} errors`);
      }
      
      // Step 2: Process database flag changes for products in transaction
      const pendingChanges = await productService.getUnsynced();
      if (pendingChanges.length > 0) {
        console.log(`[UnifiedSync] Processing ${pendingChanges.length} pending product changes in transaction...`);
        
        const flagSyncResult = await transactionAwareSyncManager.executeSyncInTransaction(
          async (context) => {
            let processedCount = 0;
            const errors: string[] = [];
            
            for (const [index, change] of pendingChanges.entries()) {
              try {
                this.sendProgressUpdate('Pushing Products', index + 1, pendingChanges.length, `Pushing product ${change.name}`);
                await productService.pushProductToSupabase(change.id, change.organizationId);
                processedCount++;
              } catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                console.error(`[UnifiedSync] Failed to push product change ${change.id}:`, error);
                errors.push(`Failed to push product ${change.name}: ${errorMessage}`);
              }
            }
            
            return { processedCount, errors };
          },
          this.organizationId!,
          this.userId!
        );
        
        if (flagSyncResult.success && flagSyncResult.result) {
          totalItemsProcessed += flagSyncResult.result.processedCount;
          if (flagSyncResult.result.errors.length > 0) {
            allErrors.push(...flagSyncResult.result.errors);
          }
        } else if (flagSyncResult.error) {
          allErrors.push(`Product flag sync failed: ${flagSyncResult.error.message}`);
        }
      }
    }
    
    if (operation.direction === 'pull' || operation.direction === 'bidirectional') {
      // Pull remote changes from Supabase (non-transactional as it's read-only)
      try {
        this.sendProgressUpdate('Pulling Products', 0, 1, 'Pulling remote product changes...');
        const remoteProducts = await productService.syncProductsFromSupabase(this.userId!, this.organizationId!);
        const totalProducts = remoteProducts.length;
        totalItemsProcessed += totalProducts;

        for (const [index, remoteProduct] of remoteProducts.entries()) {
          const localProduct = await productService.getById(remoteProduct.id, this.organizationId!);
          if (localProduct) {
            const remoteTimestamp = new Date(remoteProduct.updatedAt).getTime();
            const localTimestamp = new Date(localProduct.updatedAt).getTime();

            if (remoteTimestamp > localTimestamp) {
              // Remote is newer, update local
              await productService.update(localProduct.id, remoteProduct, this.organizationId!);
            }
          } else {
            // Product doesn't exist locally, so create it
            await productService.add(remoteProduct, this.organizationId!);
          }
          this.sendProgressUpdate('Pulling Products', index + 1, totalProducts, `Synced product ${remoteProduct.name}`);
        }
        
        // Sync product-color relationships
        this.sendProgressUpdate('Syncing Relationships', 0, 1, 'Syncing product-color relationships...');
        const relationshipResult = await productService.syncProductColorsFromSupabase(this.organizationId!);
        totalItemsProcessed += relationshipResult.syncedCount || 0;
        this.sendProgressUpdate('Syncing Relationships', 1, 1, 'Product-color relationships synced.');
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[UnifiedSync] Failed to pull products:`, error);
        allErrors.push(`Failed to pull products: ${errorMessage}`);
      }
    }

    return {
      success: allErrors.length === 0,
      operation,
      itemsProcessed: totalItemsProcessed,
      duration: 0, // Will be set by caller
errors: allErrors.length > 0 ? allErrors : undefined
    };
  }

  /**
   * Sync organizations
   */
  private async syncOrganizations(
    operation: SyncOperation
  ): Promise<SyncResult> {
    console.log('[UnifiedSync] 🏢 Syncing organizations...');

    const { ServiceLocator } = await import('../service-locator');
    const organizationService = ServiceLocator.getOrganizationService();

    let itemsProcessed = 0;

    if (
      operation.direction === 'pull' ||
      operation.direction === 'bidirectional'
    ) {
      // Organizations are typically pulled from Supabase
      await organizationService.syncOrganizationsFromSupabase(this.userId!);
      itemsProcessed += 1; // Approximate
    }

    return {
      success: true,
      operation,
      itemsProcessed,
      duration: 0,
    };
  }

  /**
   * Send progress update to the renderer process
   */
  private sendProgressUpdate(
    phase: string,
    progress: number,
    total: number,
    currentOperation: string
  ) {
    if (mainWindow) {
      const percentage = total > 0 ? (progress / total) * 100 : 0;
      mainWindow.webContents.send('sync:progress-update', {
        phase,
        progress: percentage,
        currentOperation,
      });
    }
  }

  /**
   * Full sync - all data types with transaction support
   */
  private async syncFull(operation: SyncOperation): Promise<SyncResult> {
console.log('[UnifiedSync] 🔄 Starting full sync with transaction support...');
    let totalItemsProcessed = 0;
    const allErrors: string[] = [];

    // Step 1: Push local changes if direction is push or bidirectional
    if (operation.direction === 'push' || operation.direction === 'bidirectional') {
        this.sendProgressUpdate('Processing Outbox', 0, 1, 'Checking for local changes...');
        const pendingChanges = syncOutboxService.getPendingChanges();
        
        if (pendingChanges.length > 0) {
            console.log(`[UnifiedSync] 📤 Processing ${pendingChanges.length} items in transaction...`);
            
            const outboxResult = await transactionAwareSyncManager.processOutboxInTransaction(
                pendingChanges,
                this.organizationId!,
                this.userId!,
                async (item, context) => {
                    this.sendProgressUpdate('Pushing Changes', 0, pendingChanges.length, `Syncing ${item.table} ${item.action}`);
                    
                    const { ServiceLocator } = await import('../service-locator');
                    
                    switch (item.table) {
                        case 'colors':
                            const colorService = ServiceLocator.getColorService();
                            const colorId = item.data?.id;
                            if (!colorId) {
                                throw new Error(`Invalid color data in outbox: ${JSON.stringify(item.data)}`);
                            }
                            console.log(`[TransactionSync] Processing color ${item.action} for ID ${colorId} in transaction ${context.transactionId}`);
                            await colorService.pushColorToSupabase(colorId, this.organizationId!);
                            break;
                        case 'products':
                            const productService = ServiceLocator.getProductService();
                            const productId = item.data?.id;
                            if (!productId) {
                                throw new Error(`Invalid product data in outbox: ${JSON.stringify(item.data)}`);
                            }
                            console.log(`[TransactionSync] Processing product ${item.action} for ID ${productId} in transaction ${context.transactionId}`);
                            await productService.pushProductToSupabase(productId, this.organizationId!);
                            break;
                        case 'product_colors':
                            const prodService = ServiceLocator.getProductService();
                            console.log(`[TransactionSync] Processing product_colors ${item.action} in transaction ${context.transactionId}`);
                            await prodService.syncProductColorsFromSupabase(this.organizationId!);
                            break;
                    }
                }
            );
            
            totalItemsProcessed += outboxResult.itemsProcessed;
            if (outboxResult.errors.length > 0) {
                allErrors.push(...outboxResult.errors);
            }
            
            console.log(`[UnifiedSync] Full sync outbox processing completed: ${outboxResult.itemsProcessed} items processed, ${outboxResult.errors.length} errors`);
        }
        
        this.sendProgressUpdate(
          'Processing Outbox',
          1,
          1,
          'Local changes processed.'
        );
    }

    // Step 2: Pull remote changes if direction is pull or bidirectional
    if (operation.direction === 'pull' || operation.direction === 'bidirectional') {
        try {
            this.sendProgressUpdate('Syncing Colors', 0, 1, 'Pulling remote color changes...');
            // Create a pull-only operation to pass down
            const pullOnlyOp = { ...operation, type: 'colors', direction: 'pull' } as const;
            const colorResult = await this.syncColors(pullOnlyOp);
            totalItemsProcessed += colorResult.itemsProcessed;
            if (!colorResult.success && colorResult.errors) {
                allErrors.push(...colorResult.errors);
            }
            this.sendProgressUpdate('Syncing Colors', 1, 1, 'Color sync complete.');
        } catch (error: any) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            allErrors.push(`Color sync failed: ${errorMessage}`);
        }
        
        try {
            this.sendProgressUpdate('Syncing Products', 0, 1, 'Pulling remote product changes...');
            const pullOnlyOp = { ...operation, type: 'products', direction: 'pull' } as const;
            const productResult = await this.syncProducts(pullOnlyOp);
            totalItemsProcessed += productResult.itemsProcessed;
            if (!productResult.success && productResult.errors) {
                allErrors.push(...productResult.errors);
            }
            this.sendProgressUpdate('Syncing Products', 1, 1, 'Product sync complete.');
        } catch (error: any) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            allErrors.push(`Product sync failed: ${errorMessage}`);
        }
    }

    // Consider sync successful if we processed items, even if there were some outbox errors
    // This prevents stale outbox items from failing an otherwise successful sync
    const isSuccessful = totalItemsProcessed > 0 || allErrors.length === 0;
    
    return {
      success: isSuccessful,
      operation,
      itemsProcessed: totalItemsProcessed,
      duration: 0, // Will be set by caller
      errors: allErrors.length > 0 ? allErrors : undefined
    };
  }

  /**
   * Process queued operations
   */
  private async processQueue(): Promise<void> {
    if (this.operationQueue.length === 0) {
      return;
    }

    // Sort by priority and timestamp
    this.operationQueue.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      const priorityDiff =
        priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) {return priorityDiff;}
      return a.timestamp - b.timestamp; // FIFO for same priority
    });

    const nextOperation = this.operationQueue.shift()!;
    await this.executeSyncOperation(nextOperation);
  }

  /**
   * Start auto-sync timer
   */
  private startAutoSync(): void {
    if (this.autoSyncTimer) {
      clearInterval(this.autoSyncTimer);
      // Untrack the old timer if it was tracked
      resourceManager.untrackResource(`autosync_timer_${this.autoSyncTimer}`);
    }

    const intervalMs = this.config.autoSyncInterval * 60 * 1000;

    this.autoSyncTimer = setInterval(async () => {
      if (!this.isSyncing) {
        console.log('[UnifiedSync] ⏰ Auto-sync triggered');
        try {
          await this.sync('full', 'bidirectional', 'low');
        } catch (error) {
          console.error('[UnifiedSync] Auto-sync failed:', error);
        }
      }
    }, intervalMs);
    
    // Track the auto-sync timer for cleanup
    resourceManager.trackInterval(this.autoSyncTimer, {
      purpose: 'auto_sync',
      interval: intervalMs,
      component: 'unified_sync_manager'
    });
    
    console.log(`[UnifiedSync] ⏰ Auto-sync enabled (${this.config.autoSyncInterval} minutes)`);
  }

  /**
   * Stop auto-sync timer
   */
  private stopAutoSync(): void {
    if (this.autoSyncTimer) {
      clearInterval(this.autoSyncTimer);
      // Untrack the timer from resource manager
      resourceManager.untrackResource(`autosync_timer_${this.autoSyncTimer}`);
      this.autoSyncTimer = null;
      console.log('[UnifiedSync] ⏰ Auto-sync disabled');
    }
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<UnifiedSyncConfig>): void {
    this.config = { ...this.config, ...config };

    if (config.autoSyncEnabled !== undefined) {
      if (config.autoSyncEnabled) {
        this.startAutoSync();
      } else {
        this.stopAutoSync();
      }
    } else if (
      config.autoSyncInterval !== undefined &&
      this.config.autoSyncEnabled
    ) {
      this.startAutoSync(); // Restart with new interval
    }

    console.log('[UnifiedSync] 🔧 Configuration updated:', this.config);
  }

  /**
   * Check if sync manager is ready for operations
   */
  isReady(): boolean {
    return (
      this.isInitialized && this.userId !== null && this.organizationId !== null
    );
  }

  /**
   * Get sync status
   */
  getStatus(): {
    isInitialized: boolean;
    isReady: boolean;
    isSyncing: boolean;
    currentOperation: string | null;
    queueLength: number;
    lastSyncTime: number | null;
    config: any;
  } {
    return {
      isInitialized: this.isInitialized,
      isReady: this.isReady(),
      isSyncing: this.isSyncing,
      currentOperation: this.currentOperation?.type || null,
      queueLength: this.operationQueue.length,
      lastSyncTime: this.lastSyncTime,
      config: this.config,
    };
  }

  /**
   * Force stop all sync operations
   */
  stop(): void {
    this.stopAutoSync();
    this.operationQueue.length = 0;
    this.isSyncing = false;
    this.currentOperation = null;
    console.log('[UnifiedSync] 🛑 Sync manager stopped');
  }

  /**
   * Clean shutdown
   */
  shutdown(): void {
    this.stop();
    this.removeAllListeners();
    UnifiedSyncManager.instance = null;
    console.log('[UnifiedSync] 🔌 Sync manager shut down');
  }
}

// Export singleton instance
export const unifiedSyncManager = UnifiedSyncManager.getInstance();
