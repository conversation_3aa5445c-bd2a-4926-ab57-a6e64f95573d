/**
 * @file product-deduplication.service.test.ts
 * @description Comprehensive unit tests for ProductDeduplicationService
 *
 * Tests various duplicate scenarios, matching algorithms, merge strategies,
 * and edge cases to ensure robust deduplication behavior.
 */

import Database from 'better-sqlite3';
import { ProductRepository } from '../../../db/repositories/product.repository';
import { ProductColorRelationshipService } from '../product-color-relationship.service';
import { ColorRepository } from '../../../db/repositories/color.repository';
import {
  ProductDeduplicationService,
  DEFAULT_DETECTION_CRITERIA,
  DEFAULT_MATCHING_ALGORITHM,
  DEFAULT_MERGE_STRATEGY,
  DEFAULT_DEDUPLICATION_OPTIONS,
  DuplicateGroup,
  ProductDuplicate,
  DeduplicationOptions,
  MatchingAlgorithm,
  DuplicateDetectionCriteria,
  MergeStrategy,
} from '../product-deduplication.service';

describe('ProductDeduplicationService', () => {
  let db: Database.Database;
  let productRepository: ProductRepository;
  let colorRepository: ColorRepository;
  let relationshipService: ProductColorRelationshipService;
  let deduplicationService: ProductDeduplicationService;

  const testOrgId = 'test-org-123';
  const testUserId = 'test-user-456';

  beforeEach(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');

    // Create test schema
    createTestSchema(db);

    // Initialize repositories and services
    productRepository = new ProductRepository(db);
    colorRepository = new ColorRepository(db);
    relationshipService = new ProductColorRelationshipService(
      productRepository,
      colorRepository
    );
    deduplicationService = new ProductDeduplicationService(
      db,
      productRepository,
      relationshipService
    );

    // Insert test organization
    db.prepare(
      `INSERT INTO organizations (id, external_id, name) VALUES (1, ?, 'Test Org')`
    ).run(testOrgId);
  });

  afterEach(() => {
    db.close();
  });

  describe('Duplicate Detection', () => {
    test('should detect exact name duplicates', async () => {
      // Setup: Create products with identical names
      const productId1 = await createTestProduct('Duplicate Product', {
        sku: 'SKU001',
      });
      const productId2 = await createTestProduct('Duplicate Product', {
        sku: 'SKU002',
      });
      const productId3 = await createTestProduct('Different Product', {
        sku: 'SKU003',
      });

      const options: Partial<DeduplicationOptions> = {
        criteria: { ...DEFAULT_DETECTION_CRITERIA, sku: false },
        matching: { ...DEFAULT_MATCHING_ALGORITHM, type: 'exact' },
      };

      const result = deduplicationService.analyzeDuplicates(testOrgId, options);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].products).toHaveLength(2);
      expect(result.data![0].products.map(p => p.id)).toEqual(
        expect.arrayContaining([productId1, productId2])
      );
    });

    test('should detect fuzzy name duplicates', async () => {
      // Setup: Create products with similar names
      const productId1 = await createTestProduct('Product Name');
      const productId2 = await createTestProduct('Product  Name'); // Extra space
      const productId3 = await createTestProduct('Product-Name'); // Different separator
      const productId4 = await createTestProduct('Completely Different');

      const options: Partial<DeduplicationOptions> = {
        criteria: { ...DEFAULT_DETECTION_CRITERIA, sku: false },
        matching: {
          type: 'fuzzy',
          threshold: 0.8,
          ignoreCase: true,
          ignoreWhitespace: true,
          ignoreSpecialChars: true,
        },
      };

      const result = deduplicationService.analyzeDuplicates(testOrgId, options);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].products).toHaveLength(3);
      expect(result.data![0].products.map(p => p.id)).toEqual(
        expect.arrayContaining([productId1, productId2, productId3])
      );
    });

    test('should detect SKU duplicates', async () => {
      // Setup: Create products with same SKU but different names
      const productId1 = await createTestProduct('Product A', {
        sku: 'SAME-SKU',
      });
      const productId2 = await createTestProduct('Product B', {
        sku: 'SAME-SKU',
      });
      const productId3 = await createTestProduct('Product C', {
        sku: 'DIFFERENT-SKU',
      });

      const options: Partial<DeduplicationOptions> = {
        criteria: {
          name: false,
          sku: true,
          metadata: false,
          fuzzyThreshold: 0.8,
        },
        matching: { ...DEFAULT_MATCHING_ALGORITHM, type: 'exact' },
      };

      const result = deduplicationService.analyzeDuplicates(testOrgId, options);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].products).toHaveLength(2);
      expect(result.data![0].products.map(p => p.id)).toEqual(
        expect.arrayContaining([productId1, productId2])
      );
    });

    test('should detect complex multi-criteria duplicates', async () => {
      // Setup: Create products with combinations of similar criteria
      const productId1 = await createTestProduct('Product Alpha', {
        sku: 'SKU-001',
        description: 'High quality product',
        category: 'electronics',
      });
      const productId2 = await createTestProduct('Product Alpha', {
        sku: 'SKU-001-V2',
        description: 'High quality product',
        category: 'electronics',
      });
      const productId3 = await createTestProduct('Product Beta', {
        sku: 'SKU-002',
        description: 'Different product',
        category: 'furniture',
      });

      const options: Partial<DeduplicationOptions> = {
        criteria: {
          name: true,
          sku: true,
          metadata: true,
          fuzzyThreshold: 0.8,
        },
        matching: {
          type: 'fuzzy',
          threshold: 0.7,
          ignoreCase: true,
          ignoreWhitespace: true,
          ignoreSpecialChars: false,
        },
      };

      const result = deduplicationService.analyzeDuplicates(testOrgId, options);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].products).toHaveLength(2);
      expect(result.data![0].confidence).toBeGreaterThan(0.7);
    });

    test('should handle no duplicates scenario', async () => {
      // Setup: Create unique products
      await createTestProduct('Unique Product A', { sku: 'SKU-A' });
      await createTestProduct('Unique Product B', { sku: 'SKU-B' });
      await createTestProduct('Unique Product C', { sku: 'SKU-C' });

      const result = deduplicationService.analyzeDuplicates(testOrgId);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(0);
    });

    test('should handle empty product list', async () => {
      const result = deduplicationService.analyzeDuplicates(testOrgId);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(0);
    });
  });

  describe('Similarity Algorithms', () => {
    test('should calculate Levenshtein similarity correctly', async () => {
      // This tests the internal similarity calculation
      const productId1 = await createTestProduct('Testing Product');
      const productId2 = await createTestProduct('Testing Product!'); // One character difference
      const productId3 = await createTestProduct('Completely Different Name');

      const options: Partial<DeduplicationOptions> = {
        criteria: {
          name: true,
          sku: false,
          metadata: false,
          fuzzyThreshold: 0.9,
        },
        matching: {
          type: 'fuzzy',
          threshold: 0.9,
          ignoreCase: true,
          ignoreWhitespace: true,
          ignoreSpecialChars: true,
        },
      };

      const result = deduplicationService.analyzeDuplicates(testOrgId, options);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].products).toHaveLength(2);
    });

    test('should calculate Jaccard similarity correctly', async () => {
      const productId1 = await createTestProduct('ABC Product');
      const productId2 = await createTestProduct('ABC Item'); // Similar character set
      const productId3 = await createTestProduct('XYZ Widget'); // Different character set

      const options: Partial<DeduplicationOptions> = {
        criteria: {
          name: true,
          sku: false,
          metadata: false,
          fuzzyThreshold: 0.3,
        },
        matching: {
          type: 'similarity',
          threshold: 0.3,
          ignoreCase: true,
          ignoreWhitespace: false,
          ignoreSpecialChars: false,
        },
      };

      const result = deduplicationService.analyzeDuplicates(testOrgId, options);

      expect(result.success).toBe(true);
      // Should detect some similarity between ABC products
      if (result.data!.length > 0) {
        expect(result.data![0].products.map(p => p.name)).toEqual(
          expect.arrayContaining(['ABC Product', 'ABC Item'])
        );
      }
    });
  });

  describe('Primary Product Selection', () => {
    test('should select product with most colors as primary', async () => {
      // Setup products with different color counts
      const productId1 = await createTestProduct('Duplicate Product');
      const productId2 = await createTestProduct('Duplicate Product');

      // Add colors to make product2 have more colors
      const colorId1 = await createTestColor('Red', '#FF0000');
      const colorId2 = await createTestColor('Blue', '#0000FF');

      await addColorToProduct(productId1, colorId1);
      await addColorToProduct(productId2, colorId1);
      await addColorToProduct(productId2, colorId2);

      const result = deduplicationService.analyzeDuplicates(testOrgId);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].primaryProduct.id).toBe(productId2);
      expect(result.data![0].primaryProduct.colorCount).toBe(2);
    });

    test('should select older product when color counts are equal', async () => {
      // Setup products created at different times
      const productId1 = await createTestProduct('Duplicate Product');

      // Wait a bit to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));

      const productId2 = await createTestProduct('Duplicate Product');

      const result = deduplicationService.analyzeDuplicates(testOrgId);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      // Should prefer the older product (productId1)
      expect(result.data![0].primaryProduct.id).toBe(productId1);
    });
  });

  describe('Merge Strategies', () => {
    test('should merge with union color strategy', async () => {
      const productId1 = await createTestProduct('Duplicate Product');
      const productId2 = await createTestProduct('Duplicate Product');

      const colorId1 = await createTestColor('Red', '#FF0000');
      const colorId2 = await createTestColor('Blue', '#0000FF');
      const colorId3 = await createTestColor('Green', '#00FF00');

      await addColorToProduct(productId1, colorId1);
      await addColorToProduct(productId1, colorId2);
      await addColorToProduct(productId2, colorId2);
      await addColorToProduct(productId2, colorId3);

      const options: Partial<DeduplicationOptions> = {
        merge: { ...DEFAULT_MERGE_STRATEGY, colorMergeStrategy: 'union' },
        dryRun: false,
      };

      const result = deduplicationService.deduplicateProducts(
        testOrgId,
        options
      );

      expect(result.success).toBe(true);
      expect(result.data!.mergedProducts).toBe(1);
      expect(result.data!.preservedProducts).toBe(1);

      // Check that primary product has all colors
      const primaryProductColors = relationshipService.getProductColors(
        productId1,
        testOrgId
      );
      expect(primaryProductColors.success).toBe(true);
      expect(primaryProductColors.data).toHaveLength(3);
    });

    test('should merge with name conflict resolution strategies', async () => {
      const productId1 = await createTestProduct('Short');
      const productId2 = await createTestProduct('Much Longer Product Name');

      const options: Partial<DeduplicationOptions> = {
        merge: {
          ...DEFAULT_MERGE_STRATEGY,
          nameConflictResolution: 'keep_longest',
        },
        dryRun: false,
      };

      const result = deduplicationService.deduplicateProducts(
        testOrgId,
        options
      );

      expect(result.success).toBe(true);

      // Verify the primary product has the longer name
      const updatedProduct = productRepository.findById(
        result.data!.mergedProducts > 0 ? productId1 : productId2,
        testOrgId
      );
      expect(updatedProduct?.name).toBe('Much Longer Product Name');
    });

    test('should merge metadata correctly', async () => {
      const productId1 = await createTestProduct('Duplicate Product', {
        sku: 'SKU-001',
        category: 'electronics',
      });
      const productId2 = await createTestProduct('Duplicate Product', {
        sku: 'SKU-002',
        description: 'Detailed description',
        manufacturer: 'ACME Corp',
      });

      const options: Partial<DeduplicationOptions> = {
        merge: { ...DEFAULT_MERGE_STRATEGY, metadataResolution: 'merge_all' },
        dryRun: false,
      };

      const result = deduplicationService.deduplicateProducts(
        testOrgId,
        options
      );

      expect(result.success).toBe(true);

      // Check that metadata was merged
      const primaryProduct = productRepository.findById(productId1, testOrgId);
      expect(primaryProduct).toBeTruthy();

      let metadata = {};
      try {
        metadata = JSON.parse(primaryProduct!.metadata);
      } catch (error) {
        // Metadata might be stored differently
      }

      // Should contain fields from both products
      expect(metadata).toBeTruthy();
    });
  });

  describe('Backup and Rollback', () => {
    test('should create backup before deduplication', async () => {
      const productId1 = await createTestProduct('Duplicate Product');
      const productId2 = await createTestProduct('Duplicate Product');

      const options: Partial<DeduplicationOptions> = {
        createBackup: true,
        dryRun: false,
      };

      const result = deduplicationService.deduplicateProducts(
        testOrgId,
        options
      );

      expect(result.success).toBe(true);
      expect(result.data!.backup).toBeTruthy();
      expect(result.data!.backup!.affectedProducts).toHaveLength(2);
      expect(result.data!.backup!.organizationId).toBe(testOrgId);
    });

    test('should restore from backup correctly', async () => {
      const originalName = 'Original Product Name';
      const productId1 = await createTestProduct(originalName);
      const productId2 = await createTestProduct(originalName);

      const colorId = await createTestColor('Red', '#FF0000');
      await addColorToProduct(productId1, colorId);

      // Perform deduplication with backup
      const deduplicationResult = deduplicationService.deduplicateProducts(
        testOrgId,
        {
          createBackup: true,
          dryRun: false,
        }
      );

      expect(deduplicationResult.success).toBe(true);
      expect(deduplicationResult.data!.backup).toBeTruthy();

      const backupId = deduplicationResult.data!.backup!.backupId;

      // Restore from backup
      const restoreResult = deduplicationService.restoreFromBackup(backupId);

      expect(restoreResult.success).toBe(true);

      // Verify products are restored
      const restoredProduct1 = productRepository.findById(
        productId1,
        testOrgId
      );
      const restoredProduct2 = productRepository.findById(
        productId2,
        testOrgId
      );

      expect(restoredProduct1).toBeTruthy();
      expect(restoredProduct2).toBeTruthy();
      expect(restoredProduct1?.name).toBe(originalName);
      expect(restoredProduct2?.name).toBe(originalName);
    });

    test('should list backups for organization', async () => {
      const productId1 = await createTestProduct('Test Product 1');
      const productId2 = await createTestProduct('Test Product 1');

      // Create multiple backups
      await deduplicationService.deduplicateProducts(testOrgId, {
        createBackup: true,
      });
      await deduplicationService.deduplicateProducts(testOrgId, {
        createBackup: true,
      });

      const backupsResult = deduplicationService.getBackups(testOrgId);

      expect(backupsResult.success).toBe(true);
      expect(backupsResult.data!.length).toBeGreaterThan(0);
      expect(backupsResult.data![0].organizationId).toBe(testOrgId);
    });
  });

  describe('Performance and Edge Cases', () => {
    test('should handle large datasets with batching', async () => {
      // Create many products
      const productPromises: Promise<string>[] = [];
      for (let i = 0; i < 50; i++) {
        productPromises.push(
          createTestProduct(`Product Batch ${Math.floor(i / 10)}`)
        );
      }
      await Promise.all(productPromises);

      const options: Partial<DeduplicationOptions> = {
        batchSize: 5,
        dryRun: true,
      };

      const result = deduplicationService.deduplicateProducts(
        testOrgId,
        options
      );

      expect(result.success).toBe(true);
      expect(result.data!.performance.productsProcessed).toBe(50);
      expect(result.data!.performance.executionTimeMs).toBeGreaterThan(0);
    });

    test('should respect maxGroupSize limit', async () => {
      // Create a large group of duplicates
      const productPromises: Promise<string>[] = [];
      for (let i = 0; i < 60; i++) {
        productPromises.push(createTestProduct('Huge Duplicate Group'));
      }
      await Promise.all(productPromises);

      const options: Partial<DeduplicationOptions> = {
        maxGroupSize: 10,
        dryRun: true,
      };

      const result = deduplicationService.deduplicateProducts(
        testOrgId,
        options
      );

      expect(result.success).toBe(true);
      // Should skip the large group
      expect(result.data!.processedGroups).toBe(0);
    });

    test('should handle products with malformed metadata', async () => {
      const productId1 = await createTestProduct('Product With Bad Metadata');

      // Corrupt the metadata
      db.prepare(
        `
        UPDATE products 
        SET metadata = ? 
        WHERE external_id = ?
      `
      ).run('{"invalid": json}', productId1);

      const productId2 = await createTestProduct('Product With Bad Metadata');

      const result = deduplicationService.deduplicateProducts(testOrgId);

      expect(result.success).toBe(true);
      // Should handle the error gracefully
    });

    test('should handle concurrent deduplication requests', async () => {
      const productId1 = await createTestProduct('Concurrent Test Product');
      const productId2 = await createTestProduct('Concurrent Test Product');

      // Run multiple deduplication operations
      const results = await Promise.all([
        deduplicationService.deduplicateProducts(testOrgId, { dryRun: true }),
        deduplicationService.deduplicateProducts(testOrgId, { dryRun: true }),
      ]);

      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Dry Run Mode', () => {
    test('should not modify data in dry run mode', async () => {
      const originalName = 'Original Product';
      const productId1 = await createTestProduct(originalName);
      const productId2 = await createTestProduct(originalName);

      const result = deduplicationService.deduplicateProducts(testOrgId, {
        dryRun: true,
      });

      expect(result.success).toBe(true);
      expect(result.data!.mergedProducts).toBe(0);
      expect(result.data!.preservedProducts).toBe(2); // Both preserved in dry run

      // Verify products are unchanged
      const product1 = productRepository.findById(productId1, testOrgId);
      const product2 = productRepository.findById(productId2, testOrgId);

      expect(product1?.name).toBe(originalName);
      expect(product2?.name).toBe(originalName);
      expect(product1?.deleted_at).toBeNull();
      expect(product2?.deleted_at).toBeNull();
    });
  });

  // === HELPER FUNCTIONS ===

  function createTestSchema(database: Database.Database) {
    // Create organizations table
    database.exec(`
      CREATE TABLE organizations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create products table
    database.exec(`
      CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        user_id TEXT,
        name TEXT NOT NULL,
        description TEXT,
        metadata TEXT,
        is_active INTEGER DEFAULT 1,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT,
        created_by TEXT,
        UNIQUE(external_id, organization_id)
      )
    `);

    // Create colors table
    database.exec(`
      CREATE TABLE colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        code TEXT NOT NULL,
        display_name TEXT,
        hex TEXT,
        source TEXT,
        color_spaces TEXT,
        is_gradient INTEGER DEFAULT 0,
        gradient_colors TEXT,
        notes TEXT,
        tags TEXT,
        is_library INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT,
        UNIQUE(external_id, organization_id)
      )
    `);

    // Create product_colors table
    database.exec(`
      CREATE TABLE product_colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        color_id INTEGER NOT NULL,
        display_order INTEGER NOT NULL,
        organization_id TEXT NOT NULL,
        FOREIGN KEY (product_id) REFERENCES products (id),
        FOREIGN KEY (color_id) REFERENCES colors (id),
        UNIQUE(product_id, color_id)
      )
    `);
  }

  async function createTestProduct(
    name: string,
    metadata: any = {}
  ): Promise<string> {
    const productData = {
      name,
      description: metadata.description,
      organizationId: testOrgId,
    };

    const extendedMetadata = {
      ...metadata,
      description: metadata.description,
    };

    const id = productRepository.insert(productData, testOrgId, testUserId);

    // Update with extended metadata if provided
    if (Object.keys(extendedMetadata).length > 0) {
      db.prepare(
        `
        UPDATE products 
        SET metadata = ? 
        WHERE external_id = ?
      `
      ).run(JSON.stringify(extendedMetadata), id);
    }

    return id;
  }

  async function createTestColor(name: string, hex: string): Promise<string> {
    const colorId = 'color-' + Math.random().toString(36).substring(7);

    db.prepare(
      `
      INSERT INTO colors (external_id, organization_id, code, display_name, hex)
      VALUES (?, ?, ?, ?, ?)
    `
    ).run(colorId, testOrgId, name.toUpperCase(), name, hex);

    return colorId;
  }

  async function addColorToProduct(
    productId: string,
    colorId: string
  ): Promise<void> {
    relationshipService.addColorToProduct(productId, colorId, testOrgId);
  }
});
