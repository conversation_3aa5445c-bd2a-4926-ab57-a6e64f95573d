/**
 * @file email-sender.ts
 * @description Handles email composition, sending, and retry logic using Zoho Mail API
 */

import axios from 'axios';
import { secureConfig } from '../../utils/secure-config-loader';
import { LoggerFactory, logPerformance, logErrors, ILogger } from '../../utils/logger.service';
import { ZohoTokenManager, ZohoRegionConfig } from './zoho-token-manager';
import { emailRetryQueue } from './email-retry-queue';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  content: string;
  fromAddress?: string;
  isHtml?: boolean;
  cc?: string[];
  bcc?: string[];
  replyTo?: string;
}

export interface InvitationData {
  organizationName: string;
  inviterName: string;
  role: string;
  token: string;
  expiresAt: Date;
}

export interface EmailSenderConfig {
  maxRetryAttempts?: number;
  baseRetryDelayMs?: number;
  maxRetryDelayMs?: number;
  requestTimeoutMs?: number;
  enableRetryQueue?: boolean;
}

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
  queued?: boolean;
}

/**
 * Email Sender - Handles email composition and sending via Zoho Mail API
 */
export class EmailSender {
  private readonly logger: ILogger;
  private readonly tokenManager: ZohoTokenManager;
  
  // Configuration
  private readonly DEFAULT_CONFIG: Required<EmailSenderConfig> = {
    maxRetryAttempts: 3,
    baseRetryDelayMs: 1000,
    maxRetryDelayMs: 30000,
    requestTimeoutMs: 15000,
    enableRetryQueue: true
  };
  
  private config: Required<EmailSenderConfig>;

  constructor(tokenManager: ZohoTokenManager, logger?: ILogger) {
    this.logger = logger || LoggerFactory.getInstance().createLogger('EmailSender');
    this.tokenManager = tokenManager;
    this.config = { ...this.DEFAULT_CONFIG };
  }

  /**
   * Configure email sender settings
   */
  configure(config: EmailSenderConfig): void {
    this.config = { ...this.config, ...config };
    
    this.logger.info('Email sender configuration updated', {
      config: this.config,
      operation: 'configure'
    });
  }

  /**
   * Get current configuration
   */
  getConfiguration(): Required<EmailSenderConfig> {
    return { ...this.config };
  }

  /**
   * Send email with retry logic and optional queue fallback
   */
  @logPerformance(LoggerFactory.getInstance().createLogger('EmailSender'), 'sendEmail')
  @logErrors(LoggerFactory.getInstance().createLogger('EmailSender'))
  async sendEmail(options: EmailOptions): Promise<EmailSendResult> {
    this.logger.info('Sending email', {
      to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
      subject: options.subject,
      isHtml: options.isHtml,
      operation: 'sendEmail'
    });

    try {
      // Attempt immediate send with retry logic
      const result = await this.sendEmailWithRetry(options, 0);
      
      if (result.success) {
        return result;
      }
      
      // If immediate send failed and retry queue is enabled, queue for background retry
      if (this.config.enableRetryQueue) {
        try {
          const emailId = await this.queueEmailForRetry(options, 'medium');
          this.logger.info('Email queued for background retry', {
            emailId,
            operation: 'sendEmail'
          });
          
          return {
            success: true,
            queued: true,
            messageId: emailId
          };
        } catch (queueError) {
          this.logger.error('Failed to queue email for retry', queueError as Error, {
            operation: 'sendEmail'
          });
        }
      }
      
      return result;
      
    } catch (error) {
      this.logger.error('Email send failed', error as Error, {
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        operation: 'sendEmail'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Email send failed'
      };
    }
  }

  /**
   * Send invitation email with enhanced template and high priority
   */
  @logPerformance(LoggerFactory.getInstance().createLogger('EmailSender'), 'sendInvitation')
  async sendInvitationEmail(to: string, invitation: InvitationData): Promise<EmailSendResult> {
    this.logger.info('Sending invitation email', {
      to,
      organizationName: invitation.organizationName,
      inviterName: invitation.inviterName,
      role: invitation.role,
      operation: 'sendInvitationEmail'
    });

    const emailOptions = {
      to,
      subject: `🎨 ${invitation.inviterName} invited you to join ${invitation.organizationName}`,
      content: this.createInvitationHTML(invitation),
      isHtml: true,
      fromAddress: secureConfig.getConfigValue('ZOHO_SUPPORT_ALIAS', 'email-sender') as string || '<EMAIL>'
    };

    try {
      // Try immediate send first
      const result = await this.sendEmailWithRetry(emailOptions, 0);
      
      if (result.success) {
        return result;
      }
      
      // If immediate send failed, queue with high priority for background retry
      if (this.config.enableRetryQueue) {
        try {
          const emailId = await this.queueEmailForRetry(emailOptions, 'high');
          this.logger.info('Invitation email queued with high priority', {
            emailId,
            operation: 'sendInvitationEmail'
          });
          
          return {
            success: true,
            queued: true,
            messageId: emailId
          };
        } catch (queueError) {
          this.logger.error('Failed to queue invitation email', queueError as Error, {
            operation: 'sendInvitationEmail'
          });
        }
      }
      
      return result;
      
    } catch (error) {
      this.logger.error('Invitation email failed', error as Error, {
        to,
        operation: 'sendInvitationEmail'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Invitation email failed'
      };
    }
  }

  /**
   * Get retry queue statistics
   */
  getRetryQueueStats(): {
    totalQueued: number;
    readyToProcess: number;
    processed: number;
    failed: number;
    oldestEmail?: number;
  } {
    return emailRetryQueue.getQueueStats();
  }

  // Private methods

  /**
   * Send email with retry logic
   */
  private async sendEmailWithRetry(options: EmailOptions, attemptCount: number): Promise<EmailSendResult> {
    try {
      // Get valid token and region config
      const accessToken = await this.tokenManager.getValidToken();
      const regionConfig = await this.tokenManager.getRegionConfig();
      
      // Prepare email data
      const emailData = this.prepareEmailData(options);
      
      // Send email via Zoho API
      const response = await this.sendViaZohoAPI(emailData, accessToken, regionConfig);
      
      this.logger.info('Email sent successfully', {
        messageId: response.data?.data?.messageId,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        operation: 'sendEmailWithRetry'
      });

      return {
        success: true,
        messageId: response.data?.data?.messageId
      };
      
    } catch (error: any) {
      this.logger.warn('Email send attempt failed', {
        attempt: attemptCount + 1,
        maxAttempts: this.config.maxRetryAttempts,
        error: error.response?.data || error.message,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        operation: 'sendEmailWithRetry'
      });

      // Check if we should retry
      const shouldRetry = attemptCount < this.config.maxRetryAttempts && this.isRetryableError(error);
      
      if (shouldRetry) {
        const delay = this.calculateRetryDelay(attemptCount);
        
        this.logger.info('Retrying email send', {
          attempt: attemptCount + 2,
          delaySeconds: Math.ceil(delay / 1000),
          operation: 'sendEmailWithRetry'
        });
        
        await this.sleep(delay);
        return this.sendEmailWithRetry(options, attemptCount + 1);
      }

      // Log specific error details for troubleshooting
      this.logErrorDetails(error, attemptCount);

      return {
        success: false,
        error: this.formatErrorMessage(error, attemptCount)
      };
    }
  }

  /**
   * Prepare email data for Zoho API
   */
  private prepareEmailData(options: EmailOptions): any {
    const emailData: any = {
      fromAddress: options.fromAddress || secureConfig.getConfigValue('ZOHO_SUPPORT_ALIAS', 'email-sender') as string || '<EMAIL>',
      toAddress: Array.isArray(options.to) ? options.to.join(',') : options.to,
      subject: options.subject,
      content: options.content,
      mailFormat: options.isHtml ? 'html' : 'plaintext'
    };

    if (options.cc?.length) {
      emailData.ccAddress = options.cc.join(',');
    }
    if (options.bcc?.length) {
      emailData.bccAddress = options.bcc.join(',');
    }
    if (options.replyTo) {
      emailData.replyTo = options.replyTo;
    }

    return emailData;
  }

  /**
   * Send email via Zoho Mail API
   */
  private async sendViaZohoAPI(emailData: any, accessToken: string, regionConfig: ZohoRegionConfig): Promise<any> {
    const accountId = secureConfig.getConfigValue('ZOHO_ACCOUNT_ID', 'email-sender');
    
    return await axios.post(
      `https://${regionConfig.api}/api/accounts/${accountId}/messages`,
      emailData,
      {
        headers: {
          'Authorization': `Zoho-oauthtoken ${accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: this.config.requestTimeoutMs
      }
    );
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Rate limit errors
    if (error.response?.status === 429) {
      return true;
    }
    
    // Auth errors (token may need refresh)
    if (error.response?.status === 401) {
      return true;
    }
    
    // Server errors
    if (error.response?.status >= 500) {
      return true;
    }
    
    // Network errors
    if (error.code === 'ENOTFOUND' || 
        error.code === 'ECONNRESET' || 
        error.code === 'ETIMEDOUT') {
      return true;
    }
    
    return false;
  }

  /**
   * Calculate retry delay with exponential backoff and jitter
   */
  private calculateRetryDelay(attemptCount: number): number {
    const exponentialDelay = this.config.baseRetryDelayMs * Math.pow(2, attemptCount);
    const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5);
    return Math.min(jitteredDelay, this.config.maxRetryDelayMs);
  }

  /**
   * Log detailed error information
   */
  private logErrorDetails(error: any, attemptCount: number): void {
    if (error.response?.status === 400) {
      this.logger.error('Bad request - check email format and aliases (non-retryable)', error, {
        responseData: error.response.data,
        operation: 'sendEmailWithRetry'
      });
    } else if (error.response?.status === 403) {
      this.logger.error('Forbidden - check permissions (non-retryable)', error, {
        responseData: error.response.data,
        operation: 'sendEmailWithRetry'
      });
    } else if (attemptCount >= this.config.maxRetryAttempts) {
      this.logger.error('Max retries reached for email send', error, {
        maxRetries: this.config.maxRetryAttempts,
        finalError: error.response?.data || error.message,
        operation: 'sendEmailWithRetry'
      });
    }
  }

  /**
   * Format user-friendly error message
   */
  private formatErrorMessage(error: any, attemptCount: number): string {
    if (error.response?.status === 400) {
      return 'Invalid email format or configuration';
    } else if (error.response?.status === 403) {
      return 'Email service permissions error';
    } else if (error.response?.status === 429) {
      return 'Email service rate limit exceeded';
    } else if (attemptCount >= this.config.maxRetryAttempts) {
      return `Email delivery failed after ${this.config.maxRetryAttempts} attempts`;
    } else {
      return 'Email delivery failed due to service error';
    }
  }

  /**
   * Queue email for background retry
   */
  private async queueEmailForRetry(options: EmailOptions, priority: 'high' | 'medium' | 'low'): Promise<string> {
    return await emailRetryQueue.queueEmail({
      to: options.to,
      subject: options.subject,
      content: options.content,
      fromAddress: options.fromAddress,
      isHtml: options.isHtml,
      cc: options.cc,
      bcc: options.bcc,
      replyTo: options.replyTo
    }, priority);
  }

  /**
   * Create invitation email HTML template
   */
  private createInvitationHTML(invitation: InvitationData): string {
    return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChromaSync Team Invitation</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      line-height: 1.6; 
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
    }
    .container { 
      max-width: 600px;
      margin: 0 auto;
      background: #ffffff; 
      border-radius: 12px; 
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .content {
      padding: 40px 30px;
    }
    .invitation-card {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-left: 4px solid #3b82f6;
      border-radius: 8px;
      padding: 24px;
      margin: 24px 0;
    }
    .instructions {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin: 24px 0;
    }
    .instructions h3 {
      margin-top: 0;
      color: #1f2937;
    }
    .instructions ol {
      margin: 10px 0;
      padding-left: 20px;
    }
    .instructions li {
      margin: 8px 0;
    }
    .token-display {
      background: #1f2937;
      color: #10b981;
      font-family: 'Monaco', 'Courier New', monospace;
      padding: 12px 16px;
      border-radius: 6px;
      margin: 12px 0;
      word-break: break-all;
      text-align: center;
      font-size: 14px;
    }
    .footer {
      padding: 30px;
      background: #f8fafc;
      text-align: center;
      font-size: 14px;
      color: #64748b;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">
        <svg width="160" height="29" viewBox="0 0 320.6 57.8" xmlns="http://www.w3.org/2000/svg">
          <style>
            .logo-text { fill: white; }
            .logo-accent { fill: #3b82f6; }
          </style>
          <g>
            <path class="logo-text" d="M10.7,44.2c-3-1.8-5.4-4.1-7.1-7.1-1.7-3-2.6-6.3-2.6-10s.9-6.9,2.6-9.9c1.8-3,4.1-5.4,7.2-7.2,3-1.8,6.4-2.6,10-2.6s3.9.3,5.6.8v10.6c-1.8-1.1-3.7-1.7-5.6-1.7s-3.6.4-5.1,1.3c-1.5.9-2.7,2.1-3.5,3.6s-1.3,3.2-1.3,5.1.4,3.6,1.3,5.1,2.1,2.7,3.6,3.6c1.5.9,3.2,1.3,5,1.3s3.8-.6,5.6-1.7v10.6c-1.8.5-3.7.8-5.6.8-3.7,0-7-.9-10.1-2.6Z"/>
            <path class="logo-text" d="M30.1,6.1h9.3v12.6c1-1.1,2.2-2,3.5-2.6,1.4-.6,2.8-.9,4.3-.9,2.3,0,4.4.6,6.3,1.7,1.8,1.2,3.2,2.8,4.3,4.9,1,2.1,1.5,4.5,1.5,7.1v17.2h-9.3v-16.9c0-1.6-.5-2.8-1.4-3.8s-2.1-1.4-3.6-1.4-3,.5-4.1,1.5-1.6,2.3-1.6,3.8v16.8h-9.3V6.1Z"/>
            <path class="logo-text" d="M62.6,16h9.2v3.4c1.7-2.8,4-4.2,7-4.2s1.6.1,2.4.3v8.9h-2.9c-2,0-3.5.6-4.7,1.7-1.1,1.1-1.7,2.8-1.7,5v15.1h-9.3V16Z"/>
            <path class="logo-text" d="M89.7,44.8c-2.4-1.4-4.4-3.3-5.8-5.7-1.4-2.4-2.1-5.1-2.1-8s.7-5.6,2.1-8c1.4-2.4,3.3-4.3,5.8-5.7,2.5-1.4,5.1-2.1,8.1-2.1s5.5.7,8,2.1c2.4,1.4,4.3,3.3,5.7,5.7,1.4,2.4,2.1,5.1,2.1,8s-.7,5.6-2.1,8c-1.4,2.4-3.3,4.3-5.7,5.7-2.4,1.4-5.1,2.1-8,2.1s-5.6-.7-8.1-2.1ZM102.4,36c1.3-1.3,1.9-3,1.9-4.9s-.6-3.6-1.9-4.9c-1.3-1.3-2.9-2-4.7-2s-3.5.7-4.7,2c-1.3,1.3-1.9,3-1.9,4.9s.6,3.6,1.9,4.9c1.3,1.3,2.9,2,4.8,2s3.4-.7,4.7-2Z"/>
            <path class="logo-text" d="M150.4,25.1c-.8-.8-1.7-1.2-2.8-1.2s-2.4.4-3.2,1.2c-.8.8-1.2,1.8-1.2,3v18.1h-9.3v-18.1c0-1.2-.4-2.2-1.2-3-.8-.8-1.8-1.2-3-1.2s-2.3.4-3.1,1.1c-.8.8-1.2,1.8-1.2,3v18.1h-9.3V16h9.3v2.3c.9-1,2-1.7,3.3-2.3,1.3-.5,2.6-.8,4.1-.8s2.8.3,4.1.9c1.3.6,2.4,1.5,3.4,2.7,1.1-1.2,2.4-2.1,4-2.7,1.6-.7,3.3-1,5-1s4.2.5,6,1.6c1.7,1,3.1,2.5,4.1,4.4,1,1.9,1.5,4.1,1.5,6.6v18.3h-9.2v-18.1c0-1.2-.4-2.2-1.2-3Z"/>
            <path class="logo-text" d="M186.3,43.6c-2.1,2.2-5,3.3-8.7,3.3s-5.2-.7-7.4-2.1-4-3.3-5.2-5.7c-1.3-2.4-1.9-5.1-1.9-8.1s.6-5.7,1.9-8.1c1.3-2.4,3-4.3,5.2-5.7,2.2-1.4,4.7-2.1,7.4-2.1s6.6,1.1,8.7,3.4v-2.5h8.9v30.1h-8.9v-2.4ZM184,36c1.3-1.3,2-3,2-5s-.7-3.6-2-5c-1.3-1.3-2.9-2-4.9-2s-3.6.7-4.9,2c-1.3,1.3-2,3-2,5s.7,3.6,2,5,2.9,2,4.9,2,3.5-.7,4.9-2Z"/>
            <path class="logo-accent" d="M212.7,45.6v-9.9c3.3,1.4,6,2.1,8,2.1s3.6-1,3.6-3-.3-1.4-1-2.1c-.7-.6-1.8-1.5-3.3-2.5-1.6-1.1-3-2.1-4.1-3-1.1-.9-2-2.1-2.8-3.4-.8-1.4-1.2-2.9-1.2-4.7,0-3.9,1.2-6.8,3.7-8.8s5.7-3,9.7-3,4.4.3,6.5,1v9.5c-1.1-.7-2.2-1.1-3.3-1.5s-2.2-.5-3.2-.5-1.9.2-2.5.7c-.6.4-.9,1.1-.9,2s.3,1.3,1,2,1.7,1.5,3.1,2.6c1.7,1.2,3,2.3,4.1,3.4,1.1,1,2.1,2.3,2.9,3.8s1.2,3.1,1.2,5c0,4-1.2,6.9-3.6,8.8-2.4,1.9-5.7,2.9-9.8,2.9s-5.3-.4-8-1.2Z"/>
            <path class="logo-accent" d="M245.5,46l-11.4-30h9.7l6.6,18.9,6.6-18.9h9.8l-15,39.4h-10.2l4.1-9.3Z"/>
            <path class="logo-accent" d="M286.5,25.4c-.9-1-2-1.4-3.4-1.4s-2.9.5-3.9,1.5c-1,1-1.5,2.3-1.5,3.8v16.8h-9.3V16h9.3v2.5c1-1,2.1-1.9,3.4-2.4s2.7-.9,4.2-.9c2.3,0,4.4.6,6.1,1.7,1.8,1.2,3.2,2.8,4.1,4.9,1,2.1,1.5,4.5,1.5,7.1v17.2h-9.3v-16.9c0-1.6-.4-2.8-1.3-3.8Z"/>
            <path class="logo-accent" d="M307.2,44.8c-2.4-1.4-4.4-3.3-5.8-5.7-1.4-2.4-2.1-5.1-2.1-8s.7-5.6,2.1-8c1.4-2.4,3.4-4.4,5.8-5.8s5.1-2.1,8.1-2.1,2.6.2,4.1.5v10.1c-1.1-1-2.5-1.5-4.1-1.5s-3.5.7-4.8,2c-1.3,1.3-1.9,2.9-1.9,4.8s.6,3.5,1.9,4.8c1.3,1.3,2.9,2,4.8,2s2.9-.5,4.1-1.6v10.1c-1.2.4-2.6.5-4.1.5-2.9,0-5.6-.7-8.1-2.1Z"/>
          </g>
        </svg>
      </div>
      <p>Professional Color Management</p>
    </div>
    
    <div class="content">
      <h2>You're invited to join ${invitation.organizationName}!</h2>
      
      <div class="invitation-card">
        <p><strong>${invitation.inviterName}</strong> has invited you to join their ChromaSync team as a <strong>${invitation.role}</strong>.</p>
        <p>ChromaSync helps teams organize and sync their color palettes across projects.</p>
      </div>
      
      <div class="instructions">
        <h3>📋 How to Accept Your Invitation:</h3>
        
        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin: 16px 0;">
          <h4 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">🔑 Google Account Required</h4>
          <p style="margin: 0; color: #92400e; font-size: 14px;">
            <strong>Early Access:</strong> ChromaSync currently requires a Google account for authentication. Additional sign-in options will be available in future releases.
          </p>
        </div>
        
        <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 16px; margin: 16px 0;">
          <h4 style="margin: 0 0 12px 0; color: #0c4a6e; font-size: 16px;">✅ Easy Steps:</h4>
          <ol style="margin: 0; padding-left: 20px;">
            <li><strong>Open ChromaSync</strong> on your computer</li>
            <li><strong>Sign in with your Google account</strong> (required for early access)</li>
            <li>Go to <strong>Settings → Team</strong></li>
            <li>Click <strong>"Join Organization"</strong></li>
            <li><strong>Enter this invitation code:</strong></li>
          </ol>
        </div>
        
        <div class="token-display" style="position: relative;">
          <div style="position: absolute; top: -8px; right: 8px; background: #10b981; color: white; padding: 2px 8px; border-radius: 4px; font-size: 10px; text-transform: uppercase; font-weight: bold;">
            Copy This Code
          </div>
          ${invitation.token}
        </div>
        
        <div style="background: #fffbeb; border: 1px solid #f59e0b; border-radius: 8px; padding: 12px; margin: 16px 0; text-align: center;">
          <p style="margin: 0; color: #92400e; font-size: 14px;">
            💡 <strong>Tip:</strong> Select the code above, copy it (Ctrl+C or Cmd+C), then paste it into ChromaSync
          </p>
        </div>

        <div style="background: #fef2f2; border: 1px solid #f87171; border-radius: 8px; padding: 12px; margin: 16px 0;">
          <h4 style="margin: 0 0 8px 0; color: #991b1b; font-size: 14px;">🚨 Troubleshooting:</h4>
          <ul style="margin: 0; padding-left: 20px; color: #991b1b; font-size: 13px;">
            <li>If the email button doesn't work, always use the manual code above</li>
            <li>Make sure you're signed into the same Google account</li>
            <li>Check that ChromaSync is updated to the latest version</li>
            <li>If issues persist, forward this email to your IT admin</li>
          </ul>
        </div>
      </div>
      
      <p style="text-align: center; color: #64748b; font-size: 14px;">
        <em>⚠️ Email links may not work in all email clients. The manual invitation code above always works.</em>
      </p>
    </div>
    
    <div class="footer">
      <p><strong>⏰ This invitation expires on ${invitation.expiresAt.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })}.</strong></p>
      <p>Don't have ChromaSync yet? Download it at <a href="https://chromasync.app" style="color: #3b82f6;">chromasync.app</a></p>
      <p>Questions? Contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a></p>
      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 24px 0;">
      <p>© 2024 ChromaSync. All rights reserved.</p>
    </div>
  </div>
</body>
</html>`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}