/**
 * @file sync-concurrency-integration.test.ts
 * @description Integration tests for sync operations with CircuitBreakerAuthManager
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { CircuitBreakerAuthManager } from '../main/services/auth/circuit-breaker-auth-manager';
import { AuthenticationManager } from '../main/services/auth/authentication-manager';
import { AuthErrorRecoveryService } from '../main/services/auth/auth-error-recovery.service';
import { SessionManager } from '../main/services/auth/session-manager';

// Mock the dependencies
vi.mock('../main/services/auth/authentication-manager');
vi.mock('../main/services/auth/auth-error-recovery.service');
vi.mock('../main/services/auth/session-manager');
vi.mock('../main/utils/logger.service', () => ({
  LoggerFactory: {
    getInstance: vi.fn(() => ({
      createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn()
      }))
    }))
  },
  logPerformance: vi.fn(() => (_target: any, _propertyKey: string, descriptor: PropertyDescriptor) => descriptor),
  logErrors: vi.fn(() => (_target: any, _propertyKey: string, descriptor: PropertyDescriptor) => descriptor)
}));

describe('Sync Concurrency Integration', () => {
  let circuitBreakerAuthManager: CircuitBreakerAuthManager;
  let mockAuthenticationManager: any;
  let mockErrorRecovery: any;
  let mockSessionManager: any;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup authentication manager mock
    mockAuthenticationManager = {
      initiateOAuthFlow: vi.fn(),
      handleCallback: vi.fn(),
      refreshSession: vi.fn(),
      getCurrentSession: vi.fn(),
      getCurrentUser: vi.fn(),
      signOut: vi.fn(),
      getAuthState: vi.fn(),
      getHealthStatus: vi.fn(),
      cleanup: vi.fn()
    };

    (AuthenticationManager as Mock).mockImplementation(() => mockAuthenticationManager);

    // Setup error recovery mock
    mockErrorRecovery = {
      isAuthenticationBlockedResult: vi.fn(),
      executeWithRetry: vi.fn(),
      recordAuthAttempt: vi.fn(),
      getCircuitBreakerStatus: vi.fn(),
      attemptRecovery: vi.fn(),
      resetAuthenticationState: vi.fn(),
      configureCircuitBreaker: vi.fn(),
      configureRetry: vi.fn(),
      cleanup: vi.fn()
    };

    (AuthErrorRecoveryService as Mock).mockImplementation(() => mockErrorRecovery);

    // Setup session manager mock
    mockSessionManager = {
      startSession: vi.fn(),
      endSession: vi.fn(),
      updateActivity: vi.fn(),
      getSessionStatus: vi.fn(),
      configure: vi.fn(),
      cleanup: vi.fn()
    };

    (SessionManager as Mock).mockImplementation(() => mockSessionManager);

    // Create the auth manager instance
    circuitBreakerAuthManager = new CircuitBreakerAuthManager();
  });

  afterEach(() => {
    circuitBreakerAuthManager.cleanup();
  });

  describe('Authentication Health Monitoring', () => {
    it('should provide comprehensive health status for sync operations', () => {
      // Mock component health statuses
      mockAuthenticationManager.getHealthStatus.mockReturnValue({
        healthy: true,
        activeStates: 0,
        redirectServerRunning: false,
        callbackAttempts: 0
      });

      mockErrorRecovery.getCircuitBreakerStatus.mockReturnValue({
        isOpen: false,
        failureCount: 0,
        cooldownRemaining: 0,
        recentAttempts: 0
      });

      mockSessionManager.getSessionStatus.mockReturnValue({
        isActive: true,
        isExpired: false,
        lastActivityTime: Date.now()
      });

      const healthStatus = circuitBreakerAuthManager.getHealthStatus();

      expect(healthStatus).toHaveProperty('isHealthy');
      expect(healthStatus).toHaveProperty('circuitBreakerOpen');
      expect(healthStatus).toHaveProperty('sessionValid');
      expect(healthStatus).toHaveProperty('networkConnected');
      expect(healthStatus).toHaveProperty('activeRetries');
      expect(healthStatus).toHaveProperty('issues');

      // Health status should be available for sync validation
      expect(typeof healthStatus.isHealthy).toBe('boolean');
      expect(Array.isArray(healthStatus.issues)).toBe(true);
    });

    it('should detect authentication issues that would block sync', () => {
      // Mock unhealthy authentication state
      mockAuthenticationManager.getHealthStatus.mockReturnValue({
        healthy: false,
        activeStates: 5,
        redirectServerRunning: false,
        callbackAttempts: 3
      });

      mockErrorRecovery.getCircuitBreakerStatus.mockReturnValue({
        isOpen: true,
        failureCount: 3,
        cooldownRemaining: 60000,
        recentAttempts: 5
      });

      mockSessionManager.getSessionStatus.mockReturnValue({
        isActive: true,
        isExpired: true,
        lastActivityTime: Date.now() - 3600000
      });

      const healthStatus = circuitBreakerAuthManager.getHealthStatus();

      expect(healthStatus.isHealthy).toBe(false);
      expect(healthStatus.circuitBreakerOpen).toBe(true);
      expect(healthStatus.issues.length).toBeGreaterThan(0);
    });
  });

  describe('Session Validation for Sync', () => {
    it('should validate session before sync operations', async () => {
      const mockAuthManager = (circuitBreakerAuthManager as any).authManager;
      
      // Mock valid session
      const mockSession = {
        access_token: 'valid-token',
        refresh_token: 'refresh-token',
        expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
        user: { id: 'user-123', email: '<EMAIL>' }
      };

      mockAuthManager.getCurrentSession = vi.fn().mockResolvedValue(mockSession);
      mockAuthManager.getCurrentUser = vi.fn().mockResolvedValue(mockSession.user);

      // Get current session (should not trigger refresh)
      const session = await circuitBreakerAuthManager.getCurrentSession();
      const user = await circuitBreakerAuthManager.getCurrentUser();

      expect(session).toEqual(mockSession);
      expect(user).toEqual(mockSession.user);
    });

    it('should refresh expired session before sync operations', async () => {
      const mockAuthManager = (circuitBreakerAuthManager as any).authManager;
      const mockErrorRecovery = (circuitBreakerAuthManager as any).errorRecovery;
      
      // Mock expired session
      const expiredSession = {
        access_token: 'expired-token',
        refresh_token: 'refresh-token',
        expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes from now (near expiry)
        user: { id: 'user-123', email: '<EMAIL>' }
      };

      // Mock refreshed session
      const refreshedSession = {
        access_token: 'new-token',
        refresh_token: 'new-refresh-token',
        expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
        user: { id: 'user-123', email: '<EMAIL>' }
      };

      mockAuthManager.getCurrentSession = vi.fn().mockResolvedValue(expiredSession);
      
      // Mock successful refresh
      mockErrorRecovery.executeWithRetry = vi.fn().mockResolvedValue({
        success: true,
        session: refreshedSession
      });

      // Mock network connectivity check
      (circuitBreakerAuthManager as any).checkNetworkConnectivity = vi.fn().mockResolvedValue(true);

      // Get current session (should trigger refresh)
      const session = await circuitBreakerAuthManager.getCurrentSession();

      expect(session).toEqual(refreshedSession);
      expect(mockErrorRecovery.executeWithRetry).toHaveBeenCalledWith(
        expect.any(Function),
        'refreshSession'
      );
    });
  });

  describe('Network Interruption Handling', () => {
    it('should handle network interruptions during sync authentication', async () => {
      // Mock network connectivity failure
      (circuitBreakerAuthManager as any).checkNetworkConnectivity = vi.fn().mockResolvedValue(false);

      // Attempt session refresh with network issues
      const result = await circuitBreakerAuthManager.refreshSession();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network connectivity required for session refresh');
    });

    it('should recover from network interruptions', async () => {
      const mockErrorRecovery = (circuitBreakerAuthManager as any).errorRecovery;
      
      let networkCallCount = 0;
      (circuitBreakerAuthManager as any).checkNetworkConnectivity = vi.fn().mockImplementation(() => {
        networkCallCount++;
        return Promise.resolve(networkCallCount > 1); // Fail first, succeed second
      });

      // Mock successful refresh after network recovery
      const mockRefreshResult = {
        success: true,
        session: { access_token: 'new-token' }
      };
      mockErrorRecovery.executeWithRetry = vi.fn().mockResolvedValue(mockRefreshResult);

      // First refresh should fail
      let result = await circuitBreakerAuthManager.refreshSession();
      expect(result.success).toBe(false);

      // Second refresh should succeed
      result = await circuitBreakerAuthManager.refreshSession();
      expect(result).toEqual(mockRefreshResult);
    });
  });

  describe('Circuit Breaker Integration', () => {
    it('should prevent sync operations when circuit breaker is open', async () => {
      const mockErrorRecovery = (circuitBreakerAuthManager as any).errorRecovery;
      
      // Mock circuit breaker as open
      mockErrorRecovery.isAuthenticationBlockedResult = vi.fn().mockReturnValue({
        success: false,
        error: { message: 'Circuit breaker is open' }
      });

      // Attempt OAuth flow (should be blocked)
      await expect(circuitBreakerAuthManager.initiateOAuthFlow({ provider: 'google' }))
        .rejects.toThrow('Circuit breaker is open');
    });

    it('should allow sync operations when circuit breaker is closed', async () => {
      const mockErrorRecovery = (circuitBreakerAuthManager as any).errorRecovery;
      
      // Mock circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult = vi.fn().mockReturnValue({
        success: true,
        data: false
      });

      // Mock network connectivity
      (circuitBreakerAuthManager as any).checkNetworkConnectivity = vi.fn().mockResolvedValue(true);

      const mockResult = {
        authUrl: 'https://oauth.example.com',
        state: 'test-state',
        redirectHandler: null
      };

      mockErrorRecovery.executeWithRetry = vi.fn().mockResolvedValue(mockResult);

      // Attempt OAuth flow (should succeed)
      const result = await circuitBreakerAuthManager.initiateOAuthFlow({ provider: 'google' });
      expect(result).toEqual(mockResult);
    });
  });

  describe('Exponential Backoff for Sync Operations', () => {
    it('should apply exponential backoff for authentication retries', async () => {
      const mockErrorRecovery = (circuitBreakerAuthManager as any).errorRecovery;
      
      // Mock circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult = vi.fn().mockReturnValue({
        success: true,
        data: false
      });

      // Mock network connectivity
      (circuitBreakerAuthManager as any).checkNetworkConnectivity = vi.fn().mockResolvedValue(true);

      const mockResult = { success: true, session: { user: { id: '123' } } };
      mockErrorRecovery.executeWithRetry = vi.fn().mockResolvedValue(mockResult);

      // Handle callback (should use exponential backoff internally)
      const result = await circuitBreakerAuthManager.handleCallback('http://localhost:3000/auth/callback?code=test');

      expect(result).toEqual(mockResult);
      expect(mockErrorRecovery.executeWithRetry).toHaveBeenCalledWith(
        expect.any(Function),
        'handleCallback'
      );
    });
  });

  describe('Recovery Operations', () => {
    it('should perform comprehensive recovery for sync system', async () => {
      const mockErrorRecovery = (circuitBreakerAuthManager as any).errorRecovery;
      const mockSessionManager = (circuitBreakerAuthManager as any).sessionManager;
      
      const mockRecoveryResult = {
        success: true,
        message: 'Recovery completed',
        actionsPerformed: ['Reset circuit breaker', 'Cleared auth storage']
      };

      mockErrorRecovery.attemptRecovery = vi.fn().mockResolvedValue(mockRecoveryResult);

      const result = await circuitBreakerAuthManager.attemptRecovery();

      expect(result.success).toBe(true);
      expect(result.actionsPerformed).toContain('Reset session manager');
      expect(result.actionsPerformed).toContain('Reset network monitoring');
      expect(result.actionsPerformed).toContain('Reset retry tracking');

      expect(mockSessionManager.endSession).toHaveBeenCalled();
      expect(mockErrorRecovery.attemptRecovery).toHaveBeenCalled();
    });
  });

  describe('Configuration Integration', () => {
    it('should apply configuration to all authentication components', () => {
      const mockErrorRecovery = (circuitBreakerAuthManager as any).errorRecovery;
      const mockSessionManager = (circuitBreakerAuthManager as any).sessionManager;
      
      const config = {
        circuitBreaker: { failureThreshold: 5 },
        retry: { maxRetries: 5 },
        session: { sessionTimeoutHours: 8 },
        networkInterruption: { maxRetries: 3 },
        sessionValidation: { validationIntervalMs: 60000 }
      };

      circuitBreakerAuthManager.configure(config);

      expect(mockErrorRecovery.configureCircuitBreaker).toHaveBeenCalledWith(config.circuitBreaker);
      expect(mockErrorRecovery.configureRetry).toHaveBeenCalledWith(config.retry);
      expect(mockSessionManager.configure).toHaveBeenCalledWith(config.session);
    });
  });

  describe('Cleanup and Resource Management', () => {
    it('should cleanup all resources properly', () => {
      const mockAuthManager = (circuitBreakerAuthManager as any).authManager;
      const mockErrorRecovery = (circuitBreakerAuthManager as any).errorRecovery;
      const mockSessionManager = (circuitBreakerAuthManager as any).sessionManager;

      circuitBreakerAuthManager.cleanup();

      expect(mockAuthManager.cleanup).toHaveBeenCalled();
      expect(mockErrorRecovery.cleanup).toHaveBeenCalled();
      expect(mockSessionManager.cleanup).toHaveBeenCalled();
    });
  });
});