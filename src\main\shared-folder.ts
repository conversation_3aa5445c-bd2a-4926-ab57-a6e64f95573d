import { app } from 'electron';
import * as fs from 'fs/promises';
import * as path from 'path';
import { SharedFolderConfig, SharedFolderFile } from '../shared/types/shared-folder';
import { SecureFileOperations, validateFilePath } from './utils/secure-file-operations';
import { Result, success, failure } from '../shared/types/result.types';
import { isNotNullish, isNonEmptyString, isValidObject } from '../shared/types/type-guards';
import { safeAsyncOperation } from '../shared/utils/null-safety.utils';
import { ServiceResult } from '../shared/interfaces/service.interfaces';

/**
 * Type guard to validate SharedFolderFile objects
 */
function isValidSharedFolderFile(file: any): file is SharedFolderFile {
  return isValidObject(file) &&
         isNonEmptyString(file.name) &&
         isNonEmptyString(file.path) &&
         typeof file.isDirectory === 'boolean' &&
         (file.size === undefined || typeof file.size === 'number') &&
         (file.modified === undefined || file.modified instanceof Date);
}

export class SharedFolderManager {
  private folderPath: string;
  private config: SharedFolderConfig;
  private secureFileOps: SecureFileOperations;

  constructor(config: SharedFolderConfig) {
    this.config = config;
    this.folderPath = this.resolvePath();
    
    // Initialize secure file operations with shared folder specific config
    this.secureFileOps = new SecureFileOperations({
      allowedExtensions: ['.json', '.txt', '.csv', '.log'], // Shared folder specific extensions
      maxFileSize: 5 * 1024 * 1024, // 5MB limit for shared files
      allowedDirectories: [this.folderPath], // Only allow access within the shared folder
      blockDangerousPatterns: true
    });
    
    console.log(`[SharedFolderManager] Resolved path: ${this.folderPath}`);
  }

  private resolvePath(): string {
    // Use the basePath directly and append the specific folderName
    // Ensure basePath is valid before joining
    if (!this.config.basePath || typeof this.config.basePath !== 'string') {
      console.error('[SharedFolderManager] Invalid or missing basePath in configuration.');
      // Fallback to a default path in userData to prevent crashing, though this might not be desired behavior
      const fallbackPath = path.join(app.getPath('userData'), 'pantone_tracker_shared_fallback');
      console.warn(`[SharedFolderManager] Falling back to path: ${fallbackPath}`);
      return fallbackPath; 
    }
    // Construct the full path by joining the basePath and the folderName
    return path.join(this.config.basePath, this.config.folderName);
  }

  async ensureExists(): Promise<Result<boolean, string>> {
    try {
      await fs.mkdir(this.folderPath, { recursive: true });
      return success(true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Failed to create shared folder:', errorMessage);
      return failure(`Failed to create shared folder: ${errorMessage}`);
    }
  }

  getPath(): string {
    return this.folderPath;
  }

  /**
   * Check if a file exists in the shared folder
   */
  async fileExists(fileName: string): Promise<Result<boolean, string>> {
    if (!isNonEmptyString(fileName)) {
      return failure('Filename must be a non-empty string');
    }

    try {
      const validation = validateFilePath(fileName, this.folderPath);
      if (!validation.isValid) {
        return failure(`Invalid filename: ${validation.error}`);
      }

      const exists = this.secureFileOps.existsSecure(fileName, this.folderPath);
      return success(exists);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return failure(`Failed to check file existence: ${errorMessage}`);
    }
  }

  /**
   * Delete a file from the shared folder
   */
  async deleteFile(fileName: string): Promise<Result<boolean, string>> {
    if (!isNonEmptyString(fileName)) {
      return failure('Filename must be a non-empty string');
    }

    try {
      const validation = validateFilePath(fileName, this.folderPath);
      if (!validation.isValid) {
        return failure(`Invalid filename: ${validation.error}`);
      }

      const securePath = validation.sanitizedPath!;
      
      // Check if file exists first
      const existsResult = await this.fileExists(fileName);
      if (!existsResult.success) {
        return existsResult;
      }
      
      if (!existsResult.data) {
        return failure(`File does not exist: ${fileName}`);
      }

      await fs.unlink(securePath);
      return success(true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Failed to delete file ${fileName}:`, errorMessage);
      return failure(`Failed to delete file ${fileName}: ${errorMessage}`);
    }
  }

  /**
   * Get file info for a specific file
   */
  async getFileInfo(fileName: string): Promise<Result<SharedFolderFile, string>> {
    if (!isNonEmptyString(fileName)) {
      return failure('Filename must be a non-empty string');
    }

    try {
      const validation = validateFilePath(fileName, this.folderPath);
      if (!validation.isValid) {
        return failure(`Invalid filename: ${validation.error}`);
      }

      const filePath = validation.sanitizedPath!;
      const stats = await fs.stat(filePath);
      
      const fileInfo: SharedFolderFile = {
        name: fileName,
        path: filePath,
        isDirectory: stats.isDirectory(),
        size: stats.size,
        modified: stats.mtime
      };
      
      return isValidSharedFolderFile(fileInfo) 
        ? success(fileInfo) 
        : failure('Invalid file information retrieved');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return failure(`Failed to get file info for ${fileName}: ${errorMessage}`);
    }
  }

  async readFile(fileName: string): Promise<Result<string, string>> {
    // Input validation
    if (!isNonEmptyString(fileName)) {
      return failure('Filename must be a non-empty string');
    }

    try {
      // Validate the filename for security
      const validation = validateFilePath(fileName, this.folderPath);
      if (!validation.isValid) {
        return failure(`Invalid filename: ${validation.error}`);
      }

      // Log warnings if any
      if (validation.warnings && validation.warnings.length > 0) {
        console.warn(`[SharedFolderManager] File access warnings for ${fileName}:`, validation.warnings);
      }

      // Use secure file operations
      const content = await this.secureFileOps.readFileSecure(fileName, this.folderPath);
      return success(content);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Failed to read file ${fileName}:`, errorMessage);
      return failure(`Failed to read file ${fileName}: ${errorMessage}`);
    }
  }

  async writeFile(fileName: string, content: string): Promise<Result<boolean, string>> {
    // Input validation
    if (!isNonEmptyString(fileName)) {
      return failure('Filename must be a non-empty string');
    }
    
    if (typeof content !== 'string') {
      return failure('Content must be a string');
    }

    try {
      // Validate the filename for security
      const validation = validateFilePath(fileName, this.folderPath);
      if (!validation.isValid) {
        return failure(`Invalid filename for write: ${validation.error}`);
      }

      // Log warnings if any
      if (validation.warnings && validation.warnings.length > 0) {
        console.warn(`[SharedFolderManager] File write warnings for ${fileName}:`, validation.warnings);
      }

      // Use secure file operations
      await this.secureFileOps.writeFileSecure(fileName, content, this.folderPath);
      return success(true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Failed to write file ${fileName}:`, errorMessage);
      return failure(`Failed to write file ${fileName}: ${errorMessage}`);
    }
  }

  async listFiles(): Promise<Result<SharedFolderFile[], string>> {
    try {
      // Use secure directory listing
      const fileNames = await this.secureFileOps.listDirectorySecure('', this.folderPath);
      
      const fileDetails: (SharedFolderFile | null)[] = await Promise.all(
        fileNames.map(async (fileName): Promise<SharedFolderFile | null> => {
          // Validate each filename before processing
          const validation = validateFilePath(fileName, this.folderPath);
          if (!validation.isValid) {
            console.warn(`Skipping invalid file: ${fileName} - ${validation.error}`);
            return null;
          }

          const filePath = validation.sanitizedPath!;
          let stats;
          
          try {
            stats = await fs.stat(filePath);
          } catch (error) {
            console.error(`Failed to get stats for ${fileName}:`, error);
            // Return null for failed stats rather than fake data
            return null;
          }
          
          const fileInfo: SharedFolderFile = {
            name: fileName,
            path: filePath,
            isDirectory: typeof stats.isDirectory === 'function' ? stats.isDirectory() : false,
            size: stats.size,
            modified: stats.mtime
          };
          
          // Validate the constructed object
          return isValidSharedFolderFile(fileInfo) ? fileInfo : null;
        })
      );
      
      // Filter out null entries and ensure type safety
      const validFiles = fileDetails.filter(isNotNullish);
      return success(validFiles);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Failed to list files:', errorMessage);
      return failure(`Failed to list files: ${errorMessage}`);
    }
  }

  // Legacy compatibility methods - these preserve the old API for backward compatibility
  // but internally use the new Result-based methods

  /**
   * @deprecated Use ensureExists() which returns Result<boolean, string> instead
   */
  async ensureExistsLegacy(): Promise<boolean> {
    const result = await this.ensureExists();
    return result.success ? result.data : false;
  }

  /**
   * @deprecated Use readFile() which returns Result<string, string> instead
   */
  async readFileLegacy(fileName: string): Promise<string> {
    const result = await this.readFile(fileName);
    if (result.success) {
      return result.data;
    }
    throw new Error(result.error);
  }

  /**
   * @deprecated Use writeFile() which returns Result<boolean, string> instead
   */
  async writeFileLegacy(fileName: string, content: string): Promise<boolean> {
    const result = await this.writeFile(fileName, content);
    return result.success ? result.data : false;
  }

  /**
   * @deprecated Use listFiles() which returns Result<SharedFolderFile[], string> instead
   */
  async listFilesLegacy(): Promise<SharedFolderFile[]> {
    const result = await this.listFiles();
    return result.success ? result.data : [];
  }
} 