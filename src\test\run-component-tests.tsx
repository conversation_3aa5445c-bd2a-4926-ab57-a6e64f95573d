/**
 * @file run-component-tests.tsx
 * @description Helper for running component tests with token system
 */

import { describe, test } from 'vitest';
import { render } from '@testing-library/react';
import { ThemeContext } from '../renderer/context/ThemeContext';
import React from 'react';

export type TestCase = {
  name: string;
  component: React.ReactNode;
  expectations: Array<(_container: HTMLElement) => void>;
};

/**
 * Test runner for components using the token system
 * Token system is always enabled, tests both light and dark modes
 */
export function runComponentTests(
  componentName: string,
  testCases: TestCase[]
) {
  describe(`${componentName} Component`, () => {
    // Test with token system in light mode
    describe('with token system (light mode)', () => {
      testCases.forEach(({ name, component, expectations }) => {
        test(`${name}`, () => {
          const { container } = render(
            <ThemeContext.Provider
              value={{
                mode: 'light',
                effectiveTheme: 'light',
                setMode: () => {},
                isTransitioning: false,
              }}
            >
              {component}
            </ThemeContext.Provider>
          );

          expectations.forEach(expectFn => expectFn(container));
        });
      });
    });

    // Test with token system in dark mode
    describe('with token system (dark mode)', () => {
      testCases.forEach(({ name, component, expectations }) => {
        test(`${name}`, () => {
          const { container } = render(
            <ThemeContext.Provider
              value={{
                mode: 'dark',
                effectiveTheme: 'dark',
                setMode: () => {},
                isTransitioning: false,
              }}
            >
              {component}
            </ThemeContext.Provider>
          );

          expectations.forEach(expectFn => expectFn(container));
        });
      });
    });
  });
}
