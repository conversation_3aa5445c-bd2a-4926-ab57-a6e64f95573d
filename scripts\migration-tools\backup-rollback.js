/**
 * @file backup-rollback.js
 * @description Comprehensive database backup and rollback system for ChromaSync
 * 
 * This tool provides:
 * - Full database backups with schema and data
 * - Incremental backups between migration phases
 * - Point-in-time recovery with rollback capabilities
 * - Backup validation and integrity checks
 * - Automated backup scheduling and cleanup
 * - Compression and metadata tracking
 * - Partial restore options (schema-only, data-only, selective tables)
 * 
 * Usage Examples:
 * node backup-rollback.js create --type=full --name="pre-migration"
 * node backup-rollback.js create --type=incremental --phase="after-schema-update"
 * node backup-rollback.js restore --backup="backup-2025-01-01T10-00-00.db" --validate
 * node backup-rollback.js list --filter="full" --days=7
 * node backup-rollback.js cleanup --keep=5 --older-than=30
 * node backup-rollback.js validate --backup="backup-2025-01-01T10-00-00.db"
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const zlib = require('zlib');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

function getUserDataPath() {
  return path.join(require('os').homedir(), 'Library', 'Application Support', 'chroma-sync');
}

function getDbPath() {
  return path.join(getUserDataPath(), 'chromasync.db');
}

function getBackupDir() {
  const backupDir = path.join(getUserDataPath(), 'backups');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  return backupDir;
}

class BackupManager {
  constructor(dbPath, options = {}) {
    this.dbPath = dbPath;
    this.backupDir = getBackupDir();
    this.options = {
      compress: options.compress !== false, // Default to true
      validate: options.validate !== false, // Default to true
      maxRetries: options.maxRetries || 3,
      retentionDays: options.retentionDays || 30,
      maxBackups: options.maxBackups || 50,
      chunkSize: options.chunkSize || 1024 * 1024, // 1MB chunks
      ...options
    };
    this.db = null;
    this.backupHistory = this.loadBackupHistory();
  }

  connect() {
    try {
      this.db = new Database(this.dbPath, { readonly: true });
      console.log(`✅ Connected to database: ${this.dbPath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to connect to database: ${error.message}`);
      return false;
    }
  }

  disconnect() {
    if (this.db && this.db.open) {
      this.db.close();
      console.log('📴 Database connection closed');
    }
  }

  /**
   * Load backup history from metadata file
   */
  loadBackupHistory() {
    const historyPath = path.join(this.backupDir, 'backup-history.json');
    try {
      if (fs.existsSync(historyPath)) {
        const data = fs.readFileSync(historyPath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.warn(`⚠️  Could not load backup history: ${error.message}`);
    }
    return { backups: [], lastCleanup: null };
  }

  /**
   * Save backup history to metadata file
   */
  saveBackupHistory() {
    const historyPath = path.join(this.backupDir, 'backup-history.json');
    try {
      fs.writeFileSync(historyPath, JSON.stringify(this.backupHistory, null, 2));
    } catch (error) {
      console.error(`❌ Failed to save backup history: ${error.message}`);
    }
  }

  /**
   * Generate backup filename with timestamp
   */
  generateBackupName(type = 'full', name = null, phase = null) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const parts = ['backup', timestamp, type];
    
    if (name) {
      parts.push(name);
    }
    
    if (phase) {
      parts.push(phase);
    }
    
    return parts.join('-') + '.db';
  }

  /**
   * Calculate file checksum for integrity validation
   */
  calculateChecksum(filePath) {
    try {
      const data = fs.readFileSync(filePath);
      return crypto.createHash('sha256').update(data).digest('hex');
    } catch (error) {
      console.error(`❌ Failed to calculate checksum: ${error.message}`);
      return null;
    }
  }

  /**
   * Compress backup file
   */
  compressBackup(sourcePath, targetPath) {
    return new Promise((resolve, reject) => {
      const readStream = fs.createReadStream(sourcePath);
      const writeStream = fs.createWriteStream(targetPath);
      const gzip = zlib.createGzip({ level: 6 });

      readStream
        .pipe(gzip)
        .pipe(writeStream)
        .on('finish', () => {
          // Remove uncompressed file
          fs.unlinkSync(sourcePath);
          resolve();
        })
        .on('error', reject);
    });
  }

  /**
   * Decompress backup file
   */
  decompressBackup(sourcePath, targetPath) {
    return new Promise((resolve, reject) => {
      const readStream = fs.createReadStream(sourcePath);
      const writeStream = fs.createWriteStream(targetPath);
      const gunzip = zlib.createGunzip();

      readStream
        .pipe(gunzip)
        .pipe(writeStream)
        .on('finish', resolve)
        .on('error', reject);
    });
  }

  /**
   * Get database statistics
   */
  getDatabaseStats() {
    try {
      const tables = this.db.prepare(`
        SELECT name 
        FROM sqlite_master 
        WHERE type='table' 
        AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `).all();

      const stats = {
        tableCount: tables.length,
        totalRows: 0,
        tableStats: {},
        fileSize: fs.statSync(this.dbPath).size,
        lastModified: fs.statSync(this.dbPath).mtime
      };

      tables.forEach(table => {
        try {
          const count = this.db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
          stats.tableStats[table.name] = count.count;
          stats.totalRows += count.count;
        } catch (error) {
          stats.tableStats[table.name] = `Error: ${error.message}`;
        }
      });

      return stats;
    } catch (error) {
      console.error(`❌ Failed to get database stats: ${error.message}`);
      return null;
    }
  }

  /**
   * Create full database backup
   */
  async createFullBackup(name = null, phase = null) {
    console.log('🔄 Creating full database backup...');
    
    const backupName = this.generateBackupName('full', name, phase);
    const backupPath = path.join(this.backupDir, backupName);
    const tempPath = backupPath + '.tmp';
    
    try {
      // Create backup metadata
      const metadata = {
        id: uuidv4(),
        name: backupName,
        type: 'full',
        customName: name,
        phase: phase,
        timestamp: new Date().toISOString(),
        originalDb: this.dbPath,
        stats: this.getDatabaseStats(),
        compressed: this.options.compress,
        validated: false,
        checksum: null,
        size: 0
      };

      // Create backup using SQLite VACUUM INTO
      console.log('   📦 Creating database copy...');
      this.db.exec(`VACUUM INTO '${tempPath}'`);

      // Calculate checksum before compression
      const originalChecksum = this.calculateChecksum(tempPath);
      
      let finalPath = tempPath;
      
      // Compress if enabled
      if (this.options.compress) {
        console.log('   🗜️  Compressing backup...');
        const compressedPath = backupPath + '.gz';
        await this.compressBackup(tempPath, compressedPath);
        finalPath = compressedPath;
        metadata.compressed = true;
      } else {
        // Rename temp file to final name
        fs.renameSync(tempPath, backupPath);
        finalPath = backupPath;
      }

      // Update metadata
      metadata.size = fs.statSync(finalPath).size;
      metadata.checksum = this.calculateChecksum(finalPath);
      metadata.originalChecksum = originalChecksum;

      // Validate backup if enabled
      if (this.options.validate) {
        console.log('   ✅ Validating backup...');
        const isValid = await this.validateBackup(finalPath, metadata);
        metadata.validated = isValid;
        
        if (!isValid) {
          throw new Error('Backup validation failed');
        }
      }

      // Save metadata
      const metadataPath = finalPath + '.meta';
      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

      // Update backup history
      this.backupHistory.backups.push(metadata);
      this.saveBackupHistory();

      console.log(`✅ Full backup created: ${backupName}`);
      console.log(`   📁 Location: ${finalPath}`);
      console.log(`   📊 Size: ${this.formatBytes(metadata.size)}`);
      console.log(`   🔢 Checksum: ${metadata.checksum.substring(0, 16)}...`);
      
      return metadata;
      
    } catch (error) {
      // Cleanup on failure
      [tempPath, backupPath, backupPath + '.gz', backupPath + '.meta'].forEach(file => {
        if (fs.existsSync(file)) {
          try {
            fs.unlinkSync(file);
          } catch (e) {
            console.warn(`⚠️  Could not cleanup file: ${file}`);
          }
        }
      });
      
      console.error(`❌ Failed to create backup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create incremental backup (schema + specific tables)
   */
  async createIncrementalBackup(name = null, phase = null, tables = null) {
    console.log('🔄 Creating incremental backup...');
    
    const backupName = this.generateBackupName('incremental', name, phase);
    const backupPath = path.join(this.backupDir, backupName);
    const tempPath = backupPath + '.tmp';
    
    try {
      // Create new database file
      const incrementalDb = new Database(tempPath);
      
      // Copy schema
      console.log('   📋 Copying schema...');
      const schema = this.db.prepare(`
        SELECT sql FROM sqlite_master 
        WHERE type IN ('table', 'index', 'trigger', 'view') 
        AND name NOT LIKE 'sqlite_%'
        ORDER BY type, name
      `).all();
      
      schema.forEach(item => {
        if (item.sql) {
          incrementalDb.exec(item.sql);
        }
      });

      // Copy table data
      const tablesToBackup = tables || this.getModifiedTables();
      console.log(`   📦 Copying ${tablesToBackup.length} tables...`);
      
      tablesToBackup.forEach(tableName => {
        console.log(`     - ${tableName}`);
        const data = this.db.prepare(`SELECT * FROM ${tableName}`).all();
        
        if (data.length > 0) {
          // Get column names
          const columns = this.db.prepare(`PRAGMA table_info(${tableName})`).all();
          const columnNames = columns.map(col => col.name);
          
          // Create insert statement
          const placeholders = columnNames.map(() => '?').join(', ');
          const insertStmt = incrementalDb.prepare(`
            INSERT INTO ${tableName} (${columnNames.join(', ')}) 
            VALUES (${placeholders})
          `);
          
          // Insert data
          data.forEach(row => {
            const values = columnNames.map(col => row[col]);
            insertStmt.run(values);
          });
        }
      });

      incrementalDb.close();

      // Create metadata
      const metadata = {
        id: uuidv4(),
        name: backupName,
        type: 'incremental',
        customName: name,
        phase: phase,
        timestamp: new Date().toISOString(),
        originalDb: this.dbPath,
        tables: tablesToBackup,
        compressed: this.options.compress,
        validated: false,
        checksum: null,
        size: 0
      };

      // Calculate checksum before compression
      const originalChecksum = this.calculateChecksum(tempPath);
      
      let finalPath = tempPath;
      
      // Compress if enabled
      if (this.options.compress) {
        console.log('   🗜️  Compressing backup...');
        const compressedPath = backupPath + '.gz';
        await this.compressBackup(tempPath, compressedPath);
        finalPath = compressedPath;
        metadata.compressed = true;
      } else {
        fs.renameSync(tempPath, backupPath);
        finalPath = backupPath;
      }

      // Update metadata
      metadata.size = fs.statSync(finalPath).size;
      metadata.checksum = this.calculateChecksum(finalPath);
      metadata.originalChecksum = originalChecksum;

      // Validate backup if enabled
      if (this.options.validate) {
        console.log('   ✅ Validating backup...');
        const isValid = await this.validateBackup(finalPath, metadata);
        metadata.validated = isValid;
        
        if (!isValid) {
          throw new Error('Backup validation failed');
        }
      }

      // Save metadata
      const metadataPath = finalPath + '.meta';
      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

      // Update backup history
      this.backupHistory.backups.push(metadata);
      this.saveBackupHistory();

      console.log(`✅ Incremental backup created: ${backupName}`);
      console.log(`   📁 Location: ${finalPath}`);
      console.log(`   📊 Size: ${this.formatBytes(metadata.size)}`);
      console.log(`   📋 Tables: ${tablesToBackup.join(', ')}`);
      
      return metadata;
      
    } catch (error) {
      // Cleanup on failure
      [tempPath, backupPath, backupPath + '.gz', backupPath + '.meta'].forEach(file => {
        if (fs.existsSync(file)) {
          try {
            fs.unlinkSync(file);
          } catch (e) {
            console.warn(`⚠️  Could not cleanup file: ${file}`);
          }
        }
      });
      
      console.error(`❌ Failed to create incremental backup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get list of tables that have been modified recently
   */
  getModifiedTables() {
    // For now, return all tables. In a production system, you might
    // track modifications using timestamps or change logs
    try {
      const tables = this.db.prepare(`
        SELECT name 
        FROM sqlite_master 
        WHERE type='table' 
        AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `).all();
      
      return tables.map(table => table.name);
    } catch (error) {
      console.error(`❌ Failed to get modified tables: ${error.message}`);
      return [];
    }
  }

  /**
   * Validate backup integrity
   */
  async validateBackup(backupPath, metadata = null) {
    console.log('🔍 Validating backup integrity...');
    
    try {
      // Load metadata if not provided
      if (!metadata) {
        const metadataPath = backupPath + '.meta';
        if (fs.existsSync(metadataPath)) {
          metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
        }
      }

      // Check file exists and has content
      if (!fs.existsSync(backupPath)) {
        console.error('❌ Backup file does not exist');
        return false;
      }

      const fileSize = fs.statSync(backupPath).size;
      if (fileSize === 0) {
        console.error('❌ Backup file is empty');
        return false;
      }

      // Verify checksum
      console.log('   🔢 Verifying checksum...');
      const currentChecksum = this.calculateChecksum(backupPath);
      if (metadata && metadata.checksum && currentChecksum !== metadata.checksum) {
        console.error('❌ Checksum mismatch - backup may be corrupted');
        console.error(`   Expected: ${metadata.checksum}`);
        console.error(`   Actual:   ${currentChecksum}`);
        return false;
      }

      // Decompress if needed for validation
      let dbPath = backupPath;
      let tempPath = null;
      
      if (metadata && metadata.compressed) {
        console.log('   🗜️  Decompressing for validation...');
        tempPath = backupPath + '.validation.tmp';
        await this.decompressBackup(backupPath, tempPath);
        dbPath = tempPath;
      }

      // Validate database structure
      console.log('   🔍 Validating database structure...');
      const testDb = new Database(dbPath, { readonly: true });
      
      try {
        // Check database integrity
        const integrityResult = testDb.pragma('integrity_check');
        
        let resultText;
        if (Array.isArray(integrityResult) && integrityResult.length > 0) {
          // Handle array of objects from better-sqlite3
          const firstResult = integrityResult[0];
          if (typeof firstResult === 'object' && firstResult.integrity_check) {
            resultText = firstResult.integrity_check;
          } else {
            resultText = firstResult;
          }
        } else if (typeof integrityResult === 'object' && integrityResult !== null) {
          // Handle single object result
          resultText = integrityResult.integrity_check || integrityResult.result || 'unknown object';
        } else {
          resultText = integrityResult;
        }
        
        if (resultText !== 'ok') {
          console.error('❌ Database integrity check failed');
          console.error(`   Result: ${JSON.stringify(integrityResult)}`);
          return false;
        }

        // Check if tables exist
        const tables = testDb.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name NOT LIKE 'sqlite_%'
        `).all();
        
        if (tables.length === 0) {
          console.error('❌ No tables found in backup');
          return false;
        }

        // Quick row count check
        let totalRows = 0;
        tables.forEach(table => {
          try {
            const count = testDb.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
            totalRows += count.count;
          } catch (error) {
            console.warn(`⚠️  Could not count rows in ${table.name}: ${error.message}`);
          }
        });

        console.log(`   📊 Found ${tables.length} tables with ${totalRows} total rows`);
        
        testDb.close();
        
        // Cleanup temp file
        if (tempPath && fs.existsSync(tempPath)) {
          fs.unlinkSync(tempPath);
        }

        console.log('✅ Backup validation successful');
        return true;
        
      } catch (error) {
        testDb.close();
        
        // Cleanup temp file
        if (tempPath && fs.existsSync(tempPath)) {
          fs.unlinkSync(tempPath);
        }
        
        console.error(`❌ Database validation failed: ${error.message}`);
        return false;
      }
      
    } catch (error) {
      console.error(`❌ Backup validation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Restore database from backup
   */
  async restoreDatabase(backupPath, options = {}) {
    console.log(`🔄 Restoring database from backup: ${path.basename(backupPath)}`);
    
    const {
      createBackupBeforeRestore = true,
      validateBeforeRestore = true,
      validateAfterRestore = true,
      restoreType = 'full', // 'full', 'schema-only', 'data-only', 'selective'
      tables = null,
      force = false
    } = options;

    try {
      // Load backup metadata
      const metadataPath = backupPath + '.meta';
      let metadata = null;
      
      if (fs.existsSync(metadataPath)) {
        metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      }

      // Validate backup before restore
      if (validateBeforeRestore) {
        console.log('🔍 Validating backup before restore...');
        const isValid = await this.validateBackup(backupPath, metadata);
        if (!isValid && !force) {
          throw new Error('Backup validation failed. Use --force to override.');
        }
      }

      // Create backup of current database
      if (createBackupBeforeRestore) {
        console.log('💾 Creating backup of current database...');
        await this.createFullBackup('pre-restore', 'before-restore');
      }

      // Decompress backup if needed
      let sourceDbPath = backupPath;
      let tempPath = null;
      
      if (metadata && metadata.compressed) {
        console.log('🗜️  Decompressing backup...');
        tempPath = backupPath + '.restore.tmp';
        await this.decompressBackup(backupPath, tempPath);
        sourceDbPath = tempPath;
      }

      // Close current database connection
      this.disconnect();

      // Perform restore based on type
      if (restoreType === 'full') {
        console.log('🔄 Performing full restore...');
        
        // Create backup of current database
        const currentDbBackup = this.dbPath + '.pre-restore';
        if (fs.existsSync(this.dbPath)) {
          fs.copyFileSync(this.dbPath, currentDbBackup);
        }

        // Replace current database
        fs.copyFileSync(sourceDbPath, this.dbPath);
        
        console.log('✅ Full restore completed');
        
      } else if (restoreType === 'schema-only') {
        console.log('🔄 Performing schema-only restore...');
        
        const sourceDb = new Database(sourceDbPath, { readonly: true });
        const targetDb = new Database(this.dbPath);
        
        // Drop existing tables
        const existingTables = targetDb.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name NOT LIKE 'sqlite_%'
        `).all();
        
        existingTables.forEach(table => {
          targetDb.exec(`DROP TABLE IF EXISTS ${table.name}`);
        });
        
        // Copy schema
        const schema = sourceDb.prepare(`
          SELECT sql FROM sqlite_master 
          WHERE type IN ('table', 'index', 'trigger', 'view') 
          AND name NOT LIKE 'sqlite_%'
          ORDER BY type, name
        `).all();
        
        schema.forEach(item => {
          if (item.sql) {
            targetDb.exec(item.sql);
          }
        });
        
        sourceDb.close();
        targetDb.close();
        
        console.log('✅ Schema-only restore completed');
        
      } else if (restoreType === 'data-only') {
        console.log('🔄 Performing data-only restore...');
        
        const sourceDb = new Database(sourceDbPath, { readonly: true });
        const targetDb = new Database(this.dbPath);
        
        // Get tables to restore
        const tablesToRestore = tables || sourceDb.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name NOT LIKE 'sqlite_%'
        `).all().map(t => t.name);
        
        // Clear existing data and restore
        tablesToRestore.forEach(tableName => {
          console.log(`   📦 Restoring data for ${tableName}...`);
          
          try {
            // Clear existing data
            targetDb.exec(`DELETE FROM ${tableName}`);
            
            // Copy data
            const data = sourceDb.prepare(`SELECT * FROM ${tableName}`).all();
            
            if (data.length > 0) {
              const columns = sourceDb.prepare(`PRAGMA table_info(${tableName})`).all();
              const columnNames = columns.map(col => col.name);
              
              const placeholders = columnNames.map(() => '?').join(', ');
              const insertStmt = targetDb.prepare(`
                INSERT INTO ${tableName} (${columnNames.join(', ')}) 
                VALUES (${placeholders})
              `);
              
              data.forEach(row => {
                const values = columnNames.map(col => row[col]);
                insertStmt.run(values);
              });
            }
          } catch (error) {
            console.warn(`⚠️  Could not restore ${tableName}: ${error.message}`);
          }
        });
        
        sourceDb.close();
        targetDb.close();
        
        console.log('✅ Data-only restore completed');
        
      } else if (restoreType === 'selective') {
        console.log('🔄 Performing selective restore...');
        
        if (!tables || tables.length === 0) {
          throw new Error('Selective restore requires specific tables to be specified');
        }
        
        const sourceDb = new Database(sourceDbPath, { readonly: true });
        const targetDb = new Database(this.dbPath);
        
        tables.forEach(tableName => {
          console.log(`   📦 Restoring ${tableName}...`);
          
          try {
            // Get table schema
            const schema = sourceDb.prepare(`
              SELECT sql FROM sqlite_master 
              WHERE type='table' AND name = ?
            `).get(tableName);
            
            if (schema) {
              // Drop and recreate table
              targetDb.exec(`DROP TABLE IF EXISTS ${tableName}`);
              targetDb.exec(schema.sql);
              
              // Copy data
              const data = sourceDb.prepare(`SELECT * FROM ${tableName}`).all();
              
              if (data.length > 0) {
                const columns = sourceDb.prepare(`PRAGMA table_info(${tableName})`).all();
                const columnNames = columns.map(col => col.name);
                
                const placeholders = columnNames.map(() => '?').join(', ');
                const insertStmt = targetDb.prepare(`
                  INSERT INTO ${tableName} (${columnNames.join(', ')}) 
                  VALUES (${placeholders})
                `);
                
                data.forEach(row => {
                  const values = columnNames.map(col => row[col]);
                  insertStmt.run(values);
                });
              }
            }
          } catch (error) {
            console.warn(`⚠️  Could not restore ${tableName}: ${error.message}`);
          }
        });
        
        sourceDb.close();
        targetDb.close();
        
        console.log('✅ Selective restore completed');
      }

      // Cleanup temp file
      if (tempPath && fs.existsSync(tempPath)) {
        fs.unlinkSync(tempPath);
      }

      // Reconnect to database
      this.connect();

      // Validate after restore
      if (validateAfterRestore) {
        console.log('🔍 Validating database after restore...');
        const testDb = new Database(this.dbPath, { readonly: true });
        
        try {
          const integrityResult = testDb.pragma('integrity_check');
          
          let resultText;
          if (Array.isArray(integrityResult) && integrityResult.length > 0) {
            // Handle array of objects from better-sqlite3
            const firstResult = integrityResult[0];
            if (typeof firstResult === 'object' && firstResult.integrity_check) {
              resultText = firstResult.integrity_check;
            } else {
              resultText = firstResult;
            }
          } else if (typeof integrityResult === 'object' && integrityResult !== null) {
            // Handle single object result
            resultText = integrityResult.integrity_check || integrityResult.result || 'unknown object';
          } else {
            resultText = integrityResult;
          }
          
          if (resultText !== 'ok') {
            console.error('❌ Database integrity check failed after restore');
            console.error(`   Result: ${JSON.stringify(integrityResult)}`);
            return false;
          }
          
          testDb.close();
          console.log('✅ Database validation successful after restore');
        } catch (error) {
          testDb.close();
          console.error(`❌ Database validation failed after restore: ${error.message}`);
          return false;
        }
      }

      console.log('🎉 Database restore completed successfully');
      return true;
      
    } catch (error) {
      console.error(`❌ Database restore failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * List available backups
   */
  listBackups(options = {}) {
    const {
      type = null,
      days = null,
      limit = null,
      showDetails = false
    } = options;

    console.log('📋 Available Backups:');
    console.log('====================\n');

    let backups = this.backupHistory.backups.slice();

    // Filter by type
    if (type) {
      backups = backups.filter(backup => backup.type === type);
    }

    // Filter by days
    if (days) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      backups = backups.filter(backup => new Date(backup.timestamp) >= cutoffDate);
    }

    // Sort by timestamp (newest first)
    backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Limit results
    if (limit) {
      backups = backups.slice(0, limit);
    }

    if (backups.length === 0) {
      console.log('No backups found matching criteria');
      return [];
    }

    backups.forEach((backup, index) => {
      const age = this.formatAge(backup.timestamp);
      const size = this.formatBytes(backup.size);
      const status = backup.validated ? '✅' : '❓';
      
      console.log(`${index + 1}. ${backup.name} ${status}`);
      console.log(`   📅 Created: ${backup.timestamp} (${age} ago)`);
      console.log(`   📊 Size: ${size} | Type: ${backup.type}`);
      
      if (backup.customName) {
        console.log(`   🏷️  Name: ${backup.customName}`);
      }
      
      if (backup.phase) {
        console.log(`   📍 Phase: ${backup.phase}`);
      }
      
      if (showDetails) {
        console.log(`   🔢 Checksum: ${backup.checksum ? backup.checksum.substring(0, 16) + '...' : 'N/A'}`);
        console.log(`   📦 Compressed: ${backup.compressed ? 'Yes' : 'No'}`);
        
        if (backup.stats) {
          console.log(`   📋 Tables: ${backup.stats.tableCount} | Rows: ${backup.stats.totalRows}`);
        }
        
        if (backup.tables) {
          console.log(`   📋 Tables: ${backup.tables.join(', ')}`);
        }
      }
      
      console.log();
    });

    return backups;
  }

  /**
   * Cleanup old backups
   */
  async cleanupBackups(options = {}) {
    const {
      keepCount = this.options.maxBackups,
      olderThanDays = this.options.retentionDays,
      type = null,
      dryRun = false
    } = options;

    console.log('🧹 Cleaning up old backups...');
    console.log(`   📊 Keep: ${keepCount} backups`);
    console.log(`   📅 Older than: ${olderThanDays} days`);
    console.log(`   🔍 Type filter: ${type || 'all'}`);
    console.log(`   🧪 Dry run: ${dryRun ? 'Yes' : 'No'}`);
    console.log();

    let backups = this.backupHistory.backups.slice();

    // Filter by type if specified
    if (type) {
      backups = backups.filter(backup => backup.type === type);
    }

    // Sort by timestamp (newest first)
    backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    const toDelete = [];
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    // Mark backups for deletion based on age
    backups.forEach((backup, index) => {
      const backupDate = new Date(backup.timestamp);
      const shouldDelete = (index >= keepCount) || (backupDate < cutoffDate);
      
      if (shouldDelete) {
        toDelete.push(backup);
      }
    });

    if (toDelete.length === 0) {
      console.log('✅ No backups need to be cleaned up');
      return;
    }

    console.log(`🗑️  Found ${toDelete.length} backups to delete:`);
    toDelete.forEach(backup => {
      const age = this.formatAge(backup.timestamp);
      console.log(`   - ${backup.name} (${age} ago)`);
    });

    if (dryRun) {
      console.log('\n🧪 Dry run complete - no files were deleted');
      return;
    }

    // Delete backup files
    let deletedCount = 0;
    for (const backup of toDelete) {
      try {
        const backupPath = path.join(this.backupDir, backup.name);
        const compressedPath = backupPath + '.gz';
        const metadataPath = backupPath + '.meta';

        // Delete main backup file
        if (fs.existsSync(backupPath)) {
          fs.unlinkSync(backupPath);
        }
        
        // Delete compressed version
        if (fs.existsSync(compressedPath)) {
          fs.unlinkSync(compressedPath);
        }
        
        // Delete metadata
        if (fs.existsSync(metadataPath)) {
          fs.unlinkSync(metadataPath);
        }

        deletedCount++;
        
      } catch (error) {
        console.warn(`⚠️  Could not delete ${backup.name}: ${error.message}`);
      }
    }

    // Remove from history
    this.backupHistory.backups = this.backupHistory.backups.filter(
      backup => !toDelete.some(del => del.id === backup.id)
    );
    
    this.backupHistory.lastCleanup = new Date().toISOString();
    this.saveBackupHistory();

    console.log(`✅ Cleanup completed: ${deletedCount} backups deleted`);
  }

  /**
   * Generate comprehensive backup report
   */
  generateReport() {
    console.log('\n📊 ChromaSync Backup System Report');
    console.log('==================================\n');

    const backups = this.backupHistory.backups;
    const totalBackups = backups.length;
    const fullBackups = backups.filter(b => b.type === 'full').length;
    const incrementalBackups = backups.filter(b => b.type === 'incremental').length;
    const totalSize = backups.reduce((sum, b) => sum + (b.size || 0), 0);
    const validatedBackups = backups.filter(b => b.validated).length;

    // Basic statistics
    console.log('📈 Backup Statistics:');
    console.log(`   Total Backups: ${totalBackups}`);
    console.log(`   Full Backups: ${fullBackups}`);
    console.log(`   Incremental Backups: ${incrementalBackups}`);
    console.log(`   Total Size: ${this.formatBytes(totalSize)}`);
    console.log(`   Validated Backups: ${validatedBackups}/${totalBackups}`);
    console.log(`   Backup Directory: ${this.backupDir}`);
    console.log();

    // Recent backups
    if (backups.length > 0) {
      console.log('🕐 Recent Backups:');
      const recent = backups
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 5);
      
      recent.forEach(backup => {
        const age = this.formatAge(backup.timestamp);
        const size = this.formatBytes(backup.size);
        const status = backup.validated ? '✅' : '❓';
        
        console.log(`   ${status} ${backup.name} (${age} ago, ${size})`);
      });
      console.log();
    }

    // Backup health
    console.log('🏥 Backup Health:');
    const healthScore = totalBackups > 0 ? (validatedBackups / totalBackups) * 100 : 0;
    console.log(`   Health Score: ${healthScore.toFixed(1)}%`);
    
    if (healthScore < 100) {
      console.log(`   ⚠️  ${totalBackups - validatedBackups} backups need validation`);
    }
    
    if (totalBackups === 0) {
      console.log('   ⚠️  No backups found - create your first backup!');
    }
    
    console.log();

    // Recommendations
    console.log('💡 Recommendations:');
    
    if (totalBackups === 0) {
      console.log('   1. Create your first full backup with: node backup-rollback.js create --type=full');
    }
    
    if (fullBackups === 0 && incrementalBackups > 0) {
      console.log('   1. Create a full backup as base for incremental backups');
    }
    
    if (validatedBackups < totalBackups) {
      console.log('   2. Validate existing backups to ensure they are recoverable');
    }
    
    if (totalBackups > this.options.maxBackups) {
      console.log('   3. Run cleanup to remove old backups and free disk space');
    }
    
    const oldestBackup = backups.reduce((oldest, backup) => {
      const backupDate = new Date(backup.timestamp);
      return backupDate < new Date(oldest.timestamp) ? backup : oldest;
    }, backups[0]);
    
    if (oldestBackup) {
      const oldestAge = Math.floor((Date.now() - new Date(oldestBackup.timestamp).getTime()) / (1000 * 60 * 60 * 24));
      if (oldestAge > this.options.retentionDays) {
        console.log(`   4. Consider cleanup - oldest backup is ${oldestAge} days old`);
      }
    }
    
    console.log();

    return {
      totalBackups,
      fullBackups,
      incrementalBackups,
      totalSize,
      validatedBackups,
      healthScore,
      backupDir: this.backupDir,
      recentBackups: backups.slice(-5),
      recommendations: []
    };
  }

  /**
   * Schedule automatic backups
   */
  scheduleAutomaticBackups(options = {}) {
    const {
      fullBackupInterval = 24 * 60 * 60 * 1000, // 24 hours
      incrementalBackupInterval = 4 * 60 * 60 * 1000, // 4 hours
      cleanupInterval = 7 * 24 * 60 * 60 * 1000, // 7 days
      enabled = true
    } = options;

    if (!enabled) {
      console.log('📅 Automatic backups disabled');
      return;
    }

    console.log('📅 Scheduling automatic backups...');
    console.log(`   Full backup every: ${this.formatDuration(fullBackupInterval)}`);
    console.log(`   Incremental backup every: ${this.formatDuration(incrementalBackupInterval)}`);
    console.log(`   Cleanup every: ${this.formatDuration(cleanupInterval)}`);

    // Schedule full backups
    setInterval(async () => {
      try {
        console.log('🔄 Scheduled full backup starting...');
        await this.createFullBackup('auto', 'scheduled');
        console.log('✅ Scheduled full backup completed');
      } catch (error) {
        console.error(`❌ Scheduled full backup failed: ${error.message}`);
      }
    }, fullBackupInterval);

    // Schedule incremental backups
    setInterval(async () => {
      try {
        console.log('🔄 Scheduled incremental backup starting...');
        await this.createIncrementalBackup('auto', 'scheduled');
        console.log('✅ Scheduled incremental backup completed');
      } catch (error) {
        console.error(`❌ Scheduled incremental backup failed: ${error.message}`);
      }
    }, incrementalBackupInterval);

    // Schedule cleanup
    setInterval(async () => {
      try {
        console.log('🧹 Scheduled cleanup starting...');
        await this.cleanupBackups({ keepCount: this.options.maxBackups });
        console.log('✅ Scheduled cleanup completed');
      } catch (error) {
        console.error(`❌ Scheduled cleanup failed: ${error.message}`);
      }
    }, cleanupInterval);

    console.log('✅ Automatic backup scheduling enabled');
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Format age from timestamp
   */
  formatAge(timestamp) {
    const now = new Date();
    const then = new Date(timestamp);
    const diffMs = now - then;
    
    const minutes = Math.floor(diffMs / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  }

  /**
   * Format duration in milliseconds
   */
  formatDuration(ms) {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];
  
  // Parse arguments
  const options = {};
  const flags = {};
  
  args.forEach(arg => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.substring(2).split('=');
      if (value !== undefined) {
        options[key] = value;
      } else {
        flags[key] = true;
      }
    }
  });

  const dbPath = getDbPath();
  const backupManager = new BackupManager(dbPath, {
    compress: !flags['no-compress'],
    validate: !flags['no-validate']
  });

  async function runCommand() {
    try {
      switch (command) {
        case 'create':
          if (!backupManager.connect()) {
            process.exit(1);
          }
          
          const type = options.type || 'full';
          const name = options.name || null;
          const phase = options.phase || null;
          const tables = options.tables ? options.tables.split(',') : null;
          
          if (type === 'full') {
            await backupManager.createFullBackup(name, phase);
          } else if (type === 'incremental') {
            await backupManager.createIncrementalBackup(name, phase, tables);
          } else {
            console.error('❌ Invalid backup type. Use "full" or "incremental"');
            process.exit(1);
          }
          break;

        case 'restore':
          const backupName = options.backup;
          if (!backupName) {
            console.error('❌ Backup name is required for restore');
            process.exit(1);
          }
          
          const backupPath = path.join(getBackupDir(), backupName);
          if (!fs.existsSync(backupPath) && !fs.existsSync(backupPath + '.gz')) {
            console.error(`❌ Backup not found: ${backupName}`);
            process.exit(1);
          }
          
          const finalBackupPath = fs.existsSync(backupPath + '.gz') ? backupPath + '.gz' : backupPath;
          
          if (!backupManager.connect()) {
            process.exit(1);
          }
          
          await backupManager.restoreDatabase(finalBackupPath, {
            validateBeforeRestore: !flags['no-validate'],
            validateAfterRestore: !flags['no-validate'],
            createBackupBeforeRestore: !flags['no-backup'],
            restoreType: options['restore-type'] || 'full',
            tables: options.tables ? options.tables.split(',') : null,
            force: flags.force
          });
          break;

        case 'list':
          const listOptions = {
            type: options.type,
            days: options.days ? parseInt(options.days) : null,
            limit: options.limit ? parseInt(options.limit) : null,
            showDetails: flags.details || flags.verbose
          };
          
          backupManager.listBackups(listOptions);
          break;

        case 'validate':
          const validateBackupName = options.backup;
          if (!validateBackupName) {
            console.error('❌ Backup name is required for validation');
            process.exit(1);
          }
          
          const validateBackupPath = path.join(getBackupDir(), validateBackupName);
          if (!fs.existsSync(validateBackupPath) && !fs.existsSync(validateBackupPath + '.gz')) {
            console.error(`❌ Backup not found: ${validateBackupName}`);
            process.exit(1);
          }
          
          const finalValidateBackupPath = fs.existsSync(validateBackupPath + '.gz') ? validateBackupPath + '.gz' : validateBackupPath;
          
          const isValid = await backupManager.validateBackup(finalValidateBackupPath);
          process.exit(isValid ? 0 : 1);
          break;

        case 'cleanup':
          const cleanupOptions = {
            keepCount: options.keep ? parseInt(options.keep) : undefined,
            olderThanDays: options['older-than'] ? parseInt(options['older-than']) : undefined,
            type: options.type,
            dryRun: flags['dry-run']
          };
          
          await backupManager.cleanupBackups(cleanupOptions);
          break;

        case 'report':
          backupManager.generateReport();
          break;

        case 'schedule':
          const scheduleOptions = {
            fullBackupInterval: options['full-interval'] ? parseInt(options['full-interval']) * 1000 : undefined,
            incrementalBackupInterval: options['incremental-interval'] ? parseInt(options['incremental-interval']) * 1000 : undefined,
            cleanupInterval: options['cleanup-interval'] ? parseInt(options['cleanup-interval']) * 1000 : undefined,
            enabled: !flags.disable
          };
          
          if (!backupManager.connect()) {
            process.exit(1);
          }
          
          backupManager.scheduleAutomaticBackups(scheduleOptions);
          
          // Keep process running
          console.log('Press Ctrl+C to stop automatic backups');
          process.on('SIGINT', () => {
            console.log('\n📴 Stopping automatic backups...');
            backupManager.disconnect();
            process.exit(0);
          });
          break;

        default:
          console.log(`
ChromaSync Database Backup and Rollback Tool
===========================================

Usage: node backup-rollback.js <command> [options]

Commands:
  create       Create a new backup
  restore      Restore from a backup
  list         List available backups
  validate     Validate a backup
  cleanup      Clean up old backups
  report       Generate backup system report
  schedule     Schedule automatic backups

Create Options:
  --type=<type>        Backup type: full, incremental (default: full)
  --name=<name>        Custom backup name
  --phase=<phase>      Migration phase identifier
  --tables=<tables>    Comma-separated table names (for incremental)
  --no-compress        Skip compression
  --no-validate        Skip validation

Restore Options:
  --backup=<name>      Backup file name to restore from
  --restore-type=<type> Restore type: full, schema-only, data-only, selective
  --tables=<tables>    Tables to restore (for selective restore)
  --no-validate        Skip validation
  --no-backup          Skip creating backup before restore
  --force              Force restore even if validation fails

List Options:
  --type=<type>        Filter by backup type
  --days=<days>        Show backups from last N days
  --limit=<count>      Limit number of results
  --details            Show detailed information

Validate Options:
  --backup=<name>      Backup file name to validate

Cleanup Options:
  --keep=<count>       Number of backups to keep (default: 50)
  --older-than=<days>  Delete backups older than N days (default: 30)
  --type=<type>        Only cleanup specific backup type
  --dry-run            Show what would be deleted without deleting

Schedule Options:
  --full-interval=<seconds>         Full backup interval (default: 86400)
  --incremental-interval=<seconds>  Incremental backup interval (default: 14400)
  --cleanup-interval=<seconds>      Cleanup interval (default: 604800)
  --disable                         Disable automatic backups

Examples:
  node backup-rollback.js create --type=full --name="pre-migration"
  node backup-rollback.js create --type=incremental --phase="after-schema-update"
  node backup-rollback.js restore --backup="backup-2025-01-01T10-00-00.db.gz"
  node backup-rollback.js list --type=full --days=7 --details
  node backup-rollback.js cleanup --keep=5 --older-than=30 --dry-run
  node backup-rollback.js validate --backup="backup-2025-01-01T10-00-00.db.gz"
          `);
          break;
      }
      
      backupManager.disconnect();
      
    } catch (error) {
      console.error(`❌ Command failed: ${error.message}`);
      if (flags.verbose) {
        console.error(error.stack);
      }
      backupManager.disconnect();
      process.exit(1);
    }
  }

  runCommand();
}

module.exports = { BackupManager };