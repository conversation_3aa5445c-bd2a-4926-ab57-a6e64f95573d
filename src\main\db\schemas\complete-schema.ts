/**
 * Complete database schema including organization support
 * This ensures all required tables are created on first run
 */

export const COMPLETE_SCHEMA = `
-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Color sources table
CREATE TABLE IF NOT EXISTS color_sources (
  id INTEGER PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  is_system BOOLEAN NOT NULL DEFAULT FALSE,
  properties JSON
);

-- Pre-populate with standard sources
INSERT OR IGNORE INTO color_sources (id, code, name, is_system) VALUES
(1, 'user', 'User Created', FALSE),
(2, 'pantone', 'PANTONE®', TRUE),
(3, 'ral', 'RAL', TRUE),
(4, 'ncs', 'NCS', TRUE);

-- Products table - COMPLETE with ALL Supabase columns
CREATE TABLE IF NOT EXISTS products (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  type TEXT,
  sku TEXT,
  website TEXT,
  datasheet_url TEXT,
  price REAL,
  currency TEXT,
  is_master BOOLEAN NOT NULL DEFAULT FALSE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  -- Organization support (UUID)
  organization_id TEXT,
  created_by TEXT,
  user_id TEXT,
  metadata JSON DEFAULT '{}',
  -- Sync fields
  device_id TEXT,
  deleted_at TEXT,
  conflict_resolved_at TEXT,
  is_synced INTEGER DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_products_master ON products(is_master);
CREATE INDEX IF NOT EXISTS idx_products_org ON products(organization_id);
CREATE INDEX IF NOT EXISTS idx_products_deleted_at ON products(deleted_at);
CREATE INDEX IF NOT EXISTS idx_products_org_active ON products(organization_id, deleted_at) WHERE deleted_at IS NULL;

-- Colors table - COMPLETE with ALL Supabase columns including color_spaces
CREATE TABLE IF NOT EXISTS colors (
  id TEXT PRIMARY KEY,
  display_name TEXT,
  code TEXT,
  hex TEXT NOT NULL,
  source_id INTEGER NOT NULL DEFAULT 1 REFERENCES color_sources(id),
  -- CRITICAL: Color spaces as JSON (Supabase expects this!)
  color_spaces JSON DEFAULT '{}',
  -- Color characteristics  
  is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
  is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
  is_effect BOOLEAN NOT NULL DEFAULT FALSE,
  -- Cloud compatibility columns (from Migration 018)
  gradient_colors TEXT,  -- CSV of hex values for gradients
  notes TEXT,           -- User notes
  tags TEXT,            -- User tags
  is_library BOOLEAN NOT NULL DEFAULT FALSE,
  -- Legacy properties
  properties JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  version INTEGER NOT NULL DEFAULT 1,
  -- Organization support (UUID)
  organization_id TEXT,
  created_by TEXT,
  user_id TEXT,
  deleted_at TEXT,
  device_id TEXT,
  conflict_resolved_at TEXT,
  is_synced INTEGER DEFAULT 0
);

CREATE INDEX IF NOT EXISTS idx_colors_hex ON colors(hex);
CREATE INDEX IF NOT EXISTS idx_colors_source ON colors(source_id);
CREATE INDEX IF NOT EXISTS idx_colors_org ON colors(organization_id);
-- New indexes for cloud compatibility columns
CREATE INDEX IF NOT EXISTS idx_colors_gradient_colors ON colors(gradient_colors) WHERE gradient_colors IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_colors_notes ON colors(notes) WHERE notes IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_colors_tags ON colors(tags) WHERE tags IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_colors_is_library ON colors(is_library);
CREATE INDEX IF NOT EXISTS idx_colors_library_gradient ON colors(is_library, is_gradient);

-- Product colors junction table
CREATE TABLE IF NOT EXISTS product_colors (
  product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  color_id TEXT NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
  display_order INTEGER NOT NULL DEFAULT 0,
  organization_id TEXT NOT NULL,
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (product_id, color_id)
);

CREATE INDEX IF NOT EXISTS idx_product_colors_product ON product_colors(product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id);
-- Organization-first composite indexes for better multi-tenant performance
CREATE INDEX IF NOT EXISTS idx_product_colors_org_product ON product_colors(organization_id, product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org_color ON product_colors(organization_id, color_id);

-- Note: Color space data is now stored in the denormalized color_spaces JSON column
-- Legacy normalized color space tables (color_rgb, color_cmyk, color_lab, color_hsl) 
-- have been superseded by migration 017 and removed

-- Organizations table
CREATE TABLE IF NOT EXISTS organizations (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
  settings JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  
  -- Constraints
  CHECK (length(id) = 36),
  CHECK (length(trim(name)) > 0),
  CHECK (length(trim(slug)) > 0)
);

-- Organization members table
CREATE TABLE IF NOT EXISTS organization_members (
  organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
  invited_by TEXT,
  PRIMARY KEY (organization_id, user_id)
);

-- Organization invitations table
CREATE TABLE IF NOT EXISTS organization_invitations (
  id TEXT PRIMARY KEY,
  organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  invited_by TEXT NOT NULL,
  invited_at TEXT DEFAULT CURRENT_TIMESTAMP,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  expires_at TEXT NOT NULL,
  accepted_at TEXT,
  token TEXT UNIQUE NOT NULL,
  UNIQUE(organization_id, email)
);

-- Users table (local cache of user data)
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  display_name TEXT,
  avatar_url TEXT,
  preferences JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Color libraries table (Pantone, RAL, NCS, etc.)
CREATE TABLE IF NOT EXISTS color_libraries (
  id INTEGER PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_system BOOLEAN NOT NULL DEFAULT TRUE,
  version TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Library colors table (individual colors within libraries)
CREATE TABLE IF NOT EXISTS library_colors (
  id INTEGER PRIMARY KEY,
  external_id TEXT UNIQUE NOT NULL,
  library_id INTEGER NOT NULL REFERENCES color_libraries(id) ON DELETE CASCADE,
  code TEXT NOT NULL,
  name TEXT NOT NULL,
  hex CHAR(7) NOT NULL,
  cmyk TEXT,
  rgb TEXT,
  lab TEXT,
  hsl TEXT,
  notes TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  sort_order INTEGER DEFAULT 0,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,

  -- Constraints
  CHECK (
    length(hex) = 7 AND
    substr(hex, 1, 1) = '#' AND
    hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'
  ),
  CHECK (length(trim(code)) > 0),
  CHECK (length(trim(name)) > 0),
  UNIQUE (library_id, code)
);

-- Color library metadata (for additional properties)
CREATE TABLE IF NOT EXISTS library_color_metadata (
  color_id INTEGER PRIMARY KEY REFERENCES library_colors(id) ON DELETE CASCADE,
  properties JSON,
  tags TEXT,
  search_terms TEXT,
  popularity_score INTEGER DEFAULT 0,
  usage_count INTEGER DEFAULT 0
) WITHOUT ROWID;

-- Indexes for color library performance
CREATE INDEX IF NOT EXISTS idx_library_colors_library ON library_colors(library_id);
CREATE INDEX IF NOT EXISTS idx_library_colors_code ON library_colors(library_id, code);
CREATE INDEX IF NOT EXISTS idx_library_colors_hex ON library_colors(hex);
CREATE INDEX IF NOT EXISTS idx_library_colors_active ON library_colors(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_library_colors_search ON library_colors(name, code);

-- Insert default color libraries
INSERT OR IGNORE INTO color_libraries (id, code, name, description, version) VALUES
(1, 'PANTONE', 'PANTONE®', 'PANTONE Color Matching System', '2024'),
(2, 'RAL', 'RAL Classic', 'RAL Classic Color Collection', '2024'),
(3, 'NCS', 'Natural Color System', 'NCS Natural Color System', '2024'),
(4, 'USER', 'User Colors', 'User-created custom colors', '1.0');

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_org_members_user ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_org_slug ON organizations(slug);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON organization_invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON organization_invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_org ON organization_invitations(organization_id);

-- Schema migrations tracking
CREATE TABLE IF NOT EXISTS schema_migrations (
  version INTEGER PRIMARY KEY,
  name TEXT NOT NULL,
  applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Mark initial schema as applied
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (1, 'initial_schema');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (2, 'add_organizations');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (3, 'add_organization_invitations');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (4, 'add_users_table');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (5, 'add_sync_columns');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (6, 'add_user_preferences');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (7, 'add_code_column');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (8, 'fix_product_colors_schema');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (9, 'production_sync_compatibility_safe');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (11, 'add_gradient_columns');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (15, 'standardize_organization_uuids');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (16, 'add_organization_id_to_product_colors');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (17, 'denormalize_color_spaces');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (18, 'complete_cloud_compatibility');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (19, 'add_rls_performance_indexes');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (20, 'remove_legacy_normalized_tables');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (21, 'fix_organization_id_consistency');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (22, 'add_products_deleted_at');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (23, 'add_is_library_column');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (24, 'add_datasheet_sync_support');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (26, 'add_is_synced_to_products');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (27, 'add_is_synced_to_colors');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (28, 'migrate_products_to_uuid_primary_key');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (29, 'migrate_colors_to_uuid_primary_key');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (30, 'migrate_organizations_to_uuid_primary_key');

-- Triggers for updated_at timestamps
CREATE TRIGGER IF NOT EXISTS update_products_timestamp 
AFTER UPDATE ON products
BEGIN
  UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_colors_timestamp 
AFTER UPDATE ON colors
BEGIN
  UPDATE colors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_organizations_timestamp 
AFTER UPDATE ON organizations
BEGIN
  UPDATE organizations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
AFTER UPDATE ON users
BEGIN
  UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
`;