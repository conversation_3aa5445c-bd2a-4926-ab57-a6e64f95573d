/**
 * @file organization.ipc.ts
 * @description IPC handlers for organization operations
 */

import { ipcMain } from 'electron';
import { registerHandlerSafely } from '../utils/ipcRegistry';
import { OrganizationService } from '../db/services/organization.service';
import type {
  CreateOrganizationRequest
} from '../../shared/types/organization.types';
import Database from 'better-sqlite3';
import { getOAuthService } from '../services/service-locator';
import {
  getCurrentOrganization,
  setCurrentOrganization
} from '../utils/organization-context';

// IPC Channel names
export enum OrganizationChannels {
  CREATE = 'organization:create',
  GET_ALL = 'organization:getAll',
  GET_BY_ID = 'organization:getById',
  GET_CURRENT = 'organization:getCurrent',
  SET_CURRENT = 'organization:setCurrent',
  GET_MEMBERS = 'organization:getMembers',
  ADD_MEMBER = 'organization:addMember',
  UPDATE_MEMBER_ROLE = 'organization:updateMemberRole',
  REMOVE_MEMBER = 'organization:removeMember',
  UPDATE_SETTINGS = 'organization:updateSettings',
  DELETE = 'organization:delete',
  INVITE_MEMBER = 'organization:inviteMember',
  ACCEPT_INVITATION = 'organization:acceptInvitation',
  GET_PENDING_INVITATIONS = 'organization:getPendingInvitations',
  REVOKE_INVITATION = 'organization:revokeInvitation'
}

// Note: Organization context is now managed by the clean organization-context.ts system

/**
 * Helper function to safely extract error message
 */
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {return error.message;}
  if (typeof error === 'string') {return error;}
  return String(error);
}

// Organization context initialization is now handled automatically by organization-utils.ts

/**
 * Register IPC handlers for organization operations
 */
export function registerOrganizationHandlers(organizationService: OrganizationService, _db: Database.Database): void {
  console.log('[OrganizationIPC] 🚀 Starting organization handler registration...');
  // Organization context is automatically initialized by organization-utils.ts
  
  // Create organization
  registerHandlerSafely(ipcMain, OrganizationChannels.CREATE, async (_event, data: CreateOrganizationRequest) => {
      try {
        console.log('[OrganizationIPC] Creating organization:', data.name);
        
        // Get current user ID from auth service
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        
        if (!user) {
          console.error('[OrganizationIPC] No authenticated user found');
          return { success: false, error: 'User not authenticated' };
        }
        
        const organization = await organizationService.createOrganization(data.name, user.id);

        // Set as current organization using the clean system
        setCurrentOrganization(organization.external_id);
        
        return { success: true, data: organization };
      } catch (error) {
        console.error('[OrganizationIPC] Error creating organization:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Get all organizations for user
  registerHandlerSafely(ipcMain, OrganizationChannels.GET_ALL, async (_event) => {
      try {
        console.log('[OrganizationIPC] Handling GET_ALL request');
        // Get current user from auth service
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        
        if (!user) {
          console.error('[OrganizationIPC] No authenticated user found for GET_ALL');
          return { success: false, error: 'User not authenticated' };
        }
        
        console.log('[OrganizationIPC] Getting organizations for user:', user.id);
        
        // Try to sync organizations from Supabase first, but continue if it fails
        console.log('[OrganizationIPC] 🔄 Starting Supabase sync for user:', user.id);
        try {
          await organizationService.syncOrganizationsFromSupabase(user.id);
          console.log('[OrganizationIPC] ✅ Supabase sync completed successfully');
        } catch (syncError) {
          console.warn('[OrganizationIPC] ❌ Failed to sync from Supabase, continuing with local data:', syncError);
          console.warn('[OrganizationIPC] Sync error details:', syncError instanceof Error ? syncError.message : String(syncError));
        }
        
        // Then get all organizations (may return empty if database is not available)
        const organizations = await organizationService.getOrganizationsForUser(user.id);
        console.log(`[OrganizationIPC] Found ${organizations.length} organizations for user ${user.id}`);
        return { success: true, data: organizations };
      } catch (error) {
        console.error('[OrganizationIPC] Error getting organizations:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Get organization by ID
  registerHandlerSafely(ipcMain, OrganizationChannels.GET_BY_ID, async (_event, organizationId: string) => {
      try {
        const organization = await organizationService.getOrganization(organizationId);
        if (!organization) {
          return { success: false, error: 'Organization not found' };
        }
        return { success: true, data: organization };
      } catch (error) {
        console.error('[OrganizationIPC] Error getting organization:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Get current organization
  registerHandlerSafely(ipcMain, OrganizationChannels.GET_CURRENT, async () => {
      try {
        const currentOrganizationId = getCurrentOrganization();
        if (!currentOrganizationId) {
          return { success: false, error: 'No organization selected' };
        }

        const organization = await organizationService.getOrganization(currentOrganizationId);
        if (!organization) {
          setCurrentOrganization(null);
          return { success: false, error: 'Current organization not found' };
        }

        return { success: true, data: organization };
      } catch (error) {
        console.error('[OrganizationIPC] Error getting current organization:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Set current organization
  registerHandlerSafely(ipcMain, OrganizationChannels.SET_CURRENT, async (_event, organizationId: string) => {
      try {
        const organization = await organizationService.getOrganization(organizationId);
        if (!organization) {
          return { success: false, error: 'Organization not found' };
        }

        console.log('[OrganizationIPC] 🔧 Switching organization context');

        // Use the clean organization context system
        setCurrentOrganization(organizationId);

        console.log('[OrganizationIPC] ✅ Current organization set to:', organizationId);

        return { success: true, data: organization };
      } catch (error) {
        console.error('[OrganizationIPC] Error setting current organization:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Get organization members
  registerHandlerSafely(ipcMain, OrganizationChannels.GET_MEMBERS, async (_event, organizationId: string) => {
      try {
        // First sync members from Supabase
        await organizationService.syncMembersFromSupabase(organizationId);
        
        // Then get all members
        const members = await organizationService.getMembers(organizationId);
        return { success: true, data: members };
      } catch (error) {
        console.error('[OrganizationIPC] Error getting members:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Add member
  registerHandlerSafely(ipcMain, OrganizationChannels.ADD_MEMBER, async (_event, organizationId: string, userId: string, role: string, invitedBy: string) => {
      try {
        await organizationService.addMember(organizationId, userId, role as any, invitedBy);
        return { success: true };
      } catch (error) {
        console.error('[OrganizationIPC] Error adding member:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Update member role
  registerHandlerSafely(ipcMain, OrganizationChannels.UPDATE_MEMBER_ROLE, async (_event, data: { organizationId: string; userId: string; role: string }) => {
      try {
        await organizationService.updateMemberRole(data.organizationId, data.userId, data.role as any);
        return { success: true };
      } catch (error) {
        console.error('[OrganizationIPC] Error updating member role:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Remove member
  registerHandlerSafely(ipcMain, OrganizationChannels.REMOVE_MEMBER, async (_event, data: { organizationId: string; userId: string }) => {
      try {
        await organizationService.removeMember(data.organizationId, data.userId);
        return { success: true };
      } catch (error) {
        console.error('[OrganizationIPC] Error removing member:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Update organization settings
  registerHandlerSafely(ipcMain, OrganizationChannels.UPDATE_SETTINGS, async (_event, organizationId: string, settings: any) => {
      try {
        await organizationService.updateOrganization(organizationId, { settings });
        return { success: true };
      } catch (error) {
        console.error('[OrganizationIPC] Error updating settings:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Delete organization
  registerHandlerSafely(ipcMain, OrganizationChannels.DELETE, async (_event, organizationId: string, forceCascade: boolean = false) => {
      try {
        // Get current user from auth service
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        
        if (!user) {
          return { success: false, error: 'User not authenticated' };
        }
        
        console.log('[OrganizationIPC] Deleting organization:', organizationId, 'forceCascade:', forceCascade);
        
        // Delete the organization
        await organizationService.deleteOrganization(organizationId, user.id, forceCascade);
        
        // Clear current organization if it was deleted
        const currentOrgId = getCurrentOrganization();
        if (currentOrgId === organizationId) {
          setCurrentOrganization(null);
        }
        
        return { success: true };
      } catch (error) {
        console.error('[OrganizationIPC] Error deleting organization:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Invite member
  registerHandlerSafely(ipcMain, OrganizationChannels.INVITE_MEMBER, async (_event, data: { organizationId: string; email: string; role: string }) => {
      try {
        // Get current user from auth service
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        
        if (!user) {
          return { success: false, error: 'User not authenticated' };
        }
        
        console.log('[OrganizationIPC] Inviting member:', data);
        console.log('[OrganizationIPC] Current user:', { id: user.id, email: user.email });
        
        const result = await organizationService.inviteMember(
          data.organizationId,
          data.email,
          data.role as 'admin' | 'member',
          user.id
        );
        
        console.log('[OrganizationIPC] Invite result:', result);
        return result;
      } catch (error) {
        console.error('[OrganizationIPC] Error inviting member:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Accept invitation
  registerHandlerSafely(ipcMain, OrganizationChannels.ACCEPT_INVITATION, async (_event, token: string) => {
      try {
        // Get current user from auth service
        const oauthService = getOAuthService();
        const user = await oauthService.getCurrentUser();
        
        if (!user) {
          return { success: false, error: 'User not authenticated' };
        }
        
        const result = await organizationService.acceptInvitation(token, user.id);
        
        return result;
      } catch (error) {
        console.error('[OrganizationIPC] Error accepting invitation:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Get pending invitations
  registerHandlerSafely(ipcMain, OrganizationChannels.GET_PENDING_INVITATIONS, async (_event, organizationId: string) => {
      try {
        const invitations = await organizationService.getPendingInvitations(organizationId);
        return { success: true, data: invitations };
      } catch (error) {
        console.error('[OrganizationIPC] Error getting invitations:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  // Revoke invitation
  registerHandlerSafely(ipcMain, OrganizationChannels.REVOKE_INVITATION, async (_event, data: { organizationId: string; invitationId: string }) => {
      try {
        const success = await organizationService.revokeInvitation(data.organizationId, data.invitationId);
        return { success };
      } catch (error) {
        console.error('[OrganizationIPC] Error revoking invitation:', error);
        return { success: false, error: getErrorMessage(error) };
      }
    });
  
  console.log('[OrganizationIPC] 🎉 Organization handler registration completed');
}

// Export helpers for backward compatibility - these now use the clean system
export { getCurrentOrganization as getCurrentOrganizationId, setCurrentOrganization as setCurrentOrganizationId } from '../utils/organization-context';