/**
 * @file service-initializer.simple.test.ts
 * @description Simple integration test for service initialization system
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock electron before importing anything else
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/test/userData'),
  },
}));

// Mock other dependencies that require electron
vi.mock('../../shared-folder', () => ({
  SharedFolderManager: vi.fn(),
}));

vi.mock('../../ipc/shared-folder-ipc', () => ({
  setupSharedFolderIPC: vi.fn(),
}));

vi.mock('../../utils/logger', () => ({
  serviceLogger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('../sentry.service', () => ({
  sentryService: { initialize: vi.fn() },
  SentryService: { isGloballyInitialized: vi.fn() },
}));

vi.mock('../service-locator', () => ({
  ServiceLocator: { initialize: vi.fn() },
  getZohoEmailService: vi.fn(() => ({ initialize: vi.fn() })),
}));

vi.mock('../../db/database', () => ({
  initDatabase: vi.fn(),
  getDatabase: vi.fn(),
}));

import {
    createServiceConfig,
    ServiceConfig,
} from '../service-initializer';

describe('Service Initializer - Core Functionality', () => {
  beforeEach(() => {
    // Reset environment variables
    process.env.ZOHO_CLIENT_ID = 'test-client-id';
    process.env.ZOHO_CLIENT_SECRET = 'test-client-secret';
    process.env.ZOHO_REFRESH_TOKEN = 'test-refresh-token';
    process.env.ZOHO_ACCOUNT_ID = 'test-account-id';
    process.env.ZOHO_REGION = 'EU';
    process.env.ZOHO_SUPPORT_ALIAS = '<EMAIL>';
    process.env.SUPABASE_URL = 'https://test.supabase.co';
    process.env.SUPABASE_ANON_KEY = 'test-anon-key';

    vi.clearAllMocks();
  });

  describe('createServiceConfig', () => {
    it('should create service configuration from environment variables', () => {
      const config = createServiceConfig();

      expect(config).toEqual({
        zoho: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          refreshToken: 'test-refresh-token',
          accountId: 'test-account-id',
          region: 'EU',
          supportAlias: '<EMAIL>',
        },
        supabase: {
          url: 'https://test.supabase.co',
          anonKey: 'test-anon-key',
        },
      });
    });

    it('should use default values for missing environment variables', () => {
      delete process.env.ZOHO_REGION;
      delete process.env.ZOHO_SUPPORT_ALIAS;
      delete process.env.ZOHO_CLIENT_ID;
      delete process.env.SUPABASE_URL;

      const config = createServiceConfig();

      expect(config.zoho.region).toBe('US');
      expect(config.zoho.supportAlias).toBe('<EMAIL>');
      expect(config.zoho.clientId).toBeUndefined();
      expect(config.supabase.url).toBeUndefined();
    });

    it('should handle empty environment variables', () => {
      process.env.ZOHO_CLIENT_ID = '';
      process.env.SUPABASE_URL = '';

      const config = createServiceConfig();

      expect(config.zoho.clientId).toBe('');
      expect(config.supabase.url).toBe('');
    });

    it('should preserve all required configuration structure', () => {
      const config = createServiceConfig();

      // Verify structure
      expect(config).toHaveProperty('zoho');
      expect(config).toHaveProperty('supabase');
      
      expect(config.zoho).toHaveProperty('clientId');
      expect(config.zoho).toHaveProperty('clientSecret');
      expect(config.zoho).toHaveProperty('refreshToken');
      expect(config.zoho).toHaveProperty('accountId');
      expect(config.zoho).toHaveProperty('region');
      expect(config.zoho).toHaveProperty('supportAlias');
      
      expect(config.supabase).toHaveProperty('url');
      expect(config.supabase).toHaveProperty('anonKey');
    });

    it('should handle special characters in environment variables', () => {
      process.env.ZOHO_CLIENT_SECRET = 'secret!@#$%^&*()';
      process.env.SUPABASE_ANON_KEY = 'key-with-dashes_and_underscores.dots';

      const config = createServiceConfig();

      expect(config.zoho.clientSecret).toBe('secret!@#$%^&*()');
      expect(config.supabase.anonKey).toBe('key-with-dashes_and_underscores.dots');
    });
  });

  describe('ServiceConfig type validation', () => {
    it('should match ServiceConfig interface', () => {
      const config = createServiceConfig();

      // TypeScript compilation will fail if this doesn't match the interface
      const typedConfig: ServiceConfig = config;

      expect(typedConfig).toBeDefined();
      expect(typeof typedConfig.zoho.clientId).toBe('string' || 'undefined');
      expect(typeof typedConfig.zoho.region).toBe('string');
      expect(typeof typedConfig.supabase.url).toBe('string' || 'undefined');
    });

    it('should allow partial configuration', () => {
      // Clear all environment variables
      Object.keys(process.env).forEach(key => {
        if (key.startsWith('ZOHO_') || key.startsWith('SUPABASE_')) {
          delete process.env[key];
        }
      });

      const config = createServiceConfig();

      // Should still create valid config with defaults
      expect(config.zoho.region).toBe('US');
      expect(config.zoho.supportAlias).toBe('<EMAIL>');
      expect(config.zoho.clientId).toBeUndefined();
      expect(config.supabase.url).toBeUndefined();
    });
  });

  describe('Configuration validation', () => {
    it('should create consistent configuration objects', () => {
      const config1 = createServiceConfig();
      const config2 = createServiceConfig();

      expect(config1).toEqual(config2);
    });

    it('should reflect environment changes', () => {
      const config1 = createServiceConfig();
      
      process.env.ZOHO_CLIENT_ID = 'changed-client-id';
      
      const config2 = createServiceConfig();

      expect(config1.zoho.clientId).not.toBe(config2.zoho.clientId);
      expect(config2.zoho.clientId).toBe('changed-client-id');
    });

    it('should handle undefined vs empty string correctly', () => {
      delete process.env.ZOHO_CLIENT_ID;
      process.env.ZOHO_CLIENT_SECRET = '';

      const config = createServiceConfig();

      expect(config.zoho.clientId).toBeUndefined();
      expect(config.zoho.clientSecret).toBe('');
    });
  });

  describe('Error resilience', () => {
    it('should not throw errors with malformed environment', () => {
      // Set some potentially problematic values
      process.env.ZOHO_REGION = null as any;
      process.env.SUPABASE_URL = undefined as any;

      expect(() => createServiceConfig()).not.toThrow();
    });

    it('should handle very long environment variable values', () => {
      const longValue = 'a'.repeat(10000);
      process.env.ZOHO_CLIENT_SECRET = longValue;

      const config = createServiceConfig();

      expect(config.zoho.clientSecret).toBe(longValue);
      expect(config.zoho.clientSecret.length).toBe(10000);
    });
  });
});