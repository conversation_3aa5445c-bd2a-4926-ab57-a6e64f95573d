/**
 * @file enhanced-color-library.service.ts
 * @description Enhanced color library service for renderer with optimized loading
 *
 * This service provides an optimized interface for the renderer to access
 * color libraries with lazy loading, caching, and better performance.
 */

import type { ColorEntry } from '../../shared/types/color.types';

export interface ColorLibrarySearchOptions {
  library?: 'PANTONE' | 'RAL' | 'NCS';
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'code' | 'name' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

export interface ColorLibrarySearchResult {
  colors: ColorEntry[];
  total: number;
  hasMore: boolean;
}

export interface ColorLibraryMetadata {
  name: string;
  code: string;
  version: string;
  colorCount: number;
  lastUpdated: string;
  description: string;
}

/**
 * Enhanced color library service for optimized performance
 */
export class EnhancedColorLibraryService {
  private cache = new Map<
    string,
    {
      data: ColorEntry[];
      timestamp: number;
      metadata?: ColorLibraryMetadata;
    }
  >();

  private readonly CACHE_TTL = 1000 * 60 * 10; // 10 minutes cache

  /**
   * Search colors with enhanced performance
   */
  async searchColors(
    options: ColorLibrarySearchOptions = {}
  ): Promise<ColorLibrarySearchResult> {
    try {
      // Use the color library API
      const result =
        (await (window as any).colorLibraryAPI?.searchColorsEnhanced?.(
          options
        )) || (await (window as any).colorLibraryAPI?.searchColors?.(options));

      if (result) {
        return this.transformResult(result);
      }

      throw new Error('Color library API not available');
    } catch (error) {
      console.error(
        '[EnhancedColorLibraryService] Failed to search colors:',
        error
      );
      return { colors: [], total: 0, hasMore: false };
    }
  }

  /**
   * Get Pantone colors with caching
   */
  async getPantoneColors(
    options: Omit<ColorLibrarySearchOptions, 'library'> = {}
  ): Promise<ColorLibrarySearchResult> {
    const cacheKey = `pantone:${JSON.stringify(options)}`;

    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return {
        colors: cached.data,
        total: cached.data.length,
        hasMore: false,
      };
    }

    try {
      const result = await this.searchColors({
        ...options,
        library: 'PANTONE',
      });

      // Cache the result
      this.cache.set(cacheKey, {
        data: result.colors,
        timestamp: Date.now(),
      });

      return result;
    } catch (error) {
      console.error(
        '[EnhancedColorLibraryService] Failed to get Pantone colors:',
        error
      );
      return { colors: [], total: 0, hasMore: false };
    }
  }

  /**
   * Get RAL colors with caching
   */
  async getRalColors(
    options: Omit<ColorLibrarySearchOptions, 'library'> = {}
  ): Promise<ColorLibrarySearchResult> {
    const cacheKey = `ral:${JSON.stringify(options)}`;

    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return {
        colors: cached.data,
        total: cached.data.length,
        hasMore: false,
      };
    }

    try {
      const result = await this.searchColors({ ...options, library: 'RAL' });

      // Cache the result
      this.cache.set(cacheKey, {
        data: result.colors,
        timestamp: Date.now(),
      });

      return result;
    } catch (error) {
      console.error(
        '[EnhancedColorLibraryService] Failed to get RAL colors:',
        error
      );
      return { colors: [], total: 0, hasMore: false };
    }
  }

  /**
   * Get all Pantone colors (for backward compatibility)
   */
  async getAllPantoneColors(): Promise<ColorEntry[]> {
    try {
      const result = await this.getPantoneColors({ limit: 10000 });
      return result.colors;
    } catch (error) {
      console.error(
        '[EnhancedColorLibraryService] Failed to get all Pantone colors:',
        error
      );
      return [];
    }
  }

  /**
   * Get all RAL colors (for backward compatibility)
   */
  async getAllRalColors(): Promise<ColorEntry[]> {
    try {
      const result = await this.getRalColors({ limit: 10000 });
      return result.colors;
    } catch (error) {
      console.error(
        '[EnhancedColorLibraryService] Failed to get all RAL colors:',
        error
      );
      return [];
    }
  }

  /**
   * Load library chunk for virtual scrolling
   */
  async loadLibraryChunk(
    libraryCode: string,
    startIndex: number,
    chunkSize: number = 100
  ) {
    try {
      // Use the optimized chunk loading API
      const result = await (window as any).colorLibraryAPI?.loadLibraryChunk?.(
        libraryCode,
        startIndex,
        chunkSize
      );

      if (result) {
        return result;
      }

      // Use search API with pagination as alternative
      const searchResult = await this.searchColors({
        library: libraryCode as any,
        offset: startIndex,
        limit: chunkSize,
      });

      return {
        metadata: { name: `${libraryCode} Colors`, code: libraryCode },
        colors: searchResult.colors,
        startIndex,
        endIndex: startIndex + searchResult.colors.length,
      };
    } catch (error) {
      console.error(
        '[EnhancedColorLibraryService] Failed to load library chunk:',
        error
      );
      return null;
    }
  }

  /**
   * Get library metadata
   */
  async getLibraryMetadata(
    libraryCode: string
  ): Promise<ColorLibraryMetadata | null> {
    try {
      if ((window as any).colorLibraryAPI?.getLibraryMetadata) {
        return await (window as any).colorLibraryAPI.getLibraryMetadata(
          libraryCode
        );
      }

      return null;
    } catch (error) {
      console.error(
        '[EnhancedColorLibraryService] Failed to get library metadata:',
        error
      );
      return null;
    }
  }

  /**
   * Get available libraries
   */
  async getAvailableLibraries(): Promise<ColorLibraryMetadata[]> {
    try {
      if ((window as any).colorLibraryAPI?.getAvailableLibraries) {
        return await (window as any).colorLibraryAPI.getAvailableLibraries();
      }

      return [];
    } catch (error) {
      console.error(
        '[EnhancedColorLibraryService] Failed to get available libraries:',
        error
      );
      return [];
    }
  }

  /**
   * Clear cache
   */
  clearCache(libraryCode?: string): void {
    if (libraryCode) {
      // Clear specific library cache
      const keysToDelete = Array.from(this.cache.keys()).filter(key =>
        key.includes(libraryCode.toLowerCase())
      );
      keysToDelete.forEach(key => this.cache.delete(key));
    } else {
      // Clear all cache
      this.cache.clear();
    }

    // Also clear server-side cache if available
    if ((window as any).colorLibraryAPI?.clearCache) {
      (window as any).colorLibraryAPI.clearCache(libraryCode);
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    const stats = {
      localEntries: this.cache.size,
      localMemoryUsage: 0,
      serverStats: null as any,
    };

    // Calculate local memory usage
    this.cache.forEach(entry => {
      stats.localMemoryUsage += JSON.stringify(entry.data).length;
    });

    // Get server-side stats if available
    if ((window as any).colorLibraryAPI?.getCacheStats) {
      (window as any).colorLibraryAPI
        .getCacheStats()
        .then((serverStats: any) => {
          stats.serverStats = serverStats;
        });
    }

    return stats;
  }

  /**
   * Transform search result to standardized format
   */
  private transformResult(result: any): ColorLibrarySearchResult {
    return {
      colors: this.transformColors(result.colors || []),
      total: result.total || 0,
      hasMore: result.hasMore || false,
    };
  }

  /**
   * Transform array of colors to standardized format
   */
  private transformColors(colors: any[]): ColorEntry[] {
    return colors.map(color => this.transformColor(color));
  }

  /**
   * Transform single color to standardized format
   */
  private transformColor(color: any): ColorEntry {
    return {
      id: color.external_id || color.id,
      product: color.library_name || color.library_code || color.product,
      organizationId: color.organizationId || '',
      name: color.name,
      code: color.code,
      hex: color.hex,
      cmyk: color.cmyk,
      rgb: color.rgb,
      lab: color.lab,
      hsl: color.hsl,
      notes: color.notes,
      isLibrary: true,
      createdAt: color.createdAt || new Date().toISOString(),
      updatedAt: color.updatedAt || new Date().toISOString(),
    };
  }

  // Legacy fallback methods removed - service now relies on modern IPC API
}

// Singleton instance
let enhancedColorLibraryService: EnhancedColorLibraryService | null = null;

/**
 * Get singleton instance of enhanced color library service
 */
export function getEnhancedColorLibraryService(): EnhancedColorLibraryService {
  if (!enhancedColorLibraryService) {
    enhancedColorLibraryService = new EnhancedColorLibraryService();
  }
  return enhancedColorLibraryService;
}

/**
 * Backward compatibility exports
 */

/**
 * Get Pantone colors (replaces static import)
 */
export async function getPantoneColors(): Promise<ColorEntry[]> {
  const service = getEnhancedColorLibraryService();
  return await service.getAllPantoneColors();
}

/**
 * Get RAL colors (replaces static import)
 */
export async function getRalColors(): Promise<ColorEntry[]> {
  const service = getEnhancedColorLibraryService();
  return await service.getAllRalColors();
}

/**
 * Search Pantone colors by name or code
 */
export async function searchPantoneColors(
  query: string,
  limit: number = 50
): Promise<ColorEntry[]> {
  const service = getEnhancedColorLibraryService();
  const result = await service.getPantoneColors({ search: query, limit });
  return result.colors;
}

/**
 * Search RAL colors by name or code
 */
export async function searchRalColors(
  query: string,
  limit: number = 50
): Promise<ColorEntry[]> {
  const service = getEnhancedColorLibraryService();
  const result = await service.getRalColors({ search: query, limit });
  return result.colors;
}

export default EnhancedColorLibraryService;
