-- Migration: 20250616_standardize_organization_uuids.sql
-- Purpose: Standardize organization IDs to use UUIDs throughout the application
-- Issue: Sync failures due to mixed integer/UUID usage for organization_id
-- Date: 2025-06-16
-- Author: ChromaSync Team

-- This migration ensures all organization_id references use UUIDs (external_id)
-- instead of internal integer IDs for consistency with cloud database

BEGIN TRANSACTION;

-- Check if migration has already been applied
SELECT CASE 
  WHEN (SELECT COUNT(*) FROM colors WHERE organization_id IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')) = 0
  THEN 'Migration already applied or not needed'
  ELSE 'Migration needed'
END as migration_status;

-- Update colors to use UUID organization_id
UPDATE colors 
SET organization_id = (
    SELECT external_id 
    FROM organizations 
    WHERE organizations.id = CAST(colors.organization_id AS INTEGER)
)
WHERE colors.organization_id IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')
  AND EXISTS (
    SELECT 1 
    FROM organizations 
    WHERE organizations.id = CAST(colors.organization_id AS INTEGER)
  );

-- Update products to use UUID organization_id
UPDATE products 
SET organization_id = (
    SELECT external_id 
    FROM organizations 
    WHERE organizations.id = CAST(products.organization_id AS INTEGER)
)
WHERE products.organization_id IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')
  AND EXISTS (
    SELECT 1 
    FROM organizations 
    WHERE organizations.id = CAST(products.organization_id AS INTEGER)
  );

-- Update datasheets if table exists
UPDATE datasheets 
SET organization_id = (
    SELECT external_id 
    FROM organizations 
    WHERE organizations.id = CAST(datasheets.organization_id AS INTEGER)
)
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='datasheets')
  AND datasheets.organization_id IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')
  AND EXISTS (
    SELECT 1 
    FROM organizations 
    WHERE organizations.id = CAST(datasheets.organization_id AS INTEGER)
  );

-- Verify migration success
SELECT 
    'Migration complete. Remaining integer org IDs - Colors: ' || 
    (SELECT COUNT(*) FROM colors WHERE organization_id IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')) ||
    ', Products: ' || 
    (SELECT COUNT(*) FROM products WHERE organization_id IN ('1', '2', '3', '4', '5', '6', '7', '8', '9'))
    as migration_result;

COMMIT;
