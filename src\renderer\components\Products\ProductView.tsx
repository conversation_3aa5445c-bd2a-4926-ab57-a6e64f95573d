/**
 * @file ProductView.tsx
 * @description Component to display and manage colors in a product
 */

import React, { useState, useEffect } from 'react';
import { useProductStore } from '../../store/product.store';
import { useColorStore } from '../../store/color.store';
import { Plus, Trash2, Alert<PERSON>riangle, Folder, RefreshCw } from 'lucide-react';
import ColorSwatch from '../ColorSwatches/ColorSwatch';
import AddToProductModal from './AddToProductModal';
import { useTokens } from '../../hooks/useTokens';
import { useColorProductMap } from '../../hooks/useColorProductMap';
import Modal from '../ui/Modal';
import DatasheetList from '../Datasheets/DatasheetList';

interface ProductViewProps {
  activeProductId: string | null;
}

const ProductView: React.FC<ProductViewProps> = ({ activeProductId }) => {
  const tokens = useTokens();

  const {
    products,
    removeColorFromProduct,
    fetchProductsWithColors,
    forceRefreshProducts
  } = useProductStore();

  // Use the same hook as ColorSwatches component for product counts
  const { productMap, isLoading: isProductMapLoading } = useColorProductMap();

  const [isLoading, setIsLoading] = useState(false);
  const [showAddColorModal, setShowAddColorModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Development-only logging for debugging product selection
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG_PRODUCTS) {
      console.log('[ProductView] activeProductId changed:', {
        activeProductId,
        productsCount: products.length
      });
    }
  }, [activeProductId, products]);

  // Find the active product
  const activeProduct = products.find(p => p.id === activeProductId);
  
  // Development-only logging for debugging product lookup
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG_PRODUCTS) {
      console.log('[ProductView] activeProduct lookup:', {
        activeProductId,
        foundProduct: !!activeProduct,
        productName: activeProduct?.name || 'none',
        colorCount: activeProduct?.colors?.length || 0
      });
    }
  }, [activeProductId, activeProduct, products]);

  // Load colors when active product changes
  useEffect(() => {
    if (activeProductId) {
      setIsLoading(true);
      // The products are already fetched in the panel, so we don't need to fetch them again.
      // We just need to find the active product from the list.
      setIsLoading(false);
    }
  }, [activeProductId]);

  // Handle removing color from product (completely deletes the color)
  const handleRemoveColor = async (colorId: string) => {
    if (!activeProductId) {return;}

    try {
      // Instead of just removing association, delete the color entirely
      const { deleteColor } = useColorStore.getState();
      await deleteColor(colorId);
      setShowDeleteConfirm(null);
    } catch (err) {
      console.error('Failed to delete color:', err);
    }
  };

  // If no product is selected
  if (!activeProductId) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8 text-center">
        <Folder className="h-12 w-12 text-[var(--color-ui-foreground-tertiary)] mb-4" />
        <h3 className="text-lg font-medium text-[var(--color-ui-foreground-primary)] mb-2">No Product Selected</h3>
        <p className="text-[var(--color-ui-foreground-tertiary)] max-w-md">
          Select a product from the sidebar to view its colors or create a new product to get started.
        </p>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8">
        <div className="animate-pulse flex flex-col items-center">
          <div className="rounded-full bg-[var(--color-ui-background-tertiary)] h-12 w-12 mb-4"></div>
          <div className="h-4 bg-[var(--color-ui-background-tertiary)] rounded w-48 mb-2"></div>
          <div className="h-3 bg-[var(--color-ui-background-tertiary)] rounded w-32"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Product header */}
      <div className="border-b border-[var(--color-ui-border-light)] bg-[var(--color-ui-background-secondary)]">
        <div className="px-5 py-3.5 flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-[var(--color-ui-foreground-primary)] leading-tight">
              {activeProduct?.name || 'Product'}
            </h2>
            {activeProduct?.description && (
              <p className="text-[var(--color-ui-foreground-secondary)] text-xs mt-1 max-w-md">
                {activeProduct.description}
              </p>
            )}
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowAddColorModal(true)}
              className="flex items-center px-3.5 py-1.5 text-xs font-medium rounded-md bg-[var(--color-brand-primary)] text-[var(--color-ui-foreground-inverse)] hover:opacity-90 transition-colors"
              style={{
                transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
              }}
            >
              <Plus size={14} className="mr-1" />
              Add Color
            </button>
            
            <button
              onClick={async () => {
                console.log('[ProductView] 🔄 Manual refresh triggered');
                setIsLoading(true);
                try {
                  // Force clear and reload products with enhanced refresh
                  await forceRefreshProducts();
                  console.log('[ProductView] ✅ Manual refresh completed');
                } catch (error) {
                  console.error('[ProductView] ❌ Manual refresh failed:', error);
                } finally {
                  setIsLoading(false);
                }
              }}
              disabled={isLoading}
              className="flex items-center px-3.5 py-1.5 text-xs font-medium rounded-md bg-[var(--color-ui-background-tertiary)] text-[var(--color-ui-foreground-primary)] hover:bg-[var(--color-ui-background-secondary)] transition-colors disabled:opacity-50"
              title="Refresh product data"
            >
              <RefreshCw size={14} className={`mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Datasheets section */}
      {activeProduct && activeProductId && (
        <DatasheetList
          productId={activeProductId}
          productName={activeProduct.name}
        />
      )}

      {/* Product content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <h3 className="text-lg font-medium text-[var(--color-ui-foreground-primary)] mb-3">Colors</h3>

          {activeProduct && (!activeProduct.colors || activeProduct.colors.length === 0) ? (
            <div className="py-12 flex flex-col items-center justify-center text-center border border-[var(--color-ui-border-light)] rounded-md bg-[var(--color-ui-background-secondary)]">
              <AlertTriangle className="h-12 w-12 text-[var(--color-ui-foreground-tertiary)] mb-4" />
              <h3 className="text-lg font-medium text-[var(--color-ui-foreground-primary)] mb-2">
                This product has no colors
              </h3>
              <p className="text-[var(--color-ui-foreground-tertiary)] max-w-md mb-6">
                Start adding colors to this product
              </p>
              
              {/* Debug info in development */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-[var(--color-ui-foreground-tertiary)] mb-4 p-2 bg-[var(--color-ui-background-primary)] rounded border max-w-md">
                  <div><strong>Product ID:</strong> {activeProductId || 'undefined'}</div>
                  <div><strong>Product Name:</strong> {activeProduct?.name || 'not found'}</div>
                  <div><strong>Colors Array:</strong> {activeProduct?.colors ? `[${activeProduct.colors.length}]` : 'undefined'}</div>
                  <div><strong>Total Products:</strong> {products.length}</div>
                  <div><strong>Test Products Found:</strong></div>
                  {products.filter(p => p.name === 'Test Product').map((p, i) => (
                    <div key={p.id} className="ml-2">
                      <strong>{i + 1}.</strong> {p.id.slice(0, 8)}... ({p.colors?.length || 0} colors)
                      {activeProductId === p.id && <span className="text-green-500"> ← ACTIVE</span>}
                    </div>
                  ))}
                </div>
              )}
              <button
                onClick={() => setShowAddColorModal(true)}
                className="flex items-center px-4 py-2 text-sm font-medium rounded-md bg-[var(--color-brand-primary)] text-[var(--color-ui-foreground-inverse)] hover:bg-[var(--color-brand-primary-dark)] transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.out}`
                }}
              >
                <Plus size={16} className="mr-1.5" />
                Add Color
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-6 gap-4">
              {activeProduct?.colors?.map(color => (
                <div key={color.id} className="relative group">
                  <ColorSwatch 
                    entry={color} 
                    productMap={productMap}
                    isProductMapLoading={isProductMapLoading}
                  />
                  <button
                    onClick={() => setShowDeleteConfirm(color.id)}
                    className="absolute top-2 right-2 bg-[var(--color-ui-background-primary)] bg-opacity-90 text-[var(--color-ui-foreground-primary)] rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity"
                    title="Delete color permanently"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add to product modal */}
      {activeProductId && (
        <AddToProductModal
          isOpen={showAddColorModal}
          onClose={() => setShowAddColorModal(false)}
          productId={activeProductId}
        />
      )}

      {/* Delete color confirmation modal */}
      {showDeleteConfirm && (
        <Modal
          isOpen={Boolean(showDeleteConfirm)}
          onClose={() => setShowDeleteConfirm(null)}
          title="Delete Color"
        >
          <div className="p-5">
            <p className="text-[var(--color-ui-foreground-secondary)] text-sm mb-5">
              Are you sure you want to permanently delete this color? This will remove it from all views and cannot be undone.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="px-3.5 py-1.5 text-xs font-medium text-[var(--color-ui-foreground-primary)] bg-[var(--color-ui-background-tertiary)] rounded-md hover:bg-[var(--color-ui-background-secondary)] transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                Cancel
              </button>
              <button
                onClick={() => handleRemoveColor(showDeleteConfirm)}
                className="px-3.5 py-1.5 text-xs font-medium bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                Delete Permanently
              </button>
            </div>
          </div>
        </Modal>
      )}

    </div>
  );
};

export default ProductView;