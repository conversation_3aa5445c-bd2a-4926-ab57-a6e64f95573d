/**
 * @file backup.service.ts
 * @description Simple backup service for database protection
 *
 * Provides basic backup functionality on app start/close with corruption detection
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import { getDatabasePath } from '../db/core/connection';

export interface BackupInfo {
  timestamp: number;
  filepath: string;
  checksum: string;
  size: number;
  type: 'startup' | 'shutdown' | 'manual';
}

export interface BackupResult {
  success: boolean;
  backupInfo?: BackupInfo;
  error?: string;
}

/**
 * Simple backup service for database protection
 */
export class BackupService {
  private static instance: BackupService;
  private backupDir: string;
  private maxBackups = 10; // Keep last 10 backups
  private isEnabled = true;

  private constructor() {
    const userDataPath = app.getPath('userData');
    this.backupDir = path.join(userDataPath, 'backups');
    this.ensureBackupDirectory();
  }

  static getInstance(): BackupService {
    if (!BackupService.instance) {
      BackupService.instance = new BackupService();
    }
    return BackupService.instance;
  }

  /**
   * Create backup on app startup
   */
  async createStartupBackup(): Promise<BackupResult> {
    if (!this.isEnabled) {
      console.log('[BackupService] Backup disabled, skipping startup backup');
      return { success: true };
    }

    try {
      console.log('[BackupService] Creating startup backup...');

      // Check database integrity first
      const integrityCheck = await this.checkDatabaseIntegrity();
      if (!integrityCheck.isValid) {
        console.error(
          '[BackupService] Database integrity check failed:',
          integrityCheck.error
        );
        // Continue with backup anyway to preserve data
      }

      const result = await this.createBackup('startup');

      if (result.success) {
        console.log(
          '[BackupService] ✅ Startup backup completed:',
          result.backupInfo?.filepath
        );
        await this.cleanupOldBackups();
      } else {
        console.error(
          '[BackupService] ❌ Startup backup failed:',
          result.error
        );
      }

      return result;
    } catch (error) {
      console.error('[BackupService] Startup backup error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown backup error',
      };
    }
  }

  /**
   * Create backup on app shutdown
   */
  async createShutdownBackup(): Promise<BackupResult> {
    if (!this.isEnabled) {
      console.log('[BackupService] Backup disabled, skipping shutdown backup');
      return { success: true };
    }

    try {
      console.log('[BackupService] Creating shutdown backup...');

      const result = await this.createBackup('shutdown');

      if (result.success) {
        console.log(
          '[BackupService] ✅ Shutdown backup completed:',
          result.backupInfo?.filepath
        );
        await this.cleanupOldBackups();
      } else {
        console.error(
          '[BackupService] ❌ Shutdown backup failed:',
          result.error
        );
      }

      return result;
    } catch (error) {
      console.error('[BackupService] Shutdown backup error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown backup error',
      };
    }
  }

  /**
   * Create manual backup
   */
  async createManualBackup(): Promise<BackupResult> {
    try {
      console.log('[BackupService] Creating manual backup...');

      const result = await this.createBackup('manual');

      if (result.success) {
        console.log(
          '[BackupService] ✅ Manual backup completed:',
          result.backupInfo?.filepath
        );
      } else {
        console.error('[BackupService] ❌ Manual backup failed:', result.error);
      }

      return result;
    } catch (error) {
      console.error('[BackupService] Manual backup error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown backup error',
      };
    }
  }

  /**
   * Check database integrity
   */
  async checkDatabaseIntegrity(): Promise<{
    isValid: boolean;
    error?: string;
  }> {
    try {
      const dbPath = getDatabasePath();

      if (!fs.existsSync(dbPath)) {
        return { isValid: false, error: 'Database file does not exist' };
      }

      // Basic file checks
      const stats = fs.statSync(dbPath);
      if (stats.size === 0) {
        return { isValid: false, error: 'Database file is empty' };
      }

      // Check if file is readable
      try {
        fs.accessSync(dbPath, fs.constants.R_OK);
      } catch {
        return { isValid: false, error: 'Database file is not readable' };
      }

      // Basic SQLite file format check
      const buffer = Buffer.alloc(16);
      const fd = fs.openSync(dbPath, 'r');
      fs.readSync(fd, buffer, 0, 16, 0);
      fs.closeSync(fd);

      const sqliteHeader = 'SQLite format 3\0';
      const fileHeader = buffer.toString('ascii', 0, sqliteHeader.length);

      if (fileHeader !== sqliteHeader) {
        return { isValid: false, error: 'Invalid SQLite file format' };
      }

      console.log('[BackupService] Database integrity check passed');
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error:
          error instanceof Error
            ? error.message
            : 'Unknown integrity check error',
      };
    }
  }

  /**
   * Get list of available backups
   */
  async getBackupList(): Promise<BackupInfo[]> {
    try {
      if (!fs.existsSync(this.backupDir)) {
        return [];
      }

      const files = fs
        .readdirSync(this.backupDir)
        .filter(file => file.endsWith('.db'))
        .map(file => {
          const filepath = path.join(this.backupDir, file);
          const stats = fs.statSync(filepath);

          // Parse filename for metadata
          const match = file.match(/chromasync-backup-(\d+)-(\w+)\.db$/);
          const timestamp = match?.[1]
            ? parseInt(match[1])
            : stats.mtime.getTime();
          const type = (match ? match[2] : 'manual') as
            | 'startup'
            | 'shutdown'
            | 'manual';

          return {
            timestamp,
            filepath,
            checksum: this.calculateFileChecksum(filepath),
            size: stats.size,
            type,
          };
        })
        .sort((a, b) => b.timestamp - a.timestamp); // Newest first

      return files;
    } catch (error) {
      console.error('[BackupService] Error getting backup list:', error);
      return [];
    }
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(
    backupPath: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const dbPath = getDatabasePath();

      if (!fs.existsSync(backupPath)) {
        throw new Error('Backup file does not exist');
      }

      // Create backup of current database before restore
      const preRestoreBackup = await this.createBackup('manual');
      if (!preRestoreBackup.success) {
        console.warn('[BackupService] Failed to create pre-restore backup');
      }

      // Copy backup file to database location
      fs.copyFileSync(backupPath, dbPath);

      // Verify restored database
      const integrityCheck = await this.checkDatabaseIntegrity();
      if (!integrityCheck.isValid) {
        throw new Error(
          `Restored database failed integrity check: ${integrityCheck.error}`
        );
      }

      console.log(
        '[BackupService] ✅ Database restored successfully from:',
        backupPath
      );
      return { success: true };
    } catch (error) {
      console.error('[BackupService] Restore failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown restore error',
      };
    }
  }

  /**
   * Enable or disable backup service
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    console.log(
      `[BackupService] Backup service ${enabled ? 'enabled' : 'disabled'}`
    );
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Ensure backup directory exists
   */
  private ensureBackupDirectory(): void {
    try {
      if (!fs.existsSync(this.backupDir)) {
        fs.mkdirSync(this.backupDir, { recursive: true });
        console.log(
          `[BackupService] Created backup directory: ${this.backupDir}`
        );
      }
    } catch (error) {
      console.error(
        '[BackupService] Failed to create backup directory:',
        error
      );
    }
  }

  /**
   * Create a backup with specified type
   */
  private async createBackup(
    type: 'startup' | 'shutdown' | 'manual'
  ): Promise<BackupResult> {
    try {
      const dbPath = getDatabasePath();

      if (!fs.existsSync(dbPath)) {
        throw new Error('Database file does not exist');
      }

      const timestamp = Date.now();
      const backupFilename = `chromasync-backup-${timestamp}-${type}.db`;
      const backupPath = path.join(this.backupDir, backupFilename);

      // Copy database file
      fs.copyFileSync(dbPath, backupPath);

      // Verify backup
      if (!fs.existsSync(backupPath)) {
        throw new Error('Backup file was not created');
      }

      const stats = fs.statSync(backupPath);
      const checksum = this.calculateFileChecksum(backupPath);

      const backupInfo: BackupInfo = {
        timestamp,
        filepath: backupPath,
        checksum,
        size: stats.size,
        type,
      };

      return {
        success: true,
        backupInfo,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown backup error',
      };
    }
  }

  /**
   * Calculate file checksum for integrity verification
   */
  private calculateFileChecksum(filepath: string): string {
    try {
      const fileBuffer = fs.readFileSync(filepath);
      const hashSum = crypto.createHash('sha256');
      hashSum.update(fileBuffer);
      return hashSum.digest('hex');
    } catch (error) {
      console.warn('[BackupService] Failed to calculate checksum:', error);
      return '';
    }
  }

  /**
   * Clean up old backups to maintain storage limits
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const backups = await this.getBackupList();

      if (backups.length <= this.maxBackups) {
        return; // No cleanup needed
      }

      const backupsToDelete = backups.slice(this.maxBackups);

      for (const backup of backupsToDelete) {
        try {
          fs.unlinkSync(backup.filepath);
          console.log(
            `[BackupService] Deleted old backup: ${path.basename(backup.filepath)}`
          );
        } catch (error) {
          console.warn(
            `[BackupService] Failed to delete backup ${backup.filepath}:`,
            error
          );
        }
      }

      console.log(
        `[BackupService] Cleaned up ${backupsToDelete.length} old backups`
      );
    } catch (error) {
      console.error('[BackupService] Error during backup cleanup:', error);
    }
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export function getBackupService(): BackupService {
  return BackupService.getInstance();
}

// Convenience functions
export async function createStartupBackup(): Promise<BackupResult> {
  return getBackupService().createStartupBackup();
}

export async function createShutdownBackup(): Promise<BackupResult> {
  return getBackupService().createShutdownBackup();
}

export async function createManualBackup(): Promise<BackupResult> {
  return getBackupService().createManualBackup();
}

export async function checkDatabaseIntegrity(): Promise<{
  isValid: boolean;
  error?: string;
}> {
  return getBackupService().checkDatabaseIntegrity();
}
