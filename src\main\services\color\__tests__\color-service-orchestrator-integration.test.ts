/**
 * @file color-service-orchestrator-integration.test.ts
 * @description Integration tests for ColorService orchestrator pattern
 * 
 * Tests that the ColorService properly orchestrates all extracted services:
 * - ColorRepository (data access)
 * - ColorSpaceCalculator (color space calculations)
 * - GradientProcessor (gradient processing)
 * - ColorValidator (validation and standardization)
 * - ColorSyncService (Supabase synchronization)
 * - ColorAnalyticsService (usage analytics)
 */

import { describe, test, expect, beforeEach, vi, afterEach } from 'vitest';
import Database from 'better-sqlite3';
import { ColorService } from '../../../db/services/color.service';
import { ColorRepository } from '../../../db/repositories/color.repository';
import { ColorSpaceCalculator } from '../color-space-calculator.service';
import { GradientProcessor } from '../gradient-processor.service';
import { ColorValidator } from '../color-validator.service';
import { ColorSyncService } from '../color-sync.service';
import { ColorAnalyticsService } from '../color-analytics.service';
import { NewColorEntry, UpdateColorEntry } from '../../../../shared/types/color.types';

// Mock the sync outbox service
vi.mock('../../sync/sync-outbox.service', () => ({
  syncOutboxService: {
    addToOutbox: vi.fn().mockResolvedValue(undefined)
  }
}));

// Mock database for integration testing
let mockDb: any;
let colorService: ColorService;
let colorRepository: ColorRepository;
let colorSpaceCalculator: ColorSpaceCalculator;
let gradientProcessor: GradientProcessor;
let colorValidator: ColorValidator;
let colorSyncService: ColorSyncService;
let colorAnalyticsService: ColorAnalyticsService;

// Mock data for integration tests
const testOrganizationId = 'test-org-123';
const testUserId = 'test-user-456';

describe('ColorService Orchestrator Integration Tests', () => {
  beforeEach(() => {
    // Create mock database with realistic behavior
    mockDb = {
      prepare: vi.fn((sql: string) => ({
        get: vi.fn().mockReturnValue(null),
        run: vi.fn().mockReturnValue({ changes: 1, lastInsertRowid: 1 }),
        all: vi.fn().mockReturnValue([])
      })),
      exec: vi.fn(),
      close: vi.fn()
    };

    // Create real service instances for integration testing
    colorRepository = new ColorRepository(mockDb);
    colorSpaceCalculator = new ColorSpaceCalculator();
    gradientProcessor = new GradientProcessor();
    colorValidator = new ColorValidator();
    colorSyncService = new ColorSyncService(mockDb, colorRepository, colorValidator, gradientProcessor);
    colorAnalyticsService = new ColorAnalyticsService(mockDb, colorRepository);

    // Create ColorService with all dependencies injected (orchestrator pattern)
    colorService = new ColorService(
      mockDb,
      colorRepository,
      colorSpaceCalculator,
      gradientProcessor,
      colorValidator,
      colorSyncService,
      colorAnalyticsService
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Service Orchestrator Architecture', () => {
    test('should have all services properly injected', () => {
      // Verify all services are injected into ColorService
      expect((colorService as any).colorRepository).toBeInstanceOf(ColorRepository);
      expect((colorService as any).colorSpaceCalculator).toBeInstanceOf(ColorSpaceCalculator);
      expect((colorService as any).gradientProcessor).toBeInstanceOf(GradientProcessor);
      expect((colorService as any).colorValidator).toBeInstanceOf(ColorValidator);
      expect((colorService as any).colorSyncService).toBeInstanceOf(ColorSyncService);
      expect((colorService as any).colorAnalyticsService).toBeInstanceOf(ColorAnalyticsService);
    });

    test('should create default service instances when none provided', () => {
      const serviceWithDefaults = new ColorService(mockDb);
      
      expect((serviceWithDefaults as any).colorRepository).toBeInstanceOf(ColorRepository);
      expect((serviceWithDefaults as any).colorSpaceCalculator).toBeInstanceOf(ColorSpaceCalculator);
      expect((serviceWithDefaults as any).gradientProcessor).toBeInstanceOf(GradientProcessor);
      expect((serviceWithDefaults as any).colorValidator).toBeInstanceOf(ColorValidator);
      expect((serviceWithDefaults as any).colorSyncService).toBeInstanceOf(ColorSyncService);
      expect((serviceWithDefaults as any).colorAnalyticsService).toBeInstanceOf(ColorAnalyticsService);
    });

    test('should delegate data access operations to ColorRepository', () => {
      const repositorySpy = vi.spyOn(colorRepository, 'findAll');
      repositorySpy.mockReturnValue([]);

      colorService.getAll(testOrganizationId);

      expect(repositorySpy).toHaveBeenCalledWith(testOrganizationId);
    });

    test('should delegate validation operations to ColorValidator', () => {
      const validatorSpy = vi.spyOn(colorValidator, 'validateHex');
      validatorSpy.mockReturnValue({ isValid: true, errors: [], warnings: [] });

      const newColor: NewColorEntry = {
        name: 'Test Color',
        hex: '#FF0000',
        cmyk: 'C:0 M:100 Y:100 K:0'
      };

      // Mock repository insert to avoid actual database operations
      vi.spyOn(colorRepository, 'insert').mockReturnValue('test-id');

      colorService.add(newColor, testUserId, testOrganizationId);

      expect(validatorSpy).toHaveBeenCalledWith('#FF0000');
    });

    test('should delegate color space calculations to ColorSpaceCalculator', () => {
      const calculatorSpy = vi.spyOn(colorSpaceCalculator, 'getCalculatedColorSpaces');
      calculatorSpy.mockReturnValue({ rgb: 'rgb(255,0,0)', hsl: 'hsl(0,100%,50%)', lab: 'lab(53.24 80.09 67.20)' });

      // Mock repository to return a color entry
      vi.spyOn(colorRepository, 'findAll').mockReturnValue([{
        id: 'test-id',  // Direct UUID mapping
        hex: '#FF0000',
        display_name: 'Test Color',
        code: 'TEST-001',
        color_spaces: '{"cmyk":{"c":0,"m":100,"y":100,"k":0}}',
        is_library: 0,
        product_name: 'Test Product'
      }]);

      colorService.getAll(testOrganizationId);

      expect(calculatorSpy).toHaveBeenCalledWith('#FF0000');
    });

    test('should delegate gradient processing to GradientProcessor', () => {
      const processorSpy = vi.spyOn(gradientProcessor, 'createGradientColorsCSV');
      processorSpy.mockReturnValue('255,0,0,0.0;0,255,0,1.0');

      const gradientColor: NewColorEntry = {
        name: 'Gradient Color',
        hex: '#FF0000',
        gradient: {
          gradientStops: [
            { color: '#FF0000', position: 0 },
            { color: '#00FF00', position: 1 }
          ]
        }
      };

      // Mock repository insert and validator
      vi.spyOn(colorRepository, 'insert').mockReturnValue('gradient-id');
      vi.spyOn(colorValidator, 'validateHex').mockReturnValue({ isValid: true, errors: [], warnings: [] });
      vi.spyOn(colorValidator, 'standardizeHex').mockReturnValue('#FF0000');
      vi.spyOn(colorValidator, 'normalizeColorName').mockReturnValue('Gradient Color');
      vi.spyOn(colorValidator, 'standardizeColorCode').mockReturnValue('GRAD-001');

      colorService.add(gradientColor, testUserId, testOrganizationId);

      expect(processorSpy).toHaveBeenCalledWith(gradientColor.gradient!.gradientStops);
    });

    test('should delegate sync operations to ColorSyncService', () => {
      const syncSpy = vi.spyOn(colorSyncService, 'getUnsyncedColors');
      syncSpy.mockReturnValue([]);

      colorService.getUnsynced();

      expect(syncSpy).toHaveBeenCalled();
    });

    test('should delegate analytics operations to ColorAnalyticsService', () => {
      const analyticsSpy = vi.spyOn(colorAnalyticsService, 'getColorUsageCounts');
      analyticsSpy.mockReturnValue(new Map());

      colorService.getColorUsageCounts(testOrganizationId);

      expect(analyticsSpy).toHaveBeenCalledWith(testOrganizationId);
    });
  });

  describe('Cross-Service Integration Workflows', () => {
    test('should orchestrate complete color addition workflow', async () => {
      // Mock all service dependencies
      const validateHexSpy = vi.spyOn(colorValidator, 'validateHex').mockReturnValue({ isValid: true, errors: [], warnings: [] });
      const standardizeHexSpy = vi.spyOn(colorValidator, 'standardizeHex').mockReturnValue('#FF0000');
      const validateCMYKSpy = vi.spyOn(colorValidator, 'validateCMYK').mockReturnValue({ isValid: true, errors: [], warnings: [] });
      const standardizeCMYKSpy = vi.spyOn(colorValidator, 'standardizeCMYK').mockReturnValue('C:0 M:100 Y:100 K:0');
      const normalizeNameSpy = vi.spyOn(colorValidator, 'normalizeColorName').mockReturnValue('Test Red');
      const standardizeCodeSpy = vi.spyOn(colorValidator, 'standardizeColorCode').mockReturnValue('TEST-RED-001');
      const insertSpy = vi.spyOn(colorRepository, 'insert').mockReturnValue('new-color-id');

      // Mock source lookup
      mockDb.prepare.mockReturnValue({
        get: vi.fn().mockReturnValue({ id: 1 }),
        run: vi.fn(),
        all: vi.fn()
      });

      const newColor: NewColorEntry = {
        name: 'test red',
        hex: '#ff0000',
        cmyk: 'c:0 m:100 y:100 k:0',
        source: 'user'
      };

      const result = await colorService.add(newColor, testUserId, testOrganizationId);

      // Verify orchestration workflow
      expect(validateHexSpy).toHaveBeenCalledWith('#ff0000');
      expect(standardizeHexSpy).toHaveBeenCalledWith('#ff0000');
      expect(validateCMYKSpy).toHaveBeenCalledWith('c:0 m:100 y:100 k:0');
      expect(standardizeCMYKSpy).toHaveBeenCalledWith('c:0 m:100 y:100 k:0');
      expect(normalizeNameSpy).toHaveBeenCalledWith('test red');
      expect(standardizeCodeSpy).toHaveBeenCalledWith('test red');
      expect(insertSpy).toHaveBeenCalled();
      expect(result).toBe('new-color-id');
    });

    test('should orchestrate complete color update workflow with validation', async () => {
      const validateHexSpy = vi.spyOn(colorValidator, 'validateHex').mockReturnValue({ isValid: true, errors: [], warnings: [] });
      const standardizeHexSpy = vi.spyOn(colorValidator, 'standardizeHex').mockReturnValue('#00FF00');
      const normalizeNameSpy = vi.spyOn(colorValidator, 'normalizeColorName').mockReturnValue('Updated Green');
      const hexToCmykSpy = vi.spyOn(colorSpaceCalculator, 'hexToCmyk').mockReturnValue({ c: 100, m: 0, y: 100, k: 0 });
      const updateSpy = vi.spyOn(colorRepository, 'update').mockReturnValue(true);

      const colorUpdates: UpdateColorEntry = {
        name: 'updated green',
        hex: '#00ff00'
      };

      const result = await colorService.update('color-id', colorUpdates, testOrganizationId);

      // Verify orchestration workflow
      expect(validateHexSpy).toHaveBeenCalledWith('#00ff00');
      expect(standardizeHexSpy).toHaveBeenCalledWith('#00ff00');
      expect(normalizeNameSpy).toHaveBeenCalledWith('updated green');
      expect(hexToCmykSpy).toHaveBeenCalledWith('#00FF00');
      expect(updateSpy).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    test('should orchestrate gradient color workflow with multiple services', async () => {
      const validateHexSpy = vi.spyOn(colorValidator, 'validateHex').mockReturnValue({ isValid: true, errors: [], warnings: [] });
      const standardizeHexSpy = vi.spyOn(colorValidator, 'standardizeHex').mockReturnValue('#FF0000');
      const normalizeNameSpy = vi.spyOn(colorValidator, 'normalizeColorName').mockReturnValue('Gradient Color');
      const standardizeCodeSpy = vi.spyOn(colorValidator, 'standardizeColorCode').mockReturnValue('GRAD-001');
      const hexToCmykSpy = vi.spyOn(colorSpaceCalculator, 'hexToCmyk').mockReturnValue({ c: 0, m: 100, y: 100, k: 0 });
      const createGradientCSVSpy = vi.spyOn(gradientProcessor, 'createGradientColorsCSV').mockReturnValue('255,0,0,0.0;0,255,0,1.0');
      const insertSpy = vi.spyOn(colorRepository, 'insert').mockReturnValue('gradient-id');

      // Mock source lookup
      mockDb.prepare.mockReturnValue({
        get: vi.fn().mockReturnValue({ id: 1 }),
        run: vi.fn(),
        all: vi.fn()
      });

      const gradientColor: NewColorEntry = {
        name: 'gradient color',
        hex: '#ff0000',
        gradient: {
          gradientStops: [
            { color: '#FF0000', position: 0 },
            { color: '#00FF00', position: 1 }
          ]
        }
      };

      const result = await colorService.add(gradientColor, testUserId, testOrganizationId);

      // Verify all services were orchestrated correctly
      expect(validateHexSpy).toHaveBeenCalledWith('#ff0000');
      expect(standardizeHexSpy).toHaveBeenCalledWith('#ff0000');
      expect(normalizeNameSpy).toHaveBeenCalledWith('gradient color');
      expect(standardizeCodeSpy).toHaveBeenCalledWith('gradient color');
      expect(hexToCmykSpy).toHaveBeenCalledWith('#FF0000');
      expect(createGradientCSVSpy).toHaveBeenCalledWith(gradientColor.gradient!.gradientStops);
      expect(insertSpy).toHaveBeenCalled();
      expect(result).toBe('gradient-id');
    });

    test('should orchestrate color retrieval with runtime calculations', () => {
      const findAllSpy = vi.spyOn(colorRepository, 'findAll').mockReturnValue([{
        id: 'test-id',  // Direct UUID mapping
        hex: '#0000FF',
        display_name: 'Test Blue',
        code: 'BLUE-001',
        color_spaces: '{"cmyk":{"c":100,"m":100,"y":0,"k":0}}',
        is_library: 0,
        product_name: 'Test Product',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      }]);

      const getColorSpacesSpy = vi.spyOn(colorSpaceCalculator, 'getCalculatedColorSpaces').mockReturnValue({
        rgb: 'rgb(0,0,255)',
        hsl: 'hsl(240,100%,50%)',
        lab: 'lab(32.30 79.19 -107.86)'
      });

      const invalidateOrphansSpy = vi.spyOn(colorRepository, 'invalidateOrphans').mockImplementation(() => {});

      const results = colorService.getAll(testOrganizationId);

      // Verify orchestration
      expect(findAllSpy).toHaveBeenCalledWith(testOrganizationId);
      expect(getColorSpacesSpy).toHaveBeenCalledWith('#0000FF');
      // invalidateOrphans is only called if there are orphan colors (colors without product_name and not library)
      // Since our test data has product_name, no orphans exist, so invalidateOrphans won't be called
      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        id: 'test-id',
        name: 'Test Blue',
        hex: '#0000FF',
        cmyk: 'C:100 M:100 Y:0 K:0',
        rgb: 'rgb(0,0,255)',
        hsl: 'hsl(240,100%,50%)',
        lab: 'lab(32.30 79.19 -107.86)',
        product: 'Test Product'
      });
    });

    test('should orchestrate analytics workflow with usage data', async () => {
      const getUsageCountsSpy = vi.spyOn(colorAnalyticsService, 'getColorUsageCounts').mockReturnValue(new Map([
        ['Red', { count: 3, products: ['Product A', 'Product B', 'Product C'] }],
        ['Blue', { count: 2, products: ['Product D', 'Product E'] }]
      ]));

      const getAllSpy = vi.spyOn(colorService, 'getAll').mockReturnValue([
        {
          id: 'red-id',
          name: 'Red',
          hex: '#FF0000',
          cmyk: 'C:0 M:100 Y:100 K:0',
          source: 'user',
          code: 'RED-001',
          product: 'Product A',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
          organizationId: testOrganizationId,
          isLibrary: false
        }
      ]);

      const result = await colorService.getAllWithUsage(testOrganizationId);

      expect(getAllSpy).toHaveBeenCalledWith(testOrganizationId);
      expect(getUsageCountsSpy).toHaveBeenCalledWith(testOrganizationId);
      expect(result).toMatchObject({
        colors: expect.any(Array),
        usageCounts: {
          'Red': { count: 3, products: ['Product A', 'Product B', 'Product C'] },
          'Blue': { count: 2, products: ['Product D', 'Product E'] }
        },
        organizationId: testOrganizationId,
        totalColors: 1,
        colorsWithUsage: 2
      });
    });
  });

  describe('Error Handling and Service Integration', () => {
    test('should handle validation errors gracefully across services', async () => {
      const validateHexSpy = vi.spyOn(colorValidator, 'validateHex').mockReturnValue({
        isValid: false,
        errors: ['Invalid HEX format'],
        warnings: []
      });

      const newColor: NewColorEntry = {
        name: 'Invalid Color',
        hex: 'invalid-hex'
      };

      await expect(colorService.add(newColor, testUserId, testOrganizationId)).rejects.toThrow('Invalid HEX color: Invalid HEX format');
      expect(validateHexSpy).toHaveBeenCalledWith('invalid-hex');
    });

    test('should handle repository errors in orchestration', () => {
      const findAllSpy = vi.spyOn(colorRepository, 'findAll').mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      const results = colorService.getAll(testOrganizationId);

      expect(findAllSpy).toHaveBeenCalledWith(testOrganizationId);
      expect(results).toEqual([]); // Should return empty array on error
    });

    test('should handle color space calculation errors', () => {
      const findAllSpy = vi.spyOn(colorRepository, 'findAll').mockReturnValue([{
        id: 'test-id',  // Direct UUID mapping
        hex: 'invalid-hex',
        display_name: 'Test Color',
        code: 'TEST-001',
        color_spaces: '{"cmyk":{"c":0,"m":0,"y":0,"k":0}}',
        is_library: 0,
        product_name: 'Test Product'
      }]);

      const getColorSpacesSpy = vi.spyOn(colorSpaceCalculator, 'getCalculatedColorSpaces').mockImplementation(() => {
        throw new Error('Invalid color format');
      });

      const invalidateOrphansSpy = vi.spyOn(colorRepository, 'invalidateOrphans').mockImplementation(() => {});

      const results = colorService.getAll(testOrganizationId);

      expect(findAllSpy).toHaveBeenCalledWith(testOrganizationId);
      expect(getColorSpacesSpy).toHaveBeenCalledWith('invalid-hex');
      // No orphans in this test data (has product_name), so invalidateOrphans won't be called
      expect(results).toHaveLength(1);
      // Should include color entry with null color spaces due to error
      expect(results[0]).toMatchObject({
        id: 'test-id',
        hex: 'invalid-hex'
      });
      // Verify that the runtime color space values are null/undefined due to the error
      expect(results[0].rgb).toBeFalsy(); // null or undefined
      expect(results[0].hsl).toBeFalsy(); // null or undefined
      expect(results[0].lab).toBeFalsy(); // null or undefined
    });

    test('should handle analytics service errors gracefully', () => {
      const getUsageCountsSpy = vi.spyOn(colorAnalyticsService, 'getColorUsageCounts').mockImplementation(() => {
        throw new Error('Analytics calculation failed');
      });

      const result = colorService.getColorUsageCounts(testOrganizationId);

      expect(getUsageCountsSpy).toHaveBeenCalledWith(testOrganizationId);
      expect(result).toEqual(new Map()); // Should return empty Map on error
    });
  });

  describe('Service Lifecycle and Dependencies', () => {
    test('should properly initialize all services in dependency order', () => {
      // Verify that services with dependencies get their dependencies injected
      expect((colorSyncService as any).colorRepository).toBe(colorRepository);
      expect((colorSyncService as any).colorValidator).toBe(colorValidator);
      expect((colorSyncService as any).gradientProcessor).toBe(gradientProcessor);
      expect((colorAnalyticsService as any).colorRepository).toBe(colorRepository);
    });

    test('should handle service initialization failures', () => {
      expect(() => {
        new ColorService(null as any);
      }).toThrow('ColorService requires a valid database instance');
    });

    test('should maintain service isolation during operations', async () => {
      // Test that services don't interfere with each other's state
      const validateSpy1 = vi.spyOn(colorValidator, 'validateHex');
      const validateSpy2 = vi.spyOn(colorValidator, 'validateCMYK');
      const calculateSpy = vi.spyOn(colorSpaceCalculator, 'hexToCmyk');

      validateSpy1.mockReturnValue({ isValid: true, errors: [], warnings: [] });
      validateSpy2.mockReturnValue({ isValid: true, errors: [], warnings: [] });
      calculateSpy.mockReturnValue({ c: 0, m: 100, y: 100, k: 0 });

      const insertSpy = vi.spyOn(colorRepository, 'insert').mockReturnValue('test-id-1');
      
      // Mock other required calls
      vi.spyOn(colorValidator, 'standardizeHex').mockReturnValue('#FF0000');
      vi.spyOn(colorValidator, 'standardizeCMYK').mockReturnValue('C:0 M:100 Y:100 K:0');
      vi.spyOn(colorValidator, 'normalizeColorName').mockReturnValue('Test Color');
      vi.spyOn(colorValidator, 'standardizeColorCode').mockReturnValue('TEST-001');

      mockDb.prepare.mockReturnValue({
        get: vi.fn().mockReturnValue({ id: 1 }),
        run: vi.fn(),
        all: vi.fn()
      });

      // Use colors without CMYK so hexToCmyk gets called
      const color1: NewColorEntry = { name: 'Color 1', hex: '#FF0000' };
      const color2: NewColorEntry = { name: 'Color 2', hex: '#00FF00' };

      await colorService.add(color1, testUserId, testOrganizationId);
      
      // Reset mocks for second color
      insertSpy.mockReturnValue('test-id-2');
      validateSpy1.mockClear();
      calculateSpy.mockClear();
      vi.spyOn(colorValidator, 'standardizeHex').mockReturnValue('#00FF00');
      calculateSpy.mockReturnValue({ c: 100, m: 0, y: 100, k: 0 });

      await colorService.add(color2, testUserId, testOrganizationId);

      // Verify services were called independently
      expect(validateSpy1).toHaveBeenCalledTimes(1); // Called once for second color after clear
      expect(calculateSpy).toHaveBeenCalledTimes(1); // Called once for second color after clear
      expect(insertSpy).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance and Optimization Integration', () => {
    test('should efficiently orchestrate services for bulk operations', () => {
      const colors = Array.from({ length: 100 }, (_, i) => ({
        id: `color-${i}`,  // Direct UUID mapping
        hex: '#FF0000',
        display_name: `Color ${i}`,
        code: `COLOR-${i}`,
        color_spaces: '{"cmyk":{"c":0,"m":100,"y":100,"k":0}}',
        is_library: 0,
        product_name: `Product ${i}`
      }));

      const findAllSpy = vi.spyOn(colorRepository, 'findAll').mockReturnValue(colors);
      const getColorSpacesSpy = vi.spyOn(colorSpaceCalculator, 'getCalculatedColorSpaces').mockReturnValue({
        rgb: 'rgb(255,0,0)',
        hsl: 'hsl(0,100%,50%)',
        lab: 'lab(53.24 80.09 67.20)'
      });

      const invalidateOrphansSpy = vi.spyOn(colorRepository, 'invalidateOrphans').mockImplementation(() => {});

      const startTime = performance.now();
      const results = colorService.getAll(testOrganizationId);
      const endTime = performance.now();

      expect(findAllSpy).toHaveBeenCalledOnce();
      expect(getColorSpacesSpy).toHaveBeenCalledTimes(100);
      // No invalidateOrphans call expected since all colors have product_name
      expect(results).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(100); // Should be efficient
    });

    test('should cache and reuse service instances across operations', () => {
      // Verify that the same service instances are reused
      const service1 = (colorService as any).colorValidator;
      const service2 = (colorService as any).colorValidator;
      
      expect(service1).toBe(service2); // Same instance
      expect(service1).toBe(colorValidator); // Same as injected instance
    });
  });
});