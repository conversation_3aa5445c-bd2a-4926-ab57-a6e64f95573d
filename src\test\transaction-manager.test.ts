/**
 * Unit tests for Database Transaction Management System
 * Tests transaction rollback scenarios, savepoints, and error handling
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DatabaseTransactionManager, executeInTransaction } from '../main/db/core/transaction-manager';
import { TransactionAwareSyncManager } from '../main/services/sync/transaction-aware-sync';
import { syncOutboxService, SyncOutboxItem } from '../main/services/sync/sync-outbox.service';

// Mock better-sqlite3 database with proper transaction behavior
const mockDatabase = {
  transaction: vi.fn(),
  exec: vi.fn(),
  prepare: vi.fn(() => ({
    run: vi.fn(),
    get: vi.fn(),
    all: vi.fn()
  })),
  close: vi.fn()
};

// Mock database connection
vi.mock('../main/db/core/connection', () => ({
  getPooledConnection: vi.fn(() => Promise.resolve(mockDatabase)),
  releasePooledConnection: vi.fn()
}));

// Mock sync outbox service
vi.mock('../main/services/sync/sync-outbox.service', () => ({
  syncOutboxService: {
    markAsSynced: vi.fn(),
    markAsFailed: vi.fn(),
    getPendingChanges: vi.fn(() => [])
  }
}));

describe('DatabaseTransactionManager', () => {
  let transactionManager: DatabaseTransactionManager;
  
  beforeEach(() => {
    transactionManager = DatabaseTransactionManager.getInstance();
    vi.clearAllMocks();
  });
  
  afterEach(() => {
    // Clear transaction logs to prevent memory leaks in tests
    transactionManager.clearOldLogs(0);
  });

  describe('Basic Transaction Operations', () => {
    it('should execute operation in transaction successfully', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);
      
      const result = await transactionManager.executeInTransaction(
        mockOperation,
        { enableLogging: true }
      );
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.transactionId).toBeDefined();
      expect(result.duration).toBeGreaterThanOrEqual(0);
      expect(mockOperation).toHaveBeenCalledWith(mockDatabase, result.transactionId);
    });

    it('should handle transaction rollback on operation failure', async () => {
      const testError = new Error('Operation failed');
      const mockOperation = vi.fn().mockRejectedValue(testError);
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        fn(mockDatabase); // Execute the function first
        throw testError; // Then throw error to simulate transaction rollback
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);
      
      const result = await transactionManager.executeInTransaction(
        mockOperation,
        { enableLogging: true }
      );
      
      expect(result.success).toBe(false);
      expect(result.error).toBe(testError);
      expect(result.transactionId).toBeDefined();
      expect(result.logEntries).toHaveLength(2); // START and ROLLBACK
      expect(result.logEntries[1].operation).toBe('TRANSACTION_ROLLBACK');
      expect(result.logEntries[1].error).toBe('Operation failed');
    });

    it('should handle transaction timeout', async () => {
      const slowOperation = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100))
      );
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase);
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);
      
      const result = await transactionManager.executeInTransaction(
        slowOperation,
        { timeout: 50, enableLogging: true }
      );
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('timeout');
    });
  });

  describe('Savepoint Management', () => {
    it('should create and manage savepoints', async () => {
      const savepointName = 'test_savepoint';
      
      await transactionManager.createSavepoint(mockDatabase, 'test-tx', savepointName);
      
      expect(mockDatabase.exec).toHaveBeenCalledWith(`SAVEPOINT ${savepointName}`);
      
      const savepoints = transactionManager.getActiveSavepoints('test-tx');
      expect(savepoints).toHaveLength(1);
      expect(savepoints[0].name).toBe(savepointName);
    });

    it('should rollback to savepoint', async () => {
      const savepointName = 'test_savepoint';
      
      await transactionManager.createSavepoint(mockDatabase, 'test-tx', savepointName);
      await transactionManager.rollbackToSavepoint(mockDatabase, 'test-tx', savepointName);
      
      expect(mockDatabase.exec).toHaveBeenCalledWith(`ROLLBACK TO SAVEPOINT ${savepointName}`);
    });

    it('should release savepoint', async () => {
      const savepointName = 'test_savepoint';
      
      await transactionManager.createSavepoint(mockDatabase, 'test-tx', savepointName);
      await transactionManager.releaseSavepoint(mockDatabase, 'test-tx', savepointName);
      
      expect(mockDatabase.exec).toHaveBeenCalledWith(`RELEASE SAVEPOINT ${savepointName}`);
      
      const savepoints = transactionManager.getActiveSavepoints('test-tx');
      expect(savepoints).toHaveLength(0);
    });
  });

  describe('Transaction Logging', () => {
    it('should log transaction operations when enabled', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);
      
      const result = await transactionManager.executeInTransaction(
        mockOperation,
        { enableLogging: true }
      );
      
      expect(result.logEntries).toHaveLength(2); // START and COMMIT
      expect(result.logEntries[0].operation).toBe('TRANSACTION_START');
      expect(result.logEntries[1].operation).toBe('TRANSACTION_COMMIT');
      
      const storedLogs = transactionManager.getTransactionLogs(result.transactionId);
      expect(storedLogs).toHaveLength(2);
    });

    it('should not log when logging is disabled', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);
      
      const result = await transactionManager.executeInTransaction(
        mockOperation,
        { enableLogging: false }
      );
      
      expect(result.logEntries).toHaveLength(0);
    });

    it('should clear old transaction logs', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);
      
      // Create some transactions
      await transactionManager.executeInTransaction(mockOperation, { enableLogging: true });
      await transactionManager.executeInTransaction(mockOperation, { enableLogging: true });
      
      const statsBefore = transactionManager.getStats();
      expect(statsBefore.totalLoggedTransactions).toBe(2);
      
      // Clear logs older than 0ms (all logs)
      const clearedCount = transactionManager.clearOldLogs(0);
      
      expect(clearedCount).toBe(2);
      const statsAfter = transactionManager.getStats();
      expect(statsAfter.totalLoggedTransactions).toBe(0);
    });
  });

  describe('Transaction Statistics', () => {
    it('should provide accurate transaction statistics', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);
      
      const initialStats = transactionManager.getStats();
      
      await transactionManager.executeInTransaction(mockOperation, { enableLogging: true });
      
      const finalStats = transactionManager.getStats();
      expect(finalStats.totalLoggedTransactions).toBe(initialStats.totalLoggedTransactions + 1);
      expect(finalStats.memoryUsage.logEntries).toBeGreaterThan(initialStats.memoryUsage.logEntries);
    });
  });
});

describe('TransactionAwareSyncManager', () => {
  let syncManager: TransactionAwareSyncManager;
  
  beforeEach(() => {
    syncManager = TransactionAwareSyncManager.getInstance();
    vi.clearAllMocks();
  });

  describe('Outbox Processing with Transactions', () => {
    it('should process outbox items in transaction with rollback on failure', async () => {
      const mockOutboxItems: SyncOutboxItem[] = [
        {
          id: 'item1',
          table: 'colors',
          action: 'create',
          data: { id: 'color1', name: 'Red' },
          timestamp: Date.now(),
          attemptCount: 0,
          maxAttempts: 3,
          nextAttemptTime: Date.now()
        },
        {
          id: 'item2',
          table: 'colors',
          action: 'update',
          data: { id: 'color2', name: 'Blue' },
          timestamp: Date.now(),
          attemptCount: 0,
          maxAttempts: 3,
          nextAttemptTime: Date.now()
        }
      ];

      const mockProcessor = vi.fn()
        .mockResolvedValueOnce(undefined) // First item succeeds
        .mockRejectedValueOnce(new Error('Processing failed')); // Second item fails

      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);

      const result = await syncManager.processOutboxInTransaction(
        mockOutboxItems,
        'org1',
        'user1',
        mockProcessor
      );

      expect(result.success).toBe(true); // Transaction succeeds even if some items fail
      expect(result.itemsProcessed).toBe(1); // Only first item processed successfully
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('Processing failed');
      
      // Verify outbox service calls
      expect(syncOutboxService.markAsSynced).toHaveBeenCalledWith('item1');
      expect(syncOutboxService.markAsFailed).toHaveBeenCalledWith('item2', 'Processing failed');
    });

    it('should handle empty outbox gracefully', async () => {
      const result = await syncManager.processOutboxInTransaction(
        [],
        'org1',
        'user1',
        vi.fn()
      );

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(0);
      expect(result.errors).toHaveLength(0);
      expect(result.transactionId).toBe('no-transaction');
    });

    it('should process outbox items sequentially', async () => {
      // Create 5 items to test sequential processing
      const mockOutboxItems: SyncOutboxItem[] = Array.from({ length: 5 }, (_, i) => ({
        id: `item${i + 1}`,
        table: 'colors',
        action: 'create',
        data: { id: `color${i + 1}`, name: `Color ${i + 1}` },
        timestamp: Date.now(),
        attemptCount: 0,
        maxAttempts: 3,
        nextAttemptTime: Date.now()
      }));

      const mockProcessor = vi.fn().mockResolvedValue(undefined);
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);

      const result = await syncManager.processOutboxInTransaction(
        mockOutboxItems,
        'org1',
        'user1',
        mockProcessor
      );

      expect(result.success).toBe(true);
      expect(result.itemsProcessed).toBe(5);
      expect(mockProcessor).toHaveBeenCalledTimes(5);
    });
  });

  describe('Complex Operations with Rollback', () => {
    it('should execute multiple operations with rollback on failure', async () => {
      const operations = [
        {
          name: 'operation1',
          operation: vi.fn().mockResolvedValue('result1'),
          rollback: vi.fn().mockResolvedValue(undefined)
        },
        {
          name: 'operation2',
          operation: vi.fn().mockRejectedValue(new Error('Operation 2 failed')),
          rollback: vi.fn().mockResolvedValue(undefined)
        }
      ];

      const mockTransaction = vi.fn().mockImplementation((fn) => {
        fn(mockDatabase); // Execute the function first
        throw new Error('Operation 2 failed'); // Then throw error to simulate transaction rollback
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);

      const result = await syncManager.executeWithRollback(
        operations,
        'org1',
        'user1'
      );

      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('Operation 2 failed');
      
      // First operation should have been called
      expect(operations[0].operation).toHaveBeenCalled();
      // Rollback should have been called for completed operations
      expect(operations[0].rollback).toHaveBeenCalled();
    });

    it('should complete all operations successfully', async () => {
      const operations = [
        {
          name: 'operation1',
          operation: vi.fn().mockResolvedValue('result1')
        },
        {
          name: 'operation2',
          operation: vi.fn().mockResolvedValue('result2')
        }
      ];

      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);

      const result = await syncManager.executeWithRollback(
        operations,
        'org1',
        'user1'
      );

      expect(result.success).toBe(true);
      expect(result.result).toEqual(['result1', 'result2']);
      
      // Both operations should have been called
      expect(operations[0].operation).toHaveBeenCalled();
      expect(operations[1].operation).toHaveBeenCalled();
    });
  });

  describe('Transaction Manager Integration', () => {
    it('should provide transaction logs for debugging', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);

      const result = await syncManager.executeSyncInTransaction(
        mockOperation,
        'org1',
        'user1',
        { enableLogging: true }
      );

      expect(result.success).toBe(true);
      
      const logs = syncManager.getTransactionLogs(result.transactionId);
      expect(logs).toHaveLength(2); // START and COMMIT
    });

    it('should provide transaction statistics', () => {
      const stats = syncManager.getStats();
      
      expect(stats).toHaveProperty('activeTransactions');
      expect(stats).toHaveProperty('totalLoggedTransactions');
      expect(stats).toHaveProperty('totalSavepoints');
      expect(stats).toHaveProperty('memoryUsage');
      expect(typeof stats.activeTransactions).toBe('number');
    });
  });
});

describe('Integration Tests', () => {
  describe('Convenience Function', () => {
    it('should execute transaction using convenience function', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success');
      const mockTransaction = vi.fn().mockImplementation((fn) => {
        return fn(mockDatabase); // Execute the function with the database
      });
      mockDatabase.transaction.mockReturnValue(mockTransaction);

      const result = await executeInTransaction(
        mockOperation,
        { enableLogging: true }
      );

      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(mockOperation).toHaveBeenCalled();
    });
  });

  describe('Error Scenarios', () => {
    it('should handle database connection failures', async () => {
      const { getPooledConnection } = await import('../main/db/core/connection');
      vi.mocked(getPooledConnection).mockRejectedValueOnce(new Error('Connection failed'));

      const result = await executeInTransaction(
        vi.fn(),
        { enableLogging: true }
      );

      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('Connection failed');
    });

    it('should handle savepoint creation failures', async () => {
      mockDatabase.exec.mockImplementationOnce(() => {
        throw new Error('Savepoint creation failed');
      });

      const transactionManager = DatabaseTransactionManager.getInstance();
      
      await expect(
        transactionManager.createSavepoint(mockDatabase, 'test-tx', 'test_sp')
      ).rejects.toThrow('Savepoint creation failed');
    });

    it('should handle rollback failures gracefully', async () => {
      mockDatabase.exec.mockImplementationOnce(() => {
        throw new Error('Rollback failed');
      });

      const transactionManager = DatabaseTransactionManager.getInstance();
      
      await expect(
        transactionManager.rollbackToSavepoint(mockDatabase, 'test-tx', 'test_sp')
      ).rejects.toThrow('Rollback failed');
    });
  });
});