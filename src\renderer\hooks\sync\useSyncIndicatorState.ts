import { useState } from 'react';
import { SyncStatus as SyncStatusEnum } from '../../../shared/constants/sync-status';
import { useSyncStatus } from '../../store/sync.store';

export interface SyncProgress {
  phase: string;
  progress: number;
  currentOperation: string;
  itemsProcessed: number;
  itemsTotal: number;
  estimatedTimeRemaining?: number;
  errors: string[];
  warnings: string[];
}

export interface QueueStats {
  memoryQueue: {
    size: number;
    pending: number;
    failed: number;
  };
  persistentQueue: {
    size: number;
    pending: number;
    failed: number;
    oldestItemAge: number;
  };
}

export interface SyncMetrics {
  performance: {
    averageResponseTime: number;
    operationsPerMinute: number;
    errorRate: number;
  };
  health: {
    status: string;
    networkQuality: string;
    lastHealthCheck: number;
  };
}

export function useSyncIndicatorState() {
  const { status: storeStatus, lastSyncTime } = useSyncStatus();

  const [status, setStatus] = useState<SyncStatusEnum>(storeStatus || SyncStatusEnum.IDLE);
  const [isManualSyncing, setIsManualSyncing] = useState(false);
  const [hasUnsyncedChanges, setHasUnsyncedChanges] = useState(false);
  const [lastSync, setLastSync] = useState<number | null>(lastSyncTime);
  const [tooltip, setTooltip] = useState<string>('');
  const [storeRefreshStatus, setStoreRefreshStatus] = useState<{
    colorStore: boolean;
    productStore: boolean;
  }>({ colorStore: false, productStore: false });
  const [syncProgress, setSyncProgress] = useState<SyncProgress | null>({
    phase: 'idle',
    progress: 0,
    currentOperation: '',
    itemsProcessed: 0,
    itemsTotal: 0,
    errors: [],
    warnings: [],
  });
  const [queueStats, setQueueStats] = useState<QueueStats | null>({
    memoryQueue: { size: 0, pending: 0, failed: 0 },
    persistentQueue: { size: 0, pending: 0, failed: 0, oldestItemAge: 0 },
  });
  const [metrics, setMetrics] = useState<SyncMetrics>({
    performance: { averageResponseTime: 0, operationsPerMinute: 0, errorRate: 0 },
    health: { status: 'unknown', networkQuality: 'unknown', lastHealthCheck: 0 },
  });
  const [currentPhase, setCurrentPhase] = useState<string>('');
  const [detailedMessage, setDetailedMessage] = useState<string>('');
  const [showDetailedView, setShowDetailedView] = useState(false);

  return {
    status,
    setStatus,
    isManualSyncing,
    setIsManualSyncing,
    hasUnsyncedChanges,
    setHasUnsyncedChanges,
    lastSync,
    setLastSync,
    tooltip,
    setTooltip,
    storeRefreshStatus,
    setStoreRefreshStatus,
    syncProgress,
    setSyncProgress,
    queueStats,
    setQueueStats,
    metrics,
    setMetrics,
    currentPhase,
    setCurrentPhase,
    detailedMessage,
    setDetailedMessage,
    showDetailedView,
    setShowDetailedView,
  };
}
