/* Draggable region - enables window dragging on frameless windows */
.app-drag-region {
  -webkit-app-region: drag;
  app-region: drag;
}

/* Ensure buttons inside draggable regions are not draggable */
.app-drag-region button,
.app-drag-region a,
.app-drag-region input,
.app-drag-region select,
.app-drag-region textarea,
.window-controls button {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

/* Always visible titlebar styling */
.app-title-bar {
  opacity: 1;
  height: 36px;
  transition: opacity 150ms ease-out, height 150ms ease-out;
  overflow: hidden;
}

/* Theme transition helper - simplified for reliability */
.theme-transition {
  /* Simple transition for reliable theme switching */
  transition: background-color 200ms ease-out,
              color 200ms ease-out,
              border-color 200ms ease-out !important;
}

/* Disable transitions for complex components */
.complex-list,
.data-grid,
.complex-component {
  transition: none !important;
  margin-top: 0 !important; /* Ensure consistent margin */
  padding-top: 0 !important; /* Ensure consistent padding */
  height: auto !important; /* Let content determine height */
}

/* Improved rounded corners implementation */
.app-window {
  border-radius: 12px;
  overflow: hidden;
  /* Removed box-shadow to prevent border-like appearance */
  /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); */
  /* Add backdrop blur for a more modern look */
  backdrop-filter: blur(3px);
}

/* Window edge definition - subtle stroke for visibility */
:root {
  --window-border-color: rgba(0, 0, 0, 0.18);
}

.dark {
  --window-border-color: rgba(255, 255, 255, 0.15);
}

/* Apply consistent window border */
#root {
  border: 1px solid var(--window-border-color) !important;
}

/* Ensure clean edges without borders */
* {
  /* Remove any default outlines that might appear as borders */
  outline: none !important;
}

/* Remove any borders on the app container */
.app-container {
  border: none !important;
  outline: none !important;
}

/* Add subtle border for better visibility */
/* Removed to prevent double border effect
.app-window-border {
  border: 1px solid rgba(255, 255, 255, 0.1);
}
*/

/* Dark mode content styling - work with the corner mask */
.dark, 
html.dark, 
body.dark {
  color: #FFFFFF;
  background-color: transparent !important;
}

/* Platform-specific fixes for clean window edges */
/* Windows and Linux need explicit clipping for rounded corners */
@supports not (-webkit-app-region: drag) {
  /* Non-macOS platforms */
  .app-container {
    /* Ensure content is clipped to rounded corners */
    border-radius: 12px;
    overflow: hidden;
  }
}

/* Ensure HTML and body elements don't create unwanted borders */
html, body {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

/* Root element in dark mode must stay transparent to show corner mask */
.dark #root,
html.dark #root,
body.dark #root {
  background-color: transparent !important;
}

/* Critical fix for sharp corners in dark mode - ensure inner content respects rounded corners */
.dark .min-h-screen,
html.dark .min-h-screen,
body.dark .min-h-screen {
  border-radius: 12px !important;
  overflow: hidden !important; 
  background-color: #1A1A1C !important;
}

/* App container must be properly styled in dark mode */
.dark .app-container,
html.dark .app-container, 
body.dark .app-container {
  background-color: transparent !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  position: relative;
  z-index: 1;
}

/* Content container must follow the rounded corners */
.dark .bg-ui-background-primary,
html.dark .bg-ui-background-primary,
body.dark .bg-ui-background-primary {
  border-radius: 12px !important;
  overflow: hidden !important;
  background-color: #1A1A1C !important;
}

/* Ensure the corner mask works on Windows */
.corner-mask {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #1A1A1C;
  border-radius: 12px;
  z-index: -1;
  pointer-events: none;
}

.dark .corner-mask,
html.dark .corner-mask,
body.dark .corner-mask {
  display: block;
}

/* Consistent table view spacing */
.table-view-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* More explicit dark mode styles for specific components */
.dark .bg-ui-background-tertiary,
html.dark .bg-ui-background-tertiary,
body.dark .bg-ui-background-tertiary {
  background-color: var(--color-ui-background-tertiary) !important;
}

.dark button,
html.dark button,
body.dark button {
  color: var(--color-ui-foreground-primary) !important;
}

.dark button svg,
html.dark button svg,
body.dark button svg {
  color: inherit !important;
}

/* Analyze Colors button specific fix */
html.dark [aria-label="Analyze Colors"],
body.dark [aria-label="Analyze Colors"],
.dark [aria-label="Analyze Colors"] {
  background-color: var(--color-ui-background-tertiary) !important;
  color: var(--color-ui-foreground-primary) !important;
}

/* Table rows and text in dark mode */
html.dark tbody tr,
body.dark tbody tr,
.dark tbody tr {
  background-color: var(--color-ui-background-tertiary) !important;
  border-color: var(--color-ui-border-dark) !important;
  color: #F0F0F0 !important; /* Lighter text color for dark mode */
}

/* Table text colors now handled by design tokens - no overrides needed */

/* Additional table element text colors for dark mode */
/* Removed hardcoded color overrides - proper CSS variables will be used via design tokens */

/* Removed problematic table text overrides - using design tokens instead */