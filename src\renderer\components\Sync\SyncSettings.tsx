/**
 * @file SyncSettings.tsx
 * @description Component for sync settings
 */

import React, { useState } from 'react';
import { useSyncConfig } from '../../store/sync.store';
import { ConflictResolutionStrategy } from '../../../shared/types/sync.types';

export const SyncSettings: React.FC = () => {
  const { config, updateConfig } = useSyncConfig();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Local state for form values
  const [autoSync, setAutoSync] = useState(config.autoSync);
  const [syncInterval, setSyncInterval] = useState(
    config.syncInterval.toString()
  );
  const [conflictResolution, setConflictResolution] = useState(
    config.conflictResolution
  );
  const [maxStorageVersions, setMaxStorageVersions] = useState(
    config.maxStorageVersions.toString()
  );

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate inputs
      const syncIntervalNum = parseInt(syncInterval, 10);
      if (isNaN(syncIntervalNum) || syncIntervalNum < 1) {
        setError('Sync interval must be a positive number');
        setIsLoading(false);
        return;
      }

      const maxStorageVersionsNum = parseInt(maxStorageVersions, 10);
      if (isNaN(maxStorageVersionsNum) || maxStorageVersionsNum < 1) {
        setError('Max storage versions must be a positive number');
        setIsLoading(false);
        return;
      }

      // Update config
      await updateConfig({
        autoSync,
        syncInterval: syncIntervalNum,
        conflictResolution,
        maxStorageVersions: maxStorageVersionsNum,
      });

      setSuccess('Settings saved successfully');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='flex flex-col space-y-4'>
      <h2 className='text-lg font-semibold'>Sync Settings</h2>

      <form onSubmit={handleSubmit} className='flex flex-col space-y-4'>
        {error && (
          <div className='text-sm text-red-500 bg-red-100 p-2 rounded'>
            {error}
          </div>
        )}

        {success && (
          <div className='text-sm text-green-500 bg-green-100 p-2 rounded'>
            {success}
          </div>
        )}

        <div className='flex items-center space-x-2'>
          <input
            id='autoSync'
            type='checkbox'
            checked={autoSync}
            onChange={e => setAutoSync(e.target.checked)}
            className='h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300 rounded'
            disabled={isLoading}
          />
          <label htmlFor='autoSync' className='text-sm text-gray-700'>
            Enable automatic synchronization
          </label>
        </div>

        <div className='flex flex-col space-y-1'>
          <label htmlFor='syncInterval' className='text-sm text-gray-700'>
            Sync interval (minutes)
          </label>
          <input
            id='syncInterval'
            type='number'
            min='1'
            value={syncInterval}
            onChange={e => setSyncInterval(e.target.value)}
            className='px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500'
            disabled={isLoading || !autoSync}
          />
        </div>

        <div className='flex flex-col space-y-1'>
          <label htmlFor='conflictResolution' className='text-sm text-gray-700'>
            Conflict resolution strategy
          </label>
          <select
            id='conflictResolution'
            value={conflictResolution}
            onChange={e =>
              setConflictResolution(
                e.target.value as ConflictResolutionStrategy
              )
            }
            className='px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500'
            disabled={isLoading}
          >
            <option value={ConflictResolutionStrategy.LAST_WRITE_WINS}>
              Last Write Wins
            </option>
            <option value={ConflictResolutionStrategy.MANUAL}>
              Manual Resolution
            </option>
            <option value={ConflictResolutionStrategy.MERGE}>
              Automatic Merge
            </option>
          </select>
        </div>

        <div className='flex flex-col space-y-1'>
          <label htmlFor='maxStorageVersions' className='text-sm text-gray-700'>
            Maximum versions to keep
          </label>
          <input
            id='maxStorageVersions'
            type='number'
            min='1'
            value={maxStorageVersions}
            onChange={e => setMaxStorageVersions(e.target.value)}
            className='px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500'
            disabled={isLoading}
          />
        </div>

        <button
          type='submit'
          disabled={isLoading}
          className={`px-4 py-2 rounded ${
            isLoading
              ? 'bg-gray-300 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          {isLoading ? 'Saving...' : 'Save Settings'}
        </button>
      </form>
    </div>
  );
};
