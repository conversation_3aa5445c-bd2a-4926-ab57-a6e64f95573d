/**
 * @file color-mapping.service.ts
 * @description Service for mapping database rows to ColorEntry objects
 * 
 * Extracted from ColorService to follow single responsibility principle.
 * Handles all data transformation operations for colors including:
 * - Database row to ColorEntry mapping
 * - Color space data parsing and conversion
 * - Gradient data transformation
 * - Property parsing and normalization
 */

import { ColorEntry, GradientInfo, RGB, HSL, LAB } from '../../../shared/types/color.types';
import { ColorSpaceCalculator } from './color-space-calculator.service';

export interface ColorRowData {
  id: string;  // Direct UUID primary key
  source?: string;
  code: string;
  display_name: string | null;
  hex: string;
  color_spaces: string | null;
  properties: string | null;
  product_name?: string;
  created_at: string;
  updated_at: string;
  organization_id: string;
  is_library: number | boolean;
  notes: string | null;
  tags: string | null;
}

/**
 * ColorMappingService handles data transformation between database and domain models
 * 
 * This service is responsible for:
 * - Converting database rows to ColorEntry objects
 * - Parsing and transforming JSON data from database columns
 * - Applying business rules during data transformation
 * - Handling color space calculations during mapping
 * - Processing gradient data for display
 */
export class ColorMappingService {
  
  constructor(
    private colorSpaceCalculator: ColorSpaceCalculator
  ) {}

  /**
   * Convert database row to ColorEntry with optimized data transformation
   * @param row - Database row data
   * @param organizationId - Organization context
   * @param options - Optional conversion settings
   * @returns Transformed ColorEntry object
   */
  convertToColorEntry(row: ColorRowData, organizationId?: string, options?: { 
    skipColorSpaceCalculation?: boolean;
    skipGradientProcessing?: boolean;
  }): ColorEntry {
    try {
      const opts = options || {};
      
      // Parse JSON data only once and cache
      const props = this.parseProperties(row.properties);
      const colorSpaces = this.parseColorSpaces(row.color_spaces);
      
      // Extract CMYK from JSON and format as string (lightweight operation)
      const cmyk = this.extractCMYKString(colorSpaces);
      
      // Only calculate expensive color spaces if needed
      let rgb: RGB | undefined;
      let hsl: HSL | undefined; 
      let lab: LAB | undefined;
      
      if (!opts.skipColorSpaceCalculation) {
        const calculated = this.getColorSpaces(row.hex);
        rgb = calculated.rgb ?? undefined;
        hsl = calculated.hsl ?? undefined;
        lab = calculated.lab ?? undefined;
      }
      
      // Extract product name from joined data or fallback to properties
      const product = row.product_name || props.product || '';
      
      // Only process gradients if needed and data exists
      let gradient: GradientInfo | undefined;
      if (!opts.skipGradientProcessing && (colorSpaces.gradient || props.gradient)) {
        gradient = this.processGradientData(colorSpaces, props);
      }
      
      const colorEntry: ColorEntry = {
        id: row.id,  // Direct UUID mapping
        source: row.source || 'user',
        code: row.code,
        name: row.display_name || row.code,
        hex: row.hex,
        cmyk,
        ...(rgb && { rgb }),
        ...(hsl && { hsl }),
        ...(lab && { lab }),
        product,
        gradient,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        organizationId: organizationId || row.organization_id,
        isLibrary: row.is_library === 1 || row.is_library === true,
        notes: row.notes ?? undefined,
        tags: row.tags ?? undefined
      };

      return colorEntry;
      
    } catch (error) {
      console.error('[ColorMappingService] Error converting row to ColorEntry:', error);
      
      // Return a basic ColorEntry with minimal data for error cases
      return {
        id: row.id || 'unknown',  // Direct UUID mapping
        source: 'user',
        code: row.code || 'unknown',
        name: row.display_name || row.code || 'unknown',
        hex: row.hex || '#000000',
        cmyk: 'C:0 M:0 Y:0 K:0',
        product: '',
        createdAt: row.created_at || new Date().toISOString(),
        updatedAt: row.updated_at || new Date().toISOString(),
        organizationId: organizationId || row.organization_id || '',
        isLibrary: false,
        notes: undefined,
        tags: undefined
      };
    }
  }

  /**
   * Parse properties JSON safely
   * @param propertiesJson - JSON string from database
   * @returns Parsed properties object
   */
  private parseProperties(propertiesJson: string | null): any {
    if (!propertiesJson) {
      return {};
    }
    
    try {
      return JSON.parse(propertiesJson);
    } catch (error) {
      console.warn('[ColorMappingService] Failed to parse properties JSON:', error);
      return {};
    }
  }

  /**
   * Parse color spaces JSON safely
   * @param colorSpacesJson - JSON string from database
   * @returns Parsed color spaces object
   */
  private parseColorSpaces(colorSpacesJson: string | null): any {
    if (!colorSpacesJson) {
      return {};
    }
    
    try {
      return typeof colorSpacesJson === 'string' 
        ? JSON.parse(colorSpacesJson) 
        : colorSpacesJson;
    } catch (error) {
      console.warn('[ColorMappingService] Failed to parse color_spaces JSON:', error);
      return {};
    }
  }

  /**
   * Extract CMYK values and format as string
   * @param colorSpaces - Parsed color spaces object
   * @returns CMYK string representation
   */
  private extractCMYKString(colorSpaces: any): string {
    if (!colorSpaces.cmyk) {
      return 'C:0 M:0 Y:0 K:0';
    }
    
    const cmykObj = colorSpaces.cmyk;
    return `C:${cmykObj.c || 0} M:${cmykObj.m || 0} Y:${cmykObj.y || 0} K:${cmykObj.k || 0}`;
  }

  /**
   * Get calculated color spaces from hex using ColorSpaceCalculator
   * @param hex - Hex color value
   * @returns Calculated color spaces
   */
  private getColorSpaces(hex: string) {
    if (!this.colorSpaceCalculator) {
      console.error('[ColorMappingService] ColorSpaceCalculator not available');
      return { rgb: null, hsl: null, lab: null };
    }
    
    try {
      return this.colorSpaceCalculator.getCalculatedColorSpaces(hex);
    } catch (error) {
      console.error('[ColorMappingService] Error in color space calculation:', error);
      return { rgb: null, hsl: null, lab: null };
    }
  }

  /**
   * Process gradient data from color spaces and properties
   * ENFORCES NEW FORMAT ONLY - legacy data must be migrated
   * @param colorSpaces - Parsed color spaces object
   * @param props - Parsed properties object
   * @returns Processed gradient data or undefined
   */
  private processGradientData(colorSpaces: any, props: any): GradientInfo | undefined {
    // Check colorSpaces.gradient for NEW format only
    if (colorSpaces.gradient?.colors && Array.isArray(colorSpaces.gradient.colors)) {
      console.log('[ColorMappingService] ✅ Using NEW gradient format from color_spaces:', colorSpaces.gradient.colors);
      return {
        colors: colorSpaces.gradient.colors,
        colorCodes: colorSpaces.gradient.colorCodes,
        type: 'linear',
        angle: colorSpaces.gradient.angle || 45
      };
    }
    
    // Check props.gradient for NEW format only
    if (props.gradient?.colors && Array.isArray(props.gradient.colors)) {
      console.log('[ColorMappingService] ✅ Using NEW gradient format from properties:', props.gradient.colors);
      return {
        colors: props.gradient.colors,
        colorCodes: props.gradient.colorCodes,
        type: 'linear',
        angle: props.gradient.angle || 45
      };
    }
    
    // HANDLE LEGACY FORMAT (temporary until migration is complete)
    if (colorSpaces.gradient?.gradientStops && Array.isArray(colorSpaces.gradient.gradientStops)) {
      console.log('[ColorMappingService] ⚠️ Using LEGACY gradient format from color_spaces (gradientStops)');
      const stops = colorSpaces.gradient.gradientStops;
      const colors = stops.map((stop: any) => stop.color).filter((color: any) => color);
      const colorCodes = stops.map((stop: any) => stop.colorCode).filter((code: any) => code);
      
      return {
        colors,
        colorCodes: colorCodes.length > 0 ? colorCodes : undefined,
        type: 'linear',
        angle: colorSpaces.gradient.angle || 45
      };
    }
    
    if (props.gradient?.gradientStops && Array.isArray(props.gradient.gradientStops)) {
      console.log('[ColorMappingService] ⚠️ Using LEGACY gradient format from properties (gradientStops)');
      const stops = props.gradient.gradientStops;
      const colors = stops.map((stop: any) => stop.color).filter((color: any) => color);
      const colorCodes = stops.map((stop: any) => stop.colorCode).filter((code: any) => code);
      
      return {
        colors,
        colorCodes: colorCodes.length > 0 ? colorCodes : undefined,
        type: 'linear',
        angle: props.gradient.angle || 45
      };
    }
    
    // NO GRADIENT FORMAT FOUND
    console.log('[ColorMappingService] ❌ No gradient format found, gradient will not be displayed');
    return undefined;
  }

  /**
   * Get service information and capabilities
   * @returns Service metadata
   */
  getServiceInfo(): { name: string; version: string; capabilities: string[] } {
    return {
      name: 'ColorMappingService',
      version: '1.0.0',
      capabilities: [
        'Database row to ColorEntry conversion',
        'JSON data parsing and validation',
        'Color space data transformation',
        'Gradient data processing',
        'Property normalization',
        'Error-safe data transformation',
        'Debug logging for troubleshooting',
        'Fallback handling for corrupt data'
      ]
    };
  }
}