/**
 * @file logger.service.ts
 * @description Enhanced logging service with different levels and contexts
 */

import { Result, success, failure } from '../../shared/types/result.types';
import { isNotNullish, isError, toError } from '../../shared/types/type-guards';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Interface for Sentry service to avoid 'any' type
interface SentryServiceInterface {
  captureError(error: Error, context?: unknown): void;
  captureMessage(message: string, level: string, context?: unknown): void;
  addBreadcrumb(message: string, category: string, metadata?: unknown): void;
}

export interface ILogContext {
  service?: string;
  operation?: string;
  userId?: string;
  organizationId?: string;
  correlationId?: string;
  error?: Error;
  [key: string]: unknown;
}

export interface ILogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: ILogContext;
  args?: unknown[];
  error?: Error;
}

export interface ILogger {
  info(message: string, context?: ILogContext, ...args: unknown[]): void;
  warn(message: string, context?: ILogContext, ...args: unknown[]): void;
  error(message: string, error?: Error, context?: ILogContext, ...args: unknown[]): void;
  debug(message: string, context?: ILogContext, ...args: unknown[]): void;
  
  // Contextual logger creation
  withContext(context: ILogContext): ILogger;
  
  // Performance logging
  time(label: string): void;
  timeEnd(label: string): void;
}

export interface ILoggerSink {
  write(entry: ILogEntry): void;
}

/**
 * Console sink for development
 */
export class ConsoleSink implements ILoggerSink {
  constructor(private minLevel: LogLevel = 'info') {}

  write(entry: ILogEntry): void {
    if (!this.shouldLog(entry.level)) {return;}

    const timestamp = entry.timestamp;
    const context = entry.context ? this.formatContext(entry.context) : '';
    const prefix = `[${timestamp}] [${entry.level.toUpperCase()}]${context}`;
    
    switch (entry.level) {
      case 'debug':
        console.debug(prefix, entry.message, ...(entry.args || []));
        break;
      case 'info':
        console.log(prefix, entry.message, ...(entry.args || []));
        break;
      case 'warn':
        console.warn(prefix, entry.message, ...(entry.args || []));
        break;
      case 'error':
        if (entry.error) {
          console.error(prefix, entry.message, entry.error, ...(entry.args || []));
        } else {
          console.error(prefix, entry.message, ...(entry.args || []));
        }
        break;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const minIndex = levels.indexOf(this.minLevel);
    const currentIndex = levels.indexOf(level);
    return currentIndex >= minIndex;
  }

  private formatContext(context: ILogContext): string {
    const parts: string[] = [];
    
    if (context.service) {parts.push(`service:${context.service}`);}
    if (context.operation) {parts.push(`op:${context.operation}`);}
    if (context.userId) {parts.push(`user:${context.userId.substring(0, 8)}`);}
    if (context.organizationId) {parts.push(`org:${context.organizationId.substring(0, 8)}`);}
    if (context.correlationId) {parts.push(`corr:${context.correlationId.substring(0, 8)}`);}
    
    return parts.length > 0 ? ` [${parts.join(', ')}]` : '';
  }
}

/**
 * Enhanced file sink for production logging with error handling and log rotation
 */
export class FileSink implements ILoggerSink {
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private static readonly MAX_FILES = 5;
  private writeFailureCount = 0;
  private lastWriteFailure = 0;
  private readonly FAILURE_THRESHOLD = 5;
  private readonly FAILURE_COOLDOWN = 30000; // 30 seconds

  constructor(
    private filePath: string,
    private minLevel: LogLevel = 'info'
  ) {
    this.ensureLogDirectory();
  }

  write(entry: ILogEntry): void {
    if (!this.shouldLog(entry.level)) {return;}

    // Check if we're in failure cooldown
    if (this.isInFailureCooldown()) {
      return;
    }

    // Use dynamic import in a non-blocking way
    this.writeAsync(entry);
  }

  private async writeAsync(entry: ILogEntry): Promise<void> {
    try {
      const fs = await import('node:fs');
      const logLine = JSON.stringify({
        ...entry,
        timestamp: entry.timestamp || new Date().toISOString(),
        level: entry.level.toUpperCase(),
        pid: process.pid
      }) + '\n';
      
      // Check file size and rotate if needed
      await this.rotateLogIfNeeded();
      
      fs.appendFileSync(this.filePath, logLine, 'utf8');
      
      // Reset failure count on successful write
      this.writeFailureCount = 0;
    } catch (error) {
      this.handleWriteFailure(error, entry);
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const minIndex = levels.indexOf(this.minLevel);
    const currentIndex = levels.indexOf(level);
    return currentIndex >= minIndex;
  }

  private ensureLogDirectory(): void {
    // Use dynamic import in constructor context
    import('node:fs').then(fs => {
      import('node:path').then(path => {
        try {
          const logDir = path.dirname(this.filePath);
          
          if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
          }
        } catch (error) {
          console.warn('Failed to create log directory:', error);
        }
      });
    }).catch(error => {
      console.warn('Failed to load path module:', error);
    });
  }

  private async rotateLogIfNeeded(): Promise<void> {
    try {
      const fs = await import('node:fs');
      const path = await import('node:path');
      
      if (!fs.existsSync(this.filePath)) {
        return;
      }

      const stats = fs.statSync(this.filePath);
      if (stats.size > FileSink.MAX_FILE_SIZE) {
        // Rotate log files
        const logDir = path.dirname(this.filePath);
        const logName = path.basename(this.filePath, '.log');
        
        // Remove oldest log file
        const oldestLog = path.join(logDir, `${logName}.${FileSink.MAX_FILES}.log`);
        if (fs.existsSync(oldestLog)) {
          fs.unlinkSync(oldestLog);
        }
        
        // Shift log files
        for (let i = FileSink.MAX_FILES - 1; i >= 1; i--) {
          const currentLog = path.join(logDir, `${logName}.${i}.log`);
          const nextLog = path.join(logDir, `${logName}.${i + 1}.log`);
          
          if (fs.existsSync(currentLog)) {
            fs.renameSync(currentLog, nextLog);
          }
        }
        
        // Move current log to .1
        const firstRotatedLog = path.join(logDir, `${logName}.1.log`);
        fs.renameSync(this.filePath, firstRotatedLog);
      }
    } catch (error) {
      console.warn('Failed to rotate log file:', error);
    }
  }

  private isInFailureCooldown(): boolean {
    if (this.writeFailureCount < this.FAILURE_THRESHOLD) {
      return false;
    }
    
    const now = Date.now();
    if (now - this.lastWriteFailure > this.FAILURE_COOLDOWN) {
      // Reset after cooldown period
      this.writeFailureCount = 0;
      return false;
    }
    
    return true;
  }

  private handleWriteFailure(error: unknown, entry: ILogEntry): void {
    this.writeFailureCount++;
    this.lastWriteFailure = Date.now();
    
    // Only log to console if we haven't exceeded failure threshold
    if (this.writeFailureCount <= this.FAILURE_THRESHOLD) {
      console.error(`Failed to write to log file (attempt ${this.writeFailureCount}):`, error);
      console.log('[FILE-LOG-FALLBACK]', entry);
      
      if (this.writeFailureCount === this.FAILURE_THRESHOLD) {
        console.warn(`File logging disabled for ${this.FAILURE_COOLDOWN / 1000}s due to repeated failures`);
      }
    }
  }
}

/**
 * Sentry sink for error reporting
 */
export class SentrySink implements ILoggerSink {
  private sentryService: SentryServiceInterface | null = null;

  constructor(private minLevel: LogLevel = 'error') {
    // Lazy load Sentry service to avoid circular dependencies
    this.initSentry();
  }

  private async initSentry(): Promise<void> {
    try {
      const { sentryService } = await import('../services/sentry.service');
      this.sentryService = sentryService as SentryServiceInterface;
    } catch (error) {
      console.warn('Failed to load Sentry service:', error);
    }
  }

  write(entry: ILogEntry): void {
    if (!this.shouldLog(entry.level) || !this.sentryService) {
      return;
    }

    try {
      const context = {
        userId: entry.context?.userId,
        organizationId: entry.context?.organizationId,
        service: entry.context?.service,
        operation: entry.context?.operation,
        additionalData: {
          ...entry.context,
          timestamp: entry.timestamp,
        },
      };

      if (entry.level === 'error' && entry.error) {
        this.sentryService.captureError(entry.error, context);
      } else if (entry.level === 'warn' || entry.level === 'error') {
        this.sentryService.captureMessage(entry.message, entry.level, context);
      }

      // Add breadcrumb for all levels
      this.sentryService.addBreadcrumb(
        entry.message,
        entry.context?.service || 'logger',
        {
          level: entry.level,
          context: entry.context,
        }
      );
    } catch (error) {
      console.error('Failed to send log to Sentry:', error);
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const minIndex = levels.indexOf(this.minLevel);
    const currentIndex = levels.indexOf(level);
    return currentIndex >= minIndex;
  }
}

/**
 * Enhanced logger implementation
 */
export class Logger implements ILogger {
  private timers = new Map<string, number>();

  constructor(
    private sinks: ILoggerSink[] = [new ConsoleSink()],
    private defaultContext: ILogContext = {}
  ) {}

  info(message: string, context?: ILogContext, ...args: unknown[]): void {
    this.log('info', message, undefined, context, ...args);
  }

  warn(message: string, context?: ILogContext, ...args: unknown[]): void {
    this.log('warn', message, undefined, context, ...args);
  }

  error(message: string, error?: Error, context?: ILogContext, ...args: unknown[]): void {
    this.log('error', message, error, context, ...args);
  }

  debug(message: string, context?: ILogContext, ...args: unknown[]): void {
    this.log('debug', message, undefined, context, ...args);
  }

  withContext(context: ILogContext): ILogger {
    const mergedContext = { ...this.defaultContext, ...context };
    return new Logger(this.sinks, mergedContext);
  }

  time(label: string): void {
    this.timers.set(label, performance.now());
  }

  timeEnd(label: string): void {
    const startTime = this.timers.get(label);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.info(`Timer "${label}": ${duration.toFixed(2)}ms`, { operation: 'timer' });
      this.timers.delete(label);
    } else {
      this.warn(`Timer "${label}" was not started`, { operation: 'timer' });
    }
  }

  private log(
    level: LogLevel,
    message: string,
    error?: Error,
    context?: ILogContext,
    ...args: unknown[]
  ): void {
    const entry: ILogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context: { ...this.defaultContext, ...context },
      args: args.length > 0 ? args : undefined,
      error
    };

    for (const sink of this.sinks) {
      try {
        sink.write(entry);
      } catch (sinkError) {
        // Don't fail if a sink fails - just report it
        console.error('Logger sink failed:', sinkError);
      }
    }
  }
}

/**
 * Service-specific logger factory
 */
export class LoggerFactory {
  private static instance: LoggerFactory;
  private defaultSinks: ILoggerSink[];

  private constructor() {
    const isProduction = process.env.NODE_ENV === 'production';
    const isDevelopment = process.env.NODE_ENV === 'development';

    this.defaultSinks = [];

    if (isDevelopment) {
      this.defaultSinks.push(new ConsoleSink('debug'));
    } else {
      this.defaultSinks.push(new ConsoleSink('info'));
    }

    if (isProduction) {
      // Add file sink for production using Electron's logs directory
      this.initializeFileLogging().catch(err => console.warn('File logging init failed:', err));
    } else {
      // Add debug file logging for development
      this.initializeFileLogging('debug').catch(err => console.warn('File logging init failed:', err));
    }

    // Add Sentry sink for error reporting (both prod and dev)
    this.initializeSentryLogging();
  }

  private async initializeFileLogging(minLevel: LogLevel = 'info'): Promise<void> {
    try {
      const path = await import('node:path');
      let logDir: string;
      
      // Try to use Electron's logs directory, fallback to user data
      try {
        const { app } = await import('electron');
        logDir = app.getPath('logs');
      } catch (electronError) {
        // Fallback for non-Electron environments or if app not ready
        const os = await import('node:os');
        logDir = path.join(os.tmpdir(), 'chromasync-logs');
      }
      
      const fs = await import('node:fs');
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      // Create separate log files for different purposes
      const dateStr = new Date().toISOString().split('T')[0];
      const mainLogFile = path.join(logDir, `chromasync-${dateStr}.log`);
      const errorLogFile = path.join(logDir, `chromasync-errors-${dateStr}.log`);
      
      // Main log file (all levels)
      this.defaultSinks.push(new FileSink(mainLogFile, minLevel));
      
      // Error-only log file for quick troubleshooting
      this.defaultSinks.push(new FileSink(errorLogFile, 'error'));
      
      console.log(`File logging initialized: ${logDir}`);
    } catch (error) {
      console.warn('Failed to initialize file logging:', error);
    }
  }

  private initializeSentryLogging(): void {
    try {
      // Add Sentry sink for error and warning levels
      this.defaultSinks.push(new SentrySink('warn'));
      console.log('Sentry logging sink initialized');
    } catch (error) {
      console.warn('Failed to initialize Sentry logging:', error);
    }
  }

  static getInstance(): LoggerFactory {
    if (!LoggerFactory.instance) {
      LoggerFactory.instance = new LoggerFactory();
    }
    return LoggerFactory.instance;
  }

  createLogger(serviceName: string, additionalContext?: ILogContext): ILogger {
    const context = {
      service: serviceName,
      ...additionalContext
    };

    return new Logger(this.defaultSinks, context);
  }

  addSink(sink: ILoggerSink): void {
    this.defaultSinks.push(sink);
  }

  removeSink(sink: ILoggerSink): void {
    const index = this.defaultSinks.indexOf(sink);
    if (index > -1) {
      this.defaultSinks.splice(index, 1);
    }
  }

  /**
   * Log critical errors with enhanced details for debugging
   */
  logCriticalError(error: Error, context: ILogContext & { 
    operation?: string; 
    userId?: string; 
    organizationId?: string;
    additionalData?: Record<string, unknown>;
  }): void {
    const logger = this.createLogger('CriticalErrorLogger');
    
    const enhancedContext = {
      ...context,
      timestamp: new Date().toISOString(),
      stack: error.stack,
      name: error.name,
      message: error.message,
      userAgent: (global as any).process?.versions,
      platform: (global as any).process?.platform,
      arch: (global as any).process?.arch,
      version: (global as any).process?.env?.npm_package_version || 'unknown'
    } as const;

    logger.error('Critical error occurred', error, enhancedContext);
  }

  /**
   * Get logs directory path
   */
  async getLogsDirectory(): Promise<string | null> {
    try {
      const path = await import('node:path');
      try {
        const { app } = await import('electron');
        return app.getPath('logs');
      } catch {
        const os = await import('node:os');
        return path.join(os.tmpdir(), 'chromasync-logs');
      }
    } catch {
      return null;
    }
  }
}

/**
 * Default logger instance for quick access
 */
export const defaultLogger = LoggerFactory.getInstance().createLogger('ChromaSync');

/**
 * Performance measurement decorator with proper TypeScript typing
 */
export function logPerformance(logger: ILogger, operation: string) {
  return function (
    target: unknown, 
    propertyName: string, 
    descriptor: PropertyDescriptor
  ): PropertyDescriptor {
    const method = descriptor.value;

    if (typeof method === 'function') {
      descriptor.value = async function (this: unknown, ...args: unknown[]) {
        const label = `${(target as any).constructor.name}.${propertyName}`;
        logger.time(label);
        
        try {
          const result = await method.apply(this, args);
          logger.timeEnd(label);
          logger.debug(`Operation completed: ${operation}`, { operation: propertyName });
          return result;
        } catch (error) {
          logger.timeEnd(label);
          logger.error(`Operation failed: ${operation}`, toError(error), { operation: propertyName });
          throw error;
        }
      };
    }

    return descriptor;
  };
}

/**
 * Error boundary decorator with proper TypeScript typing
 */
export function logErrors(logger: ILogger, context?: ILogContext) {
  return function (
    target: unknown, 
    propertyName: string, 
    descriptor: PropertyDescriptor
  ): PropertyDescriptor {
    const method = descriptor.value;

    if (typeof method === 'function') {
      descriptor.value = async function (this: unknown, ...args: unknown[]) {
        try {
          return await method.apply(this, args);
        } catch (error) {
          logger.error(
            `Unhandled error in ${(target as any).constructor.name}.${propertyName}`,
            toError(error),
            { ...context, operation: propertyName }
          );
          throw error;
        }
      };
    }

    return descriptor;
  };
}