/**
 * @file sentry.service.ts
 * @description Sentry error tracking service following existing service patterns
 */

import * as Sentry from '@sentry/electron';
import { ILogger, LoggerFactory } from '../utils/logger.service';
import { secureConfig } from '../utils/secure-config-loader';

export interface SentryConfig {
  dsn?: string;
  environment?: string;
  release?: string;
  enabled?: boolean;
  sampleRate?: number;
  debug?: boolean;
}

export interface SentryContext {
  userId?: string;
  organizationId?: string;
  service?: string;
  operation?: string;
  additionalData?: Record<string, any>;
}

/**
 * Sentry service for error tracking and performance monitoring
 */
export class SentryService {
  private static globallyInitialized = false; // Global guard to prevent multiple Sentry.init() calls
  private static initializationAttempts = 0;
  
  private readonly logger: ILogger;
  private initialized = false;
  private config: SentryConfig;

  constructor(logger?: ILogger) {
    this.logger = logger || LoggerFactory.getInstance().createLogger('SentryService');
    this.config = this.loadConfig();
  }

  /**
   * Initialize Sentry with configuration
   */
  async initialize(): Promise<void> {
    SentryService.initializationAttempts++;
    
    if (SentryService.globallyInitialized) {
      this.logger.warn('Sentry already initialized globally - skipping duplicate initialization', {
        attempt: SentryService.initializationAttempts,
        operation: 'initialize'
      });
      this.initialized = true; // Mark this instance as initialized too
      return;
    }
    
    if (this.initialized) {
      this.logger.warn('Sentry already initialized for this instance');
      return;
    }

    try {
      if (!this.config.enabled || !this.config.dsn) {
        this.logger.info('Sentry disabled or no DSN provided');
        return;
      }

      this.logger.info('Initializing Sentry...', {
        attempt: SentryService.initializationAttempts,
        environment: this.config.environment,
        operation: 'initialize'
      });

      await Sentry.init({
        dsn: this.config.dsn,
        environment: this.config.environment || 'production',
        release: this.config.release || process.env.npm_package_version || 'unknown',
        debug: this.config.debug || false,
      });

      // Set initial context
      this.setGlobalContext();

      // Mark as initialized both globally and for this instance
      SentryService.globallyInitialized = true;
      this.initialized = true;
      
      this.logger.info('Sentry initialized successfully', {
        environment: this.config.environment,
        release: this.config.release,
        attempt: SentryService.initializationAttempts,
        operation: 'initialize'
      });

    } catch (error) {
      this.logger.error('Failed to initialize Sentry', error as Error, { operation: 'initialize' });
    }
  }

  /**
   * Check if Sentry is globally initialized (static method)
   */
  static isGloballyInitialized(): boolean {
    return SentryService.globallyInitialized;
  }

  /**
   * Get initialization attempt count (for debugging)
   */
  static getInitializationAttempts(): number {
    return SentryService.initializationAttempts;
  }

  /**
   * Capture an error with context
   */
  captureError(error: Error, context?: SentryContext): string | null {
    if (!this.initialized) {
      return null;
    }

    try {
      return Sentry.withScope((scope) => {
        if (context) {
          this.setScopeContext(scope, context);
        }
        
        return Sentry.captureException(error);
      });
    } catch (sentryError) {
      this.logger.error('Failed to capture error in Sentry', sentryError as Error, {
        originalError: error.message,
        operation: 'captureError'
      });
      return null;
    }
  }

  /**
   * Capture a message with context
   */
  captureMessage(message: string, level: Sentry.SeverityLevel = 'info', context?: SentryContext): string | null {
    if (!this.initialized) {
      return null;
    }

    try {
      return Sentry.withScope((scope) => {
        if (context) {
          this.setScopeContext(scope, context);
        }
        
        scope.setLevel(level);
        return Sentry.captureMessage(message);
      });
    } catch (error) {
      this.logger.error('Failed to capture message in Sentry', error as Error, {
        message,
        operation: 'captureMessage'
      });
      return null;
    }
  }

  /**
   * Set user context for all future events
   */
  setUser(userId: string, email?: string, organizationId?: string): void {
    if (!this.initialized) {
      return;
    }

    try {
      Sentry.setUser({
        id: userId,
        email: email,
        username: email?.split('@')[0],
      });

      if (organizationId) {
        Sentry.setTag('organizationId', organizationId);
      }

      this.logger.debug('Sentry user context set', {
        userId: userId.substring(0, 8),
        organizationId: organizationId?.substring(0, 8),
        operation: 'setUser'
      });
    } catch (error) {
      this.logger.error('Failed to set Sentry user context', error as Error, { operation: 'setUser' });
    }
  }

  /**
   * Clear user context
   */
  clearUser(): void {
    if (!this.initialized) {
      return;
    }

    try {
      Sentry.setUser(null);
      this.logger.debug('Sentry user context cleared', { operation: 'clearUser' });
    } catch (error) {
      this.logger.error('Failed to clear Sentry user context', error as Error, { operation: 'clearUser' });
    }
  }

  /**
   * Add breadcrumb for debugging
   */
  addBreadcrumb(message: string, category?: string, data?: Record<string, any>): void {
    if (!this.initialized) {
      return;
    }

    try {
      Sentry.addBreadcrumb({
        message,
        category: category || 'default',
        data: data ? this.sanitizeData(data) : undefined,
        timestamp: Date.now() / 1000,
      });
    } catch (error) {
      this.logger.error('Failed to add Sentry breadcrumb', error as Error, { operation: 'addBreadcrumb' });
    }
  }

  /**
   * Add a breadcrumb for performance monitoring (simplified)
   */
  addPerformanceBreadcrumb(name: string, op: string, description?: string, duration?: number): void {
    if (!this.initialized) {
      return;
    }

    try {
      this.addBreadcrumb(
        `${op}: ${name}`,
        'performance',
        {
          operation: op,
          description,
          duration: duration ? `${duration}ms` : undefined,
        }
      );
    } catch (error) {
      this.logger.error('Failed to add performance breadcrumb', error as Error, { operation: 'addPerformanceBreadcrumb' });
    }
  }

  /**
   * Check if Sentry is initialized and enabled
   */
  isEnabled(): boolean {
    return this.initialized && (this.config.enabled !== false);
  }

  /**
   * Get current configuration
   */
  getConfiguration(): SentryConfig {
    return { ...this.config };
  }

  /**
   * Update configuration (requires restart)
   */
  updateConfiguration(newConfig: Partial<SentryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('Sentry configuration updated (restart required)', {
      enabled: this.config.enabled,
      environment: this.config.environment,
      operation: 'updateConfiguration'
    });
  }

  // Private methods

  private loadConfig(): SentryConfig {
    return {
      dsn: secureConfig.getConfigValue('SENTRY_DSN', 'sentry-service') as string,
      environment: process.env.NODE_ENV || 'production',
      release: process.env.npm_package_version || 'unknown',
      enabled: secureConfig.getConfigValue('SENTRY_ENABLED', 'sentry-service') as boolean,
      sampleRate: parseFloat(secureConfig.getConfigValue('SENTRY_SAMPLE_RATE', 'sentry-service') as string || '0.1'),
      debug: secureConfig.getConfigValue('SENTRY_DEBUG', 'sentry-service') as boolean,
    };
  }

  private setGlobalContext(): void {
    try {
      // Set global tags
      Sentry.setTag('app', 'chromasync');
      Sentry.setTag('platform', process.platform);
      Sentry.setTag('arch', process.arch);
      Sentry.setTag('version', process.env.npm_package_version || 'unknown');
      
      // Set global context
      Sentry.setContext('app', {
        name: 'ChromaSync',
        version: process.env.npm_package_version || 'unknown',
        build: process.env.BUILD_NUMBER || 'unknown',
      });

      Sentry.setContext('runtime', {
        name: 'electron',
        version: process.versions.electron,
        node: process.versions.node,
        chrome: process.versions.chrome,
      });
    } catch (error) {
      this.logger.error('Failed to set Sentry global context', error as Error);
    }
  }

  private setScopeContext(scope: Sentry.Scope, context: SentryContext): void {
    try {
      if (context.userId) {
        scope.setTag('userId', context.userId);
      }
      
      if (context.organizationId) {
        scope.setTag('organizationId', context.organizationId);
      }
      
      if (context.service) {
        scope.setTag('service', context.service);
      }
      
      if (context.operation) {
        scope.setTag('operation', context.operation);
      }
      
      if (context.additionalData) {
        scope.setContext('additional', this.sanitizeData(context.additionalData));
      }
    } catch (error) {
      this.logger.error('Failed to set Sentry scope context', error as Error);
    }
  }

  private filterSentryEvent(event: Sentry.Event, _hint: Sentry.EventHint): Sentry.Event | null {
    try {
      // Filter out sensitive information
      if (event.request?.headers) {
        delete event.request.headers['authorization'];
        delete event.request.headers['cookie'];
        delete event.request.headers['x-api-key'];
      }

      // Filter out known non-critical errors
      if (event.exception?.values) {
        for (const exception of event.exception.values) {
          if (exception.value?.includes('ResizeObserver loop limit exceeded')) {
            return null; // Ignore this common browser warning
          }
          if (exception.value?.includes('Non-Error promise rejection captured')) {
            return null; // Ignore promise rejections that aren't real errors
          }
        }
      }

      return event;
    } catch (error) {
      this.logger.error('Error in Sentry event filter', error as Error);
      return event; // Return original event if filtering fails
    }
  }

  private filterBreadcrumb(breadcrumb: Sentry.Breadcrumb): Sentry.Breadcrumb | null {
    try {
      // Filter out sensitive breadcrumbs
      if (breadcrumb.data?.password || breadcrumb.data?.token || breadcrumb.data?.secret) {
        return null;
      }

      // Sanitize data
      if (breadcrumb.data) {
        breadcrumb.data = this.sanitizeData(breadcrumb.data);
      }

      return breadcrumb;
    } catch (error) {
      this.logger.error('Error in Sentry breadcrumb filter', error as Error);
      return breadcrumb; // Return original if filtering fails
    }
  }

  private sanitizeData(data: Record<string, any>): Record<string, any> {
    const sanitized = { ...data };
    const sensitiveKeys = ['password', 'token', 'secret', 'apikey', 'auth', 'credential'];
    
    for (const key of Object.keys(sanitized)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }
}

// Export singleton instance
export const sentryService = new SentryService();
