import { useState, useEffect } from 'react';
import { formatHex } from '../../shared/utils/color';

interface ColorModifierProps {
  color: {
    id: string;
    hex: string;
    code: string;
    cmyk: string;
  };
  onChange: (newColor: {
    id: string;
    hex: string;
    code: string;
    cmyk: string;
  }) => void;
}

// Helper functions for color conversion
const hexToRgb = (hex: string): { r: number; g: number; b: number } => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return { r, g, b };
};

const rgbToHex = (r: number, g: number, b: number): string => {
  const hex = `#${Math.round(r).toString(16).padStart(2, '0')}${Math.round(g).toString(16).padStart(2, '0')}${Math.round(b).toString(16).padStart(2, '0')}`;
  return formatHex(hex);
};

const rgbToHsl = (
  r: number,
  g: number,
  b: number
): { h: number; s: number; l: number } => {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0,
    s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }

    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
};

const hslToRgb = (
  h: number,
  s: number,
  l: number
): { r: number; g: number; b: number } => {
  h /= 360;
  s /= 100;
  l /= 100;

  let r, g, b;

  if (s === 0) {
    r = g = b = l;
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) {
        t += 1;
      }
      if (t > 1) {
        t -= 1;
      }
      if (t < 1 / 6) {
        return p + (q - p) * 6 * t;
      }
      if (t < 1 / 2) {
        return q;
      }
      if (t < 2 / 3) {
        return p + (q - p) * (2 / 3 - t) * 6;
      }
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }

  return { r: r * 255, g: g * 255, b: b * 255 };
};

function ColorModifier({ color, onChange }: ColorModifierProps) {
  const [hue, setHue] = useState(0);
  const [saturation, setSaturation] = useState(0);
  const [lightness, setLightness] = useState(0);

  // Update HSL values when color changes
  useEffect(() => {
    if (color && color.hex) {
      const rgb = hexToRgb(color.hex);
      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);
      setHue(Math.round(hsl.h));
      setSaturation(Math.round(hsl.s));
      setLightness(Math.round(hsl.l));
    }
  }, [color]);

  // Update color when HSL changes
  const updateColor = (h: number, s: number, l: number) => {
    const rgb = hslToRgb(h, s, l);
    const hexColor = rgbToHex(rgb.r, rgb.g, rgb.b);

    onChange({
      ...color,
      hex: hexColor,
    });
  };

  return (
    <div className='mt-3 bg-ui-background-tertiary dark:bg-zinc-800 rounded-md p-4'>
      <h4 className='text-sm font-medium mb-3'>Modify Color</h4>

      {/* Hue Slider */}
      <div className='mb-4'>
        <div className='flex items-center justify-between mb-2'>
          <span className='text-xs text-ui-foreground-tertiary'>Hue</span>
          <span className='text-xs font-medium'>{hue}°</span>
        </div>
        <div
          className='h-6 w-full rounded-full mb-1 overflow-hidden'
          style={{
            background: `linear-gradient(to right,
              hsl(0, ${saturation}%, ${lightness}%),
              hsl(60, ${saturation}%, ${lightness}%),
              hsl(120, ${saturation}%, ${lightness}%),
              hsl(180, ${saturation}%, ${lightness}%),
              hsl(240, ${saturation}%, ${lightness}%),
              hsl(300, ${saturation}%, ${lightness}%),
              hsl(360, ${saturation}%, ${lightness}%))`,
          }}
         />
        <input
          type='range'
          min='0'
          max='360'
          value={hue}
          onChange={e => {
            const newHue = parseInt(e.target.value);
            setHue(newHue);
            updateColor(newHue, saturation, lightness);
          }}
          className='w-full h-2 appearance-none bg-transparent cursor-pointer
            [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5
            [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:shadow-md
            [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-gray-400
            [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:rounded-full
            [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-gray-400'
          data-testid='hue-slider'
          style={{ zIndex: 10 }}
        />
      </div>

      {/* Saturation Slider */}
      <div className='mb-4'>
        <div className='flex items-center justify-between mb-2'>
          <span className='text-xs text-ui-foreground-tertiary'>
            Saturation
          </span>
          <span className='text-xs font-medium'>{saturation}%</span>
        </div>
        <div
          className='h-6 w-full rounded-full mb-1 overflow-hidden'
          style={{
            background: `linear-gradient(to right,
              hsl(${hue}, 0%, ${lightness}%),
              hsl(${hue}, 50%, ${lightness}%),
              hsl(${hue}, 100%, ${lightness}%))`,
          }}
         />
        <input
          type='range'
          min='0'
          max='100'
          value={saturation}
          onChange={e => {
            const newSaturation = parseInt(e.target.value);
            setSaturation(newSaturation);
            updateColor(hue, newSaturation, lightness);
          }}
          className='w-full h-2 appearance-none bg-transparent cursor-pointer
            [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5
            [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:shadow-md
            [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-gray-400
            [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:rounded-full
            [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-gray-400'
          data-testid='saturation-slider'
          style={{ zIndex: 10 }}
        />
      </div>

      {/* Lightness Slider */}
      <div className='mb-4'>
        <div className='flex items-center justify-between mb-2'>
          <span className='text-xs text-ui-foreground-tertiary'>Lightness</span>
          <span className='text-xs font-medium'>{lightness}%</span>
        </div>
        <div
          className='h-6 w-full rounded-full mb-1 overflow-hidden'
          style={{
            background: `linear-gradient(to right,
              hsl(${hue}, ${saturation}%, 0%),
              hsl(${hue}, ${saturation}%, 50%),
              hsl(${hue}, ${saturation}%, 100%))`,
          }}
         />
        <input
          type='range'
          min='0'
          max='100'
          value={lightness}
          onChange={e => {
            const newLightness = parseInt(e.target.value);
            setLightness(newLightness);
            updateColor(hue, saturation, newLightness);
          }}
          className='w-full h-2 appearance-none bg-transparent cursor-pointer
            [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5
            [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:shadow-md
            [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-gray-400
            [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:rounded-full
            [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-gray-400'
          data-testid='lightness-slider'
          style={{ zIndex: 10 }}
        />
      </div>

      {/* Color Display */}
      <div className='flex items-center'>
        <div
          className='w-8 h-8 rounded mr-2 border border-ui-border dark:border-zinc-600'
          style={{ backgroundColor: color.hex }}
         />
        <div className='text-xs font-mono'>{formatHex(color.hex)}</div>
      </div>
    </div>
  );
}

export default ColorModifier;
