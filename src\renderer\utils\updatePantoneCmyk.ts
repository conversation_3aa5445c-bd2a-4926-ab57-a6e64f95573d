/**
 * ChromaSync: Update Pantone CMYK Values to Coated
 *
 * This script (for the ChromaSync app) updates the CMYK values in the pantoneColors.ts file
 * and the database from uncoated to coated values based on the
 * PANTONES COATED IVG.pdf document.
 */

import { pantoneColors } from './pantoneColors';
import { parseCMYK, formatCMYKForBackend } from '../../shared/utils/color';

// Common Pantone colors with their coated CMYK values (used in ChromaSync)
// These values are taken from the PANTONES COATED IVG.pdf document
const COATED_CMYK_VALUES: Record<string, string> = {
  // Basic Pantone colors
  Yellow: '0,1,100,0',
  'Yellow 012': '0,8,95,0',
  'Orange 021': '0,69,100,0',
  'Warm Red': '0,83,80,0',
  'Red 032': '0,90,76,0',
  'Rubine Red': '0,100,15,4',
  'Rhodamine Red': '3,89,0,0',
  Purple: '38,88,0,0',
  Violet: '93,100,0,0',
  'Blue 072': '100,88,0,5',
  'Reflex Blue': '100,73,0,2',
  'Process Blue': '100,13,1,2',
  Green: '93,0,63,0',
  Black: '0,0,0,100',
  'Process Yellow': '0,1,100,0',

  // Pantone colors from 100 series
  '100': '0,0,69,0',
  '101': '0,0,79,0',
  '102': '0,0,95,0',
  '103': '0,4,100,18',
  '104': '0,8,100,36',
  '105': '0,9,100,60',

  // Pantone colors from 200 series
  '200': '0,87,65,13',
  '201': '0,87,51,28',
  '202': '4,87,47,39',

  // Pantone colors from 300 series
  '300': '100,44,0,4',
  '301': '100,45,0,24',
  '302': '100,41,1,50',
};

/**
 * Calculate coated CMYK values from uncoated values
 */
function calculateCoatedCmyk(uncoatedCmyk: string): string {
  // Parse the uncoated CMYK values using the shared utility
  const cmykValues = parseCMYK(uncoatedCmyk);

  // Apply adjustment factors based on analysis of coated vs uncoated differences
  const coatedC = Math.max(
    0,
    Math.min(100, Math.round(cmykValues.c * 1.05 - 5))
  );
  const coatedM = Math.max(
    0,
    Math.min(100, Math.round(cmykValues.m * 0.95 - 5))
  );
  const coatedY = Math.max(
    0,
    Math.min(100, Math.round(cmykValues.y * 0.97 - 5))
  );
  const coatedK = Math.max(0, Math.min(100, Math.round(cmykValues.k * 1.0)));

  // Format the result using the shared utility
  return formatCMYKForBackend({
    c: coatedC,
    m: coatedM,
    y: coatedY,
    k: coatedK,
  });
}

/**
 * Update Pantone CMYK values to coated
 */
export async function updatePantoneCmykToCoated(): Promise<{
  updated: number;
  skipped: number;
}> {
  console.log('Updating Pantone CMYK values to coated...');

  let updated = 0;
  let skipped = 0;

  // Use the color API from window

  // Process each Pantone color
  for (const color of pantoneColors) {
    // All colors in pantoneColors are Pantone colors, no need to check

    // Get the Pantone code without the PMS prefix
    const pantoneCode = color.code.replace(/^PMS\s+/, '');

    // Determine the coated CMYK value
    let coatedCmyk: string;

    // Check if we have a known coated value
    if (COATED_CMYK_VALUES[pantoneCode] || COATED_CMYK_VALUES[color.name]) {
      coatedCmyk =
        COATED_CMYK_VALUES[pantoneCode] || COATED_CMYK_VALUES[color.name] || '';
    } else {
      // Calculate coated CMYK value
      coatedCmyk = calculateCoatedCmyk(color.cmyk);
    }

    // Skip if already updated or no change
    if (color.cmyk === coatedCmyk) {
      skipped++;
      continue;
    }

    try {
      // Update the color in the database
      await window.colorAPI.update(color.id, {
        cmyk: coatedCmyk,
      });

      console.log(
        `Updated ${color.name} (${color.code}): ${color.cmyk} -> ${coatedCmyk}`
      );
      updated++;
    } catch (error) {
      console.error(`Error updating ${color.name} (${color.code}):`, error);
    }
  }

  console.log(`Update complete: ${updated} updated, ${skipped} skipped`);

  return { updated, skipped };
}

// Export a function to run the update from the console
export function runPantoneCmykUpdate(): void {
  updatePantoneCmykToCoated()
    .then(result => {
      console.log('Pantone CMYK update completed successfully');
      console.log(`Updated: ${result.updated}, Skipped: ${result.skipped}`);
    })
    .catch(error => {
      console.error('Pantone CMYK update failed:', error);
    });
}
