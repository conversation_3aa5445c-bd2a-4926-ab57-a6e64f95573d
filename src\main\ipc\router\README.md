# IPC Router System

A modern Express.js-style routing system for Electron IPC communication in ChromaSync.

## Overview

The IPC Router provides a clean, organized way to handle inter-process communication between the main and renderer processes. It replaces the scattered IPC handler registration with a structured routing system that supports middleware, parameter extraction, and comprehensive error handling.

## Features

- **Express.js-style routing** with HTTP methods and URL patterns
- **Parameter extraction** from route patterns (`/api/colors/:id`)
- **Middleware pipeline** with execution order control
- **Built-in security** with sender validation and parameter sanitization
- **Rate limiting** and performance monitoring
- **Seamless integration** with existing ChromaSync infrastructure
- **Legacy compatibility** during migration period

## Quick Start

### Basic Router Setup

```typescript
import { createIPCRouter, middleware } from './router';

// Create router with standard middleware
const router = createIPCRouter({
  enableLogging: true,
  performanceThreshold: 500,
  globalRateLimit: { maxRequests: 100, windowMs: 60000 },
});

// Register routes
router.get(
  '/api/colors',
  middleware.requireAuth,
  middleware.requireOrganization,
  async req => {
    const colorService =
      ServiceLocator.getInstance().getService('ColorService');
    return await colorService.getAll(req.organizationId);
  }
);

// Parameter extraction
router.get(
  '/api/colors/:id',
  middleware.requireAuth,
  middleware.requireOrganization,
  async req => {
    const { id } = req.params; // Automatically extracted
    const colorService =
      ServiceLocator.getInstance().getService('ColorService');
    return await colorService.getById(id, req.organizationId);
  }
);

// Attach to Electron IPC
router.attachToIpcMain();
```

### Complete Setup

```typescript
import { setupChromaSyncRouter } from './router';

// Set up complete router with all routes
const { router, stats } = await setupChromaSyncRouter({
  maintainLegacy: true, // Keep existing handlers during migration
  autoMigrate: false, // Don't auto-migrate legacy handlers
  developmentMode: true, // Extra logging and debug features
});

console.log(`Router initialized: ${stats.totalRoutes} routes`);
```

## Routing Patterns

### Basic Routes

```typescript
// HTTP method shortcuts
router.get('/api/colors', handler);
router.post('/api/colors', handler);
router.put('/api/colors/:id', handler);
router.delete('/api/colors/:id', handler);

// Manual registration
router.register('get', '/api/colors', middleware1, middleware2, handler);
```

### Route Groups

```typescript
// Create a route group with common prefix
const colorAPI = router.group('/api/colors');

colorAPI.get('/', getAllColors); // GET /api/colors/
colorAPI.post('/', createColor); // POST /api/colors/
colorAPI.get('/:id', getColor); // GET /api/colors/:id
colorAPI.put('/:id', updateColor); // PUT /api/colors/:id
colorAPI.delete('/:id', deleteColor); // DELETE /api/colors/:id

// Group middleware applies to all routes in group
colorAPI.use(middleware.requireAuth);
colorAPI.use(middleware.requireOrganization);
```

### Parameter Extraction

```typescript
// Single parameter
router.get('/api/colors/:id', req => {
  const { id } = req.params;
  // id is automatically extracted and sanitized
});

// Multiple parameters
router.get('/api/products/:productId/colors/:colorId', req => {
  const { productId, colorId } = req.params;
  // Both parameters extracted
});

// Wildcard routes
router.get('/api/admin/*', req => {
  // Matches /api/admin/users, /api/admin/settings, etc.
});
```

## Middleware System

### Execution Order

Middleware executes in this order:

1. Global middleware (`router.use('*', middleware)`)
2. Path-specific middleware (`router.use('/api/*', middleware)`)
3. Route-specific middleware (`router.get('/api/colors', middleware, handler)`)
4. Route handler

### Built-in Middleware

```typescript
import { middleware } from './router';

// Authentication
router.use('*', middleware.requireAuth); // Require authentication
router.use('*', middleware.optionalAuth); // Optional authentication

// Organization context
router.use('*', middleware.requireOrganization); // Require organization
router.use('*', middleware.optionalOrganization); // Optional organization

// Validation
router.post(
  '/api/colors',
  middleware.validation.custom({
    type: 'object',
    properties: {
      name: { type: 'string', minLength: 1 },
      hex: { type: 'string', pattern: '^#[0-9A-Fa-f]{6}$' },
    },
    required: ['name', 'hex'],
  }),
  handler
);

// Rate limiting
router.use('/api/*', middleware.rateLimit(100, 60000)); // 100 requests per minute

// Logging and monitoring
router.use('*', middleware.logger('ChromaSync'));
router.use('*', middleware.performanceMonitor(500)); // Warn if >500ms

// Error handling
router.use('*', middleware.errorHandler);
```

### Custom Middleware

```typescript
const customMiddleware: IPCMiddleware = async (req, res, next) => {
  // Modify request
  req.customProperty = 'value';

  // Validate something
  if (!req.body.requiredField) {
    throw new Error('Required field missing');
  }

  // Continue to next middleware/handler
  next();
};

router.use('/api/custom/*', customMiddleware);
```

## Request and Response Objects

### Request Object

```typescript
interface IPCRequest {
  channel: string; // Original IPC channel
  method: HTTPMethod; // HTTP method (get, post, etc.)
  path: string; // Route path
  params: IPCRouteParams; // Extracted parameters
  query: Record<string, any>; // Query parameters (future)
  body: any; // Request payload
  event: IpcMainEvent; // Original Electron event
  args: any[]; // Original IPC arguments
  organizationId?: string; // From middleware
  user?: any; // From auth middleware
  isAuthenticated?: boolean; // From auth middleware
  metadata: {
    timestamp: number;
    requestId: string;
    route?: IPCRoute;
    duration?: number; // From performance middleware
  };
}
```

### Response Helpers

```typescript
const handler = async (req, res) => {
  // Success response
  return res.success(data, 'Operation completed successfully');

  // Error response
  return res.error(error, 'User-friendly error message');

  // Validation error
  return res.validation(validationErrors, 'Please check your input');

  // Direct return (auto-wrapped in success)
  return { id: 123, name: 'Color Name' };
};
```

## Channel Format Support

The router supports both legacy and modern channel formats:

```typescript
// Legacy format (treated as GET request)
'color:getAll'           → GET /legacy/color:getAll

// Router format
'get:/api/colors'        → GET /api/colors
'post:/api/colors'       → POST /api/colors
'put:/api/colors/:id'    → PUT /api/colors/:id
'delete:/api/colors/:id' → DELETE /api/colors/:id
```

## Migration Strategy

### Phase 1: Side-by-side Operation

```typescript
// Set up router alongside existing handlers
const { router } = await setupChromaSyncRouter({
  maintainLegacy: true, // Keep existing IPC handlers
});

// New routes use router format
// Legacy routes continue working
```

### Phase 2: Gradual Migration

```typescript
// Migrate handlers one by one
router.get(
  '/api/colors',
  // Use existing thin handler
  colorHandlerThin.getAll
);

// Update frontend gradually
// Old: ipcRenderer.invoke('color:getAll')
// New: ipcRenderer.invoke('get:/api/colors')
```

### Phase 3: Legacy Cleanup

```typescript
// Remove legacy handlers after migration complete
const { router } = await setupChromaSyncRouter({
  maintainLegacy: false, // Disable legacy handlers
});
```

## Security Features

### Sender Validation

```typescript
// Automatic validation of IPC event sender
- Checks sender origin (file:// or chromasync://)
- Validates sender is not destroyed
- Prevents cross-origin IPC calls
```

### Parameter Sanitization

```typescript
// Automatic parameter sanitization
- Length limits (max 50 characters)
- Dangerous character removal
- URL decoding
- Type validation
```

### Rate Limiting

```typescript
// Per-sender rate limiting
router.use('/api/*', middleware.rateLimit(100, 60000));
// 100 requests per minute per sender
```

## Error Handling

### Automatic Error Handling

```typescript
// All errors are automatically caught and formatted
try {
  // Handler code
} catch (error) {
  return {
    success: false,
    error: error.message,
    userMessage: 'User-friendly error message',
    timestamp: Date.now(),
  };
}
```

### Error Classification

```typescript
// Errors are automatically classified
- Validation errors → 400-level responses
- Authentication errors → 401-level responses
- Authorization errors → 403-level responses
- Not found errors → 404-level responses
- Server errors → 500-level responses
```

## Performance Features

### Route Caching

```typescript
// Route patterns are compiled and cached
- First match compiles RegExp pattern
- Subsequent matches use cached pattern
- Significant performance improvement for high-traffic routes
```

### Performance Monitoring

```typescript
// Built-in performance monitoring
router.use('*', middleware.performanceMonitor(500));

// Logs slow requests automatically
[Performance] Slow request: GET /api/colors took 750ms
```

### Statistics

```typescript
// Router provides detailed statistics
const stats = router.getStats();
console.log({
  totalRoutes: stats.totalRoutes,
  totalMiddleware: stats.totalMiddleware,
  routesByMethod: stats.routesByMethod,
  averageMiddlewarePerRoute: stats.averageMiddlewarePerRoute,
});
```

## Development Features

### Debug Middleware

```typescript
// Automatic debug logging in development
if (process.env.NODE_ENV === 'development') {
  router.use('*', middleware.debug);
}

// Logs detailed request information
[Debug] Request: {
  channel: 'get:/api/colors',
  method: 'get',
  path: '/api/colors',
  params: {},
  body: {},
  organizationId: 'org-123'
}
```

### Route Inspection

```typescript
// Inspect registered routes
const routes = router.getRoutes();
const globalMiddleware = router.getGlobalMiddleware();
const pathMiddleware = router.getPathMiddleware();

// View router statistics
const stats = router.getStats();
```

## Best Practices

### 1. Use Route Groups

```typescript
// Good: Organized route groups
const colorAPI = router.group('/api/colors');
colorAPI.use(middleware.requireAuth);
colorAPI.use(middleware.requireOrganization);
colorAPI.get('/', getAllColors);
colorAPI.post('/', createColor);

// Avoid: Scattered individual routes
router.get(
  '/api/colors',
  middleware.requireAuth,
  middleware.requireOrganization,
  getAllColors
);
router.post(
  '/api/colors',
  middleware.requireAuth,
  middleware.requireOrganization,
  createColor
);
```

### 2. Use Appropriate Middleware Order

```typescript
// Good: Logical middleware order
router.use('*', middleware.logger('App')); // 1. Log everything
router.use('*', middleware.performanceMonitor(500)); // 2. Monitor performance
router.use('*', middleware.errorHandler); // 3. Handle errors
router.use('*', middleware.requireAuth); // 4. Require auth
router.use('/api/*', middleware.requireOrganization); // 5. Require org for API

// Avoid: Illogical order
router.use('*', middleware.requireAuth); // Auth before error handling
router.use('*', middleware.errorHandler); // Errors won't be caught
```

### 3. Use Descriptive Route Patterns

```typescript
// Good: RESTful, descriptive patterns
router.get('/api/colors', getAllColors);
router.get('/api/colors/:id', getColor);
router.post('/api/colors', createColor);
router.put('/api/colors/:id', updateColor);
router.delete('/api/colors/:id', deleteColor);

// Avoid: Non-descriptive patterns
router.get('/colors', handler);
router.get('/color-get', handler);
router.post('/do-color-stuff', handler);
```

### 4. Handle Errors Gracefully

```typescript
// Good: Specific error handling
const handler = async req => {
  try {
    const result = await service.doSomething(req.body);
    return result;
  } catch (error) {
    if (error.code === 'VALIDATION_ERROR') {
      throw new Error('Please check your input and try again.');
    }
    if (error.code === 'NOT_FOUND') {
      throw new Error('The requested resource was not found.');
    }
    throw new Error('An unexpected error occurred. Please try again.');
  }
};

// Avoid: Generic error handling
const handler = async req => {
  const result = await service.doSomething(req.body);
  return result; // Unhandled errors will show technical messages
};
```

## Integration with ChromaSync

The router system integrates seamlessly with existing ChromaSync infrastructure:

- **ServiceLocator**: Access services through dependency injection
- **Organization Context**: Automatic organization validation and scoping
- **Authentication**: Integration with existing OAuth service
- **Validation**: Uses existing IPCValidationMiddleware
- **Error Handling**: Compatible with existing error response formats
- **Rate Limiting**: Builds on existing rate limiting patterns

This provides a modern, scalable foundation for IPC communication while maintaining backward compatibility and leveraging existing architectural patterns.
