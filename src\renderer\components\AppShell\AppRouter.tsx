/**
 * @file AppRouter.tsx
 * @description Application routing and view management
 */

import React, { lazy, Suspense } from 'react';
import { MainLayout } from './MainLayout';
import { LoadingFallback } from '../common/LoadingFallback';

// Lazy load specialized views
const ColorSpace3DWindow = lazy(() =>
  import('../../color-space-3d-entry').then(m => ({
    default: m.ColorSpace3DWindow,
  }))
);

/**
 * Application router component
 */
export const AppRouter: React.FC = () => {
  // Check for special window modes
  const urlParams = new URLSearchParams(window.location.search);
  const modeParam = urlParams.get('mode');
  const isColorSpace3DWindow = modeParam === 'color-space-3d';

  // Debug logging for development
  if (process.env.NODE_ENV === 'development') {
    console.log('AppRouter: URL:', window.location.href);
    console.log('AppRouter: Mode parameter:', modeParam);
    console.log('AppRouter: Is 3D window:', isColorSpace3DWindow);
  }

  // Render specialized 3D color space window
  if (isColorSpace3DWindow) {
    return (
      <Suspense
        fallback={<LoadingFallback message='Loading 3D visualization...' />}
      >
        <ColorSpace3DWindow />
      </Suspense>
    );
  }

  // Render main application layout
  return <MainLayout />;
};

export default AppRouter;
