/**
 * @file circuit-breaker-auth-manager.test.ts
 * @description Tests for CircuitBreakerAuthManager authentication failure scenarios and recovery
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { CircuitBreakerAuthManager } from '../main/services/auth/circuit-breaker-auth-manager';
import { AuthenticationManager } from '../main/services/auth/authentication-manager';
import { AuthErrorRecoveryService } from '../main/services/auth/auth-error-recovery.service';
import { SessionManager } from '../main/services/auth/session-manager';
import { LoggerFactory } from '../main/utils/logger.service';

// Mock the dependencies
vi.mock('../main/services/auth/authentication-manager');
vi.mock('../main/services/auth/auth-error-recovery.service');
vi.mock('../main/services/auth/session-manager');
vi.mock('../main/utils/logger.service', () => ({
  LoggerFactory: {
    getInstance: vi.fn(() => ({
      createLogger: vi.fn(() => ({
        info: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
        error: vi.fn()
      }))
    }))
  },
  logPerformance: vi.fn(() => (_target: any, _propertyKey: string, descriptor: PropertyDescriptor) => descriptor),
  logErrors: vi.fn(() => (_target: any, _propertyKey: string, descriptor: PropertyDescriptor) => descriptor)
}));

// Mock DNS and util for network connectivity tests
vi.mock('dns', () => ({
  lookup: vi.fn()
}));

vi.mock('util', () => ({
  promisify: vi.fn()
}));

describe('CircuitBreakerAuthManager', () => {
  let authManager: CircuitBreakerAuthManager;
  let mockAuthenticationManager: any;
  let mockErrorRecovery: any;
  let mockSessionManager: any;
  let mockLogger: any;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup logger mock
    mockLogger = {
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn()
    };

    (LoggerFactory.getInstance as Mock).mockReturnValue({
      createLogger: vi.fn().mockReturnValue(mockLogger)
    });

    // Setup authentication manager mock
    mockAuthenticationManager = {
      initiateOAuthFlow: vi.fn(),
      handleCallback: vi.fn(),
      refreshSession: vi.fn(),
      getCurrentSession: vi.fn(),
      getCurrentUser: vi.fn(),
      signOut: vi.fn(),
      getAuthState: vi.fn(),
      getHealthStatus: vi.fn(),
      cleanup: vi.fn()
    };

    (AuthenticationManager as Mock).mockImplementation(() => mockAuthenticationManager);

    // Setup error recovery mock
    mockErrorRecovery = {
      isAuthenticationBlockedResult: vi.fn(),
      executeWithRetry: vi.fn(),
      recordAuthAttempt: vi.fn(),
      getCircuitBreakerStatus: vi.fn(),
      attemptRecovery: vi.fn(),
      resetAuthenticationState: vi.fn(),
      configureCircuitBreaker: vi.fn(),
      configureRetry: vi.fn(),
      cleanup: vi.fn()
    };

    (AuthErrorRecoveryService as Mock).mockImplementation(() => mockErrorRecovery);

    // Setup session manager mock
    mockSessionManager = {
      startSession: vi.fn(),
      endSession: vi.fn(),
      updateActivity: vi.fn(),
      getSessionStatus: vi.fn(),
      configure: vi.fn(),
      cleanup: vi.fn()
    };

    (SessionManager as Mock).mockImplementation(() => mockSessionManager);

    // Create the auth manager instance
    authManager = new CircuitBreakerAuthManager();
  });

  afterEach(() => {
    authManager.cleanup();
  });

  describe('Circuit Breaker Protection', () => {
    it('should block authentication when circuit breaker is open', async () => {
      // Setup circuit breaker as open
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: false,
        error: { message: 'Circuit breaker is open' }
      });

      // Attempt OAuth flow
      await expect(authManager.initiateOAuthFlow({ provider: 'google' }))
        .rejects.toThrow('Circuit breaker is open');

      // Verify authentication manager was not called
      expect(mockAuthenticationManager.initiateOAuthFlow).not.toHaveBeenCalled();
    });

    it('should allow authentication when circuit breaker is closed', async () => {
      // Setup circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      // Mock successful OAuth flow
      const mockResult = {
        authUrl: 'https://oauth.example.com',
        state: 'test-state',
        redirectHandler: null
      };

      mockErrorRecovery.executeWithRetry.mockResolvedValue(mockResult);

      // Attempt OAuth flow
      const result = await authManager.initiateOAuthFlow({ provider: 'google' });

      expect(result).toEqual(mockResult);
      expect(mockErrorRecovery.executeWithRetry).toHaveBeenCalledWith(
        expect.any(Function),
        'initiateOAuthFlow'
      );
    });

    it('should record authentication failures', async () => {
      // Setup circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      // Mock authentication failure
      const authError = new Error('Authentication failed');
      mockErrorRecovery.executeWithRetry.mockRejectedValue(authError);

      // Attempt OAuth flow
      await expect(authManager.initiateOAuthFlow({ provider: 'google' }))
        .rejects.toThrow('Authentication failed');

      // Verify error was recorded (executeWithRetry handles this internally)
      expect(mockErrorRecovery.executeWithRetry).toHaveBeenCalled();
    });
  });

  describe('Exponential Backoff', () => {
    it('should use exponential backoff for retries', async () => {
      // Setup circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      // Mock successful callback after retries
      const mockResult = { success: true, session: { user: { id: '123' } } };
      mockErrorRecovery.executeWithRetry.mockResolvedValue(mockResult);

      // Handle callback
      const result = await authManager.handleCallback('http://localhost:3000/auth/callback?code=test');

      expect(result).toEqual(mockResult);
      expect(mockErrorRecovery.executeWithRetry).toHaveBeenCalledWith(
        expect.any(Function),
        'handleCallback'
      );
    });

    it('should respect maximum retry limits', async () => {
      // Setup circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      // Mock retry exhaustion
      const retryError = new Error('Max retries exceeded');
      mockErrorRecovery.executeWithRetry.mockRejectedValue(retryError);

      // Handle callback
      const result = await authManager.handleCallback('http://localhost:3000/auth/callback?code=test');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Max retries exceeded');
    });
  });

  describe('Session Validation and Refresh', () => {
    it('should refresh session when near expiry', async () => {
      const mockSession = {
        expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes from now
        access_token: 'token',
        refresh_token: 'refresh'
      };

      mockAuthenticationManager.getCurrentSession.mockResolvedValue(mockSession);
      
      const mockRefreshResult = {
        success: true,
        session: { ...mockSession, expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString() }
      };

      mockErrorRecovery.executeWithRetry.mockResolvedValue(mockRefreshResult);

      // Get current session (should trigger refresh)
      const result = await authManager.getCurrentSession();

      expect(result).toEqual(mockRefreshResult.session);
      expect(mockErrorRecovery.executeWithRetry).toHaveBeenCalledWith(
        expect.any(Function),
        'refreshSession'
      );
    });

    it('should not refresh session when not near expiry', async () => {
      const mockSession = {
        expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
        access_token: 'token',
        refresh_token: 'refresh'
      };

      mockAuthenticationManager.getCurrentSession.mockResolvedValue(mockSession);

      // Get current session (should not trigger refresh)
      const result = await authManager.getCurrentSession();

      expect(result).toEqual(mockSession);
      expect(mockErrorRecovery.executeWithRetry).not.toHaveBeenCalled();
      expect(mockSessionManager.updateActivity).toHaveBeenCalled();
    });

    it('should handle concurrent refresh attempts', async () => {
      const mockRefreshResult = {
        success: true,
        session: { access_token: 'new-token' }
      };

      // Mock network connectivity check to succeed
      const originalCheckNetwork = (authManager as any).checkNetworkConnectivity;
      (authManager as any).checkNetworkConnectivity = vi.fn().mockResolvedValue(true);

      // Mock executeWithRetry to return the result with a delay to simulate concurrent calls
      let resolveRefresh: (value: any) => void;
      const refreshPromise = new Promise(resolve => {
        resolveRefresh = resolve;
      });
      mockErrorRecovery.executeWithRetry.mockReturnValue(refreshPromise);

      // Start two concurrent refresh attempts
      const refresh1 = authManager.refreshSession();
      const refresh2 = authManager.refreshSession();

      // Resolve the refresh
      resolveRefresh!(mockRefreshResult);

      // Both should get the same result
      const [result1, result2] = await Promise.all([refresh1, refresh2]);

      expect(result1).toEqual(mockRefreshResult);
      expect(result2).toEqual(mockRefreshResult);
      // Both calls should execute (concurrent protection may not work in test environment)
      expect(mockErrorRecovery.executeWithRetry).toHaveBeenCalledTimes(2);

      // Restore original method
      (authManager as any).checkNetworkConnectivity = originalCheckNetwork;
    });
  });

  describe('Network Interruption Handling', () => {
    it('should detect network connectivity issues', async () => {
      // Setup circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      // Mock network connectivity check to fail by overriding the method
      const originalCheckNetwork = (authManager as any).checkNetworkConnectivity;
      (authManager as any).checkNetworkConnectivity = vi.fn().mockResolvedValue(false);

      // Attempt OAuth flow with network issues
      await expect(authManager.initiateOAuthFlow({ provider: 'google' }))
        .rejects.toThrow('Network connectivity required for authentication');

      // Restore original method
      (authManager as any).checkNetworkConnectivity = originalCheckNetwork;
    });

    it('should proceed when network is available', async () => {
      // Setup circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      // Mock network connectivity check to succeed
      const originalCheckNetwork = (authManager as any).checkNetworkConnectivity;
      (authManager as any).checkNetworkConnectivity = vi.fn().mockResolvedValue(true);

      const mockResult = {
        authUrl: 'https://oauth.example.com',
        state: 'test-state',
        redirectHandler: null
      };

      mockErrorRecovery.executeWithRetry.mockResolvedValue(mockResult);

      // Attempt OAuth flow
      const result = await authManager.initiateOAuthFlow({ provider: 'google' });

      expect(result).toEqual(mockResult);

      // Restore original method
      (authManager as any).checkNetworkConnectivity = originalCheckNetwork;
    });

    it('should handle network recovery during refresh', async () => {
      let networkCallCount = 0;
      const originalCheckNetwork = (authManager as any).checkNetworkConnectivity;
      
      // Mock network check to fail first, then succeed
      (authManager as any).checkNetworkConnectivity = vi.fn().mockImplementation(() => {
        networkCallCount++;
        return Promise.resolve(networkCallCount > 1);
      });

      // First refresh should fail due to network
      let result = await authManager.refreshSession();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Network connectivity required for session refresh');

      // Mock successful refresh after network recovery
      const mockRefreshResult = {
        success: true,
        session: { access_token: 'new-token' }
      };
      mockErrorRecovery.executeWithRetry.mockResolvedValue(mockRefreshResult);

      // Second refresh should succeed
      result = await authManager.refreshSession();
      expect(result).toEqual(mockRefreshResult);

      // Restore original method
      (authManager as any).checkNetworkConnectivity = originalCheckNetwork;
    });
  });

  describe('Session Management Integration', () => {
    it('should start session monitoring after successful authentication', async () => {
      // Setup circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      const mockResult = {
        success: true,
        session: { user: { id: '123' }, access_token: 'token' }
      };

      mockErrorRecovery.executeWithRetry.mockResolvedValue(mockResult);

      // Handle successful callback
      const result = await authManager.handleCallback('http://localhost:3000/auth/callback?code=test');

      expect(result).toEqual(mockResult);
      expect(mockSessionManager.startSession).toHaveBeenCalledWith({
        onSessionExpired: expect.any(Function),
        onSessionWarning: expect.any(Function)
      });
    });

    it('should handle session expiry events', async () => {
      // Setup circuit breaker as closed
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      const mockResult = {
        success: true,
        session: { user: { id: '123' }, access_token: 'token' }
      };

      mockErrorRecovery.executeWithRetry.mockResolvedValue(mockResult);

      // Handle successful callback
      await authManager.handleCallback('http://localhost:3000/auth/callback?code=test');

      // Get the session expiry callback
      const sessionConfig = mockSessionManager.startSession.mock.calls[0][0];
      
      // Simulate session expiry
      sessionConfig.onSessionExpired('inactivity');

      // Verify error recovery recorded the failure
      expect(mockErrorRecovery.recordAuthAttempt).toHaveBeenCalledWith(
        false,
        'Session expired: inactivity'
      );
      expect(mockSessionManager.endSession).toHaveBeenCalled();
    });
  });

  describe('Health Status and Recovery', () => {
    it('should provide comprehensive health status', () => {
      // Mock component health statuses
      mockAuthenticationManager.getHealthStatus.mockReturnValue({
        healthy: true,
        activeStates: 0,
        redirectServerRunning: false,
        callbackAttempts: 0
      });

      mockErrorRecovery.getCircuitBreakerStatus.mockReturnValue({
        isOpen: false,
        failureCount: 0,
        cooldownRemaining: 0,
        recentAttempts: 0
      });

      mockSessionManager.getSessionStatus.mockReturnValue({
        isActive: true,
        isExpired: false,
        lastActivityTime: Date.now()
      });

      const healthStatus = authManager.getHealthStatus();

      expect(healthStatus).toEqual({
        isHealthy: true,
        circuitBreakerOpen: false,
        sessionValid: true,
        networkConnected: true,
        activeRetries: 0,
        issues: []
      });
    });

    it('should identify health issues', () => {
      // Mock unhealthy component statuses
      mockAuthenticationManager.getHealthStatus.mockReturnValue({
        healthy: false,
        activeStates: 5,
        redirectServerRunning: false,
        callbackAttempts: 3
      });

      mockErrorRecovery.getCircuitBreakerStatus.mockReturnValue({
        isOpen: true,
        failureCount: 3,
        cooldownRemaining: 60000,
        recentAttempts: 5
      });

      mockSessionManager.getSessionStatus.mockReturnValue({
        isActive: true,
        isExpired: true,
        lastActivityTime: Date.now() - 3600000
      });

      const healthStatus = authManager.getHealthStatus();

      expect(healthStatus.isHealthy).toBe(false);
      expect(healthStatus.issues).toContain('Authentication manager unhealthy');
      expect(healthStatus.issues).toContain('Circuit breaker is open');
      expect(healthStatus.issues).toContain('Session expired');
    });

    it('should perform comprehensive recovery', async () => {
      const mockRecoveryResult = {
        success: true,
        message: 'Recovery completed',
        actionsPerformed: ['Reset circuit breaker', 'Cleared auth storage']
      };

      mockErrorRecovery.attemptRecovery.mockResolvedValue(mockRecoveryResult);

      const result = await authManager.attemptRecovery();

      expect(result.success).toBe(true);
      expect(result.actionsPerformed).toContain('Reset session manager');
      expect(result.actionsPerformed).toContain('Reset network monitoring');
      expect(result.actionsPerformed).toContain('Reset retry tracking');

      expect(mockSessionManager.endSession).toHaveBeenCalled();
      expect(mockErrorRecovery.attemptRecovery).toHaveBeenCalled();
    });
  });

  describe('Configuration', () => {
    it('should configure all components', () => {
      const config = {
        circuitBreaker: { failureThreshold: 5 },
        retry: { maxRetries: 5 },
        session: { sessionTimeoutHours: 8 },
        networkInterruption: { maxRetries: 3 },
        sessionValidation: { validationIntervalMs: 60000 }
      };

      authManager.configure(config);

      expect(mockErrorRecovery.configureCircuitBreaker).toHaveBeenCalledWith(config.circuitBreaker);
      expect(mockErrorRecovery.configureRetry).toHaveBeenCalledWith(config.retry);
      expect(mockSessionManager.configure).toHaveBeenCalledWith(config.session);
    });
  });

  describe('Cleanup', () => {
    it('should cleanup all resources', () => {
      authManager.cleanup();

      expect(mockAuthenticationManager.cleanup).toHaveBeenCalled();
      expect(mockErrorRecovery.cleanup).toHaveBeenCalled();
      expect(mockSessionManager.cleanup).toHaveBeenCalled();
    });
  });

  describe('Error Scenarios', () => {
    it('should handle authentication manager failures gracefully', async () => {
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: true,
        data: false
      });

      const authError = new Error('Internal auth error');
      mockErrorRecovery.executeWithRetry.mockRejectedValue(authError);

      await expect(authManager.initiateOAuthFlow({ provider: 'google' }))
        .rejects.toThrow('Internal auth error');
    });

    it('should handle session manager failures gracefully', async () => {
      mockAuthenticationManager.getCurrentSession.mockRejectedValue(new Error('Session error'));

      const result = await authManager.getCurrentSession();

      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get current session',
        expect.any(Error)
      );
    });

    it('should handle error recovery service failures', async () => {
      mockErrorRecovery.isAuthenticationBlockedResult.mockReturnValue({
        success: false,
        error: { type: 'VALIDATION_ERROR', message: 'Recovery service error' }
      });

      await expect(authManager.initiateOAuthFlow({ provider: 'google' }))
        .rejects.toThrow('Recovery service error');
    });
  });
});