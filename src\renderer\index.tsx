/**
 * @file index.tsx
 * @description Entry point for the renderer process
 */

import React, { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
// ErrorBoundary is defined below
import './index.css'; // This imports Tailwind
import './test.css';  // This imports our test CSS

// Test console logging - this should appear in debug-console.log
console.log('🚀 [RENDERER] Starting up - imports loaded');
console.info('📋 [RENDERER] Environment:', process.env.NODE_ENV);
console.warn('⚠️ [RENDERER] This is a test warning to verify logging');

// Add error boundary to catch initialization errors
window.addEventListener('error', (event) => {
  console.error('Global error caught:', event.error);
  console.error('Stack:', event.error?.stack);
});

// Clear potentially corrupted localStorage in production
if (process.env.NODE_ENV === 'production') {
  try {
    const colorStorage = localStorage.getItem('color-storage');
    if (colorStorage) {
      const parsed = JSON.parse(colorStorage);
      // Check for corrupted state
      if (parsed.state && (!Array.isArray(parsed.state.colors) || 
          !Array.isArray(parsed.state.pantoneColors) || 
          !Array.isArray(parsed.state.ralColors))) {
        console.warn('Clearing corrupted color storage');
        localStorage.removeItem('color-storage');
      }
    }
  } catch (e) {
    console.error('Error checking localStorage:', e);
    localStorage.removeItem('color-storage');
  }
}

/**
 * Development Keyboard Shortcuts Component
 * Reserved for future development tools
 */
const DevToolsKeyboardShortcuts: React.FC = () => {
  useEffect(() => {
    // Future keyboard shortcuts can be added here
    const handleKeyDown = (e: KeyboardEvent) => {
      // Reserved for future development shortcuts
      console.debug('Dev shortcut detected:', e.key);
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return null;
};

// Mount React app
console.log('🎯 [RENDERER] About to mount React app');
const container = document.getElementById('root');
if (!container) {
  throw new Error('Failed to find the root element');
}

console.log('⚛️ [RENDERER] Creating React root');
const root = ReactDOM.createRoot(container);

// Only render the DevTools component in development
const isDev = process.env.NODE_ENV === 'development';

// Error boundary component
class ErrorBoundary extends React.Component<{children: React.ReactNode}, {hasError: boolean, error: Error | null}> {
  constructor(props: {children: React.ReactNode}) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('React Error Boundary caught:', error, errorInfo);
  }

  override render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', color: 'red' }}>
          <h1>Something went wrong</h1>
          <pre>{this.state.error?.message}</pre>
          <pre>{this.state.error?.stack}</pre>
          <button onClick={() => {
            localStorage.clear();
            window.location.reload();
          }}>Clear Storage and Reload</button>
        </div>
      );
    }

    return this.props.children;
  }
}

console.log('🚀 [RENDERER] Rendering React app to DOM');
root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <App />
      {isDev && <DevToolsKeyboardShortcuts />}
    </ErrorBoundary>
  </React.StrictMode>
);
console.log('✅ [RENDERER] React app render call completed'); 