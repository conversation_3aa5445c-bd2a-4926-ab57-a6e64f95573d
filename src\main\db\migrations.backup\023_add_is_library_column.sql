-- Migration: 023_add_is_library_column.sql
-- Purpose: Add is_library column to colors table if it doesn't exist
-- Date: 2025-06-19
-- Author: ChromaSync Team

BEGIN TRANSACTION;

-- Check if is_library column exists
CREATE TEMPORARY TABLE column_check AS
SELECT COUNT(*) as column_exists 
FROM pragma_table_info('colors') 
WHERE name = 'is_library';

-- Only add column if it doesn't exist
-- SQLite doesn't support conditional ALTER TABLE, so we need to check manually
-- This migration should be idempotent

-- First, check current state
SELECT 
  CASE 
    WHEN (SELECT column_exists FROM column_check) > 0 
    THEN 'is_library column already exists' 
    ELSE 'is_library column needs to be added' 
  END as status;

-- If you need to add the column manually, use:
-- ALTER TABLE colors ADD COLUMN is_library BOOLEAN NOT NULL DEFAULT 0;

-- Clean up
DROP TABLE column_check;

COMMIT;

-- Note: Since SQLite doesn't support conditional ALTER TABLE,
-- the actual column addition should be done by checking first:
-- 
-- 1. Check if column exists:
--    SELECT COUNT(*) FROM pragma_table_info('colors') WHERE name = 'is_library';
-- 
-- 2. If result is 0, then add column:
--    ALTER TABLE colors ADD COLUMN is_library BOOLEAN NOT NULL DEFAULT 0;
