-- Migration 020: Fix product_colors foreign key types
-- CRITICAL FIX: Change INTEGER foreign keys to TEXT (UUID) to match products/colors tables
-- 
-- Root Cause: product_colors table was incorrectly defined with INTEGER foreign keys
-- but products.id and colors.id are TEXT (UUID) primary keys, causing all relationship
-- syncing to fail silently.

PRAGMA foreign_keys = OFF;

-- Step 1: Create new table with correct UUID foreign key types
CREATE TABLE product_colors_new (
  product_id TEXT NOT NULL,  -- Changed from INTEGER to TEXT
  color_id TEXT NOT NULL,    -- Changed from INTEGER to TEXT  
  display_order INTEGER NOT NULL DEFAULT 0,
  organization_id TEXT NOT NULL,
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (product_id, color_id),
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
  FOREIGN KEY (color_id) REFERENCES colors(id) ON DELETE CASCADE
);

-- Step 2: Copy any existing valid data (where UUIDs can be matched)
-- This preserves relationships that were somehow created with valid UUIDs
INSERT INTO product_colors_new (product_id, color_id, display_order, organization_id, added_at)
SELECT 
  p.id as product_id,
  c.id as color_id, 
  pc.display_order,
  pc.organization_id,
  pc.added_at
FROM product_colors pc
INNER JOIN products p ON p.id = CAST(pc.product_id AS TEXT)  -- Try to match UUIDs
INNER JOIN colors c ON c.id = CAST(pc.color_id AS TEXT)      -- Try to match UUIDs
WHERE pc.organization_id IS NOT NULL;

-- Step 3: Drop old table and rename new table
DROP TABLE product_colors;
ALTER TABLE product_colors_new RENAME TO product_colors;

-- Step 4: Recreate indexes with correct types
CREATE INDEX idx_product_colors_product ON product_colors(product_id);
CREATE INDEX idx_product_colors_color ON product_colors(color_id);
CREATE INDEX idx_product_colors_org ON product_colors(organization_id);
CREATE INDEX idx_product_colors_org_product ON product_colors(organization_id, product_id);
CREATE INDEX idx_product_colors_org_color ON product_colors(organization_id, color_id);

PRAGMA foreign_keys = ON;

-- Verification: Check that foreign key constraints work
PRAGMA foreign_key_check(product_colors);