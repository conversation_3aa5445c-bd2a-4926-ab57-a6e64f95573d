/**
 * @file example-usage.js
 * @description Example usage scenarios for the Migration Test Suite
 * 
 * This file demonstrates various ways to use the migration test suite
 * programmatically, including custom test configurations, integration
 * patterns, and advanced usage scenarios.
 */

const { MigrationTestSuite, TEST_CONFIG } = require('./migration-test-suite');
const { TestRunner } = require('./test-runner');
const path = require('path');

/**
 * Example 1: Basic test suite usage
 */
async function basicUsageExample() {
  console.log('📘 Example 1: Basic Test Suite Usage');
  console.log('=====================================\n');
  
  const testSuite = new MigrationTestSuite({
    verbose: true,
    testDataSize: 'small'
  });
  
  try {
    const report = await testSuite.runCompleteTestSuite();
    
    console.log('\n✅ Basic test completed');
    console.log(`Status: ${report.summary.overallStatus}`);
    console.log(`Tests run: ${report.summary.testsRun}`);
    console.log(`Duration: ${report.summary.totalDuration.toFixed(2)}ms`);
    
    return report;
    
  } catch (error) {
    console.error('❌ Basic test failed:', error.message);
    throw error;
  }
}

/**
 * Example 2: Custom test configuration
 */
async function customConfigurationExample() {
  console.log('\n📘 Example 2: Custom Test Configuration');
  console.log('=======================================\n');
  
  const customConfig = {
    verbose: false,
    testDataSize: 'medium',
    sourceDbPath: '/custom/path/to/database.db'
  };
  
  const testSuite = new MigrationTestSuite(customConfig);
  
  try {
    // Initialize manually
    await testSuite.initialize();
    
    // Run specific phases only
    await testSuite.runSchemaValidationTests();
    await testSuite.generateTestData();
    await testSuite.runPerformanceBenchmarks();
    
    // Generate custom report
    testSuite.generateFinalReport();
    
    console.log('✅ Custom configuration test completed');
    
    return testSuite.testReport;
    
  } finally {
    await testSuite.cleanup();
  }
}

/**
 * Example 3: Performance comparison across configurations
 */
async function performanceComparisonExample() {
  console.log('\n📘 Example 3: Performance Comparison');
  console.log('====================================\n');
  
  const runner = new TestRunner({
    verbose: true,
    outputDir: __dirname
  });
  
  const configurations = {
    'Small Dataset': {
      testDataSize: 'small'
    },
    'Medium Dataset': {
      testDataSize: 'medium'
    },
    'Large Dataset': {
      testDataSize: 'large'
    }
  };
  
  try {
    const comparison = await runner.runPerformanceComparison(configurations);
    
    console.log('\n✅ Performance comparison completed');
    console.log(`Best configuration: ${comparison.summary.bestConfiguration.name}`);
    console.log(`Performance score: ${comparison.summary.bestConfiguration.score.toFixed(2)}`);
    
    return comparison;
    
  } catch (error) {
    console.error('❌ Performance comparison failed:', error.message);
    throw error;
  }
}

/**
 * Example 4: Stress testing scenario
 */
async function stressTestingExample() {
  console.log('\n📘 Example 4: Stress Testing');
  console.log('============================\n');
  
  const runner = new TestRunner({
    verbose: true,
    outputDir: __dirname
  });
  
  try {
    const stressResults = await runner.runStressTests();
    
    console.log('\n✅ Stress testing completed');
    
    // Analyze results
    const passedLevels = Object.values(stressResults).filter(r => r.passed).length;
    const totalLevels = Object.keys(stressResults).length;
    
    console.log(`Passed stress levels: ${passedLevels}/${totalLevels}`);
    
    // Display results by level
    Object.entries(stressResults).forEach(([level, result]) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${level}: ${result.passed ? 'PASSED' : 'FAILED'}`);
    });
    
    return stressResults;
    
  } catch (error) {
    console.error('❌ Stress testing failed:', error.message);
    throw error;
  }
}

/**
 * Example 5: Phase-specific testing
 */
async function phaseSpecificExample() {
  console.log('\n📘 Example 5: Phase-Specific Testing');
  console.log('====================================\n');
  
  const runner = new TestRunner({
    verbose: true,
    outputDir: __dirname
  });
  
  const phases = ['schema', 'migration', 'performance'];
  
  try {
    const report = await runner.runPhases(phases, {
      testDataSize: 'medium'
    });
    
    console.log('\n✅ Phase-specific testing completed');
    console.log(`Phases tested: ${phases.join(', ')}`);
    console.log(`Total tests: ${report.summary.totalTests}`);
    console.log(`Passed: ${report.summary.passedTests}`);
    console.log(`Failed: ${report.summary.failedTests}`);
    
    return report;
    
  } catch (error) {
    console.error('❌ Phase-specific testing failed:', error.message);
    throw error;
  }
}

/**
 * Example 6: Environment validation and planning
 */
async function environmentValidationExample() {
  console.log('\n📘 Example 6: Environment Validation');
  console.log('====================================\n');
  
  const runner = new TestRunner({
    verbose: true,
    outputDir: __dirname
  });
  
  try {
    // Validate environment first
    const validation = await runner.validateEnvironment();
    
    if (!validation.passed) {
      console.log('❌ Environment validation failed - cannot proceed');
      return validation;
    }
    
    console.log('✅ Environment validation passed');
    
    // Generate execution plan
    const plan = runner.generateExecutionPlan(['complete'], {
      testDataSize: 'large'
    });
    
    console.log('\n📋 Execution Plan Generated');
    console.log(`Estimated duration: ${plan.estimatedDuration} seconds`);
    console.log(`Total phases: ${plan.phases.length}`);
    
    if (plan.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      plan.warnings.forEach(warning => console.log(`   - ${warning}`));
    }
    
    return { validation, plan };
    
  } catch (error) {
    console.error('❌ Environment validation failed:', error.message);
    throw error;
  }
}

/**
 * Example 7: Custom test implementation
 */
async function customTestExample() {
  console.log('\n📘 Example 7: Custom Test Implementation');
  console.log('=======================================\n');
  
  class CustomMigrationTestSuite extends MigrationTestSuite {
    constructor(options) {
      super(options);
    }
    
    /**
     * Custom test for specific business logic
     */
    async testCustomBusinessLogic() {
      const startTime = performance.now();
      
      try {
        // Custom test implementation
        console.log('   🔍 Running custom business logic test');
        
        // Example: Test UUID uniqueness across all tables
        const tables = ['organizations', 'products', 'colors'];
        const allUUIDs = [];
        
        for (const table of tables) {
          const uuids = this.testDb.prepare(`
            SELECT external_id FROM ${table} WHERE external_id IS NOT NULL
          `).all().map(row => row.external_id);
          
          allUUIDs.push(...uuids);
        }
        
        const uniqueUUIDs = new Set(allUUIDs);
        
        if (uniqueUUIDs.size !== allUUIDs.length) {
          throw new Error(`UUID collision detected: ${allUUIDs.length} total UUIDs, ${uniqueUUIDs.size} unique`);
        }
        
        const duration = performance.now() - startTime;
        
        return {
          test: 'Custom Business Logic',
          status: 'passed',
          duration: duration,
          message: `Validated ${allUUIDs.length} UUIDs across ${tables.length} tables`,
          details: {
            totalUUIDs: allUUIDs.length,
            uniqueUUIDs: uniqueUUIDs.size,
            tablesChecked: tables.length
          }
        };
        
      } catch (error) {
        const duration = performance.now() - startTime;
        return {
          test: 'Custom Business Logic',
          status: 'failed',
          duration: duration,
          error: error.message
        };
      }
    }
    
    /**
     * Override schema validation to include custom checks
     */
    async runSchemaValidationTests() {
      await super.runSchemaValidationTests();
      
      // Add custom test
      const customResult = await this.testCustomBusinessLogic();
      this.testReport.phases.schemaValidation.results.push(customResult);
      
      console.log('   ✅ Added custom business logic test to schema validation');
    }
  }
  
  const customTestSuite = new CustomMigrationTestSuite({
    verbose: true,
    testDataSize: 'small'
  });
  
  try {
    await customTestSuite.initialize();
    await customTestSuite.generateTestData();
    await customTestSuite.runSchemaValidationTests();
    
    console.log('\n✅ Custom test implementation completed');
    
    return customTestSuite.testReport;
    
  } finally {
    await customTestSuite.cleanup();
  }
}

/**
 * Example 8: Integration testing pattern
 */
async function integrationTestingExample() {
  console.log('\n📘 Example 8: Integration Testing Pattern');
  console.log('=========================================\n');
  
  const testResults = [];
  
  try {
    // Step 1: Environment validation
    console.log('🔍 Step 1: Validating environment...');
    const runner = new TestRunner({ verbose: false });
    const validation = await runner.validateEnvironment();
    
    if (!validation.passed) {
      throw new Error('Environment validation failed');
    }
    
    testResults.push({ step: 'validation', status: 'passed' });
    console.log('✅ Environment validation passed');
    
    // Step 2: Schema validation
    console.log('\n🔍 Step 2: Schema validation...');
    const schemaReport = await runner.runPhases(['schema'], { testDataSize: 'small' });
    
    if (schemaReport.summary.failedTests > 0) {
      throw new Error('Schema validation failed');
    }
    
    testResults.push({ step: 'schema', status: 'passed' });
    console.log('✅ Schema validation passed');
    
    // Step 3: Migration testing
    console.log('\n🔍 Step 3: Migration testing...');
    const migrationReport = await runner.runPhases(['migration'], { testDataSize: 'small' });
    
    if (migrationReport.summary.failedTests > 0) {
      throw new Error('Migration testing failed');
    }
    
    testResults.push({ step: 'migration', status: 'passed' });
    console.log('✅ Migration testing passed');
    
    // Step 4: Performance validation
    console.log('\n🔍 Step 4: Performance validation...');
    const perfReport = await runner.runPhases(['performance'], { testDataSize: 'small' });
    
    // Check for performance issues
    const hasPerformanceIssues = perfReport.results.performance.results.some(
      result => result.details && result.details.performanceRatio > 3
    );
    
    if (hasPerformanceIssues) {
      console.log('⚠️  Performance issues detected, but continuing...');
      testResults.push({ step: 'performance', status: 'warning' });
    } else {
      testResults.push({ step: 'performance', status: 'passed' });
      console.log('✅ Performance validation passed');
    }
    
    // Final summary
    console.log('\n📋 Integration Test Summary:');
    testResults.forEach(result => {
      const icon = result.status === 'passed' ? '✅' : 
                   result.status === 'warning' ? '⚠️' : '❌';
      console.log(`${icon} ${result.step}: ${result.status.toUpperCase()}`);
    });
    
    const overallStatus = testResults.every(r => r.status !== 'failed') ? 'PASSED' : 'FAILED';
    console.log(`\n🎯 Overall Status: ${overallStatus}`);
    
    return {
      overallStatus: overallStatus,
      steps: testResults,
      reports: {
        validation,
        schema: schemaReport,
        migration: migrationReport,
        performance: perfReport
      }
    };
    
  } catch (error) {
    console.error(`❌ Integration testing failed at step: ${error.message}`);
    
    testResults.push({ 
      step: 'failed', 
      status: 'failed', 
      error: error.message 
    });
    
    return {
      overallStatus: 'FAILED',
      steps: testResults,
      error: error.message
    };
  }
}

/**
 * Main function to run all examples
 */
async function runAllExamples() {
  console.log('🚀 Migration Test Suite - Example Usage');
  console.log('=======================================\n');
  
  const examples = [
    { name: 'Basic Usage', fn: basicUsageExample },
    { name: 'Custom Configuration', fn: customConfigurationExample },
    { name: 'Performance Comparison', fn: performanceComparisonExample },
    { name: 'Stress Testing', fn: stressTestingExample },
    { name: 'Phase-Specific Testing', fn: phaseSpecificExample },
    { name: 'Environment Validation', fn: environmentValidationExample },
    { name: 'Custom Test Implementation', fn: customTestExample },
    { name: 'Integration Testing Pattern', fn: integrationTestingExample }
  ];
  
  const results = {};
  
  for (const example of examples) {
    try {
      console.log(`\n🔄 Running ${example.name}...`);
      const result = await example.fn();
      results[example.name] = { status: 'success', result };
      console.log(`✅ ${example.name} completed successfully`);
    } catch (error) {
      results[example.name] = { status: 'error', error: error.message };
      console.log(`❌ ${example.name} failed: ${error.message}`);
    }
  }
  
  // Summary
  console.log('\n📋 Examples Summary:');
  console.log('===================');
  
  Object.entries(results).forEach(([name, result]) => {
    const icon = result.status === 'success' ? '✅' : '❌';
    console.log(`${icon} ${name}: ${result.status.toUpperCase()}`);
  });
  
  const successCount = Object.values(results).filter(r => r.status === 'success').length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${successCount}/${totalCount} examples completed successfully`);
  
  return results;
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🧪 Migration Test Suite - Example Usage

Usage: node example-usage.js <example-number>

Examples:
  1  - Basic usage
  2  - Custom configuration
  3  - Performance comparison
  4  - Stress testing
  5  - Phase-specific testing
  6  - Environment validation
  7  - Custom test implementation
  8  - Integration testing pattern
  
  all - Run all examples

Example:
  node example-usage.js 1
  node example-usage.js all
    `);
    process.exit(0);
  }
  
  const exampleNumber = args[0];
  
  async function runExample() {
    try {
      switch (exampleNumber) {
        case '1':
          await basicUsageExample();
          break;
        case '2':
          await customConfigurationExample();
          break;
        case '3':
          await performanceComparisonExample();
          break;
        case '4':
          await stressTestingExample();
          break;
        case '5':
          await phaseSpecificExample();
          break;
        case '6':
          await environmentValidationExample();
          break;
        case '7':
          await customTestExample();
          break;
        case '8':
          await integrationTestingExample();
          break;
        case 'all':
          await runAllExamples();
          break;
        default:
          console.error(`❌ Unknown example: ${exampleNumber}`);
          process.exit(1);
      }
      
      console.log('\n🏁 Example completed!');
      process.exit(0);
      
    } catch (error) {
      console.error('❌ Example failed:', error.message);
      process.exit(1);
    }
  }
  
  runExample();
}

module.exports = {
  basicUsageExample,
  customConfigurationExample,
  performanceComparisonExample,
  stressTestingExample,
  phaseSpecificExample,
  environmentValidationExample,
  customTestExample,
  integrationTestingExample,
  runAllExamples
};