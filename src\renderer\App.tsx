/**
 * @file App.tsx
 * @description Root component for the ChromaSync application
 * Refactored to use modular AppShell architecture
 */

import { lazy, Suspense } from 'react';
import { AppShell } from './components/AppShell/AppShell';
import { AppInitializer } from './components/AppInitializer';
import { TokenProvider } from './context/TokenProvider';
import { ColorBlindnessProvider } from './context/ColorBlindnessContext';
import { SyncStatusMonitor } from './components/Sync/SyncStatusMonitor';

/**
 * Main App component - entry point for ChromaSync
 * 
 * Handles:
 * - 3D Color Space window routing
 * - Main application shell delegation
 */
export default function App() {
  // Check if this is a 3D color space window
  const urlParams = new URLSearchParams(window.location.search);
  const modeParam = urlParams.get('mode');
  const isColorSpace3DWindow = modeParam === 'color-space-3d';
  
  // If this is a 3D window, render the specialized component
  if (isColorSpace3DWindow) {
    const ColorSpace3DWindow = lazy(() => 
      import('./color-space-3d-entry').then(m => ({ default: m.ColorSpace3DWindow }))
    );
    
    return (
      <TokenProvider>
        <ColorBlindnessProvider>
          <Suspense 
            fallback={
              <div className="flex items-center justify-center h-screen bg-ui-background-primary">
                <div className="flex items-center space-x-3">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
                  <span className="text-ui-foreground-secondary">Loading 3D visualization...</span>
                </div>
              </div>
            }
          >
            <ColorSpace3DWindow />
          </Suspense>
        </ColorBlindnessProvider>
      </TokenProvider>
    );
  }

  // Render main application with AppInitializer at the top level
  return (
    <TokenProvider>
      <ColorBlindnessProvider>
        <AppInitializer>
          <AppShell />
          {process.env.NODE_ENV === 'development' && <SyncStatusMonitor />}
        </AppInitializer>
      </ColorBlindnessProvider>
    </TokenProvider>
  );
}