/**
 * @file color.repository.interface.ts
 * @description Interface for ColorRepository data access layer with UUID support
 *
 * Defines the contract for all database operations related to colors.
 * This interface separates data access concerns from business logic.
 * Updated to support UUID-based operations while maintaining migration compatibility.
 */

import Database from 'better-sqlite3';
import {
  NewColorEntry,
  UpdateColorEntry,
} from '../../../../shared/types/color.types';
import {
  UUID,
  ColorUUID,
  CreateColorUUID,
  UpdateColorUUID,
  // ColorWithProductsUUID - unused
} from '../../../../shared/types/uuid-types';
// TODO: Re-enable when migration types are needed
// import {
//   TransitionID,
//   TransitionColor,
//   MigrationResult,
//   BatchResult
// } from '../../../../shared/types/migration-types';

/**
 * Legacy color repository interface (for backward compatibility)
 */
export interface IColorRepository {
  // Core CRUD Operations
  findAll(organizationId: string): ColorRow[];
  findById(colorId: string, organizationId: string): ColorRow | null;
  insert(colorData: NewColorEntry, organizationId: string): string;
  update(
    id: string,
    updates: UpdateColorEntry,
    organizationId: string
  ): boolean;
  softDelete(colorId: string, organizationId: string): boolean;

  // Query Operations
  getUsageCounts(
    organizationId: string
  ): Map<string, { count: number; products: string[] }>;
  getColorNameProductMap(organizationId: string): Map<string, string[]>;
  findUnsynced(): ColorRow[];
  search(query: string, organizationId: string): ColorRow[];
  findByCode(code: string, organizationId: string): ColorRow | null;
  findByHex(hex: string, organizationId: string): ColorRow[];

  // Soft Delete Operations
  findSoftDeleted(
    organizationId: string,
    limit?: number,
    offset?: number
  ): ColorRow[];
  restoreRecord(colorId: string, organizationId: string): boolean;
  bulkRestoreRecords(
    colorIds: string[],
    organizationId: string
  ): { success: boolean; restored: number };
  cleanupOldSoftDeleted(
    organizationId: string,
    daysOld?: number
  ): { success: boolean; cleaned: number };

  // Bulk Operations
  clearAll(organizationId: string, hardDelete?: boolean): boolean;
  invalidateOrphans(organizationId: string): void;
  bulkInsert(
    colors: CreateColorUUID[],
    organizationId: string
  ): { success: boolean; inserted: number; errors: string[] };
  bulkUpdate(
    updates: Array<{ id: string; data: UpdateColorEntry }>,
    organizationId: string
  ): { success: boolean; updated: number; errors: string[] };
  bulkDelete(
    colorIds: string[],
    organizationId: string,
    hardDelete?: boolean
  ): { success: boolean; deleted: number; errors: string[] };

  // Sync Operations
  markAsSynced(colorId: string): void;
  markAsUnsynced(colorId: string): void;
  bulkMarkAsSynced(colorIds: string[]): { success: boolean; marked: number };
  upsertFromSync(colorData: any, organizationId: string): ColorRow | null;

  // Migration Support
  findByLegacyId(legacyId: number, organizationId: string): ColorRow | null;
  getLegacyId(colorId: string, organizationId: string): number | null;

  // Source Operations
  getSourceIdByCode(sourceCode: string): string | null;
  findBySourceId(sourceId: string, organizationId: string): ColorRow[];

  // Utility
  exists(colorId: string, organizationId: string): boolean;
  count(organizationId: string): number;
  getPreparedStatement(sql: string): Database.Statement;
}

/**
 * Extended color repository interface with optimization features
 */
export interface IOptimizedColorRepository extends IColorRepository {
  // Optimized query methods
  findAllOptimized(organizationId: string): ColorRow[];
  getUsageCountsOptimized(
    organizationId: string
  ): Map<string, { count: number; products: string[] }>;
  getColorNameProductMapOptimized(
    organizationId: string
  ): Map<string, string[]>;

  // Performance monitoring
  metricsEnabled: boolean;
  enableMetrics(): void;
  disableMetrics(): void;

  // Aggregation table operations
  getAggregationTableStats(organizationId: string): Record<string, any>;
  refreshAggregationTables(organizationId: string): boolean;

  // Statement caching
  clearStatementCache(): void;
  getStatementCacheSize(): number;
}

/**
 * Extended color repository interface with UUID support
 */
export interface IColorRepositoryUUID {
  // Core CRUD Operations
  findAll(organizationId: UUID): Promise<ColorUUIDRow[]>;
  findById(colorId: UUID, organizationId: UUID): Promise<ColorUUIDRow | null>;
  insert(colorData: CreateColorUUID, organizationId: UUID): Promise<UUID>;
  update(
    id: UUID,
    updates: UpdateColorUUID,
    organizationId: UUID
  ): Promise<boolean>;
  softDelete(colorId: UUID, organizationId: UUID): Promise<boolean>;
  hardDelete(colorId: UUID, organizationId: UUID): Promise<boolean>;

  // Query Operations
  getUsageCounts(
    organizationId: UUID
  ): Promise<Map<UUID, { count: number; products: UUID[] }>>;
  getColorNameProductMap(organizationId: UUID): Promise<Map<string, UUID[]>>;
  findUnsynced(organizationId?: UUID): Promise<ColorUUIDRow[]>;
  search(
    query: string,
    organizationId: UUID,
    options?: SearchOptions
  ): Promise<ColorSearchResult[]>;
  findByCode(code: string, organizationId: UUID): Promise<ColorUUIDRow | null>;
  findByHex(hex: string, organizationId: UUID): Promise<ColorUUIDRow[]>;
  findByName(name: string, organizationId: UUID): Promise<ColorUUIDRow[]>;
  findWithRelationships(
    colorId: UUID,
    organizationId: UUID
  ): Promise<ColorWithRelationshipsRow | null>;

  // Soft Delete Operations
  findSoftDeleted(
    organizationId: UUID,
    limit?: number,
    offset?: number
  ): Promise<ColorUUIDRow[]>;
  restoreRecord(colorId: UUID, organizationId: UUID): Promise<boolean>;
  bulkRestoreRecords(
    colorIds: UUID[],
    organizationId: UUID
  ): Promise<BulkColorResult>;
  cleanupOldSoftDeleted(
    organizationId: UUID,
    daysOld?: number
  ): Promise<BulkColorResult>;

  // Bulk Operations
  clearAll(organizationId: UUID, hardDelete?: boolean): Promise<boolean>;
  invalidateOrphans(organizationId: UUID): Promise<void>;
  bulkInsert(
    colors: CreateColorUUID[],
    organizationId: UUID
  ): Promise<BulkColorResult>;
  bulkUpdate(
    updates: Array<{ id: UUID; data: UpdateColorUUID }>,
    organizationId: UUID
  ): Promise<BulkColorResult>;
  bulkDelete(
    colorIds: UUID[],
    organizationId: UUID,
    hardDelete?: boolean
  ): Promise<BulkColorResult>;
  bulkValidate(
    colors: CreateColorUUID[],
    organizationId: UUID
  ): Promise<ColorValidationResult[]>;

  // Sync Operations
  markAsSynced(colorId: UUID): Promise<void>;
  markAsUnsynced(colorId: UUID): Promise<void>;
  bulkMarkAsSynced(colorIds: UUID[]): Promise<BulkColorResult>;
  upsertFromSync(
    colorData: ColorUUID,
    organizationId: UUID
  ): Promise<ColorUUIDRow | null>;
  findPendingSync(organizationId: UUID): Promise<ColorUUIDRow[]>;
  resolveConflicts(
    conflicts: Array<{ local: ColorUUIDRow; remote: ColorUUIDRow }>,
    organizationId: UUID
  ): Promise<ColorConflictResult[]>;

  // Source Operations
  getSourceIdByCode(sourceCode: string): Promise<UUID | null>;
  findBySourceId(sourceId: UUID, organizationId: UUID): Promise<ColorUUIDRow[]>;

  // Utility Operations
  exists(colorId: UUID, organizationId: UUID): Promise<boolean>;
  count(organizationId: UUID): Promise<number>;
  validateColor(
    colorData: CreateColorUUID | UpdateColorUUID
  ): Promise<ColorValidationResult>;
  getDuplicates(
    organizationId: UUID
  ): Promise<Array<{ colors: ColorUUIDRow[]; similarity: number }>>;

  // Performance Operations
  warmCache(organizationId: UUID): Promise<void>;
  clearCache(organizationId: UUID): Promise<void>;
  getStatistics(organizationId: UUID): Promise<ColorStatistics>;
}

/**
 * Legacy database row interface for colors (for backward compatibility)
 */
export interface ColorRow {
  id: string; // UUID primary key
  organization_id: string;
  source_id: number | null;
  code: string;
  display_name: string | null;
  hex: string;
  color_spaces: string | null;
  is_gradient: boolean;
  is_metallic: boolean;
  is_effect: boolean;
  is_library: boolean;
  gradient_colors: string | null;
  notes: string | null;
  tags: string | null;
  properties: string | null;
  is_synced: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  // Join fields
  source?: string;
  product_name?: string;
}

/**
 * UUID-based database row interface for colors
 */
export interface ColorUUIDRow {
  id: UUID;
  organization_id: UUID;
  source_id: UUID | null;
  name: string;
  display_name: string | null;
  code: string | null;
  hex: string;
  color_spaces: string | null; // JSON string
  is_gradient: boolean;
  is_metallic: boolean;
  is_effect: boolean;
  is_library: boolean;
  gradient_colors: string | null;
  notes: string | null;
  tags: string | null;
  properties: string | null; // JSON string
  is_synced: boolean;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  created_by: UUID | null;
  user_id: UUID | null;
  device_id: string | null;
  conflict_resolved_at: string | null;
  // Join fields
  source_name?: string;
  product_count?: number;
  organization_name?: string;
}

/**
 * Color with relationships result interface
 */
export interface ColorWithRelationshipsRow extends ColorUUIDRow {
  product_relationships?: Array<{
    product_id: UUID;
    product_name: string;
    display_order: number;
    added_at: string;
  }>;
  source_details?: {
    source_id: UUID;
    source_name: string;
    source_code: string;
  };
}

/**
 * Color search result interface
 */
export interface ColorSearchResult {
  color_id: UUID;
  color_name: string;
  display_name: string | null;
  code: string | null;
  hex: string;
  organization_id: UUID;
  is_library: boolean;
  product_count: number;
  relevance_score: number;
  matched_fields: string[];
}

/**
 * Usage count result interface
 */
export interface UsageCountResult {
  color_id: UUID;
  color_name: string;
  product_count: number;
  product_ids: string; // JSON array of UUIDs
  product_names: string; // JSON array of names
}

/**
 * Color name to products mapping result
 */
export interface ColorProductMapResult {
  color_id: UUID;
  color_name: string;
  display_name: string;
  product_ids: string; // JSON array of UUIDs
  product_names: string; // JSON array of names
}

/**
 * Bulk operation result interface
 */
export interface BulkColorResult {
  success: boolean;
  processed: number;
  succeeded: number;
  failed: number;
  errors: Array<{
    color_id?: UUID;
    error_message: string;
    error_code: string;
  }>;
  duration: number;
}

/**
 * Color validation result interface
 */
export interface ColorValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  warnings: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

/**
 * Color conflict resolution result
 */
export interface ColorConflictResult {
  resolved: boolean;
  conflict_id: UUID;
  resolution_strategy: 'merge' | 'replace' | 'keep_both';
  final_color: ColorUUIDRow;
  conflicted_color?: ColorUUIDRow;
  resolved_at: string;
  resolved_by: UUID;
}

/**
 * Search options interface
 */
export interface SearchOptions {
  limit?: number;
  offset?: number;
  includeDeleted?: boolean;
  includeLibraryColors?: boolean;
  sortBy?: 'name' | 'created_at' | 'updated_at' | 'relevance';
  sortOrder?: 'asc' | 'desc';
  filters?: {
    is_gradient?: boolean;
    is_metallic?: boolean;
    is_effect?: boolean;
    source_ids?: UUID[];
    hex_ranges?: Array<{ min: string; max: string }>;
  };
}

/**
 * Color statistics interface
 */
export interface ColorStatistics {
  total_colors: number;
  active_colors: number;
  deleted_colors: number;
  unsynced_colors: number;
  library_colors: number;
  gradient_colors: number;
  metallic_colors: number;
  effect_colors: number;
  colors_by_source: Record<string, number>;
  colors_by_month: Record<string, number>;
  top_used_colors: Array<{
    color_id: UUID;
    color_name: string;
    usage_count: number;
  }>;
  orphaned_colors: number;
  duplicate_colors: number;
  average_products_per_color: number;
  last_sync_at: string | null;
  performance_metrics: {
    avg_query_time: number;
    cache_hit_rate: number;
    index_efficiency: number;
  };
}
