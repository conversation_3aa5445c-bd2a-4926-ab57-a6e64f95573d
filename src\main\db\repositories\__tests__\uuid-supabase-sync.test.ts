/**
 * @file uuid-supabase-sync.test.ts
 * @description Integration tests for Supabase sync with pure UUID architecture
 *
 * Tests the critical sync functionality to ensure:
 * - UUID primary keys sync correctly between local and Supabase
 * - Foreign key relationships are maintained during sync
 * - Bidirectional sync works with pure UUID architecture
 * - Conflict resolution handles UUIDs properly
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { ColorRepository } from '../color.repository';
import { ProductRepository } from '../product.repository';
import { OrganizationRepository } from '../organization.repository';
import { ColorSyncService } from '../../services/color-sync.service';
import { ProductSyncService } from '../../services/product-sync.service';
import { NewColorEntry } from '../../../../shared/types/color.types';
import { NewProduct } from '../../../../shared/types/product.types';

// Mock Supabase client for controlled testing
const mockSupabaseClient = {
  from: vi.fn(),
  auth: {
    getSession: vi.fn(() =>
      Promise.resolve({
        data: {
          session: { user: { id: 'test-user-id', email: '<EMAIL>' } },
        },
        error: null,
      })
    ),
  },
};

const mockSupabaseQuery = {
  select: vi.fn().mockReturnThis(),
  insert: vi.fn().mockReturnThis(),
  update: vi.fn().mockReturnThis(),
  upsert: vi.fn().mockReturnThis(),
  delete: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  in: vi.fn().mockReturnThis(),
  gt: vi.fn().mockReturnThis(),
  order: vi.fn().mockReturnThis(),
  limit: vi.fn().mockReturnThis(),
  single: vi.fn(),
  then: vi.fn(),
};

// Mock the supabase-client module
vi.mock('../../../services/supabase-client', () => ({
  getSupabaseClient: () => mockSupabaseClient,
  ensureAuthenticatedSession: () =>
    Promise.resolve({
      session: { user: { id: 'test-user-id', email: '<EMAIL>' } },
      error: null,
    }),
}));

describe.sequential('UUID Supabase Sync Integration Tests', () => {
  let db: Database.Database;
  let colorRepo: ColorRepository;
  let productRepo: ProductRepository;
  let organizationRepo: OrganizationRepository;
  let colorSyncService: ColorSyncService;
  let productSyncService: ProductSyncService;

  let testOrgId: string;
  let testUserId: string;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup mock default behaviors
    mockSupabaseClient.from.mockReturnValue(mockSupabaseQuery);
    mockSupabaseQuery.then.mockResolvedValue({ data: [], error: null });
    mockSupabaseQuery.single.mockResolvedValue({ data: null, error: null });

    // Create in-memory SQLite database for testing
    db = new Database(':memory:');

    // Set up pure UUID schema (post-migration)
    setupUUIDSchema(db);

    // Create repository instances
    colorRepo = new ColorRepository(db);
    productRepo = new ProductRepository(db);
    organizationRepo = new OrganizationRepository(db);

    // Create sync service instances
    colorSyncService = new ColorSyncService(db);
    productSyncService = new ProductSyncService(db);

    // Generate test UUIDs
    testOrgId = uuidv4();
    testUserId = uuidv4();

    // Create test organization
    setupTestOrganization(db, testOrgId, testUserId);
  });

  afterEach(() => {
    if (db && db.open) {
      try {
        db.close();
      } catch (error) {
        console.warn('Database close error:', error);
      }
    }
  });

  describe('UUID Color Sync Operations', () => {
    test('should sync new color to Supabase with UUID primary key', async () => {
      const colorData: NewColorEntry = {
        name: 'Sync Test Color',
        hex: '#FF6B35',
        source: 'USER',
        code: 'STC001',
        isLibrary: false,
      };

      // Create color locally
      const colorId = colorRepo.insert(colorData, testOrgId);
      expect(colorId).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/
      );

      // Mock successful Supabase upsert
      mockSupabaseQuery.then.mockResolvedValueOnce({
        data: [
          {
            id: colorId,
            organization_id: testOrgId,
            name: colorData.name,
            hex: colorData.hex,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ],
        error: null,
      });

      // Sync to Supabase
      const syncResult = await colorSyncService.syncColorsToSupabase(
        testOrgId,
        testUserId
      );
      expect(syncResult.success).toBe(true);

      // Verify Supabase was called with correct UUID data
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('colors');
      expect(mockSupabaseQuery.upsert).toHaveBeenCalled();

      // Extract the upserted data from the mock call
      const upsertCall = mockSupabaseQuery.upsert.mock.calls[0];
      const upsertedData = upsertCall[0];

      expect(upsertedData).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: colorId, // UUID primary key
            organization_id: testOrgId, // UUID foreign key
            name: colorData.name,
            hex: colorData.hex,
          }),
        ])
      );
    });

    test('should sync colors from Supabase and maintain UUID relationships', async () => {
      const supabaseColorId = uuidv4();
      const supabaseColorData = {
        id: supabaseColorId,
        organization_id: testOrgId,
        name: 'Supabase Color',
        hex: '#4ECDC4',
        source: 'PANTONE',
        code: 'PANTONE-123',
        is_library: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: testUserId,
      };

      // Mock Supabase response with UUID data
      mockSupabaseQuery.then.mockResolvedValueOnce({
        data: [supabaseColorData],
        error: null,
      });

      // Sync from Supabase
      const syncResult = await colorSyncService.syncColorsFromSupabase(
        testOrgId,
        testUserId
      );
      expect(syncResult.success).toBe(true);

      // Verify color was inserted with correct UUID
      const localColor = colorRepo.findById(supabaseColorId, testOrgId);
      expect(localColor).toBeTruthy();
      expect(localColor!.id).toBe(supabaseColorId);
      expect(localColor!.external_id).toBe(supabaseColorId); // Should be same as id
      expect(localColor!.organization_id).toBe(testOrgId);
      expect(localColor!.display_name).toBe('Supabase Color');
      expect(localColor!.hex).toBe('#4ECDC4');
    });
  });

  describe('UUID Product Sync Operations', () => {
    test('should sync new product to Supabase with UUID primary key', async () => {
      const productData: NewProduct = {
        name: 'Sync Test Product',
        description: 'Product for UUID sync testing',
      };

      // Create product locally
      const productId = productRepo.insert(productData, testOrgId, testUserId);
      expect(productId).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/
      );

      // Mock successful Supabase upsert
      mockSupabaseQuery.then.mockResolvedValueOnce({
        data: [
          {
            id: productId,
            organization_id: testOrgId,
            name: productData.name,
            description: productData.description,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ],
        error: null,
      });

      // Sync to Supabase
      const syncResult = await productSyncService.syncProductsToSupabase(
        testOrgId,
        testUserId
      );
      expect(syncResult.success).toBe(true);

      // Verify Supabase was called with correct UUID data
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('products');
      expect(mockSupabaseQuery.upsert).toHaveBeenCalled();

      const upsertCall = mockSupabaseQuery.upsert.mock.calls[0];
      const upsertedData = upsertCall[0];

      expect(upsertedData).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: productId, // UUID primary key
            organization_id: testOrgId, // UUID foreign key
            name: productData.name,
            description: productData.description,
          }),
        ])
      );
    });

    test('should sync product-color relationships with UUID foreign keys', async () => {
      // Create product and color locally
      const productData: NewProduct = { name: 'Relationship Test Product' };
      const colorData: NewColorEntry = {
        name: 'Relationship Test Color',
        hex: '#A8E6CF',
        source: 'USER',
        isLibrary: false,
      };

      const productId = productRepo.insert(productData, testOrgId, testUserId);
      const colorId = colorRepo.insert(colorData, testOrgId);

      // Create relationship
      const relationshipCreated = productRepo.addProductColor(
        productId,
        colorId,
        testOrgId
      );
      expect(relationshipCreated).toBe(true);

      // Mock Supabase responses for sync
      mockSupabaseQuery.then
        .mockResolvedValueOnce({ data: [], error: null }) // Products sync
        .mockResolvedValueOnce({ data: [], error: null }); // Product-colors sync

      // Sync to Supabase
      const syncResult = await productSyncService.syncProductsToSupabase(
        testOrgId,
        testUserId
      );
      expect(syncResult.success).toBe(true);

      // Verify product-color relationship was synced with UUID foreign keys
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('product_colors');

      // Check that the relationship data uses UUIDs
      const productColorCall = mockSupabaseQuery.upsert.mock.calls.find(
        call =>
          Array.isArray(call[0]) &&
          call[0][0]?.product_id &&
          call[0][0]?.color_id
      );

      if (productColorCall) {
        const relationshipData = productColorCall[0][0];
        expect(relationshipData.product_id).toBe(productId);
        expect(relationshipData.color_id).toBe(colorId);
        expect(relationshipData.organization_id).toBe(testOrgId);
      }
    });
  });

  describe('UUID Conflict Resolution', () => {
    test('should handle UUID conflicts during bidirectional sync', async () => {
      const conflictingColorId = uuidv4();

      // Create local color
      const localColorData: NewColorEntry = {
        name: 'Local Version',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false,
      };

      // Force specific UUID for testing
      db.prepare(
        `
        INSERT INTO colors (id, organization_id, source_id, name, display_name, hex, is_library, created_at, updated_at)
        VALUES (?, ?, 1, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `
      ).run(
        conflictingColorId,
        testOrgId,
        localColorData.name,
        localColorData.name,
        localColorData.hex,
        localColorData.isLibrary
      );

      // Mock Supabase returning conflicting version with same UUID
      const supabaseColorData = {
        id: conflictingColorId, // Same UUID
        organization_id: testOrgId,
        name: 'Supabase Version', // Different name
        hex: '#33FF57', // Different hex
        source: 'PANTONE',
        is_library: false,
        created_at: new Date(Date.now() - 1000).toISOString(), // Older
        updated_at: new Date().toISOString(), // Newer
      };

      mockSupabaseQuery.then.mockResolvedValueOnce({
        data: [supabaseColorData],
        error: null,
      });

      // Sync from Supabase (should resolve conflict by update timestamp)
      const syncResult = await colorSyncService.syncColorsFromSupabase(
        testOrgId,
        testUserId
      );
      expect(syncResult.success).toBe(true);

      // Verify conflict was resolved (newer version wins)
      const resolvedColor = colorRepo.findById(conflictingColorId, testOrgId);
      expect(resolvedColor).toBeTruthy();
      expect(resolvedColor!.id).toBe(conflictingColorId);
      expect(resolvedColor!.display_name).toBe('Supabase Version'); // Should be updated
      expect(resolvedColor!.hex).toBe('#33FF57'); // Should be updated
    });

    test('should handle malformed UUID data gracefully', async () => {
      // Mock Supabase returning data with invalid UUID
      const invalidData = {
        id: 'not-a-valid-uuid',
        organization_id: testOrgId,
        name: 'Invalid Color',
        hex: '#000000',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockSupabaseQuery.then.mockResolvedValueOnce({
        data: [invalidData],
        error: null,
      });

      // Sync should handle invalid UUID gracefully
      const syncResult = await colorSyncService.syncColorsFromSupabase(
        testOrgId,
        testUserId
      );

      // Should not crash but should report issues
      expect(syncResult.success).toBe(false);
      expect(syncResult.errors).toContain(
        expect.stringContaining('Invalid UUID')
      );
    });
  });

  describe('UUID Performance with Sync', () => {
    test('should handle bulk sync operations efficiently with UUIDs', async () => {
      const bulkSize = 50;
      const colorIds: string[] = [];

      // Create bulk colors locally
      for (let i = 0; i < bulkSize; i++) {
        const colorData: NewColorEntry = {
          name: `Bulk Color ${i}`,
          hex: `#${Math.floor(Math.random() * 16777215)
            .toString(16)
            .padStart(6, '0')}`,
          source: 'USER',
          isLibrary: false,
        };
        const colorId = colorRepo.insert(colorData, testOrgId);
        colorIds.push(colorId);
      }

      // Mock successful bulk sync
      const mockSupabaseData = colorIds.map(id => ({
        id,
        organization_id: testOrgId,
        name: `Bulk Color`,
        hex: '#000000',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      mockSupabaseQuery.then.mockResolvedValueOnce({
        data: mockSupabaseData,
        error: null,
      });

      const startTime = Date.now();
      const syncResult = await colorSyncService.syncColorsToSupabase(
        testOrgId,
        testUserId
      );
      const syncTime = Date.now() - startTime;

      expect(syncResult.success).toBe(true);
      expect(syncTime).toBeLessThan(5000); // Should complete within 5 seconds

      // Verify all UUIDs are valid in the sync call
      const upsertCall = mockSupabaseQuery.upsert.mock.calls[0];
      const upsertedData = upsertCall[0];

      upsertedData.forEach((item: any) => {
        expect(item.id).toMatch(
          /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/
        );
        expect(item.organization_id).toBe(testOrgId);
      });
    });
  });
});

/**
 * Setup pure UUID schema for testing (post-migration schema)
 */
function setupUUIDSchema(db: Database.Database) {
  // Organizations table with UUID primary key
  db.exec(`
    CREATE TABLE organizations (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
      settings JSON DEFAULT '{}',
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      CHECK (length(id) = 36)
    );

    CREATE TABLE organization_members (
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      user_id TEXT NOT NULL,
      role TEXT NOT NULL DEFAULT 'member',
      joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
      invited_by TEXT,
      PRIMARY KEY (organization_id, user_id)
    );

    CREATE TABLE color_sources (
      id INTEGER PRIMARY KEY,
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL
    );

    INSERT INTO color_sources (id, code, name) VALUES 
    (1, 'USER', 'User Created'),
    (2, 'PANTONE', 'Pantone Color'),
    (3, 'RAL', 'RAL Color');

    CREATE TABLE products (
      id TEXT PRIMARY KEY,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      user_id TEXT,
      name TEXT NOT NULL,
      description TEXT,
      metadata JSON DEFAULT '{}',
      is_active BOOLEAN NOT NULL DEFAULT TRUE,
      is_synced BOOLEAN NOT NULL DEFAULT FALSE,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT,
      created_by TEXT,
      CHECK (length(id) = 36)
    );

    CREATE TABLE colors (
      id TEXT PRIMARY KEY,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      source_id INTEGER NOT NULL DEFAULT 1 REFERENCES color_sources(id),
      name TEXT NOT NULL,
      display_name TEXT,
      code TEXT,
      hex TEXT NOT NULL,
      color_spaces JSON DEFAULT '{}',
      is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
      is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
      is_effect BOOLEAN NOT NULL DEFAULT FALSE,
      is_library BOOLEAN NOT NULL DEFAULT FALSE,
      gradient_colors TEXT,
      notes TEXT,
      tags TEXT,
      properties JSON DEFAULT '{}',
      is_synced BOOLEAN NOT NULL DEFAULT FALSE,
      version INTEGER NOT NULL DEFAULT 1,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT,
      created_by TEXT,
      user_id TEXT,
      device_id TEXT,
      conflict_resolved_at TEXT,
      CHECK (length(id) = 36),
      CHECK (length(hex) = 7 AND substr(hex, 1, 1) = '#')
    );

    CREATE TABLE product_colors (
      product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
      color_id TEXT NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
      display_order INTEGER NOT NULL DEFAULT 0,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      added_at TEXT DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (product_id, color_id)
    );

    CREATE INDEX idx_products_org ON products(organization_id);
    CREATE INDEX idx_colors_org ON colors(organization_id);
    CREATE INDEX idx_product_colors_product ON product_colors(product_id);
    CREATE INDEX idx_product_colors_color ON product_colors(color_id);
  `);
}

/**
 * Setup test organization for UUID testing
 */
function setupTestOrganization(
  db: Database.Database,
  orgId: string,
  userId: string
) {
  db.prepare(
    `
    INSERT INTO organizations (id, name, slug, plan, settings)
    VALUES (?, ?, ?, ?, ?)
  `
  ).run(orgId, 'Test Organization', 'test-org-uuid', 'free', '{}');

  db.prepare(
    `
    INSERT INTO organization_members (organization_id, user_id, role)
    VALUES (?, ?, ?)
  `
  ).run(orgId, userId, 'owner');
}
