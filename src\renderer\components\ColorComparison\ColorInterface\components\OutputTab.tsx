/**
 * Output Tab Component
 * Provides various output formats for selected colors
 */

import { memo, useState, useCallback } from 'react';
import {
  Download,
  Copy,
  Check,
  FileText,
  FileSpreadsheet,
  Palette,
  Code,
} from 'lucide-react';
import type { OutputTabProps, SimpleColorEntry } from '../types';
import { useColorComparisonStore } from '../../../../store/colorComparison.store';
import { useToast } from '../../../../hooks/useToast';
import {
  hexToRgb,
  rgbToHsl,
} from '../../../../../shared/utils/color/conversion';
import {
  exportToPDF,
  exportToCSV,
  exportToASE,
  exportToJSON,
} from '../utils/exportHelpers';

const OUTPUT_FORMATS = [
  { id: 'hex', label: 'HEX', icon: Code },
  { id: 'rgb', label: 'RGB', icon: Code },
  { id: 'hsl', label: 'HSL', icon: Code },
  { id: 'cmyk', label: 'CMYK', icon: Code },
  { id: 'css', label: 'CSS', icon: Code },
  { id: 'scss', label: 'SCSS', icon: Code },
] as const;

const EXPORT_FORMATS = [
  {
    id: 'pdf',
    label: 'PDF Report',
    icon: FileText,
    description: 'Full color analysis report',
  },
  {
    id: 'csv',
    label: 'CSV Spreadsheet',
    icon: FileSpreadsheet,
    description: 'For Excel/Google Sheets',
  },
  {
    id: 'ase',
    label: 'Adobe ASE',
    icon: Palette,
    description: 'For Creative Suite',
  },
  { id: 'json', label: 'JSON Data', icon: Code, description: 'For developers' },
] as const;

export const OutputTab = memo<OutputTabProps>(
  ({ selectedColor, outputFormat, onFormatChange }) => {
    const [copiedFormat, setCopiedFormat] = useState<string | null>(null);
    const [isExporting, setIsExporting] = useState(false);
    const { comparisonColors } = useColorComparisonStore();
    const { toast } = useToast();

    const getFormattedOutput = useCallback(
      (color: SimpleColorEntry) => {
        const rgb = hexToRgb(color.hex);
        const hsl = rgb ? rgbToHsl(rgb) : null;

        switch (outputFormat) {
          case 'hex':
            return color.hex;

          case 'rgb':
            return rgb ? `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})` : 'N/A';

          case 'hsl':
            return hsl ? `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)` : 'N/A';

          case 'cmyk':
            return color.cmyk || 'N/A';

          case 'css': {
            const varName = (color.pantone || 'color')
              .toLowerCase()
              .replace(/[^a-z0-9]/g, '-');
            return [
              `--${varName}: ${color.hex};`,
              rgb ? `--${varName}-rgb: ${rgb.r}, ${rgb.g}, ${rgb.b};` : '',
              hsl ? `--${varName}-hsl: ${hsl.h}, ${hsl.s}%, ${hsl.l}%;` : '',
            ]
              .filter(Boolean)
              .join('\n');
          }

          case 'scss': {
            const scssName = (color.pantone || 'color')
              .toLowerCase()
              .replace(/[^a-z0-9]/g, '-');
            return [
              `$${scssName}: ${color.hex};`,
              rgb ? `$${scssName}-rgb: (${rgb.r}, ${rgb.g}, ${rgb.b});` : '',
              hsl ? `$${scssName}-hsl: (${hsl.h}, ${hsl.s}%, ${hsl.l}%);` : '',
            ]
              .filter(Boolean)
              .join('\n');
          }

          default:
            return color.hex;
        }
      },
      [outputFormat]
    );

    const getAllColorsOutput = useCallback(() => {
      if (comparisonColors.length === 0) {
        return '';
      }

      const outputs = comparisonColors.map(color => {
        const simpleColor: SimpleColorEntry = {
          id: color.id,
          hex: color.hex,
          pantone: color.code || 'Custom',
          cmyk: color.cmyk || 'N/A',
        };
        return getFormattedOutput(simpleColor);
      });

      return outputs.join('\n\n');
    }, [comparisonColors, getFormattedOutput]);

    const handleCopy = useCallback(
      (format: string) => {
        const output = selectedColor
          ? getFormattedOutput(selectedColor)
          : getAllColorsOutput();
        navigator.clipboard.writeText(output);
        setCopiedFormat(format);
        toast({
          title: 'Copied to clipboard',
          type: 'success',
        });
        setTimeout(() => setCopiedFormat(null), 2000);
      },
      [selectedColor, getFormattedOutput, getAllColorsOutput, toast]
    );

    const handleCopyAll = useCallback(() => {
      const output = getAllColorsOutput();
      navigator.clipboard.writeText(output);
      setCopiedFormat('all');
      toast({
        title: `Copied ${comparisonColors.length} colors`,
        type: 'success',
      });
      setTimeout(() => setCopiedFormat(null), 2000);
    }, [getAllColorsOutput, comparisonColors.length, toast]);

    const handleExport = useCallback(
      async (format: string) => {
        if (comparisonColors.length === 0) {
          toast({
            title: 'No colors to export',
            description: 'Add colors to comparison first',
            type: 'error',
          });
          return;
        }

        setIsExporting(true);

        try {
          const simpleColors: SimpleColorEntry[] = comparisonColors.map(
            color => ({
              id: color.id,
              hex: color.hex,
              pantone: color.code || 'Custom',
              cmyk: color.cmyk || 'N/A',
            })
          );

          switch (format) {
            case 'pdf':
              await exportToPDF(simpleColors, true);
              break;
            case 'csv':
              exportToCSV(simpleColors);
              break;
            case 'ase':
              exportToASE(simpleColors);
              break;
            case 'json':
              exportToJSON(simpleColors, true);
              break;
          }

          toast({
            title: 'Export successful',
            type: 'success',
          });
        } catch (error) {
          console.error('Export error:', error);
          toast({
            title: 'Export failed',
            description: 'An error occurred during export',
            type: 'error',
          });
        } finally {
          setIsExporting(false);
        }
      },
      [comparisonColors, toast]
    );

    if (!selectedColor && comparisonColors.length === 0) {
      return (
        <div className='p-4 text-center text-ui-text-muted'>
          <Download className='mx-auto mb-2 h-8 w-8 opacity-50' />
          <p>Select colors to export</p>
        </div>
      );
    }

    return (
      <div className='p-3 space-y-3'>
        {/* Format Selection */}
        <div
          className='p-3'
          style={{
            backgroundColor: 'var(--color-ui-background-tertiary)',
            borderRadius: 'var(--radius-lg)',
          }}
        >
          <h4
            className='text-sm font-medium mb-3'
            style={{
              fontSize: 'var(--font-size-sm)',
              fontWeight: 'var(--font-weight-medium)',
              color: 'var(--color-ui-foreground-primary)',
            }}
          >
            Code Format
          </h4>

          <div className='grid grid-cols-3 gap-2'>
            {OUTPUT_FORMATS.map(format => {
              const Icon = format.icon;
              return (
                <button
                  key={format.id}
                  onClick={() => onFormatChange(format.id)}
                  className={`px-3 py-2 text-xs font-medium rounded-md transition-all flex items-center justify-center gap-1.5 ${
                    outputFormat === format.id
                      ? 'bg-brand-primary text-white shadow-sm'
                      : 'bg-ui-background hover:bg-ui-background-secondary text-ui-text-secondary hover:text-ui-text-primary border border-ui-border'
                  }`}
                >
                  <Icon className='h-3 w-3' />
                  {format.label}
                </button>
              );
            })}
          </div>
        </div>

        {/* Output Preview */}
        <div
          className='p-3'
          style={{
            backgroundColor: 'var(--color-ui-background-tertiary)',
            borderRadius: 'var(--radius-lg)',
          }}
        >
          <div className='flex justify-between items-center mb-2'>
            <h4
              className='text-sm font-medium'
              style={{
                fontSize: 'var(--font-size-sm)',
                fontWeight: 'var(--font-weight-medium)',
                color: 'var(--color-ui-foreground-primary)',
              }}
            >
              {selectedColor
                ? 'Selected Color'
                : `All Colors (${comparisonColors.length})`}
            </h4>
            <div className='flex items-center gap-2'>
              {comparisonColors.length > 1 && (
                <button
                  onClick={handleCopyAll}
                  className='text-xs text-ui-text-secondary hover:text-brand-primary transition-colors flex items-center gap-1'
                  data-export-button
                >
                  {copiedFormat === 'all' ? (
                    <>
                      <Check className='h-3 w-3' />
                      Copied All
                    </>
                  ) : (
                    <>
                      <Copy className='h-3 w-3' />
                      Copy All
                    </>
                  )}
                </button>
              )}
              <button
                onClick={() => handleCopy(outputFormat)}
                className='text-xs text-brand-primary hover:text-brand-primary-dark transition-colors flex items-center gap-1'
              >
                {copiedFormat === outputFormat ? (
                  <>
                    <Check className='h-3 w-3' />
                    Copied
                  </>
                ) : (
                  <>
                    <Copy className='h-3 w-3' />
                    Copy
                  </>
                )}
              </button>
            </div>
          </div>

          <pre
            className='p-3 text-xs font-mono overflow-x-auto max-h-48 overflow-y-auto'
            style={{
              backgroundColor: 'var(--color-ui-background-primary)',
              border: `1px solid var(--color-ui-border-light)`,
              borderRadius: 'var(--radius-DEFAULT)',
              fontSize: 'var(--font-size-xs)',
              fontFamily: 'var(--font-family-mono)',
              color: 'var(--color-ui-foreground-primary)',
            }}
          >
            {selectedColor
              ? getFormattedOutput(selectedColor)
              : getAllColorsOutput()}
          </pre>

          {/* Color Preview */}
          {selectedColor && (
            <div className='mt-3 flex items-center gap-3'>
              <div
                className='w-12 h-12 rounded-md border border-ui-border shadow-sm'
                style={{ backgroundColor: selectedColor.hex }}
              />
              <div>
                <div className='text-sm font-medium'>
                  {selectedColor.pantone}
                </div>
                <div className='text-xs text-ui-text-muted font-mono'>
                  {selectedColor.hex}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Export Options */}
        <div
          className='p-3'
          style={{
            backgroundColor: 'var(--color-ui-background-tertiary)',
            borderRadius: 'var(--radius-lg)',
          }}
        >
          <h4
            className='text-sm font-medium mb-3'
            style={{
              fontSize: 'var(--font-size-sm)',
              fontWeight: 'var(--font-weight-medium)',
              color: 'var(--color-ui-foreground-primary)',
            }}
          >
            Export Options
          </h4>

          <div className='grid grid-cols-2 gap-2'>
            {EXPORT_FORMATS.map(format => {
              const Icon = format.icon;
              return (
                <button
                  key={format.id}
                  onClick={() => handleExport(format.id)}
                  disabled={isExporting || comparisonColors.length === 0}
                  className='p-3 rounded-lg bg-ui-background hover:bg-ui-background-secondary border border-ui-border transition-all disabled:opacity-50 disabled:cursor-not-allowed group'
                >
                  <div className='flex items-start gap-3'>
                    <Icon className='h-5 w-5 text-ui-text-secondary group-hover:text-brand-primary transition-colors mt-0.5' />
                    <div className='text-left'>
                      <div className='text-sm font-medium group-hover:text-brand-primary transition-colors'>
                        {format.label}
                      </div>
                      <div className='text-xs text-ui-text-muted'>
                        {format.description}
                      </div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          {comparisonColors.length === 0 && (
            <p className='text-xs text-ui-text-muted text-center mt-2'>
              Add colors to comparison to enable export
            </p>
          )}
        </div>
      </div>
    );
  }
);

OutputTab.displayName = 'OutputTab';
