/**
 * @file color-service-e2e.test.ts
 * @description Comprehensive end-to-end tests for the refactored ColorService orchestrator
 *
 * Tests validate the complete ColorService refactor with the orchestrator pattern,
 * ensuring all specialized services work together correctly for real-world scenarios.
 *
 * Test Categories:
 * 1. Complete color creation workflow (validation → calculation → storage → sync)
 * 2. Color update workflow with CMYK recalculation
 * 3. Color retrieval with analytics integration
 * 4. Gradient color processing end-to-end
 * 5. Error handling across service boundaries
 * 6. Bulk operations performance
 * 7. Service integration stability
 */

import {
  describe,
  test,
  expect,
  beforeAll,
  afterAll,
  beforeEach,
  vi,
  MockedFunction,
} from 'vitest';
import Database from 'better-sqlite3';
import { ColorService } from '../color.service';
import { ColorRepository } from '../../repositories/color.repository';
import { ColorSpaceCalculator } from '../../../services/color/color-space-calculator.service';
import { GradientProcessor } from '../../../services/color/gradient-processor.service';
import { ColorValidator } from '../../../services/color/color-validator.service';
import { ColorSyncService } from '../../../services/color/color-sync.service';
import { ColorAnalyticsService } from '../../../services/color/color-analytics.service';
import { ColorMappingService } from '../../../services/color/color-mapping.service';
import {
  NewColorEntry,
  UpdateColorEntry,
  ColorEntry,
} from '../../../../shared/types/color.types';

// Mock the sync outbox service to avoid external dependencies
vi.mock('../../../services/sync/sync-outbox.service', () => ({
  syncOutboxService: {
    addToOutbox: vi.fn().mockResolvedValue(undefined),
  },
}));

describe('ColorService E2E Tests - Orchestrator Pattern', () => {
  let db: Database.Database;
  let colorService: ColorService;
  let mockColorRepository: ColorRepository;
  let mockColorSpaceCalculator: ColorSpaceCalculator;
  let mockGradientProcessor: GradientProcessor;
  let mockColorValidator: ColorValidator;
  let mockColorSyncService: ColorSyncService;
  let mockColorAnalyticsService: ColorAnalyticsService;
  let mockColorMappingService: ColorMappingService;

  const testOrganizationId = '550e8400-e29b-41d4-a716-446655440000';
  const testUserId = 'user-123';

  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');

    // Set up complete schema with all required tables
    db.exec(`
      PRAGMA foreign_keys = ON;
      
      CREATE TABLE color_sources (
        id INTEGER PRIMARY KEY,
        code TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        is_system BOOLEAN NOT NULL DEFAULT FALSE,
        properties JSON
      );

      INSERT INTO color_sources (id, code, name, is_system) VALUES
      (1, 'user', 'User Created', FALSE),
      (2, 'pantone', 'PANTONE®', TRUE),
      (3, 'ral', 'RAL', TRUE);

      CREATE TABLE colors (
        id TEXT PRIMARY KEY,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        source_id INTEGER,
        code TEXT NOT NULL,
        display_name TEXT,
        hex TEXT NOT NULL,
        color_spaces TEXT,
        is_gradient INTEGER DEFAULT 0,
        is_metallic INTEGER DEFAULT 0,
        is_effect INTEGER DEFAULT 0,
        is_library INTEGER DEFAULT 0,
        gradient_colors TEXT,
        notes TEXT,
        tags TEXT,
        properties TEXT,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL,
        FOREIGN KEY (source_id) REFERENCES color_sources(id)
      );

      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL
      );

      CREATE TABLE product_colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id TEXT NOT NULL,
        color_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id),
        FOREIGN KEY (color_id) REFERENCES colors(id)
      );

      CREATE TABLE organizations (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      INSERT INTO organizations (id, name) VALUES ('${testOrganizationId}', 'Test Organization');
    `);
  });

  beforeEach(() => {
    // Create real instances for integration testing
    mockColorRepository = new ColorRepository(db);
    mockColorSpaceCalculator = new ColorSpaceCalculator();
    mockGradientProcessor = new GradientProcessor();
    mockColorValidator = new ColorValidator();
    mockColorSyncService = new ColorSyncService(
      db,
      mockColorRepository,
      mockColorValidator,
      mockGradientProcessor
    );
    mockColorAnalyticsService = new ColorAnalyticsService(
      db,
      mockColorRepository
    );
    mockColorMappingService = new ColorMappingService(
      mockColorSpaceCalculator,
      mockGradientProcessor
    );

    // Create ColorService with all dependencies
    colorService = new ColorService(
      db,
      mockColorRepository,
      mockColorSpaceCalculator,
      mockGradientProcessor,
      mockColorValidator,
      mockColorSyncService,
      mockColorAnalyticsService,
      mockColorMappingService
    );

    // Clear colors table before each test
    db.exec(
      `DELETE FROM colors WHERE organization_id = '${testOrganizationId}'`
    );
    db.exec(
      `DELETE FROM product_colors WHERE organization_id = '${testOrganizationId}'`
    );
    db.exec(
      `DELETE FROM products WHERE organization_id = '${testOrganizationId}'`
    );
  });

  afterAll(() => {
    db.close();
  });

  describe('1. Complete Color Creation Workflow', () => {
    test('should orchestrate complete color creation from validation to storage', async () => {
      const newColor: NewColorEntry = {
        name: 'Test Red',
        code: 'TEST-001',
        hex: '#ff0000',
        cmyk: 'C:0 M:100 Y:100 K:0',
        source: 'user',
        notes: 'Test color for validation',
        product: 'Test Product',
      };

      // Execute complete workflow
      const colorId = await colorService.add(
        newColor,
        testUserId,
        testOrganizationId
      );

      // Validate workflow results
      expect(colorId).toBeTruthy();
      expect(typeof colorId).toBe('string');

      // Verify color was stored correctly
      const storedColor = colorService.getById(colorId, testOrganizationId);
      expect(storedColor).toBeTruthy();
      expect(storedColor!.name).toBe('Test Red');
      expect(storedColor!.code).toBe('TEST-001');
      expect(storedColor!.hex).toBe('#FF0000'); // Should be standardized to uppercase
      expect(storedColor!.cmyk).toBe('C:0 M:100 Y:100 K:0'); // CMYK should be parsed and stored

      // Verify validation was applied
      expect(storedColor!.hex.startsWith('#')).toBe(true);
      expect(storedColor!.hex.length).toBe(7);

      // Verify color spaces were calculated and stored
      expect(storedColor!.rgb).toBeDefined();
      expect(storedColor!.hsl).toBeDefined();
    });

    test('should handle gradient colors with complete processing', async () => {
      const gradientColor: NewColorEntry = {
        name: 'Sunset Gradient',
        code: 'GRAD-001',
        hex: '#ff6b35',
        gradient: {
          gradientStops: [
            { color: '#ff6b35', position: 0, colorCode: 'SUNSET-1' },
            { color: '#f7931e', position: 0.5, colorCode: 'SUNSET-2' },
            { color: '#ffcd3c', position: 1, colorCode: 'SUNSET-3' },
          ],
          angle: 45,
        },
        source: 'user',
      };

      const colorId = await colorService.add(
        gradientColor,
        testUserId,
        testOrganizationId
      );
      const storedColor = colorService.getById(colorId, testOrganizationId);

      expect(storedColor).toBeTruthy();
      expect(storedColor!.gradient).toBeDefined();
      expect(storedColor!.gradient!.gradientStops).toHaveLength(3);
      expect(storedColor!.gradient!.angle).toBe(45);

      // Verify gradient processing was applied
      expect(storedColor!.gradient!.gradientStops[0].color).toBe('#ff6b35');
      expect(storedColor!.gradient!.gradientStops[0].position).toBe(0);
    });

    test('should validate input and throw errors for invalid data', async () => {
      const invalidColor: NewColorEntry = {
        name: 'Invalid Color',
        code: 'INV-001',
        hex: 'invalid-hex', // Invalid hex
        cmyk: 'invalid-cmyk', // Invalid CMYK
        source: 'user',
      };

      await expect(
        colorService.add(invalidColor, testUserId, testOrganizationId)
      ).rejects.toThrow('Invalid HEX color');
    });
  });

  describe('2. Color Update Workflow with CMYK Recalculation', () => {
    test('should update color and recalculate CMYK when hex changes', async () => {
      // First create a color
      const newColor: NewColorEntry = {
        name: 'Original Blue',
        code: 'BLUE-001',
        hex: '#0000ff',
        cmyk: 'C:100 M:100 Y:0 K:0',
        source: 'user',
      };

      const colorId = await colorService.add(
        newColor,
        testUserId,
        testOrganizationId
      );

      // Update the hex color
      const updates: UpdateColorEntry = {
        hex: '#00ff00', // Change to green
        name: 'Updated Green',
      };

      const success = await colorService.update(
        colorId,
        updates,
        testOrganizationId
      );
      expect(success).toBe(true);

      // Verify the update and CMYK recalculation
      const updatedColor = colorService.getById(colorId, testOrganizationId);
      expect(updatedColor).toBeTruthy();
      expect(updatedColor!.hex).toBe('#00FF00');
      expect(updatedColor!.name).toBe('Updated Green');

      // CMYK should be recalculated for the new hex value
      expect(updatedColor!.cmyk).toBeDefined();
      expect(updatedColor!.cmyk).not.toBe('C:100 M:100 Y:0 K:0'); // Should be different from original
    });

    test('should handle gradient updates correctly', async () => {
      // Create gradient color
      const gradientColor: NewColorEntry = {
        name: 'Original Gradient',
        code: 'GRAD-UPD',
        hex: '#ff0000',
        gradient: {
          gradientStops: [
            { color: '#ff0000', position: 0 },
            { color: '#0000ff', position: 1 },
          ],
          angle: 90,
        },
        source: 'user',
      };

      const colorId = await colorService.add(
        gradientColor,
        testUserId,
        testOrganizationId
      );

      // Update gradient
      const updates: UpdateColorEntry = {
        gradient: {
          gradientStops: [
            { color: '#00ff00', position: 0 },
            { color: '#ff00ff', position: 0.5 },
            { color: '#ffff00', position: 1 },
          ],
          angle: 45,
        },
      };

      const success = await colorService.update(
        colorId,
        updates,
        testOrganizationId
      );
      expect(success).toBe(true);

      const updatedColor = colorService.getById(colorId, testOrganizationId);
      expect(updatedColor!.gradient!.gradientStops).toHaveLength(3);
      expect(updatedColor!.gradient!.angle).toBe(45);
    });
  });

  describe('3. Color Retrieval with Analytics Integration', () => {
    test('should retrieve colors with usage analytics', async () => {
      // Create test product
      const productId = 'test-product-123';
      db.exec(`
        INSERT INTO products (id, external_id, name, organization_id)
        VALUES ('${productId}', '${productId}', 'Test Product', '${testOrganizationId}')
      `);

      // Create test colors
      const color1: NewColorEntry = {
        name: 'Red Paint',
        code: 'RED-001',
        hex: '#ff0000',
        source: 'user',
        product: 'Test Product',
      };

      const color2: NewColorEntry = {
        name: 'Blue Paint',
        code: 'BLUE-001',
        hex: '#0000ff',
        source: 'user',
        product: 'Test Product',
      };

      const colorId1 = await colorService.add(
        color1,
        testUserId,
        testOrganizationId
      );
      const colorId2 = await colorService.add(
        color2,
        testUserId,
        testOrganizationId
      );

      // Create product-color relationships
      db.exec(`
        INSERT INTO product_colors (product_id, color_id, organization_id)
        VALUES ('${productId}', '${colorId1}', '${testOrganizationId}'),
               ('${productId}', '${colorId2}', '${testOrganizationId}')
      `);

      // Test getAllWithUsage - should integrate analytics
      const response = await colorService.getAllWithUsage(testOrganizationId);

      expect(response.colors).toHaveLength(2);
      expect(response.totalColors).toBe(2);
      expect(response.usageCounts).toBeDefined();
      expect(response.organizationId).toBe(testOrganizationId);

      // Verify usage counts are calculated
      const usageKeys = Object.keys(response.usageCounts);
      expect(usageKeys.length).toBeGreaterThan(0);
    });

    test('should build color name to product mapping correctly', async () => {
      // Create test data
      const productId1 = 'product-1';
      const productId2 = 'product-2';

      db.exec(`
        INSERT INTO products (id, external_id, name, organization_id)
        VALUES ('${productId1}', '${productId1}', 'Product A', '${testOrganizationId}'),
               ('${productId2}', '${productId2}', 'Product B', '${testOrganizationId}')
      `);

      const color1: NewColorEntry = {
        name: 'Shared Red',
        code: 'SHARED-RED',
        hex: '#ff0000',
        source: 'user',
      };

      const colorId = await colorService.add(
        color1,
        testUserId,
        testOrganizationId
      );

      // Add color to multiple products
      db.exec(`
        INSERT INTO product_colors (product_id, color_id, organization_id)
        VALUES ('${productId1}', '${colorId}', '${testOrganizationId}'),
               ('${productId2}', '${colorId}', '${testOrganizationId}')
      `);

      const colorProductMap =
        colorService.buildColorNameProductMap(testOrganizationId);

      expect(colorProductMap).toBeInstanceOf(Map);
      expect(colorProductMap.size).toBeGreaterThan(0);

      // Should have mapping for the shared color
      const products = colorProductMap.get('Shared Red');
      expect(products).toBeDefined();
      expect(products).toContain('Product A');
      expect(products).toContain('Product B');
    });
  });

  describe('4. Gradient Color Processing End-to-End', () => {
    test('should process complex gradient with multiple stops', async () => {
      const complexGradient: NewColorEntry = {
        name: 'Complex Rainbow',
        code: 'RAINBOW-001',
        hex: '#ff0000',
        gradient: {
          gradientStops: [
            { color: '#ff0000', position: 0, colorCode: 'RED' },
            { color: '#ff7f00', position: 0.16, colorCode: 'ORANGE' },
            { color: '#ffff00', position: 0.33, colorCode: 'YELLOW' },
            { color: '#00ff00', position: 0.5, colorCode: 'GREEN' },
            { color: '#0000ff', position: 0.66, colorCode: 'BLUE' },
            { color: '#4b0082', position: 0.83, colorCode: 'INDIGO' },
            { color: '#9400d3', position: 1, colorCode: 'VIOLET' },
          ],
          angle: 90,
        },
        source: 'user',
      };

      const colorId = await colorService.add(
        complexGradient,
        testUserId,
        testOrganizationId
      );
      const storedColor = colorService.getById(colorId, testOrganizationId);

      expect(storedColor).toBeTruthy();
      expect(storedColor!.gradient).toBeDefined();
      expect(storedColor!.gradient!.gradientStops).toHaveLength(7);

      // Verify all gradient stops were processed correctly
      const stops = storedColor!.gradient!.gradientStops;
      expect(stops[0].color).toBe('#ff0000');
      expect(stops[0].position).toBe(0);
      expect(stops[6].color).toBe('#9400d3');
      expect(stops[6].position).toBe(1);
    });

    test('should validate gradient stop positions', async () => {
      const invalidGradient: NewColorEntry = {
        name: 'Invalid Gradient',
        code: 'INVALID-GRAD',
        hex: '#ff0000',
        gradient: {
          gradientStops: [
            { color: '#ff0000', position: 0 },
            { color: '#0000ff', position: 1.5 }, // Invalid position > 1
          ],
          angle: 45,
        },
        source: 'user',
      };

      // This should either throw an error or handle invalid positions gracefully
      // The exact behavior depends on the GradientProcessor implementation
      const colorId = await colorService.add(
        invalidGradient,
        testUserId,
        testOrganizationId
      );
      const storedColor = colorService.getById(colorId, testOrganizationId);

      // Verify that the gradient was processed (even if positions were corrected)
      expect(storedColor).toBeTruthy();
      expect(storedColor!.gradient).toBeDefined();
    });
  });

  describe('5. Error Handling Across Service Boundaries', () => {
    test('should handle database errors gracefully', async () => {
      // Close database to simulate connection error
      const originalDb = colorService['db'];

      // Create a new ColorService with a closed database
      const closedDb = new Database(':memory:');
      closedDb.close();

      const brokenColorService = new ColorService(closedDb);

      const newColor: NewColorEntry = {
        name: 'Error Test',
        code: 'ERROR-001',
        hex: '#ff0000',
        source: 'user',
      };

      // Should handle database errors gracefully
      await expect(
        brokenColorService.add(newColor, testUserId, testOrganizationId)
      ).rejects.toThrow();
    });

    test('should handle validation errors in color update', async () => {
      // Create a valid color first
      const validColor: NewColorEntry = {
        name: 'Valid Color',
        code: 'VALID-001',
        hex: '#ff0000',
        source: 'user',
      };

      const colorId = await colorService.add(
        validColor,
        testUserId,
        testOrganizationId
      );

      // Try to update with invalid data
      const invalidUpdate: UpdateColorEntry = {
        hex: 'not-a-hex-color',
      };

      const success = await colorService.update(
        colorId,
        invalidUpdate,
        testOrganizationId
      );
      expect(success).toBe(false); // Should return false for validation errors
    });

    test('should handle missing organization ID gracefully', async () => {
      const colors = colorService.getAll(''); // Empty organization ID
      expect(colors).toEqual([]);
    });
  });

  describe('6. Bulk Operations Performance', () => {
    test('should handle bulk color operations efficiently', async () => {
      const startTime = Date.now();
      const colorIds: string[] = [];

      // Create 50 colors to test bulk performance
      for (let i = 0; i < 50; i++) {
        const color: NewColorEntry = {
          name: `Bulk Color ${i}`,
          code: `BULK-${i.toString().padStart(3, '0')}`,
          hex: `#${((Math.random() * 16777215) | 0).toString(16).padStart(6, '0')}`,
          source: 'user',
        };

        const colorId = await colorService.add(
          color,
          testUserId,
          testOrganizationId
        );
        colorIds.push(colorId);
      }

      const creationTime = Date.now() - startTime;

      // Retrieve all colors
      const retrievalStart = Date.now();
      const allColors = colorService.getAll(testOrganizationId);
      const retrievalTime = Date.now() - retrievalStart;

      expect(allColors).toHaveLength(50);
      expect(colorIds).toHaveLength(50);

      // Performance expectations (adjust based on requirements)
      expect(creationTime).toBeLessThan(5000); // Should create 50 colors in under 5 seconds
      expect(retrievalTime).toBeLessThan(1000); // Should retrieve all colors in under 1 second

      console.log(
        `Bulk operations: Created 50 colors in ${creationTime}ms, retrieved in ${retrievalTime}ms`
      );
    });

    test('should handle bulk restore operations', async () => {
      // Create and delete colors
      const colorIds: string[] = [];

      for (let i = 0; i < 10; i++) {
        const color: NewColorEntry = {
          name: `Restore Test ${i}`,
          code: `RESTORE-${i}`,
          hex: `#${((Math.random() * 16777215) | 0).toString(16).padStart(6, '0')}`,
          source: 'user',
        };

        const colorId = await colorService.add(
          color,
          testUserId,
          testOrganizationId
        );
        colorIds.push(colorId);

        // Delete the color
        await colorService.delete(colorId, testOrganizationId);
      }

      // Bulk restore
      const restoreResult = colorService.bulkRestore(
        colorIds,
        testOrganizationId
      );

      expect(restoreResult.success).toBe(true);
      expect(restoreResult.restored).toBe(10);
    });
  });

  describe('7. Service Integration Stability', () => {
    test('should maintain consistency across all service operations', async () => {
      // Create a color with all possible features
      const complexColor: NewColorEntry = {
        name: 'Integration Test Color',
        code: 'INTEGRATION-001',
        hex: '#3366cc',
        cmyk: 'C:75 M:50 Y:0 K:20',
        gradient: {
          gradientStops: [
            { color: '#3366cc', position: 0 },
            { color: '#66ccff', position: 1 },
          ],
          angle: 135,
        },
        notes: 'Complex test color for integration testing',
        tags: 'test,integration,complex',
        source: 'user',
        isLibrary: false,
      };

      // Test complete workflow
      const colorId = await colorService.add(
        complexColor,
        testUserId,
        testOrganizationId
      );

      // Verify all services worked together
      const retrievedColor = colorService.getById(colorId, testOrganizationId);
      expect(retrievedColor).toBeTruthy();

      // Test update
      const updateSuccess = await colorService.update(
        colorId,
        {
          notes: 'Updated integration test notes',
        },
        testOrganizationId
      );
      expect(updateSuccess).toBe(true);

      // Test analytics integration
      const usageCounts = colorService.getColorUsageCounts(testOrganizationId);
      expect(usageCounts).toBeInstanceOf(Map);

      // Test soft delete
      const deleteSuccess = await colorService.delete(
        colorId,
        testOrganizationId
      );
      expect(deleteSuccess).toBe(true);

      // Test restore
      const restoreSuccess = colorService.restore(colorId, testOrganizationId);
      expect(restoreSuccess).toBe(true);

      // Verify color is accessible again
      const restoredColor = colorService.getById(colorId, testOrganizationId);
      expect(restoredColor).toBeTruthy();
      expect(restoredColor!.notes).toBe('Updated integration test notes');
    });

    test('should handle concurrent operations safely', async () => {
      // Test concurrent color creation
      const promises = [];

      for (let i = 0; i < 10; i++) {
        const color: NewColorEntry = {
          name: `Concurrent Color ${i}`,
          code: `CONCURRENT-${i}`,
          hex: `#${((Math.random() * 16777215) | 0).toString(16).padStart(6, '0')}`,
          source: 'user',
        };

        promises.push(colorService.add(color, testUserId, testOrganizationId));
      }

      const results = await Promise.all(promises);

      // All operations should succeed
      expect(results).toHaveLength(10);
      results.forEach(colorId => {
        expect(typeof colorId).toBe('string');
        expect(colorId).toBeTruthy();
      });

      // Verify all colors were created
      const allColors = colorService.getAll(testOrganizationId);
      expect(allColors.length).toBeGreaterThanOrEqual(10);
    });

    test('should maintain data integrity across service boundaries', async () => {
      // Create color with specific data
      const testColor: NewColorEntry = {
        name: 'Data Integrity Test',
        code: 'INTEGRITY-001',
        hex: '#ff5733',
        cmyk: 'C:0 M:65 Y:80 K:0',
        source: 'pantone',
        notes: 'Testing data integrity across services',
      };

      const colorId = await colorService.add(
        testColor,
        testUserId,
        testOrganizationId
      );

      // Retrieve and verify all data is consistent
      const retrieved = colorService.getById(colorId, testOrganizationId);
      expect(retrieved).toBeTruthy();

      // Verify validator processed the data correctly
      expect(retrieved!.hex).toBe('#FF5733'); // Should be uppercase

      // Verify calculator processed color spaces
      expect(retrieved!.rgb).toBeDefined();
      expect(retrieved!.hsl).toBeDefined();

      // Verify repository stored all data
      expect(retrieved!.name).toBe('Data Integrity Test');
      expect(retrieved!.code).toBe('INTEGRITY-001');
      expect(retrieved!.notes).toBe('Testing data integrity across services');

      // Verify source was processed correctly
      expect(retrieved!.source).toBe('pantone');
    });
  });

  describe('8. Real-World Usage Scenarios', () => {
    test('should handle typical user workflow: create, edit, use in product', async () => {
      // Step 1: User creates a new color
      const userColor: NewColorEntry = {
        name: 'Corporate Blue',
        code: 'CORP-BLUE-001',
        hex: '#1e3a8a',
        source: 'user',
        notes: 'Primary brand color',
      };

      const colorId = await colorService.add(
        userColor,
        testUserId,
        testOrganizationId
      );

      // Step 2: User edits the color after feedback
      const editSuccess = await colorService.update(
        colorId,
        {
          hex: '#2563eb', // Slightly brighter blue
          notes: 'Updated based on brand guidelines',
        },
        testOrganizationId
      );

      expect(editSuccess).toBe(true);

      // Step 3: Color is used in a product
      const productId = 'brand-product-123';
      db.exec(`
        INSERT INTO products (id, external_id, name, organization_id)
        VALUES ('${productId}', '${productId}', 'Brand Product', '${testOrganizationId}')
      `);

      db.exec(`
        INSERT INTO product_colors (product_id, color_id, organization_id)
        VALUES ('${productId}', '${colorId}', '${testOrganizationId}')
      `);

      // Step 4: User retrieves colors with usage information
      const colorsWithUsage =
        await colorService.getAllWithUsage(testOrganizationId);

      expect(colorsWithUsage.colors).toHaveLength(1);
      expect(colorsWithUsage.colorsWithUsage).toBeGreaterThan(0);

      const color = colorsWithUsage.colors[0];
      expect(color.name).toBe('Corporate Blue');
      expect(color.hex).toBe('#2563EB'); // Should reflect the update
      expect(color.notes).toBe('Updated based on brand guidelines');
    });

    test('should handle color library management workflow', async () => {
      // Create library colors
      const libraryColors: NewColorEntry[] = [
        {
          name: 'Pantone Red 185',
          code: 'PMS-185',
          hex: '#e21836',
          source: 'pantone',
          isLibrary: true,
        },
        {
          name: 'Pantone Blue 286',
          code: 'PMS-286',
          hex: '#003087',
          source: 'pantone',
          isLibrary: true,
        },
      ];

      const libraryColorIds = [];
      for (const color of libraryColors) {
        const colorId = await colorService.add(
          color,
          testUserId,
          testOrganizationId
        );
        libraryColorIds.push(colorId);
      }

      // Retrieve all colors and verify library colors are included
      const allColors = colorService.getAll(testOrganizationId);
      const libraryColorCount = allColors.filter(c => c.isLibrary).length;

      expect(libraryColorCount).toBe(2);
      expect(allColors.length).toBe(2);

      // Verify library colors have correct properties
      const pantoneRed = allColors.find(c => c.code === 'PMS-185');
      expect(pantoneRed).toBeTruthy();
      expect(pantoneRed!.isLibrary).toBe(true);
      expect(pantoneRed!.source).toBe('pantone');
    });
  });
});
