/**
 * @file color.repository.ts
 * @description Repository for color data access operations
 *
 * Handles all database operations for colors following the Repository pattern.
 * Separates data access concerns from business logic in ColorService.
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { ColorRow } from './interfaces/color.repository.interface';

// Local interfaces for SQL result mapping
interface LocalUsageCountResult {
  color_name: string;
  product_count: number;
  product_names: string;
}

interface LocalColorProductMapResult {
  display_name: string;
  product_names: string;
}

// Minimal interface for actually used methods
export interface IColorRepositoryCore {
  // Core CRUD
  findAll(organizationId: string): ColorRow[];
  findById(colorId: string, organizationId: string): ColorRow | null;
  insert(colorData: NewColorEntry, organizationId: string): string;
  update(
    id: string,
    updates: UpdateColorEntry,
    organizationId: string
  ): boolean;
  softDelete(colorId: string, organizationId: string): boolean;

  // Analytics
  getUsageCounts(
    organizationId: string
  ): Map<string, { count: number; products: string[] }>;
  getColorNameProductMap(organizationId: string): Map<string, string[]>;

  // Source operations
  getSourceIdByCode(sourceCode: string): string | null;

  // Utility
  getPreparedStatement(sql: string): Database.Statement;
}
import {
  NewColorEntry,
  UpdateColorEntry,
} from '../../../shared/types/color.types';
import { requireValidOrganizationId } from '../../utils/organization-validation';
// TODO: Re-enable when soft delete is implemented
// import { createSoftDeleteManager } from '../../utils/soft-delete-patterns';

export class ColorRepository implements IColorRepositoryCore {
  private static preparedStatements = new Map<string, Database.Statement>();
  // TODO: Implement soft delete functionality
  // private softDeleteManager: ReturnType<typeof createSoftDeleteManager>;

  constructor(private db: Database.Database) {
    // TODO: Initialize soft delete manager when implemented
    // this.softDeleteManager = createSoftDeleteManager(db);
  }

  // Core CRUD Operations
  findAll(organizationId: string): ColorRow[] {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      SELECT 
        c.id,
        c.organization_id,
        c.source_id,
        cs.code as source,
        c.code,
        c.display_name,
        c.hex,
        c.color_spaces,
        c.is_gradient,
        c.is_metallic,
        c.is_effect,
        c.is_library,
        c.gradient_colors,
        c.notes,
        c.tags,
        c.properties,
        c.is_synced,
        c.created_at,
        c.updated_at,
        c.deleted_at,
        p.name as product_name
      FROM colors c
      LEFT JOIN color_sources cs ON c.source_id = cs.id
      LEFT JOIN product_colors pc ON c.id = pc.color_id AND pc.organization_id = c.organization_id
      LEFT JOIN products p ON pc.product_id = p.id AND p.deleted_at IS NULL
      WHERE c.deleted_at IS NULL AND c.organization_id = ?
      ORDER BY c.code ASC, p.name ASC
    `);

    return stmt.all(organizationId) as ColorRow[];
  }

  findById(colorId: string, organizationId: string): ColorRow | null {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      SELECT 
        c.*,
        cs.code as source
      FROM colors c
      LEFT JOIN color_sources cs ON c.source_id = cs.id
      WHERE c.id = ? AND c.organization_id = ? AND c.deleted_at IS NULL
    `);

    const result = stmt.get(colorId, organizationId) as ColorRow | undefined;
    return result || null;
  }

  insert(colorData: NewColorEntry, organizationId: string): string {
    requireValidOrganizationId(organizationId);

    try {
      // Use provided ID (for sync) or generate new UUID for primary key
      const colorId = (colorData as any).id || uuidv4();
      const now = new Date().toISOString();

      // Map NewColorEntry fields to database fields
      const displayName = (colorData as any).display_name || colorData.name;

      // Verify organization exists (organization_id is UUID)
      const orgStmt = this.getPreparedStatement(
        `SELECT id FROM organizations WHERE id = ?`
      );
      const orgRecord = orgStmt.get(organizationId) as
        | { id: string }
        | undefined;
      if (!orgRecord) {
        throw new Error(`Organization not found: ${organizationId}`);
      }

      // Get source_id from source code
      let sourceId = (colorData as any).source_id || null;
      if (!sourceId && colorData.source) {
        const sourceStmt = this.getPreparedStatement(
          `SELECT id FROM color_sources WHERE code = ?`
        );
        const source = sourceStmt.get(colorData.source) as
          | { id: number }
          | undefined;
        sourceId = source ? source.id : 1; // Default to first source
      }

      // Ensure sourceId is a number, not null
      if (!sourceId) {
        sourceId = 1;
      }

      // Create color_spaces JSON from CMYK
      let colorSpaces = (colorData as any).color_spaces;
      if (!colorSpaces && colorData.cmyk) {
        colorSpaces = JSON.stringify({ cmyk: colorData.cmyk });
      }

      // Ensure colorSpaces is a string, not null
      if (!colorSpaces) {
        colorSpaces = '{}';
      }

      const stmt = this.getPreparedStatement(`
        INSERT INTO colors (
          id, display_name, code, hex, source_id, color_spaces,
          is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
          is_library, properties, created_at, updated_at, organization_id,
          created_by, user_id, deleted_at, device_id, conflict_resolved_at, is_synced
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        colorId, // id (UUID primary key)
        displayName, // display_name field
        colorData.code,
        colorData.hex,
        sourceId,
        colorSpaces,
        (colorData as any).is_gradient ? 1 : 0,
        (colorData as any).is_metallic ? 1 : 0,
        (colorData as any).is_effect ? 1 : 0,
        (colorData as any).gradient_colors || null,
        colorData.notes || null,
        colorData.tags || null,
        colorData.isLibrary ? 1 : 0,
        (colorData as any).properties || null,
        now,
        now,
        organizationId, // Use UUID for organization_id foreign key
        null, // created_by
        null, // user_id
        null, // deleted_at
        null, // device_id
        null, // conflict_resolved_at
        (colorData as any).is_synced ? 1 : 0
      );

      return colorId;
    } catch (error) {
      console.error('[ColorRepository] Error inserting color:', error);
      console.error(
        '[ColorRepository] Color data that failed:',
        JSON.stringify(colorData, null, 2)
      );
      throw error;
    }
  }

  update(
    id: string,
    updates: UpdateColorEntry,
    organizationId: string
  ): boolean {
    requireValidOrganizationId(organizationId);

    const updateFields: string[] = [];
    const values: any[] = [];

    // Build dynamic update query with proper serialization
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);

        // Serialize objects and arrays to JSON strings for SQLite
        if (typeof value === 'object' && value !== null) {
          values.push(JSON.stringify(value));
        } else if (typeof value === 'boolean') {
          // Convert boolean to integer for SQLite
          values.push(value ? 1 : 0);
        } else {
          values.push(value);
        }
      }
    });

    if (updateFields.length === 0) {
      return false;
    }

    // Add updated_at timestamp
    updateFields.push('updated_at = ?');
    values.push(new Date().toISOString());

    // Add WHERE conditions
    values.push(id, organizationId);

    const query = `
      UPDATE colors 
      SET ${updateFields.join(', ')}
      WHERE id = ? AND organization_id = ? AND deleted_at IS NULL
    `;

    const stmt = this.getPreparedStatement(query);
    const result = stmt.run(...values);

    return result.changes > 0;
  }

  softDelete(colorId: string, organizationId: string): boolean {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      UPDATE colors 
      SET deleted_at = ?
      WHERE id = ? AND organization_id = ? AND deleted_at IS NULL
    `);

    const result = stmt.run(new Date().toISOString(), colorId, organizationId);
    return result.changes > 0;
  }

  // Query Operations
  getUsageCounts(
    organizationId: string
  ): Map<string, { count: number; products: string[] }> {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      SELECT 
        c.display_name as color_name,
        COUNT(DISTINCT p.id) as product_count,
        GROUP_CONCAT(DISTINCT p.name) as product_names
      FROM colors c
      LEFT JOIN product_colors pc ON c.id = pc.color_id AND pc.organization_id = c.organization_id
      LEFT JOIN products p ON pc.product_id = p.id AND p.deleted_at IS NULL
      WHERE c.deleted_at IS NULL AND c.organization_id = ?
      GROUP BY c.id, c.display_name
      HAVING product_count > 0
    `);

    const results = stmt.all(organizationId) as LocalUsageCountResult[];
    const usageMap = new Map<string, { count: number; products: string[] }>();

    results.forEach(row => {
      usageMap.set(row.color_name, {
        count: row.product_count,
        products: row.product_names ? row.product_names.split(',') : [],
      });
    });

    return usageMap;
  }

  getColorNameProductMap(organizationId: string): Map<string, string[]> {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      SELECT 
        c.display_name,
        GROUP_CONCAT(DISTINCT p.name) as product_names
      FROM colors c
      LEFT JOIN product_colors pc ON c.id = pc.color_id AND pc.organization_id = c.organization_id
      LEFT JOIN products p ON pc.product_id = p.id AND p.deleted_at IS NULL
      WHERE c.deleted_at IS NULL AND c.organization_id = ?
      GROUP BY c.display_name
    `);

    const results = stmt.all(organizationId) as LocalColorProductMapResult[];
    const colorProductMap = new Map<string, string[]>();

    results.forEach(row => {
      if (row.display_name) {
        colorProductMap.set(
          row.display_name,
          row.product_names ? row.product_names.split(',') : []
        );
      }
    });

    return colorProductMap;
  }

  findUnsynced(): ColorRow[] {
    const stmt = this.getPreparedStatement(`
      SELECT c.*, cs.code as source
      FROM colors c
      LEFT JOIN color_sources cs ON c.source_id = cs.id
      WHERE c.is_synced = 0 AND c.deleted_at IS NULL
    `);

    return stmt.all() as ColorRow[];
  }

  // Soft Delete Operations
  findSoftDeleted(
    organizationId: string,
    limit?: number,
    offset?: number
  ): ColorRow[] {
    requireValidOrganizationId(organizationId);

    let sql = `
      SELECT c.*, cs.code as source
      FROM colors c
      LEFT JOIN color_sources cs ON c.source_id = cs.id
      WHERE c.deleted_at IS NOT NULL AND c.organization_id = ?
      ORDER BY c.deleted_at DESC
    `;

    const params: any[] = [organizationId];

    if (limit !== undefined) {
      sql += ' LIMIT ?';
      params.push(limit);
    }

    if (offset !== undefined) {
      sql += ' OFFSET ?';
      params.push(offset);
    }

    const stmt = this.getPreparedStatement(sql);
    return stmt.all(...params) as ColorRow[];
  }

  restoreRecord(colorId: string, organizationId: string): boolean {
    requireValidOrganizationId(organizationId);

    const stmt = this.getPreparedStatement(`
      UPDATE colors 
      SET deleted_at = NULL
      WHERE id = ? AND organization_id = ? AND deleted_at IS NOT NULL
    `);

    const result = stmt.run(colorId, organizationId);
    return result.changes > 0;
  }

  bulkRestoreRecords(
    colorIds: string[],
    organizationId: string
  ): { success: boolean; restored: number } {
    requireValidOrganizationId(organizationId);

    if (colorIds.length === 0) {
      return { success: true, restored: 0 };
    }

    const placeholders = colorIds.map(() => '?').join(',');
    const stmt = this.getPreparedStatement(`
      UPDATE colors 
      SET deleted_at = NULL
      WHERE id IN (${placeholders}) AND organization_id = ? AND deleted_at IS NOT NULL
    `);

    const result = stmt.run(...colorIds, organizationId);
    return { success: true, restored: result.changes };
  }

  cleanupOldSoftDeleted(
    organizationId: string,
    daysOld: number = 30
  ): { success: boolean; cleaned: number } {
    requireValidOrganizationId(organizationId);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const stmt = this.getPreparedStatement(`
      DELETE FROM colors 
      WHERE organization_id = ? 
        AND deleted_at IS NOT NULL 
        AND deleted_at < ?
    `);

    const result = stmt.run(organizationId, cutoffDate.toISOString());
    return { success: true, cleaned: result.changes };
  }

  // Bulk Operations
  clearAll(organizationId: string, hardDelete: boolean = false): boolean {
    requireValidOrganizationId(organizationId);

    let stmt: Database.Statement;

    if (hardDelete) {
      stmt = this.getPreparedStatement(`
        DELETE FROM colors WHERE organization_id = ?
      `);
    } else {
      stmt = this.getPreparedStatement(`
        UPDATE colors 
        SET deleted_at = ?
        WHERE organization_id = ? AND deleted_at IS NULL
      `);
    }

    const params = hardDelete
      ? [organizationId]
      : [new Date().toISOString(), organizationId];
    const result = stmt.run(...params);
    return result.changes > 0;
  }

  invalidateOrphans(organizationId: string): void {
    requireValidOrganizationId(organizationId);

    // Soft delete colors that have no product relationships and are not library colors
    const stmt = this.getPreparedStatement(`
      UPDATE colors 
      SET deleted_at = ?
      WHERE organization_id = ? 
        AND deleted_at IS NULL
        AND is_library = 0
        AND id NOT IN (
          SELECT DISTINCT pc.color_id 
          FROM product_colors pc 
          INNER JOIN products p ON pc.product_id = p.id 
          WHERE p.deleted_at IS NULL AND p.is_active = 1
        )
    `);

    stmt.run(new Date().toISOString(), organizationId);
  }

  // Utility
  markAsSynced(colorId: string): void {
    const stmt = this.getPreparedStatement(`
      UPDATE colors 
      SET is_synced = 1
      WHERE id = ?
    `);

    stmt.run(colorId);
  }

  getSourceIdByCode(sourceCode: string): string | null {
    const stmt = this.getPreparedStatement(
      `SELECT id FROM color_sources WHERE code = ?`
    );
    const source = stmt.get(sourceCode) as { id: string } | undefined;
    return source ? source.id : null;
  }

  getPreparedStatement(sql: string): Database.Statement {
    if (!ColorRepository.preparedStatements.has(sql)) {
      ColorRepository.preparedStatements.set(sql, this.db.prepare(sql));
    }
    return ColorRepository.preparedStatements.get(sql)!;
  }
}
