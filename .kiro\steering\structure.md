# ChromaSync Project Structure

## Root Directory Organization
```
├── src/                    # Source code (TypeScript/React)
├── scripts/                # Build, migration, and utility scripts
├── docs/                   # Documentation and guides
├── assets/                 # Static assets (logos, icons)
├── supabase/              # Supabase configuration and functions
├── mcp-servers/           # Model Context Protocol servers
└── .kiro/                 # Kiro AI assistant configuration
```

## Source Code Structure (`src/`)
```
src/
├── main/                  # Electron main process (Node.js)
│   ├── db/               # Database layer (SQLite + migrations)
│   ├── ipc/              # IPC handlers for renderer communication
│   ├── services/         # Business logic services
│   ├── utils/            # Utilities and helpers
│   └── windows/          # Window management
├── renderer/             # React frontend
│   ├── components/       # React components
│   ├── hooks/           # Custom React hooks
│   ├── store/           # Zustand state management
│   ├── utils/           # Frontend utilities
│   └── styles/          # CSS and styling
├── preload/             # Secure IPC bridge scripts
├── shared/              # Shared types and utilities
└── types/               # TypeScript type definitions
```

## Key Architectural Patterns

### Multi-Process Architecture
- **Main Process** (`src/main/`): Node.js backend with database access
- **Renderer Process** (`src/renderer/`): React frontend UI
- **Preload Scripts** (`src/preload/`): Secure IPC communication bridge

### Database Layer (`src/main/db/`)
```
db/
├── core/                # Core database functionality
├── migrations/          # Database schema migrations
├── repositories/        # Data access layer
├── services/           # Database business logic
├── schemas/            # Database schema definitions
└── utils/              # Database utilities
```

### IPC Communication (`src/main/ipc/`)
- **Handler Pattern**: Each domain has dedicated IPC handlers
- **Validation**: All inputs validated before processing
- **Organization Context**: Multi-tenant data isolation
- **Security**: Prepared statements, input sanitization

### Service Layer (`src/main/services/`)
```
services/
├── auth/               # Authentication services
├── color/              # Color management services
├── organization/       # Multi-tenant organization services
├── product/            # Product management services
├── sync/               # Cloud synchronization services
└── common/             # Shared service utilities
```

## Naming Conventions

### Files and Directories
- **kebab-case**: For file and directory names
- **PascalCase**: For React components
- **camelCase**: For TypeScript files and functions
- **UPPER_CASE**: For constants and environment variables

### Code Conventions
- **Interfaces**: Prefix with `I` (e.g., `IColorService`)
- **Types**: Descriptive names ending in `Type` (e.g., `ColorType`)
- **Enums**: PascalCase with descriptive names
- **Database**: snake_case for columns, camelCase for TypeScript

## Import Path Aliases
```typescript
@/*           → src/*
@main/*       → src/main/*
@renderer/*   → src/renderer/*
@shared/*     → src/shared/*
@components/* → src/renderer/components/*
@hooks/*      → src/renderer/hooks/*
@services/*   → src/main/services/*
@db/*         → src/main/db/*
```

## Configuration Files
- **TypeScript**: Multiple tsconfig files for different contexts
- **Build**: electron-vite.config.ts for Electron-specific Vite setup
- **Styling**: Tailwind with CSS custom properties
- **Testing**: Vitest with jsdom environment
- **Linting**: ESLint with TypeScript and React rules

## Documentation Structure (`docs/`)
```
docs/
├── architecture/       # Architecture decision records
├── archive/           # Historical documentation
├── development-tools/ # Development utilities
└── *.md              # Main documentation files
```

## Security Considerations
- **IPC Security**: Whitelist of allowed IPC channels
- **Input Validation**: All user inputs validated and sanitized
- **SQL Injection Prevention**: Prepared statements only
- **Context Isolation**: Secure preload script implementation
- **Organization Isolation**: All data scoped by `organization_id`