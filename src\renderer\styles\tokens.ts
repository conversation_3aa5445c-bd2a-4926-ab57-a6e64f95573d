/**
 * @file tokens.ts
 * @description Apple-inspired design token system for the ChromaSync app
 */

// Color Palette
export const colors = {
  // Brand colors (maintaining the core Pantone brand color)
  brand: {
    primary: '#1E40AF', // Deep Blue - Professional color management standard
    secondary: '#1D4ED8', // Hover Blue
    accent: '#3B82F6', // Light Blue accent
  },
  
  // UI colors - Apple inspired
  ui: {
    background: {
      primary: '#FFFFFF',
      secondary: '#F5F5F7', // Light gray like Apple.com
      tertiary: '#E9E9EB', // Subtle tertiary background
    },
    foreground: {
      primary: '#1D1D1F', // Dark but not pure black (Apple text color)
      secondary: '#515154', // Apple secondary text
      tertiary: '#86868B', // Apple tertiary text
      inverse: '#FFFFFF',
    },
    border: {
      light: '#E5E5E7',
      medium: '#D2D2D7',
      dark: '#86868B',
    },
    focus: '#2563EB', // Focus Blue
  },
  
  // Feedback colors
  feedback: {
    success: '#059669', // Professional green
    warning: '#D97706', // Professional amber
    error: '#DC2626', // Professional red
    info: '#0284C7', // Info blue
  },
};

// Typography
export const typography = {
  fontFamily: {
    sans: ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', 'Helvetica', 'Arial', 'sans-serif'],
    mono: ['SF Mono', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
  },
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem',  // 36px
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },
};

// Spacing system with index signature for type safety
export const spacing: {
  px: string;
  0: string;
  0.5: string;
  1: string;
  1.5: string;
  2: string;
  2.5: string;
  3: string;
  4: string;
  5: string;
  6: string;
  8: string;
  10: string;
  12: string;
  16: string;
  20: string;
  24: string;
  [key: string]: string;
} = {
  px: '1px',
  0: '0',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px
  4: '1rem',        // 16px
  5: '1.25rem',     // 20px
  6: '1.5rem',      // 24px
  8: '2rem',        // 32px
  10: '2.5rem',     // 40px
  12: '3rem',       // 48px
  16: '4rem',       // 64px
  20: '5rem',       // 80px
  24: '6rem',       // 96px
};

// Border radius (more generous like Apple's interfaces)
export const borderRadius = {
  none: '0',
  sm: '0.25rem',    // 4px
  DEFAULT: '0.5rem', // 8px - more rounded like Apple
  md: '0.625rem',    // 10px
  lg: '0.75rem',     // 12px
  xl: '1rem',        // 16px
  '2xl': '1.25rem',  // 20px
  full: '9999px',
};

// Subtle shadows like Apple uses
export const shadows = {
  sm: '0 1px 2px rgba(0, 0, 0, 0.04)',
  DEFAULT: '0 2px 5px rgba(0, 0, 0, 0.06)',
  md: '0 4px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03)',
  lg: '0 10px 20px rgba(0, 0, 0, 0.04), 0 2px 6px rgba(0, 0, 0, 0.03)',
  xl: '0 20px 25px rgba(0, 0, 0, 0.03), 0 3px 10px rgba(0, 0, 0, 0.02)',
};

// Smooth transitions like Apple interfaces
export const transitions = {
  duration: {
    75: '75ms',
    100: '100ms',
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms',
  },
  easing: {
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    // Apple's easing curve
    apple: 'cubic-bezier(0.25, 0.1, 0.25, 1.0)',
  },
};

// Z-index
export const zIndex = {
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  auto: 'auto',
  dropdown: '1000',
  modal: '1100',
  tooltip: '1200',
};

// Breakpoints
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

// Export all tokens as a default object
export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  transitions,
  zIndex,
  breakpoints,
}; 