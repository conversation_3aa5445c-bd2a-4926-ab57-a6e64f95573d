# ChromaSync Development Notes

## 🎯 CURRENT STATUS: Task 1 COMPLETED! ✅

**MAJOR BREAKTHROUGH**: **Gradient Data Sync Issue FIXED!**

### ✅ Task 1: Fix Gradient Data Sync Integrity - COMPLETED

**Duration**: 2 hours (ahead of 2-3 day estimate!)
**Status**: 🎉 **SUCCESS** - All acceptance criteria met

**Problem Solved**:

- ❌ **Before**: 1 gradient with empty `gradient_colors` field
- ✅ **After**: 1 gradient with valid gradient data (`#003366,#0077CC,#66B3FF`)

**Root Cause**: Data structure mismatch in `ColorSyncService.prepareColorForSupabase()` - was calling `color.gradient.gradientStops` but `GradientInfo` interface has `colors` array, not `gradientStops`.

**Solution**:

1. Fixed gradient data transformation with new `createGradientColorsFromGradientInfo()` method
2. Added reverse parsing with `parseGradientColorsCSV()` method
3. Updated all color conversion methods for bidirectional gradient sync
4. Comprehensive testing validates full round-trip data integrity

**Files Modified**: `src/main/services/color/color-sync.service.ts`
**Validation**: All unit tests pass, database shows 0 empty gradients

### 🔄 NEXT PRIORITY: Task 2 - Color Space Data Preservation

**Focus**: Ensure CMYK, RGB, LAB, HSL values survive sync operations without precision loss

---

## Project Overview

ChromaSync is an Electron.js application for managing color palettes and product associations. It uses SQLite for local storage and Supabase for cloud synchronization.

## Key Technical Details

### Database Architecture

- **Local**: SQLite database with organizations, products, colors, and product_colors tables
- **Cloud**: Supabase with matching schema for multi-user synchronization
- **Sync**: Bidirectional sync between local SQLite and Supabase

### Organization System

- Users can belong to multiple organizations
- Each organization has its own color palette and product catalog
- Organization switching changes the data context throughout the app

### Current Issues Resolved

1. **Product Count Display**: Fixed duplicate product filtering in ProductsPanel.tsx to show correct color counts
2. **Database Deduplication**: Implemented automatic product deduplication after sync
3. **UUID Consistency**: Ensured organization IDs use UUIDs consistently between local and cloud

### Current Issue: Organization Sync Failure - SOLVED ✅

- **Problem**: New organizations created in the app appear locally but fail to sync to Supabase
- **Root Cause**: Organization service was calling Supabase without ensuring user authentication first
- **Solution**: Modified organization service to use `ensureAuthenticatedSession()` before Supabase operations
- **RLS Setup**: Created RLS policies migration file (`rls-policies-migration.sql`) that needs to be applied

#### Next Steps to Complete Fix:

1. ✅ Apply RLS policies by running `rls-policies-migration.sql` in Supabase dashboard
2. ⏳ Test organization creation with authenticated user
3. ⏳ Verify organizations sync to Supabase correctly

#### Testing Instructions:

1. Launch the app: `npm start`
2. Authenticate with Google OAuth (user: <EMAIL>)
3. Create a new organization through the UI
4. Check console logs for "✅ Successfully synced to Supabase with user auth"
5. Verify the organization appears in Supabase database

#### What's Fixed:

- Organization service now ensures user authentication before Supabase calls
- Proper error handling and logging for sync operations
- RLS policies created for secure organization management
- Local-first approach maintained as fallback

### Current Issue: Duplicate **ELECTRON_LOG** IPC Handler - SOLVED ✅

- **Problem**: Dynamic import of initial-data-sync.ts caused duplicate electron-log IPC handler registration
- **Root Cause**: electron-log automatically registers IPC handlers on module load, causing conflicts when imported multiple times
- **Solution**: Added IPC handler guard in logger.ts that prevents duplicate registrations
- **Additional Fix**: Added require.main polyfill for bundled environments where electron-log expects it

#### Fix Implementation:

1. ✅ Moved logger import to be the very first import in main index.ts
2. ✅ Added ipcMain.handle override to prevent duplicate **ELECTRON_LOG** registrations
3. ✅ Added require.main polyfill for electron-log compatibility
4. ✅ Disabled IPC transport in electron-log as additional safety measure

## Supabase Configuration

### MCP Access Token

```bash
export SUPABASE_ACCESS_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM4MzIxNiwiZXhwIjoyMDYzOTU5MjE2fQ.b6LPM_pAKVJs-QH_VmMQii9-mMJIzRe14pX-3Fjxvb4
```

### Project Details

- **Project ID**: tzqnxhsnitogmtihhsrq
- **URL**: https://tzqnxhsnitogmtihhsrq.supabase.co
- **Service Role Key**: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM4MzIxNiwiZXhwIjoyMDYzOTU5MjE2fQ.b6LPM_pAKVJs-QH_VmMQii9-mMJIzRe14pX-3Fjxvb4

### MCP Setup Instructions

To use Supabase MCP tools in Claude Code, configure with this command:

```bash
claude mcp add supabase -s local -e SUPABASE_ACCESS_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM4MzIxNiwiZXhwIjoyMDYzOTU5MjE2fQ.b6LPM_pAKVJs-QH_VmMQii9-mMJIzRe14pX-3Fjxvb4 -- npx -y @supabase/mcp-server-supabase@latest
```

Alternative setup with environment variable:

```bash
export SUPABASE_ACCESS_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR6cW54aHNuaXRvZ210aWhoc3JxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODM4MzIxNiwiZXhwIjoyMDYzOTU5MjE2fQ.b6LPM_pAKVJs-QH_VmMQii9-mMJIzRe14pX-3Fjxvb4
```

Available MCP tools:

- `mcp__supabase__execute_sql` - Execute SQL queries
- `mcp__supabase__list_tables` - List database tables
- `mcp__supabase__apply_migration` - Apply database migrations
- `mcp__supabase__get_advisors` - Get security/performance advisors

### Data Verification

- **IVG Organization ID**: 4047153f-7be8-490b-9cb2-a1e3ed04b92b
- **Colors in Supabase**: 456 colors confirmed (as of July 2025)
- **Products in Supabase**: Available for sync
- **User ID**: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf

### Key User for Testing

- **Email**: <EMAIL>
- **User ID**: bdcfcd21-ddb3-4386-bfb3-e6d5906babbf
- **Current Organizations**: 2 (IVG, Default Organization)

## Development Commands

### Build and Run

```bash
npm start                    # Build and start Electron app
npm run build               # Build only
npm run dev                 # Development mode
```

### Testing Organization Sync

```bash
# Check current organizations in Supabase
curl -X GET "https://tzqnxhsnitogmtihhsrq.supabase.co/rest/v1/organizations?select=*" \
-H "apikey: [SERVICE_ROLE_KEY]" \
-H "Authorization: Bearer [SERVICE_ROLE_KEY]"

# Check user memberships
curl -X GET "https://tzqnxhsnitogmtihhsrq.supabase.co/rest/v1/organization_members?select=*,organizations(name)&user_id=eq.bdcfcd21-ddb3-4386-bfb3-e6d5906babbf" \
-H "apikey: [SERVICE_ROLE_KEY]" \
-H "Authorization: Bearer [SERVICE_ROLE_KEY]"
```

## File Locations

### Key Source Files

- `src/main/db/services/organization.service.ts` - Organization CRUD and Supabase sync
- `src/renderer/store/organization.store.ts` - Frontend organization state management
- `src/renderer/components/Products/ProductsPanel.tsx` - Product display with color counts
- `src/main/db/services/product.service.ts` - Product operations and deduplication

### Database Locations

- **Production SQLite**: `~/Library/Application Support/chroma-sync/chromasync.db` (macOS)
- **Development SQLite**: `/Users/<USER>/Documents/Applications/chromasync-mac/chromasync.db` (empty, 0 bytes)
- **Test Database**: `/Users/<USER>/Documents/Applications/chromasync-mac/test-chromasync.db`
- **Backup**: `/Users/<USER>/Documents/Applications/chromasync-mac/chromasync.db.backup-20250705-181213` (499KB with data)
- **Archive Backups**: `.archive/backups-20250525/` (historical backups)
- **Migrations**: `src/main/db/migrations/`

## Sync and Data Structure Analysis (July 6, 2025)

### Critical Issues Found

#### 1. Gradient Data Structure Inconsistencies ⚠️

**Problem**: Multiple gradient storage formats causing sync conflicts

- **Local SQLite**: Uses `gradient_colors` TEXT field (CSV format) + `is_gradient` boolean
- **Supabase**: Missing gradient-specific storage, relies on `color_spaces` JSONB
- **Type System**: Complex legacy compatibility layer between old/new gradient formats

**Evidence**:

```sql
-- Supabase gradient color example
SELECT * FROM colors WHERE is_gradient = true LIMIT 1;
-- Shows: gradient_colors="" (empty), but is_gradient=true
```

**Impact**: Gradient colors may lose data during sync, especially color codes and position data

#### 2. Color Space Storage Mismatch ⚠️

**Problem**: Denormalized vs normalized color space storage

- **Local SQLite**: Uses `color_spaces` JSON column (denormalized, migration 017)
- **Legacy Code**: Still references normalized tables (color_rgb, color_cmyk, etc.)
- **Sync Logic**: May not properly convert between formats

**Evidence**: Local schema shows denormalized storage, but code still has conversion logic for normalized tables

#### 3. UUID Primary Key Migration Issues ⚠️

**Problem**: Complex ID mapping between local and remote

- **Local**: Uses TEXT UUIDs as primary keys (migrations 28-30)
- **Supabase**: Uses UUID primary keys with separate `external_id` field
- **Mapping**: Relies on `supabase_id` field for cross-reference

**Impact**: Potential data duplication or orphaned records during sync

#### 4. Outbox Service Stale Operations ⚠️

**Problem**: Sync outbox accumulates failed operations

- Failed delete operations remain in queue even when items don't exist
- Auto-cleanup exists but may miss edge cases
- 24-hour cleanup may be too aggressive for slow network conditions

### Schema Comparison: Local vs Supabase

#### Colors Table Differences:

```sql
-- Local SQLite (complete-schema.ts)
colors (
  id TEXT PRIMARY KEY,           -- UUID as TEXT
  color_spaces JSON,             -- Denormalized color spaces
  gradient_colors TEXT,          -- CSV of hex values
  notes TEXT,                    -- User notes
  tags TEXT,                     -- User tags
  is_library BOOLEAN,            -- Library flag
  supabase_id INTEGER            -- Mapping to Supabase
)

-- Supabase (actual schema)
colors (
  id UUID PRIMARY KEY,           -- Native UUID
  external_id UUID,              -- Maps to local id
  color_spaces JSONB,            -- Same as local
  gradient_colors TEXT,          -- Present but empty
  notes TEXT,                    -- Present
  tags TEXT,                     -- Present
  is_library BOOLEAN,            -- Present
  is_synced INTEGER              -- Sync flag (shouldn't exist in Supabase)
)
```

#### Missing Tables in Supabase:

- `color_sources` - Color source definitions
- `library_colors` - Color library data
- `color_libraries` - Library definitions
- `users` - Local user cache (expected to be missing)

### Sync Service Analysis

#### UnifiedSyncManager Issues:

1. **Service Readiness**: Complex validation that may fail unnecessarily
2. **Error Recovery**: Handles `not found` errors but may miss other edge cases
3. **Progress Reporting**: Good implementation but could benefit from more granular updates
4. **Outbox Processing**: Proper handling but relies on service-level push methods

#### Color Sync Specific Issues:

1. **Gradient Processing**: No specific gradient sync logic in color service
2. **Color Space Conversion**: May not properly handle denormalized format
3. **Library Color Sync**: No sync logic for library colors (by design?)

### Recommendations

#### High Priority Fixes:

1. **Standardize Gradient Storage**: Choose single format (recommend JSONB with stops array)
2. **Color Space Validation**: Ensure sync preserves all color space data
3. **UUID Mapping Audit**: Verify no orphaned records exist
4. **Gradient Sync Logic**: Add specific handling for gradient data

#### Medium Priority Improvements:

1. **Outbox Cleanup**: Make cleanup more conservative (7 days vs 24 hours)
2. **Sync Diagnostics**: Add more detailed logging for gradient operations
3. **Schema Validation**: Add runtime validation between local and remote schemas

#### Low Priority Enhancements:

1. **Library Color Sync**: Consider syncing color libraries between devices
2. **Offline Resilience**: Improve handling of extended offline periods
3. **Performance**: Optimize bulk gradient operations

### Test Scenarios Needed:

1. Create gradient with multiple stops → sync → verify all data preserved
2. Update gradient colors → sync → check color codes maintained
3. Delete gradient → verify cleanup in both local and remote
4. Offline gradient creation → sync after reconnect
5. Conflict resolution for gradient updates from multiple devices

### Immediate Action Items

#### 1. Gradient Data Integrity Fix

- [ ] Audit existing gradients in Supabase with empty `gradient_colors`
- [ ] Implement proper gradient serialization in color sync service
- [ ] Add validation to ensure gradient stops are preserved during sync

#### 2. Schema Alignment

- [ ] Compare complete schemas between local SQLite and Supabase
- [ ] Document any intentional differences (like library tables)
- [ ] Create migration to fix any unintended schema drift

#### 3. Sync Testing Protocol

- [ ] Create comprehensive test for gradient sync roundtrip
- [ ] Test color space preservation during sync operations
- [ ] Verify UUID mapping consistency after sync

## Task Master TODO List Created 📋

**Location**: `.taskmaster/tasks/sync-fixes-todo.md` (detailed) and `sync-fixes-tasks.json` (structured)

### Critical Priority Tasks (Fix Immediately):

1. **Fix Gradient Data Sync Integrity** (2-3 days) - Most critical issue
2. **Audit and Fix Color Space Data Preservation** (1-2 days)
3. **UUID Mapping Consistency Audit** (1-2 days)

### High Priority Tasks (This Week):

4. **Enhance Sync Outbox Management** (1 day)
5. **Schema Alignment Documentation** (1 day)
6. **Implement Comprehensive Gradient Sync Testing** (2 days)

### Medium Priority Tasks (This Sprint):

7. **Add Detailed Sync Operation Logging** (1 day)
8. **Color Space Conversion Validation** (1 day)
9. **Sync Error Recovery Enhancement** (1-2 days)
10. **Performance Optimization for Bulk Operations** (2 days)

**Total Estimated Time**: 14-20 days
**Immediate Focus**: Start with Task 1 (Gradient Data Sync) as it's the most critical data integrity issue.

## Known Working Features

- Organization switching and context management
- Product and color synchronization from Supabase
- Product deduplication after sync
- Color palette display with usage counts

## Testing Protocol

1. Create new organization in app UI
2. Verify it appears in local dropdown
3. Check Supabase for sync status
4. Test switching between organizations
5. Verify data isolation between organizations
