/**
 * @file email-retry-queue.ts
 * @description Email retry queue using electron-store for persistence
 * Follows existing patterns from electron-store-patched.ts
 */

import PatchedStore from '../../utils/electron-store-patched';
import { app } from 'electron';

interface QueuedEmail {
  id: string;
  emailData: {
    to: string | string[];
    subject: string;
    content: string;
    fromAddress?: string;
    isHtml?: boolean;
    cc?: string[];
    bcc?: string[];
    replyTo?: string;
  };
  attemptCount: number;
  maxAttempts: number;
  nextRetryTime: number;
  createdAt: number;
  lastError?: string;
  priority: 'high' | 'medium' | 'low';
}

interface RetryQueueState extends Record<string, unknown> {
  emails: QueuedEmail[];
  lastProcessedTime: number;
  processedCount: number;
  failedCount: number;
}

export class EmailRetryQueue {
  private store: PatchedStore<RetryQueueState>;
  private processingInterval: ReturnType<typeof setInterval> | null = null;
  private isProcessing = false;

  // Queue configuration following existing patterns
  private readonly PROCESSING_INTERVAL = 30000; // Process every 30 seconds
  private readonly MAX_QUEUE_SIZE = 100;
  private readonly DEFAULT_MAX_ATTEMPTS = 5;
  private readonly BASE_RETRY_DELAY = 60000; // 1 minute base delay

  constructor() {
    // Initialize store using existing electron-store-patched pattern
    this.store = new PatchedStore<RetryQueueState>({
      name: 'email-retry-queue',
      cwd: app.getPath('userData'),
      defaults: {
        emails: [],
        lastProcessedTime: 0,
        processedCount: 0,
        failedCount: 0,
      },
      // Follow existing pattern for error handling
      clearInvalidConfig: true,
    });
  }

  /**
   * Initialize the retry queue and start processing
   */
  async initialize(): Promise<void> {
    console.log('[EmailRetryQueue] Initializing email retry queue...');

    // Clean up any expired emails on startup
    await this.cleanupExpiredEmails();

    // Start processing if there are queued emails
    const queuedEmails = this.getQueuedEmails();
    if (queuedEmails.length > 0) {
      console.log(
        `[EmailRetryQueue] Found ${queuedEmails.length} queued emails`
      );
      this.startProcessing();
    }

    console.log('[EmailRetryQueue] Email retry queue initialized');
  }

  /**
   * Add an email to the retry queue
   */
  async queueEmail(
    emailData: QueuedEmail['emailData'],
    priority: QueuedEmail['priority'] = 'medium',
    maxAttempts: number = this.DEFAULT_MAX_ATTEMPTS
  ): Promise<string> {
    const emailId = this.generateEmailId();
    const now = Date.now();

    const queuedEmail: QueuedEmail = {
      id: emailId,
      emailData,
      attemptCount: 0,
      maxAttempts,
      nextRetryTime: now,
      createdAt: now,
      priority,
    };

    // Check queue size limit
    const currentEmails = this.getQueuedEmails();
    if (currentEmails.length >= this.MAX_QUEUE_SIZE) {
      // Remove oldest low-priority emails to make space
      this.cleanupOldEmails();
    }

    // Add to queue
    const emails = [...currentEmails, queuedEmail];
    this.store.set('emails', emails);

    console.log(
      `[EmailRetryQueue] Queued email ${emailId} (priority: ${priority})`
    );

    // Start processing if not already running
    if (!this.isProcessing) {
      this.startProcessing();
    }

    return emailId;
  }

  /**
   * Remove an email from the queue
   */
  removeEmail(emailId: string): void {
    const emails = this.getQueuedEmails().filter(email => email.id !== emailId);
    this.store.set('emails', emails);
    console.log(`[EmailRetryQueue] Removed email ${emailId} from queue`);
  }

  /**
   * Mark an email as failed and schedule retry
   */
  async markEmailFailed(emailId: string, error: string): Promise<boolean> {
    const emails = this.getQueuedEmails();
    const emailIndex = emails.findIndex(email => email.id === emailId);

    if (emailIndex === -1) {
      console.warn(`[EmailRetryQueue] Email ${emailId} not found in queue`);
      return false;
    }

    const email = emails[emailIndex];
    if (!email) {return false;}

    email.attemptCount++;
    email.lastError = error;

    // Check if max attempts reached
    if (email.attemptCount >= email.maxAttempts) {
      console.log(
        `[EmailRetryQueue] Email ${emailId} exceeded max attempts (${email.maxAttempts})`
      );
      this.removeEmail(emailId);

      // Update failed count
      const failedCount = this.store.get('failedCount', 0);
      this.store.set('failedCount', failedCount + 1);

      return false;
    }

    // Calculate next retry time with exponential backoff
    const delay = this.calculateRetryDelay(email.attemptCount);
    email.nextRetryTime = Date.now() + delay;

    // Update the email in the queue
    emails[emailIndex] = email;
    this.store.set('emails', emails);

    console.log(
      `[EmailRetryQueue] Email ${emailId} scheduled for retry in ${Math.ceil(delay / 1000)}s (attempt ${email.attemptCount}/${email.maxAttempts})`
    );

    return true;
  }

  /**
   * Get all queued emails ready for processing
   */
  getReadyEmails(): QueuedEmail[] {
    const now = Date.now();
    return this.getQueuedEmails()
      .filter(email => email.nextRetryTime <= now)
      .sort((a, b) => {
        // Sort by priority (high first), then by next retry time
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        const priorityDiff =
          priorityOrder[a.priority] - priorityOrder[b.priority];
        if (priorityDiff !== 0) {
          return priorityDiff;
        }
        return a.nextRetryTime - b.nextRetryTime;
      });
  }

  /**
   * Get queue statistics
   */
  getQueueStats(): {
    totalQueued: number;
    readyToProcess: number;
    processed: number;
    failed: number;
    oldestEmail?: number;
  } {
    const emails = this.getQueuedEmails();
    const readyEmails = this.getReadyEmails();
    const oldestEmail =
      emails.length > 0 ? Math.min(...emails.map(e => e.createdAt)) : undefined;

    return {
      totalQueued: emails.length,
      readyToProcess: readyEmails.length,
      processed: this.store.get('processedCount', 0),
      failed: this.store.get('failedCount', 0),
      oldestEmail,
    };
  }

  /**
   * Start the background processing
   */
  private startProcessing(): void {
    if (this.processingInterval) {
      return; // Already processing
    }

    console.log('[EmailRetryQueue] Starting background processing');
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, this.PROCESSING_INTERVAL);

    // Process immediately
    setTimeout(() => this.processQueue(), 1000);
  }

  /**
   * Stop the background processing
   */
  stopProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('[EmailRetryQueue] Stopped background processing');
    }
  }

  /**
   * Process queued emails (will be implemented with actual email service)
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return; // Already processing
    }

    const readyEmails = this.getReadyEmails();
    if (readyEmails.length === 0) {
      // No emails ready, stop processing
      this.stopProcessing();
      return;
    }

    this.isProcessing = true;

    try {
      console.log(
        `[EmailRetryQueue] Processing ${readyEmails.length} ready emails`
      );

      // Process one email at a time to avoid overwhelming the service
      for (const email of readyEmails.slice(0, 3)) {
        // Limit to 3 at a time
        // This will be connected to the actual email service later
        console.log(
          `[EmailRetryQueue] Would process email ${email.id} (attempt ${email.attemptCount + 1})`
        );

        // For now, just remove from queue (will be replaced with actual sending)
        // this.removeEmail(email.id);

        // Update processed count
        const processedCount = this.store.get('processedCount', 0);
        this.store.set('processedCount', processedCount + 1);
      }

      // Update last processed time
      this.store.set('lastProcessedTime', Date.now());
    } catch (error) {
      console.error('[EmailRetryQueue] Error processing queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attemptCount: number): number {
    const exponentialDelay =
      this.BASE_RETRY_DELAY * Math.pow(2, attemptCount - 1);
    const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5);
    const maxDelay = 24 * 60 * 60 * 1000; // Max 24 hours
    return Math.min(jitteredDelay, maxDelay);
  }

  /**
   * Generate unique email ID
   */
  private generateEmailId(): string {
    return `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all queued emails from store
   */
  private getQueuedEmails(): QueuedEmail[] {
    return this.store.get('emails', []);
  }

  /**
   * Clean up expired emails (older than 7 days)
   */
  private cleanupExpiredEmails(): void {
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    const emails = this.getQueuedEmails().filter(
      email => email.createdAt > sevenDaysAgo
    );
    const removedCount = this.getQueuedEmails().length - emails.length;

    if (removedCount > 0) {
      this.store.set('emails', emails);
      console.log(
        `[EmailRetryQueue] Cleaned up ${removedCount} expired emails`
      );
    }
  }

  /**
   * Clean up old low-priority emails when queue is full
   */
  private cleanupOldEmails(): void {
    const emails = this.getQueuedEmails();
    const lowPriorityEmails = emails
      .filter(email => email.priority === 'low')
      .sort((a, b) => a.createdAt - b.createdAt);

    if (lowPriorityEmails.length > 0) {
      const emailToRemove = lowPriorityEmails[0];
      if (emailToRemove) {
        this.removeEmail(emailToRemove.id);
        console.log(
          `[EmailRetryQueue] Removed old low-priority email ${emailToRemove.id} to make space`
        );
      }
    }
  }

  /**
   * Cleanup method for graceful shutdown
   */
  async cleanup(): Promise<void> {
    this.stopProcessing();
    console.log('[EmailRetryQueue] Cleanup completed');
  }
}

// Singleton instance
export const emailRetryQueue = new EmailRetryQueue();
