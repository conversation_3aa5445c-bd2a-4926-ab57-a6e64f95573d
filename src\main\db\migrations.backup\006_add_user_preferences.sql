-- Migration: Add preferences column to users table
-- Version: 006
-- Date: 2025-06-04

-- This migration is handled by the migration runner which checks if column exists
-- If preferences column already exists, this will be skipped by the runner
-- ALTER TABLE users ADD COLUMN preferences TEXT DEFAULT '{}';

-- Update existing records to have empty preferences if null (safe to run multiple times)
UPDATE users SET preferences = '{}' WHERE preferences IS NULL;
