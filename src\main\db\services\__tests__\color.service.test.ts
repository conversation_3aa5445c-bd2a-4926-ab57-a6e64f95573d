import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ColorService } from '../color.service';
import Database from 'better-sqlite3';

describe('ColorService', () => {
  let service: ColorService;
  let testDb: Database.Database;

  beforeEach(() => {
    // Create in-memory database for testing
    testDb = new Database(':memory:');

    // Create minimal schema for testing
    testDb.exec(`
      CREATE TABLE colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT UNIQUE NOT NULL,
        source_id INTEGER NOT NULL DEFAULT 1,
        code TEXT NOT NULL,
        display_name TEXT,
        hex TEXT NOT NULL,
        is_gradient BOOLEAN DEFAULT FALSE,
        is_metallic BOOLEAN DEFAULT FALSE,
        is_effect BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        properties TEXT,
        search_terms TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT
      );
      
      CREATE TABLE color_cmyk (
        color_id INTEGER PRIMARY KEY,
        c REAL NOT NULL,
        m REAL NOT NULL,
        y REAL NOT NULL,
        k REAL NOT NULL,
        FOREIGN KEY (color_id) REFERENCES colors(id)
      );
      
      CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        sku TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        metadata TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE TABLE product_colors (
        product_id INTEGER NOT NULL,
        color_id INTEGER NOT NULL,
        display_order INTEGER DEFAULT 0,
        usage_type TEXT DEFAULT 'standard',
        quantity INTEGER,
        metadata TEXT,
        added_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_id, color_id),
        FOREIGN KEY (product_id) REFERENCES products(id),
        FOREIGN KEY (color_id) REFERENCES colors(id)
      );
    `);

    service = new ColorService(testDb);
  });

  afterEach(() => {
    testDb.close();
  });

  describe('add', () => {
    it('should add a new color', () => {
      const newColor = {
        product: 'Test Product',
        name: 'Test Red',
        code: 'TEST-001',
        hex: '#FF0000',
        cmyk: '0,100,100,0',
        notes: 'Test color',
        isLibrary: false,
      };

      const result = service.add(newColor);

      expect(result).toBeDefined();
      expect(result.id).toBeTruthy();
      expect(result.name).toBe('Test Red');
      expect(result.code).toBe('TEST-001');
      expect(result.hex).toBe('#FF0000');
      expect(result.cmyk).toBe('0,100,100,0');
    });

    it('should validate hex color format', () => {
      const invalidColor = {
        product: 'Test Product',
        name: 'Invalid Color',
        code: 'INVALID-001',
        hex: 'not-a-hex',
        cmyk: '0,0,0,0',
        isLibrary: false,
      };

      expect(() => service.add(invalidColor)).toThrow();
    });
  });

  describe('getAll', () => {
    it('should return all active colors', () => {
      // Add some test colors
      const colors = [
        {
          product: 'Product 1',
          name: 'Color 1',
          code: 'COL-001',
          hex: '#FF0000',
          cmyk: '0,100,100,0',
          isLibrary: false,
        },
        {
          product: 'Product 2',
          name: 'Color 2',
          code: 'COL-002',
          hex: '#00FF00',
          cmyk: '100,0,100,0',
          isLibrary: false,
        },
      ];

      colors.forEach(color => service.add(color));

      const result = service.getAll();
      expect(result).toHaveLength(2);
      expect(result[0].code).toBe('COL-001');
      expect(result[1].code).toBe('COL-002');
    });
  });

  describe('update', () => {
    it('should update an existing color', () => {
      const color = service.add({
        product: 'Test Product',
        name: 'Original Name',
        code: 'UPDATE-001',
        hex: '#FF0000',
        cmyk: '0,100,100,0',
        isLibrary: false,
      });

      const updated = service.update(color.id, {
        name: 'Updated Name',
        hex: '#00FF00',
      });

      expect(updated).toBeDefined();
      expect(updated!.name).toBe('Updated Name');
      expect(updated!.hex).toBe('#00FF00');
      expect(updated!.code).toBe('UPDATE-001'); // Unchanged
    });
  });

  describe('delete', () => {
    it('should soft delete a color', () => {
      const color = service.add({
        product: 'Test Product',
        name: 'To Delete',
        code: 'DELETE-001',
        hex: '#FF0000',
        cmyk: '0,100,100,0',
        isLibrary: false,
      });

      const result = service.delete(color.id);
      expect(result).toBe(true);

      // Color should not appear in getAll
      const allColors = service.getAll();
      expect(allColors).toHaveLength(0);
    });
  });
});
