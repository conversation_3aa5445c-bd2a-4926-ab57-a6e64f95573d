/**
 * @file csp-configuration.ts
 * @description Content Security Policy configuration following Electron security best practices
 * 
 * Implements secure CSP policies to prevent XSS, data injection, and other security vulnerabilities.
 * Reference: https://www.electronjs.org/docs/latest/tutorial/security
 */

import { session } from 'electron';

// CSP Directive Types for better type safety
interface CSPDirectives {
  'default-src': string[];
  'script-src': string[];
  'style-src': string[];
  'img-src': string[];
  'font-src': string[];
  'connect-src': string[];
  'media-src': string[];
  'object-src': string[];
  'child-src': string[];
  'frame-src': string[];
  'worker-src': string[];
  'manifest-src': string[];
  'base-uri': string[];
  'form-action': string[];
  'frame-ancestors': string[];
  'plugin-types': string[];
  'sandbox': string[];
  'upgrade-insecure-requests': boolean;
  'block-all-mixed-content': boolean;
}

/**
 * CSP Policy Configuration
 */
export class CSPConfiguration {
  private static instance: CSPConfiguration;

  private constructor() {}

  static getInstance(): CSPConfiguration {
    if (!CSPConfiguration.instance) {
      CSPConfiguration.instance = new CSPConfiguration();
    }
    return CSPConfiguration.instance;
  }

  /**
   * Get production CSP directives (most restrictive)
   */
  private getProductionCSP(): Partial<CSPDirectives> {
    return {
      'default-src': ["'none'"], // Deny everything by default
      'script-src': [
        "'self'",
        // Allow specific scripts for React/Vite in production
        "'unsafe-inline'" // Only for production React hydration - consider removing if possible
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'" // Required for CSS-in-JS libraries and dynamic styling
      ],
      'img-src': [
        "'self'",
        'data:', // Data URLs for base64 images
        'blob:', // Blob URLs for generated images
        'file:' // Local file access for color library images
      ],
      'font-src': [
        "'self'",
        'data:' // Data URLs for embedded fonts
      ],
      'connect-src': [
        "'self'",
        'https://*.supabase.co', // Supabase API endpoints
        'https://fonts.googleapis.com', // Google Fonts API (if used)
        'wss://*.supabase.co' // Supabase WebSocket connections
      ],
      'media-src': ["'none'"], // No media content expected
      'object-src': ["'none'"], // Prevent Flash, Java applets, etc.
      'child-src': ["'none'"], // No child browsing contexts
      'frame-src': ["'none'"], // No iframes
      'worker-src': [
        "'self'",
        'blob:' // Allow Web Workers from blob URLs
      ],
      'manifest-src': ["'self'"], // Web app manifest
      'base-uri': ["'self'"], // Restrict base element
      'form-action': ["'self'"], // Restrict form submissions
      'frame-ancestors': ["'none'"], // Prevent embedding in frames
      'upgrade-insecure-requests': true,
      'block-all-mixed-content': true
    };
  }

  /**
   * Get development CSP directives (slightly relaxed for debugging)
   */
  private getDevelopmentCSP(): Partial<CSPDirectives> {
    const prodCSP = this.getProductionCSP();
    
    return {
      ...prodCSP,
      'script-src': [
        "'self'",
        "'unsafe-inline'", // Required for Vite hot reload
        "'unsafe-eval'", // Required for development source maps and React DevTools
        'http://localhost:*', // Vite dev server
        'ws://localhost:*' // Vite WebSocket for hot reload
      ],
      'connect-src': [
        ...(prodCSP['connect-src'] || []),
        'http://localhost:*', // Vite dev server
        'ws://localhost:*', // WebSocket connections for dev tools
        'wss://localhost:*' // Secure WebSocket for dev tools
      ]
    };
  }

  /**
   * Convert CSP directives to header string
   */
  private directivesToString(directives: Partial<CSPDirectives>): string {
    const cspParts: string[] = [];

    Object.entries(directives).forEach(([directive, value]) => {
      if (typeof value === 'boolean') {
        if (value) {
          cspParts.push(directive);
        }
      } else if (Array.isArray(value) && value.length > 0) {
        cspParts.push(`${directive} ${value.join(' ')}`);
      }
    });

    return cspParts.join('; ');
  }

  /**
   * Get CSP header value based on environment
   */
  getCSPHeader(): string {
    const isDevelopment = process.env.NODE_ENV === 'development' || process.env.ELECTRON_VITE_DEV_SERVER_URL;
    
    console.log(`[CSP] Configuring CSP for ${isDevelopment ? 'development' : 'production'} environment`);
    
    const directives = isDevelopment ? this.getDevelopmentCSP() : this.getProductionCSP();
    const cspHeader = this.directivesToString(directives);
    
    console.log(`[CSP] Generated CSP header: ${cspHeader}`);
    return cspHeader;
  }

  /**
   * Setup CSP via HTTP headers (recommended approach)
   */
  setupCSPHeaders(): void {
    const cspHeader = this.getCSPHeader();
    
    // Set up CSP for all web requests
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      const responseHeaders = {
        ...details.responseHeaders,
        'Content-Security-Policy': [cspHeader],
        // Additional security headers
        'X-Content-Type-Options': ['nosniff'],
        'X-Frame-Options': ['DENY'],
        'X-XSS-Protection': ['1; mode=block'],
        'Referrer-Policy': ['strict-origin-when-cross-origin'],
        'Permissions-Policy': ['camera=(), microphone=(), geolocation=(), payment=()']
      };

      callback({ responseHeaders });
    });

    console.log('[CSP] ✅ CSP headers configured via webRequest.onHeadersReceived');
  }

  /**
   * Get CSP meta tag content for HTML (fallback)
   */
  getCSPMetaTag(): string {
    return this.getCSPHeader();
  }

  /**
   * Validate current CSP configuration
   */
  validateCSP(): { isValid: boolean; warnings: string[]; errors: string[] } {
    const warnings: string[] = [];
    const errors: string[] = [];
    const isDevelopment = process.env.NODE_ENV === 'development';

    const directives = isDevelopment ? this.getDevelopmentCSP() : this.getProductionCSP();

    // Check for potential security issues
    if (directives['script-src']?.includes("'unsafe-inline'")) {
      const message = "'unsafe-inline' in script-src reduces security";
      if (isDevelopment) {
        warnings.push(`Development: ${message}`);
      } else {
        warnings.push(`Production: ${message} - consider using nonces or hashes`);
      }
    }

    if (directives['script-src']?.includes("'unsafe-eval'")) {
      const message = "'unsafe-eval' in script-src is dangerous";
      if (isDevelopment) {
        warnings.push(`Development: ${message} (allowed for DevTools)`);
      } else {
        errors.push(`Production: ${message} - this should be removed`);
      }
    }

    // Check for missing critical directives
    const criticalDirectives = ['default-src', 'script-src', 'object-src'];
    criticalDirectives.forEach(directive => {
      if (!directives[directive as keyof CSPDirectives]) {
        errors.push(`Missing critical directive: ${directive}`);
      }
    });

    // Validate that default-src is appropriately restrictive
    if (directives['default-src']?.includes("'unsafe-inline'") || 
        directives['default-src']?.includes("'unsafe-eval'")) {
      errors.push("default-src should not include 'unsafe-inline' or 'unsafe-eval'");
    }

    return {
      isValid: errors.length === 0,
      warnings,
      errors
    };
  }

  /**
   * Log CSP status and validation results
   */
  logCSPStatus(): void {
    const validation = this.validateCSP();
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    console.log(`[CSP] 🛡️  Content Security Policy Status (${isDevelopment ? 'Development' : 'Production'})`);
    console.log(`[CSP] Valid: ${validation.isValid ? '✅' : '❌'}`);
    
    if (validation.warnings.length > 0) {
      console.warn('[CSP] ⚠️  Warnings:');
      validation.warnings.forEach(warning => console.warn(`[CSP]   - ${warning}`));
    }
    
    if (validation.errors.length > 0) {
      console.error('[CSP] ❌ Errors:');
      validation.errors.forEach(error => console.error(`[CSP]   - ${error}`));
    }

    // Log the current CSP header for debugging
    if (isDevelopment) {
      console.log(`[CSP] 🔍 Current CSP: ${this.getCSPHeader()}`);
    }
  }
}

// Export singleton instance
export const cspConfiguration = CSPConfiguration.getInstance();