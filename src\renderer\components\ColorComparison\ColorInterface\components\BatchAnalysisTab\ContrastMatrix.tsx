import { useMemo, useState } from 'react';
import { calculateContrastRatio, calculateAPCAContrast } from '../../../../../../shared/utils/color/analysis';
import { hexToRgb } from '../../../../../../shared/utils/color/conversion';
// import { useTokens } from '../../../../../hooks/useTokens';
import { Info, Download, ChevronDown } from 'lucide-react';
import { isNotNullish, getOrDefault, safeArrayAccess } from '../../../../../../shared/types/type-guards';

interface ContrastMatrixProps {
  colors: Array<{
    id: string;
    hex: string;
    name: string;
  }> | null | undefined;
}

type ContrastMode = 'wcag' | 'apca';

export default function ContrastMatrix({ colors }: ContrastMatrixProps) {
  // const _tokens = useTokens();
  const [contrastMode, setContrastMode] = useState<ContrastMode>('wcag');
  const [selectedPair, setSelectedPair] = useState<{ row: number; col: number } | null>(null);
  const [_filterMode, _setFilterMode] = useState<'all' | 'pass' | 'fail'>('all');
  const [showExportMenu, setShowExportMenu] = useState(false);

  // Early return for empty colors
  if (!colors || colors.length === 0) {
    return (
      <div className="h-full overflow-y-auto p-4 flex items-center justify-center">
        <div className="text-center text-ui-foreground-secondary">
          <Info className="mx-auto mb-2 h-8 w-8 opacity-50" />
          <p>No colors available for contrast analysis</p>
          <p className="text-xs mt-1">Select colors to analyze contrast ratios</p>
        </div>
      </div>
    );
  }

  // Limit colors for performance with null safety
  const displayColors = useMemo(() => {
    if (!colors || colors.length === 0) {
      return [];
    }
    if (colors.length > 20) {
      console.warn(`Limiting contrast matrix to first 20 colors for performance (${colors.length} selected)`);
      return colors.slice(0, 20);
    }
    return colors;
  }, [colors]);

  const contrastData = useMemo(() => {
    const matrix: number[][] = [];
    
    if (!displayColors || displayColors.length === 0) {
      return matrix;
    }
    
    for (let i = 0; i < displayColors.length; i++) {
      matrix[i] = new Array(displayColors.length).fill(0); // Initialize with zeros
      const color1 = safeArrayAccess(displayColors, i);
      if (!color1) continue;
      
      const rgb1 = hexToRgb(color1.hex);
      if (!rgb1) {
        // Row already filled with zeros during initialization
        continue;
      }

      for (let j = 0; j < displayColors.length; j++) {
        if (i === j) {
          matrix[i]![j] = 0; // Same color - non-null assertion safe due to initialization
          continue;
        }

        const color2 = safeArrayAccess(displayColors, j);
        if (!color2) {
          matrix[i]![j] = 0; // Non-null assertion safe due to initialization
          continue;
        }

        const rgb2 = hexToRgb(color2.hex);
        if (!rgb2) {
          matrix[i]![j] = 0; // Non-null assertion safe due to initialization
          continue;
        }

        try {
          if (contrastMode === 'wcag') {
            matrix[i]![j] = calculateContrastRatio(rgb1, rgb2);
          } else {
            matrix[i]![j] = Math.abs(calculateAPCAContrast(rgb1, rgb2));
          }
        } catch (error) {
          console.warn('Error calculating contrast:', error);
          matrix[i]![j] = 0;
        }
      }
    }

    return matrix;
  }, [displayColors, contrastMode]);

  const getContrastLevel = (value: number) => {
    if (value === 0) {return { class: 'bg-gray-200', label: 'N/A' };}
    
    if (contrastMode === 'wcag') {
      if (value >= 7) {return { class: 'bg-green-500', label: 'AAA' };}
      if (value >= 4.5) {return { class: 'bg-yellow-500', label: 'AA' };}
      if (value >= 3) {return { class: 'bg-orange-500', label: 'AA Large' };}
      return { class: 'bg-red-500', label: 'Fail' };
    } else {
      // APCA thresholds
      if (value >= 90) {return { class: 'bg-green-500', label: 'Excellent' };}
      if (value >= 60) {return { class: 'bg-yellow-500', label: 'Good' };}
      if (value >= 45) {return { class: 'bg-orange-500', label: 'Fair' };}
      return { class: 'bg-red-500', label: 'Poor' };
    }
  };

  const handleCellClick = (row: number, col: number) => {
    if (row === col) {return;}
    setSelectedPair({ row, col });
  };

  const exportToCSV = () => {
    if (!displayColors || displayColors.length === 0) {
      console.warn('No data to export');
      return;
    }

    const headers = ['Color', ...displayColors.map(c => getOrDefault(c?.name, c?.hex || 'Unknown'))];
    const rows = displayColors.map((rowColor, rowIndex) => {
      const row = [getOrDefault(rowColor?.name, rowColor?.hex || 'Unknown')];
      const rowData = safeArrayAccess(contrastData, rowIndex);
      if (rowData) {
        rowData.forEach((value, colIndex) => {
          if (rowIndex === colIndex) {
            row.push('—');
          } else {
            row.push(isNotNullish(value) ? value.toFixed(2) : '0.00');
          }
        });
      } else {
        // Fill with dashes if no data
        for (let i = 0; i < displayColors.length; i++) {
          row.push('—');
        }
      }
      return row;
    });

    const csv = [headers, ...rows].map(row => row.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `contrast-matrix-${contrastMode}-${Date.now()}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const exportToJSON = () => {
    if (!displayColors || displayColors.length === 0) {
      console.warn('No data to export');
      return;
    }

    const data = {
      mode: contrastMode,
      timestamp: new Date().toISOString(),
      colors: displayColors,
      matrix: contrastData,
      levels: contrastData.map(row => 
        row ? row.map(value => getContrastLevel(value)) : []
      )
    };

    const json = JSON.stringify(data, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `contrast-matrix-${contrastMode}-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="h-full overflow-y-auto p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Contrast Matrix</h3>
          <p className="text-sm text-ui-foreground-secondary mt-1">
            {contrastMode === 'wcag' ? 'WCAG 2.1' : 'APCA (WCAG 3.0)'} contrast ratios between color pairs
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setContrastMode('wcag')}
            className={`px-3 py-1.5 text-sm rounded transition-colors ${
              contrastMode === 'wcag'
                ? 'bg-brand-primary text-white'
                : 'bg-ui-background-secondary hover:bg-ui-background-tertiary'
            }`}
          >
            WCAG 2.1
          </button>
          <button
            onClick={() => setContrastMode('apca')}
            className={`px-3 py-1.5 text-sm rounded transition-colors ${
              contrastMode === 'apca'
                ? 'bg-brand-primary text-white'
                : 'bg-ui-background-secondary hover:bg-ui-background-tertiary'
            }`}
          >
            APCA
          </button>
          
          <div className="relative">
            <button
              onClick={() => setShowExportMenu(!showExportMenu)}
              className="p-1.5 hover:bg-ui-background-secondary rounded transition-colors flex items-center gap-1"
              title="Export matrix"
            >
              <Download className="w-4 h-4" />
              <ChevronDown className="w-3 h-3" />
            </button>
            
            {showExportMenu && (
              <div className="absolute right-0 top-full mt-1 bg-white dark:bg-zinc-800 rounded-lg shadow-lg border border-ui-border-light z-10">
                <button
                  onClick={() => {
                    exportToCSV();
                    setShowExportMenu(false);
                  }}
                  className="block w-full px-4 py-2 text-sm text-left hover:bg-ui-background-secondary transition-colors"
                >
                  Export as CSV
                </button>
                <button
                  onClick={() => {
                    exportToJSON();
                    setShowExportMenu(false);
                  }}
                  className="block w-full px-4 py-2 text-sm text-left hover:bg-ui-background-secondary transition-colors"
                >
                  Export as JSON
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {(colors?.length || 0) > 20 && (
        <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <Info className="w-4 h-4 text-yellow-600 flex-shrink-0" />
          <p className="text-sm text-yellow-800">
            Showing first 20 colors for performance. Select fewer colors for complete analysis.
          </p>
        </div>
      )}

      <div className="overflow-auto bg-ui-background-secondary rounded-lg p-4">
        <table className="w-full">
          <thead>
            <tr>
              <th className="p-2 text-xs font-medium text-ui-foreground-secondary">vs</th>
              {displayColors.map((color, index) => (
                <th key={color?.id || `col-${index}`} className="p-2 min-w-[60px]">
                  <div className="flex flex-col items-center">
                    <div
                      className="w-6 h-6 rounded border border-ui-border-light mb-1"
                      style={{ backgroundColor: color?.hex || '#000000' }}
                    />
                    <span className="text-xs text-ui-foreground-secondary">{index + 1}</span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {displayColors.map((rowColor, rowIndex) => {
              const rowData = safeArrayAccess(contrastData, rowIndex);
              return (
                <tr key={rowColor?.id || `row-${rowIndex}`}>
                  <td className="p-2">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-6 h-6 rounded border border-ui-border-light flex-shrink-0"
                        style={{ backgroundColor: rowColor?.hex || '#000000' }}
                      />
                      <span className="text-xs text-ui-foreground-secondary">{rowIndex + 1}</span>
                    </div>
                  </td>
                  {displayColors.map((_, colIndex) => {
                    const value = rowData ? safeArrayAccess(rowData, colIndex) : undefined;
                    const level = getContrastLevel(value ?? 0);
                    const isSelected = selectedPair?.row === rowIndex && selectedPair?.col === colIndex;
                    
                    return (
                      <td key={`${rowIndex}-${colIndex}`} className="p-1">
                        <button
                          onClick={() => handleCellClick(rowIndex, colIndex)}
                          disabled={rowIndex === colIndex}
                          className={`
                            w-full h-10 rounded text-xs font-medium text-white
                            transition-all duration-200 relative
                            ${rowIndex === colIndex ? 'cursor-default' : 'cursor-pointer hover:scale-105'}
                            ${isSelected ? 'ring-2 ring-brand-primary ring-offset-2' : ''}
                            ${level.class}
                          `}
                        >
                          {rowIndex !== colIndex && isNotNullish(value) && value.toFixed(1)}
                        </button>
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Legend */}
      <div className="flex items-center gap-4 text-sm">
        <span className="font-medium">Legend:</span>
        {contrastMode === 'wcag' ? (
          <>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-green-500 rounded" />
              <span>AAA (≥7:1)</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-yellow-500 rounded" />
              <span>AA (≥4.5:1)</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-orange-500 rounded" />
              <span>AA Large (≥3:1)</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-red-500 rounded" />
              <span>Fail (&lt;3:1)</span>
            </div>
          </>
        ) : (
          <>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-green-500 rounded" />
              <span>Excellent (≥90)</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-yellow-500 rounded" />
              <span>Good (≥60)</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-orange-500 rounded" />
              <span>Fair (≥45)</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-4 bg-red-500 rounded" />
              <span>Poor (&lt;45)</span>
            </div>
          </>
        )}
      </div>

      {/* Selected Pair Details */}
      {selectedPair && (
        <div className="bg-ui-background-tertiary rounded-lg p-4">
          <h4 className="font-medium mb-2">Contrast Details</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-ui-foreground-secondary mb-1">Foreground</p>
              <div className="flex items-center gap-2">
                <div
                  className="w-8 h-8 rounded border border-ui-border-light"
                  style={{ backgroundColor: safeArrayAccess(displayColors, selectedPair.row)?.hex || '#000000' }}
                />
                <span className="text-sm font-medium">
                  {getOrDefault(
                    safeArrayAccess(displayColors, selectedPair.row)?.name,
                    safeArrayAccess(displayColors, selectedPair.row)?.hex || 'Unknown'
                  )}
                </span>
              </div>
            </div>
            <div>
              <p className="text-sm text-ui-foreground-secondary mb-1">Background</p>
              <div className="flex items-center gap-2">
                <div
                  className="w-8 h-8 rounded border border-ui-border-light"
                  style={{ backgroundColor: safeArrayAccess(displayColors, selectedPair.col)?.hex || '#000000' }}
                />
                <span className="text-sm font-medium">
                  {getOrDefault(
                    safeArrayAccess(displayColors, selectedPair.col)?.name,
                    safeArrayAccess(displayColors, selectedPair.col)?.hex || 'Unknown'
                  )}
                </span>
              </div>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-ui-border-light">
            <p className="text-sm">
              <span className="font-medium">Contrast Ratio:</span>{' '}
              {(
                safeArrayAccess(contrastData, selectedPair.row) &&
                safeArrayAccess(safeArrayAccess(contrastData, selectedPair.row)!, selectedPair.col) !== undefined
              ) ? (
                safeArrayAccess(safeArrayAccess(contrastData, selectedPair.row)!, selectedPair.col)!.toFixed(2)
              ) : (
                '0.00'
              )}:1
            </p>
            <p className="text-sm text-ui-foreground-secondary mt-1">
              {getContrastLevel(
                safeArrayAccess(contrastData, selectedPair.row) &&
                safeArrayAccess(safeArrayAccess(contrastData, selectedPair.row)!, selectedPair.col) !== undefined
                  ? safeArrayAccess(safeArrayAccess(contrastData, selectedPair.row)!, selectedPair.col)!
                  : 0
              ).label}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}