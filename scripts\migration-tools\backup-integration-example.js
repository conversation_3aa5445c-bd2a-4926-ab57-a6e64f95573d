/**
 * @file backup-integration-example.js
 * @description Example integration of backup system with migration workflow
 * 
 * This example demonstrates how to:
 * - Create backups before migrations
 * - Implement safe migration rollback
 * - Integrate with existing migration tools
 * - Handle migration failures gracefully
 * - Create checkpoint backups during multi-step migrations
 */

const { BackupManager } = require('./backup-rollback');
const { DataIntegrityChecker } = require('./data-integrity-checker');
const { SchemaInspector } = require('./schema-inspector');
const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

function getUserDataPath() {
  return path.join(require('os').homedir(), 'Library', 'Application Support', 'chroma-sync');
}

function getDbPath() {
  return path.join(getUserDataPath(), 'chromasync.db');
}

class SafeMigrationRunner {
  constructor(options = {}) {
    this.dbPath = getDbPath();
    this.backupManager = new BackupManager(this.dbPath, {
      compress: true,
      validate: true,
      ...options
    });
    this.integrityChecker = new DataIntegrityChecker(this.dbPath);
    this.schemaInspector = new SchemaInspector(this.dbPath);
    this.migrationLog = [];
    this.currentBackup = null;
    this.rollbackBackup = null;
  }

  /**
   * Initialize migration environment
   */
  async initialize() {
    console.log('🔧 Initializing safe migration environment...');
    
    // Connect to database
    if (!this.backupManager.connect()) {
      throw new Error('Could not connect to database');
    }
    
    // Create pre-migration backup
    console.log('💾 Creating pre-migration backup...');
    this.rollbackBackup = await this.backupManager.createFullBackup('pre-migration', 'initialization');
    
    // Run initial integrity check
    console.log('🔍 Running initial integrity check...');
    if (!this.integrityChecker.connect()) {
      throw new Error('Could not connect to integrity checker');
    }
    
    const integrityReport = this.integrityChecker.generateReport();
    
    if (integrityReport.summary.criticalIssues > 0) {
      console.warn(`⚠️  Found ${integrityReport.summary.criticalIssues} critical issues`);
      console.warn('💡 Consider running integrity fixes before migration');
    }
    
    this.integrityChecker.disconnect();
    
    console.log('✅ Migration environment initialized');
  }

  /**
   * Create checkpoint backup during migration
   */
  async createCheckpoint(phase, description = null) {
    console.log(`📍 Creating checkpoint: ${phase}`);
    
    this.currentBackup = await this.backupManager.createFullBackup(`checkpoint-${phase}`, phase);
    
    this.migrationLog.push({
      timestamp: new Date().toISOString(),
      phase: phase,
      description: description,
      backupId: this.currentBackup.id,
      backupName: this.currentBackup.name
    });
    
    console.log(`✅ Checkpoint created: ${this.currentBackup.name}`);
    return this.currentBackup;
  }

  /**
   * Create incremental backup for specific changes
   */
  async createIncrementalBackup(phase, tables, description = null) {
    console.log(`📦 Creating incremental backup: ${phase}`);
    
    const backup = await this.backupManager.createIncrementalBackup(`incremental-${phase}`, phase, tables);
    
    this.migrationLog.push({
      timestamp: new Date().toISOString(),
      phase: phase,
      description: description,
      backupId: backup.id,
      backupName: backup.name,
      type: 'incremental',
      tables: tables
    });
    
    console.log(`✅ Incremental backup created: ${backup.name}`);
    return backup;
  }

  /**
   * Execute migration step with automatic rollback on failure
   */
  async executeMigrationStep(stepName, migrationFunction, options = {}) {
    const {
      createCheckpoint = true,
      rollbackOnError = true,
      validateAfter = true,
      tables = null
    } = options;

    console.log(`\n🔄 Executing migration step: ${stepName}`);
    
    // Create checkpoint before step
    let checkpoint = null;
    if (createCheckpoint) {
      checkpoint = await this.createCheckpoint(stepName, `Before ${stepName}`);
    }

    try {
      // Execute the migration function
      const result = await migrationFunction();
      
      // Validate database after migration
      if (validateAfter) {
        await this.validateAfterMigration(stepName);
      }
      
      // Create incremental backup of affected tables
      if (tables && tables.length > 0) {
        await this.createIncrementalBackup(`after-${stepName}`, tables, `After ${stepName}`);
      }
      
      console.log(`✅ Migration step completed: ${stepName}`);
      return result;
      
    } catch (error) {
      console.error(`❌ Migration step failed: ${stepName}`);
      console.error(`   Error: ${error.message}`);
      
      if (rollbackOnError && checkpoint) {
        console.log('🔄 Rolling back to checkpoint...');
        await this.rollbackToCheckpoint(checkpoint);
      }
      
      throw error;
    }
  }

  /**
   * Validate database after migration step
   */
  async validateAfterMigration(stepName) {
    console.log(`🔍 Validating database after ${stepName}...`);
    
    if (!this.integrityChecker.connect()) {
      throw new Error('Could not connect to integrity checker');
    }
    
    try {
      const report = this.integrityChecker.generateReport();
      
      if (report.summary.criticalIssues > 0) {
        console.warn(`⚠️  Found ${report.summary.criticalIssues} critical issues after ${stepName}`);
        
        // Save report for debugging
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const reportPath = path.join(this.backupManager.backupDir, `integrity-report-${stepName}-${timestamp}.json`);
        this.integrityChecker.saveReport(reportPath);
        
        throw new Error(`Database integrity issues found after ${stepName}`);
      }
      
      console.log(`✅ Database validation passed after ${stepName}`);
      
    } finally {
      this.integrityChecker.disconnect();
    }
  }

  /**
   * Rollback to specific checkpoint
   */
  async rollbackToCheckpoint(checkpoint) {
    console.log(`🔄 Rolling back to checkpoint: ${checkpoint.name}`);
    
    const backupPath = path.join(this.backupManager.backupDir, checkpoint.name);
    const compressedPath = backupPath + '.gz';
    const finalPath = fs.existsSync(compressedPath) ? compressedPath : backupPath;
    
    await this.backupManager.restoreDatabase(finalPath, {
      createBackupBeforeRestore: true,
      validateBeforeRestore: true,
      validateAfterRestore: true
    });
    
    console.log(`✅ Rollback completed to checkpoint: ${checkpoint.name}`);
  }

  /**
   * Rollback to pre-migration state
   */
  async rollbackToPreMigration() {
    if (!this.rollbackBackup) {
      throw new Error('No pre-migration backup available');
    }
    
    console.log('🔄 Rolling back to pre-migration state...');
    
    const backupPath = path.join(this.backupManager.backupDir, this.rollbackBackup.name);
    const compressedPath = backupPath + '.gz';
    const finalPath = fs.existsSync(compressedPath) ? compressedPath : backupPath;
    
    await this.backupManager.restoreDatabase(finalPath, {
      createBackupBeforeRestore: false,
      validateBeforeRestore: true,
      validateAfterRestore: true
    });
    
    console.log('✅ Rollback to pre-migration state completed');
  }

  /**
   * Complete migration process
   */
  async completeMigration() {
    console.log('🎉 Completing migration process...');
    
    // Create final backup
    const finalBackup = await this.backupManager.createFullBackup('post-migration', 'completion');
    
    // Final validation
    await this.validateAfterMigration('final');
    
    // Generate migration report
    const report = this.generateMigrationReport();
    
    // Save migration log
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const logPath = path.join(this.backupManager.backupDir, `migration-log-${timestamp}.json`);
    fs.writeFileSync(logPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      migrationLog: this.migrationLog,
      finalBackup: finalBackup,
      report: report
    }, null, 2));
    
    console.log('✅ Migration completed successfully');
    console.log(`📋 Migration log saved: ${logPath}`);
    
    return report;
  }

  /**
   * Generate migration report
   */
  generateMigrationReport() {
    const report = {
      timestamp: new Date().toISOString(),
      status: 'completed',
      totalSteps: this.migrationLog.length,
      backupsCreated: this.migrationLog.length,
      rollbackBackup: this.rollbackBackup ? this.rollbackBackup.name : null,
      steps: this.migrationLog.map(log => ({
        phase: log.phase,
        timestamp: log.timestamp,
        description: log.description,
        backup: log.backupName
      }))
    };
    
    console.log('\n📊 Migration Report:');
    console.log('===================');
    console.log(`Status: ${report.status}`);
    console.log(`Total Steps: ${report.totalSteps}`);
    console.log(`Backups Created: ${report.backupsCreated}`);
    console.log(`Rollback Backup: ${report.rollbackBackup || 'N/A'}`);
    
    return report;
  }

  /**
   * Cleanup migration environment
   */
  async cleanup() {
    console.log('🧹 Cleaning up migration environment...');
    
    if (this.backupManager) {
      this.backupManager.disconnect();
    }
    
    if (this.integrityChecker) {
      this.integrityChecker.disconnect();
    }
    
    console.log('✅ Migration environment cleaned up');
  }
}

// Example usage scenarios
class MigrationExamples {
  constructor() {
    this.migrationRunner = new SafeMigrationRunner();
  }

  /**
   * Example: UUID Migration with Checkpoints
   */
  async runUUIDMigration() {
    console.log('🚀 Starting UUID Migration Example');
    console.log('==================================\n');

    try {
      await this.migrationRunner.initialize();
      
      // Step 1: Add UUID columns
      await this.migrationRunner.executeMigrationStep(
        'add-uuid-columns',
        async () => {
          console.log('   Adding UUID columns...');
          // Migration logic would go here
          return { tablesModified: ['organizations', 'products', 'colors'] };
        },
        {
          tables: ['organizations', 'products', 'colors'],
          validateAfter: true
        }
      );
      
      // Step 2: Populate UUID values
      await this.migrationRunner.executeMigrationStep(
        'populate-uuids',
        async () => {
          console.log('   Populating UUID values...');
          // Migration logic would go here
          return { recordsUpdated: 150 };
        },
        {
          tables: ['organizations', 'products', 'colors'],
          validateAfter: true
        }
      );
      
      // Step 3: Update foreign key references
      await this.migrationRunner.executeMigrationStep(
        'update-foreign-keys',
        async () => {
          console.log('   Updating foreign key references...');
          // Migration logic would go here
          return { relationshipsUpdated: 45 };
        },
        {
          tables: ['product_colors', 'organization_members'],
          validateAfter: true
        }
      );
      
      // Step 4: Drop old columns
      await this.migrationRunner.executeMigrationStep(
        'drop-old-columns',
        async () => {
          console.log('   Dropping old integer columns...');
          // Migration logic would go here
          return { columnsDropped: 3 };
        },
        {
          createCheckpoint: true,
          validateAfter: true
        }
      );
      
      const report = await this.migrationRunner.completeMigration();
      console.log('\n🎉 UUID Migration completed successfully!');
      
      return report;
      
    } catch (error) {
      console.error('\n❌ Migration failed, attempting rollback...');
      
      try {
        await this.migrationRunner.rollbackToPreMigration();
        console.log('✅ Successfully rolled back to pre-migration state');
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError.message);
      }
      
      throw error;
      
    } finally {
      await this.migrationRunner.cleanup();
    }
  }

  /**
   * Example: Schema Update with Validation
   */
  async runSchemaUpdate() {
    console.log('🚀 Starting Schema Update Example');
    console.log('=================================\n');

    try {
      await this.migrationRunner.initialize();
      
      // Step 1: Add new table
      await this.migrationRunner.executeMigrationStep(
        'add-new-table',
        async () => {
          console.log('   Adding new table...');
          // Migration logic would go here
          return { tableCreated: 'user_preferences' };
        },
        {
          tables: ['user_preferences'],
          validateAfter: true
        }
      );
      
      // Step 2: Add new columns to existing table
      await this.migrationRunner.executeMigrationStep(
        'add-columns',
        async () => {
          console.log('   Adding new columns...');
          // Migration logic would go here
          return { columnsAdded: ['last_login', 'preferences_json'] };
        },
        {
          tables: ['organizations'],
          validateAfter: true
        }
      );
      
      // Step 3: Create indexes
      await this.migrationRunner.executeMigrationStep(
        'create-indexes',
        async () => {
          console.log('   Creating indexes...');
          // Migration logic would go here
          return { indexesCreated: 3 };
        },
        {
          validateAfter: true
        }
      );
      
      const report = await this.migrationRunner.completeMigration();
      console.log('\n🎉 Schema Update completed successfully!');
      
      return report;
      
    } catch (error) {
      console.error('\n❌ Schema update failed, attempting rollback...');
      
      try {
        await this.migrationRunner.rollbackToPreMigration();
        console.log('✅ Successfully rolled back to pre-migration state');
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError.message);
      }
      
      throw error;
      
    } finally {
      await this.migrationRunner.cleanup();
    }
  }

  /**
   * Example: Data Transformation with Rollback
   */
  async runDataTransformation() {
    console.log('🚀 Starting Data Transformation Example');
    console.log('=======================================\n');

    try {
      await this.migrationRunner.initialize();
      
      // Step 1: Transform color data
      await this.migrationRunner.executeMigrationStep(
        'transform-colors',
        async () => {
          console.log('   Transforming color data...');
          // Simulate a transformation that might fail
          if (Math.random() > 0.7) {
            throw new Error('Random transformation failure for demo');
          }
          return { recordsTransformed: 50 };
        },
        {
          tables: ['colors'],
          validateAfter: true,
          rollbackOnError: true
        }
      );
      
      // Step 2: Update product associations
      await this.migrationRunner.executeMigrationStep(
        'update-associations',
        async () => {
          console.log('   Updating product associations...');
          // Migration logic would go here
          return { associationsUpdated: 25 };
        },
        {
          tables: ['product_colors'],
          validateAfter: true
        }
      );
      
      const report = await this.migrationRunner.completeMigration();
      console.log('\n🎉 Data Transformation completed successfully!');
      
      return report;
      
    } catch (error) {
      console.error('\n❌ Data transformation failed');
      console.error(`   Error: ${error.message}`);
      
      // Note: rollback would have been handled automatically by executeMigrationStep
      console.log('✅ Automatic rollback completed');
      
      throw error;
      
    } finally {
      await this.migrationRunner.cleanup();
    }
  }
}

// CLI usage
if (require.main === module) {
  const examples = new MigrationExamples();
  const exampleType = process.argv[2] || 'uuid';
  
  async function runExample() {
    try {
      switch (exampleType) {
        case 'uuid':
          await examples.runUUIDMigration();
          break;
        case 'schema':
          await examples.runSchemaUpdate();
          break;
        case 'data':
          await examples.runDataTransformation();
          break;
        default:
          console.log(`
ChromaSync Migration Integration Examples
========================================

Usage: node backup-integration-example.js <example-type>

Example Types:
  uuid     - UUID migration with checkpoints
  schema   - Schema update with validation
  data     - Data transformation with rollback

Examples:
  node backup-integration-example.js uuid
  node backup-integration-example.js schema
  node backup-integration-example.js data
          `);
          break;
      }
      
      process.exit(0);
      
    } catch (error) {
      console.error(`❌ Example failed: ${error.message}`);
      process.exit(1);
    }
  }

  runExample();
}

module.exports = { SafeMigrationRunner, MigrationExamples };