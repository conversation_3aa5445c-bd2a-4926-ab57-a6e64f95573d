/**
 * @file KeyboardShortcutsHelp.tsx
 * @description Keyboard shortcuts help dropdown for color comparison
 */

import React, { useState, useRef, useEffect } from 'react';
import { Keyboard, X } from 'lucide-react';

const KeyboardShortcutsHelp: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const shortcuts = [
    { keys: 'Cmd/Ctrl + 1-3', action: 'Switch library tabs' },
    { keys: 'Cmd/Ctrl + 4-6', action: 'Switch analysis tabs' },
    { keys: 'Space', action: 'Quick copy color' },
    { keys: 'Delete', action: 'Remove color' },
    { keys: 'Cmd/Ctrl + R', action: 'Regenerate harmony' },
    { keys: 'Cmd/Ctrl + E', action: 'Export analysis' },
    { keys: 'Arrow keys', action: 'Navigate grid' },
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className='relative' ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className='p-1 rounded hover:bg-ui-background-hover transition-colors'
        title='Keyboard shortcuts'
      >
        <Keyboard
          size={16}
          className='text-ui-foreground-tertiary hover:text-ui-foreground-primary'
        />
      </button>

      {isOpen && (
        <div className='absolute right-0 top-full mt-1 w-64 bg-ui-background-primary border border-ui-border rounded-lg shadow-lg z-50'>
          <div className='p-3'>
            <div className='flex items-center justify-between mb-3'>
              <h3 className='text-sm font-medium text-ui-foreground-primary'>
                Keyboard Shortcuts
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className='p-1 hover:bg-ui-background-hover rounded transition-colors'
              >
                <X size={14} className='text-ui-foreground-tertiary' />
              </button>
            </div>
            <div className='space-y-2'>
              {shortcuts.map((shortcut, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between gap-3 text-xs'
                >
                  <kbd className='px-2 py-1 bg-ui-background-secondary text-ui-foreground-primary rounded text-xs font-mono whitespace-nowrap border border-ui-border'>
                    {shortcut.keys}
                  </kbd>
                  <span className='text-ui-foreground-secondary flex-1 text-right'>
                    {shortcut.action}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default KeyboardShortcutsHelp;
