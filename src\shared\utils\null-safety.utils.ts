/**
 * @file null-safety.utils.ts
 * @description Null safety utilities specifically for ChromaSync codebase patterns
 */

import { isNotNullish, getOrThrow, safeArrayAccess } from '../types/type-guards';

/**
 * Safe array first element access with fallback
 */
export function firstOrDefault<T>(array: T[] | null | undefined, defaultValue: T): T {
  return safeArrayAccess(array, 0) ?? defaultValue;
}

/**
 * Safe array first element access with error if empty
 */
export function firstOrThrow<T>(array: T[] | null | undefined, errorMessage?: string): T {
  const first = safeArrayAccess(array, 0);
  if (first === undefined) {
    throw new Error(errorMessage || 'Array is empty or undefined');
  }
  return first;
}

/**
 * Safe validation error access - returns first error or default message
 */
export function getFirstValidationError(
  errors: Array<{ message: string }> | null | undefined,
  defaultMessage = 'Validation failed'
): string {
  const firstError = safeArrayAccess(errors, 0);
  return firstError?.message ?? defaultMessage;
}

/**
 * Safe organization ID validation
 */
export function requireValidOrganizationId(orgId: string | null | undefined): string {
  if (!orgId || orgId.trim() === '') {
    throw new Error('Organization ID is required');
  }
  return orgId.trim();
}

/**
 * Safe user ID validation
 */
export function requireValidUserId(userId: string | null | undefined): string {
  if (!userId || userId.trim() === '') {
    throw new Error('User ID is required');
  }
  return userId.trim();
}

/**
 * Safe external ID access from organization
 */
export function getOrganizationExternalId(org: { external_id?: string | null } | null | undefined): string {
  if (!org || !org.external_id) {
    throw new Error('Organization external ID is required');
  }
  return org.external_id;
}

/**
 * Safe CSV parsing for gradient colors
 */
export function parseGradientColorsCSV(csvString: string | null | undefined): string[] {
  if (!csvString || csvString.trim() === '') {
    return [];
  }
  
  return csvString
    .split(',')
    .map(color => color.trim())
    .filter(color => color.length > 0);
}

/**
 * Safe color space access from JSON
 */
export function parseColorSpaces(colorSpacesJson: string | null | undefined): Record<string, any> {
  if (!colorSpacesJson) {
    return {};
  }
  
  try {
    const parsed = JSON.parse(colorSpacesJson);
    return typeof parsed === 'object' && parsed !== null ? parsed : {};
  } catch {
    return {};
  }
}

/**
 * Safe product relationship access
 */
export function getProductRelationships(
  relationships: Array<any> | null | undefined
): Array<{ product_id: string; product_name: string; display_order: number }> {
  if (!Array.isArray(relationships)) {
    return [];
  }
  
  return relationships
    .filter(rel => rel && typeof rel === 'object')
    .filter(rel => rel.product_id && rel.product_name)
    .map(rel => ({
      product_id: String(rel.product_id),
      product_name: String(rel.product_name),
      display_order: typeof rel.display_order === 'number' ? rel.display_order : 0
    }));
}

/**
 * Safe database row to color entry conversion with null safety
 */
export function mapColorRowSafely(row: any): {
  id: string;
  name: string;
  hex: string;
  code: string;
  organizationId: string;
} {
  if (!row || typeof row !== 'object') {
    throw new Error('Invalid color row data');
  }
  
  return {
    id: getOrThrow(row.id, 'Color ID is required'),
    name: getOrThrow(row.display_name || row.name, 'Color name is required'),
    hex: getOrThrow(row.hex, 'Color hex value is required'),
    code: getOrThrow(row.code, 'Color code is required'),
    organizationId: getOrThrow(row.organization_id, 'Organization ID is required')
  };
}

/**
 * Safe error handling for async operations
 */
export async function safeAsyncOperation<T>(
  operation: () => Promise<T>,
  errorContext?: string
): Promise<{ success: true; data: T } | { success: false; error: string }> {
  try {
    const data = await operation();
    return { success: true, data };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const fullMessage = errorContext ? `${errorContext}: ${errorMessage}` : errorMessage;
    return { success: false, error: fullMessage };
  }
}

/**
 * Safe sync operation with user and organization validation
 */
export function validateSyncContext(
  userId: string | null | undefined,
  organizationId: string | null | undefined
): { userId: string; organizationId: string } {
  const validUserId = requireValidUserId(userId);
  const validOrgId = requireValidOrganizationId(organizationId);
  
  return { userId: validUserId, organizationId: validOrgId };
}

/**
 * Safe batch operation result processing
 */
export function processBatchResults<T>(
  results: Array<{ success: boolean; data?: T; error?: string }> | null | undefined
): {
  successful: T[];
  failed: Array<{ error: string; index: number }>;
  total: number;
} {
  if (!Array.isArray(results)) {
    return { successful: [], failed: [], total: 0 };
  }
  
  const successful: T[] = [];
  const failed: Array<{ error: string; index: number }> = [];
  
  results.forEach((result, index) => {
    if (result && typeof result === 'object') {
      if (result.success && result.data !== undefined) {
        successful.push(result.data);
      } else {
        failed.push({
          error: result.error || 'Unknown error',
          index
        });
      }
    } else {
      failed.push({
        error: 'Invalid result format',
        index
      });
    }
  });
  
  return { successful, failed, total: results.length };
}