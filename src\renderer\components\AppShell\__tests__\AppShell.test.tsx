/**
 * @file AppShell.test.tsx
 * @description Tests for the main AppShell component
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AppShell } from '../AppShell';

// Mock all the providers and components
vi.mock('../../context/TokenProvider', () => ({
  TokenProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='token-provider'>{children}</div>
  ),
}));

vi.mock('../../context/ColorBlindnessContext', () => ({
  ColorBlindnessProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='colorblindness-provider'>{children}</div>
  ),
}));

vi.mock('../AppInitializer', () => ({
  AppInitializer: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='app-initializer'>{children}</div>
  ),
}));

vi.mock('./AppProviders', () => ({
  AppProviders: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='app-providers'>{children}</div>
  ),
}));

vi.mock('./AppRouter', () => ({
  AppRouter: () => <div data-testid='app-router'>App Router</div>,
}));

vi.mock('./LicenseGuard', () => ({
  LicenseGuard: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='license-guard'>{children}</div>
  ),
}));

vi.mock('./AppStartup', () => ({
  AppStartup: ({ stage, progress }: { stage: string; progress: number }) => (
    <div data-testid='app-startup'>
      Startup - Stage: {stage}, Progress: {progress}%
    </div>
  ),
}));

vi.mock('./DebugPanel', () => ({
  DebugPanel: ({
    isOpen,
    onClose,
  }: {
    isOpen: boolean;
    onClose: () => void;
  }) => (
    <div data-testid='debug-panel' data-open={isOpen}>
      Debug Panel
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

vi.mock('./AppModals', () => ({
  AppModals: () => <div data-testid='app-modals'>App Modals</div>,
}));

// Mock the hook
const mockUseAppInitialization = vi.fn();
vi.mock('./hooks/useAppInitialization', () => ({
  useAppInitialization: () => mockUseAppInitialization(),
}));

describe('AppShell', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Set default mock values
    mockUseAppInitialization.mockReturnValue({
      isReady: true,
      stage: 'complete',
      progress: 100,
      licenseStatus: 'valid',
      debugPanelOpen: false,
      setDebugPanelOpen: vi.fn(),
    });

    // Mock environment variable
    process.env.NODE_ENV = 'development';
  });

  it('should render the main app structure when ready', () => {
    render(<AppShell />);

    // Check that all main components are rendered
    expect(screen.getByTestId('app-providers')).toBeInTheDocument();
    expect(screen.getByTestId('token-provider')).toBeInTheDocument();
    expect(screen.getByTestId('colorblindness-provider')).toBeInTheDocument();
    expect(screen.getByTestId('app-initializer')).toBeInTheDocument();
    expect(screen.getByTestId('license-guard')).toBeInTheDocument();
    expect(screen.getByTestId('app-router')).toBeInTheDocument();
    expect(screen.getByTestId('app-modals')).toBeInTheDocument();
  });

  it('should render startup screen when not ready', () => {
    mockUseAppInitialization.mockReturnValue({
      isReady: false,
      stage: 'initializing',
      progress: 50,
      licenseStatus: 'checking',
      debugPanelOpen: false,
      setDebugPanelOpen: vi.fn(),
    });

    render(<AppShell />);

    expect(screen.getByTestId('app-startup')).toBeInTheDocument();
    expect(
      screen.getByText('Startup - Stage: initializing, Progress: 50%')
    ).toBeInTheDocument();

    // Main app components should not be rendered
    expect(screen.queryByTestId('app-router')).not.toBeInTheDocument();
  });

  it('should render debug panel in development mode', () => {
    process.env.NODE_ENV = 'development';

    mockUseAppInitialization.mockReturnValue({
      isReady: true,
      stage: 'complete',
      progress: 100,
      licenseStatus: 'valid',
      debugPanelOpen: true,
      setDebugPanelOpen: vi.fn(),
    });

    render(<AppShell />);

    const debugPanel = screen.getByTestId('debug-panel');
    expect(debugPanel).toBeInTheDocument();
    expect(debugPanel).toHaveAttribute('data-open', 'true');
  });

  it('should not render debug panel in production mode', () => {
    process.env.NODE_ENV = 'production';

    mockUseAppInitialization.mockReturnValue({
      isReady: true,
      stage: 'complete',
      progress: 100,
      licenseStatus: 'valid',
      debugPanelOpen: true,
      setDebugPanelOpen: vi.fn(),
    });

    render(<AppShell />);

    expect(screen.queryByTestId('debug-panel')).not.toBeInTheDocument();
  });

  it('should render skip to main content link for accessibility', () => {
    render(<AppShell />);

    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toBeInTheDocument();
    expect(skipLink).toHaveAttribute('href', '#main-content');
    expect(skipLink).toHaveClass('skip-to-main');
  });

  it('should handle debug panel close', () => {
    const mockSetDebugPanelOpen = vi.fn();

    mockUseAppInitialization.mockReturnValue({
      isReady: true,
      stage: 'complete',
      progress: 100,
      licenseStatus: 'valid',
      debugPanelOpen: true,
      setDebugPanelOpen: mockSetDebugPanelOpen,
    });

    render(<AppShell />);

    const closeButton = screen.getByText('Close');
    closeButton.click();

    expect(mockSetDebugPanelOpen).toHaveBeenCalledWith(false);
  });

  it('should pass correct props to child components', () => {
    const mockStage = 'loading-data';
    const mockProgress = 75;
    const mockLicenseStatus = 'expired';

    mockUseAppInitialization.mockReturnValue({
      isReady: false,
      stage: mockStage,
      progress: mockProgress,
      licenseStatus: mockLicenseStatus,
      debugPanelOpen: false,
      setDebugPanelOpen: vi.fn(),
    });

    render(<AppShell />);

    expect(
      screen.getByText(
        `Startup - Stage: ${mockStage}, Progress: ${mockProgress}%`
      )
    ).toBeInTheDocument();
  });

  it('should maintain proper component hierarchy', () => {
    render(<AppShell />);

    const appProviders = screen.getByTestId('app-providers');
    const tokenProvider = screen.getByTestId('token-provider');
    const colorBlindnessProvider = screen.getByTestId(
      'colorblindness-provider'
    );
    const appInitializer = screen.getByTestId('app-initializer');
    const licenseGuard = screen.getByTestId('license-guard');

    // Check hierarchy (AppProviders should contain TokenProvider, etc.)
    expect(appProviders).toContainElement(tokenProvider);
    expect(tokenProvider).toContainElement(colorBlindnessProvider);
    expect(colorBlindnessProvider).toContainElement(appInitializer);
    expect(appInitializer).toContainElement(licenseGuard);
    expect(licenseGuard).toContainElement(screen.getByTestId('app-router'));
  });
});
