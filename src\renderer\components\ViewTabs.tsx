/**
 * @file ViewTabs.tsx
 * @description Apple-inspired segmented control for switching between table and swatch views
 */

import { useTokens } from '../hooks/useTokens';
import { useColorStore } from '../store/color.store';
import { useRef, useState } from 'react';
import { useClickOutside } from '../utils/useClickOutside';
import { useModal } from '../hooks/useModal';
import { AlertModal } from './ui/Modal/AlertModal';

interface ViewTabsProps {
  activeView: 'table' | 'swatches' | 'codes' | 'products';
  onViewChange: (mode: 'table' | 'swatches' | 'codes' | 'products') => void;
}

export default function ViewTabs({ activeView, onViewChange }: ViewTabsProps) {
  const tokens = useTokens();
  const { importColors, exportColors, clearColors } = useColorStore();
  const { alertState, closeAlert, showSuccess, showError } = useModal();
  const [menuOpen, setMenuOpen] = useState(false);
  const [clearDialogOpen, setClearDialogOpen] = useState(false);
  const [clearConfirmText, setClearConfirmText] = useState('');
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const clearDialogRef = useRef<HTMLDivElement>(null);
  const importDialogRef = useRef<HTMLDivElement>(null);
  const exportDialogRef = useRef<HTMLDivElement>(null);

  // Use a simple container for just the segmented control
  const containerClasses = "w-full";

  // Segmented control container - full width
  const segmentedControlClasses = "inline-flex rounded-[var(--radius-lg)] bg-[var(--color-ui-background-tertiary)] p-[var(--spacing-1)] w-full";

  // Get tab button classes based on active state
  const getTabClasses = (isActive: boolean) => {
    return `flex-1 px-[var(--spacing-2)] py-[calc(var(--spacing-1.5))] text-[var(--font-size-sm)] font-[var(--font-weight-medium)] rounded-[var(--radius-md)] transition-all text-center ${
      isActive
        ? `bg-[var(--color-ui-background-primary)] text-[var(--color-ui-foreground-primary)] shadow-sm`
        : 'text-[var(--color-ui-foreground-secondary)] hover:text-[var(--color-ui-foreground-primary)]'
    }`;
  };

  // @ts-ignore - Intentionally unused
  // Control button class - keeping this for dialog buttons
  const _controlButtonClass = "p-2 rounded-md bg-ui-background-tertiary dark:bg-zinc-800 text-ui-foreground-primary dark:text-white hover:bg-ui-background-secondary dark:hover:bg-zinc-700 transition-colors";

  // @ts-ignore - Intentionally unused
  // Handle import
  const _handleImport = async () => {
    setImportDialogOpen(true);
    setMenuOpen(false);
  };

  const [importMode, setImportMode] = useState<'replace' | 'merge'>('replace');
  const [importFormat, setImportFormat] = useState<'json' | 'csv'>('json');

  const handleImportConfirm = async () => {
    try {
      const result = await importColors(importMode);
      setImportDialogOpen(false);
      if (result.success) {
        showSuccess(result.message || 'Colors imported successfully');
      }
    } catch (error) {
      console.error('Import failed:', error);
      showError(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setImportDialogOpen(false);
    }
  };

  const handleImportCancel = () => {
    setImportDialogOpen(false);
  };

  // @ts-ignore - Intentionally unused
  // Handle export
  const _handleExport = async () => {
    setExportDialogOpen(true);
    setMenuOpen(false);
  };

  const [exportFormat, setExportFormat] = useState<'json' | 'csv'>('json');

  const handleExportConfirm = async () => {
    try {
      const result = await exportColors(undefined, exportFormat);
      setExportDialogOpen(false);
      if (result.success) {
        showSuccess(result.message || `Colors exported successfully as ${exportFormat.toUpperCase()}`);
      }
    } catch (error) {
      console.error('Export failed:', error);
      showError(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setExportDialogOpen(false);
    }
  };

  const handleExportCancel = () => {
    setExportDialogOpen(false);
  };

  // @ts-ignore - Intentionally unused
  // Handle clear
  const _handleClear = async () => {
    setClearDialogOpen(true);
    setMenuOpen(false);
  };

  const handleConfirmClear = async () => {
    if (clearConfirmText.toLowerCase() === 'clear all data') {
      try {
        await clearColors();
        setClearDialogOpen(false);
        setClearConfirmText('');
      } catch (error) {
        console.error('Clear all failed:', error);
      }
    }
  };

  const handleCancelClear = () => {
    setClearDialogOpen(false);
    setClearConfirmText('');
  };

  // Use shared useClickOutside hook for dialogs and menu
  useClickOutside(menuRef, () => setMenuOpen(false), menuOpen);
  useClickOutside(clearDialogRef, () => {
    setClearDialogOpen(false);
    setClearConfirmText('');
  }, clearDialogOpen);
  useClickOutside(importDialogRef, () => setImportDialogOpen(false), importDialogOpen);
  useClickOutside(exportDialogRef, () => setExportDialogOpen(false), exportDialogOpen);

  return (
    <div className={containerClasses}>
      {/* Apple-style segmented control */}
      <nav className={segmentedControlClasses} style={{
        transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`
      }} role="tablist" aria-label="View options">
        <button
          className={getTabClasses(activeView === 'table')}
          onClick={() => onViewChange('table')}
          role="tab"
          aria-selected={activeView === 'table'}
          data-testid="view-tab-table"
          title="Detailed view of all color information in table format"
        >
          <div className="flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M3 10h18M3 14h18M3 6h18M3 18h18"
              />
            </svg>
            <span>Details</span>
          </div>
        </button>
        <button
          className={getTabClasses(activeView === 'codes')}
          onClick={() => onViewChange('codes')}
          role="tab"
          aria-selected={activeView === 'codes'}
          data-testid="view-tab-codes"
          title="View colors organized by their reference codes"
        >
          <div className="flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
              />
            </svg>
            <span className="whitespace-nowrap text-[var(--font-size-xs)]">Codes</span>
          </div>
        </button>
        <button
          className={getTabClasses(activeView === 'swatches')}
          onClick={() => onViewChange('swatches')}
          role="tab"
          aria-selected={activeView === 'swatches'}
          data-testid="view-tab-swatches"
          title="Visual grid view of color swatches"
        >
          <div className="flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <span>Swatches</span>
          </div>
        </button>
        <button
          className={getTabClasses(activeView === 'products')}
          onClick={() => onViewChange('products')}
          role="tab"
          aria-selected={activeView === 'products'}
          data-testid="view-tab-products"
          title="Organize colors by product"
        >
          <div className="flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"
              />
            </svg>
            <span>Products</span>
          </div>
        </button>
      </nav>

      {/* Clear All Dialog */}
      {clearDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <div
            ref={clearDialogRef}
            className="bg-ui-background-primary dark:bg-zinc-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4"
          >
            <h3 className="text-lg font-medium mb-4 text-ui-foreground-primary dark:text-white">
              Clear All Data
            </h3>
            <div className="mb-6">
              <p className="text-ui-foreground-primary dark:text-gray-300 mb-4">
                Are you sure you want to clear all color data? This cannot be undone.
              </p>
              <p className="text-ui-foreground-primary dark:text-gray-300 mb-4">
                To confirm, please type <span className="font-bold">clear all data</span> below:
              </p>
              <input
                type="text"
                value={clearConfirmText}
                onChange={(e) => setClearConfirmText(e.target.value)}
                placeholder="Type 'clear all data' to confirm"
                className="w-full px-3 py-2 bg-ui-background-tertiary dark:bg-zinc-700 border border-ui-border-light dark:border-zinc-600 rounded-md text-ui-foreground-primary dark:text-white placeholder-ui-foreground-tertiary dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-primary"
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelClear}
                className="px-4 py-2 text-[var(--font-size-sm)] font-[var(--font-weight-medium)] text-ui-foreground-primary dark:text-white bg-ui-background-tertiary dark:bg-zinc-700 hover:bg-ui-background-secondary dark:hover:bg-zinc-600 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmClear}
                className={`px-4 py-2 text-[var(--font-size-sm)] font-[var(--font-weight-medium)] text-white rounded-md transition-colors ${
                  clearConfirmText.toLowerCase() === 'clear all data'
                    ? 'bg-red-600 hover:bg-red-700'
                    : 'bg-red-300 dark:bg-red-800 cursor-not-allowed'
                }`}
                disabled={clearConfirmText.toLowerCase() !== 'clear all data'}
              >
                Clear All
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Import Dialog */}
      {importDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <div
            ref={importDialogRef}
            className="bg-ui-background-primary dark:bg-zinc-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4"
          >
            <h3 className="text-lg font-medium mb-4 text-ui-foreground-primary dark:text-white">
              Import Colors
            </h3>
            <div className="mb-6">
              <p className="text-ui-foreground-primary dark:text-gray-300 mb-4">
                Import color data from a file. This will open a file selection dialog.
              </p>

              <div className="mb-4">
                <p className="text-ui-foreground-primary dark:text-gray-300 mb-2 font-medium">Format:</p>
                <div className="flex items-center space-x-4 mb-2">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      className="form-radio h-4 w-4 text-brand-primary"
                      checked={importFormat === 'json'}
                      onChange={() => setImportFormat('json')}
                    />
                    <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">JSON</span>
                  </label>
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      className="form-radio h-4 w-4 text-brand-primary"
                      checked={importFormat === 'csv'}
                      onChange={() => setImportFormat('csv')}
                    />
                    <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">CSV</span>
                  </label>
                </div>
                <p className="text-xs text-ui-foreground-tertiary dark:text-gray-400 italic">
                  {importFormat === 'json'
                    ? 'JSON format supports full color data with all properties.'
                    : 'CSV format should have headers with at least name, pantone, and hex columns.'}
                </p>
              </div>

              <div className="mb-4">
                <p className="text-ui-foreground-primary dark:text-gray-300 mb-2 font-medium">Import Mode:</p>
                <div className="flex items-center space-x-4 mb-2">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      className="form-radio h-4 w-4 text-brand-primary"
                      checked={importMode === 'replace'}
                      onChange={() => setImportMode('replace')}
                    />
                    <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">Replace All</span>
                  </label>
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      className="form-radio h-4 w-4 text-brand-primary"
                      checked={importMode === 'merge'}
                      onChange={() => setImportMode('merge')}
                    />
                    <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">Merge</span>
                  </label>
                </div>
                <p className="text-xs text-ui-foreground-tertiary dark:text-gray-400 italic">
                  {importMode === 'replace'
                    ? 'Replace will clear all existing colors before importing.'
                    : 'Merge will add new colors while preserving existing ones.'}
                </p>
              </div>

              <p className="text-ui-foreground-primary dark:text-gray-300 mb-2">
                Supported format:
              </p>
              {importFormat === 'json' ? (
                <pre className="bg-ui-background-tertiary dark:bg-zinc-700 rounded-md p-3 mb-4 text-[var(--font-size-sm)] overflow-auto">
                  {`[
  {
    "name": "Color Name",
    "code": "19-4052",
    "hex": "#0F4C81",
    "product": "Product Name",
    "cmyk": "C:100 M:80 Y:15 K:0",
    ...
  },
  ...
]`}
                </pre>
              ) : (
                <pre className="bg-ui-background-tertiary dark:bg-zinc-700 rounded-md p-3 mb-4 text-[var(--font-size-sm)] overflow-auto">
                  {`product,name,code,hex,cmyk,rgb
Product Name,Classic Blue,19-4052,#0F4C81,"C:100 M:80 Y:15 K:0","R:15 G:76 B:129"
Product Name,Illuminating,13-0647,#F5DF4D,"C:2 M:12 Y:85 K:0","R:245 G:223 B:77"
`}
                </pre>
              )}
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleImportCancel}
                className="px-4 py-2 text-[var(--font-size-sm)] font-[var(--font-weight-medium)] text-ui-foreground-primary dark:text-white bg-ui-background-tertiary dark:bg-zinc-700 hover:bg-ui-background-secondary dark:hover:bg-zinc-600 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleImportConfirm}
                className="px-4 py-2 text-[var(--font-size-sm)] font-[var(--font-weight-medium)] text-white bg-brand-primary hover:bg-brand-primary-dark rounded-md transition-colors"
              >
                Select File
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Export Dialog */}
      {exportDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <div
            ref={exportDialogRef}
            className="bg-ui-background-primary dark:bg-zinc-800 rounded-lg shadow-xl p-6 max-w-md w-full mx-4"
          >
            <h3 className="text-lg font-medium mb-4 text-ui-foreground-primary dark:text-white">
              Export Colors
            </h3>
            <div className="mb-6">
              <p className="text-ui-foreground-primary dark:text-gray-300 mb-4">
                Export your color data. This will open a save file dialog.
              </p>

              <div className="mb-4">
                <p className="text-ui-foreground-primary dark:text-gray-300 mb-2 font-medium">Export Format:</p>
                <div className="flex items-center space-x-4 mb-2">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      className="form-radio h-4 w-4 text-brand-primary"
                      checked={exportFormat === 'json'}
                      onChange={() => setExportFormat('json')}
                    />
                    <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">JSON</span>
                  </label>
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      className="form-radio h-4 w-4 text-brand-primary"
                      checked={exportFormat === 'csv'}
                      onChange={() => setExportFormat('csv')}
                    />
                    <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">CSV</span>
                  </label>
                </div>
                <p className="text-xs text-ui-foreground-tertiary dark:text-gray-400 italic">
                  {exportFormat === 'json'
                    ? 'JSON format includes all color data and is ideal for backups.'
                    : 'CSV format is simpler and can be opened in spreadsheet applications.'}
                </p>
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleExportCancel}
                className="px-4 py-2 text-[var(--font-size-sm)] font-[var(--font-weight-medium)] text-ui-foreground-primary dark:text-white bg-ui-background-tertiary dark:bg-zinc-700 hover:bg-ui-background-secondary dark:hover:bg-zinc-600 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleExportConfirm}
                className="px-4 py-2 text-[var(--font-size-sm)] font-[var(--font-weight-medium)] text-white bg-brand-primary hover:bg-brand-primary-dark rounded-md transition-colors"
              >
                Export as {exportFormat.toUpperCase()}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Alert Modal */}
      <AlertModal
        isOpen={alertState.isOpen}
        onClose={closeAlert}
        title={alertState.title}
        message={alertState.message}
        variant={alertState.variant as 'success' | 'error' | 'warning' | 'info'}
      />
    </div>
  );
}
