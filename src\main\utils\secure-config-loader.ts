/**
 * @file secure-config-loader.ts
 * @description Enhanced secure configuration management with validation and access control
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';

// Note: Removed ES module imports for CommonJS compatibility

interface SecureAppConfig {
  ZOHO_CLIENT_ID?: string;
  ZOHO_CLIENT_SECRET?: string;
  ZOHO_ACCOUNT_ID?: string;
  ZOHO_REFRESH_TOKEN?: string;
  ZOHO_REGION?: string;
  ZOHO_SUPPORT_ALIAS?: string;
  SUPABASE_URL?: string;
  SUPABASE_ANON_KEY?: string;
  SUPABASE_SERVICE_ROLE_KEY?: string;
  SENTRY_DSN?: string;
  SENTRY_ENABLED?: boolean;
  SENTRY_SAMPLE_RATE?: string;
  SENTRY_DEBUG?: boolean;
  JWT_SECRET?: string;
  AUTH_REDIRECT_URL?: string;
  ALLOWED_ORIGIN?: string;
  CLOUDFLARE_ANALYTICS_TOKEN?: string;
}

interface ConfigAccess {
  timestamp: number;
  accessor: string;
  key: string;
}

class SecureConfigManager {
  private static instance: SecureConfigManager;
  private cachedConfig: SecureAppConfig | null = null;
  private accessLog: ConfigAccess[] = [];
  private sensitiveKeys = [
    'ZOHO_CLIENT_SECRET', 
    'ZOHO_REFRESH_TOKEN', 
    'SUPABASE_SERVICE_ROLE_KEY',
    'JWT_SECRET',
    'SENTRY_DSN',
    'CLOUDFLARE_ANALYTICS_TOKEN'
  ];

  private constructor() {}

  public static getInstance(): SecureConfigManager {
    if (!SecureConfigManager.instance) {
      SecureConfigManager.instance = new SecureConfigManager();
    }
    return SecureConfigManager.instance;
  }

  /**
   * Load configuration with security validation
   */
  public loadConfig(): SecureAppConfig {
    if (this.cachedConfig) {
      return this.cachedConfig;
    }

    // Try environment variables first (development)
    if (this.isEnvironmentConfigAvailable()) {
      console.log('[SecureConfig] Using environment variables');
      this.cachedConfig = this.loadFromEnvironment();
      this.validateConfig(this.cachedConfig);
      return this.cachedConfig;
    }

    // Try build config (production)
    try {
      const buildConfig = this.loadFromBuildConfig();
      if (buildConfig && Object.keys(buildConfig).length > 0) {
        console.log('[SecureConfig] Using build configuration');
        this.cachedConfig = buildConfig;
        this.validateConfig(this.cachedConfig);
        return this.cachedConfig;
      }
    } catch (error) {
      console.error('[SecureConfig] Failed to load build config:', error);
    }

    console.warn('[SecureConfig] No valid configuration found - services may be limited');
    this.cachedConfig = {};
    return this.cachedConfig;
  }

  /**
   * Get configuration value with access logging
   */
  public getConfigValue(key: keyof SecureAppConfig, accessor?: string): string | boolean | undefined {
    const config = this.loadConfig();
    
    // Log access to sensitive keys
    if (this.sensitiveKeys.includes(key)) {
      this.logSensitiveAccess(key, accessor || 'unknown');
    }

    return config[key];
  }

  /**
   * Get non-sensitive configuration subset for client use
   */
  public getClientSafeConfig(): Partial<SecureAppConfig> {
    const config = this.loadConfig();
    
    return {
      ZOHO_REGION: config.ZOHO_REGION,
      ZOHO_SUPPORT_ALIAS: config.ZOHO_SUPPORT_ALIAS,
      SUPABASE_URL: config.SUPABASE_URL,
      SUPABASE_ANON_KEY: config.SUPABASE_ANON_KEY, // Anon key is safe for client
      AUTH_REDIRECT_URL: config.AUTH_REDIRECT_URL,
      SENTRY_ENABLED: config.SENTRY_ENABLED,
      SENTRY_SAMPLE_RATE: config.SENTRY_SAMPLE_RATE,
      SENTRY_DEBUG: config.SENTRY_DEBUG
    };
  }

  /**
   * Validate configuration completeness and format
   */
  private validateConfig(config: SecureAppConfig): void {
    const validationErrors: string[] = [];

    // Validate Supabase configuration
    if (config.SUPABASE_URL && !this.isValidUrl(config.SUPABASE_URL)) {
      validationErrors.push('Invalid SUPABASE_URL format');
    }

    // Validate Zoho configuration
    if (config.ZOHO_REGION && !['EU', 'US'].includes(config.ZOHO_REGION)) {
      validationErrors.push('ZOHO_REGION must be either EU or US');
    }

    if (config.ZOHO_SUPPORT_ALIAS && !this.isValidEmail(config.ZOHO_SUPPORT_ALIAS)) {
      validationErrors.push('Invalid ZOHO_SUPPORT_ALIAS email format');
    }

    // Validate redirect URL
    if (config.AUTH_REDIRECT_URL && !this.isValidUrl(config.AUTH_REDIRECT_URL)) {
      validationErrors.push('Invalid AUTH_REDIRECT_URL format');
    }

    // Check for insecure configurations
    if (config.ALLOWED_ORIGIN === '*' && process.env.NODE_ENV === 'production') {
      console.warn('[SecureConfig] Warning: ALLOWED_ORIGIN is set to * in production');
    }

    if (config.JWT_SECRET === 'your_strong_secret_here') {
      console.warn('[SecureConfig] Warning: JWT_SECRET is using default value');
    }

    if (validationErrors.length > 0) {
      throw new Error(`Configuration validation failed: ${validationErrors.join(', ')}`);
    }
  }

  /**
   * Check if environment variables are available
   */
  private isEnvironmentConfigAvailable(): boolean {
    // Check for actual Supabase configuration (more reliable than Zoho placeholders)
    const hasSupabase = !!(process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY);
    const hasZoho = !!(process.env.ZOHO_CLIENT_ID && process.env.ZOHO_CLIENT_ID !== 'your-zoho-client-id');
    
    console.log('[SecureConfig] Environment check:', {
      hasSupabase,
      hasZoho,
      supabaseUrl: process.env.SUPABASE_URL ? 'present' : 'missing',
      supabaseKey: process.env.SUPABASE_ANON_KEY ? 'present' : 'missing',
      zohoId: process.env.ZOHO_CLIENT_ID
    });
    
    return hasSupabase || hasZoho;
  }

  /**
   * Load configuration from environment variables
   */
  private loadFromEnvironment(): SecureAppConfig {
    return {
      ZOHO_CLIENT_ID: process.env.ZOHO_CLIENT_ID,
      ZOHO_CLIENT_SECRET: process.env.ZOHO_CLIENT_SECRET,
      ZOHO_ACCOUNT_ID: process.env.ZOHO_ACCOUNT_ID,
      ZOHO_REFRESH_TOKEN: process.env.ZOHO_REFRESH_TOKEN,
      ZOHO_REGION: process.env.ZOHO_REGION,
      ZOHO_SUPPORT_ALIAS: process.env.ZOHO_SUPPORT_ALIAS,
      SUPABASE_URL: process.env.SUPABASE_URL,
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY,
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
      SENTRY_DSN: process.env.SENTRY_DSN,
      SENTRY_ENABLED: process.env.SENTRY_ENABLED !== 'false',
      SENTRY_SAMPLE_RATE: process.env.SENTRY_SAMPLE_RATE,
      SENTRY_DEBUG: process.env.SENTRY_DEBUG === 'true',
      JWT_SECRET: process.env.JWT_SECRET,
      AUTH_REDIRECT_URL: process.env.AUTH_REDIRECT_URL,
      ALLOWED_ORIGIN: process.env.ALLOWED_ORIGIN,
      CLOUDFLARE_ANALYTICS_TOKEN: process.env.CLOUDFLARE_ANALYTICS_TOKEN
    };
  }

  /**
   * Load configuration from build config file
   */
  private loadFromBuildConfig(): SecureAppConfig | null {
    const configPath = app.isPackaged 
      ? path.join(process.resourcesPath, 'app-config.json')
      : path.join(__dirname, '../../out/app-config.json');
    
    if (!fs.existsSync(configPath)) {
      return null;
    }

    const configData = fs.readFileSync(configPath, 'utf-8');
    return JSON.parse(configData) as SecureAppConfig;
  }

  /**
   * Log access to sensitive configuration values
   */
  private logSensitiveAccess(key: string, accessor: string): void {
    const access: ConfigAccess = {
      timestamp: Date.now(),
      accessor,
      key
    };

    this.accessLog.push(access);

    // Keep only last 100 access logs to prevent memory bloat
    if (this.accessLog.length > 100) {
      this.accessLog = this.accessLog.slice(-100);
    }

    // Log warning for development
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[SecureConfig] Sensitive config access: ${key} by ${accessor}`);
    }
  }

  /**
   * Get access log for security auditing
   */
  public getAccessLog(): ConfigAccess[] {
    return [...this.accessLog]; // Return copy to prevent manipulation
  }

  /**
   * Clear cached configuration (for testing)
   */
  public clearCache(): void {
    this.cachedConfig = null;
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Generate a secure hash for configuration integrity checking
   */
  public getConfigHash(): string {
    const config = this.loadConfig();
    const configString = JSON.stringify(config, Object.keys(config).sort());
    return crypto.createHash('sha256').update(configString).digest('hex');
  }
}

// Export singleton instance
export const secureConfig = SecureConfigManager.getInstance();

// Legacy compatibility exports removed - use secureConfig directly

// New secure exports
export function getClientSafeConfig(): Partial<SecureAppConfig> {
  return secureConfig.getClientSafeConfig();
}

export function getConfigAccessLog(): ConfigAccess[] {
  return secureConfig.getAccessLog();
}

export function getConfigIntegrityHash(): string {
  return secureConfig.getConfigHash();
}