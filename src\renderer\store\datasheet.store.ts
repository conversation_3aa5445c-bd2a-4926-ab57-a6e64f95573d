/**
 * @file datasheet.store.ts
 * @description Zustand store for managing datasheets
 */

import { create } from 'zustand';
import { DatasheetEntry } from '../../shared/types/datasheet.types';

interface IpcResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  count?: number;
}

interface DatasheetState {
  // State
  datasheets: Record<string, DatasheetEntry[]>; // Keyed by productId
  isLoading: boolean;
  error: string | null;

  // Actions
  getDatasheetsByProduct: (productId: string) => Promise<DatasheetEntry[]>;
  addWebLinkToProduct: (productId: string, url: string, displayName: string) => Promise<DatasheetEntry | null>;
  removeDatasheet: (datasheetId: string) => Promise<boolean>;
  openDatasheet: (datasheetId: string) => Promise<{ success: boolean; message?: string }>;
  openDatasheetsByProduct: (productId: string) => Promise<{ success: boolean; count: number }>;
  migrateDatasheets: () => Promise<{ success: boolean; count: number }>;
  clearDatasheets: () => void;
}

export const useDatasheetStore = create<DatasheetState>((set, _get) => ({
  // Initial state
  datasheets: {},
  isLoading: false,
  error: null,

  // Get datasheets for a product
  getDatasheetsByProduct: async (productId: string) => {
    set({ isLoading: true, error: null });
    try {
      const result = await window.datasheetAPI.getByProduct({
        productId
      }) as IpcResponse<DatasheetEntry[]>;

      if (result.success && result.data) {
        // Update state with the datasheets
        set(state => ({
          datasheets: {
            ...state.datasheets,
            [productId]: result.data || []
          },
          isLoading: false
        }));
        return result.data;
      } else {
        set({
          error: result.message || 'Failed to get datasheets',
          isLoading: false
        });
        return [];
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      set({ error: message, isLoading: false });
      console.error('Error getting datasheets:', error);
      return [];
    }
  },



  // Add web link to product
  addWebLinkToProduct: async (productId: string, url: string, displayName: string) => {
    set({ isLoading: true, error: null });
    try {
      const result = await window.datasheetAPI.addWebLink({
        productId,
        url,
        displayName
      }) as IpcResponse<DatasheetEntry>;

      if (result.success && result.data) {
        // Update state with the new datasheet
        set(state => {
          const existingDatasheets = state.datasheets[productId] || [];
          return {
            datasheets: {
              ...state.datasheets,
              [productId]: [result.data!, ...existingDatasheets]
            },
            isLoading: false
          };
        });
        return result.data;
      } else {
        set({
          error: result.message || 'Failed to add web link',
          isLoading: false
        });
        return null;
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      set({ error: message, isLoading: false });
      console.error('Error adding web link:', error);
      return null;
    }
  },

  // Remove datasheet
  removeDatasheet: async (datasheetId: string) => {
    set({ isLoading: true, error: null });
    try {
      const result = await window.datasheetAPI.remove({
        datasheetId
      }) as IpcResponse<boolean>;

      if (result.success) {
        // Update state by removing the datasheet
        set(state => {
          const updatedDatasheets = { ...state.datasheets };

          // Find which product contains this datasheet
          Object.keys(updatedDatasheets).forEach(productId => {
            updatedDatasheets[productId] = updatedDatasheets[productId].filter(
              d => d.id !== datasheetId
            );
          });

          return {
            datasheets: updatedDatasheets,
            isLoading: false
          };
        });
        return true;
      } else {
        set({
          error: result.message || 'Failed to remove datasheet',
          isLoading: false
        });
        return false;
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      set({ error: message, isLoading: false });
      console.error('Error removing datasheet:', error);
      return false;
    }
  },

  // Open a datasheet
  openDatasheet: async (datasheetId: string) => {
    set({ isLoading: true, error: null });
    try {
      const result = await window.datasheetAPI.open({
        datasheetId
      }) as IpcResponse<boolean>;

      set({ isLoading: false });

      return {
        success: result.success,
        message: result.message
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      set({ error: message, isLoading: false });
      console.error('Error opening datasheet:', error);
      return {
        success: false,
        message: message
      };
    }
  },

  // Open all datasheets for a product
  openDatasheetsByProduct: async (productId: string) => {
    set({ isLoading: true, error: null });
    try {
      const result = await window.datasheetAPI.openAll({
        productId
      }) as IpcResponse<boolean>;

      set({ isLoading: false });

      return {
        success: result.success,
        count: result.count || 0
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      set({ error: message, isLoading: false });
      console.error('Error opening datasheets:', error);
      return {
        success: false,
        count: 0
      };
    }
  },

  // Migrate datasheets from old table
  migrateDatasheets: async () => {
    set({ isLoading: true, error: null });
    try {
      const result = await window.datasheetAPI.migrate() as IpcResponse<boolean>;

      set({ isLoading: false });

      return {
        success: result.success,
        count: result.count || 0
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      set({ error: message, isLoading: false });
      console.error('Error migrating datasheets:', error);
      return {
        success: false,
        count: 0
      };
    }
  },

  // Clear datasheets from store
  clearDatasheets: () => {
    set({ datasheets: {}, error: null });
  }
}));
