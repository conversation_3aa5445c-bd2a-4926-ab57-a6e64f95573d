# IPC Error Handling and Validation Utilities

This directory contains comprehensive utilities for standardizing error handling and validation across all IPC handlers in the ChromaSync application.

## Overview

The IPC error handling system provides:

- **Standardized error responses** with consistent structure
- **Comprehensive input validation** with multiple validation types
- **Automatic context validation** for authentication and organization
- **Error classification and logging** with appropriate severity levels
- **Wrapper functions** for easy integration with existing handlers
- **Business rule validation helpers** for common patterns

## Quick Start

### 1. Basic Handler with Error Handling

```typescript
import { wrap, respond } from './ipc-error-handling';
import { registerHandlerSafely } from '../ipcRegistry';

const handler = wrap.withErrorHandling(
  async (userId: string) => {
    // Your business logic here
    return await getUserById(userId);
  },
  {
    channel: 'user:get-by-id',
    requireAuth: true,
    requireOrganization: true,
    customErrorMessage: 'Failed to retrieve user'
  }
);

registerHandlerSafely(ipcMain, 'user:get-by-id', handler);
```

### 2. Handler with Input Validation

```typescript
import { wrap, validate } from './ipc-error-handling';

const handler = wrap.withValidation(
  async (request: CreateUserRequest) => {
    // Business logic - validation already passed
    return await createUser(request);
  },
  // Validation function
  (request: CreateUserRequest) => {
    const requiredValidation = validate.required(request, ['name', 'email']);
    const formatValidation = validate.formats(request, { email: 'email' });
    return validate.combine(requiredValidation, formatValidation);
  },
  {
    channel: 'user:create',
    requireAuth: true,
    customErrorMessage: 'Failed to create user'
  }
);
```

### 3. Manual Error Handling for Complex Operations

```typescript
import { validate, respond } from './ipc-error-handling';

const complexHandler = async (data: ComplexRequest): Promise<IPCResponse<Result>> => {
  try {
    // Manual context validation
    const contextValidation = await validate.fullContext();
    if (!contextValidation.isValid) {
      return respond.validationError(contextValidation);
    }

    // Complex business logic
    const result = await performComplexOperation(data);
    return respond.success(result, 'Operation completed successfully');

  } catch (error) {
    return respond.fromError(error, 'Complex operation failed');
  }
};
```

## Core Components

### Error Classification System

Errors are automatically classified into categories with appropriate severity levels:

```typescript
enum ErrorCategory {
  VALIDATION = 'validation',      // Input validation errors
  AUTHENTICATION = 'authentication', // Auth failures
  AUTHORIZATION = 'authorization',   // Permission errors
  ORGANIZATION = 'organization',     // Org context errors
  DATABASE = 'database',            // Database errors
  NETWORK = 'network',              // Network/connectivity
  BUSINESS_RULE = 'business_rule',  // Business logic violations
  SYSTEM = 'system',                // System errors
  UNKNOWN = 'unknown'               // Unclassified errors
}

enum ErrorSeverity {
  LOW = 'low',           // User recoverable, minor issues
  MEDIUM = 'medium',     // Requires user action
  HIGH = 'high',         // System errors, auth failures
  CRITICAL = 'critical'  // Database failures, crashes
}
```

### Validation Helpers

The `validate` object provides comprehensive validation functions:

```typescript
// Required field validation
const requiredResult = validate.required(data, ['name', 'email']);

// Type validation
const typeResult = validate.types(data, { 
  name: 'string', 
  age: 'number' 
});

// Format validation
const formatResult = validate.formats(data, { 
  email: 'email', 
  color: 'hex' 
});

// Numeric range validation
const rangeResult = validate.ranges(data, { 
  age: { min: 0, max: 120 } 
});

// Context validation
const authResult = await validate.authContext();
const orgResult = await validate.orgContext();
const fullResult = await validate.fullContext();

// Business rule validation
const existsResult = validate.resourceExists('User', userId, userExists);
const permissionResult = validate.permissions('delete', hasPermission, 'admin');

// Combine multiple validations
const combined = validate.combine(requiredResult, typeResult, formatResult);
```

### Response Helpers

The `respond` object provides standardized response creation:

```typescript
// Success response
const successResponse = respond.success(data, 'Operation successful');

// Error response from classified error
const errorResponse = respond.error(ipcError);

// Error response from generic error
const genericErrorResponse = respond.fromError(error, 'User message');

// Validation error response
const validationResponse = respond.validationError(validationResult);
```

### Wrapper Helpers

The `wrap` object provides handler wrappers:

```typescript
// Basic error handling wrapper
const handler = wrap.withErrorHandling(businessLogic, options);

// Validation + error handling wrapper
const validatedHandler = wrap.withValidation(businessLogic, validator, options);
```

## Validation Types

### Required Fields Validation

Validates that specified fields are present and non-empty:

```typescript
const validation = validate.required(data, ['name', 'email', 'password']);
// Returns: { isValid: boolean, errors: ValidationError[] }
```

### Type Validation

Validates that fields have the correct types:

```typescript
const validation = validate.types(data, {
  name: 'string',
  age: 'number',
  active: 'boolean',
  metadata: 'object',
  tags: 'array'
});
```

### Format Validation

Validates string formats using built-in patterns:

```typescript
const validation = validate.formats(data, {
  email: 'email',           // Email format
  website: 'url',           // URL format
  color: 'hex',             // Hex color (#RRGGBB)
  id: 'uuid'                // UUID format
});
```

### Numeric Range Validation

Validates that numbers fall within specified ranges:

```typescript
const validation = validate.ranges(data, {
  age: { min: 0, max: 120 },
  percentage: { min: 0, max: 100 },
  priority: { min: 1 }       // Only minimum specified
});
```

### Business Rule Validation

Helpers for common business rule patterns:

```typescript
// Check if resource exists
const existsValidation = validate.resourceExists('Color', colorId, colorExists);

// Check if resource doesn't already exist (for creation)
const uniqueValidation = validate.resourceDoesNotExist('User', email, userExists);

// Check permissions
const permissionValidation = validate.permissions('delete', hasDeletePermission, 'admin');
```

## Context Validation

### Authentication Context

Validates that a user is authenticated:

```typescript
const authResult = await validate.authContext();
if (!authResult.isValid) {
  return respond.validationError(authResult, 'Please log in');
}
```

### Organization Context

Validates that an organization is selected:

```typescript
const orgResult = await validate.orgContext();
if (!orgResult.isValid) {
  return respond.validationError(orgResult, 'Please select an organization');
}
```

### Full Context

Validates both authentication and organization:

```typescript
const contextResult = await validate.fullContext();
if (!contextResult.isValid) {
  return respond.validationError(contextResult);
}
```

## Handler Options

### IPCHandlerOptions

Configure handler behavior with options:

```typescript
interface IPCHandlerOptions {
  requireOrganization?: boolean;  // Default: true
  requireAuth?: boolean;          // Default: true
  logChannel?: string;            // Custom log channel
  customErrorMessage?: string;    // Custom user error message
  skipDuplicateCheck?: boolean;   // Skip duplicate handler check
}
```

### Usage Examples

```typescript
// Minimal options - uses defaults
const handler = wrap.withErrorHandling(logic, {
  channel: 'my-channel'
});

// Full options
const handler = wrap.withErrorHandling(logic, {
  channel: 'my-channel',
  requireAuth: true,
  requireOrganization: false,
  customErrorMessage: 'Custom error message for users',
  logChannel: 'custom-log-channel'
});

// Skip validation for system handlers
const systemHandler = wrap.withErrorHandling(logic, {
  channel: 'system-channel',
  requireAuth: false,
  requireOrganization: false,
  skipValidation: true
});
```

## Error Response Structure

All IPC responses follow the standardized `IPCResponse<T>` structure:

```typescript
interface IPCResponse<T = any> {
  success: boolean;           // Operation success flag
  data?: T;                   // Response data (if successful)
  error?: string;             // Technical error message
  userMessage?: string;       // User-friendly message
  timestamp: number;          // Response timestamp
}
```

### Success Response Example

```typescript
{
  success: true,
  data: { id: '123', name: 'Sample Color' },
  userMessage: 'Color retrieved successfully',
  timestamp: 1640995200000
}
```

### Error Response Example

```typescript
{
  success: false,
  data: null,
  error: 'Database connection timeout',
  userMessage: 'Unable to retrieve data. Please try again.',
  timestamp: 1640995200000
}
```

## Best Practices

### 1. Use Appropriate Validation Level

```typescript
// For simple operations - use wrapper
const simpleHandler = wrap.withErrorHandling(logic, options);

// For complex validation - use validation wrapper
const complexHandler = wrap.withValidation(logic, validator, options);

// For very complex operations - use manual handling
const manualHandler = async (data) => {
  try {
    const validation = await validate.fullContext();
    if (!validation.isValid) {
      return respond.validationError(validation);
    }
    // Complex logic...
    return respond.success(result);
  } catch (error) {
    return respond.fromError(error);
  }
};
```

### 2. Combine Validations Effectively

```typescript
function validateUserRequest(request: UserRequest): ValidationResult {
  return validate.combine(
    validate.required(request, ['name', 'email']),
    validate.types(request, { name: 'string', email: 'string' }),
    validate.formats(request, { email: 'email' }),
    validate.ranges(request, { age: { min: 0, max: 120 } })
  );
}
```

### 3. Provide Meaningful Error Messages

```typescript
const handler = wrap.withErrorHandling(logic, {
  channel: 'user:create',
  customErrorMessage: 'Unable to create user account. Please check your information and try again.',
  requireAuth: true,
  requireOrganization: false
});
```

### 4. Handle Partial Success in Batch Operations

```typescript
const batchHandler = async (requests: Request[]): Promise<IPCResponse<BatchResult>> => {
  const successful = [];
  const failed = [];
  
  for (const request of requests) {
    try {
      const result = await processRequest(request);
      successful.push(result);
    } catch (error) {
      failed.push({ request, error: error.message });
    }
  }
  
  return respond.success({
    successful,
    failed,
    totalProcessed: requests.length
  });
};
```

### 5. Use Appropriate Error Categories

Ensure your business logic throws errors that can be properly classified:

```typescript
// Good - specific error types
throw new Error('User not found in database');           // -> DATABASE
throw new Error('Authentication token expired');        // -> AUTHENTICATION
throw new Error('Network connection failed');           // -> NETWORK

// Better - use custom error classes
throw new UserNotFoundError(userId);
throw new AuthenticationError('Token expired');
throw new NetworkError('Connection timeout');
```

## Migration Guide

### From Old Pattern

```typescript
// Old pattern
ipcMain.handle('channel', async (event, data) => {
  try {
    // Manual validation
    if (!data.name) {
      return { success: false, error: 'Name required' };
    }
    
    // Business logic
    const result = await doSomething(data);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});
```

### To New Pattern

```typescript
// New pattern
const handler = wrap.withValidation(
  async (data: RequestType) => {
    return await doSomething(data);
  },
  (data: RequestType) => validate.required(data, ['name']),
  {
    channel: 'channel',
    customErrorMessage: 'Operation failed'
  }
);

registerHandlerSafely(ipcMain, 'channel', handler);
```

## Testing

The utilities are designed to be easily testable:

```typescript
// Test validation functions
describe('validateUserRequest', () => {
  it('should validate required fields', () => {
    const result = validateUserRequest({});
    expect(result.isValid).toBe(false);
    expect(result.errors).toHaveLength(2); // name and email required
  });
});

// Test handlers
describe('userHandler', () => {
  it('should return success for valid input', async () => {
    const response = await handler(validUserData);
    expect(response.success).toBe(true);
    expect(response.data).toBeDefined();
  });
});
```

## Performance Considerations

- Validation is performed synchronously where possible
- Context validation (auth/org) is cached to avoid repeated database calls
- Error classification is lightweight and doesn't impact performance
- Logging is optimized based on error severity levels

## Integration with Existing Code

The utilities are designed to integrate seamlessly with existing ChromaSync patterns:

- Uses existing `IPCResponse<T>` interface
- Integrates with `registerHandlerSafely` from ipcRegistry
- Leverages existing organization context utilities
- Compatible with existing OAuth service patterns

## Troubleshooting

### Common Issues

1. **Validation not working**: Ensure you're using the correct validation functions and combining results properly
2. **Context validation failing**: Check that auth service and organization context are properly initialized
3. **Error messages not showing**: Verify that `userMessage` is being set in error responses
4. **Performance issues**: Consider caching context validation for high-frequency operations

### Debug Logging

Enable debug logging to troubleshoot issues:

```typescript
const handler = wrap.withErrorHandling(logic, {
  channel: 'debug-channel',
  logChannel: 'debug',  // Will log additional debug info
});
```