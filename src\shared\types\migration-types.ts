/**
 * @file migration-types.ts
 * @description Comprehensive migration-specific type definitions for ChromaSync
 * 
 * This file contains all types needed to support the gradual migration from
 * integer-based IDs to UUID-based IDs across the entire application.
 * 
 * The migration follows a phased approach:
 * 1. Pre-migration: Integer IDs only
 * 2. Dual-ID phase: Both integer and UUID IDs exist
 * 3. Post-migration: UUID IDs only
 * 
 * <AUTHOR> Migration Team
 * @version 1.0.0
 */

import { UUID } from './uuid-types';

// ===== CORE MIGRATION TYPES =====

/**
 * Represents an ID that can be either integer or UUID during migration
 */
export type TransitionID = number | UUID;

/**
 * Represents a dual-ID system where both integer and UUID exist
 */
export interface DualID {
  /** Legacy integer ID (will be removed after migration) */
  id: number;
  /** New UUID (will become primary after migration) */
  external_id: UUID;
}

/**
 * Migration phases supported by the system
 */
export enum MigrationPhase {
  /** Phase 0: Pre-migration - Integer IDs only */
  PRE_MIGRATION = 'pre_migration',
  /** Phase 1: Dual-ID creation - New records get both IDs */
  DUAL_ID_CREATION = 'dual_id_creation',
  /** Phase 2: UUID backfill - Add UUIDs to existing records */
  UUID_BACKFILL = 'uuid_backfill',
  /** Phase 3: API transition - APIs accept both but prefer UUID */
  API_TRANSITION = 'api_transition',
  /** Phase 4: UUID primary - UUID becomes primary, integer deprecated */
  UUID_PRIMARY = 'uuid_primary',
  /** Phase 5: Integer cleanup - Remove integer IDs */
  INTEGER_CLEANUP = 'integer_cleanup',
  /** Phase 6: Post-migration - UUID IDs only */
  POST_MIGRATION = 'post_migration'
}

/**
 * Migration direction for rollback operations
 */
export enum MigrationDirection {
  FORWARD = 'forward',
  BACKWARD = 'backward'
}

/**
 * Migration state for tracking progress
 */
export interface MigrationState {
  /** Current migration phase */
  phase: MigrationPhase;
  /** Timestamp when migration started */
  startedAt: string;
  /** Timestamp when current phase started */
  phaseStartedAt: string;
  /** Last updated timestamp */
  updatedAt: string;
  /** Migration progress percentage (0-100) */
  progress: number;
  /** Whether migration is currently running */
  isRunning: boolean;
  /** Whether migration is paused */
  isPaused: boolean;
  /** Error message if migration failed */
  error?: string;
  /** Migration configuration */
  config: MigrationConfig;
}

// ===== MIGRATION CONFIGURATION =====

/**
 * Configuration options for migration phases
 */
export interface MigrationConfig {
  /** Batch size for processing records */
  batchSize: number;
  /** Delay between batches (ms) */
  batchDelay: number;
  /** Maximum retries for failed operations */
  maxRetries: number;
  /** Timeout for individual operations (ms) */
  operationTimeout: number;
  /** Whether to enable dry-run mode */
  dryRun: boolean;
  /** Whether to skip validation */
  skipValidation: boolean;
  /** Tables to migrate (empty means all) */
  includeTables: string[];
  /** Tables to exclude from migration */
  excludeTables: string[];
  /** Whether to create backups before migration */
  createBackups: boolean;
  /** Whether to enable verbose logging */
  verboseLogging: boolean;
}

/**
 * Default migration configuration
 */
export const DEFAULT_MIGRATION_CONFIG: MigrationConfig = {
  batchSize: 100,
  batchDelay: 100,
  maxRetries: 3,
  operationTimeout: 30000,
  dryRun: false,
  skipValidation: false,
  includeTables: [],
  excludeTables: [],
  createBackups: true,
  verboseLogging: false
};

// ===== TRANSITION TYPES =====

/**
 * Base interface for entities during migration
 */
export interface MigrationEntity {
  /** Legacy integer ID (optional during transition) */
  id?: number;
  /** New UUID (optional during transition) */
  external_id?: UUID;
  /** Migration metadata */
  _migration?: {
    phase: MigrationPhase;
    migratedAt?: string;
    version: number;
  };
}

/**
 * Organization during migration transition
 */
export interface TransitionOrganization extends MigrationEntity {
  id?: number;
  external_id?: UUID;
  name: string;
  slug: string;
  plan: 'free' | 'team' | 'enterprise';
  settings: OrganizationSettings;
  created_at: string;
  updated_at: string;
  memberCount?: number;
  userRole?: 'owner' | 'admin' | 'member';
}

/**
 * Color during migration transition
 */
export interface TransitionColor extends MigrationEntity {
  id?: number;
  external_id?: UUID;
  organization_id?: TransitionID;
  source_id?: TransitionID;
  name: string;
  display_name?: string;
  code?: string;
  hex: string;
  color_spaces: Record<string, any>;
  is_gradient: boolean;
  is_metallic: boolean;
  is_effect: boolean;
  is_library: boolean;
  gradient_colors?: string;
  notes?: string;
  tags?: string;
  properties?: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by?: UUID;
  user_id?: UUID;
  deleted_at?: string;
  device_id?: string;
  conflict_resolved_at?: string;
  is_synced: boolean;
  version: number;
}

/**
 * Product during migration transition
 */
export interface TransitionProduct extends MigrationEntity {
  id?: number;
  external_id?: UUID;
  organization_id?: TransitionID;
  name: string;
  description?: string;
  category?: string;
  type?: string;
  sku?: string;
  website?: string;
  datasheet_url?: string;
  price?: number;
  currency?: string;
  is_master: boolean;
  is_active: boolean;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by?: UUID;
  user_id?: UUID;
  deleted_at?: string;
  device_id?: string;
  conflict_resolved_at?: string;
  is_synced: boolean;
}

/**
 * Product-Color relationship during migration
 */
export interface TransitionProductColor extends MigrationEntity {
  product_id?: TransitionID;
  color_id?: TransitionID;
  organization_id?: TransitionID;
  display_order: number;
  added_at: string;
}

/**
 * Organization settings interface
 */
export interface OrganizationSettings {
  allowedDomains?: string[];
  colorNamePattern?: string;
  requireApproval?: boolean;
  defaultColorProperties?: Record<string, any>;
}

// ===== CONVERSION UTILITIES =====

/**
 * Utility type for converting between ID types
 */
export interface IDConverter {
  /** Convert integer ID to UUID */
  intToUUID(id: number): UUID | null;
  /** Convert UUID to integer ID */
  uuidToInt(uuid: UUID): number | null;
  /** Check if ID is integer */
  isInteger(id: TransitionID): id is number;
  /** Check if ID is UUID */
  isUUID(id: TransitionID): id is UUID;
  /** Get primary ID based on current phase */
  getPrimaryId(entity: MigrationEntity): TransitionID | null;
  /** Get secondary ID based on current phase */
  getSecondaryId(entity: MigrationEntity): TransitionID | null;
}

/**
 * ID mapping for tracking conversions
 */
export interface IDMapping {
  /** Table name */
  table: string;
  /** Integer ID */
  intId: number;
  /** Corresponding UUID */
  uuid: UUID;
  /** When mapping was created */
  createdAt: string;
  /** Migration batch ID */
  batchId: string;
}

// ===== MIGRATION RESULTS =====

/**
 * Result of a migration operation
 */
export interface MigrationResult {
  /** Whether operation was successful */
  success: boolean;
  /** Error message if failed */
  error?: string;
  /** Number of records processed */
  recordsProcessed: number;
  /** Number of records successfully migrated */
  recordsMigrated: number;
  /** Number of records that failed */
  recordsFailed: number;
  /** Time taken for operation (ms) */
  duration: number;
  /** Migration phase when operation was performed */
  phase: MigrationPhase;
  /** Detailed results by table */
  tableResults: MigrationTableResult[];
  /** Validation results */
  validation?: ValidationResult;
  /** Rollback information if needed */
  rollback?: RollbackInfo;
}

/**
 * Migration result for a specific table
 */
export interface MigrationTableResult {
  /** Table name */
  table: string;
  /** Number of records processed */
  recordsProcessed: number;
  /** Number of records migrated */
  recordsMigrated: number;
  /** Number of records failed */
  recordsFailed: number;
  /** Processing time (ms) */
  duration: number;
  /** Any errors encountered */
  errors: MigrationError[];
}

/**
 * Batch processing result
 */
export interface BatchResult {
  /** Batch ID */
  batchId: string;
  /** Records in batch */
  recordCount: number;
  /** Successfully processed records */
  successCount: number;
  /** Failed records */
  failureCount: number;
  /** Batch processing time (ms) */
  duration: number;
  /** Errors encountered */
  errors: MigrationError[];
}

// ===== PHASE-SPECIFIC TYPES =====

/**
 * Phase 1: Dual-ID Creation configuration
 */
export interface DualIDCreationConfig {
  /** Whether to generate UUIDs for new records */
  generateUUIDs: boolean;
  /** UUID generation strategy */
  uuidStrategy: 'random' | 'deterministic' | 'custom';
  /** Custom UUID generator function */
  customGenerator?: (record: any) => UUID;
}

/**
 * Phase 2: UUID Backfill configuration
 */
export interface UUIDBackfillConfig {
  /** Tables to backfill */
  tables: string[];
  /** Batch size for backfill */
  batchSize: number;
  /** Whether to update existing UUIDs */
  updateExisting: boolean;
  /** UUID generation strategy for backfill */
  generationStrategy: 'random' | 'deterministic';
}

/**
 * Phase 3: API Transition configuration
 */
export interface APITransitionConfig {
  /** Whether to accept both ID types */
  acceptBothIds: boolean;
  /** Preferred ID type for responses */
  preferredIdType: 'integer' | 'uuid';
  /** Whether to include both IDs in responses */
  includeBothIds: boolean;
  /** API endpoints to transition */
  endpoints: string[];
}

/**
 * Phase 4: UUID Primary configuration
 */
export interface UUIDPrimaryConfig {
  /** Whether to deprecate integer IDs */
  deprecateIntegerIds: boolean;
  /** Warning message for integer ID usage */
  deprecationWarning: string;
  /** Grace period for integer ID support (days) */
  gracePeriod: number;
}

/**
 * Phase 5: Integer Cleanup configuration
 */
export interface IntegerCleanupConfig {
  /** Whether to remove integer ID columns */
  removeIntegerColumns: boolean;
  /** Whether to create archive tables */
  createArchives: boolean;
  /** Tables to clean up */
  tables: string[];
  /** Confirmation required for cleanup */
  requireConfirmation: boolean;
}

// ===== ERROR HANDLING =====

/**
 * Migration-specific error types
 */
export enum MigrationErrorType {
  /** Validation error */
  VALIDATION_ERROR = 'validation_error',
  /** Constraint violation */
  CONSTRAINT_VIOLATION = 'constraint_violation',
  /** Data corruption */
  DATA_CORRUPTION = 'data_corruption',
  /** Timeout error */
  TIMEOUT_ERROR = 'timeout_error',
  /** Connection error */
  CONNECTION_ERROR = 'connection_error',
  /** Permission error */
  PERMISSION_ERROR = 'permission_error',
  /** Configuration error */
  CONFIG_ERROR = 'config_error',
  /** Phase transition error */
  PHASE_TRANSITION_ERROR = 'phase_transition_error',
  /** Rollback error */
  ROLLBACK_ERROR = 'rollback_error',
  /** Unknown error */
  UNKNOWN_ERROR = 'unknown_error'
}

/**
 * Detailed migration error information
 */
export interface MigrationError {
  /** Error type */
  type: MigrationErrorType;
  /** Error message */
  message: string;
  /** Error code */
  code: string;
  /** Table where error occurred */
  table?: string;
  /** Record ID where error occurred */
  recordId?: TransitionID;
  /** Additional error details */
  details?: Record<string, any>;
  /** Stack trace */
  stackTrace?: string;
  /** When error occurred */
  timestamp: string;
  /** Migration phase when error occurred */
  phase: MigrationPhase;
  /** Whether error is retryable */
  retryable: boolean;
}

/**
 * Error recovery strategies
 */
export enum RecoveryStrategy {
  /** Retry the operation */
  RETRY = 'retry',
  /** Skip the record */
  SKIP = 'skip',
  /** Pause migration */
  PAUSE = 'pause',
  /** Rollback changes */
  ROLLBACK = 'rollback',
  /** Manual intervention required */
  MANUAL = 'manual'
}

/**
 * Error recovery configuration
 */
export interface ErrorRecoveryConfig {
  /** Strategy for different error types */
  strategies: Record<MigrationErrorType, RecoveryStrategy>;
  /** Maximum retry attempts */
  maxRetries: number;
  /** Retry delay (ms) */
  retryDelay: number;
  /** Whether to pause on critical errors */
  pauseOnCritical: boolean;
  /** Whether to notify on errors */
  notifyOnError: boolean;
}

// ===== VALIDATION TYPES =====

/**
 * Validation rule for migration
 */
export interface MigrationValidationRule {
  /** Rule name */
  name: string;
  /** Rule description */
  description: string;
  /** Table to validate */
  table: string;
  /** Validation function */
  validate: (record: any) => ValidationResult;
  /** Whether rule is critical */
  critical: boolean;
  /** Rule category */
  category: 'data_integrity' | 'referential_integrity' | 'business_logic';
}

/**
 * Validation result
 */
export interface ValidationResult {
  /** Whether validation passed */
  isValid: boolean;
  /** Validation errors */
  errors: ValidationError[];
  /** Validation warnings */
  warnings: ValidationWarning[];
  /** Records validated */
  recordsValidated: number;
  /** Validation time (ms) */
  duration: number;
}

/**
 * Validation error
 */
export interface ValidationError {
  /** Error message */
  message: string;
  /** Error code */
  code: string;
  /** Field that failed validation */
  field?: string;
  /** Record ID */
  recordId?: TransitionID;
  /** Table name */
  table?: string;
  /** Validation rule that failed */
  rule?: string;
  /** Error details */
  details?: Record<string, any>;
}

/**
 * Validation warning
 */
export interface ValidationWarning {
  /** Warning message */
  message: string;
  /** Warning code */
  code: string;
  /** Field that triggered warning */
  field?: string;
  /** Record ID */
  recordId?: TransitionID;
  /** Table name */
  table?: string;
  /** Validation rule that warned */
  rule?: string;
}

// ===== ROLLBACK TYPES =====

/**
 * Rollback information
 */
export interface RollbackInfo {
  /** Rollback point ID */
  rollbackId: string;
  /** Phase to rollback to */
  targetPhase: MigrationPhase;
  /** When rollback point was created */
  createdAt: string;
  /** Rollback reason */
  reason: string;
  /** Whether rollback is possible */
  canRollback: boolean;
  /** Rollback steps */
  steps: RollbackStep[];
}

/**
 * Individual rollback step
 */
export interface RollbackStep {
  /** Step ID */
  stepId: string;
  /** Step description */
  description: string;
  /** Step type */
  type: 'data_restore' | 'schema_change' | 'index_rebuild' | 'constraint_restore';
  /** SQL commands to execute */
  sql?: string[];
  /** Whether step is critical */
  critical: boolean;
  /** Expected duration (ms) */
  estimatedDuration: number;
}

/**
 * Rollback result
 */
export interface RollbackResult {
  /** Whether rollback was successful */
  success: boolean;
  /** Error message if failed */
  error?: string;
  /** Steps completed */
  stepsCompleted: number;
  /** Total steps */
  totalSteps: number;
  /** Rollback duration (ms) */
  duration: number;
  /** Phase after rollback */
  finalPhase: MigrationPhase;
}

// ===== MONITORING TYPES =====

/**
 * Migration progress information
 */
export interface MigrationProgress {
  /** Current phase */
  phase: MigrationPhase;
  /** Overall progress percentage */
  overallProgress: number;
  /** Phase progress percentage */
  phaseProgress: number;
  /** Estimated time remaining (ms) */
  estimatedTimeRemaining: number;
  /** Records processed */
  recordsProcessed: number;
  /** Total records to process */
  totalRecords: number;
  /** Current operation */
  currentOperation: string;
  /** Progress by table */
  tableProgress: Record<string, number>;
  /** Started at */
  startedAt: string;
  /** Phase started at */
  phaseStartedAt: string;
  /** Last updated */
  lastUpdated: string;
}

/**
 * Migration metrics
 */
export interface MigrationMetrics {
  /** Total migration time (ms) */
  totalDuration: number;
  /** Time per phase (ms) */
  phaseDurations: Record<MigrationPhase, number>;
  /** Records processed per second */
  throughput: number;
  /** Error rate */
  errorRate: number;
  /** Retry count */
  retryCount: number;
  /** Peak memory usage (MB) */
  peakMemoryUsage: number;
  /** Average batch processing time (ms) */
  avgBatchTime: number;
  /** Successful operations */
  successfulOperations: number;
  /** Failed operations */
  failedOperations: number;
}

// ===== UTILITY TYPES =====

/**
 * Type guard for checking if entity has integer ID
 */
export function hasIntegerId(entity: MigrationEntity): entity is MigrationEntity & { id: number } {
  return typeof entity.id === 'number';
}

/**
 * Type guard for checking if entity has UUID
 */
export function hasUUID(entity: MigrationEntity): entity is MigrationEntity & { external_id: UUID } {
  return typeof entity.external_id === 'string' && entity.external_id.length === 36;
}

/**
 * Type guard for checking if ID is integer
 */
export function isIntegerID(id: TransitionID): id is number {
  return typeof id === 'number';
}

/**
 * Type guard for checking if ID is UUID
 */
export function isUUIDID(id: TransitionID): id is UUID {
  return typeof id === 'string' && id.length === 36;
}

// ===== EXPORT TYPES =====
// Note: All types and enums are already exported individually above
// Removed duplicate export block to prevent TypeScript conflicts

/**
 * Constants for migration
 */
export const MIGRATION_CONSTANTS = {
  /** Maximum batch size allowed */
  MAX_BATCH_SIZE: 1000,
  /** Minimum batch size allowed */
  MIN_BATCH_SIZE: 10,
  /** Default batch delay (ms) */
  DEFAULT_BATCH_DELAY: 100,
  /** Maximum retry attempts */
  MAX_RETRY_ATTEMPTS: 5,
  /** Default operation timeout (ms) */
  DEFAULT_TIMEOUT: 30000,
  /** Migration version */
  MIGRATION_VERSION: '1.0.0',
  /** Migration metadata table name */
  MIGRATION_TABLE: '_migration_state',
  /** ID mapping table name */
  ID_MAPPING_TABLE: '_id_mappings'
} as const;