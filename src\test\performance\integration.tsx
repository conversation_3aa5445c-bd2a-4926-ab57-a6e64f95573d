/**
 * Performance Testing Integration
 * Add this to your main App.tsx to enable performance testing route
 */

import React, { lazy, Suspense } from 'react';
import { Route } from 'react-router-dom';

// Lazy load the performance profiler
const PerformanceProfiler = lazy(() =>
  import('./test/performance/PerformanceProfiler').then(module => ({
    default: module.PerformanceProfiler,
  }))
);

// Add this route to your existing routes
export const PerformanceTestRoute: React.FC = () => {
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Route
      path='/performance-test'
      element={
        <Suspense fallback={<div>Loading Performance Profiler...</div>}>
          <PerformanceProfiler />
        </Suspense>
      }
    />
  );
};

// Usage in your App.tsx:
// <Routes>
//   {/* Your existing routes */}
//   <PerformanceTestRoute />
// </Routes>

// Quick performance check function
export async function quickPerformanceCheck(): Promise<boolean> {
  console.log('🏃 Running quick performance check...');

  const checks = {
    rendering: true,
    memory: true,
    responsiveness: true,
  };

  // Check rendering performance
  const renderStart = performance.now();
  const testElements = Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    hex: `#${Math.floor(Math.random() * 16777215)
      .toString(16)
      .padStart(6, '0')}`,
  }));
  const renderTime = performance.now() - renderStart;

  if (renderTime > 100) {
    console.warn(
      `⚠️ Slow render performance: ${renderTime.toFixed(2)}ms for 1000 elements`
    );
    checks.rendering = false;
  }

  // Check memory usage
  if (performance.memory) {
    const memoryMB = performance.memory.usedJSHeapSize / 1024 / 1024;
    if (memoryMB > 100) {
      console.warn(`⚠️ High memory usage: ${memoryMB.toFixed(2)}MB`);
      checks.memory = false;
    }
  }

  // Check responsiveness
  const responseStart = performance.now();
  await new Promise(resolve => setTimeout(resolve, 0));
  const responseTime = performance.now() - responseStart;

  if (responseTime > 16.67) {
    console.warn(`⚠️ Poor responsiveness: ${responseTime.toFixed(2)}ms`);
    checks.responsiveness = false;
  }

  const allPassed = Object.values(checks).every(check => check);

  if (allPassed) {
    console.log('✅ Quick performance check passed!');
  } else {
    console.log(
      '❌ Performance issues detected. Run full test suite for details.'
    );
  }

  return allPassed;
}

// Export performance monitoring hook
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = React.useState({
    fps: 0,
    memory: 0,
    renderTime: 0,
  });

  React.useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measure = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        const memory = performance.memory
          ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
          : 0;

        setMetrics(prev => ({
          ...prev,
          fps,
          memory,
        }));

        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measure);
    };

    animationId = requestAnimationFrame(measure);
    return () => cancelAnimationFrame(animationId);
  }, []);

  return metrics;
}
