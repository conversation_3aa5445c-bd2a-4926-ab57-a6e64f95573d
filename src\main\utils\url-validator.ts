/**
 * @file url-validator.ts
 * @description Comprehensive URL validation for external link handling with security protection
 */

/**
 * URL validation configuration
 */
interface URLValidationConfig {
  allowedProtocols?: string[];
  allowedDomains?: string[];
  blockedDomains?: string[];
  maxLength?: number;
  allowIPAddresses?: boolean;
  allowLocalhost?: boolean;
  allowPrivateIPs?: boolean;
}

/**
 * URL validation result
 */
interface URLValidationResult {
  isValid: boolean;
  sanitizedUrl?: string;
  error?: string;
  warnings?: string[];
  metadata?: {
    protocol: string;
    hostname: string;
    port?: string;
    isPrivateIP: boolean;
    isLocalhost: boolean;
    isDomainBlocked: boolean;
  };
}

/**
 * Default URL validation configuration
 */
const DEFAULT_CONFIG: URLValidationConfig = {
  allowedProtocols: ['http:', 'https:'],
  allowedDomains: [], // Empty means allow all (unless in blocked list)
  blockedDomains: [
    'localhost',
    '127.0.0.1',
    '0.0.0.0',
    '::1',
    'file.local',
    'test.local',
  ],
  maxLength: 2048,
  allowIPAddresses: false,
  allowLocalhost: false,
  allowPrivateIPs: false,
};

/**
 * Dangerous URL patterns that should always be blocked
 */
const DANGEROUS_URL_PATTERNS = [
  /javascript:/i,
  /data:/i,
  /vbscript:/i,
  /file:/i,
  /ftp:/i,
  /chrome:/i,
  /chrome-extension:/i,
  /moz-extension:/i,
  /about:/i,
  /<script/i,
  /onload=/i,
  /onerror=/i,
  /onclick=/i,
  /eval\(/i,
  /alert\(/i,
  /confirm\(/i,
  /prompt\(/i,
  /document\./i,
  /window\./i,
  /[;&|`$(){}[\]]/, // Shell metacharacters
  /\n|\r/, // Newlines
  /\\x[0-9a-fA-F]{2}/, // Hex encoded characters
  /%(0|1|2)[0-9a-fA-F]/, // URL encoded control characters
];

/**
 * Private IP address ranges (RFC 1918, RFC 4193, etc.)
 */
const PRIVATE_IP_RANGES = [
  /^10\./,
  /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
  /^192\.168\./,
  /^127\./,
  /^169\.254\./,
  /^::1$/,
  /^fe80:/,
  /^fc00:/,
  /^fd00:/,
];

/**
 * ChromaSync specific allowed domains for business functionality
 */
const CHROMASYNC_ALLOWED_DOMAINS = [
  'chromasync.app',
  'auth.chromasync.app',
  'sharepoint.com',
  'onedrive.com',
  'microsoft.com',
  'office.com',
  'live.com',
  'google.com',
  'googleapis.com',
  'supabase.co',
  'pantone.com',
  'ral-farben.de',
  'github.com',
  'gitlab.com',
  'zoom.us',
  'teams.microsoft.com',
  'slack.com',
];

/**
 * URL Validator class for comprehensive URL validation
 */
export class URLValidator {
  private config: URLValidationConfig;

  constructor(customConfig?: Partial<URLValidationConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...customConfig };

    // Add ChromaSync specific domains to allowed list if not specified
    if (
      !this.config.allowedDomains ||
      this.config.allowedDomains.length === 0
    ) {
      this.config.allowedDomains = CHROMASYNC_ALLOWED_DOMAINS;
    }
  }

  /**
   * Validate a URL comprehensively
   */
  public validateURL(url: string): URLValidationResult {
    const warnings: string[] = [];

    // Basic input validation
    if (!url || typeof url !== 'string') {
      return { isValid: false, error: 'URL must be a non-empty string' };
    }

    if (url.length > this.config.maxLength!) {
      return {
        isValid: false,
        error: `URL exceeds maximum length of ${this.config.maxLength} characters`,
      };
    }

    // Check for dangerous patterns
    for (const pattern of DANGEROUS_URL_PATTERNS) {
      if (pattern.test(url)) {
        return {
          isValid: false,
          error: `URL contains dangerous pattern: ${pattern.source}`,
        };
      }
    }

    // Parse the URL
    let parsedUrl: URL;
    try {
      // Handle URLs that start with www. but no protocol
      const normalizedUrl = url.startsWith('www.') ? `https://${url}` : url;
      parsedUrl = new URL(normalizedUrl);
    } catch (error) {
      return { isValid: false, error: 'Invalid URL format' };
    }

    // Extract metadata
    const metadata = {
      protocol: parsedUrl.protocol,
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || undefined,
      isPrivateIP: this.isPrivateIP(parsedUrl.hostname),
      isLocalhost: this.isLocalhost(parsedUrl.hostname),
      isDomainBlocked: this.isDomainBlocked(parsedUrl.hostname),
    };

    // Validate protocol
    if (!this.config.allowedProtocols!.includes(parsedUrl.protocol)) {
      return {
        isValid: false,
        error: `Protocol ${parsedUrl.protocol} is not allowed`,
        metadata,
      };
    }

    // Check for IP addresses
    if (this.isIPAddress(parsedUrl.hostname)) {
      if (!this.config.allowIPAddresses) {
        return {
          isValid: false,
          error: 'IP addresses are not allowed',
          metadata,
        };
      }
      warnings.push('URL uses IP address instead of domain name');
    }

    // Check for localhost
    if (metadata.isLocalhost && !this.config.allowLocalhost) {
      return {
        isValid: false,
        error: 'Localhost URLs are not allowed',
        metadata,
      };
    }

    // Check for private IPs
    if (metadata.isPrivateIP && !this.config.allowPrivateIPs) {
      return {
        isValid: false,
        error: 'Private IP addresses are not allowed',
        metadata,
      };
    }

    // Check blocked domains
    if (metadata.isDomainBlocked) {
      return {
        isValid: false,
        error: `Domain ${parsedUrl.hostname} is blocked`,
        metadata,
      };
    }

    // Check allowed domains (if specified)
    if (this.config.allowedDomains && this.config.allowedDomains.length > 0) {
      const isAllowed = this.config.allowedDomains.some(allowedDomain => {
        return (
          parsedUrl.hostname === allowedDomain ||
          parsedUrl.hostname.endsWith(`.${allowedDomain}`)
        );
      });

      if (!isAllowed) {
        return {
          isValid: false,
          error: `Domain ${parsedUrl.hostname} is not in the allowed list`,
          metadata,
        };
      }
    }

    // Additional security checks
    if (parsedUrl.username || parsedUrl.password) {
      warnings.push('URL contains embedded credentials');
    }

    if (
      parsedUrl.protocol === 'http:' &&
      !parsedUrl.hostname.includes('localhost')
    ) {
      warnings.push('URL uses insecure HTTP protocol');
    }

    // Return validated URL
    return {
      isValid: true,
      sanitizedUrl: parsedUrl.toString(),
      warnings: warnings.length > 0 ? warnings : undefined,
      metadata,
    };
  }

  /**
   * Quick validation for common use cases
   */
  public isValidURL(url: string): boolean {
    return this.validateURL(url).isValid;
  }

  /**
   * Get sanitized URL if valid
   */
  public sanitizeURL(url: string): string | null {
    const result = this.validateURL(url);
    return result.isValid ? result.sanitizedUrl! : null;
  }

  /**
   * Check if hostname is an IP address
   */
  private isIPAddress(hostname: string): boolean {
    // IPv4 regex
    const ipv4Regex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    // IPv6 regex (simplified)
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

    return ipv4Regex.test(hostname) || ipv6Regex.test(hostname);
  }

  /**
   * Check if hostname is localhost
   */
  private isLocalhost(hostname: string): boolean {
    const localhostPatterns = ['localhost', '127.0.0.1', '::1', '0.0.0.0'];
    return localhostPatterns.includes(hostname.toLowerCase());
  }

  /**
   * Check if hostname is a private IP
   */
  private isPrivateIP(hostname: string): boolean {
    return PRIVATE_IP_RANGES.some(range => range.test(hostname));
  }

  /**
   * Check if domain is in blocked list
   */
  private isDomainBlocked(hostname: string): boolean {
    if (!this.config.blockedDomains) {
      return false;
    }

    return this.config.blockedDomains.some(blockedDomain => {
      return (
        hostname === blockedDomain || hostname.endsWith(`.${blockedDomain}`)
      );
    });
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<URLValidationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  public getConfig(): URLValidationConfig {
    return { ...this.config };
  }

  /**
   * Add allowed domain
   */
  public addAllowedDomain(domain: string): void {
    if (!this.config.allowedDomains) {
      this.config.allowedDomains = [];
    }
    if (!this.config.allowedDomains.includes(domain)) {
      this.config.allowedDomains.push(domain);
    }
  }

  /**
   * Add blocked domain
   */
  public addBlockedDomain(domain: string): void {
    if (!this.config.blockedDomains) {
      this.config.blockedDomains = [];
    }
    if (!this.config.blockedDomains.includes(domain)) {
      this.config.blockedDomains.push(domain);
    }
  }

  /**
   * Remove allowed domain
   */
  public removeAllowedDomain(domain: string): void {
    if (this.config.allowedDomains) {
      this.config.allowedDomains = this.config.allowedDomains.filter(
        d => d !== domain
      );
    }
  }

  /**
   * Remove blocked domain
   */
  public removeBlockedDomain(domain: string): void {
    if (this.config.blockedDomains) {
      this.config.blockedDomains = this.config.blockedDomains.filter(
        d => d !== domain
      );
    }
  }
}

// Default validator instance for common use
export const defaultURLValidator = new URLValidator();

// Specific validators for different use cases
export const strictURLValidator = new URLValidator({
  allowedProtocols: ['https:'],
  allowIPAddresses: false,
  allowLocalhost: false,
  allowPrivateIPs: false,
  maxLength: 1024,
});

export const developmentURLValidator = new URLValidator({
  allowedProtocols: ['http:', 'https:'],
  allowIPAddresses: true,
  allowLocalhost: true,
  allowPrivateIPs: true,
  maxLength: 2048,
});

// Utility functions for backward compatibility
export function validateURL(url: string): URLValidationResult {
  return defaultURLValidator.validateURL(url);
}

export function isValidURL(url: string): boolean {
  return defaultURLValidator.isValidURL(url);
}

export function sanitizeURL(url: string): string | null {
  return defaultURLValidator.sanitizeURL(url);
}

// Export types for external use
export type { URLValidationConfig, URLValidationResult };
