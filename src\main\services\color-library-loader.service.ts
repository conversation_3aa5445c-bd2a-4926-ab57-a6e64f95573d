/**
 * @file color-library-loader.service.ts
 * @description Efficient color library loader with lazy loading and caching
 *
 * This service replaces the massive static color library files with
 * an optimized loading system that improves startup performance.
 */

import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import { ColorEntry } from '../../shared/types/color.types';

interface ColorLibraryMetadata {
  name: string;
  code: string;
  version: string;
  colorCount: number;
  lastUpdated: string;
  description: string;
}

interface ColorLibraryData {
  metadata: ColorLibraryMetadata;
  colors: ColorEntry[];
}

interface ColorLibraryChunk {
  metadata: ColorLibraryMetadata;
  colors: ColorEntry[];
  startIndex: number;
  endIndex: number;
}

/**
 * Efficient color library loader with chunking and caching
 */
export class ColorLibraryLoaderService {
  private readonly CHUNK_SIZE = 100; // Load colors in chunks of 100
  private readonly CACHE_TTL = 1000 * 60 * 30; // 30 minutes cache

  private cache = new Map<
    string,
    {
      data: ColorLibraryData | ColorLibraryChunk;
      timestamp: number;
    }
  >();

  private libraryPaths = new Map<string, string>();
  private metadata = new Map<string, ColorLibraryMetadata>();

  constructor() {
    this.initializeLibraryPaths();
  }

  /**
   * Initialize library file paths
   */
  private initializeLibraryPaths(): void {
    // Try multiple possible locations for the data files
    const possibleDataDirs = [
      path.join(__dirname, '../../shared/data/libraries'), // Development: src/main/services -> src/shared/data/libraries
      path.join(__dirname, '../../../shared/data/libraries'), // Build: out/main -> src/shared/data/libraries
      path.join(process.cwd(), 'src/shared/data/libraries'), // Fallback: from project root
      path.join(__dirname, '../../../../../src/shared/data/libraries'), // Alternative build structure
    ];

    let dataDir = '';
    for (const dir of possibleDataDirs) {
      if (fsSync.existsSync(dir)) {
        dataDir = dir;
        break;
      }
    }

    if (!dataDir) {
      // If no data directory found, use the first option and let legacy loading handle it
      dataDir = possibleDataDirs[0] ?? '';
      console.warn(
        '[ColorLibraryLoader] No data directory found, will fallback to legacy loading'
      );
    } else {
      console.log(`[ColorLibraryLoader] Using data directory: ${dataDir}`);
    }

    this.libraryPaths.set('PANTONE', path.join(dataDir, 'pantone.json'));
    this.libraryPaths.set('RAL', path.join(dataDir, 'ral.json'));
    this.libraryPaths.set('NCS', path.join(dataDir, 'ncs.json'));
  }

  /**
   * Get library metadata without loading full data
   */
  async getLibraryMetadata(
    libraryCode: string
  ): Promise<ColorLibraryMetadata | null> {
    try {
      // Check cache first
      const cached = this.metadata.get(libraryCode);
      if (cached) {
        return cached;
      }

      const libraryPath = this.libraryPaths.get(libraryCode.toUpperCase());
      if (!libraryPath) {
        console.warn(`[ColorLibraryLoader] Unknown library: ${libraryCode}`);
        return null;
      }

      // Try to read metadata from a separate metadata file first
      const metadataPath = libraryPath.replace('.json', '.metadata.json');
      try {
        const metadataContent = await fs.readFile(metadataPath, 'utf-8');
        const metadata = JSON.parse(metadataContent) as ColorLibraryMetadata;
        this.metadata.set(libraryCode, metadata);
        return metadata;
      } catch {
        // If no metadata file, read from main file (less efficient)
        const data = await this.loadLibraryData(libraryCode);
        return data?.metadata || null;
      }
    } catch (error) {
      console.error(
        `[ColorLibraryLoader] Error loading metadata for ${libraryCode}:`,
        error
      );
      return null;
    }
  }

  /**
   * Load full library data with caching
   */
  async loadLibraryData(libraryCode: string): Promise<ColorLibraryData | null> {
    try {
      const cacheKey = `library:${libraryCode}`;

      // Check cache first
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return cached.data as ColorLibraryData;
      }

      const libraryPath = this.libraryPaths.get(libraryCode.toUpperCase());
      if (!libraryPath) {
        console.warn(`[ColorLibraryLoader] Unknown library: ${libraryCode}`);
        return null;
      }

      console.log(
        `[ColorLibraryLoader] Loading library: ${libraryCode} from ${libraryPath}`
      );

      const startTime = Date.now();
      const fileContent = await fs.readFile(libraryPath, 'utf-8');
      const data = JSON.parse(fileContent) as ColorLibraryData;

      const loadTime = Date.now() - startTime;
      console.log(
        `[ColorLibraryLoader] Loaded ${data.colors.length} colors from ${libraryCode} in ${loadTime}ms`
      );

      // Cache the data
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now(),
      });

      // Cache metadata separately
      this.metadata.set(libraryCode, data.metadata);

      return data;
    } catch (error) {
      console.error(
        `[ColorLibraryLoader] Error loading library ${libraryCode}:`,
        error
      );

      // Fallback to legacy loading if external files don't exist
      return await this.loadLegacyLibraryData(libraryCode);
    }
  }

  /**
   * Load a chunk of colors from a library
   */
  async loadLibraryChunk(
    libraryCode: string,
    startIndex: number,
    chunkSize: number = this.CHUNK_SIZE
  ): Promise<ColorLibraryChunk | null> {
    try {
      const cacheKey = `chunk:${libraryCode}:${startIndex}:${chunkSize}`;

      // Check cache first
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return cached.data as ColorLibraryChunk;
      }

      const libraryData = await this.loadLibraryData(libraryCode);
      if (!libraryData) {
        return null;
      }

      const endIndex = Math.min(
        startIndex + chunkSize,
        libraryData.colors.length
      );
      const colors = libraryData.colors.slice(startIndex, endIndex);

      const chunk: ColorLibraryChunk = {
        metadata: libraryData.metadata,
        colors,
        startIndex,
        endIndex,
      };

      // Cache the chunk
      this.cache.set(cacheKey, {
        data: chunk,
        timestamp: Date.now(),
      });

      return chunk;
    } catch (error) {
      console.error(
        `[ColorLibraryLoader] Error loading chunk for ${libraryCode}:`,
        error
      );
      return null;
    }
  }

  /**
   * Search colors in a library with efficient filtering
   */
  async searchLibraryColors(
    libraryCode: string,
    searchQuery: string,
    options: {
      limit?: number;
      offset?: number;
      sortBy?: 'code' | 'name';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{ colors: ColorEntry[]; total: number; hasMore: boolean }> {
    try {
      const {
        limit = 50,
        offset = 0,
        sortBy = 'code',
        sortOrder = 'asc',
      } = options;

      const libraryData = await this.loadLibraryData(libraryCode);
      if (!libraryData) {
        return { colors: [], total: 0, hasMore: false };
      }

      // Filter colors based on search query
      let filteredColors = libraryData.colors;

      if (searchQuery && searchQuery.trim()) {
        const query = searchQuery.toLowerCase().trim();
        filteredColors = libraryData.colors.filter(
          color =>
            color.code.toLowerCase().includes(query) ||
            color.name.toLowerCase().includes(query) ||
            color.hex.toLowerCase().includes(query)
        );
      }

      // Sort colors
      filteredColors.sort((a, b) => {
        const aValue = sortBy === 'code' ? a.code : a.name;
        const bValue = sortBy === 'code' ? b.code : b.name;

        const comparison = aValue.localeCompare(bValue);
        return sortOrder === 'asc' ? comparison : -comparison;
      });

      const total = filteredColors.length;
      const paginatedColors = filteredColors.slice(offset, offset + limit);
      const hasMore = offset + limit < total;

      return {
        colors: paginatedColors,
        total,
        hasMore,
      };
    } catch (error) {
      console.error(
        `[ColorLibraryLoader] Error searching ${libraryCode}:`,
        error
      );
      return { colors: [], total: 0, hasMore: false };
    }
  }

  /**
   * Get available library codes
   */
  getAvailableLibraries(): string[] {
    return Array.from(this.libraryPaths.keys());
  }

  /**
   * Clear cache for a specific library or all libraries
   */
  clearCache(libraryCode?: string): void {
    if (libraryCode) {
      // Clear specific library cache
      const keysToDelete = Array.from(this.cache.keys()).filter(key =>
        key.includes(libraryCode)
      );
      keysToDelete.forEach(key => this.cache.delete(key));
      this.metadata.delete(libraryCode);
    } else {
      // Clear all cache
      this.cache.clear();
      this.metadata.clear();
    }

    console.log(
      `[ColorLibraryLoader] Cache cleared${libraryCode ? ` for ${libraryCode}` : ''}`
    );
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    totalMemoryUsage: number;
    librariesCached: string[];
  } {
    const librariesCached = Array.from(this.metadata.keys());

    // Estimate memory usage (rough calculation)
    let totalMemoryUsage = 0;
    this.cache.forEach(entry => {
      totalMemoryUsage += JSON.stringify(entry.data).length;
    });

    return {
      totalEntries: this.cache.size,
      totalMemoryUsage,
      librariesCached,
    };
  }

  /**
   * Legacy fallback loading from static TypeScript files
   */
  private async loadLegacyLibraryData(
    libraryCode: string
  ): Promise<ColorLibraryData | null> {
    try {
      console.log(
        `[ColorLibraryLoader] Attempting legacy loading for ${libraryCode}`
      );

      // This is a fallback for when external JSON files aren't available
      // We'll dynamically import the old TypeScript files
      if (libraryCode.toUpperCase() === 'PANTONE') {
        const { pantoneColors } = await import(
          '../../renderer/utils/pantoneColors'
        );

        return {
          metadata: {
            name: 'Pantone Colors',
            code: 'PANTONE',
            version: '1.0.0',
            colorCount: pantoneColors.length,
            lastUpdated: new Date().toISOString(),
            description: 'Pantone color library (legacy)',
          },
          colors: pantoneColors.map(color => ({
            id: color.id,
            product: 'PANTONE',
            organizationId: '', // Will be set by the service
            name: color.name,
            code: color.code,
            hex: color.hex,
            cmyk: color.cmyk,
            isLibrary: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })),
        };
      }

      if (libraryCode.toUpperCase() === 'RAL') {
        const { ralColors } = await import('../../renderer/utils/ralColors');

        return {
          metadata: {
            name: 'RAL Colors',
            code: 'RAL',
            version: '1.0.0',
            colorCount: ralColors.length,
            lastUpdated: new Date().toISOString(),
            description: 'RAL color library (legacy)',
          },
          colors: ralColors.map(color => ({
            ...color,
            organizationId: '', // Will be set by the service
            isLibrary: true,
          })),
        };
      }

      return null;
    } catch (error) {
      console.error(
        `[ColorLibraryLoader] Legacy loading failed for ${libraryCode}:`,
        error
      );
      return null;
    }
  }
}

// Singleton instance
let colorLibraryLoaderService: ColorLibraryLoaderService | null = null;

/**
 * Get singleton instance of color library loader service
 */
export function getColorLibraryLoaderService(): ColorLibraryLoaderService {
  if (!colorLibraryLoaderService) {
    colorLibraryLoaderService = new ColorLibraryLoaderService();
  }
  return colorLibraryLoaderService;
}

export default ColorLibraryLoaderService;
