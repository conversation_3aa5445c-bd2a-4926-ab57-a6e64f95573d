/**
 * @file circuit-breaker-auth-manager.ts
 * @description Enhanced authentication manager with circuit breaker pattern and exponential backoff
 */

import { AuthenticationManager, AuthFlowResult, AuthFlowOptions } from './authentication-manager';
import { AuthErrorRecoveryService, CircuitBreakerConfig, RetryConfig } from './auth-error-recovery.service';
import { SessionManager, SessionConfig, SessionStatus } from './session-manager';
import { LoggerFactory, ILogger } from '../../utils/logger.service';
import { toError } from '../../../shared/types/type-guards';

export interface NetworkInterruptionConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
  timeoutMs: number;
  networkCheckIntervalMs: number;
}

export interface SessionValidationConfig {
  validationIntervalMs: number;
  refreshThresholdMinutes: number;
  maxRefreshRetries: number;
  networkTimeoutMs: number;
}

export interface CircuitBreakerAuthConfig {
  circuitBreaker: Partial<CircuitBreakerConfig>;
  retry: Partial<RetryConfig>;
  session: Partial<SessionConfig>;
  networkInterruption: Partial<NetworkInterruptionConfig>;
  sessionValidation: Partial<SessionValidationConfig>;
}

export interface AuthManagerStatus {
  isHealthy: boolean;
  circuitBreakerOpen: boolean;
  sessionValid: boolean;
  networkConnected: boolean;
  lastAuthAttempt?: number;
  nextRetryTime?: number;
  activeRetries: number;
  issues: string[];
}

/**
 * Enhanced authentication manager with circuit breaker pattern, exponential backoff,
 * and network interruption handling
 */
export class CircuitBreakerAuthManager {
  private readonly logger: ILogger;
  private readonly authManager: AuthenticationManager;
  private readonly errorRecovery: AuthErrorRecoveryService;
  private readonly sessionManager: SessionManager;

  // Network monitoring
  private networkCheckInterval: NodeJS.Timeout | null = null;
  private isNetworkConnected: boolean = true;

  // Session validation
  private sessionValidationInterval: NodeJS.Timeout | null = null;
  private activeRefreshPromise: Promise<AuthFlowResult> | null = null;

  // Retry tracking
  private activeRetries: number = 0;
  private lastAuthAttempt: number = 0;

  // Default configurations
  private readonly DEFAULT_NETWORK_CONFIG: NetworkInterruptionConfig = {
    maxRetries: 5,
    baseDelayMs: 1000,
    maxDelayMs: 60000,
    backoffMultiplier: 2,
    timeoutMs: 30000,
    networkCheckIntervalMs: 10000 // 10 seconds
  };

  private readonly DEFAULT_SESSION_VALIDATION_CONFIG: SessionValidationConfig = {
    validationIntervalMs: 300000, // 5 minutes
    refreshThresholdMinutes: 10,
    maxRefreshRetries: 3,
    networkTimeoutMs: 15000
  };

  constructor(logger?: ILogger) {
    this.logger = logger || LoggerFactory.getInstance().createLogger('CircuitBreakerAuthManager');
    
    // Initialize core services
    this.authManager = new AuthenticationManager(this.logger);
    this.errorRecovery = new AuthErrorRecoveryService(this.logger);
    this.sessionManager = new SessionManager(this.logger);

    // Start monitoring
    this.startNetworkMonitoring();
    this.startSessionValidation();

    this.logger.info('CircuitBreakerAuthManager initialized');
  }

  /**
   * Configure the authentication manager
   */
  configure(config: Partial<CircuitBreakerAuthConfig>): void {
    this.logger.info('Configuring CircuitBreakerAuthManager', { config });

    if (config.circuitBreaker) {
      this.errorRecovery.configureCircuitBreaker(config.circuitBreaker);
    }

    if (config.retry) {
      this.errorRecovery.configureRetry(config.retry);
    }

    if (config.session) {
      this.sessionManager.configure(config.session);
    }

    // Network and session validation configs are stored internally
    // as they don't have dedicated services
  }

  /**
   * Initiate OAuth flow with circuit breaker protection
   */
  async initiateOAuthFlow(options: AuthFlowOptions): Promise<{
    authUrl: string;
    state: string;
    redirectHandler: any;
  }> {
    const startTime = performance.now();
    
    try {
      // Check circuit breaker
      const blockResult = this.errorRecovery.isAuthenticationBlockedResult();
      if (!blockResult.success) {
        throw toError(blockResult.error.message);
      }

      if (blockResult.data) {
        throw toError('Authentication temporarily blocked due to repeated failures');
      }

      // Check network connectivity
      if (!await this.checkNetworkConnectivity()) {
        throw toError('Network connectivity required for authentication');
      }

      this.lastAuthAttempt = Date.now();
      this.activeRetries++;

      // Execute with retry logic and exponential backoff
      const result = await this.errorRecovery.executeWithRetry(
        () => this.authManager.initiateOAuthFlow(options),
        'initiateOAuthFlow'
      );

      this.activeRetries = Math.max(0, this.activeRetries - 1);
      
      const endTime = performance.now();
      this.logger.info('OAuth flow initiated successfully', {
        duration: endTime - startTime,
        operation: 'initiateOAuthFlow'
      });

      return result;

    } catch (error) {
      this.activeRetries = Math.max(0, this.activeRetries - 1);
      
      const endTime = performance.now();
      this.logger.error('OAuth flow initiation failed', error as Error, {
        duration: endTime - startTime,
        operation: 'initiateOAuthFlow'
      });
      
      throw error;
    }
  }

  /**
   * Handle OAuth callback with enhanced error handling
   */
  async handleCallback(callbackUrl: string): Promise<AuthFlowResult> {
    const startTime = performance.now();
    
    try {
      // Check circuit breaker
      const blockResult = this.errorRecovery.isAuthenticationBlockedResult();
      if (!blockResult.success) {
        return {
          success: false,
          error: blockResult.error.message
        };
      }

      if (blockResult.data) {
        return {
          success: false,
          error: 'Authentication temporarily blocked due to repeated failures'
        };
      }

      this.lastAuthAttempt = Date.now();
      this.activeRetries++;

      // Execute with retry logic
      const result = await this.errorRecovery.executeWithRetry(
        () => this.authManager.handleCallback(callbackUrl),
        'handleCallback'
      );

      this.activeRetries = Math.max(0, this.activeRetries - 1);

      // Start session if authentication was successful
      if (result.success && result.session) {
        this.sessionManager.startSession({
          onSessionExpired: (reason) => {
            this.logger.warn('Session expired', { reason });
            this.handleSessionExpiry(reason);
          },
          onSessionWarning: (minutesRemaining) => {
            this.logger.info('Session expiring soon', { minutesRemaining });
          }
        });
      }

      const endTime = performance.now();
      this.logger.info('Callback handled successfully', {
        success: result.success,
        duration: endTime - startTime,
        operation: 'handleCallback'
      });

      return result;

    } catch (error) {
      this.activeRetries = Math.max(0, this.activeRetries - 1);
      
      const endTime = performance.now();
      this.logger.error('Callback handling failed', error as Error, {
        duration: endTime - startTime,
        operation: 'handleCallback'
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Callback processing failed'
      };
    }
  }

  /**
   * Refresh session with network interruption handling
   */
  async refreshSession(): Promise<AuthFlowResult> {
    const startTime = performance.now();

    try {
      // Prevent concurrent refresh attempts
      if (this.activeRefreshPromise) {
        this.logger.debug('Session refresh already in progress, waiting...');
        return await this.activeRefreshPromise;
      }

      // Check network connectivity
      if (!await this.checkNetworkConnectivity()) {
        return {
          success: false,
          error: 'Network connectivity required for session refresh'
        };
      }

      // Create refresh promise
      this.activeRefreshPromise = this.errorRecovery.executeWithRetry(
        () => this.authManager.refreshSession(),
        'refreshSession'
      );

      const result = await this.activeRefreshPromise;
      this.activeRefreshPromise = null;

      // Update session activity
      if (result.success) {
        this.sessionManager.updateActivity();
      }

      const endTime = performance.now();
      this.logger.info('Session refresh completed', {
        success: result.success,
        duration: endTime - startTime,
        operation: 'refreshSession'
      });

      return result;

    } catch (error) {
      this.activeRefreshPromise = null;
      
      const endTime = performance.now();
      this.logger.error('Session refresh failed', error as Error, {
        duration: endTime - startTime,
        operation: 'refreshSession'
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Session refresh failed'
      };
    }
  }

  /**
   * Get current session with validation
   */
  async getCurrentSession(): Promise<any> {
    try {
      const session = await this.authManager.getCurrentSession();
      
      if (session) {
        // Check if session needs refresh
        if (this.shouldRefreshSession(session)) {
          this.logger.debug('Session needs refresh, attempting refresh...');
          const refreshResult = await this.refreshSession();
          
          if (refreshResult.success && refreshResult.session) {
            return refreshResult.session;
          } else {
            this.logger.warn('Session refresh failed, returning current session');
          }
        }
        
        // Update activity tracking
        this.sessionManager.updateActivity();
      }

      return session;

    } catch (error) {
      this.logger.error('Failed to get current session', error as Error);
      return null;
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<any> {
    return await this.authManager.getCurrentUser();
  }

  /**
   * Sign out with cleanup
   */
  async signOut(): Promise<void> {
    const startTime = performance.now();
    
    try {
      this.logger.info('Signing out user');

      // End session monitoring
      this.sessionManager.endSession();

      // Sign out from auth manager
      await this.authManager.signOut();

      // Reset error recovery state
      this.errorRecovery.resetAuthenticationState();

      const endTime = performance.now();
      this.logger.info('Sign out completed', {
        duration: endTime - startTime,
        operation: 'signOut'
      });

    } catch (error) {
      const endTime = performance.now();
      this.logger.error('Sign out failed', error as Error, {
        duration: endTime - startTime,
        operation: 'signOut'
      });
      throw error;
    }
  }

  /**
   * Get authentication state with enhanced status
   */
  async getAuthState(): Promise<{
    isAuthenticated: boolean;
    user?: any;
    session?: any;
    circuitBreakerOpen: boolean;
    networkConnected: boolean;
    sessionStatus: SessionStatus;
  }> {
    try {
      const authState = await this.authManager.getAuthState();
      const circuitStatus = this.errorRecovery.getCircuitBreakerStatus();
      const sessionStatus = this.sessionManager.getSessionStatus(authState.session);

      return {
        ...authState,
        circuitBreakerOpen: circuitStatus.isOpen,
        networkConnected: this.isNetworkConnected,
        sessionStatus
      };

    } catch (error) {
      this.logger.error('Failed to get auth state', error as Error);
      
      return {
        isAuthenticated: false,
        circuitBreakerOpen: this.errorRecovery.getCircuitBreakerStatus().isOpen,
        networkConnected: this.isNetworkConnected,
        sessionStatus: this.sessionManager.getSessionStatus()
      };
    }
  }

  /**
   * Get comprehensive health status
   */
  getHealthStatus(): AuthManagerStatus {
    const authHealth = this.authManager.getHealthStatus();
    const circuitStatus = this.errorRecovery.getCircuitBreakerStatus();
    const sessionStatus = this.sessionManager.getSessionStatus();
    
    const issues: string[] = [];
    
    if (!authHealth.healthy) {
      issues.push('Authentication manager unhealthy');
    }
    
    if (circuitStatus.isOpen) {
      issues.push('Circuit breaker is open');
    }
    
    if (!this.isNetworkConnected) {
      issues.push('Network connectivity issues');
    }
    
    if (sessionStatus.isExpired) {
      issues.push('Session expired');
    }

    return {
      isHealthy: issues.length === 0,
      circuitBreakerOpen: circuitStatus.isOpen,
      sessionValid: sessionStatus.isActive && !sessionStatus.isExpired,
      networkConnected: this.isNetworkConnected,
      lastAuthAttempt: this.lastAuthAttempt || undefined,
      nextRetryTime: circuitStatus.nextRetryTime,
      activeRetries: this.activeRetries,
      issues
    };
  }

  /**
   * Attempt recovery from authentication issues
   */
  async attemptRecovery(): Promise<{
    success: boolean;
    message: string;
    actionsPerformed: string[];
  }> {
    this.logger.info('Attempting authentication recovery');
    
    const recoveryResult = await this.errorRecovery.attemptRecovery();
    
    if (recoveryResult.success) {
      // Reset session manager
      this.sessionManager.endSession();
      
      // Reset network monitoring
      this.isNetworkConnected = true;
      
      // Reset retry tracking
      this.activeRetries = 0;
      this.lastAuthAttempt = 0;
      
      recoveryResult.actionsPerformed.push('Reset session manager');
      recoveryResult.actionsPerformed.push('Reset network monitoring');
      recoveryResult.actionsPerformed.push('Reset retry tracking');
    }
    
    return recoveryResult;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.logger.info('Cleaning up CircuitBreakerAuthManager');
    
    // Stop monitoring intervals
    if (this.networkCheckInterval) {
      clearInterval(this.networkCheckInterval);
      this.networkCheckInterval = null;
    }
    
    if (this.sessionValidationInterval) {
      clearInterval(this.sessionValidationInterval);
      this.sessionValidationInterval = null;
    }
    
    // Cleanup services
    this.authManager.cleanup();
    this.errorRecovery.cleanup();
    this.sessionManager.cleanup();
    
    // Reset state
    this.activeRetries = 0;
    this.activeRefreshPromise = null;
  }

  // Private methods

  private startNetworkMonitoring(): void {
    const config = this.DEFAULT_NETWORK_CONFIG;
    
    this.networkCheckInterval = setInterval(async () => {
      try {
        this.isNetworkConnected = await this.checkNetworkConnectivity();
      } catch (error) {
        this.logger.error('Network check failed', error as Error);
        this.isNetworkConnected = false;
      }
    }, config.networkCheckIntervalMs);
  }

  private startSessionValidation(): void {
    const config = this.DEFAULT_SESSION_VALIDATION_CONFIG;
    
    this.sessionValidationInterval = setInterval(async () => {
      try {
        await this.validateCurrentSession();
      } catch (error) {
        this.logger.error('Session validation failed', error as Error);
      }
    }, config.validationIntervalMs);
  }

  private async checkNetworkConnectivity(): Promise<boolean> {
    try {
      // Simple connectivity check - try to resolve a DNS name
      const { promisify } = require('util');
      const dns = require('dns');
      const lookup = promisify(dns.lookup);
      
      await lookup('google.com');
      return true;
    } catch (error) {
      this.logger.debug('Network connectivity check failed', { error: error as Error });
      return false;
    }
  }

  private async validateCurrentSession(): Promise<void> {
    try {
      const session = await this.authManager.getCurrentSession();
      
      if (session && this.shouldRefreshSession(session)) {
        this.logger.debug('Session validation triggered refresh');
        await this.refreshSession();
      }
    } catch (error) {
      this.logger.error('Session validation failed', error as Error);
    }
  }

  private shouldRefreshSession(session: any): boolean {
    if (!session?.expires_at) {
      return false;
    }

    const config = this.DEFAULT_SESSION_VALIDATION_CONFIG;
    const expiryTime = new Date(session.expires_at).getTime();
    const now = Date.now();
    const timeUntilExpiry = expiryTime - now;
    const refreshThreshold = config.refreshThresholdMinutes * 60 * 1000;

    return timeUntilExpiry <= refreshThreshold && timeUntilExpiry > 0;
  }

  private handleSessionExpiry(reason: 'inactivity' | 'timeout'): void {
    this.logger.warn('Handling session expiry', { reason });
    
    // Record the session expiry as an auth failure
    this.errorRecovery.recordAuthAttempt(false, `Session expired: ${reason}`);
    
    // End the session
    this.sessionManager.endSession();
  }
}