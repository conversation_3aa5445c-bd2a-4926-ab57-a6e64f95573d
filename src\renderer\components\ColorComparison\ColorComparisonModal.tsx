/**
 * @file ColorComparisonModal.tsx
 * @description Modal for comparing colors using the compact interface.
 * Features:
 * - Color libraries (Saved, Pantone, RAL)
 * - List/grid view toggle
 * - Color sorting by hue in grid view (default)
 * - Search functionality
 * - Direct color palette building for analysis
 */

import React, { useState, useEffect, useCallback, useMemo, useRef, useLayoutEffect } from 'react';
import { X, Search, Grid as GridIcon, List as ListIcon } from 'lucide-react';
import { FixedSizeList, FixedSizeGrid } from 'react-window';
import { useColorComparisonStore } from '../../store/colorComparison.store';
import { useColorStore } from '../../store/color.store';
import { ColorEntry } from '../../../shared/types/color.types';
import { ColorInterface } from './ColorInterface';
import { useTokens } from '../../hooks/useTokens';
import KeyboardShortcutsHelp from './KeyboardShortcutsHelp';
import { isNotNullish, safeArrayAccess } from '../../../shared/types/type-guards';

interface ColorComparisonModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type ColorLibraryTab = 'saved' | 'pantone' | 'ral';
type ViewMode = 'list' | 'grid';

const ColorComparisonModal: React.FC<ColorComparisonModalProps> = ({
  open,
  onOpenChange,
}) => {
  const { 
    comparisonColors, 
    addColorToComparison,
    selectedColorIds,
    isMultiSelectMode,
    toggleMultiSelectMode,
    toggleColorSelection,
    selectAllColors,
    clearSelection,
    addSelectedColorsToComparison
  } = useColorComparisonStore();
  const { colors, pantoneColors, ralColors } = useColorStore();
  const tokens = useTokens();

  const [selectedTab, setSelectedTab] = useState<ColorLibraryTab>('saved');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredColors, setFilteredColors] = useState<ColorEntry[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [containerHeight, setContainerHeight] = useState(400); // Reasonable default
  const [containerWidth, setContainerWidth] = useState(200); // Reasonable default
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedColorIndex, setSelectedColorIndex] = useState<number | null>(null);

  // Keyboard shortcuts handler
  useEffect(() => {
    if (!open) {
      return;
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      // Tab switching: Cmd/Ctrl + 1-3
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case '1':
            e.preventDefault();
            setSelectedTab('saved');
            break;
          case '2':
            e.preventDefault();
            setSelectedTab('pantone');
            break;
          case '3':
            e.preventDefault();
            setSelectedTab('ral');
            break;
          case 'm':
            e.preventDefault();
            toggleMultiSelectMode();
            break;
          case 'a':
            if (isMultiSelectMode) {
              e.preventDefault();
              selectAllColors(filteredColors);
            }
            break;
          case 'Enter':
            if (isMultiSelectMode && selectedColorIds.size > 0) {
              e.preventDefault();
              addSelectedColorsToComparison();
            }
            break;
        }
      }

      // Escape: Clear selection or exit multi-select
      if (e.key === 'Escape') {
        if (isMultiSelectMode) {
          if (selectedColorIds.size > 0) {
            clearSelection();
          } else {
            toggleMultiSelectMode();
          }
        }
      }

      // Space: Quick copy selected color
      if (e.key === ' ' && selectedColorIndex !== null && filteredColors[selectedColorIndex]) {
        e.preventDefault();
        const color = filteredColors[selectedColorIndex];
        navigator.clipboard.writeText(color.hex);
      }

      // Delete: Remove selected color from comparison
      if (e.key === 'Delete' && selectedColorIndex !== null && filteredColors[selectedColorIndex]) {
        e.preventDefault();
        // This would need to be implemented in the ColorInterface component
      }

      // Arrow navigation
      if (viewMode === 'grid' && filteredColors.length > 0) {
        const columnsCount = Math.floor(containerWidth / 80);
        const currentIndex = selectedColorIndex ?? -1;

        switch (e.key) {
          case 'ArrowUp':
            e.preventDefault();
            setSelectedColorIndex(Math.max(0, currentIndex - columnsCount));
            break;
          case 'ArrowDown':
            e.preventDefault();
            setSelectedColorIndex(Math.min(filteredColors.length - 1, currentIndex + columnsCount));
            break;
          case 'ArrowLeft':
            e.preventDefault();
            setSelectedColorIndex(Math.max(0, currentIndex - 1));
            break;
          case 'ArrowRight':
            e.preventDefault();
            setSelectedColorIndex(Math.min(filteredColors.length - 1, currentIndex + 1));
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [open, selectedTab, viewMode, selectedColorIndex, filteredColors, containerWidth, isMultiSelectMode, selectedColorIds, toggleMultiSelectMode, selectAllColors, clearSelection, addSelectedColorsToComparison]);

  // Update container dimensions with minimum bounds
  useLayoutEffect(() => {
    if (!open || !containerRef.current) {
      return;
    }

    const updateDimensions = () => {
      if (containerRef.current) {
        // Get the inner dimensions (clientWidth/Height includes padding)
        // const rect = containerRef.current.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(containerRef.current);
        const paddingLeft = parseFloat(computedStyle.paddingLeft);
        const paddingRight = parseFloat(computedStyle.paddingRight);
        const paddingTop = parseFloat(computedStyle.paddingTop);
        const paddingBottom = parseFloat(computedStyle.paddingBottom);
        
        // Calculate inner dimensions excluding padding
        const innerWidth = containerRef.current.clientWidth - paddingLeft - paddingRight;
        const innerHeight = containerRef.current.clientHeight - paddingTop - paddingBottom;
        
        // Ensure minimum dimensions to prevent disappearing content
        const height = Math.max(200, innerHeight);
        const width = Math.max(180, innerWidth);
        setContainerHeight(height);
        setContainerWidth(width);
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [open]);

  // Filter colors based on selected tab and search query
  useEffect(() => {
    let libraryColors: ColorEntry[] = [];

    // Get colors based on selected tab
    switch (selectedTab) {
      case 'saved':
        libraryColors = colors;
        break;
      case 'pantone':
        libraryColors = pantoneColors;
        break;
      case 'ral':
        libraryColors = ralColors;
        break;
      default:
        libraryColors = colors;
    }

    // Filter out colors already in comparison
    libraryColors = libraryColors.filter(
      (color) => !comparisonColors.some((c) => c.id === color.id)
    );

    // Apply search filter if there is a query
    if (searchQuery) {
      libraryColors = libraryColors.filter(color =>
        color.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        color.hex?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (color.product && color.product.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (color.name && color.name.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    setFilteredColors(libraryColors);
  }, [selectedTab, colors, pantoneColors, ralColors, comparisonColors, searchQuery]);

  // Function to convert hex to HSL for sorting
  const hexToHsl = (hex: string): { h: number, s: number, l: number } => {
    // Remove the hash if it exists
    hex = hex.replace(/^#/, '');

    // Parse the r, g, b values
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }

      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  };

  // Sort colors by hue
  const sortColorsByHue = (colors: ColorEntry[]): ColorEntry[] => {
    return [...colors].sort((a, b) => {
      const hslA = hexToHsl(a.hex);
      const hslB = hexToHsl(b.hex);

      // If hues are similar (within 15 degrees), sort by saturation and lightness
      if (Math.abs(hslA.h - hslB.h) < 15) {
        // Sort by saturation (highest first), then by lightness (highest first)
        if (Math.abs(hslA.s - hslB.s) > 10) {
          return hslB.s - hslA.s; // Higher saturation first
        }
        return hslB.l - hslA.l; // Higher lightness first
      }

      // Otherwise sort by hue
      return hslA.h - hslB.h;
    });
  };

  // Get the colors to display based on view mode (always sort by hue for grid view)
  const getDisplayColors = (): ColorEntry[] => {
    if (viewMode === 'grid') {
      return sortColorsByHue(filteredColors);
    }
    return filteredColors;
  };

  // Virtualized List Item Renderer
  const ListItemRenderer = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const color = safeArrayAccess(getDisplayColors(), index);
    
    // Defensive rendering - return empty div if color is not available
    if (!isNotNullish(color)) {
      return <div style={style} />;
    }
    
    return (
      <div style={style}>
        <div
          className="flex items-center p-2 mx-2 rounded-md cursor-pointer hover:bg-ui-background-hover transition-colors group"
          onClick={() => addColorToComparison(color)}
        >
          <div
            className="w-8 h-8 rounded-md mr-3 border border-ui-border shadow-sm group-hover:shadow-md transition-shadow"
            style={{ backgroundColor: color.hex }}
          />
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-ui-text-primary truncate">{color.code || 'Unnamed'}</div>
            <div className="text-xs font-mono text-ui-text-tertiary truncate">{color.hex}</div>
          </div>
        </div>
      </div>
    );
  }, [filteredColors, viewMode, addColorToComparison]);

  // Calculate grid dimensions with safety bounds
  const gridColumnCount = useMemo(() => {
    const width = containerWidth;
    if (width < 200) {return 2;} // Reduced minimum to 2 columns for very small windows
    if (width < 240) {return 3;}
    if (width < 280) {return 4;}
    return 5;
  }, [containerWidth]);

  const gridItemSize = useMemo(() => {
    // Calculate size to fit exactly within container width without horizontal scroll
    // Account for padding (p-1 = 4px) on each side of grid items
    const paddingPerItem = 8; // 4px left + 4px right from p-1
    const totalPadding = gridColumnCount * paddingPerItem;
    const availableWidth = containerWidth - totalPadding;
    const calculatedSize = Math.floor(availableWidth / gridColumnCount);
    return Math.max(40, calculatedSize);
  }, [containerWidth, gridColumnCount]);

  // Virtualized Grid Item Renderer
  const GridItemRenderer = useCallback(({ columnIndex, rowIndex, style }: { columnIndex: number; rowIndex: number; style: React.CSSProperties }) => {
    const index = rowIndex * gridColumnCount + columnIndex;
    const colors = getDisplayColors();
    
    if (index >= colors.length) {
      return null;
    }
    
    const color = safeArrayAccess(colors, index);
    
    // Defensive rendering - return empty div if color is not available
    if (!isNotNullish(color)) {
      return <div style={style} className="p-1" />;
    }
    
    const isSelected = selectedColorIds.has(color.id);
    
    const handleClick = () => {
      if (isMultiSelectMode) {
        toggleColorSelection(color.id);
      } else {
        addColorToComparison(color);
      }
    };
    
    return (
      <div style={style} className="p-1">
        <div
          className="relative group h-full"
          onClick={handleClick}
          title={`${color.code || 'Unnamed'} - ${color.hex}`}
        >
          <div
            className={`w-full h-full aspect-square rounded-md border cursor-pointer transition-all duration-200 ease-in-out group-hover:shadow-lg group-hover:scale-105 group-hover:z-10 ${
              isSelected 
                ? 'border-brand-primary border-2 ring-2 ring-brand-primary/20' 
                : 'border-ui-border'
            }`}
            style={{
              backgroundColor: color.hex,
              transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`
            }}
          />
          
          {/* Multi-select checkbox */}
          {isMultiSelectMode && (
            <div className="absolute top-1 right-1 z-10">
              <div className={`w-4 h-4 rounded-sm border-2 bg-white flex items-center justify-center ${
                isSelected ? 'border-brand-primary bg-brand-primary' : 'border-gray-400'
              }`}>
                {isSelected && (
                  <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            </div>
          )}
          
          {/* Info tooltip on hover */}
          <div className="absolute left-1/2 transform -translate-x-1/2 -bottom-1 opacity-0 group-hover:opacity-100 group-hover:bottom-0 transition-all whitespace-nowrap pointer-events-none z-20"
            style={{
              transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`
            }}>
            <div className="bg-ui-background-primary text-ui-text-primary text-xs leading-tight px-2 py-1 rounded-md shadow-lg border border-ui-border backdrop-blur-sm">
              {color.code || 'Unnamed'}
              {isMultiSelectMode && (
                <div className="text-brand-primary text-xs mt-0.5">
                  {isSelected ? 'Selected' : 'Click to select'}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }, [filteredColors, viewMode, addColorToComparison, tokens, gridColumnCount, isMultiSelectMode, selectedColorIds, toggleColorSelection]);

  if (!open) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[999] flex items-center justify-center">
      <div 
        className="bg-ui-background-primary w-full h-full rounded-[var(--radius-lg)] shadow-[var(--shadow-xl)] overflow-hidden flex flex-col z-[1000] relative"
        style={{
          transition: `all ${tokens.transitions.duration[300]} ${tokens.transitions.easing.apple}`,
          width: 'calc(100vw - 2rem)',
          height: window.navigator.userAgent.includes('Mac') ? 'calc(100vh - 4rem)' : 'calc(100vh - 2rem)',
          maxWidth: '100vw',
          maxHeight: '100vh',
          margin: window.navigator.userAgent.includes('Mac') ? '3rem 1rem 1rem 1rem' : '1rem'
        }}
      >
        {/* Header */}
        <div 
          className="px-[var(--spacing-4)] py-[var(--spacing-2)] border-b border-ui-border-light flex items-center justify-between bg-ui-background-primary"
          style={{
            paddingTop: window.navigator.userAgent.includes('Mac') ? '40px' : 'var(--spacing-2)'
          }}
        >
          <h2 className="text-[var(--fontSize-lg)] font-semibold text-ui-foreground-primary">Colour Analysis</h2>
          <div className="flex items-center gap-[var(--spacing-2)]">
            <KeyboardShortcutsHelp />
            <button
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 rounded-full inline-flex items-center justify-center text-ui-foreground-secondary hover:text-ui-foreground-primary hover:bg-ui-background-hover pointer-events-auto relative z-[1001]"
              style={{
                transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
              }}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        <div className="flex h-full overflow-hidden">
          {/* Color Library Panel (Left) - Fixed width for consistency */}
          <div className="w-[300px] border-r border-ui-border flex flex-col overflow-hidden bg-ui-background-quaternary flex-shrink-0">
            {/* Search bar - Enhanced with better spacing */}
            <div className="p-[var(--spacing-2)] border-b border-ui-border bg-ui-background-tertiary">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search..."
                  className="w-full py-[var(--spacing-1.5)] px-[var(--spacing-2)] pl-[var(--spacing-8)] rounded-[var(--radius-md)] bg-ui-background-primary border border-ui-border text-ui-text-primary focus:outline-none focus:ring-2 focus:ring-brand-primary text-[var(--fontSize-sm)]"
                />
                <div className="absolute inset-y-0 left-0 pl-[var(--spacing-2)] flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-ui-text-tertiary" />
                </div>
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute inset-y-0 right-0 pr-[var(--spacing-2)] flex items-center text-ui-text-tertiary hover:text-ui-text-primary"
                    style={{
                      transition: `color ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                    }}
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Color count indicator with integrated tabs */}
            <div className="bg-ui-background-quaternary border-b border-ui-border">
              {/* Tab toggle controls - Optimized for narrow sidebar */}
              <div className="p-[var(--spacing-1)]">
                <div className="bg-ui-background-primary rounded-[var(--radius-md)] p-[var(--spacing-0.5)] flex items-center shadow-[var(--shadow-sm)] mx-[var(--spacing-1)]">
                  <button
                    className={`px-[var(--spacing-2)] py-[var(--spacing-1)] text-[var(--fontSize-xs)] font-medium rounded-[var(--radius-sm)] flex items-center justify-center flex-1 ${
                      selectedTab === 'saved'
                        ? 'bg-brand-primary/10 text-brand-primary'
                        : 'text-ui-text-secondary hover:text-ui-text-primary hover:bg-ui-background-hover'
                    }`}
                    style={{
                      transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`
                    }}
                    onClick={() => setSelectedTab('saved')}
                  >
                    <span>Saved</span>
                  </button>
                  <button
                    className={`px-[var(--spacing-2)] py-[var(--spacing-1)] text-[var(--fontSize-xs)] font-medium rounded-[var(--radius-sm)] flex items-center justify-center flex-1 ${
                      selectedTab === 'pantone'
                        ? 'bg-brand-primary/10 text-brand-primary'
                        : 'text-ui-text-secondary hover:text-ui-text-primary hover:bg-ui-background-hover'
                    }`}
                    style={{
                      transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`
                    }}
                    onClick={() => setSelectedTab('pantone')}
                  >
                    <span>Pantone</span>
                  </button>
                  <button
                    className={`px-[var(--spacing-2)] py-[var(--spacing-1)] text-[var(--fontSize-xs)] font-medium rounded-[var(--radius-sm)] flex items-center justify-center flex-1 ${
                      selectedTab === 'ral'
                        ? 'bg-brand-primary/10 text-brand-primary'
                        : 'text-ui-text-secondary hover:text-ui-text-primary hover:bg-ui-background-hover'
                    }`}
                    style={{
                      transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`
                    }}
                    onClick={() => setSelectedTab('ral')}
                  >
                    <span>RAL</span>
                  </button>
                </div>
                
                {/* View mode buttons - Separate row */}
                <div className="flex items-center justify-center gap-[var(--spacing-1)] mt-[var(--spacing-1)]">
                  <button
                    className={`p-[var(--spacing-1)] rounded-[var(--radius-sm)] ${
                      viewMode === 'grid'
                        ? 'bg-brand-primary/10 text-brand-primary'
                        : 'text-ui-text-secondary hover:text-ui-text-primary'
                    }`}
                    style={{
                      transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                    }}
                    onClick={() => setViewMode('grid')}
                    title="Grid View"
                  >
                    <GridIcon className="h-3 w-3" />
                  </button>
                  <button
                    className={`p-[var(--spacing-1)] rounded-[var(--radius-sm)] ${
                      viewMode === 'list'
                        ? 'bg-brand-primary/10 text-brand-primary'
                        : 'text-ui-text-secondary hover:text-ui-text-primary'
                    }`}
                    style={{
                      transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                    }}
                    onClick={() => setViewMode('list')}
                    title="List View"
                  >
                    <ListIcon className="h-3 w-3" />
                  </button>
                </div>
              </div>

              {/* Color count and multi-select controls */}
              <div className="px-[var(--spacing-3)] py-[var(--spacing-1)]">
                <div className="flex items-center justify-between">
                  <span className="text-[var(--fontSize-xs)] font-medium text-ui-text-secondary">
                    {filteredColors.length} {filteredColors.length === 1 ? 'colour' : 'colours'}
                  </span>
                  
                  {/* Multi-select toggle button */}
                  <button
                    onClick={toggleMultiSelectMode}
                    className={`px-2 py-1 text-xs rounded-md transition-all ${
                      isMultiSelectMode 
                        ? 'bg-brand-primary text-white' 
                        : 'bg-ui-background-hover text-ui-text-secondary hover:text-ui-text-primary'
                    }`}
                    title="Toggle multi-select mode (M)"
                  >
                    {isMultiSelectMode ? 'Exit Multi-Select' : 'Multi-Select'}
                  </button>
                </div>
                
                {/* Multi-select actions */}
                {isMultiSelectMode && (
                  <div className="mt-2 flex items-center gap-2">
                    <span className="text-xs text-brand-primary">
                      {selectedColorIds.size} selected
                    </span>
                    <button
                      onClick={() => selectAllColors(filteredColors)}
                      className="text-xs text-ui-text-secondary hover:text-ui-text-primary"
                      title="Select all (A)"
                    >
                      Select All
                    </button>
                    {selectedColorIds.size > 0 && (
                      <>
                        <button
                          onClick={clearSelection}
                          className="text-xs text-ui-text-secondary hover:text-ui-text-primary"
                          title="Clear selection (Esc)"
                        >
                          Clear
                        </button>
                        <button
                          onClick={addSelectedColorsToComparison}
                          className="text-xs bg-brand-primary text-white px-2 py-0.5 rounded hover:bg-brand-primary-hover"
                          title="Add selected to comparison (Enter)"
                        >
                          Add {selectedColorIds.size} to Comparison
                        </button>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Color list/grid - Virtualized for performance */}
            <div className="flex-1 relative overflow-y-auto overflow-x-hidden p-2" ref={containerRef}>
              {filteredColors.length > 0 && containerHeight > 0 && containerWidth > 0 ? (
                viewMode === 'list' ? (
                  <FixedSizeList
                    height={containerHeight}
                    itemCount={getDisplayColors().length}
                    itemSize={52} // Height of each list item
                    width={containerWidth}
                    overscanCount={5}
                  >
                    {ListItemRenderer}
                  </FixedSizeList>
                ) : (
                  <FixedSizeGrid
                    height={containerHeight}
                    columnCount={Math.max(1, gridColumnCount)} // Ensure at least 1 column
                    columnWidth={Math.max(40, gridItemSize)} // Ensure minimum width
                    rowCount={Math.max(1, Math.ceil(getDisplayColors().length / gridColumnCount))}
                    rowHeight={Math.max(40, gridItemSize)} // Ensure minimum height
                    width={containerWidth}
                    overscanRowCount={2}
                    overscanColumnCount={1}
                  >
                    {GridItemRenderer}
                  </FixedSizeGrid>
                )
              ) : (
                <div className="text-center py-8 text-ui-text-secondary">
                  {searchQuery ? (
                    <div>
                      <p className="mb-1">No colours match your search.</p>
                      <p className="text-sm">Try adjusting your search terms.</p>
                    </div>
                  ) : (
                    <div>
                      <p className="mb-1">No colours available.</p>
                      <p className="text-sm">Add some colours to your library or switch tabs.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Compact Color Interface (Right) */}
          <div className="flex-1 overflow-hidden">
            <ColorInterface />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ColorComparisonModal;
