import React, { useEffect, useState } from 'react';
import {
  getSharedFolderPath,
  listSharedFolderFiles,
  loadJsonFromSharedFolder,
} from '../utils/sharedFolderUtils';
import { SharedFolderFile } from '../../shared/types/shared-folder';

interface ColorData {
  id: string;
  product: string;
  name: string;
  code: string;
  hex: string;
  cmyk: string;
  notes: string;
}

const SharedFolderInfo: React.FC = () => {
  const [folderPath, setFolderPath] = useState<string>('');
  const [files, setFiles] = useState<SharedFolderFile[]>([]);
  const [colors, setColors] = useState<ColorData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadSharedFolderData() {
      try {
        setIsLoading(true);
        setError(null);

        // Get shared folder path
        const path = await getSharedFolderPath();
        setFolderPath(path);

        // List files in shared folder
        const fileList = await listSharedFolderFiles();
        setFiles(fileList);

        // Load color data if available
        if (fileList.some(file => file.name === 'colors.json')) {
          const colorData =
            await loadJsonFromSharedFolder<ColorData[]>('colors.json');
          if (colorData) {
            setColors(colorData);
          }
        }
      } catch (err) {
        setError(
          `Error loading shared folder data: ${err instanceof Error ? err.message : String(err)}`
        );
        console.error('Error in SharedFolderInfo:', err);
      } finally {
        setIsLoading(false);
      }
    }

    loadSharedFolderData();
  }, []);

  if (isLoading) {
    return <div className='p-4'>Loading shared folder data...</div>;
  }

  if (error) {
    return <div className='p-4 text-red-600'>{error}</div>;
  }

  return (
    <div className='p-4 bg-white rounded-lg shadow'>
      <h2 className='text-xl font-bold mb-3'>Shared Folder Information</h2>

      <div className='mb-4'>
        <h3 className='text-lg font-semibold'>Folder Path</h3>
        <p className='bg-gray-100 p-2 rounded'>{folderPath}</p>
      </div>

      <div className='mb-4'>
        <h3 className='text-lg font-semibold'>Files ({files.length})</h3>
        {files.length === 0 ? (
          <p>No files found in shared folder.</p>
        ) : (
          <ul className='bg-gray-100 p-2 rounded divide-y divide-gray-200'>
            {files.map(file => (
              <li key={file.path} className='py-1'>
                <span className='font-medium'>{file.name}</span>
                {!file.isDirectory && file.size && (
                  <span className='text-sm text-gray-600 ml-2'>
                    ({(file.size / 1024).toFixed(1)} KB)
                  </span>
                )}
                {file.isDirectory && (
                  <span className='text-sm bg-blue-100 text-blue-800 px-2 rounded ml-2'>
                    Directory
                  </span>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>

      {colors.length > 0 && (
        <div className='mb-4'>
          <h3 className='text-lg font-semibold'>
            Colour Data ({colors.length})
          </h3>
          <div className='overflow-x-auto'>
            <table className='min-w-full bg-gray-100 rounded'>
              <thead>
                <tr className='bg-gray-200'>
                  <th className='p-2 text-left'>Product</th>
                  <th className='p-2 text-left'>Name</th>
                  <th className='p-2 text-left'>Code</th>
                  <th className='p-2 text-left'>Colour</th>
                </tr>
              </thead>
              <tbody>
                {colors.map(color => (
                  <tr key={color.id} className='border-t border-gray-300'>
                    <td className='p-2'>{color.product}</td>
                    <td className='p-2'>{color.name}</td>
                    <td className='p-2'>{color.code}</td>
                    <td className='p-2'>
                      <div className='flex items-center'>
                        <div
                          className='w-6 h-6 rounded-full mr-2'
                          style={{ backgroundColor: color.hex }}
                        />
                        {color.hex}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      <div className='text-sm text-gray-600 mt-4'>
        <p>
          This component demonstrates how to use the shared folder functionality
          in the ChromaSync application. Files stored in this location are
          accessible across platforms.
        </p>
      </div>
    </div>
  );
};

export default SharedFolderInfo;
