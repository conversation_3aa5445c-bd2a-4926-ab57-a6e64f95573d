/**
 * Database Transaction Management System
 * Provides comprehensive transaction support for better-sqlite3 with logging and rollback capabilities
 */

import { v4 as uuidv4 } from 'uuid';
import type { Database } from 'better-sqlite3';
import { getPooledConnection, releasePooledConnection } from './connection';

export interface TransactionOptions {
  /** Transaction isolation level (better-sqlite3 supports DEFERRED, IMMEDIATE, EXCLUSIVE) */
  mode?: 'DEFERRED' | 'IMMEDIATE' | 'EXCLUSIVE';
  /** Enable detailed transaction logging */
  enableLogging?: boolean;
  /** Maximum transaction duration in milliseconds */
  timeout?: number;
}

export interface TransactionLogEntry {
  id: string;
  transactionId: string;
  operation: string;
  tableName?: string;
  recordId?: string;
  beforeData?: unknown;
  afterData?: unknown;
  timestamp: number;
  duration?: number;
  error?: string;
}

export interface TransactionResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  transactionId: string;
  duration: number;
  logEntries: TransactionLogEntry[];
}

export interface SavepointInfo {
  name: string;
  transactionId: string;
  createdAt: number;
}

/**
 * Database Transaction Manager
 * Provides transaction support with logging, savepoints, and rollback capabilities
 */
export class DatabaseTransactionManager {
  private static instance: DatabaseTransactionManager | null = null;
  private transactionLogs: Map<string, TransactionLogEntry[]> = new Map();
  private activeSavepoints: Map<string, SavepointInfo[]> = new Map();
  private readonly maxLogEntries = 1000; // Prevent memory leaks
  
  /**
   * Get singleton instance
   */
  static getInstance(): DatabaseTransactionManager {
    if (!DatabaseTransactionManager.instance) {
      DatabaseTransactionManager.instance = new DatabaseTransactionManager();
    }
    return DatabaseTransactionManager.instance;
  }
  
  private constructor() {
    // Private constructor for singleton pattern
  }
  
  /**
   * Execute operation within a database transaction
   */
  async executeInTransaction<T>(
    operation: (db: Database, transactionId: string) => Promise<T> | T,
    options: TransactionOptions = {}
  ): Promise<TransactionResult<T>> {
    const transactionId = uuidv4();
    const startTime = Date.now();
    const logEntries: TransactionLogEntry[] = [];
    
    const {
      mode = 'IMMEDIATE',
      enableLogging = true,
      timeout = 30000
    } = options;
    
    let connection: Database | null = null;
    let timeoutHandle: NodeJS.Timeout | null = null;
    
    try {
      // Get database connection
      connection = await getPooledConnection();
      
      if (enableLogging) {
        this.logOperation(transactionId, 'TRANSACTION_START', undefined, undefined, undefined, logEntries);
      }
      
      // Set up transaction timeout
      if (timeout > 0) {
        timeoutHandle = setTimeout(() => {
          throw new Error(`Transaction timeout after ${timeout}ms`);
        }, timeout);
      }
      
      // Start transaction with specified mode
      const transaction = connection.transaction((db: Database) => {
        return operation(db, transactionId);
      });
      
      // Configure transaction mode
      if (mode !== 'DEFERRED') {
        if (mode === 'IMMEDIATE') {
          transaction.immediate = true;
        } else if (mode === 'EXCLUSIVE') {
          transaction.exclusive = true;
        }
      }
      
      // Execute transaction - better-sqlite3 transactions are synchronous
      // but our operation might be async, so we need to handle both cases
      const result = await Promise.resolve(transaction());
      
      if (enableLogging) {
        this.logOperation(transactionId, 'TRANSACTION_COMMIT', undefined, undefined, undefined, logEntries);
      }
      
      const duration = Date.now() - startTime;
      
      // Store logs for debugging
      if (enableLogging) {
        this.storeTransactionLogs(transactionId, logEntries);
      }
      
      return {
        success: true,
        result,
        transactionId,
        duration,
        logEntries
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const transactionError = error as Error;
      
      if (enableLogging) {
        this.logOperation(
          transactionId, 
          'TRANSACTION_ROLLBACK', 
          undefined, 
          undefined, 
          undefined, 
          logEntries,
          transactionError.message
        );
        this.storeTransactionLogs(transactionId, logEntries);
      }
      
      console.error(`[TransactionManager] Transaction ${transactionId} failed:`, transactionError);
      
      return {
        success: false,
        error: transactionError,
        transactionId,
        duration,
        logEntries
      };
      
    } finally {
      // Clear timeout
      if (timeoutHandle) {
        clearTimeout(timeoutHandle);
      }
      
      // Release connection
      if (connection) {
        releasePooledConnection(connection);
      }
      
      // Clean up savepoints for this transaction
      this.activeSavepoints.delete(transactionId);
    }
  }
  
  /**
   * Create a savepoint within a transaction
   */
  async createSavepoint(
    connection: Database, 
    transactionId: string, 
    savepointName?: string
  ): Promise<string> {
    const name = savepointName || `sp_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
    
    try {
      connection.exec(`SAVEPOINT ${name}`);
      
      // Track savepoint
      if (!this.activeSavepoints.has(transactionId)) {
        this.activeSavepoints.set(transactionId, []);
      }
      
      const savepoints = this.activeSavepoints.get(transactionId)!;
      savepoints.push({
        name,
        transactionId,
        createdAt: Date.now()
      });
      
      console.log(`[TransactionManager] Created savepoint ${name} for transaction ${transactionId}`);
      return name;
      
    } catch (error) {
      console.error(`[TransactionManager] Failed to create savepoint ${name}:`, error);
      throw error;
    }
  }
  
  /**
   * Rollback to a specific savepoint
   */
  async rollbackToSavepoint(
    connection: Database, 
    transactionId: string, 
    savepointName: string
  ): Promise<void> {
    try {
      connection.exec(`ROLLBACK TO SAVEPOINT ${savepointName}`);
      
      // Remove savepoints created after this one
      const savepoints = this.activeSavepoints.get(transactionId);
      if (savepoints) {
        const savepointIndex = savepoints.findIndex(sp => sp.name === savepointName);
        if (savepointIndex >= 0) {
          savepoints.splice(savepointIndex + 1);
        }
      }
      
      console.log(`[TransactionManager] Rolled back to savepoint ${savepointName} for transaction ${transactionId}`);
      
    } catch (error) {
      console.error(`[TransactionManager] Failed to rollback to savepoint ${savepointName}:`, error);
      throw error;
    }
  }
  
  /**
   * Release a savepoint (commit changes up to that point)
   */
  async releaseSavepoint(
    connection: Database, 
    transactionId: string, 
    savepointName: string
  ): Promise<void> {
    try {
      connection.exec(`RELEASE SAVEPOINT ${savepointName}`);
      
      // Remove the savepoint from tracking
      const savepoints = this.activeSavepoints.get(transactionId);
      if (savepoints) {
        const index = savepoints.findIndex(sp => sp.name === savepointName);
        if (index >= 0) {
          savepoints.splice(index, 1);
        }
      }
      
      console.log(`[TransactionManager] Released savepoint ${savepointName} for transaction ${transactionId}`);
      
    } catch (error) {
      console.error(`[TransactionManager] Failed to release savepoint ${savepointName}:`, error);
      throw error;
    }
  }
  
  /**
   * Log a transaction operation
   */
  private logOperation(
    transactionId: string,
    operation: string,
    tableName?: string,
    recordId?: string,
    data?: { before?: unknown; after?: unknown },
    logEntries?: TransactionLogEntry[],
    error?: string
  ): void {
    const logEntry: TransactionLogEntry = {
      id: uuidv4(),
      transactionId,
      operation,
      tableName,
      recordId,
      beforeData: data?.before,
      afterData: data?.after,
      timestamp: Date.now(),
      error
    };
    
    if (logEntries) {
      logEntries.push(logEntry);
    }
    
    console.log(`[TransactionManager] ${operation}${tableName ? ` on ${tableName}` : ''}${recordId ? ` (${recordId})` : ''}${error ? ` - ERROR: ${error}` : ''}`);
  }
  
  /**
   * Store transaction logs for debugging
   */
  private storeTransactionLogs(transactionId: string, logEntries: TransactionLogEntry[]): void {
    this.transactionLogs.set(transactionId, logEntries);
    
    // Prevent memory leaks by limiting stored logs
    if (this.transactionLogs.size > this.maxLogEntries) {
      const oldestKey = this.transactionLogs.keys().next().value;
      if (oldestKey) {
        this.transactionLogs.delete(oldestKey);
      }
    }
  }
  
  /**
   * Get transaction logs for debugging
   */
  getTransactionLogs(transactionId: string): TransactionLogEntry[] {
    return this.transactionLogs.get(transactionId) || [];
  }
  
  /**
   * Get all transaction logs (for debugging)
   */
  getAllTransactionLogs(): Map<string, TransactionLogEntry[]> {
    return new Map(this.transactionLogs);
  }
  
  /**
   * Clear old transaction logs
   */
  clearOldLogs(olderThanMs: number = 24 * 60 * 60 * 1000): number {
    const cutoffTime = Date.now() - olderThanMs;
    let clearedCount = 0;
    
    for (const [transactionId, logs] of this.transactionLogs.entries()) {
      const hasRecentLogs = logs.some(log => log.timestamp > cutoffTime);
      if (!hasRecentLogs) {
        this.transactionLogs.delete(transactionId);
        clearedCount++;
      }
    }
    
    console.log(`[TransactionManager] Cleared ${clearedCount} old transaction logs`);
    return clearedCount;
  }
  
  /**
   * Get active savepoints for a transaction
   */
  getActiveSavepoints(transactionId: string): SavepointInfo[] {
    return this.activeSavepoints.get(transactionId) || [];
  }
  
  /**
   * Get transaction statistics
   */
  getStats(): {
    activeTransactions: number;
    totalLoggedTransactions: number;
    totalSavepoints: number;
    memoryUsage: {
      logEntries: number;
      savepointEntries: number;
    };
  } {
    let totalSavepoints = 0;
    for (const savepoints of this.activeSavepoints.values()) {
      totalSavepoints += savepoints.length;
    }
    
    let totalLogEntries = 0;
    for (const logs of this.transactionLogs.values()) {
      totalLogEntries += logs.length;
    }
    
    return {
      activeTransactions: this.activeSavepoints.size,
      totalLoggedTransactions: this.transactionLogs.size,
      totalSavepoints,
      memoryUsage: {
        logEntries: totalLogEntries,
        savepointEntries: totalSavepoints
      }
    };
  }
}

/**
 * Convenience function to execute operation in transaction
 */
export async function executeInTransaction<T>(
  operation: (db: Database, transactionId: string) => Promise<T> | T,
  options?: TransactionOptions
): Promise<TransactionResult<T>> {
  const manager = DatabaseTransactionManager.getInstance();
  return manager.executeInTransaction(operation, options);
}

/**
 * Export singleton instance
 */
export const transactionManager = DatabaseTransactionManager.getInstance();