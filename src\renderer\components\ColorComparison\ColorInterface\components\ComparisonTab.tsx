/**
 * Comparison Tab Component
 * Displays color comparison metrics and recommendations
 */

import { memo } from 'react';
import { AlertTriangle, Info } from 'lucide-react';
import type { ComparisonTabProps } from '../types';

export const ComparisonTab = memo<ComparisonTabProps>(
  ({ selectedColor, secondaryColor, recommendations, similarColors }) => {
    if (!selectedColor || !secondaryColor) {
      return (
        <div className='p-4 text-center text-ui-text-muted'>
          <Info className='mx-auto mb-2 h-8 w-8 opacity-50' />
          <p>Select two colors to compare</p>
        </div>
      );
    }

    return (
      <div className='p-3'>
        {/* Color comparison header */}
        <div
          className='rounded-md p-3 mb-3'
          style={{
            backgroundColor: 'var(--color-ui-background-tertiary)',
            borderRadius: 'var(--radius-md)',
          }}
        >
          <div className='flex items-center gap-3'>
            {/* Primary Color */}
            <div className='flex-1'>
              <div
                className='text-[10px] mb-1'
                style={{
                  color: 'var(--color-ui-foreground-tertiary)',
                  fontSize: 'var(--font-size-xs)',
                }}
              >
                Primary Color
              </div>
              <div className='flex items-center gap-2'>
                <div
                  className='w-12 h-12 rounded-md'
                  style={{
                    backgroundColor: selectedColor.hex,
                    border: `1px solid var(--color-ui-border-light)`,
                    borderRadius: 'var(--radius-md)',
                  }}
                />
                <div>
                  <div
                    className='text-xs font-medium'
                    style={{
                      color: 'var(--color-ui-foreground-primary)',
                      fontWeight: 'var(--font-weight-medium)',
                    }}
                  >
                    {selectedColor.pantone}
                  </div>
                  <div
                    className='text-[10px] font-mono'
                    style={{
                      color: 'var(--color-ui-foreground-tertiary)',
                      fontFamily: 'var(--font-family-mono)',
                    }}
                  >
                    {selectedColor.hex}
                  </div>
                  <div
                    className='text-[10px]'
                    style={{ color: 'var(--color-ui-foreground-tertiary)' }}
                  >
                    {selectedColor.cmyk}
                  </div>
                </div>
              </div>
            </div>

            {/* VS Indicator */}
            <div
              className='text-xs font-bold'
              style={{
                color: 'var(--color-ui-foreground-tertiary)',
                fontWeight: 'var(--font-weight-bold)',
              }}
            >
              VS
            </div>

            {/* Secondary Color */}
            <div className='flex-1'>
              <div
                className='text-[10px] mb-1'
                style={{
                  color: 'var(--color-ui-foreground-tertiary)',
                  fontSize: 'var(--font-size-xs)',
                }}
              >
                Secondary Color
              </div>
              <div className='flex items-center gap-2'>
                <div
                  className='w-12 h-12 rounded-md'
                  style={{
                    backgroundColor: secondaryColor.hex,
                    border: `1px solid var(--color-ui-border-light)`,
                    borderRadius: 'var(--radius-md)',
                  }}
                />
                <div>
                  <div
                    className='text-xs font-medium'
                    style={{
                      color: 'var(--color-ui-foreground-primary)',
                      fontWeight: 'var(--font-weight-medium)',
                    }}
                  >
                    {secondaryColor.pantone}
                  </div>
                  <div
                    className='text-[10px] font-mono'
                    style={{
                      color: 'var(--color-ui-foreground-tertiary)',
                      fontFamily: 'var(--font-family-mono)',
                    }}
                  >
                    {secondaryColor.hex}
                  </div>
                  <div
                    className='text-[10px]'
                    style={{ color: 'var(--color-ui-foreground-tertiary)' }}
                  >
                    {secondaryColor.cmyk}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recommendations */}
        {recommendations.length > 0 && (
          <div className='space-y-2 mb-3'>
            {recommendations.map((rec, idx) => {
              const getBackgroundColor = (severity: string) => {
                switch (severity) {
                  case 'error':
                    return 'var(--feedback-bg-error)';
                  case 'warning':
                    return 'var(--feedback-bg-warning)';
                  default:
                    return 'var(--feedback-bg-info)';
                }
              };

              const getBorderColor = (severity: string) => {
                switch (severity) {
                  case 'error':
                    return 'var(--feedback-border-error)';
                  case 'warning':
                    return 'var(--feedback-border-warning)';
                  default:
                    return 'var(--feedback-border-info)';
                }
              };

              const getIconColor = (severity: string) => {
                switch (severity) {
                  case 'error':
                    return 'var(--color-feedback-error)';
                  case 'warning':
                    return 'var(--color-feedback-warning)';
                  default:
                    return 'var(--color-feedback-info)';
                }
              };

              return (
                <div
                  key={idx}
                  className='p-2 text-xs'
                  style={{
                    backgroundColor: getBackgroundColor(rec.severity || 'info'),
                    border: `1px solid ${getBorderColor(rec.severity || 'info')}`,
                    borderRadius: 'var(--radius-md)',
                    fontSize: 'var(--font-size-xs)',
                  }}
                >
                  <div className='flex items-start gap-2'>
                    {rec.severity === 'error' ? (
                      <AlertTriangle
                        className='h-3 w-3 mt-0.5'
                        style={{ color: getIconColor(rec.severity || 'info') }}
                      />
                    ) : rec.severity === 'warning' ? (
                      <AlertTriangle
                        className='h-3 w-3 mt-0.5'
                        style={{ color: getIconColor(rec.severity || 'info') }}
                      />
                    ) : (
                      <Info
                        className='h-3 w-3 mt-0.5'
                        style={{ color: getIconColor(rec.severity || 'info') }}
                      />
                    )}
                    <div className='flex-1'>
                      <div
                        className='font-medium mb-0.5'
                        style={{
                          fontWeight: 'var(--font-weight-medium)',
                          color: 'var(--color-ui-foreground-primary)',
                        }}
                      >
                        {rec.title}
                      </div>
                      <div
                        className='text-[10px] mb-1'
                        style={{
                          color: 'var(--color-ui-foreground-secondary)',
                          fontSize: 'var(--font-size-xs)',
                        }}
                      >
                        {rec.description}
                      </div>
                      <div
                        className='text-[10px] font-medium'
                        style={{
                          fontSize: 'var(--font-size-xs)',
                          fontWeight: 'var(--font-weight-medium)',
                          color: 'var(--color-ui-foreground-primary)',
                        }}
                      >
                        {rec.actionable}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Similar Colors */}
        {similarColors.length > 0 && (
          <div
            className='rounded-md p-3'
            style={{
              backgroundColor: 'var(--color-ui-background-tertiary)',
              borderRadius: 'var(--radius-md)',
            }}
          >
            <h4
              className='text-xs font-medium mb-2'
              style={{
                fontSize: 'var(--font-size-xs)',
                fontWeight: 'var(--font-weight-medium)',
                color: 'var(--color-ui-foreground-primary)',
              }}
            >
              Similar Colors
            </h4>
            <div className='grid grid-cols-3 gap-2'>
              {similarColors.map(color => (
                <div
                  key={color.id}
                  className='text-center cursor-pointer transition-standard'
                  style={{ cursor: 'pointer' }}
                  onMouseEnter={e => {
                    e.currentTarget.style.opacity = '0.8';
                  }}
                  onMouseLeave={e => {
                    e.currentTarget.style.opacity = '1';
                  }}
                >
                  <div
                    className='w-full aspect-square rounded-md mb-1'
                    style={{
                      backgroundColor: color.hex,
                      border: `1px solid var(--color-ui-border-light)`,
                      borderRadius: 'var(--radius-md)',
                    }}
                  />
                  <div
                    className='text-[9px] font-medium truncate'
                    style={{
                      fontWeight: 'var(--font-weight-medium)',
                      color: 'var(--color-ui-foreground-primary)',
                    }}
                  >
                    {color.pantone}
                  </div>
                  <div
                    className='text-[8px] font-mono'
                    style={{
                      fontFamily: 'var(--font-family-mono)',
                      color: 'var(--color-ui-foreground-tertiary)',
                    }}
                  >
                    {color.hex}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }
);

ComparisonTab.displayName = 'ComparisonTab';
