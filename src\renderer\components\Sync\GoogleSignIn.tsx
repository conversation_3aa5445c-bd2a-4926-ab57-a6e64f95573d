/**
 * @file GoogleSignIn.tsx
 * @description Google Sign-In component for ChromaSync
 */

import React, { useState } from 'react';

interface GoogleSignInProps {
  onSuccess?: (result?: any) => void;
  className?: string;
}

export const GoogleSignIn: React.FC<GoogleSignInProps> = ({
  onSuccess,
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await window.syncAPI.login();

      if (result.success) {
        // Pass the full result to the parent component so it can handle GDPR consent
        onSuccess?.(result);
      } else {
        setError(result.error || 'Sign in failed');
      }
    } catch (error) {
      console.error('Sign in failed:', error);
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {error && (
        <div className='text-sm text-red-600 bg-red-50 px-4 py-2 rounded-md'>
          {error}
        </div>
      )}

      <button
        onClick={handleGoogleSignIn}
        disabled={isLoading}
        className='group relative w-full flex justify-center items-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
      >
        <svg className='w-5 h-5 mr-2' viewBox='0 0 24 24' fill='currentColor'>
          <path d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z' />
          <path d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z' />
          <path d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z' />
          <path d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z' />
        </svg>
        {isLoading ? 'Signing in...' : 'Sign in with Google'}
      </button>

      <p className='text-xs text-center text-gray-500'>
        By signing in, you agree to our{' '}
        <a href='#' className='text-blue-600 hover:text-blue-500'>
          Terms of Service
        </a>{' '}
        and{' '}
        <a href='#' className='text-blue-600 hover:text-blue-500'>
          Privacy Policy
        </a>
      </p>
    </div>
  );
};
