/**
 * Enhanced Batch Analysis Tab
 * Provides comprehensive color analysis with visualizations
 */

import React, { memo, useState, useMemo } from 'react';
import { BarChart3, PieChart, Grid3x3, ExternalLink } from 'lucide-react';
import type { SimpleColorEntry } from '../../types';
import { ColorSpace3DChannels } from '../../../../../../shared/constants/channels';

// Import our comprehensive batch analysis components
import HueDistributionChart from './HueDistributionChart';
import ContrastMatrix from './ContrastMatrix';
import AnalysisInsights from './AnalysisInsights';
import BatchOperations from './BatchOperations';

type BatchTabType =
  | 'overview'
  | 'distribution'
  | 'contrast'
  | '3d'
  | 'operations';

interface BatchAnalysisProps {
  colors: SimpleColorEntry[];
}

interface TabConfig {
  id: BatchTabType;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const tabs: TabConfig[] = [
  { id: 'overview', label: 'Overview', icon: BarChart3 },
  { id: 'distribution', label: 'Hue Distribution', icon: PieChart },
  { id: 'contrast', label: 'Contrast Matrix', icon: Grid3x3 },
  { id: '3d', label: '3D Space', icon: ExternalLink },
  { id: 'operations', label: 'Batch Ops', icon: BarChart3 },
];

export const BatchAnalysisTab: React.FC<BatchAnalysisProps> = memo(
  ({ colors }) => {
    const [activeTab, setActiveTab] = useState<BatchTabType>('overview');

    // Convert SimpleColorEntry to the format our components expect
    const convertedColors = useMemo(() => {
      return colors.map(color => ({
        id: color.id,
        hex: color.hex,
        name: color.pantone || color.hex, // Use pantone as name or fallback to hex
        product_id: undefined,
      }));
    }, [colors]);

    const handle3DPopOut = async () => {
      try {
        const result = await (window as any).ipc.invoke(
          ColorSpace3DChannels.OPEN_WINDOW,
          convertedColors
        );
        if (!result.success) {
          // Silently handle failure
        }
      } catch (error) {
        // Silently handle error
      }
    };

    const renderContent = () => {
      switch (activeTab) {
        case 'overview':
          return <OverviewTab colors={convertedColors} />;
        case 'distribution':
          return <HueDistributionChart colors={convertedColors} />;
        case 'contrast':
          return <ContrastMatrix colors={convertedColors} />;
        case '3d':
          return (
            <div className='flex items-center justify-center h-full'>
              <div className='text-center space-y-4'>
                <ExternalLink className='w-12 h-12 mx-auto text-brand-primary' />
                <h3 className='text-lg font-medium'>
                  3D Color Space Visualization
                </h3>
                <p className='text-sm text-ui-foreground-secondary max-w-sm'>
                  Click the 3D Space tab to open the visualization in a separate
                  window
                </p>
              </div>
            </div>
          );
        case 'operations':
          return <BatchOperations colors={convertedColors} />;
        default:
          return null;
      }
    };

    if (colors.length === 0) {
      return (
        <div className='flex items-center justify-center h-64 text-ui-foreground-secondary'>
          <div className='text-center'>
            <PieChart className='h-8 w-8 mx-auto mb-2 opacity-50' />
            <p className='text-sm'>No colors selected for batch analysis</p>
            <p className='text-xs mt-1'>
              Add multiple colors to comparison to analyze them
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className='flex flex-col h-full'>
        {/* Tab navigation */}
        <div className='flex border-b border-ui-border px-4'>
          {tabs.map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => {
                  if (tab.id === '3d') {
                    handle3DPopOut();
                  } else {
                    setActiveTab(tab.id);
                  }
                }}
                className={`
                flex items-center px-3 py-2 gap-2 border-b-2 transition-all text-xs
                ${
                  activeTab === tab.id
                    ? 'border-brand-primary text-brand-primary'
                    : 'border-transparent text-ui-foreground-secondary hover:text-ui-foreground-primary'
                }
              `}
              >
                <Icon className='w-3 h-3' />
                <span className='font-medium'>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Dynamic content */}
        <div className='flex-1 overflow-hidden'>{renderContent()}</div>
      </div>
    );
  }
);

BatchAnalysisTab.displayName = 'BatchAnalysisTab';

// Overview tab component
function OverviewTab({ colors }: { colors: any[] }) {
  const stats = useMemo(() => {
    const colorSpaces = {
      total: colors.length,
      withNames: colors.filter(c => c.name).length,
    };

    return {
      total: colors.length,
      colorSpaces,
    };
  }, [colors]);

  return (
    <div className='h-full overflow-y-auto p-4 space-y-4'>
      {/* Summary Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
        <div className='bg-ui-background-secondary rounded-lg p-3'>
          <h3 className='text-xs font-medium text-ui-foreground-tertiary mb-1'>
            Total Colors
          </h3>
          <p className='text-xl font-bold text-ui-foreground-primary'>
            {stats.total}
          </p>
        </div>

        <div className='bg-ui-background-secondary rounded-lg p-3'>
          <h3 className='text-xs font-medium text-ui-foreground-tertiary mb-1'>
            Named Colors
          </h3>
          <p className='text-xl font-bold text-ui-foreground-primary'>
            {stats.colorSpaces.withNames}
          </p>
        </div>
      </div>

      {/* AI Insights */}
      <AnalysisInsights colors={colors} />
    </div>
  );
}
