/**
 * @file gradient-processor-formats.test.ts
 * @description Additional format and edge case tests for GradientProcessor
 * 
 * Tests various gradient data formats, import/export scenarios, and edge cases
 * that complement the main gradient-processor.test.ts file
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { 
  GradientProcessor, 
  GradientStop, 
  GradientInfo, 
  GradientValidationResult, 
  GradientAnalysis 
} from '../gradient-processor.service';

describe('GradientProcessor - Format Testing', () => {
  let processor: GradientProcessor;

  beforeEach(() => {
    processor = new GradientProcessor();
  });

  describe('Legacy Format Support', () => {
    test('should handle legacy gradientStops format from properties', () => {
      const legacyFormat = {
        gradientStops: [
          { color: '#FF0000', position: 0.0 },
          { color: '#0000FF', position: 1.0 }
        ],
        angle: 90
      };

      const validation = processor.validateGradientInfo(legacyFormat);
      expect(validation.isValid).toBe(true);

      const css = processor.generateLinearGradientCSS(legacyFormat.gradientStops, legacyFormat.angle);
      expect(css).toBe('linear-gradient(90deg, #FF0000 0%, #0000FF 100%)');
    });

    test('should handle legacy stops format from color_spaces JSON', () => {
      const legacyStopsFormat = [
        { color: '#FF0000', position: 0.0 },
        { color: '#00FF00', position: 0.5 },
        { color: '#0000FF', position: 1.0 }
      ];

      const csv = processor.createGradientColorsCSV(legacyStopsFormat);
      expect(csv).toBe('#FF0000,#00FF00,#0000FF');
    });
  });

  describe('Color Format Variations', () => {
    test('should handle uppercase hex colors', () => {
      const upperCaseStops: GradientStop[] = [
        { color: '#FF0000', position: 0.0 },
        { color: '#00FF00', position: 0.5 },
        { color: '#0000FF', position: 1.0 }
      ];

      const result = processor.validateGradientStops(upperCaseStops);
      expect(result.isValid).toBe(true);

      const css = processor.generateLinearGradientCSS(upperCaseStops);
      expect(css).toBe('linear-gradient(45deg, #FF0000 0%, #00FF00 50%, #0000FF 100%)');
    });

    test('should handle lowercase hex colors', () => {
      const lowerCaseStops: GradientStop[] = [
        { color: '#ff0000', position: 0.0 },
        { color: '#00ff00', position: 0.5 },
        { color: '#0000ff', position: 1.0 }
      ];

      const result = processor.validateGradientStops(lowerCaseStops);
      expect(result.isValid).toBe(true);

      const css = processor.generateLinearGradientCSS(lowerCaseStops);
      expect(css).toBe('linear-gradient(45deg, #ff0000 0%, #00ff00 50%, #0000ff 100%)');
    });

    test('should handle mixed case hex colors', () => {
      const mixedCaseStops: GradientStop[] = [
        { color: '#Ff0000', position: 0.0 },
        { color: '#00Ff00', position: 0.5 },
        { color: '#0000Ff', position: 1.0 }
      ];

      const result = processor.validateGradientStops(mixedCaseStops);
      expect(result.isValid).toBe(true);
    });
  });

  describe('Position Format Variations', () => {
    test('should handle integer positions (0, 1)', () => {
      const integerPositions: GradientStop[] = [
        { color: '#FF0000', position: 0 },
        { color: '#0000FF', position: 1 }
      ];

      const result = processor.validateGradientStops(integerPositions);
      expect(result.isValid).toBe(true);

      const css = processor.generateLinearGradientCSS(integerPositions);
      expect(css).toBe('linear-gradient(45deg, #FF0000 0%, #0000FF 100%)');
    });

    test('should handle precise decimal positions', () => {
      const precisePositions: GradientStop[] = [
        { color: '#FF0000', position: 0.0 },
        { color: '#00FF00', position: 0.333333 },
        { color: '#0000FF', position: 0.666667 },
        { color: '#FFFF00', position: 1.0 }
      ];

      const result = processor.validateGradientStops(precisePositions);
      expect(result.isValid).toBe(true);

      const css = processor.generateLinearGradientCSS(precisePositions);
      expect(css).toContain('33.3333%');
      expect(css).toContain('66.6667%');
    });
  });

  describe('CMYK Format Support', () => {
    test('should handle complete CMYK data in stops', () => {
      const cmykStops: GradientStop[] = [
        { 
          color: '#FF0000', 
          position: 0.0,
          cmyk: { c: 0, m: 100, y: 100, k: 0 },
          colorCode: 'RED-01'
        },
        { 
          color: '#0000FF', 
          position: 1.0,
          cmyk: { c: 100, m: 100, y: 0, k: 0 },
          colorCode: 'BLUE-01'
        }
      ];

      const result = processor.validateGradientStops(cmykStops);
      expect(result.isValid).toBe(true);

      const csv = processor.createGradientColorsCSV(cmykStops);
      expect(csv).toBe('#FF0000|RED-01,#0000FF|BLUE-01');
    });

    test('should handle partial CMYK data', () => {
      const partialCmykStops: GradientStop[] = [
        { 
          color: '#FF0000', 
          position: 0.0,
          cmyk: { c: 0, m: 100, y: 100, k: 0 }
        },
        { 
          color: '#0000FF', 
          position: 1.0
          // No CMYK data
        }
      ];

      const result = processor.validateGradientStops(partialCmykStops);
      expect(result.isValid).toBe(true);
    });
  });

  describe('Color Code Format Support', () => {
    test('should handle various color code formats', () => {
      const colorCodeVariations: GradientStop[] = [
        { color: '#FF0000', position: 0.0, colorCode: 'RED-001' },
        { color: '#00FF00', position: 0.25, colorCode: 'GRN_002' },
        { color: '#0000FF', position: 0.5, colorCode: 'BLUE.003' },
        { color: '#FFFF00', position: 0.75, colorCode: 'YELLOW-004-SPECIAL' },
        { color: '#FF00FF', position: 1.0, colorCode: 'MAGENTA' }
      ];

      const csv = processor.createGradientColorsCSV(colorCodeVariations);
      expect(csv).toBe('#FF0000|RED-001,#00FF00|GRN_002,#0000FF|BLUE.003,#FFFF00|YELLOW-004-SPECIAL,#FF00FF|MAGENTA');

      const analysis = processor.analyzeGradient(colorCodeVariations);
      expect(analysis.hasColorCodes).toBe(true);
    });

    test('should handle empty and null color codes', () => {
      const mixedColorCodes: GradientStop[] = [
        { color: '#FF0000', position: 0.0, colorCode: 'RED-01' },
        { color: '#00FF00', position: 0.5, colorCode: '' },
        { color: '#0000FF', position: 1.0 }
      ];

      const csv = processor.createGradientColorsCSV(mixedColorCodes);
      expect(csv).toBe('#FF0000|RED-01,#00FF00,#0000FF');
    });
  });

  describe('JSON Storage Format', () => {
    test('should create consistent JSON for storage', () => {
      const stops: GradientStop[] = [
        { color: '#FF0000', position: 0.0, colorCode: 'RED-01' },
        { color: '#0000FF', position: 1.0, colorCode: 'BLUE-01' }
      ];

      const json = processor.createGradientJSON(stops, 90);
      const parsed = JSON.parse(json);

      expect(parsed.stops).toEqual(stops);
      expect(parsed.angle).toBe(90);
      expect(typeof json).toBe('string');
    });

    test('should handle JSON with complex stop data', () => {
      const complexStops: GradientStop[] = [
        { 
          color: '#FF0000', 
          position: 0.0,
          cmyk: { c: 0, m: 100, y: 100, k: 0 },
          colorCode: 'PANTONE-RED-032'
        },
        { 
          color: '#0000FF', 
          position: 1.0,
          cmyk: { c: 100, m: 100, y: 0, k: 0 },
          colorCode: 'PANTONE-BLUE-072'
        }
      ];

      const json = processor.createGradientJSON(complexStops, 180);
      const parsed = JSON.parse(json);

      expect(parsed.stops[0].cmyk).toEqual({ c: 0, m: 100, y: 100, k: 0 });
      expect(parsed.stops[0].colorCode).toBe('PANTONE-RED-032');
      expect(parsed.angle).toBe(180);
    });
  });

  describe('Import Format Compatibility', () => {
    test('should handle CSS gradient format parsing', () => {
      // Test importing from CSS-like format
      const cssBasedStops: GradientStop[] = [
        { color: '#ff0000', position: 0.0 },
        { color: '#00ff00', position: 0.5 },
        { color: '#0000ff', position: 1.0 }
      ];

      const generatedCSS = processor.generateLinearGradientCSS(cssBasedStops, 45);
      expect(generatedCSS).toBe('linear-gradient(45deg, #ff0000 0%, #00ff00 50%, #0000ff 100%)');
    });

    test('should handle Adobe format-like data', () => {
      // Simulate Adobe-style gradient data
      const adobeStyleStops: GradientStop[] = [
        { color: '#FF0000', position: 0.0 },
        { color: '#FFFF00', position: 0.2 },
        { color: '#00FF00', position: 0.4 },
        { color: '#00FFFF', position: 0.6 },
        { color: '#0000FF', position: 0.8 },
        { color: '#FF00FF', position: 1.0 }
      ];

      const analysis = processor.analyzeGradient(adobeStyleStops);
      expect(analysis.complexity).toBe('complex');
      expect(analysis.stopCount).toBe(6);
    });

    test('should handle Sketch format-like data', () => {
      // Simulate Sketch-style gradient data
      const sketchStyleStops: GradientStop[] = [
        { color: '#FFFFFF', position: 0.0 },
        { color: '#000000', position: 1.0 }
      ];

      const css = processor.generateLinearGradientCSS(sketchStyleStops, 270);
      expect(css).toBe('linear-gradient(270deg, #FFFFFF 0%, #000000 100%)');
    });
  });

  describe('Real-World Gradient Patterns', () => {
    test('should handle rainbow gradient', () => {
      const rainbowGradient: GradientStop[] = [
        { color: '#FF0000', position: 0.0 },     // Red
        { color: '#FF7F00', position: 0.1667 },  // Orange  
        { color: '#FFFF00', position: 0.3333 },  // Yellow
        { color: '#00FF00', position: 0.5 },     // Green
        { color: '#0000FF', position: 0.6667 },  // Blue
        { color: '#4B0082', position: 0.8333 },  // Indigo
        { color: '#9400D3', position: 1.0 }      // Violet
      ];

      const result = processor.validateGradientStops(rainbowGradient);
      expect(result.isValid).toBe(true);

      const analysis = processor.analyzeGradient(rainbowGradient);
      expect(analysis.complexity).toBe('complex');
      expect(analysis.stopCount).toBe(7);
    });

    test('should handle metallic gradient effect', () => {
      const metallicGradient: GradientStop[] = [
        { color: '#E8E8E8', position: 0.0 },
        { color: '#FFFFFF', position: 0.25 },
        { color: '#D4D4D4', position: 0.5 },
        { color: '#FFFFFF', position: 0.75 },
        { color: '#E8E8E8', position: 1.0 }
      ];

      const css = processor.generateLinearGradientCSS(metallicGradient, 90);
      expect(css).toContain('linear-gradient(90deg');
      expect(css).toContain('#E8E8E8 0%');
      expect(css).toContain('#FFFFFF 25%');
      expect(css).toContain('#D4D4D4 50%');
    });

    test('should handle sunset gradient', () => {
      const sunsetGradient: GradientStop[] = [
        { color: '#FF6B35', position: 0.0 },   // Orange-red
        { color: '#FF8E53', position: 0.25 },  // Light orange
        { color: '#FF6B9D', position: 0.5 },   // Pink
        { color: '#C44569', position: 0.75 },  // Deep pink
        { color: '#6C5CE7', position: 1.0 }    // Purple
      ];

      const analysis = processor.analyzeGradient(sunsetGradient);
      expect(analysis.complexity).toBe('complex');
      expect(analysis.hasDuplicatePositions).toBe(false);
    });
  });

  describe('Performance with Large Gradients', () => {
    test('should handle gradients with many stops efficiently', () => {
      const largeGradient: GradientStop[] = Array.from({ length: 50 }, (_, i) => ({
        color: `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`,
        position: i / 49
      }));

      const start = Date.now();
      
      const validation = processor.validateGradientStops(largeGradient);
      const css = processor.generateLinearGradientCSS(largeGradient);
      const analysis = processor.analyzeGradient(largeGradient);
      const sorted = processor.sortStopsByPosition(largeGradient);
      
      const duration = Date.now() - start;

      expect(validation.isValid).toBe(true);
      expect(css).toContain('linear-gradient(45deg');
      expect(analysis.stopCount).toBe(50);
      expect(sorted).toHaveLength(50);
      expect(duration).toBeLessThan(100); // Should complete in under 100ms
    });

    test('should handle very precise position calculations', () => {
      const preciseGradient: GradientStop[] = Array.from({ length: 20 }, (_, i) => ({
        color: '#FF0000',
        position: i / 19.0
      }));

      const resolved = processor.resolveDuplicatePositions(preciseGradient);
      const css = processor.generateLinearGradientCSS(resolved);

      expect(resolved).toHaveLength(20);
      expect(css).toContain('linear-gradient');
    });
  });

  describe('Cross-Format Consistency', () => {
    test('should maintain consistency across different operations', () => {
      const testStops: GradientStop[] = [
        { color: '#FF0000', position: 0.0, colorCode: 'RED-01' },
        { color: '#00FF00', position: 0.5, colorCode: 'GREEN-02' },
        { color: '#0000FF', position: 1.0, colorCode: 'BLUE-03' }
      ];

      // Test that all operations work consistently
      const validation = processor.validateGradientStops(testStops);
      const css = processor.generateLinearGradientCSS(testStops);
      const csv = processor.createGradientColorsCSV(testStops);
      const json = processor.createGradientJSON(testStops);
      const analysis = processor.analyzeGradient(testStops);
      const sorted = processor.sortStopsByPosition(testStops);

      expect(validation.isValid).toBe(true);
      expect(css).toBe('linear-gradient(45deg, #FF0000 0%, #00FF00 50%, #0000FF 100%)');
      expect(csv).toBe('#FF0000|RED-01,#00FF00|GREEN-02,#0000FF|BLUE-03');
      expect(JSON.parse(json).stops).toEqual(testStops);
      expect(analysis.hasColorCodes).toBe(true);
      expect(sorted).toEqual(testStops); // Already sorted
    });

    test('should handle round-trip format conversion', () => {
      const originalStops: GradientStop[] = [
        { color: '#FF0000', position: 0.0 },
        { color: '#0000FF', position: 1.0 }
      ];

      // Convert to JSON and back
      const json = processor.createGradientJSON(originalStops, 90);
      const parsed = JSON.parse(json);
      
      const css1 = processor.generateLinearGradientCSS(originalStops, 90);
      const css2 = processor.generateLinearGradientCSS(parsed.stops, parsed.angle);

      expect(css1).toBe(css2);
    });
  });
});