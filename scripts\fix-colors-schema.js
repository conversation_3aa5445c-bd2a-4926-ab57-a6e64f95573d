/**
 * @file fix-colors-schema.js
 * @description Fix SQLite colors table schema to match the expected structure
 * This script adds missing columns and ensures proper data types
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const os = require('os');

function getUserDataPath() {
  // Use Electron's app.getPath('userData') equivalent for each platform
  switch (process.platform) {
    case 'darwin':
      return path.join(os.homedir(), 'Library', 'Application Support', 'chroma-sync');
    case 'win32':
      return path.join(process.env.APPDATA || os.homedir(), 'chroma-sync');
    case 'linux':
      return path.join(process.env.XDG_CONFIG_HOME || path.join(os.homedir(), '.config'), 'chroma-sync');
    default:
      return path.join(os.homedir(), '.chroma-sync');
  }
}

function getDbPath() {
  const userDataPath = getUserDataPath();
  return path.join(userDataPath, 'chromasync.db');
}

function checkTableSchema(db, tableName) {
  console.log(`\n=== Checking ${tableName} table schema ===`);
  
  try {
    const pragma = db.prepare(`PRAGMA table_info(${tableName})`);
    const columns = pragma.all();
    
    console.log(`${tableName} columns:`);
    columns.forEach(col => {
      console.log(`  - ${col.name}: ${col.type} (nullable: ${col.notnull === 0})`);
    });
    
    return columns;
  } catch (error) {
    console.error(`Error checking ${tableName} schema:`, error.message);
    return [];
  }
}

function fixColorsSchema(db) {
  console.log('\n=== Fixing colors table schema ===');
  
  const columns = checkTableSchema(db, 'colors');
  const columnNames = columns.map(col => col.name);
  
  // List of required columns with their types
  const requiredColumns = [
    { name: 'is_synced', type: 'BOOLEAN', defaultValue: 'FALSE' },
    { name: 'deleted_at', type: 'TEXT', defaultValue: 'NULL' },
    { name: 'created_by', type: 'TEXT', defaultValue: 'NULL' },
    { name: 'device_id', type: 'TEXT', defaultValue: 'NULL' },
    { name: 'conflict_resolved_at', type: 'TEXT', defaultValue: 'NULL' }
  ];
  
  // Add missing columns
  for (const column of requiredColumns) {
    if (!columnNames.includes(column.name)) {
      console.log(`Adding missing column: ${column.name}`);
      try {
        const alterStmt = `ALTER TABLE colors ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.defaultValue}`;
        console.log(`Executing: ${alterStmt}`);
        db.exec(alterStmt);
        console.log(`✅ Successfully added column: ${column.name}`);
      } catch (error) {
        console.error(`❌ Failed to add column ${column.name}:`, error.message);
      }
    } else {
      console.log(`✅ Column ${column.name} already exists`);
    }
  }
}

function verifyInsert(db) {
  console.log('\n=== Testing color insertion ===');
  
  try {
    // Get a test organization ID (need internal ID for foreign key)
    const orgStmt = db.prepare('SELECT id, external_id FROM organizations LIMIT 1');
    const org = orgStmt.get();
    
    if (!org) {
      console.log('❌ No organizations found, cannot test insertion');
      return;
    }
    
    console.log(`Using organization: ${org.external_id} (internal ID: ${org.id})`);
    
    // Test insert with minimal data
    const testColorData = {
      id: 'test-color-id-' + Date.now(),
      external_id: 'test-color-ext-' + Date.now(),
      organization_id: org.id,  // Use internal ID for foreign key
      source_id: 1,
      code: 'TEST-COLOR',
      display_name: 'Test Color',
      hex: '#FF0000',
      color_spaces: '{"cmyk":{"c":0,"m":100,"y":100,"k":0}}',
      is_gradient: 0,
      is_metallic: 0,
      is_effect: 0,
      is_library: 0,
      gradient_colors: null,
      notes: null,
      tags: null,
      properties: '{}',
      is_synced: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    const insertStmt = db.prepare(`
      INSERT INTO colors (
        external_id, name, display_name, code, hex, source_id, color_spaces,
        is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
        is_library, properties, created_at, updated_at, organization_id,
        created_by, user_id, deleted_at, device_id, conflict_resolved_at, is_synced
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    console.log('Inserting test color with data:', {
      external_id: testColorData.external_id,
      name: testColorData.display_name,
      display_name: testColorData.display_name,
      code: testColorData.code,
      hex: testColorData.hex,
      source_id: testColorData.source_id,
      color_spaces: testColorData.color_spaces,
      organization_id: testColorData.organization_id
    });
    
    insertStmt.run(
      testColorData.external_id,
      testColorData.display_name,   // name field
      testColorData.display_name,   // display_name field
      testColorData.code,
      testColorData.hex,
      testColorData.source_id,
      testColorData.color_spaces,
      testColorData.is_gradient,
      testColorData.is_metallic,
      testColorData.is_effect,
      testColorData.gradient_colors,
      testColorData.notes,
      testColorData.tags,
      testColorData.is_library,
      testColorData.properties,
      testColorData.created_at,
      testColorData.updated_at,
      testColorData.organization_id,
      null,                         // created_by
      null,                         // user_id
      null,                         // deleted_at
      null,                         // device_id
      null,                         // conflict_resolved_at
      testColorData.is_synced
    );
    
    console.log('✅ Test color insertion successful');
    
    // Clean up test color
    const deleteStmt = db.prepare('DELETE FROM colors WHERE external_id = ?');
    deleteStmt.run(testColorData.external_id);
    console.log('✅ Test color cleaned up');
    
  } catch (error) {
    console.error('❌ Test color insertion failed:', error.message);
    console.error('Error details:', error);
  }
}

function main() {
  const dbPath = getDbPath();
  
  console.log('ChromaSync Database Schema Fix');
  console.log('==============================');
  console.log(`Database path: ${dbPath}`);
  
  if (!fs.existsSync(dbPath)) {
    console.error('❌ Database file not found. Please run the app first to create the database.');
    process.exit(1);
  }
  
  let db;
  try {
    db = new Database(dbPath);
    console.log('✅ Database connection established');
    
    // Check current schema
    checkTableSchema(db, 'colors');
    
    // Fix schema
    fixColorsSchema(db);
    
    // Verify the fix worked
    console.log('\n=== Verifying schema after fixes ===');
    checkTableSchema(db, 'colors');
    
    // Test insertion
    verifyInsert(db);
    
    console.log('\n✅ Schema fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
    process.exit(1);
  } finally {
    if (db) {
      db.close();
      console.log('✅ Database connection closed');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, checkTableSchema, fixColorsSchema };