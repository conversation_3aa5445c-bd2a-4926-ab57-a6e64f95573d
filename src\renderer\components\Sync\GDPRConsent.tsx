/**
 * @file GDPRConsent.tsx
 * @description GDPR consent dialog for ChromaSync
 */

import React from 'react';

interface GDPRConsentProps {
  onAccept: () => void;
  onDecline: () => void;
  isLoading?: boolean;
}

export const GDPRConsent: React.FC<GDPRConsentProps> = ({
  onAccept,
  onDecline,
  isLoading = false,
}) => {
  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
      <div className='bg-white rounded-lg max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto'>
        <h2 className='text-2xl font-bold mb-4'>Privacy & Data Protection</h2>

        <div className='space-y-4 text-sm'>
          <p>
            ChromaSync is committed to protecting your privacy in compliance
            with GDPR.
          </p>

          <div className='bg-gray-50 p-4 rounded'>
            <h3 className='font-semibold mb-2'>We collect and process:</h3>
            <ul className='list-disc list-inside space-y-1'>
              <li>Your Google account email and profile information</li>
              <li>Color and product data you create</li>
              <li>Device information for sync purposes</li>
            </ul>
          </div>

          <div className='bg-gray-50 p-4 rounded'>
            <h3 className='font-semibold mb-2'>Your rights:</h3>
            <ul className='list-disc list-inside space-y-1'>
              <li>Access your data anytime</li>
              <li>Export your data in standard formats</li>
              <li>Delete your account and all associated data</li>
              <li>Opt-out of any marketing communications</li>
            </ul>
          </div>

          <p>
            Your data is encrypted and stored securely. We never share your data
            with third parties.
          </p>
        </div>

        <div className='mt-6 flex gap-4'>
          <button
            onClick={onAccept}
            disabled={isLoading}
            className='flex-1 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            {isLoading ? 'Processing...' : 'Accept & Continue'}
          </button>
          <button
            onClick={onDecline}
            disabled={isLoading}
            className='flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            Decline
          </button>
        </div>
      </div>
    </div>
  );
};
