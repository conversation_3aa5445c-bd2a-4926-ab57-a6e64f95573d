/**
 * @file ConfirmModal.tsx
 * @description Confirmation modal component to replace window.confirm
 */

import React from 'react';
import { Modal } from './Modal';
import { AlertTriangle, Info, AlertCircle } from 'lucide-react';

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  variant?: 'danger' | 'warning' | 'info';
  confirmText?: string;
  cancelText?: string;
  confirmButtonClassName?: string;
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  variant = 'info',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmButtonClassName,
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const getIcon = () => {
    switch (variant) {
      case 'danger':
        return <AlertCircle className='w-6 h-6 text-red-600' />;
      case 'warning':
        return <AlertTriangle className='w-6 h-6 text-yellow-600' />;
      case 'info':
      default:
        return <Info className='w-6 h-6 text-blue-600' />;
    }
  };

  const getDefaultConfirmButtonClass = () => {
    switch (variant) {
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'warning':
        return 'bg-yellow-600 hover:bg-yellow-700 text-white';
      case 'info':
      default:
        return 'bg-blue-600 hover:bg-blue-700 text-white';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size='sm'
      closeOnBackdropClick={false}
      showCloseButton={false}
    >
      <div className='flex items-start space-x-4'>
        <div className='flex-shrink-0'>{getIcon()}</div>
        <div className='flex-1'>
          <p className='text-gray-700 dark:text-gray-300 mb-6'>{message}</p>
          <div className='flex justify-end space-x-3'>
            <button
              onClick={onClose}
              className='px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors'
            >
              {cancelText}
            </button>
            <button
              onClick={handleConfirm}
              className={`px-4 py-2 rounded-md transition-colors ${
                confirmButtonClassName || getDefaultConfirmButtonClass()
              }`}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};
