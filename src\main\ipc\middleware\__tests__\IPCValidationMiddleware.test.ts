/**
 * @file IPCValidationMiddleware.test.ts
 * @description Tests for IPC validation middleware security features
 */

import { IPCValidationMiddleware, CommonSchemas } from '../IPCValidationMiddleware';
import { IpcMainEvent } from 'electron';

// Mock Electron IPC event
const createMockEvent = (url: string = 'file://test.html', senderId: number = 1): Partial<IpcMainEvent> => ({
  sender: {
    id: senderId,
    getURL: () => url,
    isDestroyed: () => false
  } as any
});

describe('IPCValidationMiddleware', () => {
  let middleware: IPCValidationMiddleware;

  beforeEach(() => {
    middleware = IPCValidationMiddleware.getInstance();
  });

  describe('Sender Validation', () => {
    test('should accept valid file:// origin', () => {
      const mockEvent = createMockEvent('file://test.html');
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:channel', {});
      
      // Should fail because channel is not registered, but sender validation should pass
      expect(result?.error).not.toBe('Unauthorized sender');
    });

    test('should accept valid chromasync:// origin', () => {
      const mockEvent = createMockEvent('chromasync://auth/callback');
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:channel', {});
      
      // Should fail because channel is not registered, but sender validation should pass
      expect(result?.error).not.toBe('Unauthorized sender');
    });

    test('should reject untrusted origin', () => {
      // First register the channel so we can test sender validation
      middleware.registerValidation({
        channel: 'test:channel',
        schema: {
          type: 'object',
          properties: {
            test: { type: 'string' }
          },
          required: ['test'],
          additionalProperties: false
        }
      });

      const mockEvent = createMockEvent('https://malicious.com');
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:channel', { test: 'value' });
      
      expect(result?.success).toBe(false);
      expect(result?.error).toBe('Unauthorized sender');
    });

    test('should reject destroyed sender', () => {
      // Register channel for this test
      middleware.registerValidation({
        channel: 'test:destroyed',
        schema: {
          type: 'object',
          properties: {
            test: { type: 'string' }
          },
          required: ['test'],
          additionalProperties: false
        }
      });

      const mockEvent = {
        sender: {
          id: 1,
          getURL: () => 'file://test.html',
          isDestroyed: () => true
        }
      };
      
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:destroyed', { test: 'value' });
      
      expect(result?.success).toBe(false);
      expect(result?.error).toBe('Unauthorized sender');
    });
  });

  describe('Channel Validation', () => {
    test('should reject unregistered channels', () => {
      const mockEvent = createMockEvent();
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'unregistered:channel', {});
      
      expect(result?.success).toBe(false);
      expect(result?.error).toBe('Channel not allowed');
    });

    test('should allow registered channels', () => {
      // Register a test channel
      middleware.registerValidation({
        channel: 'test:registered',
        schema: {
          type: 'object',
          properties: {
            test: { type: 'string' }
          },
          required: ['test'],
          additionalProperties: false
        }
      });

      const mockEvent = createMockEvent();
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:registered', { test: 'valid' });
      
      // Should pass channel validation (null means continue processing)
      expect(result).toBeNull();
    });
  });

  describe('Data Validation', () => {
    beforeEach(() => {
      // Register test validation schema
      middleware.registerValidation({
        channel: 'test:validation',
        schema: {
          type: 'object',
          properties: {
            organizationId: { type: 'string', minLength: 1, maxLength: 100 },
            name: { type: 'string', minLength: 1, maxLength: 50 },
            email: { type: 'string', format: 'email' }
          },
          required: ['organizationId', 'name'],
          additionalProperties: false
        }
      });
    });

    test('should validate correct data', () => {
      const mockEvent = createMockEvent();
      const validData = {
        organizationId: 'test-org-123',
        name: 'Test User',
        email: '<EMAIL>'
      };
      
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:validation', validData);
      expect(result).toBeNull(); // null means validation passed
    });

    test('should reject missing required fields', () => {
      const mockEvent = createMockEvent();
      const invalidData = {
        name: 'Test User'
        // Missing required organizationId
      };
      
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:validation', invalidData);
      expect(result?.success).toBe(false);
      expect(result?.error).toBe('Invalid request data');
      expect(result?.message).toContain('organizationId');
    });

    test('should reject invalid data types', () => {
      const mockEvent = createMockEvent();
      const invalidData = {
        organizationId: 123, // Should be string
        name: 'Test User'
      };
      
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:validation', invalidData);
      expect(result?.success).toBe(false);
      expect(result?.error).toBe('Invalid request data');
    });

    test('should remove additional properties (security feature)', () => {
      const mockEvent = createMockEvent();
      const dataWithExtra = {
        organizationId: 'test-org-123',
        name: 'Test User',
        maliciousField: 'hack attempt' // Should be removed
      };
      
      // Our AJV config removes additional properties for security
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:validation', dataWithExtra);
      expect(result).toBeNull(); // Should pass validation (additional properties removed)
    });

    test('should validate email format', () => {
      const mockEvent = createMockEvent();
      const invalidData = {
        organizationId: 'test-org-123',
        name: 'Test User',
        email: 'not-an-email' // Invalid email format
      };
      
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:validation', invalidData);
      expect(result?.success).toBe(false);
      expect(result?.error).toBe('Invalid request data');
    });
  });

  describe('Rate Limiting', () => {
    beforeEach(() => {
      // Register channel with strict rate limiting for testing
      middleware.registerValidation({
        channel: 'test:ratelimited',
        schema: {
          type: 'object',
          properties: {
            test: { type: 'string' }
          },
          required: ['test'],
          additionalProperties: false
        },
        rateLimit: {
          maxRequests: 2,
          windowMs: 1000 // 2 requests per second
        }
      });
    });

    test('should allow requests within rate limit', () => {
      const mockEvent = createMockEvent('file://test.html', 1);
      const testData = { test: 'value' };
      
      // First request should pass
      const result1 = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:ratelimited', testData);
      expect(result1).toBeNull();
      
      // Second request should pass
      const result2 = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:ratelimited', testData);
      expect(result2).toBeNull();
    });

    test('should block requests exceeding rate limit', () => {
      const mockEvent = createMockEvent('file://test.html', 1);
      const testData = { test: 'value' };
      
      // First two requests should pass
      middleware.validateRequest(mockEvent as IpcMainEvent, 'test:ratelimited', testData);
      middleware.validateRequest(mockEvent as IpcMainEvent, 'test:ratelimited', testData);
      
      // Third request should be blocked
      const result3 = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:ratelimited', testData);
      expect(result3?.success).toBe(false);
      expect(result3?.error).toBe('Rate limit exceeded');
    });

    test('should track rate limits per sender', () => {
      const testData = { test: 'value' };
      
      // Two requests from sender 1
      const mockEvent1 = createMockEvent('file://test.html', 1);
      middleware.validateRequest(mockEvent1 as IpcMainEvent, 'test:ratelimited', testData);
      middleware.validateRequest(mockEvent1 as IpcMainEvent, 'test:ratelimited', testData);
      
      // Request from sender 2 should still be allowed
      const mockEvent2 = createMockEvent('file://test.html', 2);
      const result = middleware.validateRequest(mockEvent2 as IpcMainEvent, 'test:ratelimited', testData);
      expect(result).toBeNull();
    });
  });

  describe('Common Schemas', () => {
    test('should validate organization ID schema', () => {
      const validData = { organizationId: 'valid-org-123' };
      const invalidData = { organizationId: '' }; // Too short
      
      // This would normally be done through the middleware, but testing schema directly
      expect(CommonSchemas.organizationId.properties.organizationId.minLength).toBe(1);
      expect(CommonSchemas.organizationId.properties.organizationId.maxLength).toBe(100);
    });

    test('should have required security constraints', () => {
      // All common schemas should not allow additional properties
      expect(CommonSchemas.organizationId.additionalProperties).toBe(false);
      expect(CommonSchemas.colorId.additionalProperties).toBe(false);
      expect(CommonSchemas.productId.additionalProperties).toBe(false);
    });
  });

  describe('Security Edge Cases', () => {
    test('should handle null/undefined data gracefully', () => {
      middleware.registerValidation({
        channel: 'test:null',
        schema: {
          type: 'object',
          properties: {
            test: { type: 'string' }
          },
          required: ['test'],
          additionalProperties: false
        }
      });

      const mockEvent = createMockEvent();
      
      const result1 = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:null', null);
      expect(result1?.success).toBe(false);
      
      const result2 = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:null', undefined);
      expect(result2?.success).toBe(false);
    });

    test('should handle circular references safely', () => {
      middleware.registerValidation({
        channel: 'test:circular',
        schema: {
          type: 'object',
          properties: {
            test: { type: 'string' }
          },
          required: ['test'],
          additionalProperties: false
        }
      });

      const mockEvent = createMockEvent();
      const circularData: any = { test: 'value' };
      circularData.self = circularData; // Create circular reference
      
      // Should handle circular reference gracefully
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:circular', circularData);
      expect(result?.success).toBe(false);
    });

    test('should prevent code injection in string fields', () => {
      middleware.registerValidation({
        channel: 'test:injection',
        schema: {
          type: 'object',
          properties: {
            name: { type: 'string', maxLength: 50 }
          },
          required: ['name'],
          additionalProperties: false
        }
      });

      const mockEvent = createMockEvent();
      const maliciousData = {
        name: '<script>alert("xss")</script>' // XSS attempt
      };
      
      // Should pass validation (string validation), but application should handle XSS prevention
      const result = middleware.validateRequest(mockEvent as IpcMainEvent, 'test:injection', maliciousData);
      expect(result).toBeNull(); // Passes schema validation, but string content needs app-level sanitization
    });
  });

  describe('Statistics and Monitoring', () => {
    test('should provide validation statistics', () => {
      const stats = middleware.getValidationStats();
      
      expect(stats).toHaveProperty('totalChannels');
      expect(stats).toHaveProperty('activeRateLimits');
      expect(stats).toHaveProperty('allowedChannels');
      expect(Array.isArray(stats.allowedChannels)).toBe(true);
    });

    test('should clean up expired rate limits', () => {
      // This test would require time manipulation or longer timeouts
      // For now, just verify the method exists and doesn't throw
      expect(() => middleware.cleanupRateLimits()).not.toThrow();
    });
  });
});