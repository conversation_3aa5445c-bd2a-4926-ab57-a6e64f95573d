/**
 * @file color-service-integration.test.ts
 * @description Integration tests for ColorService with ColorSpaceCalculator
 * 
 * Tests that the ColorService correctly integrates with the ColorSpaceCalculator
 * service for color space calculations.
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { ColorService } from '../../../db/services/color.service';
import { ColorSpaceCalculator } from '../color-space-calculator.service';

describe('ColorService + ColorSpaceCalculator Integration', () => {
  let colorService: ColorService;
  let colorSpaceCalculator: ColorSpaceCalculator;

  beforeEach(() => {
    // Mock database for testing (since we only need to test the integration)
    const mockDb = {} as any;
    
    // Create ColorSpaceCalculator instance
    colorSpaceCalculator = new ColorSpaceCalculator();
    
    // Create ColorService with injected ColorSpaceCalculator
    colorService = new ColorService(mockDb, undefined, colorSpaceCalculator);
  });

  describe('ColorSpaceCalculator Integration', () => {
    test('should inject ColorSpaceCalculator service correctly', () => {
      expect(colorService).toBeDefined();
      expect((colorService as any).colorSpaceCalculator).toBe(colorSpaceCalculator);
    });

    test('should use ColorSpaceCalculator for getColorSpaces method', () => {
      const testHex = '#FF0000';
      
      // Access private method for testing
      const getColorSpaces = (colorService as any).getColorSpaces.bind(colorService);
      const result = getColorSpaces(testHex);
      
      // Should return the expected RGB, HSL, LAB structure
      expect(result).toHaveProperty('rgb');
      expect(result).toHaveProperty('hsl');
      expect(result).toHaveProperty('lab');
      
      // Verify RGB values for red color
      expect(result.rgb).toEqual({ r: 255, g: 0, b: 0 });
      expect(result.hsl).toEqual({ h: 0, s: 100, l: 50 });
      expect(result.lab).toBeDefined();
    });

    test('should handle invalid hex colors gracefully', () => {
      const invalidHex = 'invalid';
      
      // Access private method for testing
      const getColorSpaces = (colorService as any).getColorSpaces.bind(colorService);
      const result = getColorSpaces(invalidHex);
      
      // Should return null values for invalid hex
      expect(result.rgb).toBeNull();
      expect(result.hsl).toBeNull();
      expect(result.lab).toBeNull();
    });

    test('should use ColorSpaceCalculator for CMYK conversions', () => {
      const testHex = '#00FF00'; // Green
      
      // Test CMYK conversion through the calculator
      const cmyk = colorSpaceCalculator.hexToCmyk(testHex);
      
      expect(cmyk).toEqual({ c: 100, m: 0, y: 100, k: 0 });
    });

    test('should maintain color space calculation consistency', () => {
      const testColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#000000'];
      
      testColors.forEach(hex => {
        // Access private method for testing
        const getColorSpaces = (colorService as any).getColorSpaces.bind(colorService);
        const result = getColorSpaces(hex);
        
        if (result.rgb) {
          // Should be able to convert back to hex
          const convertedHex = colorSpaceCalculator.rgbToHex(result.rgb);
          expect(convertedHex).toBe(hex);
        }
      });
    });

    test('should provide all required color space formats', () => {
      const testHex = '#FF5733';
      
      // Test all color space conversions
      const rgb = colorSpaceCalculator.hexToRgb(testHex);
      const hsl = colorSpaceCalculator.hexToHsl(testHex);
      const cmyk = colorSpaceCalculator.hexToCmyk(testHex);
      const allSpaces = colorSpaceCalculator.getAllColorSpaces(testHex);
      
      expect(rgb).toBeDefined();
      expect(hsl).toBeDefined();
      expect(cmyk).toBeDefined();
      expect(allSpaces).toBeDefined();
      
      if (allSpaces) {
        expect(allSpaces.hex).toBe(testHex);
        expect(allSpaces.rgb).toEqual(rgb);
        expect(allSpaces.hsl).toEqual(hsl);
        expect(allSpaces.cmyk).toEqual(cmyk);
      }
    });
  });

  describe('Service Dependency Injection', () => {
    test('should create default ColorSpaceCalculator when none provided', () => {
      const mockDb = {} as any;
      const serviceWithDefaults = new ColorService(mockDb);
      
      expect((serviceWithDefaults as any).colorSpaceCalculator).toBeInstanceOf(ColorSpaceCalculator);
    });

    test('should use provided ColorSpaceCalculator instance', () => {
      const mockDb = {} as any;
      const customCalculator = new ColorSpaceCalculator();
      const serviceWithCustom = new ColorService(mockDb, undefined, customCalculator);
      
      expect((serviceWithCustom as any).colorSpaceCalculator).toBe(customCalculator);
    });
  });

  describe('Performance Integration', () => {
    test('should maintain performance with service layer', () => {
      const testHex = '#FF5733';
      const iterations = 100;
      
      const start = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        // Access private method for testing
        const getColorSpaces = (colorService as any).getColorSpaces.bind(colorService);
        getColorSpaces(testHex);
      }
      
      const end = Date.now();
      const duration = end - start;
      
      // Should complete 100 conversions quickly (under 50ms)
      expect(duration).toBeLessThan(50);
    });
  });
});