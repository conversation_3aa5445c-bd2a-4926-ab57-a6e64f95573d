import { render, screen } from '@testing-library/react';
import { useTokens } from '../renderer/hooks/useTokens';
import { vi, describe, test, expect, beforeEach } from 'vitest';
import FormInput from '../renderer/components/FormInput';
import ViewTabs from '../renderer/components/ViewTabs';
import WindowControls from '../renderer/components/WindowControls';

// Mock the hooks
vi.mock('../renderer/hooks/useTokens', () => ({
  useTokens: vi.fn()
}));

// Mock window.electron for WindowControls
vi.mock('../renderer/types/window.d.ts', () => ({}));
Object.defineProperty(window, 'electron', {
  value: {
    window: {
      minimize: vi.fn(),
      maximize: vi.fn(),
      unmaximize: vi.fn(),
      isMaximized: vi.fn().mockResolvedValue(false),
      close: vi.fn()
    }
  },
  writable: true
});

describe('Token System Validation', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.mocked(useTokens).mockReset();
    
    // Default mock for useTokens
    vi.mocked(useTokens).mockReturnValue({
      theme: 'light',
      colors: {
        brand: { primary: '#ff0000', secondary: '#00ff00', accent: '#0000ff' },
        ui: {
          background: { primary: '#ffffff', secondary: '#f5f5f5', tertiary: '#eeeeee' },
          foreground: { primary: '#000000', secondary: '#333333', tertiary: '#666666', inverse: '#ffffff' },
          border: { light: '#dddddd', medium: '#cccccc', dark: '#bbbbbb' },
          focus: '#0066cc'
        },
        feedback: { success: '#00cc00', warning: '#ffcc00', error: '#cc0000', info: '#0099ff' }
      },
      typography: {
        fontFamily: { sans: ['Arial'], mono: ['Courier'] },
        fontSize: { xs: '0.75rem', sm: '0.875rem', base: '1rem', lg: '1.125rem', xl: '1.25rem', '2xl': '1.5rem', '3xl': '1.875rem', '4xl': '2.25rem' },
        fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
        lineHeight: { none: '1', tight: '1.25', snug: '1.375', normal: '1.5', relaxed: '1.625', loose: '2' }
      },
      spacing: { px: '1px', 0: '0', 0.5: '0.125rem', 1: '0.25rem', 1.5: '0.375rem', 2: '0.5rem', 2.5: '0.625rem', 3: '0.75rem', 4: '1rem', 5: '1.25rem', 6: '1.5rem', 8: '2rem', 10: '2.5rem', 12: '3rem', 16: '4rem', 20: '5rem', 24: '6rem' },
      borderRadius: { none: '0', sm: '0.125rem', DEFAULT: '0.25rem', md: '0.375rem', lg: '0.5rem', xl: '0.75rem', '2xl': '1rem', full: '9999px' },
      shadows: { sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)', DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)', md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)', lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)', xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' },
      transitions: {
        duration: { 75: '75ms', 100: '100ms', 150: '150ms', 200: '200ms', 300: '300ms', 500: '500ms', 700: '700ms', 1000: '1000ms' },
        easing: { linear: 'linear', in: 'cubic-bezier(0.4, 0, 1, 1)', out: 'cubic-bezier(0, 0, 0.2, 1)', inOut: 'cubic-bezier(0.4, 0, 0.2, 1)', apple: 'cubic-bezier(0.25, 0.1, 0.25, 1)' }
      },
      zIndex: { 0: '0', 10: '10', 20: '20', 30: '30', 40: '40', 50: '50', auto: 'auto', dropdown: '1000', modal: '1050', tooltip: '1100' },
      breakpoints: { sm: '640px', md: '768px', lg: '1024px', xl: '1280px', '2xl': '1536px' }
    });
  });

  test('FormInput uses token-based styling', () => {
    render(
      <FormInput
        id="test-input"
        name="test-input"
        value=""
        onChange={() => {}}
        label="Test Input"
        required
      />
    );

    const label = screen.getByText('Test Input');
    expect(label).toBeInTheDocument();
    
    // Check for token-based classes
    expect(label.className).toContain('text-[var(--font-size-sm)]');
    expect(label.className).toContain('font-[var(--font-weight-medium)]');
    expect(label.className).toContain('text-[var(--color-ui-foreground-secondary)]');
  });

  test('ViewTabs uses token-based styling', () => {
    render(
      <ViewTabs
        activeView="table"
        onViewChange={() => {}}
      />
    );

    const tableTabs = screen.getByTestId('table-view-tab');
    expect(tableTabs).toBeInTheDocument();
    
    // Check for token-based class patterns
    const container = tableTabs.closest('div');
    expect(container).not.toBeNull();
    expect(container!.className).toContain('rounded-[var(--radius-lg)]');
    expect(container!.className).toContain('bg-ui-background-tertiary');
    expect(container!.className).toContain('p-[var(--spacing-1)]');
  });

  test('WindowControls uses token-based styling', () => {
    render(<WindowControls />);

    // Find minimize button using aria-label
    const minimizeBtn = screen.getByLabelText('Minimize');
    expect(minimizeBtn).toBeInTheDocument();
    
    // Check for token-based classes
    expect(minimizeBtn.className).toContain('p-[var(--spacing-2)]');
    
    // Verify the style attributes have token values
    expect(minimizeBtn.style.transition).toContain('150ms');
  });

  test('Components properly handle transition styles', () => {
    render(<WindowControls />);
    
    const closeBtn = screen.getByLabelText('Close');
    expect(closeBtn).toBeInTheDocument();
    
    // Check that the transition styles are applied with token values
    expect(closeBtn.style.transition).toBeTruthy();
    expect(closeBtn.style.transition).toContain('150ms');
    expect(closeBtn.style.transition).toContain('all');
  });
}); 