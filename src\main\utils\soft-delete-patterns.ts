/**
 * @file soft-delete-patterns.ts
 * @description Standardized soft delete patterns with organization scoping
 */

import Database from 'better-sqlite3';
import { requireValidOrganizationId } from './organization-validation';

export interface SoftDeleteOptions {
  organizationId?: string;
  userId?: string;
  cascade?: boolean;
  context?: string;
}

export interface SoftDeleteResult {
  success: boolean;
  affected: number;
  cascadeAffected?: Record<string, number>;
  error?: string;
}

/**
 * Standardized soft delete patterns with organization scoping
 */
export class SoftDeleteManager {
  constructor(private db: Database.Database) {}

  /**
   * Soft delete a single record with organization scoping
   */
  softDeleteRecord(
    table: string,
    idField: string,
    id: string,
    options: SoftDeleteOptions = {}
  ): SoftDeleteResult {
    try {
      this.validateTable(table);
      this.validateField(idField);

      const { organizationId, userId, context } = options;
      const timestamp = new Date().toISOString();

      let sql = `
        UPDATE ${table} 
        SET deleted_at = ?, updated_at = ?, deleted_by = ?
        WHERE ${idField} = ? AND deleted_at IS NULL
      `;
      const params = [timestamp, timestamp, userId || null, id];

      // Add organization scoping if provided
      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(
          organizationId,
          `SoftDeleteManager.softDeleteRecord.${table}`
        );
        sql += ' AND organization_id = ?';
        params.push(validatedOrgId);
      }

      const result = this.db.prepare(sql).run(...params);

      console.log(
        `[SoftDeleteManager] Soft deleted ${result.changes} record(s) from ${table}${context ? ` (${context})` : ''}`
      );

      return {
        success: result.changes > 0,
        affected: result.changes,
      };
    } catch (error) {
      console.error(
        `[SoftDeleteManager] Error soft deleting from ${table}:`,
        error
      );
      return {
        success: false,
        affected: 0,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Restore a soft deleted record
   */
  restoreRecord(
    table: string,
    idField: string,
    id: string,
    options: SoftDeleteOptions = {}
  ): SoftDeleteResult {
    try {
      this.validateTable(table);
      this.validateField(idField);

      const { organizationId, userId, context } = options;
      const timestamp = new Date().toISOString();

      let sql = `
        UPDATE ${table} 
        SET deleted_at = NULL, updated_at = ?, restored_by = ?
        WHERE ${idField} = ? AND deleted_at IS NOT NULL
      `;
      const params = [timestamp, userId || null, id];

      // Add organization scoping if provided
      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(
          organizationId,
          `SoftDeleteManager.restoreRecord.${table}`
        );
        sql += ' AND organization_id = ?';
        params.push(validatedOrgId);
      }

      const result = this.db.prepare(sql).run(...params);

      console.log(
        `[SoftDeleteManager] Restored ${result.changes} record(s) in ${table}${context ? ` (${context})` : ''}`
      );

      return {
        success: result.changes > 0,
        affected: result.changes,
      };
    } catch (error) {
      console.error(
        `[SoftDeleteManager] Error restoring record in ${table}:`,
        error
      );
      return {
        success: false,
        affected: 0,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Bulk soft delete with organization scoping
   */
  bulkSoftDelete(
    table: string,
    idField: string,
    ids: string[],
    options: SoftDeleteOptions = {}
  ): SoftDeleteResult {
    try {
      this.validateTable(table);
      this.validateField(idField);

      if (!ids.length) {
        return { success: true, affected: 0 };
      }

      const { organizationId, userId, context } = options;
      const timestamp = new Date().toISOString();

      const bulkTransaction = this.db.transaction(() => {
        let totalAffected = 0;

        for (const id of ids) {
          let sql = `
            UPDATE ${table} 
            SET deleted_at = ?, updated_at = ?, deleted_by = ?
            WHERE ${idField} = ? AND deleted_at IS NULL
          `;
          const params = [timestamp, timestamp, userId || null, id];

          // Add organization scoping if provided
          if (organizationId) {
            const validatedOrgId = requireValidOrganizationId(
              organizationId,
              `SoftDeleteManager.bulkSoftDelete.${table}`
            );
            sql += ' AND organization_id = ?';
            params.push(validatedOrgId);
          }

          const result = this.db.prepare(sql).run(...params);
          totalAffected += result.changes;
        }

        return totalAffected;
      });

      const affected = bulkTransaction();

      console.log(
        `[SoftDeleteManager] Bulk soft deleted ${affected} record(s) from ${table}${context ? ` (${context})` : ''}`
      );

      return {
        success: affected > 0,
        affected,
      };
    } catch (error) {
      console.error(
        `[SoftDeleteManager] Error bulk soft deleting from ${table}:`,
        error
      );
      return {
        success: false,
        affected: 0,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Get soft deleted records for recovery
   */
  getSoftDeletedRecords(
    table: string,
    organizationId?: string,
    limit: number = 100,
    offset: number = 0
  ): any[] {
    try {
      this.validateTable(table);

      let sql = `
        SELECT * FROM ${table} 
        WHERE deleted_at IS NOT NULL
      `;
      const params: any[] = [];

      // Add organization scoping if provided
      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(
          organizationId,
          `SoftDeleteManager.getSoftDeletedRecords.${table}`
        );
        sql += ' AND organization_id = ?';
        params.push(validatedOrgId);
      }

      sql += ' ORDER BY deleted_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      return this.db.prepare(sql).all(...params);
    } catch (error) {
      console.error(
        `[SoftDeleteManager] Error getting soft deleted records from ${table}:`,
        error
      );
      return [];
    }
  }

  /**
   * Permanently delete old soft-deleted records (cleanup)
   */
  cleanupOldSoftDeleted(
    table: string,
    daysOld: number = 30,
    organizationId?: string
  ): SoftDeleteResult {
    try {
      this.validateTable(table);

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      const cutoffTimestamp = cutoffDate.toISOString();

      let sql = `
        DELETE FROM ${table} 
        WHERE deleted_at IS NOT NULL AND deleted_at < ?
      `;
      const params = [cutoffTimestamp];

      // Add organization scoping if provided
      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(
          organizationId,
          `SoftDeleteManager.cleanupOldSoftDeleted.${table}`
        );
        sql += ' AND organization_id = ?';
        params.push(validatedOrgId);
      }

      const result = this.db.prepare(sql).run(...params);

      console.log(
        `[SoftDeleteManager] Permanently deleted ${result.changes} old soft-deleted record(s) from ${table} (older than ${daysOld} days)`
      );

      return {
        success: true,
        affected: result.changes,
      };
    } catch (error) {
      console.error(
        `[SoftDeleteManager] Error cleaning up old soft deleted records from ${table}:`,
        error
      );
      return {
        success: false,
        affected: 0,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Check if record is soft deleted
   */
  isSoftDeleted(
    table: string,
    idField: string,
    id: string,
    organizationId?: string
  ): boolean {
    try {
      this.validateTable(table);
      this.validateField(idField);

      let sql = `
        SELECT 1 FROM ${table} 
        WHERE ${idField} = ? AND deleted_at IS NOT NULL
      `;
      const params = [id];

      if (organizationId) {
        const validatedOrgId = requireValidOrganizationId(
          organizationId,
          `SoftDeleteManager.isSoftDeleted.${table}`
        );
        sql += ' AND organization_id = ?';
        params.push(validatedOrgId);
      }

      const result = this.db.prepare(sql).get(...params);
      return !!result;
    } catch (error) {
      console.error(
        `[SoftDeleteManager] Error checking if record is soft deleted in ${table}:`,
        error
      );
      return false;
    }
  }

  /**
   * Validate table name against allowlist
   */
  private validateTable(table: string): void {
    const allowedTables = [
      'colors',
      'products',
      'datasheets',
      'organizations',
      'users',
      'product_colors',
      'organization_members',
    ];

    if (!allowedTables.includes(table)) {
      throw new Error(`Invalid table name: ${table}`);
    }
  }

  /**
   * Validate field name
   */
  private validateField(field: string): void {
    const validPattern = /^[a-zA-Z_][a-zA-Z0-9_]*$/;

    if (!validPattern.test(field)) {
      throw new Error(`Invalid field name: ${field}`);
    }
  }
}

/**
 * Factory function to create a soft delete manager instance
 */
export function createSoftDeleteManager(
  db: Database.Database
): SoftDeleteManager {
  return new SoftDeleteManager(db);
}

/**
 * Common soft delete queries with organization scoping
 */
export class SoftDeleteQueries {
  /**
   * Build active records query (not soft deleted)
   */
  static activeRecordsWhere(organizationId?: string): {
    where: string;
    params: any[];
  } {
    let where = 'deleted_at IS NULL';
    const params: any[] = [];

    if (organizationId) {
      where += ' AND organization_id = ?';
      params.push(organizationId);
    }

    return { where, params };
  }

  /**
   * Build soft deleted records query
   */
  static softDeletedWhere(organizationId?: string): {
    where: string;
    params: any[];
  } {
    let where = 'deleted_at IS NOT NULL';
    const params: any[] = [];

    if (organizationId) {
      where += ' AND organization_id = ?';
      params.push(organizationId);
    }

    return { where, params };
  }

  /**
   * Build organization scoped query
   */
  static withOrganizationScope(organizationId: string): {
    where: string;
    params: any[];
  } {
    return {
      where: 'organization_id = ?',
      params: [organizationId],
    };
  }
}
