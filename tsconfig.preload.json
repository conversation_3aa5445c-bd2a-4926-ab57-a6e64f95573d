{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    /* Preload Script Specific Configuration */
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020", "DOM"],
    "types": ["node", "electron"],
    "moduleResolution": "node",
    
    /* Preload Environment - Bridge between main and renderer */
    "skipLibCheck": true,
    "declaration": false,
    "sourceMap": true,
    "outDir": "./out/preload",
    "rootDir": "./src/preload",
    
    /* Security and Isolation */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "noEmit": false,
    
    /* Enhanced Type Safety for Preload Scripts */
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    
    /* Preload Script Path Mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@preload/*": ["src/preload/*"],
      "@shared/*": ["src/shared/*"],
      "@types/*": ["src/types/*"]
    }
  },
  "include": [
    "src/preload/**/*.ts",
    "src/shared/types/**/*.ts",
    "src/types/**/*.d.ts"
  ],
  "exclude": [
    "src/main/**/*",
    "src/renderer/**/*",
    "src/test/**/*",
    "**/*.test.ts",
    "**/__tests__/**/*",
    "node_modules"
  ]
}