/**
 * @file organization-member.service.ts
 * @description Service for managing organization members, invitations, and role-based access control
 *
 * This service handles all business logic related to organization membership including:
 * - Member invitation and acceptance workflow
 * - Role management and permissions (owner/admin/member)
 * - Member removal with proper authorization
 * - Bulk member operations and search/filtering
 * - Permission validation and role-based access control
 * - Member activity tracking and onboarding workflow
 *
 * Uses OrganizationRepository for data access operations and focuses on
 * business logic rather than raw database operations.
 */

import { v4 as uuidv4 } from 'uuid';
// import { OrganizationRepository } from '../../db/repositories/organization.repository';
import { IOrganizationRepository } from '../../db/repositories/interfaces/organization.repository.interface';
import {
  OrganizationMember,
  OrganizationInvite,
} from '../../../shared/types/organization.types';
import { getSupabaseClient } from '../supabase-client';
import { getZohoEmailService } from '../service-locator';

/**
 * Interface for member search and filtering options
 */
export interface MemberSearchOptions {
  role?: 'owner' | 'admin' | 'member';
  searchTerm?: string; // Search by email or name
  sortBy?: 'joined_at' | 'role' | 'name';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Interface for bulk member operations
 */
export interface BulkMemberOperation {
  userIds: string[];
  operation: 'update_role' | 'remove';
  newRole?: 'admin' | 'member';
}

/**
 * Interface for member invitation data
 */
export interface MemberInvitationData {
  organizationName: string;
  inviterName: string;
  role: string;
  token: string;
  expiresAt: Date;
}

/**
 * Interface for member activity tracking
 */
export interface MemberActivity {
  userId: string;
  action: 'invited' | 'joined' | 'role_changed' | 'removed';
  performedBy: string;
  timestamp: string;
  details?: Record<string, any>;
}

/**
 * Interface for member onboarding progress
 */
export interface MemberOnboardingStatus {
  userId: string;
  completedSteps: string[];
  totalSteps: number;
  isComplete: boolean;
  lastActivity: string;
}

/**
 * Result interface for authorization checks
 */
export interface AuthorizationResult {
  authorized: boolean;
  reason?: string;
  requiredRole?: string;
  currentRole?: string;
}

/**
 * Result interface for invitation operations
 */
export interface InvitationResult {
  success: boolean;
  invitation?: OrganizationInvite;
  error?: string;
  emailSent?: boolean;
}

/**
 * Result interface for member operations
 */
export interface MemberOperationResult {
  success: boolean;
  member?: OrganizationMember;
  error?: string;
  changes?: number;
}

export class OrganizationMemberService {
  private readonly INVITATION_EXPIRY_DAYS = 7;
  private readonly ONBOARDING_STEPS = [
    'profile_completed',
    'first_color_created',
    'first_product_created',
    'team_introduced',
  ];

  constructor(private repository: IOrganizationRepository) {}

  /**
   * MEMBER MANAGEMENT OPERATIONS
   */

  /**
   * Get all members for an organization with enhanced data
   */
  async getMembers(
    organizationId: string,
    options?: MemberSearchOptions
  ): Promise<OrganizationMember[]> {
    try {
      // Get base member data from repository
      const memberRows = this.repository.findMembers(organizationId);

      if (memberRows.length === 0) {
        return [];
      }

      // Get current user ID for isCurrentUser flag
      const currentUserId = await this.getCurrentUserId();

      // Map and enhance member data
      let members: OrganizationMember[] = await Promise.all(
        memberRows.map(async row => {
          const member = this.mapToMember(row);

          // Enhance with additional user data if missing
          if (!row.user_email && row.user_id) {
            const userData = await this.fetchUserFromLocal(row.user_id);
            if (userData) {
              member.user = userData;
              // Sync to local database for future queries
              await this.repository.syncUserProfile({
                id: userData.id,
                email: userData.email,
                name: userData.name,
              });
            }
          }

          return {
            ...member,
            isCurrentUser: row.user_id === currentUserId,
          };
        })
      );

      // Apply search and filtering
      if (options) {
        members = this.filterMembers(members, options);
      }

      return members;
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error getting members:',
        error
      );
      throw new Error(
        `Failed to get organization members: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get a specific member by user ID
   */
  async getMember(
    organizationId: string,
    userId: string
  ): Promise<OrganizationMember | null> {
    try {
      const memberRow = this.repository.findMember(organizationId, userId);
      if (!memberRow) {
        return null;
      }

      const member = this.mapToMember(memberRow);
      const currentUserId = await this.getCurrentUserId();

      return {
        ...member,
        isCurrentUser: memberRow.user_id === currentUserId,
      };
    } catch (error) {
      console.error('[OrganizationMemberService] Error getting member:', error);
      return null;
    }
  }

  /**
   * Add a member to an organization (direct add, not via invitation)
   */
  async addMember(
    organizationId: string,
    userId: string,
    role: 'admin' | 'member',
    invitedBy: string
  ): Promise<MemberOperationResult> {
    try {
      // Validate authorization
      const authResult = await this.validateMemberManagementPermission(
        organizationId,
        invitedBy
      );
      if (!authResult.authorized) {
        return {
          success: false,
          error: authResult.reason || 'Insufficient permissions to add members',
        };
      }

      // Check if user is already a member
      const existingMember = await this.getMember(organizationId, userId);
      if (existingMember) {
        return {
          success: false,
          error: 'User is already a member of this organization',
        };
      }

      // Add member via repository
      const success = this.repository.insertMember(
        organizationId,
        userId,
        role,
        invitedBy
      );
      if (!success) {
        return {
          success: false,
          error: 'Failed to add member to organization',
        };
      }

      // Get the newly added member
      const member = await this.getMember(organizationId, userId);
      if (!member) {
        return {
          success: false,
          error: 'Member was added but could not be retrieved',
        };
      }

      // Track activity
      await this.trackMemberActivity({
        userId,
        action: 'joined',
        performedBy: invitedBy,
        timestamp: new Date().toISOString(),
        details: { role, addedDirectly: true },
      });

      return {
        success: true,
        member,
        changes: 1,
      };
    } catch (error) {
      console.error('[OrganizationMemberService] Error adding member:', error);
      return {
        success: false,
        error: `Failed to add member: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Update a member's role with proper authorization
   */
  async updateMemberRole(
    organizationId: string,
    userId: string,
    newRole: 'admin' | 'member',
    updatedBy: string
  ): Promise<MemberOperationResult> {
    try {
      // Validate authorization
      const authResult = await this.validateRoleChangePermission(
        organizationId,
        userId,
        newRole,
        updatedBy
      );
      if (!authResult.authorized) {
        return {
          success: false,
          error:
            authResult.reason ||
            'Insufficient permissions to change member role',
        };
      }

      // Get current member data
      const currentMember = await this.getMember(organizationId, userId);
      if (!currentMember) {
        return {
          success: false,
          error: 'Member not found',
        };
      }

      // Prevent changing owner role
      if (currentMember.role === 'owner') {
        return {
          success: false,
          error: 'Cannot change owner role',
        };
      }

      // Update role via repository
      const success = this.repository.updateMemberRole(
        organizationId,
        userId,
        newRole
      );
      if (!success) {
        return {
          success: false,
          error: 'Failed to update member role',
        };
      }

      // Get updated member
      const updatedMember = await this.getMember(organizationId, userId);
      if (!updatedMember) {
        return {
          success: false,
          error: 'Member role was updated but could not be retrieved',
        };
      }

      // Track activity
      await this.trackMemberActivity({
        userId,
        action: 'role_changed',
        performedBy: updatedBy,
        timestamp: new Date().toISOString(),
        details: {
          oldRole: currentMember.role,
          newRole,
          changedBy: updatedBy,
        },
      });

      return {
        success: true,
        member: updatedMember,
        changes: 1,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error updating member role:',
        error
      );
      return {
        success: false,
        error: `Failed to update member role: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Remove a member from an organization with proper authorization
   */
  async removeMember(
    organizationId: string,
    userId: string,
    removedBy: string
  ): Promise<MemberOperationResult> {
    try {
      // Validate authorization
      const authResult = await this.validateMemberRemovalPermission(
        organizationId,
        userId,
        removedBy
      );
      if (!authResult.authorized) {
        return {
          success: false,
          error:
            authResult.reason || 'Insufficient permissions to remove member',
        };
      }

      // Get member data before removal
      const member = await this.getMember(organizationId, userId);
      if (!member) {
        return {
          success: false,
          error: 'Member not found',
        };
      }

      // Prevent removing owner
      if (member.role === 'owner') {
        return {
          success: false,
          error: 'Cannot remove organization owner',
        };
      }

      // Remove member via repository
      const success = this.repository.removeMember(organizationId, userId);
      if (!success) {
        return {
          success: false,
          error: 'Failed to remove member from organization',
        };
      }

      // Track activity
      await this.trackMemberActivity({
        userId,
        action: 'removed',
        performedBy: removedBy,
        timestamp: new Date().toISOString(),
        details: {
          removedRole: member.role,
          removedBy,
        },
      });

      return {
        success: true,
        member,
        changes: 1,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error removing member:',
        error
      );
      return {
        success: false,
        error: `Failed to remove member: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * INVITATION MANAGEMENT OPERATIONS
   */

  /**
   * Invite a member to join an organization
   */
  async inviteMember(
    organizationId: string,
    email: string,
    role: 'admin' | 'member',
    invitedBy: string
  ): Promise<InvitationResult> {
    try {
      // Validate authorization
      const authResult = await this.validateInvitationPermission(
        organizationId,
        invitedBy
      );
      if (!authResult.authorized) {
        return {
          success: false,
          error:
            authResult.reason || 'Insufficient permissions to invite members',
        };
      }

      // Get organization details
      const org = this.repository.findById(organizationId);
      if (!org) {
        return {
          success: false,
          error: 'Organization not found',
        };
      }

      // Check for existing invitation
      const existingInvitation = this.repository.checkExistingInvitation(
        organizationId,
        email
      );
      if (existingInvitation) {
        if (existingInvitation.accepted_at) {
          return {
            success: false,
            error:
              'User has already accepted an invitation to this organization',
          };
        } else {
          // Delete existing invitation to allow new one
          this.repository.deleteInvitation(organizationId, email);
        }
      }

      // Ensure inviter user exists in local database
      await this.ensureUserExists(invitedBy);

      // Create invitation
      const externalId = uuidv4();
      const token = uuidv4();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + this.INVITATION_EXPIRY_DAYS);

      const invitationId = this.repository.insertInvitation({
        id: externalId,
        organization_id: org.id,
        email,
        role,
        invited_by: invitedBy,
        token,
        expires_at: expiresAt.toISOString(),
      });

      if (!invitationId) {
        return {
          success: false,
          error: 'Failed to create invitation',
        };
      }

      // Get inviter details for email
      const inviterUser = this.repository.findUser(invitedBy);
      const inviterName =
        inviterUser?.display_name ||
        inviterUser?.name ||
        inviterUser?.email?.split('@')[0] ||
        'A team member';

      // Send invitation email
      let emailSent = false;
      try {
        emailSent = await this.sendInvitationEmail(email, {
          organizationName: org.name,
          inviterName,
          role,
          token,
          expiresAt,
        });
      } catch (emailError) {
        console.error(
          '[OrganizationMemberService] Failed to send invitation email:',
          emailError
        );
        // Don't fail the invitation creation if email fails
      }

      // Track activity
      await this.trackMemberActivity({
        userId: email, // Use email as identifier for invitations
        action: 'invited',
        performedBy: invitedBy,
        timestamp: new Date().toISOString(),
        details: {
          role,
          email,
          token: `${token.substring(0, 8)  }...`,
          emailSent,
        },
      });

      return {
        success: true,
        invitation: {
          id: externalId,
          organization_id: organizationId,
          email,
          role,
          token,
          invited_by: invitedBy,
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString(),
        },
        emailSent,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error inviting member:',
        error
      );
      return {
        success: false,
        error: `Failed to invite member: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Accept an invitation to join an organization
   */
  async acceptInvitation(
    token: string,
    userId: string
  ): Promise<{ success: boolean; organization?: any; error?: string }> {
    try {
      // Find the invitation
      const invitation = this.repository.findInvitation(token);
      if (!invitation) {
        return {
          success: false,
          error: 'Invalid or expired invitation',
        };
      }

      // Check if invitation has expired
      if (new Date(invitation.expires_at) < new Date()) {
        return {
          success: false,
          error: 'This invitation has expired',
        };
      }

      // Verify user email matches invitation
      const user = this.repository.findUser(userId);
      if (!user || user.email !== invitation.email) {
        return {
          success: false,
          error: 'This invitation is for a different email address',
        };
      }

      // Check if user is already a member
      const organizationId = this.repository.findById(
        invitation.organization_id.toString()
      )?.external_id;
      if (
        organizationId &&
        this.repository.checkUserMembership(organizationId, userId)
      ) {
        return {
          success: false,
          error: 'You are already a member of this organization',
        };
      }

      // Add user to organization and mark invitation as accepted
      const success = this.repository.insertMember(
        organizationId!,
        userId,
        invitation.role,
        invitation.invited_by
      );

      if (!success) {
        return {
          success: false,
          error: 'Failed to add you to the organization',
        };
      }

      // Mark invitation as accepted
      this.repository.acceptInvitation(invitation.id.toString());

      // Get organization details
      const org = this.repository.findById(
        invitation.organization_id.toString()
      );

      // Track activity
      await this.trackMemberActivity({
        userId,
        action: 'joined',
        performedBy: userId,
        timestamp: new Date().toISOString(),
        details: {
          role: invitation.role,
          invitedBy: invitation.invited_by,
          acceptedInvitation: true,
        },
      });

      return {
        success: true,
        organization: org ? this.mapToOrganization(org) : undefined,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error accepting invitation:',
        error
      );
      return {
        success: false,
        error: 'Failed to accept invitation',
      };
    }
  }

  /**
   * Get pending invitations for an organization
   */
  async getPendingInvitations(
    organizationId: string,
    requestedBy: string
  ): Promise<OrganizationInvite[]> {
    try {
      // Validate authorization
      const authResult = await this.validateInvitationViewPermission(
        organizationId,
        requestedBy
      );
      if (!authResult.authorized) {
        throw new Error(
          authResult.reason || 'Insufficient permissions to view invitations'
        );
      }

      const invitations =
        this.repository.findPendingInvitations(organizationId);

      return invitations.map(inv => ({
        id: inv.external_id,
        organization_id: organizationId,
        email: inv.email,
        role: inv.role as 'admin' | 'member',
        token: inv.token,
        invited_by: inv.invited_by,
        expires_at: inv.expires_at,
        created_at: inv.created_at,
      }));
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error getting pending invitations:',
        error
      );
      throw error;
    }
  }

  /**
   * Revoke an invitation
   */
  async revokeInvitation(
    organizationId: string,
    invitationId: string,
    revokedBy: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Validate authorization
      const authResult = await this.validateInvitationPermission(
        organizationId,
        revokedBy
      );
      if (!authResult.authorized) {
        return {
          success: false,
          error:
            authResult.reason ||
            'Insufficient permissions to revoke invitations',
        };
      }

      const success = this.repository.revokeInvitation(
        organizationId,
        invitationId
      );

      if (success) {
        // Track activity
        await this.trackMemberActivity({
          userId: invitationId, // Use invitation ID as identifier
          action: 'removed',
          performedBy: revokedBy,
          timestamp: new Date().toISOString(),
          details: {
            action: 'invitation_revoked',
            revokedBy,
          },
        });
      }

      return {
        success,
        error: success ? undefined : 'Failed to revoke invitation',
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error revoking invitation:',
        error
      );
      return {
        success: false,
        error: `Failed to revoke invitation: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * BULK OPERATIONS
   */

  /**
   * Perform bulk operations on multiple members
   */
  async bulkMemberOperation(
    organizationId: string,
    operation: BulkMemberOperation,
    performedBy: string
  ): Promise<{
    success: boolean;
    results: MemberOperationResult[];
    errors: string[];
  }> {
    const results: MemberOperationResult[] = [];
    const errors: string[] = [];

    try {
      // Validate authorization for bulk operations
      const authResult = await this.validateBulkOperationPermission(
        organizationId,
        performedBy
      );
      if (!authResult.authorized) {
        return {
          success: false,
          results: [],
          errors: [
            authResult.reason || 'Insufficient permissions for bulk operations',
          ],
        };
      }

      for (const userId of operation.userIds) {
        try {
          let result: MemberOperationResult;

          switch (operation.operation) {
            case 'update_role':
              if (!operation.newRole) {
                errors.push(`No new role specified for user ${userId}`);
                continue;
              }
              result = await this.updateMemberRole(
                organizationId,
                userId,
                operation.newRole,
                performedBy
              );
              break;

            case 'remove':
              result = await this.removeMember(
                organizationId,
                userId,
                performedBy
              );
              break;

            default:
              errors.push(`Unknown operation: ${operation.operation}`);
              continue;
          }

          results.push(result);
          if (!result.success && result.error) {
            errors.push(`${userId}: ${result.error}`);
          }
        } catch (error) {
          const errorMsg = `${userId}: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMsg);
          results.push({
            success: false,
            error: errorMsg,
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      return {
        success: successCount > 0,
        results,
        errors,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error in bulk operation:',
        error
      );
      return {
        success: false,
        results,
        errors: [
          `Bulk operation failed: ${error instanceof Error ? error.message : String(error)}`,
        ],
      };
    }
  }

  /**
   * PERMISSION VALIDATION
   */

  /**
   * Check if user has permission to manage members
   */
  async validateMemberManagementPermission(
    organizationId: string,
    userId: string
  ): Promise<AuthorizationResult> {
    try {
      const userRole = this.repository.getUserRole(organizationId, userId);

      if (!userRole) {
        return {
          authorized: false,
          reason: 'User is not a member of this organization',
          currentRole: 'none',
        };
      }

      if (userRole === 'member') {
        return {
          authorized: false,
          reason: 'Members cannot manage other members',
          requiredRole: 'admin',
          currentRole: userRole,
        };
      }

      return {
        authorized: true,
        currentRole: userRole,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error validating member management permission:',
        error
      );
      return {
        authorized: false,
        reason: 'Error checking permissions',
      };
    }
  }

  /**
   * Check if user has permission to change another user's role
   */
  async validateRoleChangePermission(
    organizationId: string,
    targetUserId: string,
    _newRole: string,
    performingUserId: string
  ): Promise<AuthorizationResult> {
    try {
      const performerRole = this.repository.getUserRole(
        organizationId,
        performingUserId
      );
      const targetRole = this.repository.getUserRole(
        organizationId,
        targetUserId
      );

      if (!performerRole) {
        return {
          authorized: false,
          reason: 'You are not a member of this organization',
          currentRole: 'none',
        };
      }

      if (!targetRole) {
        return {
          authorized: false,
          reason: 'Target user is not a member of this organization',
        };
      }

      // Owner can change anyone's role (except other owners)
      if (performerRole === 'owner' && targetRole !== 'owner') {
        return { authorized: true, currentRole: performerRole };
      }

      // Admin can only change member roles
      if (performerRole === 'admin' && targetRole === 'member') {
        return { authorized: true, currentRole: performerRole };
      }

      // Members can't change roles
      if (performerRole === 'member') {
        return {
          authorized: false,
          reason: 'Members cannot change user roles',
          requiredRole: 'admin',
          currentRole: performerRole,
        };
      }

      // Admin trying to change admin/owner role
      if (
        performerRole === 'admin' &&
        (targetRole === 'admin' || targetRole === 'owner')
      ) {
        return {
          authorized: false,
          reason: 'Admins cannot change other admin or owner roles',
          requiredRole: 'owner',
          currentRole: performerRole,
        };
      }

      return {
        authorized: false,
        reason: 'Insufficient permissions for this role change',
        currentRole: performerRole,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error validating role change permission:',
        error
      );
      return {
        authorized: false,
        reason: 'Error checking permissions',
      };
    }
  }

  /**
   * Check if user has permission to remove members
   */
  async validateMemberRemovalPermission(
    organizationId: string,
    targetUserId: string,
    performingUserId: string
  ): Promise<AuthorizationResult> {
    try {
      const performerRole = this.repository.getUserRole(
        organizationId,
        performingUserId
      );
      const targetRole = this.repository.getUserRole(
        organizationId,
        targetUserId
      );

      if (!performerRole) {
        return {
          authorized: false,
          reason: 'You are not a member of this organization',
          currentRole: 'none',
        };
      }

      if (!targetRole) {
        return {
          authorized: false,
          reason: 'Target user is not a member of this organization',
        };
      }

      // Cannot remove owner
      if (targetRole === 'owner') {
        return {
          authorized: false,
          reason: 'Cannot remove organization owner',
        };
      }

      // Owner can remove anyone (except owner, checked above)
      if (performerRole === 'owner') {
        return { authorized: true, currentRole: performerRole };
      }

      // Admin can only remove members
      if (performerRole === 'admin' && targetRole === 'member') {
        return { authorized: true, currentRole: performerRole };
      }

      // Self-removal is allowed for non-owners
      if (performingUserId === targetUserId && targetRole !== 'owner') {
        return { authorized: true, currentRole: performerRole };
      }

      return {
        authorized: false,
        reason: 'Insufficient permissions to remove this member',
        requiredRole: targetRole === 'admin' ? 'owner' : 'admin',
        currentRole: performerRole,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error validating member removal permission:',
        error
      );
      return {
        authorized: false,
        reason: 'Error checking permissions',
      };
    }
  }

  /**
   * Check if user has permission to send invitations
   */
  async validateInvitationPermission(
    organizationId: string,
    userId: string
  ): Promise<AuthorizationResult> {
    return this.validateMemberManagementPermission(organizationId, userId);
  }

  /**
   * Check if user has permission to view invitations
   */
  async validateInvitationViewPermission(
    organizationId: string,
    userId: string
  ): Promise<AuthorizationResult> {
    return this.validateMemberManagementPermission(organizationId, userId);
  }

  /**
   * Check if user has permission for bulk operations
   */
  async validateBulkOperationPermission(
    organizationId: string,
    userId: string
  ): Promise<AuthorizationResult> {
    try {
      const userRole = this.repository.getUserRole(organizationId, userId);

      if (!userRole) {
        return {
          authorized: false,
          reason: 'User is not a member of this organization',
          currentRole: 'none',
        };
      }

      // Only owners and admins can perform bulk operations
      if (userRole === 'member') {
        return {
          authorized: false,
          reason: 'Members cannot perform bulk operations',
          requiredRole: 'admin',
          currentRole: userRole,
        };
      }

      return {
        authorized: true,
        currentRole: userRole,
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error validating bulk operation permission:',
        error
      );
      return {
        authorized: false,
        reason: 'Error checking permissions',
      };
    }
  }

  /**
   * MEMBER ACTIVITY TRACKING
   */

  /**
   * Track member activity for audit purposes
   */
  async trackMemberActivity(activity: MemberActivity): Promise<void> {
    try {
      // In a real implementation, this would store activities in a dedicated table
      // For now, we'll log the activity for debugging purposes
      console.log('[OrganizationMemberService] Member Activity:', {
        userId: activity.userId,
        action: activity.action,
        performedBy: activity.performedBy,
        timestamp: activity.timestamp,
        details: activity.details,
      });

      // TODO: Implement proper activity storage
      // this.repository.insertMemberActivity(activity);
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error tracking member activity:',
        error
      );
      // Don't throw here as activity tracking shouldn't break operations
    }
  }

  /**
   * Get member activity history for an organization
   */
  async getMemberActivityHistory(
    organizationId: string,
    requestedBy: string,
    _limit: number = 50
  ): Promise<MemberActivity[]> {
    try {
      // Validate authorization
      const authResult = await this.validateMemberManagementPermission(
        organizationId,
        requestedBy
      );
      if (!authResult.authorized) {
        throw new Error(
          authResult.reason ||
            'Insufficient permissions to view member activity'
        );
      }

      // TODO: Implement proper activity retrieval
      // return this.repository.findMemberActivities(organizationId, limit);

      // For now, return empty array
      return [];
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error getting member activity history:',
        error
      );
      throw error;
    }
  }

  /**
   * MEMBER ONBOARDING
   */

  /**
   * Get member onboarding status
   */
  async getMemberOnboardingStatus(
    _organizationId: string,
    userId: string
  ): Promise<MemberOnboardingStatus> {
    try {
      // TODO: Implement proper onboarding status tracking
      // For now, return a basic status
      return {
        userId,
        completedSteps: [],
        totalSteps: this.ONBOARDING_STEPS.length,
        isComplete: false,
        lastActivity: new Date().toISOString(),
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error getting onboarding status:',
        error
      );
      throw error;
    }
  }

  /**
   * Update member onboarding progress
   */
  async updateOnboardingProgress(
    _organizationId: string,
    userId: string,
    completedStep: string
  ): Promise<MemberOnboardingStatus> {
    try {
      // Validate that the step is valid
      if (!this.ONBOARDING_STEPS.includes(completedStep)) {
        throw new Error(`Invalid onboarding step: ${completedStep}`);
      }

      // TODO: Implement proper onboarding progress storage
      // For now, return updated status
      return {
        userId,
        completedSteps: [completedStep],
        totalSteps: this.ONBOARDING_STEPS.length,
        isComplete: false,
        lastActivity: new Date().toISOString(),
      };
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Error updating onboarding progress:',
        error
      );
      throw error;
    }
  }

  /**
   * UTILITY METHODS
   */

  /**
   * Filter members based on search options
   */
  private filterMembers(
    members: OrganizationMember[],
    options: MemberSearchOptions
  ): OrganizationMember[] {
    let filtered = [...members];

    // Filter by role
    if (options.role) {
      filtered = filtered.filter(member => member.role === options.role);
    }

    // Filter by search term (email or name)
    if (options.searchTerm) {
      const searchLower = options.searchTerm.toLowerCase();
      filtered = filtered.filter(
        member =>
          member.user?.email?.toLowerCase().includes(searchLower) ||
          member.user?.name?.toLowerCase().includes(searchLower)
      );
    }

    // Sort results
    if (options.sortBy) {
      filtered.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (options.sortBy) {
          case 'joined_at':
            aValue = new Date(a.joined_at).getTime();
            bValue = new Date(b.joined_at).getTime();
            break;
          case 'role':
            // Sort order: owner, admin, member
            const roleOrder = { owner: 0, admin: 1, member: 2 };
            aValue = roleOrder[a.role];
            bValue = roleOrder[b.role];
            break;
          case 'name':
            aValue = (a.user?.name || a.user?.email || '').toLowerCase();
            bValue = (b.user?.name || b.user?.email || '').toLowerCase();
            break;
          default:
            return 0;
        }

        if (options.sortOrder === 'desc') {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });
    }

    // Apply pagination
    if (options.offset || options.limit) {
      const start = options.offset || 0;
      const end = options.limit ? start + options.limit : undefined;
      filtered = filtered.slice(start, end);
    }

    return filtered;
  }

  /**
   * Map database row to OrganizationMember
   */
  private mapToMember(row: any): OrganizationMember {
    const displayName =
      row.user_display_name ||
      row.user_name ||
      (row.user_email
        ? row.user_email.split('@')[0]
        : `user-${row.user_id.substring(0, 8)}`);

    return {
      organization_id: row.organization_id,
      user_id: row.user_id,
      role: row.role,
      joined_at: row.joined_at,
      invited_by: row.invited_by,
      user: {
        id: row.user_id,
        email: row.user_email || 'Unknown Email',
        name: displayName,
      },
    };
  }

  /**
   * Map database row to Organization
   */
  private mapToOrganization(row: any): any {
    return {
      id: row.external_id,
      external_id: row.external_id,
      name: row.name,
      slug: row.slug,
      plan: row.plan,
      settings:
        typeof row.settings === 'string'
          ? JSON.parse(row.settings)
          : row.settings || {},
      created_at: row.created_at,
      updated_at: row.updated_at,
      memberCount: row.member_count || 1,
    };
  }

  /**
   * Get current user ID from Supabase
   */
  private async getCurrentUserId(): Promise<string | null> {
    try {
      const supabase = getSupabaseClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      return user?.id || null;
    } catch (error) {
      console.error(
        '[OrganizationMemberService] Failed to get current user:',
        error
      );
      return null;
    }
  }

  /**
   * Fetch user data from local users table (LOCAL-FIRST)
   */
  private async fetchUserFromLocal(
    userId: string
  ): Promise<{ id: string; email: string; name?: string } | null> {
    try {
      // Use local repository to fetch user data - no cloud dependency
      const user = this.repository.findUser(userId);

      if (!user || !user.email) {
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        name:
          user.display_name || user.name || user.email.split('@')[0] || 'User',
      };
    } catch (error) {
      console.error(
        `[OrganizationMemberService] Error fetching user ${userId} from local database:`,
        error
      );
      return null;
    }
  }

  /**
   * Ensure user exists in local database
   */
  private async ensureUserExists(userId: string): Promise<void> {
    try {
      const existingUser = this.repository.findUser(userId);
      if (existingUser) {
        return;
      }

      // Fetch from Supabase and sync
      const userData = await this.fetchUserFromLocal(userId);
      if (userData) {
        this.repository.syncUserProfile({
          id: userData.id,
          email: userData.email,
          name: userData.name,
        });
      }
    } catch (error) {
      console.error(
        `[OrganizationMemberService] Error ensuring user exists: ${userId}:`,
        error
      );
      // Don't throw - this is a fallback operation
    }
  }

  /**
   * Send invitation email using Zoho Mail service
   */
  private async sendInvitationEmail(
    to: string,
    invitation: MemberInvitationData
  ): Promise<boolean> {
    try {
      const zohoEmailService = getZohoEmailService();
      const result = await zohoEmailService.sendInvitationEmail(to, invitation);

      if (result) {
        console.log(
          `[OrganizationMemberService] Invitation email sent successfully to ${to}`
        );
      } else {
        console.error(
          `[OrganizationMemberService] Failed to send invitation email to ${to}`
        );
      }

      return result;
    } catch (error) {
      console.error('[OrganizationMemberService] Email sending error:', error);
      return false;
    }
  }
}
