/**
 * @file sync-api.ts
 * @description Sync API preload script for secure IPC communication
 */

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { SyncConfig } from '../shared/types/sync.types';

// Typed API for sync operations
contextBridge.exposeInMainWorld('syncAPI', {
  // Get sync configuration
  getConfig: async () => {
    console.log('Preload: calling syncAPI.getConfig');
    return ipcRenderer.invoke('sync:get-config');
  },

  // Check for unsynced local changes
  hasUnsyncedLocalChanges: async () => {
    console.log('Preload: calling syncAPI.hasUnsyncedLocalChanges');
    return ipcRenderer.invoke('sync:has-unsynced-local-changes');
  },

  // Update sync configuration
  updateConfig: async (config: Partial<SyncConfig>) => {
    console.log('Preload: calling syncAPI.updateConfig');
    return ipcRenderer.invoke('sync:update-config', config);
  },

  // Get sync state
  getState: async () => {
    console.log('Preload: calling syncAPI.getState');
    return ipcR<PERSON>er.invoke('sync:get-state');
  },

  // Get authentication state
  getAuthState: async () => {
    console.log('Preload: calling syncAPI.getAuthState');
    return ipcRenderer.invoke('sync:get-auth-state');
  },

  // Login with Google (new Supabase implementation)
  login: async () => {
    console.log('Preload: calling syncAPI.login (Google OAuth)');
    return ipcRenderer.invoke('sync:login');
  },

  // Accept GDPR consent
  acceptGDPR: async (ip?: string) => {
    console.log('Preload: calling syncAPI.acceptGDPR');
    return ipcRenderer.invoke('sync:accept-gdpr', ip);
  },

  // Debug handlers for development
  debugOutbox: async () => {
    console.log('Preload: calling syncAPI.debugOutbox');
    return ipcRenderer.invoke('sync:debug-outbox');
  },

  clearOutbox: async () => {
    console.log('Preload: calling syncAPI.clearOutbox');
    return ipcRenderer.invoke('sync:clear-outbox');
  },

  // Export user data (GDPR)
  exportData: async () => {
    console.log('Preload: calling syncAPI.exportData');
    return ipcRenderer.invoke('sync:export-data');
  },

  // Delete account and data (GDPR)
  deleteAccount: async () => {
    console.log('Preload: calling syncAPI.deleteAccount');
    return ipcRenderer.invoke('sync:delete-account');
  },

  // Logout
  logout: async () => {
    console.log('Preload: calling syncAPI.logout');
    return ipcRenderer.invoke('sync:logout');
  },

  // Sync data (manual sync)
  sync: async () => {
    console.log('Preload: calling syncAPI.sync');
    return ipcRenderer.invoke('sync:sync');
  },

  // Initialize sync
  initialize: async () => {
    console.log('Preload: calling syncAPI.initialize');
    return ipcRenderer.invoke('sync:initialize');
  },

  // Legacy compatibility methods (for backward compatibility)
  signup: async (_email: string, _password: string) => {
    console.log('Preload: signup not supported in Google-only mode');
    return { success: false, error: 'Please use Google sign-in' };
  },

  syncData: async () => {
    console.log('Preload: calling syncAPI.syncData (legacy)');
    return ipcRenderer.invoke('sync:sync');
  },

  testConnection: async () => {
    console.log('Preload: calling syncAPI.testConnection');
    const authState = await ipcRenderer.invoke('sync:get-auth-state');
    return { success: authState.isAuthenticated };
  },

  subscribe: async () => {
    console.log('Preload: subscribe handled automatically by real-time sync');
    return { success: true };
  },

  unsubscribe: async () => {
    console.log('Preload: unsubscribe handled automatically by real-time sync');
    return { success: true };
  },

  // Resolve conflicts (using last-write-wins strategy)
  resolveConflicts: async (resolutions: Array<{
    conflictId: string;
    resolution: 'local' | 'remote' | 'merged';
    mergedData?: unknown;
  }>) => {
    console.log('Preload: conflicts resolved automatically with last-write-wins');
    return { success: true, resolutions };
  },

  // OAuth configuration methods
  configureOAuth: async (settings: {
    authTimeout?: number;
    sessionTimeout?: number;
    sessionWarningTime?: number;
    autoLogoutEnabled?: boolean;
  }) => {
    console.log('Preload: calling oauth:configure');
    return ipcRenderer.invoke('oauth:configure', settings);
  },

  getOAuthConfig: async () => {
    console.log('Preload: calling oauth:get-config');
    return ipcRenderer.invoke('oauth:get-config');
  },

  updateActivity: async () => {
    console.log('Preload: calling oauth:update-activity');
    return ipcRenderer.invoke('oauth:update-activity');
  },

  resetAuthLoop: async () => {
    console.log('Preload: calling oauth:reset-auth-loop');
    return ipcRenderer.invoke('oauth:reset-auth-loop');
  },

  recoverAuth: async () => {
    console.log('Preload: calling oauth:recover-auth');
    return ipcRenderer.invoke('oauth:recover-auth');
  },

  checkAuthHealth: async () => {
    console.log('Preload: calling oauth:check-health');
    return ipcRenderer.invoke('oauth:check-health');
  },

  // ============================================================================
  // PROGRESS TRACKING APIs
  // ============================================================================

  // Get current sync progress
  getProgress: async () => {
    console.log('Preload: calling syncAPI.getProgress');
    return ipcRenderer.invoke('sync:get-progress');
  },

  // Get sync queue statistics
  getQueueStats: async () => {
    console.log('Preload: calling syncAPI.getQueueStats');
    return ipcRenderer.invoke('sync:get-queue-stats');
  },

  // Clear failed delete operations for non-existent items
  clearNotFoundDeletes: async () => {
    console.log('Preload: calling syncAPI.clearNotFoundDeletes');
    return ipcRenderer.invoke('sync:clear-not-found-deletes');
  },


  // Get sync performance metrics
  getMetrics: async () => {
    console.log('Preload: calling syncAPI.getMetrics');
    return ipcRenderer.invoke('sync:get-metrics');
  },

  // Get comprehensive sync status report
  getStatusReport: async () => {
    console.log('Preload: calling syncAPI.getStatusReport');
    return ipcRenderer.invoke('sync:get-status-report');
  },

  // Update user activity for intelligent polling
  updateUserActivity: async () => {
    console.log('Preload: calling syncAPI.updateUserActivity');
    return ipcRenderer.invoke('sync:update-user-activity');
  },

  // ============================================================================
  // REAL-TIME EVENT LISTENERS
  // ============================================================================

  // Listen for detailed sync progress updates
  onProgressUpdate: (callback: (progress: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to sync:progress-update');
    const handler = (_: unknown, progress: Record<string, unknown>) => callback(progress);
    ipcRenderer.on('sync:progress-update', handler);
    return () => ipcRenderer.removeListener('sync:progress-update', handler);
  },

  // Listen for sync status updates
  onStatusUpdate: (callback: (status: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to sync:status-update');
    const handler = (_: unknown, status: Record<string, unknown>) => callback(status);
    ipcRenderer.on('sync:status-update', handler);
    return () => ipcRenderer.removeListener('sync:status-update', handler);
  },

  // Listen for sync queue updates
  onQueueUpdate: (callback: (queueStats: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to sync:queue-update');
    const handler = (_: unknown, queueStats: Record<string, unknown>) => callback(queueStats);
    ipcRenderer.on('sync:queue-update', handler);
    return () => ipcRenderer.removeListener('sync:queue-update', handler);
  },

  // Listen for sync metrics updates
  onMetricsUpdate: (callback: (metrics: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to sync:metrics-update');
    const handler = (_: unknown, metrics: Record<string, unknown>) => callback(metrics);
    ipcRenderer.on('sync:metrics-update', handler);
    return () => ipcRenderer.removeListener('sync:metrics-update', handler);
  },

  // Listen for slow operation warnings
  onSlowOperation: (callback: (data: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to sync:slow-operation');
    const handler = (_: unknown, data: Record<string, unknown>) => callback(data);
    ipcRenderer.on('sync:slow-operation', handler);
    return () => ipcRenderer.removeListener('sync:slow-operation', handler);
  },

  // Listen for conflict events
  onConflicts: (callback: (conflicts: Record<string, unknown>[]) => void) => {
    console.log('Preload: subscribing to sync:conflicts');
    const handler = (_: unknown, conflicts: Record<string, unknown>[]) => callback(conflicts);
    ipcRenderer.on('sync:conflicts', handler);
    return () => ipcRenderer.removeListener('sync:conflicts', handler);
  },

  // Remove all real-time event listeners
  removeAllProgressListeners: () => {
    console.log('Preload: removing all sync progress listeners');
    ipcRenderer.removeAllListeners('sync:progress-update');
    ipcRenderer.removeAllListeners('sync:status-update');
    ipcRenderer.removeAllListeners('sync:queue-update');
    ipcRenderer.removeAllListeners('sync:metrics-update');
    ipcRenderer.removeAllListeners('sync:slow-operation');
    ipcRenderer.removeAllListeners('sync:conflicts');
  },
});

// Debug console logger for development
contextBridge.exposeInMainWorld('debugAPI', {
  log: (level: string, message: string) => {
    ipcRenderer.invoke('debug-console-log', { level, message });
  }
});

// Set up console interception for development (runs in preload context before renderer)
if (process.env.NODE_ENV === 'development') {
  // This code needs to run in the main world, so we'll inject it using a script tag
  const consoleInterceptorScript = `
    (function() {
      // Buffer for logs before debugAPI is ready
      const logBuffer = [];
      let debugAPIReady = false;
      let checkInterval;

      // Store original console methods immediately
      const originalConsole = {
        log: console.log,
        warn: console.warn,
        error: console.error,
        info: console.info
      };

      const safeStringify = (obj) => {
        try {
          return typeof obj === 'object' ? JSON.stringify(obj, null, 2) : String(obj);
        } catch (e) {
          return '[Circular Reference]';
        }
      };

      const sendToDebugLog = (level, args) => {
        const message = args.map(safeStringify).join(' ');
        
        if (debugAPIReady && window.debugAPI?.log) {
          try {
            window.debugAPI.log(level, message);
          } catch (e) {
            // Fallback to original console if IPC fails
            originalConsole.error('[DebugLogger] IPC failed:', e);
          }
        } else {
          // Buffer the log until debugAPI is ready
          logBuffer.push({ level, message, timestamp: Date.now() });
        }
      };

      const flushLogBuffer = () => {
        if (debugAPIReady && window.debugAPI?.log) {
          while (logBuffer.length > 0) {
            const { level, message } = logBuffer.shift();
            try {
              window.debugAPI.log(level, message);
            } catch (e) {
              originalConsole.error('[DebugLogger] Failed to flush buffered log:', e);
              break;
            }
          }
        }
      };

      // Check for debugAPI availability
      const checkDebugAPI = () => {
        if (window.debugAPI?.log && !debugAPIReady) {
          debugAPIReady = true;
          clearInterval(checkInterval);
          console.log('[DebugLogger] Console interception enabled in preload');
          flushLogBuffer();
        }
      };

      // Poll for debugAPI (it should be available almost immediately)
      checkInterval = setInterval(checkDebugAPI, 10);
      
      // Timeout after 1 second if debugAPI never becomes available
      setTimeout(() => {
        if (!debugAPIReady) {
          clearInterval(checkInterval);
          originalConsole.warn('[DebugLogger] debugAPI timeout - logging disabled');
        }
      }, 1000);

      // Override console methods immediately
      console.log = (...args) => {
        originalConsole.log(...args);
        sendToDebugLog('LOG', args);
      };

      console.warn = (...args) => {
        originalConsole.warn(...args);
        sendToDebugLog('WARN', args);
      };

      console.error = (...args) => {
        originalConsole.error(...args);
        sendToDebugLog('ERROR', args);
      };

      console.info = (...args) => {
        originalConsole.info(...args);
        sendToDebugLog('INFO', args);
      };

      // Initial check in case debugAPI is already available
      checkDebugAPI();
    })();
  `;

  // Inject the script into the main world when DOM is ready
  window.addEventListener('DOMContentLoaded', () => {
    const script = document.createElement('script');
    script.textContent = consoleInterceptorScript;
    document.head.appendChild(script);
  });
}

// Auto-sync API - exposed separately for better organization
contextBridge.exposeInMainWorld('autoSyncAPI', {
  // Get auto-sync configuration
  getConfig: async () => {
    console.log('Preload: calling auto-sync:get-config');
    return ipcRenderer.invoke('auto-sync:get-config');
  },

  // Update auto-sync configuration
  updateConfig: async (config: Record<string, unknown>) => {
    console.log('Preload: calling auto-sync:update-config');
    return ipcRenderer.invoke('auto-sync:update-config', config);
  },

  // Initialize auto-sync manager
  initialize: async (config?: Record<string, unknown>) => {
    console.log('Preload: calling auto-sync:initialize');
    return ipcRenderer.invoke('auto-sync:initialize', config);
  },

  // Force sync now
  forceSync: async () => {
    console.log('Preload: calling auto-sync:force-sync');
    return ipcRenderer.invoke('auto-sync:force-sync');
  },

  // Get auto-sync status
  getStatus: async () => {
    console.log('Preload: calling auto-sync:get-status');
    return ipcRenderer.invoke('auto-sync:get-status');
  },

  // Clean up auto-sync manager
  cleanup: async () => {
    console.log('Preload: calling auto-sync:cleanup');
    return ipcRenderer.invoke('auto-sync:cleanup');
  },

  // Listen for auto-sync events
  onSyncStarted: (callback: (data: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to auto-sync:sync-started');
    ipcRenderer.on('auto-sync:sync-started', (_, data) => callback(data));
  },

  onSyncCompleted: (callback: (data: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to auto-sync:sync-completed');
    ipcRenderer.on('auto-sync:sync-completed', (_, data) => callback(data));
  },

  onSyncFailed: (callback: (data: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to auto-sync:sync-failed');
    ipcRenderer.on('auto-sync:sync-failed', (_, data) => callback(data));
  },

  onConfigUpdated: (callback: (data: Record<string, unknown>) => void) => {
    console.log('Preload: subscribing to auto-sync:config-updated');
    ipcRenderer.on('auto-sync:config-updated', (_, data) => callback(data));
  },

  // Remove event listeners
  removeAllListeners: () => {
    console.log('Preload: removing all auto-sync listeners');
    ipcRenderer.removeAllListeners('auto-sync:sync-started');
    ipcRenderer.removeAllListeners('auto-sync:sync-completed');
    ipcRenderer.removeAllListeners('auto-sync:sync-failed');
    ipcRenderer.removeAllListeners('auto-sync:config-updated');
  },
});
