/**
 * Custom hook for finding similar colors
 * Uses Delta E calculations with memoization for performance
 */

import { useMemo } from 'react';
import { useColorStore } from '../../../../store/color.store';
import { calculateDeltaERgb } from '../../../../../shared/utils/color/analysis';
import { hexToRgb as hexToRgbUtil } from '../../../../../shared/utils/color/conversion';
import type { SimpleColorEntry } from '../types';

const MAX_SIMILAR_COLORS = 6;
const SIMILARITY_THRESHOLD = 15; // Delta E threshold for "similar"

export const useSimilarColors = (baseColor: SimpleColorEntry | null) => {
  const {
    pantoneColors,
    colors: savedColors,
    loadPantoneColors,
  } = useColorStore();

  const similarColors = useMemo(() => {
    if (!baseColor?.hex) {
      return [];
    }

    const baseRgb = hexToRgbUtil(baseColor.hex);
    if (!baseRgb) {
      return [];
    }

    // Ensure arrays are available and defined
    const safePantoneColors = pantoneColors || [];
    const safeSavedColors = savedColors || [];

    // Auto-load pantone colors if empty
    if (safePantoneColors.length === 0) {
      loadPantoneColors().catch(console.error);
    }

    // Combine Pantone and saved colors
    const allColors = [...safePantoneColors, ...safeSavedColors];

    // Calculate distances and filter
    const colorDistances = allColors
      .filter(color => color.hex && color.id !== baseColor.id)
      .map(color => {
        const rgb = hexToRgbUtil(color.hex);
        if (!rgb) {
          return null;
        }

        const distance = calculateDeltaERgb(baseRgb, rgb);
        return { color, distance };
      })
      .filter(
        (item): item is { color: (typeof allColors)[0]; distance: number } =>
          item !== null && item.distance < SIMILARITY_THRESHOLD
      )
      .sort((a, b) => a.distance - b.distance)
      .slice(0, MAX_SIMILAR_COLORS);

    // Convert to SimpleColorEntry format
    return colorDistances.map(({ color }) => ({
      id: color.id,
      hex: color.hex,
      pantone: color.code || 'Custom',
      cmyk: color.cmyk || 'N/A',
    }));
  }, [baseColor, pantoneColors, savedColors]);

  const findNearestPrintableColor = useMemo(() => {
    const safePantoneColors = pantoneColors || [];
    if (!baseColor?.hex || !safePantoneColors.length) {
      return null;
    }

    const baseRgb = hexToRgbUtil(baseColor.hex);
    if (!baseRgb) {
      return null;
    }

    let nearest = safePantoneColors[0];
    let minDistance = Infinity;

    for (const color of safePantoneColors) {
      if (!color.hex) {
        continue;
      }

      const rgb = hexToRgbUtil(color.hex);
      if (!rgb) {
        continue;
      }

      const distance = calculateDeltaERgb(baseRgb, rgb);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = color;
      }
    }

    return {
      color: nearest,
      deltaE: minDistance,
      isPrintable: minDistance < 2, // Delta E < 2 is considered "same" color
    };
  }, [baseColor, pantoneColors]);

  return {
    similarColors,
    nearestPrintable: findNearestPrintableColor,
  };
};
