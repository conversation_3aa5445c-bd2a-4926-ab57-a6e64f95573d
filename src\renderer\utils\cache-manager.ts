/**
 * @file cache-manager.ts
 * @description Global cache management utilities to prevent excessive API calls
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  organizationId?: string;
}

class CacheManager {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = 30000; // 30 seconds

  /**
   * Get cached data if it exists and is not expired
   */
  get<T>(key: string, organizationId?: string): T | null {
    const cacheKey = organizationId ? `${key}_${organizationId}` : key;
    const entry = this.cache.get(cacheKey);

    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > this.defaultTTL) {
      this.cache.delete(cacheKey);
      return null;
    }

    // Check if organization context has changed
    if (organizationId && entry.organizationId !== organizationId) {
      this.cache.delete(cacheKey);
      return null;
    }

    return entry.data;
  }

  /**
   * Set cached data
   */
  set<T>(key: string, data: T, organizationId?: string, ttl?: number): void {
    const cacheKey = organizationId ? `${key}_${organizationId}` : key;

    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      organizationId,
    });

    // Set expiration if custom TTL provided
    if (ttl && ttl !== this.defaultTTL) {
      setTimeout(() => {
        this.cache.delete(cacheKey);
      }, ttl);
    }
  }

  /**
   * Invalidate cache entries for a specific organization
   */
  invalidateOrganization(organizationId: string): void {
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (entry.organizationId === organizationId) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    console.log(
      `[CacheManager] Invalidated ${keysToDelete.length} cache entries for organization ${organizationId}`
    );
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    console.log(`[CacheManager] Cleared ${size} cache entries`);
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// Global cache manager instance
export const cacheManager = new CacheManager();

// Export utility functions
export const useCache = <T>(
  key: string,
  fetchFn: () => Promise<T>,
  organizationId?: string,
  ttl?: number
): Promise<T> => {
  const cached = cacheManager.get<T>(key, organizationId);

  if (cached !== null) {
    return Promise.resolve(cached);
  }

  return fetchFn().then(data => {
    cacheManager.set(key, data, organizationId, ttl);
    return data;
  });
};
