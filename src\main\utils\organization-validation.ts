/**
 * @file organization-validation.ts
 * @description Utility functions for validating organization context and IDs
 */

/**
 * Organization validation error types
 */
export class OrganizationValidationError extends Error {
  constructor(
    message: string,
    public readonly code: string
  ) {
    super(message);
    this.name = 'OrganizationValidationError';
  }
}

/**
 * Validation result for organization operations
 */
interface OrganizationValidationResult {
  isValid: boolean;
  sanitizedId?: string;
  error?: string;
  warnings?: string[];
}

/**
 * Organization ID format validation patterns
 */
const ORGANIZATION_ID_PATTERNS = {
  // General UUID pattern for external IDs (relaxed from strict v4)
  externalId: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
  // Allow some flexibility for internal IDs (integers)
  internalId: /^\d+$/,
};

/**
 * Validates an organization ID format and security
 */
export function validateOrganizationId(
  organizationId: unknown,
  context?: string
): OrganizationValidationResult {
  const contextMsg = context ? ` in ${context}` : '';

  // Basic type and null validation
  if (organizationId === null || organizationId === undefined) {
    return {
      isValid: false,
      error: `Organization ID is required${contextMsg}`,
    };
  }

  if (typeof organizationId !== 'string') {
    return {
      isValid: false,
      error: `Organization ID must be a string${contextMsg}, got ${typeof organizationId}`,
    };
  }

  // Empty string validation
  const trimmed = organizationId.trim();
  if (trimmed === '') {
    return {
      isValid: false,
      error: `Organization ID cannot be empty${contextMsg}`,
    };
  }

  // Length validation
  if (trimmed.length < 3) {
    return {
      isValid: false,
      error: `Organization ID too short${contextMsg}`,
    };
  }

  if (trimmed.length > 50) {
    return {
      isValid: false,
      error: `Organization ID too long${contextMsg}`,
    };
  }

  // Format validation
  const isValidExternalId = ORGANIZATION_ID_PATTERNS.externalId.test(trimmed);
  const isValidInternalId = ORGANIZATION_ID_PATTERNS.internalId.test(trimmed);

  if (!isValidExternalId && !isValidInternalId) {
    return {
      isValid: false,
      error: `Invalid organization ID format${contextMsg}. Expected UUID or integer ID`,
    };
  }

  const warnings: string[] = [];

  // Warn about internal ID usage (should prefer external UUIDs)
  if (isValidInternalId) {
    warnings.push(
      'Using internal integer ID - consider using external UUID for better security'
    );
  }

  return {
    isValid: true,
    sanitizedId: trimmed,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

/**
 * Validates and throws error if organization ID is invalid
 */
export function requireValidOrganizationId(
  organizationId: unknown,
  context?: string
): string {
  const result = validateOrganizationId(organizationId, context);

  if (!result.isValid) {
    throw new OrganizationValidationError(
      result.error!,
      'INVALID_ORGANIZATION_ID'
    );
  }

  if (result.warnings && result.warnings.length > 0) {
    console.warn(
      `[OrganizationValidation] ${context || 'Operation'} warnings:`,
      result.warnings
    );
  }

  return result.sanitizedId!;
}

/**
 * Validates that user has access to organization (placeholder for future implementation)
 */
export function validateOrganizationAccess(
  organizationId: string,
  userId: string,
  _requiredRole?: 'owner' | 'admin' | 'member'
): boolean {
  // TODO: Implement actual organization access validation
  // This would check the organization_members table

  // For now, just validate the IDs are present
  const orgResult = validateOrganizationId(
    organizationId,
    'organization access check'
  );
  const userResult = validateUserId(userId, 'organization access check');

  if (!orgResult.isValid || !userResult.isValid) {
    return false;
  }

  // Placeholder - in real implementation would query database
  return true;
}

/**
 * Validates user ID format (similar to organization ID validation)
 */
export function validateUserId(
  userId: unknown,
  context?: string
): OrganizationValidationResult {
  const contextMsg = context ? ` in ${context}` : '';

  if (userId === null || userId === undefined) {
    return {
      isValid: false,
      error: `User ID is required${contextMsg}`,
    };
  }

  if (typeof userId !== 'string') {
    return {
      isValid: false,
      error: `User ID must be a string${contextMsg}, got ${typeof userId}`,
    };
  }

  const trimmed = userId.trim();
  if (trimmed === '') {
    return {
      isValid: false,
      error: `User ID cannot be empty${contextMsg}`,
    };
  }

  if (trimmed.length < 3 || trimmed.length > 50) {
    return {
      isValid: false,
      error: `Invalid user ID length${contextMsg}`,
    };
  }

  return {
    isValid: true,
    sanitizedId: trimmed,
  };
}

/**
 * Decorator for service methods that require organization validation
 */
export function RequireOrganization(
  target: any,
  propertyName: string,
  descriptor: PropertyDescriptor
) {
  const method = descriptor.value;

  descriptor.value = function (organizationId: string, ...args: any[]) {
    // Validate organization ID as first parameter
    const validOrgId = requireValidOrganizationId(
      organizationId,
      `${target.constructor.name}.${propertyName}`
    );

    // Call original method with validated ID
    return method.call(this, validOrgId, ...args);
  };

  return descriptor;
}

/**
 * Creates a validation wrapper for service methods
 */
export function withOrganizationValidation<T extends (...args: any[]) => any>(
  fn: T,
  context: string
): T {
  return ((...args: any[]) => {
    // Assume first parameter is organizationId
    if (args.length > 0) {
      args[0] = requireValidOrganizationId(args[0], context);
    }
    return fn(...args);
  }) as T;
}

/**
 * Batch validation for multiple organization IDs
 */
export function validateOrganizationIds(
  organizationIds: unknown[],
  context?: string
): { valid: string[]; invalid: Array<{ id: unknown; error: string }> } {
  const valid: string[] = [];
  const invalid: Array<{ id: unknown; error: string }> = [];

  for (const id of organizationIds) {
    const result = validateOrganizationId(id, context);
    if (result.isValid) {
      valid.push(result.sanitizedId!);
    } else {
      invalid.push({ id, error: result.error! });
    }
  }

  return { valid, invalid };
}

/**
 * Security audit function for organization operations
 */
export function auditOrganizationOperation(
  operation: string,
  organizationId: string,
  userId?: string,
  metadata?: Record<string, any>
): void {
  const timestamp = new Date().toISOString();
  const auditEntry = {
    timestamp,
    operation,
    organizationId,
    userId,
    metadata,
  };

  // In production, this would write to an audit log
  console.log(`[OrganizationAudit] ${timestamp} - ${operation}:`, auditEntry);
}

/**
 * Type guard for organization ID validation
 */
export function isValidOrganizationId(value: unknown): value is string {
  return validateOrganizationId(value).isValid;
}

/**
 * Helper to safely extract organization ID from request objects
 */
export function extractOrganizationId(
  request: any,
  fieldName = 'organizationId'
): string {
  const id = request?.[fieldName];
  return requireValidOrganizationId(id, `request.${fieldName}`);
}

// Export types for external use
export type { OrganizationValidationResult };
