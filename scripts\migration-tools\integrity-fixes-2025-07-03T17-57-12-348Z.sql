-- Fix: 1 invalid UUIDs found in organizations.external_id
-- WARNING: This will replace invalid UUIDs with new ones
-- Review the affected records first:
SELECT external_id FROM organizations WHERE external_id IS NOT NULL;

-- Generate new UUIDs for invalid entries:
UPDATE organizations 
SET external_id = lower(hex(randomblob(4))) || '-' || 
                     lower(hex(randomblob(2))) || '-' || 
                     '4' || substr(lower(hex(randomblob(2))), 2) || '-' || 
                     substr('89ab', abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))), 2) || '-' || 
                     lower(hex(randomblob(6)))
WHERE external_id IS NOT NULL 
AND length(external_id) != 36;

-- Fix: 26 records in products reference non-existent organizations
-- Option 1: Find the correct organization_id and update
-- First, identify which organization these records should belong to:
SELECT DISTINCT organization_id, COUNT(*) as count 
FROM products 
WHERE organization_id NOT IN (SELECT id FROM organizations)
GROUP BY organization_id;

-- Option 2: If these records should belong to the default organization (id=1):
-- UPDATE products SET organization_id = 1 
-- WHERE organization_id NOT IN (SELECT id FROM organizations);

-- Option 3: Delete orphaned records (use with caution):
-- DELETE FROM products 
-- WHERE organization_id NOT IN (SELECT id FROM organizations);

-- Fix: 456 records in colors reference non-existent organizations
-- Option 1: Find the correct organization_id and update
-- First, identify which organization these records should belong to:
SELECT DISTINCT organization_id, COUNT(*) as count 
FROM colors 
WHERE organization_id NOT IN (SELECT id FROM organizations)
GROUP BY organization_id;

-- Option 2: If these records should belong to the default organization (id=1):
-- UPDATE colors SET organization_id = 1 
-- WHERE organization_id NOT IN (SELECT id FROM organizations);

-- Option 3: Delete orphaned records (use with caution):
-- DELETE FROM colors 
-- WHERE organization_id NOT IN (SELECT id FROM organizations);

-- Fix: 529 records in product_colors reference non-existent organizations
-- Option 1: Find the correct organization_id and update
-- First, identify which organization these records should belong to:
SELECT DISTINCT organization_id, COUNT(*) as count 
FROM product_colors 
WHERE organization_id NOT IN (SELECT id FROM organizations)
GROUP BY organization_id;

-- Option 2: If these records should belong to the default organization (id=1):
-- UPDATE product_colors SET organization_id = 1 
-- WHERE organization_id NOT IN (SELECT id FROM organizations);

-- Option 3: Delete orphaned records (use with caution):
-- DELETE FROM product_colors 
-- WHERE organization_id NOT IN (SELECT id FROM organizations);