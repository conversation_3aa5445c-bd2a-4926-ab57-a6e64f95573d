-- Migration: Add missing columns for Supabase sync
-- This migration adds columns required by the sync service

-- Note: In SQLite, ALTER TABLE ADD COLUMN will fail if the column already exists
-- The migration runner will handle these errors gracefully

-- Add sync columns to colors table
ALTER TABLE colors ADD COLUMN user_id TEXT;
ALTER TABLE colors ADD COLUMN deleted_at TEXT;
ALTER TABLE colors ADD COLUMN device_id TEXT;

-- Add sync columns to products table  
ALTER TABLE products ADD COLUMN user_id TEXT;
ALTER TABLE products ADD COLUMN metadata JSON DEFAULT '{}';
ALTER TABLE products ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT TRUE;

-- Add name column to users table
ALTER TABLE users ADD COLUMN name TEXT;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_colors_user_id ON colors(user_id);
CREATE INDEX IF NOT EXISTS idx_colors_deleted_at ON colors(deleted_at);
CREATE INDEX IF NOT EXISTS idx_products_user_id ON products(user_id);
CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);