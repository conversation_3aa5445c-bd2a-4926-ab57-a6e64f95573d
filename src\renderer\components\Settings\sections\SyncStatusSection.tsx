/**
 * @file SyncStatusSection.tsx
 * @description Sync status display section component
 */

import React from 'react';
import { CheckCircle, AlertCircle, Clock, Wifi, WifiOff } from 'lucide-react';
import { useSyncStore } from '../../../store/sync.store';

/**
 * Sync status section component
 */
export const SyncStatusSection: React.FC = () => {
  const {
    isOnline,
    authState,
    lastSyncTime,
    status: syncStatus,
    pendingChanges,
    error,
  } = useSyncStore();

  const getStatusIcon = () => {
    if (!isOnline) {
      return <WifiOff size={20} className='text-ui-foreground-secondary' />;
    }
    if (!authState.isAuthenticated) {
      return <AlertCircle size={20} className='text-feedback-warning' />;
    }
    if (error) {
      return <AlertCircle size={20} className='text-feedback-error' />;
    }
    if (syncStatus === 'syncing') {
      return <Clock size={20} className='text-brand-primary animate-spin' />;
    }
    return <CheckCircle size={20} className='text-feedback-success' />;
  };

  const getStatusText = () => {
    if (!isOnline) {
      return 'Offline';
    }
    if (!authState.isAuthenticated) {
      return 'Not signed in';
    }
    if (error) {
      return 'Sync error';
    }
    if (syncStatus === 'syncing') {
      return 'Syncing...';
    }
    return 'Synced';
  };

  const getStatusColor = () => {
    if (!isOnline || !authState.isAuthenticated) {
      return 'text-ui-foreground-secondary dark:text-gray-400';
    }
    if (error) {
      return 'text-feedback-error dark:text-red-400';
    }
    if (syncStatus === 'syncing') {
      return 'text-brand-primary dark:text-blue-400';
    }
    return 'text-feedback-success dark:text-green-400';
  };

  return (
    <section>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        Sync Status
      </h3>

      <div className='space-y-4'>
        {/* Current Status */}
        <div className='bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4'>
          <div className='flex items-center justify-between mb-3'>
            <div className='flex items-center'>
              {getStatusIcon()}
              <span className={`ml-2 font-medium ${getStatusColor()}`}>
                {getStatusText()}
              </span>
            </div>
            <div className='flex items-center text-ui-foreground-secondary dark:text-gray-400'>
              {isOnline ? <Wifi size={16} /> : <WifiOff size={16} />}
            </div>
          </div>

          {/* Status Details */}
          <div className='space-y-2 text-sm'>
            <div className='flex justify-between'>
              <span className='text-ui-foreground-secondary dark:text-gray-400'>
                Connection:
              </span>
              <span
                className={
                  isOnline ? 'text-feedback-success' : 'text-feedback-error'
                }
              >
                {isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-ui-foreground-secondary dark:text-gray-400'>
                Authentication:
              </span>
              <span
                className={
                  authState.isAuthenticated
                    ? 'text-feedback-success'
                    : 'text-feedback-warning'
                }
              >
                {authState.isAuthenticated ? 'Signed in' : 'Not signed in'}
              </span>
            </div>
            {lastSyncTime && (
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Last sync:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {new Date(lastSyncTime).toLocaleString()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Pending Changes */}
        {pendingChanges > 0 && (
          <div className='bg-brand-primary/10 dark:bg-blue-900/20 border border-brand-primary dark:border-blue-600 rounded-[var(--radius-lg)] p-4'>
            <div className='flex items-center'>
              <Clock
                size={16}
                className='text-brand-primary dark:text-blue-400 mr-2'
              />
              <span className='text-ui-foreground-primary dark:text-white font-medium'>
                {pendingChanges} change{pendingChanges !== 1 ? 's' : ''} pending
                sync
              </span>
            </div>
            <p className='text-sm text-ui-foreground-secondary dark:text-gray-400 mt-1'>
              These changes will sync automatically when you're online and
              signed in.
            </p>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className='bg-feedback-error/10 dark:bg-red-900/20 border border-feedback-error dark:border-red-600 rounded-[var(--radius-lg)] p-4'>
            <div className='flex items-start'>
              <AlertCircle
                size={16}
                className='text-feedback-error dark:text-red-400 mr-2 mt-0.5 flex-shrink-0'
              />
              <div>
                <span className='text-ui-foreground-primary dark:text-white font-medium block'>
                  Sync Error
                </span>
                <p className='text-sm text-ui-foreground-secondary dark:text-gray-400 mt-1'>
                  {error || 'An unknown error occurred during sync.'}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default SyncStatusSection;
