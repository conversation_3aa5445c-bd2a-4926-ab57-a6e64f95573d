import { useState, useEffect, useRef, useMemo } from 'react';
import { ColorSpace3DChannels } from '../shared/constants/channels';
import { ErrorBoundary } from './components/ErrorBoundary';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { hexToRgb, rgbToLab } from '../shared/utils/color/conversion';
import {
  Info,
  Download,
  RotateCw,
  ZoomIn,
  ZoomOut,
  RotateCcw,
} from 'lucide-react';
import './styles/index.css';

interface ColorData {
  id: string;
  hex: string;
  name: string;
}

type ColorSpaceType = 'lab' | 'rgb' | 'hsv';

// Specialized component for pop-out window - no scrolling, everything in view
function ColorSpace3DPopout({ colors }: { colors: ColorData[] }) {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const animationIdRef = useRef<number | null>(null);

  const [colorSpace, setColorSpace] = useState<ColorSpaceType>('lab');
  const [showAxes, setShowAxes] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [pointSize, setPointSize] = useState(5);
  const [autoRotate, setAutoRotate] = useState(false);
  const [rotationSpeed, setRotationSpeed] = useState(1);
  const [zoom, setZoom] = useState(100);
  const [opacity, setOpacity] = useState(1);

  // Limit colors for performance
  const displayColors = useMemo(() => {
    if (colors.length > 500) {
      console.warn(
        `Limiting 3D visualization to first 500 colors for performance (${colors.length} selected)`
      );
      return colors.slice(0, 500);
    }
    return colors;
  }, [colors]);

  useEffect(() => {
    if (!mountRef.current) {
      return;
    }

    try {
      // Check WebGL support
      const canvas = document.createElement('canvas');
      const gl =
        canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        console.error('WebGL not supported');
        throw new Error('WebGL not supported');
      }

      // Scene setup
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0xf5f5f5);
      sceneRef.current = scene;

      // Camera
      const camera = new THREE.PerspectiveCamera(
        50,
        mountRef.current.clientWidth / mountRef.current.clientHeight,
        0.1,
        1000
      );
      camera.position.set(150, 150, 150);

      // Renderer with error handling
      let renderer: THREE.WebGLRenderer;
      try {
        renderer = new THREE.WebGLRenderer({
          antialias: true,
          preserveDrawingBuffer: true,
          powerPreference: 'default',
        });
      } catch (error) {
        console.error('Failed to create WebGL renderer:', error);
        throw new Error('WebGL renderer creation failed');
      }

      renderer.setSize(
        mountRef.current.clientWidth,
        mountRef.current.clientHeight
      );
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

      if (mountRef.current) {
        mountRef.current.appendChild(renderer.domElement);
      }
      rendererRef.current = renderer;

      // Controls
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;
      controls.rotateSpeed = 0.5;
      controls.autoRotate = autoRotate;
      controls.autoRotateSpeed = rotationSpeed;
      controlsRef.current = controls;

      // Lighting
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
      scene.add(ambientLight);
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4);
      directionalLight.position.set(100, 100, 50);
      scene.add(directionalLight);

      // Add axes helper if enabled
      if (showAxes) {
        const axesHelper = new THREE.AxesHelper(100);
        scene.add(axesHelper);
      }

      // Add grid
      if (showGrid) {
        const gridHelper = new THREE.GridHelper(200, 20, 0x888888, 0xcccccc);
        scene.add(gridHelper);
      }

      // Add color points
      const geometry = new THREE.SphereGeometry(pointSize / 10, 16, 16);
      const colorGroup = new THREE.Group();

      displayColors.forEach(color => {
        const rgb = hexToRgb(color.hex);
        if (!rgb) {
          return;
        }

        let position: THREE.Vector3;

        if (colorSpace === 'lab') {
          const lab = rgbToLab(rgb);
          position = new THREE.Vector3(lab.a, lab.l - 50, lab.b);
        } else if (colorSpace === 'rgb') {
          position = new THREE.Vector3(
            (rgb.r - 128) * 0.5,
            (rgb.g - 128) * 0.5,
            (rgb.b - 128) * 0.5
          );
        } else {
          const { h, s, v } = rgbToHsv(rgb);
          const radius = s * 50;
          const angle = (h / 360) * Math.PI * 2;
          position = new THREE.Vector3(
            radius * Math.cos(angle),
            v * 100 - 50,
            radius * Math.sin(angle)
          );
        }

        const material = new THREE.MeshPhongMaterial({
          color: color.hex,
          emissive: color.hex,
          emissiveIntensity: 0.2,
          opacity,
          transparent: opacity < 1,
        });
        const sphere = new THREE.Mesh(geometry, material);
        sphere.position.copy(position);
        sphere.userData = { color };
        colorGroup.add(sphere);
      });

      scene.add(colorGroup);

      // Handle resize
      const handleResize = () => {
        if (!mountRef.current) {
          return;
        }
        camera.aspect =
          mountRef.current.clientWidth / mountRef.current.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(
          mountRef.current.clientWidth,
          mountRef.current.clientHeight
        );
      };
      window.addEventListener('resize', handleResize);

      // Animation loop
      const animate = () => {
        animationIdRef.current = requestAnimationFrame(animate);
        controls.update();
        renderer.render(scene, camera);
      };
      animate();

      // Cleanup
      return () => {
        if (animationIdRef.current) {
          cancelAnimationFrame(animationIdRef.current);
        }
        window.removeEventListener('resize', handleResize);
        if (mountRef.current && renderer.domElement) {
          try {
            mountRef.current.removeChild(renderer.domElement);
          } catch (error) {
            console.warn('Error removing renderer canvas:', error);
          }
        }
        geometry.dispose();
        renderer.dispose();
        controls.dispose();
      };
    } catch (error) {
      console.error('3D Color Space initialization error:', error);

      if (mountRef.current) {
        mountRef.current.innerHTML = `
          <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; color: #6b7280; font-family: system-ui;">
            <div style="text-align: center;">
              <div style="font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
              <div>WebGL not supported or failed to initialize</div>
              <div style="font-size: 0.875rem; margin-top: 0.5rem;">Try updating your graphics drivers</div>
            </div>
          </div>
        `;
      }

      return () => {};
    }
  }, [
    displayColors,
    colorSpace,
    showAxes,
    showGrid,
    pointSize,
    autoRotate,
    rotationSpeed,
    opacity,
  ]);

  // Update zoom when it changes
  useEffect(() => {
    if (controlsRef.current && controlsRef.current.object) {
      const camera = controlsRef.current.object as THREE.PerspectiveCamera;
      const distance = 150 * (100 / zoom);
      camera.position.setLength(distance);
      controlsRef.current.update();
    }
  }, [zoom]);

  const handleReset = () => {
    if (controlsRef.current) {
      controlsRef.current.reset();
      setZoom(100);
      setRotationSpeed(1);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 10, 200));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 10, 50));
  };

  const handleExport = () => {
    if (rendererRef.current && sceneRef.current) {
      const camera = controlsRef.current?.object as THREE.Camera;
      if (camera) {
        rendererRef.current.render(sceneRef.current, camera);
        const dataURL = rendererRef.current.domElement.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = `color-space-${colorSpace}-${Date.now()}.png`;
        link.href = dataURL;
        link.click();
      }
    }
  };

  return (
    <div
      className='h-full flex flex-col overflow-hidden bg-ui-background-primary'
      style={{ height: '100vh', maxHeight: '100vh' }}
    >
      {/* Header */}
      <div
        className='flex-shrink-0 flex items-center border-b border-ui-border'
        style={{
          minHeight: '60px',
          maxHeight: '60px',
          paddingLeft: window.navigator.userAgent.includes('Mac')
            ? '80px'
            : '12px', // Extra padding for macOS traffic lights
          paddingRight: '12px',
          paddingTop: '8px',
          paddingBottom: '8px',
        }}
      >
        {/* Left side - Title and color space selector */}
        <div className='flex items-center gap-3 flex-1'>
          <h3 className='text-base font-medium'>3D Color Space</h3>
          <select
            value={colorSpace}
            onChange={e => setColorSpace(e.target.value as ColorSpaceType)}
            className='px-2 py-1 text-xs border border-ui-border-light rounded bg-ui-background-primary'
          >
            <option value='lab'>LAB</option>
            <option value='rgb'>RGB</option>
            <option value='hsv'>HSV</option>
          </select>
        </div>

        {/* Right side - Zoom and other controls */}
        <div className='flex items-center gap-2 flex-shrink-0'>
          <div className='flex items-center gap-1 border-r border-ui-border-light pr-2'>
            <button
              onClick={handleZoomOut}
              className='p-1 hover:bg-ui-background-secondary rounded transition-colors'
              title='Zoom out'
            >
              <ZoomOut className='w-3 h-3' />
            </button>
            <span className='text-xs font-mono w-10 text-center'>{zoom}%</span>
            <button
              onClick={handleZoomIn}
              className='p-1 hover:bg-ui-background-secondary rounded transition-colors'
              title='Zoom in'
            >
              <ZoomIn className='w-3 h-3' />
            </button>
          </div>

          <button
            onClick={handleReset}
            className='p-1 hover:bg-ui-background-secondary rounded transition-colors'
            title='Reset view'
          >
            <RotateCw className='w-3 h-3' />
          </button>

          <button
            onClick={handleExport}
            className='p-1 hover:bg-ui-background-secondary rounded transition-colors'
            title='Export as image'
          >
            <Download className='w-3 h-3' />
          </button>
        </div>
      </div>

      {/* 3D Canvas - takes remaining space */}
      <div className='flex-1 relative overflow-hidden' style={{ minHeight: 0 }}>
        {colors.length > 500 && (
          <div className='absolute top-1 left-1 right-1 z-10 flex items-center gap-1 p-1 bg-yellow-50 border border-yellow-200 rounded text-xs'>
            <Info className='w-3 h-3 text-yellow-600 flex-shrink-0' />
            <p className='text-yellow-800 truncate'>
              Showing first 500 colors for performance
            </p>
          </div>
        )}

        <div
          ref={mountRef}
          className='w-full h-full'
          style={{ width: '100%', height: '100%' }}
        />
      </div>

      {/* Ultra Compact Controls Panel */}
      <div
        className='flex-shrink-0 border-t border-ui-border bg-ui-background-secondary px-2 py-1'
        style={{ minHeight: '40px', maxHeight: '40px' }}
      >
        <div className='flex items-center justify-between text-xs'>
          <div className='flex items-center gap-2'>
            <label className='flex items-center gap-1'>
              Size
              <input
                type='range'
                min='3'
                max='15'
                value={pointSize}
                onChange={e => setPointSize(Number(e.target.value))}
                className='w-12'
                style={{ height: '16px' }}
              />
              <span className='font-mono w-4'>{pointSize}</span>
            </label>
          </div>

          <div className='flex items-center gap-2'>
            <label className='flex items-center gap-1'>
              Opacity
              <input
                type='range'
                min='0.1'
                max='1'
                step='0.1'
                value={opacity}
                onChange={e => setOpacity(Number(e.target.value))}
                className='w-12'
                style={{ height: '16px' }}
              />
              <span className='font-mono w-6'>
                {(opacity * 100).toFixed(0)}%
              </span>
            </label>
          </div>

          <div className='flex items-center gap-1'>
            <button
              onClick={() => setAutoRotate(!autoRotate)}
              className={`p-0.5 rounded transition-colors ${
                autoRotate
                  ? 'bg-brand-primary text-white'
                  : 'bg-ui-background-tertiary'
              }`}
              title='Auto Rotate'
            >
              <RotateCcw className='w-3 h-3' />
            </button>

            <label className='flex items-center gap-0.5'>
              <input
                type='checkbox'
                checked={showAxes}
                onChange={e => setShowAxes(e.target.checked)}
                className='w-3 h-3'
              />
              <span>Axes</span>
            </label>

            <label className='flex items-center gap-0.5'>
              <input
                type='checkbox'
                checked={showGrid}
                onChange={e => setShowGrid(e.target.checked)}
                className='w-3 h-3'
              />
              <span>Grid</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}

// Helper function to convert RGB to HSV
function rgbToHsv(rgb: { r: number; g: number; b: number }) {
  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const delta = max - min;

  let h = 0;
  if (delta !== 0) {
    if (max === r) {
      h = ((g - b) / delta) % 6;
    } else if (max === g) {
      h = (b - r) / delta + 2;
    } else {
      h = (r - g) / delta + 4;
    }
    h = h * 60;
    if (h < 0) {
      h += 360;
    }
  }

  const s = max === 0 ? 0 : delta / max;
  const v = max;

  return { h, s, v };
}

export function ColorSpace3DWindow() {
  const [colors, setColors] = useState<ColorData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Listen for color updates from main window
    const handleColorUpdate = (newColors: ColorData[]) => {
      console.log('3D Window: Received color update', newColors.length);
      setColors(newColors);
      setIsLoading(false);
    };

    // Set up IPC listener for color updates using direct electron API
    let cleanup: (() => void) | undefined;
    try {
      // Try the preload IPC method first
      if ((window as any).ipc?.on) {
        console.log(
          '3D Window: Setting up IPC listener via preload for channel:',
          ColorSpace3DChannels.UPDATE_COLORS
        );
        cleanup = (window as any).ipc.on(
          ColorSpace3DChannels.UPDATE_COLORS,
          handleColorUpdate
        );
        console.log('3D Window: Preload IPC listener set up successfully');
      }
      // Try direct electron ipcRenderer if available (for testing)
      else if ((window as any).electron?.ipcRenderer) {
        console.log(
          '3D Window: Setting up direct IPC listener for channel:',
          ColorSpace3DChannels.UPDATE_COLORS
        );
        const { ipcRenderer } = (window as any).electron;
        const listener = (_: any, newColors: ColorData[]) =>
          handleColorUpdate(newColors);
        ipcRenderer.on(ColorSpace3DChannels.UPDATE_COLORS, listener);
        cleanup = () =>
          ipcRenderer.removeListener(
            ColorSpace3DChannels.UPDATE_COLORS,
            listener
          );
        console.log('3D Window: Direct IPC listener set up successfully');
      } else {
        console.warn(
          '3D Window: No IPC method available, window may not receive updates'
        );
      }
    } catch (error) {
      console.error('3D Window: Error setting up IPC listener:', error);
    }

    // Use a timeout to simulate loading and then show empty state if no colors
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    // Cleanup
    return () => {
      clearTimeout(timer);
      if (cleanup) {
        cleanup();
      }
    };
  }, []);

  if (isLoading) {
    return (
      <div className='flex items-center justify-center h-screen bg-ui-background-primary'>
        <div className='flex items-center space-x-3'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary' />
          <span className='text-ui-foreground-secondary'>
            Loading 3D visualization...
          </span>
        </div>
      </div>
    );
  }

  if (colors.length === 0) {
    return (
      <div className='flex items-center justify-center h-screen bg-ui-background-primary'>
        <div className='text-center space-y-4'>
          <div className='text-6xl'>🎨</div>
          <h2 className='text-xl font-semibold text-ui-foreground-primary'>
            No Colors Selected
          </h2>
          <p className='text-ui-foreground-secondary max-w-md'>
            Select colors in the main ChromaSync window to visualize them in 3D
            space.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      className='h-screen w-full bg-ui-background-primary overflow-hidden'
      style={{
        height: '100vh',
        width: '100vw',
        maxHeight: '100vh',
        maxWidth: '100vw',
        position: 'fixed',
        top: 0,
        left: 0,
        overflow: 'hidden',
      }}
    >
      <ErrorBoundary
        fallback={
          <div className='flex items-center justify-center h-screen bg-ui-background-primary'>
            <div className='text-center space-y-4'>
              <div className='text-6xl'>⚠️</div>
              <h2 className='text-xl font-semibold text-ui-foreground-primary'>
                3D Visualization Error
              </h2>
              <p className='text-ui-foreground-secondary max-w-md'>
                There was an error loading the 3D visualization. Please try
                closing and reopening the window.
              </p>
            </div>
          </div>
        }
      >
        <div
          className='h-full w-full overflow-hidden'
          style={{
            height: '100vh',
            width: '100vw',
            maxHeight: '100vh',
            maxWidth: '100vw',
            overflow: 'hidden',
          }}
        >
          <ColorSpace3DPopout colors={colors} />
        </div>
      </ErrorBoundary>
    </div>
  );
}

// Export the component for use in App.tsx - no standalone rendering needed
