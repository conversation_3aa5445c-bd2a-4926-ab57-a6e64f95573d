/**
 * @file DiffViewer.tsx
 * @description Component for displaying differences between two versions of data
 */

import React, { useMemo } from 'react';
import { ChevronRight, ChevronDown, AlertCircle, Plus, Minus } from 'lucide-react';

interface DiffField {
  field: string;
  localValue: any;
  remoteValue: any;
  hasConflict: boolean;
  type: 'text' | 'number' | 'object' | 'array';
}

interface DiffViewerProps {
  localData: Record<string, any>;
  remoteData: Record<string, any>;
  title?: string;
  className?: string;
  onFieldSelect?: (field: string, value: 'local' | 'remote') => void;
  selectedFields?: Record<string, 'local' | 'remote'>;
  compact?: boolean;
}

export const DiffViewer: React.FC<DiffViewerProps> = ({
  localData,
  remoteData,
  title,
  className = '',
  onFieldSelect,
  selectedFields = {},
  compact = false
}) => {
  const [expandedSections, setExpandedSections] = React.useState<Set<string>>(new Set(['conflicts']));

  const diffFields = useMemo(() => {
    const fields: DiffField[] = [];
    const allKeys = new Set([...Object.keys(localData), ...Object.keys(remoteData)]);

    allKeys.forEach(key => {
      const localValue = localData[key];
      const remoteValue = remoteData[key];
      const hasConflict = JSON.stringify(localValue) !== JSON.stringify(remoteValue);
      
      let type: DiffField['type'] = 'text';
      if (typeof localValue === 'number' || typeof remoteValue === 'number') {
        type = 'number';
      } else if (Array.isArray(localValue) || Array.isArray(remoteValue)) {
        type = 'array';
      } else if (typeof localValue === 'object' || typeof remoteValue === 'object') {
        type = 'object';
      }

      fields.push({
        field: key,
        localValue,
        remoteValue,
        hasConflict,
        type
      });
    });

    return fields.sort((a, b) => {
      // Sort conflicts first, then alphabetically
      if (a.hasConflict && !b.hasConflict) return -1;
      if (!a.hasConflict && b.hasConflict) return 1;
      return a.field.localeCompare(b.field);
    });
  }, [localData, remoteData]);

  const conflictCount = diffFields.filter(f => f.hasConflict).length;
  const unchangedCount = diffFields.filter(f => !f.hasConflict).length;

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) {
      return 'null';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const getValueClassName = (isLocal: boolean, hasConflict: boolean, isSelected?: boolean) => {
    const baseClasses = 'p-3 rounded-md border text-sm font-mono whitespace-pre-wrap break-words';
    
    if (!hasConflict) {
      return `${baseClasses} bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300`;
    }

    if (isSelected) {
      return `${baseClasses} ${isLocal 
        ? 'bg-blue-50 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600 text-blue-900 dark:text-blue-200' 
        : 'bg-green-50 dark:bg-green-900/30 border-green-300 dark:border-green-600 text-green-900 dark:text-green-200'
      }`;
    }

    return `${baseClasses} ${isLocal 
      ? 'bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-700 text-red-900 dark:text-red-200' 
      : 'bg-yellow-50 dark:bg-yellow-900/30 border-yellow-200 dark:border-yellow-700 text-yellow-900 dark:text-yellow-200'
    } cursor-pointer hover:opacity-80 transition-opacity`;
  };

  const renderField = (field: DiffField) => {
    const isSelected = selectedFields[field.field];
    const localSelected = isSelected === 'local';
    const remoteSelected = isSelected === 'remote';

    return (
      <div key={field.field} className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {field.field}
            </span>
            {field.hasConflict && (
              <span title="Field has conflicts">
                <AlertCircle className="w-4 h-4 text-red-500" />
              </span>
            )}
            <span className="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              {field.type}
            </span>
          </div>
          {field.hasConflict && onFieldSelect && (
            <div className="flex space-x-2 text-xs">
              <button
                onClick={() => onFieldSelect(field.field, 'local')}
                className={`px-2 py-1 rounded transition-colors ${
                  localSelected
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                Use Local
              </button>
              <button
                onClick={() => onFieldSelect(field.field, 'remote')}
                className={`px-2 py-1 rounded transition-colors ${
                  remoteSelected
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                Use Remote
              </button>
            </div>
          )}
        </div>

        <div className={`grid ${compact ? 'grid-cols-1 space-y-2' : 'grid-cols-2 gap-4'}`}>
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Minus className="w-4 h-4 text-red-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Local Version
              </span>
            </div>
            <div
              className={getValueClassName(true, field.hasConflict, localSelected)}
              onClick={field.hasConflict && onFieldSelect ? () => onFieldSelect(field.field, 'local') : undefined}
            >
              {formatValue(field.localValue)}
            </div>
          </div>

          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Plus className="w-4 h-4 text-green-500" />
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                Remote Version
              </span>
            </div>
            <div
              className={getValueClassName(false, field.hasConflict, remoteSelected)}
              onClick={field.hasConflict && onFieldSelect ? () => onFieldSelect(field.field, 'remote') : undefined}
            >
              {formatValue(field.remoteValue)}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const conflictingFields = diffFields.filter(f => f.hasConflict);
  const unchangedFields = diffFields.filter(f => !f.hasConflict);

  return (
    <div className={`bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {title && (
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">{title}</h3>
        </div>
      )}

      <div className="p-6 space-y-6">
        {/* Summary */}
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center space-x-4 text-sm">
            <span className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="font-medium">{conflictCount} conflicts</span>
            </span>
            <span className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
              <span>{unchangedCount} unchanged</span>
            </span>
          </div>
        </div>

        {/* Conflicting Fields */}
        {conflictCount > 0 && (
          <div>
            <button
              onClick={() => toggleSection('conflicts')}
              className="flex items-center space-x-2 w-full text-left p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors"
            >
              {expandedSections.has('conflicts') ? (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-500" />
              )}
              <span className="font-medium text-red-600 dark:text-red-400">
                Conflicting Fields ({conflictCount})
              </span>
            </button>

            {expandedSections.has('conflicts') && (
              <div className="mt-4 space-y-6 pl-6">
                {conflictingFields.map(renderField)}
              </div>
            )}
          </div>
        )}

        {/* Unchanged Fields */}
        {unchangedCount > 0 && (
          <div>
            <button
              onClick={() => toggleSection('unchanged')}
              className="flex items-center space-x-2 w-full text-left p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors"
            >
              {expandedSections.has('unchanged') ? (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-500" />
              )}
              <span className="font-medium text-gray-600 dark:text-gray-400">
                Unchanged Fields ({unchangedCount})
              </span>
            </button>

            {expandedSections.has('unchanged') && (
              <div className="mt-4 space-y-6 pl-6">
                {unchangedFields.map(renderField)}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};