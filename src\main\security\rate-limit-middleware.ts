/**
 * @file rate-limit-middleware.ts
 * @description Rate limiting middleware for IPC endpoints
 * 
 * Provides middleware functions to apply rate limiting to IPC handlers
 * with different configurations based on endpoint sensitivity.
 */

import { IpcMainEvent } from 'electron';
import { rateLimiter, RateLimitConfig, RateLimitPresets, IPCChannelCategories } from './rate-limiter';

/**
 * Rate limit error class
 */
export class RateLimitError extends Error {
  public readonly code = 'RATE_LIMIT_EXCEEDED';
  public readonly resetTime: number;
  public readonly remaining: number;

  constructor(message: string, resetTime: number, remaining: number) {
    super(message);
    this.name = 'RateLimitError';
    this.resetTime = resetTime;
    this.remaining = remaining;
  }
}

/**
 * Rate limit middleware options
 */
export interface RateLimitMiddlewareOptions extends Partial<RateLimitConfig> {
  /** Custom error handler */
  onError?: (error: RateLimitError, channel: string, event: IpcMainEvent, args: any[]) => void;
  /** Whether to log rate limit violations */
  logViolations?: boolean;
}

/**
 * Create rate limiting middleware for IPC handlers
 */
export function createRateLimitMiddleware(
  config?: Partial<RateLimitConfig>,
  options?: RateLimitMiddlewareOptions
): (channel: string, handler: IPCHandler) => IPCHandler {
  const { onError, logViolations = true, ...rateLimitConfig } = options || {};

  return function rateLimitMiddleware(
    channel: string,
    handler: IPCHandler
  ): IPCHandler {
    return async (event: any, ...args: any[]): Promise<any> => {
      try {
        // Check rate limit
        const result = rateLimiter.checkLimit(channel, args, {
          ...rateLimitConfig,
          ...config
        });

        if (!result.allowed) {
          const error = new RateLimitError(
            result.message || 'Rate limit exceeded',
            result.resetTime,
            result.remaining
          );

          if (logViolations) {
            console.warn(`[RateLimit] ${channel} rate limit exceeded:`, {
              remaining: result.remaining,
              resetTime: new Date(result.resetTime).toISOString(),
              args: args.length
            });
          }

          if (onError) {
            onError(error, channel, event, args);
            return;
          }

          // Return error response
          return {
            success: false,
            error: {
              code: error.code,
              message: error.message,
              resetTime: error.resetTime,
              remaining: error.remaining
            }
          };
        }

        // Add rate limit headers to response if handler returns an object
        const response = await handler(event, ...args);
        
        if (response && typeof response === 'object' && !Array.isArray(response)) {
          response._rateLimitInfo = {
            remaining: result.remaining,
            resetTime: result.resetTime
          };
        }

        return response;

      } catch (error) {
        // Re-throw non-rate-limit errors
        if (error instanceof RateLimitError) {
          throw error;
        }

        // Handle other errors normally
        throw error;
      }
    };
  };
}

// Type for IPC handler functions
type IPCHandler = (event: any, ...args: any[]) => any;

/**
 * Apply rate limiting to a handler based on channel category
 */
export function applyChannelRateLimit(channel: string): (handler: IPCHandler) => IPCHandler {
  // Determine rate limit config based on channel
  let config: Partial<RateLimitConfig>;
  
  if (IPCChannelCategories.AUTH.includes(channel as any) || 
      IPCChannelCategories.ORGANIZATION.includes(channel as any)) {
    config = RateLimitPresets.VERY_SENSITIVE;
  } else if (IPCChannelCategories.SENSITIVE_DATA.includes(channel as any)) {
    config = RateLimitPresets.SENSITIVE;
  } else if (IPCChannelCategories.FILE_OPS.includes(channel as any)) {
    config = RateLimitPresets.FILE_OPERATIONS;
  } else if (IPCChannelCategories.HIGH_FREQ.includes(channel as any)) {
    config = RateLimitPresets.HIGH_FREQUENCY;
  } else if (IPCChannelCategories.NORMAL.includes(channel as any)) {
    config = RateLimitPresets.NORMAL;
  } else {
    // Default to normal for unrecognized channels
    config = RateLimitPresets.NORMAL;
  }

  const middleware = createRateLimitMiddleware(config, {
    logViolations: true,
    keyGenerator: (ch, _args) => {
      // Generate more specific keys for sensitive operations
      if (IPCChannelCategories.AUTH.includes(ch as any) || 
          IPCChannelCategories.ORGANIZATION.includes(ch as any)) {
        // For auth operations, use a more restrictive global key
        return `sensitive:${ch}`;
      }
      return `channel:${ch}`;
    },
    onLimitReached: (ch, key) => {
      console.warn(`[Security] Rate limit exceeded for sensitive operation: ${ch} (${key})`);
      
      // Could trigger additional security measures here
      // e.g., temporary IP blocking, user account flagging, etc.
    }
  });
  
  return (handler: Function) => middleware(channel, handler as (event: IpcMainEvent, ...args: any[]) => any);
}

/**
 * Rate limiting decorator for IPC handlers
 */
export function RateLimit(config?: Partial<RateLimitConfig>) {
  return function(_target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const middleware = createRateLimitMiddleware(config);
    
    descriptor.value = function(this: any, event: IpcMainEvent, ...args: any[]) {
      const wrappedHandler = middleware(propertyKey, originalMethod.bind(this));
      return wrappedHandler(event, ...args);
    };

    return descriptor;
  };
}

/**
 * Specialized decorators for different sensitivity levels
 */
export const RateLimitDecorators = {
  /**
   * High frequency operations (1000 requests/minute)
   */
  HighFrequency: () => RateLimit(RateLimitPresets.HIGH_FREQUENCY),

  /**
   * Normal operations (100 requests/minute)
   */
  Normal: () => RateLimit(RateLimitPresets.NORMAL),

  /**
   * Sensitive operations (20 requests/minute)
   */
  Sensitive: () => RateLimit(RateLimitPresets.SENSITIVE),

  /**
   * Very sensitive operations (5 requests/minute)
   */
  VerySensitive: () => RateLimit(RateLimitPresets.VERY_SENSITIVE),

  /**
   * File operations (50 requests/5 minutes)
   */
  FileOperations: () => RateLimit(RateLimitPresets.FILE_OPERATIONS),

  /**
   * System operations (30 requests/5 minutes)
   */
  System: () => RateLimit(RateLimitPresets.SYSTEM)
};

/**
 * Rate limit status check utility
 */
export function getRateLimitStatus(channel: string, key?: string, config?: Partial<RateLimitConfig>): {
  limited: boolean;
  remaining: number;
  resetTime: number;
  timeToReset: number;
} {
  const statusKey = key || `channel:${channel}`;
  const status = rateLimiter.getStatus(statusKey, config);
  
  if (!status.exists) {
    const maxRequests = config?.maxRequests || 100;
    return {
      limited: false,
      remaining: maxRequests,
      resetTime: Date.now() + 60000,
      timeToReset: 60000
    };
  }

  return {
    limited: (status.remaining || 0) <= 0,
    remaining: status.remaining || 0,
    resetTime: status.resetTime || Date.now() + 60000,
    timeToReset: Math.max(0, (status.resetTime || Date.now()) - Date.now())
  };
}

/**
 * Utility to check if a channel is rate limited
 */
export function isChannelRateLimited(channel: string): boolean {
  const result = rateLimiter.checkLimit(channel, [], { maxRequests: 1 });
  return !result.allowed;
}

/**
 * Bulk rate limit check for multiple channels
 */
export function checkMultipleChannels(channels: string[]): {
  [channel: string]: {
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }
} {
  const results: any = {};
  
  for (const channel of channels) {
    results[channel] = rateLimiter.checkLimit(channel, []);
  }
  
  return results;
}

/**
 * Rate limit monitoring utilities
 */
export const RateLimitMonitor = {
  /**
   * Get all active rate limits
   */
  getActiveLimits() {
    return rateLimiter.getAllLimits();
  },

  /**
   * Get rate limit statistics
   */
  getStats() {
    const limits = rateLimiter.getAllLimits();
    // const _now = Date.now(); // Unused variable
    
    return {
      total: limits.length,
      approaching: limits.filter(l => l.remaining <= 5).length,
      exceeded: limits.filter(l => l.remaining === 0).length,
      expiringSoon: limits.filter(l => l.timeToReset <= 30000).length, // 30 seconds
      averageUsage: limits.length > 0 
        ? limits.reduce((sum, l) => sum + (100 - l.remaining), 0) / limits.length
        : 0
    };
  },

  /**
   * Clear all rate limits (emergency use)
   */
  clearAll() {
    rateLimiter.resetAllLimits();
    console.log('[RateLimit] All rate limits cleared by monitor');
  },

  /**
   * Clear specific rate limit
   */
  clear(key: string) {
    rateLimiter.resetLimit(key);
    console.log(`[RateLimit] Cleared rate limit for: ${key}`);
  }
};