# Claude Memory - ChromaSync Project

## Project Overview
ChromaSync is an Electron application for color and product management with cloud synchronization via Supabase. The application uses a clean architecture with strict process separation between main (Node.js) and renderer (React) processes.

## Recent Critical Fixes Completed (June 2025)

### ✅ MAJOR SUCCESS: Complete Sync Resolution
**Problem**: Supabase sync was returning 0 data initially despite 456 colors and 24 products existing in cloud database.

**Root Cause**: Session coordination failure between authentication and sync services causing Row Level Security (RLS) to block queries.

**Solution Implemented**:
1. **Session Coordination Fix**: Added `ensureAuthenticatedSession()` function in `supabase-client.ts` that validates/refreshes session before all RLS queries
2. **UUID Standardization**: Fixed all organization_id columns to use TEXT (UUID) instead of mixed integer/UUID usage
3. **Product-Color Relationships**: Fixed sync and display of 529 product-color relationships
4. **Clean Architecture**: Removed 500+ lines of duplicate code, consolidated .clean.ts files

**Results**: 
- ✅ 456 colors syncing successfully from Supabase → Local DB
- ✅ 24 products syncing successfully from Supabase → Local DB  
- ✅ 529 product-color relationships syncing and displaying correctly
- ✅ Products UI now shows accurate color counts instead of "0 colours"

## Architecture Standards (CRITICAL - Always Follow)

### Database & Sync Rules
1. **UUID Standard**: Always use UUID strings for organization_id, never integers
2. **Session Authentication**: All Supabase queries MUST use `ensureAuthenticatedSession()` before RLS operations
3. **Sync Sequence**: Auth → Organizations → Colors → Products → Relationships
4. **Database Access**: ONLY in main process, never in renderer
5. **Snake Case SQL**: All database queries must use snake_case naming

### Key Session Coordination Pattern
```typescript
// REQUIRED pattern for all Supabase sync operations:
const { getSupabaseClient, ensureAuthenticatedSession } = await import('../../services/supabase-client');

const { session, error: sessionError } = await ensureAuthenticatedSession();
if (!session || sessionError) {
  console.error(`No authenticated session: ${sessionError}`);
  return []; // Early return if no authentication
}

const supabase = getSupabaseClient();
// Now safe to make RLS queries...
```

### File Structure (Post-Clean Architecture)
- `src/main/db/services/color.service.ts` - Main color service (consolidated)
- `src/main/db/services/product.service.ts` - Main product service (consolidated)  
- `src/main/db/services/organization.service.ts` - Organization management
- `src/main/services/supabase-client.ts` - Enhanced with session coordination
- `src/main/utils/organization-context.ts` - Clean organization context manager
- `src/main/services/auth/oauth-service.ts` - Clean OAuth implementation
- `src/main/services/email/zoho-email.service.ts` - Clean email service

### Deleted Files (No Longer Exist)
- `src/main/db/services/color.service.clean.ts` - Consolidated into main
- `src/main/ipc/color.ipc.clean.ts` - Consolidated into main
- `src/main/utils/organization-context.clean.ts` - Renamed to main
- `src/main/services/auth/oauth-service.refactored.ts` - Renamed to main
- `src/main/services/email/zoho-email.service.refactored.ts` - Renamed to main

## Critical UUID Implementation

### Database Schema (Complete Schema)
ALL organization_id columns are TEXT (UUID):
- colors.organization_id: TEXT 
- products.organization_id: TEXT
- product_colors.organization_id: TEXT
- organization_members.organization_id: TEXT
- organization_invitations.organization_id: TEXT

### Query Patterns (ALWAYS Use UUID)
```sql
-- CORRECT - Use UUID string directly
WHERE organization_id = ? 
-- Called with: organizationId (UUID string)

-- WRONG - Never use integer conversion
WHERE organization_id = ?
-- Called with: localOrg.id (integer)
```

## Sync Infrastructure Status

### Fully Operational Components
- ✅ Authentication & Session Management (ensureAuthenticatedSession)
- ✅ Database Schema & Migrations (TEXT UUID columns)
- ✅ Sync Infrastructure & Sequence (Auth→Orgs→Colors→Products→Relationships) 
- ✅ UUID Standardization (consistent throughout)
- ✅ Organization Context Management
- ✅ Product-Color Relationship Sync & Display
- ✅ Error Handling & Validation

### Performance Optimizations Applied
- ✅ Optimistic updates in color store (addColor)
- ✅ Smart organization switching (sync OR load, not both)
- ✅ Automatic sync trigger when switching organizations  
- ✅ Removed unnecessary data clearing on organization switch

## Environment Configuration

### Required Environment Variables
```bash
# Supabase (Required for sync)
SUPABASE_URL=https://tzqnxhsnitogmtihhsrq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... # For admin operations only

# Zoho Email (Required for invitations)  
ZOHO_CLIENT_ID=1000.ZN7GG1SU13DXYH2D2JXF934HWFVJDG
ZOHO_CLIENT_SECRET=4ca85d5c0a70404f8778128ae3b8e1cc08bc585356
ZOHO_REFRESH_TOKEN=1000.cb0bee690e6822c311c935c9a8409883...
ZOHO_ACCOUNT_ID=6851937000000002002
ZOHO_REGION=EU
ZOHO_SUPPORT_ALIAS=<EMAIL>

# OAuth
AUTH_REDIRECT_URL=https://auth.chromasync.app/callback
```

## Development Commands

### Essential Commands
```bash
npm install          # Install dependencies
npm run dev          # Start dev server with hot reload  
npm run build        # Build for production
npm run package      # Build installer for all platforms

# Testing
npm test             # Run all tests (Vitest)
npm run test:watch   # Watch mode for continuous testing
npm run lint         # Run ESLint
npm run typecheck    # TypeScript type checking

# Database
node scripts/init-database.cjs  # Initialize fresh database
# Local DB: ~/Library/Application Support/chroma-sync/chromasync.db (macOS)
# To reset: rm -f ~/Library/Application\ Support/chroma-sync/chromasync.db*
```

## Common Issues & Solutions

### Sync Issues
1. **"0 data syncing"**: Check `ensureAuthenticatedSession()` is used before all Supabase queries
2. **"Product shows 0 colours"**: Verify UUID usage in product-color relationship queries
3. **"RLS blocking queries"**: Ensure authenticated session exists before making queries
4. **"Organization not found"**: Check UUID format consistency in organization context

### Performance Issues  
1. **Slow startup**: Color libraries load asynchronously, this is expected
2. **Memory usage**: Monitor with large datasets (100k+ colors tested)
3. **Sync performance**: Use prepared statements and batch operations

### Development Issues
1. **Build errors**: Ensure all .clean.ts files are removed, use consolidated versions
2. **Type errors**: Run `npm run typecheck` and fix before committing
3. **Test failures**: Run `npm test` and ensure all pass before commits

## Git Workflow

### Current Branch Structure
- `main` - Production-ready code
- `feature/clean-architecture-migration` - Current active development branch
- All major sync fixes committed to feature branch (commit: b8ed5ef)

### Recent Commits
- `b8ed5ef` - fix: resolve critical Supabase sync and product-color relationship issues
- `a26abc6` - feat: add comprehensive database migration and sync fixes  
- `24e5a17` - fix: prevent duplicate IPC handler registrations for organization channels

## Data Verification

### Current Production Data (Supabase)
- 456 colors in organization: 4047153f-7be8-490b-9cb2-a1e3ed04b92b
- 24 products in same organization
- 529 product-color relationships
- All data syncing successfully to local SQLite database

### Database Verification Commands
```bash
# Check sync status
sqlite3 ~/Library/Application\ Support/chroma-sync/chromasync.db "
SELECT 
  (SELECT COUNT(*) FROM colors) as colors,
  (SELECT COUNT(*) FROM products) as products, 
  (SELECT COUNT(*) FROM product_colors) as relationships;
"

# Check organization UUIDs
sqlite3 ~/Library/Application\ Support/chroma-sync/chromasync.db "
SELECT DISTINCT organization_id, LENGTH(organization_id) FROM colors;
"
```

## Next Development Priorities

### Ready for Implementation
1. **Component-level optimization**: Remove redundant fetching in ProductsPanel
2. **Initialization optimization**: Remove multiple sync calls in AppInitializer  
3. **Store optimization**: Add optimistic updates to product store operations
4. **Centralized loading**: Prevent duplicate data loads across components

### Future Enhancements
1. **Error handling**: Network retry queue, auth expiry prompts
2. **Performance**: Batch database operations, sync debouncing, progress indicators
3. **UI improvements**: Comprehensive IPC→Service→DB flow mapping
4. **Testing**: Offline functionality, auth expiry, network loss scenarios

## Security Notes
- Production builds embed credentials in `app-config.json` - secure deployment required
- OAuth uses PKCE flow for enhanced security
- Tokens stored using hardware-backed encryption (safeStorage)
- Row Level Security (RLS) enforces data isolation in Supabase
- Never expose service role key to renderer process

---
Last Updated: June 18, 2025
Status: All critical sync issues resolved, application fully functional
Next: Performance optimizations and component-level improvements