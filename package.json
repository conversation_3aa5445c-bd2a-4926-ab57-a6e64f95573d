{"name": "chroma-sync", "version": "2.0.0-dev", "description": "ChromaSync: Electron app for color management and synchronization", "main": "out/main/index.js", "author": {"name": "<PERSON>", "email": "micha<PERSON>@chromasync.app"}, "repository": {"type": "git", "url": "https://github.com/chromasync/chromasync.git"}, "homepage": "https://chromasync.app", "bugs": {"url": "https://github.com/chromasync/chromasync/issues", "email": "<EMAIL>"}, "scripts": {"ts": "ts-node-esm", "print-schema": "ts-node-esm scripts/print-schema.ts", "typecheck": "tsc --noEmit", "typecheck:watch": "tsc --noEmit --watch", "start": "npm run build:dev && electron .", "start:quick": "npm run build:dev:quick && electron .", "start:windows": "npm run build:dev:quick && electron .", "dev": "npm run build:dev && electron .", "dev:watch": "node scripts/watch-preload.js & electron-vite --outDir out", "build:dev": "npm run typecheck && electron-vite build --mode development --outDir out", "build:dev:quick": "electron-vite build --mode development --outDir out", "build": "npm run typecheck && electron-vite build --outDir out && node scripts/build-config.cjs", "build:obfuscated": "npm run typecheck && electron-vite build --outDir out && node scripts/build-config.cjs && node scripts/copy-migrations.js && node scripts/obfuscate.js", "postinstall": "electron-builder install-app-deps", "rebuild": "electron-builder install-app-deps", "rebuild:manual": "npx @electron/rebuild", "package": "npm run build:obfuscated && electron-builder", "package:win": "npm run build:obfuscated && electron-builder --win", "package:win:dir": "npm run build:obfuscated && electron-builder --win --dir", "package:win:portable": "npm run build:obfuscated && electron-builder --win portable", "package:win:zip": "npm run build:obfuscated && electron-builder --win zip", "package:win:unsigned": "npm run build:obfuscated && electron-builder --win zip --config electron-builder-unsigned.json", "package:mac": "npm run build:obfuscated && electron-builder --mac", "package:linux": "npm run build:obfuscated && electron-builder --linux", "package:mac:simple": "npm run build:obfuscated && electron-packager . \"ChromaSync\" --platform=darwin --arch=x64 --icon=assets/Colour\\ Tracker.svg --out=dist --overwrite", "create:dmg": "electron-installer-dmg dist/ChromaSync-darwin-x64/ChromaSync.app \"ChromaSync\" --out=dist --icon=assets/Colour\\ Tracker.svg --title=\"Install ChromaSync\" --overwrite", "package:mac:dmg": "npm run package:mac:simple && npm run create:dmg", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx src --fix", "lint:fix-imports": "eslint --ext .js,.jsx,.ts,.tsx src --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\" \"*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\" \"*.{ts,tsx,js,jsx,json,css,md}\"", "format:src": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "test:accessibility": "vitest run accessibility", "test:performance": "vitest run performance", "test:cross-browser": "vitest run cross-browser", "test:rtl": "vitest run rtl", "test:all": "node ./src/test/run-all-tests.js", "test:perf": "ts-node-esm src/test/performance/run-performance-tests.ts", "test:perf:full": "node --max-old-space-size=4096 --expose-gc --loader ts-node/esm src/test/performance/run-performance-tests.ts", "test:perf:quick": "node src/test/performance/quick-test.js", "test:sync": "node scripts/test-product-color-sync.js", "test:complete": "node scripts/test-complete-sync.js", "test:perf:ui": "echo 'Add /performance-test route to your app and run npm run dev'", "release": "npm run build && electron-builder --publish always", "count-colors": "electron-vite build && node scripts/check-library.js", "release-notes": "node scripts/release-notes-generator.js", "release-notes:changelog": "node scripts/release-notes-generator.js --changelog", "release-notes:version": "node scripts/release-notes-generator.js", "changelog": "node scripts/release-notes-generator.js --changelog", "library:convert": "node scripts/convert-color-libraries.cjs", "library:test": "node scripts/test-enhanced-library-loading.cjs", "library:update": "npm run library:convert && npm run library:test", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --max-warnings 0", "prettier --write", "git add"], "*.{json,md,css,scss,yaml,yml}": ["prettier --write", "git add"], "*.ts": ["bash -c 'tsc --noEmit --skipLib<PERSON><PERSON><PERSON>'"]}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "@sentry/electron": "^6.8.0", "@sentry/node": "^9.29.0", "@supabase/mcp-server-supabase": "^0.4.1", "@supabase/supabase-js": "^2.49.8", "@types/papaparse": "^5.3.16", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-window": "^1.8.8", "@types/three": "^0.176.0", "@visx/visx": "^3.12.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "axios": "^1.8.4", "better-sqlite3": "^11.10.0", "cheerio": "^1.0.0", "colord": "^2.9.3", "csv-parser": "^3.2.0", "electron-is-dev": "^2.0.0", "electron-log": "^5.0.0", "electron-store": "^8.1.0", "electron-updater": "^6.3.9", "electron-util": "^0.17.2", "generic-pool": "^3.9.0", "hotkeys-js": "^3.13.9", "jspdf": "^3.0.1", "lucide-react": "^0.483.0", "node-machine-id": "^1.1.12", "nodemailer": "^7.0.3", "papaparse": "^5.5.3", "pdf-parse": "^1.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.59.0", "react-router-dom": "^7.6.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.3", "sqlite3": "^5.1.7", "three": "^0.176.0", "zod": "^3.25.67", "zustand": "^4.4.7"}, "devDependencies": {"@axe-core/playwright": "^4.8.1", "@electron/rebuild": "^4.0.1", "@playwright/test": "^1.51.0", "@rollup/plugin-commonjs": "^28.0.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/better-sqlite3": "^7.6.12", "@types/jest": "^29.5.14", "@types/node": "^20.11.13", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^3.2.3", "autoprefixer": "^10.4.17", "dotenv": "^16.5.0", "electron": "^35.7.0", "electron-builder": "^24.13.3", "electron-installer-dmg": "^5.0.1", "electron-notarize": "^1.2.2", "electron-packager": "^17.1.2", "electron-rebuild": "^3.2.9", "electron-vite": "^3.0.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "javascript-obfuscator": "^4.1.1", "jsdom": "^26.0.0", "kill-port": "^2.0.1", "lint-staged": "^16.1.2", "playwright": "^1.51.0", "postcss": "^8.4.33", "prettier": "^3.6.2", "puppeteer": "^24.4.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "uuid": "^11.1.0", "vite": "^4.5.9", "vitest": "^3.0.8"}}