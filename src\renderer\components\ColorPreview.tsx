/**
 * @file ColorPreview.tsx
 * @description A color preview component with picker functionality
 * Now supports gradient display
 */

import React, { useRef } from 'react';
import { useTokens } from '../hooks/useTokens';
import { GradientInfo } from '../../shared/types/color.types';

interface ColorPreviewProps {
  hex: string;
  colorName?: string;
  onChange: (hexColor: string) => void;
  isDarkColor: boolean;
  gradient?: GradientInfo;
  onGradientClick?: () => void;
}

export default function ColorPreview({
  hex,
  colorName,
  onChange,
  isDarkColor: _isDarkColor,
  gradient,
  onGradientClick,
}: ColorPreviewProps) {
  const tokens = useTokens();
  const colorPickerRef = useRef<HTMLInputElement>(null);

  const openColorPicker = () => {
    if (gradient && onGradientClick) {
      onGradientClick();
      return;
    }
    colorPickerRef.current?.click();
  };

  const handleColorPickerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  // Preview container styles using design tokens
  const previewClasses =
    'w-20 h-20 overflow-hidden flex flex-col cursor-pointer relative flex-shrink-0 rounded-[var(--radius-md)]';

  // Label styles using design tokens
  const labelClasses =
    'absolute bottom-0 left-0 right-0 p-[var(--spacing-1)] text-xs text-center text-white bg-black bg-opacity-40';

  return (
    <div
      className={previewClasses}
      style={{
        background: gradient
          ? `linear-gradient(45deg, ${gradient.colors.join(', ')})`
          : hex || '#e53e3e',
        transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
      }}
      onClick={openColorPicker}
      aria-label={
        gradient
          ? 'Gradient preview - click to edit'
          : 'Color preview - click to change'
      }
      role='button'
      tabIndex={0}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          openColorPicker();
        }
      }}
      data-testid={gradient ? 'gradient-preview' : 'color-preview'}
    >
      {!gradient && (
        <input
          ref={colorPickerRef}
          type='color'
          value={hex || '#e53e3e'}
          onChange={handleColorPickerChange}
          className='opacity-0 absolute inset-0 w-full h-full cursor-pointer'
          aria-label='Choose color'
        />
      )}

      {colorName && <div className={labelClasses}>{colorName}</div>}
    </div>
  );
}
