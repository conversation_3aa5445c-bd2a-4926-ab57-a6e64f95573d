/**
 * In-memory database fallback when better-sqlite3 fails to load
 * This allows the app to run with limited functionality
 */

import { v4 as uuidv4 } from 'uuid';

export class InMemoryDatabase {
  private products: Map<string, any> = new Map();
  private colors: Map<string, any> = new Map();
  // Unused but kept for future implementation
  // private _productColors: Map<string, Set<string>> = new Map();

  constructor() {
    console.log('[InMemoryDB] Initialized in-memory database fallback');
  }

  // Mimic better-sqlite3 API
  prepare(sql: string) {
    const lowerSql = sql.toLowerCase();

    return {
      all: () => {
        if (lowerSql.includes('from products')) {
          return Array.from(this.products.values());
        }
        if (lowerSql.includes('from colors')) {
          return Array.from(this.colors.values());
        }
        return [];
      },
      get: (params?: any) => {
        if (lowerSql.includes('from products')) {
          if (params?.id) {
            return this.products.get(params.id);
          }
          if (params?.$id) {
            return this.products.get(params.$id);
          }
        }
        if (lowerSql.includes('from colors')) {
          if (params?.id) {
            return this.colors.get(params.id);
          }
          if (params?.$id) {
            return this.colors.get(params.$id);
          }
        }
        return null;
      },
      run: (params?: any) => {
        if (lowerSql.includes('insert into products')) {
          const id = params?.id || params?.$id || uuidv4();
          const product = {
            id,
            external_id: id,
            name: params?.name || params?.$name || 'Unnamed Product',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          this.products.set(id, product);
          return { lastInsertRowid: id, changes: 1 };
        }
        if (lowerSql.includes('insert into colors')) {
          const id = params?.id || params?.$id || uuidv4();
          const color = {
            id,
            external_id: id,
            code: params?.code || params?.$code || 'COLOR',
            hex: params?.hex || params?.$hex || '#000000',
            source_id: 1,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          this.colors.set(id, color);
          return { lastInsertRowid: id, changes: 1 };
        }
        return { lastInsertRowid: null, changes: 0 };
      },
    };
  }

  exec(sql: string) {
    console.log('[InMemoryDB] Exec called with:', `${sql.substring(0, 50)  }...`);
    return this;
  }

  pragma(sql: string) {
    console.log('[InMemoryDB] Pragma called with:', sql);
    return this;
  }

  close() {
    console.log('[InMemoryDB] Database closed');
  }
}
