/**
 * @file useDesignTokens.ts
 * @description React hook for accessing design tokens in components
 */

import { useMemo } from 'react';
import tokens from '../styles/tokens';
import { getColor, getTypography, getSpacing } from '../utils/theme';
import { useColorStore } from '../store/color.store';

// Border radius types
type BorderRadiusSize =
  | 'none'
  | 'sm'
  | 'DEFAULT'
  | 'md'
  | 'lg'
  | 'xl'
  | '2xl'
  | 'full';

// Shadow sizes
type ShadowSize = 'sm' | 'DEFAULT' | 'md' | 'lg' | 'xl';

interface DesignTokensHookReturn {
  // Color helpers
  getColor: (path: string) => string | undefined;
  getBrandColor: (
    key: 'primary' | 'secondary' | 'accent'
  ) => string | undefined;
  getUiColor: (path: string) => string | undefined;
  getFeedbackColor: (
    key: 'success' | 'warning' | 'error' | 'info'
  ) => string | undefined;

  // Typography helpers
  getFontSize: (
    size: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'
  ) => string | undefined;
  getFontWeight: (
    weight: 'normal' | 'medium' | 'semibold' | 'bold'
  ) => string | undefined;
  getLineHeight: (
    height: 'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose'
  ) => string | undefined;

  // Spacing helpers
  getSpacing: (key: string | number) => string | undefined;

  // Other design tokens
  getBorderRadius: (size: BorderRadiusSize) => string;
  getShadow: (size: ShadowSize) => string;

  // Raw tokens
  tokens: typeof tokens;

  // System theme detection
  isDarkMode: boolean;
}

/**
 * Hook to access design tokens in components
 * @returns Object with functions to access design tokens
 */
export function useDesignTokens(): DesignTokensHookReturn {
  // Get dark mode from the color store
  const { darkMode } = useColorStore();

  // Memoize the hook return value to prevent unnecessary re-renders
  const designTokens = useMemo<DesignTokensHookReturn>(() => {
    return {
      // Color helpers
      getColor,
      getBrandColor: key => getColor(`brand.${key}`),
      getUiColor: path => getColor(`ui.${path}`),
      getFeedbackColor: key => getColor(`feedback.${key}`),

      // Typography helpers
      getFontSize: size =>
        getTypography(`fontSize.${size}`) as string | undefined,
      getFontWeight: weight =>
        getTypography(`fontWeight.${weight}`) as string | undefined,
      getLineHeight: height =>
        getTypography(`lineHeight.${height}`) as string | undefined,

      // Spacing helper
      getSpacing,

      // Other design tokens with proper typing
      getBorderRadius: size => tokens.borderRadius[size],
      getShadow: size => tokens.shadows[size],

      // Raw tokens
      tokens,

      // System theme
      isDarkMode: darkMode,
    };
  }, [darkMode]);

  return designTokens;
}

export default useDesignTokens;
