/**
 * @file sync-handlers.ts
 * @description Simplified IPC handlers for unified sync operations
 */

import { ipc<PERSON>ain, <PERSON>rowserWindow } from 'electron';
import { registerHandlerSafely } from '../utils/ipcRegistry';
import { getOAuthService } from '../services/service-locator';
// import { gdprService } from '../services/gdpr.service';
import { getCurrentOrganization, setCurrentOrganization } from '../utils/organization-context';
import { OrganizationService } from '../db/services/organization.service';
import { getDatabase } from '../db/database';
import { unifiedSyncManager } from '../services/sync/unified-sync-manager';
import { syncOutboxService } from '../services/sync/sync-outbox.service';
import { syncStatusManager } from '../services/sync/sync-status-manager.service';
// import * as fs from 'fs';
// import type { Organization } from '../../shared/types/organization.types';

export interface SyncConfig {
  enabled: boolean;
  interval: number;
  lastSync?: string;
}

async function executeSync() {
  try {
    console.log('[Sync] 🚀 Manual sync requested');
    
    // Check if sync manager is ready
    if (!unifiedSyncManager.isReady()) {
      console.warn('[Sync] ⚠️ Sync manager not ready, attempting to initialize...');
      const initSuccess = await initializeSyncManager();
      if (!initSuccess) {
        return {
          success: false,
          message: 'Sync system not ready. Please ensure you are authenticated and an organization is selected.',
          timestamp: Date.now()
        };
      }
    }
    
    const oauthService = getOAuthService();
    const user = await oauthService.getCurrentUser();
    
    if (!user?.id) {
      console.error('[Sync] ❌ No authenticated user found');
      return {
        success: false,
        message: 'Authentication required to sync',
        timestamp: Date.now()
      };
    }

    const currentOrgId = getCurrentOrganization();
    if (!currentOrgId) {
      console.error('[Sync] ❌ No organization context found');
      return {
        success: false,
        message: 'Organization context required to sync',
        timestamp: Date.now()
      };
    }

    const database = getDatabase();
    
    // Check if initial sync is needed
    const dataCheck = database.prepare(`
      SELECT 
        (SELECT COUNT(*) FROM colors WHERE organization_id = ?) as colors,
        (SELECT COUNT(*) FROM products WHERE organization_id = ?) as products,
        (SELECT COUNT(*) FROM product_colors pc 
         JOIN products p ON pc.product_id = p.id 
         WHERE p.organization_id = ?) as product_colors
    `).get(currentOrgId, currentOrgId, currentOrgId) as { colors: number; products: number; product_colors: number } | undefined;
    
    console.log('[Sync] Data check result:', dataCheck);
    const needsInitialSync = dataCheck && (dataCheck.colors === 0 || dataCheck.products === 0 || dataCheck.product_colors === 0);
    console.log('[Sync] Needs initial sync:', needsInitialSync);
    
    if (needsInitialSync) {
      console.log('[Sync] 🆕 Performing initial data sync...');
      try {
        // FIXED: Use direct service calls instead of dynamic import to avoid Electron bundling issues
        const { ServiceLocator } = await import('../services/service-locator');
        
        // Initialize sync results
        let colorsCount = 0;
        let productsCount = 0;
        let relationshipsCount = 0;
        const errors: string[] = [];
        
        // Step 1: Sync organizations first (required for all other sync operations)
        console.log('[Sync] Step 1: Syncing organizations...');
        try {
          const organizationService = ServiceLocator.getOrganizationService();
          await organizationService.syncOrganizationsFromSupabase(user.id);
          console.log(`[Sync] ✅ Organizations synced for user ${user.id}`);
        } catch (error) {
          const errorMsg = `Failed to sync organizations: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(`[Sync] ❌ ${errorMsg}`);
          errors.push(errorMsg);
        }
        
        // Step 2: Sync colors directly
        console.log('[Sync] Step 2: Syncing colors from Supabase...');
        try {
          const colorService = ServiceLocator.getColorService();
          const colors = await colorService.syncColorsFromSupabase(user.id, currentOrgId);
          colorsCount = colors.length;
          console.log(`[Sync] ✅ Synced ${colorsCount} colors`);
        } catch (error) {
          const errorMsg = `Failed to sync colors: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(`[Sync] ❌ ${errorMsg}`);
          errors.push(errorMsg);
        }
        
        // Step 3: Sync products
        console.log('[Sync] Step 3: Syncing products from Supabase...');
        try {
          const productService = ServiceLocator.getProductService();
          const products = await productService.syncProductsFromSupabase(user.id, currentOrgId);
          productsCount = products.length;
          console.log(`[Sync] ✅ Synced ${productsCount} products`);
        } catch (error) {
          const errorMsg = `Failed to sync products: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(`[Sync] ❌ ${errorMsg}`);
          errors.push(errorMsg);
        }
        
        // Step 4: Sync product-color relationships
        console.log('[Sync] Step 4: Syncing product-color relationships...');
        try {
          const productService = ServiceLocator.getProductService();
          const syncResult = await productService.syncProductColorsFromSupabase(currentOrgId);
          if (syncResult.success) {
            relationshipsCount = syncResult.syncedCount;
            console.log(`[Sync] ✅ Synced ${relationshipsCount} product-color relationships`);
          } else {
            throw new Error(`Product-color sync failed: ${syncResult.errors.join(', ')}`);
          }
        } catch (error) {
          const errorMsg = `Failed to sync product-color relationships: ${error instanceof Error ? error.message : 'Unknown error'}`;
          console.error(`[Sync] ❌ ${errorMsg}`);
          errors.push(errorMsg);
        }
        
        // Log summary
        const success = colorsCount > 0 || productsCount > 0 || relationshipsCount > 0;
        if (success) {
          console.log(`[Sync] ✅ Initial sync completed: ${colorsCount} colors, ${productsCount} products, ${relationshipsCount} relationships`);
        } else if (errors.length > 0) {
          console.log('[Sync] ⚠️ Initial sync completed with errors:', errors);
        } else {
          console.log('[Sync] ⚠️ No data was synced - this might be a new organization');
        }
        
      } catch (error) {
        console.error('[Sync] ❌ Failed to perform initial data sync:', error);
        console.error('[Sync] Error details:', error instanceof Error ? error.stack : 'No stack trace');
      }
    }

    console.log('[Sync] 🚀 Starting unified sync process...');
    console.log('[Sync] 🔍 Sync manager status before sync:', unifiedSyncManager.getStatus());
    console.log('[Sync] 🔍 Sync manager ready?:', unifiedSyncManager.isReady());
    
    // Enable verbose sync logging for debugging
    process.env.VERBOSE_SYNC_LOGGING = 'true';
    process.env.SYNC_ERROR_ELEVATION = 'true';
    
    console.log('[Sync] 🎯 About to call unifiedSyncManager.sync() with args:', {
      type: 'full',
      direction: 'bidirectional', 
      priority: 'normal',
      userId: user.id,
      organizationId: currentOrgId
    });
    
    // Execute sync
    let result;
    try {
      result = await unifiedSyncManager.sync('full', 'bidirectional', 'normal');
      console.log('[Sync] 🎯 unifiedSyncManager.sync() returned result:', result);
    } catch (syncError) {
      console.error('[Sync] ❌ unifiedSyncManager.sync() threw an error:', syncError);
      console.error('[Sync] ❌ Error stack:', syncError instanceof Error ? syncError.stack : 'No stack trace');
      
      // Reset verbose logging
      process.env.VERBOSE_SYNC_LOGGING = 'false';
      process.env.SYNC_ERROR_ELEVATION = 'false';
      
      return {
        success: false,
        message: `Sync manager error: ${syncError instanceof Error ? syncError.message : 'Unknown error'}`,
        timestamp: Date.now()
      };
    }
    
    // Reset verbose logging
    process.env.VERBOSE_SYNC_LOGGING = 'false';
    process.env.SYNC_ERROR_ELEVATION = 'false';

    if (result.success) {
      console.log(`[Sync] ✅ Sync completed successfully - processed ${result.itemsProcessed} items in ${result.duration}ms`);
      
      // Verification: Check actual Supabase data after sync
      try {
        const { ensureAuthenticatedSession } = await import('../services/supabase-client');
        const authResult = await ensureAuthenticatedSession();
        
        if (!authResult.session) {
          throw new Error('No authenticated session for verification');
        }
        
        const { getSupabaseClient } = await import('../services/supabase-client');
        const supabase = getSupabaseClient();
        
        console.log('[Sync] 🔍 Running verification queries...');
        
        // Try with a simple query first
        const { data: _colorData, count: colorCount, error: colorError } = await supabase
          .from('colors')
          .select('id', { count: 'exact' })
          .eq('organization_id', currentOrgId)
          .is('deleted_at', null);

        const { data: _productData, count: productCount, error: productError } = await supabase
          .from('products')
          .select('id', { count: 'exact' })
          .eq('organization_id', currentOrgId)
          .is('deleted_at', null);

        console.log(`[Sync] Verification queries completed:`);
        console.log(`[Sync] - Colors: ${colorCount} (error: ${colorError?.message || 'none'})`);
        console.log(`[Sync] - Products: ${productCount} (error: ${productError?.message || 'none'})`);
        
        // Also check local database for comparison
        const database = getDatabase();
        const localColorCount = database.prepare('SELECT COUNT(*) as count FROM colors WHERE organization_id = ? AND deleted_at IS NULL').get(currentOrgId)?.count || 0;
        const localProductCount = database.prepare('SELECT COUNT(*) as count FROM products WHERE organization_id = ? AND deleted_at IS NULL').get(currentOrgId)?.count || 0;
        
        console.log(`[Sync] Local verification - Colors: ${localColorCount}, Products: ${localProductCount}`);

        return {
          success: true,
          message: 'Sync completed successfully',
          counts: {
            colors: colorCount ?? localColorCount,
            products: productCount ?? localProductCount, 
            datasheets: 0
          },
          syncResult: {
            itemsProcessed: result.itemsProcessed,
            duration: result.duration
          },
          timestamp: Date.now()
        };
      } catch (verifyError) {
        console.warn('[Sync] ⚠️ Verification failed, but sync succeeded:', verifyError);
        return {
          success: true,
          message: 'Sync completed (verification skipped)',
          syncResult: {
            itemsProcessed: result.itemsProcessed,
            duration: result.duration
          },
          timestamp: Date.now()
        };
      }
    } else {
      console.error('[Sync] ❌ Sync failed:', result.errors);
      return {
        success: false,
        message: result.errors?.[0] || 'Sync operation failed',
        timestamp: Date.now()
      };
    }
  } catch (error: unknown) {
    console.error('[Sync] ❌ Sync error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: Date.now()
    };
  }
}

async function initializeSyncManager(): Promise<boolean> {
  const oauthService = getOAuthService();
  const user = await oauthService.getCurrentUser();
  
  if (!user?.id) {
    console.error('[Sync] ❌ No authenticated user found for initialization');
    return false;
  }

  const currentOrgId = getCurrentOrganization();
  if (!currentOrgId) {
    console.error('[Sync] ❌ No organization context found for initialization');
    return false;
  }
  
  // Initialize sync manager
  console.log('[Sync] 🔧 Initializing sync manager with user and org context...');
  await unifiedSyncManager.initialize(user.id, currentOrgId, getSyncConfig());
  
  console.log('[Sync] ✅ Sync manager initialized successfully');
  return true;
}

function getSyncConfig() {
  // TODO: Load this from a user-configurable store
  return {
    autoSyncEnabled: true,
    autoSyncInterval: 5,
    realtimeEnabled: false,
    maxRetries: 3
  };
}

export function registerSyncHandlers() {
  // Login with Google
  registerHandlerSafely(ipcMain, 'sync:login', async () => {
    console.log('[Sync] Login handler called');
    
    try {
      const oauthService = getOAuthService();
      console.log('[Sync] Starting complete login process...');
      
      const result = await oauthService.completeLoginProcess();
      
      if (result.success) {
        if (result.status === 'authenticated' && !result.requiresConsent) {
          initializeSyncEventForwarding();
        }
      }
      
      console.log('[Sync] Login process completed:', { 
        success: result.success, 
        status: result.status, 
        requiresConsent: result.requiresConsent 
      });
      
      return result;
    } catch (error: unknown) {
      console.error('[Sync] Login error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  });

  // Accept GDPR consent
  registerHandlerSafely(ipcMain, 'sync:accept-gdpr', async (_, ip?: string) => {
    try {
      const oauthService = getOAuthService();
      console.log('[Sync] Processing GDPR consent acceptance...');
      
      const result = await oauthService.acceptGDPRConsentAndContinue(ip);
      
      if (result.success) {
        initializeSyncEventForwarding();
        console.log('[Sync] GDPR consent processed and sync initialized successfully');
      }

      return result;
    } catch (error: unknown) {
      console.error('[Sync] GDPR consent processing failed:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  });

  // Main sync operation
  registerHandlerSafely(ipcMain, 'sync:start', executeSync);

  // Get sync status
  registerHandlerSafely(ipcMain, 'sync:status', async () => {
    try {
      return {
        success: true,
        data: unifiedSyncManager.getStatus(),
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[Sync] Error getting status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  });

  // Logout
  registerHandlerSafely(ipcMain, 'sync:logout', async () => {
    try {
      console.log('[Sync] Logout requested - cleaning up sync state');
      
      // Stop sync manager
      unifiedSyncManager.stop();
      
      const oauthService = getOAuthService();
      await oauthService.signOut();
      
      return { success: true, message: 'Logged out successfully' };
    } catch (error: unknown) {
      console.error('[Sync] Logout error:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  });

  // Get current user info
  registerHandlerSafely(ipcMain, 'sync:user', async () => {
    try {
      const oauthService = getOAuthService();
      const user = await oauthService.getCurrentUser();
      
      if (!user) {
        return { user: null, authenticated: false };
      }
      
      return { 
        user: {
          id: user.id,
          email: user.email,
          name: user.user_metadata?.name || user.email
        }, 
        authenticated: true 
      };
    } catch (error: unknown) {
      console.error('[Sync] Error getting user info:', error);
      return { user: null, authenticated: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  // Organization management
  registerHandlerSafely(ipcMain, 'sync:get-organizations', async () => {
    try {
      const oauthService = getOAuthService();
      const user = await oauthService.getCurrentUser();
      
      if (!user?.id) {
        return { organizations: [], currentOrganizationId: null };
      }

      const database = getDatabase();
      const organizationService = new OrganizationService(database);
      const organizations = await organizationService.getOrganizationsForUser(user.id);
      const currentOrgId = getCurrentOrganization();
      
      return {
        organizations,
        currentOrganizationId: currentOrgId
      };
    } catch (error: unknown) {
      console.error('[Sync] Error getting organizations:', error);
      return { organizations: [], currentOrganizationId: null, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  registerHandlerSafely(ipcMain, 'sync:set-organization', async (_, organizationId: string) => {
    try {
      setCurrentOrganization(organizationId);
      
      // Re-initialize sync manager with new organization
      const oauthService = getOAuthService();
      const user = await oauthService.getCurrentUser();
      
      if (user?.id) {
        await unifiedSyncManager.initialize(user.id, organizationId);
      }
      
      return { success: true, organizationId };
    } catch (error: unknown) {
      console.error('[Sync] Error setting organization:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  // Get authentication state
  registerHandlerSafely(ipcMain, 'sync:get-auth-state', async () => {
    try {
      const oauthService = getOAuthService();
      const user = await oauthService.getCurrentUser();
      
      if (!user) {
        return { 
          authenticated: false,
          isAuthenticated: false,
          user: null,
          hasConsent: false,
          requiresConsent: false
        };
      }
      
      // Check if user has GDPR consent (simplified check)
      let hasConsent = true; // Default to true for now since login succeeded
      
      // Get organization information
      const database = getDatabase();
      const organizationService = new OrganizationService(database);
      const organizations = await organizationService.getOrganizationsForUser(user.id);
      const currentOrgId = getCurrentOrganization();
      
      // Determine organization status
      let organizationStatus: 'needs_organization_setup' | 'needs_organization_selection' | 'authenticated' = 'authenticated';
      
      if (organizations.length === 0) {
        organizationStatus = 'needs_organization_setup';
      } else if (!currentOrgId || !organizations.find(org => org.external_id === currentOrgId)) {
        organizationStatus = 'needs_organization_selection';
      }
      
      return {
        authenticated: true,
        isAuthenticated: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.user_metadata?.name || user.email
        },
        hasConsent,
        requiresConsent: !hasConsent,
        status: organizationStatus,
        organizations,
        currentOrganizationId: currentOrgId
      };
    } catch (error: unknown) {
      console.error('[Sync] Error getting auth state:', error);
      return {
        authenticated: false,
        isAuthenticated: false,
        user: null,
        hasConsent: false,
        requiresConsent: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Add alias handlers for frontend compatibility
  registerHandlerSafely(ipcMain, 'sync:sync', executeSync);
  
  registerHandlerSafely(ipcMain, 'sync:get-progress', async () => {
    console.log('[SyncHandlers] get-progress called - using centralized status manager');
    return syncStatusManager.getProgress();
  });

  registerHandlerSafely(ipcMain, 'sync:has-unsynced-local-changes', async () => {
    console.log('[SyncHandlers] has-unsynced-local-changes called - using centralized status manager');
    return syncStatusManager.hasUnsyncedLocalChanges();
  });

  registerHandlerSafely(ipcMain, 'sync:clear-not-found-deletes', async () => {
    try {
      const removedCount = syncOutboxService.clearNotFoundDeleteOperations();
      return {
        success: true,
        removedCount,
        message: `Cleared ${removedCount} failed delete operations`,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[Sync] Error clearing not found delete operations:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  });

  registerHandlerSafely(ipcMain, 'sync:get-queue-stats', async () => {
    console.log('[SyncHandlers] get-queue-stats called - using centralized status manager');
    return syncStatusManager.getQueueStats();
  });

  registerHandlerSafely(ipcMain, 'sync:get-config', async () => {
    const status = unifiedSyncManager.getStatus();
    return {
      success: true,
      config: {
        ...status.config,
        enabled: status.config.autoSyncEnabled,
        interval: status.config.autoSyncInterval * 60 * 1000,
        lastSync: status.lastSyncTime ? new Date(status.lastSyncTime).toISOString() : null
      },
      timestamp: Date.now()
    };
  });

  registerHandlerSafely(ipcMain, 'sync:get-metrics', async () => {
    console.log('[SyncHandlers] get-metrics called - using centralized status manager');
    return syncStatusManager.getMetrics();
  });

  // Add handler for sync status report (for monitoring)
  registerHandlerSafely(ipcMain, 'sync:get-status-report', async () => {
    console.log('[SyncHandlers] get-status-report called');
    return {
      success: true,
      report: syncStatusManager.getStatusReport(),
      timestamp: Date.now()
    };
  });

  // Add handler for user activity tracking
  registerHandlerSafely(ipcMain, 'sync:update-user-activity', async () => {
    syncStatusManager.updateUserActivity();
    return { success: true };
  });

  // Debug handler to clear sync outbox (for development)
  registerHandlerSafely(ipcMain, 'sync:clear-outbox', async () => {
    try {
      const pendingCount = syncOutboxService.getPendingChanges().length;
      syncOutboxService.clearAll();
      console.log(`[Sync] 🧹 Cleared ${pendingCount} pending items from sync outbox`);
      return {
        success: true,
        message: `Cleared ${pendingCount} pending items from sync outbox`,
        timestamp: Date.now()
      };
    } catch (error: unknown) {
      console.error('[Sync] Error clearing outbox:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  });

  // Debug handler to inspect sync outbox (for development)
  registerHandlerSafely(ipcMain, 'sync:debug-outbox', async () => {
    try {
      const pendingChanges = syncOutboxService.getPendingChanges();
      console.log(`[Sync] 🔍 Debug: Found ${pendingChanges.length} items in sync outbox:`, pendingChanges);
      return {
        success: true,
        pendingChanges,
        count: pendingChanges.length,
        hasChanges: pendingChanges.length > 0,
        timestamp: Date.now()
      };
    } catch (error: unknown) {
      console.error('[Sync] Error inspecting outbox:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  });

  console.log('[Sync] ✅ Simplified sync handlers registered');
}

// ============================================================================
// SIMPLIFIED EVENT FORWARDING FOR UNIFIED SYNC
// ============================================================================

/**
 * Set up simplified event forwarding for unified sync manager
 */
export function setupUnifiedSyncEventForwarding(mainWindow: BrowserWindow): void {
  console.log('[SyncEvents] Setting up unified sync event forwarding to renderer');

  // Check if sync manager is ready before setting up event forwarding
  if (!unifiedSyncManager.isReady()) {
    console.warn('[SyncEvents] Sync manager not ready, deferring event forwarding setup');
    unifiedSyncManager.once('initialized', () => {
      setupUnifiedSyncEventForwarding(mainWindow);
    });
    return;
  }

  // Listen to unified sync manager events
  unifiedSyncManager.on('sync_started', (operation: any) => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('sync:status-update', {
        status: 'syncing',
        phase: 'initializing',
        message: `Starting ${operation.type} sync...`,
        timestamp: Date.now()
      });
    }
  });

  unifiedSyncManager.on('sync_completed', (result: any) => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('sync:status-update', {
        status: 'success',
        phase: 'complete',
        message: 'Sync completed successfully',
        timestamp: Date.now(),
        itemsProcessed: result.itemsProcessed,
        duration: result.duration
      });
    }
  });

  unifiedSyncManager.on('sync_failed', (result: any) => {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('sync:status-update', {
        status: 'error',
        phase: 'error',
        message: result.errors?.[0] || 'Sync failed',
        timestamp: Date.now()
      });
    }
  });

  console.log('[SyncEvents] ✅ Unified sync event forwarding configured');
}

export function initializeSyncEventForwarding(): void {
  // Get the main window
  const mainWindow = BrowserWindow.getAllWindows().find(w => !w.isDestroyed());
  if (mainWindow) {
    setupUnifiedSyncEventForwarding(mainWindow);
    setupSyncStatusManagerIntegration(mainWindow);
  } else {
    console.warn('[SyncEvents] No main window found for event forwarding setup');
  }
}

/**
 * Setup integration between sync status manager and main window
 */
function setupSyncStatusManagerIntegration(mainWindow: BrowserWindow): void {
  console.log('[SyncEvents] Setting up sync status manager integration');

  // Track window focus for intelligent polling
  mainWindow.on('focus', () => {
    syncStatusManager.setAppFocused(true);
  });

  mainWindow.on('blur', () => {
    syncStatusManager.setAppFocused(false);
  });

  // Track window visibility
  mainWindow.on('show', () => {
    syncStatusManager.setAppFocused(true);
    syncStatusManager.updateUserActivity();
  });

  mainWindow.on('hide', () => {
    syncStatusManager.setAppFocused(false);
  });

  // Clean up on window close
  mainWindow.on('closed', () => {
    syncStatusManager.shutdown();
  });

  console.log('[SyncEvents] ✅ Sync status manager integration configured');
}