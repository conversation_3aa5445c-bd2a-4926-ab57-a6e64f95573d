/**
 * @file color.repository.simple.test.ts
 * @description Simplified test for ColorRepository integration
 */

import { describe, test, expect, beforeAll, afterAll } from 'vitest';
import Database from 'better-sqlite3';
import { ColorRepository } from '../color.repository';

describe('ColorRepository Integration', () => {
  let db: Database.Database;
  let repository: ColorRepository;
  const mockOrganizationId = '550e8400-e29b-41d4-a716-446655440000';

  beforeAll(() => {
    // Create persistent in-memory database for all tests
    db = new Database(':memory:');

    // Set up schema
    db.exec(`
      CREATE TABLE color_sources (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL,
        name TEXT NOT NULL
      );
      
      INSERT INTO color_sources (code, name) VALUES ('user', 'User Colors');
      INSERT INTO color_sources (code, name) VALUES ('RAL', 'RAL Colors');
      
      CREATE TABLE colors (
        id TEXT PRIMARY KEY,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        source_id INTEGER,
        code TEXT NOT NULL,
        display_name TEXT,
        hex TEXT NOT NULL,
        color_spaces TEXT,
        is_gradient INTEGER DEFAULT 0,
        is_metallic INTEGER DEFAULT 0,
        is_effect INTEGER DEFAULT 0,
        is_library INTEGER DEFAULT 0,
        gradient_colors TEXT,
        notes TEXT,
        tags TEXT,
        properties TEXT,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL
      );
      
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        deleted_at TEXT NULL
      );
      
      CREATE TABLE product_colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id TEXT NOT NULL,
        color_id TEXT NOT NULL,
        organization_id TEXT NOT NULL
      );
    `);

    repository = new ColorRepository(db);
  });

  afterAll(() => {
    db.close();
  });

  test('should connect to database and create repository', () => {
    expect(repository).toBeDefined();
    expect(db.open).toBe(true);
  });

  test('should insert and retrieve a color', () => {
    const newColor: any = {
      code: 'TEST001',
      name: 'Test Color',
      hex: '#FF0000',
      cmyk: 'C:0 M:100 Y:100 K:0',
      product: 'Test Product',
    };

    const colorId = repository.insert(newColor, mockOrganizationId);
    expect(colorId).toBeTruthy();
    expect(typeof colorId).toBe('string');

    const retrievedColor = repository.findById(colorId, mockOrganizationId);
    expect(retrievedColor).toBeTruthy();
    expect(retrievedColor.code).toBe('TEST001');
    expect(retrievedColor.hex).toBe('#FF0000');
    expect(retrievedColor.display_name).toBe('Test Color');
  });

  test('should return all colors for organization', () => {
    const colors = repository.findAll(mockOrganizationId);
    expect(Array.isArray(colors)).toBe(true);
    expect(colors.length).toBeGreaterThanOrEqual(1);
  });

  test('should soft delete a color', () => {
    // Insert a color to delete
    const newColor: any = {
      code: 'DELETE_TEST',
      name: 'Delete Test Color',
      hex: '#000000',
      cmyk: 'C:0 M:0 Y:0 K:100',
      product: 'Delete Test Product',
    };

    const colorId = repository.insert(newColor, mockOrganizationId);
    expect(colorId).toBeTruthy();

    // Verify it exists
    const beforeDelete = repository.findById(colorId, mockOrganizationId);
    expect(beforeDelete).toBeTruthy();

    // Soft delete it
    const deleteResult = repository.softDelete(colorId, mockOrganizationId);
    expect(deleteResult).toBe(true);

    // Verify it's gone from normal queries
    const afterDelete = repository.findById(colorId, mockOrganizationId);
    expect(afterDelete).toBeNull();
  });

  test('should validate organization ID format', () => {
    expect(() => {
      repository.findAll('invalid-org-id');
    }).toThrow('Invalid organization ID format');
  });
});
