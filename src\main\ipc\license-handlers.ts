/**
 * @file license-handlers.ts
 * @description IPC handlers for license management
 */

import { ipcMain, dialog, BrowserWindow } from 'electron';
import {
  validateLicense,
  activateDevice,
  deactivateDevice,
  isDeviceActivated,
  getDeviceLicenseKey,
  getDeviceId,
} from '../license/license-validator';

/**
 * Registers IPC handlers for license management
 * @param mainWindow The main application window
 */
export function registerLicenseHandlers(
  mainWindow: BrowserWindow | null
): void {
  // Get device activation status
  ipcMain.handle('license:get-status', async () => {
    try {
      const isValid = await validateLicense();
      const isActivated = await isDeviceActivated();

      return {
        success: true,
        data: {
          isValid,
          isActivated,
        },
      };
    } catch (error) {
      console.error('Error getting device status:', error);
      return {
        success: false,
        error: 'Failed to get device status',
      };
    }
  });

  // Activate device
  ipcMain.handle('license:activate-device', async () => {
    try {
      const result = await activateDevice();

      // Show success dialog if activation was successful
      if (result && mainWindow) {
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: 'Device Activated',
          message: 'Your device has been activated successfully.',
          buttons: ['OK'],
        });
      }

      return {
        success: result,
        message: result
          ? 'Device activated successfully'
          : 'Device activation failed',
      };
    } catch (error) {
      console.error('Error activating device:', error);
      return {
        success: false,
        error: 'Failed to activate device',
      };
    }
  });

  // Deactivate device
  ipcMain.handle('license:deactivate-device', async () => {
    try {
      // Confirm deactivation
      if (mainWindow) {
        const confirmation = await dialog.showMessageBox(mainWindow, {
          type: 'question',
          title: 'Deactivate Device',
          message: 'Are you sure you want to deactivate this device?',
          buttons: ['Cancel', 'Deactivate'],
          defaultId: 0,
          cancelId: 0,
        });

        if (confirmation.response === 0) {
          return {
            success: false,
            message: 'Deactivation cancelled',
          };
        }
      }

      const result = await deactivateDevice();

      return {
        success: result,
        message: result
          ? 'Device deactivated successfully'
          : 'Device deactivation failed',
      };
    } catch (error) {
      console.error('Error deactivating device:', error);
      return {
        success: false,
        error: 'Failed to deactivate device',
      };
    }
  });

  // Show device activation dialog
  ipcMain.handle('license:show-activation-dialog', async () => {
    try {
      if (!mainWindow) {
        return {
          success: false,
          error: 'Main window not available',
        };
      }

      const isValid = await validateLicense();
      const isActivated = await isDeviceActivated();

      // Send event to renderer to show activation dialog
      mainWindow.webContents.send('license:show-activation-dialog', {
        isValid,
        isActivated,
      });

      return {
        success: true,
      };
    } catch (error) {
      console.error('Error showing activation dialog:', error);
      return {
        success: false,
        error: 'Failed to show activation dialog',
      };
    }
  });

  // Get device ID
  ipcMain.handle('license:get-device-id', () => {
    return getDeviceId();
  });

  // Get device license key
  ipcMain.handle('license:get-device-license-key', () => {
    return getDeviceLicenseKey();
  });

  // Activate license with automatic key
  ipcMain.handle('license:activate-license', async () => {
    try {
      // Use the automatic license key generation
      const result = await activateDevice();

      return {
        success: result,
        message: result
          ? 'License activated successfully'
          : 'License activation failed',
        licenseKey: result ? 'auto-generated' : undefined,
      };
    } catch (error) {
      console.error('Error activating license:', error);
      return {
        success: false,
        error: 'Failed to activate license',
      };
    }
  });

  // Send license dialog event to renderer
  ipcMain.handle('license:show-dialog', async () => {
    try {
      if (!mainWindow) {
        return {
          success: false,
          error: 'Main window not available',
        };
      }

      const deviceId = getDeviceId();
      const licenseKey = getDeviceLicenseKey();
      const isValid = await validateLicense();
      const isActivated = await isDeviceActivated();

      // Send event to renderer to show license dialog
      mainWindow.webContents.send('license:show-dialog', {
        isValid,
        isActivated,
        deviceId,
        licenseKey,
        inTrialMode: !isActivated, // Only in trial mode if not activated
        trialDaysRemaining: 30, // Hardcoded for now
      });

      return {
        success: true,
      };
    } catch (error) {
      console.error('Error showing license dialog:', error);
      return {
        success: false,
        error: 'Failed to show license dialog',
      };
    }
  });

  // Check device activation on startup and periodically
  ipcMain.handle('license:check', async () => {
    try {
      const isValid = await validateLicense();

      // TESTING MODE: We don't show any error dialogs in testing mode
      // since all devices are automatically authorized

      /* RESTRICTED MODE (uncomment when ready)
      if (!isValid && mainWindow) {
        // If device is not authorized, show dialog
        dialog.showMessageBox(mainWindow, {
          type: 'error',
          title: 'Device Not Authorized',
          message: 'This device is not authorized to use ChromaSync. Please contact your administrator to authorize this device.',
          buttons: ['Request Authorization', 'Exit'],
          defaultId: 0,
          cancelId: 1
        }).then(result => {
          if (result.response === 0) {
            // Show device ID and license key for authorization request
            const deviceId = getDeviceId();
            const licenseKey = getDeviceLicenseKey();
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Device Information',
              buttons: ['Copy to Clipboard', 'OK'],
              defaultId: 1
            }).then(copyResult => {
              if (copyResult.response === 0) {
                // Copy device info to clipboard
                if (mainWindow) {
                  mainWindow.webContents.clipboard.writeText(`Device ID: ${deviceId}\nLicense Key: ${licenseKey}`);
                }
              }
            });
          } else {
            app.quit();
          }
        });
      }
      */

      return {
        success: true,
        data: {
          isValid,
        },
      };
    } catch (error) {
      console.error('Error checking device authorization:', error);
      return {
        success: false,
        error: 'Failed to check device authorization',
      };
    }
  });
}
