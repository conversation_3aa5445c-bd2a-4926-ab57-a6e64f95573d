# Dependency Injection Implementation Guide

This document describes the dependency injection pattern implemented for IPC handlers in ChromaSync, eliminating the `new Service()` anti-pattern and establishing centralized service management.

## 🎯 Problem Solved

Previously, IPC handlers had inconsistent service instantiation:
- Some handlers created `new Service(db)` instances
- Service state and caching was inconsistent
- Testing was difficult due to tight coupling
- No centralized service lifecycle management

## 📁 Files Created/Modified

### Enhanced Files
- `src/main/services/service-container.ts` - Added all database services
- `src/main/services/service-locator.ts` - Added getters for all services
- `src/shared/types/ipc.types.ts` - Added IPC type definitions

### New Files
- `src/main/ipc/ipc-registry.ts` - Central IPC handler registration with DI
- `src/main/ipc/soft-delete-with-di.ipc.ts` - Example of fully migrated handler

## 🏗️ Service Container Architecture

### Service Registration
```typescript
// In service-container.ts
private async initializeCoreServices(): Promise<void> {
  const database = getDatabase();
  
  // All services registered as singletons
  const colorService = new ColorService(database);
  const productService = new ProductService(database);
  const datasheetService = new DatasheetService(database);
  const organizationService = new OrganizationService(database);
  
  this.register('colorService', colorService);
  this.register('productService', productService);
  // ... etc
}
```

### Service Locator Pattern
```typescript
// Access services anywhere in the application
const colorService = ServiceLocator.getColorService();
const productService = ServiceLocator.getProductService();
```

## 🔄 Migration Patterns

### Before: Direct Instantiation (Anti-Pattern)
```typescript
// ❌ Bad - Creates new instances in each handler
export function registerSoftDeleteIpcHandlers(): void {
  const db = getDatabase();
  const colorService = new ColorService(db);  // Problem: New instance each time
  const productService = new ProductService(db);

  ipcMain.handle('colors:get-soft-deleted', async (_, organizationId, limit, offset) => {
    try {
      return colorService.getSoftDeleted(organizationId, limit, offset);
    } catch (error) {
      console.error('Error:', error);
      return [];
    }
  });
}
```

### After: Dependency Injection (Best Practice)
```typescript
// ✅ Good - Uses injected services
export function registerSoftDeleteHandlers(services?: Partial<SoftDeleteServices>): void {
  // Use injected services or fall back to ServiceLocator
  const colorService = services?.colorService || ServiceLocator.getColorService();
  const productService = services?.productService || ServiceLocator.getProductService();

  registerSecureHandler(
    'colors:get-soft-deleted',
    async (organizationId: string, limit?: number, offset?: number) => {
      const result = await colorService.getSoftDeleted(organizationId, limit, offset);
      return createSuccessResponse(result, `Retrieved ${result.length} items`);
    },
    ipcMain,
    {
      logChannel: 'SoftDelete',
      customErrorMessage: 'Failed to retrieve deleted colors'
    }
  );
}
```

## 🎯 Usage Examples

### 1. IPC Handler with Service Injection
```typescript
interface MyHandlerServices {
  colorService: ColorService;
  productService: ProductService;
}

export function registerMyHandlers(services?: Partial<MyHandlerServices>): void {
  const colorService = services?.colorService || ServiceLocator.getColorService();
  const productService = services?.productService || ServiceLocator.getProductService();

  registerSecureHandler(
    'my:operation',
    async (organizationId: string, data: any) => {
      // Use the injected services
      const colors = await colorService.getAll(organizationId);
      const products = await productService.getAll(organizationId);
      return { colors, products };
    },
    ipcMain
  );
}
```

### 2. Central Registration with Service Bundle
```typescript
// In main application startup
import { registerAllIpcHandlers } from './ipc/ipc-registry';

async function initializeApplication() {
  // Initialize services
  await ServiceLocator.initialize();
  
  // Option 1: Use ServiceLocator (services auto-injected)
  await registerAllIpcHandlers();
  
  // Option 2: Explicit service injection
  const serviceBundle = {
    colorService: ServiceLocator.getColorService(),
    productService: ServiceLocator.getProductService(),
    // ... other services
  };
  await registerAllIpcHandlers(serviceBundle);
}
```

### 3. Testing with Dependency Injection
```typescript
// Easy testing with mock services
describe('SoftDelete Handlers', () => {
  it('should handle color restoration', async () => {
    const mockColorService = {
      restore: jest.fn().mockResolvedValue(true),
      getSoftDeleted: jest.fn().mockResolvedValue([])
    } as jest.Mocked<ColorService>;

    // Inject mock services
    registerSoftDeleteHandlers({
      colorService: mockColorService
    });

    // Test the handler
    expect(mockColorService.restore).toHaveBeenCalled();
  });
});
```

## 📊 Service Registry

### Core Database Services
- `colorService: ColorService` - Color CRUD operations
- `productService: ProductService` - Product management
- `datasheetService: DatasheetService` - Datasheet operations
- `organizationService: OrganizationService` - Multi-tenant operations

### Import/Library Services
- `colorImportService: ColorImportService` - Color import operations
- `colorLibraryImportService: ColorLibraryImportService` - Library imports
- `colorLibraryQueryService: ColorLibraryQueryService` - Library queries

### Authentication Services
- `authenticationManager: AuthenticationManager`
- `sessionManager: SessionManager`
- `oauthService: OAuthService`

### System Services
- `database: Database` - SQLite database instance
- `logger: ILogger` - Application logger
- `syncSystem: SyncSystem` - Real-time synchronization

## 🔧 Configuration & Initialization

### Service Container Configuration
```typescript
await ServiceLocator.initialize({
  database: customDatabase,  // Optional: provide custom database
  config: {
    // Service-specific configuration
    zoho: { clientId: '...', clientSecret: '...' },
    supabase: { url: '...', anonKey: '...' }
  }
});
```

### Health Monitoring
```typescript
// Check service health
const health = await ServiceLocator.getHealthStatus();
console.log('Services healthy:', health.healthy);
console.log('Service details:', health.services);
```

## 🚀 Migration Strategy

### Phase 1: Enhance Service Container ✅ COMPLETED
- [x] Add all database services to service container
- [x] Add ServiceLocator getters for all services
- [x] Create central IPC registry with DI

### Phase 2: Migrate High-Priority Handlers
1. **soft-delete.ipc.ts** → Use new DI pattern
2. **integrity.ipc.ts** → Add service injection
3. **audit.ipc.ts** → Standardize service usage

### Phase 3: Migrate Remaining Handlers
1. **color-library.ipc.ts** → Add error handling + DI
2. **settings.ipc.ts** → Extract service dependencies
3. **sync-handlers.ts** → Simplify service access

## ✅ Benefits Achieved

1. **Singleton Pattern**: All services are now singletons with proper lifecycle
2. **Testability**: Easy to inject mock services for testing
3. **Consistency**: Centralized service management across the app
4. **Performance**: No duplicate service instances
5. **Maintainability**: Clear separation of concerns
6. **Type Safety**: Full TypeScript support with proper service types

## 🎯 Handler Migration Checklist

When migrating an IPC handler file:

- [ ] Remove `new Service(db)` instantiation
- [ ] Add service interface for type safety
- [ ] Accept services as optional parameter
- [ ] Use ServiceLocator as fallback
- [ ] Combine with universal IPC wrapper
- [ ] Add proper error handling
- [ ] Update registration in ipc-registry.ts
- [ ] Add tests with mock services

## 📝 Example Files

### Complete Examples
- `src/main/ipc/soft-delete-with-di.ipc.ts` - Fully migrated handler
- `src/main/ipc/ipc-registry.ts` - Central registration pattern
- `src/main/ipc/product.ipc.ts` - Already uses DI (reference implementation)

### Files to Migrate
- `src/main/ipc/soft-delete.ipc.ts` → Replace with DI version
- `src/main/ipc/integrity.ipc.ts` → Add service injection
- `src/main/ipc/audit.ipc.ts` → Standardize service usage

This dependency injection pattern establishes a robust, testable, and maintainable foundation for all IPC handlers while eliminating service instantiation anti-patterns.