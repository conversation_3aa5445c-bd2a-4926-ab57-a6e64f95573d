/**
 * @file middleware.ts
 * @description Router middleware integrations for ChromaSync existing infrastructure
 * 
 * This file provides seamless integration between the new IPC Router system
 * and ChromaSync's existing middleware components including:
 * - Organization context validation
 * - Authentication middleware
 * - Input validation with JSON schemas
 * - Rate limiting
 * - Error handling and response formatting
 */

import { IPCMiddleware /*, IPCRequest, IPCResponseHelper*/ } from './IPCRouter';
import { 
  validateOrganizationContext,
  getValidatedOrganizationId 
} from '../../middleware/organization-context.middleware';
import { 
  // IPCValidationMiddleware,
  ipcValidation,
  CommonSchemas 
} from '../middleware/IPCValidationMiddleware';
import { ServiceLocator } from '../../services/service-locator';

// ============================================================================
// ORGANIZATION MIDDLEWARE
// ============================================================================

/**
 * Middleware to require organization context
 */
export const requireOrganization: IPCMiddleware = async (req, _res, next) => {
  try {
    const validation = await validateOrganizationContext();
    
    if (!validation.isValid && validation.error) {
      throw new Error(validation.error.userMessage || 'Organization context required');
    }
    
    // Add organization ID to request
    req.organizationId = validation.organizationId!;
    next();
  } catch (error) {
    throw new Error(`Organization validation failed: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * Middleware to optionally include organization context
 */
export const optionalOrganization: IPCMiddleware = async (req, _res, next) => {
  try {
    // Try to get organization ID but don't fail if not available
    const organizationId = await getValidatedOrganizationId();
    if (organizationId) {
      req.organizationId = organizationId;
    }
    next();
  } catch (error) {
    // Continue without organization context
    next();
  }
};

// ============================================================================
// AUTHENTICATION MIDDLEWARE
// ============================================================================

/**
 * Middleware to require authentication
 */
export const requireAuth: IPCMiddleware = async (req, _res, next) => {
  try {
    const authService = ServiceLocator.getAuthenticationManager();
    
    if (!authService) {
      throw new Error('Authentication service not available');
    }
    
    const authState = await authService.getAuthState();
    
    if (!authState.isAuthenticated) {
      throw new Error('Authentication required');
    }
    
    // Add user information to request
    req.user = authState.user;
    req.isAuthenticated = true;
    
    next();
  } catch (error) {
    throw new Error(`Authentication failed: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * Middleware to optionally include authentication context
 */
export const optionalAuth: IPCMiddleware = async (req, _res, next) => {
  try {
    const authService = ServiceLocator.getAuthenticationManager();
    
    if (authService) {
      const authState = await authService.getAuthState();
      req.user = authState.user;
      req.isAuthenticated = authState.isAuthenticated;
    }
    
    next();
  } catch (error) {
    // Continue without authentication context
    req.isAuthenticated = false;
    next();
  }
};

// ============================================================================
// VALIDATION MIDDLEWARE
// ============================================================================

/**
 * Create validation middleware for specific schema
 */
export function validateRequest(_schema: any): IPCMiddleware {
  return async (req, _res, next) => {
    try {
      // Use existing IPCValidationMiddleware
      const validationResult = ipcValidation.validateRequest(
        req.event,
        req.channel,
        req.body
      );
      
      if (validationResult) {
        // Validation failed
        throw new Error(validationResult.error || 'Validation failed');
      }
      
      next();
    } catch (error) {
      throw new Error(`Validation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  };
}

/**
 * Pre-built validation middleware for common schemas
 */
export const validation = {
  organizationId: validateRequest(CommonSchemas.organizationId),
  colorId: validateRequest(CommonSchemas.colorId),
  productId: validateRequest(CommonSchemas.productId),
  
  /**
   * Custom validation middleware factory
   */
  custom: (schema: any) => validateRequest(schema)
};

// ============================================================================
// RATE LIMITING MIDDLEWARE
// ============================================================================

/**
 * Simple in-memory rate limiter
 */
class RateLimiter {
  private requests = new Map<string, { count: number; resetTime: number }>();
  
  isAllowed(key: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const entry = this.requests.get(key);
    
    if (!entry || now > entry.resetTime) {
      this.requests.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }
    
    if (entry.count >= maxRequests) {
      return false;
    }
    
    entry.count++;
    return true;
  }
  
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.requests.entries()) {
      if (now > entry.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

const rateLimiter = new RateLimiter();

// Clean up expired entries every 5 minutes
setInterval(() => rateLimiter.cleanup(), 5 * 60 * 1000);

/**
 * Rate limiting middleware factory
 */
export function rateLimit(maxRequests: number, windowMs: number): IPCMiddleware {
  return (req, _res, next) => {
    const key = `${req.event.sender.id}:${req.channel}`;
    
    if (!rateLimiter.isAllowed(key, maxRequests, windowMs)) {
      throw new Error('Rate limit exceeded. Please slow down your requests.');
    }
    
    next();
  };
}

// ============================================================================
// LOGGING MIDDLEWARE
// ============================================================================

/**
 * Request logging middleware
 */
export function logger(prefix: string = 'IPC'): IPCMiddleware {
  return (req, res, next) => {
    const start = Date.now();
    
    console.log(`[${prefix}] → ${req.method.toUpperCase()} ${req.path} [${req.metadata.requestId}]`);
    
    // Override response helpers to log responses
    const originalSuccess = res.success;
    const originalError = res.error;
    
    res.success = (data?: any, message?: string) => {
      const duration = Date.now() - start;
      console.log(`[${prefix}] ✅ ${req.method.toUpperCase()} ${req.path} [${req.metadata.requestId}] ${duration}ms`);
      return originalSuccess(data, message);
    };
    
    res.error = (error: any, message?: string) => {
      const duration = Date.now() - start;
      console.log(`[${prefix}] ❌ ${req.method.toUpperCase()} ${req.path} [${req.metadata.requestId}] ${duration}ms - ${error}`);
      return originalError(error, message);
    };
    
    next();
  };
}

// ============================================================================
// CORS MIDDLEWARE (for future web-based contexts)
// ============================================================================

/**
 * CORS middleware for web contexts
 */
export const cors: IPCMiddleware = (_req, _res, next) => {
  // Add CORS headers to response if needed
  // This is primarily for future compatibility
  next();
};

// ============================================================================
// ERROR HANDLING MIDDLEWARE
// ============================================================================

/**
 * Global error handling middleware
 * Should be added as the first global middleware
 */
export const errorHandler: IPCMiddleware = async (req, _res, next) => {
  try {
    next();
  } catch (error) {
    console.error(`[IPCRouter] Error in ${req.method.toUpperCase()} ${req.path}:`, error);
    
    // Don't throw here - let the router handle the error response
    throw error;
  }
};

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * Performance monitoring middleware
 */
export function performanceMonitor(slowThreshold: number = 1000): IPCMiddleware {
  return (req, _res, next) => {
    const start = Date.now();
    
    const originalNext = next;
    next = () => {
      const duration = Date.now() - start;
      
      if (duration > slowThreshold) {
        console.warn(`[Performance] Slow request: ${req.method.toUpperCase()} ${req.path} took ${duration}ms`);
      }
      
      // Add performance data to request metadata
      req.metadata.duration = duration;
      
      originalNext();
    };
    
    next();
  };
}

// ============================================================================
// DEVELOPMENT HELPERS
// ============================================================================

/**
 * Debug middleware for development
 */
export const debug: IPCMiddleware = (req, _res, next) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('[Debug] Request:', {
      channel: req.channel,
      method: req.method,
      path: req.path,
      params: req.params,
      body: req.body,
      organizationId: req.organizationId,
      user: req.user
    });
  }
  
  next();
};

// ============================================================================
// MIDDLEWARE FACTORY
// ============================================================================

/**
 * Middleware factory for common combinations
 */
export const middleware = {
  // Organization context
  requireOrganization,
  optionalOrganization,
  
  // Authentication
  requireAuth,
  optionalAuth,
  
  // Validation
  validation,
  
  // Rate limiting
  rateLimit,
  
  // Logging and monitoring
  logger,
  performanceMonitor,
  debug,
  
  // Error handling
  errorHandler,
  
  // Common middleware stacks
  standard: [
    logger('ChromaSync'),
    performanceMonitor(500),
    errorHandler,
    requireAuth,
    requireOrganization
  ],
  
  public: [
    logger('ChromaSync'),
    performanceMonitor(500),
    errorHandler
  ],
  
  system: [
    logger('System'),
    performanceMonitor(1000),
    errorHandler
  ]
};

export default middleware;