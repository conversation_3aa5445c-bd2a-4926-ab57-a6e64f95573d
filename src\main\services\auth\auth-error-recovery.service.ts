/**
 * @file auth-error-recovery.service.ts
 * @description Handles authentication error recovery, circuit breaking, and retry logic
 */

import { <PERSON><PERSON>er<PERSON>indow } from 'electron';
import { createSafeStore } from '../../utils/store-util';
import {
  LoggerFactory,
  /* logPerformance, logErrors, */ ILogger,
} from '../../utils/logger.service';
import {
  Result,
  success,
  failure,
  AsyncResult,
} from '../../../shared/types/result.types';
import { toError } from '../../../shared/types/type-guards';

export interface AuthAttempt {
  timestamp: number;
  error?: string;
  success: boolean;
  provider?: string;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  cooldownMinutes: number;
  recoveryAttempts: number;
  windowMinutes: number;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
}

export interface RecoveryResult {
  success: boolean;
  message: string;
  actionsPerformed: string[];
}

/**
 * Service result type for auth error recovery operations
 */
export type AuthRecoveryResult<T> = Result<T, AuthRecoveryError>;

/**
 * Auth recovery specific error type
 */
export interface AuthRecoveryError {
  readonly type:
    | 'CIRCUIT_BREAKER'
    | 'RATE_LIMIT'
    | 'RECOVERY_FAILED'
    | 'VALIDATION_ERROR';
  readonly message: string;
  readonly attempts?: number;
  readonly cooldownRemaining?: number;
}

export interface CircuitBreakerStatus {
  isOpen: boolean;
  failureCount: number;
  cooldownRemaining: number;
  nextRetryTime?: number;
  recentAttempts: number;
}

/**
 * Authentication error recovery service with circuit breaker pattern
 */
export class AuthErrorRecoveryService {
  private readonly logger: ILogger;
  private readonly store = createSafeStore<Record<string, any>>({
    name: 'auth-error-recovery',
  });

  // Circuit breaker state
  private authAttempts: AuthAttempt[] = [];
  private circuitOpen: boolean = false;
  private cooldownUntil: number = 0;
  private lastRecoveryAttempt: number = 0;

  // Configuration - Reduced thresholds for debugging
  private readonly DEFAULT_CIRCUIT_CONFIG: CircuitBreakerConfig = {
    failureThreshold: 2, // Reduced from 3 for faster debugging
    cooldownMinutes: 1, // Reduced from 15 for faster debugging
    recoveryAttempts: 2,
    windowMinutes: 5,
  };

  private readonly DEFAULT_RETRY_CONFIG: RetryConfig = {
    maxRetries: 3,
    baseDelayMs: 1000,
    maxDelayMs: 30000,
    backoffMultiplier: 2,
  };

  // Storage keys
  private readonly STORAGE_KEYS = {
    CIRCUIT_CONFIG: 'auth.circuit.config',
    RETRY_CONFIG: 'auth.retry.config',
    ATTEMPTS: 'auth.attempts',
    CIRCUIT_STATE: 'auth.circuit.state',
  } as const;

  constructor(logger?: ILogger) {
    this.logger =
      logger ||
      LoggerFactory.getInstance().createLogger('AuthErrorRecoveryService');
    this.loadPersistedState();
  }

  /**
   * Record an authentication attempt
   */
  recordAuthAttempt(
    success: boolean,
    error?: string,
    provider: string = 'google'
  ): void {
    const attempt: AuthAttempt = {
      timestamp: Date.now(),
      error,
      success,
      provider,
    };

    this.authAttempts.push(attempt);

    // Clean up old attempts outside the monitoring window
    this.cleanupOldAttempts();

    // Persist state
    this.persistState();

    this.logger.info('Auth attempt recorded', {
      success,
      errorMessage: error || 'none',
      provider,
      totalAttempts: this.authAttempts.length,
      operation: 'recordAuthAttempt',
    });

    // Check circuit breaker conditions
    if (!success) {
      this.checkCircuitBreaker();
    } else {
      // Reset circuit breaker on successful authentication
      this.resetCircuitBreaker();
    }
  }

  /**
   * Check if authentication should be blocked (circuit breaker is open)
   */
  isAuthenticationBlocked(): boolean {
    // Check if circuit is manually opened
    if (this.circuitOpen) {
      // Check if cooldown has expired
      if (Date.now() >= this.cooldownUntil) {
        this.logger.info(
          'Circuit breaker cooldown expired, allowing authentication'
        );
        this.resetCircuitBreaker();
        return false;
      }
      return true;
    }

    return false;
  }

  /**
   * Get circuit breaker status
   */
  getCircuitBreakerStatus(): CircuitBreakerStatus {
    // Circuit breaker config available but not used in current implementation
    const recentAttempts = this.getRecentFailures();
    const cooldownRemaining = this.circuitOpen
      ? Math.max(0, this.cooldownUntil - Date.now())
      : 0;

    return {
      isOpen: this.circuitOpen,
      failureCount: recentAttempts.length,
      cooldownRemaining,
      nextRetryTime: this.circuitOpen ? this.cooldownUntil : undefined,
      recentAttempts: this.authAttempts.length,
    };
  }

  /**
   * Get time remaining until authentication is allowed again
   */
  getCooldownRemainingMinutes(): number {
    if (!this.circuitOpen) {
      return 0;
    }

    const remaining = this.cooldownUntil - Date.now();
    return Math.ceil(remaining / (60 * 1000));
  }

  /**
   * Attempt automatic recovery from authentication issues
   * TODO: Re-enable decorators when decorator signatures are fixed
   */
  // @logPerformance(LoggerFactory.getInstance().createLogger('AuthErrorRecoveryService'), 'attemptRecovery')
  // @logErrors(LoggerFactory.getInstance().createLogger('AuthErrorRecoveryService'))
  async attemptRecovery(): Promise<RecoveryResult> {
    this.logger.info('Starting authentication recovery', {
      operation: 'attemptRecovery',
    });

    const actionsPerformed: string[] = [];

    try {
      // Step 1: Clear authentication storage
      await this.clearAuthStorage();
      actionsPerformed.push('Cleared authentication storage');

      // Step 2: Reset circuit breaker
      this.resetCircuitBreaker();
      actionsPerformed.push('Reset circuit breaker');

      // Step 3: Clear attempt history
      this.clearAttemptHistory();
      actionsPerformed.push('Cleared attempt history');

      // Step 4: Notify windows about recovery
      this.notifyRecovery();
      actionsPerformed.push('Notified UI components');

      this.logger.info('Authentication recovery completed', {
        actionsPerformed,
        operation: 'attemptRecovery',
      });

      return {
        success: true,
        message:
          'Authentication system has been reset. Please try signing in again.',
        actionsPerformed,
      };
    } catch (error) {
      this.logger.error('Recovery failed', error as Error, {
        operation: 'attemptRecovery',
      });

      return {
        success: false,
        message:
          'Failed to recover authentication system. Please restart the application.',
        actionsPerformed,
      };
    }
  }

  /**
   * Execute authentication operation with retry logic
   * TODO: Re-enable decorator when decorator signatures are fixed
   */
  // @logPerformance(LoggerFactory.getInstance().createLogger('AuthErrorRecoveryService'), 'executeWithRetry')
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string = 'auth_operation'
  ): Promise<T> {
    const config = this.getRetryConfig();
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
      try {
        this.logger.debug('Executing auth operation', {
          operationName,
          attempt,
          maxRetries: config.maxRetries,
          operation: 'executeWithRetry',
        });

        const result = await operation();

        // Record successful attempt
        this.recordAuthAttempt(true, undefined);

        this.logger.info('Auth operation succeeded', {
          operationName,
          attempt,
          operation: 'executeWithRetry',
        });

        return result;
      } catch (error) {
        lastError = error as Error;
        const errorMessage = lastError.message;

        this.logger.warn('Auth operation failed', {
          operationName,
          attempt,
          error: lastError,
          operation: 'executeWithRetry',
        });

        // Record failed attempt
        this.recordAuthAttempt(false, errorMessage);

        // Don't retry on final attempt
        if (attempt === config.maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.baseDelayMs * Math.pow(config.backoffMultiplier, attempt - 1),
          config.maxDelayMs
        );

        this.logger.debug('Waiting before retry', {
          operationName,
          attempt,
          delayMs: delay,
          operation: 'executeWithRetry',
        });

        await this.sleep(delay);
      }
    }

    this.logger.error('Auth operation failed after all retries', lastError, {
      operationName,
      maxRetries: config.maxRetries,
      finalError: lastError?.message,
      operation: 'executeWithRetry',
    });

    throw (
      lastError ||
      toError(`${operationName} failed after ${config.maxRetries} retries`)
    );
  }

  /**
   * Configure circuit breaker settings
   */
  configureCircuitBreaker(config: Partial<CircuitBreakerConfig>): void {
    const currentConfig = this.getCircuitBreakerConfig();
    const newConfig = { ...currentConfig, ...config };

    this.store.set(this.STORAGE_KEYS.CIRCUIT_CONFIG, newConfig);

    this.logger.info('Circuit breaker configuration updated', {
      config: newConfig,
      operation: 'configureCircuitBreaker',
    });
  }

  /**
   * Configure retry settings
   */
  configureRetry(config: Partial<RetryConfig>): void {
    const currentConfig = this.getRetryConfig();
    const newConfig = { ...currentConfig, ...config };

    this.store.set(this.STORAGE_KEYS.RETRY_CONFIG, newConfig);

    this.logger.info('Retry configuration updated', {
      config: newConfig,
      operation: 'configureRetry',
    });
  }

  /**
   * Get authentication error statistics
   */
  getErrorStatistics(): {
    totalAttempts: number;
    successfulAttempts: number;
    failedAttempts: number;
    recentFailures: number;
    errorFrequency: Record<string, number>;
    averageTimeBetweenFailures: number;
  } {
    const total = this.authAttempts.length;
    const successful = this.authAttempts.filter(a => a.success).length;
    const failed = total - successful;
    const recentFailures = this.getRecentFailures().length;

    // Count error types
    const errorFrequency: Record<string, number> = {};
    this.authAttempts.forEach(attempt => {
      if (!attempt.success && attempt.error) {
        const errorType = this.categorizeError(attempt.error);
        errorFrequency[errorType] = (errorFrequency[errorType] || 0) + 1;
      }
    });

    // Calculate average time between failures
    const failures = this.authAttempts.filter(a => !a.success);
    let averageTimeBetweenFailures = 0;
    if (failures.length > 1) {
      const times = failures.map(f => f.timestamp).sort();
      const intervals = times.slice(1).map((time, i) => time - (times[i] ?? 0));
      averageTimeBetweenFailures =
        intervals.reduce((sum, interval) => sum + interval, 0) /
        intervals.length;
    }

    return {
      totalAttempts: total,
      successfulAttempts: successful,
      failedAttempts: failed,
      recentFailures,
      errorFrequency,
      averageTimeBetweenFailures,
    };
  }

  /**
   * Reset authentication state and circuit breaker (public method)
   */
  resetAuthenticationState(): void {
    this.logger.info('Resetting authentication state', {
      operation: 'resetAuthenticationState',
    });

    // Reset circuit breaker
    this.resetCircuitBreaker();

    // Clear attempt history
    this.clearAttemptHistory();

    // Persist the reset state
    this.persistState();

    this.logger.info('Authentication state reset completed');
  }

  /**
   * Execute authentication operation with retry logic using Result pattern
   */
  async executeWithRetryResult<T>(
    operation: () => Promise<T>,
    operationName: string = 'auth_operation'
  ): AsyncResult<T, AuthRecoveryError> {
    try {
      const result = await this.executeWithRetry(operation, operationName);
      return success(result);
    } catch (error) {
      return failure({
        type: 'RECOVERY_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
        attempts: this.authAttempts.length,
      });
    }
  }

  /**
   * Check if authentication is blocked using Result pattern
   */
  isAuthenticationBlockedResult(): AuthRecoveryResult<boolean> {
    try {
      if (this.circuitOpen) {
        if (Date.now() >= this.cooldownUntil) {
          this.logger.info(
            'Circuit breaker cooldown expired, allowing authentication'
          );
          this.resetCircuitBreaker();
          return success(false);
        }

        return failure({
          type: 'CIRCUIT_BREAKER',
          message:
            'Authentication temporarily blocked due to repeated failures',
          cooldownRemaining: this.cooldownUntil - Date.now(),
        });
      }

      return success(false);
    } catch (error) {
      return failure({
        type: 'VALIDATION_ERROR',
        message: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Attempt recovery using Result pattern
   */
  async attemptRecoveryResult(): AsyncResult<
    RecoveryResult,
    AuthRecoveryError
  > {
    try {
      const result = await this.attemptRecovery();
      return success(result);
    } catch (error) {
      return failure({
        type: 'RECOVERY_FAILED' as const,
        message: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.logger.debug('Cleaning up auth error recovery service');
    this.persistState();
  }

  // Private methods

  private checkCircuitBreaker(): void {
    const config = this.getCircuitBreakerConfig();
    const recentFailures = this.getRecentFailures();

    if (recentFailures.length >= config.failureThreshold) {
      this.openCircuitBreaker();
    }
  }

  private openCircuitBreaker(): void {
    const config = this.getCircuitBreakerConfig();

    this.circuitOpen = true;
    this.cooldownUntil = Date.now() + config.cooldownMinutes * 60 * 1000;

    this.logger.warn('Circuit breaker opened due to repeated failures', {
      failureThreshold: config.failureThreshold,
      cooldownMinutes: config.cooldownMinutes,
      operation: 'openCircuitBreaker',
    });

    // Notify UI about circuit breaker activation
    this.notifyCircuitBreakerOpened();

    // Persist state
    this.persistState();
  }

  private resetCircuitBreaker(): void {
    if (this.circuitOpen) {
      this.logger.info('Circuit breaker reset - authentication re-enabled');
    }

    this.circuitOpen = false;
    this.cooldownUntil = 0;

    // Notify UI about circuit breaker reset
    if (this.circuitOpen) {
      this.notifyCircuitBreakerReset();
    }

    // Persist state
    this.persistState();
  }

  private getRecentFailures(): AuthAttempt[] {
    const config = this.getCircuitBreakerConfig();
    const windowMs = config.windowMinutes * 60 * 1000;
    const cutoff = Date.now() - windowMs;

    return this.authAttempts.filter(
      attempt => !attempt.success && attempt.timestamp > cutoff
    );
  }

  private cleanupOldAttempts(): void {
    const config = this.getCircuitBreakerConfig();
    const windowMs = config.windowMinutes * 60 * 1000;
    const cutoff = Date.now() - windowMs;

    const oldCount = this.authAttempts.length;
    this.authAttempts = this.authAttempts.filter(
      attempt => attempt.timestamp > cutoff
    );
    const newCount = this.authAttempts.length;

    if (oldCount > newCount) {
      this.logger.debug('Cleaned up old auth attempts', {
        removed: oldCount - newCount,
        remaining: newCount,
      });
    }
  }

  private clearAttemptHistory(): void {
    this.authAttempts = [];
    this.persistState();

    this.logger.info('Auth attempt history cleared');
  }

  private async clearAuthStorage(): Promise<void> {
    try {
      // Clear electron-store auth data (preserve config)
      const circuitConfig = this.store.get(this.STORAGE_KEYS.CIRCUIT_CONFIG);
      const retryConfig = this.store.get(this.STORAGE_KEYS.RETRY_CONFIG);

      // Clear auth-specific keys
      this.store.delete('chromasync-auth-session');
      this.store.delete('currentOrganizationId');
      this.store.delete(this.STORAGE_KEYS.ATTEMPTS);
      this.store.delete(this.STORAGE_KEYS.CIRCUIT_STATE);

      // Restore configs
      if (circuitConfig) {
        this.store.set(this.STORAGE_KEYS.CIRCUIT_CONFIG, circuitConfig);
      }
      if (retryConfig) {
        this.store.set(this.STORAGE_KEYS.RETRY_CONFIG, retryConfig);
      }

      this.logger.debug('Auth storage cleared');
    } catch (error) {
      this.logger.error('Failed to clear auth storage', error as Error);
      throw error;
    }
  }

  private notifyCircuitBreakerOpened(): void {
    const config = this.getCircuitBreakerConfig();

    this.notifyWindows('auth:circuit-breaker-opened', {
      cooldownMinutes: config.cooldownMinutes,
      cooldownUntil: this.cooldownUntil,
      failureThreshold: config.failureThreshold,
      windowMinutes: config.windowMinutes,
    });
  }

  private notifyCircuitBreakerReset(): void {
    this.notifyWindows('auth:circuit-breaker-reset', {});
  }

  private notifyRecovery(): void {
    this.notifyWindows('auth:recovery-completed', {
      timestamp: Date.now(),
    });
  }

  private notifyWindows(channel: string, data: any): void {
    try {
      BrowserWindow.getAllWindows().forEach(window => {
        if (!window.isDestroyed()) {
          window.webContents.send(channel, data);
        }
      });
    } catch (error) {
      this.logger.error('Failed to notify windows', error as Error, {
        channel,
      });
    }
  }

  private categorizeError(error: string): string {
    const errorLower = error.toLowerCase();

    if (errorLower.includes('network') || errorLower.includes('fetch')) {
      return 'network';
    }
    if (errorLower.includes('timeout')) {
      return 'timeout';
    }
    if (errorLower.includes('access_denied')) {
      return 'user_cancelled';
    }
    if (errorLower.includes('invalid_grant')) {
      return 'invalid_grant';
    }
    if (errorLower.includes('server_error')) {
      return 'server_error';
    }
    if (errorLower.includes('temporarily_unavailable')) {
      return 'service_unavailable';
    }

    return 'unknown';
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private persistState(): void {
    try {
      this.store.set(this.STORAGE_KEYS.ATTEMPTS, this.authAttempts);
      this.store.set(this.STORAGE_KEYS.CIRCUIT_STATE, {
        circuitOpen: this.circuitOpen,
        cooldownUntil: this.cooldownUntil,
        lastRecoveryAttempt: this.lastRecoveryAttempt,
      });
    } catch (error) {
      this.logger.error('Failed to persist state', error as Error);
    }
  }

  private loadPersistedState(): void {
    try {
      const attempts =
        (this.store.get(this.STORAGE_KEYS.ATTEMPTS) as AuthAttempt[]) || [];
      const state =
        (this.store.get(this.STORAGE_KEYS.CIRCUIT_STATE) as any) || {};

      this.authAttempts = attempts;
      this.circuitOpen = state.circuitOpen || false;
      this.cooldownUntil = state.cooldownUntil || 0;
      this.lastRecoveryAttempt = state.lastRecoveryAttempt || 0;

      // Check if cooldown has expired
      if (this.circuitOpen && Date.now() >= this.cooldownUntil) {
        this.resetCircuitBreaker();
      }

      // Clean up old attempts
      this.cleanupOldAttempts();

      this.logger.debug('Persisted state loaded', {
        attempts: this.authAttempts.length,
        circuitOpen: this.circuitOpen,
        cooldownRemaining: this.getCooldownRemainingMinutes(),
      });
    } catch (error) {
      this.logger.error('Failed to load persisted state', error as Error);
    }
  }

  private getCircuitBreakerConfig(): CircuitBreakerConfig {
    return (
      (this.store.get(
        this.STORAGE_KEYS.CIRCUIT_CONFIG
      ) as CircuitBreakerConfig) || this.DEFAULT_CIRCUIT_CONFIG
    );
  }

  private getRetryConfig(): RetryConfig {
    return (
      (this.store.get(this.STORAGE_KEYS.RETRY_CONFIG) as RetryConfig) ||
      this.DEFAULT_RETRY_CONFIG
    );
  }
}
