/**
 * @file colorCount.ts
 * @description Utility to count colors in each library
 */

import { getPantoneColors } from './pantoneColors';
import { getRalColors } from './ralColors';

/**
 * Count the colors in each library and log the results
 * For development and testing purposes
 */
export function countLibraryColors(): void {
  try {
    const pantoneColors = getPantoneColors() || [];
    const ralColors = getRalColors() || [];
    
    console.log('Color Library Statistics:');
    console.log(`Pantone colors: ${pantoneColors.length}`);
    console.log(`RAL colors: ${ralColors.length}`);
    console.log(`Total colors: ${pantoneColors.length + ralColors.length}`);
  } catch (error) {
    console.error('Error counting library colors:', error);
    console.log('Color Library Statistics: Error loading libraries');
  }
}

// Export function for use in console
export default countLibraryColors; 