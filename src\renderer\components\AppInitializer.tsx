/**
 * @file AppInitializer.refactored.tsx
 * @description Refactored AppInitializer using state machine pattern for better maintainability
 * Replaces the complex, race-condition-prone original implementation
 */

import React from 'react';
import { Loader2, AlertCircle } from 'lucide-react';
import { useAppInitialization } from '../hooks/useAppInitialization';
import { SyncAuth } from './Sync/SyncAuth';
import { OrganizationSetup } from './organization/OrganizationSetup';
import { OrganizationSelection } from './organization/OrganizationSelection';
import SplashScreen from './SplashScreen';

interface AppInitializerProps {
  children: React.ReactNode;
}

/**
 * LoadingScreen component for consistent loading UI
 */
const LoadingScreen: React.FC<{ message: string }> = ({ message }) => (
  <div className="flex items-center justify-center h-screen bg-ui-background-primary">
    <div className="flex flex-col items-center space-y-4">
      <Loader2 className="w-8 h-8 animate-spin text-brand-primary" />
      <p className="text-ui-foreground-secondary">{message}</p>
    </div>
  </div>
);

/**
 * ErrorScreen component for error states
 */
const ErrorScreen: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (
  <div className="flex items-center justify-center h-screen bg-ui-background-primary">
    <div className="flex flex-col items-center space-y-4 text-center max-w-md p-6">
      <AlertCircle className="w-12 h-12 text-red-500" />
      <h2 className="text-xl font-semibold text-ui-foreground-primary">
        Initialization Failed
      </h2>
      <p className="text-ui-foreground-secondary">
        {error}
      </p>
      <button
        onClick={onRetry}
        className="px-4 py-2 bg-brand-primary text-white rounded hover:bg-brand-secondary transition-colors"
      >
        Try Again
      </button>
    </div>
  </div>
);

/**
 * AuthScreen component for authentication
 */
const AuthScreen: React.FC<{ onSuccess: () => void }> = ({ onSuccess }) => (
  <div className="flex items-center justify-center h-screen bg-ui-background-primary">
    <div className="w-full max-w-md p-8 bg-ui-background-secondary rounded-lg shadow-xl">
      <h1 className="text-2xl font-bold text-center mb-6 text-ui-foreground-primary">
        Welcome to ChromaSync
      </h1>
      <SyncAuth onSuccess={onSuccess} />
    </div>
  </div>
);

/**
 * Refactored AppInitializer using state machine pattern
 * This eliminates race conditions and follows React's declarative model
 */
export const AppInitializer: React.FC<AppInitializerProps> = ({ children }) => {
  const {
    state,
    error,
    isAuthenticated,
    user,
    currentOrganization,
    organizations,
    handlers,
    isReady,
    startupReady,
    stage,
    progress,
    debugPanelOpen,
    setDebugPanelOpen
  } = useAppInitialization();

  // Development-only logging - removed for performance
  if (process.env.NODE_ENV === 'development' && process.env.DEBUG_INIT) {
    console.log('[AppInitializer] Current state:', state, {
      isAuthenticated,
      hasUser: !!user,
      hasCurrentOrg: !!currentOrganization,
      orgCount: organizations.length,
      isReady,
      stage,
      progress
    });
  }

  // Show splash screen only while startup manager is loading OR state machine is initializing
  // Once startup is ready AND state machine has moved past initializing, show appropriate screen
  if (!startupReady || state === 'initializing') {
    return <SplashScreen stage={stage} progress={progress} />;
  }

  // Once startup manager is ready OR state machine has moved past initializing, 
  // render based on state machine state for auth/org flows
  switch (state) {
    case 'checking-auth':
      return <LoadingScreen message="Checking authentication..." />;

    case 'needs-auth':
      return <AuthScreen onSuccess={handlers.onAuthSuccess} />;

    case 'loading-organizations':
      return <LoadingScreen message="Loading organizations..." />;

    case 'needs-org-setup':
      return (
        <div className="flex items-center justify-center h-screen bg-ui-background-primary">
          <OrganizationSetup 
            user={user as any || null} 
            onComplete={handlers.onOrgSetupComplete}
          />
        </div>
      );

    case 'needs-org-selection':
      return (
        <div className="flex items-center justify-center min-h-screen bg-ui-background-primary p-4">
          <OrganizationSelection
            organizations={organizations}
            onSelect={handlers.onOrgSelected}
          />
        </div>
      );

    case 'syncing-data':
      return <LoadingScreen message="Syncing data..." />;

    case 'error':
      return (
        <ErrorScreen 
          error={error || 'An unknown error occurred'} 
          onRetry={handlers.onRetry} 
        />
      );

    case 'ready':
      // All initialization complete - render the main app
      // Development-only logging for debugging
      if (process.env.NODE_ENV === 'development' && process.env.DEBUG_INIT) {
        console.log('[AppInitializer] ✅ Ready - Rendering main app');
      }
      
      return (
        <div style={{ width: '100%', height: '100vh' }}>
          <div style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
            {children}
          </div>
          {/* Debug panel access for development */}
          {process.env.NODE_ENV === 'development' && debugPanelOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
              <div className="bg-white p-4 rounded-lg max-w-md">
                <h3 className="text-lg font-semibold mb-2">Debug Panel</h3>
                <p>State: {state}</p>
                <p>Stage: {stage}</p>
                <p>Progress: {progress}%</p>
                <button 
                  onClick={() => setDebugPanelOpen(false)}
                  className="mt-2 px-4 py-2 bg-blue-500 text-white rounded"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      );

    default:
      // This should never happen with proper TypeScript
      console.error('[AppInitializer] Unknown state:', state);
      return (
        <ErrorScreen 
          error={`Unknown initialization state: ${state}`} 
          onRetry={handlers.onRetry} 
        />
      );
  }
};

/**
 * Export the refactored component as default to replace the original
 * This maintains backward compatibility while providing the improved implementation
 */
export default AppInitializer;