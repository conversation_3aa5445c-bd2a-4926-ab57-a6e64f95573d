/**
 * Native module loader for better-sqlite3
 * Handles the dynamic loading of the native module in Electron
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs';

/**
 * Load better-sqlite3 with proper path resolution
 */
export function loadBetterSqlite3(): any {
  try {
    // Since we're using CommonJS now, use require directly
    const Database = require('better-sqlite3');
    console.log('[DB] Successfully loaded better-sqlite3 with require');
    
    // Test that we can actually create a database instance
    try {
      const testDb = new Database(':memory:');
      testDb.close();
      console.log('[DB] better-sqlite3 module is working correctly');
    } catch (testError) {
      console.error('[DB] better-sqlite3 module loaded but cannot create database:', testError);
      throw testError;
    }
    
    return Database;
  } catch (error) {
    console.error('[DB] Failed to load better-sqlite3 with require:', error);
    console.error('[DB] Error details:', {
      message: (error as Error).message,
      stack: (error as Error).stack,
      code: (error as any).code
    });
    
    // Try alternative module resolution
    const possiblePaths = [
      // Development paths
      path.join(process.cwd(), 'node_modules', 'better-sqlite3'),
      path.join(__dirname, '..', '..', '..', 'node_modules', 'better-sqlite3'),
      
      // Production paths  
      path.join(app.getAppPath(), 'node_modules', 'better-sqlite3'),
    ];
    
    for (const modulePath of possiblePaths) {
      try {
        if (fs.existsSync(modulePath)) {
          console.log(`[DB] Trying to load better-sqlite3 from: ${modulePath}`);
          const Database = require(modulePath);
          
          // Test the module works
          try {
            const testDb = new Database(':memory:');
            testDb.close();
            console.log('[DB] Successfully loaded and tested better-sqlite3 from path');
            return Database;
          } catch (testError) {
            console.error(`[DB] Module at ${modulePath} loads but cannot create database:`, testError);
            continue;
          }
        }
      } catch (pathError) {
        console.error(`[DB] Failed to load from ${modulePath}:`, pathError);
        // Continue to next path
      }
    }
    
    throw new Error('Could not load better-sqlite3 from any known location');
  }
}
