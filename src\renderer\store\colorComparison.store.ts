/**
 * @file colorComparison.store.ts
 * @description Zustand store for color comparison feature
 */

import { create } from 'zustand';
import { ColorEntry } from '../../shared/types/color.types';
import { 
  HarmonyType, 
  HarmonyOptions, 
  HSL 
} from '../../shared/types/colorComparison.types';
import { 
  hexToRgb, 
  rgbToHsl, 
  hslToRgb, 
  rgbToHex 
} from '../../shared/utils/color';

// For finding closest Pantone/RAL colors
import { useColorStore } from './color.store';

// Type for color library tabs
export type ColorLibraryTab = 'saved' | 'pantone' | 'ral';

export interface ColorComparisonState {
  // State
  comparisonColors: ColorEntry[];
  activeColorIndex: number | null;
  lastClickedColorId: string | null; // Track last clicked color
  isComparisonModalOpen: boolean;
  selectedHarmonyType: HarmonyType;
  harmonyOptions: HarmonyOptions;
  harmonyResults: ColorEntry[];
  selectedColorLibrary: ColorLibraryTab;
  
  // Multi-select state
  selectedColorIds: Set<string>; // For batch operations
  isMultiSelectMode: boolean;
  
  // Actions
  addColorToComparison: (color: ColorEntry) => void;
  addColor: (color: ColorEntry) => void;
  removeColorFromComparison: (index: number) => void;
  setActiveColorIndex: (index: number | null) => void;
  setLastClickedColorId: (id: string | null) => void; // New action
  clearComparison: () => void;
  toggleComparisonModal: () => void;
  setHarmonyType: (type: HarmonyType) => void;
  setHarmonyOptions: (options: Partial<HarmonyOptions>) => void;
  generateHarmonies: () => void;
  setSelectedColorLibrary: (tab: ColorLibraryTab) => void;
  
  // Multi-select actions
  toggleMultiSelectMode: () => void;
  toggleColorSelection: (colorId: string) => void;
  selectAllColors: (colors: ColorEntry[]) => void;
  clearSelection: () => void;
  addSelectedColorsToComparison: () => void;
}

export const useColorComparisonStore = create<ColorComparisonState>((set, get) => ({
  // Initial state
  comparisonColors: [],
  activeColorIndex: null,
  lastClickedColorId: null, // Initial value
  isComparisonModalOpen: false,
  selectedHarmonyType: 'analogous',
  harmonyOptions: {
    count: 4,
    angle: 30
  },
  harmonyResults: [],
  selectedColorLibrary: 'saved',
  
  // Multi-select state
  selectedColorIds: new Set<string>(),
  isMultiSelectMode: false,
  
  // Actions
  addColorToComparison: (color) => set((state) => {
    // Check if color already exists in comparison
    const exists = state.comparisonColors.some(c => c.id === color.id);
    if (exists) {return state;}
    
    // Limit to 10 colors
    if (state.comparisonColors.length >= 10) {return state;}
    
    return {
      comparisonColors: [...state.comparisonColors, color],
      // Auto-select this color if it's the first one
      activeColorIndex: state.activeColorIndex === null ? 0 : state.activeColorIndex,
      // Set as last clicked color
      lastClickedColorId: color.id
    };
  }),
  
  addColor: (color) => get().addColorToComparison(color),
  
  removeColorFromComparison: (index) => set((state) => {
    const newColors = [...state.comparisonColors];
    const removedColorId = newColors[index]?.id;
    newColors.splice(index, 1);
    
    // Update activeColorIndex if needed
    let newActiveIndex = state.activeColorIndex;
    if (newActiveIndex === index) {
      newActiveIndex = newColors.length > 0 ? 0 : null;
    } else if (newActiveIndex !== null && newActiveIndex > index) {
      newActiveIndex--;
    }
    
    // Update lastClickedColorId if needed
    const newLastClickedId = 
      state.lastClickedColorId === removedColorId 
        ? (newColors.length > 0 ? newColors[0].id : null) 
        : state.lastClickedColorId;
    
    return {
      comparisonColors: newColors,
      activeColorIndex: newActiveIndex,
      lastClickedColorId: newLastClickedId
    };
  }),
  
  setActiveColorIndex: (index) => set((state) => {
    // Update lastClickedColorId when activeColorIndex changes
    const newLastClickedId = index !== null && state.comparisonColors[index] 
      ? state.comparisonColors[index].id 
      : state.lastClickedColorId;
    
    return { 
      activeColorIndex: index,
      lastClickedColorId: newLastClickedId
    };
  }),
  
  setLastClickedColorId: (id) => set((state) => {
    // Also update activeColorIndex if the color is in the comparison list
    const newActiveIndex = id !== null 
      ? state.comparisonColors.findIndex(c => c.id === id)
      : state.activeColorIndex;
    
    return { 
      lastClickedColorId: id,
      activeColorIndex: newActiveIndex !== -1 ? newActiveIndex : state.activeColorIndex
    };
  }),
  
  clearComparison: () => set({ 
    comparisonColors: [],
    activeColorIndex: null,
    lastClickedColorId: null,
    harmonyResults: []
  }),
  
  toggleComparisonModal: () => set((state) => ({ 
    isComparisonModalOpen: !state.isComparisonModalOpen 
  })),
  
  setHarmonyType: (type) => set({ selectedHarmonyType: type }),
  
  setHarmonyOptions: (options) => set((state) => ({
    harmonyOptions: { ...state.harmonyOptions, ...options }
  })),
  
  setSelectedColorLibrary: (tab) => set({ selectedColorLibrary: tab }),
  
  generateHarmonies: () => set((state) => {
    // Get either the active color or the last clicked color
    let baseColorIndex = state.activeColorIndex;
    
    // If no active index but we have a lastClickedColorId, try to find it
    if (baseColorIndex === null && state.lastClickedColorId) {
      baseColorIndex = state.comparisonColors.findIndex(c => c.id === state.lastClickedColorId);
    }
    
    // If still no valid index, use the first color if available
    if (baseColorIndex === null || baseColorIndex === -1) {
      baseColorIndex = state.comparisonColors.length > 0 ? 0 : null;
    }
    
    if (baseColorIndex === null || !state.comparisonColors[baseColorIndex]) {
      return { harmonyResults: [] };
    }
    
    const baseColor = state.comparisonColors[baseColorIndex];
    const baseHex = baseColor.hex;
    const baseRgb = hexToRgb(baseHex);
    
    // Ensure RGB conversion was successful
    if (!baseRgb) {
      return { harmonyResults: [] };
    }
    
    const baseHsl = rgbToHsl(baseRgb);
    const count = state.harmonyOptions.count || 4;
    const angle = state.harmonyOptions.angle || 30;
    
    // Generate harmony colors based on type
    let harmonies: ColorEntry[] = [];
    
    switch(state.selectedHarmonyType) {
      case 'complementary':
        harmonies.push(generateComplementaryColor(baseColor, baseHsl));
        break;
        
      case 'analogous':
        harmonies.push(...generateAnalogousColors(baseColor, baseHsl, count, angle));
        break;
        
      case 'triadic':
        harmonies.push(...generateTriadicColors(baseColor, baseHsl));
        break;
        
      case 'tetradic':
        harmonies.push(...generateTetradicColors(baseColor, baseHsl));
        break;
        
      case 'splitComplementary':
        harmonies.push(...generateSplitComplementaryColors(baseColor, baseHsl, angle));
        break;
        
      case 'monochromatic':
        harmonies.push(...generateMonochromaticColors(baseColor, baseHsl, count));
        break;
        
      case 'shades':
        harmonies.push(...generateShades(baseColor, baseHsl, count));
        break;
        
      case 'tints':
        harmonies.push(...generateTints(baseColor, baseHsl, count));
        break;
        
      case 'compound':
        harmonies.push(...generateCompoundColors(baseColor, baseHsl));
        break;
    }
    
    // Find the closest Pantone/RAL colors for each harmony color
    harmonies = findClosestLibraryColors(harmonies, state.selectedColorLibrary);
    
    return { 
      harmonyResults: harmonies,
      // Update activeColorIndex if it was derived from lastClickedColorId
      activeColorIndex: baseColorIndex
    };
  }),

  // Multi-select actions
  toggleMultiSelectMode: () => set((state) => ({
    isMultiSelectMode: !state.isMultiSelectMode,
    selectedColorIds: new Set() // Clear selection when toggling mode
  })),

  toggleColorSelection: (colorId) => set((state) => {
    const newSelection = new Set(state.selectedColorIds);
    if (newSelection.has(colorId)) {
      newSelection.delete(colorId);
    } else {
      newSelection.add(colorId);
    }
    return { selectedColorIds: newSelection };
  }),

  selectAllColors: (colors) => set(() => ({
    selectedColorIds: new Set(colors.map(c => c.id))
  })),

  clearSelection: () => set(() => ({
    selectedColorIds: new Set()
  })),

  addSelectedColorsToComparison: () => set((state) => {
    const { selectedColorIds, comparisonColors } = state;
    const colorStore = useColorStore.getState();
    
    // Get all available colors from current library
    let availableColors: ColorEntry[] = [];
    switch (state.selectedColorLibrary) {
      case 'saved':
        availableColors = colorStore.colors || [];
        break;
      case 'pantone':
        availableColors = colorStore.pantoneColors || [];
        // Auto-load pantone colors if empty
        if (availableColors.length === 0) {
          colorStore.loadPantoneColors().catch(console.error);
        }
        break;
      case 'ral':
        availableColors = colorStore.ralColors || [];
        // Auto-load RAL colors if empty
        if (availableColors.length === 0) {
          colorStore.loadRalColors().catch(console.error);
        }
        break;
    }

    // Find colors by selected IDs
    const colorsToAdd = availableColors.filter(color => 
      selectedColorIds.has(color.id) && 
      !comparisonColors.some(existing => existing.id === color.id)
    );

    // Limit total colors to 10
    const totalNewColors = Math.min(colorsToAdd.length, 10 - comparisonColors.length);
    const newColors = colorsToAdd.slice(0, totalNewColors);

    return {
      comparisonColors: [...comparisonColors, ...newColors],
      selectedColorIds: new Set(), // Clear selection after adding
      isMultiSelectMode: false, // Exit multi-select mode
      activeColorIndex: comparisonColors.length === 0 && newColors.length > 0 ? 0 : state.activeColorIndex
    };
  })
}));

// New helper function to find closest library colors
function findClosestLibraryColors(colors: ColorEntry[], library: ColorLibraryTab = 'pantone'): ColorEntry[] {
  // Get the appropriate color library
  const colorStore = useColorStore.getState();
  const libraryColors = library === 'ral' ? (colorStore.ralColors || []) : (colorStore.pantoneColors || []);
  
  // Auto-load colors if empty
  if (libraryColors.length === 0) {
    if (library === 'ral') {
      colorStore.loadRalColors().catch(console.error);
    } else {
      colorStore.loadPantoneColors().catch(console.error);
    }
    return colors; // Return original colors while loading
  }
  
  return colors.map(color => {
    // Calculate color distance to each library color
    const targetRgb = hexToRgb(color.hex);
    if (!targetRgb) {return color;}
    
    let closestColor = libraryColors[0];
    let minDistance = Number.MAX_VALUE;
    
    libraryColors.forEach(libColor => {
      const libRgb = hexToRgb(libColor.hex);
      if (!libRgb) {return;}
      
      // Simple Euclidean distance in RGB space
      const distance = Math.sqrt(
        Math.pow(targetRgb.r - libRgb.r, 2) +
        Math.pow(targetRgb.g - libRgb.g, 2) +
        Math.pow(targetRgb.b - libRgb.b, 2)
      );
      
      if (distance < minDistance) {
        minDistance = distance;
        closestColor = libColor;
      }
    });
    
    // Return the closest library color but keep the harmony name
    return {
      ...closestColor,
      id: `harmony-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      product: `${color.product}`,
      notes: `${closestColor.code || closestColor.product} - Harmony color based on ${color.product}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  });
}

// Helper functions for generating color harmonies

function generateComplementaryColor(base: ColorEntry, baseHsl: HSL): ColorEntry {
  const h = (baseHsl.h + 180) % 360;
  
  return createHarmonyColor(base, 'Complementary', h, baseHsl.s, baseHsl.l);
}

function generateAnalogousColors(base: ColorEntry, baseHsl: HSL, count: number, angle: number): ColorEntry[] {
  const colors: ColorEntry[] = [];
  
  // Generate colors at +/- angles
  const halfCount = Math.floor(count / 2);
  
  for (let i = 1; i <= halfCount; i++) {
    // Colors with negative angle (counter-clockwise)
    const h1 = (baseHsl.h - (i * angle) + 360) % 360;
    colors.push(createHarmonyColor(base, `Analogous -${i}`, h1, baseHsl.s, baseHsl.l));
    
    // Colors with positive angle (clockwise)
    const h2 = (baseHsl.h + (i * angle)) % 360;
    colors.push(createHarmonyColor(base, `Analogous +${i}`, h2, baseHsl.s, baseHsl.l));
  }
  
  // If count is odd, add one more in the positive direction
  if (count % 2 !== 0) {
    const h = (baseHsl.h + (halfCount + 1) * angle) % 360;
    colors.push(createHarmonyColor(base, `Analogous +${halfCount + 1}`, h, baseHsl.s, baseHsl.l));
  }
  
  return colors;
}

function generateTriadicColors(base: ColorEntry, baseHsl: HSL): ColorEntry[] {
  const colors: ColorEntry[] = [];
  
  // Triadic colors are 120° apart
  const h1 = (baseHsl.h + 120) % 360;
  const h2 = (baseHsl.h + 240) % 360;
  
  colors.push(createHarmonyColor(base, 'Triadic 1', h1, baseHsl.s, baseHsl.l));
  colors.push(createHarmonyColor(base, 'Triadic 2', h2, baseHsl.s, baseHsl.l));
  
  return colors;
}

function generateTetradicColors(base: ColorEntry, baseHsl: HSL): ColorEntry[] {
  const colors: ColorEntry[] = [];
  
  // Tetradic (rectangle) colors form a rectangle in the color wheel
  const h1 = (baseHsl.h + 90) % 360;
  const h2 = (baseHsl.h + 180) % 360;
  const h3 = (baseHsl.h + 270) % 360;
  
  colors.push(createHarmonyColor(base, 'Tetradic 1', h1, baseHsl.s, baseHsl.l));
  colors.push(createHarmonyColor(base, 'Tetradic 2', h2, baseHsl.s, baseHsl.l));
  colors.push(createHarmonyColor(base, 'Tetradic 3', h3, baseHsl.s, baseHsl.l));
  
  return colors;
}

function generateSplitComplementaryColors(base: ColorEntry, baseHsl: HSL, angle: number): ColorEntry[] {
  const colors: ColorEntry[] = [];
  
  // Split complementary uses the complement and colors on either side
  const complementHue = (baseHsl.h + 180) % 360;
  const h1 = (complementHue - angle + 360) % 360;
  const h2 = (complementHue + angle) % 360;
  
  colors.push(createHarmonyColor(base, 'Split Comp 1', h1, baseHsl.s, baseHsl.l));
  colors.push(createHarmonyColor(base, 'Split Comp 2', h2, baseHsl.s, baseHsl.l));
  
  return colors;
}

function generateMonochromaticColors(base: ColorEntry, baseHsl: HSL, count: number): ColorEntry[] {
  const colors: ColorEntry[] = [];
  
  // Monochromatic colors vary in saturation and lightness
  const satStep = (100 - baseHsl.s) / (count + 1);
  const lightStep = (100 - baseHsl.l) / (count + 1);
  
  for (let i = 1; i <= count; i++) {
    const newSat = Math.max(0, Math.min(100, baseHsl.s + (i * satStep * 0.7)));
    const newLight = Math.max(0, Math.min(100, baseHsl.l + (i * lightStep * 0.7) - 35));
    
    colors.push(createHarmonyColor(base, `Monochromatic ${i}`, baseHsl.h, newSat, newLight));
  }
  
  return colors;
}

function generateShades(base: ColorEntry, baseHsl: HSL, count: number): ColorEntry[] {
  const colors: ColorEntry[] = [];
  
  // Shades are darker versions of the same hue
  for (let i = 1; i <= count; i++) {
    const lightness = Math.max(0, baseHsl.l - (i * (baseHsl.l / (count + 1))));
    colors.push(createHarmonyColor(base, `Shade ${i}`, baseHsl.h, baseHsl.s, lightness));
  }
  
  return colors;
}

function generateTints(base: ColorEntry, baseHsl: HSL, count: number): ColorEntry[] {
  const colors: ColorEntry[] = [];
  
  // Tints are lighter versions of the same hue
  for (let i = 1; i <= count; i++) {
    const lightness = Math.min(100, baseHsl.l + (i * ((100 - baseHsl.l) / (count + 1))));
    colors.push(createHarmonyColor(base, `Tint ${i}`, baseHsl.h, baseHsl.s, lightness));
  }
  
  return colors;
}

function generateCompoundColors(base: ColorEntry, baseHsl: HSL): ColorEntry[] {
  const colors: ColorEntry[] = [];
  
  // Compound colors use complementary + adjacent hues
  const h1 = (baseHsl.h + 180) % 360; // Complement
  const h2 = (baseHsl.h + 30) % 360;  // Adjacent 1
  const h3 = (baseHsl.h - 30 + 360) % 360; // Adjacent 2
  
  colors.push(createHarmonyColor(base, 'Compound 1', h1, baseHsl.s, baseHsl.l));
  colors.push(createHarmonyColor(base, 'Compound 2', h2, baseHsl.s, baseHsl.l));
  colors.push(createHarmonyColor(base, 'Compound 3', h3, baseHsl.s, baseHsl.l));
  
  return colors;
}

// Helper to create a color entry for harmony
function createHarmonyColor(baseColor: ColorEntry, name: string, h: number, s: number, l: number): ColorEntry {
  // Convert HSL to RGB
  const rgb = hslToRgb({ h, s, l });
  
  // Convert RGB to HEX
  const hex = rgbToHex(rgb);
  
  return {
    id: `harmony-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    product: `${baseColor.product} ${name}`,
    name: baseColor.name,
    code: `${baseColor.code} ${name}`,
    hex,
    cmyk: `C${l}, M${s}, Y${h}, K10`, // Approximate CMYK values
    notes: `Generated harmony color based on ${baseColor.product} ${baseColor.code}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
} 