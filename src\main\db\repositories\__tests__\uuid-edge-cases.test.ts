/**
 * @file uuid-edge-cases.test.ts
 * @description Edge case tests for pure UUID architecture
 * 
 * Comprehensive testing of edge cases including:
 * - Malformed UUID handling
 * - Missing ID scenarios  
 * - UUID collision scenarios
 * - Cross-platform UUID compatibility
 * - Performance under stress conditions
 * - Boundary conditions and error recovery
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';
import { v4 as uuidv4, validate as isValidUUID } from 'uuid';
import { ColorRepository } from '../color.repository';
import { ProductRepository } from '../product.repository';
import { OrganizationRepository } from '../organization.repository';
import { NewColorEntry, UpdateColorEntry } from '../../../../shared/types/color.types';
import { NewProduct, UpdateProduct } from '../../../../shared/types/product.types';

describe.sequential('UUID Edge Case Tests', () => {
  let db: Database.Database;
  let colorRepo: ColorRepository;
  let productRepo: ProductRepository;
  let organizationRepo: OrganizationRepository;
  
  let testOrgId: string;
  let testUserId: string;

  beforeEach(() => {
    // Create in-memory SQLite database for testing
    db = new Database(':memory:');
    
    // Set up pure UUID schema (post-migration)
    setupUUIDSchema(db);
    
    // Create repository instances
    colorRepo = new ColorRepository(db);
    productRepo = new ProductRepository(db);
    organizationRepo = new OrganizationRepository(db);
    
    // Generate test UUIDs
    testOrgId = uuidv4();
    testUserId = uuidv4();
    
    // Create test organization
    setupTestOrganization(db, testOrgId, testUserId);
  });

  afterEach(() => {
    if (db && db.open) {
      try {
        db.close();
      } catch (error) {
        console.warn('Database close error:', error);
      }
    }
  });

  describe('Malformed UUID Handling', () => {
    const invalidUUIDs = [
      '', // Empty string
      'not-a-uuid', // Random string
      '123', // Number as string
      '550e8400-e29b-41d4-a716', // Too short (missing last segment)
      '550e8400-e29b-41d4-a716-************-extra', // Too long
      '550e8400-x29b-41d4-a716-************', // Invalid character
      '550e8400-e29b-51d4-a716-************', // Invalid version (5 instead of 4)
      '550e8400-e29b-41d4-c716-************', // Invalid variant (c instead of 8/9/a/b)
      'GGGGGGGG-GGGG-GGGG-GGGG-GGGGGGGGGGGG', // Invalid hex characters
      '550e8400e29b41d4a716************', // Missing dashes
      '550e8400-e29b-41d4-a716-44665544000', // Wrong length segments
      null as any, // Null value
      undefined as any, // Undefined value
      {} as any, // Object instead of string
      [] as any, // Array instead of string
      12345 as any // Number instead of string
    ];

    test.each(invalidUUIDs)('should handle invalid UUID: %s', (invalidUUID) => {
      // Test organization queries
      expect(() => {
        organizationRepo.findById(invalidUUID);
      }).toThrow();

      // Test color queries  
      expect(() => {
        colorRepo.findById(invalidUUID, testOrgId);
      }).toThrow();

      // Test product queries
      expect(() => {
        productRepo.findById(invalidUUID, testOrgId);
      }).toThrow();
    });

    test('should reject malformed UUIDs in insert operations', () => {
      const invalidOrgId = 'invalid-org-id';
      
      const colorData: NewColorEntry = {
        name: 'Test Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      // Should throw when trying to insert with invalid organization ID
      expect(() => {
        colorRepo.insert(colorData, invalidOrgId);
      }).toThrow();

      const productData: NewProduct = {
        name: 'Test Product',
        description: 'Test description'
      };

      expect(() => {
        productRepo.insert(productData, invalidOrgId, testUserId);
      }).toThrow();
    });

    test('should validate UUID format in relationship operations', () => {
      // Create valid entities first
      const productData: NewProduct = { name: 'Valid Product' };
      const colorData: NewColorEntry = {
        name: 'Valid Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      const validProductId = productRepo.insert(productData, testOrgId, testUserId);
      const validColorId = colorRepo.insert(colorData, testOrgId);

      // Test invalid UUIDs in relationship operations
      const invalidId = 'not-a-uuid';

      expect(() => {
        productRepo.addProductColor(invalidId, validColorId, testOrgId);
      }).toThrow();

      expect(() => {
        productRepo.addProductColor(validProductId, invalidId, testOrgId);
      }).toThrow();

      expect(() => {
        productRepo.getProductColors(invalidId, testOrgId);
      }).toThrow();
    });
  });

  describe('Missing ID Scenarios', () => {
    test('should handle queries for non-existent UUIDs gracefully', () => {
      const nonExistentUUID = uuidv4();

      // Should return null/empty for non-existent valid UUIDs
      expect(organizationRepo.findById(nonExistentUUID)).toBeNull();
      expect(colorRepo.findById(nonExistentUUID, testOrgId)).toBeNull();
      expect(productRepo.findById(nonExistentUUID, testOrgId)).toBeNull();

      // Should return empty arrays for collections
      expect(organizationRepo.findMembers(nonExistentUUID)).toEqual([]);
      expect(productRepo.getProductColors(nonExistentUUID, testOrgId)).toEqual([]);
    });

    test('should handle update operations on non-existent UUIDs', () => {
      const nonExistentUUID = uuidv4();

      const colorUpdates: UpdateColorEntry = {
        name: 'Updated Name',
        hex: '#00FF00'
      };

      const productUpdates: UpdateProduct = {
        name: 'Updated Product Name'
      };

      // Should return false for updates on non-existent records
      expect(colorRepo.update(nonExistentUUID, colorUpdates, testOrgId)).toBe(false);
      expect(productRepo.update(nonExistentUUID, productUpdates, testOrgId)).toBe(false);
    });

    test('should handle delete operations on non-existent UUIDs', () => {
      const nonExistentUUID = uuidv4();

      // Should return false for deletes on non-existent records
      expect(colorRepo.softDelete(nonExistentUUID, testOrgId)).toBe(false);
      expect(productRepo.softDelete(nonExistentUUID, testOrgId)).toBe(false);
    });

    test('should handle orphaned relationship cleanup', () => {
      // Create entities
      const productData: NewProduct = { name: 'Orphan Test Product' };
      const colorData: NewColorEntry = {
        name: 'Orphan Test Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      const productId = productRepo.insert(productData, testOrgId, testUserId);
      const colorId = colorRepo.insert(colorData, testOrgId);

      // Create relationship
      productRepo.addProductColor(productId, colorId, testOrgId);
      
      // Verify relationship exists
      let relationships = productRepo.getProductColors(productId, testOrgId);
      expect(relationships).toHaveLength(1);

      // Delete color (should clean up relationships)
      colorRepo.softDelete(colorId, testOrgId);

      // Verify relationship is cleaned up
      relationships = productRepo.getProductColors(productId, testOrgId);
      expect(relationships).toHaveLength(0);
    });
  });

  describe('UUID Collision and Uniqueness', () => {
    test('should prevent duplicate UUID insertion', () => {
      const duplicateId = uuidv4();

      // Create organization data with specific UUID
      const orgData1 = {
        external_id: duplicateId,
        name: 'First Organization',
        slug: 'first-org',
        plan: 'free' as const,
        settings: '{}',
        ownerId: testUserId
      };

      const orgData2 = {
        external_id: duplicateId,
        name: 'Second Organization',
        slug: 'second-org',
        plan: 'free' as const,
        settings: '{}',
        ownerId: testUserId
      };

      // First insert should succeed
      const firstId = organizationRepo.insert(orgData1);
      expect(firstId).toBe(duplicateId);

      // Second insert with same UUID should fail
      expect(() => {
        organizationRepo.insert(orgData2);
      }).toThrow();
    });

    test('should handle UUID generation failures gracefully', () => {
      // Mock UUID generation to return invalid UUID
      const originalUuidv4 = uuidv4;
      vi.mocked(uuidv4).mockReturnValue('invalid-uuid' as any);

      try {
        const colorData: NewColorEntry = {
          name: 'UUID Fail Test',
          hex: '#FF5733',
          source: 'USER',
          isLibrary: false
        };

        // Should handle UUID generation failure
        expect(() => {
          colorRepo.insert(colorData, testOrgId);
        }).toThrow();
      } finally {
        // Restore original function
        vi.mocked(uuidv4).mockImplementation(originalUuidv4);
      }
    });
  });

  describe('Cross-Platform UUID Compatibility', () => {
    test('should handle UUIDs from different platforms consistently', () => {
      // Test various UUID formats that might come from different systems
      const testCases = [
        'f47ac10b-58cc-4372-a567-0e02b2c3d479', // Standard lowercase
        'F47AC10B-58CC-4372-A567-0E02B2C3D479', // Uppercase
        'f47ac10b-58cc-4372-A567-0e02b2c3d479', // Mixed case
      ];

      testCases.forEach((testUUID, index) => {
        const orgData = {
          external_id: testUUID,
          name: `Cross Platform Org ${index}`,
          slug: `cross-platform-${index}`,
          plan: 'free' as const,
          settings: '{}',
          ownerId: testUserId
        };

        // Should handle all UUID case variations
        const createdId = organizationRepo.insert(orgData);
        expect(createdId).toBe(testUUID);

        // Should be able to retrieve with any case
        const retrieved = organizationRepo.findById(testUUID);
        expect(retrieved).toBeTruthy();
        expect(retrieved!.id).toBe(testUUID);
      });
    });

    test('should maintain UUID consistency across operations', () => {
      const testUUID = uuidv4();
      
      // Insert with specific UUID
      const colorData: NewColorEntry = {
        name: 'Consistency Test',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      // Use direct insert to control UUID
      db.prepare(`
        INSERT INTO colors (id, organization_id, source_id, name, display_name, hex, is_library, created_at, updated_at)
        VALUES (?, ?, 1, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `).run(testUUID, testOrgId, colorData.name, colorData.name, colorData.hex, colorData.isLibrary);

      // Verify UUID consistency across different queries
      const byId = colorRepo.findById(testUUID, testOrgId);
      const allColors = colorRepo.findAll(testOrgId);
      const foundColor = allColors.find(c => c.id === testUUID);

      expect(byId).toBeTruthy();
      expect(foundColor).toBeTruthy();
      expect(byId!.id).toBe(testUUID);
      expect(foundColor!.id).toBe(testUUID);
      expect(byId!.external_id).toBe(testUUID);
      expect(foundColor!.external_id).toBe(testUUID);
    });
  });

  describe('Performance Under Stress', () => {
    test('should handle rapid UUID operations without degradation', () => {
      const operationCount = 200;
      const startTime = Date.now();

      // Perform rapid operations
      for (let i = 0; i < operationCount; i++) {
        const colorData: NewColorEntry = {
          name: `Stress Test Color ${i}`,
          hex: `#${Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')}`,
          source: 'USER',
          isLibrary: false
        };

        const colorId = colorRepo.insert(colorData, testOrgId);
        expect(isValidUUID(colorId)).toBe(true);

        // Perform additional operations on the same UUID
        const retrieved = colorRepo.findById(colorId, testOrgId);
        expect(retrieved).toBeTruthy();

        const updated = colorRepo.update(colorId, { name: `Updated ${i}` }, testOrgId);
        expect(updated).toBe(true);
      }

      const operationTime = Date.now() - startTime;
      
      // Verify all colors were created
      const allColors = colorRepo.findAll(testOrgId);
      expect(allColors).toHaveLength(operationCount);

      // Performance should be reasonable (adjust threshold as needed)
      expect(operationTime).toBeLessThan(10000); // 10 seconds for 200 * 3 operations
      
      console.log(`Stress test completed: ${operationCount * 3} operations in ${operationTime}ms`);
    });

    test('should handle concurrent UUID access patterns', () => {
      const batchSize = 50;
      const batches = 3;
      
      // Simulate concurrent access by creating multiple entities rapidly
      const allIds: string[] = [];
      
      for (let batch = 0; batch < batches; batch++) {
        const batchIds: string[] = [];
        
        // Create batch rapidly
        for (let i = 0; i < batchSize; i++) {
          const productData: NewProduct = {
            name: `Concurrent Product ${batch}-${i}`,
            description: `Batch ${batch} item ${i}`
          };
          
          const productId = productRepo.insert(productData, testOrgId, testUserId);
          batchIds.push(productId);
          allIds.push(productId);
        }
        
        // Verify batch integrity
        expect(batchIds).toHaveLength(batchSize);
        batchIds.forEach(id => {
          expect(isValidUUID(id)).toBe(true);
        });
      }

      // Verify no UUID collisions
      const uniqueIds = new Set(allIds);
      expect(uniqueIds.size).toBe(allIds.length);

      // Verify all records can be retrieved
      allIds.forEach(id => {
        const product = productRepo.findById(id, testOrgId);
        expect(product).toBeTruthy();
        expect(product!.id).toBe(id);
      });
    });
  });

  describe('Boundary Conditions and Error Recovery', () => {
    test('should handle extremely long UUID-related data', () => {
      const longName = 'A'.repeat(1000); // Very long name
      
      const colorData: NewColorEntry = {
        name: longName,
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false,
        notes: 'B'.repeat(2000) // Very long notes
      };

      // Should handle long data gracefully
      const colorId = colorRepo.insert(colorData, testOrgId);
      expect(isValidUUID(colorId)).toBe(true);

      const retrieved = colorRepo.findById(colorId, testOrgId);
      expect(retrieved).toBeTruthy();
      expect(retrieved!.display_name).toBe(longName);
    });

    test('should recover from database connection issues', () => {
      // Create some data first
      const colorData: NewColorEntry = {
        name: 'Recovery Test',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      const colorId = colorRepo.insert(colorData, testOrgId);
      expect(isValidUUID(colorId)).toBe(true);

      // Simulate connection issue by closing database
      db.close();

      // Should handle gracefully when database is closed
      expect(() => {
        colorRepo.findById(colorId, testOrgId);
      }).toThrow();

      // Recreate database and verify resilience
      db = new Database(':memory:');
      setupUUIDSchema(db);
      setupTestOrganization(db, testOrgId, testUserId);
      
      // Create new repository with fresh connection
      colorRepo = new ColorRepository(db);
      
      // Should be able to create new records
      const newColorId = colorRepo.insert(colorData, testOrgId);
      expect(isValidUUID(newColorId)).toBe(true);
    });

    test('should handle transaction rollback scenarios', () => {
      const initialColorCount = colorRepo.findAll(testOrgId).length;
      
      // Start a transaction
      const transaction = db.transaction(() => {
        // Insert multiple colors
        for (let i = 0; i < 5; i++) {
          const colorData: NewColorEntry = {
            name: `Transaction Test ${i}`,
            hex: `#FF${i.toString().padStart(4, '0')}`,
            source: 'USER',
            isLibrary: false
          };
          colorRepo.insert(colorData, testOrgId);
        }
        
        // Force an error to trigger rollback
        throw new Error('Simulated transaction error');
      });

      // Execute transaction and expect it to fail
      expect(() => {
        transaction();
      }).toThrow('Simulated transaction error');

      // Verify rollback - color count should be unchanged
      const finalColorCount = colorRepo.findAll(testOrgId).length;
      expect(finalColorCount).toBe(initialColorCount);
    });
  });
});

/**
 * Setup pure UUID schema for testing (post-migration schema)
 */
function setupUUIDSchema(db: Database.Database) {
  // Organizations table with UUID primary key
  db.exec(`
    CREATE TABLE organizations (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
      settings JSON DEFAULT '{}',
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      CHECK (length(id) = 36)
    );

    CREATE TABLE organization_members (
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      user_id TEXT NOT NULL,
      role TEXT NOT NULL DEFAULT 'member',
      joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
      invited_by TEXT,
      PRIMARY KEY (organization_id, user_id)
    );

    CREATE TABLE color_sources (
      id INTEGER PRIMARY KEY,
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL
    );

    INSERT INTO color_sources (id, code, name) VALUES 
    (1, 'USER', 'User Created'),
    (2, 'PANTONE', 'Pantone Color'),
    (3, 'RAL', 'RAL Color');

    CREATE TABLE products (
      id TEXT PRIMARY KEY,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      user_id TEXT,
      name TEXT NOT NULL,
      description TEXT,
      metadata JSON DEFAULT '{}',
      is_active BOOLEAN NOT NULL DEFAULT TRUE,
      is_synced BOOLEAN NOT NULL DEFAULT FALSE,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT,
      created_by TEXT,
      CHECK (length(id) = 36)
    );

    CREATE TABLE colors (
      id TEXT PRIMARY KEY,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      source_id INTEGER NOT NULL DEFAULT 1 REFERENCES color_sources(id),
      name TEXT NOT NULL,
      display_name TEXT,
      code TEXT,
      hex TEXT NOT NULL,
      color_spaces JSON DEFAULT '{}',
      is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
      is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
      is_effect BOOLEAN NOT NULL DEFAULT FALSE,
      is_library BOOLEAN NOT NULL DEFAULT FALSE,
      gradient_colors TEXT,
      notes TEXT,
      tags TEXT,
      properties JSON DEFAULT '{}',
      is_synced BOOLEAN NOT NULL DEFAULT FALSE,
      version INTEGER NOT NULL DEFAULT 1,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT,
      created_by TEXT,
      user_id TEXT,
      device_id TEXT,
      conflict_resolved_at TEXT,
      CHECK (length(id) = 36),
      CHECK (length(hex) = 7 AND substr(hex, 1, 1) = '#')
    );

    CREATE TABLE product_colors (
      product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
      color_id TEXT NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
      display_order INTEGER NOT NULL DEFAULT 0,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      added_at TEXT DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (product_id, color_id)
    );

    CREATE INDEX idx_products_org ON products(organization_id);
    CREATE INDEX idx_colors_org ON colors(organization_id);
    CREATE INDEX idx_product_colors_product ON product_colors(product_id);
    CREATE INDEX idx_product_colors_color ON product_colors(color_id);
  `);
}

/**
 * Setup test organization for UUID testing
 */
function setupTestOrganization(db: Database.Database, orgId: string, userId: string) {
  db.prepare(`
    INSERT INTO organizations (id, name, slug, plan, settings)
    VALUES (?, ?, ?, ?, ?)
  `).run(orgId, 'Test Organization', 'test-org-uuid', 'free', '{}');

  db.prepare(`
    INSERT INTO organization_members (organization_id, user_id, role)
    VALUES (?, ?, ?)
  `).run(orgId, userId, 'owner');
}