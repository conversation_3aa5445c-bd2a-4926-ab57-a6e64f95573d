/**
 * @file product-sync.service.test.ts
 * @description Comprehensive unit tests for ProductSyncService
 *
 * Tests all sync operations with proper Supabase mocking and error handling.
 * Ensures data consistency, authentication handling, and sync status management.
 */

import Database from 'better-sqlite3';
import {
  ProductSyncService,
  ProductSyncOptions,
  ProductSyncResult,
} from '../product-sync.service';
import { ProductRepository } from '../../../db/repositories/product.repository';
import { IProductRepository } from '../../../db/repositories/interfaces/product.repository.interface';
import { Product } from '../../../../shared/types/product.types';

import { vi } from 'vitest';

// Mock the supabase-client module
vi.mock('../../../services/supabase-client', () => ({
  getSupabaseClient: vi.fn(),
  ensureAuthenticatedSession: vi.fn(),
}));

// Mock the organization service
vi.mock('../../../db/services/organization.service', () => ({
  OrganizationService: vi.fn().mockImplementation(() => ({
    syncOrganizationsFromSupabase: vi.fn().mockResolvedValue(undefined),
  })),
}));

describe('ProductSyncService', () => {
  let db: Database.Database;
  let productRepository: any;
  let productSyncService: ProductSyncService;
  let mockSupabaseClient: any;
  let mockSession: any;

  const mockOrganizationId = '123e4567-e89b-12d3-a456-426614174000';
  const mockUserId = '987fcdeb-51a2-43d1-b567-321987654321';
  const mockProductId = 'product-123';

  beforeEach(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');

    // Create tables
    db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        user_id TEXT,
        name TEXT NOT NULL,
        description TEXT,
        metadata TEXT,
        is_active BOOLEAN DEFAULT 1,
        is_synced BOOLEAN DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL,
        created_by TEXT,
        UNIQUE(external_id, organization_id)
      );
      
      CREATE TABLE IF NOT EXISTS colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        code TEXT NOT NULL,
        display_name TEXT,
        hex TEXT,
        color_spaces TEXT,
        is_gradient BOOLEAN DEFAULT 0,
        gradient_colors TEXT,
        notes TEXT,
        tags TEXT,
        is_library BOOLEAN DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        is_synced BOOLEAN DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL,
        UNIQUE(external_id, organization_id)
      );
      
      CREATE TABLE IF NOT EXISTS product_colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        color_id INTEGER NOT NULL,
        display_order INTEGER DEFAULT 0,
        organization_id TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id),
        FOREIGN KEY (color_id) REFERENCES colors (id),
        UNIQUE(product_id, color_id)
      );
      
      CREATE TABLE IF NOT EXISTS organizations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Insert test organization
    db.prepare(
      `
      INSERT INTO organizations (external_id, name) 
      VALUES (?, ?)
    `
    ).run(mockOrganizationId, 'Test Organization');

    // Create mock repository
    productRepository = {
      findAll: vi.fn(),
      findById: vi.fn(),
      insert: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      search: vi.fn(),
      findUnsynced: vi.fn(),
      getAllWithColors: vi.fn(),
      getProductWithColors: vi.fn(),
      addProductColor: vi.fn(),
      removeProductColor: vi.fn(),
      getProductColors: vi.fn(),
      findSoftDeleted: vi.fn(),
      restoreRecord: vi.fn(),
      deleteMultiple: vi.fn(),
      upsertFromSupabase: vi.fn(),
      deduplicateProducts: vi.fn(),
      markAsSynced: vi.fn(),
      getInternalId: vi.fn(),
      getPreparedStatement: vi.fn(),
    };

    // Create service instance
    productSyncService = new ProductSyncService(db, productRepository);

    // Setup mock Supabase client
    mockSupabaseClient = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      is: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      upsert: vi.fn(),
      update: vi.fn(),
      all: vi.fn(),
    };

    mockSession = {
      user: {
        id: mockUserId,
        email: '<EMAIL>',
      },
    };

    // Setup module mocks
    const supabaseModule = await import('../../../services/supabase-client');
    vi.mocked(supabaseModule.getSupabaseClient).mockReturnValue(
      mockSupabaseClient
    );
    vi.mocked(supabaseModule.ensureAuthenticatedSession).mockResolvedValue({
      session: mockSession,
      error: null,
    });
  });

  afterEach(() => {
    db.close();
    vi.clearAllMocks();
  });

  describe('pushProductToSupabase', () => {
    const mockProduct: Product = {
      id: mockProductId,
      name: 'Test Product',
      description: 'Test Description',
      organizationId: mockOrganizationId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    beforeEach(() => {
      productRepository.findById.mockReturnValue({
        id: 1,
        external_id: mockProductId,
        organization_id: mockOrganizationId,
        user_id: mockUserId,
        name: 'Test Product',
        description: 'Test Description',
        metadata: null,
        is_active: true,
        is_synced: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
        created_by: mockUserId,
      });
    });

    it('should successfully push a product to Supabase', async () => {
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      await productSyncService.pushProductToSupabase(
        mockProductId,
        mockOrganizationId,
        mockUserId
      );

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('products');
      expect(mockSupabaseClient.upsert).toHaveBeenCalledWith({
        external_id: mockProductId,
        organization_id: mockOrganizationId,
        user_id: mockUserId,
        name: 'Test Product',
        description: 'Test Description',
        updated_at: expect.any(String),
      });
      expect(productRepository.markAsSynced).toHaveBeenCalledWith(
        mockProductId
      );
    });

    it('should handle deletion sync', async () => {
      mockSupabaseClient.update.mockResolvedValue({ error: null });

      await productSyncService.pushProductToSupabase(
        mockProductId,
        mockOrganizationId,
        mockUserId,
        true
      );

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('products');
      expect(mockSupabaseClient.update).toHaveBeenCalledWith({
        deleted_at: expect.any(String),
        updated_at: expect.any(String),
      });
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith(
        'external_id',
        mockProductId
      );
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith(
        'organization_id',
        mockOrganizationId
      );
    });

    it('should throw error when no authenticated session', async () => {
      const supabaseModule = await import('../../../services/supabase-client');
      vi.mocked(supabaseModule.ensureAuthenticatedSession).mockResolvedValue({
        session: null,
        error: 'No session',
      });

      await expect(
        productSyncService.pushProductToSupabase(
          mockProductId,
          mockOrganizationId,
          mockUserId
        )
      ).rejects.toThrow('No authenticated session');
    });

    it('should throw error when product not found', async () => {
      productRepository.findById.mockReturnValue(null);

      await expect(
        productSyncService.pushProductToSupabase(
          mockProductId,
          mockOrganizationId,
          mockUserId
        )
      ).rejects.toThrow(`Product ${mockProductId} not found`);
    });

    it('should throw error when Supabase operation fails', async () => {
      const supabaseError = new Error('Supabase error');
      mockSupabaseClient.upsert.mockResolvedValue({ error: supabaseError });

      await expect(
        productSyncService.pushProductToSupabase(
          mockProductId,
          mockOrganizationId,
          mockUserId
        )
      ).rejects.toThrow(supabaseError);
    });
  });

  describe('pushProductsToSupabase', () => {
    it('should successfully push multiple products in batches', async () => {
      const productIds = ['product-1', 'product-2', 'product-3'];

      productRepository.findById.mockImplementation(id => ({
        id: 1,
        external_id: id,
        organization_id: mockOrganizationId,
        user_id: mockUserId,
        name: `Product ${id}`,
        description: null,
        metadata: null,
        is_active: true,
        is_synced: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
        created_by: mockUserId,
      }));

      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      const result = await productSyncService.pushProductsToSupabase(
        productIds,
        mockOrganizationId,
        { batchSize: 2 }
      );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(3);
      expect(result.errors).toHaveLength(0);
      expect(mockSupabaseClient.upsert).toHaveBeenCalledTimes(3);
    });

    it('should handle retry logic with exponential backoff', async () => {
      const productIds = ['product-1'];

      productRepository.findById.mockReturnValue({
        id: 1,
        external_id: 'product-1',
        organization_id: mockOrganizationId,
        user_id: mockUserId,
        name: 'Product 1',
        description: null,
        metadata: null,
        is_active: true,
        is_synced: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
        created_by: mockUserId,
      });

      // Fail first two attempts, succeed on third
      mockSupabaseClient.upsert
        .mockResolvedValueOnce({ error: new Error('First failure') })
        .mockResolvedValueOnce({ error: new Error('Second failure') })
        .mockResolvedValueOnce({ error: null });

      const result = await productSyncService.pushProductsToSupabase(
        productIds,
        mockOrganizationId,
        { retryAttempts: 3 }
      );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(1);
      expect(mockSupabaseClient.upsert).toHaveBeenCalledTimes(3);
    });

    it('should handle permanent failures after retry limit', async () => {
      const productIds = ['product-1'];

      productRepository.findById.mockReturnValue({
        id: 1,
        external_id: 'product-1',
        organization_id: mockOrganizationId,
        user_id: mockUserId,
        name: 'Product 1',
        description: null,
        metadata: null,
        is_active: true,
        is_synced: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
        created_by: mockUserId,
      });

      mockSupabaseClient.upsert.mockResolvedValue({
        error: new Error('Persistent failure'),
      });

      const result = await productSyncService.pushProductsToSupabase(
        productIds,
        mockOrganizationId,
        { retryAttempts: 2 }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(0);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain(
        'Failed to push product product-1 after 2 attempts'
      );
    });
  });

  describe('syncProductsFromSupabase', () => {
    const mockSupabaseProducts = [
      {
        external_id: 'product-1',
        organization_id: mockOrganizationId,
        name: 'Product 1',
        description: 'Description 1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
      },
      {
        external_id: 'product-2',
        organization_id: mockOrganizationId,
        name: 'Product 2',
        description: 'Description 2',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
      },
    ];

    beforeEach(() => {
      // Mock connectivity check
      mockSupabaseClient.select.mockResolvedValue({ count: 5, error: null });

      // Mock product fetch
      mockSupabaseClient.all = vi.fn().mockResolvedValue({
        data: mockSupabaseProducts,
        error: null,
      });

      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 0,
        errors: [],
      });
    });

    it('should successfully sync products from Supabase', async () => {
      productRepository.update.mockReturnValue(true);
      productRepository.insert.mockReturnValue('new-product-id');

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId,
        mockOrganizationId
      );

      expect(result).toHaveLength(2);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('products');
      expect(mockSupabaseClient.select).toHaveBeenCalledWith('*');
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith(
        'organization_id',
        mockOrganizationId
      );
      expect(mockSupabaseClient.is).toHaveBeenCalledWith('deleted_at', null);
    });

    it('should handle authentication failure', async () => {
      const supabaseModule = await import('../../../services/supabase-client');
      vi.mocked(supabaseModule.ensureAuthenticatedSession).mockResolvedValue({
        session: null,
        error: 'Auth failed',
      });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId,
        mockOrganizationId
      );

      expect(result).toHaveLength(0);
    });

    it('should handle Supabase query errors', async () => {
      mockSupabaseClient.all = vi.fn().mockResolvedValue({
        data: null,
        error: new Error('Query failed'),
      });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId,
        mockOrganizationId
      );

      expect(result).toHaveLength(0);
    });

    it('should preserve local deletions by default', async () => {
      // Setup existing product that's deleted locally
      const existingDeletedProduct = {
        id: 1,
        external_id: 'product-1',
        deleted_at: new Date().toISOString(),
        is_active: 0,
      };

      db.prepare(`
        SELECT id, external_id, deleted_at, is_active 
        FROM products 
        WHERE external_id = ? AND organization_id = ?
      `).get = vi.fn().mockReturnValue(existingDeletedProduct);

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId,
        mockOrganizationId
      );

      // Should not update the locally deleted product
      expect(productRepository.update).not.toHaveBeenCalledWith(
        'product-1',
        expect.any(Object),
        expect.any(String),
        expect.any(String),
        true
      );
    });

    it('should run post-sync deduplication', async () => {
      await productSyncService.syncProductsFromSupabase(
        mockUserId,
        mockOrganizationId
      );

      expect(productRepository.deduplicateProducts).toHaveBeenCalledWith(
        mockOrganizationId
      );
    });
  });

  describe('syncProductColorsFromSupabase', () => {
    const mockProductColorRelationships = [
      {
        product_id: 1,
        color_id: 1,
        display_order: 1,
      },
      {
        product_id: 1,
        color_id: 2,
        display_order: 2,
      },
    ];

    const mockSupabaseProducts = [{ id: 1, external_id: 'product-1' }];

    const mockSupabaseColors = [
      { id: 1, external_id: 'color-1' },
      { id: 2, external_id: 'color-2' },
    ];

    beforeEach(() => {
      // Mock lookup data fetch
      mockSupabaseClient.select
        .mockResolvedValueOnce({ data: mockSupabaseProducts, error: null })
        .mockResolvedValueOnce({ data: mockSupabaseColors, error: null });

      // Mock relationships fetch
      mockSupabaseClient.all = vi.fn().mockResolvedValue({
        data: mockProductColorRelationships,
        error: null,
      });

      // Mock local ID lookups
      db.prepare = vi.fn().mockImplementation(sql => ({
        get: vi.fn().mockImplementation((externalId, orgId) => {
          if (sql.includes('products')) {
            return { id: 1 };
          }
          if (sql.includes('colors')) {
            return { id: externalId === 'color-1' ? 1 : 2 };
          }
          return null;
        }),
        run: vi.fn().mockReturnValue({ changes: 1 }),
      }));
    });

    it('should successfully sync product-color relationships', async () => {
      const result =
        await productSyncService.syncProductColorsFromSupabase(
          mockOrganizationId
        );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(2);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle missing external IDs', async () => {
      const invalidRelationships = [
        {
          product_id: 999, // Non-existent product
          color_id: 1,
          display_order: 1,
        },
      ];

      mockSupabaseClient.all = vi.fn().mockResolvedValue({
        data: invalidRelationships,
        error: null,
      });

      const result =
        await productSyncService.syncProductColorsFromSupabase(
          mockOrganizationId
        );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(0);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle Supabase fetch errors', async () => {
      mockSupabaseClient.select.mockResolvedValue({
        data: null,
        error: new Error('Fetch failed'),
      });

      const result =
        await productSyncService.syncProductColorsFromSupabase(
          mockOrganizationId
        );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(0);
      expect(result.errors).toContain(
        'Failed to fetch lookup data: Fetch failed Fetch failed'
      );
    });
  });

  describe('getUnsyncedProducts', () => {
    it('should return unsynced products', () => {
      const mockUnsyncedRows = [
        {
          id: 1,
          external_id: 'product-1',
          organization_id: mockOrganizationId,
          name: 'Product 1',
          metadata: '{"description":"Test"}',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ];

      productRepository.findUnsynced.mockReturnValue(mockUnsyncedRows);

      const result = productSyncService.getUnsyncedProducts();

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('product-1');
      expect(result[0].name).toBe('Product 1');
      expect(result[0].description).toBe('Test');
    });

    it('should handle errors gracefully', () => {
      productRepository.findUnsynced.mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = productSyncService.getUnsyncedProducts();

      expect(result).toHaveLength(0);
    });
  });

  describe('markProductAsSynced', () => {
    it('should mark product as synced', () => {
      productSyncService.markProductAsSynced(mockProductId);

      expect(productRepository.markAsSynced).toHaveBeenCalledWith(
        mockProductId
      );
    });
  });

  describe('getServiceInfo', () => {
    it('should return service metadata', () => {
      const info = productSyncService.getServiceInfo();

      expect(info.name).toBe('ProductSyncService');
      expect(info.version).toBe('1.0.0');
      expect(info.capabilities).toContain('Push products to Supabase');
      expect(info.capabilities).toContain('Sync products from Supabase');
      expect(info.capabilities).toContain('Sync product-color relationships');
      expect(info.capabilities).toContain('Product deduplication');
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle missing organization gracefully', async () => {
      // Remove organization from database
      db.prepare('DELETE FROM organizations WHERE external_id = ?').run(
        mockOrganizationId
      );

      const { OrganizationService } = await import(
        '../../../db/services/organization.service'
      );
      const mockOrgService = new OrganizationService();
      mockOrgService.syncOrganizationsFromSupabase.mockResolvedValue(undefined);

      // Re-add organization after sync
      db.prepare(
        `
        INSERT INTO organizations (external_id, name) 
        VALUES (?, ?)
      `
      ).run(mockOrganizationId, 'Test Organization');

      mockSupabaseClient.all = vi.fn().mockResolvedValue({
        data: [],
        error: null,
      });
      mockSupabaseClient.select.mockResolvedValue({ count: 0, error: null });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId,
        mockOrganizationId
      );

      expect(result).toHaveLength(0);
      expect(mockOrgService.syncOrganizationsFromSupabase).toHaveBeenCalledWith(
        mockUserId
      );
    });

    it('should handle malformed metadata gracefully', () => {
      const mockRowWithBadMetadata = {
        id: 1,
        external_id: 'product-1',
        organization_id: mockOrganizationId,
        name: 'Product 1',
        metadata: 'invalid-json{',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      productRepository.findUnsynced.mockReturnValue([mockRowWithBadMetadata]);

      const result = productSyncService.getUnsyncedProducts();

      expect(result).toHaveLength(1);
      expect(result[0].description).toBeUndefined(); // Should handle gracefully
    });

    it('should validate product data before sync', async () => {
      const invalidProduct = {
        id: '',
        name: '',
        organizationId: mockOrganizationId,
      };

      productRepository.findById.mockReturnValue({
        id: 1,
        external_id: '',
        organization_id: mockOrganizationId,
        name: '',
        description: null,
        metadata: null,
        is_active: true,
        is_synced: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
        created_by: mockUserId,
      });

      await expect(
        productSyncService.pushProductToSupabase(
          '',
          mockOrganizationId,
          mockUserId
        )
      ).rejects.toThrow('Product ID is required for sync');
    });
  });
});
