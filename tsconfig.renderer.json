{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    /* Renderer Process Specific Configuration */
    "target": "ES2020",
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable", "ES6"],
    "types": ["react", "react-dom", "node"],
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    
    /* Browser Environment */
    "skipLibCheck": true,
    "declaration": false,
    "sourceMap": true,
    "outDir": "./out/renderer",
    "rootDir": "./src/renderer",
    
    /* React and DOM Specific */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "noEmit": true,
    
    /* Enhanced Type Safety for Renderer Process */
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    
    /* Renderer Process Path Mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@renderer/*": ["src/renderer/*"],
      "@shared/*": ["src/shared/*"],
      "@types/*": ["src/types/*"],
      "@components/*": ["src/renderer/components/*"],
      "@hooks/*": ["src/renderer/hooks/*"],
      "@store/*": ["src/renderer/store/*"],
      "@utils/*": ["src/renderer/utils/*"],
      "@assets/*": ["src/renderer/assets/*"],
      "@styles/*": ["src/renderer/styles/*"]
    }
  },
  "include": [
    "src/renderer/**/*.ts",
    "src/renderer/**/*.tsx",
    "src/shared/**/*.ts",
    "src/types/**/*.d.ts"
  ],
  "exclude": [
    "src/main/**/*",
    "src/preload/**/*",
    "src/test/**/*",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/__tests__/**/*",
    "node_modules"
  ]
}