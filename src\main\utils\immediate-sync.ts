/**
 * @file immediate-sync.ts
 * @description Smart immediate sync - Push-only to prevent loops
 */

/**
 * Configuration for immediate sync behavior
 */
interface ImmediateSyncConfig {
  enabled: boolean;
  pushOnly: boolean; // Only push changes, don't pull back
  skipOutboxDuringSync: boolean; // Skip outbox when doing immediate sync
}

/**
 * Default configuration - Push-only immediate sync
 */
const DEFAULT_CONFIG: ImmediateSyncConfig = {
  enabled: true,
  pushOnly: true, // Only push user changes immediately
  skipOutboxDuringSync: true, // Avoid double-syncing
};

let config: ImmediateSyncConfig = DEFAULT_CONFIG;

/**
 * Smart immediate sync - Push single item to Supabase without triggering full sync
 * This prevents sync loops by avoiding bidirectional syncs for user actions
 */
export async function queueImmediateSync(
  table?: string,
  action?: 'upsert' | 'delete',
  itemId?: string,
  syncData?: any
): Promise<void> {
  if (!config.enabled) {
    console.debug('[ImmediateSync] Immediate sync disabled, skipping');
    return;
  }

  try {
    // If no parameters provided, trigger a manual full sync
    if (!table) {
      console.log(`[ImmediateSync] 🚀 Manual full sync requested`);
      const { unifiedSyncManager } = await import(
        '../services/sync/unified-sync-manager'
      );
      const status = unifiedSyncManager.getStatus();
      if (!status.isInitialized) {
        console.warn('[ImmediateSync] Unified sync manager not initialized');
        return;
      }

      const syncPromises = [
        unifiedSyncManager.sync('colors', 'bidirectional', 'high'),
        unifiedSyncManager.sync('products', 'bidirectional', 'high'),
        unifiedSyncManager.sync('organizations', 'bidirectional', 'high'),
      ];

      const results = await Promise.allSettled(syncPromises);
      const successful = results.filter(
        r => r.status === 'fulfilled' && r.value.success
      ).length;
      console.log(
        `[ImmediateSync] ✅ Manual sync completed: ${successful}/${results.length} successful`
      );
      return;
    }

    // For immediate sync of specific items, only push to Supabase (no pull)
    if (config.pushOnly && itemId && syncData && action) {
      console.log(
        `[ImmediateSync] 🚀 PUSH-ONLY sync: ${action} on ${table} (${itemId})`
      );
      await pushItemToSupabase(table, action, itemId, syncData);
      return;
    }

    // Fallback to outbox-based sync (let manual/auto sync handle it)
    console.log(
      `[ImmediateSync] 📦 Item queued for next sync: ${table}:${itemId}`
    );
  } catch (error) {
    console.error('[ImmediateSync] Failed to perform immediate sync:', error);
  }
}

/**
 * Push a single item directly to Supabase without triggering full sync
 */
async function pushItemToSupabase(
  table: string,
  action: 'upsert' | 'delete',
  itemId: string,
  syncData: any
): Promise<void> {
  try {
    const { getSupabaseClient, ensureAuthenticatedSession } = await import(
      '../services/supabase-client'
    );

    // Ensure we have authentication
    const { session, error: sessionError } = await ensureAuthenticatedSession();
    if (!session || sessionError) {
      console.warn(
        '[ImmediateSync] No authenticated session for immediate push'
      );
      return;
    }

    const supabase = getSupabaseClient();

    // Determine table name for Supabase
    let tableName = table;
    if (table.includes('product')) {tableName = 'products';}
    else if (table.includes('color')) {tableName = 'colors';}
    else if (table.includes('organization')) {tableName = 'organizations';}

    if (action === 'delete') {
      // For deletes, set deleted_at timestamp
      const { error } = await supabase
        .from(tableName)
        .update({ deleted_at: new Date().toISOString() })
        .eq('id', itemId);

      if (error) {
        console.error(
          `[ImmediateSync] Failed to delete ${tableName}:${itemId}:`,
          error
        );
      } else {
        console.log(
          `[ImmediateSync] ✅ Pushed delete to Supabase: ${tableName}:${itemId}`
        );
      }
    } else {
      // For upserts, use upsert operation
      const { error } = await supabase
        .from(tableName)
        .upsert(syncData, { onConflict: 'id' });

      if (error) {
        console.error(
          `[ImmediateSync] Failed to upsert ${tableName}:${itemId}:`,
          error
        );
      } else {
        console.log(
          `[ImmediateSync] ✅ Pushed upsert to Supabase: ${tableName}:${itemId}`
        );
      }
    }
  } catch (error) {
    console.error(`[ImmediateSync] Error pushing ${table}:${itemId}:`, error);
  }
}

/**
 * Perform immediate sync of multiple items as a batch
 */
export async function batchImmediateSync(
  operations: Array<{
    table: string;
    action: 'upsert' | 'delete';
    itemId: string;
    syncData?: any;
  }>
): Promise<void> {
  if (!config.enabled) {
    console.debug('[ImmediateSync] Immediate batch sync disabled, skipping');
    return;
  }

  try {
    if (config.pushOnly) {
      // Push each item individually
      console.log(
        `[ImmediateSync] 🚀 PUSH-ONLY batch: ${operations.length} operations`
      );
      for (const op of operations) {
        if (op.syncData) {
          await pushItemToSupabase(op.table, op.action, op.itemId, op.syncData);
        }
      }
      return;
    }

    // Fallback to full sync for batch operations
    console.log(
      `[ImmediateSync] 📦 Batch queued for next sync: ${operations.length} items`
    );
  } catch (error) {
    console.error(
      '[ImmediateSync] Failed to perform batch immediate sync:',
      error
    );
  }
}

/**
 * Update immediate sync configuration
 */
export function updateImmediateSyncConfig(
  updates: Partial<ImmediateSyncConfig>
): void {
  config = { ...config, ...updates };
  console.log('[ImmediateSync] Config updated:', config);
}

/**
 * Check if immediate sync is enabled
 */
export function isImmediateSyncEnabled(): boolean {
  return config.enabled;
}

/**
 * Trigger immediate sync manually (for user actions)
 * This is a simplified wrapper for the most common use case
 */
export async function triggerImmediateSync(): Promise<void> {
  return queueImmediateSync(); // General sync with no parameters
}

/**
 * Get immediate sync status
 */
export function getImmediateSyncStatus() {
  return {
    enabled: config.enabled,
    pushOnly: config.pushOnly,
    skipOutboxDuringSync: config.skipOutboxDuringSync,
    config,
  };
}
