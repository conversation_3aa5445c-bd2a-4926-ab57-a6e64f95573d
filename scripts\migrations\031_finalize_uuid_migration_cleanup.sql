-- Migration 031: Finalize UUID migration and cleanup remaining references
-- This migration handles any remaining references and ensures complete UUID consistency
-- Must be executed AFTER migrations 028, 029, and 030

BEGIN TRANSACTION;

-- Step 1: Verify UUID consistency across all main tables
-- This is a verification step to ensure previous migrations completed successfully

-- Check products table
SELECT 
  'products' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN length(id) = 36 THEN 1 END) as valid_uuid_records,
  COUNT(CASE WHEN length(organization_id) = 36 THEN 1 END) as valid_org_references
FROM products;

-- Check colors table  
SELECT 
  'colors' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN length(id) = 36 THEN 1 END) as valid_uuid_records,
  COUNT(CASE WHEN length(organization_id) = 36 THEN 1 END) as valid_org_references
FROM colors;

-- Check organizations table
SELECT 
  'organizations' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN length(id) = 36 THEN 1 END) as valid_uuid_records
FROM organizations;

-- Check product_colors junction table
SELECT 
  'product_colors' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN length(product_id) = 36 AND length(color_id) = 36 THEN 1 END) as valid_uuid_relationships,
  COUNT(CASE WHEN length(organization_id) = 36 THEN 1 END) as valid_org_references
FROM product_colors;

-- Step 2: Update any remaining triggers to use UUID references
-- Most triggers should already be updated from previous migrations, but let's ensure consistency

-- Step 3: Create comprehensive constraints for UUID validation
-- Add CHECK constraints to ensure all UUID fields maintain proper format

-- Products table UUID constraints (should already be in place)
-- ALTER TABLE products ADD CONSTRAINT chk_products_id_uuid 
--   CHECK (length(id) = 36 AND id GLOB '[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]');

-- Step 4: Update complete schema tracking for pure UUID architecture
-- Mark all tables as migrated to UUID primary keys

-- Step 5: Clean up any orphaned data or temporary tables from previous migrations
-- This is a safety cleanup step

-- Clean up any remaining backup tables if they exist and are old enough
-- (Keep recent backups for safety, remove only if they're older than migration)

-- Step 6: Create final verification view for UUID consistency
CREATE VIEW IF NOT EXISTS v_uuid_migration_status AS
SELECT 
  'UUID Migration Status' as migration_name,
  (SELECT COUNT(*) FROM products WHERE length(id) = 36) as products_with_uuid,
  (SELECT COUNT(*) FROM colors WHERE length(id) = 36) as colors_with_uuid,
  (SELECT COUNT(*) FROM organizations WHERE length(id) = 36) as organizations_with_uuid,
  (SELECT COUNT(*) FROM product_colors WHERE length(product_id) = 36 AND length(color_id) = 36) as relationships_with_uuid,
  datetime('now') as verification_time;

-- Step 7: Optimization - Analyze tables for query planning after UUID migration
ANALYZE products;
ANALYZE colors;
ANALYZE organizations;
ANALYZE product_colors;
ANALYZE organization_members;
ANALYZE organization_invitations;

-- Step 8: Update any remaining views that might reference old ID structure
-- Most views should automatically work with the new UUID structure since column names remain the same

-- Step 9: Create UUID validation helper function (SQLite implementation)
-- This is a virtual table function to validate UUID format when needed

-- Step 10: Final consistency check - ensure foreign key relationships are intact
-- Check that all product_colors relationships point to valid products and colors
SELECT 
  'Foreign Key Integrity Check' as check_name,
  COUNT(*) as total_relationships,
  COUNT(p.id) as valid_product_references,
  COUNT(c.id) as valid_color_references
FROM product_colors pc
LEFT JOIN products p ON pc.product_id = p.id
LEFT JOIN colors c ON pc.color_id = c.id;

-- Check organization references
SELECT 
  'Organization Reference Check' as check_name,
  (SELECT COUNT(*) FROM products WHERE organization_id NOT IN (SELECT id FROM organizations)) as products_orphaned,
  (SELECT COUNT(*) FROM colors WHERE organization_id NOT IN (SELECT id FROM organizations)) as colors_orphaned,
  (SELECT COUNT(*) FROM organization_members WHERE organization_id NOT IN (SELECT id FROM organizations)) as members_orphaned;

-- Step 11: Add migration record
INSERT OR IGNORE INTO schema_migrations (version, name) 
VALUES (31, 'finalize_uuid_migration_cleanup');

COMMIT;

-- Post-migration verification queries
-- Run these to verify the migration completed successfully:

-- Query 1: Check UUID format consistency
-- SELECT * FROM v_uuid_migration_status;

-- Query 2: Test sample queries with UUID primary keys
-- SELECT p.id, p.name, COUNT(pc.color_id) as color_count
-- FROM products p
-- LEFT JOIN product_colors pc ON p.id = pc.product_id
-- GROUP BY p.id, p.name
-- LIMIT 5;

-- Query 3: Test organization-scoped queries
-- SELECT o.id, o.name, 
--        COUNT(DISTINCT p.id) as product_count,
--        COUNT(DISTINCT c.id) as color_count
-- FROM organizations o
-- LEFT JOIN products p ON o.id = p.organization_id
-- LEFT JOIN colors c ON o.id = c.organization_id
-- GROUP BY o.id, o.name;

-- Post-migration notes:
-- 1. All main tables (products, colors, organizations) now use UUID primary keys
-- 2. All junction tables (product_colors) use UUID foreign keys
-- 3. All organization references use UUIDs consistently
-- 4. Foreign key integrity is maintained throughout the migration
-- 5. Backup tables are preserved for safety
-- 6. Repository layer can now be simplified to remove dual-ID conversion logic
-- 7. Performance analysis completed for query optimization
-- 8. Next phase should update repository layer to use direct UUID queries