/**
 * @file DebugPanel.tsx
 * @description Debug panel wrapper for development mode
 */

import React from 'react';
import DebugMonitoringPanel from '../DebugMonitoringPanel';

interface DebugPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Debug panel wrapper component
 */
export const DebugPanel: React.FC<DebugPanelProps> = ({ isOpen, onClose }) => {
  return <DebugMonitoringPanel isOpen={isOpen} onClose={onClose} />;
};

export default DebugPanel;
