/**
 * @file soft-delete.ipc.ts
 * @description IPC handlers for soft delete operations
 */

import { ipcMain } from 'electron';
import { getDatabase } from '../db/database';
import { ColorService } from '../db/services/color.service';
import { ProductService } from '../db/services/product.service';
import { DatasheetService } from '../db/services/datasheet.service';
import { validateRequired } from '../utils/input-validation';
import { registerHandlerSafely } from '../utils/ipcRegistry';

/**
 * Service bundle interface for soft delete handlers
 */
interface SoftDeleteServices {
  colorService: ColorService;
  productService: ProductService;
  datasheetService: DatasheetService;
}

/**
 * Register soft delete IPC handlers (legacy version)
 */
export function registerSoftDeleteIpcHandlers(): void {
  const db = getDatabase();
  const colorService = new ColorService(db);
  const productService = new ProductService(db);
  const datasheetService = new DatasheetService(db);

  // Color soft delete handlers
  registerHandlerSafely(
    ipcMain,
    'colors:get-soft-deleted',
    async (_, organizationId: string, limit?: number, offset?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return colorService.getSoftDeleted(validatedOrgId, limit, offset);
      } catch (error) {
        console.error('[IPC] Error getting soft deleted colors:', error);
        return [];
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'colors:restore',
    async (_, colorId: string, organizationId: string, userId?: string) => {
      try {
        const validatedColorId = validateRequired(
          'colorId',
          colorId,
          'uuid'
        ) as string;
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return colorService.restore(validatedColorId, validatedOrgId, userId);
      } catch (error) {
        console.error('[IPC] Error restoring color:', error);
        return false;
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'colors:bulk-restore',
    async (_, colorIds: string[], organizationId: string, userId?: string) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        const validatedColorIds = colorIds.map(
          id => validateRequired('colorId', id, 'uuid') as string
        );
        return colorService.bulkRestore(
          validatedColorIds,
          validatedOrgId,
          userId
        );
      } catch (error) {
        console.error('[IPC] Error bulk restoring colors:', error);
        return { success: false, restored: 0 };
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'colors:cleanup-old-soft-deleted',
    async (_, organizationId: string, daysOld?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return colorService.cleanupOldSoftDeleted(validatedOrgId, daysOld);
      } catch (error) {
        console.error(
          '[IPC] Error cleaning up old soft deleted colors:',
          error
        );
        return { success: false, cleaned: 0 };
      }
    }
  );

  // Product soft delete handlers
  registerHandlerSafely(
    ipcMain,
    'products:get-soft-deleted',
    async (_, organizationId: string, limit?: number, offset?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return productService.getSoftDeleted(validatedOrgId, limit, offset);
      } catch (error) {
        console.error('[IPC] Error getting soft deleted products:', error);
        return [];
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'products:restore',
    async (_, productId: string, organizationId: string, userId?: string) => {
      try {
        const validatedProductId = validateRequired(
          'productId',
          productId,
          'uuid'
        ) as string;
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return productService.restore(
          validatedProductId,
          validatedOrgId,
          userId
        );
      } catch (error) {
        console.error('[IPC] Error restoring product:', error);
        return false;
      }
    }
  );

  // Datasheet soft delete handlers
  registerHandlerSafely(
    ipcMain,
    'datasheets:get-soft-deleted',
    async (_, organizationId: string, limit?: number, offset?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return datasheetService.getSoftDeleted(validatedOrgId, limit, offset);
      } catch (error) {
        console.error('[IPC] Error getting soft deleted datasheets:', error);
        return [];
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'datasheets:restore',
    async (_, datasheetId: string, organizationId: string, userId?: string) => {
      try {
        const validatedDatasheetId = validateRequired(
          'datasheetId',
          datasheetId,
          'uuid'
        ) as string;
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return datasheetService.restoreDatasheet(
          validatedDatasheetId,
          validatedOrgId,
          userId
        );
      } catch (error) {
        console.error('[IPC] Error restoring datasheet:', error);
        return false;
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'datasheets:cleanup-old-soft-deleted',
    async (_, organizationId: string, daysOld?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return datasheetService.cleanupOldSoftDeleted(validatedOrgId, daysOld);
      } catch (error) {
        console.error(
          '[IPC] Error cleaning up old soft deleted datasheets:',
          error
        );
        return { success: false, cleaned: 0 };
      }
    }
  );

  console.log('[IPC] Soft delete handlers registered');
}

/**
 * Register soft delete IPC handlers with dependency injection (new DI version)
 */
export function registerSoftDeleteHandlers(services: SoftDeleteServices): void {
  const { colorService, productService, datasheetService } = services;

  // Color soft delete handlers
  registerHandlerSafely(
    ipcMain,
    'colors:get-soft-deleted',
    async (_, organizationId: string, limit?: number, offset?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return colorService.getSoftDeleted(validatedOrgId, limit, offset);
      } catch (error) {
        console.error('[IPC] Error getting soft deleted colors:', error);
        return [];
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'colors:restore',
    async (_, colorId: string, organizationId: string, userId?: string) => {
      try {
        const validatedColorId = validateRequired(
          'colorId',
          colorId,
          'uuid'
        ) as string;
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return colorService.restore(validatedColorId, validatedOrgId, userId);
      } catch (error) {
        console.error('[IPC] Error restoring color:', error);
        return false;
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'colors:bulk-restore',
    async (_, colorIds: string[], organizationId: string, userId?: string) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        const validatedColorIds = colorIds.map(
          id => validateRequired('colorId', id, 'uuid') as string
        );
        return colorService.bulkRestore(
          validatedColorIds,
          validatedOrgId,
          userId
        );
      } catch (error) {
        console.error('[IPC] Error bulk restoring colors:', error);
        return { success: false, restored: 0 };
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'colors:cleanup-old-soft-deleted',
    async (_, organizationId: string, daysOld?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return colorService.cleanupOldSoftDeleted(validatedOrgId, daysOld);
      } catch (error) {
        console.error(
          '[IPC] Error cleaning up old soft deleted colors:',
          error
        );
        return { success: false, cleaned: 0 };
      }
    }
  );

  // Product soft delete handlers
  registerHandlerSafely(
    ipcMain,
    'products:get-soft-deleted',
    async (_, organizationId: string, limit?: number, offset?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return productService.getSoftDeleted(validatedOrgId, limit, offset);
      } catch (error) {
        console.error('[IPC] Error getting soft deleted products:', error);
        return [];
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'products:restore',
    async (_, productId: string, organizationId: string, userId?: string) => {
      try {
        const validatedProductId = validateRequired(
          'productId',
          productId,
          'uuid'
        ) as string;
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return productService.restore(
          validatedProductId,
          validatedOrgId,
          userId
        );
      } catch (error) {
        console.error('[IPC] Error restoring product:', error);
        return false;
      }
    }
  );

  // Datasheet soft delete handlers
  registerHandlerSafely(
    ipcMain,
    'datasheets:get-soft-deleted',
    async (_, organizationId: string, limit?: number, offset?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return datasheetService.getSoftDeleted(validatedOrgId, limit, offset);
      } catch (error) {
        console.error('[IPC] Error getting soft deleted datasheets:', error);
        return [];
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'datasheets:restore',
    async (_, datasheetId: string, organizationId: string, userId?: string) => {
      try {
        const validatedDatasheetId = validateRequired(
          'datasheetId',
          datasheetId,
          'uuid'
        ) as string;
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return datasheetService.restoreDatasheet(
          validatedDatasheetId,
          validatedOrgId,
          userId
        );
      } catch (error) {
        console.error('[IPC] Error restoring datasheet:', error);
        return false;
      }
    }
  );

  registerHandlerSafely(
    ipcMain,
    'datasheets:cleanup-old-soft-deleted',
    async (_, organizationId: string, daysOld?: number) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return datasheetService.cleanupOldSoftDeleted(validatedOrgId, daysOld);
      } catch (error) {
        console.error(
          '[IPC] Error cleaning up old soft deleted datasheets:',
          error
        );
        return { success: false, cleaned: 0 };
      }
    }
  );

  console.log('[IPC] Soft delete handlers registered (DI version)');
}
