#!/usr/bin/env node

/**
 * Database diagnostic script for product-color associations
 * Helps identify why products are showing 0 color counts
 */

const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

// Database path (same as app uses) - check both possible locations
const possiblePaths = [
  path.join(os.homedir(), 'Library', 'Application Support', 'chroma-sync', 'chroma-sync.db'),
  path.join(os.homedir(), 'Library', 'Application Support', 'chroma-sync', 'database.db'),
  path.join(os.homedir(), 'Library', 'Application Support', 'ChromaSync', 'chroma-sync.db'),
];

let dbPath = null;
for (const testPath of possiblePaths) {
  if (require('fs').existsSync(testPath)) {
    dbPath = testPath;
    break;
  }
}

if (!dbPath) {
  console.log('❌ No database file found in any of these locations:');
  possiblePaths.forEach(p => console.log(`  - ${p}`));
  process.exit(1);
}

console.log('🔍 ChromaSync Product-Color Association Diagnostic');
console.log('================================================');
console.log(`Database path: ${dbPath}`);
console.log('');

try {
  const db = new Database(dbPath, { readonly: true });

  // 1. Check if database exists and is accessible
  console.log('✅ Database connection successful');
  console.log('');

  // Check what tables exist
  console.log('🗂️  Available Tables:');
  console.log('====================');
  const tables = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' ORDER BY name`).all();
  const tableNames = tables.map(t => t.name);
  tables.forEach(table => {
    console.log(`  - ${table.name}`);
  });
  console.log('');

  // 2. Check table schemas
  console.log('📋 Table Schemas:');
  console.log('=================');

  if (tableNames.includes('products')) {
    const productColumnsInfo = db.prepare(`PRAGMA table_info(products)`).all();
    console.log('🏢 Products table columns:');
    productColumnsInfo.forEach(col => {
      if (col.name.includes('organization')) {
        console.log(`  - ${col.name}: ${col.type} (${col.notnull ? 'NOT NULL' : 'NULLABLE'})`);
      }
    });
  } else {
    console.log('🏢 Products table: NOT FOUND');
  }

  if (tableNames.includes('colors')) {
    const colorColumnsInfo = db.prepare(`PRAGMA table_info(colors)`).all();
    console.log('🎨 Colors table columns:');
    colorColumnsInfo.forEach(col => {
      if (col.name.includes('organization')) {
        console.log(`  - ${col.name}: ${col.type} (${col.notnull ? 'NOT NULL' : 'NULLABLE'})`);
      }
    });
  } else {
    console.log('🎨 Colors table: NOT FOUND');
  }

  if (tableNames.includes('product_colors')) {
    const productColorsInfo = db.prepare(`PRAGMA table_info(product_colors)`).all();
    console.log('🔗 Product_colors table columns:');
    productColorsInfo.forEach(col => {
      console.log(`  - ${col.name}: ${col.type} (${col.notnull ? 'NOT NULL' : 'NULLABLE'})`);
    });
  } else {
    console.log('🔗 Product_colors table: NOT FOUND');
  }
  console.log('');

  // 3. Count records by table
  console.log('📊 Record Counts:');
  console.log('=================');
  
  const organizationCount = tableNames.includes('organizations') ? 
    db.prepare(`SELECT COUNT(*) as count FROM organizations`).get().count : 0;
  const productCount = tableNames.includes('products') ? 
    db.prepare(`SELECT COUNT(*) as count FROM products WHERE deleted_at IS NULL`).get().count : 0;
  const colorCount = tableNames.includes('colors') ? 
    db.prepare(`SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL`).get().count : 0;
  const productColorCount = tableNames.includes('product_colors') ? 
    db.prepare(`SELECT COUNT(*) as count FROM product_colors`).get().count : 0;

  console.log(`🏢 Organizations: ${organizationCount}`);
  console.log(`📦 Products (active): ${productCount}`);
  console.log(`🎨 Colors (active): ${colorCount}`);
  console.log(`🔗 Product-Color associations: ${productColorCount}`);
  console.log('');

  // 4. Check organization ID formats
  console.log('🆔 Organization ID Analysis:');
  console.log('============================');

  if (organizationCount > 0) {
    // Sample organization IDs
    const sampleOrgs = db.prepare(`SELECT external_id, id FROM organizations LIMIT 3`).all();
    console.log('Sample organization IDs:');
    sampleOrgs.forEach(org => {
      console.log(`  - ID: ${org.id} (internal), External: ${org.external_id} (format: ${typeof org.external_id})`);
    });
  } else {
    console.log('No organizations found in database');
  }

  // Check organization_id formats in different tables
  if (productCount > 0) {
    const sampleProductOrgs = db.prepare(`SELECT DISTINCT organization_id FROM products WHERE deleted_at IS NULL LIMIT 3`).all();
    console.log('Sample product organization_ids:');
    sampleProductOrgs.forEach(row => {
      const orgId = row.organization_id;
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(orgId);
      console.log(`  - ${orgId} (${isUUID ? 'UUID' : 'Not UUID'})`);
    });
  }

  if (colorCount > 0) {
    const sampleColorOrgs = db.prepare(`SELECT DISTINCT organization_id FROM colors WHERE deleted_at IS NULL LIMIT 3`).all();
    console.log('Sample color organization_ids:');
    sampleColorOrgs.forEach(row => {
      const orgId = row.organization_id;
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(orgId);
      console.log(`  - ${orgId} (${isUUID ? 'UUID' : 'Not UUID'})`);
    });
  }

  if (productColorCount > 0) {
    const samplePCOrgs = db.prepare(`SELECT DISTINCT organization_id FROM product_colors LIMIT 3`).all();
    console.log('Sample product_colors organization_ids:');
    samplePCOrgs.forEach(row => {
      const orgId = row.organization_id;
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(orgId);
      console.log(`  - ${orgId} (${isUUID ? 'UUID' : 'Not UUID'})`);
    });
  }
  console.log('');

  // 5. Product-Color relationship analysis by organization
  console.log('🔍 Relationship Analysis by Organization:');
  console.log('========================================');

  const orgStats = db.prepare(`
    SELECT 
      o.external_id as org_id,
      o.name as org_name,
      (SELECT COUNT(*) FROM products p WHERE p.organization_id = o.external_id AND p.deleted_at IS NULL) as product_count,
      (SELECT COUNT(*) FROM colors c WHERE c.organization_id = o.external_id AND c.deleted_at IS NULL) as color_count,
      (SELECT COUNT(*) FROM product_colors pc WHERE pc.organization_id = o.external_id) as relationship_count
    FROM organizations o
    ORDER BY org_name
  `).all();

  orgStats.forEach(stat => {
    console.log(`📋 ${stat.org_name} (${stat.org_id}):`);
    console.log(`  - Products: ${stat.product_count}`);
    console.log(`  - Colors: ${stat.color_count}`);
    console.log(`  - Relationships: ${stat.relationship_count}`);
    
    if (stat.product_count > 0 && stat.color_count > 0 && stat.relationship_count === 0) {
      console.log(`  ⚠️  WARNING: Has products and colors but NO relationships!`);
    }
    console.log('');
  });

  // 6. Detailed breakdown for first organization with issues
  const problematicOrg = orgStats.find(stat => 
    stat.product_count > 0 && stat.color_count > 0 && stat.relationship_count === 0
  );

  if (problematicOrg) {
    console.log(`🔬 Detailed Analysis for ${problematicOrg.org_name}:`);
    console.log('=============================================');

    console.log('Sample products:');
    const sampleProducts = db.prepare(`
      SELECT external_id, name, organization_id 
      FROM products 
      WHERE organization_id = ? AND deleted_at IS NULL 
      LIMIT 3
    `).all(problematicOrg.org_id);
    
    sampleProducts.forEach(product => {
      console.log(`  - ${product.name} (${product.external_id}) org: ${product.organization_id}`);
    });

    console.log('Sample colors:');
    const sampleColors = db.prepare(`
      SELECT external_id, display_name, organization_id 
      FROM colors 
      WHERE organization_id = ? AND deleted_at IS NULL 
      LIMIT 3
    `).all(problematicOrg.org_id);
    
    sampleColors.forEach(color => {
      console.log(`  - ${color.display_name} (${color.external_id}) org: ${color.organization_id}`);
    });

    // Check for orphaned relationships in product_colors
    console.log('Checking for orphaned relationships...');
    const orphanedRelationships = db.prepare(`
      SELECT pc.*, p.external_id as product_external_id, c.external_id as color_external_id
      FROM product_colors pc
      LEFT JOIN products p ON pc.product_id = p.id 
      LEFT JOIN colors c ON pc.color_id = c.id
      WHERE (p.id IS NULL OR c.id IS NULL)
      LIMIT 5
    `).all();

    if (orphanedRelationships.length > 0) {
      console.log('❌ Found orphaned relationships:');
      orphanedRelationships.forEach(rel => {
        console.log(`  - Product ID ${rel.product_id} (${rel.product_external_id || 'MISSING'}) -> Color ID ${rel.color_id} (${rel.color_external_id || 'MISSING'})`);
      });
    } else {
      console.log('✅ No orphaned relationships found');
    }
  }

  // 7. Final recommendations
  console.log('💡 Recommendations:');
  console.log('===================');

  if (productColorCount === 0) {
    console.log('🚨 CRITICAL: No product-color relationships exist in database');
    console.log('   Recommendation: Run data sync or create sample relationships');
  } else if (orgStats.some(stat => stat.product_count > 0 && stat.color_count > 0 && stat.relationship_count === 0)) {
    console.log('⚠️  WARNING: Some organizations have products/colors but no relationships');
    console.log('   Recommendation: Check sync process and organization_id consistency');
  } else {
    console.log('✅ Product-color relationships exist, issue may be in query logic');
    console.log('   Recommendation: Check application logs and organization context');
  }

  db.close();

} catch (error) {
  console.error('❌ Error accessing database:', error.message);
  console.log('');
  console.log('Possible causes:');
  console.log('- Database file does not exist (app not initialized)');
  console.log('- Database is locked by running application');
  console.log('- Permission issues');
  console.log('');
  console.log('Try:');
  console.log('1. Close the ChromaSync application');
  console.log('2. Run this script again');
  console.log('3. Check database file exists at:', dbPath);
}