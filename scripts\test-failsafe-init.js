#!/usr/bin/env node

/**
 * Test the failsafe database initialization specifically
 */

const path = require('path');

// Mock electron app for testing
const mockApp = {
  getPath: (name) => {
    if (name === 'userData') {
      return path.join(require('os').homedir(), 'Library/Application Support/chroma-sync');
    }
    return process.cwd();
  },
  getAppPath: () => process.cwd()
};

// Set up global.app mock
global.app = mockApp;

async function testFailsafeInit() {
  console.log('🔍 Testing Failsafe Database Initialization...');
  
  try {
    // Remove any existing database to test fresh initialization
    const dbPath = path.join(mockApp.getPath('userData'), 'chromasync.db');
    console.log('Database path:', dbPath);
    
    // Import the failsafe module
    const { failsafeInitDatabase } = await import('../out/main/failsafe-init-45e921ed.js');
    
    console.log('\n🚀 Running failsafe initialization...');
    const db = await failsafeInitDatabase();
    
    if (db) {
      console.log('✅ Failsafe initialization succeeded!');
      
      // Test database functionality
      const tables = db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
      `).all();
      
      console.log('📋 Tables created:', tables.map(t => t.name));
      
      // Check for core tables
      const coreTable = ['colors', 'products', 'organizations', 'product_colors'];
      const missingTables = coreTable.filter(table => 
        !tables.some(t => t.name === table)
      );
      
      if (missingTables.length === 0) {
        console.log('✅ All core tables present');
      } else {
        console.log('❌ Missing tables:', missingTables);
      }
      
      db.close();
      return true;
    } else {
      console.log('❌ Failsafe initialization returned null');
      return false;
    }
    
  } catch (error) {
    console.error('\n❌ Failsafe test failed:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });
    return false;
  }
}

// Run the test
testFailsafeInit().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});