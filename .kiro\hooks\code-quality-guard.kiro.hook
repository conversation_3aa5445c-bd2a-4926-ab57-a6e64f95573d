{"enabled": true, "name": "Code Quality Guardian", "description": "Monitors TypeScript, React, and configuration files for changes and provides code quality analysis, type safety checks, and architectural guidance", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.ts", "src/**/*.tsx", "*.config.ts", "*.config.js", "package.json", "tsconfig*.json"]}, "then": {"type": "askAgent", "prompt": "A developer has modified code files in the ChromaSync project. Please analyze the changes for:\n\n1. **Type Safety**: Check for any `any` types, missing type annotations, or TypeScript errors\n2. **Architecture Compliance**: Ensure changes follow the multi-process Electron architecture (main/renderer/preload separation)\n3. **Security**: Validate IPC handlers have proper input validation and SQL queries use prepared statements\n4. **Performance**: Look for potential performance issues, especially in database queries or React components\n5. **Multi-tenant Compliance**: Verify all database operations include `organization_id` scoping\n6. **Code Quality**: Check for proper error handling, consistent naming conventions, and adherence to project patterns\n\nProvide specific, actionable feedback with code examples where helpful. Focus on preventing bugs and maintaining the high code quality standards of this enterprise application."}}