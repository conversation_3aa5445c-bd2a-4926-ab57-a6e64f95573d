/**
 * @file resource-manager.ts
 * @description Comprehensive resource management and cleanup system for sync services
 * 
 * This system manages:
 * - Event listener cleanup
 * - Memory monitoring and automatic cleanup
 * - Shutdown procedures for all sync components
 * - Timer and interval cleanup
 * - Memory leak detection
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';

export interface ResourceTracker {
  id: string;
  type: 'timer' | 'interval' | 'listener' | 'stream' | 'connection' | 'memory';
  resource: any;
  createdAt: number;
  metadata?: Record<string, any>;
}

export interface MemoryMetrics {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
  timestamp: number;
}

export interface CleanupResult {
  success: boolean;
  resourcesCleanedUp: number;
  errors: string[];
  duration: number;
}

/**
 * Resource Manager - Tracks and cleans up all sync-related resources
 */
export class ResourceManager extends EventEmitter {
  private static instance: ResourceManager | null = null;
  
  // Resource tracking
  private trackedResources: Map<string, ResourceTracker> = new Map();
  private eventListeners: Map<string, { emitter: EventEmitter; event: string; listener: Function }[]> = new Map();
  private timers: Set<NodeJS.Timeout> = new Set();
  private intervals: Set<NodeJS.Timeout> = new Set();
  
  // Memory monitoring
  private memoryHistory: MemoryMetrics[] = [];
  private memoryMonitoringInterval: NodeJS.Timeout | null = null;
  private memoryThreshold = 500 * 1024 * 1024; // 500MB threshold
  private memoryCheckInterval = 30000; // 30 seconds
  
  // Cleanup state
  private isShuttingDown = false;
  private cleanupPromise: Promise<void> | null = null;
  
  // Configuration
  private readonly MAX_MEMORY_HISTORY = 100;
  private readonly MEMORY_LEAK_THRESHOLD = 50 * 1024 * 1024; // 50MB growth
  private readonly MEMORY_LEAK_WINDOW = 300000; // 5 minutes
  
  private constructor() {
    super();
    this.startMemoryMonitoring();
    console.log('[ResourceManager] 🧹 Resource manager initialized');
  }
  
  /**
   * Get singleton instance
   */
  static getInstance(): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager();
    }
    return ResourceManager.instance;
  }
  
  /**
   * Track a resource for cleanup
   */
  trackResource(type: ResourceTracker['type'], resource: any, metadata?: Record<string, any>): string {
    const id = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const tracker: ResourceTracker = {
      id,
      type,
      resource,
      createdAt: Date.now(),
      metadata
    };
    
    this.trackedResources.set(id, tracker);
    
    console.log(`[ResourceManager] 📝 Tracking ${type} resource: ${id}`);
    return id;
  }
  
  /**
   * Track an event listener for cleanup
   */
  trackEventListener(emitter: EventEmitter, event: string, listener: Function, context?: string): string {
    const contextKey = context || 'default';
    
    if (!this.eventListeners.has(contextKey)) {
      this.eventListeners.set(contextKey, []);
    }
    
    this.eventListeners.get(contextKey)!.push({ emitter, event, listener });
    
    const trackerId = this.trackResource('listener', { emitter, event, listener }, { context });
    
    console.log(`[ResourceManager] 🎧 Tracking event listener: ${event} (context: ${contextKey})`);
    return trackerId;
  }
  
  /**
   * Track a timer for cleanup
   */
  trackTimer(timer: NodeJS.Timeout, metadata?: Record<string, any>): string {
    this.timers.add(timer);
    const trackerId = this.trackResource('timer', timer, metadata);
    
    console.log(`[ResourceManager] ⏰ Tracking timer: ${trackerId}`);
    return trackerId;
  }
  
  /**
   * Track an interval for cleanup
   */
  trackInterval(interval: NodeJS.Timeout, metadata?: Record<string, any>): string {
    this.intervals.add(interval);
    const trackerId = this.trackResource('interval', interval, metadata);
    
    console.log(`[ResourceManager] 🔄 Tracking interval: ${trackerId}`);
    return trackerId;
  }
  
  /**
   * Untrack a resource (when manually cleaned up)
   */
  untrackResource(id: string): boolean {
    const tracker = this.trackedResources.get(id);
    if (!tracker) {
      return false;
    }
    
    // Remove from specific collections
    if (tracker.type === 'timer') {
      this.timers.delete(tracker.resource);
    } else if (tracker.type === 'interval') {
      this.intervals.delete(tracker.resource);
    }
    
    this.trackedResources.delete(id);
    console.log(`[ResourceManager] ✅ Untracked ${tracker.type} resource: ${id}`);
    return true;
  }
  
  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitoringInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, this.memoryCheckInterval);
    
    this.trackInterval(this.memoryMonitoringInterval, { purpose: 'memory_monitoring' });
  }
  
  /**
   * Check current memory usage and detect leaks
   */
  private checkMemoryUsage(): void {
    const memUsage = process.memoryUsage();
    const metrics: MemoryMetrics = {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      timestamp: Date.now()
    };
    
    this.memoryHistory.push(metrics);
    
    // Keep only recent history
    if (this.memoryHistory.length > this.MAX_MEMORY_HISTORY) {
      this.memoryHistory = this.memoryHistory.slice(-this.MAX_MEMORY_HISTORY);
    }
    
    // Check for memory threshold breach
    if (metrics.heapUsed > this.memoryThreshold) {
      console.warn(`[ResourceManager] ⚠️ Memory usage high: ${Math.round(metrics.heapUsed / 1024 / 1024)}MB`);
      this.emit('memory-threshold-exceeded', metrics);
      this.performAutomaticCleanup();
    }
    
    // Check for memory leaks
    this.detectMemoryLeaks();
  }
  
  /**
   * Detect potential memory leaks
   */
  private detectMemoryLeaks(): void {
    if (this.memoryHistory.length < 10) {
      return; // Need more data points
    }
    
    const recent = this.memoryHistory.slice(-10);
    const oldest = recent[0];
    const newest = recent[recent.length - 1];
    
    const growth = newest.heapUsed - oldest.heapUsed;
    const timeWindow = newest.timestamp - oldest.timestamp;
    
    // Check if memory grew significantly over time window
    if (growth > this.MEMORY_LEAK_THRESHOLD && timeWindow > this.MEMORY_LEAK_WINDOW) {
      const growthRate = growth / (timeWindow / 1000); // bytes per second
      
      console.warn(`[ResourceManager] 🚨 Potential memory leak detected:`);
      console.warn(`  Growth: ${Math.round(growth / 1024 / 1024)}MB over ${Math.round(timeWindow / 1000)}s`);
      console.warn(`  Rate: ${Math.round(growthRate / 1024)}KB/s`);
      
      this.emit('memory-leak-detected', {
        growth,
        timeWindow,
        growthRate,
        currentUsage: newest.heapUsed
      });
      
      // Trigger automatic cleanup
      this.performAutomaticCleanup();
    }
  }
  
  /**
   * Perform automatic cleanup when memory issues are detected
   */
  private async performAutomaticCleanup(): Promise<void> {
    console.log('[ResourceManager] 🧹 Performing automatic cleanup due to memory pressure');
    
    try {
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
        console.log('[ResourceManager] 🗑️ Forced garbage collection');
      }
      
      // Clean up old tracked resources
      await this.cleanupStaleResources();
      
      // Emit cleanup event for other components
      this.emit('automatic-cleanup-performed');
      
    } catch (error) {
      console.error('[ResourceManager] ❌ Automatic cleanup failed:', error);
    }
  }
  
  /**
   * Clean up stale resources (older than 1 hour)
   */
  private async cleanupStaleResources(): Promise<number> {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    let cleanedUp = 0;
    
    for (const [id, tracker] of this.trackedResources.entries()) {
      if (tracker.createdAt < oneHourAgo) {
        try {
          await this.cleanupResource(tracker);
          this.trackedResources.delete(id);
          cleanedUp++;
        } catch (error) {
          console.error(`[ResourceManager] ❌ Failed to cleanup stale resource ${id}:`, error);
        }
      }
    }
    
    if (cleanedUp > 0) {
      console.log(`[ResourceManager] 🧹 Cleaned up ${cleanedUp} stale resources`);
    }
    
    return cleanedUp;
  }
  
  /**
   * Clean up a specific resource
   */
  private async cleanupResource(tracker: ResourceTracker): Promise<void> {
    switch (tracker.type) {
      case 'timer':
        clearTimeout(tracker.resource);
        this.timers.delete(tracker.resource);
        break;
        
      case 'interval':
        clearInterval(tracker.resource);
        this.intervals.delete(tracker.resource);
        break;
        
      case 'listener':
        const { emitter, event, listener } = tracker.resource;
        if (emitter && typeof emitter.removeListener === 'function') {
          emitter.removeListener(event, listener);
        }
        break;
        
      case 'stream':
        if (tracker.resource && typeof tracker.resource.destroy === 'function') {
          tracker.resource.destroy();
        }
        break;
        
      case 'connection':
        if (tracker.resource && typeof tracker.resource.close === 'function') {
          await tracker.resource.close();
        }
        break;
        
      default:
        // Generic cleanup for objects with cleanup method
        if (tracker.resource && typeof tracker.resource.cleanup === 'function') {
          await tracker.resource.cleanup();
        }
    }
  }
  
  /**
   * Get current memory metrics
   */
  getMemoryMetrics(): MemoryMetrics {
    const memUsage = process.memoryUsage();
    return {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss,
      timestamp: Date.now()
    };
  }
  
  /**
   * Get memory history for analysis
   */
  getMemoryHistory(): MemoryMetrics[] {
    return [...this.memoryHistory];
  }
  
  /**
   * Get resource tracking statistics
   */
  getResourceStats(): {
    totalTracked: number;
    byType: Record<string, number>;
    timers: number;
    intervals: number;
    listeners: number;
  } {
    const byType: Record<string, number> = {};
    
    for (const tracker of this.trackedResources.values()) {
      byType[tracker.type] = (byType[tracker.type] || 0) + 1;
    }
    
    return {
      totalTracked: this.trackedResources.size,
      byType,
      timers: this.timers.size,
      intervals: this.intervals.size,
      listeners: Array.from(this.eventListeners.values()).reduce((sum, listeners) => sum + listeners.length, 0)
    };
  }
  
  /**
   * Perform comprehensive cleanup of all resources
   */
  async cleanup(): Promise<CleanupResult> {
    if (this.isShuttingDown) {
      // Return existing cleanup promise if already shutting down
      if (this.cleanupPromise) {
        await this.cleanupPromise;
      }
      return {
        success: true,
        resourcesCleanedUp: 0,
        errors: [],
        duration: 0
      };
    }
    
    this.isShuttingDown = true;
    const startTime = performance.now();
    const errors: string[] = [];
    let resourcesCleanedUp = 0;
    
    console.log('[ResourceManager] 🧹 Starting comprehensive cleanup...');
    
    this.cleanupPromise = (async () => {
      try {
        // Stop memory monitoring first
        if (this.memoryMonitoringInterval) {
          clearInterval(this.memoryMonitoringInterval);
          this.memoryMonitoringInterval = null;
        }
        
        // Clean up all timers
        console.log(`[ResourceManager] ⏰ Cleaning up ${this.timers.size} timers...`);
        for (const timer of this.timers) {
          try {
            clearTimeout(timer);
            resourcesCleanedUp++;
          } catch (error) {
            errors.push(`Timer cleanup failed: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
        this.timers.clear();
        
        // Clean up all intervals
        console.log(`[ResourceManager] 🔄 Cleaning up ${this.intervals.size} intervals...`);
        for (const interval of this.intervals) {
          try {
            clearInterval(interval);
            resourcesCleanedUp++;
          } catch (error) {
            errors.push(`Interval cleanup failed: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
        this.intervals.clear();
        
        // Clean up all event listeners
        console.log(`[ResourceManager] 🎧 Cleaning up event listeners...`);
        for (const [context, listeners] of this.eventListeners.entries()) {
          console.log(`[ResourceManager] 🎧 Cleaning up ${listeners.length} listeners for context: ${context}`);
          for (const { emitter, event, listener } of listeners) {
            try {
              if (emitter && typeof emitter.removeListener === 'function') {
                emitter.removeListener(event, listener);
                resourcesCleanedUp++;
              }
            } catch (error) {
              errors.push(`Event listener cleanup failed: ${error instanceof Error ? error.message : String(error)}`);
            }
          }
        }
        this.eventListeners.clear();
        
        // Clean up all tracked resources
        console.log(`[ResourceManager] 📝 Cleaning up ${this.trackedResources.size} tracked resources...`);
        for (const [id, tracker] of this.trackedResources.entries()) {
          try {
            await this.cleanupResource(tracker);
            resourcesCleanedUp++;
          } catch (error) {
            errors.push(`Resource cleanup failed for ${id}: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
        this.trackedResources.clear();
        
        // Remove all event listeners from this EventEmitter
        this.removeAllListeners();
        
        console.log(`[ResourceManager] ✅ Cleanup completed: ${resourcesCleanedUp} resources cleaned up`);
        if (errors.length > 0) {
          console.warn(`[ResourceManager] ⚠️ Cleanup had ${errors.length} errors:`, errors);
        }
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error('[ResourceManager] ❌ Cleanup failed:', error);
        errors.push(`Cleanup failed: ${errorMessage}`);
      }
    })();
    
    await this.cleanupPromise;
    
    const duration = performance.now() - startTime;
    
    return {
      success: errors.length === 0,
      resourcesCleanedUp,
      errors,
      duration
    };
  }
  
  /**
   * Check if resource manager is shutting down
   */
  isShuttingDownState(): boolean {
    return this.isShuttingDown;
  }
  
  /**
   * Reset the resource manager (for testing)
   */
  reset(): void {
    this.trackedResources.clear();
    this.eventListeners.clear();
    this.timers.clear();
    this.intervals.clear();
    this.memoryHistory = [];
    this.isShuttingDown = false;
    this.cleanupPromise = null;
    
    if (this.memoryMonitoringInterval) {
      clearInterval(this.memoryMonitoringInterval);
      this.memoryMonitoringInterval = null;
    }
    
    this.removeAllListeners();
    console.log('[ResourceManager] 🔄 Resource manager reset');
  }
}

// Export singleton instance
export const resourceManager = ResourceManager.getInstance();