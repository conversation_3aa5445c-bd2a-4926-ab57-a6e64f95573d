/**
 * Simplified Database Initialization
 * Replaces complex migration system with single schema initialization
 * Fast startup: ~500ms vs 5-15 seconds with migrations
 */

import * as path from 'path';
import * as fs from 'fs';
import { app } from 'electron';
import { COMPLETE_SCHEMA } from './schemas/complete-schema';

/**
 * Get the database file path
 */
export function getDatabasePath(): string {
  const userDataPath = app.getPath('userData');
  const dbPath = path.join(userDataPath, 'chromasync.db');
  
  // Ensure directory exists
  const dir = path.dirname(dbPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  return dbPath;
}

/**
 * Initialize database with complete schema
 * Single fast initialization instead of 21+ migrations
 */
export async function initializeDatabase(): Promise<any | null> {
  const dbPath = getDatabasePath();
  console.log(`[DB] Initializing database at: ${dbPath}`);
  
  const startTime = Date.now();
  
  try {
    // Use require for better-sqlite3 to ensure it's treated as external
    console.log('[DB] Loading better-sqlite3 module...');
    const Database = require('better-sqlite3');
    console.log('[DB] better-sqlite3 module loaded successfully');
    
    // Create database connection
    console.log('[DB] Creating database connection...');
    console.log('[DB] Database path:', dbPath);
    const db = new Database(dbPath);
    console.log('[DB] Database connection created successfully');
    
    // Configure for performance
    console.log('[DB] Configuring database settings...');
    db.pragma('journal_mode = WAL');
    db.pragma('synchronous = NORMAL');
    db.pragma('cache_size = 10000');
    db.pragma('foreign_keys = ON');
    db.pragma('temp_store = MEMORY');
    console.log('[DB] Database configuration complete');
    
    // Check if this is a new database
    const tableCount = db.prepare(`
      SELECT COUNT(*) as count 
      FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `).get() as { count: number };
    
    if (tableCount.count === 0) {
      console.log('[DB] New database detected, creating complete schema...');
      console.log('[DB] Schema length:', COMPLETE_SCHEMA.length, 'characters');
      
      // Execute complete schema in transaction for speed
      const transaction = db.transaction(() => {
        console.log('[DB] Executing schema creation...');
        db.exec(COMPLETE_SCHEMA);
        console.log('[DB] Schema creation completed');
      });
      
      console.log('[DB] Starting transaction...');
      transaction();
      console.log('[DB] Transaction completed');
      
      // Force WAL checkpoint to ensure schema is written to main database file
      console.log('[DB] Forcing WAL checkpoint to persist schema...');
      try {
        db.pragma('wal_checkpoint(FULL)');
        console.log('[DB] WAL checkpoint completed successfully');
      } catch (error) {
        console.error('[DB] WAL checkpoint failed:', error);
      }
      
      const duration = Date.now() - startTime;
      console.log(`[DB] ✅ Database initialized successfully in ${duration}ms`);
    } else {
      // Existing database - verify schema is up to date
      const schemaVersion = getSchemaVersion(db);
      console.log(`[DB] Existing database found (schema version: ${schemaVersion})`);
      
      // Ensure all required tables exist
      ensureRequiredTables(db);
      
      const duration = Date.now() - startTime;
      console.log(`[DB] ✅ Database verified in ${duration}ms`);
    }
    
    // Initialize performance optimizations (includes color_usage_counts table)
    try {
      console.log('[DB] Initializing performance optimizations...');
      const { createPerformanceInitializer } = await import('./performance/performance-initializer');
      const performanceInitializer = createPerformanceInitializer(db);
      
      const result = await performanceInitializer.initializeOptimizations({
        skipIfOptimized: true,
        refreshAggregationTables: true
      });
      
      if (result.success) {
        console.log(`[DB] ✅ Performance optimizations applied: ${result.optimizationsApplied.join(', ')}`);
      } else {
        console.warn('[DB] ⚠️ Performance optimizations failed:', result.errors);
      }
    } catch (error) {
      console.warn('[DB] ⚠️ Performance optimization failed (continuing without optimization):', error);
    }
    
    return db;
    
  } catch (error) {
    console.error('[DB] ❌ Database initialization failed:', error);
    console.error('[DB] Error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as any)?.code,
      errno: (error as any)?.errno
    });
    return null;
  }
}

/**
 * Get current schema version from migrations table
 */
function getSchemaVersion(db: any): number {
  try {
    const result = db.prepare(`
      SELECT MAX(version) as version 
      FROM schema_migrations
    `).get() as { version: number } | undefined;
    
    return result?.version || 0;
  } catch {
    // schema_migrations table doesn't exist
    return 0;
  }
}

/**
 * Ensure all required tables exist (for existing databases)
 */
function ensureRequiredTables(db: any): void {
  const requiredTables = [
    'products',
    'colors', 
    'product_colors',
    'organizations',
    'organization_members',
    'color_sources',
    'schema_migrations'
  ];
  
  const existingTables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name NOT LIKE 'sqlite_%'
  `).all().map((row: any) => row.name);
  
  const missingTables = requiredTables.filter(
    table => !existingTables.includes(table)
  );
  
  if (missingTables.length > 0) {
    console.log(`[DB] Missing tables detected: ${missingTables.join(', ')}`);
    console.log('[DB] Creating missing tables from complete schema...');
    
    // Execute complete schema (CREATE IF NOT EXISTS will handle existing tables)
    db.exec(COMPLETE_SCHEMA);
  }
}

/**
 * Optimize database for better performance
 */
export function optimizeDatabase(db: any): void {
  console.log('[DB] Running optimization...');
  
  try {
    // Analyze query plans
    db.exec('ANALYZE');
    
    // Checkpoint WAL
    db.pragma('wal_checkpoint(TRUNCATE)');
    
    // Optimize pragma
    db.pragma('optimize');
    
    console.log('[DB] ✅ Database optimization complete');
  } catch (error) {
    console.error('[DB] ⚠️ Optimization failed:', error);
  }
}

/**
 * Get database statistics
 */
export function getDatabaseStats(db: any): any {
  const dbPath = getDatabasePath();
  
  try {
    const fileSize = fs.statSync(dbPath).size;
    const pagePragma = db.pragma('page_count') as Array<{ page_count: number }>;
    const sizePragma = db.pragma('page_size') as Array<{ page_size: number }>;
    const pageCount = pagePragma[0]?.page_count ?? 0;
    const pageSize = sizePragma[0]?.page_size ?? 0;
    
    const productCount = db.prepare('SELECT COUNT(*) as count FROM products WHERE deleted_at IS NULL').get() as { count: number };
    const colorCount = db.prepare('SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL').get() as { count: number };
    const orgCount = db.prepare('SELECT COUNT(*) as count FROM organizations').get() as { count: number };
    
    return {
      fileSize: Math.round(fileSize / 1024 / 1024 * 100) / 100, // MB
      pageCount,
      pageSize,
      productCount: productCount.count,
      colorCount: colorCount.count,
      organizationCount: orgCount.count,
      schemaVersion: getSchemaVersion(db)
    };
  } catch (error) {
    console.error('[DB] Error getting stats:', error);
    return null;
  }
}