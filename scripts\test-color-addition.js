/**
 * @file test-color-addition.js
 * @description Test script to verify color addition functionality works correctly
 */

const Database = require('better-sqlite3');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

function getUserDataPath() {
  return path.join(require('os').homedir(), 'Library', 'Application Support', 'chroma-sync');
}

function getDbPath() {
  return path.join(getUserDataPath(), 'chromasync.db');
}

function testColorAddition() {
  const dbPath = getDbPath();
  console.log(`Testing color addition with database: ${dbPath}`);
  
  const db = new Database(dbPath);
  
  try {
    // Get organization
    const orgStmt = db.prepare('SELECT id, external_id FROM organizations LIMIT 1');
    const org = orgStmt.get();
    
    if (!org) {
      console.error('❌ No organizations found');
      return;
    }
    
    console.log(`✅ Using organization: ${org.external_id} (internal ID: ${org.id})`);
    
    // Test color data
    const testColor = {
      external_id: 'test-color-' + Date.now(),
      name: 'Test Color Blue',
      display_name: 'Test Color Blue',
      code: 'TEST-BLUE-001',
      hex: '#0066CC',
      source_id: 1,
      color_spaces: '{"cmyk":{"c":100,"m":50,"y":0,"k":20}}',
      is_gradient: 0,
      is_metallic: 0,
      is_effect: 0,
      gradient_colors: null,
      notes: 'Test color for verification',
      tags: null,
      is_library: 0,
      properties: '{"product":"Test Product"}',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      organization_id: org.id, // Use internal integer ID
      created_by: null,
      user_id: null,
      deleted_at: null,
      device_id: null,
      conflict_resolved_at: null,
      is_synced: 0
    };
    
    console.log('🔍 Inserting test color...');
    
    const insertStmt = db.prepare(`
      INSERT INTO colors (
        external_id, name, display_name, code, hex, source_id, color_spaces,
        is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
        is_library, properties, created_at, updated_at, organization_id,
        created_by, user_id, deleted_at, device_id, conflict_resolved_at, is_synced
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = insertStmt.run(
      testColor.external_id,
      testColor.name,
      testColor.display_name,
      testColor.code,
      testColor.hex,
      testColor.source_id,
      testColor.color_spaces,
      testColor.is_gradient,
      testColor.is_metallic,
      testColor.is_effect,
      testColor.gradient_colors,
      testColor.notes,
      testColor.tags,
      testColor.is_library,
      testColor.properties,
      testColor.created_at,
      testColor.updated_at,
      testColor.organization_id,
      testColor.created_by,
      testColor.user_id,
      testColor.deleted_at,
      testColor.device_id,
      testColor.conflict_resolved_at,
      testColor.is_synced
    );
    
    console.log(`✅ Color inserted successfully with ID: ${result.lastInsertRowid}`);
    
    // Verify the color was inserted
    const verifyStmt = db.prepare(`
      SELECT external_id, name, code, hex, organization_id 
      FROM colors 
      WHERE external_id = ?
    `);
    
    const insertedColor = verifyStmt.get(testColor.external_id);
    if (insertedColor) {
      console.log('✅ Color verification successful:');
      console.log(`   - External ID: ${insertedColor.external_id}`);
      console.log(`   - Name: ${insertedColor.name}`);
      console.log(`   - Code: ${insertedColor.code}`);
      console.log(`   - Hex: ${insertedColor.hex}`);
      console.log(`   - Organization ID: ${insertedColor.organization_id}`);
    } else {
      console.error('❌ Failed to verify inserted color');
    }
    
    // Clean up test color
    const deleteStmt = db.prepare('DELETE FROM colors WHERE external_id = ?');
    deleteStmt.run(testColor.external_id);
    console.log('✅ Test color cleaned up');
    
    console.log('\n🎉 Color addition test completed successfully!');
    
  } catch (error) {
    console.error('❌ Color addition test failed:', error.message);
    console.error('Error details:', error);
  } finally {
    db.close();
  }
}

if (require.main === module) {
  testColorAddition();
}

module.exports = { testColorAddition };