/**
 * @file AppProviders.tsx
 * @description Centralized provider wrapper for global application state
 */

import React, { ReactNode } from 'react';
import { ErrorBoundary } from '../common/ErrorBoundary';

interface AppProvidersProps {
  children: ReactNode;
}

/**
 * Application providers wrapper
 */
export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={(error, errorInfo) => (
        <div className='flex items-center justify-center h-screen bg-ui-background-primary'>
          <div className='text-center p-8 max-w-md'>
            <h1 className='text-2xl font-bold text-ui-foreground-primary mb-4'>
              Application Error
            </h1>
            <p className='text-ui-foreground-secondary mb-4'>
              An unexpected error occurred. Please restart the application.
            </p>
            <details className='text-left text-sm text-ui-foreground-tertiary'>
              <summary className='cursor-pointer mb-2'>Error Details</summary>
              <pre className='whitespace-pre-wrap break-words'>
                {error.message}
                {'\n\n'}
                {errorInfo?.componentStack}
              </pre>
            </details>
            <button
              onClick={() => window.location.reload()}
              className='mt-4 px-4 py-2 bg-brand-primary text-white rounded-lg hover:bg-brand-primary-dark transition-colors'
            >
              Restart Application
            </button>
          </div>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  );
};

export default AppProviders;
