/**
 * @file ErrorLogsSection.tsx
 * @description Error logs display and management section component
 */

import React, { useState, useEffect } from 'react';
import { AlertTriangle, Download, Trash2, RefreshCw } from 'lucide-react';

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'error' | 'warn' | 'info';
  message: string;
  details?: string;
}

/**
 * Error logs section component
 */
export const ErrorLogsSection: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchLogs = async () => {
    setIsLoading(true);
    try {
      // This would call the IPC method to get application logs
      const response = await window.api.getApplicationLogs();
      if (response.success && Array.isArray(response.logs)) {
        setLogs(response.logs);
      } else {
        console.error('Failed to fetch logs:', response.error);
        setLogs([]);
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error);
      setLogs([]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = async () => {
    try {
      await window.api.clearApplicationLogs();
      setLogs([]);
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  };

  const exportLogs = async () => {
    try {
      await window.api.exportApplicationLogs();
    } catch (error) {
      console.error('Failed to export logs:', error);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, []);

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <AlertTriangle size={14} className='text-feedback-error' />;
      case 'warn':
        return <AlertTriangle size={14} className='text-feedback-warning' />;
      default:
        return <AlertTriangle size={14} className='text-brand-primary' />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-feedback-error dark:text-red-400';
      case 'warn':
        return 'text-feedback-warning dark:text-yellow-400';
      default:
        return 'text-brand-primary dark:text-blue-400';
    }
  };

  return (
    <section>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        Error Logs
      </h3>

      <div className='space-y-4'>
        {/* Controls */}
        <div className='flex space-x-2'>
          <button
            onClick={fetchLogs}
            disabled={isLoading}
            className='flex items-center px-3 py-2 bg-ui-background-secondary dark:bg-zinc-800 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors disabled:opacity-50'
          >
            <RefreshCw
              size={14}
              className={`mr-2 ${isLoading ? 'animate-spin' : ''}`}
            />
            Refresh
          </button>

          <button
            onClick={exportLogs}
            className='flex items-center px-3 py-2 bg-ui-background-secondary dark:bg-zinc-800 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors'
          >
            <Download size={14} className='mr-2' />
            Export
          </button>

          <button
            onClick={clearLogs}
            className='flex items-center px-3 py-2 bg-feedback-error dark:bg-red-600 text-white rounded-[var(--radius-md)] hover:bg-feedback-error/90 dark:hover:bg-red-700 transition-colors'
          >
            <Trash2 size={14} className='mr-2' />
            Clear All
          </button>
        </div>

        {/* Logs Display */}
        <div className='bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] border border-ui-border-light dark:border-zinc-600'>
          {isLoading ? (
            <div className='p-4 text-center text-ui-foreground-secondary dark:text-gray-400'>
              Loading logs...
            </div>
          ) : !Array.isArray(logs) || logs.length === 0 ? (
            <div className='p-4 text-center text-ui-foreground-secondary dark:text-gray-400'>
              No log entries found
            </div>
          ) : (
            <div className='max-h-96 overflow-y-auto'>
              <div className='divide-y divide-ui-border-light dark:divide-zinc-600'>
                {logs.map(log => (
                  <div key={log.id} className='p-3'>
                    <div className='flex items-start justify-between'>
                      <div className='flex items-start flex-1 min-w-0'>
                        <div className='flex items-center mr-2 mt-0.5'>
                          {getLevelIcon(log.level)}
                        </div>
                        <div className='flex-1 min-w-0'>
                          <div className='flex items-center mb-1'>
                            <span
                              className={`text-xs font-medium uppercase tracking-wider ${getLevelColor(log.level)} mr-2`}
                            >
                              {log.level}
                            </span>
                            <span className='text-xs text-ui-foreground-secondary dark:text-gray-400'>
                              {new Date(log.timestamp).toLocaleString()}
                            </span>
                          </div>
                          <p className='text-sm text-ui-foreground-primary dark:text-gray-300 break-words'>
                            {log.message}
                          </p>
                          {log.details && (
                            <details className='mt-2'>
                              <summary className='text-xs text-ui-foreground-secondary dark:text-gray-400 cursor-pointer hover:text-ui-foreground-primary dark:hover:text-gray-300'>
                                Show details
                              </summary>
                              <pre className='text-xs text-ui-foreground-secondary dark:text-gray-400 mt-1 whitespace-pre-wrap font-mono bg-ui-background-tertiary dark:bg-zinc-900 p-2 rounded overflow-x-auto'>
                                {log.details}
                              </pre>
                            </details>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Log Info */}
        <div className='text-xs text-ui-foreground-secondary dark:text-gray-400'>
          <p>
            Logs are automatically collected to help diagnose issues. Personal
            data is not included in error logs.
          </p>
        </div>
      </div>
    </section>
  );
};

export default ErrorLogsSection;
