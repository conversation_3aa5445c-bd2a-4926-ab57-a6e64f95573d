/**
 * @file color-analytics-performance.test.ts
 * @description Performance tests for ColorAnalyticsService with large datasets
 * 
 * Tests analytics performance with realistic large datasets to ensure
 * the service can handle production-scale data efficiently.
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { ColorAnalyticsService } from '../color-analytics.service';

// Mock database for performance testing
const mockDb = {
  prepare: vi.fn(() => ({
    get: vi.fn(),
    run: vi.fn(),
    all: vi.fn()
  }))
} as any;

// Mock repository with large dataset simulation
let mockColorRepository: any;
let analyticsService: ColorAnalyticsService;

describe('ColorAnalyticsService Performance Tests', () => {
  beforeEach(() => {
    mockColorRepository = {
      getUsageCounts: vi.fn(),
      getColorNameProductMap: vi.fn()
    };
    
    // Mock the product map to return empty map by default
    mockColorRepository.getColorNameProductMap.mockReturnValue(new Map());
    
    analyticsService = new ColorAnalyticsService(mockDb, mockColorRepository);
  });

  describe('Large Dataset Performance', () => {
    test('should handle 10,000 colors efficiently', () => {
      // Generate large dataset (10K colors)
      const largeUsageMap = new Map();
      for (let i = 0; i < 10000; i++) {
        const usage = Math.floor(Math.random() * 50) + 1;
        const products = Array.from({length: Math.min(usage, 10)}, (_, j) => `Product-${i}-${j}`);
        largeUsageMap.set(`Color-${i}`, { count: usage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(largeUsageMap);
      
      const startTime = performance.now();
      const result = analyticsService.getUsageStatistics('large-org');
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      expect(result.data.totalColors).toBe(10000);
      expect(result.metadata.processingTimeMs).toBeLessThan(1000); // Under 1 second
      expect(executionTime).toBeLessThan(1000); // Actual execution under 1 second
      console.log(`10K colors statistics: ${executionTime.toFixed(2)}ms`);
    });

    test('should handle 50,000 colors with pagination', () => {
      // Generate very large dataset (50K colors)
      const veryLargeUsageMap = new Map();
      for (let i = 0; i < 50000; i++) {
        const usage = Math.floor(Math.random() * 100) + 1;
        const productCount = Math.min(usage, 20);
        const products = Array.from({length: productCount}, (_, j) => `Product-${i}-${j}`);
        veryLargeUsageMap.set(`Color-${i}`, { count: usage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(veryLargeUsageMap);
      
      const startTime = performance.now();
      const result = analyticsService.getUsageStatisticsPaginated('very-large-org', { limit: 1000, offset: 0 });
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      expect(result.data.statistics.totalColors).toBe(50000);
      expect(executionTime).toBeLessThan(2000); // Under 2 seconds for 50K
      console.log(`50K colors paginated statistics: ${executionTime.toFixed(2)}ms`);
    });

    test('should handle popularity rankings efficiently with large datasets', () => {
      // Generate dataset with varied popularity
      const popularityMap = new Map();
      const productMap = new Map();
      
      for (let i = 0; i < 25000; i++) {
        // Create realistic distribution: few very popular, many with low usage
        let usage: number;
        if (i < 100) {
          usage = Math.floor(Math.random() * 500) + 100; // Very popular
        } else if (i < 1000) {
          usage = Math.floor(Math.random() * 100) + 20; // Popular
        } else {
          usage = Math.floor(Math.random() * 20) + 1; // Normal
        }
        
        const products = Array.from({length: Math.min(usage, 15)}, (_, j) => `Product-${i}-${j}`);
        const colorName = `Color-${i}`;
        popularityMap.set(colorName, { count: usage, products });
        productMap.set(colorName, products);
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(popularityMap);
      mockColorRepository.getColorNameProductMap.mockReturnValue(productMap);
      
      const startTime = performance.now();
      const result = analyticsService.getPopularityRankings('popularity-org', { limit: 100 });
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      expect(result.data).toHaveLength(100);
      expect(executionTime).toBeLessThan(500); // Ranking should be fast
      expect(result.data[0].usageCount).toBeGreaterThan(result.data[99].usageCount);
      console.log(`25K colors popularity ranking: ${executionTime.toFixed(2)}ms`);
    });

    test('should handle trend analysis with large historical datasets', () => {
      // Generate large dataset for trend analysis
      const trendMap = new Map();
      for (let i = 0; i < 15000; i++) {
        const currentUsage = Math.floor(Math.random() * 30) + 1;
        const products = Array.from({length: Math.min(currentUsage, 8)}, (_, j) => `Product-${i}-${j}`);
        trendMap.set(`Color-${i}`, { count: currentUsage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(trendMap);
      
      const startTime = performance.now();
      const result = analyticsService.getUsageTrends('trend-org', { timePeriodDays: 30, limit: 1000 });
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      expect(result.data).toHaveLength(1000);
      expect(executionTime).toBeLessThan(800); // Trend analysis should be efficient
      console.log(`15K colors trend analysis: ${executionTime.toFixed(2)}ms`);
    });
  });

  describe('Memory Efficiency Tests', () => {
    test('should not accumulate memory during repeated large operations', () => {
      const mediumUsageMap = new Map();
      for (let i = 0; i < 5000; i++) {
        const usage = Math.floor(Math.random() * 25) + 1;
        const products = Array.from({length: Math.min(usage, 6)}, (_, j) => `Product-${i}-${j}`);
        mediumUsageMap.set(`Color-${i}`, { count: usage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(mediumUsageMap);
      
      // Measure baseline memory (simulated)
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Run multiple operations
      const iterations = 100;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        analyticsService.getUsageStatistics('memory-test-org');
        analyticsService.getPopularityRankings('memory-test-org', { limit: 50 });
        
        // Clear any potential references every 10 iterations
        if (i % 10 === 0) {
          global.gc && global.gc(); // Force garbage collection if available
        }
      }
      
      const endTime = performance.now();
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const avgTimePerIteration = (endTime - startTime) / iterations;
      
      expect(avgTimePerIteration).toBeLessThan(50); // Each operation should be fast
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB increase
      console.log(`Memory test - Avg time per iteration: ${avgTimePerIteration.toFixed(2)}ms, Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });

    test('should handle pagination efficiently without loading entire dataset', () => {
      // Simulate very large dataset that would be problematic if loaded entirely
      const hugeUsageMap = new Map();
      for (let i = 0; i < 100000; i++) {
        const usage = Math.floor(Math.random() * 10) + 1;
        const products = [`Product-${i}`];
        hugeUsageMap.set(`Color-${i}`, { count: usage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(hugeUsageMap);
      
      // Test multiple pagination requests
      const pageSize = 1000;
      const totalPages = 5; // Test first 5 pages
      const startTime = performance.now();
      
      for (let page = 0; page < totalPages; page++) {
        const offset = page * pageSize;
        const result = analyticsService.getUsageStatisticsPaginated('huge-org', {
          limit: pageSize,
          offset: offset
        });
        
        expect(result.data.statistics.totalColors).toBe(100000);
        // Note: Current implementation returns hasMore: false (placeholder)
        expect(typeof result.data.hasMore).toBe('boolean');
      }
      
      const endTime = performance.now();
      const avgTimePerPage = (endTime - startTime) / totalPages;
      
      expect(avgTimePerPage).toBeLessThan(200); // Each page should load quickly
      console.log(`Pagination test - Avg time per page: ${avgTimePerPage.toFixed(2)}ms`);
    });
  });

  describe('Performance Metrics and Monitoring', () => {
    test('should provide accurate performance metrics for large datasets', () => {
      const performanceMap = new Map();
      for (let i = 0; i < 20000; i++) {
        const usage = Math.floor(Math.random() * 40) + 1;
        const products = Array.from({length: Math.min(usage, 12)}, (_, j) => `Product-${i}-${j}`);
        performanceMap.set(`Color-${i}`, { count: usage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(performanceMap);
      
      const startTime = performance.now();
      const metrics = analyticsService.getPerformanceMetrics('performance-org');
      const endTime = performance.now();
      
      expect(metrics.datasetSize).toBe(20000);
      expect(metrics.averageQueryTime).toBeGreaterThanOrEqual(0); // Allow 0 for very fast operations
      expect(metrics.memoryUsage).toMatch(/\d+(\.\d+)?\s+(B|KB|MB|GB)/);
      expect(metrics.optimizationSuggestions).toBeInstanceOf(Array);
      
      // Check for appropriate suggestions based on dataset size
      expect(metrics.optimizationSuggestions.some(s => 
        s.includes('pagination') || s.includes('indexing')
      )).toBe(true);
      
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(100);
      console.log(`Performance metrics for 20K colors: ${executionTime.toFixed(2)}ms`);
    });

    test('should suggest optimizations for very large datasets', () => {
      const veryLargeMap = new Map();
      for (let i = 0; i < 75000; i++) {
        veryLargeMap.set(`Color-${i}`, { count: 1, products: ['Product'] });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(veryLargeMap);
      
      const metrics = analyticsService.getPerformanceMetrics('optimization-org');
      
      expect(metrics.datasetSize).toBe(75000);
      expect(metrics.optimizationSuggestions).toContain('Consider implementing database-level pagination');
      expect(metrics.optimizationSuggestions).toContain('Implement caching for frequently accessed analytics');
      expect(metrics.optimizationSuggestions).toContain('Consider background processing for complex analytics');
    });
  });

  describe('Stress Testing', () => {
    test('should handle concurrent analytics operations', async () => {
      const concurrentMap = new Map();
      for (let i = 0; i < 8000; i++) {
        const usage = Math.floor(Math.random() * 20) + 1;
        const products = Array.from({length: Math.min(usage, 5)}, (_, j) => `Product-${i}-${j}`);
        concurrentMap.set(`Color-${i}`, { count: usage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(concurrentMap);
      
      const startTime = performance.now();
      
      // Simulate concurrent operations
      const operations = [
        () => analyticsService.getUsageStatistics('concurrent-org'),
        () => analyticsService.getPopularityRankings('concurrent-org', { limit: 100 }),
        () => analyticsService.getUsageTrends('concurrent-org', { timePeriodDays: 30 }),
        () => analyticsService.getPerformanceMetrics('concurrent-org'),
        () => analyticsService.getUsageStatisticsPaginated('concurrent-org', { limit: 500, offset: 0 })
      ];
      
      // Run all operations simultaneously
      const results = await Promise.all(operations.map(op => 
        new Promise(resolve => {
          const opStartTime = performance.now();
          const result = op();
          const opEndTime = performance.now();
          resolve({ result, time: opEndTime - opStartTime });
        })
      ));
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      expect(totalTime).toBeLessThan(1500); // All concurrent operations under 1.5 seconds
      
      results.forEach((opResult: any, index) => {
        expect(opResult.time).toBeLessThan(800); // Each operation under 800ms
        expect(opResult.result).toBeDefined();
      });
      
      console.log(`Concurrent operations (5 operations on 8K colors): ${totalTime.toFixed(2)}ms`);
    });

    test('should maintain performance under sustained load', () => {
      const sustainedMap = new Map();
      for (let i = 0; i < 12000; i++) {
        const usage = Math.floor(Math.random() * 30) + 1;
        const products = Array.from({length: Math.min(usage, 8)}, (_, j) => `Product-${i}-${j}`);
        sustainedMap.set(`Color-${i}`, { count: usage, products });
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(sustainedMap);
      
      const iterations = 50;
      const executionTimes: number[] = [];
      
      // Run sustained operations and measure performance degradation
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        analyticsService.getUsageStatistics('sustained-org');
        analyticsService.getPopularityRankings('sustained-org', { limit: 50 });
        
        const endTime = performance.now();
        executionTimes.push(endTime - startTime);
      }
      
      // Check for performance consistency
      const avgTime = executionTimes.reduce((sum, time) => sum + time, 0) / iterations;
      const maxTime = Math.max(...executionTimes);
      const minTime = Math.min(...executionTimes);
      const variance = maxTime - minTime;
      
      expect(avgTime).toBeLessThan(200); // Average operation time
      expect(variance).toBeLessThan(100); // Performance should be consistent
      expect(maxTime).toBeLessThan(300); // No operation should be extremely slow
      
      console.log(`Sustained load test - Avg: ${avgTime.toFixed(2)}ms, Min: ${minTime.toFixed(2)}ms, Max: ${maxTime.toFixed(2)}ms, Variance: ${variance.toFixed(2)}ms`);
    });
  });

  describe('Edge Case Performance', () => {
    test('should handle datasets with extreme usage distributions efficiently', () => {
      const extremeMap = new Map();
      const extremeProductMap = new Map();
      
      // Create extreme distribution: 1 color with massive usage, rest with minimal
      const ultraPopularProducts = Array.from({length: 1000}, (_, i) => `Product-${i}`);
      extremeMap.set('Ultra Popular Color', { 
        count: 10000, 
        products: ultraPopularProducts
      });
      extremeProductMap.set('Ultra Popular Color', ultraPopularProducts);
      
      for (let i = 1; i < 5000; i++) {
        const colorName = `Normal Color ${i}`;
        const products = [`Product-${i}`];
        extremeMap.set(colorName, { count: 1, products });
        extremeProductMap.set(colorName, products);
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(extremeMap);
      mockColorRepository.getColorNameProductMap.mockReturnValue(extremeProductMap);
      
      const startTime = performance.now();
      const stats = analyticsService.getUsageStatistics('extreme-org');
      const rankings = analyticsService.getPopularityRankings('extreme-org', { limit: 100 });
      const endTime = performance.now();
      
      expect(stats.data.maxUsage).toBe(10000);
      expect(stats.data.minUsage).toBe(1);
      expect(rankings.data[0].colorName).toBe('Ultra Popular Color');
      expect(rankings.data[0].usageCount).toBe(10000);
      
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(300); // Should handle extreme distributions efficiently
      console.log(`Extreme distribution test: ${executionTime.toFixed(2)}ms`);
    });

    test('should handle datasets with many zero-usage colors efficiently', () => {
      const zeroUsageMap = new Map();
      const zeroProductMap = new Map();
      
      // Most colors have zero usage (realistic for new organizations)
      for (let i = 0; i < 8000; i++) {
        const colorName = `Unused Color ${i}`;
        zeroUsageMap.set(colorName, { count: 0, products: [] });
        zeroProductMap.set(colorName, []);
      }
      
      // Only a few colors have usage
      for (let i = 0; i < 200; i++) {
        const usage = Math.floor(Math.random() * 5) + 1;
        const products = Array.from({length: usage}, (_, j) => `Product-${i}-${j}`);
        const colorName = `Used Color ${i}`;
        zeroUsageMap.set(colorName, { count: usage, products });
        zeroProductMap.set(colorName, products);
      }
      
      mockColorRepository.getUsageCounts.mockReturnValue(zeroUsageMap);
      mockColorRepository.getColorNameProductMap.mockReturnValue(zeroProductMap);
      
      const startTime = performance.now();
      const stats = analyticsService.getUsageStatistics('zero-usage-org');
      const rankings = analyticsService.getPopularityRankings('zero-usage-org', { limit: 50 });
      const endTime = performance.now();
      
      expect(stats.data.totalColors).toBe(8200);
      expect(stats.data.colorsWithUsage).toBe(200);
      expect(stats.data.usageDistribution.noUsage).toBe(8000);
      expect(rankings.data).toHaveLength(50); // Should only return colors with usage
      
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(250);
      console.log(`Zero usage distribution test: ${executionTime.toFixed(2)}ms`);
    });
  });
});