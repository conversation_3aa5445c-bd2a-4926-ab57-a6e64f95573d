/**
 * @file CMYKDisplay.tsx
 * @description Component for displaying CMYK color values with visualization
 */

import { useTokens } from '../hooks/useTokens';

interface CMYKDisplayProps {
  cmykString: string;
  cmyk: { c: number; m: number; y: number; k: number };
  isCopied: boolean;
  onCopy: () => void;
}

export default function CMYKDisplay({
  cmykString,
  cmyk,
  isCopied,
  onCopy,
}: CMYKDisplayProps) {
  const tokens = useTokens();

  // Container classes
  const containerClasses = 'flex flex-col';

  // Header classes using design tokens
  const headerClasses =
    'flex items-center justify-between mb-[var(--spacing-2)] bg-ui-background-primary rounded-[var(--radius-md)] p-[var(--spacing-2)] shadow-sm border border-ui-border-light';

  // Label classes using design tokens
  const labelClasses = 'text-sm font-medium text-ui-foreground-primary';

  // Code classes using design tokens
  const codeClasses =
    'text-sm bg-ui-background-secondary px-[var(--spacing-2)] py-[var(--spacing-1)] rounded-[var(--radius-sm)] mr-[var(--spacing-1)]';

  // Copy button classes using design tokens
  const getCopyButtonClasses = () => {
    return isCopied
      ? 'text-feedback-success hover:text-feedback-success hover:bg-feedback-success/10 p-[var(--spacing-1)] rounded-full transition-colors'
      : 'text-ui-foreground-secondary hover:text-brand-primary transition-colors p-[var(--spacing-1)] rounded-full hover:bg-brand-primary/10';
  };

  // CMYK visualization container using design tokens
  const visualizationContainerClasses =
    'relative mt-[var(--spacing-1)] bg-ui-background-primary rounded-[var(--radius-md)] p-[var(--spacing-3)] shadow-sm border border-ui-border-light';

  // Grid for CMYK bars using design tokens
  const gridClasses =
    'grid grid-cols-4 gap-[var(--spacing-1)] h-8 mb-[var(--spacing-1)]';

  // Bar item classes
  const barItemClasses =
    'relative h-full flex items-center justify-center rounded overflow-hidden';

  // Text value classes using design tokens
  const valueTextClasses =
    'flex text-xs text-ui-foreground-tertiary justify-between';

  // Value number classes
  const valueNumberClasses = 'font-medium';

  return (
    <div className={containerClasses}>
      <div
        className={headerClasses}
        style={{
          transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
        }}
      >
        <span className={labelClasses}>CMYK:</span>
        <div className='flex items-center'>
          <code className={codeClasses}>{cmykString}</code>
          <button
            className={getCopyButtonClasses()}
            onClick={onCopy}
            title='Copy CMYK'
            aria-label='Copy CMYK values'
          >
            {isCopied ? (
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-5 w-5'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M5 13l4 4L19 7'
                />
              </svg>
            ) : (
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-5 w-5'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <rect x='9' y='9' width='13' height='13' rx='2' ry='2' />
                <path d='M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1' />
              </svg>
            )}
          </button>
        </div>
      </div>

      <div
        className={visualizationContainerClasses}
        style={{
          transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
        }}
      >
        <div className={gridClasses}>
          <div className={barItemClasses}>
            <div
              className='absolute inset-0 bg-cyan-600'
              style={{ right: `${100 - cmyk.c}%` }}
             />
            <span className='relative text-xs font-bold text-white drop-shadow-md'>
              C
            </span>
          </div>
          <div className={barItemClasses}>
            <div
              className='absolute inset-0'
              style={{ right: `${100 - cmyk.m}%`, backgroundColor: '#E0158D' }}
             />
            <span className='relative text-xs font-bold text-white drop-shadow-md'>
              M
            </span>
          </div>
          <div className={barItemClasses}>
            <div
              className='absolute inset-0 bg-yellow-500'
              style={{ right: `${100 - cmyk.y}%` }}
             />
            <span className='relative text-xs font-bold text-white drop-shadow-md'>
              Y
            </span>
          </div>
          <div className={barItemClasses}>
            <div
              className='absolute inset-0 bg-black'
              style={{ right: `${100 - cmyk.k}%` }}
             />
            <span className='relative text-xs font-bold text-white drop-shadow-md'>
              K
            </span>
          </div>
        </div>

        <div className={valueTextClasses}>
          <span className={valueNumberClasses}>{cmyk.c}%</span>
          <span className={valueNumberClasses}>{cmyk.m}%</span>
          <span className={valueNumberClasses}>{cmyk.y}%</span>
          <span className={valueNumberClasses}>{cmyk.k}%</span>
        </div>
      </div>
    </div>
  );
}
