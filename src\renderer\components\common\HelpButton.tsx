import React, { useState } from 'react';
import ShortcutHelp from './ShortcutHelp';

const HelpButton: React.FC = () => {
  const [showShortcuts, setShowShortcuts] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowShortcuts(true)}
        className='flex items-center justify-center w-8 h-8 rounded-full bg-brand-primary/10 text-brand-primary hover:bg-brand-primary/20 transition-colors'
        title='Keyboard Shortcuts'
      >
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='16'
          height='16'
          viewBox='0 0 24 24'
          fill='none'
          stroke='currentColor'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        >
          <path d='M3 3h18v5H3z' />
          <path d='M3 8h18l-9 9-9-9z' />
        </svg>
      </button>

      <ShortcutHelp
        isOpen={showShortcuts}
        onClose={() => setShowShortcuts(false)}
      />
    </>
  );
};

export default HelpButton;
