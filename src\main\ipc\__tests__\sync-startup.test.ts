/**
 * @file sync-startup.test.ts
 * @description Test sync initialization on app startup with authentication
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { getSyncSystem, initializeSyncOnStartup } from '../sync-startup';

// Mock dependencies
vi.mock('../../services/service-locator', () => ({
  ServiceLocator: {
    getCircuitBreakerAuthManager: vi.fn(),
  },
}));

vi.mock('../../services/sync/unified-sync-manager', () => ({
  unifiedSyncManager: {
    initialize: vi.fn(),
  },
}));

vi.mock('../../utils/organization-context', () => ({
  getCurrentOrganization: vi.fn(),
}));

vi.mock('../sync-handlers', () => ({
  initializeSyncEventForwarding: vi.fn(),
}));

describe('Sync Startup', () => {
  let mockCircuitBreakerAuthManager: any;
  let mockUnifiedSyncManager: any;
  let mockGetCurrentOrganization: any;
  let mockInitializeSyncEventForwarding: any;

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks();

    // Setup default mocks
    mockCircuitBreakerAuthManager = {
      getHealthStatus: vi.fn(() => ({
        isHealthy: true,
        circuitBreakerOpen: false,
        networkConnected: true,
        sessionValid: true,
        issues: [],
      })),
      getCurrentUser: vi.fn(() => Promise.resolve({
        id: 'test-user-id',
        email: '<EMAIL>',
      })),
      getCurrentSession: vi.fn(() => Promise.resolve({
        access_token: 'test-token',
      })),
    };

    const { ServiceLocator } = await import('../../services/service-locator');
    vi.mocked(ServiceLocator.getCircuitBreakerAuthManager).mockReturnValue(mockCircuitBreakerAuthManager);

    const { unifiedSyncManager } = await import('../../services/sync/unified-sync-manager');
    mockUnifiedSyncManager = unifiedSyncManager;
    vi.mocked(mockUnifiedSyncManager.initialize).mockResolvedValue(undefined);

    const { getCurrentOrganization } = await import('../../utils/organization-context');
    mockGetCurrentOrganization = getCurrentOrganization;
    vi.mocked(mockGetCurrentOrganization).mockReturnValue('test-org-id');

    const { initializeSyncEventForwarding } = await import('../sync-handlers');
    mockInitializeSyncEventForwarding = initializeSyncEventForwarding;
    vi.mocked(mockInitializeSyncEventForwarding).mockImplementation(() => {});

    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('initializeSyncOnStartup', () => {
    it('should successfully initialize sync for authenticated user with healthy auth system', async () => {
      await initializeSyncOnStartup();

      expect(mockCircuitBreakerAuthManager.getHealthStatus).toHaveBeenCalled();
      expect(mockCircuitBreakerAuthManager.getCurrentUser).toHaveBeenCalled();
      expect(mockCircuitBreakerAuthManager.getCurrentSession).toHaveBeenCalled();
      expect(mockGetCurrentOrganization).toHaveBeenCalled();
      expect(mockUnifiedSyncManager.initialize).toHaveBeenCalledWith('test-user-id', 'test-org-id');
      expect(mockInitializeSyncEventForwarding).toHaveBeenCalled();
    });

    it('should skip initialization when authentication system is not healthy', async () => {
      mockCircuitBreakerAuthManager.getHealthStatus.mockReturnValue({
        isHealthy: false,
        issues: ['Network error'],
      });

      await initializeSyncOnStartup();

      expect(mockCircuitBreakerAuthManager.getCurrentUser).not.toHaveBeenCalled();
      expect(mockUnifiedSyncManager.initialize).not.toHaveBeenCalled();
    });

    it('should skip initialization when circuit breaker is open', async () => {
      mockCircuitBreakerAuthManager.getHealthStatus.mockReturnValue({
        isHealthy: true,
        circuitBreakerOpen: true,
        networkConnected: true,
        sessionValid: true,
        issues: [],
      });

      await initializeSyncOnStartup();

      expect(mockCircuitBreakerAuthManager.getCurrentUser).not.toHaveBeenCalled();
      expect(mockUnifiedSyncManager.initialize).not.toHaveBeenCalled();
    });

    it('should skip initialization when network is not connected', async () => {
      mockCircuitBreakerAuthManager.getHealthStatus.mockReturnValue({
        isHealthy: true,
        circuitBreakerOpen: false,
        networkConnected: false,
        sessionValid: true,
        issues: [],
      });

      await initializeSyncOnStartup();

      expect(mockCircuitBreakerAuthManager.getCurrentUser).not.toHaveBeenCalled();
      expect(mockUnifiedSyncManager.initialize).not.toHaveBeenCalled();
    });

    it('should skip initialization when no authenticated user', async () => {
      mockCircuitBreakerAuthManager.getCurrentUser.mockResolvedValue(null);

      await initializeSyncOnStartup();

      expect(mockCircuitBreakerAuthManager.getCurrentSession).not.toHaveBeenCalled();
      expect(mockUnifiedSyncManager.initialize).not.toHaveBeenCalled();
    });

    it('should skip initialization when no valid session', async () => {
      mockCircuitBreakerAuthManager.getCurrentSession.mockResolvedValue(null);

      await initializeSyncOnStartup();

      expect(mockGetCurrentOrganization).not.toHaveBeenCalled();
      expect(mockUnifiedSyncManager.initialize).not.toHaveBeenCalled();
    });

    it('should skip initialization when no organization context', async () => {
      mockGetCurrentOrganization.mockReturnValue(null);

      await initializeSyncOnStartup();

      expect(mockUnifiedSyncManager.initialize).not.toHaveBeenCalled();
    });

    it('should handle initialization errors gracefully', async () => {
      mockUnifiedSyncManager.initialize.mockRejectedValue(new Error('Initialization failed'));

      await expect(initializeSyncOnStartup()).resolves.not.toThrow();
      
      expect(console.error).toHaveBeenCalledWith(
        '[SyncStartup] ❌ Failed to initialize sync on startup:',
        expect.any(Error)
      );
    });
  });

  describe('getSyncSystem', () => {
    it('should return the unified sync manager', () => {
      const syncSystem = getSyncSystem();
      expect(syncSystem).toBe(mockUnifiedSyncManager);
    });
  });
});