/**
 * @file service-initializer.test.ts
 * @description Unit tests for service initialization system with focused mocking
 */

import Database from 'better-sqlite3';
import {
    afterEach,
    beforeEach,
    describe,
    expect,
    it,
    MockedFunction,
    vi,
} from 'vitest';
import {
    createServiceConfig,
    initializeAllServices,
    initializeAuxiliaryServices,
    initializeDatabaseDependentServices,
    initializeDatabaseService,
    initializeEarlyServices,
    initializeEmailService,
    initializeSentry,
    initializeServiceLocator,
    initializeSharedFolderService,
    ServiceConfig,
} from '../service-initializer';

// Mock external dependencies
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/test/userData'),
  },
}));

vi.mock('../../db/database', () => ({
  initDatabase: vi.fn(),
  getDatabase: vi.fn(),
}));

vi.mock('../../shared-folder', () => ({
  SharedFolderManager: vi.fn().mockImplementation(() => ({
    initialize: vi.fn(),
  })),
}));

vi.mock('../../ipc/shared-folder-ipc', () => ({
  setupSharedFolderIPC: vi.fn(),
}));

vi.mock('../sentry.service', () => ({
  sentryService: {
    initialize: vi.fn(),
  },
  SentryService: {
    isGloballyInitialized: vi.fn(() => false),
    getInitializationAttempts: vi.fn(() => 0),
  },
}));

vi.mock('../service-locator', () => ({
  ServiceLocator: {
    initialize: vi.fn(),
  },
  getZohoEmailService: vi.fn(() => ({
    initialize: vi.fn(),
  })),
}));

vi.mock('../../utils/logger', () => ({
  serviceLogger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('Service Initializer', () => {
  let mockDb: Database.Database;
  let mockInitDatabase: MockedFunction<any>;
  let mockGetDatabase: MockedFunction<any>;
  let mockSentryService: any;
  let mockSentryServiceClass: any;
  let mockServiceLocator: any;
  let mockZohoEmailService: any;

  beforeEach(async () => {
    // Get mocked functions
    const { initDatabase, getDatabase } = await import('../../db/database');
    const { sentryService, SentryService } = await import('../sentry.service');
    const { ServiceLocator, getZohoEmailService } = await import('../service-locator');
    
    mockInitDatabase = initDatabase as MockedFunction<any>;
    mockGetDatabase = getDatabase as MockedFunction<any>;
    mockSentryService = sentryService;
    mockSentryServiceClass = SentryService;
    mockServiceLocator = ServiceLocator;
    mockZohoEmailService = (getZohoEmailService as MockedFunction<any>)();

    // Create mock database
    mockDb = {
      prepare: vi.fn(() => ({
        get: vi.fn(() => ({ test: 1 })),
      })),
    } as any;

    // Setup database mocks
    mockInitDatabase.mockResolvedValue(mockDb);
    mockGetDatabase.mockReturnValue(mockDb);

    // Reset environment variables
    process.env.ZOHO_CLIENT_ID = 'test-client-id';
    process.env.ZOHO_CLIENT_SECRET = 'test-client-secret';
    process.env.SUPABASE_URL = 'https://test.supabase.co';
    process.env.SUPABASE_ANON_KEY = 'test-anon-key';

    // Reset all mocks
    vi.clearAllMocks();
    
    // Reset mock implementations
    mockSentryService.initialize.mockResolvedValue(undefined);
    mockSentryServiceClass.isGloballyInitialized.mockReturnValue(false);
    mockServiceLocator.initialize.mockResolvedValue(undefined);
    mockZohoEmailService.initialize.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initializeSentry', () => {
    it('should initialize Sentry successfully', async () => {
      await initializeSentry();

      expect(mockSentryServiceClass.isGloballyInitialized).toHaveBeenCalled();
      expect(mockSentryService.initialize).toHaveBeenCalled();
    });

    it('should skip initialization if Sentry is already initialized', async () => {
      (mockSentryServiceClass.isGloballyInitialized as MockedFunction<any>).mockReturnValue(true);

      await initializeSentry();

      expect(mockSentryServiceClass.isGloballyInitialized).toHaveBeenCalled();
      expect(mockSentryService.initialize).not.toHaveBeenCalled();
    });

    it('should handle Sentry initialization errors gracefully', async () => {
      (mockSentryService.initialize as MockedFunction<any>).mockRejectedValue(
        new Error('Sentry init failed')
      );

      // Should not throw
      await expect(initializeSentry()).resolves.toBeUndefined();
    });
  });

  describe('initializeDatabaseService', () => {
    it('should initialize database successfully', async () => {
      const result = await initializeDatabaseService();

      expect(mockInitDatabase).toHaveBeenCalled();
      expect(mockGetDatabase).toHaveBeenCalled();
      expect(result).toBe(mockDb);
    });

    it('should throw error if database initialization returns null', async () => {
      mockInitDatabase.mockResolvedValue(null);

      await expect(initializeDatabaseService()).rejects.toThrow(
        'Database initialization failed - initDatabase() returned null'
      );
    });

    it('should throw error if getDatabase returns null after initialization', async () => {
      mockGetDatabase.mockReturnValue(null);

      await expect(initializeDatabaseService()).rejects.toThrow(
        'Database is still null after initialization - getDatabase() returned null'
      );
    });

    it('should verify database connection with test query', async () => {
      const mockPrepare = vi.fn(() => ({
        get: vi.fn(() => ({ test: 1 })),
      }));
      mockDb.prepare = mockPrepare;

      await initializeDatabaseService();

      expect(mockPrepare).toHaveBeenCalledWith('SELECT 1 as test');
    });

    it('should throw error if database connection test fails', async () => {
      const mockPrepare = vi.fn(() => ({
        get: vi.fn(() => null),
      }));
      mockDb.prepare = mockPrepare;

      await expect(initializeDatabaseService()).rejects.toThrow(
        'Database connection test failed'
      );
    });

    it('should handle database connection test exceptions', async () => {
      const mockPrepare = vi.fn(() => ({
        get: vi.fn(() => {
          throw new Error('Query failed');
        }),
      }));
      mockDb.prepare = mockPrepare;

      await expect(initializeDatabaseService()).rejects.toThrow(
        'Database connection test failed: Query failed'
      );
    });
  });

  describe('createServiceConfig', () => {
    it('should create service configuration from environment variables', () => {
      const config = createServiceConfig();

      expect(config).toEqual({
        zoho: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          refreshToken: undefined,
          accountId: undefined,
          region: 'US',
          supportAlias: '<EMAIL>',
        },
        supabase: {
          url: 'https://test.supabase.co',
          anonKey: 'test-anon-key',
        },
      });
    });

    it('should use default values for missing environment variables', () => {
      delete process.env.ZOHO_REGION;
      delete process.env.ZOHO_SUPPORT_ALIAS;

      const config = createServiceConfig();

      expect(config.zoho.region).toBe('US');
      expect(config.zoho.supportAlias).toBe('<EMAIL>');
    });
  });

  describe('initializeServiceLocator', () => {
    it('should initialize ServiceLocator successfully', async () => {
      const config: ServiceConfig = createServiceConfig();

      await initializeServiceLocator(mockDb, config);

      expect(mockServiceLocator.initialize).toHaveBeenCalledWith({
        database: mockDb,
        config,
      });
    });

    it('should throw error if ServiceLocator initialization fails', async () => {
      (mockServiceLocator.initialize as MockedFunction<any>).mockRejectedValue(
        new Error('ServiceLocator init failed')
      );

      const config: ServiceConfig = createServiceConfig();

      await expect(initializeServiceLocator(mockDb, config)).rejects.toThrow(
        'ServiceLocator init failed'
      );
    });
  });

  describe('initializeSharedFolderService', () => {
    it('should initialize shared folder service successfully', async () => {
      const result = initializeSharedFolderService();

      expect(result).toBeDefined();
    });
  });

  describe('initializeEmailService', () => {
    it('should initialize email service successfully', async () => {
      await initializeEmailService();

      expect(mockZohoEmailService.initialize).toHaveBeenCalled();
    });

    it('should handle email service initialization errors gracefully', async () => {
      (mockZohoEmailService.initialize as MockedFunction<any>).mockRejectedValue(
        new Error('Email init failed')
      );

      // Should not throw, but log error and continue
      await expect(initializeEmailService()).resolves.toBeUndefined();
    });
  });

  describe('initializeAllServices', () => {
    it('should initialize all services successfully', async () => {
      const result = await initializeAllServices();

      expect(result.success).toBe(true);
      expect(result.database).toBe(mockDb);
      expect(result.initTime).toBeGreaterThan(0);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should handle service initialization errors', async () => {
      mockInitDatabase.mockRejectedValue(new Error('Database init failed'));

      const result = await initializeAllServices();

      expect(result.success).toBe(false);
      expect(result.database).toBeNull();
      expect(result.errors).toContain('Database init failed');
      expect(result.initTime).toBeGreaterThan(0);
    });

    it('should measure initialization time', async () => {
      const startTime = Date.now();
      const result = await initializeAllServices();
      const endTime = Date.now();

      expect(result.initTime).toBeGreaterThanOrEqual(0);
      expect(result.initTime).toBeLessThanOrEqual(endTime - startTime + 10); // Allow small margin
    });
  });

  describe('initializeEarlyServices', () => {
    it('should initialize early services successfully', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await initializeEarlyServices();

      expect(consoleSpy).toHaveBeenCalledWith('[Services] 🚀 Initializing early services...');
      expect(consoleSpy).toHaveBeenCalledWith('[Services] ✅ Early services initialized successfully');

      consoleSpy.mockRestore();
    });

    it('should handle early service initialization errors', async () => {
      (mockSentryService.initialize as MockedFunction<any>).mockRejectedValue(
        new Error('Sentry init failed')
      );

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Should not throw
      await expect(initializeEarlyServices()).resolves.toBeUndefined();

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '[Services] ❌ Error initializing early services:',
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe('initializeDatabaseDependentServices', () => {
    it('should initialize database-dependent services successfully', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await initializeDatabaseDependentServices(mockDb);

      expect(consoleSpy).toHaveBeenCalledWith('[Services] 🚀 Initializing database-dependent services...');
      expect(consoleSpy).toHaveBeenCalledWith('[Services] ✅ Database-dependent services initialized successfully');

      consoleSpy.mockRestore();
    });

    it('should throw error if database-dependent service initialization fails', async () => {
      (mockServiceLocator.initialize as MockedFunction<any>).mockRejectedValue(
        new Error('ServiceLocator init failed')
      );

      await expect(initializeDatabaseDependentServices(mockDb)).rejects.toThrow(
        'ServiceLocator init failed'
      );
    });
  });

  describe('initializeAuxiliaryServices', () => {
    it('should initialize auxiliary services successfully', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await initializeAuxiliaryServices();

      expect(consoleSpy).toHaveBeenCalledWith('[Services] 🚀 Initializing auxiliary services...');
      expect(consoleSpy).toHaveBeenCalledWith('[Services] ✅ Auxiliary services initialized successfully');

      consoleSpy.mockRestore();
    });

    it('should handle auxiliary service initialization errors gracefully', async () => {
      (mockZohoEmailService.initialize as MockedFunction<any>).mockRejectedValue(
        new Error('Email init failed')
      );

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Should not throw, but log error and continue
      await expect(initializeAuxiliaryServices()).resolves.toBeUndefined();

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '[Services] ⚠️ Some auxiliary services failed to initialize:',
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe('dependency injection', () => {
    it('should pass correct dependencies to ServiceLocator', async () => {
      const config = createServiceConfig();

      await initializeServiceLocator(mockDb, config);

      expect(mockServiceLocator.initialize).toHaveBeenCalledWith({
        database: mockDb,
        config: expect.objectContaining({
          zoho: expect.objectContaining({
            clientId: 'test-client-id',
            clientSecret: 'test-client-secret',
          }),
          supabase: expect.objectContaining({
            url: 'https://test.supabase.co',
            anonKey: 'test-anon-key',
          }),
        }),
      });
    });

    it('should verify Sentry initialization works with singleton pattern', async () => {
      // First call should initialize
      await initializeSentry();
      expect(mockSentryServiceClass.isGloballyInitialized).toHaveBeenCalled();

      // Second call should skip initialization
      (mockSentryServiceClass.isGloballyInitialized as MockedFunction<any>).mockReturnValue(true);
      await initializeSentry();

      expect(mockSentryServiceClass.getInitializationAttempts).toHaveBeenCalled();
    });
  });

  describe('error handling and resilience', () => {
    it('should handle partial service initialization failures', async () => {
      // Make email service fail but others succeed
      (mockZohoEmailService.initialize as MockedFunction<any>).mockRejectedValue(
        new Error('Email service unavailable')
      );

      const result = await initializeAllServices();

      // Should still succeed overall since email is not critical
      expect(result.success).toBe(true);
      expect(result.database).toBe(mockDb);
    });

    it('should fail gracefully when critical services fail', async () => {
      // Make database initialization fail
      mockInitDatabase.mockRejectedValue(new Error('Critical database failure'));

      const result = await initializeAllServices();

      expect(result.success).toBe(false);
      expect(result.errors).toContain('Critical database failure');
      expect(result.database).toBeNull();
    });
  });
});