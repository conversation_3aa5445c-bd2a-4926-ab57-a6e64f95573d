/**
 * @file startup-cache.service.ts
 * @description Caching service for startup state to skip redundant checks and operations
 */

interface StartupState {
  lastSuccessfulAuth: number;
  lastHealthCheck: number;
  lastApiAvailabilityCheck: number;
  authToken?: string;
  apiEndpoints?: Record<string, boolean>;
  lastStartupTime: number;
  version: string;
}

interface StartupCacheConfig {
  authCacheTTL: number; // Auth state cache TTL
  healthCheckTTL: number; // Health check cache TTL
  apiAvailabilityTTL: number; // API availability cache TTL
}

class StartupCacheService {
  private static readonly CACHE_KEY = 'chromasync:startup-cache';

  private static readonly DEFAULT_CONFIG: StartupCacheConfig = {
    authCacheTTL: 10 * 60 * 1000, // 10 minutes
    healthCheckTTL: 5 * 60 * 1000, // 5 minutes
    apiAvailabilityTTL: 15 * 60 * 1000, // 15 minutes
  };

  /**
   * Get current app version from package.json or environment
   */
  private static getAppVersion(): string {
    // In development, use timestamp to invalidate cache on each restart
    if (process.env.NODE_ENV === 'development') {
      return `dev-${Date.now()}`;
    }

    // In production, use actual version
    return process.env.REACT_APP_VERSION || '1.0.0';
  }

  /**
   * Get cached startup state
   */
  static getCachedState(): StartupState | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (!cached) {
        return null;
      }

      const state: StartupState = JSON.parse(cached);

      // Check if version changed (invalidates all cache)
      const currentVersion = this.getAppVersion();
      if (state.version !== currentVersion) {
        console.log('[StartupCache] Version changed, invalidating cache');
        this.invalidateCache();
        return null;
      }

      return state;
    } catch (error) {
      console.error('[StartupCache] Error reading cache:', error);
      this.invalidateCache();
      return null;
    }
  }

  /**
   * Update cached startup state
   */
  static updateState(updates: Partial<Omit<StartupState, 'version'>>): void {
    try {
      const current = this.getCachedState() || {
        lastSuccessfulAuth: 0,
        lastHealthCheck: 0,
        lastApiAvailabilityCheck: 0,
        lastStartupTime: 0,
        version: this.getAppVersion(),
      };

      const updated: StartupState = {
        ...current,
        ...updates,
        version: this.getAppVersion(),
      };

      localStorage.setItem(this.CACHE_KEY, JSON.stringify(updated));
      console.log('[StartupCache] ✅ State updated:', Object.keys(updates));
    } catch (error) {
      console.error('[StartupCache] Error updating state:', error);
    }
  }

  /**
   * Check if auth state is cached and valid
   */
  static isAuthCacheValid(): boolean {
    const state = this.getCachedState();
    if (!state || !state.lastSuccessfulAuth) {
      return false;
    }

    const age = Date.now() - state.lastSuccessfulAuth;
    return age < this.DEFAULT_CONFIG.authCacheTTL;
  }

  /**
   * Check if health check is cached and valid
   */
  static isHealthCheckCacheValid(): boolean {
    const state = this.getCachedState();
    if (!state || !state.lastHealthCheck) {
      return false;
    }

    const age = Date.now() - state.lastHealthCheck;
    return age < this.DEFAULT_CONFIG.healthCheckTTL;
  }

  /**
   * Check if API availability is cached and valid
   */
  static isApiAvailabilityCacheValid(): boolean {
    const state = this.getCachedState();
    if (!state || !state.lastApiAvailabilityCheck) {
      return false;
    }

    const age = Date.now() - state.lastApiAvailabilityCheck;
    return age < this.DEFAULT_CONFIG.apiAvailabilityTTL;
  }

  /**
   * Get cached API endpoints status
   */
  static getCachedApiEndpoints(): Record<string, boolean> | null {
    if (!this.isApiAvailabilityCacheValid()) {
      return null;
    }

    const state = this.getCachedState();
    return state?.apiEndpoints || null;
  }

  /**
   * Record successful auth
   */
  static recordSuccessfulAuth(token?: string): void {
    this.updateState({
      lastSuccessfulAuth: Date.now(),
      authToken: token,
    });
  }

  /**
   * Record successful health check
   */
  static recordSuccessfulHealthCheck(): void {
    this.updateState({
      lastHealthCheck: Date.now(),
    });
  }

  /**
   * Record API availability check
   */
  static recordApiAvailabilityCheck(endpoints: Record<string, boolean>): void {
    this.updateState({
      lastApiAvailabilityCheck: Date.now(),
      apiEndpoints: endpoints,
    });
  }

  /**
   * Record startup completion
   */
  static recordStartupCompletion(startupTime: number): void {
    this.updateState({
      lastStartupTime: startupTime,
    });
  }

  /**
   * Invalidate all cached startup state
   */
  static invalidateCache(): void {
    localStorage.removeItem(this.CACHE_KEY);
    console.log('[StartupCache] 🗑️ Cache invalidated');
  }

  /**
   * Invalidate specific cache components
   */
  static invalidateAuth(): void {
    this.updateState({
      lastSuccessfulAuth: 0,
      authToken: undefined,
    });
  }

  static invalidateHealthCheck(): void {
    this.updateState({
      lastHealthCheck: 0,
    });
  }

  static invalidateApiAvailability(): void {
    this.updateState({
      lastApiAvailabilityCheck: 0,
      apiEndpoints: undefined,
    });
  }

  /**
   * Get cache statistics for debugging
   */
  static getCacheStats(): {
    authAge: number | null;
    healthCheckAge: number | null;
    apiAvailabilityAge: number | null;
    authValid: boolean;
    healthCheckValid: boolean;
    apiAvailabilityValid: boolean;
  } {
    const state = this.getCachedState();

    if (!state) {
      return {
        authAge: null,
        healthCheckAge: null,
        apiAvailabilityAge: null,
        authValid: false,
        healthCheckValid: false,
        apiAvailabilityValid: false,
      };
    }

    const now = Date.now();

    return {
      authAge: state.lastSuccessfulAuth ? now - state.lastSuccessfulAuth : null,
      healthCheckAge: state.lastHealthCheck
        ? now - state.lastHealthCheck
        : null,
      apiAvailabilityAge: state.lastApiAvailabilityCheck
        ? now - state.lastApiAvailabilityCheck
        : null,
      authValid: this.isAuthCacheValid(),
      healthCheckValid: this.isHealthCheckCacheValid(),
      apiAvailabilityValid: this.isApiAvailabilityCacheValid(),
    };
  }
}

export default StartupCacheService;
