/**
 * @file ProductsPanel.tsx
 * @description Component for managing and displaying products with their colors
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useProductStore, ProductWithColors } from '../../store/product.store';
import { useColorStore } from '../../store/color.store';
import { useOrganizationStore } from '../../store/organization.store';
import ProductView from './ProductView';
import { Plus, Folder, Trash2, RefreshCw, AlertCircle } from 'lucide-react';
import { useTokens } from '../../hooks/useTokens';
import ProductCreateModal from './ProductCreateModal';
import Modal from '../ui/Modal';
import { ProductCardSkeleton } from '../ui/Skeleton';

const ProductsPanel: React.FC = () => {
  const tokens = useTokens();
  const {
    products,
    activeProductId,
    setActiveProductId,
    deleteProduct,
    fetchProductsWithColors,
    isLoading,
    error
  } = useProductStore();
  
  // Ensure products is always an array
  const safeProducts = Array.isArray(products) ? products : [];

  const { searchQuery } = useColorStore();
  const { currentOrganization, loadCurrentOrganization } = useOrganizationStore();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncMessage, setSyncMessage] = useState<string | null>(null);

  // Load organization and products declaratively
  useEffect(() => {
    if (!currentOrganization) {
      loadCurrentOrganization();
      return;
    }
    
    // Fetch products when organization is available
    fetchProductsWithColors();
  }, [currentOrganization, loadCurrentOrganization, fetchProductsWithColors]);

  const uniqueProducts = useMemo(() => {
    const productsByName = new Map();
    
    // Group products by name
    safeProducts.forEach(product => {
      const name = product.name;
      if (!productsByName.has(name)) {
        productsByName.set(name, []);
      }
      productsByName.get(name).push(product);
    });
    
    // For each name, pick the product with colors, or the oldest one if none have colors
    return Array.from(productsByName.values()).map(products => {
      // First, try to find a product with colors
      const productsWithColors = products.filter((p: ProductWithColors) => p.colors && p.colors.length > 0);
      if (productsWithColors.length > 0) {
        // If multiple products have colors, pick the one with the most colors
        return productsWithColors.reduce((best: ProductWithColors, current: ProductWithColors) => {
          const bestColors = best.colors?.length || 0;
          const currentColors = current.colors?.length || 0;
          return currentColors > bestColors ? current : best;
        });
      }
      
      // If no product has colors, pick the oldest one (first created)
      return products.reduce((oldest: ProductWithColors, current: ProductWithColors) => {
        return new Date(current.createdAt) < new Date(oldest.createdAt) ? current : oldest;
      });
    });
  }, [safeProducts]);

  // Auto-select first product if none is selected and products are available
  useEffect(() => {
    if (uniqueProducts.length > 0 && !activeProductId) {
      setActiveProductId(uniqueProducts[0].id);
    }
  }, [uniqueProducts, activeProductId, setActiveProductId]);

  // Function to refresh products
  const handleSyncProducts = async () => {
    try {
      setIsSyncing(true);
      setSyncMessage(null);

      // Refresh products
      if (process.env.NODE_ENV === 'development') {
        console.log('Refreshing products...');
      }
      await fetchProductsWithColors();

      // Set success message
      setSyncMessage('Products refreshed successfully');

      // Auto-hide the message after 3 seconds
      setTimeout(() => {
        setSyncMessage(null);
      }, 3000);
    } catch (error) {
      console.error('Error refreshing products:', error);
      setSyncMessage('Error refreshing products');
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle product selection
  const handleSelectProduct = (id: string) => {
    setActiveProductId(id);
  };

  // Handle product deletion
  const handleDeleteProduct = async () => {
    if (!deleteConfirmId) {return;}

    try {
      await deleteProduct(deleteConfirmId);
      setDeleteConfirmId(null);

      // If the deleted product was active, clear the active selection
      if (deleteConfirmId === activeProductId) {
        setActiveProductId(null);
      }
    } catch (err) {
      console.error('Failed to delete product:', err);
    }
  };

  // Filter products based on search query from the main search bar
  const filteredProducts = searchQuery
    ? uniqueProducts.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : uniqueProducts;

  // Show organization warning if no organization is selected
  if (!currentOrganization) {
    return (
      <div className="h-full flex flex-col bg-[var(--color-ui-background-primary)] border-r border-[var(--color-ui-border-light)]">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center px-6">
            <AlertCircle className="mx-auto h-12 w-12 text-[var(--color-ui-foreground-tertiary)] mb-4" />
            <h3 className="text-sm font-medium text-[var(--color-ui-foreground-primary)] mb-2">
              No Organization Selected
            </h3>
            <p className="text-xs text-[var(--color-ui-foreground-secondary)] mb-4">
              Please select an organization from the dropdown menu to view products.
            </p>
            {error && (
              <p className="text-xs text-red-500 mt-2">
                {error}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-[var(--color-ui-background-primary)] border-r border-[var(--color-ui-border-light)]">
      {/* Split view with sidebar and content */}
      <div className="flex flex-1 h-full overflow-hidden">
        {/* Products sidebar */}
        <div className="w-56 border-r border-[var(--color-ui-border-light)] flex flex-col bg-[var(--color-ui-background-secondary)]">
          {/* Premium header with actions */}
          <div className="flex items-center justify-between px-3 py-2.5 border-b border-[var(--color-ui-border-light)]">
            <h2 className="text-xs font-medium uppercase tracking-wide text-[var(--color-ui-foreground-secondary)]">Products</h2>
            <div className="flex gap-1">
              <button
                onClick={handleSyncProducts}
                className={`p-1 text-[var(--color-ui-foreground-tertiary)] hover:text-[var(--color-ui-foreground-primary)] rounded transition-colors ${isSyncing ? 'animate-spin' : ''}`}
                aria-label="Refresh products"
                title="Refresh products"
                disabled={isSyncing}
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                <RefreshCw size={14} />
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="p-1 text-[var(--color-ui-foreground-tertiary)] hover:text-[var(--color-ui-foreground-primary)] rounded transition-colors"
                aria-label="Create product"
                title="Create new product"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                <Plus size={14} />
              </button>
            </div>
          </div>


          {/* Status message */}
          {syncMessage && (
            <div className="px-3 py-1.5 text-xs text-center bg-green-100 text-green-800 border-b border-[var(--color-ui-border-light)]">
              {syncMessage}
            </div>
          )}

          <div className="flex-1 overflow-y-auto py-2 products-scroll">
            {isLoading ? (
              <div className="px-4 space-y-3">
                {Array.from({ length: 4 }).map((_, i) => (
                  <ProductCardSkeleton key={i} />
                ))}
              </div>
            ) : error && error.includes('No organization selected') ? (
              <div className="px-4 py-6 text-center">
                <AlertCircle className="mx-auto h-8 w-8 text-yellow-500 mb-2" />
                <p className="text-[var(--color-ui-foreground-secondary)] text-xs">
                  Organization context error
                </p>
                <button
                  onClick={handleSyncProducts}
                  className="mt-3 text-xs text-brand-primary hover:text-brand-primary-dark transition-colors"
                  style={{
                    transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                  }}
                >
                  Retry
                </button>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="px-4 py-6 text-center">
                <Folder className="mx-auto h-8 w-8 text-[var(--color-ui-foreground-tertiary)] mb-2" />
                <p className="text-[var(--color-ui-foreground-tertiary)] text-xs">
                  {searchQuery ? 'No products match your search' : 'No products yet'}
                </p>
                {!searchQuery && (
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="mt-3 text-xs text-brand-primary hover:text-brand-primary-dark transition-colors"
                    style={{
                      transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                    }}
                  >
                    Create your first product
                  </button>
                )}
              </div>
            ) : (
              <ul className="space-y-0.5 px-2">
                {filteredProducts.map(product => {
                  // Generate a color for the product based on its name (just as a visual indicator)
                  const colorHash = product.name.split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0) % 360;
                  const dotColor = `hsl(${colorHash}, 70%, 60%)`;

                  return (
                    <li key={product.id}>
                      <div
                        className={`group flex items-center px-2.5 py-1.5 rounded-md cursor-pointer transition-all ${
                          activeProductId === product.id
                            ? 'product-item-active bg-[var(--color-ui-background-tertiary)] text-[var(--color-ui-foreground-primary)]'
                            : 'text-[var(--color-ui-foreground-secondary)] hover:bg-[var(--color-ui-background-tertiary)] hover:bg-opacity-50 border-l-2 border-transparent pl-2'
                        }`}
                        onClick={() => handleSelectProduct(product.id)}
                        style={{
                          transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                        }}
                      >
                        <div className="flex items-center flex-1 min-w-0">
                          <div
                            className="product-dot mr-2 flex-shrink-0"
                            style={{ backgroundColor: dotColor }}
                          />
                          <div className="truncate text-sm">{product.name}</div>
                        </div>

                        <div className="flex items-center ml-1">
                          <span className="text-xs text-[var(--color-ui-foreground-secondary)] font-mono">
                            {product.colors?.length || 0}
                          </span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setDeleteConfirmId(product.id);
                            }}
                            className="p-1 ml-1 text-[var(--color-ui-foreground-tertiary)] hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                            aria-label={`Delete ${product.name}`}
                            title={`Delete ${product.name}`}
                            style={{
                              transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                            }}
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
        </div>

        {/* Product view */}
        <div className="flex-1 overflow-hidden">
          <ProductView activeProductId={activeProductId} />
        </div>
      </div>

      {/* Create product modal */}
      <ProductCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />

      {/* Delete confirmation modal */}
      {deleteConfirmId && (
        <Modal
          isOpen={true}
          onClose={() => setDeleteConfirmId(null)}
          title="Delete Product?"
        >
          <div className="p-5">
            <div className="text-[var(--color-ui-foreground-secondary)] text-sm mb-5">
              Are you sure you want to delete this product? This action cannot be undone.
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirmId(null)}
                className="px-3.5 py-1.5 text-xs font-medium text-[var(--color-ui-foreground-primary)] bg-[var(--color-ui-background-tertiary)] rounded-md hover:bg-[var(--color-ui-background-secondary)] transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteProduct}
                className="px-3.5 py-1.5 text-xs font-medium bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ProductsPanel;