#!/usr/bin/env node

/**
 * Simple database initialization test script
 * Tests the core database functionality directly
 */

const path = require('path');
const fs = require('fs');

// Mock electron app for testing
const mockApp = {
  getPath: (name) => {
    if (name === 'userData') {
      return path.join(require('os').homedir(), 'Library/Application Support/chroma-sync');
    }
    return process.cwd();
  },
  getAppPath: () => process.cwd()
};

// Set up global.app mock
global.app = mockApp;

async function testDatabaseInit() {
  console.log('🔍 Testing Database Initialization...');
  
  try {
    // Test 1: Load better-sqlite3
    console.log('\n1️⃣ Testing better-sqlite3 loading...');
    const Database = require('better-sqlite3');
    console.log('✅ better-sqlite3 loaded successfully');
    
    // Test 2: Create in-memory database
    console.log('\n2️⃣ Testing in-memory database...');
    const memDb = new Database(':memory:');
    const result = memDb.prepare('SELECT 1 as test').get();
    memDb.close();
    console.log('✅ In-memory database works:', result);
    
    // Test 3: Test database path
    console.log('\n3️⃣ Testing database path...');
    const dbPath = path.join(mockApp.getPath('userData'), 'chromasync.db');
    console.log('Database path:', dbPath);
    
    // Ensure directory exists
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      console.log('Creating database directory...');
      fs.mkdirSync(dbDir, { recursive: true });
    }
    console.log('✅ Database directory exists');
    
    // Test 4: Create actual database file
    console.log('\n4️⃣ Testing actual database file...');
    const db = new Database(dbPath, { fileMustExist: false });
    
    // Test basic operation
    const testResult = db.prepare('SELECT 1 as test').get();
    console.log('✅ Database file works:', testResult);
    
    // Test 5: Apply PRAGMA settings
    console.log('\n5️⃣ Testing PRAGMA settings...');
    db.exec(`
      PRAGMA foreign_keys = ON;
      PRAGMA journal_mode = WAL;
      PRAGMA synchronous = NORMAL;
      PRAGMA temp_store = MEMORY;
    `);
    console.log('✅ PRAGMA settings applied');
    
    // Test 6: Create a simple table
    console.log('\n6️⃣ Testing table creation...');
    db.exec(`
      CREATE TABLE IF NOT EXISTS test_table (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Insert test data
    const stmt = db.prepare('INSERT INTO test_table (name) VALUES (?)');
    const insertResult = stmt.run('test_name');
    console.log('✅ Table creation and insert:', insertResult);
    
    // Query test data
    const selectResult = db.prepare('SELECT * FROM test_table').all();
    console.log('✅ Query result:', selectResult);
    
    // Clean up
    db.close();
    console.log('✅ Database closed successfully');
    
    console.log('\n🎉 All database tests passed!');
    return true;
    
  } catch (error) {
    console.error('\n❌ Database test failed:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });
    return false;
  }
}

// Run the test
testDatabaseInit().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});