#!/usr/bin/env node
/**
 * Test script to verify product-color sync functionality
 * Run this after fresh install to verify sync works automatically
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

const organizationId = '4047153f-7be8-490b-9cb2-a1e3ed04b92b';

function getDbPath() {
  const platform = os.platform();
  switch (platform) {
    case 'darwin':
      return path.join(os.homedir(), 'Library/Application Support/chroma-sync/chromasync.db');
    case 'win32':
      return path.join(os.homedir(), 'AppData/Roaming/chroma-sync/chromasync.db');
    case 'linux':
      return path.join(os.homedir(), '.config/chroma-sync/chromasync.db');
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
}

function checkProductColorCount() {
  const dbPath = getDbPath();
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found:', dbPath);
    return 0;
  }

  try {
    const result = execSync(
      `sqlite3 "${dbPath}" "SELECT COUNT(*) FROM product_colors;"`,
      { encoding: 'utf8' }
    ).trim();
    
    const count = parseInt(result);
    console.log(`📊 Product-color relationships found: ${count}`);
    return count;
  } catch (error) {
    console.error('❌ Error checking database:', error.message);
    return 0;
  }
}

function checkOrganizationStatus() {
  const dbPath = getDbPath();
  
  try {
    const result = execSync(
      `sqlite3 "${dbPath}" "SELECT external_id, name FROM organizations WHERE external_id = '${organizationId}';"`,
      { encoding: 'utf8' }
    ).trim();
    
    if (result) {
      console.log(`✅ Organization found: ${result}`);
      return true;
    } else {
      console.log(`❌ Organization ${organizationId} not found`);
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking organization:', error.message);
    return false;
  }
}

function main() {
  console.log('🧪 Testing Product-Color Sync Implementation');
  console.log('='.repeat(50));
  
  console.log('\n📍 Database location:', getDbPath());
  
  console.log('\n1. Checking organization status...');
  const orgExists = checkOrganizationStatus();
  
  console.log('\n2. Checking product-color relationships...');
  const count = checkProductColorCount();
  
  console.log('\n📋 Results Summary:');
  console.log(`   Organization exists: ${orgExists ? '✅' : '❌'}`);
  console.log(`   Product-color count: ${count}`);
  
  if (count >= 500) {
    console.log('\n🎉 SUCCESS: Product-color sync appears to be working correctly!');
    console.log('   Expected ~529 relationships, found:', count);
  } else if (count > 0) {
    console.log('\n⚠️  PARTIAL: Some relationships found, but fewer than expected');
    console.log('   This might indicate incomplete sync or test data');
  } else {
    console.log('\n❌ FAILURE: No product-color relationships found');
    console.log('   This indicates the automatic sync is not working');
    console.log('\n🔧 Troubleshooting steps:');
    console.log('   1. Ensure the app has been started and authenticated');
    console.log('   2. Check the app logs for sync errors');
    console.log('   3. Verify Supabase connection and organization setup');
  }
  
  console.log('\n🧪 Test completed');
}

if (require.main === module) {
  main();
}
