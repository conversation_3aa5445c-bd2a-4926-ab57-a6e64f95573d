-- Migration 007: Add code and display_name columns to colors table
-- This migration adds the columns that are expected by the sync service

-- Add the code column to the colors table if it doesn't exist
ALTER TABLE colors ADD COLUMN code TEXT;

-- Add the display_name column to the colors table if it doesn't exist
ALTER TABLE colors ADD COLUMN display_name TEXT;

-- Add properties column for storing additional color metadata
ALTER TABLE colors ADD COLUMN properties JSON DEFAULT '{}';

-- Copy data from color_code to code if color_code exists
UPDATE colors SET code = color_code WHERE color_code IS NOT NULL AND code IS NULL;

-- Copy name to display_name if display_name is empty
UPDATE colors SET display_name = name WHERE display_name IS NULL;

-- For compatibility, keep any existing color_code column but prioritize 'code'
-- The RealtimeSyncService expects the 'code' and 'display_name' columns specifically