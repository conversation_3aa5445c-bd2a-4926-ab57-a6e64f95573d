/**
 * @file string-utils.ts
 * @description Utility functions for string manipulations and normalizations
 */

import { isNonEmptyString } from '../../shared/types/type-guards';
import {
  isNonNull,
} from '../../shared/types/null-safety.types';

/**
 * Normalizes a string to title case (capitalizes first letter of each word)
 * @param str The string to normalize
 * @returns The normalized string in title case
 */
export function toTitleCase(str: string | null | undefined): string {
  if (!isNonEmptyString(str)) {
    return '';
  }

  // Split by spaces and capitalize first letter of each word
  return str
    .toLowerCase()
    .split(' ')
    .map(word => {
      if (word.length === 0) {
        return word;
      }
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(' ');
}

/**
 * Normalizes a color name to ensure consistent capitalization
 * and formatting throughout the application
 * @param name The color name string to normalize
 * @returns The normalized color name string
 */
export function normalizeColorName(name: string | null | undefined): string {
  if (!isNonEmptyString(name)) {
    return '';
  }

  // Apply title case normalization
  return toTitleCase(name);
}

/**
 * Standardizes color code formatting to ensure consistency
 * throughout the application (e.g., "2945 C", not "2945C" or "P 2945C")
 * @param code The color code string to standardize
 * @returns The standardized color code string
 */
export function standardizeColorCode(code: string | null | undefined): string {
  if (!isNonEmptyString(code)) {
    return '';
  }

  // Remove any leading "P" or "p" designator if present
  const standardized = code.replace(/^[pP]\s+/, '');

  // Extract numbers and letters separately with enhanced null safety
  const matches = standardized.match(/([0-9]+)\s*([A-Za-z]+)?/);
  if (!matches || !isNonNull(matches[1])) {
    return code; // If no match found, return original
  }

  const numbers = matches[1]; // Now guaranteed to be non-null
  const suffix = matches[2] || '';

  // If there's a suffix, ensure a space between numbers and suffix
  // and convert suffix to uppercase for consistency
  if (suffix && suffix.trim().length > 0) {
    return `${numbers} ${suffix.trim().toUpperCase()}`;
  }

  return numbers;
}
