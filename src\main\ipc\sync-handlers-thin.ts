/**
 * @file sync-handlers-thin.ts
 * @description Thin IPC handlers for sync operations using UnifiedSyncManager
 * 
 * These handlers follow the thin handler pattern:
 * - Minimal business logic (just validation and delegation)
 * - Delegate to specialized services
 * - Use organization context where needed
 * - Return standardized IPC response format
 */

import { ipcMain } from 'electron';
import { registerHandlerSafely } from '../utils/ipcRegistry';
import { getCurrentOrganization } from '../utils/organization-context';
import { getOAuthService } from '../services/service-locator';
import type { UnifiedSyncManager } from '../services/sync/unified-sync-manager';
import type { SyncOutboxService } from '../services/sync/sync-outbox.service';
import type { SyncStatusManagerService } from '../services/sync/sync-status-manager.service';

/**
 * Standardized IPC response format
 */
interface IPCResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  userMessage?: string;
  timestamp: number;
}

/**
 * Thin sync handlers using dependency injection
 */
export class SyncHandlersThin {
  constructor(
    private unifiedSyncManager: UnifiedSyncManager,
    private syncOutboxService: SyncOutboxService,
    private syncStatusManager: SyncStatusManagerService
  ) {}

  /**
   * Register all thin sync handlers
   */
  public registerHandlers(): void {
    // Manual sync execution
    registerHandlerSafely(ipcMain, 'sync:execute', this.handleExecuteSync.bind(this));
    registerHandlerSafely(ipcMain, 'sync:execute-type', this.handleExecuteSyncType.bind(this));
    
    // Sync status and configuration
    registerHandlerSafely(ipcMain, 'sync:get-status', this.handleGetStatus.bind(this));
    registerHandlerSafely(ipcMain, 'sync:get-config', this.handleGetConfig.bind(this));
    registerHandlerSafely(ipcMain, 'sync:update-config', this.handleUpdateConfig.bind(this));
    
    // Sync monitoring and statistics
    registerHandlerSafely(ipcMain, 'sync:get-progress', this.handleGetProgress.bind(this));
    registerHandlerSafely(ipcMain, 'sync:get-metrics', this.handleGetMetrics.bind(this));
    registerHandlerSafely(ipcMain, 'sync:get-queue-stats', this.handleGetQueueStats.bind(this));
    registerHandlerSafely(ipcMain, 'sync:get-status-report', this.handleGetStatusReport.bind(this));
    
    // Outbox management
    registerHandlerSafely(ipcMain, 'sync:has-unsynced-changes', this.handleHasUnsyncedChanges.bind(this));
    registerHandlerSafely(ipcMain, 'sync:get-outbox-status', this.handleGetOutboxStatus.bind(this));
    registerHandlerSafely(ipcMain, 'sync:clear-outbox', this.handleClearOutbox.bind(this));
    registerHandlerSafely(ipcMain, 'sync:clear-failed-operations', this.handleClearFailedOperations.bind(this));
    registerHandlerSafely(ipcMain, 'sync:clear-orphaned-operations', this.handleClearOrphanedOperations.bind(this));
    
    // Sync initialization and control
    registerHandlerSafely(ipcMain, 'sync:initialize', this.handleInitialize.bind(this));
    registerHandlerSafely(ipcMain, 'sync:stop', this.handleStop.bind(this));
    registerHandlerSafely(ipcMain, 'sync:reset', this.handleReset.bind(this));
    
    // User activity tracking
    registerHandlerSafely(ipcMain, 'sync:update-user-activity', this.handleUpdateUserActivity.bind(this));
    
    console.log('[SyncHandlersThin] ✅ All thin sync handlers registered');
  }

  /**
   * Execute full sync with authentication and organization validation
   */
  private async handleExecuteSync(): Promise<IPCResponse> {
    try {
      const authCheck = await this.validateAuthAndOrg();
      if (!authCheck.success) {
        return authCheck;
      }

      // Check if sync manager is ready
      if (!this.unifiedSyncManager.isReady()) {
        const initResult = await this.initializeSyncManager();
        if (!initResult.success) {
          return {
            success: false,
            error: 'Failed to initialize sync manager',
            userMessage: 'Sync system not ready. Please ensure you are authenticated and an organization is selected.',
            timestamp: Date.now()
          };
        }
      }

      const result = await this.unifiedSyncManager.sync('full', 'bidirectional', 'normal');
      
      return {
        success: result.success,
        data: {
          itemsProcessed: result.itemsProcessed,
          duration: result.duration,
          operation: result.operation
        },
        error: result.success ? undefined : result.errors?.[0],
        userMessage: result.success ? 'Sync completed successfully' : 'Sync operation failed',
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Execute sync failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Sync operation failed',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Execute sync for specific type
   */
  private async handleExecuteSyncType(
    _: any,
    type: 'colors' | 'products' | 'organizations' | 'full',
    direction: 'push' | 'pull' | 'bidirectional' = 'bidirectional'
  ): Promise<IPCResponse> {
    try {
      const authCheck = await this.validateAuthAndOrg();
      if (!authCheck.success) {
        return authCheck;
      }

      if (!this.unifiedSyncManager.isReady()) {
        return {
          success: false,
          error: 'Sync manager not ready',
          userMessage: 'Sync system not ready',
          timestamp: Date.now()
        };
      }

      const result = await this.unifiedSyncManager.sync(type, direction, 'normal');
      
      return {
        success: result.success,
        data: {
          itemsProcessed: result.itemsProcessed,
          duration: result.duration,
          operation: result.operation
        },
        error: result.success ? undefined : result.errors?.[0],
        userMessage: result.success ? `${type} sync completed successfully` : `${type} sync failed`,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Execute sync type failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Sync operation failed',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get sync status
   */
  private async handleGetStatus(): Promise<IPCResponse> {
    try {
      const status = this.unifiedSyncManager.getStatus();
      
      return {
        success: true,
        data: status,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Get status failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get sync configuration
   */
  private async handleGetConfig(): Promise<IPCResponse> {
    try {
      const status = this.unifiedSyncManager.getStatus();
      
      return {
        success: true,
        data: {
          ...status.config,
          enabled: status.config.autoSyncEnabled,
          interval: status.config.autoSyncInterval * 60 * 1000, // Convert to milliseconds
          lastSync: status.lastSyncTime ? new Date(status.lastSyncTime).toISOString() : null
        },
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Get config failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Update sync configuration
   */
  private async handleUpdateConfig(
    _: any,
    config: {
      autoSyncEnabled?: boolean;
      autoSyncInterval?: number;
      realtimeEnabled?: boolean;
      maxRetries?: number;
    }
  ): Promise<IPCResponse> {
    try {
      // Convert interval from milliseconds to minutes if provided
      const processedConfig = { ...config };
      if (config.autoSyncInterval !== undefined) {
        processedConfig.autoSyncInterval = Math.ceil(config.autoSyncInterval / (60 * 1000));
      }

      this.unifiedSyncManager.updateConfig(processedConfig);
      
      return {
        success: true,
        data: this.unifiedSyncManager.getStatus().config,
        userMessage: 'Sync configuration updated successfully',
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Update config failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Failed to update sync configuration',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get sync progress
   */
  private async handleGetProgress(): Promise<IPCResponse> {
    try {
      const result = await this.syncStatusManager.getProgress();
      return {
        success: true,
        data: result,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Get progress failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get sync metrics
   */
  private async handleGetMetrics(): Promise<IPCResponse> {
    try {
      const result = await this.syncStatusManager.getMetrics();
      return {
        success: true,
        data: result,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Get metrics failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get queue statistics
   */
  private async handleGetQueueStats(): Promise<IPCResponse> {
    try {
      const result = await this.syncStatusManager.getQueueStats();
      return {
        success: true,
        data: result,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Get queue stats failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get comprehensive status report
   */
  private async handleGetStatusReport(): Promise<IPCResponse> {
    try {
      const report = this.syncStatusManager.getStatusReport();
      return {
        success: true,
        data: report,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Get status report failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Check for unsynced local changes
   */
  private async handleHasUnsyncedChanges(): Promise<IPCResponse> {
    try {
      const result = await this.syncStatusManager.hasUnsyncedLocalChanges();
      return {
        success: true,
        data: result,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Has unsynced changes failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get outbox status
   */
  private async handleGetOutboxStatus(): Promise<IPCResponse> {
    try {
      const pendingChanges = this.syncOutboxService.getPendingChanges();
      
      return {
        success: true,
        data: {
          pendingChanges,
          count: pendingChanges.length,
          hasChanges: pendingChanges.length > 0
        },
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Get outbox status failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Clear all outbox items
   */
  private async handleClearOutbox(): Promise<IPCResponse> {
    try {
      const pendingCount = this.syncOutboxService.getPendingChanges().length;
      this.syncOutboxService.clearAll();
      
      return {
        success: true,
        data: { removedCount: pendingCount },
        userMessage: `Cleared ${pendingCount} pending items from sync outbox`,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Clear outbox failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Failed to clear outbox',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Clear failed delete operations
   */
  private async handleClearFailedOperations(): Promise<IPCResponse> {
    try {
      const removedCount = this.syncOutboxService.clearNotFoundDeleteOperations();
      
      return {
        success: true,
        data: { removedCount },
        userMessage: `Cleared ${removedCount} failed delete operations`,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Clear failed operations failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Failed to clear failed operations',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Clear orphaned operations
   */
  private async handleClearOrphanedOperations(): Promise<IPCResponse> {
    try {
      const removedCount = this.syncOutboxService.clearNotFoundDeleteOperations();
      
      return {
        success: true,
        data: { removedCount },
        userMessage: `Cleared ${removedCount} orphaned operations`,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Clear orphaned operations failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Failed to clear orphaned operations',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Initialize sync manager
   */
  private async handleInitialize(): Promise<IPCResponse> {
    try {
      const authCheck = await this.validateAuthAndOrg();
      if (!authCheck.success) {
        return authCheck;
      }

      const result = await this.initializeSyncManager();
      return result;
    } catch (error) {
      console.error('[SyncHandlersThin] Initialize failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Failed to initialize sync manager',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Stop sync manager
   */
  private async handleStop(): Promise<IPCResponse> {
    try {
      this.unifiedSyncManager.stop();
      
      return {
        success: true,
        userMessage: 'Sync manager stopped successfully',
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Stop failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Failed to stop sync manager',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Reset sync manager and status
   */
  private async handleReset(): Promise<IPCResponse> {
    try {
      this.unifiedSyncManager.stop();
      this.syncStatusManager.reset();
      
      return {
        success: true,
        userMessage: 'Sync system reset successfully',
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Reset failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Failed to reset sync system',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Update user activity
   */
  private async handleUpdateUserActivity(): Promise<IPCResponse> {
    try {
      this.syncStatusManager.updateUserActivity();
      
      return {
        success: true,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Update user activity failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Validate authentication and organization context
   */
  private async validateAuthAndOrg(): Promise<IPCResponse> {
    try {
      const oauthService = getOAuthService();
      const user = await oauthService.getCurrentUser();
      
      if (!user?.id) {
        return {
          success: false,
          error: 'No authenticated user found',
          userMessage: 'Authentication required to sync',
          timestamp: Date.now()
        };
      }

      const currentOrgId = getCurrentOrganization();
      if (!currentOrgId) {
        return {
          success: false,
          error: 'No organization context found',
          userMessage: 'Organization context required to sync',
          timestamp: Date.now()
        };
      }

      return {
        success: true,
        data: { userId: user.id, organizationId: currentOrgId },
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Auth/org validation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Authentication validation failed',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Initialize sync manager with user and organization context
   */
  private async initializeSyncManager(): Promise<IPCResponse> {
    try {
      const oauthService = getOAuthService();
      const user = await oauthService.getCurrentUser();
      const currentOrgId = getCurrentOrganization();
      
      if (!user?.id || !currentOrgId) {
        return {
          success: false,
          error: 'Missing user or organization context',
          userMessage: 'Authentication and organization context required',
          timestamp: Date.now()
        };
      }

      const config = {
        autoSyncEnabled: true,
        autoSyncInterval: 5, // 5 minutes
        realtimeEnabled: false,
        maxRetries: 3
      };

      await this.unifiedSyncManager.initialize(user.id, currentOrgId, config);
      
      return {
        success: true,
        userMessage: 'Sync manager initialized successfully',
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[SyncHandlersThin] Initialize sync manager failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        userMessage: 'Failed to initialize sync manager',
        timestamp: Date.now()
      };
    }
  }
}

/**
 * Factory function to create and register thin sync handlers
 */
export function createSyncHandlersThin(
  unifiedSyncManager: UnifiedSyncManager,
  syncOutboxService: SyncOutboxService,
  syncStatusManager: SyncStatusManagerService
): SyncHandlersThin {
  return new SyncHandlersThin(unifiedSyncManager, syncOutboxService, syncStatusManager);
}