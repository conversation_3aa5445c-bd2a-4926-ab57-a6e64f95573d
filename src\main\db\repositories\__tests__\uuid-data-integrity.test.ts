/**
 * @file uuid-data-integrity.test.ts
 * @description Data integrity validation tests for pure UUID architecture
 * 
 * Comprehensive testing of data integrity across all operations:
 * - Foreign key constraint validation with UUIDs
 * - Referential integrity during cascading operations
 * - Data consistency across repository boundaries  
 * - Transaction integrity with UUID operations
 * - Schema constraint enforcement
 * - Cross-table relationship validation
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import Database from 'better-sqlite3';
import { v4 as uuidv4, validate as isValidUUID } from 'uuid';
import { ColorRepository } from '../color.repository';
import { ProductRepository } from '../product.repository';
import { OrganizationRepository } from '../organization.repository';
import { NewColorEntry, UpdateColorEntry } from '../../../../shared/types/color.types';
import { NewProduct, UpdateProduct } from '../../../../shared/types/product.types';

describe.sequential('UUID Data Integrity Validation Tests', () => {
  let db: Database.Database;
  let colorRepo: ColorRepository;
  let productRepo: ProductRepository;
  let organizationRepo: OrganizationRepository;
  
  let testOrgId: string;
  let testUserId: string;

  beforeEach(() => {
    // Create in-memory SQLite database for testing
    db = new Database(':memory:');
    
    // Set up pure UUID schema with constraints (post-migration)
    setupUUIDSchema(db);
    
    // Create repository instances
    colorRepo = new ColorRepository(db);
    productRepo = new ProductRepository(db);
    organizationRepo = new OrganizationRepository(db);
    
    // Generate test UUIDs
    testOrgId = uuidv4();
    testUserId = uuidv4();
    
    // Create test organization
    setupTestOrganization(db, testOrgId, testUserId);
  });

  afterEach(() => {
    if (db && db.open) {
      try {
        db.close();
      } catch (error) {
        console.warn('Database close error:', error);
      }
    }
  });

  describe('Foreign Key Constraint Validation', () => {
    test('should enforce organization foreign key constraints for colors', () => {
      const nonExistentOrgId = uuidv4();
      
      const colorData: NewColorEntry = {
        name: 'FK Test Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      // Should fail when trying to insert color with non-existent organization
      expect(() => {
        colorRepo.insert(colorData, nonExistentOrgId);
      }).toThrow();
    });

    test('should enforce organization foreign key constraints for products', () => {
      const nonExistentOrgId = uuidv4();
      
      const productData: NewProduct = {
        name: 'FK Test Product',
        description: 'Testing foreign key constraints'
      };

      // Should fail when trying to insert product with non-existent organization
      expect(() => {
        productRepo.insert(productData, nonExistentOrgId, testUserId);
      }).toThrow();
    });

    test('should enforce foreign key constraints in product-color relationships', () => {
      const nonExistentProductId = uuidv4();
      const nonExistentColorId = uuidv4();

      // Create valid entities
      const productData: NewProduct = { name: 'Valid Product' };
      const colorData: NewColorEntry = {
        name: 'Valid Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      const validProductId = productRepo.insert(productData, testOrgId, testUserId);
      const validColorId = colorRepo.insert(colorData, testOrgId);

      // Should fail with non-existent product ID
      expect(() => {
        productRepo.addProductColor(nonExistentProductId, validColorId, testOrgId);
      }).toThrow();

      // Should fail with non-existent color ID
      expect(() => {
        productRepo.addProductColor(validProductId, nonExistentColorId, testOrgId);
      }).toThrow();

      // Should fail with non-existent organization ID
      expect(() => {
        productRepo.addProductColor(validProductId, validColorId, uuidv4());
      }).toThrow();
    });

    test('should enforce organization member foreign key constraints', () => {
      const nonExistentOrgId = uuidv4();
      const anotherUserId = uuidv4();

      // Should fail when adding member to non-existent organization
      expect(() => {
        organizationRepo.insertMember(nonExistentOrgId, anotherUserId, 'member');
      }).toThrow();
    });
  });

  describe('Referential Integrity During Cascading Operations', () => {
    test('should maintain referential integrity when organization is deleted', () => {
      // Create a separate organization for deletion testing
      const deleteOrgId = uuidv4();
      const deleteOrgData = {
        external_id: deleteOrgId,
        name: 'Delete Test Organization',
        slug: 'delete-test-org',
        plan: 'free' as const,
        settings: '{}',
        ownerId: testUserId
      };

      organizationRepo.insert(deleteOrgData);

      // Create products and colors in this organization
      const productData: NewProduct = { name: 'Cascade Test Product' };
      const colorData: NewColorEntry = {
        name: 'Cascade Test Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      const productId = productRepo.insert(productData, deleteOrgId, testUserId);
      const colorId = colorRepo.insert(colorData, deleteOrgId);

      // Create relationship
      productRepo.addProductColor(productId, colorId, deleteOrgId);

      // Add member
      organizationRepo.insertMember(deleteOrgId, testUserId, 'owner');

      // Verify data exists
      expect(organizationRepo.findById(deleteOrgId)).toBeTruthy();
      expect(productRepo.findById(productId, deleteOrgId)).toBeTruthy();
      expect(colorRepo.findById(colorId, deleteOrgId)).toBeTruthy();
      expect(organizationRepo.findMembers(deleteOrgId)).toHaveLength(1);
      expect(productRepo.getProductColors(productId, deleteOrgId)).toHaveLength(1);

      // Delete organization (should cascade)
      db.prepare('DELETE FROM organizations WHERE id = ?').run(deleteOrgId);

      // Verify cascade deletion worked
      expect(organizationRepo.findById(deleteOrgId)).toBeNull();
      expect(productRepo.findById(productId, deleteOrgId)).toBeNull();
      expect(colorRepo.findById(colorId, deleteOrgId)).toBeNull();
      expect(organizationRepo.findMembers(deleteOrgId)).toEqual([]);
      expect(productRepo.getProductColors(productId, deleteOrgId)).toEqual([]);
    });

    test('should maintain referential integrity when product is deleted', () => {
      // Create entities
      const productData: NewProduct = { name: 'Delete Test Product' };
      const colorData: NewColorEntry = {
        name: 'Delete Test Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      const productId = productRepo.insert(productData, testOrgId, testUserId);
      const colorId = colorRepo.insert(colorData, testOrgId);

      // Create relationship
      productRepo.addProductColor(productId, colorId, testOrgId);

      // Verify relationship exists
      expect(productRepo.getProductColors(productId, testOrgId)).toHaveLength(1);

      // Hard delete product (should cascade to product_colors)
      db.prepare('DELETE FROM products WHERE id = ?').run(productId);

      // Verify cascade worked
      expect(productRepo.findById(productId, testOrgId)).toBeNull();
      expect(productRepo.getProductColors(productId, testOrgId)).toEqual([]);
      
      // Color should still exist
      expect(colorRepo.findById(colorId, testOrgId)).toBeTruthy();
    });

    test('should maintain referential integrity when color is deleted', () => {
      // Create entities
      const productData: NewProduct = { name: 'Color Delete Test Product' };
      const colorData: NewColorEntry = {
        name: 'Color Delete Test Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      const productId = productRepo.insert(productData, testOrgId, testUserId);
      const colorId = colorRepo.insert(colorData, testOrgId);

      // Create relationship
      productRepo.addProductColor(productId, colorId, testOrgId);

      // Verify relationship exists
      expect(productRepo.getProductColors(productId, testOrgId)).toHaveLength(1);

      // Hard delete color (should cascade to product_colors)
      db.prepare('DELETE FROM colors WHERE id = ?').run(colorId);

      // Verify cascade worked
      expect(colorRepo.findById(colorId, testOrgId)).toBeNull();
      expect(productRepo.getProductColors(productId, testOrgId)).toEqual([]);
      
      // Product should still exist
      expect(productRepo.findById(productId, testOrgId)).toBeTruthy();
    });
  });

  describe('Data Consistency Across Repository Boundaries', () => {
    test('should maintain consistent UUID references across repositories', () => {
      // Create organization through repository
      const newOrgId = uuidv4();
      const orgData = {
        external_id: newOrgId,
        name: 'Cross-Repo Test Org',
        slug: 'cross-repo-test',
        plan: 'free' as const,
        settings: '{}',
        ownerId: testUserId
      };

      organizationRepo.insert(orgData);

      // Create product and color referencing this organization
      const productData: NewProduct = { name: 'Cross-Repo Product' };
      const colorData: NewColorEntry = {
        name: 'Cross-Repo Color',
        hex: '#FF5733',
        source: 'USER',
        isLibrary: false
      };

      const productId = productRepo.insert(productData, newOrgId, testUserId);
      const colorId = colorRepo.insert(colorData, newOrgId);

      // Verify all references point to the same organization UUID
      const org = organizationRepo.findById(newOrgId);
      const product = productRepo.findById(productId, newOrgId);
      const color = colorRepo.findById(colorId, newOrgId);

      expect(org!.id).toBe(newOrgId);
      expect(product!.organization_id).toBe(newOrgId);
      expect(color!.organization_id).toBe(newOrgId);

      // Verify external_id consistency
      expect(org!.external_id).toBe(newOrgId);
      expect(product!.external_id).toBe(productId);
      expect(color!.external_id).toBe(colorId);
    });

    test('should maintain consistent data types across operations', () => {
      // Create entities with specific UUIDs to test type consistency
      const specificOrgId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
      const specificProductId = 'f47ac10b-58cc-4372-a567-0e02b2c3d480';
      const specificColorId = 'f47ac10b-58cc-4372-a567-0e02b2c3d481';

      // Direct database insert to control UUIDs
      db.prepare(`
        INSERT INTO organizations (id, name, slug, plan, settings)
        VALUES (?, ?, ?, ?, ?)
      `).run(specificOrgId, 'Type Test Org', 'type-test', 'free', '{}');

      db.prepare(`
        INSERT INTO products (id, organization_id, name, user_id, created_at, updated_at)
        VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))
      `).run(specificProductId, specificOrgId, 'Type Test Product', testUserId);

      db.prepare(`
        INSERT INTO colors (id, organization_id, source_id, name, display_name, hex, is_library, created_at, updated_at)
        VALUES (?, ?, 1, ?, ?, ?, ?, datetime('now'), datetime('now'))
      `).run(specificColorId, specificOrgId, 'Type Test Color', 'Type Test Color', '#FF5733', 0);

      // Verify all repositories return consistent data types
      const org = organizationRepo.findById(specificOrgId);
      const product = productRepo.findById(specificProductId, specificOrgId);
      const color = colorRepo.findById(specificColorId, specificOrgId);

      expect(typeof org!.id).toBe('string');
      expect(typeof product!.id).toBe('string');
      expect(typeof color!.id).toBe('string');
      expect(typeof product!.organization_id).toBe('string');
      expect(typeof color!.organization_id).toBe('string');

      // Verify UUID format consistency
      expect(isValidUUID(org!.id)).toBe(true);
      expect(isValidUUID(product!.id)).toBe(true);
      expect(isValidUUID(color!.id)).toBe(true);
      expect(isValidUUID(product!.organization_id)).toBe(true);
      expect(isValidUUID(color!.organization_id)).toBe(true);
    });

    test('should maintain data consistency during complex multi-repository operations', () => {
      // Create multiple organizations
      const org1Id = uuidv4();
      const org2Id = uuidv4();

      const orgData1 = {
        external_id: org1Id,
        name: 'Multi-Repo Org 1',
        slug: 'multi-repo-1',
        plan: 'free' as const,
        settings: '{}',
        ownerId: testUserId
      };

      const orgData2 = {
        external_id: org2Id,
        name: 'Multi-Repo Org 2',
        slug: 'multi-repo-2',
        plan: 'team' as const,
        settings: '{}',
        ownerId: testUserId
      };

      organizationRepo.insert(orgData1);
      organizationRepo.insert(orgData2);

      // Create products and colors in both organizations
      const products1: string[] = [];
      const colors1: string[] = [];
      const products2: string[] = [];
      const colors2: string[] = [];

      for (let i = 0; i < 3; i++) {
        // Org 1
        const product1Id = productRepo.insert({ name: `Org1 Product ${i}` }, org1Id, testUserId);
        const color1Id = colorRepo.insert({
          name: `Org1 Color ${i}`,
          hex: `#FF00${i.toString().padStart(2, '0')}`,
          source: 'USER',
          isLibrary: false
        }, org1Id);
        
        products1.push(product1Id);
        colors1.push(color1Id);

        // Org 2
        const product2Id = productRepo.insert({ name: `Org2 Product ${i}` }, org2Id, testUserId);
        const color2Id = colorRepo.insert({
          name: `Org2 Color ${i}`,
          hex: `#00FF${i.toString().padStart(2, '0')}`,
          source: 'USER',
          isLibrary: false
        }, org2Id);
        
        products2.push(product2Id);
        colors2.push(color2Id);

        // Create relationships
        productRepo.addProductColor(product1Id, color1Id, org1Id);
        productRepo.addProductColor(product2Id, color2Id, org2Id);
      }

      // Verify data isolation between organizations
      const org1Products = productRepo.findAll(org1Id);
      const org1Colors = colorRepo.findAll(org1Id);
      const org2Products = productRepo.findAll(org2Id);
      const org2Colors = colorRepo.findAll(org2Id);

      expect(org1Products).toHaveLength(3);
      expect(org1Colors).toHaveLength(3);
      expect(org2Products).toHaveLength(3);
      expect(org2Colors).toHaveLength(3);

      // Verify no cross-contamination
      org1Products.forEach(p => expect(p.organization_id).toBe(org1Id));
      org1Colors.forEach(c => expect(c.organization_id).toBe(org1Id));
      org2Products.forEach(p => expect(p.organization_id).toBe(org2Id));
      org2Colors.forEach(c => expect(c.organization_id).toBe(org2Id));

      // Verify relationships are correctly isolated
      products1.forEach(productId => {
        const relationships = productRepo.getProductColors(productId, org1Id);
        expect(relationships).toHaveLength(1);
        expect(relationships[0].organization_id).toBe(org1Id);
      });

      products2.forEach(productId => {
        const relationships = productRepo.getProductColors(productId, org2Id);
        expect(relationships).toHaveLength(1);
        expect(relationships[0].organization_id).toBe(org2Id);
      });
    });
  });

  describe('Transaction Integrity with UUID Operations', () => {
    test('should maintain UUID consistency within transactions', () => {
      const initialOrgCount = db.prepare('SELECT COUNT(*) as count FROM organizations').get() as { count: number };
      const initialProductCount = db.prepare('SELECT COUNT(*) as count FROM products').get() as { count: number };
      const initialColorCount = db.prepare('SELECT COUNT(*) as count FROM colors').get() as { count: number };

      const transaction = db.transaction(() => {
        // Create organization
        const newOrgId = uuidv4();
        const orgData = {
          external_id: newOrgId,
          name: 'Transaction Test Org',
          slug: 'transaction-test',
          plan: 'free' as const,
          settings: '{}',
          ownerId: testUserId
        };

        organizationRepo.insert(orgData);

        // Create multiple products and colors
        for (let i = 0; i < 5; i++) {
          const productData: NewProduct = { name: `Transaction Product ${i}` };
          const colorData: NewColorEntry = {
            name: `Transaction Color ${i}`,
            hex: `#${i.toString().padStart(6, '0')}`,
            source: 'USER',
            isLibrary: false
          };

          const productId = productRepo.insert(productData, newOrgId, testUserId);
          const colorId = colorRepo.insert(colorData, newOrgId);

          // Create relationship
          productRepo.addProductColor(productId, colorId, newOrgId);
        }

        // All UUIDs should be valid within transaction
        const transactionOrgs = db.prepare('SELECT * FROM organizations WHERE id = ?').all(newOrgId);
        const transactionProducts = db.prepare('SELECT * FROM products WHERE organization_id = ?').all(newOrgId);
        const transactionColors = db.prepare('SELECT * FROM colors WHERE organization_id = ?').all(newOrgId);

        expect(transactionOrgs).toHaveLength(1);
        expect(transactionProducts).toHaveLength(5);
        expect(transactionColors).toHaveLength(5);

        transactionProducts.forEach((p: any) => {
          expect(isValidUUID(p.id)).toBe(true);
          expect(p.organization_id).toBe(newOrgId);
        });

        transactionColors.forEach((c: any) => {
          expect(isValidUUID(c.id)).toBe(true);
          expect(c.organization_id).toBe(newOrgId);
        });
      });

      // Execute transaction
      transaction();

      // Verify final state
      const finalOrgCount = db.prepare('SELECT COUNT(*) as count FROM organizations').get() as { count: number };
      const finalProductCount = db.prepare('SELECT COUNT(*) as count FROM products').get() as { count: number };
      const finalColorCount = db.prepare('SELECT COUNT(*) as count FROM colors').get() as { count: number };

      expect(finalOrgCount.count).toBe(initialOrgCount.count + 1);
      expect(finalProductCount.count).toBe(initialProductCount.count + 5);
      expect(finalColorCount.count).toBe(initialColorCount.count + 5);
    });

    test('should rollback UUID operations on transaction failure', () => {
      const initialCounts = {
        orgs: db.prepare('SELECT COUNT(*) as count FROM organizations').get() as { count: number },
        products: db.prepare('SELECT COUNT(*) as count FROM products').get() as { count: number },
        colors: db.prepare('SELECT COUNT(*) as count FROM colors').get() as { count: number }
      };

      const failingTransaction = db.transaction(() => {
        // Create some valid data
        const newOrgId = uuidv4();
        const orgData = {
          external_id: newOrgId,
          name: 'Failing Transaction Org',
          slug: 'failing-transaction',
          plan: 'free' as const,
          settings: '{}',
          ownerId: testUserId
        };

        organizationRepo.insert(orgData);

        const productData: NewProduct = { name: 'Failing Transaction Product' };
        const productId = productRepo.insert(productData, newOrgId, testUserId);

        // Force a constraint violation to trigger rollback
        db.prepare(`
          INSERT INTO products (id, organization_id, name, user_id, created_at, updated_at)
          VALUES (?, ?, ?, ?, datetime('now'), datetime('now'))
        `).run(productId, newOrgId, 'Duplicate Product', testUserId); // Same UUID should fail
      });

      // Execute transaction and expect it to fail
      expect(() => {
        failingTransaction();
      }).toThrow();

      // Verify rollback worked
      const finalCounts = {
        orgs: db.prepare('SELECT COUNT(*) as count FROM organizations').get() as { count: number },
        products: db.prepare('SELECT COUNT(*) as count FROM products').get() as { count: number },
        colors: db.prepare('SELECT COUNT(*) as count FROM colors').get() as { count: number }
      };

      expect(finalCounts.orgs.count).toBe(initialCounts.orgs.count);
      expect(finalCounts.products.count).toBe(initialCounts.products.count);
      expect(finalCounts.colors.count).toBe(initialCounts.colors.count);
    });
  });

  describe('Schema Constraint Enforcement', () => {
    test('should enforce UUID length constraints', () => {
      const invalidUUIDs = [
        'too-short',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479-too-long',
        '',
        'f47ac10b-58cc-4372-a567-0e02b2c3d47', // 35 chars instead of 36
        'f47ac10b-58cc-4372-a567-0e02b2c3d479x' // 37 chars instead of 36
      ];

      invalidUUIDs.forEach(invalidUUID => {
        expect(() => {
          db.prepare(`
            INSERT INTO organizations (id, name, slug, plan, settings)
            VALUES (?, ?, ?, ?, ?)
          `).run(invalidUUID, 'Test Org', 'test-slug', 'free', '{}');
        }).toThrow();
      });
    });

    test('should enforce unique constraints on UUID fields', () => {
      const duplicateUUID = uuidv4();

      // First insert should succeed
      db.prepare(`
        INSERT INTO organizations (id, name, slug, plan, settings)
        VALUES (?, ?, ?, ?, ?)
      `).run(duplicateUUID, 'First Org', 'first-org', 'free', '{}');

      // Second insert with same UUID should fail
      expect(() => {
        db.prepare(`
          INSERT INTO organizations (id, name, slug, plan, settings)
          VALUES (?, ?, ?, ?, ?)
        `).run(duplicateUUID, 'Second Org', 'second-org', 'free', '{}');
      }).toThrow();
    });

    test('should enforce CHECK constraints on UUID format', () => {
      // Test invalid UUID formats that pass length check but fail format
      const invalidFormats = [
        '12345678-1234-1234-1234-123456789012', // Valid length but no hex
        'gggggggg-gggg-gggg-gggg-gggggggggggg', // Valid length but invalid chars
        '12345678-1234-5234-1234-123456789012', // Invalid version (5)
        '12345678-1234-4234-c234-123456789012', // Invalid variant (c)
      ];

      invalidFormats.forEach(invalidFormat => {
        expect(() => {
          db.prepare(`
            INSERT INTO organizations (id, name, slug, plan, settings)
            VALUES (?, ?, ?, ?, ?)
          `).run(invalidFormat, 'Test Org', `test-${Date.now()}`, 'free', '{}');
        }).toThrow();
      });
    });
  });
});

/**
 * Setup pure UUID schema for testing (post-migration schema)
 */
function setupUUIDSchema(db: Database.Database) {
  // Organizations table with UUID primary key and constraints
  db.exec(`
    CREATE TABLE organizations (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
      settings JSON DEFAULT '{}',
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      CHECK (length(id) = 36),
      CHECK (id GLOB '[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f]-4[0-9a-f][0-9a-f][0-9a-f]-[89ab][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]')
    );

    CREATE TABLE organization_members (
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      user_id TEXT NOT NULL,
      role TEXT NOT NULL DEFAULT 'member',
      joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
      invited_by TEXT,
      PRIMARY KEY (organization_id, user_id)
    );

    CREATE TABLE color_sources (
      id INTEGER PRIMARY KEY,
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL
    );

    INSERT INTO color_sources (id, code, name) VALUES 
    (1, 'USER', 'User Created'),
    (2, 'PANTONE', 'Pantone Color'),
    (3, 'RAL', 'RAL Color');

    CREATE TABLE products (
      id TEXT PRIMARY KEY,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      user_id TEXT,
      name TEXT NOT NULL,
      description TEXT,
      metadata JSON DEFAULT '{}',
      is_active BOOLEAN NOT NULL DEFAULT TRUE,
      is_synced BOOLEAN NOT NULL DEFAULT FALSE,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT,
      created_by TEXT,
      CHECK (length(id) = 36),
      CHECK (id GLOB '[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f]-4[0-9a-f][0-9a-f][0-9a-f]-[89ab][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]')
    );

    CREATE TABLE colors (
      id TEXT PRIMARY KEY,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      source_id INTEGER NOT NULL DEFAULT 1 REFERENCES color_sources(id),
      name TEXT NOT NULL,
      display_name TEXT,
      code TEXT,
      hex TEXT NOT NULL,
      color_spaces JSON DEFAULT '{}',
      is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
      is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
      is_effect BOOLEAN NOT NULL DEFAULT FALSE,
      is_library BOOLEAN NOT NULL DEFAULT FALSE,
      gradient_colors TEXT,
      notes TEXT,
      tags TEXT,
      properties JSON DEFAULT '{}',
      is_synced BOOLEAN NOT NULL DEFAULT FALSE,
      version INTEGER NOT NULL DEFAULT 1,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
      deleted_at TEXT,
      created_by TEXT,
      user_id TEXT,
      device_id TEXT,
      conflict_resolved_at TEXT,
      CHECK (length(id) = 36),
      CHECK (id GLOB '[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f]-4[0-9a-f][0-9a-f][0-9a-f]-[89ab][0-9a-f][0-9a-f][0-9a-f]-[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]'),
      CHECK (length(hex) = 7 AND substr(hex, 1, 1) = '#')
    );

    CREATE TABLE product_colors (
      product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
      color_id TEXT NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
      display_order INTEGER NOT NULL DEFAULT 0,
      organization_id TEXT NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      added_at TEXT DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (product_id, color_id)
    );

    CREATE INDEX idx_products_org ON products(organization_id);
    CREATE INDEX idx_colors_org ON colors(organization_id);
    CREATE INDEX idx_product_colors_product ON product_colors(product_id);
    CREATE INDEX idx_product_colors_color ON product_colors(color_id);
  `);
}

/**
 * Setup test organization for UUID testing
 */
function setupTestOrganization(db: Database.Database, orgId: string, userId: string) {
  db.prepare(`
    INSERT INTO organizations (id, name, slug, plan, settings)
    VALUES (?, ?, ?, ?, ?)
  `).run(orgId, 'Test Organization', 'test-org-uuid', 'free', '{}');

  db.prepare(`
    INSERT INTO organization_members (organization_id, user_id, role)
    VALUES (?, ?, ?)
  `).run(orgId, userId, 'owner');
}