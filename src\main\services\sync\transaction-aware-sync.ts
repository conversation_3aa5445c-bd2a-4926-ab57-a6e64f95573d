/**
 * Transaction-Aware Sync Operations
 * Provides sync operations wrapped in database transactions for data consistency
 */

import { transactionManager, TransactionOptions, TransactionResult } from '../../db/core/transaction-manager';
import { syncOutboxService, SyncOutboxItem } from './sync-outbox.service';

export interface SyncTransactionContext {
  transactionId: string;
  organizationId: string;
  userId: string;
  enableLogging: boolean;
}

export interface SyncOperationResult {
  success: boolean;
  itemsProcessed: number;
  errors: string[];
  transactionId: string;
  duration: number;
}

/**
 * Transaction-aware sync operations manager
 */
export class TransactionAwareSyncManager {
  private static instance: TransactionAwareSyncManager | null = null;
  
  static getInstance(): TransactionAwareSyncManager {
    if (!TransactionAwareSyncManager.instance) {
      TransactionAwareSyncManager.instance = new TransactionAwareSyncManager();
    }
    return TransactionAwareSyncManager.instance;
  }
  
  private constructor() {
    // Private constructor for singleton pattern
  }
  
  /**
   * Execute sync operation within a transaction
   */
  async executeSyncInTransaction<T>(
    operation: (context: SyncTransactionContext) => Promise<T>,
    organizationId: string,
    userId: string,
    options: TransactionOptions = {}
  ): Promise<TransactionResult<T>> {
    const transactionOptions: TransactionOptions = {
      mode: 'IMMEDIATE', // Use IMMEDIATE mode for sync operations to prevent conflicts
      enableLogging: true,
      timeout: 60000, // 60 second timeout for sync operations
      ...options
    };
    
    return transactionManager.executeInTransaction(
      async (db: any, transactionId: string) => {
        const context: SyncTransactionContext = {
          transactionId,
          organizationId,
          userId,
          enableLogging: transactionOptions.enableLogging || true
        };
        
        console.log(`[TransactionAwareSync] Starting sync operation in transaction ${transactionId}`);
        
        try {
          const result = await operation(context);
          console.log(`[TransactionAwareSync] Sync operation completed successfully in transaction ${transactionId}`);
          return result;
        } catch (error) {
          console.error(`[TransactionAwareSync] Sync operation failed in transaction ${transactionId}:`, error);
          throw error;
        }
      },
      transactionOptions
    );
  }
  
  /**
   * Process outbox items within a transaction
   */
  async processOutboxInTransaction(
    outboxItems: SyncOutboxItem[],
    organizationId: string,
    userId: string,
    processor: (item: SyncOutboxItem, context: SyncTransactionContext) => Promise<void>
  ): Promise<SyncOperationResult> {
    if (outboxItems.length === 0) {
      return {
        success: true,
        itemsProcessed: 0,
        errors: [],
        transactionId: 'no-transaction',
        duration: 0
      };
    }
    
    const result = await this.executeSyncInTransaction(
      async (context: SyncTransactionContext) => {
        const errors: string[] = [];
        let processedCount = 0;
        
        console.log(`[TransactionAwareSync] Processing ${outboxItems.length} outbox items in transaction ${context.transactionId}`);
        
        // Process items sequentially within the transaction
        // For simplicity, we'll process all items in one transaction without nested savepoints
        for (const item of outboxItems) {
          try {
            await processor(item, context);
            
            // Mark as synced only after successful processing
            syncOutboxService.markAsSynced(item.id);
            processedCount++;
            
            console.log(`[TransactionAwareSync] Successfully processed ${item.table} ${item.action} (${item.id})`);
            
          } catch (itemError) {
            const errorMessage = itemError instanceof Error ? itemError.message : String(itemError);
            console.error(`[TransactionAwareSync] Failed to process outbox item ${item.id}:`, itemError);
            
            errors.push(`Failed to sync ${item.table} ${item.action}: ${errorMessage}`);
            
            // Mark as failed in outbox
            syncOutboxService.markAsFailed(item.id, errorMessage);
            
            // Continue processing other items even if one fails
            // The transaction will still be committed for successful items
          }
        }
        
        return {
          processedCount,
          errors
        };
      },
      organizationId,
      userId,
      {
        mode: 'IMMEDIATE',
        enableLogging: true,
        timeout: 120000 // 2 minute timeout for outbox processing
      }
    );
    
    return {
      success: result.success && (result.result?.errors.length || 0) === 0,
      itemsProcessed: result.result?.processedCount || 0,
      errors: result.result?.errors || (result.error ? [result.error.message] : []),
      transactionId: result.transactionId,
      duration: result.duration
    };
  }
  
  /**
   * Execute database operations with automatic rollback on failure
   */
  async executeWithRollback<T>(
    operations: Array<{
      name: string;
      operation: (context: SyncTransactionContext) => Promise<T>;
      rollback?: (context: SyncTransactionContext) => Promise<void>;
    }>,
    organizationId: string,
    userId: string
  ): Promise<TransactionResult<T[]>> {
    return this.executeSyncInTransaction(
      async (context: SyncTransactionContext) => {
        const results: T[] = [];
        const completedOperations: Array<{
          name: string;
          rollback?: (context: SyncTransactionContext) => Promise<void>;
        }> = [];
        
        try {
          for (const { name, operation, rollback } of operations) {
            console.log(`[TransactionAwareSync] Executing operation: ${name}`);
            
            // Create savepoint before each operation
            const savepointName = await transactionManager.createSavepoint(
              null,
              context.transactionId,
              `op_${name.replace(/\s+/g, '_').toLowerCase()}`
            );
            
            try {
              const result = await operation(context);
              results.push(result);
              completedOperations.push({ name, rollback });
              
              // Release savepoint on success
              await transactionManager.releaseSavepoint(
                null,
                context.transactionId,
                savepointName
              );
              
              console.log(`[TransactionAwareSync] Operation ${name} completed successfully`);
              
            } catch (operationError) {
              console.error(`[TransactionAwareSync] Operation ${name} failed:`, operationError);
              
              // Rollback to savepoint
              await transactionManager.rollbackToSavepoint(
                null,
                context.transactionId,
                savepointName
              );
              
              throw operationError;
            }
          }
          
          return results;
          
        } catch (error) {
          console.error(`[TransactionAwareSync] Transaction failed, executing rollback operations:`, error);
          
          // Execute custom rollback operations in reverse order
          for (let i = completedOperations.length - 1; i >= 0; i--) {
            const { name, rollback } = completedOperations[i];
            if (rollback) {
              try {
                console.log(`[TransactionAwareSync] Executing rollback for: ${name}`);
                await rollback(context);
              } catch (rollbackError) {
                console.error(`[TransactionAwareSync] Rollback failed for ${name}:`, rollbackError);
              }
            }
          }
          
          throw error;
        }
      },
      organizationId,
      userId,
      {
        mode: 'IMMEDIATE',
        enableLogging: true,
        timeout: 180000 // 3 minute timeout for complex operations
      }
    );
  }
  
  /**
   * Get transaction logs for debugging
   */
  getTransactionLogs(transactionId: string) {
    return transactionManager.getTransactionLogs(transactionId);
  }
  
  /**
   * Get transaction manager statistics
   */
  getStats() {
    return transactionManager.getStats();
  }
}

/**
 * Export singleton instance
 */
export const transactionAwareSyncManager = TransactionAwareSyncManager.getInstance();