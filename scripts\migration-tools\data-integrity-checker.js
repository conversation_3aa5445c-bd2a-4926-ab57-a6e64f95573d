/**
 * @file data-integrity-checker.js
 * @description Comprehensive data integrity checker for ChromaSync SQLite database
 * 
 * This tool validates:
 * - Foreign key relationships consistency
 * - Orphaned records detection
 * - UUID format validation for external_id fields
 * - Duplicate external_ids within tables
 * - Organization_id consistency across tables
 * - Missing required fields
 * - Data type consistency
 * - Generates detailed integrity report
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4, validate: isValidUUID } = require('uuid');

function getUserDataPath() {
  return path.join(require('os').homedir(), 'Library', 'Application Support', 'chroma-sync');
}

function getDbPath() {
  return path.join(getUserDataPath(), 'chromasync.db');
}

class DataIntegrityChecker {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.db = null;
    this.integrityReport = {
      timestamp: new Date().toISOString(),
      database: dbPath,
      checks: {
        foreignKeys: [],
        orphanedRecords: [],
        uuidValidation: [],
        duplicateExternalIds: [],
        organizationConsistency: [],
        missingRequiredFields: [],
        dataTypeConsistency: [],
        customChecks: []
      },
      summary: {
        totalIssues: 0,
        criticalIssues: 0,
        warningIssues: 0,
        passedChecks: 0,
        failedChecks: 0
      }
    };
  }

  connect() {
    try {
      this.db = new Database(this.dbPath, { readonly: true });
      console.log(`✅ Connected to database: ${this.dbPath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to connect to database: ${error.message}`);
      return false;
    }
  }

  disconnect() {
    if (this.db && this.db.open) {
      this.db.close();
      console.log('📴 Database connection closed');
    }
  }

  /**
   * Get all table names in the database
   */
  getTables() {
    const query = `
      SELECT name 
      FROM sqlite_master 
      WHERE type='table' 
      AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `;
    
    return this.db.prepare(query).all().map(row => row.name);
  }

  /**
   * Get detailed schema information for a table
   */
  getTableSchema(tableName) {
    const query = `PRAGMA table_info(${tableName})`;
    const columns = this.db.prepare(query).all();
    
    return {
      name: tableName,
      columns: columns.map(col => ({
        name: col.name,
        type: col.type,
        nullable: !col.notnull,
        defaultValue: col.dflt_value,
        primaryKey: !!col.pk
      }))
    };
  }

  /**
   * Get foreign key relationships for a table
   */
  getForeignKeys(tableName) {
    const query = `PRAGMA foreign_key_list(${tableName})`;
    return this.db.prepare(query).all().map(fk => ({
      from: fk.from,
      table: fk.table,
      to: fk.to,
      onUpdate: fk.on_update,
      onDelete: fk.on_delete
    }));
  }

  /**
   * Add issue to report
   */
  addIssue(category, issue) {
    this.integrityReport.checks[category].push({
      ...issue,
      timestamp: new Date().toISOString()
    });
    
    this.integrityReport.summary.totalIssues++;
    
    if (issue.severity === 'critical') {
      this.integrityReport.summary.criticalIssues++;
    } else if (issue.severity === 'warning') {
      this.integrityReport.summary.warningIssues++;
    }
  }

  /**
   * Check foreign key relationships consistency
   */
  checkForeignKeyIntegrity() {
    console.log('🔍 Checking foreign key relationships...');
    
    const tables = this.getTables();
    let checksPerformed = 0;
    let issuesFound = 0;
    
    tables.forEach(tableName => {
      const foreignKeys = this.getForeignKeys(tableName);
      
      foreignKeys.forEach(fk => {
        checksPerformed++;
        
        try {
          // Check if foreign key values exist in referenced table
          const orphanedQuery = `
            SELECT COUNT(*) as count
            FROM ${tableName} child
            LEFT JOIN ${fk.table} parent ON child.${fk.from} = parent.${fk.to}
            WHERE child.${fk.from} IS NOT NULL 
            AND parent.${fk.to} IS NULL
          `;
          
          const result = this.db.prepare(orphanedQuery).get();
          
          if (result.count > 0) {
            issuesFound++;
            this.addIssue('foreignKeys', {
              table: tableName,
              foreignKey: fk.from,
              referencedTable: fk.table,
              referencedColumn: fk.to,
              orphanedCount: result.count,
              severity: 'critical',
              description: `${result.count} records in ${tableName}.${fk.from} reference non-existent records in ${fk.table}.${fk.to}`,
              query: orphanedQuery
            });
          }
        } catch (error) {
          issuesFound++;
          this.addIssue('foreignKeys', {
            table: tableName,
            foreignKey: fk.from,
            referencedTable: fk.table,
            referencedColumn: fk.to,
            severity: 'critical',
            description: `Error checking foreign key constraint: ${error.message}`,
            error: error.message
          });
        }
      });
    });
    
    console.log(`   ✅ Checked ${checksPerformed} foreign key relationships`);
    console.log(`   ${issuesFound === 0 ? '✅' : '❌'} Found ${issuesFound} foreign key issues`);
    
    if (issuesFound === 0) {
      this.integrityReport.summary.passedChecks++;
    } else {
      this.integrityReport.summary.failedChecks++;
    }
  }

  /**
   * Check for orphaned records that should be cascade deleted
   */
  checkOrphanedRecords() {
    console.log('🔍 Checking for orphaned records...');
    
    const orphanedChecks = [
      {
        table: 'product_colors',
        parentTable: 'products',
        parentKey: 'id',
        foreignKey: 'product_id',
        description: 'Product-color relationships without valid products'
      },
      {
        table: 'product_colors',
        parentTable: 'colors',
        parentKey: 'id',
        foreignKey: 'color_id',
        description: 'Product-color relationships without valid colors'
      },
      {
        table: 'organization_members',
        parentTable: 'organizations',
        parentKey: 'id',
        foreignKey: 'organization_id',
        description: 'Organization memberships without valid organizations'
      },
      {
        table: 'organization_invitations',
        parentTable: 'organizations',
        parentKey: 'id',
        foreignKey: 'organization_id',
        description: 'Organization invitations without valid organizations'
      }
    ];
    
    let checksPerformed = 0;
    let issuesFound = 0;
    
    orphanedChecks.forEach(check => {
      checksPerformed++;
      
      try {
        const query = `
          SELECT COUNT(*) as count
          FROM ${check.table} child
          LEFT JOIN ${check.parentTable} parent ON child.${check.foreignKey} = parent.${check.parentKey}
          WHERE parent.${check.parentKey} IS NULL
        `;
        
        const result = this.db.prepare(query).get();
        
        if (result.count > 0) {
          issuesFound++;
          this.addIssue('orphanedRecords', {
            table: check.table,
            parentTable: check.parentTable,
            foreignKey: check.foreignKey,
            parentKey: check.parentKey,
            orphanedCount: result.count,
            severity: 'critical',
            description: `${check.description}: ${result.count} orphaned records`,
            query: query
          });
        }
      } catch (error) {
        issuesFound++;
        this.addIssue('orphanedRecords', {
          table: check.table,
          parentTable: check.parentTable,
          severity: 'critical',
          description: `Error checking orphaned records: ${error.message}`,
          error: error.message
        });
      }
    });
    
    console.log(`   ✅ Checked ${checksPerformed} orphaned record scenarios`);
    console.log(`   ${issuesFound === 0 ? '✅' : '❌'} Found ${issuesFound} orphaned record issues`);
    
    if (issuesFound === 0) {
      this.integrityReport.summary.passedChecks++;
    } else {
      this.integrityReport.summary.failedChecks++;
    }
  }

  /**
   * Validate UUID format for external_id fields
   */
  checkUUIDValidation() {
    console.log('🔍 Checking UUID format validation...');
    
    const uuidTables = [
      { table: 'organizations', field: 'external_id' },
      { table: 'products', field: 'external_id' },
      { table: 'colors', field: 'external_id' },
      { table: 'organization_invitations', field: 'external_id' }
    ];
    
    let checksPerformed = 0;
    let issuesFound = 0;
    
    uuidTables.forEach(({ table, field }) => {
      checksPerformed++;
      
      try {
        const query = `SELECT ${field} FROM ${table} WHERE ${field} IS NOT NULL`;
        const results = this.db.prepare(query).all();
        
        let invalidUUIDs = [];
        results.forEach(row => {
          const value = row[field];
          if (value && !isValidUUID(value)) {
            invalidUUIDs.push(value);
          }
        });
        
        if (invalidUUIDs.length > 0) {
          issuesFound++;
          this.addIssue('uuidValidation', {
            table: table,
            field: field,
            invalidCount: invalidUUIDs.length,
            totalCount: results.length,
            severity: 'critical',
            description: `${invalidUUIDs.length} invalid UUIDs found in ${table}.${field}`,
            samples: invalidUUIDs.slice(0, 5), // Show first 5 invalid UUIDs
            query: query
          });
        }
      } catch (error) {
        issuesFound++;
        this.addIssue('uuidValidation', {
          table: table,
          field: field,
          severity: 'critical',
          description: `Error validating UUIDs: ${error.message}`,
          error: error.message
        });
      }
    });
    
    console.log(`   ✅ Checked ${checksPerformed} UUID fields`);
    console.log(`   ${issuesFound === 0 ? '✅' : '❌'} Found ${issuesFound} UUID validation issues`);
    
    if (issuesFound === 0) {
      this.integrityReport.summary.passedChecks++;
    } else {
      this.integrityReport.summary.failedChecks++;
    }
  }

  /**
   * Check for duplicate external_ids within tables
   */
  checkDuplicateExternalIds() {
    console.log('🔍 Checking for duplicate external_ids...');
    
    const uuidTables = [
      'organizations',
      'products', 
      'colors',
      'organization_invitations'
    ];
    
    let checksPerformed = 0;
    let issuesFound = 0;
    
    uuidTables.forEach(table => {
      checksPerformed++;
      
      try {
        const query = `
          SELECT external_id, COUNT(*) as duplicate_count
          FROM ${table}
          WHERE external_id IS NOT NULL
          GROUP BY external_id
          HAVING COUNT(*) > 1
        `;
        
        const results = this.db.prepare(query).all();
        
        if (results.length > 0) {
          issuesFound++;
          const totalDuplicates = results.reduce((sum, row) => sum + row.duplicate_count, 0);
          
          this.addIssue('duplicateExternalIds', {
            table: table,
            duplicateGroups: results.length,
            totalDuplicateRecords: totalDuplicates,
            severity: 'critical',
            description: `${results.length} duplicate external_id groups found in ${table} affecting ${totalDuplicates} records`,
            duplicates: results.slice(0, 10), // Show first 10 duplicate groups
            query: query
          });
        }
      } catch (error) {
        issuesFound++;
        this.addIssue('duplicateExternalIds', {
          table: table,
          severity: 'critical',
          description: `Error checking duplicate external_ids: ${error.message}`,
          error: error.message
        });
      }
    });
    
    console.log(`   ✅ Checked ${checksPerformed} tables for duplicate external_ids`);
    console.log(`   ${issuesFound === 0 ? '✅' : '❌'} Found ${issuesFound} duplicate external_id issues`);
    
    if (issuesFound === 0) {
      this.integrityReport.summary.passedChecks++;
    } else {
      this.integrityReport.summary.failedChecks++;
    }
  }

  /**
   * Check organization_id consistency across tables
   */
  checkOrganizationConsistency() {
    console.log('🔍 Checking organization_id consistency...');
    
    const orgTables = [
      'products',
      'colors',
      'product_colors',
      'organization_members'
    ];
    
    let checksPerformed = 0;
    let issuesFound = 0;
    
    // Check if organization_id values exist in organizations table
    orgTables.forEach(table => {
      checksPerformed++;
      
      try {
        const query = `
          SELECT COUNT(*) as count
          FROM ${table} child
          LEFT JOIN organizations org ON child.organization_id = org.id
          WHERE child.organization_id IS NOT NULL 
          AND org.id IS NULL
        `;
        
        const result = this.db.prepare(query).get();
        
        if (result.count > 0) {
          issuesFound++;
          this.addIssue('organizationConsistency', {
            table: table,
            invalidOrgIdCount: result.count,
            severity: 'critical',
            description: `${result.count} records in ${table} reference non-existent organizations`,
            query: query
          });
        }
      } catch (error) {
        issuesFound++;
        this.addIssue('organizationConsistency', {
          table: table,
          severity: 'critical',
          description: `Error checking organization consistency: ${error.message}`,
          error: error.message
        });
      }
    });
    
    // Check for records without organization_id where required
    const requiredOrgTables = ['products', 'colors', 'product_colors'];
    requiredOrgTables.forEach(table => {
      checksPerformed++;
      
      try {
        const query = `SELECT COUNT(*) as count FROM ${table} WHERE organization_id IS NULL`;
        const result = this.db.prepare(query).get();
        
        if (result.count > 0) {
          issuesFound++;
          this.addIssue('organizationConsistency', {
            table: table,
            missingOrgIdCount: result.count,
            severity: 'critical',
            description: `${result.count} records in ${table} are missing required organization_id`,
            query: query
          });
        }
      } catch (error) {
        issuesFound++;
        this.addIssue('organizationConsistency', {
          table: table,
          severity: 'critical',
          description: `Error checking required organization_id: ${error.message}`,
          error: error.message
        });
      }
    });
    
    console.log(`   ✅ Checked ${checksPerformed} organization consistency scenarios`);
    console.log(`   ${issuesFound === 0 ? '✅' : '❌'} Found ${issuesFound} organization consistency issues`);
    
    if (issuesFound === 0) {
      this.integrityReport.summary.passedChecks++;
    } else {
      this.integrityReport.summary.failedChecks++;
    }
  }

  /**
   * Check for missing required fields
   */
  checkMissingRequiredFields() {
    console.log('🔍 Checking for missing required fields...');
    
    const requiredFieldChecks = [
      { table: 'organizations', field: 'name', description: 'Organization name is required' },
      { table: 'organizations', field: 'slug', description: 'Organization slug is required' },
      { table: 'products', field: 'name', description: 'Product name is required' },
      { table: 'colors', field: 'code', description: 'Color code is required' },
      { table: 'colors', field: 'hex', description: 'Color hex value is required' },
      { table: 'organization_members', field: 'user_id', description: 'Member user_id is required' },
      { table: 'organization_members', field: 'role', description: 'Member role is required' }
    ];
    
    let checksPerformed = 0;
    let issuesFound = 0;
    
    requiredFieldChecks.forEach(check => {
      checksPerformed++;
      
      try {
        const query = `
          SELECT COUNT(*) as count 
          FROM ${check.table} 
          WHERE ${check.field} IS NULL OR trim(${check.field}) = ''
        `;
        
        const result = this.db.prepare(query).get();
        
        if (result.count > 0) {
          issuesFound++;
          this.addIssue('missingRequiredFields', {
            table: check.table,
            field: check.field,
            missingCount: result.count,
            severity: 'critical',
            description: `${check.description}: ${result.count} records missing ${check.field}`,
            query: query
          });
        }
      } catch (error) {
        issuesFound++;
        this.addIssue('missingRequiredFields', {
          table: check.table,
          field: check.field,
          severity: 'critical',
          description: `Error checking required field: ${error.message}`,
          error: error.message
        });
      }
    });
    
    console.log(`   ✅ Checked ${checksPerformed} required fields`);
    console.log(`   ${issuesFound === 0 ? '✅' : '❌'} Found ${issuesFound} missing required field issues`);
    
    if (issuesFound === 0) {
      this.integrityReport.summary.passedChecks++;
    } else {
      this.integrityReport.summary.failedChecks++;
    }
  }

  /**
   * Check data type consistency
   */
  checkDataTypeConsistency() {
    console.log('🔍 Checking data type consistency...');
    
    const dataTypeChecks = [
      {
        table: 'colors',
        field: 'hex',
        rule: 'hex_format',
        description: 'Color hex must be valid 7-character hex format (#RRGGBB)',
        query: `SELECT COUNT(*) as count FROM colors WHERE hex IS NOT NULL AND (length(hex) != 7 OR hex NOT GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]')`
      },
      {
        table: 'colors',
        field: 'is_gradient',
        rule: 'boolean_format',
        description: 'is_gradient must be boolean (0 or 1)',
        query: `SELECT COUNT(*) as count FROM colors WHERE is_gradient IS NOT NULL AND is_gradient NOT IN (0, 1)`
      },
      {
        table: 'colors',
        field: 'is_library',
        rule: 'boolean_format',
        description: 'is_library must be boolean (0 or 1)',
        query: `SELECT COUNT(*) as count FROM colors WHERE is_library IS NOT NULL AND is_library NOT IN (0, 1)`
      },
      {
        table: 'products',
        field: 'is_active',
        rule: 'boolean_format',
        description: 'is_active must be boolean (0 or 1)',
        query: `SELECT COUNT(*) as count FROM products WHERE is_active IS NOT NULL AND is_active NOT IN (0, 1)`
      },
      {
        table: 'organization_members',
        field: 'role',
        rule: 'enum_format',
        description: 'role must be one of: owner, admin, member',
        query: `SELECT COUNT(*) as count FROM organization_members WHERE role NOT IN ('owner', 'admin', 'member')`
      },
      {
        table: 'organizations',
        field: 'plan',
        rule: 'enum_format',
        description: 'plan must be one of: free, team, enterprise',
        query: `SELECT COUNT(*) as count FROM organizations WHERE plan IS NOT NULL AND plan NOT IN ('free', 'team', 'enterprise')`
      }
    ];
    
    let checksPerformed = 0;
    let issuesFound = 0;
    
    dataTypeChecks.forEach(check => {
      checksPerformed++;
      
      try {
        const result = this.db.prepare(check.query).get();
        
        if (result.count > 0) {
          issuesFound++;
          this.addIssue('dataTypeConsistency', {
            table: check.table,
            field: check.field,
            rule: check.rule,
            invalidCount: result.count,
            severity: 'warning',
            description: `${check.description}: ${result.count} invalid values`,
            query: check.query
          });
        }
      } catch (error) {
        issuesFound++;
        this.addIssue('dataTypeConsistency', {
          table: check.table,
          field: check.field,
          rule: check.rule,
          severity: 'critical',
          description: `Error checking data type consistency: ${error.message}`,
          error: error.message
        });
      }
    });
    
    console.log(`   ✅ Checked ${checksPerformed} data type consistency rules`);
    console.log(`   ${issuesFound === 0 ? '✅' : '❌'} Found ${issuesFound} data type consistency issues`);
    
    if (issuesFound === 0) {
      this.integrityReport.summary.passedChecks++;
    } else {
      this.integrityReport.summary.failedChecks++;
    }
  }

  /**
   * Perform custom ChromaSync-specific checks
   */
  checkCustomIntegrity() {
    console.log('🔍 Performing custom ChromaSync integrity checks...');
    
    let checksPerformed = 0;
    let issuesFound = 0;
    
    // Check 1: Products without colors
    checksPerformed++;
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM products p
        LEFT JOIN product_colors pc ON p.id = pc.product_id
        WHERE pc.product_id IS NULL
        AND p.deleted_at IS NULL
      `;
      
      const result = this.db.prepare(query).get();
      
      if (result.count > 0) {
        this.addIssue('customChecks', {
          checkName: 'products_without_colors',
          affectedCount: result.count,
          severity: 'warning',
          description: `${result.count} active products have no associated colors`,
          query: query,
          recommendation: 'Consider reviewing these products or adding default colors'
        });
      }
    } catch (error) {
      issuesFound++;
      this.addIssue('customChecks', {
        checkName: 'products_without_colors',
        severity: 'critical',
        description: `Error checking products without colors: ${error.message}`,
        error: error.message
      });
    }
    
    // Check 2: Colors without products
    checksPerformed++;
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM colors c
        LEFT JOIN product_colors pc ON c.id = pc.color_id
        WHERE pc.color_id IS NULL
        AND c.deleted_at IS NULL
        AND c.is_library = 0
      `;
      
      const result = this.db.prepare(query).get();
      
      if (result.count > 0) {
        this.addIssue('customChecks', {
          checkName: 'colors_without_products',
          affectedCount: result.count,
          severity: 'warning',
          description: `${result.count} non-library colors are not associated with any products`,
          query: query,
          recommendation: 'Consider cleaning up unused colors or converting to library colors'
        });
      }
    } catch (error) {
      issuesFound++;
      this.addIssue('customChecks', {
        checkName: 'colors_without_products',
        severity: 'critical',
        description: `Error checking colors without products: ${error.message}`,
        error: error.message
      });
    }
    
    // Check 3: Organizations without members
    checksPerformed++;
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM organizations o
        LEFT JOIN organization_members om ON o.id = om.organization_id
        WHERE om.organization_id IS NULL
      `;
      
      const result = this.db.prepare(query).get();
      
      if (result.count > 0) {
        this.addIssue('customChecks', {
          checkName: 'organizations_without_members',
          affectedCount: result.count,
          severity: 'warning',
          description: `${result.count} organizations have no members`,
          query: query,
          recommendation: 'Consider adding owners to these organizations or removing unused organizations'
        });
      }
    } catch (error) {
      issuesFound++;
      this.addIssue('customChecks', {
        checkName: 'organizations_without_members',
        severity: 'critical',
        description: `Error checking organizations without members: ${error.message}`,
        error: error.message
      });
    }
    
    // Check 4: Organizations without owners
    checksPerformed++;
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM organizations o
        LEFT JOIN organization_members om ON o.id = om.organization_id AND om.role = 'owner'
        WHERE om.organization_id IS NULL
      `;
      
      const result = this.db.prepare(query).get();
      
      if (result.count > 0) {
        issuesFound++;
        this.addIssue('customChecks', {
          checkName: 'organizations_without_owners',
          affectedCount: result.count,
          severity: 'critical',
          description: `${result.count} organizations have no owners`,
          query: query,
          recommendation: 'Every organization must have at least one owner'
        });
      }
    } catch (error) {
      issuesFound++;
      this.addIssue('customChecks', {
        checkName: 'organizations_without_owners',
        severity: 'critical',
        description: `Error checking organizations without owners: ${error.message}`,
        error: error.message
      });
    }
    
    // Check 5: Duplicate organization slugs
    checksPerformed++;
    try {
      const query = `
        SELECT slug, COUNT(*) as duplicate_count
        FROM organizations
        WHERE slug IS NOT NULL
        GROUP BY slug
        HAVING COUNT(*) > 1
      `;
      
      const results = this.db.prepare(query).all();
      
      if (results.length > 0) {
        issuesFound++;
        this.addIssue('customChecks', {
          checkName: 'duplicate_organization_slugs',
          affectedGroups: results.length,
          severity: 'critical',
          description: `${results.length} duplicate organization slugs found`,
          duplicates: results,
          query: query,
          recommendation: 'Organization slugs must be unique'
        });
      }
    } catch (error) {
      issuesFound++;
      this.addIssue('customChecks', {
        checkName: 'duplicate_organization_slugs',
        severity: 'critical',
        description: `Error checking duplicate organization slugs: ${error.message}`,
        error: error.message
      });
    }
    
    console.log(`   ✅ Performed ${checksPerformed} custom integrity checks`);
    console.log(`   ${issuesFound === 0 ? '✅' : '❌'} Found ${issuesFound} custom integrity issues`);
    
    if (issuesFound === 0) {
      this.integrityReport.summary.passedChecks++;
    } else {
      this.integrityReport.summary.failedChecks++;
    }
  }

  /**
   * Generate comprehensive integrity report
   */
  generateReport() {
    console.log('\n🔍 ChromaSync Data Integrity Check');
    console.log('==================================\n');
    
    const startTime = Date.now();
    
    // Basic database info
    const tables = this.getTables();
    console.log(`📊 Database: ${this.dbPath}`);
    console.log(`📋 Total Tables: ${tables.length}`);
    
    // Get row counts
    const rowCounts = {};
    tables.forEach(tableName => {
      try {
        const count = this.db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get();
        rowCounts[tableName] = count.count;
      } catch (error) {
        rowCounts[tableName] = `Error: ${error.message}`;
      }
    });
    
    console.log('\n📈 Table Row Counts:');
    Object.entries(rowCounts).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} rows`);
    });
    
    console.log('\n🔍 Running Integrity Checks...\n');
    
    // Run all integrity checks
    this.checkForeignKeyIntegrity();
    this.checkOrphanedRecords();
    this.checkUUIDValidation();
    this.checkDuplicateExternalIds();
    this.checkOrganizationConsistency();
    this.checkMissingRequiredFields();
    this.checkDataTypeConsistency();
    this.checkCustomIntegrity();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Update report metadata
    this.integrityReport.duration = duration;
    this.integrityReport.rowCounts = rowCounts;
    this.integrityReport.tableCount = tables.length;
    
    // Display summary
    console.log('\n📋 Integrity Check Summary:');
    console.log('============================');
    console.log(`⏱️  Duration: ${duration}ms`);
    console.log(`✅ Passed Checks: ${this.integrityReport.summary.passedChecks}`);
    console.log(`❌ Failed Checks: ${this.integrityReport.summary.failedChecks}`);
    console.log(`🔴 Critical Issues: ${this.integrityReport.summary.criticalIssues}`);
    console.log(`🟡 Warning Issues: ${this.integrityReport.summary.warningIssues}`);
    console.log(`📊 Total Issues: ${this.integrityReport.summary.totalIssues}`);
    
    // Display critical issues
    if (this.integrityReport.summary.criticalIssues > 0) {
      console.log('\n🔴 Critical Issues Found:');
      console.log('=========================');
      
      Object.entries(this.integrityReport.checks).forEach(([category, issues]) => {
        const criticalIssues = issues.filter(issue => issue.severity === 'critical');
        if (criticalIssues.length > 0) {
          console.log(`\n${category.toUpperCase()}:`);
          criticalIssues.forEach((issue, index) => {
            console.log(`  ${index + 1}. ${issue.description}`);
            if (issue.table) console.log(`     Table: ${issue.table}`);
            if (issue.query) console.log(`     Query: ${issue.query}`);
          });
        }
      });
    }
    
    // Overall status
    const overallStatus = this.integrityReport.summary.criticalIssues === 0 ? 'HEALTHY' : 'NEEDS_ATTENTION';
    console.log(`\n🎯 Overall Database Status: ${overallStatus}`);
    
    if (overallStatus === 'NEEDS_ATTENTION') {
      console.log('⚠️  Action Required: Please review and fix critical issues before proceeding with migrations or production use.');
    } else {
      console.log('✅ Database integrity is good! Safe to proceed with migrations.');
    }
    
    return this.integrityReport;
  }

  /**
   * Save integrity report to JSON file
   */
  saveReport(outputPath) {
    try {
      fs.writeFileSync(outputPath, JSON.stringify(this.integrityReport, null, 2));
      console.log(`\n💾 Integrity report saved to: ${outputPath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to save report: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate SQL fix suggestions for found issues
   */
  generateFixSuggestions() {
    const suggestions = [];
    
    // Generate fix suggestions for each category
    Object.entries(this.integrityReport.checks).forEach(([category, issues]) => {
      issues.forEach(issue => {
        if (issue.severity === 'critical') {
          switch (category) {
            case 'uuidValidation':
              suggestions.push({
                issue: issue.description,
                category: category,
                fixType: 'FIX_INVALID_UUIDS',
                sql: `-- Fix: ${issue.description}
-- WARNING: This will replace invalid UUIDs with new ones
-- Review the affected records first:
SELECT ${issue.field} FROM ${issue.table} WHERE ${issue.field} IS NOT NULL;

-- Generate new UUIDs for invalid entries:
UPDATE ${issue.table} 
SET ${issue.field} = lower(hex(randomblob(4))) || '-' || 
                     lower(hex(randomblob(2))) || '-' || 
                     '4' || substr(lower(hex(randomblob(2))), 2) || '-' || 
                     substr('89ab', abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))), 2) || '-' || 
                     lower(hex(randomblob(6)))
WHERE ${issue.field} IS NOT NULL 
AND length(${issue.field}) != 36;`
              });
              break;
              
            case 'foreignKeys':
              suggestions.push({
                issue: issue.description,
                category: category,
                fixType: 'DELETE_ORPHANED',
                sql: `-- Fix: ${issue.description}
DELETE FROM ${issue.table} 
WHERE ${issue.foreignKey} NOT IN (
  SELECT ${issue.referencedColumn} FROM ${issue.referencedTable}
);`
              });
              break;
              
            case 'duplicateExternalIds':
              suggestions.push({
                issue: issue.description,
                category: category,
                fixType: 'UPDATE_DUPLICATES',
                sql: `-- Fix: ${issue.description}
-- WARNING: This will update duplicate external_ids with new UUIDs
-- Review the affected records before running this query
UPDATE ${issue.table} 
SET external_id = lower(hex(randomblob(16))) 
WHERE rowid NOT IN (
  SELECT MIN(rowid) 
  FROM ${issue.table} 
  GROUP BY external_id
);`
              });
              break;
              
            case 'organizationConsistency':
              if (issue.invalidOrgIdCount) {
                suggestions.push({
                  issue: issue.description,
                  category: category,
                  fixType: 'FIX_ORGANIZATION_REFERENCES',
                  sql: `-- Fix: ${issue.description}
-- Option 1: Find the correct organization_id and update
-- First, identify which organization these records should belong to:
SELECT DISTINCT organization_id, COUNT(*) as count 
FROM ${issue.table} 
WHERE organization_id NOT IN (SELECT id FROM organizations)
GROUP BY organization_id;

-- Option 2: If these records should belong to the default organization (id=1):
-- UPDATE ${issue.table} SET organization_id = 1 
-- WHERE organization_id NOT IN (SELECT id FROM organizations);

-- Option 3: Delete orphaned records (use with caution):
-- DELETE FROM ${issue.table} 
-- WHERE organization_id NOT IN (SELECT id FROM organizations);`
                });
              }
              break;
              
            case 'missingRequiredFields':
              suggestions.push({
                issue: issue.description,
                category: category,
                fixType: 'SET_DEFAULT_VALUES',
                sql: `-- Fix: ${issue.description}
-- WARNING: Review these records before applying default values
SELECT * FROM ${issue.table} WHERE ${issue.field} IS NULL OR trim(${issue.field}) = '';
-- Then apply appropriate fixes based on your business logic`
              });
              break;
          }
        }
      });
    });
    
    return suggestions;
  }
}

// CLI usage
if (require.main === module) {
  const dbPath = getDbPath();
  const checker = new DataIntegrityChecker(dbPath);
  
  if (checker.connect()) {
    try {
      const report = checker.generateReport();
      
      // Save report to file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const outputPath = path.join(__dirname, `integrity-report-${timestamp}.json`);
      checker.saveReport(outputPath);
      
      // Generate fix suggestions
      const fixSuggestions = checker.generateFixSuggestions();
      if (fixSuggestions.length > 0) {
        const fixPath = path.join(__dirname, `integrity-fixes-${timestamp}.sql`);
        const fixContent = fixSuggestions.map(fix => fix.sql).join('\n\n');
        fs.writeFileSync(fixPath, fixContent);
        console.log(`🔧 Fix suggestions saved to: ${fixPath}`);
      }
      
      console.log('\n✅ Data integrity check complete!');
      
      // Exit with appropriate code
      process.exit(report.summary.criticalIssues > 0 ? 1 : 0);
      
    } catch (error) {
      console.error(`❌ Integrity check failed: ${error.message}`);
      console.error(error.stack);
      process.exit(1);
    } finally {
      checker.disconnect();
    }
  } else {
    process.exit(1);
  }
}

module.exports = { DataIntegrityChecker };