-- Migration 018: Complete Cloud Schema Compatibility
-- Adds remaining columns needed for full Supabase cloud schema compatibility
-- This completes the denormalization migration started in 017

-- Add missing cloud-compatible columns
-- Note: SQLite doesn't support IF NOT EXISTS for columns, so these may error if they already exist
-- The migration runner handles these gracefully

-- Add gradient_colors column (stores comma-separated hex values for gradients)
ALTER TABLE colors ADD COLUMN gradient_colors TEXT;

-- Add notes column (user notes about colors)
ALTER TABLE colors ADD COLUMN notes TEXT;

-- Add tags column (user-defined tags)
ALTER TABLE colors ADD COLUMN tags TEXT;

-- Add is_library column (marks library/system colors vs user colors)
ALTER TABLE colors ADD COLUMN is_library BOOLEAN NOT NULL DEFAULT FALSE;

-- Data Migration: Populate new columns from existing data

-- Step 1: Populate gradient_colors for existing gradients
-- Extract hex values from gradient stops and join with commas
UPDATE colors SET gradient_colors = (
  SELECT group_concat(
    json_extract(value, '$.color'), 
    ','
  )
  FROM json_each(json_extract(color_spaces, '$.gradient.stops'))
  ORDER BY json_extract(value, '$.position')
) WHERE json_extract(color_spaces, '$.gradient') IS NOT NULL 
  AND json_array_length(json_extract(color_spaces, '$.gradient.stops')) > 0;

-- Step 2: Populate notes from properties JSON
-- Move notes from properties to dedicated column
UPDATE colors SET notes = json_extract(properties, '$.notes') 
WHERE json_extract(properties, '$.notes') IS NOT NULL 
  AND json_extract(properties, '$.notes') != '';

-- Step 3: Set is_library flag for non-user colors
-- Mark library colors (Pantone, RAL, etc.) vs user-created colors
UPDATE colors SET is_library = CASE 
  WHEN source_id = 1 THEN FALSE  -- User colors (source_id = 1)
  ELSE TRUE                      -- Library colors (Pantone, RAL, etc.)
END;

-- Step 4: Populate tags if any exist in properties
UPDATE colors SET tags = json_extract(properties, '$.tags')
WHERE json_extract(properties, '$.tags') IS NOT NULL 
  AND json_extract(properties, '$.tags') != '';

-- Create indexes for the new columns to maintain performance
CREATE INDEX IF NOT EXISTS idx_colors_gradient_colors ON colors(gradient_colors) 
WHERE gradient_colors IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_colors_notes ON colors(notes) 
WHERE notes IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_colors_tags ON colors(tags) 
WHERE tags IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_colors_is_library ON colors(is_library);

-- Performance index for combined queries
CREATE INDEX IF NOT EXISTS idx_colors_library_gradient ON colors(is_library, is_gradient);

-- Data validation: Fix any gradient hex values that might be inconsistent
-- Update main hex field to match first gradient stop color
UPDATE colors SET hex = (
  SELECT json_extract(value, '$.color')
  FROM json_each(json_extract(color_spaces, '$.gradient.stops'))
  ORDER BY json_extract(value, '$.position')
  LIMIT 1
) WHERE is_gradient = 1 
  AND json_extract(color_spaces, '$.gradient') IS NOT NULL
  AND json_array_length(json_extract(color_spaces, '$.gradient.stops')) > 0;

-- Clean up properties JSON by removing data that now has dedicated columns
-- This keeps the schema clean and avoids duplication
UPDATE colors SET properties = json_remove(
  properties,
  '$.notes',
  '$.tags'
) WHERE json_extract(properties, '$.notes') IS NOT NULL 
     OR json_extract(properties, '$.tags') IS NOT NULL;

-- Record migration completion
INSERT OR IGNORE INTO schema_migrations (version, name, applied_at) 
VALUES (18, 'complete_cloud_compatibility', CURRENT_TIMESTAMP);

-- Migration verification queries (for manual checking)
-- SELECT COUNT(*) as total_colors FROM colors;
-- SELECT COUNT(*) as colors_with_gradient_colors FROM colors WHERE gradient_colors IS NOT NULL;
-- SELECT COUNT(*) as library_colors FROM colors WHERE is_library = 1;
-- SELECT COUNT(*) as user_colors FROM colors WHERE is_library = 0;
-- SELECT COUNT(*) as colors_with_notes FROM colors WHERE notes IS NOT NULL;