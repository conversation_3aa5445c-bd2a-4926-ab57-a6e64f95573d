/**
 * @file useAppInitialization.ts
 * @description Custom hook to manage app initialization state machine
 * Focused on the AppInitializer auth/org flow - NOT AppShell startup
 */

import { useEffect, useReducer, useCallback, useState, useMemo } from 'react';
import { useSyncAuth, useSyncStore } from '../store/sync.store';
import { useOrganizationStore } from '../store/organization.store';
import { useColorStore } from '../store/color.store';
import { useStartupManager } from './useStartupManager';
import { usePerformanceMonitoring } from './usePerformanceMonitoring';
import { errorLogger } from '../utils/errorLogger';
import { showAcceptInvitationModal } from '../components/organization/AcceptInvitation';
import StartupCacheService from '../services/startup-cache.service';
import { colorCacheService } from '../services/color-cache.service';

// State machine states
export type InitializationState =
  | 'initializing'
  | 'checking-auth'
  | 'loading-organizations'
  | 'syncing-data'
  | 'needs-auth'
  | 'needs-org-setup'
  | 'needs-org-selection'
  | 'ready'
  | 'error';

// License status interface
interface LicenseStatus {
  isValid: boolean;
  inTrialMode: boolean;
  trialDaysRemaining: number;
}

// Action types for the state machine
type InitializationAction =
  | { type: 'START_INITIALIZATION' }
  | { type: 'AUTH_CHECK_COMPLETE'; isAuthenticated: boolean }
  | { type: 'ORGANIZATIONS_LOADED'; organizations: any[]; currentOrg: any }
  | { type: 'SYNC_COMPLETE' }
  | { type: 'AUTH_SUCCESS' }
  | { type: 'ORG_SETUP_COMPLETE' }
  | { type: 'ORG_SELECTED' }
  | { type: 'ERROR'; error: string }
  | { type: 'RESET' };

interface InitializationContext {
  state: InitializationState;
  error?: string;
}

// State machine reducer
function initializationReducer(
  context: InitializationContext,
  action: InitializationAction
): InitializationContext {
  console.log('[AppInit] State transition:', context.state, '->', action.type);

  switch (action.type) {
    case 'START_INITIALIZATION':
      return { state: 'checking-auth' };

    case 'AUTH_CHECK_COMPLETE':
      return {
        state: action.isAuthenticated ? 'loading-organizations' : 'needs-auth',
      };

    case 'ORGANIZATIONS_LOADED':
      if (action.currentOrg) {
        return { state: 'syncing-data' };
      } else if (action.organizations.length > 1) {
        return { state: 'needs-org-selection' };
      } else if (action.organizations.length === 1) {
        return { state: 'syncing-data' }; // Auto-select single org
      } else {
        return { state: 'needs-org-setup' };
      }

    case 'SYNC_COMPLETE':
      return { state: 'ready' };

    case 'AUTH_SUCCESS':
      return { state: 'loading-organizations' };

    case 'ORG_SETUP_COMPLETE':
    case 'ORG_SELECTED':
      return { state: 'syncing-data' };

    case 'ERROR':
      return { state: 'error', error: action.error };

    case 'RESET':
      return { state: 'initializing' };

    default:
      return context;
  }
}

/**
 * Custom hook that manages the app initialization state machine and AppShell state
 *
 * Returns both state machine properties (for AppInitializer) and AppShell properties
 * to maintain a single source of truth for application startup and shell state.
 */
export function useAppInitialization() {
  const [context, dispatch] = useReducer(initializationReducer, {
    state: 'initializing',
  });

  // Additional AppShell state
  const [licenseStatus, setLicenseStatus] = useState<LicenseStatus>({
    isValid: true,
    inTrialMode: false,
    trialDaysRemaining: 0,
  });
  const [debugPanelOpen, setDebugPanelOpen] = useState(false);

  // Memoized store selectors to prevent unnecessary re-renders
  const { isAuthenticated, user } = useSyncAuth();

  // Break dependency chains by consolidating store access with memoization
  const syncActions = useMemo(
    () => ({
      refreshAuthState: useSyncStore.getState().refreshAuthState,
      syncData: useSyncStore.getState().syncData,
    }),
    []
  );

  const colorActions = useMemo(
    () => ({
      loadColorsWithUsage: useColorStore.getState().loadColorsWithUsage,
    }),
    []
  );

  // Use optimized selectors that only subscribe to specific state slices
  // CRITICAL FIX: Remove useCallback to ensure fresh state subscriptions
  const organizationData = useOrganizationStore(state => {
    console.log('[AppInit] 🔍 Organization selector called:', {
      organizationCount: state.organizations.length,
      currentOrg: state.currentOrganization?.name || 'none',
      isLoading: state.isLoading,
    });
    return {
      currentOrganization: state.currentOrganization,
      organizations: state.organizations,
      loadOrganizationContext: state.loadOrganizationContext,
      isLoading: state.isLoading,
    };
  });

  // Startup management for AppShell compatibility
  const {
    stage,
    progress,
    isReady: startupReady,
    getStartupTime,
  } = useStartupManager();
  const _performanceStats = usePerformanceMonitoring('App');

  // Initialize the app
  useEffect(() => {
    if (context.state === 'initializing') {
      dispatch({ type: 'START_INITIALIZATION' });
    }
  }, [context.state]);

  // Handle auth checking with caching
  useEffect(() => {
    if (context.state === 'checking-auth') {
      const checkAuth = async () => {
        try {
          // Check if we can skip auth check due to valid cache
          if (StartupCacheService.isAuthCacheValid()) {
            console.log(
              '[AppInit] ⚡ Skipping auth check - using cached state'
            );
            dispatch({
              type: 'AUTH_CHECK_COMPLETE',
              isAuthenticated: true, // If cache is valid, we're authenticated
            });
            return;
          }

          console.log('[AppInit] 🔍 Performing auth check (no valid cache)...');
          await syncActions.refreshAuthState();
          const authResponse = await window.syncAPI?.getAuthState();
          const isAuthenticated = !!authResponse?.isAuthenticated;

          // Cache successful auth
          if (isAuthenticated) {
            StartupCacheService.recordSuccessfulAuth();
          }

          dispatch({
            type: 'AUTH_CHECK_COMPLETE',
            isAuthenticated,
          });
        } catch (error) {
          dispatch({
            type: 'ERROR',
            error: `Auth check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          });
        }
      };
      checkAuth();
    }
  }, [context.state, syncActions.refreshAuthState]);

  // Handle loading organizations
  useEffect(() => {
    if (context.state === 'loading-organizations') {
      const loadOrgs = async () => {
        try {
          console.log(
            '[AppInit] 🔄 Starting consolidated organization loading...'
          );
          const result = await organizationData.loadOrganizationContext();

          if (!result.success) {
            throw new Error(
              result.error || 'Failed to load organization context'
            );
          }

          // CRITICAL FIX: Wait for store to be properly updated before checking state
          // Give the store time to update after the loadOrganizationContext call
          await new Promise(resolve => setTimeout(resolve, 100));

          // Get fresh values from the store after loading
          const storeState = useOrganizationStore.getState();
          const freshOrganizations = storeState.organizations;
          const freshCurrentOrganization = storeState.currentOrganization;

          console.log(
            '[AppInit] 🏢 Organization context loaded successfully:',
            {
              organizations: freshOrganizations.map(org => ({
                id: org.external_id,
                name: org.name,
              })),
              currentOrganization: freshCurrentOrganization
                ? {
                    id: freshCurrentOrganization.external_id,
                    name: freshCurrentOrganization.name,
                  }
                : null,
              orgCount: freshOrganizations.length,
              willTransitionTo: freshCurrentOrganization
                ? 'syncing-data'
                : freshOrganizations.length > 1
                  ? 'needs-org-selection'
                  : freshOrganizations.length === 1
                    ? 'syncing-data'
                    : 'needs-org-setup',
            }
          );

          dispatch({
            type: 'ORGANIZATIONS_LOADED',
            organizations: freshOrganizations,
            currentOrg: freshCurrentOrganization,
          });
        } catch (error) {
          dispatch({
            type: 'ERROR',
            error: `Failed to load organizations: ${error instanceof Error ? error.message : 'Unknown error'}`,
          });
        }
      };
      loadOrgs();
    }
  }, [context.state, organizationData.loadOrganizationContext]);

  // Handle data syncing
  useEffect(() => {
    if (context.state === 'syncing-data') {
      const performSync = async () => {
        try {
          console.log('[AppInit] 🔄 Starting initial data sync...');

          // Check current data state using direct store access
          const initialColors = useColorStore.getState().colors;
          console.log(
            '[AppInit] 📊 Initial color count before sync:',
            initialColors.length
          );

          // Always attempt sync to ensure we have the latest data
          console.log('[AppInit] 📡 Calling syncData...');
          const syncResult = await syncActions.syncData();
          console.log('[AppInit] 📡 Sync result:', syncResult);

          // Wait a moment for sync to complete
          await new Promise(resolve => setTimeout(resolve, 500));

          // Check what we have after sync
          const syncedColors = useColorStore.getState().colors;
          console.log(
            '[AppInit] 📊 Color count after sync:',
            syncedColors.length
          );

          // If still no colors, try to load manually
          if (syncedColors.length === 0) {
            console.log(
              '[AppInit] 🔄 No colors after sync, attempting manual data load...'
            );
            await colorActions.loadColorsWithUsage();

            const manualColors = useColorStore.getState().colors;
            console.log(
              '[AppInit] 📊 Color count after manual load:',
              manualColors.length
            );

            if (manualColors.length === 0) {
              console.warn(
                '[AppInit] ⚠️ Still no colors found after all attempts. This might indicate:'
              );
              console.warn('- Supabase sync is not working');
              console.warn('- Organization context is not set correctly');
              console.warn('- Database initialization failed');
              console.warn('- User has no data in Supabase');
            }
          }

          console.log('[AppInit] ✅ Initial sync process completed');
          dispatch({ type: 'SYNC_COMPLETE' });
        } catch (error) {
          console.error('[AppInit] ❌ Sync failed with error:', error);
          // Load local data if sync fails
          try {
            console.log(
              '[AppInit] 🔄 Loading local data after sync failure...'
            );
            await colorActions.loadColorsWithUsage();
            const fallbackColors = useColorStore.getState().colors;
            console.log(
              '[AppInit] 📊 Fallback color count:',
              fallbackColors.length
            );
          } catch (localError) {
            console.error(
              '[AppInit] ❌ Local data loading also failed:',
              localError
            );
          }
          dispatch({ type: 'SYNC_COMPLETE' });
        }
      };
      performSync();
    }
  }, [context.state, syncActions.syncData, colorActions.loadColorsWithUsage]);

  // Watch for organization selection to trigger state transition
  useEffect(() => {
    if (
      context.state === 'needs-org-selection' &&
      organizationData.currentOrganization
    ) {
      console.log(
        '[AppInit] 🏢 Organization selected, transitioning to syncing state:',
        organizationData.currentOrganization.external_id
      );
      dispatch({ type: 'ORG_SELECTED' });
    }
  }, [context.state, organizationData.currentOrganization]);

  // AppShell-specific effects

  // Log startup completion and record timing
  useEffect(() => {
    if (startupReady) {
      const startupTime = getStartupTime();
      console.log(`[Startup] Application ready in ${startupTime}ms`);
      StartupCacheService.recordStartupCompletion(startupTime);

      // Log cache performance after startup
      setTimeout(() => {
        console.log(
          '[Startup] Cache performance summary after initialization:'
        );
        colorCacheService.logPerformanceSummary();
      }, 2000);
    }
  }, [startupReady, getStartupTime]);

  // Colors are now loaded during sync phase, no need for separate loading
  // This effect was causing redundant data loading

  // Initialize error logging and global event handlers
  useEffect(() => {
    // Log application start
    errorLogger.logInfo('Application started', {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      viewport: `${window.innerWidth}x${window.innerHeight}`,
    });

    // Debug panel keyboard shortcut (Ctrl+Shift+D in development)
    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        e.ctrlKey &&
        e.shiftKey &&
        e.key === 'D' &&
        process.env.NODE_ENV === 'development'
      ) {
        setDebugPanelOpen(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Listen for invitation events
  useEffect(() => {
    const handleInvitationReceived = (invitation: any) => {
      console.log('[App] Invitation received:', invitation);
      showAcceptInvitationModal(invitation);
    };

    if (window.organizationAPI?.onInvitationReceived) {
      window.organizationAPI.onInvitationReceived(handleInvitationReceived);
    }
  }, []);

  // Event handlers for UI interactions
  const handleAuthSuccess = useCallback(async () => {
    dispatch({ type: 'AUTH_SUCCESS' });
  }, []);

  const handleOrgSetupComplete = useCallback(async () => {
    dispatch({ type: 'ORG_SETUP_COMPLETE' });
  }, []);

  const handleOrgSelected = useCallback(async () => {
    dispatch({ type: 'ORG_SELECTED' });
  }, []);

  const handleRetry = useCallback(() => {
    dispatch({ type: 'RESET' });
  }, []);

  // Memoized derived state to prevent unnecessary re-computations
  const isReady = useMemo(() => {
    return startupReady && context.state === 'ready';
  }, [startupReady, context.state]);

  // Memoized handlers to prevent re-creation on every render
  const memoizedHandlers = useMemo(
    () => ({
      onAuthSuccess: handleAuthSuccess,
      onOrgSetupComplete: handleOrgSetupComplete,
      onOrgSelected: handleOrgSelected,
      onRetry: handleRetry,
    }),
    [handleAuthSuccess, handleOrgSetupComplete, handleOrgSelected, handleRetry]
  );

  // Optimized debug logging with memoized values to reduce re-renders
  const debugInfo = useMemo(
    () => ({
      stateIsReady: context.state === 'ready',
      startupReady,
      combinedReady: isReady,
      currentState: context.state,
      isAuthenticated,
      hasCurrentOrg: !!organizationData.currentOrganization,
      orgCount: organizationData.organizations.length,
    }),
    [
      context.state,
      startupReady,
      isReady,
      isAuthenticated,
      organizationData.currentOrganization,
      organizationData.organizations,
    ]
  );

  useEffect(() => {
    console.log('[AppInit] 🔍 State debug:', debugInfo);

    // Critical state transition logging
    if (context.state === 'ready' && !startupReady) {
      console.log(
        '[AppInit] ⚠️  State machine is ready but startup manager is not!'
      );
    } else if (startupReady && context.state !== 'ready') {
      console.log(
        '[AppInit] ⚠️  Startup manager is ready but state machine is not! Current state:',
        context.state
      );
    } else if (context.state === 'ready' && startupReady) {
      console.log(
        '[AppInit] ✅ Both state machine AND startup manager are ready!'
      );
    }
  }, [debugInfo, context.state, startupReady]);

  return {
    // State machine properties (for AppInitializer)
    state: context.state,
    error: context.error,
    isAuthenticated,
    user,
    currentOrganization: organizationData.currentOrganization,
    organizations: organizationData.organizations,
    handlers: memoizedHandlers,
    // AppShell properties
    isReady,
    startupReady,
    stage,
    progress,
    licenseStatus,
    debugPanelOpen,
    setDebugPanelOpen,
  };
}
