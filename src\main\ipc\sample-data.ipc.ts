/**
 * @file sample-data-migrated.ipc.ts
 * @description Migrated sample data IPC handlers using universal wrapper pattern
 */

import { ipcMain } from 'electron';
import { ProductService } from '../db/services/product.service';
import { ColorService } from '../db/services/color.service';
import {
  registerSecureHandler,
  createSuccessResponse
} from '../utils/ipc-wrapper';
import { ServiceLocator } from '../services/service-locator';

// Sample data for 10 products (keeping the existing sample data structure)
const sampleProducts = [
  {
    name: "Summer Collection 2024",
    sku: "SUMM-2024",
    description: "Vibrant summer fashion line",
    colors: [
      { code: "PANTONE 17-1463", name: "Tangerine Tango", hex: "#DD4124", library: "code" },
      { code: "PANTONE 14-4318", name: "Aqua Sky", hex: "#7BC4C4", library: "code" },
      { code: "PANTONE 13-0647", name: "Illuminating Yellow", hex: "#F5DF4D", library: "code" },
      { code: "PANTONE 18-3838", name: "<PERSON> Violet", hex: "#5F4B8B", library: "code" },
      { code: "PANTONE 15-5519", name: "Turquoise", hex: "#45B8AC", library: "code" }
    ]
  },
  {
    name: "Urban Office Furniture",
    sku: "URB-OFF-001",
    description: "Modern office furniture collection",
    colors: [
      { code: "RAL 7016", name: "Anthracite Grey", hex: "#293133", library: "ral" },
      { code: "RAL 9003", name: "Signal White", hex: "#F4F4F4", library: "ral" },
      { code: "RAL 5024", name: "Pastel Blue", hex: "#5D9B9B", library: "ral" },
      { code: "RAL 7035", name: "Light Grey", hex: "#D7D7D7", library: "ral" },
      { code: "CUSTOM-001", name: "Executive Brown", hex: "#3E2723", library: "user" }
    ]
  },
  {
    name: "Kids Room Essentials",
    sku: "KIDS-ESS-001",
    description: "Colorful and safe furniture for children",
    colors: [
      { code: "PANTONE 13-4411", name: "Baby Blue", hex: "#B5D3E7", library: "code" },
      { code: "PANTONE 12-0752", name: "Buttercup", hex: "#FCE883", library: "code" },
      { code: "PANTONE 14-4318", name: "Mint Green", hex: "#98D982", library: "code" },
      { code: "PANTONE 14-2311", name: "Pink Lavender", hex: "#E8B5CE", library: "code" },
      { code: "PANTONE 16-1441", name: "Peach", hex: "#FFBE98", library: "code" }
    ]
  },
  {
    name: "Automotive Paint Series",
    sku: "AUTO-PAINT-001",
    description: "Professional automotive paint collection",
    colors: [
      { code: "RAL 3020", name: "Traffic Red", hex: "#CC0605", library: "ral" },
      { code: "RAL 5017", name: "Traffic Blue", hex: "#063971", library: "ral" },
      { code: "RAL 6029", name: "Mint Green", hex: "#20603D", library: "ral" },
      { code: "RAL 1021", name: "Cadmium Yellow", hex: "#F3DA0B", library: "ral" },
      { code: "RAL 9004", name: "Signal Black", hex: "#282828", library: "ral" }
    ]
  },
  {
    name: "Luxury Bedroom Collection",
    sku: "LUX-BED-001",
    description: "Premium bedroom furniture with elegant finishes",
    colors: [
      { code: "PANTONE 18-1142", name: "Marsala", hex: "#955251", library: "code" },
      { code: "PANTONE 19-4052", name: "Classic Blue", hex: "#0F4C75", library: "code" },
      { code: "PANTONE 17-2031", name: "Rose Quartz", hex: "#F7CAC9", library: "code" },
      { code: "PANTONE 15-3919", name: "Serenity", hex: "#92A8D1", library: "code" },
      { code: "CUSTOM-002", name: "Champagne Gold", hex: "#F7E7CE", library: "user" }
    ]
  },
  {
    name: "Industrial Kitchen Series",
    sku: "IND-KITCH-001",
    description: "Commercial-grade kitchen equipment and surfaces",
    colors: [
      { code: "RAL 9006", name: "White Aluminum", hex: "#A5A5A5", library: "ral" },
      { code: "RAL 8019", name: "Grey Brown", hex: "#403A3A", library: "ral" },
      { code: "RAL 7024", name: "Graphite Grey", hex: "#474A51", library: "ral" },
      { code: "RAL 9010", name: "Pure White", hex: "#F4F4F4", library: "ral" },
      { code: "RAL 3009", name: "Oxide Red", hex: "#703332", library: "ral" }
    ]
  },
  {
    name: "Outdoor Patio Furniture",
    sku: "OUT-PAT-001",
    description: "Weather-resistant outdoor furniture collection",
    colors: [
      { code: "PANTONE 17-1456", name: "Fiesta", hex: "#DD4124", library: "code" },
      { code: "PANTONE 19-4914", name: "Reflecting Pond", hex: "#16445A", library: "code" },
      { code: "PANTONE 15-0343", name: "Greenery", hex: "#88B04B", library: "code" },
      { code: "PANTONE 18-2120", name: "Radiant Orchid", hex: "#B565A7", library: "code" },
      { code: "CUSTOM-003", name: "Teak Natural", hex: "#B8860B", library: "user" }
    ]
  },
  {
    name: "Smart Home Electronics",
    sku: "SMART-HOME-001",
    description: "Connected home devices and accessories",
    colors: [
      { code: "RAL 7047", name: "Telegrey 4", hex: "#D0D0D0", library: "ral" },
      { code: "RAL 9017", name: "Traffic Black", hex: "#1D1D1B", library: "ral" },
      { code: "RAL 5023", name: "Distant Blue", hex: "#49678D", library: "ral" },
      { code: "RAL 1034", name: "Pastel Yellow", hex: "#EFA94A", library: "ral" },
      { code: "RAL 6034", name: "Pastel Turquoise", hex: "#7FB5B5", library: "ral" }
    ]
  },
  {
    name: "Artisan Ceramics Line",
    sku: "ART-CER-001",
    description: "Handcrafted ceramic pieces with unique glazes",
    colors: [
      { code: "PANTONE 18-1763", name: "Living Coral", hex: "#FF6F61", library: "code" },
      { code: "PANTONE 16-1546", name: "Coral Pink", hex: "#F7786B", library: "code" },
      { code: "PANTONE 17-1558", name: "Fusion Coral", hex: "#F04A37", library: "code" },
      { code: "PANTONE 18-1664", name: "Aurora Red", hex: "#B32821", library: "code" },
      { code: "CUSTOM-004", name: "Ceramic Glaze Blue", hex: "#4A90E2", library: "user" }
    ]
  },
  {
    name: "Corporate Office Branding",
    sku: "CORP-BRAND-001",
    description: "Professional office space color coordination",
    colors: [
      { code: "PANTONE 19-4052", name: "Classic Blue", hex: "#0F4C75", library: "code" },
      { code: "PANTONE 432", name: "Cool Gray 8", hex: "#939598", library: "code" },
      { code: "PANTONE 877", name: "Silver", hex: "#C4C4C4", library: "code" },
      { code: "RAL 1015", name: "Light Ivory", hex: "#E6D690", library: "ral" },
      { code: "CUSTOM-005", name: "Corporate Navy", hex: "#1B365D", library: "user" }
    ]
  }
];

/**
 * Service dependencies interface
 */
interface SampleDataServices {
  productService: ProductService;
  colorService: ColorService;
}

/**
 * Register sample data IPC handlers with dependency injection and universal wrapper
 * 
 * @param services - Optional service bundle for dependency injection
 */
export function registerSampleDataHandlers(services?: Partial<SampleDataServices>): void {
  console.log('[SampleDataIPC] Registering sample data handlers with dependency injection');

  // Use dependency injection or fallback to ServiceLocator
  const productService = services?.productService || ServiceLocator.getProductService();
  const colorService = services?.colorService || ServiceLocator.getColorService();

  console.log('[SampleDataIPC] Services available:', {
    productService: !!productService,
    colorService: !!colorService
  });

  // Create all sample products
  registerSecureHandler(
    'sampleData:createAll',
    async (organizationId: string) => {
      console.log('[SampleDataIPC] Creating all sample products for organization:', organizationId);
      
      const results = [];
      let successCount = 0;
      let errorCount = 0;

      for (const productData of sampleProducts) {
        try {
          // Create product
          const product = await productService.create({
            name: productData.name,
            metadata: {
              description: productData.description,
              sku: productData.sku,
              category: 'sample',
              sampleData: true
            }
          }, organizationId);

          console.log(`[SampleDataIPC] Created product: ${product.name}`);

          // Add colors to the product
          const colorResults = [];
          for (const colorData of productData.colors) {
            try {
              // Create color entry
              const colorEntry = {
                name: colorData.name,
                code: colorData.code,
                hex: colorData.hex,
                cmyk: 'C:0 M:0 Y:0 K:0', // Default CMYK values
                product: product.name,
                source: 'sample-data',
                notes: `Sample color from ${colorData.library}`,
                tags: 'sample-data',
                organizationId: organizationId
              };

              const colorId = await colorService.add(colorEntry, undefined, organizationId);
              const color = await colorService.getById(colorId, organizationId);
              
              if (!color) {
                throw new Error(`Failed to retrieve created color with ID: ${colorId}`);
              }
              
              console.log(`[SampleDataIPC] Added color: ${color.name} (${color.hex})`);

              // Associate color with product
              await productService.addColor(product.id, color.id, organizationId);
              colorResults.push({
                success: true,
                color: color.name,
                hex: color.hex
              });

            } catch (colorError) {
              console.error(`[SampleDataIPC] Error adding color ${colorData.name}:`, colorError);
              colorResults.push({
                success: false,
                color: colorData.name,
                error: colorError instanceof Error ? colorError.message : String(colorError)
              });
              errorCount++;
            }
          }

          results.push({
            success: true,
            product: product.name,
            sku: productData.sku,
            colorsAdded: colorResults.filter(c => c.success).length,
            colorErrors: colorResults.filter(c => !c.success).length,
            colors: colorResults
          });

          successCount++;

        } catch (productError) {
          console.error(`[SampleDataIPC] Error creating product ${productData.name}:`, productError);
          results.push({
            success: false,
            product: productData.name,
            error: productError instanceof Error ? productError.message : String(productError)
          });
          errorCount++;
        }
      }

      console.log(`[SampleDataIPC] Sample data creation completed: ${successCount} products created, ${errorCount} errors`);

      return createSuccessResponse(
        {
          totalProducts: sampleProducts.length,
          successfulProducts: successCount,
          failedProducts: errorCount,
          results
        },
        `Sample data creation completed: ${successCount}/${sampleProducts.length} products created successfully`
      );
    },
    ipcMain,
    {
      logChannel: 'SampleDataCreate',
      customErrorMessage: 'Failed to create sample data. Please try again.'
    }
  );

  // Remove all sample data
  registerSecureHandler(
    'sampleData:removeAll',
    async (organizationId: string) => {
      console.log('[SampleDataIPC] Removing all sample data for organization:', organizationId);

      // Get all products with sample metadata
      const allProducts = productService.getAll(organizationId);
      const sampleProductsToRemove = allProducts.filter(p => 
        p.metadata?.sampleData === true || p.metadata?.category === 'sample'
      );

      let removedProducts = 0;
      let removedColors = 0;

      for (const product of sampleProductsToRemove) {
        try {
          // Get associated colors
          const productColors = productService.getColors(product.id, organizationId);
          
          // Remove color associations
          for (const color of productColors) {
            productService.removeColor(product.id, color.id, organizationId);
            
            // If color was created as sample data, remove it entirely
            if (color.source === 'sample-data') {
              colorService.delete(color.id, organizationId);
              removedColors++;
            }
          }
          
          // Remove the product
          productService.delete(product.id, organizationId);
          removedProducts++;
          
          console.log(`[SampleDataIPC] Removed sample product: ${product.name}`);
          
        } catch (error) {
          console.error(`[SampleDataIPC] Error removing sample product ${product.name}:`, error);
        }
      }

      console.log(`[SampleDataIPC] Sample data removal completed: ${removedProducts} products, ${removedColors} colors removed`);

      return createSuccessResponse(
        {
          productsRemoved: removedProducts,
          colorsRemoved: removedColors,
          totalFound: sampleProductsToRemove.length
        },
        `Sample data removal completed: ${removedProducts} products and ${removedColors} colors removed`
      );
    },
    ipcMain,
    {
      logChannel: 'SampleDataRemove',
      customErrorMessage: 'Failed to remove sample data. Please try again.'
    }
  );

  console.log('[SampleDataIPC] Sample data handlers registered successfully');
}

/**
 * Helper function to convert hex to RGB
 */
// function hexToRgb(hex: string): { r: number; g: number; b: number } {
//   const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
//   return result ? {
//     r: parseInt(result[1], 16),
//     g: parseInt(result[2], 16),
//     b: parseInt(result[3], 16)
//   } : { r: 0, g: 0, b: 0 };
// }

/**
 * Alternative registration using only ServiceLocator
 */
export function registerSampleDataHandlersFromLocator(): void {
  console.log('[SampleDataIPC] Registering sample data handlers using ServiceLocator pattern');
  registerSampleDataHandlers();
}