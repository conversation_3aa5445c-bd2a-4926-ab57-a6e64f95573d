/**
 * @file DeveloperToolsSection.tsx
 * @description Developer tools and debugging section component
 */

import React, { useState } from 'react';
import { Code, Terminal, Database, Eye, EyeOff } from 'lucide-react';
import { useSettingsStore } from '../../../store/settings.store';

/**
 * Developer tools section component
 */
export const DeveloperToolsSection: React.FC = () => {
  const settings = useSettingsStore();
  const [showApiKeys, setShowApiKeys] = useState(false);

  const openDevTools = async () => {
    try {
      // Use the existing window toggleDevTools API which already works
      if (window.electron?.window?.toggleDevTools) {
        await window.electron.window.toggleDevTools();
      } else {
        // Fallback to the settings API if needed
        await window.api.openDevTools();
      }
    } catch (error) {
      console.error('Failed to open dev tools:', error);
    }
  };

  const runPerformanceTest = async () => {
    try {
      await window.api.runPerformanceTest();
    } catch (error) {
      console.error('Failed to run performance test:', error);
    }
  };

  const checkDatabaseIntegrity = async () => {
    try {
      await window.api.checkDatabaseIntegrity();
    } catch (error) {
      console.error('Failed to check database integrity:', error);
    }
  };

  return (
    <section>
      <h3 className='text-lg font-medium text-ui-foreground-primary dark:text-white mb-4'>
        Developer Tools
      </h3>

      <div className='space-y-4'>
        {/* Debug Mode Toggle */}
        <div>
          <label className='flex items-center justify-between cursor-pointer bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4'>
            <div className='flex items-center'>
              <Code
                size={20}
                className='text-brand-primary dark:text-blue-400 mr-3'
              />
              <div>
                <span className='text-ui-foreground-primary dark:text-gray-300 block font-medium'>
                  Debug Mode
                </span>
                <span className='text-sm text-ui-foreground-secondary dark:text-gray-400'>
                  Enable verbose logging and debug features
                </span>
              </div>
            </div>
            <input
              type='checkbox'
              className='form-checkbox h-5 w-5 text-brand-primary rounded'
              checked={settings.debugMode}
              onChange={e => settings.setDebugMode(e.target.checked)}
            />
          </label>
        </div>

        {/* Verbose Logging */}
        <div>
          <label className='flex items-center justify-between cursor-pointer bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4'>
            <div className='flex items-center'>
              <Terminal
                size={20}
                className='text-ui-foreground-secondary dark:text-gray-400 mr-3'
              />
              <div>
                <span className='text-ui-foreground-primary dark:text-gray-300 block font-medium'>
                  Verbose Logging
                </span>
                <span className='text-sm text-ui-foreground-secondary dark:text-gray-400'>
                  Log detailed information for debugging
                </span>
              </div>
            </div>
            <input
              type='checkbox'
              className='form-checkbox h-5 w-5 text-brand-primary rounded'
              checked={settings.verboseLogging}
              onChange={e => settings.setVerboseLogging(e.target.checked)}
            />
          </label>
        </div>

        {/* Development Tools */}
        <div className='space-y-2'>
          <h4 className='text-ui-foreground-primary dark:text-white font-medium'>
            Development Tools
          </h4>

          <button
            onClick={openDevTools}
            className='w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors'
          >
            <div className='flex items-center'>
              <Code
                size={16}
                className='text-ui-foreground-secondary dark:text-gray-400 mr-2'
              />
              <span className='text-ui-foreground-primary dark:text-gray-300'>
                Open Developer Tools
              </span>
            </div>
          </button>

          <button
            onClick={runPerformanceTest}
            className='w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors'
          >
            <div className='flex items-center'>
              <Terminal
                size={16}
                className='text-ui-foreground-secondary dark:text-gray-400 mr-2'
              />
              <span className='text-ui-foreground-primary dark:text-gray-300'>
                Run Performance Test
              </span>
            </div>
          </button>

          <button
            onClick={checkDatabaseIntegrity}
            className='w-full flex items-center justify-between p-3 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 transition-colors'
          >
            <div className='flex items-center'>
              <Database
                size={16}
                className='text-ui-foreground-secondary dark:text-gray-400 mr-2'
              />
              <span className='text-ui-foreground-primary dark:text-gray-300'>
                Check Database Integrity
              </span>
            </div>
          </button>
        </div>

        {/* Environment Information */}
        <div>
          <h4 className='text-ui-foreground-primary dark:text-white font-medium mb-3'>
            Environment Information
          </h4>
          <div className='bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4'>
            <div className='space-y-2 text-sm'>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Electron Version:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {window.electron?.process?.versions?.electron || 'Unknown'}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Chrome Version:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {window.electron?.process?.versions?.chrome || 'Unknown'}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Node.js Version:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {window.electron?.process?.versions?.node || 'Unknown'}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-ui-foreground-secondary dark:text-gray-400'>
                  Platform:
                </span>
                <span className='text-ui-foreground-primary dark:text-gray-300'>
                  {window.electron?.process?.platform || 'Unknown'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* API Configuration (for debugging) */}
        {settings.debugMode && (
          <div>
            <div className='flex items-center justify-between mb-3'>
              <h4 className='text-ui-foreground-primary dark:text-white font-medium'>
                API Configuration
              </h4>
              <button
                onClick={() => setShowApiKeys(!showApiKeys)}
                className='flex items-center text-ui-foreground-secondary dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-gray-300'
              >
                {showApiKeys ? <EyeOff size={16} /> : <Eye size={16} />}
                <span className='ml-1 text-sm'>
                  {showApiKeys ? 'Hide' : 'Show'}
                </span>
              </button>
            </div>
            <div className='bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4'>
              <div className='space-y-2 text-sm font-mono'>
                <div className='flex justify-between'>
                  <span className='text-ui-foreground-secondary dark:text-gray-400'>
                    Supabase URL:
                  </span>
                  <span className='text-ui-foreground-primary dark:text-gray-300'>
                    {showApiKeys
                      ? 'https://your-project.supabase.co'
                      : '••••••••••••••••'}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-ui-foreground-secondary dark:text-gray-400'>
                    Auth Redirect:
                  </span>
                  <span className='text-ui-foreground-primary dark:text-gray-300'>
                    {showApiKeys
                      ? 'https://auth.chromasync.app/callback'
                      : '••••••••••••••••'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default DeveloperToolsSection;
