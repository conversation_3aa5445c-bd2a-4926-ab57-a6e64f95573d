/**
 * @file sync-diagnostics.ts
 * @description Diagnostic tools for analyzing sync issues and data inconsistencies
 * 
 * This service provides comprehensive analysis of sync data between Supabase and local database,
 * helping identify mapping failures, missing relationships, and data inconsistencies.
 */

import Database from 'better-sqlite3';

export interface SyncDiagnosticResult {
  timestamp: string;
  organizationId: string;
  
  // Data counts
  supabase: {
    products: number;
    colors: number;
    relationships: number;
  };
  
  local: {
    products: number;
    colors: number;
    relationships: number;
  };
  
  // Mapping analysis
  mapping: {
    mappedProducts: number;
    mappedColors: number;
    unmappedProducts: Array<{ id: number; name: string }>;
    unmappedColors: Array<{ id: number; hex: string; name?: string }>;
  };
  
  // Relationship analysis
  relationships: {
    syncedCount: number;
    failedMappings: Array<{
      supabaseProductId: number;
      supabaseColorId: number;
      reason: string;
    }>;
    orphanedLocal: Array<{
      productId: string;
      colorId: string;
      productName?: string;
      colorName?: string;
    }>;
  };
  
  // Schema validation
  schema: {
    productColorsTableCorrect: boolean;
    foreignKeyTypes: { productId: string; colorId: string };
    indexesPresent: string[];
  };
  
  // Recommendations
  recommendations: string[];
}

export interface SupabaseData {
  products: Array<{ id: number; name: string }>;
  colors: Array<{ id: number; hex: string; code: string; display_name?: string }>;
  relationships: Array<{ product_id: number; color_id: number; display_order?: number }>;
}

/**
 * Diagnostic service for sync operations
 */
export class SyncDiagnostics {
  constructor(private db: Database.Database) {}

  /**
   * Run comprehensive sync diagnostics
   */
  async runDiagnostics(
    organizationId: string,
    supabaseData: SupabaseData
  ): Promise<SyncDiagnosticResult> {
    console.log(`[SyncDiagnostics] 🔍 Running comprehensive diagnostics for organization: ${organizationId}`);
    
    const result: SyncDiagnosticResult = {
      timestamp: new Date().toISOString(),
      organizationId,
      supabase: {
        products: supabaseData.products.length,
        colors: supabaseData.colors.length,
        relationships: supabaseData.relationships.length
      },
      local: {
        products: 0,
        colors: 0,
        relationships: 0
      },
      mapping: {
        mappedProducts: 0,
        mappedColors: 0,
        unmappedProducts: [],
        unmappedColors: []
      },
      relationships: {
        syncedCount: 0,
        failedMappings: [],
        orphanedLocal: []
      },
      schema: {
        productColorsTableCorrect: false,
        foreignKeyTypes: { productId: '', colorId: '' },
        indexesPresent: []
      },
      recommendations: []
    };

    // Step 1: Analyze local data counts
    await this.analyzeLocalDataCounts(organizationId, result);
    
    // Step 2: Analyze schema
    await this.analyzeSchema(result);
    
    // Step 3: Analyze mapping success/failures
    await this.analyzeMappings(organizationId, supabaseData, result);
    
    // Step 4: Analyze relationships
    await this.analyzeRelationships(organizationId, supabaseData, result);
    
    // Step 5: Generate recommendations
    this.generateRecommendations(result);
    
    console.log(`[SyncDiagnostics] ✅ Diagnostics completed`);
    return result;
  }

  /**
   * Analyze local database data counts
   */
  private async analyzeLocalDataCounts(organizationId: string, result: SyncDiagnosticResult): Promise<void> {
    try {
      const productCount = this.db.prepare(`
        SELECT COUNT(*) as count FROM products 
        WHERE organization_id = ? AND deleted_at IS NULL
      `).get(organizationId) as { count: number };
      
      const colorCount = this.db.prepare(`
        SELECT COUNT(*) as count FROM colors 
        WHERE organization_id = ? AND deleted_at IS NULL
      `).get(organizationId) as { count: number };
      
      const relationshipCount = this.db.prepare(`
        SELECT COUNT(*) as count FROM product_colors 
        WHERE organization_id = ?
      `).get(organizationId) as { count: number };
      
      result.local.products = productCount.count;
      result.local.colors = colorCount.count;
      result.local.relationships = relationshipCount.count;
      
    } catch (error) {
      console.error(`[SyncDiagnostics] Error analyzing local data:`, error);
      result.recommendations.push('Failed to analyze local data - database may be corrupted');
    }
  }

  /**
   * Analyze database schema
   */
  private async analyzeSchema(result: SyncDiagnosticResult): Promise<void> {
    try {
      // Check product_colors table schema
      const tableInfo = this.db.prepare(`PRAGMA table_info(product_colors)`).all() as Array<{
        name: string;
        type: string;
        notnull: number;
        dflt_value: any;
        pk: number;
      }>;
      
      const productIdColumn = tableInfo.find(col => col.name === 'product_id');
      const colorIdColumn = tableInfo.find(col => col.name === 'color_id');
      
      if (productIdColumn) {
        result.schema.foreignKeyTypes.productId = productIdColumn.type;
      }
      if (colorIdColumn) {
        result.schema.foreignKeyTypes.colorId = colorIdColumn.type;
      }
      
      result.schema.productColorsTableCorrect = 
        productIdColumn?.type === 'TEXT' && colorIdColumn?.type === 'TEXT';
      
      // Check indexes
      const indexes = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type = 'index' AND tbl_name = 'product_colors'
      `).all() as Array<{ name: string }>;
      
      result.schema.indexesPresent = indexes.map(idx => idx.name);
      
    } catch (error) {
      console.error(`[SyncDiagnostics] Error analyzing schema:`, error);
      result.recommendations.push('Failed to analyze database schema');
    }
  }

  /**
   * Analyze mapping success and failures
   */
  private async analyzeMappings(
    organizationId: string,
    supabaseData: SupabaseData,
    result: SyncDiagnosticResult
  ): Promise<void> {
    try {
      // Get local data for mapping
      const localProducts = this.db.prepare(`
        SELECT id, name FROM products 
        WHERE organization_id = ? AND deleted_at IS NULL
      `).all(organizationId) as Array<{ id: string; name: string }>;
      
      const localColors = this.db.prepare(`
        SELECT id, hex, code, display_name FROM colors 
        WHERE organization_id = ? AND deleted_at IS NULL
      `).all(organizationId) as Array<{ id: string; hex: string; code: string | null; display_name: string | null }>;
      
      // Analyze product mappings
      for (const supabaseProduct of supabaseData.products) {
        const localProduct = localProducts.find(p => p.name.trim() === supabaseProduct.name.trim());
        if (localProduct) {
          result.mapping.mappedProducts++;
        } else {
          result.mapping.unmappedProducts.push({
            id: supabaseProduct.id,
            name: supabaseProduct.name
          });
        }
      }
      
      // Analyze color mappings
      for (const supabaseColor of supabaseData.colors) {
        const normalizedSupabaseHex = this.normalizeHex(supabaseColor.hex);
        
        let localColor = localColors.find(c => 
          this.normalizeHex(c.hex) === normalizedSupabaseHex
        );
        
        // Secondary matching by code if hex doesn't match
        if (!localColor && supabaseColor.code) {
          localColor = localColors.find(c => 
            c.code === supabaseColor.code && 
            c.display_name === supabaseColor.display_name
          );
        }
        
        if (localColor) {
          result.mapping.mappedColors++;
        } else {
          result.mapping.unmappedColors.push({
            id: supabaseColor.id,
            hex: supabaseColor.hex,
            name: supabaseColor.display_name || supabaseColor.code
          });
        }
      }
      
    } catch (error) {
      console.error(`[SyncDiagnostics] Error analyzing mappings:`, error);
      result.recommendations.push('Failed to analyze ID mappings');
    }
  }

  /**
   * Analyze relationship sync issues
   */
  private async analyzeRelationships(
    organizationId: string,
    supabaseData: SupabaseData,
    result: SyncDiagnosticResult
  ): Promise<void> {
    try {
      const { SupabaseUuidMapper } = await import('./supabase-uuid-mapper');
      
      // Initialize mapper to simulate the sync process
      const uuidMapper = new SupabaseUuidMapper();
      await uuidMapper.initializeMappings();
      
      // Analyze each relationship
      for (const relationship of supabaseData.relationships) {
        const productMapping = uuidMapper.getLocalProductUuid(String(relationship.product_id));
        const colorMapping = uuidMapper.getLocalColorUuid(String(relationship.color_id));
        
        if (productMapping.success && colorMapping.success) {
          // Check if this relationship actually exists in local database
          const exists = this.db.prepare(`
            SELECT 1 FROM product_colors 
            WHERE product_id = ? AND color_id = ? AND organization_id = ?
          `).get(productMapping.localUuid, colorMapping.localUuid, organizationId);
          
          if (exists) {
            result.relationships.syncedCount++;
          }
        } else {
          result.relationships.failedMappings.push({
            supabaseProductId: relationship.product_id,
            supabaseColorId: relationship.color_id,
            reason: !productMapping.success ? productMapping.error! : colorMapping.error!
          });
        }
      }
      
      // Find orphaned local relationships (exist locally but not in Supabase)
      const localRelationships = this.db.prepare(`
        SELECT 
          pc.product_id,
          pc.color_id,
          p.name as product_name,
          c.display_name as color_name
        FROM product_colors pc
        LEFT JOIN products p ON pc.product_id = p.id
        LEFT JOIN colors c ON pc.color_id = c.id
        WHERE pc.organization_id = ?
      `).all(organizationId) as Array<{
        product_id: string;
        color_id: string;
        product_name: string | null;
        color_name: string | null;
      }>;
      
      // Check which local relationships don't have corresponding Supabase data
      for (const localRel of localRelationships) {
        // This is a simplified check - in a real implementation you'd need reverse mapping
        const hasSupabaseEquivalent = result.relationships.syncedCount > 0; // Simplified
        
        if (!hasSupabaseEquivalent) {
          result.relationships.orphanedLocal.push({
            productId: localRel.product_id,
            colorId: localRel.color_id,
            productName: localRel.product_name || undefined,
            colorName: localRel.color_name || undefined
          });
        }
      }
      
    } catch (error) {
      console.error(`[SyncDiagnostics] Error analyzing relationships:`, error);
      result.recommendations.push('Failed to analyze relationship mappings');
    }
  }

  /**
   * Generate recommendations based on diagnostic results
   */
  private generateRecommendations(result: SyncDiagnosticResult): void {
    // Schema recommendations
    if (!result.schema.productColorsTableCorrect) {
      if (result.schema.foreignKeyTypes.productId === 'INTEGER') {
        result.recommendations.push('CRITICAL: product_colors table has INTEGER foreign keys instead of TEXT - run schema migration');
      }
    }
    
    // Mapping recommendations
    if (result.mapping.unmappedProducts.length > 0) {
      result.recommendations.push(`${result.mapping.unmappedProducts.length} products from Supabase couldn't be mapped to local products - check for name mismatches`);
    }
    
    if (result.mapping.unmappedColors.length > 0) {
      result.recommendations.push(`${result.mapping.unmappedColors.length} colors from Supabase couldn't be mapped to local colors - check for hex/code mismatches`);
    }
    
    // Relationship recommendations
    const expectedRelationships = result.supabase.relationships;
    const actualRelationships = result.relationships.syncedCount;
    
    if (actualRelationships === 0 && expectedRelationships > 0) {
      result.recommendations.push('CRITICAL: No relationships synced despite having data in Supabase - check schema and mappings');
    } else if (actualRelationships < expectedRelationships * 0.8) {
      result.recommendations.push(`Only ${actualRelationships}/${expectedRelationships} relationships synced - investigate mapping failures`);
    }
    
    // Performance recommendations
    const expectedIndexes = [
      'idx_product_colors_product',
      'idx_product_colors_color', 
      'idx_product_colors_org'
    ];
    
    const missingIndexes = expectedIndexes.filter(idx => !result.schema.indexesPresent.includes(idx));
    if (missingIndexes.length > 0) {
      result.recommendations.push(`Missing indexes: ${missingIndexes.join(', ')} - may impact performance`);
    }
    
    // Success case
    if (result.recommendations.length === 0) {
      result.recommendations.push('All diagnostics passed - sync configuration appears healthy');
    }
  }

  /**
   * Normalize hex values for comparison
   */
  private normalizeHex(hex: string): string {
    if (!hex) return '';
    return hex.replace('#', '').toUpperCase();
  }

  /**
   * Generate a formatted diagnostic report
   */
  generateReport(result: SyncDiagnosticResult): string {
    const report = [
      '='.repeat(80),
      'CHROMASYNC SYNC DIAGNOSTICS REPORT',
      '='.repeat(80),
      `Organization: ${result.organizationId}`,
      `Timestamp: ${result.timestamp}`,
      '',
      'DATA COUNTS:',
      `  Supabase: ${result.supabase.products} products, ${result.supabase.colors} colors, ${result.supabase.relationships} relationships`,
      `  Local:    ${result.local.products} products, ${result.local.colors} colors, ${result.local.relationships} relationships`,
      '',
      'MAPPING ANALYSIS:',
      `  Products: ${result.mapping.mappedProducts}/${result.supabase.products} mapped (${result.mapping.unmappedProducts.length} failed)`,
      `  Colors:   ${result.mapping.mappedColors}/${result.supabase.colors} mapped (${result.mapping.unmappedColors.length} failed)`,
      '',
      'RELATIONSHIP SYNC:',
      `  Synced: ${result.relationships.syncedCount}/${result.supabase.relationships} relationships`,
      `  Failed mappings: ${result.relationships.failedMappings.length}`,
      `  Orphaned local: ${result.relationships.orphanedLocal.length}`,
      '',
      'SCHEMA STATUS:',
      `  product_colors table correct: ${result.schema.productColorsTableCorrect ? 'YES' : 'NO'}`,
      `  Foreign key types: product_id=${result.schema.foreignKeyTypes.productId}, color_id=${result.schema.foreignKeyTypes.colorId}`,
      `  Indexes present: ${result.schema.indexesPresent.length}`,
      '',
      'RECOMMENDATIONS:',
      ...result.recommendations.map(rec => `  • ${rec}`),
      '',
      '='.repeat(80)
    ].join('\n');
    
    return report;
  }
}