/**
 * @file ColorSwatch.tsx
 * @description Single color swatch component displaying a color entry
 * Now supports gradient display with improved design and modal integration
 */

import React, { useState, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { ColorEntry } from '../../../shared/types/color.types';
import { useTokens } from '../../hooks/useTokens';
import { formatHex, parseCMYK, formatCMYKForDisplay } from '../../../shared/utils/color';
import { useColorStore } from '../../store/color.store';
import { useOrganizationStore } from '../../store/organization.store';
import { getProductsForColorName } from '../../hooks/useColorProductMap';
import GradientDetailsModal from '../GradientDetailsModal';
import GradientPickerModal from '../GradientPickerModal';

// Import unified window API types to resolve TS2717 conflicts
import type { ProductName } from '../../../shared/types/brand-types';

// Helper to darken a hex color
// function darkenColor(hex: string | undefined, amount: number = 0.7): string {
//   // Default to black if hex is undefined or invalid
//   if (!hex) {return '#000000';}
//   
//   // Remove the # if present
//   hex = hex.replace('#', '');
//   
//   // Ensure hex is a valid 6-character hex code
//   if (!/^[0-9A-Fa-f]{6}$/.test(hex)) {
//     return '#000000';
//   }
//   
//   // Convert to RGB
//   let r = parseInt(hex.substring(0, 2), 16);
//   let g = parseInt(hex.substring(2, 4), 16);
//   let b = parseInt(hex.substring(4, 6), 16);
//   
//   // Darken by multiplying each component by the amount
//   r = Math.floor(r * amount);
//   g = Math.floor(g * amount);
//   b = Math.floor(b * amount);
//   
//   // Convert back to hex
//   return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
// }

// Get the first color from a gradient
// function getFirstGradientColor(gradientCSS: string): string {
//   const colorMatches = gradientCSS.match(/#[0-9a-f]{3,6}/gi);
//   return colorMatches && colorMatches.length > 0 ? colorMatches[0] : '#000000';
// }

// Group header component to display at the top of the swatch
interface ProductGroupHeaderProps {
  count: number;
  onClick: (e: React.MouseEvent<HTMLDivElement>) => void;
  isExpanded: boolean;
  backgroundColor?: string;
}

const ProductGroupHeader: React.FC<ProductGroupHeaderProps> = ({ count, onClick, isExpanded }) => {
  ProductGroupHeader.displayName = 'ProductGroupHeader';
  return (
    <div 
      className="absolute top-2 left-2 z-10 py-1 px-2.5 rounded-full cursor-pointer flex items-center space-x-1.5 shadow-sm bg-ui-background-secondary border border-ui-border-light"
      onClick={onClick}
      aria-expanded={isExpanded}
      aria-label={count === -1 ? 'Loading product data...' : `${count} ${count === 1 ? 'product' : 'products'} using this color. Click to ${isExpanded ? 'hide' : 'show'} list`}
      role="button"
      tabIndex={0}
    >
      <span className="text-ui-foreground-primary text-xs font-medium">
        {count === -1 ? '...' : `${count} ${count === 1 ? 'product' : 'products'}`}
      </span>
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        className={`h-3 w-3 text-ui-foreground-primary transition-transform duration-200 ${isExpanded ? 'transform rotate-180' : ''}`} 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
        aria-hidden="true"
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    </div>
  );
};

interface ColorSwatchProps {
  entry: ColorEntry;
  productMap: Record<string, string[]>;
  isProductMapLoading: boolean;
}

const ColorSwatch = ({ entry, productMap, isProductMapLoading }: ColorSwatchProps) => {
  const tokens = useTokens();
  const { colors: _colors } = useColorStore();
  // const { openSelectionDatasheets } = useSelectionStore(); // DISABLED - use products instead
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [showProductList, setShowProductList] = useState(false);
  const [showGradientDetails, setShowGradientDetails] = useState(false);
  const [showGradientEditor, setShowGradientEditor] = useState(false);

  // Calculate usage count by color name (memoized for performance)
  const colorName = entry.name;
  
  
  // Use authoritative product map for complete product relationships
  const { uniqueProductNames, uniqueProductsCount } = useMemo(() => {
    if (!colorName) {
      return { uniqueProductNames: [], uniqueProductsCount: 0 };
    }
    
    // Add null checks for productMap
    if (!productMap) {
      return { uniqueProductNames: [], uniqueProductsCount: -1 };
    }
    
    // Get complete product list from the authoritative product map
    const allProductsForThisName = getProductsForColorName(productMap, colorName);
    
    // Use the complete product list from the map (already unique)
    const uniqueProductNames = allProductsForThisName || [];
    const uniqueProductsCount = uniqueProductNames.length;
    
    // If product map is still loading, don't show 0 - wait for data
    if (isProductMapLoading || Object.keys(productMap || {}).length === 0) {
      return { uniqueProductNames: [], uniqueProductsCount: -1 }; // -1 indicates loading/no data
    }
    
    return { uniqueProductNames, uniqueProductsCount };
  }, [colorName, productMap, isProductMapLoading]);

  // Helper function to clean code for display
  const getCleanCode = (code: string): string => {
    // Remove the unique identifier suffix (e.g., "939-MB2UE4Z6-8CAA" -> "939")
    const parts = code.split('-');
    return parts[0];
  };

  // Function to copy values to clipboard
  const copyToClipboard = useCallback((value: string, field: string) => {
    navigator.clipboard.writeText(value)
      .then(() => {
        setCopiedField(field);
        // Clear the copied indicator after 1.5 seconds
        setTimeout(() => setCopiedField(null), 1500);
      })
      .catch(err => {
        console.error('Could not copy to clipboard: ', err);
      });
  }, []);

  // Function to open product datasheet with enhanced type safety
  const handleProductClick = useCallback(async (product: string, e: React.MouseEvent) => {
    if (!product) {return;}
    
    e.stopPropagation(); // Prevent dropdown from closing
    
    try {
      // Open product datasheet using the API with branded type conversion
      const response = await window.api.openProductDatasheet(product as ProductName);
      if (response.success && response.data) {
        console.log(`Successfully opened datasheet for "${product}"`);
      } else {
        console.warn(`Failed to open datasheet for "${product}":`, response.error);
      }
    } catch (error) {
      console.error(`Error opening datasheet for "${product}":`, error);
    }
  }, []);

  // Determine if this is a gradient
  const hasGradient = !!entry.gradient;
  
  // Helper function to extract gradient colors from actual gradient data structure
  const getGradientColors = useCallback((): string[] | null => {
    if (!hasGradient || !entry.gradient) return null;
    
    const gradient = entry.gradient as any; // Cast to any to access various possible structures
    
    // Debug logging for specific gradient
    if (entry.name === 'New Grad Test') {
      console.log('🔍 [ColorSwatch] DEBUG: Gradient structure for', entry.name, {
        hasEntryGradient: !!entry.gradient,
        gradient: entry.gradient,
        hasGradientStops: !!gradient.gradientStops,
        gradientStopsArray: gradient.gradientStops,
        hasGradientCSS: !!gradient.gradientCSS,
        gradientCSS: gradient.gradientCSS,
        hasColors: !!gradient.colors,
        colors: gradient.colors,
        fullEntry: entry
      });
    }
    
    // PRIORITY FIX: Try StandardGradientData.colors first (ideal case)
    if (gradient.colors && Array.isArray(gradient.colors) && gradient.colors.length > 0) {
      if (entry.name === 'New Grad Test') {
        console.log('✅ USING NEW FORMAT: StandardGradientData.colors:', gradient.colors);
      }
      return gradient.colors;
    }
    
    // Try gradientStops array (legacy fallback)
    if (gradient.gradientStops && Array.isArray(gradient.gradientStops) && gradient.gradientStops.length > 0) {
      // Extract colors from gradient stops
      const colors = gradient.gradientStops.map((stop: any) => {
        // Try different possible color field names
        const color = stop.color || stop.hex || stop.value || stop.rgb;
        if (entry.name === 'New Grad Test') {
          console.log('⚠️ USING LEGACY FORMAT: gradientStops, processing stop:', stop, 'extracted color:', color);
        }
        return color;
      }).filter((color: any) => color && typeof color === 'string');
      
      if (colors.length > 0) {
        if (entry.name === 'New Grad Test') {
          console.log('⚠️ LEGACY PATH: Extracted colors from gradientStops:', colors);
        }
        return colors;
      }
    }
    
    // Try parsing gradientCSS if available (most reliable fallback)
    if (gradient.gradientCSS && typeof gradient.gradientCSS === 'string') {
      const cssColors = gradient.gradientCSS.match(/#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/g);
      if (cssColors && cssColors.length > 0) {
        console.log('Extracted colors from gradientCSS:', cssColors);
        return cssColors;
      }
    }
    
    console.warn('No gradient colors found for:', entry.name, 'gradient data:', gradient);
    return null;
  }, [hasGradient, entry.gradient, entry.name]);
  


  // Handle swatch click
  const handleSwatchClick = useCallback((e: React.MouseEvent) => {
    if (hasGradient) {
      e.stopPropagation();
      setShowGradientEditor(true); // Changed from setShowGradientDetails to setShowGradientEditor for consistency
    } else if (entry.hex) {
      copyToClipboard(formatHex(entry.hex), 'hex');
    } else {
      copyToClipboard('#000000', 'hex');
    }
  }, [hasGradient, entry, copyToClipboard]);

  // Handle keyboard events for accessibility
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleSwatchClick(e as unknown as React.MouseEvent);
    }
  }, [handleSwatchClick]);

  // Handle gradient edit
  const handleGradientEdit = useCallback(() => {
    setShowGradientDetails(false); // Close details modal
    setShowGradientEditor(true); // Open editor modal
  }, []);

  // Handle edit success - force refresh to ensure UI updates for gradients
  const handleEditSuccess = useCallback(async () => {
    setShowGradientEditor(false);
    // Force cache invalidation and refresh after gradient edit to ensure UI updates properly
    const { colorCacheService } = await import('../../services/color-cache.service');
    const { currentOrganization } = useOrganizationStore.getState();
    const currentOrg = currentOrganization;
    
    if (currentOrg?.id) {
      // Invalidate cache to force fresh data fetch
      colorCacheService.invalidateOrganization(currentOrg.id);
    }
    
    // Trigger fresh data load
    const { loadColorsWithUsage } = useColorStore.getState();
    await loadColorsWithUsage();
  }, []);

  // Handle edit cancel
  const handleEditCancel = useCallback(() => {
    setShowGradientEditor(false);
  }, []);


  return (
    <>
      <div 
        className="group relative rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border border-ui-border-light bg-ui-background-primary transform hover:-translate-y-1"
        onMouseLeave={() => setShowProductList(false)}
        data-testid="color-swatch"
        onClick={handleSwatchClick}
        onKeyDown={handleKeyDown}
        role="button"
        tabIndex={0}
        aria-label={hasGradient 
          ? `${entry.name || 'Unknown name'}, gradient. Click to edit gradient` 
          : `${entry.name || 'Unknown name'}, Code ${entry.code || 'N/A'}, Hex ${formatHex(entry.hex || '#000000')}, CMYK ${formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0'))}`}
        style={{
          transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.inOut}`,
          borderRadius: tokens.borderRadius.xl,
          overflow: showProductList ? 'visible' : 'hidden'
        }}
      >
        {/* Color preview - more responsive height */}
        <div 
          className="flex-grow h-24 relative p-3 flex items-end"
          style={{ 
            background: hasGradient ? 
              (() => {
                const colors = getGradientColors();
                if (colors && colors.length > 0) {
                  const gradientCSS = `linear-gradient(45deg, ${colors.map((color: string, index: number) => {
                    const position = colors.length === 1 ? 50 : (index / (colors.length - 1)) * 100;
                    return `${color} ${position}%`;
                  }).join(', ')})`;
                  
                  return gradientCSS;
                } else {
                  // If gradient exists but no colors, show a debug gradient
                  console.warn('Gradient detected but no colors found for:', entry.name, 'showing debug gradient');
                  return 'linear-gradient(45deg, #FF6B6B, #4ECDC4)'; // Bright colors to make it obvious
                }
              })() : 
              (entry.hex || '#000000'),
            boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.05)", // Add subtle inner shadow to ensure visibility
            borderTopLeftRadius: '1rem',
            borderTopRightRadius: '1rem'
          }}
          data-testid={hasGradient ? "gradient-swatch" : "color-swatch-preview"}
        >
          {/* Product group indicator - always display regardless of product count */}
          <ProductGroupHeader 
            count={uniqueProductsCount} 
            onClick={(e) => {
              e.stopPropagation();
              setShowProductList(!showProductList);
            }}
            isExpanded={showProductList}
          />
        </div>
        
        {/* Product list - using portal to render outside parent hierarchy */}
        {showProductList && document.body && createPortal(
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 bg-black/50 z-[9998]"
              onClick={() => setShowProductList(false)}
            />
            {/* Modal */}
            <div 
              className="fixed bg-ui-background-primary z-[9999] max-h-[400px] overflow-y-auto rounded-lg shadow-lg border border-ui-border-light"
              style={{
                boxShadow: tokens.shadows.lg,
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '320px',
                maxWidth: '90vw',
                maxHeight: '80vh'
              }}
              onClick={(e) => e.stopPropagation()}
              role="dialog"
              aria-label="Products using this color"
            >
            <div className="p-4 border-b border-ui-border-light sticky top-0 bg-ui-background-primary z-[10000]">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium">
                  {uniqueProductsCount === -1 ? 'Loading...' : `${uniqueProductsCount} ${uniqueProductsCount === 1 ? 'Product' : 'Products'}`} with "{entry.name || 'Unknown name'}"
                </h3>
                <button 
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-zinc-700"
                  onClick={() => setShowProductList(false)}
                  aria-label="Close product list"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-3">
              {uniqueProductNames.map((productName, index) => (
                <div 
                  key={productName || index}
                  className="p-3 hover:bg-ui-background-hover rounded-lg cursor-pointer mb-1 last:mb-0 transition-colors group"
                  onClick={(e) => handleProductClick(productName, e)}
                  role="button"
                  tabIndex={0}
                  aria-label={`View product datasheet for ${productName} ${entry.name}`}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleProductClick(productName, e as unknown as React.MouseEvent);
                    }
                  }}
                >
                  <div className="text-sm font-medium text-ui-foreground-primary group-hover:text-ui-foreground-primary">{productName}</div>
                  <div className="text-xs text-ui-foreground-tertiary group-hover:text-ui-foreground-secondary mt-0.5">{entry.name}</div>
                </div>
              ))}
            </div>
            </div>
          </>,
          document.body
        )}
        
        {/* Color info - improved with always visible hex and code */}
        <div className="p-3 bg-ui-background-primary border-t border-ui-border-light" style={{
          borderBottomLeftRadius: '1rem',
          borderBottomRightRadius: '1rem'
        }}>
          <div className="flex flex-col">
            {/* Top row - Name information */}
            <div className="mb-2">
              <h3 className="text-sm font-semibold text-ui-foreground-primary truncate">
                {entry.name || 'Unknown name'}
              </h3>
            </div>
            
            {/* Bottom row - color information aligned to the right */}
            <div className="flex flex-col items-end w-full">
              {/* For solid colors, show Pantone */}
              {!hasGradient && (
                <div className="group/code relative w-full text-right">
                  <span 
                    className="text-xs font-medium text-right text-ui-foreground-secondary cursor-pointer hover:text-red-600 transition-colors block"
                    onClick={(e) => {
                      e.stopPropagation();
                      copyToClipboard(entry.code || 'N/A', 'code');
                    }}
                    role="button"
                    tabIndex={0}
                    aria-label={`Copy code ${entry.code || 'N/A'}`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        copyToClipboard(entry.code || 'N/A', 'code');
                      }
                    }}
                    title={entry.code || 'N/A'}
                  >
                    {entry.code ? getCleanCode(entry.code) : 'N/A'}
                  </span>
                  {copiedField === 'code' ? (
                    <span className="text-red-600 text-xs ml-1 absolute right-0 -top-5 whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                      Copied!
                    </span>
                  ) : (
                    <span className="absolute right-0 -top-5 opacity-0 group-hover/code:opacity-100 transition-opacity duration-200 text-xs whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                      Click to copy
                    </span>
                  )}
                </div>
              )}
              
              {/* For all colors, show hex or "Gradient CSS" */}
              <div className="group/hex relative mt-1 w-full text-right">
                <span 
                  className={`text-xs ${hasGradient ? 'text-red-600 font-medium' : 'text-ui-foreground-tertiary font-mono'} cursor-pointer hover:text-red-700 transition-colors block`}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (hasGradient) {
                      setShowGradientEditor(true);
                    } else {
                      copyToClipboard(formatHex(entry.hex), 'hex');
                    }
                  }}
                  role="button"
                  tabIndex={0}
                  aria-label={hasGradient ? `Click to edit gradient` : `Copy hex code ${entry.hex}`}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      if (hasGradient) {
                        setShowGradientEditor(true);
                      } else if (entry.hex) {
                        copyToClipboard(formatHex(entry.hex), 'hex');
                      } else {
                        copyToClipboard('#000000', 'hex');
                      }
                    }
                  }}
                  title={hasGradient ? 'Edit gradient' : (entry.hex ? formatHex(entry.hex) : '#000000')}
                >
                  {hasGradient ? 'Gradient' : (entry.hex ? formatHex(entry.hex) : '#000000')}
                </span>
                {!hasGradient && copiedField === 'hex' && (
                  <span className="text-red-600 text-xs ml-1 absolute right-0 top-5 whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                    Copied!
                  </span>
                )}
                {!hasGradient && (
                  <span className="absolute right-0 top-5 opacity-0 group-hover/hex:opacity-100 transition-opacity duration-200 text-xs whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                    Click to copy
                  </span>
                )}
              </div>
              
              {/* For solid colors, show CMYK */}
              {!hasGradient && (
                <div className="group/cmyk relative mt-1 w-full text-right">
                  <span 
                    className="text-2xs font-mono text-ui-foreground-tertiary cursor-pointer hover:text-ui-foreground-primary transition-colors whitespace-nowrap overflow-visible block"
                    onClick={(e) => {
                      e.stopPropagation();
                      copyToClipboard(formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0')), 'cmyk');
                    }}
                    role="button"
                    tabIndex={0}
                    aria-label={`Copy CMYK values ${formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0'))}`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        copyToClipboard(formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0')), 'cmyk');
                      }
                    }}
                    title={formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0'))}
                    style={{ fontSize: '9px', letterSpacing: '-0.01em' }}
                  >
                    {formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0'))}
                  </span>
                  {copiedField === 'cmyk' ? (
                    <span className="text-red-600 text-xs ml-1 absolute right-0 top-5 whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                      Copied!
                    </span>
                  ) : (
                    <span className="absolute right-0 top-5 opacity-0 group-hover/cmyk:opacity-100 transition-opacity duration-200 text-xs whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                      Click to copy
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Use portal for modal to ensure it's not constrained by parent container */}
      {hasGradient && entry.gradient && showGradientDetails && document.body && createPortal(
        <GradientDetailsModal 
          isOpen={showGradientDetails}
          onClose={() => setShowGradientDetails(false)}
          gradient={entry.gradient}
          product={entry.product}
          colorEntry={entry}
          onEdit={handleGradientEdit}
        />,
        document.body
      )}

      {/* Gradient Editor Modal */}
      {hasGradient && entry.gradient && showGradientEditor && document.body && createPortal(
        <GradientPickerModal
          isOpen={showGradientEditor}
          onClose={handleEditCancel}
          onSuccess={handleEditSuccess}
          initialValue={entry.gradient}
          editMode={true}
          color={entry}
          productName={entry.product}
        />,
        document.body
      )}
    </>
  );
}

// Set display name
ColorSwatch.displayName = 'ColorSwatch';

// Export as memoized component for performance
export default ColorSwatch;