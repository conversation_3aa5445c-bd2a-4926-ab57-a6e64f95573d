import { defineConfig, externalizeDepsPlugin } from 'electron-vite';
import { resolve } from 'path';
import type { UserConfig } from 'vite';

// Enhanced type-safe configuration for Electron build system
export default defineConfig({
  main: {
    plugins: [
      externalizeDepsPlugin({
        // External dependencies for main process
        include: [
          'better-sqlite3',
          'electron-log',
          'electron-store',
          'electron-updater',
          'node-machine-id',
          'generic-pool',
          '@sentry/electron',
          '@sentry/node',
        ],
      }),
    ],
    resolve: {
      conditions: ['node'],
      browserField: false,
      mainFields: ['module', 'jsnext:main', 'jsnext'],
      alias: {
        '@': resolve(__dirname, 'src'),
        '@main': resolve(__dirname, 'src/main'),
        '@shared': resolve(__dirname, 'src/shared'),
        '@types': resolve(__dirname, 'src/types'),
        '@db': resolve(__dirname, 'src/main/db'),
        '@services': resolve(__dirname, 'src/main/services'),
        '@utils': resolve(__dirname, 'src/main/utils'),
      },
    },
    define: {
      // Environment variables for main process
      __MAIN_PROCESS__: true,
      __RENDERER_PROCESS__: false,
      __PRELOAD_PROCESS__: false,
    },
    build: {
      ssr: true,
      target: 'node18',
      minify: process.env.NODE_ENV === 'production',
      sourcemap: process.env.NODE_ENV === 'development' ? 'inline' : false,
      outDir: 'out/main',
      assetsDir: 'assets',
      rollupOptions: {
        external: [
          'electron',
          'better-sqlite3',
          'electron-log',
          'electron-store',
          'electron-updater',
          'electron-util',
          'node-machine-id',
          'generic-pool',
          '@sentry/electron',
          '@sentry/node',
          'nodemailer',
          'axios',
          'crypto',
          'fs',
          'path',
          'os',
          'url',
          'util',
          'events',
          'stream',
          'buffer',
          'child_process',
        ],
        output: {
          format: 'cjs',
          entryFileNames: '[name].js',
          chunkFileNames: '[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash][extname]',
        },
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          unknownGlobalSideEffects: false,
        },
      },
      copyPublicDir: false,
      emptyOutDir: true,
    },
  } satisfies UserConfig,

  preload: {
    resolve: {
      conditions: ['node'],
      browserField: false,
      alias: {
        '@': resolve(__dirname, 'src'),
        '@preload': resolve(__dirname, 'src/preload'),
        '@shared': resolve(__dirname, 'src/shared'),
        '@types': resolve(__dirname, 'src/types'),
      },
    },
    define: {
      __MAIN_PROCESS__: false,
      __RENDERER_PROCESS__: false,
      __PRELOAD_PROCESS__: true,
    },
    build: {
      ssr: true,
      target: 'node18',
      minify: process.env.NODE_ENV === 'production',
      sourcemap: process.env.NODE_ENV === 'development' ? 'inline' : false,
      outDir: 'out/preload',
      rollupOptions: {
        external: [
          'electron',
          'node-machine-id',
          'axios',
          'uuid',
          'crypto',
          'os',
          'path',
          'fs',
          'util',
          'events',
        ],
        output: {
          format: 'cjs',
          entryFileNames: '[name].js',
          chunkFileNames: '[name].js',
          assetFileNames: 'assets/[name][extname]',
        },
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          unknownGlobalSideEffects: false,
        },
      },
      emptyOutDir: true,
    },
  } satisfies UserConfig,

  renderer: {
    resolve: {
      conditions: ['browser'],
      browserField: true,
      alias: {
        '@': resolve(__dirname, 'src'),
        '@shared': resolve(__dirname, 'src/shared'),
        '@renderer': resolve(__dirname, 'src/renderer'),
        '@components': resolve(__dirname, 'src/renderer/components'),
        '@hooks': resolve(__dirname, 'src/renderer/hooks'),
        '@store': resolve(__dirname, 'src/renderer/store'),
        '@utils': resolve(__dirname, 'src/renderer/utils'),
        '@assets': resolve(__dirname, 'src/renderer/assets'),
        '@styles': resolve(__dirname, 'src/renderer/styles'),
        '@types': resolve(__dirname, 'src/types'),
        // Legacy alias support
        shared: resolve(__dirname, 'src/shared'),
      },
    },
    define: {
      __MAIN_PROCESS__: false,
      __RENDERER_PROCESS__: true,
      __PRELOAD_PROCESS__: false,
      // Expose environment variables safely
      __DEV__: process.env.NODE_ENV === 'development',
      __PROD__: process.env.NODE_ENV === 'production',
    },
    build: {
      target: ['chrome118'], // Match Electron's Chrome version
      minify: process.env.NODE_ENV === 'production' ? 'esbuild' : false,
      sourcemap: process.env.NODE_ENV === 'development' ? 'inline' : false,
      outDir: 'out/renderer',
      assetsDir: 'assets',
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'src/renderer/index.html'),
          'color-space-3d': resolve(
            __dirname,
            'src/renderer/color-space-3d.html'
          ),
        },
        output: {
          format: 'es',
          entryFileNames: 'assets/[name]-[hash].js',
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash][extname]',
          manualChunks: {
            // Vendor chunk for large dependencies
            vendor: [
              'react',
              'react-dom',
              'react-router-dom',
              '@mui/material',
              '@mui/icons-material',
              '@emotion/react',
              '@emotion/styled',
            ],
            // Utilities chunk
            utils: ['zustand', 'colord', 'zod', 'axios'],
            // Chart libraries
            charts: ['recharts', '@visx/visx'],
          },
        },
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          unknownGlobalSideEffects: false,
        },
      },
      // Performance optimizations
      chunkSizeWarningLimit: 1000,
      cssCodeSplit: true,
      reportCompressedSize: false,
      emptyOutDir: true,
    },
    // CSS processing
    css: {
      modules: false,
      postcss: {
        plugins: [],
      },
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/renderer/styles/variables.scss";`,
        },
      },
    },
    // Development server configuration
    server: {
      host: 'localhost',
      port: 3000,
      strictPort: true,
      hmr: {
        port: 3001,
      },
    },
    // Optimization configuration
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@mui/material',
        '@mui/icons-material',
        '@emotion/react',
        '@emotion/styled',
        'zustand',
        'colord',
        'zod',
      ],
      exclude: ['electron'],
    },
  } satisfies UserConfig,
});
