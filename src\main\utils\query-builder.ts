/**
 * Safe Query Builder Utility
 * Provides secure query building patterns to prevent SQL injection
 */

export interface QueryBuilderOptions {
  table: string;
  select?: string[];
  where?: Record<string, any>;
  orderBy?: { column: string; direction: 'ASC' | 'DESC' }[];
  limit?: number;
  offset?: number;
  joins?: Array<{
    type: 'INNER' | 'LEFT' | 'RIGHT';
    table: string;
    on: string;
  }>;
}

export interface WhereClause {
  sql: string;
  params: any[];
}

/**
 * Safe query builder that prevents SQL injection
 */
export class SafeQueryBuilder {
  private static allowedTables = [
    'colors',
    'products',
    'datasheets',
    'organizations',
    'users',
    'product_colors',
    'organization_members',
  ];

  private static allowedColumns = [
    'id',
    'external_id',
    'name',
    'organization_id',
    'user_id',
    'created_at',
    'updated_at',
    'deleted_at',
    'is_active',
    'hex',
    'code',
    'display_name',
    'source_id',
    'is_gradient',
    'description',
    'metadata',
    'color_spaces',
    'notes',
    'tags',
    'is_library',
    'file_type',
    'url',
    'path',
  ];

  /**
   * Build a safe SELECT query
   */
  static buildSelect(options: QueryBuilderOptions): {
    sql: string;
    params: any[];
  } {
    // Validate table name
    this.validateTable(options.table);

    let sql = 'SELECT ';

    // Build SELECT clause
    if (options.select && options.select.length > 0) {
      const validColumns = options.select.filter(col =>
        this.isValidColumn(col)
      );
      sql += validColumns.join(', ');
    } else {
      sql += '*';
    }

    sql += ` FROM ${options.table}`;

    // Build JOINs
    if (options.joins) {
      for (const join of options.joins) {
        this.validateTable(join.table);
        sql += ` ${join.type} JOIN ${join.table} ON ${join.on}`;
      }
    }

    // Build WHERE clause
    const params: any[] = [];
    if (options.where) {
      const whereClause = this.buildWhereClause(options.where);
      if (whereClause.sql) {
        sql += ` WHERE ${whereClause.sql}`;
        params.push(...whereClause.params);
      }
    }

    // Build ORDER BY
    if (options.orderBy && options.orderBy.length > 0) {
      const orderClauses = options.orderBy
        .filter(order => this.isValidColumn(order.column))
        .map(order => `${order.column} ${order.direction}`);

      if (orderClauses.length > 0) {
        sql += ` ORDER BY ${orderClauses.join(', ')}`;
      }
    }

    // Build LIMIT and OFFSET
    if (options.limit !== undefined) {
      sql += ` LIMIT ?`;
      params.push(options.limit);
    }

    if (options.offset !== undefined) {
      sql += ` OFFSET ?`;
      params.push(options.offset);
    }

    return { sql, params };
  }

  /**
   * Build a safe INSERT query
   */
  static buildInsert(
    table: string,
    data: Record<string, any>
  ): { sql: string; params: any[] } {
    this.validateTable(table);

    const columns = Object.keys(data).filter(col => this.isValidColumn(col));
    const placeholders = columns.map(() => '?').join(', ');
    const params = columns.map(col => data[col]);

    const sql = `INSERT INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`;

    return { sql, params };
  }

  /**
   * Build a safe UPDATE query
   */
  static buildUpdate(
    table: string,
    data: Record<string, any>,
    where: Record<string, any>
  ): { sql: string; params: any[] } {
    this.validateTable(table);

    const updateColumns = Object.keys(data).filter(col =>
      this.isValidColumn(col)
    );
    const setClauses = updateColumns.map(col => `${col} = ?`).join(', ');
    const updateParams = updateColumns.map(col => data[col]);

    const whereClause = this.buildWhereClause(where);

    const sql = `UPDATE ${table} SET ${setClauses}${whereClause.sql ? ` WHERE ${whereClause.sql}` : ''}`;
    const params = [...updateParams, ...whereClause.params];

    return { sql, params };
  }

  /**
   * Build a safe DELETE query
   */
  static buildDelete(
    table: string,
    where: Record<string, any>
  ): { sql: string; params: any[] } {
    this.validateTable(table);

    const whereClause = this.buildWhereClause(where);

    if (!whereClause.sql) {
      throw new Error('DELETE queries must have a WHERE clause');
    }

    const sql = `DELETE FROM ${table} WHERE ${whereClause.sql}`;

    return { sql, params: whereClause.params };
  }

  /**
   * Build WHERE clause with proper parameterization
   */
  private static buildWhereClause(
    conditions: Record<string, any>
  ): WhereClause {
    const clauses: string[] = [];
    const params: any[] = [];

    for (const [column, value] of Object.entries(conditions)) {
      if (!this.isValidColumn(column)) {
        continue;
      }

      if (value === null) {
        clauses.push(`${column} IS NULL`);
      } else if (value === undefined) {
        // Skip undefined values
        continue;
      } else if (Array.isArray(value)) {
        if (value.length > 0) {
          const placeholders = value.map(() => '?').join(', ');
          clauses.push(`${column} IN (${placeholders})`);
          params.push(...value);
        }
      } else if (typeof value === 'object' && value.operator) {
        // Support operators like { operator: 'LIKE', value: '%test%' }
        const operator = this.validateOperator(value.operator);
        clauses.push(`${column} ${operator} ?`);
        params.push(value.value);
      } else {
        clauses.push(`${column} = ?`);
        params.push(value);
      }
    }

    return {
      sql: clauses.join(' AND '),
      params,
    };
  }

  /**
   * Validate table name against allowlist
   */
  private static validateTable(table: string): void {
    if (!this.allowedTables.includes(table)) {
      throw new Error(`Invalid table name: ${table}`);
    }
  }

  /**
   * Validate column name
   */
  private static isValidColumn(column: string): boolean {
    // Allow qualified column names (table.column)
    const columnParts = column.split('.');
    const columnName =
      columnParts.length > 1 ? columnParts[1] || column : column;
    return (
      this.allowedColumns.includes(columnName) ||
      /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(columnName)
    );
  }

  /**
   * Validate SQL operator
   */
  private static validateOperator(operator: string): string {
    const allowedOperators = [
      '=',
      '!=',
      '<>',
      '<',
      '>',
      '<=',
      '>=',
      'LIKE',
      'NOT LIKE',
      'IS',
      'IS NOT',
    ];
    if (!allowedOperators.includes(operator.toUpperCase())) {
      throw new Error(`Invalid operator: ${operator}`);
    }
    return operator.toUpperCase();
  }
}

/**
 * Helper functions for common query patterns
 */
export class QueryHelpers {
  /**
   * Build organization-scoped query
   */
  static withOrganizationScope(
    baseOptions: QueryBuilderOptions,
    organizationId: string
  ): QueryBuilderOptions {
    return {
      ...baseOptions,
      where: {
        ...baseOptions.where,
        organization_id: organizationId,
      },
    };
  }

  /**
   * Build active records query (not deleted)
   */
  static withActiveRecords(
    baseOptions: QueryBuilderOptions
  ): QueryBuilderOptions {
    return {
      ...baseOptions,
      where: {
        ...baseOptions.where,
        deleted_at: null,
      },
    };
  }

  /**
   * Build paginated query
   */
  static withPagination(
    baseOptions: QueryBuilderOptions,
    page: number,
    pageSize: number
  ): QueryBuilderOptions {
    return {
      ...baseOptions,
      limit: pageSize,
      offset: (page - 1) * pageSize,
    };
  }

  /**
   * Build search query with LIKE
   */
  static withSearch(
    baseOptions: QueryBuilderOptions,
    searchColumn: string,
    searchTerm: string
  ): QueryBuilderOptions {
    return {
      ...baseOptions,
      where: {
        ...baseOptions.where,
        [searchColumn]: {
          operator: 'LIKE',
          value: `%${searchTerm}%`,
        },
      },
    };
  }
}

/**
 * Example usage:
 *
 * // Simple select
 * const { sql, params } = SafeQueryBuilder.buildSelect({
 *   table: 'colors',
 *   select: ['id', 'name', 'hex'],
 *   where: { organization_id: orgId, deleted_at: null },
 *   orderBy: [{ column: 'name', direction: 'ASC' }],
 *   limit: 50
 * });
 *
 * // With helpers
 * const options = QueryHelpers.withOrganizationScope(
 *   QueryHelpers.withActiveRecords({
 *     table: 'colors',
 *     select: ['id', 'name', 'hex']
 *   }),
 *   organizationId
 * );
 * const { sql, params } = SafeQueryBuilder.buildSelect(options);
 */
