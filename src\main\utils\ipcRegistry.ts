/**
 * @file ipcRegistry.ts
 * @description Utility to track registered IPC handlers and prevent duplicate registrations
 * Now includes integrated rate limiting for security.
 */

import { IpcMainInvokeEvent } from 'electron';
import { applyChannelRateLimit } from '../security/rate-limit-middleware';

// Type for IPC handler functions
type IPCHandler = (event: IpcMainInvokeEvent, ...args: any[]) => any;

// Track registered IPC channels to prevent duplicate registrations
const registeredChannels = new Set<string>();

/**
 * Safely register an IPC handler, preventing duplicate registrations
 * @param channel The IPC channel name
 * @returns Boolean indicating if the channel is available for registration
 */
export function canRegisterHandler(channel: string): boolean {
  if (registeredChannels.has(channel)) {
    console.log(
      `IPC handler for '${channel}' already registered. Skipping duplicate registration.`
    );
    return false;
  }

  registeredChannels.add(channel);
  return true;
}

/**
 * Reset the registry and remove all tracked handlers from IPC
 * @param ipcMain The IPC main instance (optional, imported if not provided)
 */
export function resetRegistry(ipcMain?: any): void {
  if (!ipcMain) {
    // Import ipcMain if not provided
    const { ipcMain: electronIpcMain } = require('electron');
    ipcMain = electronIpcMain;
  }

  // Remove all tracked handlers from Electron's IPC system
  for (const channel of registeredChannels) {
    try {
      ipcMain.removeHandler(channel);
      console.log(`[IPC Registry] Removed handler for '${channel}'`);
    } catch {
      // Handler might not exist, which is fine
    }
  }

  // Clear our tracking
  registeredChannels.clear();
  console.log('[IPC Registry] Registry reset completed');
}

/**
 * Check if a handler is already registered
 * @param channel The IPC channel name
 * @returns Boolean indicating if the channel is already registered
 */
export function isHandlerRegistered(channel: string): boolean {
  return registeredChannels.has(channel);
}

/**
 * Get all registered channels (for debugging)
 * @returns Array of registered channel names
 */
export function getRegisteredChannels(): string[] {
  return Array.from(registeredChannels);
}

/**
 * Register an IPC handler safely without manual removeHandler calls
 * This replaces the old pattern of removeHandler() + canRegisterHandler() + ipcMain.handle()
 * @param ipcMain The IPC main instance
 * @param channel The channel name
 * @param handler The handler function
 * @param options Configuration options
 * @returns Boolean indicating success
 */
export function registerHandlerSafely(
  ipcMain: any,
  channel: string,
  handler: IPCHandler,
  options: {
    enableRateLimit?: boolean;
    rateLimitConfig?: any;
  } = {}
): boolean {
  // Remove existing handler if it exists (no need for try/catch)
  try {
    ipcMain.removeHandler(channel);
  } catch {
    // Handler doesn't exist, which is fine
  }

  // Remove from our tracking if it was there
  registeredChannels.delete(channel);

  // Check if we can register (this adds it to tracking)
  if (!canRegisterHandler(channel)) {
    return false;
  }

  // Apply rate limiting if enabled (default: true for security)
  let finalHandler: IPCHandler = handler;
  if (options.enableRateLimit !== false) {
    const rateLimitMiddleware = applyChannelRateLimit(channel);
    finalHandler = rateLimitMiddleware(handler) as IPCHandler;
  }

  // Register the new handler with rate limiting
  ipcMain.handle(channel, finalHandler);
  console.log(
    `[IPC Registry] Registered handler for '${channel}' with rate limiting: ${options.enableRateLimit !== false}`
  );
  return true;
}

/**
 * Register an IPC handler with explicit rate limiting disabled
 * Use this for high-frequency operations that need custom rate limiting
 * @param ipcMain The IPC main instance
 * @param channel The channel name
 * @param handler The handler function
 * @returns Boolean indicating success
 */
export function registerHandlerWithoutRateLimit(
  ipcMain: any,
  channel: string,
  handler: IPCHandler
): boolean {
  return registerHandlerSafely(ipcMain, channel, handler, {
    enableRateLimit: false,
  });
}

/**
 * Unregister a handler and remove it from tracking
 * @param ipcMain The IPC main instance
 * @param channel The channel name
 */
export function unregisterHandler(ipcMain: any, channel: string): void {
  try {
    ipcMain.removeHandler(channel);
  } catch {
    // Handler doesn't exist, which is fine
  }
  registeredChannels.delete(channel);
}
