/**
 * @file ipc-error-handling.ts
 * @description Comprehensive error handling and validation utilities for IPC layer
 * 
 * This utility provides:
 * - Standardized error response creation
 * - Input validation helpers
 * - Error type classification and handling
 * - User-friendly error message generation
 * - Logging and debugging support
 * - Organization context validation
 * - Authentication validation
 * - Business rule validation helpers
 */

import { IPCResponse, IPCHandlerOptions } from '../../../shared/types/ipc.types';
import { getCurrentOrganization } from '../../utils/organization-context';
import { getOAuthService } from '../../services/service-locator';
import { AppError, InvalidHexCodeError, InvalidCMYKError } from '../../../shared/types/errors';

// ============================================================================
// ERROR CLASSIFICATION AND TYPES
// ============================================================================

/**
 * Categories of errors for classification and handling
 */
export enum ErrorCategory {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  ORGANIZATION = 'organization',
  DATABASE = 'database',
  NETWORK = 'network',
  BUSINESS_RULE = 'business_rule',
  SYSTEM = 'system',
  UNKNOWN = 'unknown'
}

/**
 * Error severity levels for logging and user notification
 */
export enum ErrorSeverity {
  LOW = 'low',           // Minor validation errors, user recoverable
  MEDIUM = 'medium',     // Business rule violations, requires user action
  HIGH = 'high',         // System errors, authentication failures
  CRITICAL = 'critical'  // Database failures, system crashes
}

/**
 * Extended error information for comprehensive error handling
 */
export interface IPCError {
  category: ErrorCategory;
  severity: ErrorSeverity;
  code: string;
  technicalMessage: string;
  userMessage: string;
  details?: Record<string, any>;
  timestamp: number;
  stackTrace?: string;
}

/**
 * Validation result for input validation
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Individual validation error
 */
export interface ValidationError {
  field: string;
  code: string;
  message: string;
  value?: any;
}

// ============================================================================
// ERROR RESPONSE CREATION UTILITIES
// ============================================================================

/**
 * Creates a standardized success response
 */
export function createSuccessResponse<T>(data: T, userMessage?: string): IPCResponse<T> {
  return {
    success: true,
    data,
    userMessage,
    timestamp: Date.now()
  };
}

/**
 * Creates a standardized error response from an IPCError
 */
export function createErrorResponse<T = any>(error: IPCError): IPCResponse<T> {
  return {
    success: false,
    // data is omitted (undefined) for error responses
    error: error.technicalMessage,
    userMessage: error.userMessage,
    timestamp: error.timestamp
  };
}

/**
 * Creates a standardized error response from a generic error
 */
export function createErrorResponseFromError<T = any>(
  error: unknown,
  _defaultUserMessage: string = 'An unexpected error occurred',
  context?: string
): IPCResponse<T> {
  const ipcError = classifyError(error, context);
  return createErrorResponse<T>(ipcError);
}

/**
 * Creates a validation error response
 */
export function createValidationErrorResponse<T = any>(
  validationResult: ValidationResult,
  userMessage: string = 'Please check your input and try again'
): IPCResponse<T> {
  const error: IPCError = {
    category: ErrorCategory.VALIDATION,
    severity: ErrorSeverity.LOW,
    code: 'VALIDATION_FAILED',
    technicalMessage: `Validation failed: ${validationResult.errors.map(e => `${e.field}: ${e.message}`).join(', ')}`,
    userMessage,
    details: { validationErrors: validationResult.errors },
    timestamp: Date.now()
  };

  return createErrorResponse<T>(error);
}

// ============================================================================
// ERROR CLASSIFICATION AND HANDLING
// ============================================================================

/**
 * Classifies an unknown error into an IPCError with proper categorization
 */
export function classifyError(error: unknown, context?: string): IPCError {
  const timestamp = Date.now();
  let stackTrace: string | undefined;

  // Extract stack trace if available
  if (error instanceof Error && error.stack) {
    stackTrace = error.stack;
  }

  // Handle known application errors
  if (error instanceof InvalidHexCodeError || error instanceof InvalidCMYKError) {
    return {
      category: ErrorCategory.VALIDATION,
      severity: ErrorSeverity.LOW,
      code: 'INVALID_COLOR_FORMAT',
      technicalMessage: error.message,
      userMessage: 'Please check the color format and try again.',
      details: { originalError: error },
      timestamp,
      stackTrace
    };
  }

  if (error instanceof AppError) {
    return {
      category: ErrorCategory.BUSINESS_RULE,
      severity: ErrorSeverity.MEDIUM,
      code: 'BUSINESS_RULE_VIOLATION',
      technicalMessage: error.message,
      userMessage: 'The operation could not be completed due to business rules.',
      details: { originalError: error },
      timestamp,
      stackTrace
    };
  }

  // Handle standard Error instances
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    // Database errors
    if (message.includes('database') || message.includes('sqlite') || message.includes('sql')) {
      return {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.HIGH,
        code: 'DATABASE_ERROR',
        technicalMessage: error.message,
        userMessage: 'A database error occurred. Please try again.',
        details: { context },
        timestamp,
        stackTrace
      };
    }

    // Network/connectivity errors
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return {
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        code: 'NETWORK_ERROR',
        technicalMessage: error.message,
        userMessage: 'Network connection error. Please check your internet connection and try again.',
        details: { context },
        timestamp,
        stackTrace
      };
    }

    // Authentication errors
    if (message.includes('auth') || message.includes('login') || message.includes('token')) {
      return {
        category: ErrorCategory.AUTHENTICATION,
        severity: ErrorSeverity.HIGH,
        code: 'AUTHENTICATION_ERROR',
        technicalMessage: error.message,
        userMessage: 'Authentication failed. Please log in again.',
        details: { context },
        timestamp,
        stackTrace
      };
    }

    // Organization context errors
    if (message.includes('organization') || message.includes('org')) {
      return {
        category: ErrorCategory.ORGANIZATION,
        severity: ErrorSeverity.MEDIUM,
        code: 'ORGANIZATION_ERROR',
        technicalMessage: error.message,
        userMessage: 'Organization context error. Please select an organization.',
        details: { context },
        timestamp,
        stackTrace
      };
    }

    // Generic error
    return {
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.MEDIUM,
      code: 'SYSTEM_ERROR',
      technicalMessage: error.message,
      userMessage: 'An error occurred. Please try again.',
      details: { context },
      timestamp,
      stackTrace
    };
  }

  // Handle non-Error objects
  const errorString = typeof error === 'string' ? error : JSON.stringify(error);
  return {
    category: ErrorCategory.UNKNOWN,
    severity: ErrorSeverity.MEDIUM,
    code: 'UNKNOWN_ERROR',
    technicalMessage: `Unknown error: ${errorString}`,
    userMessage: 'An unexpected error occurred. Please try again.',
    details: { context, originalError: error },
    timestamp
  };
}

/**
 * Logs an error with appropriate detail level based on severity
 */
export function logError(error: IPCError, channel?: string): void {
  const prefix = channel ? `[IPC:${channel}]` : '[IPC]';
  const logMessage = `${prefix} ${error.category.toUpperCase()}: ${error.technicalMessage}`;

  switch (error.severity) {
    case ErrorSeverity.CRITICAL:
      console.error(logMessage, {
        code: error.code,
        details: error.details,
        stackTrace: error.stackTrace
      });
      break;
    case ErrorSeverity.HIGH:
      console.error(logMessage, {
        code: error.code,
        details: error.details
      });
      break;
    case ErrorSeverity.MEDIUM:
      console.warn(logMessage, { code: error.code });
      break;
    case ErrorSeverity.LOW:
      console.log(logMessage);
      break;
  }
}

// ============================================================================
// INPUT VALIDATION UTILITIES
// ============================================================================

/**
 * Validates that required fields are present and non-empty
 */
export function validateRequiredFields<T extends Record<string, any>>(
  data: T,
  requiredFields: (keyof T)[]
): ValidationResult {
  const errors: ValidationError[] = [];

  for (const field of requiredFields) {
    const value = data[field];
    if (value === undefined || value === null || value === '') {
      errors.push({
        field: String(field),
        code: 'REQUIRED_FIELD_MISSING',
        message: `${String(field)} is required`,
        value
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates field types
 */
export function validateFieldTypes<T extends Record<string, any>>(
  data: T,
  typeMap: Record<keyof T, 'string' | 'number' | 'boolean' | 'object' | 'array'>
): ValidationResult {
  const errors: ValidationError[] = [];

  for (const [field, expectedType] of Object.entries(typeMap)) {
    const value = data[field];
    if (value !== undefined && value !== null) {
      const actualType = Array.isArray(value) ? 'array' : typeof value;
      if (actualType !== expectedType) {
        errors.push({
          field,
          code: 'INVALID_TYPE',
          message: `${field} must be of type ${expectedType}, got ${actualType}`,
          value
        });
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates string formats (email, hex color, etc.)
 */
export function validateStringFormats(
  data: Record<string, any>,
  formatMap: Record<string, 'email' | 'hex' | 'uuid' | 'url'>
): ValidationResult {
  const errors: ValidationError[] = [];

  const validators = {
    email: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    hex: (value: string) => /^#[0-9A-Fa-f]{6}$/.test(value),
    uuid: (value: string) => /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value),
    url: (value: string) => {
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }
  };

  for (const [field, format] of Object.entries(formatMap)) {
    const value = data[field];
    if (value && typeof value === 'string') {
      const validator = validators[format];
      if (!validator(value)) {
        errors.push({
          field,
          code: 'INVALID_FORMAT',
          message: `${field} must be a valid ${format}`,
          value
        });
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates numeric ranges
 */
export function validateNumericRanges(
  data: Record<string, any>,
  rangeMap: Record<string, { min?: number; max?: number }>
): ValidationResult {
  const errors: ValidationError[] = [];

  for (const [field, range] of Object.entries(rangeMap)) {
    const value = data[field];
    if (typeof value === 'number') {
      if (range.min !== undefined && value < range.min) {
        errors.push({
          field,
          code: 'VALUE_TOO_LOW',
          message: `${field} must be at least ${range.min}`,
          value
        });
      }
      if (range.max !== undefined && value > range.max) {
        errors.push({
          field,
          code: 'VALUE_TOO_HIGH',
          message: `${field} must be at most ${range.max}`,
          value
        });
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Combines multiple validation results
 */
export function combineValidationResults(...results: ValidationResult[]): ValidationResult {
  const allErrors = results.flatMap(result => result.errors);
  return {
    isValid: allErrors.length === 0,
    errors: allErrors
  };
}

// ============================================================================
// CONTEXT VALIDATION UTILITIES
// ============================================================================

/**
 * Validates organization context is present
 */
export async function validateOrganizationContext(): Promise<ValidationResult> {
  try {
    const orgId = getCurrentOrganization();
    if (!orgId) {
      return {
        isValid: false,
        errors: [{
          field: 'organizationId',
          code: 'ORGANIZATION_REQUIRED',
          message: 'No organization selected. Please select an organization first.'
        }]
      };
    }

    return {
      isValid: true,
      errors: []
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [{
        field: 'organizationId',
        code: 'ORGANIZATION_CONTEXT_ERROR',
        message: 'Failed to validate organization context'
      }]
    };
  }
}

/**
 * Validates authentication context is present
 */
export async function validateAuthenticationContext(): Promise<ValidationResult> {
  try {
    const oauthService = getOAuthService();
    const user = await oauthService.getCurrentUser();
    
    if (!user || !user.id) {
      return {
        isValid: false,
        errors: [{
          field: 'authentication',
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required. Please log in.'
        }]
      };
    }

    return {
      isValid: true,
      errors: []
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [{
        field: 'authentication',
        code: 'AUTHENTICATION_CHECK_FAILED',
        message: 'Failed to validate authentication'
      }]
    };
  }
}

/**
 * Validates both organization and authentication context
 */
export async function validateFullContext(): Promise<ValidationResult> {
  const authResult = await validateAuthenticationContext();
  const orgResult = await validateOrganizationContext();
  
  return combineValidationResults(authResult, orgResult);
}

// ============================================================================
// BUSINESS RULE VALIDATION HELPERS
// ============================================================================

/**
 * Validates that a resource exists (generic helper)
 */
export function validateResourceExists(
  resourceName: string,
  resourceId: string,
  exists: boolean
): ValidationResult {
  if (!exists) {
    return {
      isValid: false,
      errors: [{
        field: 'resourceId',
        code: 'RESOURCE_NOT_FOUND',
        message: `${resourceName} with ID ${resourceId} not found`,
        value: resourceId
      }]
    };
  }

  return {
    isValid: true,
    errors: []
  };
}

/**
 * Validates that a resource doesn't already exist (for creation operations)
 */
export function validateResourceDoesNotExist(
  resourceName: string,
  identifier: string,
  exists: boolean
): ValidationResult {
  if (exists) {
    return {
      isValid: false,
      errors: [{
        field: 'identifier',
        code: 'RESOURCE_ALREADY_EXISTS',
        message: `${resourceName} with identifier ${identifier} already exists`,
        value: identifier
      }]
    };
  }

  return {
    isValid: true,
    errors: []
  };
}

/**
 * Validates permissions for an operation
 */
export function validatePermissions(
  operation: string,
  hasPermission: boolean,
  requiredRole?: string
): ValidationResult {
  if (!hasPermission) {
    const message = requiredRole 
      ? `Insufficient permissions. ${requiredRole} role required for ${operation}`
      : `Insufficient permissions for ${operation}`;
    
    return {
      isValid: false,
      errors: [{
        field: 'permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        message,
        value: { operation, requiredRole }
      }]
    };
  }

  return {
    isValid: true,
    errors: []
  };
}

// ============================================================================
// IPC HANDLER WRAPPER UTILITIES
// ============================================================================

/**
 * Wraps an IPC handler with comprehensive error handling and validation
 */
export function withErrorHandling<TArgs extends any[], TResult>(
  handler: (...args: TArgs) => Promise<TResult> | TResult,
  options: IPCHandlerOptions & {
    channel?: string;
    skipValidation?: boolean;
  } = {}
) {
  return async (event: any, ...args: TArgs): Promise<IPCResponse<TResult>> => {
    const startTime = Date.now();
    const channel = options.channel || 'unknown';

    try {
      // Validate context if required
      if (!options.skipValidation) {
        // Check authentication if required
        if (options.requireAuth !== false) {
          const authValidation = await validateAuthenticationContext();
          if (!authValidation.isValid) {
            const response = createValidationErrorResponse<TResult>(
              authValidation,
              'Authentication required. Please log in.'
            );
            logError(classifyError(new Error('Authentication validation failed'), channel), channel);
            return response;
          }
        }

        // Check organization context if required
        if (options.requireOrganization !== false) {
          const orgValidation = await validateOrganizationContext();
          if (!orgValidation.isValid) {
            const response = createValidationErrorResponse<TResult>(
              orgValidation,
              'Please select an organization first.'
            );
            logError(classifyError(new Error('Organization validation failed'), channel), channel);
            return response;
          }
        }
      }

      // Execute the handler
      const result = await handler(...args);
      
      // Log successful operations for debugging
      const duration = Date.now() - startTime;
      if (options.logChannel !== false) {
        console.log(`[IPC:${channel}] ✅ Success (${duration}ms)`);
      }

      return createSuccessResponse(result);
    } catch (error) {
      // Classify and log the error
      const ipcError = classifyError(error, channel);
      logError(ipcError, channel);

      // Create error response with custom message if provided
      const userMessage = options.customErrorMessage || ipcError.userMessage;
      const errorResponse = createErrorResponse({
        ...ipcError,
        userMessage
      });

      return errorResponse;
    }
  };
}

/**
 * Creates a validation-focused wrapper for handlers that need input validation
 */
export function withValidation<TArgs extends any[], TResult>(
  handler: (...args: TArgs) => Promise<TResult> | TResult,
  validator: (...args: TArgs) => ValidationResult | Promise<ValidationResult>,
  options: IPCHandlerOptions & { channel?: string } = {}
) {
  return withErrorHandling(async (...args: TArgs) => {
    // Run validation
    const validationResult = await validator(...args);
    if (!validationResult.isValid) {
      throw new Error(`Validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`);
    }

    // Execute handler if validation passes
    return handler(...args);
  }, options);
}

// ============================================================================
// UTILITY EXPORTS
// ============================================================================

/**
 * Quick validation helpers for common patterns
 */
export const validate = {
  required: validateRequiredFields,
  types: validateFieldTypes,
  formats: validateStringFormats,
  ranges: validateNumericRanges,
  combine: combineValidationResults,
  orgContext: validateOrganizationContext,
  authContext: validateAuthenticationContext,
  fullContext: validateFullContext,
  resourceExists: validateResourceExists,
  resourceDoesNotExist: validateResourceDoesNotExist,
  permissions: validatePermissions
};

/**
 * Quick response helpers
 */
export const respond = {
  success: createSuccessResponse,
  error: createErrorResponse,
  fromError: createErrorResponseFromError,
  validationError: createValidationErrorResponse
};

/**
 * Quick wrapper helpers
 */
export const wrap = {
  withErrorHandling,
  withValidation
};