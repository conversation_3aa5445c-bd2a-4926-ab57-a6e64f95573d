import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { UserSyncService } from './user-sync.service';
import { OrganizationRepository } from '../../db/repositories/organization.repository';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from 'better-sqlite3';

// Mock dependencies
vi.mock('../../db/repositories/organization.repository');
vi.mock('@supabase/supabase-js');

describe('UserSyncService', () => {
  let userSyncService: UserSyncService;
  let mockOrganizationRepository: vi.Mocked<OrganizationRepository>;
  let mockSupabaseClient: vi.Mocked<SupabaseClient>;
  let mockDatabase: vi.Mocked<Database>;

  beforeEach(() => {
    mockDatabase = {
      prepare: vi.fn(),
      exec: vi.fn(),
      transaction: vi.fn(),
    } as any;

    mockOrganizationRepository = {
      getUserById: vi.fn(),
      updateUser: vi.fn(),
      createUser: vi.fn(),
      getUsersByOrganization: vi.fn(),
    } as any;

    mockSupabaseClient = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      upsert: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      single: vi.fn(),
    } as any;

    userSyncService = new UserSyncService(
      mockDatabase,
      mockOrganizationRepository,
      mockSupabaseClient
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('syncUserProfileToLocal', () => {
    it('should sync user profile from Supabase to local database', async () => {
      const userId = 'user-123';
      const supabaseUser = {
        id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        avatar_url: 'https://example.com/avatar.jpg',
        updated_at: '2024-01-01T00:00:00Z'
      };

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: supabaseUser,
              error: null
            })
          })
        })
      } as any);

      mockOrganizationRepository.getUserById.mockResolvedValue(null);
      mockOrganizationRepository.createUser.mockResolvedValue(undefined);

      const result = await userSyncService.syncUserProfileToLocal(userId);

      expect(result.success).toBe(true);
      expect(result.action).toBe('created');
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('users');
      expect(mockOrganizationRepository.createUser).toHaveBeenCalledWith({
        external_id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        avatar_url: 'https://example.com/avatar.jpg',
        updated_at: '2024-01-01T00:00:00Z'
      });
    });

    it('should update existing user profile', async () => {
      const userId = 'user-123';
      const supabaseUser = {
        id: userId,
        email: '<EMAIL>',
        name: 'Updated User',
        avatar_url: 'https://example.com/new-avatar.jpg',
        updated_at: '2024-01-02T00:00:00Z'
      };

      const existingUser = {
        external_id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        updated_at: '2024-01-01T00:00:00Z'
      };

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: supabaseUser,
              error: null
            })
          })
        })
      } as any);

      mockOrganizationRepository.getUserById.mockResolvedValue(existingUser);
      mockOrganizationRepository.updateUser.mockResolvedValue(undefined);

      const result = await userSyncService.syncUserProfileToLocal(userId);

      expect(result.success).toBe(true);
      expect(result.action).toBe('updated');
      expect(mockOrganizationRepository.updateUser).toHaveBeenCalledWith(userId, {
        email: '<EMAIL>',
        name: 'Updated User',
        avatar_url: 'https://example.com/new-avatar.jpg',
        updated_at: '2024-01-02T00:00:00Z'
      });
    });

    it('should skip sync if local user is newer', async () => {
      const userId = 'user-123';
      const supabaseUser = {
        id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const existingUser = {
        external_id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        updated_at: '2024-01-02T00:00:00Z' // newer than Supabase
      };

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: supabaseUser,
              error: null
            })
          })
        })
      } as any);

      mockOrganizationRepository.getUserById.mockResolvedValue(existingUser);

      const result = await userSyncService.syncUserProfileToLocal(userId);

      expect(result.success).toBe(true);
      expect(result.action).toBe('skipped');
      expect(result.reason).toBe('Local user is newer');
      expect(mockOrganizationRepository.updateUser).not.toHaveBeenCalled();
      expect(mockOrganizationRepository.createUser).not.toHaveBeenCalled();
    });

    it('should handle Supabase errors', async () => {
      const userId = 'user-123';

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'User not found' }
            })
          })
        })
      } as any);

      const result = await userSyncService.syncUserProfileToLocal(userId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch user from Supabase: User not found');
    });
  });

  describe('syncMembersFromSupabase', () => {
    it('should sync all organization members from Supabase', async () => {
      const organizationId = 'org-123';
      const members = [
        { user_id: 'user-1', role: 'owner' },
        { user_id: 'user-2', role: 'admin' },
        { user_id: 'user-3', role: 'member' }
      ];

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            data: members,
            error: null
          })
        })
      } as any);

      // Mock successful user syncs
      vi.spyOn(userSyncService, 'syncUserProfileToLocal')
        .mockResolvedValueOnce({ success: true, action: 'updated' })
        .mockResolvedValueOnce({ success: true, action: 'created' })
        .mockResolvedValueOnce({ success: true, action: 'skipped', reason: 'Local user is newer' });

      const result = await userSyncService.syncMembersFromSupabase(organizationId);

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(3);
      expect(result.results).toHaveLength(3);
      expect(userSyncService.syncUserProfileToLocal).toHaveBeenCalledTimes(3);
    });

    it('should handle partial failures during member sync', async () => {
      const organizationId = 'org-123';
      const members = [
        { user_id: 'user-1', role: 'owner' },
        { user_id: 'user-2', role: 'admin' }
      ];

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            data: members,
            error: null
          })
        })
      } as any);

      // Mock one successful and one failed sync
      vi.spyOn(userSyncService, 'syncUserProfileToLocal')
        .mockResolvedValueOnce({ success: true, action: 'updated' })
        .mockResolvedValueOnce({ success: false, error: 'Network error' });

      const result = await userSyncService.syncMembersFromSupabase(organizationId);

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(1);
      expect(result.errorCount).toBe(1);
      expect(result.errors).toContain('user-2: Network error');
    });
  });

  describe('syncOrganizationsFromSupabase', () => {
    it('should sync user organizations from Supabase', async () => {
      const userId = 'user-123';
      const organizations = [
        {
          organization_id: 'org-1',
          role: 'owner',
          organizations: {
            id: 'org-1',
            name: 'Test Org 1',
            settings: {}
          }
        },
        {
          organization_id: 'org-2',
          role: 'member',
          organizations: {
            id: 'org-2',
            name: 'Test Org 2',
            settings: {}
          }
        }
      ];

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            data: organizations,
            error: null
          })
        })
      } as any);

      mockOrganizationRepository.createUser.mockResolvedValue(undefined);

      const result = await userSyncService.syncOrganizationsFromSupabase(userId);

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(2);
      expect(result.organizations).toHaveLength(2);
      expect(result.organizations[0]).toEqual({
        id: 'org-1',
        name: 'Test Org 1',
        role: 'owner',
        settings: {}
      });
    });

    it('should handle Supabase errors during organization sync', async () => {
      const userId = 'user-123';

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            data: null,
            error: { message: 'Database connection failed' }
          })
        })
      } as any);

      const result = await userSyncService.syncOrganizationsFromSupabase(userId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch organizations from Supabase: Database connection failed');
    });
  });

  describe('syncUserProfileToSupabase', () => {
    it('should sync local user profile to Supabase', async () => {
      const userId = 'user-123';
      const localUser = {
        external_id: userId,
        email: '<EMAIL>',
        name: 'Test User',
        avatar_url: 'https://example.com/avatar.jpg',
        updated_at: '2024-01-01T00:00:00Z'
      };

      mockOrganizationRepository.getUserById.mockResolvedValue(localUser);

      mockSupabaseClient.from.mockReturnValue({
        upsert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { id: userId },
              error: null
            })
          })
        })
      } as any);

      const result = await userSyncService.syncUserProfileToSupabase(userId);

      expect(result.success).toBe(true);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('users');
    });

    it('should handle missing local user', async () => {
      const userId = 'user-123';

      mockOrganizationRepository.getUserById.mockResolvedValue(null);

      const result = await userSyncService.syncUserProfileToSupabase(userId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('User not found in local database');
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle database connection errors', async () => {
      const userId = 'user-123';
      
      // Mock Supabase to return valid data first
      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: {
                id: userId,
                email: '<EMAIL>',
                name: 'Test User',
                updated_at: '2024-01-01T00:00:00Z'
              },
              error: null
            })
          })
        })
      } as any);

      // Then make the repository throw an error
      mockOrganizationRepository.getUserById.mockRejectedValue(new Error('Database connection lost'));

      const result = await userSyncService.syncUserProfileToLocal(userId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database connection lost');
    });

    it('should handle invalid user data', async () => {
      const userId = 'user-123';
      const invalidUser = {
        id: userId,
        // missing required fields
      };

      mockSupabaseClient.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: invalidUser,
              error: null
            })
          })
        })
      } as any);

      const result = await userSyncService.syncUserProfileToLocal(userId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid user data');
    });
  });
});