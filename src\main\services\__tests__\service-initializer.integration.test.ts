/**
 * @file service-initializer.integration.test.ts
 * @description Integration tests for service initialization with real dependencies
 */

import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
    createServiceConfig,
    initializeSentry
} from '../service-initializer';

// Only mock external dependencies that we can't control in tests
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/test/userData'),
  },
}));

// Mock database since we don't want to create real DB in tests
vi.mock('../../db/database', () => ({
  initDatabase: vi.fn().mockResolvedValue({
    prepare: vi.fn(() => ({
      get: vi.fn(() => ({ test: 1 })),
    })),
  }),
  getDatabase: vi.fn().mockReturnValue({
    prepare: vi.fn(() => ({
      get: vi.fn(() => ({ test: 1 })),
    })),
  }),
}));

// Mock external services that require network/credentials
vi.mock('../sentry.service', () => ({
  sentryService: {
    initialize: vi.fn().mockResolvedValue(undefined),
  },
  SentryService: {
    isGloballyInitialized: vi.fn(() => false),
    getInitializationAttempts: vi.fn(() => 0),
  },
}));

vi.mock('../service-locator', () => ({
  ServiceLocator: {
    initialize: vi.fn().mockResolvedValue(undefined),
  },
  getZohoEmailService: vi.fn(() => ({
    initialize: vi.fn().mockResolvedValue(undefined),
  })),
}));

vi.mock('../../shared-folder', () => ({
  SharedFolderManager: vi.fn().mockImplementation(() => ({
    initialize: vi.fn(),
  })),
}));

vi.mock('../../ipc/shared-folder-ipc', () => ({
  setupSharedFolderIPC: vi.fn(),
}));

vi.mock('../../utils/logger', () => ({
  serviceLogger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('Service Initializer - Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Set up clean environment
    process.env.ZOHO_CLIENT_ID = 'test-client-id';
    process.env.ZOHO_CLIENT_SECRET = 'test-client-secret';
    process.env.SUPABASE_URL = 'https://test.supabase.co';
    process.env.SUPABASE_ANON_KEY = 'test-anon-key';
  });

  describe('Service Configuration Integration', () => {
    it('should create valid configuration that works with service initialization', () => {
      // Act
      const config = createServiceConfig();

      // Assert - Configuration should be valid for service initialization
      expect(config).toBeDefined();
      expect(config.zoho).toBeDefined();
      expect(config.supabase).toBeDefined();
      
      // Should have required structure for ServiceLocator
      expect(typeof config.zoho.region).toBe('string');
      expect(typeof config.zoho.supportAlias).toBe('string');
    });

    it('should handle missing credentials gracefully', () => {
      // Arrange - Remove credentials
      delete process.env.ZOHO_CLIENT_ID;
      delete process.env.SUPABASE_URL;

      // Act
      const config = createServiceConfig();

      // Assert - Should still create valid config
      expect(config).toBeDefined();
      expect(config.zoho.clientId).toBeUndefined();
      expect(config.supabase.url).toBeUndefined();
      
      // But defaults should still be present
      expect(config.zoho.region).toBe('US');
      expect(config.zoho.supportAlias).toBe('<EMAIL>');
    });
  });

  describe('Sentry Initialization Integration', () => {
    it('should initialize Sentry without throwing', async () => {
      // Act & Assert
      await expect(initializeSentry()).resolves.toBeUndefined();
    });

    it('should handle Sentry initialization errors gracefully', async () => {
      // Arrange
      const { sentryService } = await import('../sentry.service');
      vi.mocked(sentryService.initialize).mockRejectedValue(new Error('Sentry failed'));

      // Act & Assert - Should not throw
      await expect(initializeSentry()).resolves.toBeUndefined();
    });

    it('should respect singleton pattern', async () => {
      // Arrange
      const { SentryService } = await import('../sentry.service');
      vi.mocked(SentryService.isGloballyInitialized).mockReturnValue(true);

      // Act
      await initializeSentry();

      // Assert - Should skip initialization
      const { sentryService } = await import('../sentry.service');
      expect(sentryService.initialize).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling Integration', () => {
    it('should provide meaningful error information', async () => {
      // This test verifies that our error handling provides useful information
      // without testing implementation details
      
      const config = createServiceConfig();
      expect(config).toBeDefined();
      
      // Configuration should be serializable for error reporting
      expect(() => JSON.stringify(config)).not.toThrow();
    });

    it('should handle partial service failures gracefully', async () => {
      // Arrange - Make one service fail
      const { sentryService } = await import('../sentry.service');
      vi.mocked(sentryService.initialize).mockRejectedValue(new Error('Service unavailable'));

      // Act - Should not throw even if one service fails
      await expect(initializeSentry()).resolves.toBeUndefined();
    });
  });

  describe('Environment Integration', () => {
    it('should work with different environment configurations', () => {
      const testCases = [
        { env: 'development', region: 'US' },
        { env: 'production', region: 'EU' },
        { env: 'test', region: 'US' },
      ];

      testCases.forEach(({ env, region }) => {
        // Arrange
        process.env.NODE_ENV = env;
        process.env.ZOHO_REGION = region;

        // Act
        const config = createServiceConfig();

        // Assert
        expect(config.zoho.region).toBe(region);
      });
    });

    it('should handle missing NODE_ENV gracefully', () => {
      // Arrange
      delete process.env.NODE_ENV;

      // Act & Assert
      expect(() => createServiceConfig()).not.toThrow();
    });
  });

  describe('Type Safety Integration', () => {
    it('should maintain type safety across service boundaries', () => {
      // Act
      const config = createServiceConfig();

      // Assert - These should compile without TypeScript errors
      const zohoConfig = config.zoho;
      const supabaseConfig = config.supabase;

      expect(zohoConfig).toBeDefined();
      expect(supabaseConfig).toBeDefined();

      // Type guards should work
      if (zohoConfig.clientId) {
        expect(typeof zohoConfig.clientId).toBe('string');
      }

      if (supabaseConfig.url) {
        expect(typeof supabaseConfig.url).toBe('string');
      }
    });
  });
});