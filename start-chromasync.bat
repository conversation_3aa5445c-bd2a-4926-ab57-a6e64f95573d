@echo off
echo Starting ChromaSync...
echo.

cd /d "C:\Projects\Applications\chromasync-mac"

echo Cleaning electron installation...
rmdir /s /q node_modules\electron 2>nul

echo Installing Windows Electron...
set npm_config_target_platform=win32
set npm_config_target_arch=x64
set npm_config_disturl=https://electronjs.org/headers
set npm_config_runtime=electron
set npm_config_cache=C:\temp\.npm
set npm_config_build_from_source=true
npm install electron --save-dev

echo Installing other dependencies...
npm install

echo.
echo Building and starting ChromaSync...
npm run start:windows

pause