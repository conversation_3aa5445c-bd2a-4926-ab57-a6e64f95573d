/**
 * @file ModalTriggers.tsx
 * @description Example component showing how to use the centralized modal system
 */

import React from 'react';
import { useAppModals } from '../../hooks/useModals';

interface ModalTriggersProps {
  className?: string;
}

/**
 * Example component demonstrating modal usage patterns
 */
export const ModalTriggers: React.FC<ModalTriggersProps> = ({ className }) => {
  const modals = useAppModals();

  const handleOpenSettings = () => {
    modals.settings.open();
  };

  const handleOpenColorForm = () => {
    modals.colorForm.openForCreate(() => {
      console.log('Color created successfully!');
    });
  };

  const handleOpenHelp = () => {
    modals.help.open();
  };

  const handleOpenGradientPicker = () => {
    modals.gradientPicker.openWithColor('#ff0000', selectedColor => {
      console.log('Selected color:', selectedColor);
    });
  };

  const handleOpenLicense = () => {
    modals.license.openWithStatus({
      isValid: false,
      inTrialMode: true,
      trialDaysRemaining: 7,
    });
  };

  const handleCloseAllModals = () => {
    modals.global.closeAll();
  };

  return (
    <div className={className}>
      <h3 className='text-lg font-medium mb-4'>Modal Examples</h3>
      <div className='space-y-2'>
        <button
          onClick={handleOpenSettings}
          className='w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors'
        >
          Open Settings
        </button>

        <button
          onClick={handleOpenColorForm}
          className='w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors'
        >
          Create New Color
        </button>

        <button
          onClick={handleOpenHelp}
          className='w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors'
        >
          Open Help
        </button>

        <button
          onClick={handleOpenGradientPicker}
          className='w-full px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors'
        >
          Pick Color with Gradient
        </button>

        <button
          onClick={handleOpenLicense}
          className='w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors'
        >
          Show License Dialog
        </button>

        <button
          onClick={handleCloseAllModals}
          className='w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors'
        >
          Close All Modals
        </button>
      </div>
    </div>
  );
};

export default ModalTriggers;
