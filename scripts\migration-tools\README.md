# ChromaSync Migration Test Suite

A comprehensive testing framework for UUID migration validation, performance benchmarking, and system reliability testing.

## Overview

The Migration Test Suite provides robust testing capabilities for validating UUID migration processes in the ChromaSync application. It includes schema validation, data integrity testing, performance benchmarking, repository/service testing, and rollback validation.

## Features

- **🔍 Schema Validation**: Comprehensive analysis of database schema and structure
- **📊 Test Data Generation**: Automated generation of realistic test datasets
- **🔄 Migration Testing**: Validation of all UUID migration phases
- **🚀 Performance Benchmarking**: Comparison between integer and UUID operations
- **🏗️ Repository Testing**: CRUD operation validation for all data layers
- **🔧 Service Testing**: Business logic and service layer validation
- **🔄 Rollback Testing**: Migration rollback and recovery procedures
- **📋 Comprehensive Reporting**: Detailed test reports with metrics and analysis

## Quick Start

### Basic Usage

Run the complete test suite:
```bash
node migration-test-suite.js
```

Run with verbose logging:
```bash
node migration-test-suite.js --verbose
```

Run with specific test data size:
```bash
node migration-test-suite.js --size large --verbose
```

### Using the Test Runner

Run specific test phases:
```bash
node test-runner.js phases schema,migration,performance
```

Run performance comparison:
```bash
node test-runner.js performance small,medium,large --verbose
```

Run stress tests:
```bash
node test-runner.js stress
```

Validate test environment:
```bash
node test-runner.js validate
```

Generate execution plan:
```bash
node test-runner.js plan complete --test-data-size large
```

## Test Phases

### 1. Schema Validation (`schema`)
- Analyzes current database schema structure
- Validates foreign key relationships
- Checks index configurations
- Performs data integrity verification

### 2. Test Data Generation (`data-generation`)
- Creates realistic test datasets
- Generates organizations, products, colors, and relationships
- Configurable data sizes (small, medium, large)
- Ensures referential integrity

### 3. Migration Phase Testing (`migration`)
- **Phase 1**: Add UUID columns to existing tables
- **Phase 2**: Populate UUID columns with valid UUIDs
- **Phase 3**: Update foreign key references
- **Phase 4**: Switch to UUID primary keys
- **Phase 5**: Clean up integer columns

### 4. Performance Benchmarking (`performance`)
- Query performance comparison (integer vs UUID)
- Insert operation benchmarks
- Update operation benchmarks
- Delete operation benchmarks
- Join operation performance analysis

### 5. Repository Testing (`repository`)
- CRUD operations for all entities
- Cross-repository consistency validation
- Error handling and edge cases
- Data integrity maintenance

### 6. Service Testing (`service`)
- Business logic validation
- Service layer CRUD operations
- Concurrent operation handling
- Input validation and sanitization

### 7. Rollback Testing (`rollback`)
- Transaction rollback validation
- Migration rollback simulation
- Data recovery verification
- Backup and restore procedures

## Migration Tools

### Data Integrity Checker (`data-integrity-checker.js`)
Comprehensive data integrity validation tool that checks:
- Foreign key relationship consistency
- Orphaned records detection
- UUID format validation
- Duplicate external_ids identification
- Organization_id consistency
- Missing required fields
- Data type consistency
- Custom ChromaSync-specific checks

```bash
# Run integrity check
node data-integrity-checker.js

# Output includes detailed report and fix suggestions
```

### Schema Inspector (`schema-inspector.js`)
Database schema analysis tool for migration planning:
- Current primary key types analysis
- Foreign key relationship mapping
- Table dependency identification
- Migration recommendations
- Detailed schema documentation

```bash
# Analyze database schema
node schema-inspector.js

# Generates schema analysis report
```

### Backup and Rollback System (`backup-rollback.js`)
Production-ready backup and recovery system with:
- **Full database backups** with schema and data
- **Incremental backups** for specific tables/phases
- **Point-in-time recovery** with rollback capabilities
- **Backup validation** and integrity checks
- **Automated scheduling** and cleanup
- **Compression** and metadata tracking
- **Partial restore** options (schema-only, data-only, selective tables)

#### Backup Operations

```bash
# Create full backup
node backup-rollback.js create --type=full --name="pre-migration"

# Create incremental backup
node backup-rollback.js create --type=incremental --phase="after-schema-update" --tables="organizations,products"

# List available backups
node backup-rollback.js list --type=full --days=7 --details

# Validate backup integrity
node backup-rollback.js validate --backup="backup-2025-01-01T10-00-00.db.gz"
```

#### Restore Operations

```bash
# Full database restore
node backup-rollback.js restore --backup="backup-2025-01-01T10-00-00.db.gz"

# Schema-only restore
node backup-rollback.js restore --backup="backup.db.gz" --restore-type=schema-only

# Selective table restore
node backup-rollback.js restore --backup="backup.db.gz" --restore-type=selective --tables="organizations,products"

# Data-only restore
node backup-rollback.js restore --backup="backup.db.gz" --restore-type=data-only
```

#### Maintenance Operations

```bash
# Cleanup old backups
node backup-rollback.js cleanup --keep=5 --older-than=30 --dry-run

# Generate backup system report
node backup-rollback.js report

# Schedule automatic backups
node backup-rollback.js schedule --full-interval=86400 --incremental-interval=14400
```

### Safe Migration Integration (`backup-integration-example.js`)
Example integration showing how to use the backup system with migrations:
- **Pre-migration validation** with integrity checks
- **Checkpoint creation** between migration phases
- **Automatic rollback** on migration failures
- **Incremental backups** for affected tables
- **Post-migration validation** and reporting

```bash
# Run UUID migration example with automatic backups
node backup-integration-example.js uuid

# Run schema update example
node backup-integration-example.js schema

# Run data transformation with rollback handling
node backup-integration-example.js data
```

### Backup System Features

#### Backup Types
- **Full Backups**: Complete database copy with all schema and data
- **Incremental Backups**: Specific tables and schema for targeted restoration
- **Checkpoint Backups**: Migration phase snapshots for rollback points

#### Validation and Integrity
- **Checksum verification** for backup file integrity
- **Database structure validation** after restore
- **Automatic integrity checks** using SQLite PRAGMA commands
- **Metadata tracking** for backup history and relationships

#### Compression and Storage
- **Automatic compression** using gzip for space efficiency
- **Configurable retention policies** for automatic cleanup
- **Metadata files** with backup details and checksums
- **Backup history tracking** in JSON format

#### Rollback Capabilities
- **Point-in-time recovery** to any backup
- **Partial rollbacks** (schema-only, data-only, selective tables)
- **Automatic pre-restore backups** for safety
- **Validation before and after** restore operations

#### Production Safety
- **Read-only connections** for backup creation
- **Atomic operations** with cleanup on failure
- **Error handling and retry logic** for robustness
- **Comprehensive logging** for audit trails

### Migration Workflow Integration

The backup system integrates seamlessly with migration workflows:

1. **Pre-Migration Backup**: Create full backup before starting
2. **Phase Checkpoints**: Create incremental backups between migration phases
3. **Automatic Rollback**: Restore to checkpoint on failure
4. **Post-Migration Validation**: Verify integrity after completion
5. **Cleanup Management**: Automatic cleanup of old backups

```javascript
// Example integration in migration code
const { BackupManager } = require('./backup-rollback');

const backupManager = new BackupManager(dbPath);
await backupManager.connect();

// Create pre-migration backup
const preBackup = await backupManager.createFullBackup('pre-migration');

try {
  // Run migration phase 1
  await runMigrationPhase1();
  
  // Create checkpoint
  await backupManager.createCheckpoint('after-phase-1');
  
  // Run migration phase 2
  await runMigrationPhase2();
  
} catch (error) {
  // Automatic rollback to pre-migration state
  await backupManager.rollbackToBackup(preBackup);
  throw error;
}
```

## Configuration

### Test Data Sizes

```javascript
const TEST_CONFIG = {
  testDataSizes: {
    small: { 
      organizations: 2, 
      products: 10, 
      colors: 20, 
      productColors: 30 
    },
    medium: { 
      organizations: 5, 
      products: 100, 
      colors: 200, 
      productColors: 500 
    },
    large: { 
      organizations: 10, 
      products: 1000, 
      colors: 2000, 
      productColors: 5000 
    }
  }
};
```

### Performance Thresholds

```javascript
const performanceThresholds = {
  queryTimeMs: 100,
  insertTimeMs: 10,
  updateTimeMs: 10,
  deleteTimeMs: 10
};
```

## Command Line Options

### Migration Test Suite

| Option | Description | Default |
|--------|-------------|---------|
| `--verbose, -v` | Enable verbose logging | false |
| `--size, -s <size>` | Test data size (small, medium, large) | medium |
| `--source, -src <path>` | Source database path | auto-detected |
| `--help, -h` | Show help message | - |

### Test Runner

| Option | Description | Default |
|--------|-------------|---------|
| `--verbose, -v` | Enable verbose logging | false |
| `--output-dir <dir>` | Output directory for reports | current directory |
| `--test-data-size <size>` | Test data size | medium |
| `--parallel` | Enable parallel execution | false |
| `--baseline <path>` | Compare against baseline | - |

## Test Reports

### Report Structure

```json
{
  "testId": "1703875200000",
  "timestamp": "2023-12-29T10:00:00.000Z",
  "sourceDatabase": "/path/to/source.db",
  "testDatabase": "/path/to/test.db",
  "testDataSize": "medium",
  "phases": {
    "preparation": { "status": "completed", "duration": 120.5 },
    "schemaValidation": { "status": "completed", "duration": 234.1 },
    "dataGeneration": { "status": "completed", "duration": 567.8 },
    "migrationPhases": { "status": "completed", "duration": 1234.5 },
    "performanceBenchmarks": { "status": "completed", "duration": 2345.6 },
    "repositoryTests": { "status": "completed", "duration": 890.1 },
    "serviceTests": { "status": "completed", "duration": 456.7 },
    "rollbackTests": { "status": "completed", "duration": 123.4 }
  },
  "summary": {
    "totalDuration": 5972.7,
    "testsRun": 45,
    "testsPassed": 43,
    "testsFailed": 2,
    "criticalIssues": 1,
    "warningIssues": 3,
    "performanceIssues": 0,
    "overallStatus": "passed"
  }
}
```

### Performance Metrics

Each performance test includes:
- Integer operation timing
- UUID operation timing
- Performance ratio (UUID/Integer)
- Average time per operation
- Iteration count

### Report Files

Generated reports include:
- `migration-test-report-<timestamp>.json` - Complete test results
- `phase-test-<phases>-<timestamp>.json` - Phase-specific results
- `performance-comparison-<timestamp>.json` - Performance analysis
- `stress-test-<timestamp>.json` - Stress test results
- `execution-plan-<timestamp>.json` - Test execution plans

## Examples

### Complete Test Suite

```bash
# Run complete test suite with verbose logging
node migration-test-suite.js --verbose --size medium

# Expected output:
# 🚀 Initializing Migration Test Suite
# ✅ Connected to source database: /path/to/chromasync.db
# ✅ Created test database: /path/to/test_migration_1703875200000.db
# 🔍 Running Schema Validation Tests
# ...
# 📋 Migration Test Suite Final Report
# ====================================
# 📊 Test Summary:
#    Total Tests: 45
#    Passed: 43
#    Failed: 2
#    Overall Status: PASSED
```

### Phase-Specific Testing

```bash
# Test only schema validation and migration phases
node test-runner.js phases schema,migration --verbose

# Expected output:
# 🧪 Running Migration Test Phases: schema, migration
# 🔄 Running Phase: schema
# 🔍 Running Schema Validation Tests
# ...
# 🔄 Running Phase: migration
# 🔄 Running Migration Phase Tests
# ...
# 📊 Phase test report saved to: phase-test-schema-migration-<timestamp>.json
```

### Performance Comparison

```bash
# Compare performance across different data sizes
node test-runner.js performance small,medium,large

# Expected output:
# 🚀 Running Performance Comparison
# 📊 Testing configuration: small
# 📊 Testing configuration: medium
# 📊 Testing configuration: large
# 📊 Performance comparison saved to: performance-comparison-<timestamp>.json
```

### Stress Testing

```bash
# Run stress tests with increasing loads
node test-runner.js stress --verbose

# Expected output:
# 💪 Running Stress Tests
# 🔥 Running light stress test
# 🔥 Running moderate stress test
# 🔥 Running heavy stress test
# 🔥 Running extreme stress test
# 💪 Stress test results saved to: stress-test-<timestamp>.json
```

## Integration

### CI/CD Pipeline Integration

```yaml
# Example GitHub Actions workflow
name: Migration Test Suite
on: [push, pull_request]

jobs:
  migration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: node scripts/migration-tools/test-runner.js validate
      - run: node scripts/migration-tools/migration-test-suite.js --size small
      - run: node scripts/migration-tools/test-runner.js performance small,medium
```

### Pre-Migration Validation

```bash
#!/bin/bash
# pre-migration-check.sh

echo "🔍 Validating environment..."
node scripts/migration-tools/test-runner.js validate

if [ $? -eq 0 ]; then
    echo "✅ Environment validation passed"
    
    echo "🧪 Running migration tests..."
    node scripts/migration-tools/migration-test-suite.js --size medium
    
    if [ $? -eq 0 ]; then
        echo "✅ Migration tests passed - Ready for production migration"
        exit 0
    else
        echo "❌ Migration tests failed - Do not proceed with migration"
        exit 1
    fi
else
    echo "❌ Environment validation failed"
    exit 1
fi
```

## Troubleshooting

### Common Issues

#### Memory Issues
```bash
# Increase Node.js memory limit
node --max-old-space-size=4096 migration-test-suite.js --size large
```

#### Permission Issues
```bash
# Ensure write permissions for test output
chmod 755 scripts/migration-tools/
```

#### Database Lock Issues
```bash
# Ensure source database is not in use
lsof /path/to/chromasync.db
```

### Debug Mode

Enable verbose logging for detailed debugging:
```bash
node migration-test-suite.js --verbose --size small
```

### Test Isolation

Each test run creates an isolated test database:
- Source database is copied to temporary test database
- All operations are performed on test database
- Test database is automatically cleaned up after tests

## Performance Expectations

### Typical Results

With medium test data size:
- Schema validation: ~5 seconds
- Data generation: ~10 seconds
- Migration phases: ~15 seconds
- Performance benchmarks: ~30 seconds
- Repository tests: ~20 seconds
- Service tests: ~25 seconds
- Rollback tests: ~10 seconds

**Total expected runtime: ~2 minutes**

### Performance Ratios

Typical UUID vs Integer performance ratios:
- **Queries**: 1.2x - 1.8x slower
- **Inserts**: 1.1x - 1.5x slower
- **Updates**: 1.2x - 1.6x slower
- **Deletes**: 1.1x - 1.4x slower
- **Joins**: 1.5x - 2.5x slower

### Optimization Tips

1. **Indexes**: Ensure proper indexing on UUID columns
2. **UUID Format**: Consider using binary UUID storage
3. **Batch Operations**: Use transactions for bulk operations
4. **Connection Pooling**: Implement connection pooling for concurrent operations

## Contributing

### Adding New Tests

1. **Create test function** in `MigrationTestSuite` class
2. **Add to appropriate phase** (schema, migration, performance, etc.)
3. **Update test report structure** if needed
4. **Add CLI options** if required
5. **Update documentation**

### Test Function Template

```javascript
async testNewFeature() {
  const startTime = performance.now();
  
  try {
    // Test implementation
    const result = await this.performTest();
    
    const duration = performance.now() - startTime;
    
    return {
      test: 'New Feature Test',
      status: 'passed',
      duration: duration,
      message: 'Test completed successfully',
      details: {
        // Test-specific details
      }
    };
    
  } catch (error) {
    const duration = performance.now() - startTime;
    return {
      test: 'New Feature Test',
      status: 'failed',
      duration: duration,
      error: error.message
    };
  }
}
```

## License

This migration test suite is part of the ChromaSync project and follows the same licensing terms.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review test reports for detailed error information
3. Enable verbose logging for additional debugging
4. Contact the development team with detailed error information

---

**Note**: Always run tests on a copy of your production database. The test suite creates isolated test databases, but it's recommended to backup your data before running any migration testing.