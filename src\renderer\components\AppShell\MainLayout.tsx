/**
 * @file MainLayout.tsx
 * @description Main application layout with header, controls, and content area
 */

import React from 'react';
import { UnifiedSyncIndicator } from '../Sync';
import { useTheme } from '../../context/ThemeContext';
import Header from '../Header';
import { AppToolbar } from './AppToolbar';
import { ContentArea } from './ContentArea';
import HelpButton from '../common/HelpButton';
import { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts';
import { useGlobalKeyboardNavigation } from '../../hooks/useGlobalKeyboardNavigation';

/**
 * Main layout component
 */
export const MainLayout: React.FC = () => {
  const { effectiveTheme, isTransitioning } = useTheme();

  // Initialize keyboard shortcuts and navigation
  useKeyboardShortcuts();
  useGlobalKeyboardNavigation();

  // Theme classes
  const themeClass = effectiveTheme;
  const transitionClass = isTransitioning ? 'theme-transition' : '';
  const overflowClass =
    effectiveTheme === 'dark' ? 'overflow-hidden' : 'overflow-auto';

  return (
    <div
      className={`app-container ${themeClass} ${transitionClass} ${overflowClass}`}
    >
      <div className='w-full h-full rounded-[12px] bg-[var(--color-ui-background-primary)] overflow-hidden flex flex-col'>
        {/* Application header */}
        <Header />

        {/* Main content area */}
        <main
          id='main-content'
          className='container mx-auto px-4 py-4 flex-1 overflow-hidden flex flex-col'
          role='main'
          aria-label='Main content'
        >
          {/* Top bar with help button and sync indicator */}
          <div className='flex justify-between items-center mb-[var(--spacing-4)]'>
            <HelpButton />
            <UnifiedSyncIndicator />
          </div>

          {/* Application toolbar with search, view tabs, and controls */}
          <AppToolbar />

          {/* Content area */}
          <ContentArea />
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
