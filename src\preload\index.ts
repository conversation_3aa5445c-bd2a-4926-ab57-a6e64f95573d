/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * @file index.ts
 * @description Preload script for securely exposing IPC communication to renderer
 */

import { contextBridge, ipcRenderer, IpcRendererEvent } from 'electron';
import { SharedFolderFile } from '../shared/types/shared-folder';

// Import separate API modules
import './color-api';
import './organization-api';
import './product-api';
import './datasheet-api';
import './test-data-api';
import './color-library-api';
import './sync-api';

// Expose process info and APIs for debugging and window control
contextBridge.exposeInMainWorld('electron', {
  process: {
    versions: {
      node: process.versions.node,
      chrome: process.versions.chrome,
      electron: process.versions.electron
    },
    platform: process.platform,
    arch: process.arch
  },
  // Add window control methods
  window: {
    minimize: () => ipcRenderer.send('window:minimize'),
    maximize: () => ipcRenderer.send('window:maximize'),
    unmaximize: () => ipcRenderer.send('window:unmaximize'),
    close: () => ipcRenderer.send('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:isMaximized'),
    toggleDevTools: () => ipcRenderer.invoke('window:toggleDevTools')
  },
  // IPC renderer APIs for specific modules
  ipcRenderer: {
    invoke: (channel: string, ...args: any[]) => {
      // Whitelist specific channels
      const validChannels = [
        'datasheet:open',
        'setup:getAppFolder',
        'setup:setAppFolder',
        'setup:isInitialSetupDone',
        'setup:completeInitialSetup',
        'setup:getSharedFolderPath',
        'setup:setSharedFolderPath',
        'test:echo'
      ];
      if (validChannels.includes(channel)) {
        console.log(`Preload: ipc.invoke called for channel "${channel}"`, args);
        return ipcRenderer.invoke(channel, ...args);
      } else {
        console.error(`Preload: ipc.invoke called for invalid channel "${channel}"`, args);
        throw new Error(`Invalid IPC channel: ${channel}`);
      }
    }
  }
});

// Expose secure IPC channel for the renderer
contextBridge.exposeInMainWorld('ipc', {
  invoke: (channel: string, ...args: any[]) => {
    // Whitelist valid channel patterns
    const validChannelPatterns = [
      /^window:/,        // Window control operations (minimize, maximize, close, devtools)
      /^selection:/,     // All selection-related channels
      /^color:/,         // All color-related channels
      /^product:/,       // All product-related channels
      /^datasheet:/,     // All datasheet-related channels
      /^shared-folder:/,  // All shared folder operations
      /^sync:/,          // All sync-related channels
      /^oauth:/,         // All OAuth-related channels
      /^test-data:/,     // All test data operations
      /^color-space-3d:/,// 3D color space window operations
      /^log-error$/,     // Error logging channel
      /^ping$/,          // Health check ping
    ];

    const isValidChannel = validChannelPatterns.some(pattern =>
      pattern.test(channel)
    );

    if (isValidChannel) {
      console.log(`Preload: window.ipc.invoke called for channel "${channel}"`, args);
      return ipcRenderer.invoke(channel, ...args);
    } else {
      console.error(`Preload: window.ipc.invoke rejected invalid channel "${channel}"`, args);
      throw new Error(`Invalid IPC channel: ${channel}`);
    }
  },
  on: (channel: string, callback: (...args: any[]) => void) => {
    const validChannels = [
      'color-space-3d:update-colors'
    ];
    
    if (validChannels.includes(channel)) {
      const listener = (_: any, ...args: any[]) => callback(...args);
      ipcRenderer.on(channel, listener);
      return () => ipcRenderer.removeListener(channel, listener);
    } else {
      throw new Error(`Invalid IPC listener channel: ${channel}`);
    }
  }
});

// Expose app API for general application functions
contextBridge.exposeInMainWorld('app', {
  // Zoom functionality
  zoom: {
    zoomIn: () => ipcRenderer.send('app:zoom-in'),
    zoomOut: () => ipcRenderer.send('app:zoom-out'),
    resetZoom: () => ipcRenderer.send('app:zoom-reset'),
    getZoomFactor: () => ipcRenderer.invoke('app:get-zoom-factor')
  },

  // Keyboard shortcuts info (for displaying in UI if needed)
  shortcuts: {
    zoomIn: process.platform === 'darwin' ? 'Cmd+Plus' : 'Ctrl+Plus',
    zoomOut: process.platform === 'darwin' ? 'Cmd+Minus' : 'Ctrl+Minus',
    resetZoom: process.platform === 'darwin' ? 'Cmd+0' : 'Ctrl+0'
  }
});

// Expose setup API for first-run configuration
contextBridge.exposeInMainWorld('setupAPI', {
  getInitialConfigStatus: () => ipcRenderer.invoke('get-initial-config-status'),
  selectSharedFolder: () => ipcRenderer.invoke('select-shared-folder'),
  saveStorageConfig: (config: { mode: 'standalone' | 'collaboration' | 'server-sync', path?: string }) => ipcRenderer.invoke('save-storage-config', config),
  // Listen for signal from main process to show the modal
  onShowSetupModal: (callback: () => void) => {
    const listener = () => callback();
    ipcRenderer.on('show-setup-modal', listener);
    return () => ipcRenderer.removeListener('show-setup-modal', listener);
  },
  // Signal main process that setup is complete
  sendSetupComplete: () => ipcRenderer.send('setup-complete')
});

// Expose product datasheet API and general API
contextBridge.exposeInMainWorld('api', {
  openProductDatasheet: (product: string) => {
    console.log(`Preload: calling api.openProductDatasheet for ${product}`);
    return ipcRenderer.invoke('product:open-datasheet', product);
  },
  
  // Settings and developer tools API
  openDevTools: () => ipcRenderer.invoke('settings:open-dev-tools'),
  runPerformanceTest: () => ipcRenderer.invoke('settings:run-performance-test'),
  checkDatabaseIntegrity: (organizationId?: string) => ipcRenderer.invoke('settings:check-database-integrity', organizationId),
  
  // Database maintenance API
  getDatabaseStats: () => ipcRenderer.invoke('settings:get-database-stats'),
  optimizeDatabase: () => ipcRenderer.invoke('settings:optimize-database'),
  vacuumDatabase: () => ipcRenderer.invoke('settings:vacuum-database'),
  
  // Error logs API
  getApplicationLogs: () => ipcRenderer.invoke('settings:get-application-logs'),
  clearApplicationLogs: () => ipcRenderer.invoke('settings:clear-application-logs'),
  exportApplicationLogs: () => ipcRenderer.invoke('settings:export-application-logs'),
  
  // Application reset API
  resetApplicationData: () => ipcRenderer.invoke('settings:reset-application-data'),
  
  // File picker API
  selectLogoFile: () => ipcRenderer.invoke('settings:select-logo-file'),
  
  // App info API
  getAppInfo: () => ipcRenderer.invoke('settings:get-app-info'),
  // General invoke method for allowed channels
  invoke: (channel: string, ...args: any[]) => {
    const allowedChannels = [
      'test:create-product',
      'product:open-datasheet'
    ];
    
    if (allowedChannels.includes(channel)) {
      console.log(`Preload: api.invoke called for channel "${channel}"`, args);
      return ipcRenderer.invoke(channel, ...args);
    } else {
      console.error(`Preload: api.invoke rejected invalid channel "${channel}"`);
      throw new Error(`Invalid API channel: ${channel}`);
    }
  },
  // Event listener methods
  on: (channel: string, callback: (event: IpcRendererEvent, ...args: any[]) => void) => {
    const allowedChannels = ['auth-complete', 'handle-invitation'];
    if (allowedChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },
  off: (channel: string, callback: (event: IpcRendererEvent, ...args: any[]) => void) => {
    const allowedChannels = ['auth-complete', 'handle-invitation'];
    if (allowedChannels.includes(channel)) {
      ipcRenderer.removeListener(channel, callback);
    }
  }
});

// Expose license API
contextBridge.exposeInMainWorld('licenseAPI', {
  // Get device status
  getStatus: async () => {
    console.log('Preload: calling licenseAPI.getStatus');
    return ipcRenderer.invoke('license:get-status');
  },

  // Activate device
  activateDevice: async () => {
    console.log('Preload: calling licenseAPI.activateDevice');
    return ipcRenderer.invoke('license:activate-device');
  },

  // Deactivate device
  deactivateDevice: async () => {
    console.log('Preload: calling licenseAPI.deactivateDevice');
    return ipcRenderer.invoke('license:deactivate-device');
  },

  // Check device authorization
  checkAuthorization: async () => {
    console.log('Preload: calling licenseAPI.checkAuthorization');
    return ipcRenderer.invoke('license:check');
  },

  // Show activation dialog
  showActivationDialog: async () => {
    console.log('Preload: calling licenseAPI.showActivationDialog');
    return ipcRenderer.invoke('license:show-activation-dialog');
  },

  // Listen for activation dialog events
  onShowActivationDialog: (callback: (data: any) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('license:show-activation-dialog', listener);
    return () => {
      ipcRenderer.removeListener('license:show-activation-dialog', listener);
    };
  },

  // Listen for license dialog events (used in App.tsx)
  onShowDialog: (callback: (data: any) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('license:show-dialog', listener);
    return () => {
      ipcRenderer.removeListener('license:show-dialog', listener);
    };
  },

  // Activate license with automatic key
  activateLicense: async () => {
    console.log('Preload: calling licenseAPI.activateLicense');
    return ipcRenderer.invoke('license:activate-license');
  },

  // Get device ID
  getDeviceId: async () => {
    console.log('Preload: calling licenseAPI.getDeviceId');
    return ipcRenderer.invoke('license:get-device-id');
  },

  // Get device license key
  getDeviceLicenseKey: async () => {
    console.log('Preload: calling licenseAPI.getDeviceLicenseKey');
    return ipcRenderer.invoke('license:get-device-license-key');
  }
});

// Expose shared folder API
contextBridge.exposeInMainWorld('sharedFolder', {
  // Get the shared folder path
  getPath: () => {
    console.log('Preload: calling sharedFolder.getPath');
    return ipcRenderer.invoke('shared-folder:get-path');
  },

  // Ensure the shared folder exists
  ensureExists: () => {
    console.log('Preload: calling sharedFolder.ensureExists');
    return ipcRenderer.invoke('shared-folder:ensure-exists');
  },

  // Read a file from the shared folder
  readFile: (fileName: string) => {
    console.log(`Preload: calling sharedFolder.readFile for ${fileName}`);
    return ipcRenderer.invoke('shared-folder:read-file', fileName);
  },

  // Write a file to the shared folder
  writeFile: (fileName: string, content: string) => {
    console.log(`Preload: calling sharedFolder.writeFile for ${fileName}`);
    return ipcRenderer.invoke('shared-folder:write-file', fileName, content);
  },

  // List files in the shared folder
  listFiles: () => {
    console.log('Preload: calling sharedFolder.listFiles');
    return ipcRenderer.invoke('shared-folder:list-files') as Promise<SharedFolderFile[]>;
  }
});

// Expose monitoring and update APIs
contextBridge.exposeInMainWorld('monitoring', {
  // Error tracking
  trackError: (error: Error | { message: string; stack?: string; componentStack?: string; location?: string }) => {
    console.log('Preload: tracking error through IPC');

    // Normalize error object
    const errorInfo = error instanceof Error
      ? {
          message: error.message,
          stack: error.stack
        }
      : error;

    return ipcRenderer.invoke('track-renderer-error', errorInfo);
  }
});

// Expose auto-update API
contextBridge.exposeInMainWorld('autoUpdate', {
  // Listen for auto-update status changes
  onStatusChange: (callback: (status: {
    status: 'checking' | 'available' | 'not-available' | 'downloading' | 'downloaded' | 'error';
    data?: any;
    error?: string;
  }) => void) => {
    const listener = (_: any, data: any) => callback(data);
    ipcRenderer.on('update-event', listener);

    // Return function to remove listener
    return () => {
      ipcRenderer.removeListener('update-event', listener);
    };
  },

  // Check for updates manually
  checkForUpdates: () => {
    return ipcRenderer.invoke('update:check-for-updates');
  },

  // Download an available update
  downloadUpdate: () => {
    return ipcRenderer.invoke('update:download-update');
  },

  // Install a downloaded update
  installUpdate: () => {
    return ipcRenderer.invoke('update:install-update');
  }
});

// Color API now defined in ./color-api.ts

// Selection API removed - now using direct product-color relationships
// Sync API now defined in ./sync-api.ts
// Organization API now defined in ./organization-api.ts

// Test Data API now defined in ./test-data-api.ts

// Color Library API now defined in ./color-library-api.ts
// Expose audit log API for security monitoring
// Expose soft delete APIs
contextBridge.exposeInMainWorld('softDeleteAPI', {
  colors: {
    getSoftDeleted: async (organizationId: string, limit?: number, offset?: number) => {
      console.log('Preload: calling softDeleteAPI.colors.getSoftDeleted');
      return ipcRenderer.invoke('colors:get-soft-deleted', organizationId, limit, offset);
    },
    restore: async (colorId: string, organizationId: string, userId?: string) => {
      console.log('Preload: calling softDeleteAPI.colors.restore');
      return ipcRenderer.invoke('colors:restore', colorId, organizationId, userId);
    },
    bulkRestore: async (colorIds: string[], organizationId: string, userId?: string) => {
      console.log('Preload: calling softDeleteAPI.colors.bulkRestore');
      return ipcRenderer.invoke('colors:bulk-restore', colorIds, organizationId, userId);
    },
    cleanupOld: async (organizationId: string, daysOld?: number) => {
      console.log('Preload: calling softDeleteAPI.colors.cleanupOld');
      return ipcRenderer.invoke('colors:cleanup-old-soft-deleted', organizationId, daysOld);
    }
  },
  products: {
    getSoftDeleted: async (organizationId: string, limit?: number, offset?: number) => {
      console.log('Preload: calling softDeleteAPI.products.getSoftDeleted');
      return ipcRenderer.invoke('products:get-soft-deleted', organizationId, limit, offset);
    },
    restore: async (productId: string, organizationId: string, userId?: string) => {
      console.log('Preload: calling softDeleteAPI.products.restore');
      return ipcRenderer.invoke('products:restore', productId, organizationId, userId);
    }
  },
  datasheets: {
    getSoftDeleted: async (organizationId: string, limit?: number, offset?: number) => {
      console.log('Preload: calling softDeleteAPI.datasheets.getSoftDeleted');
      return ipcRenderer.invoke('datasheets:get-soft-deleted', organizationId, limit, offset);
    },
    restore: async (datasheetId: string, organizationId: string, userId?: string) => {
      console.log('Preload: calling softDeleteAPI.datasheets.restore');
      return ipcRenderer.invoke('datasheets:restore', datasheetId, organizationId, userId);
    },
    cleanupOld: async (organizationId: string, daysOld?: number) => {
      console.log('Preload: calling softDeleteAPI.datasheets.cleanupOld');
      return ipcRenderer.invoke('datasheets:cleanup-old-soft-deleted', organizationId, daysOld);
    }
  }
});

// Expose relationship integrity APIs
contextBridge.exposeInMainWorld('integrityAPI', {
  runChecks: async (organizationId: string) => {
    console.log('Preload: calling integrityAPI.runChecks');
    return ipcRenderer.invoke('integrity:run-checks', organizationId);
  },
  fixColorSpaces: async (organizationId: string) => {
    console.log('Preload: calling integrityAPI.fixColorSpaces');
    return ipcRenderer.invoke('integrity:fix-color-spaces', organizationId);
  }
});

contextBridge.exposeInMainWorld('auditAPI', {
  // Get audit logs with filtering
  getLogs: (filter?: {
    organizationId?: string;
    userId?: string;
    table?: string;
    operation?: string;
    startDate?: string;
    endDate?: string;
    onlyErrors?: boolean;
  }) => {
    console.log('Preload: calling auditAPI.getLogs');
    return ipcRenderer.invoke('audit:get-logs', filter);
  },

  // Get security analytics
  getAnalytics: (organizationId?: string) => {
    console.log('Preload: calling auditAPI.getAnalytics');
    return ipcRenderer.invoke('audit:get-analytics', organizationId);
  },

  // Get suspicious activity
  getSuspiciousActivity: (organizationId?: string) => {
    console.log('Preload: calling auditAPI.getSuspiciousActivity');
    return ipcRenderer.invoke('audit:get-suspicious-activity', organizationId);
  },

  // Export audit logs
  exportLogs: (options?: {
    organizationId?: string;
    startDate?: string;
    endDate?: string;
    format?: 'json' | 'csv';
  }) => {
    console.log('Preload: calling auditAPI.exportLogs');
    return ipcRenderer.invoke('audit:export-logs', options);
  },

  // Cleanup old logs
  cleanupLogs: () => {
    console.log('Preload: calling auditAPI.cleanupLogs');
    return ipcRenderer.invoke('audit:cleanup-logs');
  }
});

// Expose IPC event listener for handling invitation deep links
contextBridge.exposeInMainWorld('invitationHandler', {
  onInvitation: (callback: (token: string) => void) => {
    ipcRenderer.on('handle-invitation', (_event, token) => {
      callback(token);
    });
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeAllListeners('handle-invitation');
    };
  }
});
