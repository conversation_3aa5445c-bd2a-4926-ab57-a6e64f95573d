/**
 * @file ErrorBoundary.tsx
 * @description React error boundary component for graceful error handling
 */

import { Component, ReactNode, ErrorInfo } from 'react';
import { errorLogger } from '../../utils/errorLogger';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo?: ErrorInfo) => ReactNode;
}

/**
 * Error boundary component for catching and handling React errors
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Log to error tracking system
    errorLogger.logError({
      message: 'React Error Boundary',
      stack: error.stack,
      context: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true
      }
    });

    // Update state with error info
    this.setState({
      error,
      errorInfo
    });
  }

  override render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback && this.state.error) {
        return this.props.fallback(this.state.error, this.state.errorInfo);
      }

      // Default fallback UI
      return (
        <div className="flex items-center justify-center h-screen bg-ui-background-primary">
          <div className="text-center p-8 max-w-md">
            <h1 className="text-2xl font-bold text-ui-foreground-primary mb-4">
              Something went wrong
            </h1>
            <p className="text-ui-foreground-secondary mb-4">
              An unexpected error occurred. Please refresh the page or restart the application.
            </p>
            {this.state.error && (
              <details className="text-left text-sm text-ui-foreground-tertiary">
                <summary className="cursor-pointer mb-2">Error Details</summary>
                <pre className="whitespace-pre-wrap break-words bg-ui-background-secondary p-3 rounded">
                  {this.state.error.message}
                  {this.state.errorInfo?.componentStack && (
                    <>
                      {'\n\nComponent Stack:'}
                      {this.state.errorInfo.componentStack}
                    </>
                  )}
                </pre>
              </details>
            )}
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-brand-primary text-white rounded-lg hover:bg-brand-primary-dark transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;