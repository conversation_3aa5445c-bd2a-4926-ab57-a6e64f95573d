/**
 * @file useKeyboardShortcuts.ts
 * @description Hook for application-level keyboard shortcuts
 */

import { useEffect } from 'react';
import useGlobalKeyboardShortcuts from '../../../hooks/useKeyboardShortcuts';

/**
 * Hook for initializing keyboard shortcuts
 */
export const useKeyboardShortcuts = () => {
  // Initialize the main keyboard shortcuts hook
  useGlobalKeyboardShortcuts();

  // Additional app-level shortcuts can be added here
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Example: Escape key to close modals or panels
      if (event.key === 'Escape') {
        // Handle escape key globally
        console.log('Escape key pressed at app level');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);
};

export default useKeyboardShortcuts;
