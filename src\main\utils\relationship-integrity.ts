/**
 * @file relationship-integrity.ts
 * @description Relationship integrity checks and orphan cleanup utilities
 */

import Database from 'better-sqlite3';
import { requireValidOrganizationId } from './organization-validation';

export interface IntegrityCheckResult {
  check: string;
  passed: boolean;
  issues: string[];
  affectedRecords: number;
}

export interface OrphanCleanupResult {
  table: string;
  orphansFound: number;
  orphansCleaned: number;
  success: boolean;
  errors: string[];
}

export interface IntegrityReport {
  timestamp: string;
  organizationId: string;
  checks: IntegrityCheckResult[];
  orphanCleanup: OrphanCleanupResult[];
  totalIssues: number;
  totalCleaned: number;
}

/**
 * Relationship integrity checker and orphan cleanup utility
 */
export class RelationshipIntegrityManager {
  constructor(private db: Database.Database) {}

  /**
   * Run comprehensive integrity checks for an organization
   */
  runIntegrityChecks(organizationId: string): IntegrityReport {
    const validatedOrgId = requireValidOrganizationId(
      organizationId,
      'RelationshipIntegrityManager.runIntegrityChecks'
    );

    const checks: IntegrityCheckResult[] = [];
    const orphanCleanup: OrphanCleanupResult[] = [];

    console.log(
      `[IntegrityManager] Running integrity checks for organization: ${validatedOrgId}`
    );

    // Check product-color relationship integrity
    checks.push(this.checkProductColorIntegrity(validatedOrgId));

    // Check datasheet-product relationship integrity
    checks.push(this.checkDatasheetProductIntegrity(validatedOrgId));

    // Check organization membership integrity
    checks.push(this.checkOrganizationMembershipIntegrity(validatedOrgId));

    // Check for orphaned color spaces
    checks.push(this.checkOrphanedColorSpaces(validatedOrgId));

    // Check for orphaned metadata
    checks.push(this.checkOrphanedMetadata(validatedOrgId));

    // Clean up orphaned relationships
    orphanCleanup.push(this.cleanupOrphanedProductColors(validatedOrgId));
    orphanCleanup.push(this.cleanupOrphanedDatasheets(validatedOrgId));

    const totalIssues = checks.reduce(
      (sum, check) => sum + check.affectedRecords,
      0
    );
    const totalCleaned = orphanCleanup.reduce(
      (sum, cleanup) => sum + cleanup.orphansCleaned,
      0
    );

    return {
      timestamp: new Date().toISOString(),
      organizationId: validatedOrgId,
      checks,
      orphanCleanup,
      totalIssues,
      totalCleaned,
    };
  }

  /**
   * Check product-color relationship integrity
   */
  private checkProductColorIntegrity(
    organizationId: string
  ): IntegrityCheckResult {
    const issues: string[] = [];
    let affectedRecords = 0;

    try {
      // Check for product_colors pointing to non-existent products
      const orphanedByProduct = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM product_colors pc
        WHERE pc.organization_id = ?
        AND NOT EXISTS (
          SELECT 1 FROM products p 
          WHERE p.id = pc.product_id 
          AND p.organization_id = pc.organization_id 
          AND p.deleted_at IS NULL
        )
      `
        )
        .get(organizationId) as { count: number };

      if (orphanedByProduct.count > 0) {
        issues.push(
          `Found ${orphanedByProduct.count} product-color relationships pointing to deleted/missing products`
        );
        affectedRecords += orphanedByProduct.count;
      }

      // Check for product_colors pointing to non-existent colors
      const orphanedByColor = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM product_colors pc
        WHERE pc.organization_id = ?
        AND NOT EXISTS (
          SELECT 1 FROM colors c 
          WHERE c.id = pc.color_id 
          AND c.organization_id = pc.organization_id 
          AND c.deleted_at IS NULL
        )
      `
        )
        .get(organizationId) as { count: number };

      if (orphanedByColor.count > 0) {
        issues.push(
          `Found ${orphanedByColor.count} product-color relationships pointing to deleted/missing colors`
        );
        affectedRecords += orphanedByColor.count;
      }

      // Check for duplicate relationships
      const duplicates = this.db
        .prepare(
          `
        SELECT product_id, color_id, COUNT(*) as count
        FROM product_colors 
        WHERE organization_id = ?
        GROUP BY product_id, color_id
        HAVING COUNT(*) > 1
      `
        )
        .all(organizationId) as {
        product_id: number;
        color_id: number;
        count: number;
      }[];

      if (duplicates.length > 0) {
        const duplicateCount = duplicates.reduce(
          (sum, dup) => sum + (dup.count - 1),
          0
        );
        issues.push(
          `Found ${duplicateCount} duplicate product-color relationships`
        );
        affectedRecords += duplicateCount;
      }

      return {
        check: 'Product-Color Relationship Integrity',
        passed: issues.length === 0,
        issues,
        affectedRecords,
      };
    } catch (error) {
      return {
        check: 'Product-Color Relationship Integrity',
        passed: false,
        issues: [
          `Error during check: ${error instanceof Error ? error.message : String(error)}`,
        ],
        affectedRecords: 0,
      };
    }
  }

  /**
   * Check datasheet-product relationship integrity
   */
  private checkDatasheetProductIntegrity(
    organizationId: string
  ): IntegrityCheckResult {
    const issues: string[] = [];
    let affectedRecords = 0;

    try {
      // Check for datasheets pointing to non-existent products
      const orphanedDatasheets = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM datasheets d
        WHERE NOT EXISTS (
          SELECT 1 FROM products p 
          WHERE p.id = d.product_id 
          AND p.organization_id = ? 
          AND p.deleted_at IS NULL
        )
        AND d.is_active = 1
      `
        )
        .get(organizationId) as { count: number };

      if (orphanedDatasheets.count > 0) {
        issues.push(
          `Found ${orphanedDatasheets.count} active datasheets pointing to deleted/missing products`
        );
        affectedRecords += orphanedDatasheets.count;
      }

      // Check for datasheets with invalid external IDs
      const invalidExternalIds = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM datasheets
        WHERE (external_id IS NULL OR external_id = '')
        AND is_active = 1
        AND product_id IN (
          SELECT id FROM products WHERE organization_id = ?
        )
      `
        )
        .get(organizationId) as { count: number };

      if (invalidExternalIds.count > 0) {
        issues.push(
          `Found ${invalidExternalIds.count} datasheets with missing/invalid external IDs`
        );
        affectedRecords += invalidExternalIds.count;
      }

      return {
        check: 'Datasheet-Product Relationship Integrity',
        passed: issues.length === 0,
        issues,
        affectedRecords,
      };
    } catch (error) {
      return {
        check: 'Datasheet-Product Relationship Integrity',
        passed: false,
        issues: [
          `Error during check: ${error instanceof Error ? error.message : String(error)}`,
        ],
        affectedRecords: 0,
      };
    }
  }

  /**
   * Check organization membership integrity
   */
  private checkOrganizationMembershipIntegrity(
    organizationId: string
  ): IntegrityCheckResult {
    const issues: string[] = [];
    let affectedRecords = 0;

    try {
      // Get internal org ID
      const org = this.db
        .prepare(
          `
        SELECT id FROM organizations WHERE external_id = ?
      `
        )
        .get(organizationId) as { id: number } | undefined;

      if (!org) {
        return {
          check: 'Organization Membership Integrity',
          passed: false,
          issues: ['Organization not found'],
          affectedRecords: 0,
        };
      }

      // Check for memberships pointing to non-existent organizations
      const orphanedMemberships = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM organization_members om
        WHERE om.organization_id = ?
        AND NOT EXISTS (
          SELECT 1 FROM organizations o 
          WHERE o.id = om.organization_id
        )
      `
        )
        .get(org.id) as { count: number };

      if (orphanedMemberships.count > 0) {
        issues.push(
          `Found ${orphanedMemberships.count} memberships pointing to non-existent organizations`
        );
        affectedRecords += orphanedMemberships.count;
      }

      // Check for duplicate memberships
      const duplicateMemberships = this.db
        .prepare(
          `
        SELECT user_id, COUNT(*) as count
        FROM organization_members 
        WHERE organization_id = ?
        GROUP BY user_id
        HAVING COUNT(*) > 1
      `
        )
        .all(org.id) as { user_id: string; count: number }[];

      if (duplicateMemberships.length > 0) {
        const duplicateCount = duplicateMemberships.reduce(
          (sum, dup) => sum + (dup.count - 1),
          0
        );
        issues.push(
          `Found ${duplicateCount} duplicate organization memberships`
        );
        affectedRecords += duplicateCount;
      }

      // Check for organizations without owners
      const ownersCount = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM organization_members
        WHERE organization_id = ? AND role = 'owner'
      `
        )
        .get(org.id) as { count: number };

      if (ownersCount.count === 0) {
        issues.push('Organization has no owners');
        affectedRecords += 1;
      }

      return {
        check: 'Organization Membership Integrity',
        passed: issues.length === 0,
        issues,
        affectedRecords,
      };
    } catch (error) {
      return {
        check: 'Organization Membership Integrity',
        passed: false,
        issues: [
          `Error during check: ${error instanceof Error ? error.message : String(error)}`,
        ],
        affectedRecords: 0,
      };
    }
  }

  /**
   * Check for orphaned color spaces
   */
  private checkOrphanedColorSpaces(
    organizationId: string
  ): IntegrityCheckResult {
    const issues: string[] = [];
    let affectedRecords = 0;

    try {
      // Check for colors with invalid color_spaces JSON
      const invalidColorSpaces = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM colors
        WHERE organization_id = ?
        AND deleted_at IS NULL
        AND (
          color_spaces IS NULL 
          OR color_spaces = '' 
          OR (color_spaces != 'null' AND json_valid(color_spaces) = 0)
        )
      `
        )
        .get(organizationId) as { count: number };

      if (invalidColorSpaces.count > 0) {
        issues.push(
          `Found ${invalidColorSpaces.count} colors with invalid or missing color_spaces JSON`
        );
        affectedRecords += invalidColorSpaces.count;
      }

      // Check for colors with missing hex values
      const missingHex = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM colors
        WHERE organization_id = ?
        AND deleted_at IS NULL
        AND (hex IS NULL OR hex = '')
      `
        )
        .get(organizationId) as { count: number };

      if (missingHex.count > 0) {
        issues.push(`Found ${missingHex.count} colors with missing hex values`);
        affectedRecords += missingHex.count;
      }

      return {
        check: 'Orphaned Color Spaces',
        passed: issues.length === 0,
        issues,
        affectedRecords,
      };
    } catch (error) {
      return {
        check: 'Orphaned Color Spaces',
        passed: false,
        issues: [
          `Error during check: ${error instanceof Error ? error.message : String(error)}`,
        ],
        affectedRecords: 0,
      };
    }
  }

  /**
   * Check for orphaned metadata
   */
  private checkOrphanedMetadata(organizationId: string): IntegrityCheckResult {
    const issues: string[] = [];
    let affectedRecords = 0;

    try {
      // Check for invalid metadata JSON in products
      const invalidProductMetadata = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM products
        WHERE organization_id = ?
        AND deleted_at IS NULL
        AND metadata IS NOT NULL
        AND metadata != ''
        AND json_valid(metadata) = 0
      `
        )
        .get(organizationId) as { count: number };

      if (invalidProductMetadata.count > 0) {
        issues.push(
          `Found ${invalidProductMetadata.count} products with invalid metadata JSON`
        );
        affectedRecords += invalidProductMetadata.count;
      }

      // Check for invalid metadata JSON in datasheets
      const invalidDatasheetMetadata = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM datasheets d
        INNER JOIN products p ON d.product_id = p.id
        WHERE p.organization_id = ?
        AND d.is_active = 1
        AND d.metadata IS NOT NULL
        AND d.metadata != ''
        AND json_valid(d.metadata) = 0
      `
        )
        .get(organizationId) as { count: number };

      if (invalidDatasheetMetadata.count > 0) {
        issues.push(
          `Found ${invalidDatasheetMetadata.count} datasheets with invalid metadata JSON`
        );
        affectedRecords += invalidDatasheetMetadata.count;
      }

      return {
        check: 'Orphaned Metadata',
        passed: issues.length === 0,
        issues,
        affectedRecords,
      };
    } catch (error) {
      return {
        check: 'Orphaned Metadata',
        passed: false,
        issues: [
          `Error during check: ${error instanceof Error ? error.message : String(error)}`,
        ],
        affectedRecords: 0,
      };
    }
  }

  /**
   * Clean up orphaned product-color relationships
   */
  private cleanupOrphanedProductColors(
    organizationId: string
  ): OrphanCleanupResult {
    const errors: string[] = [];
    let orphansFound = 0;
    let orphansCleaned = 0;

    try {
      const cleanupTransaction = this.db.transaction(() => {
        // Find orphaned relationships
        const orphanedRelationships = this.db
          .prepare(
            `
          SELECT pc.id FROM product_colors pc
          WHERE pc.organization_id = ?
          AND (
            NOT EXISTS (
              SELECT 1 FROM products p 
              WHERE p.id = pc.product_id 
              AND p.organization_id = pc.organization_id 
              AND p.deleted_at IS NULL
            )
            OR NOT EXISTS (
              SELECT 1 FROM colors c 
              WHERE c.id = pc.color_id 
              AND c.organization_id = pc.organization_id 
              AND c.deleted_at IS NULL
            )
          )
        `
          )
          .all(organizationId) as { id: number }[];

        orphansFound = orphanedRelationships.length;

        if (orphansFound > 0) {
          // Delete orphaned relationships
          const result = this.db
            .prepare(
              `
            DELETE FROM product_colors
            WHERE organization_id = ?
            AND (
              NOT EXISTS (
                SELECT 1 FROM products p 
                WHERE p.id = product_colors.product_id 
                AND p.organization_id = product_colors.organization_id 
                AND p.deleted_at IS NULL
              )
              OR NOT EXISTS (
                SELECT 1 FROM colors c 
                WHERE c.id = product_colors.color_id 
                AND c.organization_id = product_colors.organization_id 
                AND c.deleted_at IS NULL
              )
            )
          `
            )
            .run(organizationId);

          orphansCleaned = result.changes;
        }
      });

      cleanupTransaction();

      console.log(
        `[IntegrityManager] Cleaned up ${orphansCleaned}/${orphansFound} orphaned product-color relationships`
      );

      return {
        table: 'product_colors',
        orphansFound,
        orphansCleaned,
        success: true,
        errors,
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      errors.push(errorMsg);
      console.error(
        '[IntegrityManager] Error cleaning up orphaned product-color relationships:',
        error
      );

      return {
        table: 'product_colors',
        orphansFound,
        orphansCleaned,
        success: false,
        errors,
      };
    }
  }

  /**
   * Clean up orphaned datasheets
   */
  private cleanupOrphanedDatasheets(
    organizationId: string
  ): OrphanCleanupResult {
    const errors: string[] = [];
    let orphansFound = 0;
    let orphansCleaned = 0;

    try {
      const cleanupTransaction = this.db.transaction(() => {
        // Find orphaned datasheets
        const orphanedDatasheets = this.db
          .prepare(
            `
          SELECT d.id FROM datasheets d
          WHERE NOT EXISTS (
            SELECT 1 FROM products p 
            WHERE p.id = d.product_id 
            AND p.organization_id = ? 
            AND p.deleted_at IS NULL
          )
          AND d.is_active = 1
        `
          )
          .all(organizationId) as { id: number }[];

        orphansFound = orphanedDatasheets.length;

        if (orphansFound > 0) {
          // Soft delete orphaned datasheets
          const result = this.db
            .prepare(
              `
            UPDATE datasheets 
            SET is_active = 0, updated_at = datetime('now')
            WHERE NOT EXISTS (
              SELECT 1 FROM products p 
              WHERE p.id = datasheets.product_id 
              AND p.organization_id = ? 
              AND p.deleted_at IS NULL
            )
            AND is_active = 1
          `
            )
            .run(organizationId);

          orphansCleaned = result.changes;
        }
      });

      cleanupTransaction();

      console.log(
        `[IntegrityManager] Cleaned up ${orphansCleaned}/${orphansFound} orphaned datasheets`
      );

      return {
        table: 'datasheets',
        orphansFound,
        orphansCleaned,
        success: true,
        errors,
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      errors.push(errorMsg);
      console.error(
        '[IntegrityManager] Error cleaning up orphaned datasheets:',
        error
      );

      return {
        table: 'datasheets',
        orphansFound,
        orphansCleaned,
        success: false,
        errors,
      };
    }
  }

  /**
   * Fix invalid color spaces by regenerating them
   */
  fixInvalidColorSpaces(organizationId: string): {
    fixed: number;
    errors: string[];
  } {
    const errors: string[] = [];
    let fixed = 0;

    try {
      const fixTransaction = this.db.transaction(() => {
        // Get colors with invalid color spaces
        const invalidColors = this.db
          .prepare(
            `
          SELECT id, external_id, hex FROM colors
          WHERE organization_id = ?
          AND deleted_at IS NULL
          AND (
            color_spaces IS NULL 
            OR color_spaces = '' 
            OR (color_spaces != 'null' AND json_valid(color_spaces) = 0)
          )
          AND hex IS NOT NULL 
          AND hex != ''
        `
          )
          .all(organizationId) as {
          id: number;
          external_id: string;
          hex: string;
        }[];

        for (const color of invalidColors) {
          try {
            // Generate minimal color spaces JSON (CMYK only for storage efficiency)
            const colorSpaces = JSON.stringify({
              cmyk: { c: 0, m: 0, y: 0, k: 0 }, // Default CMYK - real values would be calculated
            });

            this.db
              .prepare(
                `
              UPDATE colors 
              SET color_spaces = ?, updated_at = ?
              WHERE id = ?
            `
              )
              .run(colorSpaces, new Date().toISOString(), color.id);

            fixed++;
          } catch (colorError) {
            errors.push(
              `Failed to fix color ${color.external_id}: ${colorError instanceof Error ? colorError.message : String(colorError)}`
            );
          }
        }
      });

      fixTransaction();

      console.log(
        `[IntegrityManager] Fixed ${fixed} colors with invalid color spaces`
      );

      return { fixed, errors };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      errors.push(errorMsg);
      console.error(
        '[IntegrityManager] Error fixing invalid color spaces:',
        error
      );

      return { fixed, errors };
    }
  }
}

/**
 * Factory function to create a relationship integrity manager
 */
export function createRelationshipIntegrityManager(
  db: Database.Database
): RelationshipIntegrityManager {
  return new RelationshipIntegrityManager(db);
}
