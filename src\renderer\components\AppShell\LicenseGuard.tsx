/**
 * @file LicenseGuard.tsx
 * @description License validation guard component
 */

import React, { ReactNode, useState } from 'react';
import { LicenseDialog } from '../License/LicenseDialog';

interface LicenseStatus {
  isValid: boolean;
  inTrialMode: boolean;
  trialDaysRemaining: number;
}

interface LicenseGuardProps {
  children: ReactNode;
  licenseStatus: LicenseStatus;
}

/**
 * License guard component that blocks access if license is invalid
 */
export const LicenseGuard: React.FC<LicenseGuardProps> = ({
  children,
  licenseStatus,
}) => {
  const [showLicenseDialog, setShowLicenseDialog] = useState(false);

  // Show blocking overlay if license is invalid
  if (!licenseStatus.isValid) {
    return (
      <div
        style={{
          position: 'fixed',
          zIndex: 9999,
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: 'rgba(20,20,20,0.97)',
          color: '#fff',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <h1 style={{ fontSize: 32, marginBottom: 24 }}>Access Denied</h1>
        <p style={{ fontSize: 18, marginBottom: 16 }}>
          This device is not authorized to use ChromaSync.
          <br />
          Please contact your administrator to restore access.
        </p>
        <p style={{ fontSize: 14, opacity: 0.7 }}>
          Device ID:{' '}
          {typeof window.licenseAPI?.getDeviceId === 'function'
            ? '(hidden for security)'
            : 'Unavailable'}
        </p>
      </div>
    );
  }

  return (
    <>
      {/* License Dialog */}
      <LicenseDialog
        isOpen={showLicenseDialog}
        onClose={() => setShowLicenseDialog(false)}
        licenseStatus={licenseStatus}
      />

      {children}
    </>
  );
};

export default LicenseGuard;
