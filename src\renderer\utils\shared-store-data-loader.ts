/**
 * @file shared-store-data-loader.ts
 * @description Unified data loading utility for consistent store behavior across ProductStore and ColorStore
 */

import { handleIPCResponse, IPCError } from './ipc-response-handler';
import {
  createStoreTimer,
  logStoreOperation,
} from './store-event-completion';
import {
  validateOrganizationContext,
  type OrganizationContextConfig,
} from './organization-context-validator';
import type { Result } from '../../shared/types/result.types';

/**
 * Configuration for store data loading
 */
export interface StoreDataLoaderConfig<T> {
  storeName: string;
  operation: string;
  apiCall: () => Promise<T>;
  organizationRequired?: boolean;
  organizationConfig?: OrganizationContextConfig;
  timeout?: number;
  validateData?: (data: T) => boolean;
  transformData?: (data: T) => T;
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * Store state interface for consistent state management
 */
export interface StoreState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
}

/**
 * Store state setter function type
 */
export type StateSetter<T> = (updater: Partial<StoreState<T>>) => void;

/**
 * Default configuration values
 */
const DEFAULT_CONFIG = {
  timeout: 10000,
  retryAttempts: 2,
  retryDelay: 500,
  organizationRequired: true,
};

/**
 * Create a standardized data loader function for stores
 * @param config - Data loader configuration
 * @returns Data loader function that can be used in store actions
 */
export function createStoreDataLoader<T>(config: StoreDataLoaderConfig<T>) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  return async function loadData(setState: StateSetter<T>): Promise<void> {
    const timer = createStoreTimer(
      finalConfig.storeName,
      finalConfig.operation
    );

    logStoreOperation(finalConfig.storeName, finalConfig.operation, 'start');

    // Set loading state
    setState({ isLoading: true, error: null });

    try {
      // Step 1: Validate organization context if required
      if (finalConfig.organizationRequired) {
        logStoreOperation(
          finalConfig.storeName,
          finalConfig.operation,
          'start',
          'Validating organization context...'
        );

        const orgValidation = await validateOrganizationContext(
          finalConfig.organizationConfig
        );

        if (!orgValidation.success) {
          throw new IPCError(
            orgValidation.error || 'Organization context validation failed',
            'NO_ORGANIZATION_SELECTED',
            'No organization selected. Please select an organization from the dropdown menu.'
          );
        }

        logStoreOperation(
          finalConfig.storeName,
          finalConfig.operation,
          'start',
          `Organization validated: ${orgValidation.organizationId}`
        );
      }

      // Step 2: Execute API call with timeout and retry logic
      let lastError: Error | null = null;
      let rawData: T | null = null;

      for (let attempt = 1; attempt <= finalConfig.retryAttempts; attempt++) {
        try {
          logStoreOperation(
            finalConfig.storeName,
            finalConfig.operation,
            'start',
            `API call attempt ${attempt}/${finalConfig.retryAttempts}`
          );

          // Create timeout wrapper for API call
          const apiCallWithTimeout = Promise.race([
            finalConfig.apiCall(),
            new Promise<never>((_, reject) =>
              setTimeout(
                () => reject(new IPCError('API call timeout', 'TIMEOUT')),
                finalConfig.timeout
              )
            ),
          ]);

          const response = await apiCallWithTimeout;
          rawData = handleIPCResponse<T>(response);
          break; // Success, exit retry loop
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));

          if (attempt < finalConfig.retryAttempts) {
            logStoreOperation(
              finalConfig.storeName,
              finalConfig.operation,
              'start',
              `Attempt ${attempt} failed, retrying in ${finalConfig.retryDelay}ms...`
            );
            await new Promise(resolve =>
              setTimeout(resolve, finalConfig.retryDelay)
            );
          } else {
            logStoreOperation(
              finalConfig.storeName,
              finalConfig.operation,
              'error',
              `All ${finalConfig.retryAttempts} attempts failed`
            );
          }
        }
      }

      if (!rawData) {
        throw (
          lastError || new IPCError('API call failed after all retry attempts')
        );
      }

      // Step 3: Validate data structure if validator provided
      if (finalConfig.validateData && !finalConfig.validateData(rawData)) {
        throw new IPCError(
          'Invalid data structure received from API',
          'INVALID_DATA'
        );
      }

      // Step 4: Transform data if transformer provided
      const finalData = finalConfig.transformData
        ? finalConfig.transformData(rawData)
        : rawData;

      // Step 5: Update state with successful data
      setState({
        data: finalData,
        isLoading: false,
        error: null,
      });

      // Step 6: Emit completion event
      timer.complete(true, finalData);
      logStoreOperation(
        finalConfig.storeName,
        finalConfig.operation,
        'success',
        {
          dataType: typeof finalData,
          isArray: Array.isArray(finalData),
          itemCount: Array.isArray(finalData) ? finalData.length : 'N/A',
        }
      );
    } catch (error) {
      const errorMessage =
        error instanceof IPCError
          ? error.getUserMessage()
          : error instanceof Error
            ? error.message
            : String(error);

      // Update state with error
      setState({
        isLoading: false,
        error: errorMessage,
      });

      // Emit error completion event
      timer.complete(false, undefined, errorMessage);
      logStoreOperation(
        finalConfig.storeName,
        finalConfig.operation,
        'error',
        errorMessage
      );
    }
  };
}

/**
 * Create a simplified data loader for stores that don't need complex configuration
 * @param storeName - Name of the store
 * @param apiCall - Function that returns the API promise
 * @returns Simple data loader function
 */
export function createSimpleStoreDataLoader<T>(
  storeName: string,
  apiCall: () => Promise<T>
) {
  return createStoreDataLoader<T>({
    storeName,
    operation: 'load',
    apiCall,
    organizationRequired: true,
  });
}

/**
 * Create a data loader specifically for color data
 * @param apiCall - Color API call function
 * @returns Color-specific data loader
 */
export function createColorDataLoader(apiCall: () => Promise<any>) {
  return createStoreDataLoader({
    storeName: 'color-store',
    operation: 'refresh',
    apiCall,
    organizationRequired: true,
    validateData: (data: any) => {
      // Validate color data structure
      return (
        data &&
        typeof data === 'object' &&
        Array.isArray(data.colors) &&
        typeof data.usageCounts === 'object'
      );
    },
    transformData: (data: any) => {
      // Ensure consistent data structure
      return {
        colors: data.colors || [],
        usageCounts: data.usageCounts || {},
        organizationId: data.organizationId,
        totalColors: data.totalColors || data.colors?.length || 0,
        colorsWithUsage: data.colorsWithUsage || 0,
      };
    },
  });
}

/**
 * Create a data loader specifically for product data
 * @param apiCall - Product API call function
 * @returns Product-specific data loader
 */
export function createProductDataLoader(apiCall: () => Promise<any>) {
  return createStoreDataLoader({
    storeName: 'product-store',
    operation: 'refresh',
    apiCall,
    organizationRequired: true,
    validateData: (data: any) => {
      // Validate product data structure
      return Array.isArray(data);
    },
    transformData: (data: any[]) => {
      // Ensure each product has expected structure
      return data.map(product => ({
        ...product,
        colors: product.colors || [],
      }));
    },
  });
}

/**
 * Result-based version of data loader that returns Result instead of updating state
 * @param config - Data loader configuration
 * @returns Function that returns Result with data or error
 */
export function createStoreDataLoaderResult<T>(
  config: StoreDataLoaderConfig<T>
) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  return async function loadDataResult(): Promise<Result<T, IPCError>> {
    const timer = createStoreTimer(
      finalConfig.storeName,
      finalConfig.operation
    );

    try {
      // Organization validation
      if (finalConfig.organizationRequired) {
        const orgValidation = await validateOrganizationContext(
          finalConfig.organizationConfig
        );
        if (!orgValidation.success) {
          const error = new IPCError(
            orgValidation.error || 'Organization context validation failed',
            'NO_ORGANIZATION_SELECTED'
          );
          timer.complete(false, undefined, error.message);
          return { success: false, error };
        }
      }

      // API call with retry logic
      let lastError: Error | null = null;
      let rawData: T | null = null;

      for (let attempt = 1; attempt <= finalConfig.retryAttempts; attempt++) {
        try {
          const response = await finalConfig.apiCall();
          rawData = handleIPCResponse<T>(response);
          break;
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));
          if (attempt < finalConfig.retryAttempts) {
            await new Promise(resolve =>
              setTimeout(resolve, finalConfig.retryDelay)
            );
          }
        }
      }

      if (!rawData) {
        const error =
          lastError instanceof IPCError
            ? lastError
            : new IPCError(lastError?.message || 'API call failed');
        timer.complete(false, undefined, error.message);
        return { success: false, error };
      }

      // Data validation and transformation
      if (finalConfig.validateData && !finalConfig.validateData(rawData)) {
        const error = new IPCError(
          'Invalid data structure received from API',
          'INVALID_DATA'
        );
        timer.complete(false, undefined, error.message);
        return { success: false, error };
      }

      const finalData = finalConfig.transformData
        ? finalConfig.transformData(rawData)
        : rawData;

      timer.complete(true, finalData);
      return { success: true, data: finalData };
    } catch (error) {
      const ipcError =
        error instanceof IPCError ? error : new IPCError(String(error));
      timer.complete(false, undefined, ipcError.message);
      return { success: false, error: ipcError };
    }
  };
}
