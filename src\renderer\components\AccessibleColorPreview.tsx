// import React from 'react';
import { useAccessibleColor } from '../hooks/useAccessibleColor';
import { useColorBlindness } from '../context/ColorBlindnessContext';
import { getColorBlindnessDescription } from '../../shared/utils/color/colorBlindness';

interface AccessibleColorPreviewProps {
  hex: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  background?: string;
}

export function AccessibleColorPreview({
  hex,
  className = '',
  size = 'md',
  showLabel = false,
  background = '#FFFFFF',
}: AccessibleColorPreviewProps) {
  const { mode, isEnabled } = useColorBlindness();
  const accessibleColor = useAccessibleColor(hex, background);

  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-10 h-10',
    lg: 'w-16 h-16',
  };

  return (
    <div className='relative inline-block'>
      <div
        className={`${sizeClasses[size]} rounded-md border border-ui-border-light dark:border-ui-border-dark ${className}`}
        style={{
          backgroundColor: accessibleColor.display,
          boxShadow: 'inset 0 0 0 1px rgba(0,0,0,0.05)',
        }}
        title={
          isEnabled
            ? `${getColorBlindnessDescription(mode)} view of ${hex}`
            : hex
        }
      />

      {showLabel && isEnabled && mode !== 'normal' && (
        <div className='absolute -bottom-6 left-0 right-0 text-center'>
          <span className='text-xs text-ui-foreground-secondary'>
            {mode === 'achromatopsia' ? 'B&W' : mode.substring(0, 4)}
          </span>
        </div>
      )}

      {/* Warning indicator for low contrast */}
      {accessibleColor.simulatedContrast < 4.5 && isEnabled && (
        <div
          className='absolute -top-1 -right-1 w-3 h-3 bg-feedback-warning rounded-full'
          title='Low contrast in color blind mode'
        />
      )}
    </div>
  );
}
