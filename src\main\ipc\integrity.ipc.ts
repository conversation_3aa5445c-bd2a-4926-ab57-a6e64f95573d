/**
 * @file integrity.ipc.ts
 * @description IPC handlers for relationship integrity checks and cleanup
 */

import { ipcMain } from 'electron';
import { getDatabase } from '../db/database';
import { createRelationshipIntegrityManager } from '../utils/relationship-integrity';
import { validateRequired } from '../utils/input-validation';
import { registerHandlerSafely } from '../utils/ipcRegistry';

/**
 * Register relationship integrity IPC handlers
 */
export function registerIntegrityIpcHandlers(): void {
  const db = getDatabase();
  const integrityManager = createRelationshipIntegrityManager(db);

  // Run comprehensive integrity checks
  registerHandlerSafely(
    ipcMain,
    'integrity:run-checks',
    async (_, organizationId: string) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return integrityManager.runIntegrityChecks(validatedOrgId);
      } catch (error) {
        console.error('[IPC] Error running integrity checks:', error);
        return {
          timestamp: new Date().toISOString(),
          organizationId,
          checks: [],
          orphanCleanup: [],
          totalIssues: 0,
          totalCleaned: 0,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    }
  );

  // Fix invalid color spaces
  registerHandlerSafely(
    ipcMain,
    'integrity:fix-color-spaces',
    async (_, organizationId: string) => {
      try {
        const validatedOrgId = validateRequired(
          'organizationId',
          organizationId,
          'uuid'
        ) as string;
        return integrityManager.fixInvalidColorSpaces(validatedOrgId);
      } catch (error) {
        console.error('[IPC] Error fixing color spaces:', error);
        return {
          fixed: 0,
          errors: [error instanceof Error ? error.message : String(error)],
        };
      }
    }
  );

  console.log('[IPC] Relationship integrity handlers registered');
}
