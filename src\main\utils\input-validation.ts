/**
 * Shared Input Validation Utilities
 * Provides consistent validation patterns across all services
 */

import { Result, success, failure, ValidationResult as TypedValidationResult, ValidationError } from '../../shared/types/result.types';
import { isNotNullish, isNonEmptyString, isValidNumber } from '../../shared/types/type-guards';

export interface ValidationResult<T = unknown> {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: T;
}

export interface ValidationRule<T = unknown> {
  field: string;
  value: T;
  rules: ValidationRuleType[];
}

export type ValidationRuleType = 
  | { type: 'required' }
  | { type: 'string'; minLength?: number; maxLength?: number }
  | { type: 'uuid' }
  | { type: 'email' }
  | { type: 'url' }
  | { type: 'number'; min?: number; max?: number }
  | { type: 'boolean' }
  | { type: 'array'; minItems?: number; maxItems?: number }
  | { type: 'hex-color' }
  | { type: 'enum'; values: string[] }
  | { type: 'custom'; validator: (value: unknown) => boolean; message: string };

/**
 * Core input validation service
 */
export class InputValidator {
  /**
   * Validate a single field
   */
  static validateField<T>(field: string, value: T, rules: ValidationRuleType[]): ValidationResult<T> {
    const errors: string[] = [];
    let sanitizedValue = value;

    for (const rule of rules) {
      const result = this.applyRule(field, value, rule);
      if (!result.isValid) {
        errors.push(...result.errors);
      }
      if (result.sanitizedValue !== undefined) {
        sanitizedValue = result.sanitizedValue as T;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue
    };
  }

  /**
   * Validate multiple fields
   */
  static validateFields(rules: ValidationRule[]): ValidationResult<Record<string, unknown>> {
    const allErrors: string[] = [];
    const sanitizedValues: Record<string, unknown> = {};

    for (const rule of rules) {
      const result = this.validateField(rule.field, rule.value, rule.rules);
      if (!result.isValid) {
        allErrors.push(...result.errors);
      }
      if (result.sanitizedValue !== undefined) {
        sanitizedValues[rule.field] = result.sanitizedValue;
      }
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      sanitizedValue: sanitizedValues
    };
  }

  /**
   * Apply a single validation rule
   */
  private static applyRule(field: string, value: unknown, rule: ValidationRuleType): ValidationResult {
    switch (rule.type) {
      case 'required':
        return this.validateRequired(field, value);
      
      case 'string':
        return this.validateString(field, value, rule.minLength, rule.maxLength);
      
      case 'uuid':
        return this.validateUUID(field, value);
      
      case 'email':
        return this.validateEmail(field, value);
      
      case 'url':
        return this.validateURL(field, value);
      
      case 'number':
        return this.validateNumber(field, value, rule.min, rule.max);
      
      case 'boolean':
        return this.validateBoolean(field, value);
      
      case 'array':
        return this.validateArray(field, value, rule.minItems, rule.maxItems);
      
      case 'hex-color':
        return this.validateHexColor(field, value);
      
      case 'enum':
        return this.validateEnum(field, value, rule.values);
      
      case 'custom':
        return this.validateCustom(field, value, rule.validator, rule.message);
      
      default:
        return { isValid: true, errors: [] };
    }
  }

  private static validateRequired(field: string, value: unknown): ValidationResult {
    const isValid = value !== null && value !== undefined && value !== '';
    return {
      isValid,
      errors: isValid ? [] : [`${field} is required`]
    };
  }

  private static validateString(field: string, value: unknown, minLength?: number, maxLength?: number): ValidationResult {
    const errors: string[] = [];
    
    if (typeof value !== 'string') {
      return { isValid: false, errors: [`${field} must be a string`] };
    }

    if (minLength !== undefined && value.length < minLength) {
      errors.push(`${field} must be at least ${minLength} characters`);
    }

    if (maxLength !== undefined && value.length > maxLength) {
      errors.push(`${field} must be no more than ${maxLength} characters`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: value.trim()
    };
  }

  private static validateUUID(field: string, value: unknown): ValidationResult {
    if (typeof value !== 'string') {
      return { isValid: false, errors: [`${field} must be a string`] };
    }

    // More lenient UUID validation - accepts any valid UUID format (v1, v3, v4, v5)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const isValid = uuidRegex.test(value);

    return {
      isValid,
      errors: isValid ? [] : [`${field} must be a valid UUID`],
      sanitizedValue: value.toLowerCase()
    };
  }

  private static validateEmail(field: string, value: unknown): ValidationResult {
    if (typeof value !== 'string') {
      return { isValid: false, errors: [`${field} must be a string`] };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(value);

    return {
      isValid,
      errors: isValid ? [] : [`${field} must be a valid email address`],
      sanitizedValue: value.toLowerCase().trim()
    };
  }

  private static validateURL(field: string, value: unknown): ValidationResult {
    if (typeof value !== 'string') {
      return { isValid: false, errors: [`${field} must be a string`] };
    }

    try {
      new URL(value);
      return { isValid: true, errors: [], sanitizedValue: value.trim() };
    } catch {
      return { isValid: false, errors: [`${field} must be a valid URL`] };
    }
  }

  private static validateNumber(field: string, value: unknown, min?: number, max?: number): ValidationResult {
    const errors: string[] = [];
    
    if (typeof value !== 'number' || isNaN(value)) {
      return { isValid: false, errors: [`${field} must be a number`] };
    }

    if (min !== undefined && value < min) {
      errors.push(`${field} must be at least ${min}`);
    }

    if (max !== undefined && value > max) {
      errors.push(`${field} must be no more than ${max}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: value
    };
  }

  private static validateBoolean(field: string, value: unknown): ValidationResult {
    if (typeof value !== 'boolean') {
      return { isValid: false, errors: [`${field} must be a boolean`] };
    }

    return { isValid: true, errors: [], sanitizedValue: value };
  }

  private static validateArray(field: string, value: unknown, minItems?: number, maxItems?: number): ValidationResult {
    const errors: string[] = [];
    
    if (!Array.isArray(value)) {
      return { isValid: false, errors: [`${field} must be an array`] };
    }

    if (minItems !== undefined && value.length < minItems) {
      errors.push(`${field} must have at least ${minItems} items`);
    }

    if (maxItems !== undefined && value.length > maxItems) {
      errors.push(`${field} must have no more than ${maxItems} items`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: value
    };
  }

  private static validateHexColor(field: string, value: unknown): ValidationResult {
    if (typeof value !== 'string') {
      return { isValid: false, errors: [`${field} must be a string`] };
    }

    const hexRegex = /^#[0-9A-Fa-f]{6}$/;
    const isValid = hexRegex.test(value);

    return {
      isValid,
      errors: isValid ? [] : [`${field} must be a valid hex color (e.g., #FF0000)`],
      sanitizedValue: value.toUpperCase()
    };
  }

  private static validateEnum(field: string, value: unknown, allowedValues: string[]): ValidationResult {
    if (typeof value !== 'string') {
      return {
        isValid: false,
        errors: [`${field} must be a string`],
        sanitizedValue: value
      };
    }

    const isValid = allowedValues.includes(value);

    return {
      isValid,
      errors: isValid ? [] : [`${field} must be one of: ${allowedValues.join(', ')}`],
      sanitizedValue: value
    };
  }

  private static validateCustom(field: string, value: unknown, validator: (value: unknown) => boolean, message: string): ValidationResult {
    const isValid = validator(value);

    return {
      isValid,
      errors: isValid ? [] : [`${field}: ${message}`],
      sanitizedValue: value
    };
  }
}

/**
 * Common validation schemas for ChromaSync entities
 */
export const ValidationSchemas = {
  /**
   * Organization ID validation
   */
  organizationId: [
    { type: 'required' as const },
    { type: 'string' as const, minLength: 3, maxLength: 50 },
    { type: 'uuid' as const }
  ],

  /**
   * User ID validation
   */
  userId: [
    { type: 'required' as const },
    { type: 'string' as const, minLength: 3, maxLength: 50 }
  ],

  /**
   * Color hex validation
   */
  hexColor: [
    { type: 'required' as const },
    { type: 'hex-color' as const }
  ],

  /**
   * Product name validation
   */
  productName: [
    { type: 'required' as const },
    { type: 'string' as const, minLength: 1, maxLength: 100 }
  ],

  /**
   * Color name validation
   */
  colorName: [
    { type: 'required' as const },
    { type: 'string' as const, minLength: 1, maxLength: 100 }
  ],

  /**
   * CMYK values validation
   */
  cmykValue: [
    { type: 'required' as const },
    { type: 'number' as const, min: 0, max: 100 }
  ],

  /**
   * File type validation
   */
  fileType: [
    { type: 'required' as const },
    { type: 'enum' as const, values: ['pdf', 'docx', 'xlsx', 'pptx', 'link', 'other'] }
  ],

  /**
   * URL validation
   */
  url: [
    { type: 'required' as const },
    { type: 'url' as const }
  ],

  /**
   * Email validation
   */
  email: [
    { type: 'required' as const },
    { type: 'email' as const }
  ]
};

/**
 * Helper function to validate and return sanitized values
 */
export function validateAndSanitize(rules: ValidationRule[]): Record<string, unknown> {
  const validation = InputValidator.validateFields(rules);
  
  if (!validation.isValid) {
    throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
  }

  return validation.sanitizedValue || {};
}

/**
 * Quick validation for common cases
 */
export function validateRequired(field: string, value: unknown, type: 'string' | 'uuid' | 'number' = 'string'): unknown {
  const rules: ValidationRuleType[] = [{ type: 'required' as const }];
  
  if (type === 'string') {
    rules.push({ type: 'string' as const, minLength: 1 });
  } else if (type === 'uuid') {
    rules.push({ type: 'uuid' as const });
  } else if (type === 'number') {
    rules.push({ type: 'number' as const });
  }

  const validation = InputValidator.validateField(field, value, rules);
  
  if (!validation.isValid) {
    throw new Error(`Invalid ${field}: ${validation.errors.join(', ')}`);
  }

  return validation.sanitizedValue;
}

/**
 * Service method decorator for automatic input validation
 */
export function ValidateInput(validationRules: ValidationRule[]) {
  return function <T extends (...args: any[]) => any>(
    _target: unknown, 
    _propertyName: string, 
    descriptor: TypedPropertyDescriptor<T>
  ): TypedPropertyDescriptor<T> {
    const method = descriptor.value;

    if (method) {
      descriptor.value = (function (this: unknown, ...args: unknown[]) {
        // Map arguments to validation rules
        const rulesToApply = validationRules.map((rule, index) => ({
          ...rule,
          value: args[index]
        }));

        const validation = InputValidator.validateFields(rulesToApply);
        
        if (!validation.isValid) {
          throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
        }

        // Replace arguments with sanitized values
        if (validation.sanitizedValue) {
          validationRules.forEach((rule, index) => {
            if (validation.sanitizedValue && validation.sanitizedValue[rule.field] !== undefined) {
              args[index] = validation.sanitizedValue[rule.field];
            }
          });
        }

        return method.apply(this, args);
      } as any) as T;
    }

    return descriptor;
  };
}