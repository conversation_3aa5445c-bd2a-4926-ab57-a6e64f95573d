/**
 * @file oauth-service.ts
 * @description Refactored OAuth service using modular architecture with focused services
 */

import { app } from 'electron';
import { createSafeStore } from '../../utils/store-util';
import { getDatabase } from '../../db/database';
import { OrganizationService } from '../../db/services/organization.service';
import { setCurrentOrganization } from '../../utils/organization-context';
import {
  LoggerFactory,
  logPerformance,
  logErrors,
  ILogger,
} from '../../utils/logger.service';
import { sentryService } from '../sentry.service';

// Import focused services
import {
  AuthenticationManager,
  AuthFlowResult,
} from './authentication-manager';
import {
  SessionManager,
  SessionConfig,
  SessionEvents,
} from './session-manager';
import { AuthErrorRecoveryService } from './auth-error-recovery.service';
import { AuthNotificationService } from './auth-notification.service';

export interface AuthResult {
  success: boolean;
  status?:
    | 'needs_organization_setup'
    | 'needs_organization_selection'
    | 'authenticated';
  organizations?: any[];
  error?: string;
}

export interface OAuthConfiguration {
  authTimeoutMs?: number;
  sessionConfig?: SessionConfig;
  useLocalRedirect?: boolean;
  enableCircuitBreaker?: boolean;
  enableRetryLogic?: boolean;
}

/**
 * Refactored OAuth service with modular architecture
 */
export class OAuthService {
  private readonly logger: ILogger;
  private readonly store = createSafeStore<Record<string, any>>({
    name: 'oauth-service',
  });

  // Focused services
  private readonly authManager: AuthenticationManager;
  private readonly sessionManager: SessionManager;
  private readonly errorRecovery: AuthErrorRecoveryService;
  private readonly notifications: AuthNotificationService;

  // Configuration
  private readonly CONFIG_KEYS = {
    AUTH_TIMEOUT: 'oauth.authTimeout',
    USE_LOCAL_REDIRECT: 'oauth.useLocalRedirect',
    CIRCUIT_BREAKER_ENABLED: 'oauth.circuitBreakerEnabled',
    RETRY_LOGIC_ENABLED: 'oauth.retryLogicEnabled',
  } as const;

  // Default configuration - auto-configure based on environment
  private readonly DEFAULT_CONFIG = {
    authTimeoutMs: app.isPackaged ? 180000 : 120000, // 3 minutes for packaged, 2 for dev
    useLocalRedirect: true, // Use local redirect for better callback handling
    enableCircuitBreaker: true,
    enableRetryLogic: true,
  };

  constructor(logger?: ILogger) {
    this.logger =
      logger || LoggerFactory.getInstance().createLogger('OAuthService');

    // Log environment details for debugging
    this.logger.info('OAuth service initialization', {
      isPackaged: app.isPackaged,
      platform: process.platform,
      resourcesPath: app.isPackaged ? process.resourcesPath : 'N/A',
      defaultConfig: this.DEFAULT_CONFIG,
    });

    // Initialize focused services
    this.authManager = new AuthenticationManager(this.logger);
    this.sessionManager = new SessionManager(this.logger);
    this.errorRecovery = new AuthErrorRecoveryService(this.logger);
    this.notifications = new AuthNotificationService(this.logger);

    this.logger.info('OAuth service initialized with modular architecture');
  }

  /**
   * Sign in with Google using orchestrated services
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('OAuthService'),
    'signInWithGoogle'
  )
  @logErrors(LoggerFactory.getInstance().createLogger('OAuthService'))
  async signInWithGoogle(): Promise<AuthResult> {
    this.logger.info('Starting Google sign-in flow', {
      operation: 'signInWithGoogle',
    });

    try {
      // Check circuit breaker with detailed logging
      const circuitBreakerStatus = this.errorRecovery.getCircuitBreakerStatus();
      this.logger.info('Circuit breaker status check', {
        isOpen: circuitBreakerStatus.isOpen,
        failureCount: circuitBreakerStatus.failureCount,
        cooldownRemaining: circuitBreakerStatus.cooldownRemaining,
        recentAttempts: circuitBreakerStatus.recentAttempts,
        operation: 'signInWithGoogle',
      });

      if (
        this.isCircuitBreakerEnabled() &&
        this.errorRecovery.isAuthenticationBlocked()
      ) {
        const cooldownMinutes =
          this.errorRecovery.getCooldownRemainingMinutes();
        const message = `Authentication temporarily disabled due to repeated failures. Please wait ${cooldownMinutes} minutes and try again.`;

        this.logger.warn('Authentication blocked by circuit breaker', {
          cooldownMinutes,
          failureCount: circuitBreakerStatus.failureCount,
          operation: 'signInWithGoogle',
        });

        this.notifications.showCircuitBreakerNotification(cooldownMinutes);

        return this.createAuthResult(false, message);
      }

      // Log OAuth configuration being used
      this.logger.info('Starting OAuth flow with configuration', {
        useLocalRedirect: this.isLocalRedirectEnabled(),
        authTimeout: this.getAuthTimeout(),
        circuitBreakerEnabled: this.isCircuitBreakerEnabled(),
        operation: 'signInWithGoogle',
      });

      // Execute authentication with retry logic if enabled
      const authResult = this.isRetryLogicEnabled()
        ? await this.errorRecovery.executeWithRetry(
            () => this.performAuthentication(),
            'google_signin'
          )
        : await this.performAuthentication();

      if (authResult.success) {
        // Handle post-authentication
        const result = await this.handlePostAuthentication(authResult.user);

        // Start session monitoring
        this.startSessionMonitoring();

        return result;
      } else {
        // Record failed attempt
        this.errorRecovery.recordAuthAttempt(false, authResult.error);

        return this.createAuthResult(false, authResult.error);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Authentication failed';
      this.logger.error('Sign-in failed', error as Error, {
        operation: 'signInWithGoogle',
      });

      // Send critical auth errors to Sentry
      if (sentryService.isEnabled()) {
        sentryService.captureError(error as Error, {
          service: 'OAuthService',
          operation: 'signInWithGoogle',
          additionalData: { errorMessage },
        });
      }

      // Record failed attempt
      this.errorRecovery.recordAuthAttempt(false, errorMessage);

      return this.createAuthResult(
        false,
        this.mapAuthError(errorMessage, 'initialization')
      );
    }
  }

  /**
   * Sign out user
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('OAuthService'),
    'signOut'
  )
  async signOut(): Promise<void> {
    this.logger.info('Signing out user', { operation: 'signOut' });

    try {
      // Stop session monitoring
      this.sessionManager.endSession();

      // Sign out from authentication manager
      await this.authManager.signOut();

      // Clear organization context
      setCurrentOrganization(null);

      // Clear store but preserve configuration
      this.clearAuthDataPreserveConfig();

      // Close notification windows
      this.notifications.cleanup();

      this.logger.info('Sign out completed successfully', {
        operation: 'signOut',
      });
    } catch (error) {
      this.logger.error('Sign out failed', error as Error, {
        operation: 'signOut',
      });
      throw error;
    }
  }

  /**
   * Restore session from stored credentials
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('OAuthService'),
    'restoreSession'
  )
  async restoreSession(): Promise<AuthResult | null> {
    this.logger.info('Attempting to restore session', {
      operation: 'restoreSession',
    });

    try {
      // First check if there's a stored session
      const session = await this.authManager.getCurrentSession();

      if (session && session.user) {
        // Check if session is expired
        if (this.sessionManager.isSessionExpired(session)) {
          this.logger.info('Session expired, signing out', {
            operation: 'restoreSession',
          });
          await this.signOut();
          return null;
        }

        this.logger.info('Session restored successfully', {
          userEmail: session.user.email,
          operation: 'restoreSession',
        });

        // Handle post-authentication
        const result = await this.handlePostAuthentication(session.user);

        // Start session monitoring
        this.startSessionMonitoring();

        return result;
      }

      // If no session found, try to refresh from stored tokens
      this.logger.info(
        'No active session found, attempting refresh from stored tokens'
      );
      try {
        const refreshResult = await this.authManager.refreshSession();
        if (refreshResult.success && refreshResult.session) {
          this.logger.info('Session refreshed from stored tokens', {
            userEmail: refreshResult.user?.email,
            operation: 'restoreSession',
          });

          // Handle post-authentication
          const result = await this.handlePostAuthentication(
            refreshResult.user
          );

          // Start session monitoring
          this.startSessionMonitoring();

          return result;
        }
      } catch (refreshError) {
        this.logger.debug('Token refresh failed during session restoration', {
          error:
            refreshError instanceof Error
              ? refreshError
              : new Error(String(refreshError)),
        });
        // This is expected if user hasn't authenticated yet
      }

      this.logger.info('No session to restore', {
        operation: 'restoreSession',
      });
      return null;
    } catch (error) {
      this.logger.error('Session restoration failed', error as Error, {
        operation: 'restoreSession',
      });
      return null;
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<any> {
    return await this.authManager.getCurrentUser();
  }

  /**
   * Get current session
   */
  async getSession(): Promise<any> {
    return await this.authManager.getCurrentSession();
  }

  /**
   * Update user activity (for session monitoring)
   */
  updateActivity(): void {
    this.sessionManager.updateActivity();
  }

  /**
   * Configure OAuth service
   */
  configure(config: OAuthConfiguration): void {
    this.logger.info('Configuring OAuth service', {
      config,
      operation: 'configure',
    });

    if (config.authTimeoutMs !== undefined) {
      this.store.set(this.CONFIG_KEYS.AUTH_TIMEOUT, config.authTimeoutMs);
    }

    if (config.useLocalRedirect !== undefined) {
      this.store.set(
        this.CONFIG_KEYS.USE_LOCAL_REDIRECT,
        config.useLocalRedirect
      );
    }

    if (config.enableCircuitBreaker !== undefined) {
      this.store.set(
        this.CONFIG_KEYS.CIRCUIT_BREAKER_ENABLED,
        config.enableCircuitBreaker
      );
    }

    if (config.enableRetryLogic !== undefined) {
      this.store.set(
        this.CONFIG_KEYS.RETRY_LOGIC_ENABLED,
        config.enableRetryLogic
      );
    }

    if (config.sessionConfig) {
      this.sessionManager.configure(config.sessionConfig);
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): OAuthConfiguration & {
    sessionConfig: Required<SessionConfig>;
  } {
    return {
      authTimeoutMs: this.getAuthTimeout(),
      useLocalRedirect: this.isLocalRedirectEnabled(),
      enableCircuitBreaker: this.isCircuitBreakerEnabled(),
      enableRetryLogic: this.isRetryLogicEnabled(),
      sessionConfig: this.sessionManager.getConfiguration(),
    };
  }

  /**
   * Attempt recovery from authentication issues
   */
  async attemptRecovery(): Promise<{ success: boolean; message: string }> {
    this.logger.info('Attempting authentication recovery', {
      operation: 'attemptRecovery',
    });

    try {
      const result = await this.errorRecovery.attemptRecovery();

      if (result.success) {
        // Reset all services
        this.sessionManager.endSession();
        this.notifications.cleanup();

        this.notifications.showRecoveryNotification();
      }

      return result;
    } catch (error) {
      this.logger.error('Recovery attempt failed', error as Error, {
        operation: 'attemptRecovery',
      });
      return {
        success: false,
        message: 'Recovery failed. Please restart the application.',
      };
    }
  }

  /**
   * Get authentication health status
   */
  getHealthStatus(): {
    healthy: boolean;
    authManager: any;
    sessionManager: any;
    errorRecovery: any;
    issues: string[];
  } {
    const authStatus = this.authManager.getHealthStatus();
    const sessionStatus = this.sessionManager.getSessionStatus();
    const circuitStatus = this.errorRecovery.getCircuitBreakerStatus();

    const issues: string[] = [];

    if (!authStatus.healthy) {
      issues.push('Authentication manager unhealthy');
    }

    if (sessionStatus.isExpired) {
      issues.push('Session expired');
    }

    if (circuitStatus.isOpen) {
      issues.push(
        `Circuit breaker open (${Math.ceil(circuitStatus.cooldownRemaining / 60000)} minutes remaining)`
      );
    }

    return {
      healthy: issues.length === 0,
      authManager: authStatus,
      sessionManager: sessionStatus,
      errorRecovery: circuitStatus,
      issues,
    };
  }

  /**
   * Reset authentication loop detection (for IPC compatibility)
   */
  resetAuthLoopDetection(): void {
    this.logger.info('Resetting authentication loop detection', {
      operation: 'resetAuthLoopDetection',
    });
    this.errorRecovery.resetAuthenticationState();
  }

  /**
   * Attempt authentication recovery (for IPC compatibility)
   */
  async attemptAuthRecovery(): Promise<{ success: boolean; message: string }> {
    return await this.attemptRecovery();
  }

  /**
   * Check authentication health (for IPC compatibility)
   */
  async checkAuthHealth(): Promise<any> {
    return this.getHealthStatus();
  }

  /**
   * Configure OAuth service (for IPC compatibility)
   */
  configureOAuth(config: OAuthConfiguration): void {
    this.configure(config);
  }

  /**
   * Cleanup all services
   */
  cleanup(): void {
    this.logger.info('Cleaning up OAuth service');

    this.authManager.cleanup();
    this.sessionManager.cleanup();
    this.errorRecovery.cleanup();
    this.notifications.cleanup();
  }

  // Private methods

  private async performAuthentication(): Promise<AuthFlowResult> {
    // Show progress notification
    this.notifications.showAuthProgress({
      title: 'Authenticating with Google',
      message: 'Please complete the sign-in process in your browser.',
      allowCancel: true,
      onCancel: () => {
        this.logger.info('Authentication cancelled by user');
        this.authManager.cleanup();
      },
    });

    try {
      // Initiate OAuth flow
      const { authUrl, redirectHandler } =
        await this.authManager.initiateOAuthFlow({
          provider: 'google',
          useLocalRedirect: this.isLocalRedirectEnabled(),
          timeoutMs: this.getAuthTimeout(),
        });

      // Open browser
      await this.authManager.openOAuthUrlInBrowser(authUrl);

      // Wait for callback with timeout
      return new Promise<AuthFlowResult>((resolve, reject) => {
        let callbackReceived = false;

        // Set up callback handler
        if (redirectHandler) {
          redirectHandler.start(async (callbackUrl: string) => {
            if (callbackReceived) {
              return;
            }
            callbackReceived = true;

            try {
              this.logger.info('OAuth callback received', {
                callbackUrl: `${callbackUrl.substring(0, 50)  }...`,
              });
              const result = await this.authManager.handleCallback(callbackUrl);
              this.notifications.closeAuthProgress();
              this.logger.info('OAuth callback processed successfully', {
                success: result.success,
              });
              resolve(result);
            } catch (error) {
              this.logger.error(
                'OAuth callback processing failed',
                error as Error
              );
              this.notifications.closeAuthProgress();
              reject(error);
            }
          });
        } else {
          // No redirect handler available - might be using web redirect
          this.logger.warn('No redirect handler available for OAuth callback');
          callbackReceived = true;
          this.notifications.closeAuthProgress();
          reject(
            new Error('No redirect handler configured for OAuth callback')
          );
        }

        // Set timeout
        setTimeout(() => {
          if (!callbackReceived) {
            callbackReceived = true;
            this.notifications.closeAuthProgress();
            this.authManager.cleanup();
            this.logger.error(
              'OAuth authentication timeout - callback not received within timeout period'
            );
            reject(new Error('Authentication timeout - callback not received'));
          }
        }, this.getAuthTimeout());
      });
    } catch (error) {
      this.notifications.closeAuthProgress();
      throw error;
    }
  }

  private async handlePostAuthentication(user: any): Promise<AuthResult> {
    this.logger.info('Handling post-authentication', {
      userEmail: user?.email,
      operation: 'handlePostAuthentication',
    });

    // CRITICAL: Validate user has proper email before proceeding
    if (!user?.email) {
      this.logger.error(
        'Authentication failed: User has no email address',
        undefined,
        {
          userId: user?.id,
          userMetadata: user?.user_metadata,
          operation: 'handlePostAuthentication',
        }
      );
      return this.createAuthResult(
        false,
        'Authentication failed: User email is required'
      );
    }

    // Sync user to local database - CRITICAL: Must complete before organization setup
    await this.syncUserToLocal(user);

    // Check database availability
    const db = getDatabase();
    if (!db || typeof db.prepare !== 'function') {
      this.logger.error('Database not available for organization service');
      setCurrentOrganization(null);
      return this.createAuthResult(
        true,
        undefined,
        'needs_organization_setup',
        []
      );
    }

    // Handle organization setup
    const orgService = new OrganizationService(db);

    try {
      // Sync user profile
      await orgService.syncUserProfileToLocal(
        user.id,
        user.email,
        user.user_metadata?.full_name
      );

      // CRITICAL: Clean up any existing placeholder emails for this user
      await orgService.cleanupPlaceholderEmails(user.id, user.email);

      // Sync organizations from Supabase (with local fallback)
      const supabaseOrgs = await orgService.syncOrganizationsFromSupabase(
        user.id
      );

      // Get user organizations (this may return more than just Supabase orgs due to fallback)
      const organizations = await orgService.getOrganizationsForUser(user.id);

      this.logger.info('Organizations retrieved', {
        count: organizations.length,
        supabaseCount: supabaseOrgs.length,
        hasLocalFallback: organizations.length > supabaseOrgs.length,
        operation: 'handlePostAuthentication',
      });

      // BULLETPROOF SYNC: Ensure complete data sync after authentication
      if (organizations.length > 0) {
        // Use the first available organization for sync (user will set the correct one if multiple)
        const primaryOrgId = organizations[0]?.external_id;

        if (!primaryOrgId) {
          this.logger.error('No valid organization ID found for initial sync');
          return {
            success: false,
            error: 'No valid organization ID found for initial sync',
          };
        }

        // Type assertion: primaryOrgId is guaranteed to be defined after the null check above
        const validOrgId: string = primaryOrgId;

        this.logger.info('Checking for initial data sync needs', {
          organizationId: validOrgId,
          operation: 'handlePostAuthentication',
        });

        try {
          // Check if this is a fresh database
          const db = getDatabase();
          const dataCheck = db
            ?.prepare(
              `
            SELECT 
              (SELECT COUNT(*) FROM colors WHERE organization_id = ?) as colors,
              (SELECT COUNT(*) FROM products WHERE organization_id = ?) as products,
              (SELECT COUNT(*) FROM product_colors) as product_colors
          `
            )
            .get(validOrgId, validOrgId) as any;

          const needsInitialSync =
            dataCheck &&
            (dataCheck.colors === 0 ||
              dataCheck.products === 0 ||
              dataCheck.product_colors === 0);

          if (needsInitialSync) {
            this.logger.info(
              'Fresh database detected - performing initial data sync',
              {
                organizationId: validOrgId,
              }
            );

            const { performInitialDataSync } = await import(
              '../sync/initial-data-sync'
            );
            const syncResult = await performInitialDataSync(
              user.id,
              validOrgId
            );

            if (syncResult.success) {
              this.logger.info('Initial data sync completed', {
                colors: syncResult.colors,
                products: syncResult.products,
                productColors: syncResult.productColors,
              });
            } else {
              this.logger.error('Initial data sync had errors', undefined, {
                errors: syncResult.errors,
              });
            }
          } else {
            // Initialize unified sync manager for this organization
            const { unifiedSyncManager } = await import(
              '../sync/unified-sync-manager'
            );
            await unifiedSyncManager.initialize(user.id, validOrgId);
            this.logger.info(
              'Unified sync manager initialized after authentication'
            );
          }
        } catch (syncError) {
          this.logger.error(
            'Failed to sync data after authentication',
            syncError instanceof Error
              ? syncError
              : new Error(String(syncError)),
            {
              organizationId: validOrgId,
            }
          );
          // Continue execution - this is non-critical for authentication
        }
      }

      return this.determineAuthStatus(organizations);
    } catch (error) {
      this.logger.error('Post-authentication handling failed', error as Error, {
        operation: 'handlePostAuthentication',
      });

      return this.createAuthResult(false, 'Failed to set up user account');
    }
  }

  private determineAuthStatus(organizations: any[]): AuthResult {
    if (organizations.length === 0) {
      setCurrentOrganization(null);
      return this.createAuthResult(
        true,
        undefined,
        'needs_organization_setup',
        []
      );
    } else if (organizations.length === 1) {
      // ALWAYS show organization selection screen, even for single org
      // This ensures proper UI data loading and user awareness
      setCurrentOrganization(null);
      return this.createAuthResult(
        true,
        undefined,
        'needs_organization_selection',
        organizations
      );
    } else {
      // Check for stored organization preference
      const storedOrgId = this.store.get('currentOrganizationId') as string;
      const validOrg = organizations.find(
        org => org.external_id === storedOrgId
      );

      if (validOrg) {
        // ALWAYS show organization selection screen for proper data loading
        setCurrentOrganization(null);
        return this.createAuthResult(
          true,
          undefined,
          'needs_organization_selection',
          organizations
        );
      } else {
        return this.createAuthResult(
          true,
          undefined,
          'needs_organization_selection',
          organizations
        );
      }
    }
  }

  private startSessionMonitoring(): void {
    const sessionEvents: SessionEvents = {
      onSessionWarning: (minutesRemaining: number) => {
        this.notifications.showSessionWarning(minutesRemaining);
      },
      onSessionExpired: (reason: string) => {
        this.notifications.showSessionExpired(reason);
      },
      onActivityDetected: () => {
        this.logger.debug('User activity detected');
      },
    };

    this.sessionManager.startSession(sessionEvents);
  }

  private async syncUserToLocal(user: any): Promise<void> {
    try {
      const db = getDatabase();
      if (!db || typeof db.prepare !== 'function') {
        const error = 'Database not available for user sync';
        this.logger.error(error);
        throw new Error(error);
      }

      // Check if metadata column exists, add it if missing
      try {
        db.prepare('SELECT metadata FROM users LIMIT 1').all();
      } catch (error) {
        this.logger.info('Adding missing metadata column to users table');
        try {
          db.prepare(
            'ALTER TABLE users ADD COLUMN metadata TEXT DEFAULT "{}"'
          ).run();
        } catch (alterError) {
          this.logger.warn('Could not add metadata column:', {
            error:
              alterError instanceof Error
                ? alterError
                : new Error(String(alterError)),
          });
        }
      }

      const existing = db
        .prepare('SELECT id FROM users WHERE id = ?')
        .get(user.id);

      if (!existing) {
        // Use INSERT without metadata if column doesn't exist
        try {
          // CRITICAL: Never sync users without proper email - this causes confusion
          if (!user.email) {
            this.logger.error(
              'Cannot sync user without email address',
              undefined,
              { userId: user.id }
            );
            throw new Error('User email is required for sync');
          }

          db.prepare(
            `
            INSERT INTO users (id, email, name, avatar_url, metadata)
            VALUES (?, ?, ?, ?, ?)
          `
          ).run(
            user.id,
            user.email,
            user.user_metadata?.full_name || user.email.split('@')[0] || 'User',
            user.user_metadata?.avatar_url || '',
            JSON.stringify(user.user_metadata || {})
          );
        } catch (insertError) {
          // Fallback without metadata column
          this.logger.warn('Failed to insert with metadata, trying without:', {
            error:
              insertError instanceof Error
                ? insertError
                : new Error(String(insertError)),
          });
          db.prepare(
            `
            INSERT INTO users (id, email, name, avatar_url)
            VALUES (?, ?, ?, ?)
          `
          ).run(
            user.id,
            user.email, // Email already validated above
            user.user_metadata?.full_name || user.email.split('@')[0] || 'User',
            user.user_metadata?.avatar_url || ''
          );
        }
      } else {
        // Try update with metadata, fallback without
        try {
          db.prepare(
            `
            UPDATE users 
            SET email = ?, name = ?, avatar_url = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `
          ).run(
            user.email, // Email already validated above
            user.user_metadata?.full_name || user.email.split('@')[0] || 'User',
            user.user_metadata?.avatar_url || '',
            JSON.stringify(user.user_metadata || {}),
            user.id
          );
        } catch (updateError) {
          // Fallback update without metadata
          this.logger.warn('Failed to update with metadata, trying without:', {
            error:
              updateError instanceof Error
                ? updateError
                : new Error(String(updateError)),
          });
          db.prepare(
            `
            UPDATE users 
            SET email = ?, name = ?, avatar_url = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `
          ).run(
            user.email, // Email already validated above
            user.user_metadata?.full_name || user.email.split('@')[0] || 'User',
            user.user_metadata?.avatar_url || '',
            user.id
          );
        }
      }

      // Verify user was created/updated successfully
      const verifyUser = db
        .prepare('SELECT id, email FROM users WHERE id = ?')
        .get(user.id);
      if (!verifyUser) {
        const error = `Failed to create user record for ${user.id} (${user.email})`;
        this.logger.error(error);
        throw new Error(error);
      }

      this.logger.info('User successfully synced to local database', {
        userId: user.id,
        email: user.email,
        name: user.user_metadata?.full_name || 'Unknown',
      });
    } catch (error) {
      this.logger.error(
        'Failed to sync user to local database',
        error as Error
      );
      throw error; // Re-throw to ensure authentication fails if user sync fails
    }
  }

  private clearAuthDataPreserveConfig(): void {
    // Preserve OAuth configuration
    const config = {
      [this.CONFIG_KEYS.AUTH_TIMEOUT]: this.store.get(
        this.CONFIG_KEYS.AUTH_TIMEOUT
      ),
      [this.CONFIG_KEYS.USE_LOCAL_REDIRECT]: this.store.get(
        this.CONFIG_KEYS.USE_LOCAL_REDIRECT
      ),
      [this.CONFIG_KEYS.CIRCUIT_BREAKER_ENABLED]: this.store.get(
        this.CONFIG_KEYS.CIRCUIT_BREAKER_ENABLED
      ),
      [this.CONFIG_KEYS.RETRY_LOGIC_ENABLED]: this.store.get(
        this.CONFIG_KEYS.RETRY_LOGIC_ENABLED
      ),
    };

    // Clear store
    this.store.clear();

    // Restore configuration
    Object.entries(config).forEach(([key, value]) => {
      if (value !== undefined) {
        this.store.set(key, value);
      }
    });

    // Clear global organization
    delete (global as any).currentOrganizationId;
  }

  private createAuthResult(
    success: boolean,
    error?: string,
    status?: string,
    organizations?: any[]
  ): AuthResult {
    return {
      success,
      status: status as any,
      organizations: organizations || [],
      error: error ? this.mapAuthError(error) : undefined,
    };
  }

  private mapAuthError(error: string, context?: string): string {
    const errorLower = error.toLowerCase();

    // OAuth-specific errors
    if (
      errorLower.includes('access_denied') ||
      errorLower.includes('user cancelled')
    ) {
      return 'Sign-in was cancelled. Please try again to continue.';
    }

    if (errorLower.includes('timeout')) {
      return 'Sign-in is taking longer than expected. Please check your connection and try again.';
    }

    if (errorLower.includes('network') || errorLower.includes('fetch')) {
      return 'Unable to connect to sign-in service. Please check your internet connection and try again.';
    }

    if (errorLower.includes('temporarily disabled')) {
      return error; // Already user-friendly
    }

    // Default fallback
    return context === 'initialization'
      ? 'Unable to start the sign-in process. Please try again or restart ChromaSync.'
      : 'An unexpected error occurred during sign-in. Please try again or contact support if this persists.';
  }

  // Configuration getters

  private getAuthTimeout(): number {
    return (
      (this.store.get(this.CONFIG_KEYS.AUTH_TIMEOUT) as number) ||
      this.DEFAULT_CONFIG.authTimeoutMs
    );
  }

  private isLocalRedirectEnabled(): boolean {
    const stored = this.store.get(this.CONFIG_KEYS.USE_LOCAL_REDIRECT);
    return stored !== false; // Default true
  }

  private isCircuitBreakerEnabled(): boolean {
    const stored = this.store.get(this.CONFIG_KEYS.CIRCUIT_BREAKER_ENABLED);
    return stored !== false; // Default true
  }

  private isRetryLogicEnabled(): boolean {
    const stored = this.store.get(this.CONFIG_KEYS.RETRY_LOGIC_ENABLED);
    return stored !== false; // Default true
  }

  /**
   * Check if user has provided GDPR consent
   */
  async checkGDPRConsent(): Promise<boolean> {
    // Always return true for now - GDPR consent is handled in the UI
    // In a full implementation, this would check the Supabase user_consent table
    return true;
  }

  /**
   * Record GDPR consent (for IPC compatibility)
   */
  async recordGDPRConsent(ip?: string): Promise<void> {
    // For future implementation - record consent in Supabase user_consent table
    this.logger.info('GDPR consent recorded', {
      ip,
      operation: 'recordGDPRConsent',
    });
  }

  /**
   * Accept GDPR consent and continue with sync initialization
   * This method handles the post-consent flow
   */
  async acceptGDPRConsentAndContinue(
    ip?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.logger.info('Processing GDPR consent acceptance', {
        operation: 'acceptGDPRConsentAndContinue',
      });

      // Record the consent
      await this.recordGDPRConsent(ip);

      // Get current user and organization
      const user = await this.getCurrentUser();
      const { getCurrentOrganization } = await import(
        '../../utils/organization-context'
      );
      const currentOrgId = getCurrentOrganization();

      if (user && currentOrgId) {
        this.logger.info('Starting sync system after GDPR consent', {
          userId: user.id,
          organizationId: currentOrgId,
        });

        // Initialize sync system
        await this.initializeSyncSystem(user.id, currentOrgId);

        // Perform initial data sync if needed
        await this.performInitialDataSyncIfNeeded(user.id, currentOrgId);
      }

      return { success: true };
    } catch (error) {
      this.logger.error('Failed to process GDPR consent', error as Error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Complete login process with organization setup and sync initialization
   * This method orchestrates the full login flow including GDPR, organization setup, and sync
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('OAuthService'),
    'completeLoginProcess'
  )
  async completeLoginProcess(): Promise<{
    success: boolean;
    requiresConsent?: boolean;
    status?:
      | 'needs_organization_setup'
      | 'needs_organization_selection'
      | 'authenticated';
    organizations?: any[];
    error?: string;
  }> {
    this.logger.info('Starting complete login process', {
      operation: 'completeLoginProcess',
    });

    try {
      // Step 1: Authenticate with Google
      const authResult = await this.signInWithGoogle();
      this.logger.info('Google authentication completed', {
        success: authResult.success,
        status: authResult.status,
        operation: 'completeLoginProcess',
      });

      if (!authResult.success) {
        return authResult;
      }

      // Step 2: Check GDPR consent
      const hasConsent = await this.checkGDPRConsent();
      if (!hasConsent) {
        this.logger.info('GDPR consent required', {
          operation: 'completeLoginProcess',
        });
        return {
          success: true,
          requiresConsent: true,
          status: authResult.status,
          organizations: authResult.organizations,
        };
      }

      this.logger.info('GDPR consent confirmed', {
        operation: 'completeLoginProcess',
      });

      // Step 3: Handle organization setup if user is authenticated
      if (authResult.status === 'authenticated') {
        this.logger.info(
          'User authenticated, setting up organization and sync',
          { operation: 'completeLoginProcess' }
        );

        const setupResult = await this.setupOrganizationAndSync(
          authResult.organizations || []
        );
        if (!setupResult.success) {
          return {
            success: false,
            error: setupResult.error || 'Failed to setup organization and sync',
          };
        }
      }

      this.logger.info('Complete login process finished successfully', {
        operation: 'completeLoginProcess',
      });
      return {
        success: true,
        requiresConsent: false,
        status: authResult.status,
        organizations: authResult.organizations || [],
      };
    } catch (error) {
      this.logger.error('Complete login process failed', error as Error, {
        operation: 'completeLoginProcess',
      });
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Setup organization context and initialize sync system
   * @private
   */
  private async setupOrganizationAndSync(
    organizations: any[]
  ): Promise<{ success: boolean; error?: string }> {
    try {
      this.logger.info('Setting up organization and sync', {
        organizationCount: organizations.length,
        operation: 'setupOrganizationAndSync',
      });

      // Determine which organization to use
      let currentOrgId: string | null = null;

      if (organizations.length === 1) {
        // Single organization - use it
        currentOrgId = organizations[0].external_id;
      } else if (organizations.length > 1) {
        // Multiple organizations - check for stored preference
        const storedOrgId = this.store.get('currentOrganizationId') as string;
        const validOrg = organizations.find(
          org => org.external_id === storedOrgId
        );
        if (validOrg) {
          currentOrgId = storedOrgId;
        }
      }

      if (currentOrgId) {
        this.logger.info('Setting current organization', {
          organizationId: currentOrgId,
        });
        setCurrentOrganization(currentOrgId);

        // Get current user for sync initialization
        const currentUser = await this.getCurrentUser();
        if (!currentUser || !currentUser.id) {
          throw new Error('User authentication failed - no user ID available');
        }

        // Initialize sync system
        await this.initializeSyncSystem(currentUser.id, currentOrgId);

        // Perform initial data sync if needed
        await this.performInitialDataSyncIfNeeded(currentUser.id, currentOrgId);

        this.logger.info('Organization and sync setup completed successfully');
        return { success: true };
      } else {
        this.logger.warn(
          'No organization could be determined for authenticated user'
        );
        return { success: true }; // Not an error - user may need to select/create organization
      }
    } catch (error) {
      this.logger.error(
        'Failed to setup organization and sync',
        error as Error
      );
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to setup organization and sync',
      };
    }
  }

  /**
   * Initialize sync system and related services
   * @private
   */
  private async initializeSyncSystem(
    userId: string,
    organizationId: string
  ): Promise<void> {
    this.logger.info('Initializing sync system', { userId, organizationId });

    try {
      // Initialize unified sync manager
      const { unifiedSyncManager } = await import(
        '../sync/unified-sync-manager'
      );
      await unifiedSyncManager.initialize(userId, organizationId);

      this.logger.info('Unified sync manager initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize sync system', error as Error);
      throw error;
    }
  }

  /**
   * Perform initial data sync if database is fresh
   * @private
   */
  private async performInitialDataSyncIfNeeded(
    userId: string,
    organizationId: string
  ): Promise<void> {
    try {
      this.logger.info('Checking if initial data sync is needed', {
        userId,
        organizationId,
      });

      // Check if this is a fresh database
      const db = getDatabase();
      if (!db) {
        this.logger.warn('Database not available for initial sync check');
        return;
      }

      const dataCheck = db
        .prepare(
          `
        SELECT 
          (SELECT COUNT(*) FROM colors WHERE organization_id = ?) as colors,
          (SELECT COUNT(*) FROM products WHERE organization_id = ?) as products,
          (SELECT COUNT(*) FROM product_colors) as product_colors
      `
        )
        .get(organizationId, organizationId) as
        | { colors: number; products: number; product_colors: number }
        | undefined;

      const isFreshDb =
        dataCheck &&
        (dataCheck.colors === 0 ||
          dataCheck.products === 0 ||
          dataCheck.product_colors === 0);

      if (isFreshDb) {
        this.logger.info(
          'Fresh database detected - performing complete initial sync'
        );
        const { performInitialDataSync } = await import(
          '../sync/initial-data-sync'
        );
        const syncResult = await performInitialDataSync(userId, organizationId);

        if (syncResult.success) {
          this.logger.info('Initial sync complete', {
            colors: syncResult.colors,
            products: syncResult.products,
            productColors: syncResult.productColors,
          });
        } else {
          this.logger.error('Initial sync had errors', undefined, {
            errors: syncResult.errors,
          });
        }
      } else {
        this.logger.info(
          'Existing data found - initializing unified sync manager'
        );
        const { unifiedSyncManager } = await import(
          '../sync/unified-sync-manager'
        );
        await unifiedSyncManager.initialize(userId, organizationId);
      }
    } catch (error) {
      this.logger.error('Failed to perform initial data sync', error as Error);
      // Don't throw - this is non-critical for login process
    }
  }
}

// Export singleton instance
export const oauthService = new OAuthService();
