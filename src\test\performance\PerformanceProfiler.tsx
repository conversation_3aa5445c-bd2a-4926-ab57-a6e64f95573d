/**
 * React Performance Profiler
 * Measures actual UI rendering performance
 */

import React, { useEffect, useState, useCallback } from 'react';
import { Profiler, ProfilerOnRenderCallback } from 'react';
import { generateTestColors } from './generateTestData';
import type { ColorEntry } from '../../shared/types';

interface PerformanceMetrics {
  phase: 'mount' | 'update';
  actualDuration: number;
  baseDuration: number;
  startTime: number;
  commitTime: number;
  interactions: Set<any>;
}

interface PerformanceReport {
  operation: string;
  avgRenderTime: number;
  maxRenderTime: number;
  minRenderTime: number;
  renderCount: number;
  fps: number;
}

export const PerformanceProfiler: React.FC = () => {
  const [colors, setColors] = useState<ColorEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [report, setReport] = useState<PerformanceReport[]>([]);
  const [testSize, setTestSize] = useState(10000);

  // FPS measurement
  const [fps, setFps] = useState(0);
  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime >= lastTime + 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measureFPS);
    };

    animationId = requestAnimationFrame(measureFPS);
    return () => cancelAnimationFrame(animationId);
  }, []);

  // Profiler callback
  const handleRender: ProfilerOnRenderCallback = useCallback(
    (
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime,
      interactions
    ) => {
      const metric: PerformanceMetrics = {
        phase,
        actualDuration,
        baseDuration,
        startTime,
        commitTime,
        interactions,
      };

      setMetrics(prev => [...prev, metric]);
    },
    []
  );

  // Generate test data
  const generateData = useCallback(async (count: number) => {
    setIsLoading(true);
    setMetrics([]);

    // Use setTimeout to ensure UI updates
    setTimeout(() => {
      const testColors = generateTestColors({
        count,
        includeGradients: true,
        includeAllColorSpaces: true,
        realWorldDistribution: true,
      });

      setColors(testColors);
      setIsLoading(false);
    }, 100);
  }, []);

  // Run performance tests
  const runTests = useCallback(async () => {
    const testScenarios = [
      { name: 'Initial Render', action: () => generateData(testSize) },
      { name: 'Scroll Simulation', action: () => simulateScroll() },
      { name: 'Search Operation', action: () => simulateSearch() },
      { name: 'Batch Select', action: () => simulateBatchSelect() },
      { name: 'Export Data', action: () => simulateExport() },
    ];

    const results: PerformanceReport[] = [];

    for (const scenario of testScenarios) {
      setMetrics([]);
      await scenario.action();
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for renders

      const scenarioMetrics = metrics.filter(m => m.actualDuration > 0);
      if (scenarioMetrics.length > 0) {
        const avgTime =
          scenarioMetrics.reduce((sum, m) => sum + m.actualDuration, 0) /
          scenarioMetrics.length;
        const maxTime = Math.max(...scenarioMetrics.map(m => m.actualDuration));
        const minTime = Math.min(...scenarioMetrics.map(m => m.actualDuration));

        results.push({
          operation: scenario.name,
          avgRenderTime: avgTime,
          maxRenderTime: maxTime,
          minRenderTime: minTime,
          renderCount: scenarioMetrics.length,
          fps,
        });
      }
    }

    setReport(results);
  }, [testSize, metrics, fps]);

  // Simulate scroll
  const simulateScroll = useCallback(() => {
    const scrollSteps = 10;
    let step = 0;

    const scroll = () => {
      if (step < scrollSteps) {
        window.scrollBy(0, 100);
        step++;
        requestAnimationFrame(scroll);
      }
    };

    requestAnimationFrame(scroll);
  }, []);

  // Simulate search
  const simulateSearch = useCallback(() => {
    const searchTerms = ['blue', 'red', 'pantone', '123'];
    searchTerms.forEach((term, index) => {
      setTimeout(() => {
        const filtered = colors.filter(
          c =>
            c.name?.toLowerCase().includes(term) ||
            c.code?.toLowerCase().includes(term)
        );
        console.log(`Search "${term}" found ${filtered.length} results`);
      }, index * 100);
    });
  }, [colors]);

  // Simulate batch select
  const simulateBatchSelect = useCallback(() => {
    const selectedIds = new Set<string>();
    const selectCount = Math.min(100, colors.length);

    for (let i = 0; i < selectCount; i++) {
      selectedIds.add(colors[i].id);
    }

    console.log(`Selected ${selectedIds.size} colors`);
  }, [colors]);

  // Simulate export
  const simulateExport = useCallback(() => {
    const startTime = performance.now();
    const json = JSON.stringify(colors);
    const exportTime = performance.now() - startTime;

    console.log(
      `Export generated in ${exportTime.toFixed(2)}ms (${(json.length / 1024 / 1024).toFixed(2)}MB)`
    );
  }, [colors]);

  return (
    <div className='p-6 max-w-6xl mx-auto'>
      <h1 className='text-2xl font-bold mb-6'>
        ChromaSync Performance Profiler
      </h1>

      {/* Controls */}
      <div className='mb-6 space-y-4'>
        <div className='flex items-center gap-4'>
          <label className='font-medium'>Test Size:</label>
          <select
            value={testSize}
            onChange={e => setTestSize(Number(e.target.value))}
            className='px-3 py-1 border rounded'
          >
            <option value={1000}>1,000 colors</option>
            <option value={10000}>10,000 colors</option>
            <option value={50000}>50,000 colors</option>
            <option value={100000}>100,000 colors</option>
          </select>

          <button
            onClick={() => generateData(testSize)}
            disabled={isLoading}
            className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400'
          >
            {isLoading ? 'Generating...' : 'Generate Data'}
          </button>

          <button
            onClick={runTests}
            disabled={colors.length === 0 || isLoading}
            className='px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400'
          >
            Run Performance Tests
          </button>
        </div>

        {/* Real-time metrics */}
        <div className='flex items-center gap-6 text-sm'>
          <div>
            <span className='font-medium'>Colors Loaded:</span>{' '}
            {colors.length.toLocaleString()}
          </div>
          <div>
            <span className='font-medium'>Current FPS:</span>
            <span
              className={`ml-2 font-mono ${fps >= 50 ? 'text-green-600' : fps >= 30 ? 'text-yellow-600' : 'text-red-600'}`}
            >
              {fps}
            </span>
          </div>
          <div>
            <span className='font-medium'>Memory:</span>
            {performance.memory
              ? ` ${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`
              : ' N/A'}
          </div>
        </div>
      </div>

      {/* Color Grid (Virtual Scrolling Simulation) */}
      <Profiler id='color-grid' onRender={handleRender}>
        <div className='mb-6'>
          <h2 className='text-lg font-semibold mb-2'>Color Grid (First 100)</h2>
          <div className='grid grid-cols-10 gap-1 max-h-96 overflow-auto'>
            {colors.slice(0, 100).map(color => (
              <div
                key={color.id}
                className='w-full aspect-square rounded cursor-pointer hover:scale-105 transition-transform'
                style={{ backgroundColor: color.hex }}
                title={`${color.code} - ${color.name}`}
              />
            ))}
          </div>
        </div>
      </Profiler>

      {/* Performance Report */}
      {report.length > 0 && (
        <div className='mt-8'>
          <h2 className='text-lg font-semibold mb-4'>Performance Report</h2>
          <div className='overflow-x-auto'>
            <table className='w-full border-collapse'>
              <thead>
                <tr className='border-b'>
                  <th className='text-left p-2'>Operation</th>
                  <th className='text-right p-2'>Avg Render (ms)</th>
                  <th className='text-right p-2'>Max Render (ms)</th>
                  <th className='text-right p-2'>Min Render (ms)</th>
                  <th className='text-right p-2'>Render Count</th>
                  <th className='text-right p-2'>FPS</th>
                </tr>
              </thead>
              <tbody>
                {report.map((item, index) => (
                  <tr key={index} className='border-b'>
                    <td className='p-2'>{item.operation}</td>
                    <td className='text-right p-2 font-mono'>
                      {item.avgRenderTime.toFixed(2)}
                    </td>
                    <td className='text-right p-2 font-mono'>
                      {item.maxRenderTime.toFixed(2)}
                    </td>
                    <td className='text-right p-2 font-mono'>
                      {item.minRenderTime.toFixed(2)}
                    </td>
                    <td className='text-right p-2'>{item.renderCount}</td>
                    <td className='text-right p-2 font-mono'>{item.fps}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Performance Analysis */}
          <div className='mt-6 p-4 bg-gray-100 rounded'>
            <h3 className='font-semibold mb-2'>Performance Analysis</h3>
            <ul className='space-y-1 text-sm'>
              {report.some(r => r.avgRenderTime > 16.67) && (
                <li className='text-yellow-600'>
                  ⚠️ Some operations exceed 16.67ms (60fps threshold)
                </li>
              )}
              {report.some(r => r.maxRenderTime > 50) && (
                <li className='text-red-600'>
                  ❌ Maximum render times exceed 50ms - may cause visible lag
                </li>
              )}
              {report.every(r => r.avgRenderTime < 16.67) && (
                <li className='text-green-600'>
                  ✅ All operations maintain 60fps on average
                </li>
              )}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

// Export the metrics for use in tests
export function analyzePerformance(
  metrics: PerformanceMetrics[]
): PerformanceReport {
  if (metrics.length === 0) {
    return {
      operation: 'Unknown',
      avgRenderTime: 0,
      maxRenderTime: 0,
      minRenderTime: 0,
      renderCount: 0,
      fps: 0,
    };
  }

  const renderTimes = metrics.map(m => m.actualDuration);

  return {
    operation: 'Analysis',
    avgRenderTime:
      renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length,
    maxRenderTime: Math.max(...renderTimes),
    minRenderTime: Math.min(...renderTimes),
    renderCount: metrics.length,
    fps: 60, // Approximate based on render times
  };
}
