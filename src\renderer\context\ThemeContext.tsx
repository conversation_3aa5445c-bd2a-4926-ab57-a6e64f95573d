import React, { createContext, useState, useEffect, useContext } from 'react';
import { useSettingsStore } from '../store/settings.store';

type ThemeMode = 'light' | 'dark';
type EffectiveTheme = 'light' | 'system-light' | 'dark' | 'new-york';

interface ThemeContextType {
  mode: ThemeMode; // "light" or "dark" (user toggle)
  effectiveTheme: EffectiveTheme;
  setMode: (mode: ThemeMode) => void;
  isTransitioning: boolean;
}

const defaultContext: ThemeContextType = {
  mode: 'dark',
  effectiveTheme: 'dark',
  setMode: () => {},
  isTransitioning: false,
};

export const ThemeContext = createContext<ThemeContextType>(defaultContext);

interface ThemeProviderProps {
  children: React.ReactNode;
}

function getSystemTheme(): 'light' | 'dark' {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light';
  }
  return 'light';
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // User's toggle: "light" or "dark"
  const [mode, setMode] = useState<ThemeMode>('dark');
  const [_systemTheme, setSystemTheme] = useState<'light' | 'dark'>(
    getSystemTheme()
  );
  const [isTransitioning, setIsTransitioning] = useState<boolean>(false);

  // Get user preferences from settings
  const preferredLightTheme = useSettingsStore(s => s.preferredLightTheme);
  const preferredDarkTheme = useSettingsStore(s => s.preferredDarkTheme);

  // Listen for system theme changes if needed
  useEffect(() => {
    if (preferredLightTheme !== 'system-light') {
      return;
    }
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
      // If we're in light mode and using system-light, update immediately
      if (mode === 'light' && preferredLightTheme === 'system-light') {
        // Force a re-render by toggling the mode and setting it back
        setMode('dark');
        setTimeout(() => setMode('light'), 10);
      }
    };
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [preferredLightTheme, mode]);

  // Compute the effective theme
  let effectiveTheme: EffectiveTheme = 'light';
  if (mode === 'light') {
    effectiveTheme =
      preferredLightTheme === 'system-light'
        ? 'system-light' // Use system-light theme when selected
        : 'light';
  } else if (mode === 'dark') {
    effectiveTheme = preferredDarkTheme;
  }

  // If system-light and system is dark, you could optionally use "dark" here, but spec says "system-light" is always light.

  // Apply theme class to document
  useEffect(() => {
    const htmlElement = document.documentElement;
    const bodyElement = document.body;
    setIsTransitioning(true);
    htmlElement.classList.add('theme-transition');

    // Remove all theme classes
    htmlElement.classList.remove('light', 'dark', 'new-york', 'system-light');
    bodyElement.classList.remove('light', 'dark', 'new-york', 'system-light');

    // Add the effective theme class
    htmlElement.classList.add(effectiveTheme);
    bodyElement.classList.add(effectiveTheme);

    // Set color-scheme for light/dark
    if (effectiveTheme === 'dark' || effectiveTheme === 'new-york') {
      htmlElement.style.colorScheme = 'dark';
    } else {
      htmlElement.style.colorScheme = 'light';
    }

    // Notify Electron main process of dark mode change
    try {
      // Use type assertion to avoid TypeScript errors
      const electronApi = window.electron as any;
      if (electronApi?.ipc?.invoke) {
        electronApi.ipc
          .invoke(
            'window:set-dark-mode',
            effectiveTheme === 'dark' || effectiveTheme === 'new-york'
          )
          .catch((err: any) =>
            console.error('Failed to set window dark mode:', err)
          );
      } else if (electronApi?.invoke) {
        electronApi
          .invoke(
            'window:set-dark-mode',
            effectiveTheme === 'dark' || effectiveTheme === 'new-york'
          )
          .catch((err: any) =>
            console.error('Failed to set window dark mode:', err)
          );
      }
    } catch (error) {
      console.error('Error notifying main process of theme change:', error);
    }

    // Clear transition state after the transition is complete
    const timer = setTimeout(() => {
      htmlElement.classList.remove('theme-transition');
      setIsTransitioning(false);
    }, 250);

    return () => clearTimeout(timer);
  }, [effectiveTheme]);

  const value: ThemeContextType = {
    mode,
    effectiveTheme,
    setMode: (newMode: ThemeMode) => {
      if (!isTransitioning) {
        setMode(newMode);
      }
    },
    isTransitioning,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);
