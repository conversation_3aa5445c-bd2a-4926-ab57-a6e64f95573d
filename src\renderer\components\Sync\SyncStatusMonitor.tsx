/**
 * @file SyncStatusMonitor.tsx
 * @description Debug component to monitor sync status and API call efficiency
 */

import React, { useState, useEffect } from 'react';
import { Activity, Database, Clock, Zap, BarChart3 } from 'lucide-react';

interface SyncStatusReport {
  cache: {
    entries: number;
    staleness: Array<{
      key: string;
      age: number;
      isStale: boolean;
    }>;
  };
  polling: {
    currentFrequency: number;
    isActive: boolean;
  };
  activity: {
    lastUserActivity: number;
    lastSyncActivity: number;
    isUserActive: boolean;
    isAppFocused: boolean;
  };
  apiMetrics: {
    totalCalls: number;
    callsThisHour: number;
    cacheHitRate: number;
    recentCalls: Array<{
      method: string;
      timestamp: number;
      cached: boolean;
    }>;
  };
}

export const SyncStatusMonitor: React.FC = () => {
  const [report, setReport] = useState<SyncStatusReport | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);

  const fetchStatusReport = async () => {
    try {
      if (window.syncAPI?.getStatusReport) {
        const result = await window.syncAPI.getStatusReport();
        if (result?.success && result.report) {
          setReport(result.report);
        }
      }
    } catch (error) {
      console.error(
        '[SyncStatusMonitor] Failed to fetch status report:',
        error
      );
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchStatusReport();
    }
  }, [isOpen]);

  useEffect(() => {
    if (autoRefresh && isOpen) {
      const interval = setInterval(fetchStatusReport, 5000); // Update every 5 seconds
      return () => clearInterval(interval);
    }

    return undefined;
  }, [autoRefresh, isOpen]);

  const formatTime = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    if (diff < 60000) {return `${Math.round(diff / 1000)}s ago`;}
    if (diff < 3600000) {return `${Math.round(diff / 60000)}m ago`;}
    return `${Math.round(diff / 3600000)}h ago`;
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) {return `${ms}ms`;}
    if (ms < 60000) {return `${Math.round(ms / 1000)}s`;}
    return `${Math.round(ms / 60000)}m`;
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className='fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors z-50'
        title='Open Sync Status Monitor'
      >
        <Activity className='w-5 h-5' />
      </button>
    );
  }

  return (
    <div className='fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-xl p-4 w-96 max-h-[80vh] overflow-y-auto z-50'>
      <div className='flex justify-between items-center mb-4'>
        <h3 className='text-lg font-semibold flex items-center gap-2'>
          <Activity className='w-5 h-5' />
          Sync Status Monitor
        </h3>
        <div className='flex gap-2'>
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`px-2 py-1 text-xs rounded ${
              autoRefresh
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
            }`}
          >
            {autoRefresh ? 'Auto' : 'Manual'}
          </button>
          <button
            onClick={fetchStatusReport}
            className='px-2 py-1 text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 rounded'
          >
            Refresh
          </button>
          <button
            onClick={() => setIsOpen(false)}
            className='px-2 py-1 text-xs bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded'
          >
            Close
          </button>
        </div>
      </div>

      {!report ? (
        <div className='text-center py-4 text-gray-500'>Loading...</div>
      ) : (
        <div className='space-y-4'>
          {/* API Metrics */}
          <div className='border-b border-gray-200 dark:border-gray-600 pb-3'>
            <h4 className='font-medium flex items-center gap-2 mb-2'>
              <BarChart3 className='w-4 h-4' />
              API Efficiency
            </h4>
            <div className='grid grid-cols-2 gap-2 text-sm'>
              <div>
                <span className='text-gray-600 dark:text-gray-400'>
                  Total Calls:
                </span>
                <span className='ml-1 font-mono'>
                  {report.apiMetrics.totalCalls}
                </span>
              </div>
              <div>
                <span className='text-gray-600 dark:text-gray-400'>
                  This Hour:
                </span>
                <span className='ml-1 font-mono'>
                  {report.apiMetrics.callsThisHour}
                </span>
              </div>
              <div>
                <span className='text-gray-600 dark:text-gray-400'>
                  Cache Hit Rate:
                </span>
                <span className='ml-1 font-mono'>
                  {Math.round(report.apiMetrics.cacheHitRate * 100)}%
                </span>
              </div>
              <div>
                <span className='text-gray-600 dark:text-gray-400'>
                  Polling:
                </span>
                <span className='ml-1 font-mono'>
                  {formatDuration(report.polling.currentFrequency)}
                </span>
              </div>
            </div>
          </div>

          {/* Cache Status */}
          <div className='border-b border-gray-200 dark:border-gray-600 pb-3'>
            <h4 className='font-medium flex items-center gap-2 mb-2'>
              <Database className='w-4 h-4' />
              Cache Status
            </h4>
            <div className='space-y-1 text-sm'>
              {report.cache.staleness.map(item => (
                <div key={item.key} className='flex justify-between'>
                  <span className='text-gray-600 dark:text-gray-400'>
                    {item.key}:
                  </span>
                  <span
                    className={`font-mono text-xs ${
                      item.isStale
                        ? 'text-red-600 dark:text-red-400'
                        : 'text-green-600 dark:text-green-400'
                    }`}
                  >
                    {formatTime(Date.now() - item.age)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Activity Tracking */}
          <div className='border-b border-gray-200 dark:border-gray-600 pb-3'>
            <h4 className='font-medium flex items-center gap-2 mb-2'>
              <Clock className='w-4 h-4' />
              Activity Tracking
            </h4>
            <div className='space-y-1 text-sm'>
              <div className='flex justify-between'>
                <span className='text-gray-600 dark:text-gray-400'>
                  App Focused:
                </span>
                <span
                  className={`font-mono text-xs ${
                    report.activity.isAppFocused
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-red-600 dark:text-red-400'
                  }`}
                >
                  {report.activity.isAppFocused ? 'Yes' : 'No'}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-gray-600 dark:text-gray-400'>
                  User Active:
                </span>
                <span
                  className={`font-mono text-xs ${
                    report.activity.isUserActive
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-yellow-600 dark:text-yellow-400'
                  }`}
                >
                  {report.activity.isUserActive ? 'Yes' : 'Idle'}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-gray-600 dark:text-gray-400'>
                  Last Activity:
                </span>
                <span className='font-mono text-xs'>
                  {formatTime(report.activity.lastUserActivity)}
                </span>
              </div>
              {report.activity.lastSyncActivity > 0 && (
                <div className='flex justify-between'>
                  <span className='text-gray-600 dark:text-gray-400'>
                    Last Sync:
                  </span>
                  <span className='font-mono text-xs'>
                    {formatTime(report.activity.lastSyncActivity)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Recent API Calls */}
          <div>
            <h4 className='font-medium flex items-center gap-2 mb-2'>
              <Zap className='w-4 h-4' />
              Recent API Calls
            </h4>
            <div className='space-y-1 text-xs'>
              {report.apiMetrics.recentCalls
                .slice(-5)
                .reverse()
                .map((call, index) => (
                  <div
                    key={index}
                    className='flex justify-between items-center'
                  >
                    <span className='text-gray-600 dark:text-gray-400 truncate'>
                      {call.method}
                    </span>
                    <div className='flex items-center gap-2'>
                      <span
                        className={`px-1 rounded text-xs ${
                          call.cached
                            ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                            : 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                        }`}
                      >
                        {call.cached ? 'CACHE' : 'API'}
                      </span>
                      <span className='font-mono text-gray-500'>
                        {formatTime(call.timestamp)}
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          {/* Performance Summary */}
          <div className='bg-gray-50 dark:bg-gray-700 p-3 rounded'>
            <h4 className='font-medium mb-2 text-sm'>Performance Summary</h4>
            <div className='text-xs space-y-1'>
              <div>
                <strong>API Call Reduction:</strong>{' '}
                {Math.round(report.apiMetrics.cacheHitRate * 100)}% of requests
                served from cache
              </div>
              <div>
                <strong>Intelligent Polling:</strong>{' '}
                {formatDuration(report.polling.currentFrequency)} intervals
                based on activity
              </div>
              <div>
                <strong>Expected Hourly Calls:</strong> ~
                {Math.round(3600000 / report.polling.currentFrequency)} status
                checks
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
