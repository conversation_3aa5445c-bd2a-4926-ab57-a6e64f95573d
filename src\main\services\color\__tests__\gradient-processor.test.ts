/**
 * @file gradient-processor.test.ts
 * @description Unit tests for GradientProcessor service
 * 
 * Tests the GradientProcessor service that will handle all gradient-specific
 * logic including validation, CSS generation, data processing, and analysis.
 * 
 * Based on existing gradient functionality from ColorService and gradient-columns.ts
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { 
  GradientProcessor, 
  GradientStop, 
  GradientInfo, 
  GradientValidationResult, 
  GradientAnalysis 
} from '../gradient-processor.service';

describe('GradientProcessor Service', () => {
  let processor: GradientProcessor;

  beforeEach(() => {
    processor = new GradientProcessor();
  });

  describe('Gradient Validation', () => {
    describe('validateGradientStops', () => {
      test('should validate correct gradient stops', () => {
        const validStops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 1.0 }
        ];

        const result = processor.validateGradientStops(validStops);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      test('should reject empty gradient stops', () => {
        const result = processor.validateGradientStops([]);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Gradient must have at least one stop');
      });

      test('should reject invalid hex colors', () => {
        const invalidStops: GradientStop[] = [
          { color: 'invalid', position: 0.0 },
          { color: '#GG0000', position: 1.0 }
        ];

        const result = processor.validateGradientStops(invalidStops);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Invalid hex color format: invalid');
        expect(result.errors).toContain('Invalid hex color format: #GG0000');
      });

      test('should reject invalid positions', () => {
        const invalidStops: GradientStop[] = [
          { color: '#FF0000', position: -0.1 },
          { color: '#00FF00', position: 1.5 }
        ];

        const result = processor.validateGradientStops(invalidStops);
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('Position must be between 0 and 1: -0.1');
        expect(result.errors).toContain('Position must be between 0 and 1: 1.5');
      });

      test('should warn about duplicate positions', () => {
        const duplicateStops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 0.5 }
        ];

        const result = processor.validateGradientStops(duplicateStops);
        expect(result.warnings).toContain('Duplicate positions detected and will be resolved');
      });

      test('should validate CMYK values when present', () => {
        const stopsWithCMYK: GradientStop[] = [
          { 
            color: '#FF0000', 
            position: 0.0,
            cmyk: { c: 0, m: 100, y: 100, k: 0 }
          },
          { 
            color: '#00FF00', 
            position: 1.0,
            cmyk: { c: 150, m: -10, y: 75, k: 0 } // Invalid CMYK
          }
        ];

        const result = processor.validateGradientStops(stopsWithCMYK);
        expect(result.errors).toContain('Invalid CMYK values: c=150, m=-10, y=75, k=0');
      });
    });

    describe('validateGradientInfo', () => {
      test('should validate complete gradient info', () => {
        const gradientInfo: GradientInfo = {
          gradientStops: [
            { color: '#FF0000', position: 0.0 },
            { color: '#0000FF', position: 1.0 }
          ],
          angle: 45
        };

        const result = processor.validateGradientInfo(gradientInfo);
        expect(result.isValid).toBe(true);
      });

      test('should reject invalid angle values', () => {
        const gradientInfo: GradientInfo = {
          gradientStops: [
            { color: '#FF0000', position: 0.0 },
            { color: '#0000FF', position: 1.0 }
          ],
          angle: 400 // Invalid angle
        };

        const result = processor.validateGradientInfo(gradientInfo);
        expect(result.errors).toContain('Angle must be between 0 and 360 degrees: 400');
      });
    });
  });

  describe('CSS Generation', () => {
    describe('generateLinearGradientCSS', () => {
      test('should generate basic linear gradient CSS', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#0000FF', position: 1.0 }
        ];

        const css = processor.generateLinearGradientCSS(stops);
        expect(css).toBe('linear-gradient(45deg, #FF0000 0%, #0000FF 100%)');
      });

      test('should generate CSS with custom angle', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#0000FF', position: 1.0 }
        ];

        const css = processor.generateLinearGradientCSS(stops, 90);
        expect(css).toBe('linear-gradient(90deg, #FF0000 0%, #0000FF 100%)');
      });

      test('should generate CSS with multiple stops', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 1.0 }
        ];

        const css = processor.generateLinearGradientCSS(stops);
        expect(css).toBe('linear-gradient(45deg, #FF0000 0%, #00FF00 50%, #0000FF 100%)');
      });

      test('should handle intermediate positions correctly', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.25 },
          { color: '#0000FF', position: 0.75 },
          { color: '#FFFF00', position: 1.0 }
        ];

        const css = processor.generateLinearGradientCSS(stops);
        expect(css).toBe('linear-gradient(45deg, #FF0000 0%, #00FF00 25%, #0000FF 75%, #FFFF00 100%)');
      });
    });

    describe('generateStopCSS', () => {
      test('should generate stop CSS with percentage', () => {
        const stop: GradientStop = { color: '#FF0000', position: 0.5 };
        const css = processor.generateStopCSS(stop);
        expect(css).toBe('#FF0000 50%');
      });

      test('should handle edge positions', () => {
        const startStop: GradientStop = { color: '#FF0000', position: 0.0 };
        const endStop: GradientStop = { color: '#0000FF', position: 1.0 };

        expect(processor.generateStopCSS(startStop)).toBe('#FF0000 0%');
        expect(processor.generateStopCSS(endStop)).toBe('#0000FF 100%');
      });
    });
  });

  describe('Data Processing', () => {
    describe('createGradientColorsCSV', () => {
      test('should create CSV with colors only', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 1.0 }
        ];

        const csv = processor.createGradientColorsCSV(stops);
        expect(csv).toBe('#FF0000,#00FF00,#0000FF');
      });

      test('should create CSV with colors and color codes', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0, colorCode: 'RED-01' },
          { color: '#00FF00', position: 0.5, colorCode: 'GRN-02' },
          { color: '#0000FF', position: 1.0 }
        ];

        const csv = processor.createGradientColorsCSV(stops);
        expect(csv).toBe('#FF0000|RED-01,#00FF00|GRN-02,#0000FF');
      });

      test('should handle empty stops array', () => {
        const csv = processor.createGradientColorsCSV([]);
        expect(csv).toBe('');
      });
    });

    describe('createGradientJSON', () => {
      test('should create JSON for storage', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#0000FF', position: 1.0 }
        ];

        const json = processor.createGradientJSON(stops, 45);
        const parsed = JSON.parse(json);
        
        expect(parsed.stops).toEqual(stops);
        expect(parsed.angle).toBe(45);
      });

      test('should use default angle when not provided', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 }
        ];

        const json = processor.createGradientJSON(stops);
        const parsed = JSON.parse(json);
        
        expect(parsed.angle).toBe(45);
      });
    });

    describe('sortStopsByPosition', () => {
      test('should sort stops by position', () => {
        const unsortedStops: GradientStop[] = [
          { color: '#0000FF', position: 1.0 },
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 }
        ];

        const sorted = processor.sortStopsByPosition(unsortedStops);
        expect(sorted.map(s => s.position)).toEqual([0.0, 0.5, 1.0]);
        expect(sorted.map(s => s.color)).toEqual(['#FF0000', '#00FF00', '#0000FF']);
      });

      test('should not modify original array', () => {
        const original: GradientStop[] = [
          { color: '#0000FF', position: 1.0 },
          { color: '#FF0000', position: 0.0 }
        ];

        const sorted = processor.sortStopsByPosition(original);
        expect(original[0].position).toBe(1.0);
        expect(sorted[0].position).toBe(0.0);
      });
    });

    describe('resolveDuplicatePositions', () => {
      test('should resolve duplicate positions', () => {
        const duplicateStops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 0.5 },
          { color: '#FFFF00', position: 0.5 }
        ];

        const resolved = processor.resolveDuplicatePositions(duplicateStops);
        
        // Should have unique positions
        const positions = resolved.map(s => s.position);
        const uniquePositions = [...new Set(positions)];
        expect(positions).toHaveLength(uniquePositions.length);
        
        // First occurrence should keep original position
        expect(resolved[1].position).toBe(0.5);
        
        // Subsequent duplicates should have slightly offset positions
        expect(resolved[2].position).toBeCloseTo(0.52, 1);
        expect(resolved[3].position).toBeCloseTo(0.53, 1);
      });

      test('should handle no duplicates gracefully', () => {
        const uniqueStops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 1.0 }
        ];

        const resolved = processor.resolveDuplicatePositions(uniqueStops);
        expect(resolved).toEqual(uniqueStops);
      });
    });
  });

  describe('Format Conversion', () => {
    describe('Position conversion', () => {
      test('should convert position to percentage', () => {
        expect(processor.convertPositionToPercentage(0.0)).toBe(0);
        expect(processor.convertPositionToPercentage(0.5)).toBe(50);
        expect(processor.convertPositionToPercentage(1.0)).toBe(100);
        expect(processor.convertPositionToPercentage(0.25)).toBe(25);
      });

      test('should convert percentage to position', () => {
        expect(processor.convertPercentageToPosition(0)).toBe(0.0);
        expect(processor.convertPercentageToPosition(50)).toBe(0.5);
        expect(processor.convertPercentageToPosition(100)).toBe(1.0);
        expect(processor.convertPercentageToPosition(25)).toBe(0.25);
      });
    });

    describe('normalizePositions', () => {
      test('should ensure positions are within 0-1 range', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: -0.1 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 1.2 }
        ];

        const normalized = processor.normalizePositions(stops);
        expect(normalized[0].position).toBe(0.0);
        expect(normalized[1].position).toBe(0.5);
        expect(normalized[2].position).toBe(1.0);
      });
    });
  });

  describe('Gradient Analysis', () => {
    describe('analyzeGradient', () => {
      test('should analyze basic gradient properties', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 1.0 }
        ];

        const analysis = processor.analyzeGradient(stops);
        expect(analysis.stopCount).toBe(3);
        expect(analysis.complexity).toBe('moderate');
        expect(analysis.hasDuplicatePositions).toBe(false);
        expect(analysis.hasColorCodes).toBe(false);
      });

      test('should detect duplicate positions', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 0.5 }
        ];

        const analysis = processor.analyzeGradient(stops);
        expect(analysis.hasDuplicatePositions).toBe(true);
      });

      test('should detect color codes', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0, colorCode: 'RED-01' },
          { color: '#0000FF', position: 1.0 }
        ];

        const analysis = processor.analyzeGradient(stops);
        expect(analysis.hasColorCodes).toBe(true);
      });

      test('should calculate color distribution', () => {
        const stops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#FF0000', position: 0.3 },
          { color: '#0000FF', position: 1.0 }
        ];

        const analysis = processor.analyzeGradient(stops);
        expect(analysis.colorDistribution['#FF0000']).toBe(2);
        expect(analysis.colorDistribution['#0000FF']).toBe(1);
      });
    });

    describe('calculateComplexity', () => {
      test('should classify simple gradients', () => {
        const simpleStops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#0000FF', position: 1.0 }
        ];

        expect(processor.calculateComplexity(simpleStops)).toBe('simple');
      });

      test('should classify moderate gradients', () => {
        const moderateStops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.5 },
          { color: '#0000FF', position: 1.0 }
        ];

        expect(processor.calculateComplexity(moderateStops)).toBe('moderate');
      });

      test('should classify complex gradients', () => {
        const complexStops: GradientStop[] = [
          { color: '#FF0000', position: 0.0 },
          { color: '#00FF00', position: 0.2 },
          { color: '#0000FF', position: 0.4 },
          { color: '#FFFF00', position: 0.6 },
          { color: '#FF00FF', position: 0.8 },
          { color: '#00FFFF', position: 1.0 }
        ];

        expect(processor.calculateComplexity(complexStops)).toBe('complex');
      });
    });
  });

  describe('Utility Methods', () => {
    describe('Color validation', () => {
      test('should validate hex colors correctly', () => {
        expect(processor.isValidHexColor('#FF0000')).toBe(true);
        expect(processor.isValidHexColor('#abc123')).toBe(true);
        expect(processor.isValidHexColor('#FFFFFF')).toBe(true);
        
        expect(processor.isValidHexColor('FF0000')).toBe(false);  // No #
        expect(processor.isValidHexColor('#GG0000')).toBe(false); // Invalid hex
        expect(processor.isValidHexColor('#FFF')).toBe(false);    // Wrong length
        expect(processor.isValidHexColor('')).toBe(false);        // Empty
      });
    });

    describe('Position validation', () => {
      test('should validate positions correctly', () => {
        expect(processor.isValidPosition(0.0)).toBe(true);
        expect(processor.isValidPosition(0.5)).toBe(true);
        expect(processor.isValidPosition(1.0)).toBe(true);
        
        expect(processor.isValidPosition(-0.1)).toBe(false);
        expect(processor.isValidPosition(1.1)).toBe(false);
        expect(processor.isValidPosition(NaN)).toBe(false);
      });
    });

    describe('Service information', () => {
      test('should provide service metadata', () => {
        const info = processor.getServiceInfo();
        expect(info.name).toBe('GradientProcessor');
        expect(info.version).toBeDefined();
        expect(info.features).toBeInstanceOf(Array);
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle malformed gradient data gracefully', () => {
      const malformedStops = [
        { color: null, position: 0.0 },
        { color: '#FF0000', position: null },
        { color: undefined, position: undefined }
      ] as any;

      expect(() => processor.validateGradientStops(malformedStops)).not.toThrow();
      const result = processor.validateGradientStops(malformedStops);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should handle empty and null inputs', () => {
      expect(() => processor.createGradientColorsCSV([])).not.toThrow();
      expect(() => processor.sortStopsByPosition([])).not.toThrow();
      expect(() => processor.analyzeGradient([])).not.toThrow();
    });

    test('should handle single stop gradients', () => {
      const singleStop: GradientStop[] = [
        { color: '#FF0000', position: 0.5 }
      ];

      expect(() => processor.generateLinearGradientCSS(singleStop)).not.toThrow();
      expect(() => processor.analyzeGradient(singleStop)).not.toThrow();
      
      const analysis = processor.analyzeGradient(singleStop);
      expect(analysis.stopCount).toBe(1);
      expect(analysis.complexity).toBe('simple');
    });

    test('should handle extreme position values', () => {
      const extremeStops: GradientStop[] = [
        { color: '#FF0000', position: Number.MIN_VALUE },
        { color: '#0000FF', position: Number.MAX_VALUE }
      ];

      const normalized = processor.normalizePositions(extremeStops);
      expect(normalized[0].position).toBeGreaterThanOrEqual(0);
      expect(normalized[1].position).toBeLessThanOrEqual(1);
    });

    test('should maintain performance with large gradients', () => {
      const largeGradient: GradientStop[] = Array.from({ length: 100 }, (_, i) => ({
        color: `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`,
        position: i / 99
      }));

      const start = Date.now();
      processor.analyzeGradient(largeGradient);
      processor.generateLinearGradientCSS(largeGradient);
      processor.sortStopsByPosition(largeGradient);
      const duration = Date.now() - start;

      // Should complete operations on 100 stops in under 50ms
      expect(duration).toBeLessThan(50);
    });
  });
});