/**
 * @file useModals.ts
 * @description Utility hooks for common modal operations
 */

import { useCallback } from 'react';
import { useModal, useModalState } from '../context/ModalContext';
import { ColorEntry } from '../../shared/types/color.types';
import { Product } from '../../shared/types/product.types';

/**
 * Hook for settings modal
 */
export const useSettingsModal = () => {
  const modal = useModalState('settings');

  const openWithTab = useCallback(
    (tab?: 'general' | 'advanced' | 'team') => {
      modal.open({ initialTab: tab });
    },
    [modal]
  );

  return {
    ...modal,
    openWithTab,
  };
};

/**
 * Hook for license modal
 */
export const useLicenseModal = () => {
  const modal = useModalState('license');

  const openWithStatus = useCallback(
    (licenseStatus: any) => {
      modal.open({ licenseStatus });
    },
    [modal]
  );

  return {
    ...modal,
    openWithStatus,
  };
};

/**
 * Hook for color form modal
 */
export const useColorFormModal = () => {
  const modal = useModalState('colorForm');

  const openForCreate = useCallback(
    (onSuccess?: () => void) => {
      modal.open({ mode: 'create' }, onSuccess);
    },
    [modal]
  );

  const openForEdit = useCallback(
    (color: ColorEntry, onSuccess?: () => void) => {
      modal.open({ mode: 'edit', color }, onSuccess);
    },
    [modal]
  );

  return {
    ...modal,
    openForCreate,
    openForEdit,
  };
};

/**
 * Hook for product form modal
 */
export const useProductFormModal = () => {
  const modal = useModalState('productForm');

  const openForCreate = useCallback(
    (onSuccess?: () => void) => {
      modal.open({ mode: 'create' }, onSuccess);
    },
    [modal]
  );

  const openForEdit = useCallback(
    (product: Product, onSuccess?: () => void) => {
      modal.open({ mode: 'edit', product }, onSuccess);
    },
    [modal]
  );

  return {
    ...modal,
    openForCreate,
    openForEdit,
  };
};

/**
 * Hook for gradient picker modal
 */
export const useGradientPickerModal = () => {
  const modal = useModalState('gradientPicker');

  const openWithColor = useCallback(
    (initialColor?: string, onColorSelect?: (color: string) => void) => {
      modal.open({ initialColor, onColorSelect });
    },
    [modal]
  );

  const openForEdit = useCallback(
    (color: ColorEntry, onSuccess?: () => void) => {
      modal.open(
        {
          editMode: true,
          color,
          initialValue: color.gradient,
          productName: color.product,
        },
        onSuccess
      );
    },
    [modal]
  );

  const openForCreate = useCallback(
    (productName?: string, onSuccess?: () => void) => {
      modal.open(
        {
          editMode: false,
          productName,
        },
        onSuccess
      );
    },
    [modal]
  );

  return {
    ...modal,
    openWithColor,
    openForEdit,
    openForCreate,
  };
};

/**
 * Hook for color comparison modal
 */
export const useColorComparisonModal = () => {
  const modal = useModalState('colorComparison');

  const openWithColors = useCallback(
    (colors: ColorEntry[]) => {
      modal.open({ colors });
    },
    [modal]
  );

  return {
    ...modal,
    openWithColors,
  };
};

/**
 * Hook for setup modal
 */
export const useSetupModal = () => {
  return useModalState('setup');
};

/**
 * Hook for help modal
 */
export const useHelpModal = () => {
  return useModalState('help');
};

/**
 * Hook for invitation modal
 */
export const useAcceptInvitationModal = () => {
  const modal = useModalState('acceptInvitation');

  const openWithToken = useCallback(
    (token: string, onSuccess?: () => void) => {
      modal.open({ token }, onSuccess);
    },
    [modal]
  );

  return {
    ...modal,
    openWithToken,
  };
};

/**
 * Hook for global modal operations
 */
export const useGlobalModals = () => {
  const { closeAllModals } = useModal();

  const closeAll = useCallback(() => {
    closeAllModals();
  }, [closeAllModals]);

  return {
    closeAll,
  };
};

/**
 * Combined hook with all modal utilities
 */
export const useAppModals = () => {
  const settings = useSettingsModal();
  const license = useLicenseModal();
  const colorForm = useColorFormModal();
  const productForm = useProductFormModal();
  const gradientPicker = useGradientPickerModal();
  const colorComparison = useColorComparisonModal();
  const setup = useSetupModal();
  const help = useHelpModal();
  const acceptInvitation = useAcceptInvitationModal();
  const global = useGlobalModals();

  return {
    settings,
    license,
    colorForm,
    productForm,
    gradientPicker,
    colorComparison,
    setup,
    help,
    acceptInvitation,
    global,
  };
};
