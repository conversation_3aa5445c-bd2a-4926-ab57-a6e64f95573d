/**
 * Database Connection Management
 * Handles connection pooling, initialization, and resource management
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs';
import { loadBetterSqlite3 } from '../database-loader';

// We'll load Database dynamically to avoid bundler issues
let Database: any;

/**
 * Database connection pool for better concurrent access
 * Manages multiple connections to prevent blocking and improve performance
 */
export class DatabasePool {
  private static instance: DatabasePool;
  private connections: any[] = [];
  private readonly maxConnections = 5;
  private busyConnections = new Set<any>();
  private dbPath: string;
  
  private constructor() {
    this.dbPath = this.getDatabasePath();
    
    // Periodically clean up unhealthy connections (every 5 minutes)
    setInterval(() => {
      this.cleanupUnhealthyConnections();
    }, 5 * 60 * 1000);
  }
  
  /**
   * Get singleton instance of database pool
   */
  static getInstance(): DatabasePool {
    if (!DatabasePool.instance) {
      DatabasePool.instance = new DatabasePool();
    }
    return DatabasePool.instance;
  }
  
  /**
   * Get database path
   */
  private getDatabasePath(): string {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'chromasync.db');
  }
  
  // Connection waiting queue for proper Promise handling
  private connectionQueue: Array<{
    resolve: (connection: any) => void;
    reject: (error: Error) => void;
    timestamp: number;
  }> = [];
  private readonly connectionTimeout = 30000; // 30 seconds

  /**
   * Get available connection from pool or create new one
   */
  async getConnection(): Promise<any> {
    // Return available connection
    const availableConnection = this.connections.find(conn => !this.busyConnections.has(conn));
    if (availableConnection) {
      this.busyConnections.add(availableConnection);
      return availableConnection;
    }
    
    // Create new connection if under limit
    if (this.connections.length < this.maxConnections) {
      const newConnection = await this.createConnection();
      this.connections.push(newConnection);
      this.busyConnections.add(newConnection);
      return newConnection;
    }
    
    // Wait for connection to become available using proper Promise queue
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        // Remove from queue on timeout
        const index = this.connectionQueue.findIndex(item => item.resolve === resolve);
        if (index !== -1) {
          this.connectionQueue.splice(index, 1);
        }
        reject(new Error(`Connection timeout after ${this.connectionTimeout}ms. Pool exhausted with ${this.busyConnections.size} busy connections.`));
      }, this.connectionTimeout);

      // Add to queue
      this.connectionQueue.push({
        resolve: (connection) => {
          clearTimeout(timeout);
          resolve(connection);
        },
        reject: (error) => {
          clearTimeout(timeout);
          reject(error);
        },
        timestamp: Date.now()
      });
    });
  }
  
  /**
   * Release connection back to pool
   */
  releaseConnection(connection: any): void {
    this.busyConnections.delete(connection);
    
    // Process waiting queue if there are pending requests
    if (this.connectionQueue.length > 0) {
      const waiting = this.connectionQueue.shift();
      if (waiting) {
        this.busyConnections.add(connection);
        waiting.resolve(connection);
      }
    }
  }
  
  /**
   * Create new database connection with optimal settings
   */
  private async createConnection(): Promise<any> {
    try {
      if (!Database) {
        console.log('[DatabasePool] Loading better-sqlite3 module...');
        Database = loadBetterSqlite3();
        console.log('[DatabasePool] better-sqlite3 module loaded successfully');
      }
      
      // Ensure directory exists
      const dirPath = path.dirname(this.dbPath);
      if (!fs.existsSync(dirPath)) {
        console.log(`[DatabasePool] Creating database directory: ${dirPath}`);
        fs.mkdirSync(dirPath, { recursive: true });
      }
      
      console.log(`[DatabasePool] Creating database connection to: ${this.dbPath}`);
      const connection = new Database(this.dbPath, { 
        verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
        fileMustExist: false
      });
      console.log('[DatabasePool] Database connection created successfully');
      
      // Apply optimal PRAGMA settings
      console.log('[DatabasePool] Applying PRAGMA settings...');
      connection.exec(`
        PRAGMA foreign_keys = ON;
        PRAGMA journal_mode = WAL;
        PRAGMA synchronous = NORMAL;
        PRAGMA temp_store = MEMORY;
        PRAGMA mmap_size = 30000000000;
        PRAGMA cache_size = -64000;
        PRAGMA wal_autocheckpoint = 1000;
      `);
      console.log('[DatabasePool] PRAGMA settings applied successfully');
      
      return connection;
    } catch (error) {
      console.error('[DatabasePool] Failed to create database connection:', error);
      const errorDetails = error as Error & { code?: string };
      console.error('[DatabasePool] Error details:', {
        message: errorDetails.message,
        stack: errorDetails.stack,
        code: errorDetails.code,
        dbPath: this.dbPath
      });
      
      // Enhance error with user-friendly message and context
      const enhancedError = this.enhanceConnectionError(error);
      throw enhancedError;
    }
  }
  
  
  /**
   * Enhance connection errors with user-friendly messages
   */
  private enhanceConnectionError(error: any): Error {
    if (!error.code) {
      return new Error(
        `Failed to connect to ChromaSync database: ${error.message}. ` +
        'Please restart the application and try again.'
      );
    }
    
    const dbLocation = this.dbPath;
    
    switch (error.code) {
      case 'SQLITE_BUSY':
        return new Error(
          'ChromaSync database is currently in use by another process. ' +
          'Please close any other ChromaSync windows and try again. ' +
          'If the problem persists, restart your computer. ' +
          `\n\nTechnical details: Database location: ${dbLocation}`
        );
        
      case 'SQLITE_CANTOPEN':
        return new Error(
          'ChromaSync cannot access its database file. This usually means the app lacks permission to read or write files. ' +
          'Try running ChromaSync as administrator or check if your antivirus is blocking the app. ' +
          `\n\nTechnical details: ${dbLocation}`
        );
        
      case 'SQLITE_READONLY':
        return new Error(
          'ChromaSync database is read-only and cannot save changes. ' +
          'This typically happens when the disk is full or the file permissions are incorrect. ' +
          'Check your available disk space and file permissions. ' +
          `\n\nTechnical details: ${dbLocation}`
        );
        
      case 'SQLITE_IOERR':
        return new Error(
          'ChromaSync encountered a disk error while accessing the database. ' +
          'Please check if your hard drive is working properly and has enough free space. ' +
          'Consider running a disk check utility if this error persists. ' +
          `\n\nTechnical details: ${dbLocation}`
        );
        
      case 'ENOENT':
        return new Error(
          'ChromaSync cannot find or create its data folder. ' +
          'This may happen if the user profile directory is corrupted or inaccessible. ' +
          'Try restarting your computer or contact support if the issue continues. ' +
          `\n\nTechnical details: ${dbLocation}`
        );
        
      case 'EACCES':
        return new Error(
          'ChromaSync does not have permission to access its database. ' +
          'Try running ChromaSync as administrator, or check if your antivirus software is blocking the app. ' +
          'You may also need to manually grant ChromaSync permission to access this folder. ' +
          `\n\nTechnical details: ${dbLocation}`
        );
        
      default: {
        const userFriendlyMessage = this.generateUserFriendlyMessage(error.code, error.message);
        return new Error(
          userFriendlyMessage +
          `\n\nTechnical details: Error code ${error.code}, Database: ${dbLocation}. ` +
          'If this problem persists, please contact support with these technical details.'
        );
      }
    }
  }

  /**
   * Generate user-friendly error messages for unknown error codes
   */
  private generateUserFriendlyMessage(_errorCode: string, originalMessage: string): string {
    // Common patterns in error messages that we can make more user-friendly
    const lowerMessage = originalMessage.toLowerCase();
    
    if (lowerMessage.includes('permission') || lowerMessage.includes('denied')) {
      return 'ChromaSync does not have permission to access its files. Try running as administrator or check your antivirus settings.';
    }
    
    if (lowerMessage.includes('space') || lowerMessage.includes('disk full')) {
      return 'Your disk appears to be full. Please free up some space and try again.';
    }
    
    if (lowerMessage.includes('corrupted') || lowerMessage.includes('corrupt')) {
      return 'ChromaSync database may be corrupted. The app will attempt to recover automatically.';
    }
    
    if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
      return 'ChromaSync cannot connect to the internet. Please check your network connection.';
    }
    
    if (lowerMessage.includes('timeout')) {
      return 'ChromaSync operation timed out. This may be due to high system load or network issues.';
    }
    
    if (lowerMessage.includes('locked') || lowerMessage.includes('busy')) {
      return 'ChromaSync database is busy. Please wait a moment and try again.';
    }
    
    // Generic fallback for unknown errors
    return 'ChromaSync encountered an unexpected error. The app will try to recover automatically.';
  }
  
  /**
   * Close all connections in pool
   */
  closeAll(): void {
    // Reject all pending connections in queue
    this.connectionQueue.forEach(({ reject }) => {
      reject(new Error('Database pool is shutting down'));
    });
    this.connectionQueue = [];
    
    // Close all connections
    this.connections.forEach(conn => {
      try {
        conn.close();
      } catch (error) {
        console.warn('[DatabasePool] Error closing database connection:', error);
      }
    });
    this.connections = [];
    this.busyConnections.clear();
  }
  
  /**
   * Check if a connection is healthy
   */
  private isConnectionHealthy(connection: any): boolean {
    try {
      // Simple health check - execute a basic query
      const result = connection.prepare('SELECT 1 as health_check').get();
      return result && result.health_check === 1;
    } catch (error) {
      console.warn('[DatabasePool] Connection health check failed:', error);
      return false;
    }
  }

  /**
   * Remove unhealthy connections from pool
   */
  private cleanupUnhealthyConnections(): void {
    const healthyConnections = this.connections.filter(conn => {
      if (this.busyConnections.has(conn)) {
        return true; // Don't check busy connections
      }
      
      const isHealthy = this.isConnectionHealthy(conn);
      if (!isHealthy) {
        try {
          conn.close();
        } catch (error) {
          console.warn('[DatabasePool] Error closing unhealthy connection:', error);
        }
      }
      return isHealthy;
    });
    
    this.connections = healthyConnections;
  }

  /**
   * Get pool statistics for monitoring
   */
  getStats(): {
    totalConnections: number;
    busyConnections: number;
    availableConnections: number;
    maxConnections: number;
    queueLength: number;
    queueOldestWaitTime?: number;
  } {
    const now = Date.now();
    const oldestQueueItem = this.connectionQueue.length > 0 ? this.connectionQueue[0] : null;
    
    return {
      totalConnections: this.connections.length,
      busyConnections: this.busyConnections.size,
      availableConnections: this.connections.length - this.busyConnections.size,
      maxConnections: this.maxConnections,
      queueLength: this.connectionQueue.length,
      queueOldestWaitTime: oldestQueueItem ? now - oldestQueueItem.timestamp : undefined
    };
  }
}

/**
 * Get database connection from pool
 */
export async function getPooledConnection(): Promise<any> {
  const pool = DatabasePool.getInstance();
  return await pool.getConnection();
}

/**
 * Release database connection back to pool
 */
export function releasePooledConnection(connection: any): void {
  const pool = DatabasePool.getInstance();
  pool.releaseConnection(connection);
}

/**
 * Execute a query with automatic connection pooling
 */
export async function executeWithPool<T>(operation: (db: any) => T): Promise<T> {
  const connection = await getPooledConnection();
  try {
    return operation(connection);
  } finally {
    releasePooledConnection(connection);
  }
}

/**
 * Get database path utility function
 */
export function getDatabasePath(): string {
  const userDataPath = app.getPath('userData');
  return path.join(userDataPath, 'chromasync.db');
}
