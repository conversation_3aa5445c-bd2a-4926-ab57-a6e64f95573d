import { Page } from 'playwright';

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  threshold?: number;
}

export interface PerformanceTestResult {
  metrics: PerformanceMetric[];
  passed: boolean;
  date: string;
  environment: string;
}

export class PerformanceMetrics {
  private results: PerformanceTestResult[] = [];

  async measurePageLoad(page: Page): Promise<PerformanceMetric[]> {
    // Get page load metrics using Performance API
    const metrics = await page.evaluate(() => {
      const perfEntries = performance.getEntriesByType(
        'navigation'
      ) as PerformanceNavigationTiming[];
      if (perfEntries.length === 0) {
        return [];
      }

      const navEntry = perfEntries[0];

      return [
        { name: 'domComplete', value: navEntry.domComplete, unit: 'ms' },
        { name: 'domInteractive', value: navEntry.domInteractive, unit: 'ms' },
        { name: 'loadEventEnd', value: navEntry.loadEventEnd, unit: 'ms' },
        {
          name: 'firstContentfulPaint',
          value: navEntry.responseStart,
          unit: 'ms',
        },
      ];
    });

    return metrics;
  }

  async measureComponentLoad(
    page: Page,
    selector: string
  ): Promise<PerformanceMetric[]> {
    // Measure time to first render of a component
    const startTime = Date.now();
    await page.waitForSelector(selector, { timeout: 10000 });
    const endTime = Date.now();

    return [
      {
        name: `${selector} render time`,
        value: endTime - startTime,
        unit: 'ms',
      },
    ];
  }

  async measureInteraction(
    _page: Page,
    action: () => Promise<void>
  ): Promise<PerformanceMetric[]> {
    // Measure time for an interaction to complete
    const startTime = Date.now();
    await action();
    const endTime = Date.now();

    return [
      {
        name: 'Interaction time',
        value: endTime - startTime,
        unit: 'ms',
      },
    ];
  }

  async measureMemoryUsage(page: Page): Promise<PerformanceMetric[]> {
    // Measure JS heap size
    const memoryInfo = await page.evaluate(() => {
      // @ts-ignore: Chrome-specific API
      if (performance.memory) {
        // @ts-ignore: Chrome-specific API
        const memory = performance.memory;
        return {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
        };
      }
      return null;
    });

    if (!memoryInfo) {
      return [];
    }

    return [
      {
        name: 'Used JS Heap',
        value: Math.round(memoryInfo.usedJSHeapSize / (1024 * 1024)),
        unit: 'MB',
      },
      {
        name: 'Total JS Heap',
        value: Math.round(memoryInfo.totalJSHeapSize / (1024 * 1024)),
        unit: 'MB',
      },
    ];
  }

  async measureLargeDatasetPerformance(
    _page: Page,
    dataLoadAction: () => Promise<void>,
    dataSize: number
  ): Promise<PerformanceMetric[]> {
    const startTime = Date.now();
    await dataLoadAction();
    const endTime = Date.now();

    return [
      {
        name: `Load ${dataSize} items`,
        value: endTime - startTime,
        unit: 'ms',
        threshold: 1000, // 1 second threshold for good performance
      },
    ];
  }

  storeResult(
    metrics: PerformanceMetric[],
    environment: string
  ): PerformanceTestResult {
    const passed = metrics.every(
      metric => !metric.threshold || metric.value <= metric.threshold
    );

    const result: PerformanceTestResult = {
      metrics,
      passed,
      date: new Date().toISOString(),
      environment,
    };

    this.results.push(result);
    return result;
  }

  getResults(): PerformanceTestResult[] {
    return this.results;
  }

  getLastResult(): PerformanceTestResult | undefined {
    return this.results[this.results.length - 1];
  }

  generateReport(): string {
    if (this.results.length === 0) {
      return 'No performance test results available.';
    }

    let report = '# Performance Test Results\n\n';

    this.results.forEach((result, index) => {
      report += `## Test Run ${index + 1} (${result.date})\n`;
      report += `Environment: ${result.environment}\n`;
      report += `Status: ${result.passed ? '✅ PASSED' : '❌ FAILED'}\n\n`;

      report += '| Metric | Value | Unit | Threshold | Status |\n';
      report += '|--------|-------|------|-----------|--------|\n';

      result.metrics.forEach(metric => {
        const status = !metric.threshold
          ? 'N/A'
          : metric.value <= metric.threshold
            ? '✅'
            : '❌';

        report += `| ${metric.name} | ${metric.value} | ${metric.unit} | ${metric.threshold || 'N/A'} | ${status} |\n`;
      });

      report += '\n';
    });

    return report;
  }
}

export default PerformanceMetrics;
