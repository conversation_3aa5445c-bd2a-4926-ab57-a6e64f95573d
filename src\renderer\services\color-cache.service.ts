/**
 * @file color-cache.service.ts
 * @description Aggressive caching service for color API calls with request deduplication,
 * organization-aware cache keys, TTL-based invalidation, and comprehensive metrics
 */

import type { ColorEntry } from '../../shared/types/color.types';

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  organizationId: string;
  version: number;
}

// Cache metrics interface
interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  invalidations: number;
  deduplicatedRequests: number;
  totalRequests: number;
  averageResponseTime: number;
  lastHitTime?: number;
  lastMissTime?: number;
}

// In-flight request tracking
interface InFlightRequest<T> {
  promise: Promise<T>;
  timestamp: number;
}

// Color API result interface
interface ColorAPIResult {
  success: boolean;
  data?: {
    colors: ColorEntry[];
    usageCounts: Record<string, { count: number; products: string[] }>;
    organizationId: string;
  };
  error?: string;
}

/**
 * Aggressive caching service for color data with comprehensive features:
 * - Organization-aware cache keys
 * - TTL-based cache invalidation (30-60 seconds)
 * - Request deduplication for simultaneous calls
 * - Cache warming strategies
 * - Performance metrics and monitoring
 * - Smart invalidation on data mutations
 */
class ColorCacheService {
  private cache = new Map<string, CacheEntry<ColorAPIResult>>();
  private inFlightRequests = new Map<string, InFlightRequest<ColorAPIResult>>();
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    sets: 0,
    invalidations: 0,
    deduplicatedRequests: 0,
    totalRequests: 0,
    averageResponseTime: 0
  };
  
  // Configuration
  private readonly DEFAULT_TTL = 45000; // 45 seconds default TTL
  private readonly MAX_CACHE_SIZE = 50; // Maximum cache entries
  private readonly REQUEST_TIMEOUT = 30000; // 30 seconds timeout for in-flight requests
  private readonly CACHE_VERSION = 1; // Increment to invalidate all cache entries
  
  // Cleanup interval
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Setup periodic cleanup of expired entries and stale in-flight requests
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
      this.cleanupStaleRequests();
    }, 10000); // Every 10 seconds

    // Log startup
    console.log('[ColorCache] 🚀 Color cache service initialized with aggressive caching');
    console.log('[ColorCache] Configuration:', {
      defaultTTL: `${this.DEFAULT_TTL}ms`,
      maxCacheSize: this.MAX_CACHE_SIZE,
      requestTimeout: `${this.REQUEST_TIMEOUT}ms`,
      cacheVersion: this.CACHE_VERSION
    });
  }

  /**
   * Generate organization-aware cache key
   */
  private generateCacheKey(organizationId: string, method: string = 'getAllWithUsage'): string {
    return `color:${method}:${organizationId}:v${this.CACHE_VERSION}`;
  }

  /**
   * Check if cache entry is still valid
   */
  private isValidCacheEntry(entry: CacheEntry<ColorAPIResult>): boolean {
    const now = Date.now();
    const isExpired = (now - entry.timestamp) > entry.ttl;
    const isCorrectVersion = entry.version === this.CACHE_VERSION;
    
    return !isExpired && isCorrectVersion;
  }

  /**
   * Get cached data with metrics tracking
   */
  public get(organizationId: string, method: string = 'getAllWithUsage'): ColorAPIResult | null {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(organizationId, method);
    const entry = this.cache.get(cacheKey);
    
    this.metrics.totalRequests++;
    
    if (entry && this.isValidCacheEntry(entry)) {
      this.metrics.hits++;
      this.metrics.lastHitTime = Date.now();
      
      const responseTime = Date.now() - startTime;
      this.updateAverageResponseTime(responseTime);
      
      console.log('[ColorCache] ✅ Cache HIT for', cacheKey, {
        age: `${Date.now() - entry.timestamp}ms`,
        ttl: `${entry.ttl}ms`,
        colorsCount: entry.data.data?.colors?.length || 0,
        responseTime: `${responseTime}ms`
      });
      
      return entry.data;
    }
    
    this.metrics.misses++;
    this.metrics.lastMissTime = Date.now();
    
    console.log('[ColorCache] ❌ Cache MISS for', cacheKey, {
      reason: entry ? (this.isValidCacheEntry(entry) ? 'invalid' : 'expired') : 'not-found',
      entryExists: !!entry,
      entryAge: entry ? `${Date.now() - entry.timestamp}ms` : 'N/A'
    });
    
    return null;
  }

  /**
   * Set cache data with TTL and metrics tracking
   */
  public set(organizationId: string, data: ColorAPIResult, ttl: number = this.DEFAULT_TTL, method: string = 'getAllWithUsage'): void {
    const cacheKey = this.generateCacheKey(organizationId, method);
    
    // Enforce cache size limit
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestEntry();
    }
    
    const entry: CacheEntry<ColorAPIResult> = {
      data,
      timestamp: Date.now(),
      ttl,
      organizationId,
      version: this.CACHE_VERSION
    };
    
    this.cache.set(cacheKey, entry);
    this.metrics.sets++;
    
    console.log('[ColorCache] 💾 Cache SET for', cacheKey, {
      ttl: `${ttl}ms`,
      colorsCount: data.data?.colors?.length || 0,
      cacheSize: this.cache.size,
      success: data.success
    });
  }

  /**
   * Check if request is already in flight to prevent duplicate API calls
   */
  public isRequestInFlight(organizationId: string, method: string = 'getAllWithUsage'): boolean {
    const cacheKey = this.generateCacheKey(organizationId, method);
    const inFlight = this.inFlightRequests.get(cacheKey);
    
    if (inFlight) {
      const age = Date.now() - inFlight.timestamp;
      if (age < this.REQUEST_TIMEOUT) {
        console.log('[ColorCache] 🔄 Request already in flight for', cacheKey, { age: `${age}ms` });
        return true;
      } else {
        // Clean up stale in-flight request
        this.inFlightRequests.delete(cacheKey);
        console.log('[ColorCache] 🧹 Cleaned up stale in-flight request for', cacheKey);
      }
    }
    
    return false;
  }

  /**
   * Wait for existing in-flight request or return null if none
   */
  public async waitForInFlightRequest(organizationId: string, method: string = 'getAllWithUsage'): Promise<ColorAPIResult | null> {
    const cacheKey = this.generateCacheKey(organizationId, method);
    const inFlight = this.inFlightRequests.get(cacheKey);
    
    if (inFlight) {
      const age = Date.now() - inFlight.timestamp;
      if (age < this.REQUEST_TIMEOUT) {
        this.metrics.deduplicatedRequests++;
        console.log('[ColorCache] ⏳ Waiting for in-flight request for', cacheKey);
        
        try {
          const result = await inFlight.promise;
          console.log('[ColorCache] ✅ In-flight request completed for', cacheKey);
          return result;
        } catch (error) {
          console.log('[ColorCache] ❌ In-flight request failed for', cacheKey, error);
          this.inFlightRequests.delete(cacheKey);
          return null;
        }
      } else {
        // Clean up stale request
        this.inFlightRequests.delete(cacheKey);
      }
    }
    
    return null;
  }

  /**
   * Register an in-flight request to prevent duplicates
   */
  public registerInFlightRequest(organizationId: string, promise: Promise<ColorAPIResult>, method: string = 'getAllWithUsage'): void {
    const cacheKey = this.generateCacheKey(organizationId, method);
    
    this.inFlightRequests.set(cacheKey, {
      promise,
      timestamp: Date.now()
    });
    
    console.log('[ColorCache] 📡 Registered in-flight request for', cacheKey);
    
    // Clean up when promise resolves/rejects
    promise.finally(() => {
      this.inFlightRequests.delete(cacheKey);
      console.log('[ColorCache] 🧹 Cleaned up completed in-flight request for', cacheKey);
    });
  }

  /**
   * Invalidate cache for specific organization
   */
  public invalidateOrganization(organizationId: string): void {
    let invalidatedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.organizationId === organizationId) {
        this.cache.delete(key);
        invalidatedCount++;
      }
    }
    
    this.metrics.invalidations += invalidatedCount;
    
    console.log('[ColorCache] 🗑️ Invalidated cache for organization', organizationId, {
      invalidatedEntries: invalidatedCount,
      remainingEntries: this.cache.size
    });
  }

  /**
   * Invalidate all cache entries (on organization switch)
   */
  public invalidateAll(): void {
    const deletedCount = this.cache.size;
    this.cache.clear();
    this.inFlightRequests.clear();
    this.metrics.invalidations += deletedCount;
    
    console.log('[ColorCache] 🗑️ Invalidated ALL cache entries', {
      deletedEntries: deletedCount
    });
  }

  /**
   * Warm cache by preloading data for current organization
   */
  public async warmCache(organizationId: string): Promise<void> {
    console.log('[ColorCache] 🔥 Warming cache for organization', organizationId);
    
    // Check if already cached
    if (this.get(organizationId)) {
      console.log('[ColorCache] 🔥 Cache already warm for organization', organizationId);
      return;
    }
    
    try {
      // Use the actual API call with caching
      const result = await window.colorAPI.getAllWithUsage();
      if (result.success && result.data) {
        this.set(organizationId, result, this.DEFAULT_TTL);
        console.log('[ColorCache] 🔥 Cache warmed successfully for organization', organizationId);
      }
    } catch (error) {
      console.log('[ColorCache] ❌ Cache warming failed for organization', organizationId, error);
    }
  }

  /**
   * Get comprehensive cache metrics
   */
  public getMetrics(): CacheMetrics & {
    hitRate: number;
    cacheSize: number;
    inFlightCount: number;
    memoryUsage: string;
  } {
    const hitRate = this.metrics.totalRequests > 0 
      ? (this.metrics.hits / this.metrics.totalRequests * 100) 
      : 0;
    
    // Estimate memory usage
    const memoryUsage = this.estimateMemoryUsage();
    
    return {
      ...this.metrics,
      hitRate: Math.round(hitRate * 100) / 100,
      cacheSize: this.cache.size,
      inFlightCount: this.inFlightRequests.size,
      memoryUsage
    };
  }

  /**
   * Log cache performance summary
   */
  public logPerformanceSummary(): void {
    const metrics = this.getMetrics();
    
    console.log('[ColorCache] 📊 Performance Summary:', {
      hitRate: `${metrics.hitRate}%`,
      totalRequests: metrics.totalRequests,
      hits: metrics.hits,
      misses: metrics.misses,
      deduplicatedRequests: metrics.deduplicatedRequests,
      cacheSize: metrics.cacheSize,
      averageResponseTime: `${metrics.averageResponseTime}ms`,
      memoryUsage: metrics.memoryUsage,
      apiCallReduction: metrics.deduplicatedRequests > 0 
        ? `${Math.round((metrics.deduplicatedRequests / metrics.totalRequests) * 100)}%`
        : '0%'
    });
  }

  /**
   * Reset metrics (useful for testing)
   */
  public resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      invalidations: 0,
      deduplicatedRequests: 0,
      totalRequests: 0,
      averageResponseTime: 0
    };
    console.log('[ColorCache] 📊 Metrics reset');
  }

  /**
   * Cleanup expired cache entries
   */
  private cleanupExpiredEntries(): void {
    let cleanedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (!this.isValidCacheEntry(entry)) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log('[ColorCache] 🧹 Cleaned up', cleanedCount, 'expired cache entries');
    }
  }

  /**
   * Cleanup stale in-flight requests
   */
  private cleanupStaleRequests(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, request] of this.inFlightRequests.entries()) {
      if ((now - request.timestamp) > this.REQUEST_TIMEOUT) {
        this.inFlightRequests.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log('[ColorCache] 🧹 Cleaned up', cleanedCount, 'stale in-flight requests');
    }
  }

  /**
   * Evict oldest cache entry when cache is full
   */
  private evictOldestEntry(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log('[ColorCache] 🗑️ Evicted oldest cache entry:', oldestKey);
    }
  }

  /**
   * Update average response time
   */
  private updateAverageResponseTime(responseTime: number): void {
    if (this.metrics.hits === 1) {
      this.metrics.averageResponseTime = responseTime;
    } else {
      this.metrics.averageResponseTime = 
        (this.metrics.averageResponseTime * (this.metrics.hits - 1) + responseTime) / this.metrics.hits;
    }
  }

  /**
   * Estimate memory usage of cache
   */
  private estimateMemoryUsage(): string {
    let totalSize = 0;
    
    for (const entry of this.cache.values()) {
      // Rough estimation of entry size
      const dataSize = JSON.stringify(entry.data).length * 2; // Approximate bytes
      totalSize += dataSize + 200; // Add overhead for metadata
    }
    
    if (totalSize > 1024 * 1024) {
      return `${Math.round(totalSize / (1024 * 1024) * 100) / 100} MB`;
    } else if (totalSize > 1024) {
      return `${Math.round(totalSize / 1024 * 100) / 100} KB`;
    } else {
      return `${totalSize} bytes`;
    }
  }

  /**
   * Destroy the cache service
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.cache.clear();
    this.inFlightRequests.clear();
    console.log('[ColorCache] 💥 Cache service destroyed');
  }
}

// Singleton instance
export const colorCacheService = new ColorCacheService();

// Export for testing
export { ColorCacheService, type CacheMetrics, type ColorAPIResult };