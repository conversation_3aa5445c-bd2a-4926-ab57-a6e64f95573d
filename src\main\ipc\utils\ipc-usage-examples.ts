/**
 * @file ipc-usage-examples.ts
 * @description Examples of how to use the IPC error handling utilities
 *
 * This file demonstrates best practices for implementing IPC handlers using
 * the comprehensive error handling and validation utilities.
 */

import { ipcMain } from 'electron';
import { registerHandlerSafely } from '../../utils/ipcRegistry';
import {
  validate,
  respond,
  wrap,
  ValidationResult,
  validateRequiredFields,
} from './ipc-error-handling';
import { ColorEntry } from '../../../shared/types/color.types';
import { IPCResponse } from '../../../shared/types/ipc.types';

// ============================================================================
// EXAMPLE 1: Simple handler with automatic error handling
// ============================================================================

/**
 * Example of a simple handler using the error handling wrapper
 */
function registerSimpleColorHandler() {
  const handler = wrap.withErrorHandling(
    async (colorId: string): Promise<ColorEntry | null> => {
      // Simulate color lookup logic
      if (!colorId) {
        throw new Error('Color ID is required');
      }

      // Simulate database operation that might fail
      if (colorId === 'invalid') {
        throw new Error('Color not found in database');
      }

      // Return mock color data
      return {
        id: colorId,
        name: 'Sample Color',
        hex: '#FF0000',
        product: 'Sample Product',
        code: 'SAMPLE-001',
        cmyk: '0,100,100,0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as ColorEntry;
    },
    {
      channel: 'color:get-by-id',
      requireOrganization: true,
      requireAuth: true,
      customErrorMessage: 'Failed to retrieve color information',
    }
  );

  registerHandlerSafely(ipcMain, 'color:get-by-id', handler);
}

// ============================================================================
// EXAMPLE 2: Handler with comprehensive input validation
// ============================================================================

interface CreateColorRequest {
  name: string;
  hex: string;
  pantone?: string;
  cmyk?: string;
  product?: string;
}

/**
 * Validates color creation input
 */
function validateCreateColorRequest(
  request: CreateColorRequest
): ValidationResult {
  // Validate required fields
  const requiredValidation = validate.required(request, ['name', 'hex']);

  // Validate field types - only validate fields that are present
  const typeValidation = validate.types(request, {
    name: 'string',
    hex: 'string',
    ...(request.pantone !== undefined && { pantone: 'string' }),
    ...(request.cmyk !== undefined && { cmyk: 'string' }),
    ...(request.product !== undefined && { product: 'string' }),
  } as Record<
    keyof CreateColorRequest,
    'string' | 'number' | 'boolean' | 'object' | 'array'
  >);

  // Validate string formats
  const formatValidation = validate.formats(request, {
    hex: 'hex',
  });

  // Additional business logic validation
  const businessValidation: ValidationResult = {
    isValid: true,
    errors: [],
  };

  // Check name length
  if (request.name && request.name.length > 100) {
    businessValidation.errors.push({
      field: 'name',
      code: 'NAME_TOO_LONG',
      message: 'Color name must be 100 characters or less',
      value: request.name,
    });
    businessValidation.isValid = false;
  }

  return validate.combine(
    requiredValidation,
    typeValidation,
    formatValidation,
    businessValidation
  );
}

/**
 * Example of a handler with comprehensive validation
 */
function registerCreateColorHandler() {
  const handler = wrap.withValidation(
    async (request: CreateColorRequest): Promise<ColorEntry> => {
      // Business logic - simulate color creation
      const newColor: ColorEntry = {
        id: `color_${Date.now()}`,
        name: request.name,
        hex: request.hex,
        product: request.product || 'Default Product',
        code: request.pantone || 'DEFAULT-001',
        cmyk: request.cmyk || '0,0,0,0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as ColorEntry;

      // Simulate database save
      console.log('[Color] Creating new color:', newColor);

      return newColor;
    },
    // Validation function
    (request: CreateColorRequest) => validateCreateColorRequest(request),
    {
      channel: 'color:create',
      requireOrganization: true,
      requireAuth: true,
      customErrorMessage: 'Failed to create color',
    }
  );

  registerHandlerSafely(ipcMain, 'color:create', handler);
}

// ============================================================================
// EXAMPLE 3: Manual error handling for complex operations
// ============================================================================

/**
 * Example of manual error handling for complex operations
 */
function registerComplexSyncHandler() {
  const handler = async (
    _event: any,
    syncType: 'full' | 'incremental',
    options?: { force?: boolean }
  ): Promise<IPCResponse<{ itemsProcessed: number; duration: number }>> => {
    try {
      // Manual context validation with custom logic
      const authValidation = await validate.authContext();
      const orgValidation = await validate.orgContext();

      if (!authValidation.isValid || !orgValidation.isValid) {
        return respond.validationError(
          validate.combine(authValidation, orgValidation),
          'Please ensure you are logged in and have selected an organization'
        );
      }

      // Input validation
      const inputValidation = validateRequiredFields({ syncType }, [
        'syncType',
      ]);
      if (!inputValidation.isValid) {
        return respond.validationError<{
          itemsProcessed: number;
          duration: number;
        }>(inputValidation);
      }

      if (!['full', 'incremental'].includes(syncType)) {
        return respond.validationError<{
          itemsProcessed: number;
          duration: number;
        }>({
          isValid: false,
          errors: [
            {
              field: 'syncType',
              code: 'INVALID_SYNC_TYPE',
              message: 'syncType must be either "full" or "incremental"',
            },
          ],
        });
      }

      // Complex business logic with multiple potential failure points
      let itemsProcessed = 0;
      const startTime = Date.now();

      try {
        // Simulate sync operation phases
        console.log(`[Sync] Starting ${syncType} sync...`);

        // Phase 1: Preparation (might fail)
        await new Promise(resolve => setTimeout(resolve, 100));
        itemsProcessed += 10;

        // Phase 2: Data processing (might fail)
        if (options?.force) {
          await new Promise(resolve => setTimeout(resolve, 200));
          itemsProcessed += 25;
        } else {
          await new Promise(resolve => setTimeout(resolve, 150));
          itemsProcessed += 15;
        }

        // Phase 3: Finalization (might fail)
        await new Promise(resolve => setTimeout(resolve, 50));
        itemsProcessed += 5;

        const duration = Date.now() - startTime;

        return respond.success(
          { itemsProcessed, duration },
          `${syncType} sync completed successfully`
        );
      } catch (syncError) {
        // Handle specific sync errors
        console.error('[Sync] Sync operation failed:', syncError);
        return respond.fromError<{ itemsProcessed: number; duration: number }>(
          syncError,
          'Sync operation failed. Please try again.',
          'sync-operation'
        );
      }
    } catch (error) {
      // Handle unexpected errors
      return respond.fromError<{ itemsProcessed: number; duration: number }>(
        error,
        'An unexpected error occurred during sync',
        'complex-sync-handler'
      );
    }
  };

  registerHandlerSafely(ipcMain, 'sync:complex-operation', handler);
}

// ============================================================================
// EXAMPLE 4: Resource validation patterns
// ============================================================================

/**
 * Example of resource existence validation
 */
function registerUpdateColorHandler() {
  const handler = wrap.withErrorHandling(
    async (
      colorId: string,
      updates: Partial<ColorEntry>
    ): Promise<ColorEntry> => {
      // Validate input
      const inputValidation = validate.required({ colorId }, ['colorId']);
      if (!inputValidation.isValid) {
        throw new Error(
          `Invalid input: ${inputValidation.errors.map(e => e.message).join(', ')}`
        );
      }

      // Simulate checking if color exists
      const colorExists = colorId !== 'nonexistent';
      const existenceValidation = validate.resourceExists(
        'Color',
        colorId,
        colorExists
      );
      if (!existenceValidation.isValid) {
        throw new Error(
          existenceValidation.errors[0]?.message || 'Resource validation failed'
        );
      }

      // Validate updates if provided
      if (updates.hex) {
        const formatValidation = validate.formats(
          { hex: updates.hex },
          { hex: 'hex' }
        );
        if (!formatValidation.isValid) {
          throw new Error(`Invalid hex color format: ${updates.hex}`);
        }
      }

      // Simulate update operation
      const updatedColor: ColorEntry = {
        id: colorId,
        name: updates.name || 'Updated Color',
        hex: updates.hex || '#000000',
        product: 'Updated Product',
        code: 'UPDATE-001',
        cmyk: '0,0,0,100',
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(), // This would come from database
      } as ColorEntry;

      return updatedColor;
    },
    {
      channel: 'color:update',
      requireOrganization: true,
      requireAuth: true,
      customErrorMessage: 'Failed to update color',
    }
  );

  registerHandlerSafely(ipcMain, 'color:update', handler);
}

// ============================================================================
// EXAMPLE 5: Permission validation patterns
// ============================================================================

/**
 * Example of permission-based validation
 */
function registerDeleteColorHandler() {
  const handler = wrap.withErrorHandling(
    async (colorId: string, userRole?: string): Promise<boolean> => {
      // Validate input
      const inputValidation = validate.required({ colorId }, ['colorId']);
      if (!inputValidation.isValid) {
        throw new Error(
          `Invalid input: ${inputValidation.errors.map(e => e.message).join(', ')}`
        );
      }

      // Check if resource exists
      const colorExists = colorId !== 'nonexistent';
      const existenceValidation = validate.resourceExists(
        'Color',
        colorId,
        colorExists
      );
      if (!existenceValidation.isValid) {
        throw new Error(
          existenceValidation.errors[0]?.message || 'Resource validation failed'
        );
      }

      // Check permissions (admin or owner can delete)
      const hasDeletePermission = userRole === 'admin' || userRole === 'owner';
      const permissionValidation = validate.permissions(
        'delete color',
        hasDeletePermission,
        'admin or owner'
      );
      if (!permissionValidation.isValid) {
        throw new Error(
          permissionValidation.errors[0]?.message ||
            'Permission validation failed'
        );
      }

      // Simulate deletion
      console.log(`[Color] Deleting color ${colorId}`);
      return true;
    },
    {
      channel: 'color:delete',
      requireOrganization: true,
      requireAuth: true,
      customErrorMessage: 'Failed to delete color',
    }
  );

  registerHandlerSafely(ipcMain, 'color:delete', handler);
}

// ============================================================================
// EXAMPLE 6: Batch operations with partial success handling
// ============================================================================

interface BatchColorRequest {
  colors: CreateColorRequest[];
}

interface BatchColorResponse {
  successful: ColorEntry[];
  failed: Array<{
    index: number;
    request: CreateColorRequest;
    error: string;
  }>;
  totalProcessed: number;
}

/**
 * Example of batch operation with partial success handling
 */
function registerBatchCreateColorsHandler() {
  const handler = async (
    _event: any,
    request: BatchColorRequest
  ): Promise<IPCResponse<BatchColorResponse>> => {
    try {
      // Validate auth and org context
      const contextValidation = await validate.fullContext();
      if (!contextValidation.isValid) {
        return respond.validationError(
          contextValidation,
          'Authentication and organization context required'
        );
      }

      // Validate input
      const inputValidation = validate.required(request, ['colors']);
      if (!inputValidation.isValid) {
        return respond.validationError(inputValidation);
      }

      if (!Array.isArray(request.colors) || request.colors.length === 0) {
        return respond.validationError({
          isValid: false,
          errors: [
            {
              field: 'colors',
              code: 'INVALID_BATCH_SIZE',
              message: 'colors must be a non-empty array',
            },
          ],
        });
      }

      // Process batch with partial success handling
      const successful: ColorEntry[] = [];
      const failed: BatchColorResponse['failed'] = [];

      for (let i = 0; i < request.colors.length; i++) {
        const colorRequest = request.colors[i];

        // Skip undefined/null entries
        if (!colorRequest) {
          failed.push({
            index: i,
            request: {} as CreateColorRequest,
            error: 'Color request is null or undefined',
          });
          continue;
        }

        // Type assertion - colorRequest is guaranteed to be defined here
        const validColorRequest: CreateColorRequest = colorRequest;

        try {
          // Validate individual color request
          const colorValidation = validateCreateColorRequest(validColorRequest);
          if (!colorValidation.isValid) {
            failed.push({
              index: i,
              request: validColorRequest,
              error: colorValidation.errors.map(e => e.message).join(', '),
            });
            continue;
          }

          // Create color (simulate)
          const newColor: ColorEntry = {
            id: `batch_color_${Date.now()}_${i}`,
            name: validColorRequest.name,
            hex: validColorRequest.hex,
            product: validColorRequest.product || 'Batch Product',
            code: validColorRequest.pantone || `BATCH-${i}`,
            cmyk: validColorRequest.cmyk || '0,0,0,0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          } as ColorEntry;

          successful.push(newColor);
        } catch (error) {
          failed.push({
            index: i,
            request: validColorRequest,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      const result: BatchColorResponse = {
        successful,
        failed,
        totalProcessed: request.colors.length,
      };

      // Return success even if some items failed (partial success)
      return respond.success(
        result,
        `Batch operation completed: ${successful.length} successful, ${failed.length} failed`
      );
    } catch (error) {
      return respond.fromError<BatchColorResponse>(
        error,
        'Batch color creation failed',
        'batch-create-colors'
      );
    }
  };

  registerHandlerSafely(ipcMain, 'color:batch-create', handler);
}

// ============================================================================
// REGISTRATION FUNCTION
// ============================================================================

/**
 * Register all example handlers (for demonstration purposes)
 * In practice, you would call individual registration functions
 * where appropriate in your application.
 */
export function registerExampleHandlers(): void {
  console.log('[IPC Examples] Registering example handlers...');

  registerSimpleColorHandler();
  registerCreateColorHandler();
  registerComplexSyncHandler();
  registerUpdateColorHandler();
  registerDeleteColorHandler();
  registerBatchCreateColorsHandler();

  console.log('[IPC Examples] ✅ Example handlers registered');
}

// ============================================================================
// UTILITY FUNCTIONS FOR COMMON PATTERNS
// ============================================================================

/**
 * Helper function to create a standardized color validation function
 */
export function createColorValidator(
  requiredFields: (keyof CreateColorRequest)[] = ['name', 'hex']
): (request: CreateColorRequest) => ValidationResult {
  return (request: CreateColorRequest) => {
    const requiredValidation = validate.required(request, requiredFields);
    const typeValidation = validate.types(request, {
      name: 'string',
      hex: 'string',
      ...(request.pantone !== undefined && { pantone: 'string' }),
      ...(request.cmyk !== undefined && { cmyk: 'string' }),
      ...(request.product !== undefined && { product: 'string' }),
    } as Record<
      keyof CreateColorRequest,
      'string' | 'number' | 'boolean' | 'object' | 'array'
    >);
    const formatValidation = validate.formats(request, {
      hex: 'hex',
    });

    return validate.combine(
      requiredValidation,
      typeValidation,
      formatValidation
    );
  };
}

/**
 * Helper function to create a standardized resource handler
 */
export function createResourceHandler<T>(
  resourceName: string,
  operation: (id: string, ...args: any[]) => Promise<T> | T,
  options: {
    requireAuth?: boolean;
    requireOrg?: boolean;
    customErrorMessage?: string;
  } = {}
) {
  return wrap.withErrorHandling(operation, {
    requireAuth: options.requireAuth !== false,
    requireOrganization: options.requireOrg !== false,
    customErrorMessage:
      options.customErrorMessage ||
      `Failed to ${operation.name} ${resourceName}`,
    channel: `${resourceName.toLowerCase()}:${operation.name}`,
  });
}
