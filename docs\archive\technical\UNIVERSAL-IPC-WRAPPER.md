# Universal IPC Wrapper Implementation Guide

This document describes the new universal IPC wrapper system that standardizes security, error handling, and response formatting across all IPC handlers in ChromaSync.

## 🎯 Problem Solved

Previously, IPC handlers had inconsistent patterns for:
- Organization context validation
- Error handling and response formatting
- Service instantiation
- Security patterns

## 📁 Files Created/Modified

### New Files
- `src/main/utils/ipc-wrapper.ts` - Universal IPC wrapper functions
- `src/shared/types/ipc.types.ts` - Shared IPC type definitions (extended existing file)
- `src/main/ipc/soft-delete-refactored.ipc.ts` - Example of refactored handler

### Key Functions in `ipc-wrapper.ts`

#### 1. `createSecureOrgHandler<T, R>(handler, options)`
- **Purpose**: Wraps handlers that require organization context
- **Auto-provides**: `organizationId` as first parameter
- **Features**: Organization validation, error handling, response formatting

#### 2. `createSecureHandler<T, R>(handler, options)`
- **Purpose**: Wraps system-level handlers (no organization context required)
- **Features**: Error handling, response formatting

#### 3. `registerSecureHandler(channel, handler, ipcMain, options)`
- **Purpose**: Helper to register organization-scoped handlers with duplicate prevention
- **Combines**: Universal wrapper + `canRegisterHandler` pattern

#### 4. `registerSystemHandler(channel, handler, ipcMain, options)`
- **Purpose**: Helper to register system-level handlers
- **Combines**: Universal wrapper + duplicate prevention

## 🚀 Usage Examples

### Organization-Scoped Handler
```typescript
// Before (inconsistent)
ipcMain.handle('colors:get-all', async (_, organizationId: string) => {
  try {
    const validatedOrgId = validateRequired('organizationId', organizationId, 'uuid');
    const result = await colorService.getAll(validatedOrgId);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// After (using universal wrapper)
registerSecureHandler(
  'colors:get-all',
  async (organizationId: string) => {
    return await colorService.getAll(organizationId);
  },
  ipcMain,
  {
    logChannel: 'Colors',
    customErrorMessage: 'Failed to retrieve colors. Please try again.'
  }
);
```

### System-Level Handler
```typescript
// Before
ipcMain.handle('system:get-info', async () => {
  try {
    const result = await systemService.getInfo();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// After
registerSystemHandler(
  'system:get-info',
  async () => {
    return await systemService.getInfo();
  },
  ipcMain,
  {
    logChannel: 'System',
    requireAuth: false
  }
);
```

### Manual Wrapper Usage
```typescript
const getSoftDeletedHandler = createSecureOrgHandler(
  async (organizationId: string, limit?: number) => {
    const result = await colorService.getSoftDeleted(organizationId, limit);
    return createSuccessResponse(result, `Retrieved ${result.length} items`);
  },
  {
    logChannel: 'SoftDelete',
    customErrorMessage: 'Failed to retrieve deleted items.'
  }
);

ipcMain.handle('colors:get-soft-deleted', getSoftDeletedHandler);
```

## 🔧 Configuration Options

```typescript
interface IPCHandlerOptions {
  /** Whether organization context is required (default: true for createSecureOrgHandler) */
  requireOrganization?: boolean;
  /** Custom log channel name for debugging (default: 'IPC') */
  logChannel?: string;
  /** Custom error message for users (default: generic message) */
  customErrorMessage?: string;
  /** Whether to skip duplicate handler registration checks (default: false) */
  skipDuplicateCheck?: boolean;
  /** Whether authentication is required (default: false) */
  requireAuth?: boolean;
}
```

## 📊 Response Format

All handlers now return consistent `IPCResponse<T>` objects:

```typescript
interface IPCResponse<T = any> {
  /** Indicates if the operation was successful */
  success: boolean;
  /** The response data if successful */
  data?: T;
  /** Technical error message for debugging */
  error?: string;
  /** User-friendly message for display */
  userMessage?: string;
  /** Timestamp when the response was created */
  timestamp: number;
}
```

## 🔄 Migration Strategy

### Step 1: Identify Handler Types
- **Organization-scoped**: Colors, Products, Datasheets → Use `createSecureOrgHandler`
- **System-level**: License, Settings, App Info → Use `createSecureHandler`

### Step 2: Refactor Handlers
1. Remove manual organization validation
2. Remove try/catch boilerplate
3. Use universal wrapper functions
4. Return data directly (wrapper handles response formatting)

### Step 3: Update Service Instantiation
- Use dependency injection pattern
- Pass service instances to registration functions
- Remove `new Service()` calls from IPC files

## ✅ Benefits Achieved

1. **Consistency**: All handlers follow the same pattern
2. **Security**: Automatic organization context validation
3. **Error Handling**: Standardized error responses and logging
4. **Maintainability**: Less boilerplate, easier to test
5. **Type Safety**: Full TypeScript support with proper generics
6. **Debugging**: Consistent logging with configurable channels

## 🎯 Next Steps

### High Priority
1. Migrate `soft-delete.ipc.ts` to use new wrapper
2. Migrate `integrity.ipc.ts` to use new wrapper
3. Update `color-library.ipc.ts` error handling

### Medium Priority
1. Standardize service instantiation across all IPC files
2. Remove temporary fallback logic in existing handlers
3. Update handler registration to use `canRegisterHandler` pattern

## 📝 Example Implementations

See `src/main/ipc/soft-delete-refactored.ipc.ts` for a complete example of:
- Organization-scoped handlers
- Dependency injection pattern
- Proper error handling
- User-friendly messages
- Consistent response formatting

This universal wrapper system establishes a robust foundation for all future IPC handlers and provides a clear migration path for existing handlers.