/**
 * Apple-inspired design tokens CSS variables
 * This file should be imported in the main CSS file
 */

:root {
  /* Brand colors */
  --color-brand-primary: #1E40AF;
  --color-brand-secondary: #1D4ED8;
  --color-brand-accent: #3B82F6;
  
  /* UI Background colors */
  --color-ui-background-primary: #FFFFFF;
  --color-ui-background-secondary: #F5F5F7;
  --color-ui-background-tertiary: #E9E9EB;
  
  /* UI Foreground colors */
  --color-ui-foreground-primary: #1D1D1F;
  --color-ui-foreground-secondary: #515154;
  --color-ui-foreground-tertiary: #86868B;
  --color-ui-foreground-inverse: #FFFFFF;
  
  /* UI Border colors */
  --color-ui-border-light: #E5E5E7;
  --color-ui-border-medium: #D2D2D7;
  --color-ui-border-dark: #86868B;
  
  /* UI Hover colors */
  --color-ui-background-hover: #F0F0F0;
  
  /* UI Focus color */
  --color-ui-focus: #2563EB;
  
  /* Feedback colors */
  --color-feedback-success: #059669;
  --color-feedback-warning: #D97706;
  --color-feedback-error: #DC2626;
  --color-feedback-info: #0284C7;
  
  /* Font family */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  /* Font sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Font weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Line heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Spacing */
  --spacing-px: 1px;
  --spacing-0: 0;
  --spacing-0\.5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1\.5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2\.5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* Border radius - Apple style */
  --radius-none: 0;
  --radius-sm: 0.25rem;
  --radius-DEFAULT: 0.5rem;
  --radius-md: 0.625rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.25rem;
  --radius-full: 9999px;
  
  /* Shadows - Apple style */
  --shadow: 0 2px 5px rgba(0, 0, 0, 0.06);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.04), 0 2px 6px rgba(0, 0, 0, 0.03);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.03), 0 3px 10px rgba(0, 0, 0, 0.02);
  
  /* Transitions */
  --transition-duration-75: 75ms;
  --transition-duration-100: 100ms;
  --transition-duration-150: 150ms;
  --transition-duration-200: 200ms;
  --transition-duration-300: 300ms;
  --transition-duration-500: 500ms;
  --transition-duration-700: 700ms;
  --transition-duration-1000: 1000ms;
  
  /* Transition easings */
  --transition-easing-linear: linear;
  --transition-easing-in: cubic-bezier(0.4, 0, 1, 1);
  --transition-easing-out: cubic-bezier(0, 0, 0.2, 1);
  --transition-easing-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --transition-easing-apple: cubic-bezier(0.25, 0.1, 0.25, 1.0);
  
  /* Z-index */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;
  --z-dropdown: 1000;
  --z-modal: 1100;
  --z-tooltip: 1200;

  /* Backdrop colors */
  --backdrop-primary: rgba(0, 0, 0, 0.5);
  --backdrop-secondary: rgba(0, 0, 0, 0.3);
  --backdrop-blur: rgba(255, 255, 255, 0.1);

  /* Form element colors */
  --form-border: var(--color-ui-border-medium);
  --form-border-focus: var(--color-ui-focus);
  --form-background: var(--color-ui-background-primary);
  --form-background-disabled: var(--color-ui-background-tertiary);

  /* Button state colors */
  --button-primary-bg: var(--color-brand-primary);
  --button-primary-bg-hover: var(--color-brand-secondary);
  --button-primary-bg-active: #1E3A8A;
  --button-primary-bg-disabled: var(--color-ui-background-tertiary);
  --button-primary-text: var(--color-ui-foreground-inverse);
  --button-primary-text-disabled: var(--color-ui-foreground-tertiary);

  --button-secondary-bg: transparent;
  --button-secondary-bg-hover: var(--color-ui-background-hover);
  --button-secondary-bg-active: var(--color-ui-background-tertiary);
  --button-secondary-border: var(--color-ui-border-medium);
  --button-secondary-text: var(--color-ui-foreground-primary);

  /* Feedback background colors */
  --feedback-bg-success: #ECFDF5;
  --feedback-bg-error: #FEF2F2;
  --feedback-bg-warning: #FFFBEB;
  --feedback-bg-info: #EFF6FF;
  --feedback-border-success: #A7F3D0;
  --feedback-border-error: #FECACA;
  --feedback-border-warning: #FDE68A;
  --feedback-border-info: #BFDBFE;

  /* Progress bar colors */
  --progress-bg: var(--color-ui-background-tertiary);
  --progress-cyan: #00BCD4;
  --progress-magenta: #E91E63;
  --progress-yellow: #FFEB3B;
  --progress-black: #424242;

  /* Status indicator colors */
  --status-success-bg: var(--color-feedback-success);
  --status-warning-bg: var(--color-feedback-warning);
  --status-error-bg: var(--color-feedback-error);
  --status-info-bg: var(--color-feedback-info);
}

/* Light mode colors - Explicit definitions for proper theme switching */
html.light, .light {
  /* UI Background colors - Light mode (explicit) */
  --color-ui-background-primary: #FFFFFF;
  --color-ui-background-secondary: #F5F5F7;
  --color-ui-background-tertiary: #E9E9EB;
  
  /* UI Foreground colors - Light mode (explicit) */
  --color-ui-foreground-primary: #1D1D1F;
  --color-ui-foreground-secondary: #515154;
  --color-ui-foreground-tertiary: #86868B;
  --color-ui-foreground-inverse: #FFFFFF;
  
  /* UI Border colors - Light mode (explicit) */
  --color-ui-border-light: #E5E5E7;
  --color-ui-border-medium: #D2D2D7;
  --color-ui-border-dark: #86868B;
  
  /* UI Hover colors - Light mode */
  --color-ui-background-hover: #E5E5E7; /* Improved contrast for better readability */
  
  /* Shadows - Light mode (explicit) */
  --shadow: 0 2px 5px rgba(0, 0, 0, 0.06);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.03);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.04), 0 2px 6px rgba(0, 0, 0, 0.03);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.03), 0 3px 10px rgba(0, 0, 0, 0.02);
}

/* Dark mode colors - Apple inspired */
html.dark, .dark {
  /* UI Background colors - Dark mode */
  --color-ui-background-primary: #1D1D1F;
  --color-ui-background-secondary: #2C2C2E;
  --color-ui-background-tertiary: #3A3A3C;
  
  /* UI Foreground colors - Dark mode - Improved contrast */
  --color-ui-foreground-primary: #FFFFFF;
  --color-ui-foreground-secondary: #EBEBF0;
  --color-ui-foreground-tertiary: #AAAAAF;
  --color-ui-foreground-inverse: #FFFFFF;
  
  /* UI Border colors - Dark mode */
  --color-ui-border-light: #38383A;
  --color-ui-border-medium: #48484A;
  --color-ui-border-dark: #636366;
  
  /* UI Hover colors - Dark mode */
  --color-ui-background-hover: #38383A; /* Improved contrast for better readability */
  
  /* Shadows - Dark mode */
  --shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.45), 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.5), 0 2px 6px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.5), 0 3px 10px rgba(0, 0, 0, 0.4);

  /* Feedback background colors - Dark mode */
  --feedback-bg-success: #064E3B;
  --feedback-bg-error: #7F1D1D;
  --feedback-bg-warning: #78350F;
  --feedback-bg-info: #1E3A8A;
  --feedback-border-success: #047857;
  --feedback-border-error: #DC2626;
  --feedback-border-warning: #D97706;
  --feedback-border-info: #2563EB;
}

/* Common utility classes */
.text-brand-primary {
  color: var(--color-brand-primary);
}

.text-brand-secondary {
  color: var(--color-brand-secondary);
}

.bg-brand-primary {
  background-color: var(--color-brand-primary);
}

.bg-brand-secondary {
  background-color: var(--color-brand-secondary);
}

.border-brand-primary {
  border-color: var(--color-brand-primary);
}

.border-brand-secondary {
  border-color: var(--color-brand-secondary);
}

/* Focus outline utility */
.focus-outline {
  outline: 2px solid var(--color-ui-focus);
  outline-offset: 2px;
}

/* Transition utility */
.transition-standard {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-duration: var(--transition-duration-200);
  transition-timing-function: var(--transition-easing-apple);
}

/* Apple-style utility classes */
.apple-card {
  background-color: var(--color-ui-background-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-ui-border-medium);
} 