/**
 * @file organization.service.ts
 * @description Service for managing organizations and team members
 *
 * Refactored to use Repository pattern - delegates all database operations
 * to OrganizationRepository and focuses on business logic and external integrations.
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import {
  Organization,
  OrganizationMember,
} from '../../../shared/types/organization.types';
import { getSupabaseClient } from '../../services/supabase-client';
import { getZohoEmailService } from '../../services/service-locator';
import { OrganizationRepository } from '../repositories/organization.repository';
import {
  CreateOrganizationData,
  UpdateOrganizationData,
  CreateInvitationData,
  CreateUserData,
} from '../repositories/interfaces/organization.repository.interface';

export class OrganizationService {
  private repository: OrganizationRepository;

  constructor(db: Database.Database) {
    this.repository = new OrganizationRepository(db);
    // Email service is initialized separately in main process
  }

  /**
   * Create a new organization
   */
  async createOrganization(
    name: string,
    ownerId: string
  ): Promise<Organization> {
    // Validate inputs
    if (!name || typeof name !== 'string') {
      throw new Error(
        'Organization name is required and must be a non-empty string'
      );
    }

    if (!ownerId || typeof ownerId !== 'string') {
      throw new Error('Owner ID is required and must be a non-empty string');
    }

    // Validate UUID format for ownerId
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(ownerId)) {
      throw new Error('Owner ID must be a valid UUID format');
    }

    // Trim and validate name
    const trimmedName = name.trim();
    if (trimmedName.length === 0) {
      throw new Error('Organization name cannot be empty or only whitespace');
    }

    if (trimmedName.length > 100) {
      throw new Error('Organization name cannot exceed 100 characters');
    }

    // Check for duplicate organization name for this user
    const userOrganizations = this.repository.findForUser(ownerId);
    const existingOrg = userOrganizations.find(
      org => org.name.toLowerCase() === trimmedName.toLowerCase()
    );

    if (existingOrg) {
      throw new Error(
        `You already have an organization named "${trimmedName}"`
      );
    }

    const externalId = uuidv4();
    const slug = this.generateSlug(trimmedName);

    // Create organization data
    const orgData: CreateOrganizationData = {
      id: externalId,
      name: trimmedName,
      slug,
      plan: 'free',
      settings: JSON.stringify({}),
      ownerId,
    };

    // Insert organization via repository
    this.repository.insert(orgData);
    const org = this.repository.findById(externalId);

    if (!org) {
      throw new Error('Failed to create organization');
    }

    // Also sync to Supabase in background - LOCAL-FIRST approach
    try {
      const { getSupabaseClient } = await import(
        '../../services/supabase-client'
      );

      // Skip authentication check for local-first operation
      // If user is not authenticated, sync will simply fail gracefully
      console.log(
        '[Organization] Attempting background sync to Supabase (local-first)'
      );

      const supabase = getSupabaseClient();

      await supabase.from('organizations').insert({
        id: externalId,
        name: trimmedName,
        slug,
        plan: 'free',
        settings: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      // Add owner as member in Supabase
      await supabase.from('organization_members').insert({
        organization_id: externalId,
        user_id: ownerId,
        role: 'owner',
        joined_at: new Date().toISOString(),
      });

      console.log(
        '[Organization] ✅ Successfully synced to Supabase with user auth:',
        externalId
      );
    } catch (error) {
      const errorMessage = `Organization sync to Supabase failed: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`[OrganizationService] ❌ ${errorMessage}`);
      console.error('[OrganizationService] Error details:', error);

      // Check debug flag to decide between error propagation or graceful fallback
      const shouldElevateErrors = process.env.SYNC_ERROR_ELEVATION === 'true';
      if (shouldElevateErrors) {
        console.error(
          `[OrganizationService] 🚨 Elevating org sync error due to SYNC_ERROR_ELEVATION=true`
        );
        throw new Error(`SYNC_ORG_ERROR: ${errorMessage}`);
      } else {
        console.warn(
          `[OrganizationService] ⚠️ Continuing with local-first approach despite sync failure`
        );
        // Continue even if Supabase sync fails - local-first approach
      }
    }

    return this.mapToOrganization(org);
  }

  /**
   * Get organizations for a user
   */
  async getOrganizationsForUser(userId: string): Promise<Organization[]> {
    console.log(
      `[OrganizationService] getOrganizationsForUser called with userId: "${userId}" (type: ${typeof userId})`
    );

    const rows = this.repository.findForUser(userId);

    console.log(
      `[OrganizationService] Query returned ${rows.length} organizations for userId: "${userId}"`
    );

    if (rows.length === 0) {
      console.log(
        `[OrganizationService] DEBUG: No organizations found for userId "${userId}"`
      );

      // Check if user exists in users table
      const user = this.repository.findUser(userId);
      if (user) {
        console.log(`[OrganizationService] DEBUG: Found user in users table:`, {
          id: user.id,
          email: user.email,
        });
      } else {
        console.log(
          `[OrganizationService] DEBUG: No user found in users table with ID "${userId}"`
        );
      }
    }

    return rows.map(row => ({
      ...this.mapToOrganization(row),
      userRole: row.user_role as 'owner' | 'admin' | 'member', // Add the user's role to the organization object
    }));
  }

  /**
   * Get organization by ID
   */
  async getOrganization(orgId: string): Promise<Organization | null> {
    const row = this.repository.findById(orgId);
    return row ? this.mapToOrganization(row) : null;
  }

  /**
   * Update organization
   */
  async updateOrganization(
    orgId: string,
    updates: Partial<Organization>
  ): Promise<Organization | null> {
    // Filter allowed updates
    const updateData: UpdateOrganizationData = {};
    if (updates.name !== undefined) {updateData.name = updates.name;}
    if (updates.plan !== undefined) {updateData.plan = updates.plan;}
    if (updates.settings !== undefined)
      {updateData.settings = JSON.stringify(updates.settings);}

    if (Object.keys(updateData).length === 0) {
      return this.getOrganization(orgId);
    }

    const success = this.repository.update(orgId, updateData);
    return success ? this.getOrganization(orgId) : null;
  }

  /**
   * Delete organization with proper cascade handling
   */
  async deleteOrganization(
    orgId: string,
    userId: string,
    forceCascade: boolean = false
  ): Promise<boolean> {
    try {
      // Check if user has permission (must be owner)
      const userRole = this.repository.getUserRole(orgId, userId);
      if (userRole !== 'owner') {
        throw new Error('Only organization owners can delete organizations');
      }

      // Check if organization exists
      const org = this.repository.findById(orgId);
      if (!org) {
        throw new Error('Organization not found');
      }

      // Get data counts and perform cascade delete via repository
      const result = this.repository.deleteOrganizationCascade(
        orgId,
        forceCascade
      );

      if (!result.success) {
        throw new Error('Failed to delete organization');
      }

      console.log(
        `[OrganizationService] Successfully deleted organization with data:`,
        result.deletedData
      );

      // Also delete from Supabase
      try {
        const supabase = getSupabaseClient();
        await supabase.from('organizations').delete().eq('id', orgId);
      } catch (error) {
        console.error('Failed to delete organization from Supabase:', error);
        // Continue even if Supabase delete fails
      }

      return true;
    } catch (error) {
      console.error('Error deleting organization:', error);
      throw error;
    }
  }

  /**
   * Get organization members
   */
  async getMembers(orgId: string): Promise<OrganizationMember[]> {
    console.log(
      '[OrganizationService] Getting members for organization:',
      orgId
    );

    const rows = this.repository.findMembers(orgId);

    console.log(
      `[OrganizationService] Found ${rows.length} members in database`
    );

    // Get the current user ID locally (LOCAL-FIRST)
    let currentUserId: string | null = null;
    try {
      // For local-first operation, we should have the current user stored locally
      // This might come from the auth context or organization context
      console.log(
        '[OrganizationService] Getting current user ID locally (local-first approach)'
      );
      // TODO: Implement local current user context
      currentUserId = null; // For now, continue without current user highlighting
    } catch (error) {
      console.error('Failed to get current user locally:', error);
    }

    // Map members and ensure user data is available
    const members = await Promise.all(
      rows.map(async row => {
        const memberData = this.mapToMember(row);

        // If user data is missing locally, try to fetch from Supabase
        if (!row.user_email && row.user_id) {
          console.log(
            `[OrganizationService] Missing user data for ${row.user_id}, fetching from Supabase...`
          );
          try {
            const userData = await this.fetchUserFromLocal(row.user_id);
            if (userData) {
              memberData.user = userData;
              // Also sync to local database via repository
              this.repository.syncUserProfile({
                id: userData.id,
                email: userData.email,
                name: userData.name,
              });
            }
          } catch (error) {
            console.error(
              `Failed to fetch user data for ${row.user_id}:`,
              error
            );
          }
        }

        return {
          ...memberData,
          isCurrentUser: row.user_id === currentUserId,
        };
      })
    );

    return members;
  }

  /**
   * Add member to organization
   */
  async addMember(
    orgId: string,
    userId: string,
    role: 'admin' | 'member' = 'member',
    invitedBy?: string
  ): Promise<OrganizationMember> {
    const success = this.repository.insertMember(
      orgId,
      userId,
      role,
      invitedBy
    );

    if (!success) {
      throw new Error(
        'Failed to add member - organization not found or user already a member'
      );
    }

    const member = await this.getMember(orgId, userId);
    if (!member) {
      throw new Error('Failed to retrieve added member');
    }

    return member;
  }

  /**
   * Update member role
   */
  async updateMemberRole(
    orgId: string,
    userId: string,
    role: 'admin' | 'member'
  ): Promise<OrganizationMember | null> {
    const success = this.repository.updateMemberRole(orgId, userId, role);

    if (!success) {
      return null; // Organization not found, member not found, or trying to change owner role
    }

    return this.getMember(orgId, userId);
  }

  /**
   * Remove member from organization
   */
  async removeMember(orgId: string, userId: string): Promise<boolean> {
    return this.repository.removeMember(orgId, userId);
  }

  /**
   * Check if user is member of organization
   */
  async isMember(orgId: string, userId: string): Promise<boolean> {
    return this.repository.checkUserMembership(orgId, userId);
  }

  /**
   * Get member details
   */
  async getMember(
    orgId: string,
    userId: string
  ): Promise<OrganizationMember | null> {
    const row = this.repository.findMember(orgId, userId);
    return row ? this.mapToMember(row) : null;
  }

  /**
   * Get user's role in organization
   */
  async getUserRole(orgId: string, userId: string): Promise<string | null> {
    return this.repository.getUserRole(orgId, userId);
  }

  /**
   * Create an invitation to join an organization
   */
  async inviteMember(
    orgId: string,
    email: string,
    role: 'admin' | 'member',
    invitedById: string
  ): Promise<{ success: boolean; invitation?: any; error?: string }> {
    try {
      const org = this.repository.findById(orgId);

      console.log('[Organization] DEBUG - Organization lookup result:');
      console.log('  orgId:', orgId);
      console.log('  org found:', org);

      if (!org) {
        return { success: false, error: 'Organization not found' };
      }

      // Check if user has permission to invite (must be owner or admin)
      const inviterRole = await this.getUserRole(orgId, invitedById);
      if (
        !inviterRole ||
        (inviterRole !== 'owner' && inviterRole !== 'admin')
      ) {
        return {
          success: false,
          error: 'Insufficient permissions to invite members',
        };
      }

      // Check if email is already a member (skip this check since users table may not exist)
      // This will be caught when the user actually tries to join if they're already a member

      // Check if invitation already exists
      const existingInvitation = this.repository.checkExistingInvitation(
        orgId,
        email
      );

      console.log('[Organization] DEBUG - Existing invitation check:');
      console.log('  existingInvitation:', existingInvitation);

      if (existingInvitation) {
        if (existingInvitation.accepted_at) {
          return {
            success: false,
            error:
              'User has already accepted an invitation to this organization',
          };
        } else {
          // Delete the old invitation to allow a new one (in case it expired)
          console.log(
            '[Organization] DEBUG - Deleting existing invitation to allow new one'
          );
          this.repository.deleteInvitation(orgId, email);
        }
      }

      // Create invitation
      const externalId = uuidv4();
      const token = uuidv4();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

      // Debug logging before insert
      console.log(
        '[Organization] DEBUG - About to insert invitation with values:'
      );
      console.log('  externalId:', externalId);
      console.log('  org.id:', org.id, '(type:', typeof org.id, ')');
      console.log('  org.name:', org.name);
      console.log('  email:', email);
      console.log('  role:', role);
      console.log('  invitedById:', invitedById);
      console.log('  token:', `${token.substring(0, 8)  }...`);
      console.log('  expiresAt:', expiresAt.toISOString());

      // Ensure the inviter exists in the users table
      const inviterExists = this.repository.findUser(invitedById);

      console.log('[Organization] DEBUG - Inviter check:');
      console.log('  invitedById:', invitedById);
      console.log('  inviterExists:', !!inviterExists);

      if (!inviterExists) {
        console.log(
          '[Organization] DEBUG - User record missing for inviter - fetching from Supabase'
        );

        // CRITICAL: Try to fetch real user data from Supabase instead of creating placeholder
        const realUserData = await this.fetchUserFromLocal(invitedById);
        if (!realUserData || !realUserData.email) {
          console.error(
            '[Organization] CRITICAL - Cannot create invitation without real user data for inviter'
          );
          return {
            success: false,
            error:
              'Inviter user data not found. Please ensure the user is properly authenticated.',
          };
        }

        // Sync user to local database via repository
        const userSynced = this.repository.syncUserProfile({
          id: realUserData.id,
          email: realUserData.email,
          name: realUserData.name,
        });

        if (!userSynced) {
          console.error(
            '[Organization] DEBUG - Failed to create user record for inviter'
          );
          return { success: false, error: 'Failed to sync inviter user data.' };
        }

        console.log('[Organization] DEBUG - Created user record for inviter');
      }

      // Verify organization exists
      const organization = this.repository.findById(orgId);
      if (!organization) {
        throw new Error('Organization not found');
      }

      // Create invitation data
      const invitationData: CreateInvitationData = {
        id: externalId,
        organization_id: orgId,
        email,
        role,
        invited_by: invitedById,
        token,
        expires_at: expiresAt.toISOString(),
      };

      try {
        this.repository.insertInvitation(invitationData);
        console.log('[Organization] DEBUG - Successfully created invitation');
      } catch (insertError) {
        console.error('[Organization] DEBUG - Insert error details:', {
          code: (insertError as any).code,
          message: (insertError as any).message,
          values: {
            externalId,
            orgId,
            email,
            role,
            invitedById,
            tokenPrefix: token.substring(0, 8),
          },
        });
        throw insertError;
      }

      // Send email notification directly (bypassing Edge Function)
      try {
        // Get inviter details with proper name
        const inviterDetails = this.repository.findUser(invitedById);

        // Determine the best name to use
        let inviterName = 'A team member';
        if (inviterDetails) {
          inviterName =
            inviterDetails.display_name ||
            inviterDetails.name ||
            inviterDetails.email?.split('@')[0] ||
            'A team member';
        }

        // Send email directly via Zoho
        const emailSent = await this.sendInvitationEmailDirect(email, {
          organizationName: org.name,
          inviterName,
          role,
          token,
          expiresAt,
        });

        if (!emailSent) {
          console.error(
            '[Organization] Failed to send invitation email to',
            email
          );
          // Don't fail the invitation creation if email fails
        } else {
          console.log(
            '[Organization] Invitation email sent successfully to',
            email
          );
        }
      } catch (emailError) {
        console.error('[Organization] Email sending error:', emailError);
        // Continue anyway - invitation is created
      }

      return {
        success: true,
        invitation: {
          id: externalId,
          email,
          role,
          token,
          expiresAt: expiresAt.toISOString(),
        },
      };
    } catch (error) {
      console.error('[Organization] Error inviting member:', error);
      console.error('[Organization] Error details:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        code: (error as any)?.code,
        errno: (error as any)?.errno,
        organizationId: orgId,
        email,
        role,
        invitedById,
      });
      return {
        success: false,
        error: `Failed to create invitation: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Accept an invitation to join an organization
   */
  async acceptInvitation(
    token: string,
    userId: string
  ): Promise<{
    success: boolean;
    organization?: Organization;
    error?: string;
  }> {
    try {
      // Find the invitation
      const invitation = this.repository.findInvitation(token);

      if (!invitation) {
        return { success: false, error: 'Invalid or expired invitation' };
      }

      // Check if invitation has expired
      if (new Date(invitation.expires_at) < new Date()) {
        return { success: false, error: 'This invitation has expired' };
      }

      // Check if user email matches invitation email
      const user = this.repository.findUser(userId);

      if (!user || user.email !== invitation.email) {
        return {
          success: false,
          error: 'This invitation is for a different email address',
        };
      }

      // Get organization external ID to add member
      const org = this.repository.findById(
        invitation.organization_id.toString()
      );
      if (!org) {
        return { success: false, error: 'Organization not found' };
      }

      // Add user to organization via repository
      const memberAdded = this.repository.insertMember(
        org.id,
        userId,
        invitation.role,
        invitation.invited_by
      );
      if (!memberAdded) {
        return { success: false, error: 'Failed to add user to organization' };
      }

      // Mark invitation as accepted
      const invitationAccepted = this.repository.acceptInvitation(
        invitation.id.toString()
      );
      if (!invitationAccepted) {
        return {
          success: false,
          error: 'Failed to mark invitation as accepted',
        };
      }

      return {
        success: true,
        organization: this.mapToOrganization(org),
      };
    } catch (error) {
      console.error('[Organization] Error accepting invitation:', error);
      return { success: false, error: 'Failed to accept invitation' };
    }
  }

  /**
   * Get pending invitations for an organization
   */
  async getPendingInvitations(orgId: string): Promise<any[]> {
    const invitations = this.repository.findPendingInvitations(orgId);

    return invitations.map(inv => {
      // Get inviter details
      const inviter = this.repository.findUser(inv.invited_by);
      const inviterName = inviter
        ? inviter.display_name ||
          inviter.name ||
          inviter.email?.split('@')[0] ||
          `User ${inv.invited_by.substring(0, 8)}`
        : `User ${inv.invited_by.substring(0, 8)}`;

      return {
        id: inv.id,
        email: inv.email,
        role: inv.role,
        invitedBy: {
          id: inv.invited_by,
          name: inviterName,
        },
        expiresAt: inv.expires_at,
        createdAt: inv.created_at,
      };
    });
  }

  /**
   * Revoke an invitation
   */
  async revokeInvitation(
    orgId: string,
    invitationId: string
  ): Promise<boolean> {
    return this.repository.revokeInvitation(orgId, invitationId);
  }

  /**
   * Generate URL-safe slug from name
   */
  private generateSlug(name: string): string {
    // Validate input
    if (!name || typeof name !== 'string') {
      throw new Error(
        'Organization name is required and must be a non-empty string'
      );
    }

    // Trim whitespace and check if name is empty
    const trimmedName = name.trim();
    if (trimmedName.length === 0) {
      throw new Error('Organization name cannot be empty or only whitespace');
    }

    const baseSlug = trimmedName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    // Handle case where slug becomes empty after processing (e.g., only special chars)
    const finalBaseSlug = baseSlug.length > 0 ? baseSlug : 'organization';

    // Use repository to generate unique slug
    return this.repository.generateUniqueSlug(finalBaseSlug);
  }

  /**
   * Map database row to Organization
   */
  private mapToOrganization(row: any): Organization {
    // DEBUG: Log the exact structure of row and its settings field
    console.log(`[OrganizationService] 🔍 DEBUG: Mapping organization row:`, {
      id: row.id,
      name: row.name,
      settings: row.settings,
      settingsType: typeof row.settings,
      settingsIsNull: row.settings === null,
      settingsIsUndefined: row.settings === undefined,
      settingsStringified: JSON.stringify(row.settings),
      fullRow: row,
    });

    let parsedSettings = {};
    try {
      if (typeof row.settings === 'string') {
        parsedSettings = JSON.parse(row.settings);
        console.log(
          `[OrganizationService] 🔍 DEBUG: Parsed settings from string:`,
          parsedSettings
        );
      } else {
        parsedSettings = row.settings || {};
        console.log(
          `[OrganizationService] 🔍 DEBUG: Using settings as-is:`,
          parsedSettings
        );
      }
    } catch (error) {
      console.error(
        `[OrganizationService] ❌ ERROR: Failed to parse settings:`,
        error
      );
      console.error(`[OrganizationService] ❌ Settings value:`, row.settings);
      parsedSettings = {};
    }

    return {
      id: row.id,
      external_id: row.id,
      name: row.name,
      slug: row.slug,
      plan: row.plan,
      settings: parsedSettings,
      created_at: row.created_at,
      updated_at: row.updated_at,
      memberCount: row.member_count || 1,
    };
  }

  /**
   * Map database row to OrganizationMember
   */
  private mapToMember(row: any): OrganizationMember {
    // Determine the best name to display (prioritize display_name, then name, then email prefix)
    let displayName = 'User';
    if (row.user_display_name) {
      displayName = row.user_display_name;
    } else if (row.user_name) {
      displayName = row.user_name;
    } else if (row.user_email) {
      displayName = row.user_email.split('@')[0];
    } else {
      displayName = `user-${row.user_id.substring(0, 8)}`;
    }

    return {
      organization_id: row.organization_id,
      user_id: row.user_id,
      role: row.role,
      joined_at: row.joined_at,
      invited_by: row.invited_by,
      user: {
        id: row.user_id,
        email: row.user_email || 'Unknown Email',
        name: displayName,
      },
    };
  }

  /**
   * Fetch user data from local users table (LOCAL-FIRST)
   */
  private async fetchUserFromLocal(
    userId: string
  ): Promise<{ id: string; email: string; name?: string } | null> {
    try {
      // Use local repository to fetch user data - no cloud dependency
      const user = this.repository.findUser(userId);

      if (!user) {
        console.warn(
          `[OrganizationService] User ${userId} not found in local users table`
        );
        return null;
      }

      // CRITICAL: Never create placeholder emails - this causes confusion for users
      if (!user.email) {
        console.error(
          `[OrganizationService] User ${userId} has no email in local profile`
        );
        return null;
      }

      return {
        id: user.id,
        email: user.email,
        name:
          user.display_name || user.name || user.email.split('@')[0] || 'User',
      };
    } catch (error) {
      console.error(
        `Error fetching user ${userId} from local database:`,
        error
      );
      return null;
    }
  }

  /**
   * Sync current user profile to local database (public method for OAuth)
   */
  async syncUserProfileToLocal(
    userId: string,
    email?: string,
    fullName?: string
  ): Promise<void> {
    try {
      // CRITICAL: Never create placeholder emails - this causes confusion for users
      if (!email) {
        console.error(
          `[OrganizationService] Cannot sync user profile without email for ${userId}`
        );
        throw new Error('User email is required for profile sync');
      }

      const userData: CreateUserData = {
        id: userId,
        email,
        name: fullName || email.split('@')[0] || 'User',
      };

      const success = this.repository.syncUserProfile(userData);
      if (!success) {
        throw new Error('Failed to sync user profile to database');
      }

      console.log(
        `[OrganizationService] Successfully synced user profile: ${userData.email}`
      );
    } catch (error) {
      console.error(
        `[OrganizationService] Failed to sync user profile for ${userId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Validate email address to prevent placeholder emails
   */
  private isValidEmail(email: string): boolean {
    // Check for placeholder email patterns
    if (
      email.includes('@local.app') ||
      email.startsWith('user-') ||
      email === 'Unknown Email'
    ) {
      return false;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Cleanup existing placeholder emails in database
   */
  async cleanupPlaceholderEmails(
    currentUserId: string,
    realEmail: string
  ): Promise<void> {
    try {
      if (!this.isValidEmail(realEmail)) {
        console.error(
          '[OrganizationService] Cannot cleanup with invalid email:',
          realEmail
        );
        return;
      }

      // Update any placeholder emails for the current user via repository
      const success = this.repository.cleanupPlaceholderEmails(
        currentUserId,
        realEmail
      );

      if (success) {
        console.log(
          `[OrganizationService] ✅ Cleaned up placeholder email for user ${currentUserId} -> ${realEmail}`
        );
      }
    } catch (error) {
      console.error(
        '[OrganizationService] Failed to cleanup placeholder emails:',
        error
      );
    }
  }

  /**
   * Sync organization members from Supabase to local database
   */
  async syncMembersFromSupabase(orgId: string): Promise<void> {
    try {
      console.log(
        `[OrganizationService] Starting sync for organization: ${orgId}`
      );
      const supabase = getSupabaseClient();

      // Verify organization exists
      const organization = this.repository.findById(orgId);

      if (!organization) {
        console.error('Organization not found:', orgId);
        return;
      }

      console.log(
        `[OrganizationService] Found local organization ID: ${orgId}`
      );

      // Fetch members from Supabase (without profiles dependency)
      const { data: members, error } = await supabase
        .from('organization_members')
        .select(
          `
          user_id,
          role,
          joined_at,
          invited_by
        `
        )
        .eq('organization_id', orgId);

      if (error) {
        console.error('Failed to fetch members from Supabase:', error);
        return;
      }

      if (!members || members.length === 0) {
        console.log(
          `[OrganizationService] No members found in Supabase for organization: ${orgId}`
        );
        return;
      }

      console.log(
        `[OrganizationService] Found ${members.length} members in Supabase`
      );
      console.log('[OrganizationService] Members data:', members);

      // Sync each member to local database
      for (const member of members) {
        console.log(
          `[OrganizationService] Syncing member: ${member.user_id}`,
          member
        );

        // Fetch user data locally instead of from Supabase profiles
        const localUserData = await this.fetchUserFromLocal(member.user_id);

        if (localUserData) {
          // User already exists locally - ensure it's up to date
          const userSynced = this.repository.syncUserProfile({
            id: localUserData.id,
            email: localUserData.email,
            name:
              localUserData.name || localUserData.email.split('@')[0] || 'User',
          });

          if (userSynced) {
            console.log(
              `[OrganizationService] Verified local user: ${localUserData.id} (${localUserData.email})`
            );
          } else {
            console.warn(
              `[OrganizationService] Failed to verify local user: ${localUserData.id}`
            );
          }
        } else {
          console.warn(
            `[OrganizationService] No local user data found for member: ${member.user_id} - this member may need to sign in first`
          );
        }

        // Check if membership exists locally via repository
        const existingMembership = this.repository.checkUserMembership(
          orgId,
          member.user_id
        );

        if (!existingMembership) {
          // Add member via repository
          const memberAdded = this.repository.insertMember(
            orgId,
            member.user_id,
            member.role,
            member.invited_by
          );
          if (memberAdded) {
            console.log(
              `[OrganizationService] Added member: ${member.user_id} with role ${member.role}`
            );
          } else {
            console.warn(
              `[OrganizationService] Failed to add member: ${member.user_id}`
            );
          }
        } else {
          // Update role if changed via repository
          const roleUpdated = this.repository.updateMemberRole(
            orgId,
            member.user_id,
            member.role
          );
          if (roleUpdated) {
            console.log(
              `[OrganizationService] Updated member role: ${member.user_id} -> ${member.role}`
            );
          }
        }
      }
    } catch (error) {
      console.error('Error syncing members from Supabase:', error);
    }
  }

  /**
   * Sync organizations from Supabase to local database
   */
  async syncOrganizationsFromSupabase(userId: string): Promise<Organization[]> {
    try {
      // Import getSupabaseClient for background sync
      const { getSupabaseClient } = await import(
        '../../services/supabase-client'
      );

      console.log(
        `[OrganizationService] 🔍 Starting background organization sync for user: ${userId}`
      );

      // LOCAL-FIRST: Try sync without authentication requirements
      // If authentication fails, we'll fall back to local data gracefully
      console.log(
        '[OrganizationService] Attempting background sync (no auth required)'
      );
      const supabase = getSupabaseClient();

      // Debug: Check what's in the organization_members table
      console.log(
        `[OrganizationService] 🔍 Checking organization_members table for any records...`
      );
      const { data: allMemberships, error: debugError } = await supabase
        .from('organization_members')
        .select('user_id, role')
        .limit(10);

      if (!debugError && allMemberships) {
        console.log(
          `[OrganizationService] 🔍 Found ${allMemberships.length} total organization memberships:`,
          allMemberships.map(m => ({ user_id: m.user_id, role: m.role }))
        );
      } else {
        console.log(
          `[OrganizationService] ❌ Error checking organization_members:`,
          debugError
        );
      }

      // Try to fetch organizations using the provided user ID parameter
      let memberships, error;

      // Query with the provided user ID parameter (most reliable)
      console.log(
        `[OrganizationService] 🔍 Querying with provided user ID: ${userId}`
      );
      const userQueryResult = await supabase
        .from('organization_members')
        .select(
          `
          role,
          organization:organizations (
            id,
            name,
            slug,
            plan,
            settings,
            created_at,
            updated_at
          )
        `
        )
        .eq('user_id', userId);

      if (userQueryResult.error) {
        console.log(
          `[OrganizationService] ❌ User query failed:`,
          userQueryResult.error
        );
      } else {
        console.log(
          `[OrganizationService] ✅ User query returned ${userQueryResult.data?.length || 0} memberships`
        );
      }

      // Use the query results directly
      memberships = userQueryResult.data;
      error = userQueryResult.error;

      if (error) {
        console.error(
          '[OrganizationService] Failed to fetch organizations from Supabase:',
          error
        );
        console.log(
          '[OrganizationService] Falling back to local organizations due to Supabase error'
        );
        return this.getOrganizationsForUser(userId);
      }

      if (!memberships || memberships.length === 0) {
        console.log(
          `[OrganizationService] ❌ No organization memberships found in Supabase for userId: "${userId}"`
        );
        console.log(
          `[OrganizationService] 🔍 This user should have created organizations. Investigating...`
        );

        // DEBUG: Check organizations table directly
        console.log(
          `[OrganizationService] 🔍 Checking organizations table for user-created orgs...`
        );
        const { data: allOrgs, error: orgError } = await supabase
          .from('organizations')
          .select('id, name, slug, created_by')
          .limit(20);

        if (!orgError && allOrgs) {
          console.log(
            `[OrganizationService] 🔍 Found ${allOrgs.length} total organizations:`,
            allOrgs.map(org => ({
              id: org.id,
              name: org.name,
              created_by: org.created_by,
              matches_user: org.created_by === userId,
            }))
          );
        }

        console.log(
          '[OrganizationService] Checking if user has local organizations instead...'
        );

        // CRITICAL FIX: Check local organizations before giving up
        const localOrganizations = await this.getOrganizationsForUser(userId);
        if (localOrganizations.length > 0) {
          console.log(
            `[OrganizationService] Found ${localOrganizations.length} local organizations for user, using those instead of Supabase sync`
          );
          return localOrganizations;
        }

        console.log(
          '[OrganizationService] No local organizations found either - user truly has no organizations'
        );
        return [];
      }

      // Sync each organization to local database
      const organizations: Organization[] = [];
      const seenNames = new Set<string>(); // Track organization names to prevent duplicates

      for (const membership of memberships) {
        if (!membership.organization) {
          continue;
        }

        const org = membership.organization as any;

        // DEBUG: Log the exact structure of org and its settings field
        console.log(
          `[OrganizationService] 🔍 DEBUG: Processing organization:`,
          {
            id: org.id,
            name: org.name,
            slug: org.slug,
            plan: org.plan,
            settings: org.settings,
            settingsType: typeof org.settings,
            settingsIsNull: org.settings === null,
            settingsIsUndefined: org.settings === undefined,
            settingsStringified: JSON.stringify(org.settings),
            fullOrg: org,
          }
        );

        // Skip if we've already seen this organization name
        const normalizedName = org.name.toLowerCase();
        if (seenNames.has(normalizedName)) {
          console.warn(
            `Skipping duplicate organization: ${org.name} (${org.id})`
          );
          continue;
        }
        seenNames.add(normalizedName);

        // Check if organization exists locally via repository
        const existingOrg = this.repository.findById(org.id);

        if (!existingOrg) {
          // Check for slug conflicts and generate unique slug if needed
          const existingSlugOrg = this.repository.findBySlug(org.slug);
          let finalSlug = org.slug;

          if (existingSlugOrg && existingSlugOrg.id !== org.id) {
            // Slug conflict detected - generate unique slug
            finalSlug = this.repository.generateUniqueSlug(org.slug);
            console.warn(
              `[OrganizationService] Slug conflict detected for "${org.slug}". Using unique slug: "${finalSlug}"`
            );
          }

          // Create organization locally via repository
          const orgData: CreateOrganizationData = {
            id: org.id,
            name: org.name,
            slug: finalSlug,
            plan: org.plan || 'free',
            settings: JSON.stringify(org.settings || {}),
            ownerId: userId, // Set current user as owner during sync
          };

          const createdExternalId = this.repository.insert(orgData);
          console.log(
            `[OrganizationService] Created organization locally: ${createdExternalId}`
          );
        } else {
          // Update organization via repository
          const updateData: UpdateOrganizationData = {
            name: org.name,
            plan: org.plan || 'free',
            settings: JSON.stringify(org.settings || {}),
          };

          const updated = this.repository.update(org.id, updateData);
          if (updated) {
            console.log(
              `[OrganizationService] Updated organization: ${org.id}`
            );
          }
        }

        // Sync membership via repository
        const existingMembership = this.repository.checkUserMembership(
          org.id,
          userId
        );

        if (!existingMembership) {
          const memberAdded = this.repository.insertMember(
            org.id,
            userId,
            membership.role
          );
          if (memberAdded) {
            console.log(
              `[OrganizationService] Added membership: ${userId} -> ${org.id} as ${membership.role}`
            );
          }
        } else {
          // Update role if changed via repository
          const roleUpdated = this.repository.updateMemberRole(
            org.id,
            userId,
            membership.role
          );
          if (roleUpdated) {
            console.log(
              `[OrganizationService] Updated member role: ${userId} -> ${membership.role}`
            );
          }
        }

        // Add to return list
        const fullOrg = await this.getOrganization(org.id);
        if (fullOrg) {
          organizations.push(fullOrg);
        }
      }

      return organizations;
    } catch (error) {
      console.error('Error syncing organizations from Supabase:', error);
      return [];
    }
  }

  /**
   * Send invitation email using Zoho Mail API
   * Much more reliable than SMTP, with better error handling
   */
  private async sendInvitationEmailDirect(
    to: string,
    invitation: {
      organizationName: string;
      inviterName: string;
      role: string;
      token: string;
      expiresAt: Date;
    }
  ): Promise<boolean> {
    try {
      const zohoEmailService = getZohoEmailService();
      const result = await zohoEmailService.sendInvitationEmail(to, invitation);

      if (result) {
        console.log('[Organization] Email sent successfully via Zoho API');
        console.log('[Organization] Recipient:', to);
        console.log(
          '[Organization] Organization:',
          invitation.organizationName
        );
      } else {
        console.error('[Organization] Failed to send email via Zoho API');
      }

      return result;
    } catch (error) {
      console.error('[Organization] Email error:', error);
      return false;
    }
  }

  // ============================================================================
  // ROUTER-COMPATIBLE ALIASES
  // These methods provide compatibility with the IPC router expectations
  // ============================================================================

  /**
   * Get all organizations for current user (alias for router compatibility)
   */
  async getAll(userId?: string): Promise<Organization[]> {
    if (!userId) {
      // TODO: Get current user ID from context
      throw new Error('User ID required for getAll organizations');
    }
    return this.getOrganizationsForUser(userId);
  }

  /**
   * Create organization (alias for router compatibility)
   */
  async create(data: {
    name: string;
    ownerId?: string;
  }): Promise<Organization> {
    if (!data.ownerId) {
      // TODO: Get current user ID from context
      throw new Error('Owner ID required for organization creation');
    }
    return this.createOrganization(data.name, data.ownerId);
  }

  /**
   * Get organization by ID (alias for router compatibility)
   */
  async getById(id: string): Promise<Organization | null> {
    return this.getOrganization(id);
  }

  /**
   * Get current organization (placeholder implementation)
   */
  async getCurrent(): Promise<Organization | null> {
    // TODO: Implement current organization context logic
    // This would typically get the current organization from user context
    throw new Error('Current organization context not implemented');
  }

  /**
   * Set current organization (placeholder implementation)
   */
  async setCurrent(organizationId: string): Promise<{ success: boolean }> {
    // TODO: Implement current organization context logic
    // This would typically set the current organization in user context
    console.log(`Setting current organization to: ${organizationId}`);
    return { success: true };
  }
}
