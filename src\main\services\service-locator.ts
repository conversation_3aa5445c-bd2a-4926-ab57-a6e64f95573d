/**
 * @file service-locator.ts
 * @description Service locator pattern for easy access to container services
 */

import {
  serviceContainer,
  ServiceName,
  ServiceConfiguration,
} from './service-container';
import { ILogger } from '../utils/logger.service';
import { ColorService } from '../db/services/color.service';
import { ProductService } from '../db/services/product.service';
import { DatasheetService } from '../db/services/datasheet.service';
import { OrganizationService } from '../db/services/organization.service';
import { ColorImportService } from '../db/services/color-import.service';
import { ColorLibraryImportService } from '../db/services/color-library-import.service';
import { ColorLibraryQueryService } from '../db/services/color-library-query.service';
import { AuthenticationManager } from './auth/authentication-manager';
import { SessionManager } from './auth/session-manager';
import { CircuitBreakerAuthManager } from './auth/circuit-breaker-auth-manager';
import { AuthErrorRecoveryService } from './auth/auth-error-recovery.service';
import { AuthNotificationService } from './auth/auth-notification.service';
import { OAuthService } from './auth/oauth-service';
import { ZohoTokenManager } from './email/zoho-token-manager';
import { EmailSender } from './email/email-sender';
import { ZohoEmailService } from './email/zoho-email.service';
import type { Database } from 'better-sqlite3';
// import { unifiedSyncManager } from './sync';

/**
 * Service Locator - Provides typed access to container services
 */
export class ServiceLocator {
  private static initialized = false;

  /**
   * Initialize services with configuration
   */
  static async initialize(configuration?: ServiceConfiguration): Promise<void> {
    if (ServiceLocator.initialized) {
      return;
    }

    await serviceContainer.initialize(configuration);
    ServiceLocator.initialized = true;
  }

  /**
   * Ensure services are initialized
   */
  private static ensureInitialized(): void {
    if (!ServiceLocator.initialized) {
      throw new Error(
        'ServiceLocator not initialized. Call ServiceLocator.initialize() first.'
      );
    }
  }

  // Core Services

  /**
   * Get logger service
   */
  static getLogger(): ILogger {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<ILogger>('logger');
  }

  /**
   * Get database instance
   */
  static getDatabase(): Database {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<Database>('database');
  }

  // Database Services

  /**
   * Get color service
   */
  static getColorService(): ColorService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<ColorService>('colorService');
  }

  /**
   * Get product service
   */
  static getProductService(): ProductService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<ProductService>('productService');
  }

  /**
   * Get datasheet service
   */
  static getDatasheetService(): DatasheetService {
    ServiceLocator.ensureInitialized();
    const service = serviceContainer.get<DatasheetService>('datasheetService');
    if (!service) {
      throw new Error(
        'DatasheetService not available - database may not be initialized'
      );
    }
    return service;
  }

  /**
   * Get organization service
   */
  static getOrganizationService(): OrganizationService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<OrganizationService>('organizationService');
  }

  /**
   * Get color import service
   */
  static getColorImportService(): ColorImportService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<ColorImportService>('colorImportService');
  }

  /**
   * Get color library import service
   */
  static getColorLibraryImportService(): ColorLibraryImportService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<ColorLibraryImportService>(
      'colorLibraryImportService'
    );
  }

  /**
   * Get color library query service
   */
  static getColorLibraryQueryService(): ColorLibraryQueryService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<ColorLibraryQueryService>(
      'colorLibraryQueryService'
    );
  }

  // Authentication Services

  /**
   * Get authentication manager
   */
  static getAuthenticationManager(): AuthenticationManager {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<AuthenticationManager>('authenticationManager');
  }

  /**
   * Get session manager
   */
  static getSessionManager(): SessionManager {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<SessionManager>('sessionManager');
  }

  /**
   * Get circuit breaker auth manager (enhanced auth manager)
   */
  static getCircuitBreakerAuthManager(): CircuitBreakerAuthManager {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<CircuitBreakerAuthManager>('circuitBreakerAuthManager');
  }

  /**
   * Get auth error recovery service
   */
  static getAuthErrorRecovery(): AuthErrorRecoveryService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<AuthErrorRecoveryService>('authErrorRecovery');
  }

  /**
   * Get auth notification service
   */
  static getAuthNotification(): AuthNotificationService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<AuthNotificationService>('authNotification');
  }

  /**
   * Get OAuth service
   */
  static getOAuthService(): OAuthService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<OAuthService>('oauthService');
  }

  // Email Services

  /**
   * Get Zoho token manager
   */
  static getZohoTokenManager(): ZohoTokenManager {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<ZohoTokenManager>('zohoTokenManager');
  }

  /**
   * Get email sender
   */
  static getEmailSender(): EmailSender {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<EmailSender>('emailSender');
  }

  /**
   * Get Zoho email service
   */
  static getZohoEmailService(): ZohoEmailService {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get<ZohoEmailService>('zohoEmailService');
  }

  // Sync Services

  /**
   * Get sync system (unified sync manager)
   */
  static getSyncSystem() {
    ServiceLocator.ensureInitialized();
    return serviceContainer.get('unifiedSyncManager');
  }

  // Utility Methods

  /**
   * Check if a service exists
   */
  static hasService(serviceName: ServiceName): boolean {
    if (!ServiceLocator.initialized) {
      return false;
    }
    return serviceContainer.has(serviceName);
  }

  /**
   * Get all available service names
   */
  static getAvailableServices(): ServiceName[] {
    ServiceLocator.ensureInitialized();
    return serviceContainer.getServiceNames();
  }

  /**
   * Configure services
   */
  static configure(configuration: ServiceConfiguration): void {
    ServiceLocator.ensureInitialized();
    serviceContainer.configure(configuration);
  }

  /**
   * Get service health status
   */
  static async getHealthStatus() {
    ServiceLocator.ensureInitialized();
    return await serviceContainer.getHealthStatus();
  }

  /**
   * Cleanup all services
   */
  static async cleanup(): Promise<void> {
    if (ServiceLocator.initialized) {
      await serviceContainer.cleanup();
      ServiceLocator.initialized = false;
    }
  }

  /**
   * Reset initialization state (for testing)
   */
  static reset(): void {
    ServiceLocator.initialized = false;
  }
}

// Convenience exports for common services
export const getLogger = () => ServiceLocator.getLogger();
export const getDatabase = () => ServiceLocator.getDatabase();
export const getColorService = () => ServiceLocator.getColorService();
export const getOAuthService = () => ServiceLocator.getOAuthService();
export const getZohoEmailService = () => ServiceLocator.getZohoEmailService();
export const getSyncSystem = () => ServiceLocator.getSyncSystem();
