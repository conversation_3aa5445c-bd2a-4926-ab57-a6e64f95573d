/**
 * @file AccessibilitySection.tsx
 * @description Accessibility settings section component
 */

import React from 'react';
import { Info } from 'lucide-react';
import { useSettingsStore } from '../../../store/settings.store';

/**
 * Accessibility settings section component
 */
export const AccessibilitySection: React.FC = () => {
  const settings = useSettingsStore();

  return (
    <section>
      <h3
        className='text-lg font-medium mb-4'
        style={{
          fontSize: 'var(--font-size-lg)',
          fontWeight: 'var(--font-weight-medium)',
          color: 'var(--color-ui-foreground-primary)',
        }}
      >
        Accessibility
      </h3>
      <div className='space-y-4'>
        {/* Color Blindness Mode */}
        <div>
          <label
            className='block mb-2'
            style={{
              color: 'var(--color-ui-foreground-primary)',
              fontSize: 'var(--font-size-base)',
              fontWeight: 'var(--font-weight-medium)',
            }}
          >
            Color Blindness Mode
          </label>
          <div
            className='p-4'
            style={{
              backgroundColor: 'var(--color-ui-background-secondary)',
              borderRadius: 'var(--radius-lg)',
            }}
          >
            <div className='space-y-3'>
              <label className='flex items-center justify-between cursor-pointer'>
                <span
                  style={{
                    color: 'var(--color-ui-foreground-primary)',
                    fontSize: 'var(--font-size-base)',
                  }}
                >
                  Enable Color Blindness Mode
                </span>
                <input
                  type='checkbox'
                  className='form-checkbox h-5 w-5 text-brand-primary rounded'
                  checked={settings.colorBlindnessMode}
                  onChange={e =>
                    settings.setColorBlindnessMode(e.target.checked)
                  }
                />
              </label>
              <div className='mt-3'>
                <select
                  className='w-full px-3 py-2'
                  style={{
                    backgroundColor: 'var(--color-ui-background-tertiary)',
                    border: `1px solid var(--color-ui-border-light)`,
                    borderRadius: 'var(--radius-md)',
                    color: 'var(--color-ui-foreground-primary)',
                    fontSize: 'var(--font-size-base)',
                  }}
                  value={settings.colorBlindnessType}
                  onChange={e =>
                    settings.setColorBlindnessType(e.target.value as any)
                  }
                  disabled={!settings.colorBlindnessMode}
                >
                  <option value='normal'>Normal Vision</option>
                  <option value='protanopia'>Protanopia (Red-blind)</option>
                  <option value='deuteranopia'>
                    Deuteranopia (Green-blind)
                  </option>
                  <option value='tritanopia'>Tritanopia (Blue-blind)</option>
                  <option value='protanomaly'>Protanomaly (Red-weak)</option>
                  <option value='deuteranomaly'>
                    Deuteranomaly (Green-weak)
                  </option>
                  <option value='tritanomaly'>Tritanomaly (Blue-weak)</option>
                  <option value='achromatopsia'>
                    Achromatopsia (No color)
                  </option>
                </select>
              </div>
              <div
                className='text-sm mt-2'
                style={{
                  fontSize: 'var(--font-size-sm)',
                  color: 'var(--color-ui-foreground-secondary)',
                }}
              >
                <Info size={14} className='inline mr-1' />
                Simulates how colors appear to people with different types of
                color vision deficiencies.
              </div>
            </div>
          </div>
        </div>

        {/* High Contrast Mode */}
        <div>
          <label
            className='flex items-center justify-between cursor-pointer p-4'
            style={{
              backgroundColor: 'var(--color-ui-background-secondary)',
              borderRadius: 'var(--radius-lg)',
            }}
          >
            <div>
              <span
                className='block'
                style={{
                  color: 'var(--color-ui-foreground-primary)',
                  fontSize: 'var(--font-size-base)',
                  fontWeight: 'var(--font-weight-medium)',
                }}
              >
                High Contrast Mode
              </span>
              <span
                className='text-sm'
                style={{
                  fontSize: 'var(--font-size-sm)',
                  color: 'var(--color-ui-foreground-secondary)',
                }}
              >
                Increases contrast for better visibility
              </span>
            </div>
            <input
              type='checkbox'
              className='form-checkbox h-5 w-5 text-brand-primary rounded'
              checked={settings.highContrastMode}
              onChange={e => settings.setHighContrastMode(e.target.checked)}
            />
          </label>
        </div>

        {/* Reduce Motion */}
        <div>
          <label
            className='flex items-center justify-between cursor-pointer p-4'
            style={{
              backgroundColor: 'var(--color-ui-background-secondary)',
              borderRadius: 'var(--radius-lg)',
            }}
          >
            <div>
              <span
                className='block'
                style={{
                  color: 'var(--color-ui-foreground-primary)',
                  fontSize: 'var(--font-size-base)',
                  fontWeight: 'var(--font-weight-medium)',
                }}
              >
                Reduce Motion
              </span>
              <span
                className='text-sm'
                style={{
                  fontSize: 'var(--font-size-sm)',
                  color: 'var(--color-ui-foreground-secondary)',
                }}
              >
                Minimizes animations and transitions
              </span>
            </div>
            <input
              type='checkbox'
              className='form-checkbox h-5 w-5 text-brand-primary rounded'
              checked={settings.reduceMotion}
              onChange={e => settings.setReduceMotion(e.target.checked)}
            />
          </label>
        </div>
      </div>
    </section>
  );
};

export default AccessibilitySection;
