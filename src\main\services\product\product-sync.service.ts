/**
 * @file product-sync.service.ts
 * @description Service for product synchronization with Supabase
 *
 * Extracted from ProductService to follow single responsibility principle.
 * Handles all Supabase synchronization operations for products including:
 * - Pushing local products to Supabase
 * - Syncing products from Supabase to local database
 * - Managing sync status and error recovery
 * - Handling authentication and RLS policies
 * - Product-specific sync logic (metadata, relationships)
 */

import Database from 'better-sqlite3';
import {
  Product /*, NewProduct, UpdateProduct*/,
} from '../../../shared/types/product.types';
import { IProductRepository } from '../../db/repositories/interfaces/product.repository.interface';
// import { ColorSyncService } from '../color/color-sync.service';

// Types for sync operations
export interface ProductSyncResult {
  success: boolean;
  syncedCount: number;
  errors: string[];
  warnings: string[];
}

export interface ProductSyncOptions {
  batchSize?: number;
  retryAttempts?: number;
  skipValidation?: boolean;
  forceUpdate?: boolean;
  preserveLocalDeletions?: boolean;
}

export interface SupabaseProductData {
  id: string; // Direct UUID - same in local SQLite and Supabase
  organization_id: string;
  user_id: string;
  name: string;
  sku?: string | null;
  metadata?: string | null;
  updated_at: string;
  created_at?: string;
  deleted_at?: string | null;
}

export interface ProductColorSyncResult {
  success: boolean;
  syncedCount: number;
  errors: string[];
}

/**
 * ProductSyncService handles all product synchronization operations with Supabase
 *
 * This service is responsible for:
 * - Maintaining sync state between local SQLite and Supabase
 * - Handling authentication requirements for RLS policies
 * - Managing error recovery and retry logic
 * - Validating data consistency during sync operations
 * - Synchronizing product-color relationships
 * - Managing product metadata and SKU synchronization
 * - Handling product deduplication after sync
 */
export class ProductSyncService {
  constructor(
    private db: Database.Database,
    private productRepository: IProductRepository
  ) {}

  // =============================================================================
  // PUSH OPERATIONS (Local to Supabase)
  // =============================================================================

  /**
   * Push a single product to Supabase
   * @param productId - UUID of product to push
   * @param organizationId - Organization ID for RLS
   * @param userId - User ID for authentication
   * @param isDeleted - Whether this is a deletion sync
   * @returns Promise resolving when push completes
   */
  async pushProductToSupabase(
    productId: string,
    organizationId: string,
    userId?: string,
    isDeleted: boolean = false
  ): Promise<void> {
    try {
      // Ensure authenticated session for RLS policies
      const { getSupabaseClient, ensureAuthenticatedSession } = await import(
        '../../services/supabase-client'
      );
      const { session, error: sessionError } =
        await ensureAuthenticatedSession();

      if (!session || sessionError) {
        console.error(
          '[ProductSyncService] Cannot sync to Supabase - no authenticated session:',
          sessionError
        );
        throw new Error('No authenticated session');
      }

      const supabase = getSupabaseClient();

      if (isDeleted) {
        // Handle deletion sync - mark as deleted in Supabase
        const { error } = await supabase
          .from('products')
          .update({
            deleted_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq('id', productId) // Direct UUID match
          .eq('organization_id', organizationId);

        if (error) {
          console.error(
            `[ProductSyncService] Error deleting product ${productId} in Supabase:`,
            error
          );
          throw error;
        }

        console.log(
          `[ProductSyncService] Successfully marked product ${productId} as deleted in Supabase.`
        );
        return;
      }

      // Get product data from local repository
      const product = this.getProductForSync(productId, organizationId);
      if (!product) {
        console.error(
          `[ProductSyncService] Could not find product ${productId} to push to Supabase.`
        );
        throw new Error(`Product ${productId} not found`);
      }

      // Validate product data for sync
      this.validateProductForSync(product);

      // Prepare data for Supabase
      const supabaseData = this.prepareProductForSupabase(
        product,
        organizationId,
        userId || session.user.id
      );

      // Push to Supabase
      const { error } = await supabase.from('products').upsert(supabaseData);

      if (error) {
        console.error(
          `[ProductSyncService] Error pushing product ${productId} to Supabase:`,
          error
        );
        throw error;
      }

      // Mark as synced in local database
      this.productRepository.markAsSynced(productId);
      console.log(
        `[ProductSyncService] Successfully pushed product ${productId} to Supabase.`
      );
    } catch (error) {
      console.error(
        `[ProductSyncService] Error in pushProductToSupabase:`,
        error
      );
      throw error;
    }
  }

  /**
   * Push multiple products to Supabase in batches
   * @param productIds - Array of product IDs to push
   * @param organizationId - Organization ID for RLS
   * @param options - Sync options
   * @returns Sync result with success count and errors
   */
  async pushProductsToSupabase(
    productIds: string[],
    organizationId: string,
    options: ProductSyncOptions = {}
  ): Promise<ProductSyncResult> {
    const { batchSize = 50, retryAttempts = 3 } = options;
    const errors: string[] = [];
    const warnings: string[] = [];
    let syncedCount = 0;

    // Process in batches to avoid overwhelming Supabase
    for (let i = 0; i < productIds.length; i += batchSize) {
      const batch = productIds.slice(i, i + batchSize);

      for (const productId of batch) {
        let attempts = 0;
        let success = false;

        while (attempts < retryAttempts && !success) {
          try {
            await this.pushProductToSupabase(productId, organizationId);
            syncedCount++;
            success = true;
          } catch (error) {
            attempts++;
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';

            if (attempts === retryAttempts) {
              errors.push(
                `Failed to push product ${productId} after ${retryAttempts} attempts: ${errorMessage}`
              );
            } else {
              // Wait before retry (exponential backoff)
              await this.delay(Math.pow(2, attempts) * 1000);
            }
          }
        }
      }
    }

    return {
      success: errors.length === 0,
      syncedCount,
      errors,
      warnings,
    };
  }

  // =============================================================================
  // PULL OPERATIONS (Supabase to Local)
  // =============================================================================

  /**
   * Sync products from Supabase to local database
   * @param userId - User ID for authentication
   * @param organizationId - Organization ID to sync
   * @param options - Sync options
   * @returns Array of synced Product objects
   */
  async syncProductsFromSupabase(
    userId: string,
    organizationId: string,
    options: ProductSyncOptions = {}
  ): Promise<Product[]> {
    try {
      const { getSupabaseClient, ensureAuthenticatedSession } = await import(
        '../../services/supabase-client'
      );

      console.log(
        `[ProductSyncService] 🔍 Starting product sync for organization: ${organizationId}`
      );

      // CRITICAL: Ensure we have a valid authenticated session before making RLS queries
      const { session, error: sessionError } =
        await ensureAuthenticatedSession();
      if (!session || sessionError) {
        console.error(
          `[ProductSyncService] ❌ No authenticated session available: ${sessionError}`
        );
        console.error(
          `[ProductSyncService] Cannot proceed with sync - RLS policies will block queries`
        );
        return [];
      }

      console.log(
        `[ProductSyncService] ✅ Authenticated session confirmed for user: ${session.user?.email}`
      );

      const supabase = getSupabaseClient();

      // Test basic connectivity with authenticated session
      await this.verifySupabaseConnectivity(supabase);

      // Ensure organization exists locally before syncing products
      await this.ensureOrganizationExists(organizationId, userId);

      // Debug: Check what products we have locally before sync
      await this.debugLocalProducts(organizationId, 'before');

      // Fetch ALL products from Supabase for debugging
      await this.debugSupabaseProducts(supabase, organizationId);

      // Now fetch only non-deleted products for sync
      console.log(
        `[ProductSyncService] 🔍 FETCHING PRODUCTS FROM SUPABASE - FILTERING BY deleted_at IS NULL`
      );
      const { data: products, error } = await supabase
        .from('products')
        .select('*')
        .eq('organization_id', organizationId)
        .is('deleted_at', null); // Only fetch non-deleted products

      console.log(
        `[ProductSyncService] 🔍 SUPABASE RETURNED ${products?.length || 0} PRODUCTS (should exclude deleted)`
      );
      if (products) {
        products.forEach(p => {
          console.log(
            `[ProductSyncService] 🔍 Product: ${p.name} (deleted_at: ${p.deleted_at})`
          );
        });
      }

      if (error) {
        console.error(
          '[ProductSyncService] Failed to fetch products from Supabase:',
          error
        );
        return [];
      }

      if (!products || products.length === 0) {
        console.log(
          '[ProductSyncService] No products found in Supabase for organization:',
          organizationId
        );
        return [];
      }

      console.log(
        `[ProductSyncService] Found ${products.length} products in Supabase, syncing to local database...`
      );

      // Sync each product to local database
      const syncedProducts: Product[] = [];
      for (const supabaseProduct of products) {
        try {
          const product = await this.syncProductToLocal(
            supabaseProduct,
            organizationId,
            userId,
            options
          );
          if (product) {
            syncedProducts.push(product);
          }
        } catch (error) {
          console.error(
            `[ProductSyncService] Error syncing product ${supabaseProduct.name}:`,
            error
          );
        }
      }

      console.log(
        `[ProductSyncService] Successfully synced ${syncedProducts.length} products from Supabase`
      );

      // Handle products that are deleted in Supabase but still active locally
      await this.handleSupabaseDeletedProducts(
        supabase,
        organizationId,
        products,
        options
      );

      // Debug: Check final state of products after sync
      await this.debugLocalProducts(organizationId, 'after');

      // Run deduplication after sync to clean up any duplicates
      await this.runPostSyncDeduplication(organizationId);

      return syncedProducts;
    } catch (error) {
      console.error(
        '[ProductSyncService] Failed to sync products from Supabase:',
        error
      );
      return [];
    }
  }

  /**
   * Sync product-color relationships from Supabase for a specific organization
   * @param organizationId - Organization ID to sync
   * @returns Sync result with count and errors
   */
  async syncProductColorsFromSupabase(
    organizationId: string
  ): Promise<ProductColorSyncResult> {
    try {
      const { getSupabaseClient, ensureAuthenticatedSession } = await import(
        '../../services/supabase-client'
      );

      console.log(
        `[ProductSyncService] 🔍 Starting product-color relationships sync for organization: ${organizationId}`
      );

      // CRITICAL: Ensure we have a valid authenticated session before making RLS queries
      const { session, error: sessionError } =
        await ensureAuthenticatedSession();
      if (!session || sessionError) {
        console.error(
          `[ProductSyncService] ❌ No authenticated session available for product-color sync: ${sessionError}`
        );
        return {
          success: false,
          syncedCount: 0,
          errors: [`Authentication required for sync: ${sessionError}`],
        };
      }

      console.log(
        `[ProductSyncService] ✅ Authenticated session confirmed for product-color sync: ${session.user?.email}`
      );

      const supabase = getSupabaseClient();

      // Step 1: Fetch all required data from Supabase
      console.log(
        `[ProductSyncService] 📡 Fetching products, colors, and relationships from Supabase...`
      );

      const [productsResult, colorsResult, relationshipsResult] =
        await Promise.all([
          supabase
            .from('products')
            .select('id, name')
            .eq('organization_id', organizationId),
          supabase
            .from('colors')
            .select('id, hex, code, display_name')
            .eq('organization_id', organizationId),
          supabase
            .from('product_colors')
            .select('product_id, color_id, display_order')
            .eq('organization_id', organizationId),
        ]);

      // Check for errors
      if (productsResult.error) {
        throw new Error(
          `Failed to fetch products: ${productsResult.error.message}`
        );
      }
      if (colorsResult.error) {
        throw new Error(
          `Failed to fetch colors: ${colorsResult.error.message}`
        );
      }
      if (relationshipsResult.error) {
        throw new Error(
          `Failed to fetch relationships: ${relationshipsResult.error.message}`
        );
      }

      const supabaseProducts = productsResult.data || [];
      const supabaseColors = colorsResult.data || [];
      const productColors = relationshipsResult.data || [];

      console.log(
        `[ProductSyncService] 📊 Supabase data: ${supabaseProducts.length} products, ${supabaseColors.length} colors, ${productColors.length} relationships`
      );

      if (productColors.length === 0) {
        console.log(
          '[ProductSyncService] No product-color relationships found in Supabase'
        );
        return { success: true, syncedCount: 0, errors: [] };
      }

      // Step 2: Prepare validation sets (no UUID mapping needed - direct passthrough)
      console.log(
        `[ProductSyncService] 🗺️ Using direct UUID mapping (Supabase UUIDs = Local UUIDs)`
      );

      // Create validation sets for quick existence checks
      const localProductIds = new Set(
        this.db
          .prepare('SELECT id FROM products WHERE organization_id = ?')
          .all(organizationId)
          .map((r: any) => r.id)
      );
      const localColorIds = new Set(
        this.db
          .prepare('SELECT id FROM colors WHERE organization_id = ?')
          .all(organizationId)
          .map((r: any) => r.id)
      );

      console.log(
        `[ProductSyncService] 📈 Validation sets: ${localProductIds.size} products, ${localColorIds.size} colors available locally`
      );

      // Step 3: Apply the database migration to fix foreign key types (if needed)
      console.log(
        `[ProductSyncService] 🔧 Ensuring product_colors table has correct schema...`
      );
      try {
        // Check if we have the old INTEGER foreign key schema
        const tableInfo = this.db
          .prepare(`PRAGMA table_info(product_colors)`)
          .all() as Array<{ name: string; type: string }>;
        const productIdColumn = tableInfo.find(
          col => col.name === 'product_id'
        );

        if (productIdColumn?.type === 'INTEGER') {
          console.log(
            `[ProductSyncService] 🚨 Detected INTEGER foreign keys, applying schema migration...`
          );

          // Apply the migration
          const migrationSql = `
            PRAGMA foreign_keys = OFF;
            
            CREATE TABLE product_colors_new (
              product_id TEXT NOT NULL,
              color_id TEXT NOT NULL,
              display_order INTEGER NOT NULL DEFAULT 0,
              organization_id TEXT NOT NULL,
              added_at TEXT DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (product_id, color_id),
              FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
              FOREIGN KEY (color_id) REFERENCES colors(id) ON DELETE CASCADE
            );
            
            DROP TABLE product_colors;
            ALTER TABLE product_colors_new RENAME TO product_colors;
            
            CREATE INDEX idx_product_colors_product ON product_colors(product_id);
            CREATE INDEX idx_product_colors_color ON product_colors(color_id);
            CREATE INDEX idx_product_colors_org ON product_colors(organization_id);
            CREATE INDEX idx_product_colors_org_product ON product_colors(organization_id, product_id);
            CREATE INDEX idx_product_colors_org_color ON product_colors(organization_id, color_id);
            
            PRAGMA foreign_keys = ON;
          `;

          this.db.exec(migrationSql);
          console.log(
            `[ProductSyncService] ✅ Schema migration applied successfully`
          );
        } else {
          console.log(
            `[ProductSyncService] ✅ Schema is already correct (TEXT foreign keys)`
          );
        }
      } catch (migrationError) {
        console.error(
          `[ProductSyncService] ❌ Schema migration failed:`,
          migrationError
        );
        return {
          success: false,
          syncedCount: 0,
          errors: [
            `Schema migration failed: ${migrationError instanceof Error ? migrationError.message : String(migrationError)}`,
          ],
        };
      }

      // Step 4: Sync relationships with comprehensive validation
      console.log(
        `[ProductSyncService] 🔄 Syncing ${productColors.length} relationships...`
      );

      const upsertStmt = this.db.prepare(`
        INSERT OR REPLACE INTO product_colors (product_id, color_id, display_order, organization_id)
        VALUES (?, ?, ?, ?)
      `);

      let syncedCount = 0;
      const errors: string[] = [];
      const skippedRelationships: Array<{
        productId: string;
        colorId: string;
        reason: string;
      }> = [];

      for (const [index, relationship] of productColors.entries()) {
        try {
          const supabaseProductId = relationship.product_id;
          const supabaseColorId = relationship.color_id;

          // Direct UUID usage (no mapping needed since Supabase uses UUIDs)
          const localProductId = supabaseProductId;
          const localColorId = supabaseColorId;

          // Validate that both UUIDs exist in local database
          if (!localProductIds.has(localProductId)) {
            skippedRelationships.push({
              productId: supabaseProductId,
              colorId: supabaseColorId,
              reason: `Product UUID ${localProductId} not found in local database`,
            });
            continue;
          }

          if (!localColorIds.has(localColorId)) {
            skippedRelationships.push({
              productId: supabaseProductId,
              colorId: supabaseColorId,
              reason: `Color UUID ${localColorId} not found in local database`,
            });
            continue;
          }

          // Insert the relationship
          upsertStmt.run(
            localProductId,
            localColorId,
            relationship.display_order || 0,
            organizationId
          );

          syncedCount++;

          // Log progress every 50 relationships
          if ((index + 1) % 50 === 0) {
            console.log(
              `[ProductSyncService] 📊 Progress: ${index + 1}/${productColors.length} processed, ${syncedCount} synced`
            );
          }
        } catch (error) {
          const errorMsg = `Failed to sync relationship ${relationship.product_id}->${relationship.color_id}: ${error instanceof Error ? error.message : String(error)}`;
          console.error(`[ProductSyncService] ❌`, errorMsg);
          errors.push(errorMsg);
        }
      }

      // Step 5: Report results
      console.log(
        `[ProductSyncService] ✅ Sync completed: ${syncedCount}/${productColors.length} relationships synced`
      );

      if (skippedRelationships.length > 0) {
        console.warn(
          `[ProductSyncService] ⚠️ Skipped ${skippedRelationships.length} relationships due to mapping issues:`
        );
        // Log first 5 skipped relationships for debugging
        skippedRelationships.slice(0, 5).forEach(skip => {
          console.warn(
            `[ProductSyncService]   - Product ${skip.productId} + Color ${skip.colorId}: ${skip.reason}`
          );
        });

        // Add summary to errors for user visibility
        errors.push(
          `Skipped ${skippedRelationships.length} relationships due to mapping failures`
        );
      }

      if (errors.length > 0) {
        console.warn(
          `[ProductSyncService] ⚠️ Encountered ${errors.length} errors during sync`
        );
      }

      // Step 6: Final verification
      const verificationCount = this.db
        .prepare(
          `
        SELECT COUNT(*) as count FROM product_colors WHERE organization_id = ?
      `
        )
        .get(organizationId) as { count: number };

      console.log(
        `[ProductSyncService] 🔍 Verification: ${verificationCount.count} total relationships in local database`
      );

      const isSuccess = syncedCount > 0 && errors.length === 0;
      return {
        success: isSuccess,
        syncedCount,
        errors: errors.slice(0, 10), // Limit errors to prevent memory issues
      };
    } catch (error) {
      console.error(
        '[ProductSyncService] ❌ Failed to sync product-color relationships from Supabase:',
        error
      );
      return {
        success: false,
        syncedCount: 0,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  // =============================================================================
  // SYNC STATUS MANAGEMENT
  // =============================================================================

  /**
   * Get products that need to be synced to Supabase
   * @returns Array of unsynced Product objects
   */
  getUnsyncedProducts(): Product[] {
    try {
      const rows = this.productRepository.findUnsynced();
      if (!rows || !Array.isArray(rows)) {
        return [];
      }
      return rows.map(row => this.convertRowToProduct(row));
    } catch (error) {
      console.error(
        '[ProductSyncService] Error getting unsynced products:',
        error
      );
      return [];
    }
  }

  /**
   * Mark product as synced in local database
   * @param productId - UUID of product to mark as synced
   */
  markProductAsSynced(productId: string): void {
    this.productRepository.markAsSynced(productId);
  }

  /**
   * Reset sync status for products (mark as unsynced)
   * @param productIds - Array of product IDs to reset
   */
  resetSyncStatus(productIds: string[]): void {
    for (const productId of productIds) {
      // Implementation would depend on repository interface
      // For now, this is a placeholder
      console.log(
        `[ProductSyncService] Reset sync status for product: ${productId}`
      );
    }
  }

  // =============================================================================
  // HELPER METHODS
  // =============================================================================

  /**
   * Get product data formatted for sync operations
   * @param productId - UUID of product
   * @param organizationId - Organization ID
   * @returns Product data or null if not found
   */
  private getProductForSync(
    productId: string,
    organizationId: string
  ): Product | null {
    const row = this.productRepository.findById(productId, organizationId);
    if (!row) {return null;}

    return this.convertRowToProduct(row);
  }

  /**
   * Convert database row to Product object
   * @param row - Database row
   * @returns Product object
   */
  private convertRowToProduct(row: any): Product {
    // Parse metadata from JSON column
    let metadata: any = {};
    if (row.metadata) {
      try {
        metadata =
          typeof row.metadata === 'string'
            ? JSON.parse(row.metadata)
            : row.metadata;
      } catch (error) {
        console.warn(
          '[ProductSyncService] Failed to parse metadata JSON:',
          error
        );
        metadata = {};
      }
    }

    return {
      id: row.id,
      name: row.name,
      description: row.description || metadata.description || undefined,
      organizationId: row.organization_id,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      createdBy: row.created_by || metadata.createdBy || undefined,
      updatedBy: metadata.updatedBy || undefined,
    };
  }

  /**
   * Validate product data for sync operations
   * @param product - Product to validate
   * @throws Error if validation fails
   */
  private validateProductForSync(product: Product): void {
    if (!product.id) {
      throw new Error('Product ID is required for sync');
    }
    if (!product.name || product.name.trim() === '') {
      throw new Error('Product name is required for sync');
    }
    if (!product.organizationId) {
      throw new Error('Organization ID is required for sync');
    }
  }

  /**
   * Prepare product data for Supabase insertion/update
   * @param product - Product to prepare
   * @param organizationId - Organization ID
   * @param userId - User ID
   * @returns Formatted data for Supabase
   */
  private prepareProductForSupabase(
    product: Product,
    organizationId: string,
    userId: string
  ): SupabaseProductData {
    return {
      id: product.id, // Direct UUID - same in local and Supabase
      organization_id: organizationId,
      user_id: userId,
      name: product.name,
      sku: null, // Product type doesn't have sku property
      metadata: product.metadata ? JSON.stringify(product.metadata) : null,
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Sync individual product from Supabase to local database
   * @param supabaseProduct - Product data from Supabase
   * @param organizationId - Organization ID
   * @param userId - User ID
   * @param options - Sync options
   * @returns Synced Product or null
   */
  private async syncProductToLocal(
    supabaseProduct: any,
    organizationId: string,
    userId: string,
    options: ProductSyncOptions
  ): Promise<Product | null> {
    try {
      // With UUID primary keys, find the product by its ID directly
      let existing = this.db
        .prepare(
          `
        SELECT id, deleted_at, is_active FROM products WHERE id = ? AND organization_id = ?
      `
        )
        .get(supabaseProduct.id, organizationId) as
        | { id: string; deleted_at: string | null; is_active: number }
        | undefined;

      // If not found by ID, try to find by name as a fallback
      if (!existing) {
        existing = this.db
          .prepare(
            `
          SELECT id, deleted_at, is_active FROM products WHERE name = ? AND organization_id = ?
        `
          )
          .get(supabaseProduct.name, organizationId) as
          | { id: string; deleted_at: string | null; is_active: number }
          | undefined;
      }

      // CRITICAL FIX: Preserve local deletions
      if (existing && options.preserveLocalDeletions !== false) {
        // Check if product is deleted locally but not in Supabase
        if (
          (existing.deleted_at !== null || existing.is_active === 0) &&
          supabaseProduct.deleted_at === null
        ) {
          console.log(
            `[ProductSyncService] ⚠️ PRESERVING LOCAL DELETION: Product ${supabaseProduct.name} is deleted locally but active in Supabase`
          );
          console.log(
            `[ProductSyncService] Local state: deleted_at=${existing.deleted_at}, is_active=${existing.is_active}`
          );
          console.log(
            `[ProductSyncService] Supabase state: deleted_at=${supabaseProduct.deleted_at}`
          );
          // Skip updating this product to preserve local deletion
          return null;
        }
      }

      if (existing) {
        // Update existing product
        const success = this.productRepository.update(
          supabaseProduct.id,
          this.prepareProductForLocal(supabaseProduct),
          organizationId,
          userId,
          true // fromSync
        );

        if (success) {
          console.log(
            `[ProductSyncService] Updated local product: ${supabaseProduct.name}`
          );
        }
      } else {
        // Insert new product
        const productData = this.prepareProductForLocal(supabaseProduct);
        this.productRepository.insert(
          productData,
          organizationId,
          userId,
          true
        ); // fromSync
        console.log(
          `[ProductSyncService] Inserted new product: ${supabaseProduct.name}`
        );
      }

      // Convert to Product format for return
      return this.convertSupabaseToProduct(supabaseProduct, organizationId);
    } catch (error) {
      console.error(
        `[ProductSyncService] Error syncing product ${supabaseProduct.name}:`,
        error
      );
      return null;
    }
  }

  /**
   * Prepare Supabase product data for local database insertion
   * @param supabaseProduct - Product data from Supabase
   * @returns Formatted data for local database
   */
  private prepareProductForLocal(supabaseProduct: any): any {
    return {
      id: supabaseProduct.id, // CRITICAL: Preserve Supabase UUID for product_colors relationship mapping
      name: supabaseProduct.name,
      description: supabaseProduct.description,
      organizationId: supabaseProduct.organization_id,
    };
  }

  /**
   * Convert Supabase product data to Product
   * @param supabaseProduct - Product data from Supabase
   * @param organizationId - Organization ID
   * @returns Product object
   */
  private convertSupabaseToProduct(
    supabaseProduct: any,
    organizationId: string
  ): Product {
    return {
      id: supabaseProduct.id,
      name: supabaseProduct.name,
      description: supabaseProduct.description,
      organizationId,
      createdAt: supabaseProduct.created_at,
      updatedAt: supabaseProduct.updated_at,
      createdBy: undefined,
      updatedBy: undefined,
    };
  }

  /**
   * Verify Supabase connectivity with authenticated session
   * @param supabase - Supabase client
   */
  private async verifySupabaseConnectivity(supabase: any): Promise<void> {
    console.log(
      `[ProductSyncService] 🔍 Testing basic Supabase connection with authenticated session...`
    );

    const { count: totalProducts, error: countError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error(`[ProductSyncService] ❌ Basic query failed:`, countError);
      throw countError;
    }

    console.log(
      `[ProductSyncService] ✅ Total products accessible: ${totalProducts}`
    );
  }

  /**
   * Ensure organization exists locally before syncing products
   * @param organizationId - Organization ID
   * @param userId - User ID
   */
  private async ensureOrganizationExists(
    organizationId: string,
    userId: string
  ): Promise<void> {
    // With UUID primary keys, check organization by ID directly
    const orgStmt = this.db.prepare(`
      SELECT id FROM organizations WHERE id = ?
    `);
    let localOrg = orgStmt.get(organizationId) as { id: string } | undefined;

    // SYNC GUARD: If organization doesn't exist locally, sync it first
    if (!localOrg) {
      console.log(
        `[ProductSyncService] Organization ${organizationId} not found locally - syncing organizations first`
      );
      try {
        const { OrganizationService } = await import(
          '../../db/services/organization.service'
        );
        const orgService = new OrganizationService(this.db);
        await orgService.syncOrganizationsFromSupabase(userId);

        // Try to get the organization again after sync
        localOrg = orgStmt.get(organizationId) as { id: string } | undefined;

        if (!localOrg) {
          throw new Error(
            `Organization ${organizationId} still not found after sync`
          );
        }

        console.log(
          `[ProductSyncService] ✅ Organization synced successfully, proceeding with product sync`
        );
      } catch (syncError) {
        console.error(
          '[ProductSyncService] Failed to sync organization:',
          syncError
        );
        throw syncError;
      }
    }
  }

  /**
   * Debug local products state
   * @param organizationId - Organization ID
   * @param phase - Debug phase (before/after)
   */
  private async debugLocalProducts(
    organizationId: string,
    phase: string
  ): Promise<void> {
    console.log(
      `[ProductSyncService] 🔍 DEBUG: Checking local products ${phase} sync...`
    );
    const localProducts = this.db
      .prepare(
        `
      SELECT id, name, is_active, deleted_at 
      FROM products 
      WHERE organization_id = ?
      ORDER BY name
    `
      )
      .all(organizationId) as Array<{
      id: string;
      name: string;
      is_active: number;
      deleted_at: string | null;
    }>;

    console.log(
      `[ProductSyncService] 🔍 DEBUG: Total local products ${phase} sync: ${localProducts.length}`
    );
    const deleted = localProducts.filter(
      p => p.deleted_at !== null || p.is_active === 0
    );
    console.log(
      `[ProductSyncService] 🔍 DEBUG: Deleted products ${phase} sync: ${deleted.length}`,
      deleted.map(p => ({
        id: p.id,
        name: p.name,
        is_active: p.is_active,
        deleted_at: p.deleted_at,
      }))
    );
  }

  /**
   * Debug Supabase products state
   * @param supabase - Supabase client
   * @param organizationId - Organization ID
   */
  private async debugSupabaseProducts(
    supabase: any,
    organizationId: string
  ): Promise<void> {
    // Fetch ALL products from Supabase (including deleted ones for debugging)
    console.log(
      `[ProductSyncService] 🔍 DEBUG: Fetching ALL products from Supabase (including deleted)...`
    );
    const { data: allSupabaseProducts, error: allError } = await supabase
      .from('products')
      .select('*')
      .eq('organization_id', organizationId);

    if (!allError && allSupabaseProducts) {
      console.log(
        `[ProductSyncService] 🔍 DEBUG: Total products in Supabase: ${allSupabaseProducts.length}`
      );
      const deletedInSupabase = allSupabaseProducts.filter(
        (p: any) => p.deleted_at !== null
      );
      console.log(
        `[ProductSyncService] 🔍 DEBUG: Deleted products in Supabase: ${deletedInSupabase.length}`,
        deletedInSupabase.map((p: any) => ({
          id: p.id,
          name: p.name,
          deleted_at: p.deleted_at,
        }))
      );
    }
  }

  /**
   * Handle products that are deleted in Supabase but still active locally
   * @param supabase - Supabase client
   * @param organizationId - Organization ID
   * @param products - Active products from Supabase
   * @param options - Sync options
   */
  private async handleSupabaseDeletedProducts(
    supabase: any,
    organizationId: string,
    products: any[],
    _options: ProductSyncOptions
  ): Promise<void> {
    console.log(
      `[ProductSyncService] 🔍 Checking for products deleted in Supabase but active locally...`
    );

    // Get all local active products
    const localActiveProducts = this.db
      .prepare(
        `
      SELECT id, name 
      FROM products 
      WHERE organization_id = ? AND deleted_at IS NULL AND is_active = 1
    `
      )
      .all(organizationId) as Array<{ id: string; name: string }>;

    // Find products that exist locally but weren't in the Supabase results
    const supabaseProductIds = new Set(products.map(p => p.id));
    const localOnlyProducts = localActiveProducts.filter(
      p => !supabaseProductIds.has(p.id)
    );

    if (localOnlyProducts.length > 0) {
      console.log(
        `[ProductSyncService] ⚠️ Found ${localOnlyProducts.length} products that are active locally but not in Supabase (may be deleted):`,
        localOnlyProducts.map(p => ({ id: p.id, name: p.name }))
      );

      // Check if these products are actually deleted in Supabase
      const { data: deletedCheckProducts } = await supabase
        .from('products')
        .select('id, name, deleted_at')
        .eq('organization_id', organizationId)
        .in(
          'id',
          localOnlyProducts.map(p => p.id)
        );

      if (deletedCheckProducts) {
        for (const deletedProduct of deletedCheckProducts) {
          if (deletedProduct.deleted_at) {
            console.log(
              `[ProductSyncService] 🗑️ Marking product as deleted locally: ${deletedProduct.name} (deleted in Supabase at ${deletedProduct.deleted_at})`
            );

            // Update local product to reflect Supabase deletion
            this.productRepository.softDelete(
              deletedProduct.id,
              organizationId
            );
          }
        }
      }
    }
  }

  /**
   * Run post-sync deduplication
   * @param organizationId - Organization ID
   */
  private async runPostSyncDeduplication(
    organizationId: string
  ): Promise<void> {
    console.log(`[ProductSyncService] 🔧 Running post-sync deduplication...`);
    const deduplicationResult =
      this.productRepository.deduplicateProducts(organizationId);

    if (deduplicationResult.success) {
      console.log(
        `[ProductSyncService] ✅ Deduplication successful: ${deduplicationResult.deduplicatedCount} duplicates removed`
      );
      if (deduplicationResult.errors.length > 0) {
        console.warn(
          `[ProductSyncService] ⚠️ Deduplication warnings:`,
          deduplicationResult.errors
        );
      }
    } else {
      console.error(
        `[ProductSyncService] ❌ Deduplication failed:`,
        deduplicationResult.errors
      );
    }
  }

  /**
   * Utility method for delays in retry logic
   * @param ms - Milliseconds to delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // =============================================================================
  // PUBLIC UTILITY METHODS
  // =============================================================================

  /**
   * Get sync service information and capabilities
   * @returns Service metadata
   */
  getServiceInfo(): { name: string; version: string; capabilities: string[] } {
    return {
      name: 'ProductSyncService',
      version: '1.0.0',
      capabilities: [
        'Direct UUID sync operations',
        'Push products to Supabase',
        'Sync products from Supabase',
        'Sync product-color relationships',
        'Batch sync operations',
        'Retry logic with exponential backoff',
        'Authentication handling',
        'RLS policy compliance',
        'Sync status management',
        'Data validation during sync',
        'Organization dependency management',
        'Product deduplication',
        'Local deletion preservation',
        'SKU and metadata synchronization',
        'Error recovery and logging',
      ],
    };
  }
}
