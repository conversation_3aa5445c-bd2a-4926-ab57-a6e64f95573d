/**
 * @file WebLinkDialog.tsx
 * @description Dialog for adding web links from various cloud services (SharePoint, Google Drive, Dropbox, OneDrive, etc.) to datasheets
 */

import React, { useState } from 'react';
import { Link, AlertCircle } from 'lucide-react';
import { useTokens } from '../hooks/useTokens';
import Modal from './ui/Modal';

interface WebLinkDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (url: string, displayName: string) => void;
  initialName?: string;
}

const WebLinkDialog: React.FC<WebLinkDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialName = 'Product Specification'
}) => {
  const tokens = useTokens();
  const [url, setUrl] = useState('');
  const [displayName, setDisplayName] = useState(initialName);
  const [urlError, setUrlError] = useState('');
  const [nameError, setNameError] = useState('');

  // Reset form when dialog opens
  React.useEffect(() => {
    if (isOpen) {
      setUrl('');
      setDisplayName(initialName);
      setUrlError('');
      setNameError('');
    }
  }, [isOpen, initialName]);

  const validateForm = (): boolean => {
    let isValid = true;

    // Validate URL - minimal validation
    if (!url.trim()) {
      setUrlError('Please enter a URL');
      isValid = false;
    } else {
      // Accept any non-empty URL
      setUrlError('');
    }

    // Validate display name
    if (!displayName.trim()) {
      // Use URL as display name if not provided
      setDisplayName(url.trim());
      setNameError('');
    } else {
      setNameError('');
    }

    return isValid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        // Close the dialog first to prevent UI issues
        onClose();

        // Submit the values after a short delay
        setTimeout(() => {
          try {
            // Use URL as display name if not provided
            const trimmedUrl = url.trim();
            const trimmedName = displayName.trim() || trimmedUrl;

            console.log('Submitting web link:', { url: trimmedUrl, displayName: trimmedName });
            onSubmit(trimmedUrl, trimmedName);
          } catch (submitError) {
            console.error('Error submitting web link:', submitError);
          }
        }, 100);
      } catch (error) {
        console.error('Error handling web link submission:', error);
        onClose(); // Ensure dialog closes even if there's an error
      }
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add Document Link"
    >
      <div className="p-5">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-md p-3 mb-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" />
            <div className="text-sm text-blue-700 dark:text-blue-300">
              <p className="font-medium mb-1">How to get a shareable link:</p>
              <ol className="list-decimal ml-5 space-y-1">
                <li>Navigate to your file in any cloud service (SharePoint, Google Drive, Dropbox, OneDrive, etc.)</li>
                <li>Right-click on the file and select "Share", "Copy link", or "Get link"</li>
                <li>Copy the shareable link and paste it below</li>
              </ol>
              <p className="text-xs mt-2 opacity-90">
                <strong>Supported services:</strong> SharePoint, Google Drive, Dropbox, OneDrive, Box, and most cloud storage platforms
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="url" className="block text-sm font-medium text-ui-foreground-primary dark:text-white mb-1">
              Document URL
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Link className="h-4 w-4 text-ui-muted dark:text-gray-400" />
              </div>
              <input
                type="text"
                id="url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://drive.google.com/... or https://company.sharepoint.com/... or https://dropbox.com/..."
                className={`pl-10 w-full px-3 py-2 border ${
                  urlError ? 'border-red-500 dark:border-red-500' : 'border-ui-border-light dark:border-zinc-700'
                } rounded-md bg-ui-background-primary dark:bg-zinc-800 text-ui-foreground-primary dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-primary dark:focus:ring-brand-primary-dark`}
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              />
            </div>
            {urlError && (
              <p className="mt-1 text-xs text-red-500">{urlError}</p>
            )}
          </div>

          <div className="mb-5">
            <label htmlFor="displayName" className="block text-sm font-medium text-ui-foreground-primary dark:text-white mb-1">
              Display Name
            </label>
            <input
              type="text"
              id="displayName"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              placeholder="Product Specification"
              className={`w-full px-3 py-2 border ${
                nameError ? 'border-red-500 dark:border-red-500' : 'border-ui-border-light dark:border-zinc-700'
              } rounded-md bg-ui-background-primary dark:bg-zinc-800 text-ui-foreground-primary dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-primary dark:focus:ring-brand-primary-dark`}
              style={{
                transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
              }}
            />
            {nameError && (
              <p className="mt-1 text-xs text-red-500">{nameError}</p>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-3.5 py-1.5 text-xs font-medium text-ui-foreground-primary dark:text-white bg-ui-background-tertiary dark:bg-zinc-700 rounded-md hover:bg-ui-background-tertiary-hover dark:hover:bg-zinc-600 transition-colors"
              style={{
                transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-3.5 py-1.5 text-xs font-medium bg-brand-primary text-ui-foreground-inverse rounded-md hover:bg-brand-primary-dark transition-colors"
              style={{
                transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
              }}
            >
              Add Link
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default WebLinkDialog;
