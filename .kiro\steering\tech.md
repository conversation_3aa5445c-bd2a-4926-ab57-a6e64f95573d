# ChromaSync Technology Stack

## Core Technologies
- **Frontend**: React 18.2.0 + TypeScript 5.8.3
- **Desktop Framework**: Electron 35.5.1
- **Database**: SQLite (better-sqlite3 11.10.0) + Supabase (PostgreSQL)
- **State Management**: Zustand 4.4.7
- **Styling**: Tailwind CSS with CSS custom properties
- **Build System**: Vite 4.5.9 + electron-vite 3.0.0
- **Testing**: Vitest 3.0.8 + Testing Library

## Key Libraries
- **Color Processing**: colord 2.9.3
- **UI Components**: Material-UI 6.4.8 + Lucide React icons
- **Data Visualization**: Recharts 2.15.3 + @visx/visx 3.12.0
- **Form Handling**: React Hook Form 7.59.0 + Zod 3.25.67
- **File Processing**: PapaParse 5.5.3, jsPDF 3.0.1
- **Authentication**: Supa<PERSON> Auth + Google OAuth
- **Monitoring**: Sentry (Electron + Node)

## Build System

### Development Commands
```bash
# Start development with type checking
npm run dev

# Quick development start (skip type check)
npm run dev:watch

# Type checking only
npm run typecheck
npm run typecheck:watch
```

### Build Commands
```bash
# Production build with type checking
npm run build

# Obfuscated production build
npm run build:obfuscated

# Platform-specific packaging
npm run package:win
npm run package:mac
npm run package:linux
```

### Testing Commands
```bash
# Run all tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage

# Specific test suites
npm run test:accessibility
npm run test:performance
```

### Code Quality
```bash
# Linting
npm run lint
npm run lint:fix

# Formatting
npm run format
npm run format:check
```

## TypeScript Configuration
- **Target**: ES2020 with strict mode enabled
- **Module**: ESNext with bundler resolution
- **Path Mapping**: Extensive alias support (@main, @renderer, @shared, etc.)
- **Type Safety**: No `any` types, strict null checks, no unchecked indexed access
- **Build Optimization**: Incremental compilation with .tsbuildinfo

## Electron Architecture
- **Main Process**: Node.js backend with SQLite database
- **Renderer Process**: React frontend with Vite HMR
- **Preload Scripts**: Secure IPC bridge with context isolation
- **Security**: CSP, sandboxing, input validation, prepared statements

## Database Strategy
- **Local**: SQLite with better-sqlite3 for offline-first operation
- **Cloud**: Supabase PostgreSQL for real-time sync
- **Multi-tenant**: All data scoped by `organization_id`
- **Performance**: Optimized indexes, connection pooling, query builder