/**
 * @file zoho-token-manager.ts
 * @description Manages Zoho OAuth tokens, regional detection, and token lifecycle
 */

import axios from 'axios';
import { app } from 'electron';
import path from 'path';
import fs from 'fs/promises';
import { createSafeStore } from '../../utils/store-util';
import { secureConfig } from '../../utils/secure-config-loader';
import {
  LoggerFactory,
  logPerformance,
  logErrors,
  ILogger,
} from '../../utils/logger.service';

export interface ZohoTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface ZohoRegionConfig {
  auth: string;
  api: string;
}

export interface TokenManagerConfig {
  refreshCooldownMs?: number;
  maxRetryAttempts?: number;
  baseRetryDelayMs?: number;
  maxRetryDelayMs?: number;
  circuitBreakerFailures?: number;
  circuitBreakerTimeoutMs?: number;
}

export interface TokenManagerStatus {
  hasValidToken: boolean;
  tokenExpiresAt: number;
  region: string;
  circuitBreakerState: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  failureCount: number;
  lastRefreshAttempt: number;
}

/**
 * Zoho Token Manager - Handles OAuth tokens, region detection, and token lifecycle
 */
export class ZohoTokenManager {
  private readonly logger: ILogger;
  private readonly store = createSafeStore<Record<string, any>>({
    name: 'zoho-token-manager',
  });
  private tokens: ZohoTokens;
  private tokenFile: string;

  // Circuit breaker and retry state
  private lastRefreshAttempt: number = 0;
  private retryAttempts: number = 0;
  private circuitBreakerState: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failureCount: number = 0;
  private lastFailureTime: number = 0;

  // Configuration
  private readonly DEFAULT_CONFIG: Required<TokenManagerConfig> = {
    refreshCooldownMs: 60000, // 1 minute cooldown
    maxRetryAttempts: 5,
    baseRetryDelayMs: 1000,
    maxRetryDelayMs: 300000, // 5 minutes
    circuitBreakerFailures: 3,
    circuitBreakerTimeoutMs: 60000, // 1 minute
  };

  // Storage keys
  private readonly STORAGE_KEYS = {
    CONFIG: 'zoho.tokenManager.config',
    REGION_CACHE: 'zoho.tokenManager.regionCache',
    CIRCUIT_STATE: 'zoho.tokenManager.circuitState',
  } as const;

  constructor(logger?: ILogger) {
    this.logger =
      logger || LoggerFactory.getInstance().createLogger('ZohoTokenManager');
    this.tokenFile = path.join(app.getPath('userData'), 'zoho-tokens.json');

    this.tokens = {
      accessToken: '',
      refreshToken:
        (secureConfig.getConfigValue(
          'ZOHO_REFRESH_TOKEN',
          'zoho-token-manager'
        ) as string) || '',
      expiresAt: 0,
    };

    this.loadPersistedState();
  }

  /**
   * Initialize token manager and load existing tokens
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('ZohoTokenManager'),
    'initialize'
  )
  @logErrors(LoggerFactory.getInstance().createLogger('ZohoTokenManager'))
  async initialize(): Promise<void> {
    this.logger.info('Initializing Zoho Token Manager', {
      operation: 'initialize',
    });

    try {
      // Load tokens from file if exists
      await this.loadTokensFromFile();

      // Validate configuration
      this.validateConfiguration();

      // Detect and cache region if not already cached
      await this.ensureRegionCached();

      this.logger.info('Token manager initialized successfully', {
        hasTokens: !!this.tokens.accessToken,
        tokenExpiry:
          this.tokens.expiresAt > 0
            ? new Date(this.tokens.expiresAt).toISOString()
            : 'none',
        region: await this.getZohoRegion(),
        operation: 'initialize',
      });
    } catch (error) {
      this.logger.error('Token manager initialization failed', error as Error, {
        operation: 'initialize',
      });
      throw error;
    }
  }

  /**
   * Get valid access token, refreshing if necessary
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('ZohoTokenManager'),
    'getValidToken'
  )
  @logErrors(LoggerFactory.getInstance().createLogger('ZohoTokenManager'))
  async getValidToken(): Promise<string> {
    // Check if current token is still valid
    if (this.tokens.accessToken && Date.now() < this.tokens.expiresAt) {
      return this.tokens.accessToken;
    }

    // Refresh token if needed
    await this.refreshAccessToken();
    return this.tokens.accessToken;
  }

  /**
   * Get Zoho API configuration for current region
   */
  async getRegionConfig(): Promise<ZohoRegionConfig> {
    const region = await this.getZohoRegion();
    return this.getZohoDomain(region);
  }

  /**
   * Get current token manager status
   */
  getStatus(): TokenManagerStatus {
    return {
      hasValidToken:
        !!this.tokens.accessToken && Date.now() < this.tokens.expiresAt,
      tokenExpiresAt: this.tokens.expiresAt,
      region: this.getCachedRegion() || 'US',
      circuitBreakerState: this.circuitBreakerState,
      failureCount: this.failureCount,
      lastRefreshAttempt: this.lastRefreshAttempt,
    };
  }

  /**
   * Configure token manager settings
   */
  configure(config: TokenManagerConfig): void {
    const currentConfig = this.getConfiguration();
    const newConfig = { ...currentConfig, ...config };

    this.store.set(this.STORAGE_KEYS.CONFIG, newConfig);

    this.logger.info('Token manager configuration updated', {
      config: newConfig,
      operation: 'configure',
    });
  }

  /**
   * Get current configuration
   */
  getConfiguration(): Required<TokenManagerConfig> {
    return (
      (this.store.get(
        this.STORAGE_KEYS.CONFIG
      ) as Required<TokenManagerConfig>) || this.DEFAULT_CONFIG
    );
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.logger.debug('Cleaning up token manager');
    this.persistState();
  }

  // Private methods

  /**
   * Load tokens from file
   */
  private async loadTokensFromFile(): Promise<void> {
    try {
      const tokenData = await fs.readFile(this.tokenFile, 'utf-8');
      const savedTokens = JSON.parse(tokenData);
      this.tokens = { ...this.tokens, ...savedTokens };

      // Handle invalid expiration time
      if (!this.tokens.expiresAt || this.tokens.expiresAt === null) {
        this.tokens.expiresAt = 0; // Force refresh
      }

      this.logger.debug('Tokens loaded from file', {
        hasAccessToken: !!this.tokens.accessToken,
        expiresAt:
          this.tokens.expiresAt > 0
            ? new Date(this.tokens.expiresAt).toISOString()
            : 'invalid',
      });
    } catch (error) {
      this.logger.debug(
        'No saved tokens found, using environment refresh token'
      );
    }
  }

  /**
   * Save tokens to file
   */
  private async saveTokensToFile(): Promise<void> {
    try {
      await fs.writeFile(this.tokenFile, JSON.stringify(this.tokens, null, 2));
      this.logger.debug('Tokens saved to file');
    } catch (error) {
      this.logger.error('Failed to save tokens to file', error as Error);
      throw error;
    }
  }

  /**
   * Validate required configuration
   */
  private validateConfiguration(): void {
    const requiredConfig = [
      'ZOHO_CLIENT_ID',
      'ZOHO_CLIENT_SECRET',
      'ZOHO_ACCOUNT_ID',
    ];
    const missingConfig = requiredConfig.filter(
      key => !secureConfig.getConfigValue(key as any, 'zoho-token-manager')
    );

    if (missingConfig.length > 0 || !this.tokens.refreshToken) {
      this.logger.error('Missing Zoho configuration', undefined, {
        missing: [
          ...missingConfig,
          ...(this.tokens.refreshToken ? [] : ['ZOHO_REFRESH_TOKEN']),
        ],
      });
      throw new Error('Zoho Token Manager not configured');
    }

    // Validate region configuration
    const zohoRegion = secureConfig.getConfigValue(
      'ZOHO_REGION',
      'zoho-token-manager'
    ) as string;
    if (zohoRegion && !this.isValidZohoRegion(zohoRegion)) {
      this.logger.warn('Invalid ZOHO_REGION configuration', {
        region: zohoRegion,
        validRegions: ['EU', 'US'],
      });
    }
  }

  /**
   * Ensure region is detected and cached
   */
  private async ensureRegionCached(): Promise<void> {
    const cachedRegion = this.getCachedRegion();
    if (!cachedRegion) {
      const detectedRegion = await this.detectZohoRegion();
      this.setCachedRegion(detectedRegion);

      this.logger.info('Region auto-detected and cached', {
        region: detectedRegion,
        suggestion: `Consider setting ZOHO_REGION=${detectedRegion}`,
      });
    }
  }

  /**
   * Refresh access token with retry logic and circuit breaker
   */
  @logPerformance(
    LoggerFactory.getInstance().createLogger('ZohoTokenManager'),
    'refreshToken'
  )
  private async refreshAccessToken(): Promise<void> {
    const config = this.getConfiguration();

    // Check circuit breaker
    if (!this.checkCircuitBreaker()) {
      throw new Error(
        'Token refresh circuit breaker is OPEN - too many recent failures'
      );
    }

    // Check cooldown period
    const timeSinceLastAttempt = Date.now() - this.lastRefreshAttempt;
    if (timeSinceLastAttempt < config.refreshCooldownMs) {
      const waitTime = config.refreshCooldownMs - timeSinceLastAttempt;
      this.logger.warn('Token refresh rate limit cooldown', {
        waitTimeSeconds: Math.ceil(waitTime / 1000),
      });
      throw new Error(
        `Rate limit: Please wait ${Math.ceil(waitTime / 1000)} seconds before retrying`
      );
    }

    this.logger.info('Refreshing access token', {
      attempt: this.retryAttempts + 1,
      maxAttempts: config.maxRetryAttempts,
      operation: 'refreshAccessToken',
    });

    this.lastRefreshAttempt = Date.now();

    try {
      const region = await this.getZohoRegion();
      const domains = this.getZohoDomain(region);

      const response = await axios.post(
        `https://${domains.auth}/oauth/v2/token`,
        null,
        {
          params: {
            refresh_token: this.tokens.refreshToken,
            client_id: secureConfig.getConfigValue(
              'ZOHO_CLIENT_ID',
              'zoho-token-manager'
            ),
            client_secret: secureConfig.getConfigValue(
              'ZOHO_CLIENT_SECRET',
              'zoho-token-manager'
            ),
            grant_type: 'refresh_token',
          },
          timeout: 10000,
        }
      );

      // Update tokens
      this.tokens.accessToken = response.data.access_token;
      const expiresIn = response.data.expires_in || 3600;
      // Refresh 5 minutes before expiry
      this.tokens.expiresAt = Date.now() + (expiresIn - 300) * 1000;

      // Save tokens
      await this.saveTokensToFile();

      this.logger.info('Access token refreshed successfully', {
        expiresAt: new Date(this.tokens.expiresAt).toISOString(),
        operation: 'refreshAccessToken',
      });

      // Reset retry state and record success
      this.resetRetryAttempts();
      this.recordSuccess();
    } catch (error: any) {
      this.logger.error('Token refresh failed', error, {
        attempt: this.retryAttempts + 1,
        operation: 'refreshAccessToken',
      });

      // Handle retryable errors
      if (await this.shouldRetryTokenRefresh(error)) {
        const delay = this.calculateBackoffDelay();
        this.logger.info('Retrying token refresh', {
          delaySeconds: Math.ceil(delay / 1000),
          attempt: this.retryAttempts + 1,
        });

        await this.sleep(delay);
        return this.refreshAccessToken();
      }

      // Reset retry count and record failure
      this.resetRetryAttempts();
      this.recordFailure();

      throw new Error('Failed to refresh Zoho access token');
    }
  }

  /**
   * Check if token refresh should be retried
   */
  private async shouldRetryTokenRefresh(error: any): Promise<boolean> {
    if (!this.incrementRetryAttempts()) {
      return false; // Max retries reached
    }

    // Handle rate limiting
    if (
      error.response?.data?.error === 'Access Denied' &&
      error.response?.data?.error_description?.includes('too many requests')
    ) {
      return true;
    }

    // Handle network errors and server errors
    if (
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNRESET' ||
      error.code === 'ETIMEDOUT' ||
      (error.response?.status >= 500 && error.response?.status < 600)
    ) {
      return true;
    }

    // Check for account migration
    if (error.response?.status === 400) {
      const currentRegion = await this.getZohoRegion();
      const migrationDetected =
        await this.detectAccountMigration(currentRegion);

      if (migrationDetected) {
        this.logger.info(
          'Account migration detected, retrying with new region'
        );
        return true;
      }
    }

    return false;
  }

  /**
   * Auto-detect Zoho region
   */
  private async detectZohoRegion(): Promise<'EU' | 'US'> {
    this.logger.debug('Auto-detecting Zoho region');

    const testRegions = [
      { region: 'EU', domain: 'accounts.zoho.eu' },
      { region: 'US', domain: 'accounts.zoho.com' },
    ];

    for (const test of testRegions) {
      try {
        const response = await axios.get(
          `https://${test.domain}/oauth/userinfo`,
          {
            timeout: 3000,
            validateStatus: status => status === 401 || status === 200,
          }
        );

        if (response.status === 401) {
          this.logger.debug('Region detected', {
            region: test.region,
            domain: test.domain,
          });
          return test.region as 'EU' | 'US';
        }
      } catch (error: any) {
        if (error.response?.status === 401) {
          this.logger.debug('Region detected', {
            region: test.region,
            domain: test.domain,
          });
          return test.region as 'EU' | 'US';
        }

        this.logger.debug('Region test failed', {
          region: test.region,
          error: error.code || error.message,
        });
      }
    }

    this.logger.warn('Could not auto-detect region, falling back to US');
    return 'US';
  }

  /**
   * Detect account migration between regions
   */
  private async detectAccountMigration(
    currentRegion: string
  ): Promise<boolean> {
    this.logger.debug('Checking for account migration', { currentRegion });

    try {
      // Test alternate region
      const alternateRegion = currentRegion === 'EU' ? 'US' : 'EU';
      const alternateDomains = this.getZohoDomain(alternateRegion);

      await axios.post(
        `https://${alternateDomains.auth}/oauth/v2/token`,
        null,
        {
          params: {
            refresh_token: this.tokens.refreshToken,
            client_id: secureConfig.getConfigValue(
              'ZOHO_CLIENT_ID',
              'zoho-token-manager'
            ),
            client_secret: secureConfig.getConfigValue(
              'ZOHO_CLIENT_SECRET',
              'zoho-token-manager'
            ),
            grant_type: 'refresh_token',
          },
          timeout: 5000,
        }
      );

      // Update cached region
      this.setCachedRegion(alternateRegion);

      this.logger.info('Account migration confirmed', {
        from: currentRegion,
        to: alternateRegion,
      });

      return true;
    } catch (error) {
      this.logger.debug('No migration detected');
      return false;
    }
  }

  /**
   * Get Zoho region with caching
   */
  private async getZohoRegion(): Promise<string> {
    const configuredRegion = secureConfig.getConfigValue(
      'ZOHO_REGION',
      'zoho-token-manager'
    ) as string;

    // Use configured region if valid
    if (configuredRegion && this.isValidZohoRegion(configuredRegion)) {
      return configuredRegion.toUpperCase();
    }

    // Use cached region
    const cachedRegion = this.getCachedRegion();
    if (cachedRegion) {
      return cachedRegion;
    }

    // Auto-detect and cache
    const detectedRegion = await this.detectZohoRegion();
    this.setCachedRegion(detectedRegion);
    return detectedRegion;
  }

  /**
   * Get Zoho domains for region
   */
  private getZohoDomain(region?: string): ZohoRegionConfig {
    const isEU = region?.toUpperCase() === 'EU';
    return {
      auth: isEU ? 'accounts.zoho.eu' : 'accounts.zoho.com',
      api: isEU ? 'mail.zoho.eu' : 'mail.zoho.com',
    };
  }

  /**
   * Validate Zoho region
   */
  private isValidZohoRegion(region: string): boolean {
    return ['EU', 'US'].includes(region.toUpperCase());
  }

  /**
   * Circuit breaker management
   */
  private checkCircuitBreaker(): boolean {
    const config = this.getConfiguration();
    const now = Date.now();

    switch (this.circuitBreakerState) {
      case 'OPEN':
        if (now - this.lastFailureTime >= config.circuitBreakerTimeoutMs) {
          this.logger.debug('Circuit breaker moving to HALF_OPEN');
          this.circuitBreakerState = 'HALF_OPEN';
          return true;
        }
        this.logger.warn('Circuit breaker is OPEN, blocking token refresh');
        return false;

      case 'HALF_OPEN':
      case 'CLOSED':
        return true;

      default:
        return false;
    }
  }

  private recordSuccess(): void {
    if (this.circuitBreakerState === 'HALF_OPEN') {
      this.logger.debug('Circuit breaker moving to CLOSED after success');
      this.circuitBreakerState = 'CLOSED';
    }
    this.failureCount = 0;
    this.persistState();
  }

  private recordFailure(): void {
    const config = this.getConfiguration();
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (
      this.failureCount >= config.circuitBreakerFailures &&
      this.circuitBreakerState === 'CLOSED'
    ) {
      this.logger.warn('Circuit breaker opening after failures', {
        failureCount: this.failureCount,
      });
      this.circuitBreakerState = 'OPEN';
    } else if (this.circuitBreakerState === 'HALF_OPEN') {
      this.logger.debug('Circuit breaker reopening after failed test');
      this.circuitBreakerState = 'OPEN';
    }

    this.persistState();
  }

  /**
   * Retry management
   */
  private calculateBackoffDelay(): number {
    const config = this.getConfiguration();
    const exponentialDelay =
      config.baseRetryDelayMs * Math.pow(2, this.retryAttempts);
    const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5);
    return Math.min(jitteredDelay, config.maxRetryDelayMs);
  }

  private resetRetryAttempts(): void {
    this.retryAttempts = 0;
  }

  private incrementRetryAttempts(): boolean {
    this.retryAttempts++;
    const config = this.getConfiguration();
    return this.retryAttempts <= config.maxRetryAttempts;
  }

  /**
   * Region caching
   */
  private getCachedRegion(): string | null {
    return (this.store.get(this.STORAGE_KEYS.REGION_CACHE) as string) || null;
  }

  private setCachedRegion(region: string): void {
    this.store.set(this.STORAGE_KEYS.REGION_CACHE, region);
  }

  /**
   * State persistence
   */
  private persistState(): void {
    try {
      this.store.set(this.STORAGE_KEYS.CIRCUIT_STATE, {
        circuitBreakerState: this.circuitBreakerState,
        failureCount: this.failureCount,
        lastFailureTime: this.lastFailureTime,
        lastRefreshAttempt: this.lastRefreshAttempt,
      });
    } catch (error) {
      this.logger.error('Failed to persist state', error as Error);
    }
  }

  private loadPersistedState(): void {
    try {
      const state =
        (this.store.get(this.STORAGE_KEYS.CIRCUIT_STATE) as any) || {};

      this.circuitBreakerState = state.circuitBreakerState || 'CLOSED';
      this.failureCount = state.failureCount || 0;
      this.lastFailureTime = state.lastFailureTime || 0;
      this.lastRefreshAttempt = state.lastRefreshAttempt || 0;

      // Check if circuit breaker cooldown has expired
      const config = this.getConfiguration();
      if (
        this.circuitBreakerState === 'OPEN' &&
        Date.now() - this.lastFailureTime >= config.circuitBreakerTimeoutMs
      ) {
        this.circuitBreakerState = 'CLOSED';
        this.failureCount = 0;
      }

      this.logger.debug('Persisted state loaded', {
        circuitBreakerState: this.circuitBreakerState,
        failureCount: this.failureCount,
      });
    } catch (error) {
      this.logger.error('Failed to load persisted state', error as Error);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
