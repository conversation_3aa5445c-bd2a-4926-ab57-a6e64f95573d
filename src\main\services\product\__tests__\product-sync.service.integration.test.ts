/**
 * @file product-sync.service.integration.test.ts
 * @description Comprehensive integration tests for ProductSyncService with Supabase
 * 
 * Tests focus on integration scenarios including:
 * - Supabase authentication handling and RLS policy enforcement
 * - Data synchronization workflows (push/pull)
 * - Conflict resolution strategies
 * - Network failure recovery and retry mechanisms
 * - Batch sync operations with backoff
 * - Sync status tracking and state management
 * - Error recovery patterns
 * - Organization dependency management
 * - Product-color relationship sync
 * - Deduplication after sync
 * - Local deletion preservation
 */

import Database from 'better-sqlite3';
import { ProductSyncService, ProductSyncOptions, ProductSyncResult } from '../product-sync.service';
import { IProductRepository } from '../../../db/repositories/interfaces/product.repository.interface';
import { Product } from '../../../../shared/types/product.types';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Mock the supabase-client module for integration testing
vi.mock('../../../services/supabase-client', () => ({
  getSupabaseClient: vi.fn(),
  ensureAuthenticatedSession: vi.fn()
}));

// Mock the organization service for dependency management tests
vi.mock('../../../db/services/organization.service', () => ({
  OrganizationService: vi.fn().mockImplementation(() => ({
    syncOrganizationsFromSupabase: vi.fn().mockResolvedValue(undefined)
  }))
}));

describe('ProductSyncService Integration Tests', () => {
  let db: Database.Database;
  let productRepository: any;
  let productSyncService: ProductSyncService;
  let mockSupabaseClient: any;
  let mockSession: any;

  const mockOrganizationId = '123e4567-e89b-12d3-a456-426614174000';
  const mockUserId = '987fcdeb-51a2-43d1-b567-321987654321';
  const mockProductId = 'product-integration-test';

  // Test data factories
  const createMockProduct = (overrides = {}) => ({
    id: mockProductId,
    name: 'Integration Test Product',
    description: 'Test product for integration scenarios',
    organizationId: mockOrganizationId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  });

  const createMockDbRow = (overrides = {}) => ({
    id: 1,
    external_id: mockProductId,
    organization_id: mockOrganizationId,
    user_id: mockUserId,
    name: 'Integration Test Product',
    description: 'Test product for integration scenarios',
    metadata: null,
    is_active: 1,
    is_synced: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    deleted_at: null,
    created_by: mockUserId,
    ...overrides
  });

  const createMockSupabaseProduct = (overrides = {}) => ({
    external_id: mockProductId,
    organization_id: mockOrganizationId,
    user_id: mockUserId,
    name: 'Integration Test Product',
    description: 'Test product for integration scenarios',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    deleted_at: null,
    ...overrides
  });

  beforeEach(async () => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Create complete schema for integration testing
    db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        user_id TEXT,
        name TEXT NOT NULL,
        description TEXT,
        sku TEXT,
        metadata TEXT,
        is_active BOOLEAN DEFAULT 1,
        is_synced BOOLEAN DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL,
        created_by TEXT,
        UNIQUE(external_id, organization_id)
      );
      
      CREATE TABLE IF NOT EXISTS colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT NOT NULL,
        organization_id TEXT NOT NULL,
        code TEXT NOT NULL,
        display_name TEXT,
        hex TEXT,
        color_spaces TEXT,
        is_gradient BOOLEAN DEFAULT 0,
        gradient_colors TEXT,
        notes TEXT,
        tags TEXT,
        is_library BOOLEAN DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        is_synced BOOLEAN DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT NULL,
        UNIQUE(external_id, organization_id)
      );
      
      CREATE TABLE IF NOT EXISTS product_colors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        color_id INTEGER NOT NULL,
        display_order INTEGER DEFAULT 0,
        organization_id TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id),
        FOREIGN KEY (color_id) REFERENCES colors (id),
        UNIQUE(product_id, color_id)
      );
      
      CREATE TABLE IF NOT EXISTS organizations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        external_id TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS sync_state (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        organization_id TEXT NOT NULL,
        service_name TEXT NOT NULL,
        last_sync_at TEXT,
        sync_status TEXT DEFAULT 'idle',
        error_count INTEGER DEFAULT 0,
        last_error TEXT,
        UNIQUE(organization_id, service_name)
      );
    `);

    // Insert test organization
    db.prepare(`
      INSERT INTO organizations (external_id, name) 
      VALUES (?, ?)
    `).run(mockOrganizationId, 'Integration Test Organization');

    // Create comprehensive mock repository
    productRepository = {
      findAll: vi.fn(),
      findById: vi.fn(),
      insert: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      search: vi.fn(),
      findUnsynced: vi.fn(),
      getAllWithColors: vi.fn(),
      getProductWithColors: vi.fn(),
      addProductColor: vi.fn(),
      removeProductColor: vi.fn(),
      getProductColors: vi.fn(),
      findSoftDeleted: vi.fn(),
      restoreRecord: vi.fn(),
      deleteMultiple: vi.fn(),
      upsertFromSupabase: vi.fn(),
      deduplicateProducts: vi.fn(),
      markAsSynced: vi.fn(),
      getInternalId: vi.fn(),
      getPreparedStatement: vi.fn()
    };

    // Create service instance
    productSyncService = new ProductSyncService(db, productRepository);

    // Setup comprehensive mock Supabase client
    mockSupabaseClient = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      is: vi.fn().mockReturnThis(),
      in: vi.fn().mockReturnThis(),
      upsert: vi.fn(),
      update: vi.fn(),
      insert: vi.fn(),
      delete: vi.fn(),
      filter: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      count: vi.fn().mockReturnThis(),
      head: vi.fn().mockReturnThis(),
      // Mock response patterns
      mockChainableResponse: function(data: any, error: any = null) {
        return Promise.resolve({ data, error });
      }
    };

    mockSession = {
      user: {
        id: mockUserId,
        email: '<EMAIL>',
        user_metadata: {
          full_name: 'Integration Test User'
        }
      },
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token'
    };

    // Setup module mocks with proper typing
    const supabaseModule = await import('../../../services/supabase-client');
    vi.mocked(supabaseModule.getSupabaseClient).mockReturnValue(mockSupabaseClient);
    vi.mocked(supabaseModule.ensureAuthenticatedSession).mockResolvedValue({ 
      session: mockSession, 
      error: null 
    });
  });

  afterEach(() => {
    db.close();
    vi.clearAllMocks();
  });

  describe('Authentication Integration', () => {
    it('should handle authentication session validation before sync operations', async () => {
      const supabaseModule = await import('../../../services/supabase-client');
      
      // Test 1: Valid session
      vi.mocked(supabaseModule.ensureAuthenticatedSession).mockResolvedValue({ 
        session: mockSession, 
        error: null 
      });

      productRepository.findById.mockReturnValue(createMockDbRow());
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      await expect(
        productSyncService.pushProductToSupabase(mockProductId, mockOrganizationId, mockUserId)
      ).resolves.not.toThrow();

      expect(supabaseModule.ensureAuthenticatedSession).toHaveBeenCalled();
      expect(mockSupabaseClient.upsert).toHaveBeenCalled();
    });

    it('should fail gracefully when authentication session is invalid', async () => {
      const supabaseModule = await import('../../../services/supabase-client');
      
      // Test expired/invalid session
      vi.mocked(supabaseModule.ensureAuthenticatedSession).mockResolvedValue({ 
        session: null, 
        error: 'Session expired' 
      });

      await expect(
        productSyncService.pushProductToSupabase(mockProductId, mockOrganizationId, mockUserId)
      ).rejects.toThrow('No authenticated session');

      expect(mockSupabaseClient.upsert).not.toHaveBeenCalled();
    });

    it('should handle RLS policy enforcement with authenticated sessions', async () => {
      // Mock RLS-compliant query responses
      mockSupabaseClient.select.mockResolvedValue({ count: 10, error: null });
      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: [createMockSupabaseProduct()], 
        error: null 
      });

      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 0,
        errors: []
      });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(result).toBeDefined();
      // Verify RLS-compliant queries were made
      expect(mockSupabaseClient.eq).toHaveBeenCalledWith('organization_id', mockOrganizationId);
    });

    it('should handle RLS policy violations gracefully', async () => {
      const supabaseModule = await import('../../../services/supabase-client');
      
      // Simulate RLS policy rejection
      vi.mocked(supabaseModule.ensureAuthenticatedSession).mockResolvedValue({ 
        session: mockSession, 
        error: null 
      });

      mockSupabaseClient.select.mockRejectedValue(new Error('Row Level Security policy violation'));

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(result).toEqual([]);
    });
  });

  describe('Data Synchronization Workflows', () => {
    it('should handle complete bidirectional sync workflow', async () => {
      // Phase 1: Setup local and remote data
      const localProducts = [createMockDbRow({ external_id: 'local-1', name: 'Local Product 1' })];
      const remoteProducts = [createMockSupabaseProduct({ external_id: 'remote-1', name: 'Remote Product 1' })];

      productRepository.findUnsynced.mockReturnValue(localProducts);
      productRepository.findById.mockReturnValue(localProducts[0]);
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });
      mockSupabaseClient.select.mockResolvedValue({ count: 2, error: null });
      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: remoteProducts, 
        error: null 
      });
      productRepository.update.mockReturnValue(true);
      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 1,
        errors: []
      });

      // Phase 2: Push local changes
      const pushResult = await productSyncService.pushProductsToSupabase(
        ['local-1'], 
        mockOrganizationId
      );

      expect(pushResult.success).toBe(true);
      expect(pushResult.syncedCount).toBe(1);

      // Phase 3: Pull remote changes
      const pullResult = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(pullResult).toHaveLength(1);
      expect(productRepository.deduplicateProducts).toHaveBeenCalledWith(mockOrganizationId);
    });

    it('should handle incremental sync with timestamp tracking', async () => {
      const recentProduct = createMockSupabaseProduct({ 
        external_id: 'recent-product',
        updated_at: new Date().toISOString() 
      });
      const oldProduct = createMockSupabaseProduct({ 
        external_id: 'old-product',
        updated_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
      });

      mockSupabaseClient.select.mockResolvedValue({ count: 2, error: null });
      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: [recentProduct, oldProduct], 
        error: null 
      });
      productRepository.update.mockReturnValue(true);
      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 0,
        errors: []
      });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(result).toHaveLength(2);
      // Both products should be processed regardless of age in this integration test
      expect(productRepository.update).toHaveBeenCalledTimes(2);
    });

    it('should handle sync with metadata preservation', async () => {
      const productWithMetadata = createMockDbRow({
        external_id: 'metadata-product',
        metadata: JSON.stringify({
          customField: 'customValue',
          syncHistory: ['2023-01-01', '2023-01-02']
        })
      });

      productRepository.findById.mockReturnValue(productWithMetadata);
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      await productSyncService.pushProductToSupabase(
        'metadata-product', 
        mockOrganizationId, 
        mockUserId
      );

      expect(mockSupabaseClient.upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          external_id: 'metadata-product',
          organization_id: mockOrganizationId,
          user_id: mockUserId
        })
      );
    });
  });

  describe('Conflict Resolution Strategies', () => {
    it('should preserve local deletions during sync', async () => {
      // Local product is deleted but exists in Supabase
      const localDeletedProduct = {
        id: 1,
        external_id: 'deleted-locally',
        deleted_at: new Date().toISOString(),
        is_active: 0
      };

      const remoteActiveProduct = createMockSupabaseProduct({
        external_id: 'deleted-locally',
        name: 'Active in Supabase',
        deleted_at: null
      });

      db.prepare = vi.fn().mockImplementation((sql) => ({
        get: vi.fn().mockReturnValue(localDeletedProduct),
        run: vi.fn()
      }));

      mockSupabaseClient.select.mockResolvedValue({ count: 1, error: null });
      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: [remoteActiveProduct], 
        error: null 
      });
      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 0,
        errors: []
      });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId,
        { preserveLocalDeletions: true }
      );

      // Should not update the locally deleted product
      expect(productRepository.update).not.toHaveBeenCalledWith(
        'deleted-locally', 
        expect.any(Object), 
        expect.any(String), 
        expect.any(String), 
        true
      );
    });

    it('should handle concurrent modification conflicts', async () => {
      const baseTimestamp = new Date('2023-01-01T10:00:00Z').toISOString();
      const localModified = new Date('2023-01-01T10:30:00Z').toISOString();
      const remoteModified = new Date('2023-01-01T10:45:00Z').toISOString();

      const localProduct = createMockDbRow({
        external_id: 'conflict-product',
        name: 'Local Version',
        updated_at: localModified
      });

      const remoteProduct = createMockSupabaseProduct({
        external_id: 'conflict-product',
        name: 'Remote Version',
        updated_at: remoteModified
      });

      productRepository.findById.mockReturnValue(localProduct);
      mockSupabaseClient.select.mockResolvedValue({ count: 1, error: null });
      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: [remoteProduct], 
        error: null 
      });
      productRepository.update.mockReturnValue(true);
      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 0,
        errors: []
      });

      // In this integration test, remote wins (last-write-wins strategy)
      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(result).toHaveLength(1);
      expect(productRepository.update).toHaveBeenCalledWith(
        'conflict-product',
        expect.objectContaining({ name: 'Remote Version' }),
        mockOrganizationId,
        mockUserId,
        true
      );
    });

    it('should handle duplicate product resolution', async () => {
      const duplicateProducts = [
        createMockSupabaseProduct({ external_id: 'dup-1', name: 'Duplicate Product' }),
        createMockSupabaseProduct({ external_id: 'dup-2', name: 'Duplicate Product' })
      ];

      mockSupabaseClient.select.mockResolvedValue({ count: 2, error: null });
      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: duplicateProducts, 
        error: null 
      });
      productRepository.update.mockReturnValue(true);
      productRepository.insert.mockReturnValue('new-product-id');
      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 1,
        errors: ['Merged duplicate: Duplicate Product']
      });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(result).toHaveLength(2);
      expect(productRepository.deduplicateProducts).toHaveBeenCalledWith(mockOrganizationId);
    });
  });

  describe('Network Failure Recovery', () => {
    it('should implement exponential backoff retry strategy', async () => {
      let attemptCount = 0;
      const mockNetworkError = new Error('Network timeout');

      productRepository.findById.mockReturnValue(createMockDbRow());
      mockSupabaseClient.upsert.mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          return Promise.resolve({ error: mockNetworkError });
        }
        return Promise.resolve({ error: null });
      });

      const startTime = Date.now();
      const result = await productSyncService.pushProductsToSupabase(
        [mockProductId], 
        mockOrganizationId,
        { retryAttempts: 3 }
      );
      const duration = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(1);
      expect(attemptCount).toBe(3);
      // Should have some delay due to exponential backoff
      expect(duration).toBeGreaterThan(100); // At least some delay
    });

    it('should handle partial batch failures with recovery', async () => {
      const productIds = ['success-1', 'fail-1', 'success-2'];
      
      productRepository.findById.mockImplementation((id) => 
        createMockDbRow({ external_id: id, name: `Product ${id}` })
      );

      mockSupabaseClient.upsert.mockImplementation(async (data) => {
        if (data.external_id === 'fail-1') {
          return { error: new Error('Temporary failure') };
        }
        return { error: null };
      });

      const result = await productSyncService.pushProductsToSupabase(
        productIds, 
        mockOrganizationId,
        { retryAttempts: 1 }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(2); // Two successful
      expect(result.errors).toHaveLength(1); // One failure
      expect(result.errors[0]).toContain('fail-1');
    });

    it('should handle complete network connectivity loss', async () => {
      const connectivityError = new Error('ENOTFOUND - Network unreachable');
      
      productRepository.findById.mockReturnValue(createMockDbRow());
      mockSupabaseClient.upsert.mockRejectedValue(connectivityError);

      const result = await productSyncService.pushProductsToSupabase(
        [mockProductId], 
        mockOrganizationId,
        { retryAttempts: 2 }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(0);
      expect(result.errors).toHaveLength(1);
      expect(mockSupabaseClient.upsert).toHaveBeenCalledTimes(2); // Retry attempts
    });

    it('should handle Supabase service unavailability', async () => {
      const serviceError = new Error('Service Temporarily Unavailable');
      
      const supabaseModule = await import('../../../services/supabase-client');
      vi.mocked(supabaseModule.ensureAuthenticatedSession).mockRejectedValue(serviceError);

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(result).toEqual([]);
    });
  });

  describe('Batch Sync Operations', () => {
    it('should handle large batch sync with proper chunking', async () => {
      const largeBatch = Array.from({ length: 150 }, (_, i) => `product-${i}`);
      
      productRepository.findById.mockImplementation((id) => 
        createMockDbRow({ external_id: id, name: `Product ${id}` })
      );
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      const result = await productSyncService.pushProductsToSupabase(
        largeBatch, 
        mockOrganizationId,
        { batchSize: 25 }
      );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(150);
      expect(mockSupabaseClient.upsert).toHaveBeenCalledTimes(150);
    });

    it('should handle batch sync with mixed success/failure rates', async () => {
      const mixedBatch = Array.from({ length: 10 }, (_, i) => `product-${i}`);
      
      productRepository.findById.mockImplementation((id) => 
        createMockDbRow({ external_id: id, name: `Product ${id}` })
      );

      // Fail every third product
      mockSupabaseClient.upsert.mockImplementation(async (data) => {
        const index = parseInt(data.external_id.split('-')[1]);
        if (index % 3 === 0) {
          return { error: new Error(`Failure for ${data.external_id}`) };
        }
        return { error: null };
      });

      const result = await productSyncService.pushProductsToSupabase(
        mixedBatch, 
        mockOrganizationId,
        { batchSize: 5, retryAttempts: 1 }
      );

      expect(result.success).toBe(false);
      expect(result.syncedCount).toBe(6); // 4 successful out of 10, after retries
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should optimize batch sizes based on payload size', async () => {
      // Test with products having large metadata
      const largeMetadataProducts = Array.from({ length: 20 }, (_, i) => `large-product-${i}`);
      
      productRepository.findById.mockImplementation((id) => 
        createMockDbRow({ 
          external_id: id, 
          name: `Product ${id}`,
          metadata: JSON.stringify({ 
            largeData: 'x'.repeat(10000) // 10KB per product
          })
        })
      );
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      const result = await productSyncService.pushProductsToSupabase(
        largeMetadataProducts, 
        mockOrganizationId,
        { batchSize: 10 } // Should process in smaller effective batches
      );

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(20);
    });
  });

  describe('Sync Status Tracking', () => {
    it('should track sync status for individual products', () => {
      const unsyncedProducts = [
        createMockDbRow({ external_id: 'unsynced-1', is_synced: 0 }),
        createMockDbRow({ external_id: 'unsynced-2', is_synced: 0 })
      ];

      productRepository.findUnsynced.mockReturnValue(unsyncedProducts);

      const result = productSyncService.getUnsyncedProducts();

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('unsynced-1');
      expect(result[1].id).toBe('unsynced-2');
    });

    it('should update sync status after successful operations', async () => {
      productRepository.findById.mockReturnValue(createMockDbRow());
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      await productSyncService.pushProductToSupabase(
        mockProductId, 
        mockOrganizationId, 
        mockUserId
      );

      expect(productRepository.markAsSynced).toHaveBeenCalledWith(mockProductId);
    });

    it('should maintain sync state across service restarts', () => {
      // Test sync state persistence
      productSyncService.markProductAsSynced('persistent-product');
      
      expect(productRepository.markAsSynced).toHaveBeenCalledWith('persistent-product');
    });

    it('should handle sync status reset scenarios', () => {
      const productIds = ['reset-1', 'reset-2'];
      
      productSyncService.resetSyncStatus(productIds);
      
      // Should log reset operations (implementation specific)
      productIds.forEach(id => {
        expect(console.log).toHaveBeenCalledWith(
          expect.stringContaining(`Reset sync status for product: ${id}`)
        );
      });
    });
  });

  describe('Organization Dependency Management', () => {
    it('should sync missing organizations before product sync', async () => {
      // Remove organization to simulate missing dependency
      db.prepare('DELETE FROM organizations WHERE external_id = ?').run(mockOrganizationId);

      const { OrganizationService } = await import('../../../db/services/organization.service');
      const mockOrgService = new OrganizationService();
      
      // Mock organization sync success
      mockOrgService.syncOrganizationsFromSupabase.mockResolvedValue(undefined);
      
      // Re-add organization after "sync"
      db.prepare(`
        INSERT INTO organizations (external_id, name) 
        VALUES (?, ?)
      `).run(mockOrganizationId, 'Synced Organization');

      mockSupabaseClient.select.mockResolvedValue({ count: 0, error: null });
      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: [], 
        error: null 
      });
      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 0,
        errors: []
      });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(mockOrgService.syncOrganizationsFromSupabase).toHaveBeenCalledWith(mockUserId);
      expect(result).toEqual([]);
    });

    it('should handle organization sync failures gracefully', async () => {
      // Remove organization
      db.prepare('DELETE FROM organizations WHERE external_id = ?').run(mockOrganizationId);

      const { OrganizationService } = await import('../../../db/services/organization.service');
      const mockOrgService = new OrganizationService();
      
      // Mock organization sync failure
      mockOrgService.syncOrganizationsFromSupabase.mockRejectedValue(
        new Error('Organization sync failed')
      );

      await expect(
        productSyncService.syncProductsFromSupabase(mockUserId, mockOrganizationId)
      ).rejects.toThrow('Organization sync failed');
    });
  });

  describe('Product-Color Relationship Sync', () => {
    it('should sync product-color relationships with proper mapping', async () => {
      const mockProductColorData = [
        { product_id: 1, color_id: 1, display_order: 1 },
        { product_id: 1, color_id: 2, display_order: 2 }
      ];

      const mockSupabaseProducts = [{ id: 1, external_id: 'product-1' }];
      const mockSupabaseColors = [
        { id: 1, external_id: 'color-1' },
        { id: 2, external_id: 'color-2' }
      ];

      // Mock the sequential calls to select()
      mockSupabaseClient.select
        .mockResolvedValueOnce({ data: mockSupabaseProducts, error: null })
        .mockResolvedValueOnce({ data: mockSupabaseColors, error: null });

      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: mockProductColorData, 
        error: null 
      });

      // Mock local ID lookups
      db.prepare = vi.fn().mockImplementation((sql) => ({
        get: vi.fn().mockImplementation((externalId, orgId) => {
          if (sql.includes('products')) {
            return { id: 1 };
          }
          if (sql.includes('colors')) {
            return { id: externalId === 'color-1' ? 1 : 2 };
          }
          return null;
        }),
        run: vi.fn().mockReturnValue({ changes: 1 })
      }));

      const result = await productSyncService.syncProductColorsFromSupabase(mockOrganizationId);

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(2);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle orphaned product-color relationships', async () => {
      const orphanedRelationships = [
        { product_id: 999, color_id: 1, display_order: 1 }, // Non-existent product
        { product_id: 1, color_id: 999, display_order: 2 }  // Non-existent color
      ];

      mockSupabaseClient.select
        .mockResolvedValueOnce({ data: [], error: null }) // No products
        .mockResolvedValueOnce({ data: [], error: null }); // No colors

      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: orphanedRelationships, 
        error: null 
      });

      const result = await productSyncService.syncProductColorsFromSupabase(mockOrganizationId);

      expect(result.success).toBe(true);
      expect(result.syncedCount).toBe(0);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Error Recovery and Retry Logic', () => {
    it('should implement circuit breaker pattern for persistent failures', async () => {
      let failureCount = 0;
      const persistentError = new Error('Persistent database error');

      productRepository.findById.mockReturnValue(createMockDbRow());
      mockSupabaseClient.upsert.mockImplementation(() => {
        failureCount++;
        return Promise.resolve({ error: persistentError });
      });

      // Should eventually stop retrying after circuit breaker opens
      const result = await productSyncService.pushProductsToSupabase(
        [mockProductId], 
        mockOrganizationId,
        { retryAttempts: 5 }
      );

      expect(result.success).toBe(false);
      expect(failureCount).toBe(5); // Should respect retry limit
    });

    it('should recover from transient authentication errors', async () => {
      const supabaseModule = await import('../../../services/supabase-client');
      let authAttempts = 0;

      vi.mocked(supabaseModule.ensureAuthenticatedSession).mockImplementation(async () => {
        authAttempts++;
        if (authAttempts === 1) {
          return { session: null, error: 'Token expired' };
        }
        return { session: mockSession, error: null };
      });

      productRepository.findById.mockReturnValue(createMockDbRow());
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      // Should succeed on second attempt after auth recovery
      await expect(
        productSyncService.pushProductToSupabase(mockProductId, mockOrganizationId, mockUserId)
      ).rejects.toThrow('No authenticated session'); // First call fails

      // Simulate second call with recovered auth
      await expect(
        productSyncService.pushProductToSupabase(mockProductId, mockOrganizationId, mockUserId)
      ).resolves.not.toThrow();
    });

    it('should handle rate limiting with exponential backoff', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      let callCount = 0;

      productRepository.findById.mockReturnValue(createMockDbRow());
      mockSupabaseClient.upsert.mockImplementation(() => {
        callCount++;
        if (callCount <= 2) {
          return Promise.resolve({ error: rateLimitError });
        }
        return Promise.resolve({ error: null });
      });

      const startTime = Date.now();
      const result = await productSyncService.pushProductsToSupabase(
        [mockProductId], 
        mockOrganizationId,
        { retryAttempts: 3 }
      );
      const duration = Date.now() - startTime;

      expect(result.success).toBe(true);
      expect(callCount).toBe(3);
      expect(duration).toBeGreaterThan(1000); // Should have backoff delays
    });
  });

  describe('Service Information and Capabilities', () => {
    it('should provide comprehensive service metadata', () => {
      const serviceInfo = productSyncService.getServiceInfo();

      expect(serviceInfo.name).toBe('ProductSyncService');
      expect(serviceInfo.version).toBe('1.0.0');
      expect(serviceInfo.capabilities).toContain('Push products to Supabase');
      expect(serviceInfo.capabilities).toContain('Sync products from Supabase');
      expect(serviceInfo.capabilities).toContain('Sync product-color relationships');
      expect(serviceInfo.capabilities).toContain('Batch sync operations');
      expect(serviceInfo.capabilities).toContain('Retry logic with exponential backoff');
      expect(serviceInfo.capabilities).toContain('Authentication handling');
      expect(serviceInfo.capabilities).toContain('RLS policy compliance');
      expect(serviceInfo.capabilities).toContain('Error recovery and logging');
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle concurrent sync operations', async () => {
      const concurrentProducts = Array.from({ length: 10 }, (_, i) => `concurrent-${i}`);
      
      productRepository.findById.mockImplementation((id) => 
        createMockDbRow({ external_id: id, name: `Product ${id}` })
      );
      mockSupabaseClient.upsert.mockResolvedValue({ error: null });

      // Simulate concurrent pushes
      const promises = concurrentProducts.map(id =>
        productSyncService.pushProductToSupabase(id, mockOrganizationId, mockUserId)
      );

      await expect(Promise.all(promises)).resolves.not.toThrow();
      expect(mockSupabaseClient.upsert).toHaveBeenCalledTimes(10);
    });

    it('should optimize memory usage for large datasets', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => 
        createMockSupabaseProduct({ 
          external_id: `large-${i}`, 
          name: `Large Product ${i}`,
          description: 'x'.repeat(1000) // 1KB per product
        })
      );

      mockSupabaseClient.select.mockResolvedValue({ count: 1000, error: null });
      mockSupabaseClient.mockChainableResponse = vi.fn().mockResolvedValue({ 
        data: largeDataset, 
        error: null 
      });
      productRepository.update.mockReturnValue(true);
      productRepository.insert.mockReturnValue('new-id');
      productRepository.deduplicateProducts.mockReturnValue({
        success: true,
        deduplicatedCount: 0,
        errors: []
      });

      const result = await productSyncService.syncProductsFromSupabase(
        mockUserId, 
        mockOrganizationId
      );

      expect(result).toHaveLength(1000);
      // Should not cause memory issues
    });
  });
});