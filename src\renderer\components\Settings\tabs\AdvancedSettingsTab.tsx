/**
 * @file AdvancedSettingsTab.tsx
 * @description Advanced settings tab for developer tools and troubleshooting
 */

import React from 'react';
import { AlertTriangle } from 'lucide-react';
import ErrorLogsSection from '../sections/ErrorLogsSection';
import DeveloperToolsSection from '../sections/DeveloperToolsSection';
import DatabaseMaintenanceSection from '../sections/DatabaseMaintenanceSection';
import OrganizationManagementSection from '../sections/OrganizationManagementSection';

/**
 * Advanced settings tab component
 */
export const AdvancedSettingsTab: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Warning Banner */}
      <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-[var(--radius-lg)] p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-yellow-400" aria-hidden="true" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Advanced Settings
            </h3>
            <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
              <p>
                These settings are intended for troubleshooting and debugging purposes.
                Changes in this section may affect application stability.
              </p>
            </div>
          </div>
        </div>
      </div>

      <ErrorLogsSection />
      <DeveloperToolsSection />
      <DatabaseMaintenanceSection />
      <OrganizationManagementSection />
    </div>
  );
};

export default AdvancedSettingsTab;