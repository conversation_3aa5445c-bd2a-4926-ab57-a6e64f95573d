/**
 * @file colorGeneration.ts
 * @description Utility to generate large sets of Pantone and RAL colors
 */

import { ColorEntry } from '../../shared/types/color.types';
import { v4 as uuidv4 } from 'uuid';

// Common timestamp for all generated colors
const now = new Date().toISOString();

// Advanced Pantone colors generator
export function generatePantoneColors(): ColorEntry[] {
  const colors: ColorEntry[] = [];

  // Generate Pantone colors for different series

  // Basic colors
  const basicColors = [
    {
      code: 'PMS Process Yellow C',
      hex: '#FEDD00',
      cmyk: '0,5,100,0',
      name: 'Process Yellow',
    },
    {
      code: 'PMS Yellow 012 C',
      hex: '#FFD700',
      cmyk: '0,10,100,0',
      name: 'Yellow 012',
    },
    {
      code: 'PMS Orange 021 C',
      hex: '#FF5000',
      cmyk: '0,80,100,0',
      name: 'Orange 021',
    },
    {
      code: 'PMS Warm Red C',
      hex: '#F9423A',
      cmyk: '0,85,95,0',
      name: 'Warm Red',
    },
    {
      code: 'PMS Red 032 C',
      hex: '#EF3340',
      cmyk: '0,90,85,0',
      name: 'Red 032',
    },
    {
      code: 'PMS Rubine Red C',
      hex: '#CE0058',
      cmyk: '0,100,25,4',
      name: 'Rubine Red',
    },
    {
      code: 'PMS Rhodamine Red C',
      hex: '#E10098',
      cmyk: '0,100,0,0',
      name: 'Rhodamine Red',
    },
    {
      code: 'PMS Purple C',
      hex: '#BB29BB',
      cmyk: '35,100,0,0',
      name: 'Purple',
    },
    {
      code: 'PMS Violet C',
      hex: '#440099',
      cmyk: '90,100,0,0',
      name: 'Violet',
    },
    {
      code: 'PMS Blue 072 C',
      hex: '#10069F',
      cmyk: '100,95,0,3',
      name: 'Blue 072',
    },
    {
      code: 'PMS Reflex Blue C',
      hex: '#001489',
      cmyk: '100,90,0,10',
      name: 'Reflex Blue',
    },
    {
      code: 'PMS Process Blue C',
      hex: '#0085CA',
      cmyk: '100,10,0,10',
      name: 'Process Blue',
    },
    { code: 'PMS Green C', hex: '#00AB84', cmyk: '95,0,60,0', name: 'Green' },
    { code: 'PMS Black C', hex: '#2D2926', cmyk: '0,0,0,100', name: 'Black' },
  ];

  // Add basic colors
  basicColors.forEach(color => {
    colors.push({
      id: uuidv4(),
      product: 'Pantone',
      name: color.name,
      code: color.code,
      hex: color.hex,
      cmyk: color.cmyk,
      notes: color.name,
      createdAt: now,
      updatedAt: now,
    });
  });

  // Generate hundreds of colors series
  for (let i = 100; i <= 800; i += 100) {
    for (let j = 0; j <= 6; j++) {
      const num = i + j;
      const hue = Math.floor((i / 100) * 45) % 360;
      const saturation = 75 - j * 10;
      const lightness = 50 + j * 5;

      // Convert HSL to Hex
      const hex = hslToHex(hue, saturation, lightness);

      // Calculate approximate CMYK
      const cmyk = calculateApproximateCMYK(hex);

      colors.push({
        id: uuidv4(),
        product: 'Pantone',
        name: `Series ${num}`,
        code: `PMS ${num} C`,
        hex,
        cmyk,
        notes: `Pantone ${num}`,
        createdAt: now,
        updatedAt: now,
      });
    }
  }

  // Generate pantone colors from 1000 to 2000 series
  for (let i = 1000; i <= 2000; i += 100) {
    for (let j = 0; j <= 9; j++) {
      const num = i + j;
      const hue = Math.floor(((i - 1000) / 100) * 36) % 360;
      const saturation = 85 - j * 5;
      const lightness = 45 + j * 3;

      const hex = hslToHex(hue, saturation, lightness);
      const cmyk = calculateApproximateCMYK(hex);

      colors.push({
        id: uuidv4(),
        product: 'Pantone',
        name: `Series ${num}`,
        code: `PMS ${num} C`,
        hex,
        cmyk,
        notes: `Pantone ${num}`,
        createdAt: now,
        updatedAt: now,
      });
    }
  }

  // Generate Cool Gray series
  for (let i = 1; i <= 11; i++) {
    const lightness = 90 - i * 5;
    const hex = hslToHex(220, 5, lightness);
    const cmyk = calculateApproximateCMYK(hex);

    colors.push({
      id: uuidv4(),
      product: 'Pantone',
      name: `Cool Gray ${i}`,
      code: `PMS Cool Gray ${i} C`,
      hex,
      cmyk,
      notes: `Cool Gray ${i}`,
      createdAt: now,
      updatedAt: now,
    });
  }

  // Generate Warm Gray series
  for (let i = 1; i <= 11; i++) {
    const lightness = 90 - i * 5;
    const hex = hslToHex(40, 5, lightness);
    const cmyk = calculateApproximateCMYK(hex);

    colors.push({
      id: uuidv4(),
      product: 'Pantone',
      name: `Warm Gray ${i}`,
      code: `PMS Warm Gray ${i} C`,
      hex,
      cmyk,
      notes: `Warm Gray ${i}`,
      createdAt: now,
      updatedAt: now,
    });
  }

  return colors;
}

// Advanced RAL colors generator
export function generateRalColors(): ColorEntry[] {
  const colors: ColorEntry[] = [];

  // Generate RAL classic colors
  // RAL 1000 to 1037 (Yellow/Beige)
  for (let i = 1000; i <= 1037; i++) {
    if (i % 10 > 7 && i < 1030) {
      continue;
    } // Skip some numbers to match RAL numbering

    const hue = 50 + ((i - 1000) % 10) * 4;
    const saturation = 70 - ((i - 1000) / 10) * 5;
    const lightness = 60 - ((i - 1000) % 3) * 5;

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  // RAL 2000 to 2033 (Orange)
  for (let i = 2000; i <= 2033; i++) {
    if (i % 10 > 7 && i < 2030) {
      continue;
    } // Skip some numbers

    const hue = 30 + ((i - 2000) % 10) * 3;
    const saturation = 90 - ((i - 2000) / 10) * 10;
    const lightness = 50 - ((i - 2000) % 3) * 5;

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  // RAL 3000 to 3033 (Red)
  for (let i = 3000; i <= 3033; i++) {
    if (i % 10 > 7 && i < 3030) {
      continue;
    } // Skip some numbers

    const hue = 0 + ((i - 3000) % 10) * 3;
    const saturation = 90 - ((i - 3000) / 10) * 5;
    const lightness = 50 - ((i - 3000) % 4) * 5;

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  // RAL 4000 to 4012 (Purple/Violet)
  for (let i = 4000; i <= 4012; i++) {
    const hue = 300 + ((i - 4000) % 10) * 5;
    const saturation = 60 - ((i - 4000) / 5) * 10;
    const lightness = 40 - ((i - 4000) % 3) * 5;

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  // RAL 5000 to 5025 (Blue)
  for (let i = 5000; i <= 5025; i++) {
    if (i % 10 > 7 && i < 5020) {
      continue;
    } // Skip some numbers

    const hue = 220 + ((i - 5000) % 10) * 3;
    const saturation = 80 - ((i - 5000) / 10) * 10;
    const lightness = 45 - ((i - 5000) % 5) * 5;

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  // RAL 6000 to 6038 (Green)
  for (let i = 6000; i <= 6038; i++) {
    if (i % 10 > 7 && i < 6030) {
      continue;
    } // Skip some numbers

    const hue = 140 + ((i - 6000) % 10) * 4;
    const saturation = 70 - ((i - 6000) / 15) * 10;
    const lightness = 40 - ((i - 6000) % 4) * 5;

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  // RAL 7000 to 7048 (Gray)
  for (let i = 7000; i <= 7048; i++) {
    if (i % 10 > 7 && i < 7040) {
      continue;
    } // Skip some numbers

    const hue = ((i - 7000) % 20) * 18;
    const saturation = 5 + ((i - 7000) % 5) * 2;
    const lightness = 60 - ((i - 7000) % 8) * 5;

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  // RAL 8000 to 8028 (Brown)
  for (let i = 8000; i <= 8028; i++) {
    if (i % 10 > 7 && i < 8020) {
      continue;
    } // Skip some numbers

    const hue = 30 + ((i - 8000) % 10) * 3;
    const saturation = 60 - ((i - 8000) / 10) * 5;
    const lightness = 30 - ((i - 8000) % 5) * 2;

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  // RAL 9000 to 9023 (White/Black)
  for (let i = 9000; i <= 9023; i++) {
    if (i % 10 > 7 && i < 9020) {
      continue;
    } // Skip some numbers

    let hue = 60;
    let saturation = 0;
    let lightness = 95;

    if (i >= 9004 && i <= 9011) {
      // Black colors
      lightness = 10 - ((i - 9004) % 4) * 2;
    } else if (i >= 9001 && i <= 9003) {
      // White colors
      lightness = 95 - ((i - 9001) % 3) * 2;
    } else if (i >= 9016 && i <= 9018) {
      // More white colors
      saturation = ((i - 9016) % 3) * 2;
      lightness = 98 - ((i - 9016) % 3) * 3;
    } else {
      // Special colors
      hue = ((i - 9000) * 20) % 360;
      saturation = 5 + ((i - 9000) % 5) * 2;
      lightness = 80 - ((i - 9000) % 4) * 10;
    }

    const hex = hslToHex(hue, saturation, lightness);
    const cmyk = calculateApproximateCMYK(hex);
    const name = getRalColorName(i);

    colors.push({
      id: uuidv4(),
      product: 'RAL',
      name,
      code: `RAL ${i}`,
      hex,
      cmyk,
      notes: name,
      createdAt: now,
      updatedAt: now,
    });
  }

  return colors;
}

// Helper functions

// Convert HSL to Hex
function hslToHex(h: number, s: number, l: number): string {
  l /= 100;
  const a = (s * Math.min(l, 1 - l)) / 100;
  const f = (n: number) => {
    const k = (n + h / 30) % 12;
    const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
    return Math.round(255 * color)
      .toString(16)
      .padStart(2, '0');
  };
  return `#${f(0)}${f(8)}${f(4)}`;
}

// Calculate approximate CMYK from hex
function calculateApproximateCMYK(hex: string): string {
  // Remove the # if present
  hex = hex.replace('#', '');

  // Convert hex to RGB
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  // Calculate CMYK
  const k = 1 - Math.max(r, g, b);
  const c = k === 1 ? 0 : (1 - r - k) / (1 - k);
  const m = k === 1 ? 0 : (1 - g - k) / (1 - k);
  const y = k === 1 ? 0 : (1 - b - k) / (1 - k);

  // Convert to percentage and round
  const cPerc = Math.round(c * 100);
  const mPerc = Math.round(m * 100);
  const yPerc = Math.round(y * 100);
  const kPerc = Math.round(k * 100);

  return `${cPerc},${mPerc},${yPerc},${kPerc}`;
}

// Get RAL color name based on number
function getRalColorName(ralNumber: number): string {
  const names: Record<number, string> = {
    1000: 'Green beige',
    1001: 'Beige',
    1002: 'Sand yellow',
    1003: 'Signal yellow',
    1004: 'Golden yellow',
    1005: 'Honey yellow',
    1006: 'Maize yellow',
    1007: 'Daffodil yellow',
    1011: 'Brown beige',
    1012: 'Lemon yellow',
    1013: 'Oyster white',
    1014: 'Ivory',
    1015: 'Light ivory',
    1016: 'Sulfur yellow',
    1017: 'Saffron yellow',
    1018: 'Zinc yellow',
    1019: 'Grey beige',
    1020: 'Olive yellow',
    1021: 'Rape yellow',
    1023: 'Traffic yellow',
    1024: 'Ochre yellow',
    1026: 'Luminous yellow',
    1027: 'Curry',
    1028: 'Melon yellow',
    1032: 'Broom yellow',
    1033: 'Dahlia yellow',
    1034: 'Pastel yellow',
    1035: 'Pearl beige',
    1036: 'Pearl gold',
    1037: 'Sun yellow',
    2000: 'Yellow orange',
    2001: 'Red orange',
    2002: 'Vermilion',
    2003: 'Pastel orange',
    2004: 'Pure orange',
    2005: 'Luminous orange',
    2007: 'Luminous bright orange',
    2008: 'Bright red orange',
    2009: 'Traffic orange',
    2010: 'Signal orange',
    2011: 'Deep orange',
    2012: 'Salmon orange',
    2013: 'Pearl orange',
    3000: 'Flame red',
    3001: 'Signal red',
    3002: 'Carmine red',
    3003: 'Ruby red',
    3004: 'Purple red',
    3005: 'Wine red',
    3007: 'Black red',
    3009: 'Oxide red',
    3011: 'Brown red',
    3012: 'Beige red',
    3013: 'Tomato red',
    3014: 'Antique pink',
    3015: 'Light pink',
    3016: 'Coral red',
    3017: 'Rose',
    3018: 'Strawberry red',
    3020: 'Traffic red',
    3022: 'Salmon pink',
    3024: 'Luminous red',
    3026: 'Luminous bright red',
    3027: 'Raspberry red',
    3028: 'Pure red',
    3031: 'Orient red',
    3032: 'Pearl ruby red',
    3033: 'Pearl pink',
    4001: 'Red lilac',
    4002: 'Red violet',
    4003: 'Heather violet',
    4004: 'Claret violet',
    4005: 'Blue lilac',
    4006: 'Traffic purple',
    4007: 'Purple violet',
    4008: 'Signal violet',
    4009: 'Pastel violet',
    4010: 'Telemagenta',
    4011: 'Pearl violet',
    4012: 'Pearl blackberry',
    5000: 'Violet blue',
    5001: 'Green blue',
    5002: 'Ultramarine blue',
    5003: 'Sapphire blue',
    5004: 'Black blue',
    5005: 'Signal blue',
    5007: 'Brilliant blue',
    5008: 'Grey blue',
    5009: 'Azure blue',
    5010: 'Gentian blue',
    5011: 'Steel blue',
    5012: 'Light blue',
    5013: 'Cobalt blue',
    5014: 'Pigeon blue',
    5015: 'Sky blue',
    5017: 'Traffic blue',
    5018: 'Turquoise blue',
    5019: 'Capri blue',
    5020: 'Ocean blue',
    5021: 'Water blue',
    5022: 'Night blue',
    5023: 'Distant blue',
    5024: 'Pastel blue',
    5025: 'Pearl gentian blue',
    5026: 'Pearl night blue',
    6000: 'Patina green',
    6001: 'Emerald green',
    6002: 'Leaf green',
    6003: 'Olive green',
    6004: 'Blue green',
    6005: 'Moss green',
    6006: 'Grey olive',
    6007: 'Bottle green',
    6008: 'Brown green',
    6009: 'Fir green',
    6010: 'Grass green',
    6011: 'Reseda green',
    6012: 'Black green',
    6013: 'Reed green',
    6014: 'Yellow olive',
    6015: 'Black olive',
    6016: 'Turquoise green',
    6017: 'May green',
    6018: 'Yellow green',
    6019: 'Pastel green',
    6020: 'Chrome green',
    6021: 'Pale green',
    6022: 'Olive drab',
    6024: 'Traffic green',
    6025: 'Fern green',
    6026: 'Opal green',
    6027: 'Light green',
    6028: 'Pine green',
    6029: 'Mint green',
    6032: 'Signal green',
    6033: 'Mint turquoise',
    6034: 'Pastel turquoise',
    6035: 'Pearl green',
    6036: 'Pearl opal green',
    6037: 'Pure green',
    6038: 'Luminous green',
    7000: 'Squirrel grey',
    7001: 'Silver grey',
    7002: 'Olive grey',
    7003: 'Moss grey',
    7004: 'Signal grey',
    7005: 'Mouse grey',
    7006: 'Beige grey',
    7008: 'Khaki grey',
    7009: 'Green grey',
    7010: 'Tarpaulin grey',
    7011: 'Iron grey',
    7012: 'Basalt grey',
    7013: 'Brown grey',
    7015: 'Slate grey',
    7016: 'Anthracite grey',
    7021: 'Black grey',
    7022: 'Umbra grey',
    7023: 'Concrete grey',
    7024: 'Graphite grey',
    7026: 'Granite grey',
    7030: 'Stone grey',
    7031: 'Blue grey',
    7032: 'Pebble grey',
    7033: 'Cement grey',
    7034: 'Yellow grey',
    7035: 'Light grey',
    7036: 'Platinum grey',
    7037: 'Dusty grey',
    7038: 'Agate grey',
    7039: 'Quartz grey',
    7040: 'Window grey',
    7042: 'Traffic grey A',
    7043: 'Traffic grey B',
    7044: 'Silk grey',
    7045: 'Telegrey 1',
    7046: 'Telegrey 2',
    7047: 'Telegrey 4',
    7048: 'Pearl mouse grey',
    8000: 'Green brown',
    8001: 'Ochre brown',
    8002: 'Signal brown',
    8003: 'Clay brown',
    8004: 'Copper brown',
    8007: 'Fawn brown',
    8008: 'Olive brown',
    8011: 'Nut brown',
    8012: 'Red brown',
    8014: 'Sepia brown',
    8015: 'Chestnut brown',
    8016: 'Mahogany brown',
    8017: 'Chocolate brown',
    8019: 'Grey brown',
    8022: 'Black brown',
    8023: 'Orange brown',
    8024: 'Beige brown',
    8025: 'Pale brown',
    8028: 'Terra brown',
    9001: 'Cream',
    9002: 'Grey white',
    9003: 'Signal white',
    9004: 'Signal black',
    9005: 'Jet black',
    9006: 'White aluminium',
    9007: 'Grey aluminium',
    9010: 'Pure white',
    9011: 'Graphite black',
    9016: 'Traffic white',
    9017: 'Traffic black',
    9018: 'Papyrus white',
    9022: 'Pearl light grey',
    9023: 'Pearl dark grey',
  };

  // Return name if it exists, otherwise generate a name
  return (
    names[ralNumber] ||
    `RAL ${ralNumber} ${
      ralNumber >= 1000 && ralNumber < 2000
        ? 'Yellow'
        : ralNumber >= 2000 && ralNumber < 3000
          ? 'Orange'
          : ralNumber >= 3000 && ralNumber < 4000
            ? 'Red'
            : ralNumber >= 4000 && ralNumber < 5000
              ? 'Purple'
              : ralNumber >= 5000 && ralNumber < 6000
                ? 'Blue'
                : ralNumber >= 6000 && ralNumber < 7000
                  ? 'Green'
                  : ralNumber >= 7000 && ralNumber < 8000
                    ? 'Grey'
                    : ralNumber >= 8000 && ralNumber < 9000
                      ? 'Brown'
                      : 'Neutral'
    }`
  );
}
