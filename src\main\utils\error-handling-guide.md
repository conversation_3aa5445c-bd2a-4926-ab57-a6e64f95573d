# Service Error Handling Guide

## Overview

This guide shows how to use the standardized error handling patterns for service operations in ChromaSync.

## Basic Pattern

### 1. Add Error Handler to Service

```typescript
import {
  createServiceErrorHandler,
  ServiceResult,
} from '../utils/service-error-handler';

export class YourService {
  private errorHandler = createServiceErrorHandler('YourService');

  // ... rest of service
}
```

### 2. Wrap Operations

#### For Synchronous Operations

```typescript
getSomething(id: string): ServiceResult<Something> {
  return this.errorHandler.wrap(() => {
    // Your existing logic here
    const result = this.db.prepare('SELECT * FROM table WHERE id = ?').get(id);
    if (!result) {
      throw new Error(`Something with ID ${id} not found`);
    }
    return this.convertToSomething(result);
  }, 'getSomething', { id });
}
```

#### For Async Operations

```typescript
async createSomething(data: NewSomething): Promise<ServiceResult<Something>> {
  return await this.errorHandler.wrapAsync(async () => {
    // Your existing async logic here
    const result = await this.externalService.create(data);
    return this.convertToSomething(result);
  }, 'createSomething', data);
}
```

### 3. Using Results

#### In Services (return ServiceResult)

```typescript
// New pattern - returns ServiceResult
const result = productService.getAllWithErrorHandling(orgId);
if (result.success) {
  console.log('Products:', result.data);
} else {
  console.error('Error:', result.error);
}
```

#### In IPC Handlers (convert to safe defaults)

```typescript
import { ErrorHelpers } from '../utils/service-error-handler';

// Convert ServiceResult to safe default for existing API compatibility
ipcMain.handle('products:get-all', async (_, orgId: string) => {
  const result = productService.getAllWithErrorHandling(orgId);
  return ErrorHelpers.safeList(result); // Returns [] on error
});
```

## Error Helper Functions

```typescript
import { ErrorHelpers, ServiceErrorCode } from '../utils/service-error-handler';

// Safe conversions for backward compatibility
const products = ErrorHelpers.safeList(result); // [] on error
const product = ErrorHelpers.safeItem(result); // undefined on error
const success = ErrorHelpers.safeBool(result); // false on error

// Error checking
if (ErrorHelpers.isErrorCode(result, ServiceErrorCode.NOT_FOUND)) {
  // Handle not found specifically
}

const errorMsg = ErrorHelpers.getErrorMessage(result); // undefined if success
```

## Migration Strategy

### Phase 1: Add alongside existing methods

```typescript
// Keep existing method for backward compatibility
getAll(orgId: string): Product[] {
  // existing implementation
}

// Add new method with error handling
getAllWithErrorHandling(orgId: string): ServiceResult<Product[]> {
  return this.errorHandler.wrap(() => {
    return this.getAll(orgId);
  }, 'getAllProducts', { orgId });
}
```

### Phase 2: Update IPC handlers to use new methods

```typescript
// Old way
ipcMain.handle('products:get-all', async (_, orgId: string) => {
  return productService.getAll(orgId); // throws or returns []
});

// New way
ipcMain.handle('products:get-all', async (_, orgId: string) => {
  const result = productService.getAllWithErrorHandling(orgId);
  return ErrorHelpers.safeList(result); // Always returns Product[]
});
```

### Phase 3: Eventually replace old methods (optional)

Only if the team wants to fully standardize. Can keep both approaches.

## Error Codes

```typescript
export enum ServiceErrorCode {
  NOT_FOUND = 'NOT_FOUND', // Entity not found
  VALIDATION_ERROR = 'VALIDATION_ERROR', // Input validation failed
  DATABASE_ERROR = 'DATABASE_ERROR', // Database operation failed
  PERMISSION_DENIED = 'PERMISSION_DENIED', // Insufficient permissions
  INVALID_INPUT = 'INVALID_INPUT', // Invalid input parameters
  OPERATION_FAILED = 'OPERATION_FAILED', // Generic operation failure
}
```

## Benefits

1. **Consistent error logging** - All errors logged with service name, operation, and context
2. **Standardized error format** - Predictable error object structure
3. **Error categorization** - Automatic error code assignment
4. **Backward compatibility** - Can add alongside existing methods
5. **Better debugging** - Rich context in error logs

## When to Use

- **Always** for new service methods
- **Optionally** retrofit existing critical methods
- **Recommended** for operations that frequently fail (external API calls, complex queries)
- **Required** for operations exposed to external systems

## Simple Example

```typescript
// Before
delete(id: string): boolean {
  try {
    const result = this.db.prepare('DELETE FROM products WHERE id = ?').run(id);
    return result.changes > 0;
  } catch (error) {
    console.error('Delete failed:', error);
    return false;
  }
}

// After
deleteWithErrorHandling(id: string): ServiceResult<boolean> {
  return this.errorHandler.wrap(() => {
    const result = this.db.prepare('DELETE FROM products WHERE id = ?').run(id);
    return result.changes > 0;
  }, 'deleteProduct', { id });
}
```

The error handler automatically:

- Logs errors with consistent format
- Categorizes error types
- Provides structured error objects
- Maintains operation context
