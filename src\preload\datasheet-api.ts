/**
 * @file datasheet-api.ts
 * @description Datasheet API preload script for secure IPC communication
 */

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { DatasheetChannels } from '../shared/constants/channels';

// Typed API for datasheet operations
contextBridge.exposeInMainWorld('datasheetAPI', {
  // Get datasheets by product
  getByProduct: (data: { productId: string }) => {
    console.log(
      `Preload: calling datasheetAPI.getByProduct for ${data.productId}`
    );
    return ipcRenderer.invoke(DatasheetChannels.GET_BY_PRODUCT, data);
  },

  // Add web link datasheet
  addWebLink: (data: {
    productId: string;
    url: string;
    displayName: string;
  }) => {
    console.log(
      `Preload: calling datasheetAPI.addWebLink for ${data.productId}`
    );
    return ipcRenderer.invoke(DatasheetChannels.ADD_WEB_LINK, data);
  },

  // Remove datasheet
  remove: (data: { datasheetId: string }) => {
    console.log(`Preload: calling datasheetAPI.remove for ${data.datasheetId}`);
    return ipcRenderer.invoke(DatasheetChannels.REMOVE, data);
  },

  // Open datasheet
  open: (data: { datasheetId: string }) => {
    console.log(`Preload: calling datasheetAPI.open for ${data.datasheetId}`);
    return ipcRenderer.invoke(DatasheetChannels.OPEN, data);
  },

  // Open all datasheets for product
  openAll: (data: { productId: string }) => {
    console.log(`Preload: calling datasheetAPI.openAll for ${data.productId}`);
    return ipcRenderer.invoke(DatasheetChannels.OPEN_ALL, data);
  },

  // Migrate datasheets
  migrate: () => {
    console.log('Preload: calling datasheetAPI.migrate');
    return ipcRenderer.invoke(DatasheetChannels.MIGRATE);
  },
});
