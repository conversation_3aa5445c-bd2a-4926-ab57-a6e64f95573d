/**
 * @file ipc-utils.ts
 * @description Utility functions for IPC handlers
 */

import { ipcMain } from 'electron';

/**
 * Check if a handler can be registered
 * @param channel The channel to check
 * @returns True if the handler can be registered
 */
export function canRegisterHandler(channel: string): boolean {
  // Check if the handler is already registered
  const isRegistered = ipcMain.listenerCount(channel) > 0;

  if (isRegistered) {
    console.warn(
      `Handler for channel ${channel} is already registered. Skipping registration.`
    );
    return false;
  }

  return true;
}
