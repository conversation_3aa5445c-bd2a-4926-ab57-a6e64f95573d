/**
 * @file AppShell.tsx
 * @description Main application shell component orchestrating the layout and routing
 */

import React, { useState } from 'react';
import { AppProviders } from './AppProviders';
import { AppRouter } from './AppRouter';
import { LicenseGuard } from './LicenseGuard';
import { DebugPanel } from './DebugPanel';
import { AppModals } from './AppModals';
import { ModalProvider } from '../../context/ModalContext';
import '../../index.css';

/**
 * Main App Shell component - renders when app is fully initialized
 * Now simplified since AppInitializer handles all loading states
 */
export const AppShell: React.FC = () => {
  const [debugPanelOpen, setDebugPanelOpen] = useState(false);

  // Default license status - can be enhanced later
  const licenseStatus = {
    isValid: true,
    inTrialMode: false,
    trialDaysRemaining: 0,
  };

  return (
    <AppProviders>
      <ModalProvider>
        {/* Skip to main content link for accessibility */}
        <a href='#main-content' className='skip-to-main'>
          Skip to main content
        </a>

        {/* License validation guard */}
        <LicenseGuard licenseStatus={licenseStatus}>
          {/* Main application router */}
          <AppRouter />

          {/* Global modals */}
          <AppModals />

          {/* Debug panel for development */}
          {process.env.NODE_ENV === 'development' && (
            <DebugPanel
              isOpen={debugPanelOpen}
              onClose={() => setDebugPanelOpen(false)}
            />
          )}
        </LicenseGuard>
      </ModalProvider>
    </AppProviders>
  );
};

export default AppShell;
