/**
 * @file datasheet-migrated.ipc.ts
 * @description Migrated datasheet IPC handlers using dependency injection + universal wrapper pattern
 */

import { ipcMain } from 'electron';
import { DatasheetService } from '../db/services/datasheet.service';
import { DatasheetChannels } from '../../shared/constants/channels';
import {
  registerSecureHandler,
  createSuccessResponse,
} from '../utils/ipc-wrapper';
import { ServiceLocator } from '../services/service-locator';

/**
 * Service dependencies interface
 */
interface DatasheetServices {
  datasheetService: DatasheetService;
}

/**
 * Register datasheet IPC handlers with dependency injection and universal wrapper
 *
 * @param services - Optional service bundle for dependency injection
 */
export function registerDatasheetHandlers(
  services?: Partial<DatasheetServices>
): void {
  console.log(
    '[DatasheetIPC] Registering datasheet handlers with dependency injection'
  );

  // Use dependency injection or fallback to ServiceLocator
  const datasheetService =
    services?.datasheetService || ServiceLocator.getDatasheetService();

  console.log('[DatasheetIPC] Services available:', {
    datasheetService: !!datasheetService,
  });

  // Get datasheets by product
  registerSecureHandler(
    DatasheetChannels.GET_BY_PRODUCT,
    async (organizationId: string, data: { productId: string }) => {
      const datasheets = await datasheetService.getDatasheetsByProductId(
        data.productId,
        organizationId
      );
      return createSuccessResponse(
        datasheets,
        `Retrieved ${datasheets.length} datasheets for product`
      );
    },
    ipcMain,
    {
      logChannel: 'DatasheetIPC',
      customErrorMessage: 'Failed to retrieve datasheets. Please try again.',
    }
  );

  // Add file datasheet to product
  registerSecureHandler(
    DatasheetChannels.ADD_TO_PRODUCT,
    async (
      organizationId: string,
      data: { productId: string; filePath: string; displayName?: string }
    ) => {
      const datasheet = await datasheetService.addToProduct(
        data.productId,
        data.filePath,
        organizationId,
        data.displayName
      );
      return createSuccessResponse(
        datasheet,
        `Datasheet "${datasheet.displayName || datasheet.fileName}" added successfully`
      );
    },
    ipcMain,
    {
      logChannel: 'DatasheetIPC',
      customErrorMessage:
        'Failed to add datasheet. Please check the file and try again.',
    }
  );

  // Add web link datasheet to product
  registerSecureHandler(
    DatasheetChannels.ADD_WEB_LINK,
    async (
      organizationId: string,
      data: { productId: string; url: string; displayName: string }
    ) => {
      const datasheet = await datasheetService.addWebLink(
        data.productId,
        data.url,
        data.displayName,
        undefined, // description
        undefined, // userId
        organizationId
      );

      if (!datasheet) {
        throw new Error('Failed to add web link');
      }

      return createSuccessResponse(
        datasheet,
        `Web link "${datasheet.name}" added successfully`
      );
    },
    ipcMain,
    {
      logChannel: 'DatasheetIPC',
      customErrorMessage:
        'Failed to add web link. Please check the URL and try again.',
    }
  );

  // Remove datasheet
  registerSecureHandler(
    DatasheetChannels.REMOVE,
    async (organizationId: string, data: { datasheetId: string }) => {
      const success = await datasheetService.remove(
        data.datasheetId,
        organizationId
      );
      return createSuccessResponse(
        success,
        success
          ? 'Datasheet removed successfully'
          : 'Failed to remove datasheet'
      );
    },
    ipcMain,
    {
      logChannel: 'DatasheetIPC',
      customErrorMessage: 'Failed to remove datasheet. Please try again.',
    }
  );

  // Open datasheet
  registerSecureHandler(
    DatasheetChannels.OPEN,
    async (organizationId: string, data: { datasheetId: string }) => {
      const result = await datasheetService.open(
        data.datasheetId,
        organizationId
      );
      return createSuccessResponse(
        result,
        result.success
          ? 'Datasheet opened successfully'
          : result.message || 'Failed to open datasheet'
      );
    },
    ipcMain,
    {
      logChannel: 'DatasheetIPC',
      customErrorMessage:
        'Failed to open datasheet. Please check if the file exists.',
    }
  );

  // Open all datasheets for a product
  registerSecureHandler(
    DatasheetChannels.OPEN_ALL,
    async (organizationId: string, data: { productId: string }) => {
      const result = await datasheetService.openAllForProduct(
        data.productId,
        organizationId
      );
      return createSuccessResponse(
        {
          success: result.success,
          opened: result.opened || 0,
          failed: result.failed || 0,
          total: result.total || 0,
          errors: result.errors || [],
        },
        result.success
          ? `Opened ${result.opened || 0} of ${result.total || 0} datasheets`
          : `Failed to open datasheets: ${result.message || 'Unknown error'}`
      );
    },
    ipcMain,
    {
      logChannel: 'DatasheetIPC',
      customErrorMessage:
        'Failed to open datasheets. Some files may not be accessible.',
    }
  );

  // Migrate datasheets from old format
  registerSecureHandler(
    DatasheetChannels.MIGRATE,
    async (organizationId: string) => {
      const result =
        await datasheetService.migrateFromOldFormat(organizationId);
      return createSuccessResponse(
        {
          success: result.success,
          migrated: result.migrated || 0,
          skipped: result.skipped || 0,
          errors: result.errors || [],
        },
        result.success
          ? `Migration completed: ${result.migrated || 0} datasheets migrated, ${result.skipped || 0} skipped`
          : `Migration failed: ${result.message || 'Unknown error'}`
      );
    },
    ipcMain,
    {
      logChannel: 'DatasheetMigration',
      customErrorMessage:
        'Failed to migrate datasheets. Please contact support if this issue persists.',
    }
  );

  console.log('[DatasheetIPC] Datasheet handlers registered successfully');
}

/**
 * Alternative registration using only ServiceLocator
 */
export function registerDatasheetHandlersFromLocator(): void {
  console.log(
    '[DatasheetIPC] Registering datasheet handlers using ServiceLocator pattern'
  );
  registerDatasheetHandlers();
}
