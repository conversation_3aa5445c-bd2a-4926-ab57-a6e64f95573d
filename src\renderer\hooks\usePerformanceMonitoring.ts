import { useEffect, useRef, useState } from 'react';
import { errorLogger } from '../utils/errorLogger';

interface PerformanceStats {
  renderCount: number;
  averageRenderTime: number;
  slowRenders: number;
  memoryUsage?: number;
}

export function usePerformanceMonitoring(
  componentName: string,
  threshold = 16
) {
  const renderCount = useRef(0);
  const renderTimes = useRef<number[]>([]);
  const startTime = useRef<number>(0);
  const [stats, setStats] = useState<PerformanceStats>({
    renderCount: 0,
    averageRenderTime: 0,
    slowRenders: 0,
  });

  // Track render start
  useEffect(() => {
    startTime.current = performance.now();
  });

  // Track render completion
  useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;

    renderCount.current++;
    renderTimes.current.push(renderTime);

    // Keep only last 100 render times
    if (renderTimes.current.length > 100) {
      renderTimes.current = renderTimes.current.slice(-100);
    }

    // Log slow renders
    if (renderTime > threshold) {
      errorLogger.logWarning(`Slow render in ${componentName}`, {
        renderTime,
        threshold,
        componentName,
      });
    }

    // Log performance metric
    errorLogger.logMetric({
      name: `render_time_${componentName}`,
      value: renderTime,
      unit: 'ms',
      context: { componentName },
    });

    // Update stats
    const averageRenderTime =
      renderTimes.current.reduce((a, b) => a + b, 0) /
      renderTimes.current.length;
    const slowRenders = renderTimes.current.filter(
      time => time > threshold
    ).length;

    setStats({
      renderCount: renderCount.current,
      averageRenderTime,
      slowRenders,
      memoryUsage: (performance as any).memory?.usedJSHeapSize,
    });
  }, [componentName, threshold]); // Add dependencies to prevent infinite loop

  return stats;
}

export function useAsyncOperationMonitoring() {
  const measureOperation = async <T>(
    operationName: string,
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> => {
    return errorLogger.measureAsync(operationName, operation, context);
  };

  return { measureOperation };
}

export function useMemoryMonitoring(intervalMs = 30000) {
  const [memoryStats, setMemoryStats] = useState<{
    used: number;
    total: number;
    percentage: number;
  } | null>(null);

  useEffect(() => {
    const measureMemory = () => {
      if ((performance as any).memory) {
        const memory = (performance as any).memory;
        const used = memory.usedJSHeapSize;
        const total = memory.totalJSHeapSize;
        const percentage = (used / total) * 100;

        setMemoryStats({ used, total, percentage });

        // Log memory metric
        errorLogger.logMetric({
          name: 'memory_usage',
          value: used,
          unit: 'bytes',
          context: { total, percentage },
        });

        // Warn if memory usage is high
        if (percentage > 80) {
          errorLogger.logWarning('High memory usage detected', {
            used,
            total,
            percentage,
          });
        }
      }
    };

    measureMemory();
    const interval = setInterval(measureMemory, intervalMs);

    return () => clearInterval(interval);
  }, [intervalMs]);

  return memoryStats;
}
