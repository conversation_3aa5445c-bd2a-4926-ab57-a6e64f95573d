/**
 * @file ipc-response-handler.ts
 * @description Shared IPC response handling utilities for consistent error handling across stores
 */

import type { Result } from '../../shared/types/result.types';

// IPC Response interface to match the backend format
export interface IPCResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  userMessage?: string;
  timestamp: number;
  code?: string; // For specific error codes like 'NO_ORGANIZATION_SELECTED'
}

/**
 * Enhanced error class for IPC operations
 */
export class IPCError extends Error {
  constructor(
    message: string,
    public readonly code?: string,
    public readonly userMessage?: string,
    public readonly timestamp?: number
  ) {
    super(message);
    this.name = 'IPCError';
  }

  /**
   * Check if this is an organization context error
   */
  isOrganizationError(): boolean {
    return this.code === 'NO_ORGANIZATION_SELECTED';
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(): string {
    if (this.userMessage) {
      return this.userMessage;
    }

    if (this.isOrganizationError()) {
      return 'No organization selected. Please select an organization from the dropdown menu.';
    }

    return this.message || 'Operation failed';
  }
}

/**
 * Handle IPC responses with enhanced error handling and type safety
 * @param response - The IPC response to handle
 * @returns The extracted data or throws an IPCError
 */
export function handleIPCResponse<T>(response: T | IPCResponse<T>): T {
  // Check if it's the new IPCResponse format
  if (response && typeof response === 'object' && 'success' in response) {
    const ipcResponse = response as IPCResponse<T>;

    if (ipcResponse.success) {
      return ipcResponse.data as T;
    } else {
      // Enhanced error handling with specific error codes
      throw new IPCError(
        ipcResponse.error || 'Operation failed',
        ipcResponse.code,
        ipcResponse.userMessage,
        ipcResponse.timestamp
      );
    }
  }

  // Return as-is if it's raw data (legacy format)
  return response as T;
}

/**
 * Safe version of handleIPCResponse that returns a Result type instead of throwing
 * @param response - The IPC response to handle
 * @returns Result with success/error state
 */
export function safeHandleIPCResponse<T>(
  response: T | IPCResponse<T>
): Result<T, IPCError> {
  try {
    const data = handleIPCResponse(response);
    return { success: true, data };
  } catch (error) {
    const ipcError =
      error instanceof IPCError ? error : new IPCError(String(error));
    return { success: false, error: ipcError };
  }
}

/**
 * Type guard to check if a response is an IPCResponse
 */
export function isIPCResponse<T>(
  response: T | IPCResponse<T>
): response is IPCResponse<T> {
  return response && typeof response === 'object' && 'success' in response;
}

/**
 * Create a successful IPC response
 */
export function createSuccessResponse<T>(data: T): IPCResponse<T> {
  return {
    success: true,
    data,
    timestamp: Date.now(),
  };
}

/**
 * Create an error IPC response
 */
export function createErrorResponse(
  error: string,
  code?: string,
  userMessage?: string
): IPCResponse<never> {
  return {
    success: false,
    error,
    code,
    userMessage,
    timestamp: Date.now(),
  };
}

/**
 * Validate that response contains expected data structure
 */
export function validateResponseData<T>(
  data: T,
  validator: (data: T) => boolean,
  errorMessage = 'Invalid response data structure'
): T {
  if (!validator(data)) {
    throw new IPCError(errorMessage, 'INVALID_DATA_STRUCTURE');
  }
  return data;
}

// Export legacy compatibility function
export { handleIPCResponse as handleIPCResponseLegacy };
