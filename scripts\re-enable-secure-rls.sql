-- Re-enable Secure RLS Policies for organization_members
-- This script removes the temporary permissive policy and implements
-- secure, non-recursive RLS policies for the organization_members table.

-- Drop the permissive policy
DROP POLICY IF EXISTS "Allow all for authenticated users" ON organization_members;

-- Create simple, proven non-recursive policies
CREATE POLICY "Users see own memberships" ON organization_members
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can add themselves to an organization" ON organization_members
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Verify the new policies are in place
SELECT
  tablename,
  policyname,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'organization_members'
ORDER BY policyname;
