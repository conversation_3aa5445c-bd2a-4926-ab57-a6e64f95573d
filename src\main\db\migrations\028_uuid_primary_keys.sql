-- Migration: 028_uuid_primary_keys.sql
-- Purpose: Standardize all tables to use UUI<PERSON> as primary key (id TEXT PRIMARY KEY)
-- Issue: Dual ID system (INTEGER id + TEXT external_id) causing sync failures and confusion
-- Date: 2025-01-04
-- Author: ChromaSync Team

-- This migration removes the dual ID system and standardizes on UUID primary keys
-- matching the schema definition in complete-schema.ts

BEGIN TRANSACTION;

-- Step 1: Create backup tables to preserve data
CREATE TABLE IF NOT EXISTS products_backup AS SELECT * FROM products;
CREATE TABLE IF NOT EXISTS colors_backup AS SELECT * FROM colors;
CREATE TABLE IF NOT EXISTS product_colors_backup AS SELECT * FROM product_colors;

-- Step 2: Drop existing tables (we'll recreate with proper schema)
DROP TABLE IF EXISTS product_colors;
DROP TABLE IF EXISTS colors;
DROP TABLE IF EXISTS products;

-- Step 3: Recreate products table with UUID primary key
CREATE TABLE products (
  id TEXT PRIMARY KEY,  -- UUID, no more external_id
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  type TEXT,
  sku TEXT,
  website TEXT,
  datasheet_url TEXT,
  price REAL,
  currency TEXT,
  is_master BOOLEAN NOT NULL DEFAULT FALSE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  -- Organization support (UUID)
  organization_id TEXT NOT NULL,
  created_by TEXT,
  user_id TEXT,
  metadata JSON DEFAULT '{}',
  -- Sync fields
  device_id TEXT,
  deleted_at TEXT,
  conflict_resolved_at TEXT,
  is_synced INTEGER DEFAULT 0,
  
  -- Constraints
  CHECK (length(id) = 36),  -- UUID format validation
  CHECK (length(trim(name)) > 0)
);

-- Step 4: Recreate colors table with UUID primary key
CREATE TABLE colors (
  id TEXT PRIMARY KEY,  -- UUID, no more external_id
  name TEXT NOT NULL,
  display_name TEXT,
  code TEXT,
  hex TEXT NOT NULL,
  source_id INTEGER NOT NULL DEFAULT 1 REFERENCES color_sources(id),
  -- Color spaces as JSON
  color_spaces JSON DEFAULT '{}',
  -- Color characteristics  
  is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
  is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
  is_effect BOOLEAN NOT NULL DEFAULT FALSE,
  -- Cloud compatibility columns
  gradient_colors TEXT,
  notes TEXT,
  tags TEXT,
  is_library BOOLEAN NOT NULL DEFAULT FALSE,
  -- Legacy properties
  properties JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  version INTEGER NOT NULL DEFAULT 1,
  -- Organization support (UUID)
  organization_id TEXT NOT NULL,
  created_by TEXT,
  user_id TEXT,
  deleted_at TEXT,
  device_id TEXT,
  conflict_resolved_at TEXT,
  is_synced INTEGER DEFAULT 0,
  
  -- Constraints
  CHECK (length(id) = 36),  -- UUID format validation
  CHECK (length(trim(hex)) > 0)
);

-- Step 5: Recreate product_colors junction table with UUID foreign keys
CREATE TABLE product_colors (
  product_id TEXT NOT NULL,  -- UUID foreign key
  color_id TEXT NOT NULL,    -- UUID foreign key
  display_order INTEGER NOT NULL DEFAULT 0,
  organization_id TEXT NOT NULL,
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (product_id, color_id),
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
  FOREIGN KEY (color_id) REFERENCES colors(id) ON DELETE CASCADE
);

-- Step 6: Migrate products data (use external_id as new id)
INSERT INTO products (
  id, name, description, category, type, sku, website, datasheet_url,
  price, currency, is_master, is_active, created_at, updated_at,
  organization_id, created_by, user_id, metadata,
  device_id, deleted_at, conflict_resolved_at, is_synced
)
SELECT 
  external_id as id,  -- Use external_id as the new primary key
  name, description, category, type, sku, website, datasheet_url,
  price, currency, is_master, is_active, created_at, updated_at,
  organization_id, created_by, user_id, metadata,
  device_id, deleted_at, conflict_resolved_at, is_synced
FROM products_backup
WHERE external_id IS NOT NULL;

-- Step 7: Migrate colors data (use external_id as new id)
INSERT INTO colors (
  id, name, display_name, code, hex, source_id, color_spaces,
  is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
  is_library, properties, created_at, updated_at, version,
  organization_id, created_by, user_id, deleted_at, device_id,
  conflict_resolved_at, is_synced
)
SELECT 
  external_id as id,  -- Use external_id as the new primary key
  name, display_name, code, hex, source_id, color_spaces,
  is_gradient, is_metallic, is_effect, gradient_colors, notes, tags,
  is_library, properties, created_at, updated_at, version,
  organization_id, created_by, user_id, deleted_at, device_id,
  conflict_resolved_at, is_synced
FROM colors_backup
WHERE external_id IS NOT NULL;

-- Step 8: Migrate product_colors relationships
-- Need to map INTEGER IDs to UUIDs
INSERT INTO product_colors (product_id, color_id, display_order, organization_id, added_at)
SELECT 
  p.external_id as product_id,
  c.external_id as color_id,
  pc.display_order,
  pc.organization_id,
  pc.added_at
FROM product_colors_backup pc
JOIN products_backup p ON pc.product_id = p.id
JOIN colors_backup c ON pc.color_id = c.id
WHERE p.external_id IS NOT NULL 
  AND c.external_id IS NOT NULL;

-- Step 9: Create indexes for performance
CREATE INDEX idx_products_org ON products(organization_id);
CREATE INDEX idx_products_deleted_at ON products(deleted_at);
CREATE INDEX idx_products_org_active ON products(organization_id, deleted_at) WHERE deleted_at IS NULL;
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_products_sku ON products(sku) WHERE sku IS NOT NULL;
CREATE INDEX idx_products_is_synced ON products(is_synced);

CREATE INDEX idx_colors_hex ON colors(hex);
CREATE INDEX idx_colors_code ON colors(code) WHERE code IS NOT NULL;
CREATE INDEX idx_colors_source ON colors(source_id);
CREATE INDEX idx_colors_org ON colors(organization_id);
CREATE INDEX idx_colors_gradient_colors ON colors(gradient_colors) WHERE gradient_colors IS NOT NULL;
CREATE INDEX idx_colors_is_library ON colors(is_library);
CREATE INDEX idx_colors_org_code_opt ON colors(organization_id, code);
CREATE INDEX idx_colors_org_created_opt ON colors(organization_id, created_at);
CREATE INDEX idx_colors_org_hex_opt ON colors(organization_id, hex);
CREATE INDEX idx_colors_is_synced ON colors(is_synced);

CREATE INDEX idx_product_colors_product ON product_colors(product_id);
CREATE INDEX idx_product_colors_color ON product_colors(color_id);
CREATE INDEX idx_product_colors_org ON product_colors(organization_id);
CREATE INDEX idx_product_colors_org_product ON product_colors(organization_id, product_id);
CREATE INDEX idx_product_colors_org_color ON product_colors(organization_id, color_id);

-- Step 10: Add triggers for updated_at
CREATE TRIGGER update_products_timestamp 
AFTER UPDATE ON products
BEGIN
  UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_colors_timestamp 
AFTER UPDATE ON colors
BEGIN
  UPDATE colors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Step 11: Verify migration success
SELECT 
  'Migration complete. ' ||
  'Products: ' || (SELECT COUNT(*) FROM products) || ' migrated, ' ||
  'Colors: ' || (SELECT COUNT(*) FROM colors) || ' migrated, ' ||
  'Product-Colors: ' || (SELECT COUNT(*) FROM product_colors) || ' relationships preserved'
  as migration_result;

-- Step 12: Drop backup tables (comment out if you want to keep them for safety)
-- DROP TABLE products_backup;
-- DROP TABLE colors_backup;
-- DROP TABLE product_colors_backup;

COMMIT;